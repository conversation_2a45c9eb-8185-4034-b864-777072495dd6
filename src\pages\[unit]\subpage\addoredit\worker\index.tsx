import React, { Fragment, useEffect, useRef, useState } from 'react';
import ListTable from '@/components/ListTable';
import moment from 'moment';
import { But<PERSON>, Divider, Popconfirm } from 'antd';
import { countrysideGetList, delUnitCountryside } from '@/pages/[unit]/services/index';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import Add from './components/add';
import Tip from '@/components/Tip';

const index = (props: any) => {
  const addRef:any = useRef();
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0 });
  const [list, setList] = useState([]);

  const action = async (p = {}) => {
    const { unit: { basicInfo: { code: unitCode = '' } = {} } = {}, pageType = '' } = props;
    console.log(pageType, 'pageType');
    const {
      code = 500,
      data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await countrysideGetList({
      pageSize: pagination.pageSize,
      pageNum: pagination.page,
      unitCode,
      type: pageType,
      ...p,
    });
    if (code === 0) {
      setList(list);
      setPagination({ page: pageNum, total, pageSize });
    }
  };

  const addOrEdit = (record: any) => {
    addRef.current.open(record);
  };
  const confirm = async (record: any) => {
    const {code} = record;
    const {code:resCode = 500} = await delUnitCountryside({code});
    if(resCode === 0){
      Tip.success('操作提示', '操作成功');
      action();
    }
  };

  const columns = [
    {
      title: '人员名称',
      dataIndex: 'memName',
      width: 200,
    },
    {
      title: '性别',
      dataIndex: 'sexName',
      width: 200,
    },
    // {
    //   title: '是否本单位人员',
    //   dataIndex: 'memTypeName',
    //   width: 200,
    // },
    {
      title: '身份证',
      dataIndex: 'memIdcard',
      width: 200,
    },
    {
      title: '学历',
      dataIndex: 'd07Name',
      width: 200,
    },
    {
      title: '出生日期',
      dataIndex: 'birthday',
      width: 200,
      render: (text) => {
        if (text) {
          return moment(text).format('YYYY-MM-DD');
        }
        return null;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 200,
      render: (text, record) => {
        return (
          <span>
              <a onClick={() => addOrEdit(record)}>编辑</a>
              <Divider type="vertical"/>
              <Popconfirm title="确定要删除吗？" onConfirm={() => confirm(record)}>
               <a className={'del'}>删除</a>
              </Popconfirm>
            </span>
        );
      },
    },
  ];

  useEffect(() => {
    action({ pageNum: 1 });
  }, []);
  return (
    <Fragment>
      <div style={{ marginBottom: 10, marginLeft: 10 }}>
        <Button type={'primary'}
                onClick={() => {
                  if(addRef.current.open){
                    addRef.current.open();
                  }
                }}
                icon={<LegacyIcon type={'plus'}/>}>添加人员</Button>
      </div>
      <ListTable columns={columns}
                 data={list}
                 pagination={pagination}
                 onPageChange={(page: any, pageSize: any) => {
                   action({ pageNum: page, pageSize });
                 }}/>
      <Add ref={addRef} {...props} onOK={()=>{
        action({ pageNum: 1});
      }}/>
    </Fragment>
  );
};
export default index;
