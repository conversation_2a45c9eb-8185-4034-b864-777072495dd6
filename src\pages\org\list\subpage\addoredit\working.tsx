/**
 * 党务工作者情况
 **/
import React from 'react';
import ListTable from '@/components/ListTable';
import { PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Divider, Popconfirm } from 'antd';
import Tip from '@/components/Tip';
import { workList, workSave, workDel } from '@/pages/org/services/org';
import MemSelect from '@/components/MemSelect';

export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      pagination: {},
      selectedRowKeys: [],
      loading: false,
      type: 'add',
      list: [],
      memCode:[],
    };
  }
  getList = async (p = {}) => {
    this.setState({loading:true});
    const { org: { basicInfo: { orgCode: orgCode = '' } = {} } = {} } = this.props;
    const {
      code = 500,
      data: { list = [], pageNumber = 1, pageSize = 10, totalRow = 0 } = {},
    } = await workList({
      pageNum: 1,
      pageSize: 10,
      orgCode:this.props.org.basicInfo.orgCode,
      ...p,
    });
    this.setState({loading:false});
    if (code == 0) {
      this.setState({
        list,
        pagination: { pageSize, total: totalRow, page: pageNumber, current: pageNumber },
      });
    }
  };
  componentDidMount = () => {
    this.getList({ pageNum: 1 });
  };
  add = (e) => {
    this.setState(
      {
        type: 'add',
        dataInfo: undefined,
      },
      () => {
        this['AddWorking'].showModal();
      },
    );
  };
  edit = (item) => {
    this.setState({ dataInfo: item, type: 'edit' }, () => {
      this['AddWorking'].showModal();
    });
  };
  del = async (record) => {
    const { org: { basicInfo: { orgCode: orgCode = '' } = {} } = {} } = this.props;
    const { code = 500 } = await workDel({
      data: {
        orgCode:this.props.org.basicInfo.orgCode,
        memCode: record.code || '',
      },
    });
    if (code === 0) {
      Tip.success('操作提示', '删除成功');
      this.getList({ pageNum: 1 });
    }
  };
  onPageChange = (page, pageSize) => {
    this.getList({ pageNum: page, pageSize });
  };
  // 增加党务工作者
  addWorking = async (memCode) => {
    const { org: { basicInfo: { orgCode: orgCode = '' } = {} } = {} } = this.props;
    const {
      code = 500,
      data: { list = [], pageNumber = 1, pageSize = 10, totalRow = 0 } = {},
    } = await workSave({data:{
      orgCode:this.props.org.basicInfo.orgCode,
      memCode,
    }});
    if (code == 0) {
      Tip.success('操作提示', '增加成功');
      this.getList({ pageNum: 1 });
    }
  };
  // 选人
  checkMem=(e)=>{
    let memCode:any=[];
    e && e.map((item:any)=>{
      memCode.push(item['code'])
    })
    this.addWorking(memCode)
    this.setState({
      memCode,
    })
  }
  render(): React.ReactNode {
    const { type, dataInfo, list, loading, pagination } = this.state;
    const { basicInfo = {} } = this.props.org;
    const { current, pageSize } = pagination;
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 58,
        align:'center',
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 110,
        align:'center',
      },
      {
        title: '性别',
        dataIndex: 'sexName',
        width: 58,
        align:'center',
      },
      {
        title: '民族',
        dataIndex: 'd06Name',
        width: 100,
        align:'center',
      },
      {
        title: '党员类别',
        dataIndex: 'd08Name',
        align:'center',
        width: 100,
      },
      {
        title: '工作岗位',
        dataIndex: 'd09Name',
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 58,
        align:'center',
        render: (text, record) => {
          return (
            <span>
              {/* <a onClick={()=>this.edit(record)}>编辑</a> */}
              {/* <Divider type="vertical" /> */}
              <Popconfirm
                title="确认删除?"
                onConfirm={() => this.del(record)}
                okText="是"
                cancelText="否"
              >
                <a className={'del'}>删除</a>
              </Popconfirm>
            </span>
          );
        },
      },
    ];
    return (
      <div style={{ padding: '0 20px' }}>
        <MemSelect checkType={'checkbox'} onChange={this.checkMem} >
        <Button
          type="primary"
          icon={<PlusOutlined />}
          style={{ marginBottom: 10 }}
        >
          添加党务工作者
        </Button>
        </MemSelect>
        <ListTable
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={this.onPageChange}
          rowKey={'d42Name'}
        />
      </div>
    );
  }
}
