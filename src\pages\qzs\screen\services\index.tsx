import request from '@/utils/request';
import qs from 'qs';

// 党员搜索信息
export function memGetList(params) {
  return request(`/api/mem/getList`, {
    method: 'POST',
    body: params,
  });
}

// 党员详情
export function screenlist(params) {
  return request(`/api/mem/screen/list`, {
    method: 'POST',
    body: params,
  });
}
// 时代答卷新增
export function screensave(params) {
  return request(`/api/mem/screen/save`, {
    method: 'POST',
    body: params,
  });
}

export function findByCode(para) {
  return request(`/api/mem/screen/findByCode?${qs.stringify(para)}`, {
    method: 'GET',
  });
}

export function screenexport(para) {
  return request(
    `/api/mem/screen/export?${qs.stringify(para)}`,
    {
      method: 'GET',
    },
    'file',
  );
}

// 查询政治生日
export function politicalBirthday(params) {
  return request(`/api/mem/screen/politicalBirthday`, {
    method: 'POST',
    body: params,
  });
}

export function findDomain(para) {
  return request(`/api/mem/screen/findDomain?${qs.stringify(para)}`, {
    method: 'GET',
  });
}

// export function findDomain(params) {
//   return request(`/api/mem/screen/findDomain`, {
//     method: 'POST',
//     body: params,
//   });
// }
