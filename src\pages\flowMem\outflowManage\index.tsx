// 流动党员-流出管理
import React, { Fragment } from 'react';
import { Tabs } from 'antd';
import { getSession } from '@/utils/session';
import NotIncludeManage from './components/notIncludeManage';
import IncludeManage from './components/includeManage';
import Back from './components/back';
import History from './components/history';

const TabPane = Tabs.TabPane;

export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: '1',
    };
  }
  render() {
    const { activeTab } = this.state;
    return (
      <Fragment>
        <Tabs
          activeKey={activeTab}
          onChange={(e) => {
            this.setState({
              activeTab: e,
            });
          }}
        >
          <TabPane tab="未纳入流入地管理" key="1" />
          <TabPane tab="已纳入流入地管理" key="2" />
          <TabPane tab="流出被退回" key="3" />
          <TabPane tab="流出历史" key="4" />
        </Tabs>
        {activeTab === '1' && <NotIncludeManage org={getSession('org')} />}
        {activeTab === '2' && <IncludeManage org={getSession('org')} />}
        {activeTab === '3' && <Back org={getSession('org')} />}
        {activeTab === '4' && <History org={getSession('org')} />}
      </Fragment>
    );
  }
}
