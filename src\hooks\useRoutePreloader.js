import { useEffect, useCallback, useRef } from 'react';
import routePreloader from '@/utils/routePreloader';

/**
 * 路由预加载Hook
 * @param {Object} options - 配置选项
 * @param {string[]} options.commonRoutes - 常用路由
 */
export const useRoutePreloader = (options = {}) => {
  const { commonRoutes = [] } = options;

  const hasInitialized = useRef(false);

  // 预加载单个路由
  const preloadRoute = useCallback(async (routePath) => {
    try {
      await routePreloader.preloadRoute(routePath);
      return true;
    } catch (error) {
      console.warn(`预加载失败: ${routePath}`, error);
      return false;
    }
  }, []);

  // 批量预加载路由
  const preloadRoutes = useCallback(async (routePaths, concurrency = 3) => {
    try {
      await routePreloader.preloadRoutes(routePaths, concurrency);
      return true;
    } catch (error) {
      console.warn('批量预加载失败', error);
      return false;
    }
  }, []);

  // 预加载常用路由
  const preloadCommonRoutes = useCallback(async () => {
    await routePreloader.preloadCommonRoutes(commonRoutes);
  }, [commonRoutes]);

  // 检查路由是否已预加载
  const isRoutePreloaded = useCallback((routePath) => {
    return routePreloader.isRoutePreloaded(routePath);
  }, []);

  // 获取预加载统计
  const getPreloadStats = useCallback(() => {
    return routePreloader.getStats();
  }, []);

  // 初始化预加载
  useEffect(() => {
    if (!hasInitialized.current) {
      hasInitialized.current = true;

      // 延迟执行，避免影响首屏渲染
      const timer = setTimeout(async () => {
        console.log('📚 开始预加载常用路由');
        await preloadCommonRoutes();
      }, 1000); // 1秒后开始预加载

      return () => clearTimeout(timer);
    }
  }, [preloadCommonRoutes]);

  return {
    preloadRoute,
    preloadRoutes,
    preloadCommonRoutes,
    isRoutePreloaded,
    getPreloadStats,
  };
};

export default useRoutePreloader;
