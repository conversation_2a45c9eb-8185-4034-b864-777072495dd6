stages:
  - build
  - deploy
cache:
  key: ${CI_COMMIT_REF_SLUG}
  untracked: true
  paths:
    - node_modules/
    - dist/
    - yarn.lock
build:
  stage: build
  before_script:
    - yarn install
  script:
    - yarn run build
deploy_dev:
  stage: deploy
  only:
    - develop
  script:
    - ssh root@************ rm -rf /home/<USER>
    - ssh root@************ mkdir /home/<USER>
    - scp -r dist/ root@************:/home/<USER>
deploy_pro:
  stage: deploy
  only:
    - test
  script:
    - ssh -p 7722 root@************* rm -rf /home/<USER>/pc
    - ssh -p 7722 root@************* mkdir /home/<USER>/pc
    - scp -P 7722 -r dist/* root@*************:/home/<USER>/pc