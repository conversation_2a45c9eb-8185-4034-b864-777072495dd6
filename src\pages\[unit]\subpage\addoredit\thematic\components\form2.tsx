// 村党组织带头人队伍整体优化提升情况
import React, { Fragment, useEffect, useState } from 'react';
import { Button, Form, InputNumber, Row, Col, Switch, Divider, Checkbox } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import { formItemLayout, formItemLayout2, formItemLayout3 } from './config';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import Tip from '@/components/Tip';
import { saveForm2, findForm } from '@/pages/[unit]/services/thematic';
import _cloneDeep from 'lodash/cloneDeep';

const index = (props: any) => {
  const [form] = Form.useForm();
  const { selectTab, basicInfo } = props;
  const { code: unitCode = '' } = basicInfo;
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);

  const onFinish = async (e) => {
    setLoading(true);
    let val = _cloneDeep(e);
    ['hasAdjustReplace'].map((it) => {
      val[it] = val[it] ? 1 : 0;
    });
    const { code = 500 } = await saveForm2({
      data: {
        unitCode,
        ...val,
      },
    });
    setLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '修改成功');
    }
  };
  const getInfo = async () => {
    const { code = 500, data = {} } = await findForm({
      unitCode,
      type: '2',
    });
    if (code === 0) {
      setQuery(data);
      form.setFieldsValue(data);
    }
  };
  useEffect(() => {
    getInfo();
  }, []);

  // const options = [
  //   { label: '能力素质不胜任的', value: '1' },
  //   { label: '违纪违法的', value: '2' },
  //   { label: '因年龄、健康等原因不宜继续担任的', value: '3' },
  // ];
  return (
    <Fragment>
      <Form form={form} {...formItemLayout3} onFinish={onFinish}>
        <Row>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasAdjustReplace"
              label="本年调整撤换的村党组织书记"
              initialValue={query['hasAdjustReplace'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
            {/* <Checkbox.Group options={options} onChange={() => {}} /> */}
          </Col>
          {/* <Col span={24}>
            <div style={{ padding: '0 4%' }}>
              <Divider plain>来源</Divider>
            </div>
          </Col> */}
          <Col span={12}>
            <Form.Item name="richExpert" label="本村致富能手">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="outReturnHome" label="外出务工经商返乡人员">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="localCollegeGraduates" label="本乡本土大学毕业生">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="veterans" label="退役军人">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="agencyPersonnel" label="下派的机关、企事业单位人员">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="youngCadres" label="40岁以下年轻干部">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="candidateSecretary" label="有村党组织书记后备人选的行政村">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          {/* <Col span={24}>
            <div style={{ padding: '0 4%' }}>
              <Divider plain>培训</Divider>
            </div>
          </Col> */}
          <Col span={12}>
            <Form.Item name="trainRotation" label="参加县级集中轮训">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          {/* <Col span={24}>
            <div style={{ padding: '0 4%' }}>
              <Divider plain>管理使用</Divider>
            </div>
          </Col> */}
          <Col span={12}>
            <Form.Item
              name="recordManagement"
              label="村党组织书记实行县级党委组织部门备案管理的县（市、区、旗）"
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="employedStaff" label="从村党组织书记中录用公务员和事业编制工作人员">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="selectLeadCadres" label="从村党组织书记中选拔乡镇领导干部">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          {/* <Col span={24}>
            <div style={{ padding: '0 4%' }}>
              <Divider plain>激励保障</Divider>
            </div>
          </Col> */}
          <Col span={12}>
            <Form.Item
              name="doubleStandard"
              label="村党组织书记
              报酬待遇不低于农村居民人均可支配收入两倍标准的县（市、区、旗）"
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="endowmentInsurance"
              label="为村党组织书记统一办理养老保险的县（市、区、旗）"
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
        </Row>
        <div style={{ textAlign: 'center' }}>
          <WhiteSpace />
          <Button
            type={'primary'}
            htmlType={'submit'}
            icon={<LegacyIcon type={'check'} />}
            // onClick={() => {}}
            style={{ marginRight: 16 }}
            loading={loading}
          >
            保存
          </Button>
          {/* <Button
            type={'primary'}
            danger
            htmlType={'button'}
            icon={<LegacyIcon type={'delete'} />}
            onClick={() => {}}
          >
            取消
          </Button> */}
        </div>
      </Form>
    </Fragment>
  );
};
export default index;
