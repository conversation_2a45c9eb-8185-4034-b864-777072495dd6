/**
 * 届次信息列表
 */
import React, { Fragment } from 'react';
import { connect } from "dva";
import ListTable from 'src/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs, Popover, Modal, Dropdown, Menu } from 'antd';
import NowOrg from 'src/components/NowOrg';
import { getSession } from "@/utils/session";
import moment from 'moment'
import { setListHeight, isEmpty } from '@/utils/method';
import Notice from '@/components/Notice';
import ExportInfo from '@/components/Export';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import RuiFilter from '@/components/RuiFilter';
import EditDevlop from '@/pages/developMem/zy/components/Edit'
import ElectronicArchives from '@/pages/developMem/zy/components/electronicArchives'
import styles from './index.less';
import Application from '../components/application'
import { DownOutlined } from '@ant-design/icons';
import Tip from '@/components/Tip';


const Search = Input.Search;

@connect(({ memDevelop, archivesAdministration, unit, commonDict, loading }) => ({ memDevelop, archivesAdministration, unit, commonDict, loading: loading.effects['archivesAdministration/getDevList'] }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {},//筛选器
      search: {},//搜索框
      view: false,
      getList: this.getList,
      filenameList: {},
      visible1: false,
      record:{}
    };
  }
  // addOrEdit=()=>{
  //   this.setState({type:'add',dataInfo:{}},()=>{
  //     this['AddEdit'].showModal();
  //   });
  // };

  componentDidMount() {
    setListHeight(this)
  }

  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const org = getSession('org') || {};
    const { _org = {}, getList, page, pageSize, name } = prevState;
    if (!_isEqual(org, _org)) {
      state['org'] = org;
      state['_org'] = org;
      getList(page, pageSize, name);
    }
    return state;
  };

  getList = (pageNum = 1, pageSize = 10, name = '') => {
    let org = getSession('org') || {};
    const { filter } = this.state;
    let val = {
      memOrgCode: org['orgCode'],
      // positionOrgName: org['name'],
      pageNum: pageNum,
      pageSize: pageSize,
      searchType: "1",
      memName: name,
      ...filter,
      type: 5
    };
    for (let obj in val) {
      if (isEmpty(val[obj])) {
        delete val[obj]
      }
    }
    this.props.dispatch({
      type: 'archivesAdministration/getDevList',
      payload: {
        data: {
          ...val
        }
      }
    })
  };
  upList = () => {
    this.getList()
  };
  onPageChange = (page, pageSize) => {
    const { name, type } = this.state
    this.setState({
      page,
      pageSize
    }, () => {
      this.getList(page, pageSize, name)
    });

  };

  confirm = (record) => {
    this.props.dispatch({
      type: 'archivesAdministration/jcDel',
      payload: {
        code: record['code']
      }
    }).then(res => {
      if (res['code'] === 0) {
        Notice("操作提示", '删除成功!', "check-circle", "green");
        this.getList(this.state['page'], this.state['pageSize'], this.state['name'])
      } else {
        Notice("操作提示", res['message'], "exclamation-circle-o", "orange");
      }
    });
  };

  // };
  handleOk = () => {
    this.setState({ view: false })
  };
  handleCancel = () => {
    this.setState({ view: false })
  };

  // 筛选
  filterChange = (val) => {
    console.log('val===', val);
    this.setState({
      filter: val,
    }, () => this.getList(1, this.state['pageSize'], this.state.name));
  };
  search = (value) => {
    this.setState({
      name: isEmpty(value) ? undefined : value
    }, () => this.getList(1, this.state['pageSize'], value));
  };
  searchClear = (e) => {
    this.setState({
      name: e.target.value
    })
    if (!e.target.value) {
      this.getList(1, this.state['pageSize'], '');
    }
  };

  exportInfo = async () => {
    this.setState({
      jcInfoDownload: true,
    })
    await this['jcInfo'].submitNoModal();
    this.setState({
      jcInfoDownload: false,
    })
  };
  digitalArchives = async (record) => {
    // this['editDevlop'].destroy();
    // if (record && record['code']) {
    //   await this.props.dispatch({
    //     type: 'memDevelop/findMem',
    //     payload: {
    //       code: record['code'],
    //     }
    //   })
    // }
    // this['editDevlop'].open({ ...this.state.flowData, ...record });
    this['ElectronicArchives'].showModal(record?.code)
  }
  getFilenameList = (obj) => {
    this.setState({
      visible1: true,
      filenameList: obj
    })
  }
  handleOk1 = () => {
    this.setState({ visible1: false })
  }
  handleCancel1 = () => {
    this.setState({ visible1: false })
  }
  daexp = (record) => {
    // this.setState({
    //   record,
    //   action: '/api/zunyi/digital/exportDigitalData'
    // },()=>{
    //   this['downloadRef']&&this['downloadRef'].submitNoModal();
    // })
    if (record?.exportStatus == 0) {
      this['application'].open(record)
    }
    if (record?.exportStatus == 1) {
      Tip.info('操作提示', '导出正在申请中')
    }
    if (record?.exportStatus == 2) {
      this.setState({
        record,
        action: '/api/zunyi/digital/exportDigitalData'
      }, () => {
        this['downloadRef'] && this['downloadRef'].submitNoModal();
      })
    }

  }
  logrxp = (record) => {
    this.setState({
      record,
      action: '/api/zunyi/digital/exportDigitalLogs'
    },()=>{
      this['downloadRef']&&this['downloadRef'].submitNoModal();
    })
    // this.props.dispatch({
    //   type: 'archivesAdministration/exportDigitalLogs',
    //   payload: {
    //     data: {
    //       memOrgCode: record.memOrgCode,
    //       digitalLotNoList: [record.digitalLotNo],
    //       isExportAll: 0,
    //       type: '2'
    //     }
    //   }
    // })
  }
  render() {
    const { loading, archivesAdministration: { list1 = [], pagination1 = {} } } = this.props;
    const { dataInfo = {}, filterHeight, visible1, filenameList } = this.state;
    const { current, pageSize } = pagination1;
    const org = getSession('org') || {};
    let filterData = [
      {
        key: 'd08CodeList', name: '党员类型', value: [{ key: '5', name: '入党申请人' }, { key: '4', name: '积极分子' }, { key: '3', name: '发展对象' }],
      },
      // {
      //   key: 'isIntegrality', name: '档案完整度', value: [{ key: '1', name: '完整' }, { key: '0', name: '不完整' }],
      // },
      // {
      //   key: 'digitalType', name: '档案类别', value: [{ key: '1', name: '绿色档案' }, { key: '2', name: '蓝色档案' }, { key: '3', name: '红色档案' }, { key: '99', name: '其他' }],
      // },
      // {
      //   key: 'isVolunteer', name: '入党志愿书', value: [{ key: '1', name: '已上传' }, { key: '0', name: '未上传' }],
      // },
      // {
      //   key: 'isSure', name: '党员身份认定', value: [{ key: '1', name: '已认定' }, { key: '0', name: '未认定' }],
      // },
    ];
   const content = (
      <div className={styles.content}>
        <div className={styles.rows}>
          <div style={{ backgroundColor: '#00CD29' }}></div>
          <div>已完成</div>
        </div>
        <div className={styles.rows}>
          <div style={{ backgroundColor: '#FF4343' }}></div>
          <div>未完成</div>
        </div>
        <div className={styles.rows}>
          <div style={{ backgroundColor: 'rgb(206, 206, 206)' }}></div>
          <div>未到达</div>
        </div>

      </div>
    );
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 60,
        align: 'center',
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1
        }
      },
      {
        title: '党员姓名',
        align: 'center',
        dataIndex: 'name',
        width: 100,
      },
      {
        title: '性别',
        dataIndex: 'sexName',
        width: 80,
        align: 'center'
      },
      {
        title: '党员类型',
        dataIndex: 'd08Name',
        width: 100,
        align: 'center'
      },
      {
        title: '所在党支部',
        dataIndex: 'orgName',
        width: 300,
        // render: (text, record) => {
        //   return (
        //     <Fragment>
        //       <a onClick={() => { }}>{text}</a>
        //     </Fragment>
        //   )
        // },
      },
      {
        title: <div>
        档案完整度
        <div>{content}</div>
        {/* <Popover content={content} title="">
          <LegacyIcon onClick={() => { }} style={{ cursor: 'pointer' }} type="question-circle" />
        </Popover> */}
      </div>,
        width: 360,
        children: [
          {
            title: '第一阶段',
            dataIndex: 'jd1',
            width: 60,
            render: (text, record) => {
              let find = (record?.digitalCompletenessVOList || []).find(i => i.d222Code == '1')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#00CD29', cursor: 'pointer' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', margin: '0 auto', background: '#FF4343', cursor: 'pointer' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#CECECE', cursor: 'pointer' }}></div>
                )
              }

            }
          },
          {
            title: '第二阶段',
            dataIndex: 'jd2',
            width: 60,
            render: (text, record) => {
              let find = (record?.digitalCompletenessVOList || []).find(i => i.d222Code == '2')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#00CD29', cursor: 'pointer' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', margin: '0 auto', background: '#FF4343', cursor: 'pointer' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#CECECE', cursor: 'pointer' }}></div>
                )
              }

            }
          },
          {
            title: '第三阶段',
            dataIndex: 'jd3',
            width: 60,
            render: (text, record) => {
              let find = (record?.digitalCompletenessVOList || []).find(i => i.d222Code == '3')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#00CD29', cursor: 'pointer' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', margin: '0 auto', background: '#FF4343', cursor: 'pointer' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#CECECE', cursor: 'pointer' }}></div>
                )
              }

            }
          },
          {
            title: '第四阶段',
            dataIndex: 'jd4',
            width: 60,
            render: (text, record) => {
              let find = (record?.digitalCompletenessVOList || []).find(i => i.d222Code == '4')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#00CD29', cursor: 'pointer' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', margin: '0 auto', background: '#FF4343', cursor: 'pointer' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#CECECE', cursor: 'pointer' }}></div>
                )
              }

            }
          },
          {
            title: '第五阶段',
            dataIndex: 'jd5',
            width: 60,
            render: (text, record) => {
              let find = (record?.digitalCompletenessVOList || []).find(i => i.d222Code == '5')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#00CD29', cursor: 'pointer' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', margin: '0 auto', background: '#FF4343', cursor: 'pointer' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#CECECE', cursor: 'pointer' }}></div>
                )
              }

            }
          },
          {
            title: '其他',
            dataIndex: 'jd5',
            width: 60,
            render: (text, record) => {
              let find = (record?.digitalCompletenessVOList || []).find(i => i.d222Code == '99')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#00CD29', cursor: 'pointer' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', margin: '0 auto', background: '#FF4343', cursor: 'pointer' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', margin: '0 auto', background: '#CECECE', cursor: 'pointer' }}></div>
                )
              }

            }
          },
        ]
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        render: (text, record) => {
          return (
            <>
             <a onClick={() => this.digitalArchives(record)}>电子档案</a>
             <Divider type="vertical" />
             <Dropdown overlay={(
              <Menu>
                {/* <Menu.Item key="1" >
                <a onClick={() => this.digitalArchives(record)}>电子档案</a>
                </Menu.Item> */}
                <Menu.Item key="2" >
                {
                      record?.exportStatus == 0 &&
                      <a onClick={() => this.daexp(record)}>档案导出申请</a>
                    }
                    {
                      record?.exportStatus == 1 &&
                      <span style={{ color: '#CECECE' }}>导出申请中</span>
                    }
                    {
                      record?.exportStatus == 2 &&
                      <a onClick={() => this.daexp(record)}>档案导出</a>
                    }
                </Menu.Item>
                <Menu.Item key="3" >
                <a onClick={() => this.logrxp(record)}>日志导出</a>
                </Menu.Item>
               
              </Menu>)}
            >
               <a className="ant-dropdown-link">
                  业务操作 <DownOutlined />
                </a>
            </Dropdown>
            </>
          )
          return (
            <Fragment>
              <a onClick={() => this.digitalArchives(record)}>电子档案</a>
              <Divider type="vertical" />
              <a onClick={() => this.daexp(record)}>档案材料导出</a>
              <Divider type="vertical" />
              <a onClick={() => this.logrxp(record)}>操作日志导出</a>
              {/* <Divider type="vertical" />
              <a onClick={() => { this.setState({ visible: true }) }}>{true ? "未认定" : "取消认定"}</a> */}
            </Fragment>
          )
        },
      },
    ];

    return (
      <div>
        <NowOrg extra={
          <React.Fragment>
            {/* <Button onClick={this.exportInfo} loading={this.state.jcInfoDownload}>导出</Button> */}
            <Search style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'} />
          </React.Fragment>
        } />
        <RuiFilter
          data={filterData}
          showLine={3}
          onChange={this.filterChange}
        />
        <WhiteSpace />
        <WhiteSpace />
        <ListTable scroll={{ y: filterHeight }}
         
          columns={columns} data={list1} pagination={pagination1} onPageChange={this.onPageChange} />
        <ExportInfo wrappedComponentRef={e => this['jcInfo'] = e}
          tableName={''}
          noModal={true}
          tableListQuery={{ memName: this.state.name, type: this.state.type, positionOrgCode: org['orgCode'], positionOrgName: org['name'], }}
          action={'/api/representative/export'}
        />
        <ElectronicArchives ref={e => this['ElectronicArchives'] = e} />
        <EditDevlop wrappedComponentRef={e => this['editDevlop'] = e} onsubmit={this.getList} {...this.props} tipMsg={this.state.tipMsg} />
        <Modal
          title={'档案材料'} visible={visible1} onOk={this.handleOk1} onCancel={this.handleCancel1}
        >
          <div>
            {
              filenameList.childs?.map((item, index) => {
                return (
                  <div style={{ display: 'flex' }}>
                    <div style={{ paddingRight: '4px' }}>{item.d222Name}</div>
                    {
                      item.integrality ?
                        <div><LegacyIcon style={{ color: 'green' }} type="check" /></div> :
                        <div><LegacyIcon style={{ color: 'red' }} type="close" /></div>
                    }
                  </div>
                )
              })
            }
          </div>
        </Modal>

           <ExportInfo wrappedComponentRef={e => this['downloadRef'] = e} tableName={''} noModal={true} tableListQuery={{
                  memOrgCode: this.state?.record?.memOrgCode,
                  digitalLotNoList: [this.state.record?.digitalLotNo],
                  isExportAll: 0,
                  type: '2'
                }} action={this.state.action||''} />
                 <Application ref={e => this['application'] = e} onsubmit={this.getList}/>
      </div>
    );
  }
}
