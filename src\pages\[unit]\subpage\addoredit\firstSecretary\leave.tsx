import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Input, Modal, Radio, Row, Switch, InputNumber, Select } from 'antd';
import Tip from '@/components/Tip';
import moment from 'moment';
import { formLabel, getIdCardInfo, correctIdcard } from '@/utils/method.js';
import Date from '@/components/Date';
import { residentUpdate, residentSave, residentBackOut } from './services';
import _isEmpty from 'lodash/isEmpty';
const FormItem = Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      key: '', //PanelTable的index
      type: undefined,
    };
  }
  showModal = (record) => {
    this.setState({
      visible: true,
      record
    });
  };

  handleOk = () => {
    const { basicInfo = {} } = this.props.unit;
    const { children, title, memInfo = {}, dataInfo = {} } = this.props;
    this.props.form.validateFieldsAndScroll(async (err, val) => {

      if (!err) {
        const { code = 500 } = await residentUpdate({
          data: {
            ...this.state.record,
            endDate: val.endDate ? moment(val.endDate).valueOf() : '',
          },
        });
        if (code === 0) {
          Tip.success('操作提示','操作成功');
          this.handleCancel();
        }
      }
    });
  };
  handleCancel = () => {
    this.props.onClose();
    this.setState({
      visible: false,
    });
  };
  render() {
    const { getFieldDecorator } = this.props.form;
    const { children, title, dataInfo = {}, tipMsg = {}, memInfo = {} } = this.props;
    const { basicInfo = {} } = this.props.unit;
    const { d04Code = '' } = basicInfo;
    return (
      <React.Fragment>
        {children
          ? React.cloneElement(children as any, {
              onClick: this.showModal,
              key: 'container',
            })
          : null}
        {this.state.visible && (
          <Modal
            title={title || '请输入标题'}
            visible={this.state.visible}
            onOk={this.handleOk}
            onCancel={this.handleCancel}
            width={500}
            className="add_member_modal"
            maskClosable={false}
          >
            <Form {...formItemLayout}>
              <FormItem label={formLabel('驻村结束时间', tipMsg['endDate'])}>
                {getFieldDecorator('endDate', {
                  rules: [{ required: true, message: '请填写驻村结束时间' }],
                })(<Date />)}
              </FormItem>
            </Form>
          </Modal>
        )}
      </React.Fragment>
    );
  }
}
export default Form.create()(index);
