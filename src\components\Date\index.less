.date{
  width: 100%;
  height: 100%;
  cursor: text;
  [contenteditable]:focus{outline: none;}
  span,input{
    display: inline-block;
    white-space: pre;
    border: none;
    outline: none;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type="number"]{ 
    -moz-appearance: textfield;
  }
  input::-webkit-input-placeholder{
    color:#bfbfbf
  }
  input::-moz-placeholder{
    color:#bfbfbf
  }
  input:-ms-input-placeholder{
    color:#bfbfbf
  }
}
.disabledBg{
  background: #F5F5F5;
  input {
    background: #F5F5F5;
  }
}
.tip{
  float: right;
  color: orange;
}
