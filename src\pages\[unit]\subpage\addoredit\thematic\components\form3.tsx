// 推动发展壮大村级集体经济情况
import React, { Fragment, useEffect, useState } from 'react';
import { Button, Form, InputNumber, Row, Col, Switch, Divider, Checkbox } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import { formItemLayout, formItemLayout2, formItemLayout3 } from './config';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import Tip from '@/components/Tip';
import { saveForm3, findForm } from '@/pages/[unit]/services/thematic';
import _cloneDeep from 'lodash/cloneDeep';

const index = (props: any) => {
  const [form] = Form.useForm();
  const { selectTab, basicInfo } = props;
  const { code: unitCode = '' } = basicInfo;
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);

  const onFinish = async (e) => {
    setLoading(true);
    let val = _cloneDeep(e);
    ['hasEconomicOrganization', 'hasEconomicOrganizationHead'].map((it) => {
      val[it] = val[it] ? 1 : 0;
    });
    const { code = 500 } = await saveForm3({
      data: {
        unitCode,
        ...val,
      },
    });
    setLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '修改成功');
    }
  };
  const getInfo = async () => {
    const { code = 500, data = {} } = await findForm({
      unitCode,
      type: '3',
    });
    if (code === 0) {
      setQuery(data);
      form.setFieldsValue(data);
    }
  };
  useEffect(() => {
    getInfo();
  }, []);

  return (
    <Fragment>
      <Form form={form} {...formItemLayout3} onFinish={onFinish}>
        <Row>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasEconomicOrganization"
              label="有集体经济组织的村"
              initialValue={query['hasEconomicOrganization'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
            {/* <Checkbox.Group options={options} onChange={() => {}} /> */}
          </Col>
          {/* <Col span={24}>
            <div style={{ padding: '0 4%' }}>
              <Divider plain>来源</Divider>
            </div>
          </Col> */}
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasEconomicOrganizationHead"
              label="村党组织书记担任村级集体经济组织负责人的村"
              initialValue={query['hasEconomicOrganizationHead'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="eliminateEconomicCounty" label="已消除集体经济薄弱村空壳村的县">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="fiftyEconomicCounty"
              label="超过50%村没有集体经济收入的县（市、区、旗）"
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="compiledPlanProvince"
              label="已编制消除集体经济薄弱村空壳村规划的省（区、市）"
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="developmentMeasureCounty"
              label="统一编制集体经济发展规划、逐村研究落实发展措施的县（市、区、旗）"
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
        </Row>
        <div style={{ textAlign: 'center' }}>
          <WhiteSpace />
          <Button
            type={'primary'}
            htmlType={'submit'}
            icon={<LegacyIcon type={'check'} />}
            // onClick={() => {}}
            style={{ marginRight: 16 }}
            loading={loading}
          >
            保存
          </Button>
          {/* <Button
            type={'primary'}
            danger
            htmlType={'button'}
            icon={<LegacyIcon type={'delete'} />}
            onClick={() => {}}
          >
            取消
          </Button> */}
        </div>
      </Form>
    </Fragment>
  );
};
export default index;
