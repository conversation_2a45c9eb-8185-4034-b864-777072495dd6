import React, { Fragment, useImperativeHandle, useState } from 'react';
import Date from '@/components/Date';
import { Form, Input, Modal } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import { updateUnitCommittee } from '@/pages/[unit]/services';
import Tip from '@/components/Tip';
import moment from 'moment';

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const formItemLayout2 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const index = (props: any, ref) => {
  const { title = '标题', onOK, isVillageCommunity = false } = props;
  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleCancel = () => {
    setRecord({});
    setVisible(false);
    form.resetFields();
  };
  const onFinish = async (e) => {
    setConfirmLoading(true);
    const { code = 500 } = await updateUnitCommittee({
      data: {
        ...record,
        ...e,
        endDate: e.endDate ? moment(e.endDate).valueOf() : '',
      },
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOK && onOK(record);
    }
  };
  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      setRecord(query);
    },
    clear: () => {
      // clear();
    },
  }));
  return (
    <Modal
      title={'结束任期'}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={600}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      <Form form={form} {...formItemLayout} onFinish={onFinish}>
        <Form.Item name='endDate'
                   label="离开时间"
                   rules={[{ required: true, message: '离开时间' }]}
        >
          <Date/>
        </Form.Item>
        {
          isVillageCommunity &&
          <Fragment>
            <Form.Item
              noStyle
              name='d120Name'
              style={{ display: 'none' }}
            >
              <Input style={{ display: 'none' }}/>
            </Form.Item>
            <Form.Item name='d120Code'
                       label="离任原因"
                       rules={[{ required: true, message: '离任原因' }]}
            >
              <DictTreeSelect backType={'object'}
                              codeType={'dict_d120'}
                              onChange={e => {
                                form.setFieldsValue({
                                  d120Code: e.key,
                                  d120Name: e.name,
                                });
                              }}
                // initValue={dataInfo['d120Code']}
                              placeholder="请选择" parentDisable={true}/>
            </Form.Item>
          </Fragment>
        }
      </Form>
    </Modal>
  );
};
export default React.forwardRef(index);
