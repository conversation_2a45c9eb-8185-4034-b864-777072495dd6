/**
 * 多重党员services
 */
import request from "@/utils/request";
import qs from 'qs';
export function getList(params) {
  return request(`/api/mem/multiple/getList`,{
    method:'POST',
    body:params,
  });
}
export function addMemMultiple(params) {
  return request(`/api/mem/multiple/addMemMultiple`,{
    method:'POST',
    body:params,
  });
}
export function updateMemMultiple(params) {
  return request(`/api/mem/multiple/updateMemMultiple`,{
    method:'POST',
    body:params,
  });
}
export function findByCode(params) {
  return request(`/api/mem/multiple/findByCode?${qs.stringify(params)}`,{
    method:'Get',
  });
}
export function delMemMultiple(params) {
  return request(`/api/mem/multiple/delMemMultiple?${qs.stringify(params)}`,{
    method:'Get',
  });
}

