import React from 'react';
import { connect } from 'dva';
import { Progress, Spin } from 'antd';

interface propsType {
  dispatch?: any,
  callback?: Function,
  className?: string,
}

let num;
// @ts-ignore
@connect(({ login })=>({ login }),undefined,undefined,{forwardRef:true})
class index extends React.Component<propsType, any> {
  constructor(props) {
    super(props);
    this.state = {
      ratio: 0,
      currDataName: '',
      spinning: false,
    };
  };

  componentWillUnmount(): void {
    clearInterval(num);
  }

  getProgress = (type,projectName = '') => { //增加标识符projectName，因为进度接口可能不是一个接口，在model进行区别
    this.setState({
      spinning: true,
    });
    num = setInterval(() => {
      this.props.dispatch({
        type: 'login/getProgress',
        payload: {
          type,projectName
        }
      }).then((res = { ratio: '100', currDataName: ''}) => {
        const { ratio, currDataName } = res;
        this.setState({
          ratio,
          currDataName,
        });
        if (ratio === '100') {
          clearInterval(num);
          setTimeout(() => {
            this.props.callback && this.props.callback(res);
            this.setState({
              spinning: false,
              ratio: 0,
              currDataName: ''
            });
          }, 500);
        }
      })
    }, 1500);
  };
  render(): React.ReactNode {
    const { ratio, currDataName, spinning  } = this.state;
    const {className}=this.props;
    return (
      <Spin
        spinning={spinning}
        indicator={
          <React.Fragment>
            <span style={{ position: 'absolute', left: 0, right: 0, top: '50%'}}>
              <p>{currDataName}</p>
              <Progress type='line' strokeColor={{ from: '#108ee9', to: '#87d068' }} percent={parseInt(ratio)}/>
            </span>
          </React.Fragment>
        }
        wrapperClassName={className}
      >
        {
          this.props.children
        }
      </Spin>
    );
  }
}

export default index;
