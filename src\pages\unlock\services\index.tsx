import request from '@/utils/request';
import qs from 'qs';
export async function record(params) {
  return request('/api/lock/record', {
    method: 'POST',
    body: params
  });
}

export async function refuse(params) {
  return request('/api/lock/refuse', {
    method: 'POST',
    body: params
  });
}

export function unlock(params) {
  return request(`/api/lock/unlock`,{
    method:'POST',
    body:params,
  });
}
