// 流入管理-未纳入-接受
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input } from 'antd';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _isObject from 'lodash/isObject';
import OrgSelect from '@/components/OrgSelect';
import SearchOrg from '@/components/SearchOrg';
import DictTreeSelect from '@/components/DictTreeSelect';
import Date from '@/components/Date';
import Tip from '@/components/Tip';
import { delDevelop } from '@/pages/developMem/services/index';
import { inManageReceive } from '../../service/index';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      basicInfo: {},
      visible: false,
      timeKey: moment().valueOf(),
      confirmLoading: false,
      showInUnitD16Code: false,
      flowMemTypeDisabled: [],
      flowMemTypeCode: [],
      flowMemTypeName: []
    };
  }
  handleOk = () => {
    const { onOk } = this.props;
    const { basicInfo, flowMemTypeCode, flowMemTypeName } = this.state;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if (!_isEmpty(val.inOrgCode)) {
          if (typeof val.inOrgCode === 'object') {
            val.inOrgName = val.inOrgCode[0].name;
            val.inOrgCode = val.inOrgCode[0].code;
          }
        }
        if (!_isEmpty(val.inOrgD04Code)) {
          if (typeof val.inOrgD04Code === 'object') {
            val.inOrgD04Name = val.inOrgD04Code.name;
            val.inOrgD04Code = val.inOrgD04Code.key;
          }
        }

        if (!_isEmpty(val.inUnitD04Code)) {
          if (typeof val.inMemD09Code === 'object') {
            val.inUnitD04Name = val.inUnitD04Code.name;
            val.inUnitD04Code = val.inUnitD04Code.key;
          }
        }
        if (!_isEmpty(val.inMemD09Code)) {
          if (typeof val.inMemD09Code === 'object') {
            val.inMemD09Name = val.inMemD09Code.name;
            val.inMemD09Code = val.inMemD09Code.key;
          }
        }
        if (!_isEmpty(val.inReceivingTime)) {
          val.inReceivingTime = moment(val.inReceivingTime).valueOf();
        }
        if (flowMemTypeName) {
          if (flowMemTypeName.includes('农民工')) {
            const nmgIndex = flowMemTypeName.indexOf('农民工')
            flowMemTypeName.splice(nmgIndex, 1)
            flowMemTypeCode.splice(nmgIndex, 1)
            val['lrdIsFarmer'] = 1
          }
          val['flowMemTypeName'] = flowMemTypeName.join(',');
          val['flowMemTypeCode'] = flowMemTypeCode.join(',');
        }
        console.log(val, "valvalvalval");
        this.setState(
          {
            confirmLoading: true,
          },
          async () => {
            const res = await inManageReceive({
              data: { code: basicInfo.code, orgTypeList: ['3', '4'], ...val },
            });
            this.setState({
              confirmLoading: false,
            });
            if (res.code === 0) {
              this.handleCancel();
              Tip.success('操作提示', '操作成功');
              onOk && onOk();
            }
          },
        );
      }
    });
  };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  open = (record) => {
    this.setState({
      visible: true,
      basicInfo: record,
      timeKey: moment().valueOf(),
      flowMemTypeCode: record?.flowMemTypeCode ? record.flowMemTypeCode.split(',') : [],
      flowMemTypeName: record?.flowMemTypeName ? record.flowMemTypeName.split(',') : []
    });
    // this.props.form.setFieldsValue(record);
    // if (`${record?.inOrgD04Code}`.startsWith('4')) {
    //   this.setState({
    //     showInUnitD16Code: true,
    //   });
    // }
    if (`${record?.memD09Code}`.startsWith('02') || `${record?.memD09Code}`.startsWith('03') || `${record?.memD09Code}`.startsWith('505') || `${record?.memD09Code}` == '504') {
      this.setState({
        showInMemD20Code: true,
      });
    } else {
      this.setState({
        showInMemD20Code: false,
      });
    }
  };
  destroy = () => {
    this.setState({
      basicInfo: {},
      contactPhone: undefined,
      showInUnitD16Code: false,
      showInMemD20Code: false,
      flowMemTypeDisabled: [],
      flowMemTypeCode: [],
      flowMemTypeName: []
    });
  };

  setFlowMemTypeItemsDisabled = async (value) => {
    const { form } = this.props;
    const flowMemType = form.getFieldValue('flowMemType')
    console.log(flowMemType, value, 'flowMemType');
    // 人员类别为必填项，字段如下，其中离退休人员、新就业群体、农民工可多选，
    // 未就业高校毕业生、其他为单选。
    if (value && value.length > 0) {
      // 判断是否有2和4，有则不能选择1
      // 2和4 为未就业高校毕业生、其他
      const filterData = value.filter((item) => item.key == 2 || item.key == 4)
      if (filterData.length > 0) {
        await this.setState({
          flowMemTypeCode: [filterData[0].key],
          flowMemTypeName: [filterData[0].name]
        })
        // 2. 直接修改子组件内部状态
        if (this['flowMemTypeRef']) {
          this['flowMemTypeRef'].changeValue([filterData[0].key]);
        }
        if (filterData[0].key == 2) {
          await this.setState({
            flowMemTypeDisabled: ['1', '4', '5', '31', '32', '33', '34', '35', '36']
          })
          return
        }
        if (filterData[0].key == 4) {
          await this.setState({
            flowMemTypeDisabled: ['1', '2', '5', '31', '32', '33', '34', '35', '36']
          })
          return
        }
      } else {
        await this.setState({
          flowMemTypeDisabled: [],
          flowMemTypeCode: value.map((item) => item.key),
          flowMemTypeName: value.map((item) => item.name)
        })
      }
    } else {
      await this.setState({
        flowMemTypeDisabled: [],
        flowMemTypeCode: undefined,
        flowMemTypeName: undefined
      })
    }
  }


  render() {
    const { form } = this.props;
    const { getFieldDecorator, setFieldsValue, getFieldValue } = form;
    const { visible, basicInfo, timeKey, confirmLoading, showInUnitD16Code, showInMemD20Code = false, flowMemTypeDisabled, flowMemTypeCode } = this.state;
    return (
      <Modal destroyOnClose title="接收提示" visible={visible} onOk={this.handleOk} maskClosable={false} onCancel={this.handleCancel} width={700} confirmLoading={confirmLoading}>
        {visible && (
          <Fragment key={timeKey}>
            <Form {...formItemLayout}>
              <FormItem label="流入地党支部">
                {getFieldDecorator('inOrgCode', {
                  initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['inOrgCode'],
                  rules: [{ required: true, message: '请选择流入地党支部' }],
                })(
                  <OrgSelect
                    listType="orgAndFlow"
                    otherListQuery={{}}
                    showFilterData={false}
                    // oorg={{orgCode: basicInfo, subordinate:1}}
                    orgTypeList={['3', '4']}
                    initValue={basicInfo['inOrgCode'] || undefined}
                    placeholder={'请选择流入地党支部'}
                    onChange={(e: any) => {
                      const { d04Code = undefined, contactPhone = undefined, contacter = undefined } = e[0];
                      this['inOrgD04CodeRef'].setState({ value: d04Code });
                      this.setState({
                        d04Code,
                        contactPhone,
                        // showInUnitD16Code: `${d04Code}`.startsWith('4') ? true : false,
                      });
                      // setFieldsValue({ inOrgPhone: contactPhone, inOrgD04Code: d04Code });
                      setFieldsValue({ inOrgPhone: contactPhone, inOrgUser: contacter });
                    }}
                  />,
                )}
              </FormItem>
              <FormItem label="流入地联系方式">
                {getFieldDecorator('inOrgPhone', {
                  initialValue: _isEmpty(basicInfo['inOrgPhone']) ? undefined : basicInfo['inOrgPhone'],
                  rules: [{ required: true, message: '流入地联系方式' }],
                })(<Input placeholder="流入地联系方式" maxLength={20} />)}
              </FormItem>
              <FormItem label="流入地联系人">
                {getFieldDecorator('inOrgUser', {
                  initialValue: _isEmpty(basicInfo['inOrgUser']) ? undefined : basicInfo['inOrgUser'],
                  rules: [{ required: true, message: '流入地联系人' }],
                })(<Input placeholder="流入地联系人" maxLength={50} />)}
              </FormItem>
              {/* 4.接收功能中，流入党支部单位类别类别， 跟流入基层党委使用同一个字典表（2025.02.10流动党员升级功能改造） */}
              <FormItem label="流入党支部单位类别">
                {getFieldDecorator('inOrgD04Code', {
                  initialValue: _isEmpty(basicInfo['inOrgD04Code']) ? undefined : basicInfo['inOrgD04Code'],
                  rules: [{ required: true, message: '流入党支部单位类别' }],
                })(
                  <DictTreeSelect
                    ref={(e) => (this['inOrgD04CodeRef'] = e)}
                    // onChange={(e) => {
                    //   const { key = '' } = e;
                    //   if (key.startsWith('4')) {
                    //     this.setState({
                    //       showInUnitD16Code: true,
                    //     });
                    //   } else {
                    //     this.setState({
                    //       showInUnitD16Code: false,
                    //     });
                    //   }
                    // }}
                    parentDisable={true}
                    // codeType={'dict_d04'}
                    codeType={'dict_d205'}
                    backType={'object'}
                    initValue={_isEmpty(basicInfo['inOrgD04Code']) ? undefined : basicInfo['inOrgD04Code']}
                  />,
                )}
              </FormItem>
              {/* 流入党支部单位类别为 企业4开头的字典表时,展示经济类型 */}
              {/* 与现在 流入党支部单位类别 字典表逻辑不符，暂时注释 */}
              {/* {showInUnitD16Code && (
                <FormItem label={'经济类型'}>
                  {getFieldDecorator('inUnitD16Code', {
                    initialValue: _isEmpty(basicInfo['inUnitD16Code'])
                      ? undefined
                      : basicInfo['inUnitD16Code'],
                    rules: [{ required: true, message: '请选择经济类型' }],
                  })(
                    <DictTreeSelect
                      // disabled={readOnly}
                      parentDisable={true}
                      codeType={'dict_d16'}
                      // backType={'object'}
                      initValue={
                        _isEmpty(basicInfo['inUnitD16Code'])
                          ? undefined
                          : basicInfo['inUnitD16Code']
                      }
                    />,
                  )}
                </FormItem>
              )} */}
              <FormItem label="党员流出前原工作岗位">
                {getFieldDecorator('inMemD09Code', {
                  initialValue: _isEmpty(basicInfo['memD09Code']) ? undefined : basicInfo['memD09Code'],
                  rules: [{ required: true, message: '请输入党员流出前原工作岗位' }],
                })(
                  <DictTreeSelect
                    placeholder="请输入党员流出前原工作岗位"
                    parentDisable={true}
                    // codeType={'dict_d09'}
                    codeType={'dict_d208'}
                    backType={'object'}
                    initValue={_isEmpty(basicInfo['memD09Code']) ? undefined : basicInfo['memD09Code']}
                    onChange={(e) => {
                      const { key = '' } = e;
                      if (`${key}`.startsWith('02') || `${key}`.startsWith('03') || `${key}`.startsWith('505') || `${key}` == '504') {
                        this.setState({
                          showInMemD20Code: true,
                        });
                      } else {
                        this.setState({
                          showInMemD20Code: false,
                        });
                      }
                    }}
                  />,
                )}
              </FormItem>
              {/* // 工作岗位以02、03、505开头的，等于504的会填新社会阶层 */}
              {showInMemD20Code && (
                <FormItem label="新社会阶层">
                  {getFieldDecorator('inMemD20Code', {
                    initialValue: _isEmpty(basicInfo['inMemD20Code']) ? undefined : basicInfo['inMemD20Code'],
                    rules: [{ required: true, message: '请选择新社会阶层' }],
                  })(
                    <DictTreeSelect
                      placeholder="请选择新社会阶层"
                      parentDisable={true}
                      codeType={'dict_d20'}
                      // backType={'object'}
                      initValue={_isEmpty(basicInfo['inMemD20Code']) ? undefined : basicInfo['inMemD20Code']}
                    />,
                  )}
                </FormItem>
              )}
              <FormItem label="党员联系方式">
                {getFieldDecorator('inMemPhone', {
                  initialValue: _isEmpty(basicInfo['memPhone']) ? undefined : basicInfo['memPhone'],
                  rules: [{ required: true, message: '党员联系方式' }],
                })(<Input placeholder="党员联系方式" maxLength={20} />)}
              </FormItem>
              <FormItem label="接收时间">
                {getFieldDecorator('inReceivingTime', {
                  initialValue: _isEmpty(basicInfo['inReceivingTime']) ? undefined : moment(basicInfo['inReceivingTime']),
                  rules: [{ required: true, message: '接收时间' }],
                })(<Date />)}
              </FormItem>
              {(() => {
                console.log("🚀 ~ index ~ render ~ flowMemTypeCode111111111:", flowMemTypeCode)
                return (<FormItem label="人员类别">
                  <DictTreeSelect
                    ref={(e) => this['flowMemTypeRef'] = e}
                    treeCheckable={true}
                    backType={'object'}
                    codeType={'dict_flow_mem_type'}
                    initValue={flowMemTypeCode}
                    placeholder={'请选择人员类别'}
                    parentDisable={true}
                    itemsDisabled={flowMemTypeDisabled}
                    onChange={(value) => this.setFlowMemTypeItemsDisabled(value)}
                  />
                </FormItem>)
              })()}
              {(flowMemTypeCode && flowMemTypeCode.includes('4')) && <FormItem label="人员类型备注">
                {getFieldDecorator('flowMemTypeRemark', {
                  initialValue: _isEmpty(basicInfo['flowMemTypeRemark']) ? undefined : basicInfo['flowMemTypeRemark'],
                  rules: [{ required: true, message: '人员类型备注' }],
                })(<Input placeholder="请输入人员类型备注" />)}
              </FormItem>}
              {(flowMemTypeCode && flowMemTypeCode.includes('36')) && <FormItem label="人员类型新就业备注">
                {getFieldDecorator('flowMemTypeNewRemark', {
                  initialValue: _isEmpty(basicInfo['flowMemTypeNewRemark']) ? undefined : basicInfo['flowMemTypeNewRemark'],
                  rules: [{ required: true, message: '人员类型新就业备注' }],
                })(<Input placeholder="请输入人员类型新就业备注" />)}
              </FormItem>}
            </Form>
          </Fragment>
        )}
      </Modal>
    );
  }
}
export default Form.create()(index);
