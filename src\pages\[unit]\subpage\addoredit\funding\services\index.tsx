import request from "@/utils/request";
import qs from 'qs';

// 列表
export function getList(params) {
  return request(`/api/unit/siteConditions/getList`,{
    method:'POST',
    body:params,
  });
}

// 新增
export function add(params) {
  return request(`/api/unit/siteConditions/add`,{
    method:'POST',
    body:params,
  });
}

// 编辑
export function updateSecondary(params) {
  return request(`/api/unit/siteConditions/add`,{
    method:'POST',
    body:params,
  });
}

// 删除
export function delSecondary(params) {
  return request(`/api/unit/siteConditions/delByCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}
