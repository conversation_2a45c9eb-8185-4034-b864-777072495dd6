{"name": "the_party_platform", "version": "3.0.7", "private": true, "main": "dist/main.js", "homepage": ".", "description": "党建", "author": "HQA", "scripts": {"start": "set NODE_OPTIONS=--openssl-legacy-provider &&umi dev", "build": "set NODE_OPTIONS=--openssl-legacy-provider &&umi build", "build-noCheck": "cross-env idCheck=false umi build", "test": "umi test", "lint:es": "eslint --ext .js src mock tests", "lint:ts": "tslint \"src/**/*.ts\" \"src/**/*node.tsx\"", "precommit": "lint-staged", "electron-start": "cross-env NODE_ENV=production electron dist/main.js", "dis-win64": "electron-builder --win --x64"}, "dependencies": {"@ant-design/compatible": "^1.0.8", "@ant-design/icons": "^4.6.2", "@types/react-signature-canvas": "^1.0.5", "ant-design-pro": "^2.3.1", "antd": "4.15.3", "autofit.js": "^3.1.0", "base-64": "^0.1.0", "crypto-js": "^4.1.1", "dom-to-image": "^2.6.0", "dva-model-extend": "^0.1.2", "echarts": "^4.2.1", "echarts-for-react": "^2.0.15-beta.0", "exceljs": "^4.2.0", "file-saver": "^2.0.5", "image-conversion": "^2.1.1", "intro.js": "^4.2.2", "intro.js-react": "^0.5.0", "lib-flexible": "^0.3.2", "lodash": "^4.17.11", "luckyexcel": "^1.0.1", "md5": "^2.2.1", "moment": "^2.30.1", "postcss-pxtorem": "5.1.1", "qrcode.react": "^0.9.3", "rc-queue-anim": "^1.6.12", "rc-seamless-scroll": "^1.0.0", "react": "^16.14.0", "react-countup": "^4.1.3", "react-cropper": "^2.3.3", "react-datasheet": "^1.3.10", "react-dom": "^16.14.0", "react-image-crop": "^11.0.10", "react-infinite-scroller": "^1.2.4", "react-lazyload": "^3.2.1", "react-pageflip": "^2.0.3", "react-player": "^2.11.0", "react-signature-canvas": "^1.0.6", "react-sortable-hoc": "^1.8.3", "react-sound": "^1.2.0", "react-splitter-layout": "^4.0.0", "react-window": "^1.8.11", "react-zoom-pan-pinch": "^3.7.0", "tesseract.js": "^5.0.5", "wangeditor": "^3.1.1", "xxtea": "^0.2.1"}, "devDependencies": {"@types/jest": "^23.3.12", "@types/react": "^16.7.18", "@types/react-dom": "^16.0.11", "@types/react-test-renderer": "^16.0.3", "@umijs/preset-react": "^1", "array-move": "^1.0.0", "babel-eslint": "^8.2.6", "cross-env": "^7.0.3", "eslint": "^5.4.0", "eslint-config-umi": "^1.4.5", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-react": "^7.11.1", "husky": "^0.14.3", "lint-staged": "^7.2.2", "qs": "^6.7.0", "react-test-renderer": "^16.7.0", "tslint": "^5.12.0", "tslint-eslint-rules": "^5.4.0", "tslint-react": "^3.6.0", "umi": "3.3.9"}, "lint-staged": {}, "engines": {"node": ">=8.0.0"}, "build": {"appId": "my-first-tool-app", "files": ["dist/**/*"], "productName": "贵州党建", "directories": {"output": "build"}, "win": {"icon": "dist/favicon.ico"}, "nsis": {"oneClick": false, "perMachine": true, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}