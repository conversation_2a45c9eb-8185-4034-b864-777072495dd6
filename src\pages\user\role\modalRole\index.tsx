/**
 * 权限
 */ 
import React from 'react';
import {Modal,Tree} from "antd";
import {isEmpty} from '@/utils/method.js';
const TreeNode=Tree.TreeNode;
interface propsType {
  menuData:Array<object>,
  userRole:Array<string>,
  editObj:object,
  onOK?:any
  disabled?:string
}
export default class ModalRole extends React.Component<propsType,{}> {
  static open(){};
  static close(){};
  constructor(props){
    super(props);
    this.state={
      visible:false,
      edit:false,
    };
    ModalRole.close=this.handleCancel;
    ModalRole.open=this.show;
  }
  show=()=>{
    this.setState({
      visible:true,
    })
  };
  handleOk=()=>{
    let {onOK}=this.props;
    if(onOK){
      onOK(this.state['checkedKeys'],this.state['edit'],this.state['parentKey'])
    }
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      edit:false,
      checkedKeys:[],
    })
  };
  onCheck=(checkedKeys, e)=>{
    let parentKey:Array<object>=[];
    if (!isEmpty(e.halfCheckedKeys)) {
      e.halfCheckedKeys.map((item,index)=>{
        parentKey.push({id:item})
      })
    }
    this.setState({ checkedKeys,edit:true,parentKey });
  };
  renderTreeNodes = data => data.map((item) => {
    if (item.children) {
      return (
        <TreeNode title={item['name']} key={item['serverId']} dataRef={item}>
          {this.renderTreeNodes(item.children)}
        </TreeNode>
      );
    }
    return <TreeNode title={item['name']} key={item['serverId']} dataRef={item} />;
  });
  render(): React.ReactNode {
    let { menuData=[],editObj={},userRole=[] ,disabled}=this.props;
    let checkedKeys=this.state['checkedKeys'] || [];
    if(checkedKeys.length===0 && !this.state['edit']){
      checkedKeys=userRole;
    }
    return(
      <Modal
        title="权限管理"
        destroyOnClose
        maskClosable={false}
        visible={this.state['visible']}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
      >
        {
          this.state['visible'] && <React.Fragment>
            <p>角色名：{editObj['name']}</p>
            <div>
              <div style={{display:'inline-block',verticalAlign:'top'}}>
                菜单：
              </div>
              <div style={{display:'inline-block',maxHeight:'400px',overflow:'auto',width:'90%'}}>
                <div
                  //@ts-ignore
                  style={{pointerEvents:`${disabled}`}}
                >
                  <Tree
                    checkable
                    defaultExpandAll
                    checkedKeys={checkedKeys}
                    onCheck={this.onCheck}
                  >
                    {this.renderTreeNodes(menuData?menuData:[])}
                  </Tree>
                </div>
              </div>
            </div>
          </React.Fragment>
        }

      </Modal>
    );
  }
}
