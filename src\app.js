import { ApiAuthError, ApiError } from 'src/utils/errors';
import { notification } from 'antd';
import { getBrowserInfo } from '@/utils/method';
import '../public/fonts.css';
import React from 'react';
import RoutePreloadWrapper from '@/components/RoutePreloadWrapper';
import routePreloader from '@/utils/routePreloader';

const browser = getBrowserInfo();
if (browser['browser'] === 'chrome' && browser['ver'] && browser['ver'] < 49) {
  alert('浏览器版本过低，请更新浏览器版本');
}
if (browser['browser'] === 'firefox' && browser['ver'] && browser['ver'] < 45) {
  alert('浏览器版本过低，请更新浏览器版本');
}
if (browser['browser'] === 'edge' && browser['ver'] && browser['ver'] < 13) {
  alert('浏览器版本过低，请更新浏览器版本');
}
const buildMessage = `last build time: ${process.env.buildTime}`;
if (!window.location.pathname.startsWith('/qzs')) {
  console.log('非清镇市大屏');
  //设置延迟，不影响首屏渲染
  setTimeout(() => {
    let dataScript = ['/excel/plugins/js/plugin.js', '/excel/luckysheet.umd.js', '/excel/xlsx.full.min.js'];
    dataScript.forEach((val) => {
      let script = document.createElement('script');
      script.src = val;
      document.head.appendChild(script);
    });
    let dataLink = ['/excel/plugins/css/pluginsCss.css', '/excel/plugins/plugins.css', '/excel/css/luckysheet.css', '/excel/assets/iconfont/iconfont.css'];
    dataLink.forEach((val) => {
      let link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = val;
      document.getElementsByTagName('head')[0].appendChild(link);
    });
  }, 500);
  // window.location.reload();
}
if (process.env.hideConsole) {
  let keys = [];
  for (let key in window.console['__proto__']) {
    keys.push(key);
  }
  if (keys.length === 0) {
    for (let key in window.console) {
      keys.push(key);
    }
  }
  keys.forEach((key) => {
    if (key.match(/log|debug|error|info|warn|dir/)) {
      let tempFunction = console[key];
      (function (tempFunc) {
        console[`_${key}`] = function () {
          tempFunc.apply(console, arguments);
        }.bind(window); // eslint-disable-line
      })(tempFunction);
      window.console[key] = () => {};
    }
  });
  if (console['_info']) {
    console['_info'](buildMessage, browser);
  }
} else {
  console.info(buildMessage, browser);
}
// 非开发环境下执行此代码
if (process.env.NODE_ENV != 'development') {
  //当键盘按下时
  document.onkeydown = function () {
    //禁止F12
    if (window.event && window.event.keyCode == 123) {
      // alert("F12调试功能已被禁用");
      window.event.returnValue = false;
    }
    //禁止Ctrl+U查看源代码
    if (event.ctrlKey && window.event.keyCode == 85) {
      window.event.returnValue = false;
    }
    //禁止Ctrl+S网页另存为
    if (event.ctrlKey && window.event.keyCode == 83) {
      window.event.returnValue = false;
    }
  };
  //禁止右键、选择、复制
  document.addEventListener('contextmenu', function (event) {
    return (event.returnValue = false);
  });
  var obj = Object.create(null),
    t = Date.now();
  Object.defineProperty(obj, 'a', {
    get: function () {
      if (Date.now() - t > 100) {
        console.log('控制台打开了');
      }
    },
  });
  setInterval(function () {
    t = Date.now();
    (function () {}['constructor']('debugger')()); //debugger;
    console.log(obj.a);
  }, 200);
}
const pop = {
  error(message, doSomething = () => {}) {
    notification.error({
      message: '操作失败',
      description: message,
      duration: 3,
      onClose: function () {
        // lastErrorMessage = "";
      },
    });
  },
  destroy() {
    notification.destroy();
  },
};
window.addEventListener('sw.updated', () => {
  // 弹出提示，引导用户刷新页面
  alert('弹出提示，引导用户刷新页面');
});
// 路由预加载配置
const preloadConfig = {
  // 路由映射 - 根据 menu.js 配置的所有路由
  routeMap: {
    // 单位管理
    '/unit': () => import('@/pages/[unit]'),

    // 党组织管理
    '/org/list': () => import('@/pages/org/list'),
    '/org/lax': () => import('@/pages/org/lax'),
    '/org/special': () => import('@/pages/org/special'),
    '/org/party': () => import('@/pages/org/party'),
    '/org/hasDevelopOrg': () => import('@/pages/org/hasDevelopOrg'),

    // 党员管理
    '/mem/manage': () => import('@/pages/mem/manage'),
    '/mem/difficulty': () => import('@/pages/mem/difficulty'),
    '/mem/multiple': () => import('@/pages/mem/multiple'),
    '/mem/history': () => import('@/pages/mem/history'),
    '/mem/zy/manage': () => import('@/pages/mem/zy/manage'),

    // 入党申请人/发展党员
    '/developMem/develop': () => import('@/pages/developMem/develop'),
    '/developMem/apply': () => import('@/pages/developMem/apply'),
    '/developMem/active': () => import('@/pages/developMem/active'),
    '/developMem/object': () => import('@/pages/developMem/object'),
    '/developMem/zy/apply': () => import('@/pages/developMem/zy/apply'),
    '/developMem/zy/active': () => import('@/pages/developMem/zy/active'),
    '/developMem/zy/object': () => import('@/pages/developMem/zy/object'),
    '/developMem/out': () => import('@/pages/developMem/out'),
    '/developMem/into': () => import('@/pages/developMem/into'),
    '/developMem/mems': () => import('@/pages/developMem/mems'),
    '/developMem/plan': () => import('@/pages/developMem/plan'),

    // 流动党员
    '/flowMem/flowBack': () => import('@/pages/flowMem/flowBack'),
    '/flowMem/inflows': () => import('@/pages/flowMem/inflows'),
    '/flowMem/flowHistory': () => import('@/pages/flowMem/flowHistory'),
    '/flowMem/outflowManage': () => import('@/pages/flowMem/outflowManage'),
    '/flowMem/inflowManage': () => import('@/pages/flowMem/inflowManage'),
    '/flowMem/inflowOrganization': () => import('@/pages/flowMem/inflowOrganization'),

    // 关系转接
    '/transfer/outflows': () => import('@/pages/transfer/outflows'),
    '/transfer/inflows': () => import('@/pages/transfer/inflows'),
    '/transfer/historyout': () => import('@/pages/transfer/historyout'),
    '/transfer/historyin': () => import('@/pages/transfer/historyin'),

    // 党代表
    '/behalf/jcInfo': () => import('@/pages/behalf/jcInfo'),
    '/behalf/contactAgency': () => import('@/pages/behalf/contactAgency'),

    // 档案管理
    '/archivesAdministration': () => import('@/pages/archivesAdministration'),
    '/archivesAdministration/fileReview': () => import('@/pages/archivesAdministration/fileReview'),
    '/archivesAdministration/statistics': () => import('@/pages/archivesAdministration/statistics'),

    // 年度统计
    '/annualStatistics': () => import('@/pages/annualStatistics'),
    '/annualStatistics/instruction': () => import('@/pages/annualStatistics/instruction'),

    // 数据统计
    '/datStatistics': () => import('@/pages/datStatistics'),
    '/datStatistics/word': () => import('@/pages/datStatistics/word'),
    '/datStatistics/briefing': () => import('@/pages/datStatistics/briefing'),
    '/datStatistics/supervise': () => import('@/pages/datStatistics/supervise'),

    // 数据查询
    '/dataSearch': () => import('@/pages/dataSearch'),
    '/dataSearch/analysis': () => import('@/pages/dataSearch/analysis'),

    // 报表信息
    '/dataSheet/create': () => import('@/pages/dataSheet/create'),
    '/dataSheet/send': () => import('@/pages/dataSheet/send'),

    // 数据校验
    '/dataCheck': () => import('@/pages/dataCheck'),

    // 信息解锁
    '/unlock/locked': () => import('@/pages/unlock/locked'),
    '/unlock/unlock': () => import('@/pages/unlock/unlock'),
    '/unlock/lockLog': () => import('@/pages/unlock/lockLog'),

    // 系统管理
    '/user/user': () => import('@/pages/user/user'),
    '/user/lock': () => import('@/pages/user/lock'),
    '/user/role': () => import('@/pages/user/role'),
  },

  // 常用路由列表 - 这些路由会在应用启动时自动预加载
  // commonRoutes: [],
  commonRoutes: [
    // 核心管理页面 - 最常用的功能
    '/unit',
    '/org/list',
    '/org/lax',
    '/org/special',
    '/org/party',
    '/org/hasDevelopOrg',
    '/mem/manage',
    '/mem/difficulty',
    '/mem/multiple',
    '/mem/history',
    '/mem/zy/manage',
    '/developMem/apply',
    '/developMem/active',
    '/developMem/object',
    '/developMem/develop',
    '/developMem/zy/apply',
    '/developMem/zy/active',
    '/developMem/zy/object',
    '/developMem/out',
    '/developMem/into',
    '/developMem/mems',
    '/developMem/plan',

    // 流动党员
    '/flowMem/flowBack',
    '/flowMem/inflows',
    '/flowMem/flowHistory',
    '/flowMem/outflowManage',
    '/flowMem/inflowManage',
    '/flowMem/inflowOrganization',

    // 关系转接
    '/transfer/outflows',
    '/transfer/inflows',
    '/transfer/historyout',
    '/transfer/historyin',

    // 党代表
    '/behalf/jcInfo',
    '/behalf/contactAgency',
    '/annualStatistics',
    '/dataSearch',
    '/user/user',
  ],
};

// 设置路由映射
routePreloader.setRouteMap(preloadConfig.routeMap);

export function rootContainer(container) {
  return React.createElement(
    RoutePreloadWrapper,
    {
      enablePreload: true,
      commonRoutes: preloadConfig.commonRoutes,
    },
    container,
  );
}

export const dva = {
  config: {
    onError(err) {
      err.preventDefault();
      if (err instanceof ApiError) {
        console.log(`${err.name}：${err.message}`, err.url);
        if (err instanceof ApiAuthError) {
          // 错误做了 去重 所以会有一个同步函数 传入，确保该函数 和弹框 同时出现 OR 同时不出现
          // pop.error(err.message, () => dispatch({ type: 'auth/clearSession' }));
        } else {
          pop.destroy();
          pop.error(err.message);
        }
      } else {
        console.error('未处理的错误：', err);
      }
    },
  },
};
