import React, { useEffect, useState } from 'react';
import st from './index.less';
import { Col, Pagination, Row, Select, Spin } from 'antd';
import { pullFile } from '@/services';
import _isEmpty from 'lodash/isEmpty';
import { _history as router } from '@/utils/method';
import moment from 'moment';
import { screenlist, findByCode, findDomain, screensave } from '../../screen/services';
import { Form, Input, Tooltip, Tabs } from 'antd';
import { getSession } from '@/utils/session';
import Editor from '@/components/Editor';
import Tip from '@/components/Tip';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 0 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
};

const memSearch = (props: any) => {
  const {} = props;
  const { name, idcard, zzm } = router.location.query;
  const Sesion: any = getSession('sddj_mb') || {};

  const [form] = Form.useForm();

  const [info, setInfo] = useState<any>({});
  const [mbList, setMbList] = useState([]);
  const [mbKey, setMbKey] = useState<any>('1');
  const [text, setText] = useState<any>();

  const hadndleFinish = async (v) => {
    console.log('🚀 ~ v:', v);
  };

  const getInfo = async (record?) => {
    const res = await findByCode({ memCode: record.code });
    if (res.code == 0) {
      if (_isEmpty(res.data)) return;
      setInfo(res.data);
      // setText(res.data.answer);
    }
  };

  const getMbList = async () => {
    const res = await findDomain({ memCode: Sesion.code });
    if (res.code == 0) {
      setMbList(res.data);
      setText(res?.data?.[0]);
    }
  };
  const mubanChange = async (v) => {
    await setMbKey(v);
    await setText('');
    setText(mbList?.[v - 1] || '');
    console.log('🚀 ~ mbList?.[mbKey - 1]:', mbList?.[v - 1]);
  };
  const onChange = async (v) => {
    setText(v);
  };

  const submit = async () => {
    const res = await screensave({
      data: {
        answer: text,
        memCode: Sesion.code,
      },
    });
    if (res.code == 0) {
      Tip.success('操作提示', '保存成功');
    }
  };

  useEffect(() => {
    if (Sesion.code) {
      getInfo(Sesion);
      getMbList();
    }
  }, [Sesion.code]);
  return (
    <div className={st.searchMem}>
      <div className={st.muban}>
        <div className={st.name}>{info?.name || ''}</div>
        {/* <div className={st.select}>
          <Select
            style={{ width: '400px' }}
          >
            {mbList.map((it, index) => (
              <Select.Option value={(index + 1).toString()}>{`模板${index + 1}`}</Select.Option>
            ))}
          </Select>
        </div> */}
        <div className={st.mubanTab}>
          <Tabs activeKey={mbKey} type="card" onChange={mubanChange}>
            {mbList.map((it, index) => (
              <Tabs.TabPane tab={`模板${index + 1}`} key={index + 1}></Tabs.TabPane>
            ))}
          </Tabs>
          <div className={st.mubanBody}>
            {!!text ? (
              <Editor id={'acPlan123'} key={mbKey} onChange={onChange} init={text} />
            ) : (
              <Editor id={'acPlan123'} key={999} onChange={onChange} />
            )}
          </div>
        </div>

        <div className={st.btn} onClick={submit}>
          保 &nbsp; 存
        </div>
      </div>
    </div>
  );
};

export default memSearch;
