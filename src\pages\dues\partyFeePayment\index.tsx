/**
 * 党费交纳(2022.12.21)
 * */
 import React, { useState, useRef, useEffect } from 'react';
 import { Input, Select, Form, Modal, Tabs, Button, Divider, Popconfirm } from 'antd';
 import BasicInfo from './basicInfo'
 import StatisticsInfo from './statisticsInfo'

 const TabPane = Tabs.TabPane;
 const index = (props: any) => {
  const [activeTab, setActiveTab] = useState('1')
 
   return (
     <div>
       <Tabs defaultActiveKey={activeTab} onChange={(e)=>{
        setActiveTab(e)
       }}>
          <TabPane tab="基本信息" key="1"/>
          {/* <TabPane tab="统计信息" key="2"/> */}
        </Tabs>
        {activeTab==='1' && <BasicInfo/>}
        {activeTab==='2' && <StatisticsInfo/>}
     </div>
   );
 };
 // @ts-ignore
 export default index;
 