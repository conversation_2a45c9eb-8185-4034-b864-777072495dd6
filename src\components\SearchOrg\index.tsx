import React, { useState, Fragment } from 'react';
import { Select, Input, Form } from 'antd';
import { findOrgByName } from '@/services';
import Modal from 'antd/lib/modal/Modal';
import _isEmpty from 'lodash/isEmpty'

interface pType {
  onChange?: Function;
  params?: object;
  backType?: string;
  placeholder?: string;
  style?: any;
  children?: React.ReactElement;
  showOtherInfo?: any;
  ohterAction?: any;
}

export default function SearchOrg(props: pType) {
  const [data, setData] = useState([]);
  const [value, setValue] = useState();
  const [valueName, setValueName] = useState();
  const [valueObj, setValueObj] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const {
    onChange,
    backType = 'string',
    params = {},
    style = {},
    children,
    placeholder = '请输入关键字进行搜索匹配',
    ohterAction
  } = props;
  const action = (para) => {
    let _action = findOrgByName;
    if (ohterAction) {
      _action = ohterAction
    }
    _action({
      data: {
        pageNum: 1,
        pageSize: 100,
        ...para,
        ...params,
      },
    }).then((res) => {
      if (res['code'] == 0) {
        setData(res['data']['list']);
      }
    });
  };
  const handleSearch = (val) => {
    action({ orgName: val });
  };
  const handleChange = (val, option) => {
    setValue(val);
    setValueObj(option ? option.dataref : {});
  };
  const show = () => {
    setVisible(true);
  };
  const onOk = () => {
    setValueName(valueObj['name']);
    if (onChange) {
      if (backType == 'object') {
        onChange({ ...valueObj });
      } else {
        onChange(value, { ...valueObj });
      }
    }
    onCancel();
  };
  const onCancel = () => {
    setVisible(false);
  };
  return (
    <React.Fragment>
      {children ? (
        React.cloneElement(children as any, {
          onClick: show,
        })
      ) : (
        <Input.Search
          value={valueName}
          onClick={show}
          placeholder={placeholder}
          readOnly
          enterButton
        />
      )}
      <Modal
        title={'组织查询选择器'}
        destroyOnClose
        visible={visible}
        onOk={onOk}
        onCancel={onCancel}
        width={800}
        okText={'确定'}
        cancelText={'取消'}
      >
        <Select
          showSearch
          value={value}
          defaultActiveFirstOption={false}
          showArrow={false}
          filterOption={false}
          onSearch={handleSearch}
          onChange={handleChange}
          notFoundContent={null}
          placeholder={placeholder}
          style={{ width: '100%', ...style }}
        >
          {data.map((item) => (
            <Select.Option value={item['code'] || item['id']} dataref={item}>
              {item['parentName'] ? `${item['parentName']} -> ` : ''}
              {item['name']}
            </Select.Option>
          ))}
        </Select>
        {
          props.showOtherInfo == 'transfer' && !_isEmpty(valueObj) &&
          <Fragment>
            <div style={{ marginTop: 20 }}>
              <span>党组织名称：</span>{valueObj?.name}
            </div>
            <div style={{ marginTop: 20 }}>
              <span>联系人：</span>{valueObj?.contacter}
            </div>
            <div style={{ marginTop: 20 }}>
              联系电话：{valueObj?.contactPhone}
            </div>
            <div style={{ marginTop: 20 }}>
              上级党组织名称：{valueObj?.parentName}
            </div>
            {/* 没得这个字段 2025/2/16 屏蔽 */}
            {/* <div style={{marginTop:20}}>
              上级党组织联系方式：{''}
            </div> */}
          </Fragment>
        }
      </Modal>
    </React.Fragment>
  );
}
