import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Checkbox, Modal } from 'antd';
import ListTable from '@/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';
import styles from './index.less';
import moment from 'moment';
import SpinProgress from '@/components/SpinProgress';
import { isEmpty, fileDownloadHeader } from '@/utils/method';
import _isEmpty from 'lodash/isEmpty';
import { annualExport, getTableHeaderPegging } from '@/pages/annualStatistics/services';
import {
  newTextColumn,
  newTableOrg,
  newTableUnit,
  newTableDevMem,
  newTableFlowMem,
  newTextRewardMem,
  newrewardTextColumn,
  newTextOrgSlack,
  newTextOrgRecognition,
  newTextOrgIndustry,
  newdevelopStepLog,
  neworgPartCongress,
  neworgParty,
  newmemFlow,
  neworgTransfer,
  newexcellentTextColumn
} from "./static/staticData"

function formatTime(time, format = 'YYYY.MM') {
  if (time) {
    return moment(time).format(format);
  }
  return undefined;
}
function contactNameAndPhone(record) {
  if (!isEmpty(record['contact'])) {
    if (isEmpty(record['phone'])) {
      return record['contact'];
    } else {
      return `${record['contact']}/${record['phone']}`;
    }
  } else {
    if (isEmpty(record['phone'])) {
      return record['contact'];
    } else {
      return `${record['contact']}/${record['phone']}`;
    }
  }
}
const arr = new Array(45).fill('').map((it, index) => `${index + 1}`);
const disMem = new Array(85).fill('').map((it, index) => `${index + 1}`),
  disTransfer = arr,
  disPart = arr,
  disDevMem = arr,
  disFlowMem = arr,
  disRewardMem = arr,
  disOrgSlack = arr,
  disOrg = new Array(162).fill('').map((it, index) => `${index + 1}`),
  disOrgRecognition = arr,
  disTeam = arr,
  disOrgIndustry = arr,
  disUnit = new Array(252).fill('').map((it, index) => `${index + 1}`),
  disOrgPartCongress = arr,
  disDevStep = new Array(67).fill('').map((it, index) => `${index + 1}`),
  disFlow = arr;

const TableMem = [
  { key: 'ryxx', label: '人员基本信息' },
  { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'name', width: 70, fixed: true } },
  {
    key: '2',
    label: '身份证号码',
    value: {
      title: '身份证号码',
      dataIndex: 'idNum',
      width: 150,
      fixed: true,
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
          if (text.indexOf('*') > 0) {
            return text;
          }
          return <span>{newVal}</span>;
        } else {
          return '';
        }
      },
    },
  },
  {
    key: '3',
    label: '性别',
    value: {
      title: '性别',
      dataIndex: 'sex',
      width: 50,
      render: (text) => (text == '1' ? '男' : '女'),
    },
  },
  { key: '4', label: '年龄', value: { title: '年龄', dataIndex: 'age', width: 50 } },
  {
    key: '5',
    label: '民族',
    value: {
      title: '民族',
      dataIndex: 'ethnic',
      width: 80,
      render: (text) => (text == '01' ? '汉族' : '少数民族'),
    },
  },
  {
    key: '6',
    label: '出生年月',
    value: {
      title: '出生年月',
      dataIndex: 'birthdate',
      width: 80,
      render: (text) => formatTime(text),
    },
  },
  { key: '7', label: '年份', value: { title: '年份', dataIndex: 'year', width: 50 } },
  {
    key: '8',
    label: '统计层次',
    value: { title: '统计层次', dataIndex: 'countLevel', width: 100 },
  },
  {
    key: '74',
    label: '人员分布情况',
    value: { title: '人员分布情况', dataIndex: 'distributionName', width: 100 },
  },
  { key: '75', label: '人员类型', value: { title: '人员类型', dataIndex: 'typeName', width: 80 } },
  {
    key: '9',
    label: '参加工作时间',
    value: {
      title: '参加工作时间',
      dataIndex: 'joinWorkDate',
      width: 70,
      render: (text) => formatTime(text),
    },
  },
  {
    key: '10',
    label: '政治面貌',
    value: {
      title: '政治面貌',
      dataIndex: 'politicCountenance',
      width: 90,
      render: (text) => (text == '01' || text === '02' ? '中共党员' : '非中共党员'),
    },
  },
  {
    key: '11',
    label: '全日制学历',
    value: { title: '全日制学历', dataIndex: 'educationFullTimeName', width: 100 },
  },
  {
    key: '12',
    label: '最高学位',
    value: { title: '最高学位', dataIndex: 'educationDegreeName', width: 80 },
  },
  {
    key: '13',
    label: '最高学历',
    value: { title: '最高学历', dataIndex: 'educationName', width: 80 },
  },
  {
    key: '14',
    label: '上年度是否为领导成员',
    value: { title: '上年度是否为领导成员', dataIndex: 'isPreLeaderMem', width: 90 },
  },
  {
    key: '15',
    label: '是否兼任领导职务',
    value: { title: '是否兼任领导职务', dataIndex: 'isLeaderJob', width: 80 },
  },
  // {key:'16',label:'是否属于乡人大机关',value:{title:'是否属于乡人大机关',dataIndex:'isVillageNpc',width: 90}},
  {
    key: '76',
    label: '现工作单位及职务',
    value: { title: '现工作单位及职务', dataIndex: 'curWorkUnitAndJob', width: 200 },
  },
  // {key:'84',label:'是否纳入年度统计',value:{title:'是否纳入年度统计',dataIndex:'isYearCount',width: 80}},
  {
    key: '85',
    label: '是否具有2年以上基层工作经历',
    value: {
      title: '是否具有2年以上基层工作经历',
      dataIndex: 'addIsTwoYearWorkExpNew',
      width: 120,
    },
  },
  {
    key: '90',
    label: '任现职务层次年限',
    value: { title: '任现职务层次年限', dataIndex: 'jobCurLevelDateCount', width: 80 },
  },
  { key: 'rybhqk', label: '人员变化情况' },
  {
    key: '17',
    label: '变化情况',
    value: {
      title: '变化情况',
      dataIndex: 'changeStatus',
      width: 80,
      render: (text) => (text == '1' ? '新增人员' : text == '2' ? '减少人员' : '无变化'),
    },
  },
  {
    key: '18',
    label: '是否有2年基层工作经验',
    value: { title: '是否有2年基层工作经验', width: 100, dataIndex: 'addIsTwoYearWorkExp' },
  },
  {
    key: '19',
    label: '是否留学回归人员',
    value: { title: '是否留学回归人员', width: 80, dataIndex: 'addIsBackMem' },
  },
  {
    key: '20',
    label: '录用时间',
    value: {
      title: '录用时间',
      dataIndex: 'addTakeTime',
      width: 80,
      render: (text) => formatTime(text),
    },
  },
  {
    key: '21',
    label: '减少原因',
    value: { title: '减少原因', dataIndex: 'reduceReasonName', width: 80 },
  },
  {
    key: '82',
    label: '新增类型',
    value: { title: '新增类型', dataIndex: 'addＭemTypeName', width: 80 },
  },
  // {key:'82',label:'减少原因',value:{title:'减少原因',dataIndex:'reduceReasonName',width: 80}},
  {
    key: '22',
    label: '减少时职务层次',
    value: { title: '减少时职务层次', dataIndex: 'reduceJobLevelName', width: 80 },
  },
  {
    key: '23',
    label: '是否交流',
    value: { title: '是否交流', dataIndex: 'reduceIsSwap', width: 80 },
  },
  { key: 'zwjs', label: '职务（职级）晋升' },
  // {key:'77',label:'晋升类别',value:{title:'晋升类别',dataIndex:'promotionTypeName',width: 80}},
  {
    key: '24',
    label: '晋升时全日制学历',
    value: { title: '晋升时全日制学历', dataIndex: 'promotionFullTimeEducationName', width: 80 },
  },
  {
    key: '25',
    label: '晋升时最高学历',
    value: { title: '晋升时最高学历', dataIndex: 'promotionEducationName', width: 80 },
  },
  {
    key: '26',
    label: '晋升时间',
    value: {
      title: '晋升时间',
      dataIndex: 'promotionTime',
      width: 80,
      render: (text) => formatTime(text),
    },
  },
  {
    key: '27',
    label: '晋升时是否是中共党员',
    value: { title: '晋升时是否是中共党员', dataIndex: 'promotionIsCpcMember', width: 100 },
  },
  {
    key: '28',
    label: '晋升后职务层次',
    value: { title: '晋升后职务层次', dataIndex: 'promotionJobLevelName', width: 140 },
  },
  {
    key: '29',
    label: '晋升前职务层次',
    value: { title: '晋升前职务层次', dataIndex: 'promotionPreJobLevelName', width: 140 },
  },
  {
    key: '30',
    label: '前一职级任职时间',
    value: {
      title: '前一职级任职时间',
      dataIndex: 'promotionPreJobTime',
      width: 80,
      render: (text) => formatTime(text),
    },
  },
  // {key:'31',label:'晋升类别(废除)',value:{title:'晋升类别(废除)',dataIndex:'promotionType',width: 80}},
  {
    key: '32',
    label: '是否破格提拔',
    value: { title: '是否破格提拔', dataIndex: 'promotionIsPromoted', width: 80 },
  },
  {
    key: '33',
    label: '是否越级提拔',
    value: { title: '是否越级提拔', dataIndex: 'promotionIsLeapfrogPromoted', width: 80 },
  },
  {
    key: '34',
    label: '是否公开选拔',
    value: { title: '是否公开选拔', dataIndex: 'promotionIsPublicPromoted', width: 80 },
  },
  {
    key: '35',
    label: '是否竞争上岗',
    value: { title: '是否竞争上岗', dataIndex: 'promotionIsCompetition', width: 80 },
  },
  { key: 'zjjs', label: '职级晋升' },
  // {key:'36',label:'本年底享受职级待遇',value:{title:'本年底享受职级待遇',dataIndex:'jobCurTreatment',width: 100}},
  {
    key: '78',
    label: '本年底享受职级待遇',
    value: { title: '本年底享受职级待遇', dataIndex: 'jobCurTreatmentName', width: 100 },
  },
  // {key:'37',label:'现任职务类型',value:{title:'现任职务类型',dataIndex:'jobCurTypeName',width: 80}},
  {
    key: '38',
    label: '现任职务层次',
    value: { title: '现任职务层次', dataIndex: 'jobCurLevelName', width: 140 },
  },
  // {key:'39',label:'现任职务层次时间',value:{title:'现任职务层次时间',dataIndex:'jobCurLevelDate',width: 80,render:(text)=>formatTime(text)}},
  // {key:'40',label:'享受当前职级待遇时间(废除)',value:{title:'享受当前职级待遇时间(废除)',dataIndex:'jobCurTreatmentTime',width: 120,render:(text)=>formatTime(text)}},
  {
    key: '87',
    label: '本年度晋升职级待遇情况',
    value: { title: '本年度晋升职级待遇情况', dataIndex: 'jobCurYearTreatmentName', width: 110 },
  },
  { key: 'jlqk', label: '交流情况' },
  {
    key: '79',
    label: '交流前职务',
    value: { title: '交流前职务', dataIndex: 'swapPreJobName', width: 80 },
  },
  // {key:'80',label:'交流前职务层次',value:{title:'交流前职务层次',dataIndex:'swapPreLevelName',width: 80}},
  // {key:'41',label:'交流前职务',value:{title:'交流前职务',dataIndex:'swapPreJob',width: 120}},
  {
    key: '42',
    label: '交流时是否是中共党员',
    value: { title: '交流时是否是中共党员', dataIndex: 'swapIsCpcMember', width: 100 },
  },
  {
    key: '43',
    label: '交流时职务层次',
    value: { title: '交流时职务层次', dataIndex: 'swapJobLevelName', width: 120 },
  },
  {
    key: '44',
    label: '交流时间',
    value: {
      title: '交流时间',
      dataIndex: 'swapTime',
      width: 80,
      render: (text) => formatTime(text),
    },
  },
  {
    key: '45',
    label: '交流形式',
    value: { title: '交流形式', dataIndex: 'swapFormalName', width: 80 },
  },
  {
    key: '46',
    label: '是否任职满10年交流',
    value: { title: '是否任职满10年交流', dataIndex: 'swapIsFullTenYear', width: 90 },
  },
  // {key:'47',label:'交流去向',value:{title:'交流去向',dataIndex:'swapDirection',width: 80}},
  {
    key: '83',
    label: '交流去向',
    value: { title: '交流去向', dataIndex: 'swapDirectionName', width: 80 },
  },
  {
    key: '48',
    label: '是否回避交流',
    value: { title: '是否回避交流', dataIndex: 'swapIsAvoid', width: 80 },
  },
  {
    key: '88',
    label: '交流时全日制学历',
    value: { title: '交流时全日制学历', dataIndex: 'swapEducationFullTimeName', width: 80 },
  },
  {
    key: '89',
    label: '交流时最高学历',
    value: { title: '交流时最高学历', dataIndex: 'swapEducationName', width: 80 },
  },
  { key: 'khqk', label: '考核情况' },
  {
    key: '49',
    label: '考核时职务层次',
    value: { title: '考核时职务层次', dataIndex: 'assessJobLevelName', width: 80 },
  },
  {
    key: '50',
    label: '考核结果',
    value: { title: '考核结果', dataIndex: 'assessResultName', width: 80 },
  },
  { key: 'khqk', label: '奖惩情况' },
  {
    key: '51',
    label: '奖励项目',
    value: { title: '奖励项目', dataIndex: 'rpProjectName', width: 80 },
  },
  {
    key: '52',
    label: '奖励时职务层次',
    value: { title: '奖励时职务层次', dataIndex: 'rpRewardJobLevelName', width: 80 },
  },
  {
    key: '53',
    label: '是否有惩罚信息',
    value: { title: '是否有惩罚信息', dataIndex: 'rpIsPenalty', width: 80 },
  },
  {
    key: '54',
    label: '本年度是否有被免职',
    value: { title: '本年度是否有被免职', dataIndex: 'rpIsDismissal', width: 90 },
  },
  {
    key: '55',
    label: '免职时职务层次',
    value: { title: '免职时职务层次', dataIndex: 'rpDismissalJobLevelName', width: 80 },
  },
  {
    key: '56',
    label: '本年度是否有被降职',
    value: { title: '本年度是否有被降职', dataIndex: 'rpIsDemotion', width: 90 },
  },
  {
    key: '57',
    label: '降职时职务层次',
    value: { title: '降职时职务层次', dataIndex: 'rpDemotionJobLevelName', width: 80 },
  },
  {
    key: '58',
    label: '辞去领导职务',
    value: { title: '辞去领导职务', dataIndex: 'rpResignationLeaderJobName', width: 80 },
  },

  {
    key: '59',
    label: '辞去领导职务职务层次',
    value: {
      title: '辞去领导职务职务层次',
      dataIndex: 'rpResignationLeaderJobLevelName',
      width: 90,
    },
  },
  {
    key: '60',
    label: '政纪处分',
    value: { title: '政纪处分', dataIndex: 'rpPunishName', width: 80 },
  },
  {
    key: '61',
    label: '政纪处分时职务层次',
    value: { title: '政纪处分时职务层次', dataIndex: 'rpPunishJobLevelName', width: 90 },
  },
  {
    key: '62',
    label: '是否提出申诉',
    value: { title: '是否提出申诉', dataIndex: 'rpIsAppeal', width: 80 },
  },
  {
    key: '63',
    label: '是否接受申诉',
    value: { title: '是否接受申诉', dataIndex: 'rpIsAcceptAppeal', width: 80 },
  },
  {
    key: '64',
    label: '申诉处理决定',
    value: { title: '申诉处理决定', dataIndex: 'rpIsAppealResultName', width: 80 },
  },
  {
    key: '65',
    label: '申诉时职务层次',
    value: { title: '申诉时职务层次', dataIndex: 'rpIsAppealJobLevelName', width: 80 },
  },
  {
    key: '66',
    label: '是否提出再申诉',
    value: { title: '是否提出再申诉', dataIndex: 'rpIsReappeal', width: 80 },
  },
  {
    key: '67',
    label: '是否接受再申诉',
    value: { title: '是否接受再申诉', dataIndex: 'rpIsReappealAccept', width: 80 },
  },
  {
    key: '68',
    label: '再申诉处理决定',
    value: { title: '再申诉处理决定', dataIndex: 'rpIsReappealResultName', width: 80 },
  },
  {
    key: '69',
    label: '再申诉时职务层次',
    value: { title: '再申诉时职务层次', dataIndex: 'rpIsReappealJobLevelName', width: 80 },
  },
  { key: 'rypx', label: '人员培训' },
  { key: '70', label: '是否培训', value: { title: '是否培训', dataIndex: 'pxIsTrain', width: 80 } },
  {
    key: '71',
    label: '培训总学时',
    value: { title: '培训总学时', dataIndex: 'pxTrainTotalHour', width: 80 },
  },
  {
    key: '72',
    label: '出国培训总次数',
    value: { title: '出国培训总次数', dataIndex: 'pxTrainAbroad', width: 80 },
  },
  {
    key: '73',
    label: '西部12省区市参加对口支援培训总次数',
    value: { title: '西部12省区市参加对口支援培训总次数', dataIndex: 'pxTrainWest', width: 150 },
  },
  // {key:'86',label:'是否维护',value:{title:'是否维护',dataIndex:'isUpdate',width: 80}},
  // {key:'91',label:'逐级晋升在下一级岗位任职年限',value:{title:'逐级晋升在下一级岗位任职年限',dataIndex:'promotionPreJobTimeCount',width: 120}},
];
const TableOrg = [
  { key: '1', label: '组织名称', value: { title: '组织名称', dataIndex: 'name' } },
  {
    key: '2',
    label: '上级党组织名称',
    value: { title: '上级党组织名称', dataIndex: 'parent_name' },
  },
  { key: '3', label: '党组织类型名称', value: { title: '党组织类型名称', dataIndex: 'd01_name' } },
  {
    key: '4',
    label: '党组织所在单位情况名称',
    value: { title: '党组织所在单位情况名称', dataIndex: 'd02_name' },
  },
  {
    key: '5',
    label: '党组织隶属关系名称',
    value: { title: '党组织隶属关系名称', dataIndex: 'd03_name' },
  },
  { key: '6', label: '单位类别', value: { title: '单位类别', dataIndex: 'd04_name' } },
  { key: '7', label: '建立党组织情况', value: { title: '建立党组织情况', dataIndex: 'd05_name' } },
  { key: '8', label: '党组织书记', value: { title: '党组织书记', dataIndex: 'secretary' } },
  { key: '9', label: '联系人', value: { title: '联系人', dataIndex: 'contacter' } },
  { key: '10', label: '联系方式', value: { title: '联系方式', dataIndex: 'contact_phone' } },
  { key: '11', label: '单位名称', value: { title: '单位名称', dataIndex: 'main_unit_name' } },
];
const excellentTextColumn = [
  { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'name' } },
  { key: '2', label: '性别', value: { title: '性别', dataIndex: 'sex_name' } },
  { key: '3', label: '民族', value: { title: '民族', dataIndex: 'd06_name' } },
  {
    key: '4',
    label: '出生年月',
    value: {
      title: '出生年月',
      dataIndex: 'birthday',
      render: (text) => {
        return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
      },
    },
  },
  {
    key: '5',
    label: '入党时间',
    value: {
      title: '入党时间',
      dataIndex: 'apply_date',
      render: (text) => {
        return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
      },
    },
  },
  { key: '6', label: '工作单位及职务', value: { title: '工作单位及职务', dataIndex: 'd09_name' } },
];
const TableUnit = [
  {
    key: '1',
    label: '关联组织',
    value: { title: '关联组织', dataIndex: 'main_org_name', width: 80 },
  },
  // { key: '2', label: '党建指导组织', value: { title: '党建指导组织', dataIndex: 'manage_org_name', width: 80 } },
  { key: '3', label: '单位名称', value: { title: '单位名称', dataIndex: 'name', width: 80 } },
  {
    key: '4',
    label: '单位代码',
    value: { title: '单位代码', dataIndex: 'credit_code', width: 80 },
  },
  { key: '5', label: '单位类别', value: { title: '单位类别', dataIndex: 'd04_name', width: 80 } },
  { key: '6', label: '经济类型', value: { title: '经济类型', dataIndex: 'd16_name', width: 80 } },
  { key: '7', label: '企业规模', value: { title: '企业规模', dataIndex: 'd17_name', width: 80 } },
  {
    key: '8',
    label: '单位隶属关系',
    value: { title: '单位隶属关系', dataIndex: 'd35_name', width: 80 },
  },
  { key: '9', label: '所在地区', value: { title: '所在地区', dataIndex: 'd48_name', width: 80 } },
  { key: '10', label: '公益分类', value: { title: '公益分类', dataIndex: 'd81_name', width: 80 } },
  { key: '11', label: '办院类型', value: { title: '办院类型', dataIndex: 'd111_name', width: 80 } },
  {
    key: '12',
    label: '所长负责制情况',
    value: { title: '所长负责制情况', dataIndex: 'd112_name', width: 80 },
  },
  {
    key: '13',
    label: '在岗职工数（人）',
    value: { title: '在岗职工数（人）', dataIndex: 'on_post_num', width: 80 },
  },
  {
    key: '14',
    label: '建立党员服务机构',
    value: { title: '建立党员服务机构', dataIndex: 'is_org_service', width: 80 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '15',
    label: '建立党员志愿者队伍',
    value: { title: '建立党员志愿者队伍', dataIndex: 'is_vol_team', width: 80 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '16',
    label: '是否配备专职党务工作人员',
    value: { title: '是否配备专职党务工作人员', dataIndex: 'has_party_work', width: 80 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '17',
    label: '配备专职副书记',
    value: { title: '配备专职副书记', dataIndex: 'has_major_deputy_secretary', width: 80 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '18',
    label: '法定代表人是否党员',
    value: { title: '法定代表人是否党员', dataIndex: 'has_representative', width: 80 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '19',
    label: '是否建立工会或共青团组织',
    value: { title: '是否建立工会或共青团组织', dataIndex: 'has_union_organization', width: 80 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '20',
    label: '是否党建工作指导员联系',
    value: { title: '是否党建工作指导员联系', dataIndex: 'has_instructor_contact', width: 80 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '21',
    label: '是否主要负责人担任党组织书记',
    value: {
      title: '是否主要负责人担任党组织书记',
      dataIndex: 'has_organization_secretary',
      width: 80,
    },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '22',
    label: '是否行业协会商会',
    value: { title: '是否行业协会商会', dataIndex: 'is_decoupl_industry', width: 80 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '23',
    label: '中央一级机关党组数量',
    value: { title: '中央一级机关党组数量', dataIndex: 'unit_party_central', width: 80 },
  },
  {
    key: '24',
    label: '省（区、市）一级机关党组',
    value: { title: '省（区、市）一级机关党组', dataIndex: 'unit_party_province', width: 80 },
  },
  {
    key: '25',
    label: '市（地、州、盟）一级机关党组',
    value: { title: '市（地、州、盟）一级机关党组', dataIndex: 'unit_party_city', width: 80 },
  },
  {
    key: '26',
    label: '县（市、区、旗）一级机关党组',
    value: { title: '县（市、区、旗）一级机关党组', dataIndex: 'unit_party_county', width: 80 },
  },
  {
    key: '27',
    label: '吸收未转入组织关系的党员建立党组织数',
    value: {
      title: '吸收未转入组织关系的党员建立党组织数',
      dataIndex: 'absorbed_tissue_number',
      width: 80,
    },
  },
  {
    key: '28',
    label: '未转组织关系党员数',
    value: { title: '未转组织关系党员数', dataIndex: 'not_turned_party', width: 80 },
  },
  {
    key: '29',
    label: '党建工作指导员数',
    value: { title: '党建工作指导员数', dataIndex: 'bZT6_10', width: 80 },
  },
  {
    key: '30',
    label: '党政机关工作人员',
    value: { title: '党政机关工作人员', dataIndex: 'b30_A12', width: 80 },
  },
  {
    key: '31',
    label: '在岗专业技术人员数（人）',
    value: { title: '在岗专业技术人员数（人）', dataIndex: 'tec_num', width: 80 },
  },
  {
    key: '32',
    label: '在岗专业技术人员（高级职称）（人）',
    value: { title: '在岗专业技术人员（高级职称）（人）', dataIndex: 'zaigang_gaoji', width: 80 },
  },
  {
    key: '33',
    label: '从业人员数',
    value: { title: '从业人员数', dataIndex: 'employees_number', width: 80 },
  },
  {
    key: '34',
    label: '建立分党组',
    value: { title: '建立分党组', dataIndex: 'create_party_group', width: 80 },
  },
  {
    key: '35',
    label: '建立党组',
    value: { title: '建立党组', dataIndex: 'create_party_team', width: 80 },
  },
  {
    key: '36',
    label: '建立党组性质党委',
    value: { title: '建立党组性质党委', dataIndex: 'create_party_committee', width: 80 },
  },
  {
    key: '37',
    label: '最新第一书记年份',
    value: { title: '最新第一书记年份', dataIndex: 'year', width: 80 },
  },
  {
    key: '38',
    label: '今年选派第一书记',
    value: { title: '今年选派第一书记', dataIndex: 'first_secretary_select', width: 80 },
  },
  {
    key: '39',
    label: '本年各级培训第一书记',
    value: { title: '本年各级培训第一书记', dataIndex: 'secretary_training_num', width: 80 },
  },
  {
    key: '40',
    label: '是否为第一书记安排不低于1万元工作经费',
    value: { title: '是否为第一书记安排不低于1万元工作经费', dataIndex: 'has_thousand', width: 80 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '41',
    label: '是否派出单位落实责任、项目、资金捆绑的',
    value: { title: '是否派出单位落实责任、项目、资金捆绑的', dataIndex: 'has_bundled', width: 80 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '42',
    label: '提拔使用或晋级的第一书记数',
    value: { title: '提拔使用或晋级的第一书记数', dataIndex: 'promoted_num', width: 80 },
  },
  {
    key: '43',
    label: '因工作不胜任召回调整的第一书记数',
    value: { title: '因工作不胜任召回调整的第一书记数', dataIndex: 'adjusted_num', width: 80 },
  },
  {
    key: '44',
    label: '运转经费',
    value: { title: '运转经费', dataIndex: 'operating_expenses', width: 80 },
  },
  {
    key: '45',
    label: '每村办公经费',
    value: { title: '每村办公经费', dataIndex: 'village_per', width: 80 },
  },
  {
    key: '46',
    label: '党组织书记平均报酬（万元）',
    value: { title: '党组织书记平均报酬（万元）', dataIndex: 'secretary_salary', width: 80 },
  },
  {
    key: '47',
    label: '全部社区工作者年工资总额（万元）',
    value: { title: '全部社区工作者年工资总额（万元）', dataIndex: 'community_workers_salary', width: 80 },
  },
  {
    key: '48',
    label: '社区纳入财政预算的工作经费总额（万元）',
    value: { title: '社区纳入财政预算的工作经费总额（万元）', dataIndex: 'included_financial', width: 80 },
  },
  {
    key: '49',
    label: '全年服务群众专项经费总额（万元）',
    value: { title: '全年服务群众专项经费总额（万元）', dataIndex: 'special_funds_masses', width: 80 },
  },
  {
    key: '50',
    label: '全部专职网格员年工资总额（万元）',
    value: { title: '全部专职网格员年工资总额（万元）', dataIndex: 'zzngzze', width: 80 },
  },
  {
    key: '51',
    label: '社区中的住宅小区总数',
    value: { title: '社区中的住宅小区总数', dataIndex: 'residential_areas', width: 80 },
  },
  {
    key: '52',
    label: '配备专职网格员数',
    value: { title: '配备专职网格员数', dataIndex: 'grid_members', width: 80 },
  },
  {
    key: '53',
    label: '社区工作者人数',
    value: { title: '社区工作者人数', dataIndex: 'community_worker_count', width: 80 },
  },
];
const TextColumn = [
  { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'name', width: 70 } },
  { key: '2', label: '性别', value: { title: '性别', dataIndex: 'sex_name', width: 70 } },
  {
    key: '3',
    label: '身份证号码',
    value: {
      title: '身份证号码',
      dataIndex: 'idcard',
      width: 80,
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
          if (text.indexOf('*') > 0) {
            return text;
          }
          return <span>{newVal}</span>;
        } else {
          return '';
        }
      },
    },
  },
  { key: '4', label: '年龄', value: { title: '年龄', dataIndex: 'age', width: 80 } },
  { key: '5', label: '电话', value: { title: '电话', dataIndex: 'phone', width: 80 } },
  // { key: '6', label: '党员类型', value: { title: '党员类型', dataIndex: 'd08_name', width: 80 } },
  {
    key: '6',
    label: '预备党员日期',
    value: { title: '预备党员日期', dataIndex: 'join_org_date', width: 80 },
  },
  {
    key: '7',
    label: '转为正式党员日期',
    value: { title: '转为正式党员日期', dataIndex: 'full_member_date', width: 80 },
  },
  { key: '8', label: '单位名称', value: { title: '单位名称', dataIndex: 'unit_name', width: 80 } },
  { key: '9', label: '单位类别', value: { title: '单位类别', dataIndex: 'd04_name', width: 80 } },
  { key: '10', label: '民族', value: { title: '民族', dataIndex: 'd06_name', width: 80 } },
  { key: '11', label: '学历', value: { title: '学历', dataIndex: 'd07_name', width: 80 } },
  { key: '12', label: '党员类型', value: { title: '党员类型', dataIndex: 'd08_name', width: 80 } },
  { key: '13', label: '工作岗位', value: { title: '工作岗位', dataIndex: 'd09_name', width: 80 } },
  {
    key: '14',
    label: '新社会阶层',
    value: { title: '新社会阶层', dataIndex: 'd20_name', width: 80 },
  },
  { key: '15', label: '转正类型', value: { title: '转正类型', dataIndex: 'd28_name', width: 80 } },
  { key: '16', label: '籍贯', value: { title: '籍贯', dataIndex: 'd48_name', width: 80 } },
  { key: '17', label: '一线情况', value: { title: '一线情况', dataIndex: 'd21_name', width: 80 } },
];
const TableDevMem = [
  { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'name', width: 70 } },
  { key: '2', label: '性别', value: { title: '性别', dataIndex: 'sex_name', width: 70 } },
  {
    key: '3',
    label: '身份证号码',
    value: {
      title: '身份证号码',
      dataIndex: 'idcard',
      width: 80,
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
          if (text.indexOf('*') > 0) {
            return text;
          }
          return <span>{newVal}</span>;
        } else {
          return '';
        }
      },
    },
  },
  { key: '4', label: '年龄', value: { title: '年龄', dataIndex: 'age', width: 80 } },
  { key: '5', label: '电话', value: { title: '电话', dataIndex: 'phone', width: 80 } },
  { key: '6', label: '单位名称', value: { title: '单位名称', dataIndex: 'unit_name', width: 80 } },
  { key: '7', label: '单位类别', value: { title: '单位类别', dataIndex: 'd04_name', width: 80 } },
  { key: '8', label: '民族', value: { title: '民族', dataIndex: 'd06_name', width: 80 } },
  { key: '9', label: '学历', value: { title: '学历', dataIndex: 'd07_name', width: 80 } },
  { key: '10', label: '党员类型', value: { title: '党员类型', dataIndex: 'd08_name', width: 80 } },
  { key: '11', label: '工作岗位', value: { title: '工作岗位', dataIndex: 'd09_name', width: 80 } },
  {
    key: '12',
    label: '新社会阶层',
    value: { title: '新社会阶层', dataIndex: 'd20_name', width: 80 },
  },
  { key: '13', label: '一线情况', value: { title: '一线情况', dataIndex: 'd21_name', width: 80 } },
  { key: '14', label: '籍贯', value: { title: '籍贯', dataIndex: 'd48_name', width: 80 } },
];
const TableFlowMem = [
  { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'mem_name', width: 70 } },
  { key: '2', label: '性别', value: { title: '性别', dataIndex: 'gender_name', width: 70 } },
  {
    key: '3',
    label: '身份证号码',
    value: {
      title: '身份证号码',
      dataIndex: 'idcard',
      width: 80,
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
          if (text.indexOf('*') > 0) {
            return text;
          }
          return <span>{newVal}</span>;
        } else {
          return '';
        }
      },
    },
  },
  { key: '4', label: '出生日期', value: { title: '出生日期', dataIndex: 'birthday', width: 70 } },
  {
    key: '5',
    label: '组织单位类别',
    value: { title: '组织单位类别', dataIndex: 'd04_name', width: 80 },
  },
  { key: '6', label: '工作岗位', value: { title: '工作岗位', dataIndex: 'd09_code', width: 80 } },
  {
    key: '7',
    label: '经济控制类型',
    value: { title: '经济控制类型', dataIndex: 'd16_name', width: 80 },
  },
  {
    key: '8',
    label: '新社会阶层',
    value: { title: '新社会阶层', dataIndex: 'd20_code', width: 80 },
  },
  {
    key: '9',
    label: '流动类型',
    value: { title: '流动类型', dataIndex: 'is_prov_out_name', width: 80 },
  },
  {
    key: '10',
    label: '流出或流入日期',
    value: { title: '流出或流入日期', dataIndex: 'outflow_date', width: 80 },
  },
  {
    key: '11',
    label: '流出或流入类型代码',
    value: { title: '流出或流入类型代码', dataIndex: 'outflow_type_name', width: 80 },
  },
  {
    key: '12',
    label: '流出或流入地党组织',
    value: { title: '流出或流入地党组织', dataIndex: 'outflow_org_name', width: 80 },
  },
  {
    key: '13',
    label: '流出或流入原因',
    value: { title: '流出或流入原因', dataIndex: 'outflow_reason_name', width: 80 },
  },
  {
    key: '14',
    label: '流出或流入单位类型',
    value: { title: '流出或流入单位类型', dataIndex: 'outflow_unit_type_name', width: 80 },
  },
  {
    key: '15',
    label: '流出或流入地',
    value: { title: '流出或流入地', dataIndex: 'outflow_area_name', width: 80 },
  },
  {
    key: '16',
    label: '流出或流入党组织联系人',
    value: { title: '流出或流入党组织联系人', dataIndex: 'outflow_org_linkman', width: 80 },
  },
  {
    key: '17',
    label: '流出或流入党组织联系方式',
    value: { title: '流出或流入党组织联系方式', dataIndex: 'outflow_org_phone', width: 80 },
  },
  {
    key: '18',
    label: '流出或流入',
    value: { title: '流出或流入', dataIndex: 'flow_add_type', width: 80 },
  },
  {
    key: '19',
    label: '党员学历',
    value: { title: '党员学历', dataIndex: 'outflow_edu_name', width: 80 },
  },
  {
    key: '20',
    label: '原职业(工作岗位)',
    value: { title: '原职业(工作岗位)', dataIndex: 'outflow_job_name', width: 80 },
  },
  { key: '21', label: '组织单位', value: { title: '组织单位', dataIndex: 'unit_name', width: 80 } },

  // { key: '8', label: '流出组织', value: { title: '流出组织', dataIndex: 'mem_org_name', width: 70 } },
  // { key: '9', label: '流入组织', value: { title: '流入组织', dataIndex: 'outflow_orgName', width: 80 } },
  // { key: '10', label: '流动类型', value: { title: '流动类型', dataIndex: 'isProv_outName', width: 80 } },
  // { key: '11', label: '流出时间', value: { title: '流出时间', dataIndex: 'outflow_date', width: 80, render: (text) => { return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>; } } },
];
const TextRewardMem = [
  { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'name', width: 70 } },
  { key: '2', label: '年龄', value: { title: '年龄', dataIndex: 'age', width: 70 } },
  {
    key: '3',
    label: '预备党员日期',
    value: { title: '预备党员日期', dataIndex: 'join_org_date', width: 70 },
  },
  {
    key: '4',
    label: '转为正式党员日期',
    value: { title: '转为正式党员日期', dataIndex: 'full_member_date', width: 70 },
  },
  {
    key: '5',
    label: '党员受奖/惩名称',
    value: { title: '党员受奖/惩名称', dataIndex: 'd029_name', width: 70 },
  },
  {
    key: '6',
    label: '奖/惩原因名称',
    value: { title: '奖/惩原因名称', dataIndex: 'd030_name', width: 70 },
  },
  {
    key: '7',
    label: '党员奖/惩批准日期',
    value: { title: '党员奖/惩批准日期', dataIndex: 'start_date', width: 70 },
  },
  // { key: '8', label: '入党时间年度分类', value: { title: '入党时间年度分类', dataIndex: 'join_org_date_count', width: 70 } },
  // { key: '3', label: '奖惩内容', value: { title: '奖惩内容', dataIndex: 'd029_name', width: 70 } },
  // { key: '4', label: '奖惩原因', value: { title: '奖惩原因', dataIndex: 'd01_name', width: 70, render: (text, record, index) => (<span>{record['type'] === 1 ? `${record['d47_name'] || ''}` : `${record['d030_name'] || ''}`}</span>) } },
  // {
  //   key: '5', label: '奖惩生效日期', value: {
  //     title: '奖惩生效日期', dataIndex: 'start_date', width: 70, render: (text, record, index) => {
  //       return (
  //         <div>{text && moment(text).format('YYYY-MM-DD')}</div>
  //       )
  //     }
  //   }
  // },
  // { key: '2', label: '组织名称', value: { title: '组织名称', dataIndex: 'org_name', width: 70 } },
];
const rewardTextColumn = [
  {
    key: '1',
    label: '党组织名称',
    value: { title: '党组织名称', dataIndex: 'orgName', width: 70 },
  },
  { key: '2', label: '处分类型', value: { title: '处分类型', dataIndex: 'd42Name', width: 70 } },
  {
    key: '3',
    label: '处分时间',
    value: {
      title: '处分时间',
      dataIndex: 'startDate',
      width: 70,
      render: (text) => {
        return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
      },
    },
  },
  { key: '4', label: '处分原因', value: { title: '处分原因', dataIndex: 'remark', width: 70 } },
];
const TextOrgSlack = [
  { key: '1', label: '组织名称', value: { title: '组织名称', dataIndex: 'name', width: 70 } },
  { key: '2', label: '整顿年度', value: { title: '整顿年度', dataIndex: 'year', width: 70 } },
  {
    key: '3',
    label: '整顿开始时间',
    value: { title: '整顿开始时间', dataIndex: 'neaten_time', width: 70 },
  },
  {
    key: '4',
    label: '整备结束时间',
    value: { title: '整备结束时间', dataIndex: 'neaten_endtime', width: 70 },
  },
  {
    key: '5',
    label: '是否整顿',
    value: { title: '是否整顿', dataIndex: 'hasNeaten', width: 70 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '6',
    label: '属于党组织班子配备不齐、书记长期缺职、工作处于停滞状态的',
    value: {
      title: '属于党组织班子配备不齐、书记长期缺职、工作处于停滞状态的',
      dataIndex: 'd74_code1',
      width: 70,
    },
  },
  {
    key: '7',
    label: '属于党组织书记不胜任现职、工作不在状态、严重影响班子整体战斗力的',
    value: {
      title: '属于党组织书记不胜任现职、工作不在状态、严重影响班子整体战斗力的',
      dataIndex: 'd74_code2',
      width: 70,
    },
  },
  {
    key: '8',
    label: '属于班子不团结、内耗严重、工作不能正常开展的',
    value: {
      title: '属于班子不团结、内耗严重、工作不能正常开展的',
      dataIndex: 'd74_code3',
      width: 70,
    },
  },
  {
    key: '8',
    label: '属于组织制度形同虚设、不开展党组织活动的',
    value: { title: '属于组织制度形同虚设、不开展党组织活动的', dataIndex: 'd74_code4', width: 70 },
  },
  {
    key: '9',
    label: '属于换届选举拉票贿选问题突出的',
    value: { title: '属于换届选举拉票贿选问题突出的', dataIndex: 'd74_code5', width: 70 },
  },
  {
    key: '10',
    label: '属于宗族和黑恶势力干扰渗透严重的',
    value: { title: '属于宗族和黑恶势力干扰渗透严重的', dataIndex: 'd74_code6', width: 70 },
  },
  {
    key: '11',
    label: '属于宗教势力干扰渗透的',
    value: { title: '属于宗教势力干扰渗透的', dataIndex: 'd74_code7', width: 70 },
  },
  {
    key: '12',
    label: '属于村务居务财务公开和民主管理混乱的',
    value: { title: '属于村务居务财务公开和民主管理混乱的', dataIndex: 'd74_code8', width: 70 },
  },
  {
    key: '13',
    label: '属于社会治安问题和信访矛盾纠纷集中的',
    value: { title: '属于社会治安问题和信访矛盾纠纷集中的', dataIndex: 'd74_code9', width: 70 },
  },
  {
    key: '14',
    label: '属于无固定办公活动场所及便民服务设施的',
    value: { title: '属于无固定办公活动场所及便民服务设施的', dataIndex: 'd74_code10', width: 70 },
  },
  {
    key: '15',
    label: '属于党组织服务意识差、服务能力弱、群众意见大的',
    value: {
      title: '属于党组织服务意识差、服务能力弱、群众意见大的',
      dataIndex: 'd74_code11',
      width: 70,
    },
  },
  {
    key: '16',
    label: '年初缺配的软弱涣散基层党组织书记数（人）',
    value: {
      title: '年初缺配的软弱涣散基层党组织书记数（人）',
      dataIndex: 'early_qp_secretary',
      width: 70,
    },
  },
  {
    key: '17',
    label: '年初需调整的软弱涣散基层党组织书记数（人）',
    value: {
      title: '年初需调整的软弱涣散基层党组织书记数（人）',
      dataIndex: 'early_tz_secretary',
      width: 70,
    },
  },
  {
    key: '18',
    label: '本年度已选配',
    value: { title: '本年度已选配', dataIndex: 'has_year_selected', width: 70 },
  },
  {
    key: '19',
    label: '本年度已调整',
    value: { title: '本年度已调整', dataIndex: 'has_year_adjust', width: 70 },
  },
  {
    key: '20',
    label: '培训软弱涣散基层党组织书记（人）',
    value: { title: '培训软弱涣散基层党组织书记（人）', dataIndex: 'train_secretary', width: 70 },
  },
  {
    key: '21',
    label: '联村的县级领导班子成员（人）',
    value: {
      title: '联村的县级领导班子成员（人）',
      dataIndex: 'lc_county_level_leader',
      width: 70,
    },
  },
  {
    key: '22',
    label: '包村的县级领导班子成员（人）',
    value: {
      title: '包村的县级领导班子成员（人）',
      dataIndex: 'bc_county_level_leader',
      width: 70,
    },
  },
  {
    key: '23',
    label: '选派第一书记（人）',
    value: { title: '选派第一书记（人）', dataIndex: 'first_secretary', width: 70 },
  },
  {
    key: '24',
    label: '结对帮扶的县级及以上机关单位（人）',
    value: {
      title: '结对帮扶的县级及以上机关单位（人）',
      dataIndex: 'jdbf_county_level_unit',
      width: 70,
    },
  },
  {
    key: '25',
    label: '省市两级挂牌督办的村（个）',
    value: { title: '省市两级挂牌督办的村（个）', dataIndex: 'two_levels_listed', width: 70 },
  },
  {
    key: '26',
    label: '开展专项整治（项）',
    value: { title: '开展专项整治（项）', dataIndex: 'special_rectification', width: 70 },
  },
  {
    key: '27',
    label: '解决各类问题（个）',
    value: { title: '解决各类问题（个）', dataIndex: 'solve_problems', width: 70 },
  },
  {
    key: '28',
    label: '查处违纪违法行为（例）',
    value: { title: '查处违纪违法行为（例）', dataIndex: 'look_into_laws', width: 70 },
  },
  { key: '29', label: '单位类别', value: { title: '单位类别', dataIndex: 'd04_name', width: 70 } },
  { key: '30', label: '单位名称', value: { title: '单位名称', dataIndex: 'unit_name', width: 70 } },

  // { key: '2', label: '涣散类型', value: { title: '涣散类型', dataIndex: 'd74_name', width: 70 } },
  // {
  //   key: '3', label: '是否整顿', value: {
  //     title: '是否整顿', dataIndex: 'has_neaten', width: 70, render: (text, record, index) => {
  //       return text == 0 ? '否' : '是';
  //     },
  //   }
  // },
  // {
  //   key: '4', label: '整顿开始时间', value: {
  //     title: '整顿开始时间', dataIndex: 'neatenTime', width: 70, render: (text) => {
  //       return text ? moment(text).format('YYYY-MM-DD') : ''
  //     }
  //   }
  // },
  // {
  //   key: '5', label: '整顿结束时间', value: {
  //     title: '整顿结束时间', dataIndex: 'neatenEndTime', width: 70, render: (text) => {
  //       return text ? moment(text).format('YYYY-MM-DD') : ''
  //     }
  //   }
  // },
];
const TextOrgRecognition = [
  { key: '1', label: '组织名称', value: { title: '组织名称', dataIndex: 'org_name', width: 70 } },
  { key: '2', label: '组织类别', value: { title: '组织类别', dataIndex: 'd01_code', width: 70 } },
  { key: '3', label: '人数', value: { title: '人数', dataIndex: 'number', width: 70 } },
  {
    key: '4',
    label: '表彰对象',
    value: { title: '表彰对象', dataIndex: 'recognition_object', width: 70 },
  },
  {
    key: '5',
    label: '表彰级别',
    value: { title: '表彰级别', dataIndex: 'recognition_level', width: 70 },
  },
  {
    key: '6',
    label: '表彰类型',
    value: { title: '表彰类型', dataIndex: 'recognition_type', width: 70 },
  },
  {
    key: '7',
    label: '党委(总支部、支部)书记优秀共产党员名数',
    value: {
      title: '党委(总支部、支部)书记优秀共产党员名数',
      dataIndex: 'committee_party',
      width: 70,
    },
  },
  {
    key: '8',
    label: '党委(总支部、支部)书记优秀党务工作者名数',
    value: {
      title: '党委(总支部、支部)书记优秀党务工作者名数',
      dataIndex: 'committee_worker',
      width: 70,
    },
  },
  {
    key: '9',
    label: '生活困难优秀共产党员名数',
    value: { title: '生活困难优秀共产党员名数', dataIndex: 'difficult_party', width: 70 },
  },
  {
    key: '10',
    label: '生活困难优秀党务工作者名数',
    value: { title: '生活困难优秀党务工作者名数', dataIndex: 'difficult_worker', width: 70 },
  },
  {
    key: '11',
    label: '追授优秀共产党员名数',
    value: { title: '追授优秀共产党员名数', dataIndex: 'add_good_party', width: 70 },
  },
  {
    key: '12',
    label: '建党50周年情况（数）',
    value: { title: '建党50周年情况（数）', dataIndex: 'anniversary_situation', width: 70 },
  },
  { key: '13', label: '表彰年度', value: { title: '表彰年度', dataIndex: 'year', width: 70 } },

  // {
  //   key: '1', label: '表彰时间', value: {
  //     title: '表彰时间', dataIndex: 'annual', width: 70, render: (text) => {
  //       return text ? moment(text).format('YYYY.MM.DD') : undefined
  //     }
  //   }
  // },
  // { key: '2', label: '表彰文件号', value: { title: '表彰文件号', dataIndex: 'file_no', width: 70 } },
  // {
  //   key: '2', label: '情况说明', value: {
  //     title: '情况说明', dataIndex: '', width: 70, render: (text, record) => {
  //       return (
  //         <span>
  //           其中党委(总支部、支部)书记优秀共产党员{record['committee_party'] || 0}名；
  //           党委(总支部、支部)书记优秀党务工作者{record['committee_worker'] || 0}名；
  //           生活困难优秀共产党员{record['difficult_party'] || 0}名；
  //           生活困难优秀党务工作者{record['difficult_worker'] || 0}名
  //         </span>
  //       )
  //     }
  //   }
  // },
];
const TextOrgIndustry = [
  {
    key: '1',
    label: '组织名称',
    value: { title: '组织名称', dataIndex: 'industry_org_name', width: 70 },
  },
  {
    key: '2',
    label: '组织类别',
    value: { title: '组织类别', dataIndex: 'industry_org_type', width: 70 },
  },
  {
    key: '3',
    label: '行业分类',
    value: { title: '行业分类', dataIndex: 'industry_classification', width: 70 },
  },
  {
    key: '4',
    label: '所属层级',
    value: { title: '所属层级', dataIndex: 'subordinate_level', width: 70 },
  },
  {
    key: '5',
    label: '隶属关系',
    value: { title: '隶属关系', dataIndex: 'membership_function', width: 70 },
  },
  {
    key: '6',
    label: '书记是否由行业主管部门党员负责同志担任',
    value: {
      title: '书记是否由行业主管部门党员负责同志担任  ',
      dataIndex: 'has_secretary_industry',
      width: 70,
    },
  },
  {
    key: '7',
    label: '专职工作人员数',
    value: { title: '专职工作人员数', dataIndex: 'worker_number', width: 70 },
  },
  {
    key: '8',
    label: '是否有所属党组织',
    value: { title: '是否有所属党组织', dataIndex: 'has_party_organizations', width: 70 },
    render: (text, record) => {
      text == 1 ? '是' : '否';
    },
  },
  {
    key: '9',
    label: '管理的党组织数',
    value: { title: '管理的党组织数', dataIndex: 'manage_org_count', width: 70 },
  },
  {
    key: '10',
    label: '管理的党员数',
    value: { title: '管理的党员数', dataIndex: 'manage_mem_count', width: 70 },
  },
  {
    key: '11',
    label: '覆盖社会组织数',
    value: { title: '覆盖社会组织数', dataIndex: 'cover_social_org', width: 70 },
  },
];
const developStepLog = [
  { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'name', width: 70 } },
  { key: '2', label: '年龄', value: { title: '年龄', dataIndex: 'age', width: 50 } },
  { key: '3', label: '性别', value: { title: '性别', dataIndex: 'sex_name', width: 50 } },
  {
    key: '4',
    label: '发展党员身份证',
    value: {
      title: '发展党员身份证',
      dataIndex: 'idcard',
      width: 80,
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
          if (text.indexOf('*') > 0) {
            return text;
          }
          return <span>{newVal}</span>;
        } else {
          return '';
        }
      },
    },
  },
  { key: '5', label: '民族', value: { title: '民族', dataIndex: 'd06_name', width: 70 } },
  { key: '6', label: '学历', value: { title: '学历', dataIndex: 'd07_name', width: 70 } },
  { key: '7', label: '党员类型', value: { title: '党员类型', dataIndex: 'd08_name', width: 70 } },
  { key: '8', label: '工作岗位', value: { title: '工作岗位', dataIndex: 'd09_name', width: 70 } },
  {
    key: '9',
    label: '进入支部类型',
    value: { title: '进入支部类型', dataIndex: 'd11_name', width: 70 },
  },
  { key: '10', label: '一线情况', value: { title: '一线情况', dataIndex: 'd21_name', width: 70 } },
  { key: '11', label: '审批结果', value: { title: '审批结果', dataIndex: 'd28_name', width: 70 } },
  { key: '12', label: '籍贯', value: { title: '籍贯', dataIndex: 'd48_name', width: 70 } },
  {
    key: '13',
    label: '加入共产党类型',
    value: { title: '加入共产党类型', dataIndex: 'join_org_name', width: 70 },
  },
  {
    key: '14',
    label: '召开支委会日期(成为正式党员日期)',
    value: {
      title: '召开支委会日期(成为正式党员日期)',
      dataIndex: 'topart_turn_party_date',
      width: 70,
    },
  },
  {
    key: '15',
    label: '延长预备期到的时间',
    value: { title: '延长预备期到的时间', dataIndex: 'extend_prepar_date', width: 70 },
  },
  {
    key: '16',
    label: '召开支委会日期(成为积极分子日期)',
    value: { title: '召开支委会日期(成为积极分子日期)', dataIndex: 'active_date', width: 70 },
  },
  {
    key: '17',
    label: '召开支委会日期(成为发展对象时间)',
    value: { title: '召开支委会日期(成为发展对象时间)', dataIndex: 'object_date', width: 70 },
  },
  { key: '18', label: '组织名称', value: { title: '组织名称', dataIndex: 'org_name', width: 70 } },
];
const orgPartCongress = [
  { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'mem_name', width: 70 } },
  { key: '2', label: '性别', value: { title: '性别', dataIndex: 'sex_name', width: 70 } },
  { key: '3', label: '学历情况', value: { title: '学历情况', dataIndex: 'd07_name', width: 80 } },
  { key: '4', label: '人员身份', value: { title: '人员身份', dataIndex: 'd106_name', width: 80 } },
  {
    key: '5',
    label: '组织层级码',
    value: { title: '组织层级码', dataIndex: 'position_org_code', width: 80 },
  },
  {
    key: '6',
    label: '组织名称',
    value: { title: '组织名称', dataIndex: 'position_org_name', width: 80 },
  },
];
const orgParty = [
  { key: '1', label: '党组名称', value: { title: '党组名称', dataIndex: 'party_name', width: 70 } },
  { key: '2', label: '党组类别', value: { title: '党组类别', dataIndex: 'd108_name', width: 70 } },
  { key: '3', label: '关联单位', value: { title: '关联单位', dataIndex: 'unit_name', width: 80 } },
  {
    key: '4',
    label: '创建时间',
    value: { title: '创建时间', dataIndex: 'builde_time', width: 80 },
  },
  { key: '5', label: '组织名称', value: { title: '组织名称', dataIndex: 'org_name', width: 80 } },
];
const orgTransfer = [
  { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'transfer_mem_name', width: 50 } },
  {
    key: '2',
    label: '申请日期',
    value: { title: '申请日期', dataIndex: 'transfer_time', width: 70 },
  },
  {
    key: '3',
    label: '源组织',
    value: { title: '源组织', dataIndex: 'transfrer_out_org_name', width: 100 },
  },
  {
    key: '4',
    label: '目的组织',
    value: { title: '目的组织', dataIndex: 'transfer_in_org_name', width: 100 },
  },
  {
    key: '5',
    label: '转接类型',
    value: { title: '转接类型', dataIndex: 'transfer_type_name', width: 50 },
  },
  {
    key: '6',
    label: '接收时间',
    value: { title: '接收时间', dataIndex: 'tansfer_receive_time', width: 50 },
  },
];

const memFlow = [
  { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'mem_name', width: 50 } },
  { key: '2', label: '性别', value: { title: '性别', dataIndex: 'mem_sex_name', width: 70 } },
  { key: '3', label: '联系电话', value: { title: '联系电话', dataIndex: 'mem_phone', width: 100 } },
  {
    key: '4',
    label: '流动类型',
    value: { title: '流动类型', dataIndex: 'flow_type_name', width: 100 },
  },
  {
    key: '5',
    label: '流出地党支部',
    value: { title: '流出地党支部', dataIndex: 'mem_org_name', width: 50 },
  },
  {
    key: '6',
    label: '流入地党支部',
    value: { title: '流入地党支部', dataIndex: 'in_org_name', width: 50 },
  },
  {
    key: '6',
    label: '接收时间',
    value: { title: '接收时间', dataIndex: 'in_receiving_time', width: 50 },
  },
  { key: '6', label: '外出日期', value: { title: '外出日期', dataIndex: 'out_time', width: 50 } },
];
// 这个方法是给一级菜单报表配置预览点击弹窗选择框使用
export const getTableColsCheck = (tableType) => {
  console.log(tableType, 'tableTypetableTypetableType');


  const typeStr = String(tableType || '');

  // 使用 startsWith 匹配
  if (typeStr.startsWith('ccp_mem_report') || typeStr.startsWith('ccp_mem_all')) {
    return { dis: disMem, column: newTextColumn };
  } else if (typeStr.startsWith('ccp_org_reward')) {
    return { dis: disMem, column: newrewardTextColumn };
  } else if (typeStr.startsWith('ccp_develop_excellent_mem')) {
    return { dis: disMem, column: newexcellentTextColumn };
  } else if (typeStr.startsWith('ccp_org_all')) {
    return { dis: disOrg, column: newTableOrg };
  } else if (typeStr.startsWith('ccp_unit_all')) {
    return { dis: disUnit, column: newTableUnit };
  } else if (typeStr.startsWith('ccp_mem_develop_all')) {
    return { dis: disDevMem, column: newTableDevMem };
  } else if (typeStr.startsWith('ccp_mem_flow_all')) {
    return { dis: disFlowMem, column: newTableFlowMem };
  } else if (typeStr.startsWith('ccp_mem_reward_all')) {
    return { dis: disRewardMem, column: newTextRewardMem };
  } else if (typeStr.startsWith('ccp_org_slack_all')) {
    return { dis: disOrgSlack, column: newTextOrgSlack };
  } else if (typeStr.startsWith('ccp_org_recognition_all')) {
    return { dis: disOrgRecognition, column: newTextOrgRecognition }; // 第十四表 党内表彰情况
  } else if (typeStr.startsWith('ccp_org_industry_all')) {
    return { dis: disOrgIndustry, column: newTextOrgIndustry };
  } else if (typeStr.startsWith('ccp_develop_step_log_all')) {
    return { dis: disDevStep, column: newdevelopStepLog };
  } else if (typeStr.startsWith('ccp_org_party_congress_committee_all')) {
    return { dis: disOrgPartCongress, column: neworgPartCongress };
  } else if (typeStr.startsWith('ccp_org_party')) {
    // 党组反查
    return { dis: disPart, column: neworgParty };
  } else if (typeStr.startsWith('ccp_transfer_statistics')) {
    // 关系转接反查
    return { dis: disTransfer, column: neworgTransfer };
  } else if (typeStr.startsWith('mem_flow_all')) {
    // 流动党员
    return { dis: disFlow, column: newmemFlow };
  } else {
    return { dis: [], column: [] };
  }
};

export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      visible2: false,
      columns: [],
      activeColumns: [],
      checkData: [],
      pagination: { page: 1, pageSize: 10, total: 0 },
      progressType: '', //进度条类型
      checkExportLoading: false, // 反查列表的导出
      getTableCols: this.getTableCols,
    };
  }
  getTableCols = (tableType) => {

    // switch (tableType) {
    //   case 'ccp_mem_report':
    //     return { dis: disMem, column: TextColumn };
    //   case 'ccp_org_reward':
    //     return { dis: disMem, column: rewardTextColumn };
    //   case 'ccp_develop_excellent_mem':
    //     return { dis: disMem, column: excellentTextColumn };
    //   case 'ccp_mem_all':
    //     return { dis: disMem, column: TextColumn };
    //   case 'ccp_mem_all_2021':
    //     return { dis: disMem, column: TextColumn };
    //   case 'ccp_org_all':
    //     return { dis: disOrg, column: TableOrg };
    //   case 'ccp_unit_all':
    //     return { dis: disUnit, column: TableUnit };
    //   case 'ccp_mem_develop_all':
    //     return { dis: disDevMem, column: TableDevMem };
    //   case 'ccp_mem_flow_all':
    //     return { dis: disFlowMem, column: TableFlowMem };
    //   case 'ccp_mem_reward_all':
    //     return { dis: disRewardMem, column: TextRewardMem };
    //   case 'ccp_org_slack_all':
    //     return { dis: disOrgSlack, column: TextOrgSlack };
    //   case 'ccp_org_recognition_all':
    //     return { dis: disOrgRecognition, column: TextOrgRecognition }; // 第十四表 党内表彰情况
    //   case 'ccp_org_industry_all':
    //     return { dis: disOrgIndustry, column: TextOrgIndustry };
    //   case 'ccp_develop_step_log_all':
    //     return { dis: disDevStep, column: developStepLog };
    //   case 'ccp_org_party_congress_committee_all':
    //     return { dis: disOrgPartCongress, column: orgPartCongress };
    //   case 'ccp_org_party': // 党组反查
    //     return { dis: disPart, column: orgParty };
    //   case 'ccp_transfer_statistics': // 关系转接反查
    //     return { dis: disTransfer, column: orgTransfer };
    //   case 'mem_flow_all': // 流动党员
    //     return { dis: disFlow, column: memFlow };
    //   default:
    //     return { dis: [], column: [] };
    // }
    // 确保 tableType 是字符串类型
    // 使用 startsWith 匹配
    console.log(tableType, 'tableTypetableTypetableType');

    let typeStr = String(tableType || '');
    // 使用 startsWith 匹配
    let { activeColumns: newData } = this.state;
    if (typeStr.startsWith('ccp_mem_report') || typeStr.startsWith('ccp_mem_all')) {
      return { dis: disMem, column: this.compareData(TextColumn, newTextColumn, newData) }; //判断返回的数据 没有的不展示
    } else if (typeStr.startsWith('ccp_org_reward')) {
      return { dis: disMem, column: this.compareData(rewardTextColumn, newrewardTextColumn, newData) };
    } else if (typeStr.startsWith('ccp_develop_excellent_mem')) {
      return { dis: disMem, column: this.compareData(excellentTextColumn, newexcellentTextColumn, newData) };
    } else if (typeStr.startsWith('ccp_org_all')) {
      return { dis: disOrg, column: this.compareData(TableOrg, newTableOrg, newData) };
    } else if (typeStr.startsWith('ccp_unit_all')) {
      return { dis: disUnit, column: this.compareData(TableUnit, newTableUnit, newData) };
    } else if (typeStr.startsWith('ccp_mem_develop_all')) {
      return { dis: disDevMem, column: this.compareData(TableDevMem, newTableDevMem, newData) };
    } else if (typeStr.startsWith('ccp_mem_flow_all')) {
      return { dis: disFlowMem, column: this.compareData(TableFlowMem, newTableFlowMem, newData) };
    } else if (typeStr.startsWith('ccp_mem_reward_all')) {
      return { dis: disRewardMem, column: this.compareData(TextRewardMem, newTextRewardMem, newData) };
    } else if (typeStr.startsWith('ccp_org_slack_all')) {
      return { dis: disOrgSlack, column: this.compareData(TextOrgSlack, newTextOrgSlack, newData) };
    } else if (typeStr.startsWith('ccp_org_recognition_all')) {
      return { dis: disOrgRecognition, column: this.compareData(TextOrgRecognition, newTextOrgRecognition, newData) }; // 第十四表 党内表彰情况
    } else if (typeStr.startsWith('ccp_org_industry_all')) {
      return { dis: disOrgIndustry, column: this.compareData(TextOrgIndustry, newTextOrgIndustry, newData) };
    } else if (typeStr.startsWith('ccp_develop_step_log_all')) {
      return { dis: disDevStep, column: this.compareData(developStepLog, newdevelopStepLog, newData) };
    } else if (typeStr.startsWith('ccp_org_party_congress_committee_all')) {
      return { dis: disOrgPartCongress, column: this.compareData(orgPartCongress, neworgPartCongress, newData) };
    } else if (typeStr.startsWith('ccp_org_party')) {
      // 党组反查
      return { dis: disPart, column: this.compareData(orgParty, neworgParty, newData) };
    } else if (typeStr.startsWith('ccp_transfer_statistics')) {
      // 关系转接反查
      return { dis: disTransfer, column: this.compareData(orgTransfer, neworgTransfer, newData) };
    } else if (typeStr.startsWith('mem_flow_all')) {
      // 流动党员
      return { dis: disFlow, column: this.compareData(memFlow, newmemFlow, newData) };
    } else {
      return { dis: [], column: [] };
    }
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      visible2: false,
      columns: [],
      activeColumns: [],
    });
  };
  handleOk = () => {
    let columns: any = [];
    const { checkData } = this.state;
    const { tableType } = this.props;
    const { dis = [], column = [] } = this.getTableCols(tableType);

    for (let obj of checkData) {
      let find = column.find((ob) => ob['key'] == obj);
      if (find) {
        columns.push(find['value']);
      }
    }
    this.setState({
      columns,
      visible2: false,
    });
  };
  handleCancel2 = () => {
    this.setState({
      visible2: false,
    });
  };
  showSel = () => {
    this.setState({
      visible2: true,
    });
  };
  exportRes = async () => {
    const { params, pagination } = this.props;
    let dataApi = sessionStorage.getItem('dataApi') || '';
    this.setState({ checkExportLoading: true, progressType: 'checkExport' });
    const { code = 500, data = {} } = await annualExport({
      data: { ...params, pageNum: 1, pageSize: pagination?.total || 1000000 },
    });
    this.setState({ checkExportLoading: false });
    if (code === 0) {
      // fileDownloadHeader(`/api${data}`, data.split('/')[2],{dataApi});
      // Modal.success({
      //   content: '导出成功',
      // });
      // this['SpinProgress'].getProgress(key,'niandu');
    }
  };
  progressCallback = (res) => {
    const { progressType } = this.state;
    switch (progressType) {
      case '1':
        this.setState({ tbDownLoadLoading: false });
        fileDownloadHeader(`/api${res.url}`, res['url'].split('/')[2]);
        Modal.success({
          content: '导出成功',
        });
        break;
    }
  };

  compareData = (oldData, oldNewData, newData) => {
    console.log('🚀 ~ index ~ newData11111111111:', newData, oldData);
    if (newData.length == 0) {
      return oldData;
    }
    const newColumns: any = [];
    newData.forEach((element) => {
      oldNewData.forEach((item: any) => {
        if (element && item) {
          if (element.value.dataIndex == item.value.dataIndex) {
            newColumns.push(item);
          }
        }
      });
    });
    console.log('🚀 ~ index ~ newData:.....................', newColumns);
    return newColumns;
  };

  //获取动态表头
  getTableHeader = async () => {
    const { params, tableType } = this.props;
    await this.setState({
      tableLoading: true,
    });
    const { data, code } = await getTableHeaderPegging({
      data: {
        colIndex: params.colIndex,
        reportCode: params.reportCode,
        rowIndex: params.rowIndex,
        type: params.type,
        year: params?.year || '',
      },
    });
    if (code == 0) {
      this.setState(
        {
          activeColumns: JSON.parse(data?.includeFieldList || '[]'),
          // activeColumns: data?.fieldList || [],
          tableType: data.reportType,
        },
        () => {
          let columns: any = [];
          const { dis = [], column = [] } = this.getTableCols(data?.reportType || tableType);
          if (!_isEmpty(dis) && !_isEmpty(column)) {
            //正常获取反查表头
            for (let obj of dis) {
              let find: any = column.find((ob) => ob['key'] == obj);
              if (find) {
                columns.push(find['value']);
              }
            }
            if (params?.reportCode == '5.html') {
              columns = [
                ...columns,
                { title: '国民经济行业', dataIndex: 'd194_name', width: 70, key: 'd194_name' },
                { title: '生产性服务行业', dataIndex: 'd195_name', width: 70, key: 'd195_name' },
              ];
            }
            // console.log(columns, "columnscolumnscolumnscolumns");
            this.setState({
              columns,
              checkData: dis,
            });
          }
        },
      );
    }
    this.setState({
      tableLoading: false,
    });
  };

  componentDidUpdate(prevProps, prevState) {
    if (this.state.visible && this.state.visible !== prevState.visible) {
      this.getTableHeader();
    }
  }

  static getDerivedStateFromProps(props, state) {
    //专题表增加反查项
    const { params } = props;
    const { _tableType, getTableCols, _reportCode, tableType } = state;
    let columns: any = [];
    // if (tableType != _tableType) {
    //   //表头发生变化
    //   const { dis = [], column = [] } = getTableCols(tableType);
    //   if (!_isEmpty(dis) && !_isEmpty(column)) {
    //     //正常获取反查表头
    //     for (let obj of dis) {
    //       let find: any = column.find((ob) => ob['key'] == obj);
    //       if (find) {
    //         columns.push(find['value']);
    //       }
    //     }
    //     console.log(columns, "columnscolumnscolumnscolumns");
    //     return { columns, checkData: dis, _tableType: tableType };
    //   }
    // }
    // if (params?.reportCode != _reportCode) {
    //   const { dis = [], column = [] } = getTableCols(tableType);
    //   if (!_isEmpty(dis) && !_isEmpty(column)) {
    //     for (let obj of dis) {
    //       let find: any = column.find((ob) => ob['key'] == obj);
    //       if (find) {
    //         columns.push(find['value']);
    //       }
    //     }
    //     if (params?.reportCode == '5.html') {
    //       columns = [
    //         ...columns,
    //         { title: '国民经济行业', dataIndex: 'd194_name', width: 70, key: 'd194_name' },
    //         { title: '生产性服务行业', dataIndex: 'd195_name', width: 70, key: 'd195_name' },
    //       ];
    //     }
    //     return { columns, checkData: dis, _reportCode: params?.reportCode };
    //   }
    // }
    return null;
  }
  boxChange = (val) => {
    this.setState({
      checkData: val,
    });
  };

  render() {
    const { visible, visible2, columns, checkData = [], checkExportLoading, tableLoading } = this.state;
    const { list = [], pagination, tableType } = this.props;
    const { dis = [], column = [] } = this.getTableCols(tableType);
    console.log("00000000000000000000000", columns);
    return (
      <React.Fragment key={0}>
        <Modal
          maskClosable={false}
          title={
            <span>
              显示列设置
              <span style={{ color: 'red', marginLeft: 16, fontSize: 14, lineHeight: '16px' }}>勾选列名后反查信息将会根据勾选项进行动态变化展示，便于统计涉及基本信息核实</span>
            </span>
          }
          visible={visible2}
          onOk={this.handleOk}
          onCancel={this.handleCancel2}
          width={900}
        >
          <div className={styles.clu}>
            <Checkbox.Group value={checkData} onChange={this.boxChange}>
              {column.map((obj, index) => {
                if (!obj['value']) {
                  return (
                    <React.Fragment key={1}>
                      {index != 0 ? <WhiteSpace /> : null}
                      <Alert message={obj['label']} type="info" />
                      <WhiteSpace />
                    </React.Fragment>
                  );
                }
                // if (dis.includes(obj['key'])) {
                //   return (
                //     <Checkbox key={obj['key']} value={obj['key']} disabled checked>
                //       {obj['label']}
                //     </Checkbox>
                //   )
                // }
                return (
                  <Checkbox key={obj['key']} value={obj['key']}>
                    {obj['label']}
                  </Checkbox>
                );
              })}
            </Checkbox.Group>
          </div>
        </Modal>
        <Modal
          maskClosable={false}
          title={
            <div>
              反查结果
              {/* {tableType !== 'institution' && <Button type={'primary'} onClick={this.showSel} style={{ marginLeft: 10 }}>显示列设置</Button>} */}
              <Button type={'primary'} onClick={this.exportRes} loading={checkExportLoading} style={{ marginLeft: 10 }}>
                导出
              </Button>
            </div>
          }
          visible={visible}
          onOk={this.handleCancel}
          onCancel={this.handleCancel}
          width={1360}
          footer={null}
          bodyStyle={{ minHeight: 0 }}
        >
          <SpinProgress ref={(e) => (this['SpinProgresses'] = e)} callback={(res) => this.progressCallback(res)}>
            <div className={styles.list}>
              <ListTable
                rowKey={'_ids'}
                scroll={{
                  x: columns.reduce((total: any, it: any) => {
                    return total + it.width;
                  }, 80),
                  y: 500,
                }}
                columns={columns}
                data={list}
                pagination={pagination}
                onPageChange={this.props.pageChange}
              />
            </div>
          </SpinProgress>
        </Modal>
      </React.Fragment>
    );
  }
}
