import React, { Fragment } from 'react';
import style from './index.less';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Select, Alert, Modal, Spin, Input, Button, DatePicker } from 'antd';
import CheckTypes from './checkTypes';
import MemSelect from '@/components/MemSelect';
import SelfCheckMem from './selfCheckMem';
import Reason from './reason';
import Editor from '@/components/Editor';
import UploadComp from '@/components/UploadComp';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _isEqual from 'lodash/isEqual';
import _get from 'lodash/get';
import {
  self, chooseTypes, returnOringalObject, changeMemStyle, getArrCodes,
  reventChangeMemStyle, getAbsenceStyle, changeDxzStyle, changeDxzMemStyle, changeZwhStyle, returnZwhMemStyle
} from './config';
import { getSession } from '@/utils/session';
import { throttle } from '@/utils/method.js';
import { connect } from 'dva';
import moment from 'moment';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const FormItem = Form.Item;
const Option = Select.Option;
const { RangePicker } = DatePicker;
@connect(({ activityManage, commonDict, loading }) => ({ activityManage, commonDict, loading }))
class index extends React.Component<any, any> {
  static getDxz = () => { };
  static getDxzMem = (any) => { };
  static getZwhMem = (any) => { };
  constructor(props) {
    super(props);
    this.state = {
      types: [], // 活动类型
      finalAbsenceList: [], //最后的缺席人员包含缺席原因，以及整合格式了
      state_absenceList: [],
      state_memList: [],
      dxzArr: [], // 党小组总集合
      dxzMem: [], // 党小组人员总集合
      zwhMem: [], // 支委会人员总集合
    };
    index.getDxz = this.getDxz;
    index.getDxzMem = this.getDxzMem;
    index.getZwhMem = this.getZwhMem;
  }
  componentDidMount(): void {
    const { commonDict, dispatch, checkQuery: { isEdit = false } = {} } = this.props;
    const { dict_activity_type = [] } = commonDict;
    if (_isEmpty(dict_activity_type)) {
      dispatch({
        type: 'commonDict/getDictTree',
        payload: {
          data: {
            dicName: 'dict_activity_type'
          }
        }
      });
    }
  }
  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    const state = [];
    const { activityManage: { details = {} } = {}, form } = nextProps;
    const { _details } = prevState;
    if (!_isEqual(details, _details)) {
      state['_details'] = details;
      const { absenceList, memList, typeCodes, groupCode } = details;
      state['state_absenceList'] = reventChangeMemStyle(absenceList, '0', true);
      state['state_memList'] = reventChangeMemStyle(memList, '0');

      state['memList'] = memList;
      state['groupCode'] = groupCode;

      state['finalAbsenceList'] = getAbsenceStyle(absenceList);
      state['types'] = typeCodes;
      if (_includes(typeCodes, '23')) {
        index.getDxz();
        index.getDxzMem(_isEmpty(details) ? undefined : _get(details, 'groupCode', '').split(','));
      }
      if (_includes(typeCodes, '22')) {
        index.getZwhMem(_isEmpty(details) ? undefined : _get(details, 'acOrgCode', ''))
      }
    }
    return state;
  };
  disabledDate = (current) => {
    const { checkQuery } = this.props;
    const { isSupplt } = checkQuery;
    if (isSupplt) {
      return current && current > moment().endOf('day')
    } else {
      return current && current < moment().endOf('day')
    }
  };
  editorOnChange = (val, type) => {
    const { setFieldsValue } = this.props.form;
    setFieldsValue({
      [type]: val,
    });
  };
  // 党小组的请求们
  getDxz = async () => {
    const { checkQuery: { isEdit = false } = {}, activityManage: { details = {} } = {} } = this.props;
    const res = await this.props.dispatch({
      type: 'activityManage/getGrouop',
      payload: {
        orgCode: isEdit ? details['acOrgOrgCode'] : _get(getSession('org'), 'orgCode', '')
      }
    });
    if (_isEmpty(res)) {
      Tip.warning('操作提示', '该组织下暂无党小组，请到党组织管理中维护。')
    }
    this.setState({
      dxzArr: res,
    });
  };
  getDxzInfo = async () => {
    await this.getDxz();
    this.setState({
      finalAbsenceList: [],
      state_absenceList: [],
      state_memList: [],
      memList: [],
    });
    this.props.form.setFieldsValue({
      absenceList: undefined,
      memList: undefined,
      _memList: undefined,
    })
  };
  getDxzMem = async (val) => {
    const res = await this.props.dispatch({
      type: 'activityManage/getGrouopMem',
      payload: {
        data: {
          pageNum: 1,
          pageSize: 100,
          groupList: val
        }
      }
    });
    this.setState({
      dxzMem: changeDxzStyle(res)
    });
  };
  getDxzMemInfo = async (val) => {
    await this.getDxzMem(val);
    this.setState({
      finalAbsenceList: [],
      state_absenceList: [],
      state_memList: [],
      memList: [],
    });
    this.props.form.setFieldsValue({
      absenceList: undefined,
      memList: undefined,
      _memList: undefined,
    })
  };
  // 支委会集合
  getZwhMem = async (code) => {
    const res = await this.props.dispatch({
      type: 'activityManage/getCommitteeList',
      payload: {
        code
      }
    });
    const { data: { havaNewElect = true, electList = [] } = {} } = res;
    if (!havaNewElect) {
      Tip.warning('操作提示', '该组织下暂无支委会成员，请到党组织管理中维护。')
    }
    this.setState({
      zwhMem: changeZwhStyle(electList)
    })
  };
  getZwhMemInfo = async () => {
    const { checkQuery: { isEdit = false } = {}, activityManage: { details = {} } = {} } = this.props;
    await this.getZwhMem(isEdit ? details['acOrgCode'] : _get(getSession('org'), 'code', ''))
  };

  // 活动选择
  onActivityTypeChange = (val) => {
    if (_includes(val, '23')) {
      throttle(this.getDxzInfo, val, 500, 1000)
    }
    if (_includes(val, '22')) {
      throttle(this.getZwhMemInfo, val, 500, 1000)
    }
    this.setState({
      types: val
    });
    const { checkQuery: { isEdit = true } = {}, form } = this.props;
    if (!isEdit) {
      this.setState({
        state_absenceList: []
      });
      form.setFieldsValue({ absenceList: undefined })
    }
    if (!_isEmpty(_get(this, 'memList.wrappedInstance', ''))) {
      this['memList'].clearAll();
    }
    this.setState({
      finalAbsenceList: [],
      state_absenceList: [],
      state_memList: [],
      dxzArr: [],
      dxzMem: [],
      zwhMem: [],
      memList: [],
      groupCode: []
    });
    form.setFieldsValue({
      absenceList: undefined,
      memList: undefined,
      _memList: undefined,
    })

  };
  // 提交
  onSubmit = () => {
    const { finalAbsenceList, dxzMem, dxzArr, zwhMem } = this.state;
    const { form, commonDict: { dict_activity_type = [] } = {}, onClose, checkQuery: { isEdit = false, isSupplt = false } = {}, activityManage: { details = {} } = {} } = this.props;
    form.validateFields(async (err, values) => {
      if (_isEmpty(values['lecturerList']) && _isEmpty(values['selfLecturer'])) {
        form.setFields({
          lecturerList: { errors: [new Error('手填人员或者选择人员必填一项')] }
        });
      }
      if (_isEmpty(values['hostorList']) && _isEmpty(values['selfHostor'])) {
        form.setFields({
          lecturerList: { errors: [new Error('手填人员或者选择人员必填一项')] }
        });
      }
      if (!err) {
        const { typeCodes } = values;
        values = self(['selfHostor', 'selfAttend', 'selfLecturer'], values);
        // 人员格式更新
        values['hostorList'] = changeMemStyle(values['hostorList'], '0').concat(changeMemStyle(values['selfHostor'], '1'));
        values['lecturerList'] = changeMemStyle(values['lecturerList'], '0').concat(changeMemStyle(values['selfLecturer'], '1'));
        values['attendList'] = changeMemStyle(values['attendList'], '0').concat(changeMemStyle(values['selfAttend'], '1'));
        values['memList'] = changeMemStyle(values['memList'], '0');
        if (_includes(typeCodes, '23')) {
          values['memList'] = changeDxzMemStyle(returnOringalObject(values['_memList'], dxzMem));
          delete values['_memList'];
          // values['groupName'] = getArrCodes(dxzArr,'name').toString();
          values['groupCode'] = _isEmpty(values['groupCode']) ? '' : values['groupCode'].toString();
        }
        if (_includes(typeCodes, '22')) {
          values['memList'] = returnZwhMemStyle(returnOringalObject(values['_memList'], zwhMem));
          delete values['_memList'];
        }
        values['location'] = '0,0,0,0';
        let _finalAbsenceList: Array<any> = [];
        if (!_isEmpty(values['absenceList']) && !_isEmpty(finalAbsenceList)) {
          values['absenceList'].map(item => {
            finalAbsenceList.map(it => {
              if (item === it['memCode']) {
                _finalAbsenceList.push(it)
              }
            })
          })
        }
        values['absenceList'] = _finalAbsenceList;
        if (_includes(typeCodes, '23')) { // 党小组缺席

        }
        delete values['selfAttend'];
        delete values['selfLecturer'];
        delete values['selfHostor'];

        // 活动时间
        values['endTime'] = _isEmpty(values['holdTime']) ? undefined : moment(values['holdTime'][1], 'YYYY-MM-DD').valueOf();
        values['holdTime'] = _isEmpty(values['holdTime']) ? undefined : moment(values['holdTime'][0], 'YYYY-MM-DD').valueOf();

        if (!isSupplt) {
          //签到方式 通知方式
          values['canReview'] = _isEmpty(values['canReview']) ? '' : values['canReview'].toString();
          values['noticePlan'] = _isEmpty(values['noticePlan']) ? '' : values['noticePlan'].toString();
        }
        // 活动名称 code
        let typeNames: Array<string> = [];
        typeCodes.forEach(item => {
          dict_activity_type.forEach(it => {
            if (it['key'] === item) {
              typeNames.push(it['name'])
            }
          })
        });
        values['typeNames'] = typeNames;
        // console.log({...values},'va')
        let res, text, vals;
        if (!isEdit) {
          text = 'activityManage/add';
          vals = { ...values }
        } else {
          text = 'activityManage/edit';
          vals = { ...details, ...values }
        }
        res = await this.props.dispatch({
          type: text,
          payload: {
            data: {
              ...vals
            }
          }
        });
        const { code: resCode } = res;
        if (resCode === 0) {
          Tip.success('操作提示', '操作成功');
          onClose && onClose();
        }
      }
    })
  };
  // 缺席原因呢选择
  reasonOnChange = (arr) => {
    this.setState({
      finalAbsenceList: arr
    })
  };
  // 缺席选择
  absenceListOnChange = (val) => {
    const { checkQuery: { isEdit = false } = {}, form } = this.props;
    const { state_absenceList, types, dxzMem, zwhMem } = this.state;
    let all = _includes(types, '23') ? dxzMem : _includes(types, '22') ? zwhMem : form.getFieldValue('memList');
    let newArr: Array<any> = returnOringalObject(val, all);
    let newArr2: Array<any> = [];
    if (!_isEmpty(val)) {
      for (let obj of newArr) {
        const find = state_absenceList.find(ob => ob['code'] === obj['code']);
        if (find) {
          newArr2.push(find);
        } else {
          newArr2.push(obj)
        }
      }
    }
    this.setState({
      state_absenceList: newArr2
    })
  };
  // 应到选择
  memListOnChange = (val) => {
    const { form } = this.props;
    this.setState({
      state_memList: val,
      state_absenceList: []
    });
    form.setFieldsValue({ absenceList: undefined })
  };
  chooseDxz = (val) => {
    const { form } = this.props;
    const { dxzMem, types, zwhMem } = this.state;
    this.setState({
      state_memList: _includes(types, '23') ? returnOringalObject(val, dxzMem) : returnOringalObject(val, zwhMem),
      state_absenceList: []
    });
    form.setFieldsValue({ absenceList: undefined })
  };
  render() {
    const { types, state_memList, state_absenceList, dxzArr, dxzMem, zwhMem } = this.state;
    const { form, commonDict, checkQuery, activityManage, loading: { effects = {} } = {} } = this.props;
    const { details } = activityManage;
    const { isSupplt, isEdit } = checkQuery;
    const { dict_activity_type } = commonDict;
    const { getFieldDecorator } = form;

    // 活动类型
    let dictAcTypes: Array<any> = chooseTypes(dict_activity_type, isEdit, details);
    // 获取录取活动或者录入的组织code
    let acOrgCode = '', acOrgOrgCode = '';
    const currentOrg = getSession('org') || {};
    // 获取录取活动的组织code
    if (isEdit) {
      const { acOrgOrgCode: detailAcOrgOrgCode = '', acOrgCode: detailAcOrgCode = '' } = details || {};
      acOrgOrgCode = detailAcOrgOrgCode;
      acOrgCode = detailAcOrgCode;
    } else {
      acOrgCode = currentOrg['code'];
      acOrgOrgCode = currentOrg['orgCode'];
    }

    const notice = (
      <div>
        <div>活动类型选择提示</div>
        <div> 1. 编辑时不能更改活动类型，如果新增时重新选择了活动类型，缺席人员应当重新选择才能生效。</div>
        <div> 2. <span style={{ color: 'red' }}>"党员大会"，"支委会"，"党小组会"</span>只能任选其一。</div>
      </div>
    );
    return (
      <Spin tip="数据正在加载..." spinning={!!effects['activityManage/getDtail']}>
        <Form className={style.acForm}>
          <Alert message={notice} type="info" />
          <FormItem
            label={`${isEdit ? '活动组织' : '当前组织'}`}
            {...formItemLayout1}
          >
            {!_isEmpty(details) ? details['orgShortName'] : currentOrg['name']}
          </FormItem>
          <FormItem
            label="活动类型"
            {...formItemLayout1}
          >
            {getFieldDecorator('typeCodes', {
              rules: [{ required: true, message: '请选择活动类型' }],
              initialValue: _get(details, 'typeCodes', [])
            })(
              <CheckTypes data={dictAcTypes}
                rule={['21', '22', '23']}
                onChange={this.onActivityTypeChange}
                init={_get(details, 'typeCodes', [])}
              // canChange={!isEdit}
              />
            )}
          </FormItem>
          <FormItem
            label="活动名称"
            {...formItemLayout1}
          >
            {getFieldDecorator('name', {
              rules: [{ required: true, message: '请填写活动名称' }],
              initialValue: _isEmpty(details) ? undefined : details['name']
            })(
              <Input placeholder={'请填写活动名称'} />
            )}
          </FormItem>
          {
            _includes(types, '23') &&
            <FormItem
              label="党小组"
              {...formItemLayout1}
            >
              {getFieldDecorator('groupCode', {
                rules: [{ required: true, message: '请选择党小组' }],
                initialValue: !_isEmpty(_get(this.state, 'groupCode', [])) ? _get(this.state, 'groupCode', []).split(',') : []
              })(
                <Select
                  mode="multiple"
                  placeholder="请选择党小组"
                  // disabled={isEdit}
                  onChange={(val) => { throttle(this.getDxzMemInfo, val, 1000, 1500) }}
                >
                  {
                    !_isEmpty(dxzArr) && dxzArr.map(item => <Option key={item['code']}>{item['name']}</Option>)
                  }
                </Select>
              )}
            </FormItem>
          }
          <FormItem
            label="活动时间"
            {...formItemLayout1}
          >
            {getFieldDecorator('holdTime', {
              rules: [{ required: true, message: '请选择活动时间' }],
              initialValue: _isEmpty(details) ? undefined : [moment(details['holdTime']), moment(details['endTime'])]
            })(
              <RangePicker
                showTime={{ format: 'HH:mm' }}
                format="YYYY-MM-DD"
                placeholder={['活动开始时间', '活动结束时间']}
                disabledDate={this.disabledDate}
              />
            )}
          </FormItem>
          <FormItem
            label="活动地址"
            {...formItemLayout1}
          >
            {getFieldDecorator('address', {
              rules: [{ required: true, message: '请选择活动地址' }],
              initialValue: _isEmpty(details) ? undefined : details['address']
            })(
              <Input placeholder={'请填写活动地址'} />
            )}
          </FormItem>
          {
            !isSupplt &&
            <Fragment>
              <FormItem
                label="签到方式"
                {...formItemLayout1}
              >
                {getFieldDecorator('canReview', {
                  rules: [{ required: true, message: '请选择签到方式' }],
                  initialValue: _isEmpty(details) ? undefined : [details['canReview']]
                })(
                  <CheckTypes data={[{ name: '扫码签到', key: '1', }, { name: '打开签到', key: '2', }, { name: '定位签到', key: '3', }]}
                    rule={['1', '2', '3']}
                    init={_isEmpty(details) ? undefined : [details['canReview']]}
                  />
                )}
              </FormItem>
              <FormItem
                label="通知方式"
                {...formItemLayout1}
              >
                {getFieldDecorator('noticePlan', {
                  rules: [{ required: true, message: '请选择通知方式' }],
                  initialValue: _isEmpty(details) ? undefined : [details['noticePlan']]
                })(
                  <CheckTypes data={[{ name: '微信通知', key: '1', }, { name: '短信通知', key: '2', }, { name: '平台通知', key: '3', }]}
                    rule={['1', '2', '3']}
                    init={_isEmpty(details) ? undefined : [details['noticePlan']]}
                  />
                )}
              </FormItem>
            </Fragment>
          }
          <FormItem
            label={<span className={style.redPoint}>主持人</span>}
            {...formItemLayout1}
          >
            {getFieldDecorator('hostorList', {
              rules: [{ required: false, message: '请选择主持人' }],
              initialValue: _isEmpty(details) ? undefined : reventChangeMemStyle(_get(details, 'hostorList', []).filter(it => it['isCustomize'] === '0'), '0')
            })(
              <MemSelect checkType={'checkbox'}
                initValue={getArrCodes(_get(details, 'hostorList', []).filter(it => it['isCustomize'] === '0'), 'name').toString()}
              // org={{orgCode:acOrgCode}}
              />
            )}
            <SelfCheckMem {...this.props} type={'selfHostor'} init={_get(details, 'hostorList', []).filter(it => it['isCustomize'] === '1')} />
          </FormItem>
          <FormItem
            label="列席人员"
            {...formItemLayout1}
          >
            {getFieldDecorator('attendList', {
              rules: [{ required: false, message: '列席人员' }],
              initialValue: _isEmpty(details) ? undefined : reventChangeMemStyle(_get(details, 'attendList', []).filter(it => it['isCustomize'] === '0'), '0')
            })(
              <MemSelect checkType={'checkbox'}
                initValue={getArrCodes(_get(details, 'attendList', []).filter(it => it['isCustomize'] === '0'), 'name').toString()}
              // org={{orgCode:acOrgCode}}
              />
            )}
            <SelfCheckMem {...this.props} type={'selfAttend'} init={_get(details, 'attendList', []).filter(it => it['isCustomize'] === '1')} />
          </FormItem>
          {
            (_includes(types, '24') || _includes(types, '17')) &&
            <FormItem
              label={<span className={style.redPoint}>讲课人</span>}
              {...formItemLayout1}
            >
              {getFieldDecorator('lecturerList', {
                rules: [{ required: false, message: '讲课人' }],
                initialValue: _isEmpty(details) ? undefined : reventChangeMemStyle(_get(details, 'lecturerList', []).filter(it => it['isCustomize'] === '0'), '0')
              })(
                <MemSelect checkType={'checkbox'}
                  initValue={getArrCodes(_get(details, 'lecturerList', []).filter(it => it['isCustomize'] === '0'), 'name').toString()}
                // org={{orgCode:acOrgCode}}
                />
              )}
              <SelfCheckMem {...this.props} type={'selfLecturer'} init={_get(details, 'lecturerList', []).filter(it => it['isCustomize'] === '1')} />
            </FormItem>
          }
          {
            (_includes(types, '23') || _includes(types, '22')) ?
              <FormItem
                label="应到人员"
                {...formItemLayout1}
              >
                {getFieldDecorator('_memList', {
                  rules: [{ required: true, message: '应到人员' }],
                  initialValue: _isEmpty(details) ? undefined : getArrCodes(_get(this.state, 'memList'), 'memCode')
                })(
                  <Select
                    mode="multiple"
                    placeholder="应到人员"
                    onChange={this.chooseDxz}
                  >
                    {
                      _includes(types, '23') ?
                        !_isEmpty(dxzMem) && dxzMem.map(item => <Option key={item['code']}>{item['name']}</Option>)
                        :
                        !_isEmpty(zwhMem) && zwhMem.map(item => <Option key={item['code']}>{item['name']}</Option>)
                    }
                  </Select>
                )}
              </FormItem>
              :
              <FormItem
                label="应到人员"
                {...formItemLayout1}
              >
                {getFieldDecorator('memList', {
                  rules: [{ required: true, message: '应到人员' }],
                  initialValue: _isEmpty(details) ? undefined : reventChangeMemStyle(_get(this.state, 'memList', []), '0')
                })(
                  <MemSelect checkType={'checkbox'}
                    onChange={this.memListOnChange}
                    ref={e => this['memList'] = e}
                    initValue={getArrCodes(_get(this.state, 'memList', []).filter(it => it['isCustomize'] === '0'), 'name').toString()}
                  // org={{orgCode:acOrgCode}}
                  />
                )}
              </FormItem>
          }
          {
            isSupplt &&
            <FormItem
              label="缺席人员"
              {...formItemLayout1}
            >
              {getFieldDecorator('absenceList', {
                rules: [{ required: false, message: '缺席人员' }],
                initialValue: _isEmpty(details) ? undefined : getArrCodes(_get(details, 'absenceList'), 'memCode')
              })(
                <Select
                  mode="multiple"
                  placeholder="缺席人员根据实际情况填写"
                  onChange={this.absenceListOnChange}
                >
                  {
                    !_isEmpty(state_memList) && state_memList.map((item, index) => {
                      return (
                        <Option key={index} value={item['code']}>{item['name']}</Option>
                      )
                    })
                  }
                </Select>
              )}
            </FormItem>
          }
          <Reason absenceList={state_absenceList} {...this.props} onChange={this.reasonOnChange} />
          <FormItem
            label="主要议程"
            {...formItemLayout1}
          >
            {getFieldDecorator('acPlan', {
              rules: [{ required: false, message: '主要议程' }],
              initialValue: _isEmpty(details) ? '' : details['acPlan']
            })(
              <Editor id={'acPlan'} onChange={(val) => this.editorOnChange(val, 'acPlan')} init={_isEmpty(details) ? '' : details['acPlan']} />
            )}
          </FormItem>
          <FormItem
            label="活动内容"
            {...formItemLayout1}
          >
            {getFieldDecorator('content', {
              rules: [{ required: true, message: '活动内容' }],
              initialValue: _isEmpty(details) ? '' : details['content']
            })(
              <Editor id={'content'} onChange={(val) => this.editorOnChange(val, 'content')} init={_isEmpty(details) ? '' : details['content']} />
            )}
          </FormItem>
          {/* <FormItem
            label="议程文件"
            {...formItemLayout1}
          >
            {getFieldDecorator('acPlanFile', {
              rules: [{ required: false, message: '议程文件' }],
              // initialValue:empty(code)?empty(this.state.selectedTags)?'':this.state.selectedTags:FormInfo.type_codes
            })(
              <UploadComp init={[]}/>
            )}
          </FormItem> */}
          {/* <FormItem
            label="活动文件"
            {...formItemLayout1}
          >
            {getFieldDecorator('acFile', {
              rules: [{ required: false, message: '活动文件' }],
              // initialValue:empty(code)?empty(this.state.selectedTags)?'':this.state.selectedTags:FormInfo.type_codes
            })(
              <UploadComp init={[]}/>
            )}
          </FormItem> */}

          {getFieldDecorator('status', {
            initialValue: !isSupplt ? '1' : '3'
          })(
            <div />
          )}
          {getFieldDecorator('acOrgCode', {
            initialValue: acOrgCode
          })(
            <div />
          )}
          {getFieldDecorator('acOrgOrgCode', {
            initialValue: acOrgOrgCode
          })(
            <div />
          )}
          {getFieldDecorator('code', {
            initialValue: _isEmpty(details) ? undefined : details['code']
          })(
            <div />
          )}

          <div className={style.submit}>
            <Button type="primary" onClick={this.onSubmit} loading={isEdit ? effects['activityManage/edit'] : effects['activityManage/add']} >提交</Button>
          </div>
        </Form>
      </Spin>
    );
  }
}
export default Form.create()(index);
