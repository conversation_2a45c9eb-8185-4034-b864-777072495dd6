import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Col, Form, Input, Modal, Radio, Row } from 'antd';
import DictSelect from '@/components/DictSelect';
import {updateByFileType} from '@/pages/transfer/services'
import Tip from '@/components/Tip';

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const formItemLayout2 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const { TextArea } = Input;
const RadioGroup = Radio.Group;
const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [list, setList] = useState([]);
  const [pagination, setPagination] = useState({ pageSize: 10, total: 0, page: 1 });
  const [tableLoading, setTableLoading] = useState(false);
  const {
    width = 800,
  } = props;

  useImperativeHandle(ref, () => ({
    open: query => {
      if (query) {
        setDataInfo(query);
        form.setFieldsValue(query);
      }
      open();
      // setQurey({ ...query, _key: +new Date() });
    },
  }));
  const open = () => {
    setVisible(true);
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    const { onOK } = props;
    let val = {
      ...e,
      id:dataInfo?.id
    }
    setConfirmLoading(true);
    const {code = 500} = await updateByFileType({
      data:{
        ...val,
      }
    });
    setConfirmLoading(false);
    if(code === 0){
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  return (
    <Fragment>
      <Modal
        title={'档案管理'}
        visible={visible}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
      >
        {
          visible && <Fragment>
            <Form form={form} {...formItemLayout} onFinish={onFinish}>
              <Row>
                <Col span={12}>
                  <Form.Item noStyle name='d92Name' style={{display:'none'}}><Input style={{display:'none'}}/></Form.Item>
                  <Form.Item name='d92Code'
                             label="档案处理方式"
                             rules={[{ required: true, message: '档案处理方式' }]}
                  >
                    <DictSelect codeType={'dict_d92'}
                                initValue={dataInfo?.d92Name}
                                onChange={(e)=>{
                                  form.setFieldsValue({
                                    d92Code:e.key,
                                    d92Name:e.name,
                                  })
                                }}
                                backType={'object'}/>
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) => prevValues.d92Code !== currentValues.d92Code}
                  >
                    {({ getFieldValue }) => {
                      return getFieldValue('d92Code') === '3' ? (
                        <Form.Item name='remark'
                                   {...formItemLayout2}
                                   label="备注"
                                   rules={[{ required: true, message: '请填写快递公司以及单号' }]}
                        >
                          <TextArea placeholder={'请填写快递公司以及单号'} rows={3}/>
                        </Form.Item>
                      ) : null;
                    }}
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Fragment>
        }
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
