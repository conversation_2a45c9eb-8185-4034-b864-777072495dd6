import React, { useState, useEffect } from 'react';
import { Select, Modal, Input } from 'antd';
import { getUnitName } from '@/services';

interface pType {
  onChange?: Function;
  params?: object;
  backType?: string;
  style?: any;
  action?: any;
  initName?: any;
  initCode?: any;
  children?: React.ReactElement;
  placeholder?: string;
  disabled?: boolean;
}

export default function SearchUnit(props: pType) {
  const [data, setData] = useState([]);
  const [value, setValue] = useState();
  const [valueName, setValueName] = useState();
  const [valueObj, setValueObj] = useState({});
  const [visible, setVisible] = useState(false);
  const {
    onChange,
    backType = 'string',
    params = {},
    style,
    action: propsAciton = getUnitName,
    initName,
    initCode,
    children,
    disabled = false,
    placeholder = '请输入关键字进行搜索匹配',
  } = props;
  const action = (para) => {
    propsAciton &&
      propsAciton({
        data: {
          pageNum: 1,
          pageSize: 100,
          ...para,
          ...params,
        },
      }).then((res) => {
        if (res['code'] == 0) {
          setData(res['data']['list']);
          if (res['data'] && res['data']['list'] && res['data']['list'].length > 0) {
            setValue(initCode);
          }
        }
      });
  };
  const handleSearch = (val) => {
    action({ orgName: val });
  };
  const handleChange = (val, option) => {
    console.log(val, option, 'asdddddddddddddd');
    setValue(val);
    setValueObj(option ? option.dataref : {});
  };
  const show = () => {
    setVisible(true);
  };
  const onOk = () => {
    setValueName(valueObj['name']);
    if (onChange) {
      if (backType == 'object') {
        onChange({ ...valueObj });
      } else {
        onChange(value, { ...valueObj });
      }
    }
    onCancel();
  };
  const onCancel = () => {
    setVisible(false);
  };
  useEffect(() => {
    if (initName) {
      setValueName(initName);
      action({ orgName: initName });
    }
  }, [initName]);
  return (
    <React.Fragment>
      {children ? (
        React.cloneElement(children as any, {
          onClick: show,
        })
      ) : (
        <Input.Search
          value={valueName}
          onClick={show}
          placeholder={placeholder}
          readOnly
          enterButton
        />
      )}
      <Modal
        title={'单位查询选择器'}
        destroyOnClose
        visible={visible}
        onOk={onOk}
        onCancel={onCancel}
        width={800}
        okButtonProps={{
          disabled: !valueObj['name'],
        }}
        okText={'确定'}
        cancelText={'取消'}
      >
        <Select
          disabled={disabled}
          showSearch
          value={value}
          defaultActiveFirstOption={false}
          showArrow={false}
          filterOption={false}
          onSearch={handleSearch}
          // onChange={handleChange}
          onSelect={handleChange}
          notFoundContent={null}
          style={{ width: '100%', ...style }}
        >
          {data.map((item) => (
            <Select.Option value={item['code']} dataref={item}>
              {item['parentName'] ? `${item['parentName']} -> ` : ''}
              {item['name']}
            </Select.Option>
          ))}
        </Select>
      </Modal>
    </React.Fragment>
  );
}
