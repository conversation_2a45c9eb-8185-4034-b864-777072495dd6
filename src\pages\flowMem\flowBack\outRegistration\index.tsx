/**
 * 流出登记
 */

import React from 'react'
import { Avatar, Button, Col, Dropdown, Menu, Modal, Row } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import NowOrg from 'src/components/NowOrg';
// import Leader from './leader';

import qs from 'qs';
import {connect} from "dva";
import ListTable from 'src/components/ListTable';
import {_history as router} from "@/utils/method";


const menuData=[
  {
    code:'1',
    name:'基本信息',
    icon:'star',
  },
  {
    code:'2',
    name:'班子成员',
    icon:'qrcode',
  },
];
@connect(({unit,commonDict,loading})=>({unit,commonDict,loading:loading.effects['unit/getList']}))
export default class index extends React.Component<any,any>{
  static show(){};
  static close(){};
  static clear(){};
  constructor(props){
    super(props);
    let obj=menuData[0];
    this.state={
      visible:false,
      key:obj['code'],
      keyPath:[obj['code']],
      selected:obj,
    };
    index.show=this.open.bind(this);
    index.close=this.handleCancel.bind(this);
    index.clear=this.destroy.bind(this);
  }
  handleOk=()=>{
    this.handleCancel();
  };
  handleCancel=()=>{
    this.setState({
      visible:false
    })
  };
  open=()=>{
    this.setState({
      visible:true,
    })
  };
  destroy=()=>{
    let obj=menuData[0];
    this.setState({
      visible:false,
      key:obj['code'],
      keyPath:[obj['code']],
      selected:obj,
    });
    this.props.dispatch({//重置model
      type:'unit/updateState',
      payload:{
        basicInfo:{},
      }
    })
  };
  onSelect=(item)=>{
    const {key,keyPath}=item;
    const selected=menuData.find(obj=>obj['code']===key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  outMem=(v)=>{

  };
  render() {
    const {visible,selected,keyPath,key,pagination={}}=this.state;
    const {basicInfo={}}=this.props.unit;
    const { filterHeight,loading }=this.props;

    const columns=[
      {
        title:'姓名',
        dataIndex:'name',
      },
      {
        title:'组织',
        dataIndex:'d04Name',
      },
      {
        title:'状态',
        dataIndex:'d35Name',
      },

      {
        title:'操作',
        dataIndex:'action',
        width:100,
        render:(text,record)=>{
          return(
              <a className="ant-dropdown-link" href="#" onClick={()=>this.outMem(record)}>流动</a>
          )
        },
      },
    ];
    let data=[
      {
        name:'1',
        d04Name:'2',
        d35Name:'3',
      }
    ];
    return(
      <Modal
        title="流出登记"
        className='out_Modal'
        destroyOnClose
        closable={false}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        footer={false}
        width={700}
      >
        <div className='container'>
          <NowOrg/>

          <div>
            <ListTable scroll={{y:filterHeight}}
             
              columns={columns} data={data} pagination={pagination} onPageChange={this.onPageChange}/>
          </div>
        </div>
      </Modal>
    )
  }
}
