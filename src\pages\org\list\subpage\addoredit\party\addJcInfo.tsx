/**
 * 添加届次信息
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Alert,
  Button,
  Col,
  DatePicker,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Select,
  Upload,
} from "antd";
import WhiteSpace from '@/components/WhiteSpace'
import Tip from '@/components/Tip';
import moment from 'moment';
import Date from '@/components/Date';
import {uuid} from '@/utils/method';
import UploadComp,{getInitFileList, fitFileUrlForForm} from '@/components/UploadComp';
import {electSave,electUpdate} from '@/pages/org/services/org.js';
const FormItem=Form.Item;
const Option = Select.Option;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};
class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      visible:false,
    }
  }
  showModal=()=>{
    this.setState({
      visible:true,
      startValue:undefined,
      endValue:undefined,
    });
  };

  handleOk=()=>{
    const {children,title,dataInfo={}, type = ''}=this.props;
    this.props.form.validateFieldsAndScroll(async(err, val) => {
      if (!err) {
        // const org=getSession('org') || {};
        const {basicInfo={}}=this.props.org;
        let obj=undefined;
        if(val['tenureEndDate']){
          val['tenureEndDate']=val['tenureEndDate'].valueOf();
        }
        if(val['tenureStartDate']){
          val['tenureStartDate']=val['tenureStartDate'].valueOf();
        }

        if( moment(val['tenureEndDate']).subtract(5, 'years') < val['tenureStartDate'] ){
          this.props.form.setFields({
            tenureEndDate: { errors: [new Error('结束时间要间隔5年或以上')] }
          });
          return;
        }

        if(val['material'] && typeof val['material'] == 'object'){
          // const {data}=val['material']['0']['response']
          // val['material']=data[0]['url'];
          val['material']=fitFileUrlForForm(val['material'])
        }
        val['orgCode']=basicInfo['code'];
        val['electOrgCode']=basicInfo['orgCode'];
        val['zbCode']=basicInfo['zbCode'];
        if(type === 'edit'){
          // orgElect
          obj=await electUpdate({
            data:{
              ...dataInfo,
              ...val
            }
          });
        }else{
          // orgElect
          obj=await electSave({
            data:{
              ...dataInfo,
              ...val
            }
          });
        }
        if(obj && obj['code']===0){
          Tip.success('操作提示',dataInfo['code'] ? '修改成功' : '新增成功');
          this.handleCancel();
          this.props.onClose();
          this.props.queryList();
        }
      }
    });
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
    });
    this.props.onClose();
  };
  onStartChange=(value)=>{
    this.setState({
      startValue:value
    })
  };
  onEndChange=(value)=>{
    this.setState({
      endValue:value
    })
  };
  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.endOf('day') <= startValue.endOf('day');
  };
  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.endOf('day') <= startValue.endOf('day');
  };
  getInitFileList = (val)=>{
    let _name = [];
    if(val){
      _name = val.split('\\') || [];
      return [
        {name:[..._name].pop(),url:_name.join('/'),uid:uuid()}
      ]
    }else {
      return []
    }
  };
  render() {
    const { getFieldDecorator,getFieldValue } = this.props.form;
    // const org=getSession('org') || {};
    let {children,title,dataInfo={}}=this.props;
    const {basicInfo={}}=this.props.org;
    const props = {
      name: 'file',
      action: '/api/base/upload',
      headers: {
        authorization: sessionStorage.getItem('token') || "",dataApi: sessionStorage.getItem('dataApi') || ""
      },
      onChange(info) {
        if (info.file.status !== 'uploading') {
          console.log(info.file, info.fileList);
        }
        if (info.file.status === 'done') {
          message.success(`${info.file.name} 上传成功`);
        } else if (info.file.status === 'error') {
          message.error(`${info.file.name} 上传失败.`);
        }
      },
    };

    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        {
          this.state.visible&&
          <Modal
            title={ title || "请输入标题" }
            visible={this.state.visible}
            onOk={()=>this.handleOk()}
            onCancel={this.handleCancel}
            width={900}
            className='add_modal'
            maskClosable={false}
          >
            <WhiteSpace/>
            <Form {...formItemLayout}>
              <Row>
                <Col span={24}>
                  <FormItem
                    label="组织名称"
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('orgName', {
                      initialValue:basicInfo['name'],
                      rules: [{ required: true, message: '请输入组织名称' }],
                    })(
                      <Input placeholder={'组织全称'} disabled/>
                    )}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label="开始时间"
                  >
                    {getFieldDecorator('tenureStartDate', {
                      initialValue:dataInfo['tenureStartDate'] ? moment(dataInfo['tenureStartDate']) : undefined,
                      rules: [{ required: true, message: '请选择开始时间' }],
                      // <DatePicker placeholder="请选择" style={{width:'100%'}} onChange={this.onStartChange} disabledDate={this.disabledStartDate}/>
                    })(
                      <Date />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="结束时间"
                  >
                    {getFieldDecorator('tenureEndDate', {
                      initialValue:dataInfo['tenureEndDate'] ? moment(dataInfo['tenureEndDate']) : undefined,
                      rules: [{ required: true, message: '请选择结束日期' }],
                    })(
                      <Date startTime={getFieldValue('tenureStartDate')} isDefaultEnd={false}/>
                    )}
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </Modal>
        }

      </React.Fragment>
    )
  }
}
export default Form.create<any>()(index);
