import React, { Fragment } from 'react';
import ListTable from '@/components/ListTable';
import { getSession } from '@/utils/session';
import { PlusOutlined } from '@ant-design/icons';
import { Divider, Popconfirm, Button } from 'antd';
import EditModel from './components/addEdit';
import BackInfo from '@/pages/mem/zy/manage/components/membasic/Abroad/components/backInfo'
import SuspendPartyMembership from '@/pages/mem/zy/manage/components/membasic/Abroad/components/suspendPartyMembership'
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import moment from 'moment';
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {

    }
  }
  componentDidMount(): void {
    const {memBasic:{basicInfo:{code = ''} = {}}={}} = this.props;
    this.getList(1,10,code);
  };
  getList=(pageNum = 1, pageSize = 10, memCode = '')=>{
    !_isEmpty(memCode) && this.props.dispatch({
      type:'memAbroad/getList',
      payload:{pageNum,pageSize, memCode}
    })
  };
  onPageChange=(page)=>{
    const {memBasic:{basicInfo:{code = ''} = {}}={}} = this.props;
    this.getList(page,10,code);
  };
  // 奖惩编辑
  addOrEdit= async (record = {},type = 'edit')=>{
    this['EditModel'].destroy();
    if(!_isEmpty(record['code'])){
      await this.props.dispatch({
        type:'memAbroad/getInfo',
        payload:{
          code:record['code']
        }
      });
    }
    this['EditModel'].open({type:type});
  };
  // 添加回国信息
  addBackInfo= async (record = {})=>{
    this?.backInfoRef?.open(record);
  }
  refreshAbroadList=()=>{
    const {memBasic:{basicInfo:{code = ''} = {}}={}} = this.props;
    this.getList(1,10,code);
  }
  //奖惩删除
  del=async({code = ''}={})=>{
    const {memBasic:{basicInfo:{code:memCode = ''} = {}}={}} = this.props;
    if(!_isEmpty(code)){
      const res = await this.props.dispatch({
        type:'memAbroad/del',
        payload:{code}
      });
      const {code:resCode = 500} = res || {};
      if(resCode === 0){
        Tip.success('操作提示','删除成功');
        this.getList(1,10,memCode)
      }
    }
  };
  componentWillUnmount(): void {
    this.props.dispatch({
      type:'memAbroad/clear',
      payload:{}
    })
  }

  render() {
    const {memAbroad = {},loading:{effects = {}} ={}} = this.props;
    const {list, pagination} = memAbroad;
    const {current, pageSize} = pagination;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:58,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'所至国家（地区）名称',
        dataIndex:'countryCode',
        render:(text,record,index)=>{
          let _t = ''
          if(text == '1'){
            _t = '港澳'
          }
          if(text == '2'){
            _t = '国外'
          }
          if(text == '3'){
            _t = '台湾'
          }
          return _t
        }
      },
      {
        title:'出国（境）日期',
        dataIndex:'abroadDate',
        render:(text, record)=>{
          return (<div>{!_isNumber(text) ? '' : moment(text).format('YYYY-MM-DD')}</div>)
        }
      },
      {
        title:'回国日期',
        dataIndex:'backHomeDate',
        render:(text, record)=>{
          return (<div>{!_isNumber(text) ? '' : moment(text).format('YYYY-MM-DD')}</div>)
        }
      },
      {
        title:'党籍处理方式',
        dataIndex:'d038Name',
      },
      {
        title:'申请保留党籍的时间（月）',
        dataIndex:'partyKeepMonth',
      },
      {
        title:'是否回国',
        dataIndex:'zxc',
        render:(text, record)=>{
          const {backHomeDate = ''} = record;
          return (
            <div>
              {/*{!_isNumber(backHomeDate) ? '否' : moment(new Date()).unix() - moment(backHomeDate).unix() > 0 ? '是' : '否'}*/}
              {_isNumber(backHomeDate) ? '是' : '否'}
            </div>
          )
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        width:150,
        render:(text,record)=>{
          return(
            <span>
              {/* 出国境 当回国后，后面的按钮需要隐藏，只显示详情 */}
              {_isNumber(record?.backHomeDate) ? (<a onClick={()=>this.addOrEdit(record,'read')}>详情</a>) : (<Fragment>
                <a onClick={()=>this.addOrEdit(record,'edit')}>编辑</a>
              {/* 列表有【回国日期】就不显示 【添加回国信息】*/}
              {!(record?.backHomeDate) && <Fragment>
                <Divider type="vertical"/>
                <a onClick={()=>{this.addBackInfo(record)}}>添加回国信息</a>
                </Fragment>}
              <Fragment>
                {/* 停止党籍；出国日期五年以上的有该按钮操作 */}
              {record?.abroadDate && moment().diff(moment(record.abroadDate), 'years') >=5 && <Fragment>
                <Divider type="vertical"/>
              <a onClick={()=>{this?.['suspendPartyMembershipRef']?.open(record)}}>停止党籍</a>
                </Fragment>}
              </Fragment>
              {/* <Popconfirm title="确定要删除吗？" onConfirm={()=>this.del(record)}>
               <a className={'del'} >删除</a>
              </Popconfirm> */}
              </Fragment>)}
              
            </span>
          )
        },
      },
    ];
    return (
      <div>
        <Button htmlType={'button'} type={'primary'} onClick={this.addOrEdit}><PlusOutlined />添加</Button>
        <div style={{marginBottom:'20px'}}/>
        <ListTable columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
        <EditModel wrappedComponentRef={e=>this['EditModel'] = e} {...this.props}/>
          {/* 添加回国信息 */}
        <BackInfo ref={e=>this['backInfoRef'] = e} {...this.props} refreshAbroadList={this.refreshAbroadList} />
          {/* 停止党籍 */}
        <SuspendPartyMembership ref={e=>this['suspendPartyMembershipRef'] = e} {...this.props} />
      </div>
    );
  }
}
