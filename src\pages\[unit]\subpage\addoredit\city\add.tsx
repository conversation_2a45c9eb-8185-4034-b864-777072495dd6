import React, { Fragment, useImperativeHandle, useState, useEffect } from 'react';
import { Form, Input, Modal, InputNumber, Row, Col, Select, Button, message } from 'antd';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import DictTreeSelect from '@/components/DictTreeSelect';
import Notice from '@/components/Notice';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import { cityAdd, cityFind } from '../../../services';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

const index = (props: any, ref) => {
  const { unit: { basicInfo = {} } = {}, onOK } = props;
  const [form] = Form.useForm();
  const org = JSON.parse(sessionStorage.getItem('org') || '{}');
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [title, setTitle] = useState('新增');
  const [dataCode, setDataCode] = useState('');

  useEffect(() => {
    find();
  }, []);

  const find = async () => {
    const { code = 500, data = {} } = await cityFind({ code: basicInfo.code });
    if (code == 0) {
      form.setFieldsValue({ ...data });
      setDataCode(data.code || '');
      setDataInfo({ ...data });
    }
  };
  useImperativeHandle(ref, () => ({
    open: (dataInfo) => {
      open(dataInfo);
    },
  }));
  const open = (dataInfo) => {
    setVisible(true);
    if (!_isEmpty(dataInfo)) {
      setTitle('编辑');
      setDataInfo(dataInfo);
      form.setFieldsValue({
        ...dataInfo,
      });
    }
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    console.log('e1===', e);
    // 以下数据存在大小逻辑关系
    // 已建立党组织的
    const arr3 = [
      { label: '在居民住宅小区、楼院等划分已建立党组织的网格数', code: 'jmzzlyDzWgs' },
      { label: '在商务楼宇、商圈市场等单独划定已建立党组织的网格数', code: 'swsqDzWgs' },
      // { label: '物业服务团队已建立党组织的小区数', code: 'organizationCompanies' },
    ];
    const arr2 = [
      { label: '在居民住宅小区、楼院等划分的网格数', code: 'jmzzlyWgs' },
      { label: '在商务楼宇、商圈市场等单独划定的网格数', code: 'swsqWgs' },
      { label: '街道（乡镇）领导班子成员直接联系的网格数', code: 'jdldWgs' },
    ];
    const arr1 = [{ label: '社区划分网格总数', code: 'grids' }];

    let index = 0;
    for (const item of arr3) {
      if (e[item.code] > e[arr2[index].code]) {
        Modal.warning({
          title: '操作提示',
          content: `“${item.label}” 不能大于 “${arr2[index].label}”`,
        });
        return;
      }
      // // 在商务楼宇、商圈市场等单独划定的网格数 swsqWgs
      // if (e[item.code] > e[arr2[1].code]) {
      //   Modal.warning({
      //     title: '操作提示',
      //     content: `“${item.label}” 不能大于 “${arr2[1].label}”`,
      //   });
      //   return;
      // }
      index++;
    }
    for (const item of arr2) {
      // 社区划分网格总数 grids
      if (e[item.code] > e[arr1[0].code]) {
        Modal.warning({
          title: '操作提示',
          content: `“${item.label}” 不能大于 “${arr1[0].label}”`,
        });
        return;
      }
    }
    // 和不能大于总数
    // const totalNum = [...arr2].reduce((pre, cur) => pre + e[cur.code], 0);
    // if (totalNum > e[arr1[0].code]) {
    //   Modal.warning({
    //     title: '操作提示',
    //     content: `“${arr2[0]?.label}”+“${arr2[1]?.label}”+“${arr2[2]?.label}” 不能大于 “${arr1[0]?.label}” `,
    //   });
    //   return;
    // }
    // 由社区工作者担任的专职网格员数;配备专职网格员数
    if (e['zzWgys'] > e['gridMembers']) {
      Modal.warning({
        title: '操作提示',
        content: `“由社区工作者担任的专职网格员数” 不能大于 “配备专职网格员数”`,
      })
      return;
    }
    if (e['zzNgzze'] > 500) {
      Modal.warning({
        title: '操作提示',
        content: `“全部专职网格员年工资总额（万元）不能大于500”`,
      })
      return;
    }
    // 
    const arrZz3 = [
      { label: '物业服务团队已建立党组织的小区数', code: 'organizationCompanies' },
      { label: '建立党组织的业主委员会数量', code: 'industryAuthorityOrganization' },
    ]
    const arrZz2 = [
      { label: '有物业服务企业提供服务的小区数', code: 'tubePlots' },
      { label: '小区组建的业主委员会数量', code: 'industryAuthorityCommunity' },
    ]
    const arrZz1 = [
      { label: '社区中的住宅小区总数', code: 'residentialAreas' },
    ]
    if (e[arrZz3[0].code] > e[arrZz2[0].code]) {
      Modal.warning({
        title: '操作提示',
        content: `“${arrZz3[0].label}” 不能大于 “${arrZz2[0].label}”`,
      })
      return;
    }
    if (e[arrZz3[1].code] > e[arrZz2[1].code]) {
      Modal.warning({
        title: '操作提示',
        content: `“${arrZz3[1].label}” 不能大于 “${arrZz2[1].label}”`,
      })
      return;
    }
    if (e[arrZz2[0].code] > e[arrZz1[0].code]) {
      Modal.warning({
        title: '操作提示',
        content: `“${arrZz2[0].label}” 不能大于 “${arrZz1[0].label}”`,
      })
      return;
    }
    if (e[arrZz2[1].code] > e[arrZz1[0].code]) {
      Modal.warning({
        title: '操作提示',
        content: `“${arrZz2[1].label}” 不能大于 “${arrZz1[0].label}”`,
      })
      return;
    }
    // 和不能大于总数
    // const totalNumZz = [...arrZz2].reduce((pre, cur) => pre + e[cur.code], 0);
    // if (totalNumZz > e[arrZz1[0].code]) {
    //   Modal.warning({
    //     title: '操作提示',
    //     content: `“${arrZz2[0].label}”+“${arrZz2[1].label}” 不能大于 “${arrZz1[0].label}” `,
    //   })
    //   return;
    // }
    const { d131Name, ...val } = e;
    val['d131Code'] = d131Name?.key || undefined;
    val['d131Name'] = d131Name?.name || undefined;
    const { code = 500, data = {} } = await cityAdd({
      data: { unitCode: basicInfo.code, orgCode: org.orgCode, code: dataCode || undefined, ...val },
    });
    if (code == 0) {
      Notice('操作提示', '保存成功!', 'check-circle', 'green');
    }

    // let url = addUnitCommunity;
    // if (!_isEmpty(dataInfo)) {
    //   url = updateUnitCommunity;
    // }

    // setConfirmLoading(true);
    // const { code: resCode = 500 } = await url({
    //   data: {
    //     ...e,
    //     unitCode: basicInfo?.code,
    //     id: dataInfo?.id,
    //     code: dataInfo?.code,
    //   },
    // });
    // setConfirmLoading(false);
    // if (resCode === 0) {
    //   Tip.success('操作提示', '操作成功');
    //   close();
    //   onOK && onOK();
    // }
  };
  const renderItem = (item) => {
    const { tipMsg = {} } = props;
    const { basicInfo = {} } = props.unit;
    const {
      label,
      code,
      type,
      codeType,
      rules,
      boolText = {},
      backType = undefined,
      filter,
    } = item;
    let node = <Input />;
    switch (`${type}`) {
      case 'boolean':
        node = (
          <Select style={{ width: '100%' }}>
            <Select.Option value={1}>{boolText?.yes || '是'}</Select.Option>
            <Select.Option value={0}>{boolText?.no || '否'}</Select.Option>
          </Select>
        );
        break;
      case 'boolean2':
        node = (
          <Select style={{ width: '100%' }}>
            <Select.Option value={1}>是</Select.Option>
            <Select.Option value={0}>否</Select.Option>
            <Select.Option value={2}>未配备</Select.Option>
          </Select>
        );
        break;
      case 'dict':
        node = (
          <DictTreeSelect
            codeType={codeType}
            initValue={dataInfo[code]}
            backType={backType}
            parentDisable={true}
            filter={filter}
          />
        );
        break;
      case 'number':
        node = <InputNumber style={{ width: '100%' }} min={0} />;
        break;
    }
    return (
      <Col span={12} key={code}>
        <LongLabelFormItem
          label={label}
          required={true}
          code={code}
          tipMsg={tipMsg}
          formItemLayout={formItemLayout}
          formItem={(formItemLayout, code) => {
            return (
              <Form.Item
                name={code}
                {...formItemLayout}
                rules={[{ required: true, message: '请填写' }]}
              >
                {node}
              </Form.Item>
            );
          }}
        />
      </Col>
    );
  };
  const renderFormItems = () => {
    const { d04Code = '' } = basicInfo;
    let res: any = [];

    // 当类别为城市街道
    if (d04Code === '911') {
      let exData = [
        {
          label: '是否完成内设机构整合',
          code: 'hasCompleteInternalInstitutions',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否完成内设机构整合' }],
        },
        {
          label: '是否实现一支队伍执法',
          code: 'hasAchieveTeamLaw',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否实现一支队伍执法' }],
        },
        {
          label: '是否与区级职能部门划清职权边界',
          code: 'hasDrawClearPower',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否实现一支队伍执法' }],
        },
        {
          label:
            '是否依法赋予街道相应人事考核、征得同意、规划参与、综合管理、行政执法、应急管理、决策建议',
          code: 'hasLegallyEndowed',
          type: 'boolean',
          codeType: '',
          rules: [
            {
              required: true,
              message:
                '是否依法赋予街道相应人事考核、征得同意、规划参与、综合管理、行政执法、应急管理、决策建议',
            },
          ],
        },
        {
          label: '是否取消协税护税、招商引资任务',
          code: 'hasCancelTask',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否取消协税护税、招商引资任务' }],
        },
        {
          label: '是否建立“大工委” ',
          code: 'hasBuildCommittee',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否建立“大工委” ' }],
        },
        {
          label: '是否建立联席会议制度 ',
          code: 'hasEstablishSystem',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否建立联席会议制度 ' }],
        },
      ];
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    }
    // 当单位类别为城市社区
    if (d04Code === '921') {
      let exData = [
        {
          label: '是否制定工作清单、建立准入制度的',
          code: 'hasEstablishWorkSystem',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否制定工作清单、建立准入制度的' }],
        },
        {
          label: '是否依法赋予社区对辖区单位和个人评价权、建议权',
          code: 'hasGrantPower',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否依法赋予社区对辖区单位和个人评价权、建议权' }],
        },
        {
          label: '社区类型',
          code: 'd131Name',
          type: 'dict',
          codeType: 'dict_d131',
          backType: 'object',
          rules: [{ required: true, message: '社区类型' }],
        },
        {
          label: '社区志愿服务类社会组织数',
          code: 'communityServiceOrganizations',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '社区志愿服务类社会组织数' }],
        },
        {
          label: '社区志愿者人数',
          code: 'communityVolunteers',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '社区志愿者人数' }],
        },
        {
          label: '社区居民人口总数 ',
          code: 'residentPopulationCommunity',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '社区居民人口总数 ' }],
        },
        {
          label: '是否建立党群服务中心 ',
          code: 'hasEstablishServiceCenter',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否建立党群服务中心 ' }],
        },
        {
          label: '居民小区建立党群服务驿站数 ',
          code: 'communityServiceStations',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '居民小区建立党群服务驿站数 ' }],
        },
        {
          label: '是否实行“一站式服务”“一门式办理”',
          code: 'hasImplementServiceManagement',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否实行“一站式服务”“一门式办理”' }],
        },
        {
          label: '是否建设“智慧社区”',
          code: 'hasBuildSmartCommunity',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否建设“智慧社区”' }],
        },
        {
          label: '驻社区的机关企事业单位数',
          code: 'communityInstitutionsEnterprises',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '驻社区的机关企事业单位数' }],
        },
        {
          label: '驻社区机关企事业单位到社区报到服务数',
          code: 'communityReportingServices',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '驻社区机关企事业单位到社区报到服务数' }],
        },
        {
          label: '驻社区的非公和社会组织数',
          code: 'nonPublicCommunityOrganizations',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '驻社区的非公和社会组织数' }],
        },
        {
          label: '驻社区非公和社会组织到社区报到服务数',
          code: 'nonPublicOrganizationsServices',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '驻社区非公和社会组织到社区报到服务数' }],
        },
        {
          label: '驻社区的机关企事业单位、非公和社会组织党员人数',
          code: 'membersOnPublicOrganizations',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '驻社区的机关企事业单位、非公和社会组织党员人数' }],
        },
        {
          label: '商务楼宇数',
          code: 'commercialBuildings',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '商务楼宇数' }],
        },
        {
          label: '建立党组织的商务楼宇数',
          code: 'commercialBuildingsPartyOrganizations',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '建立党组织的商务楼宇数' }],
        },
        {
          label: '建立楼宇党群服务中心的商务楼宇数',
          code: 'organizationsCommercialBuildings',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '建立楼宇党群服务中心的商务楼宇数' }],
        },
        {
          label: '商务楼宇配备党务工作者数',
          code: 'commercialBuildingsWorkers',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '商务楼宇配备党务工作者数' }],
        },
        {
          label: '商务楼宇选派党建指导员数',
          code: 'commercialBuildingsInstructor',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '商务楼宇选派党建指导员数' }],
        },
        {
          label: '纳税500万元至1000万元商务楼宇数',
          code: 'fiveToOneBuildings',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '纳税500万元至1000万元商务楼宇数' }],
        },
        {
          label: '纳税1000万元以上商务楼宇数',
          code: 'oneMoreThanBuildings',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '纳税1000万元以上商务楼宇数' }],
        },
        {
          label: '社区划分网格总数',
          code: 'grids',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '社区划分网格总数' }],
        },
        // 2024.11.20.新增字段
        {
          label: '在居民住宅小区、楼院等划分的网格数',
          code: 'jmzzlyWgs',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '在居民住宅小区、楼院等划分的网格数' }],
        },
        {
          label: '在居民住宅小区、楼院等划分已建立党组织的网格数',
          code: 'jmzzlyDzWgs',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '在居民住宅小区、楼院等划分已建立党组织的网格数' }],
        },
        {
          label: '在商务楼宇、商圈市场等单独划定的网格数',
          code: 'swsqWgs',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '在商务楼宇、商圈市场等单独划定的网格数' }],
        },
        {
          label: '在商务楼宇、商圈市场等单独划定已建立党组织的网格数',
          code: 'swsqDzWgs',
          type: 'number',
          codeType: '',
          rules: [
            { required: true, message: '在商务楼宇、商圈市场等单独划定已建立党组织的网格数' },
          ],
        },
        {
          label: '街道（乡镇）领导班子成员直接联系的网格数',
          code: 'jdldWgs',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '街道（乡镇）领导班子成员直接联系的网格数' }],
        },
        {
          label: '配备专职网格员数',
          code: 'gridMembers',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '配备专职网格员数' }],
        },
        {
          label: '由社区工作者担任的专职网格员数',
          code: 'zzWgys',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '由社区工作者担任的专职网格员数' }],
        },
        {
          label: '全部专职网格员年工资总额（万元）',
          code: 'zzNgzze',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '全部专职网格员年工资总额（万元）' }],
        },
        {
          label: '配备兼职网格员数',
          code: 'jzWgys',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '配备兼职网格员数' }],
        },
        {
          label: '建立网格党组织数',
          code: 'organizationPartyGrid',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '建立网格党组织数' }],
        },
        {
          label: '是否完成将各类网格整合成为综合网格，形成基层治理“一张网”',
          code: 'hasCompleteNet',
          type: 'boolean',
          codeType: '',
          rules: [
            { required: true, message: '是否完成将各类网格整合成为综合网格，形成基层治理“一张网”' },
          ],
        },
        {
          label: '是否建立“大党委”的社区',
          code: 'hasLargePartyCommunity',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否建立“大党委”的社区' }],
        },
        {
          label: '是否建立联席会议制度的社区',
          code: 'hasMeetSystemCommunity',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否建立联席会议制度的社区' }],
        },
        {
          label: '社区中的住宅小区总数',
          code: 'residentialAreas',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '社区中的住宅小区总数' }],
        },
        {
          label: '有物业服务企业提供服务的小区数',
          code: 'tubePlots',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '有物业服务企业提供服务的小区数' }],
        },
        {
          label: '物业服务团队已建立党组织的小区数',
          code: 'organizationCompanies',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '物业服务团队已建立党组织的小区数' }],
        },
        {
          label: '小区组建的业主委员会数量',
          code: 'industryAuthorityCommunity',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '小区组建的业主委员会数量' }],
        },
        {
          label: '建立党组织的业主委员会数量',
          code: 'industryAuthorityOrganization',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '建立党组织的业主委员会数量' }],
        },
        {
          label: '建立党建引领下的社区居委会、业委会、物业服务企业协调运行机制的社区',
          code: 'threePartiesCommunities',
          type: 'boolean',
          codeType: '',
          rules: [
            {
              required: true,
              message: '建立党建引领下的社区居委会、业委会、物业服务企业协调运行机制的社区',
            },
          ],
        },
      ];
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    }
    return res;
  };

  return (
    <Fragment>
      {/* <Modal
        title={title}
        destroyOnClose
        visible={visible}
        confirmLoading={confirmLoading}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={'1000px'}
        bodyStyle={{height: '600px',overflow: 'auto'}}
      > */}
      <Form form={form} {...formItemLayout} onFinish={onFinish}>
        <Row>{renderFormItems()}</Row>
      </Form>
      {/* </Modal> */}
      <div style={{ textAlign: 'center' }}>
        <Button type={'primary'} onClick={() => form.submit()}>
          保存
        </Button>
      </div>
    </Fragment>
  );
};
export default React.forwardRef(index);
