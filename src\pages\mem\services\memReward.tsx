/**
 * 人员奖惩services
 */
import request from "@/utils/request";
import qs from 'qs';
export function getList(params) {
  return request(`/api/mem/reward/getList?${qs.stringify(params)}`,{
    method:'Get',
  });
}
//新增人员奖惩
export function addMemReward(params) {
  return request(`/api/mem/reward/addMemReward`,{
    method:'POST',
    body:params,
  });
}
export function findReward(params) {
  return request(`/api/mem/reward/findByCode?${qs.stringify(params)}`,{
    method:'Get',
  });
}
export function updateMemReward(params) {
  return request(`/api/mem/reward/updateMemReward`,{
    method:'POST',
    body:params,
  });
}
export function delMemReward(params) {
  return request(`/api/mem/reward/delMemReward?${qs.stringify(params)}`,{
    method:'Get',
  });
}


