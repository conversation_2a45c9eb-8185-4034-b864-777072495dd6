import request from '@/utils/request';
import qs from 'qs';
export function annualExport(params) {
  return request(
    `/api/annual/export`,
    {
      method: 'post',
      body: params,
    },
    'file',
  );
}
export function checkTable(params) {
  return request(`/api/annual/checkTable?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function delCheck(params) {
  return request(`/api/verification/delCheck?${qs.stringify(params)}`);
}
export function findCheckTableListByCode(params) {
  return request(`/api/annual/findCheckTableListByCode?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function findCheckTableListByCodeDifference(params) {
  return request(`/api/annual/findCheckTableDifference`, {
    method: 'post',
    body: params,
  });
}

export function exportNestedTable(params) {
  return request(
    `/api/annual/exportNestedTable?${qs.stringify(params)}`,
    {
      method: 'Get',
    },
    'file',
  );
}

export function getExtractPegging(params) {
  return request(`/api/annual/getExtractPegging`, {
    method: 'post',
    body: params,
  });
}

export function getExportDate(params) {
  return request(`/api/annual/getExportDate?${qs.stringify(params)}`);
}

export function getTableHeaderPegging(params) {
  console.log("🚀 ~ getTableHeaderPegging ~ params:", params)

  return request(`/api/annual/findReportExplain`, {
    method: 'post',
    body: params,
  });
}

