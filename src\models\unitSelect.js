import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {getList} from 'src/pages/[unit]/services';
import { changeListPayQuery } from '@/utils/method.js';
const unit = modelExtend(listPageModel,{
  namespace: "unitSelect",
  state:{
    basicInfo:{},
    basicInfoData:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        // const { pathname, query } = location;
      });
    }
  },
  effects: {
    // 查找人员列表
    *getList({ payload }, { call, put }) {
      const {data={}} = yield call(getList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    *getOtherList({ payload }, { call, put }) {
      const {data={}} = yield call(getList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
  }
});
export default unit;
