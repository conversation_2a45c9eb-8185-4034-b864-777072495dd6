import _isEmpty from 'lodash/isEmpty';
import { Colgroup, Head as SelfHead, fakeLine } from '@/components/DynamicTableHead';
import { reportExcel, peggingExcel, } from '../services';
// 农村党建调度表
export const getCheckTableCols = (type: any) => {
  let cols: any = [];
  switch (type) {
    // 党员信息
    case '1':
      cols = [
        {
          title: '组织名称',
          dataIndex: 'orgName',
          width: 80,
        },
        {
          title: '姓名',
          dataIndex: 'name',
          width: 80,
        },
        {
          title: '性别',
          dataIndex: 'sexName',
          width: 80,
        },
        {
          title: '年龄',
          dataIndex: 'age',
          width: 80,
        },
        {
          title: '身份证号',
          dataIndex: 'idcard',
          width: 80,
          render: (text, record) => {
            if (typeof text === 'string' && !_isEmpty(text)) {
              let newVal = text.replace(/(\d{6})\d*([0-9a-zA-Z]{2})/, '$1***********$2');
              if (text.indexOf('*') > 0) {
                return text;
              }
              return newVal;
            } else {
              return '';
            }
          },
        },
        {
          title: '出生日期',
          dataIndex: 'birthday',
          width: 80,
        },
        {
          title: '联系电话',
          dataIndex: 'phone',
          width: 80,
        },
        {
          title: '单位类别',
          dataIndex: ' d04Name',
          width: 80,
        },
        {
          title: '学历',
          dataIndex: 'd07Name',
          width: 80,
        },
        {
          title: '在任职务',
          dataIndex: 'd25Name',
          width: 80,
        },
        {
          title: '党内职务',
          dataIndex: 'd022Name',
          width: 80,
        },
        {
          title: '隶属关系',
          dataIndex: 'd35Name',
          width: 80,
        },
        {
          title: '班子成员来源',
          dataIndex: 'd138Name',
          width: 80,
        },
        {
          title: '人员身份',
          dataIndex: 'd140Name',
          width: 80,
        },
        {
          title: '人员来源',
          dataIndex: 'd141Name',
          width: 80,
        },
        {
          title: '党员集中培训情况',
          dataIndex: 'd142Name',
          width: 80,
        },
        {
          title: '村两委报酬(万元)',
          dataIndex: 'rewardSum',
          width: 80,
        },
        {
          title: '是否县乡领导班子成员帮带',
          dataIndex: 'hasLeadersHelpPeople',
          width: 80,
        },
        {
          title: '是否村任职选调生',
          dataIndex: 'hasVillageTransferStudent',
          width: 80,
        },
      ];
      break;
    // 单位信息
    case '2':
      cols = [
        {
          title: '组织名称',
          dataIndex: 'orgName',
          width: 80,
        },
        {
          title: '单位名称',
          dataIndex: 'unitName',
          width: 80,
        },
        {
          title: '单位类别',
          dataIndex: 'd04Name',
          width: 80,
        },
        {
          title: '隶属关系',
          dataIndex: 'd35Name',
          width: 80,
        },
        {
          title: '在编人数',
          dataIndex: 'atNumber',
          width: 80,
        },
        {
          title: '行政编制',
          dataIndex: 'atAdministrative',
          width: 80,
        },
        {
          title: '事业编制',
          dataIndex: 'atCareer',
          width: 80,
        },
        {
          title: '空缺编制',
          dataIndex: 'vacancy',
          width: 80,
        },
        {
          title: '行政编制',
          dataIndex: 'vacancyAdministrative',
          width: 80,
        },
        {
          title: '事业编制',
          dataIndex: 'vacancyCareer',
          width: 80,
        },
        {
          title: '被借调工作人员总数',
          dataIndex: 'secondedNum',
          width: 80,
        },
        {
          title: '占在编人数比例',
          dataIndex: 'atProportion',
          width: 80,
        },
        {
          title: '省级以上',
          dataIndex: 'provincialAbove',
          width: 80,
        },
        {
          title: '省级',
          dataIndex: 'provincial',
          width: 80,
        },
        {
          title: '市级',
          dataIndex: 'city',
          width: 80,
        },
        {
          title: '县级',
          dataIndex: 'county',
          width: 80,
        },
        {
          title: '参加县级及以上集中培训人数',
          dataIndex: 'joinAboveCountyTrainNum',
          width: 80,
        },
        {
          title: '村干部参加城镇职工养老保险人数',
          dataIndex: 'villageJoinUrbanWorkerNum',
          width: 80,
        },
        {
          title: '参加比例',
          dataIndex: 'joinProportion',
          width: 80,
        },
        {
          title: '中央和省级财政扶持资金',
          dataIndex: 'financialSupportEnforced',
          width: 80,
        },
        {
          title: '中央和省级财政扶持资金执行率',
          dataIndex: 'enforced',
          width: 80,
        },
        {
          title: '已完工验收项目数',
          dataIndex: 'completedAcceptanceProjects',
          width: 80,
        },
        {
          title: '已获得收益',
          dataIndex: 'incomeObtained',
          width: 80,
        },
        {
          title: '应到村任职选调生人数',
          dataIndex: 'numberOfStudentsToBeTransferredToTheVillage',
          width: 80,
        },
        {
          title: '不是党委委员的政府领导班子成员人数',
          dataIndex: 'numberOfNonGovernmentalMembers',
          width: 80,
        },
        {
          title: '是否有大学毕业生在村工作',
          dataIndex: 'whetherThereAreCollegeGraduatesWorkingInTheVillage',
          width: 80,
        },
      ];
      break;
    // 组织信息
    case '3':
      cols = [
        {
          title: '组织名称',
          dataIndex: 'orgName',
          width: 80,
        },
        {
          title: '党组织类型',
          dataIndex: 'd01Name',
          width: 80,
        },
        {
          title: '联系人',
          dataIndex: 'contacter',
          width: 80,
        },
        {
          title: '通讯地址',
          dataIndex: 'postAddress',
          width: 80,
        },
        {
          title: '党组织书记名称',
          dataIndex: 'secretary',
          width: 80,
        },
        {
          title: '到村任职补助经费使用率',
          dataIndex: 'toTheVillageOfficeSubsidyFundsUtilizationRate',
          width: 80,
        },
      ];
      break;
    // 发展党员
    case '4':
      cols = [
        {
          title: '姓名',
          dataIndex: 'name',
          width: 80,
        },
        {
          title: '组织名称',
          dataIndex: 'orgName',
          width: 80,
        },
        {
          title: '性别',
          dataIndex: 'sexName',
          width: 80,
        },
        {
          title: '出生日期',
          dataIndex: 'birthday',
          width: 80,
        },
        {
          title: '年龄',
          dataIndex: 'age',
          width: 80,
        },
        {
          title: '成为积极分子日期',
          dataIndex: 'activeDate',
          width: 80,
        },
        {
          title: '申请入党日期',
          dataIndex: 'applyDate',
          width: 80,
        },
        {
          title: '成为发展对象时间',
          dataIndex: 'objectDate',
          width: 80,
        },
        {
          title: '民族',
          dataIndex: 'd06Name',
          width: 80,
        },
        {
          title: '类别',
          dataIndex: 'd08Name',
          width: 80,
        },
        {
          title: '工作岗位',
          dataIndex: 'd09Name',
          width: 80,
        },
        {
          title: '进入支部类型',
          dataIndex: 'd11Name',
          width: 80,
        },
        {
          title: '新社会阶层',
          dataIndex: 'd20Name',
          width: 80,
        },
        {
          title: '一线情况',
          dataIndex: 'd21Name',
          width: 80,
        },
        {
          title: '审批结果',
          dataIndex: 'd28Name',
          width: 80,
        },
        {
          title: '籍贯',
          dataIndex: 'd48Name',
          width: 80,
        },
        {
          title: '政治面貌',
          dataIndex: 'd89Name',
          width: 80,
        },
        {
          title: '加入共产党类型',
          dataIndex: ' joinOrgName ',
          width: 80,
        },
      ];
      break;
    // 流动党员
    case '5':
      cols = [
        {
          title: '姓名',
          dataIndex: 'name',
          width: 80,
        },
        {
          title: '性别',
          dataIndex: 'sexName',
          width: 80,
        },
        {
          title: '流出或流入日期',
          dataIndex: 'outflowDate',
          width: 80,
        },
        {
          title: '流出或流入类型',
          dataIndex: 'outflowTypeName',
          width: 80,
        },
        {
          title: '流动状态',
          dataIndex: 'flowStatus',
          width: 80,
        },
        {
          title: '流动类型',
          dataIndex: 'flowAddType',
          width: 80,
        },
        {
          title: '学历教育',
          dataIndex: 'outflowEduName',
          width: 80,
        },
      ];
      break;
    case '6':
      cols = [
        {
          title: '姓名',
          dataIndex: 'name',
          width: 80,
        },
        {
          title: '组织名称',
          dataIndex: 'orgName',
          width: 80,
        },
        {
          title: '性别',
          dataIndex: 'sexName',
          width: 80,
        },
        {
          title: '身份证号',
          dataIndex: 'idcard',
          width: 80,
          render: (text, record) => {
            if (typeof text === 'string' && !_isEmpty(text)) {
              let newVal = text.replace(/(\d{6})\d*([0-9a-zA-Z]{2})/, '$1***********$2');
              if (text.indexOf('*') > 0) {
                return text;
              }
              return newVal;
            } else {
              return '';
            }
          },
        },
        {
          title: '出生日期',
          dataIndex: 'birthday',
          width: 80,
        },
        {
          title: '是否产业工人',
          dataIndex: 'hasWorker',
          width: 80,
        },
        {
          title: '组织单位类别',
          dataIndex: 'd04Name',
          width: 80,
        },
        {
          title: '当前学历情况',
          dataIndex: 'd07Name',
          width: 80,
        },
        {
          title: '年龄',
          dataIndex: 'age',
          width: 80,
        },
        {
          title: '当前工作岗位',
          dataIndex: 'd09Name',
          width: 80,
        },
        {
          title: '专业技术职务',
          dataIndex: 'd19Name',
          width: 80,
        },
      ];
      break;
    case 'org':
      cols = [
        {
          title: '组织名称',
          dataIndex: 'name',
          width: 200,
        },
        {
          title: '组织类别',
          dataIndex: 'd01Name',
          width: 80,
        },
        {
          title: '联系人',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '联系方式',
          dataIndex: 'contactPhone',
          width: 80,
        },
        {
          title: '党组织书记',
          dataIndex: 'secretary',
          width: 50,
        },
        {
          title: '党员人数',
          dataIndex: 'v6',
          width: 50,
        },
        {
          title: '正式党员人数',
          dataIndex: 'v7',
          width: 50,
        },
        {
          title: '支委会人数',
          dataIndex: 'v8',
          width: 50,
        },
        {
          title: '下一次换届时间',
          dataIndex: 'v9',
          width: 50,
        },
        {
          title: '是否设立党小组',
          dataIndex: 'v10',
          width: 50,
        },
        {
          title: '年内召开党员大会次数',
          dataIndex: 'v11',
          width: 50,
        },
        {
          title: '年内召开支委会次数',
          dataIndex: 'v12',
          width: 50,
        },
        {
          title: '年内召开党小组会次数',
          dataIndex: 'v13',
          width: 50,
        },
        {
          title: '年内上党课次数',
          dataIndex: 'v14',
          width: 50,
        },
        {
          title: '发展党员数',
          dataIndex: 'v15',
          width: 50,
        },
        {
          title: '近两年来发展党员数',
          dataIndex: 'v16',
          width: 50,
        },
      ];
      break;
    case 'mem':
      cols = [
        {
          title: '所属党组织',
          dataIndex: 'name',
          width: 200,
        },
        {
          title: '姓名',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '性别',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '民族',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '出生年月',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '身份证号',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '所属党支部名称',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '提交入党申请书时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '成为积极分子时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '成为发展对象时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '短期集中培训时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '政治审查结论性意见落款时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '成为预备党员时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '转为正式党员时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '是否按期转正',
          dataIndex: 'contacter',
          width: 50,
        },
      ];
      break;
    // 党组织工作联系点
    case '7':
      cols = [
        {
          title: '姓名',
          dataIndex: 'name',
          width: 80,
        },
        {
          title: '类型',
          dataIndex: 'd188Name',
          width: 80,
        },
        {
          title: '层级',
          dataIndex: 'd189Name',
          width: 80,
        },
        {
          title: '支部联系点名称',
          dataIndex: 'orgName',
          width: 80,
        },
        {
          title: '支部书记姓名及联系电话',
          dataIndex: 'secretary',
          width: 80,
        },
        {
          title: '联系起始时间',
          dataIndex: 'startTime',
          width: 80,
        },
        {
          title: '到联系点开展工作情况',
          dataIndex: 'contacter',
          children: [
            {
              title: '开展工作时间',
              dataIndex: 'startDate',
              width: 80,
            },
            {
              title: '开展工作类型',
              dataIndex: 'd156Name',
              width: 80,
            },
            {
              title: '工作简介',
              dataIndex: 'workRecord',
              width: 220,
            },
          ],
        },
      ];
      break;
    //基层党组织开展民主评议党员情况统计表
    case 't8':
      cols = [
        {
          title: '姓名',
          dataIndex: 'memName',
          width: 100,
        },
        {
          title: '组织名称',
          dataIndex: 'orgName',
          width: 100,
        },
        {
          title: '党员类型',
          dataIndex: 'd08Name',
          width: 100,
        },
        {
          title: '评议结果',
          dataIndex: 'resultName',
          width: 100,
        },
        {
          title: '组织处置情况',
          dataIndex: 'situation',
          width: 100,
        },
        {
          title: '评议原因',
          dataIndex: 'reason',
          width: 100,
        },
        {
          title: '上级党组织意见',
          dataIndex: 'opinion',
          width: 100,
        }
      ];
      break;
  }
  return cols;
};

export const TableCol = [
  {
    key: '0109',
    k0505: '组织名称',
    parent: '09',
    style: { width: 200 },
  },
  {
    key: '0101',
    k0505: '乡镇领导班子',
    parent: '01',
    children: [
      {
        key: '010101',
        k0505: '',
        parent: '0101',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01010101',
            k0505: '总人数',
            parent: '010101',
            style: { borderTop: 'none' },
          },
          {
            key: '01010102',
            k0505: '党委委员人数',
            parent: '010101',
          },
          {
            key: '01010103',
            k0505: '不是党委委员的政府领导班子成员人数',
            parent: '010101',
          },
        ],
      },
      {
        key: '010102',
        k0505: '乡镇党委书记',
        parent: '0101',
        children: [
          {
            key: '01010201',
            k0505: '总人数',
            parent: '010102',
          },
          {
            key: '01010202',
            k0505: '35岁及以下人数',
            parent: '010102',
          },
          {
            key: '01010203',
            k0505: '50岁及以上人数',
            parent: '010102',
          },
          {
            key: '01010204',
            k0505: '大学本科及以上学历人数',
            parent: '010102',
          },
        ],
      },
      {
        key: '010103',
        k0505: '年龄结构',
        parent: '0101',
        children: [
          {
            key: '01010301',
            k0505: '30岁以下人数',
            parent: '010103',
          },
          {
            key: '01010302',
            k0505: '35岁及以下人数',
            parent: '010103',
          },
          {
            key: '01010303',
            k0505: '50岁以上人数',
            parent: '010103',
          },
        ],
      },
      {
        key: '010104',
        k0505: '学历结构',
        parent: '0101',
        children: [
          {
            key: '01010401',
            k0505: '研究生学历人数',
            parent: '010104',
          },
          {
            key: '01010402',
            k0505: '大学 本科学历人数',
            parent: '010104',
          },
          {
            key: '01010403',
            k0505: '大学 专科学历人数',
            parent: '010104',
          },
          {
            key: '01010404',
            k0505: '中专及以下学历人数',
            parent: '010104',
          },
        ],
      },
      {
        key: '010105',
        k0505: '"五方面人员" 进班子人数',
        parent: '0101',
        children: [
          {
            key: '01010501',
            k0505: '乡镇事业编制人员数',
            parent: '010105',
          },
          {
            key: '01010502',
            k0505: '优秀村党组织书记人数',
            parent: '010105',
          },
          {
            key: '01010503',
            k0505: '到村任职过的选调生',
            parent: '010105',
          },
          {
            key: '01010504',
            k0505: '第一书记人数',
            parent: '010105',
          },
          {
            key: '01010505',
            k0505: '驻村工作队员人数',
            parent: '010105',
          },
        ],
      },
    ],
  },
  {
    key: '0102',
    k0505: '乡镇干部队伍',
    parent: '01',
    children: [
      {
        key: '010201',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020101',
            k0505: '在编人数',
            parent: '010201',
            style: { borderTop: 'none' },
          },
          {
            key: '01020102',
            k0505: '行政编制',
            parent: '010201',
          },
          {
            key: '01020103',
            k0505: '事业编制',
            parent: '010201',
          },
        ],
      },
      {
        key: '010202',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020201',
            k0505: '空缺编制数',
            parent: '010202',
            style: { borderTop: 'none' },
          },
          {
            key: '01020202',
            k0505: '行政编制',
            parent: '010202',
          },
          {
            key: '01020203',
            k0505: '事业编制',
            parent: '010202',
          },
        ],
      },
      {
        key: '010203',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020301',
            k0505: '被借调工作人员总数',
            parent: '010203',
            style: { borderTop: 'none' },
          },
          {
            key: '01020302',
            k0505: '占在编人数比例',
            parent: '010203',
          },
          {
            key: '01020303',
            k0505: '借调单位',
            parent: '010203',
            children: [
              {
                key: '0102030301',
                k0505: '省级以上',
                parent: '01020303',
              },
              {
                key: '0102030302',
                k0505: '省级',
                parent: '01020303',
              },
              {
                key: '0102030303',
                k0505: '市级',
                parent: '01020303',
              },
              {
                key: '0102030304',
                k0505: '县级',
                parent: '01020303',
              },
            ],
          },
        ],
      },
    ],
  },
  {
    key: '0103',
    k0505: '村"两委"班子成员',
    parent: '01',
    children: [
      {
        key: '010301',
        k0505: '总人数',
        parent: '0103',
      },
      {
        key: '010302',
        k0505: '党组织委员人数',
        parent: '0103',
      },
      {
        key: '010303',
        k0505: '村委会委员人数',
        parent: '0103',
      },
      {
        key: '010304',
        k0505: '"两委"交叉任职人数',
        parent: '0103',
      },
      {
        key: '010305',
        k0505: '村党组织书记',
        parent: '0103',
        children: [
          {
            key: '01030401',
            k0505: '年龄结构',
            parent: '010304',
            children: [
              {
                key: '0103040101',
                k0505: '平均年龄',
                parent: '01030401',
              },
              {
                key: '0103040102',
                k0505: '35岁及以下人数',
                parent: '01030401',
              },
              {
                key: '0103040103',
                k0505: '36岁至50岁人数',
                parent: '01030401',
              },
              {
                key: '0103040104',
                k0505: '51岁至55岁人数',
                parent: '01030401',
              },
              {
                key: '01030401054',
                k0505: '55岁以上人数',
                parent: '01030401',
              },
            ],
          },
          {
            key: '01030402',
            k0505: '学历结构',
            parent: '010304',
            children: [
              {
                key: '0103040201',
                k0505: '研究生学历人数',
                parent: '01030402',
              },
              {
                key: '0103040202',
                k0505: '大学本科学历人数',
                parent: '01030402',
              },
              {
                key: '0103040203',
                k0505: '大学 专科学历人数',
                parent: '01030402',
              },
              {
                key: '0103040204',
                k0505: '中专、高中、中技学历人数',
                parent: '01030402',
              },
              {
                key: '0103040205',
                k0505: '初中及以下学历人数',
                parent: '01030402',
              },
            ],
          },
          {
            key: '01030403',
            k0505: '教育培训',
            parent: '010304',
            children: [
              {
                key: '0103040301',
                k0505: '县级集中培训人数',
                parent: '01030403',
              },
              {
                key: '0103040302',
                k0505: '市级集中培训人数',
                parent: '01030403',
              },
              {
                key: '0103040303',
                k0505: '省级集中培训人数',
                parent: '01030403',
              },
            ],
          },
        ],
      },
      {
        key: '010306',
        k0505: '参加县级及以上集中培训人数',
        parent: '0103',
      },
      {
        key: '010307',
        k0505: '隐藏',
        parent: '0103',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01030701',
            k0505: '村干部参加城镇职工养老保险人数',
            parent: '010307',
            style: { borderTop: 'none' },
          },
          {
            key: '01030702',
            k0505: '参加比例',
            parent: '010307',
          },
        ],
      },
      {
        key: '010308',
        k0505: '"一肩挑"人员平均报酬(万元/年)',
        parent: '0103',
      },
      {
        key: '010309',
        k0505: '其他职务人员平均报酬(万元/年)',
        parent: '0103',
      },
    ],
  },
  {
    key: '0104',
    k0505: '2019年以来财政资金扶持村级集体经济',
    parent: '01',
    children: [
      {
        key: '010401',
        k0505: '隐藏',
        parent: '0104',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01040101',
            k0505: '获中央和省级财政扶持资金年度及金额',
            parent: '010401',
            style: { borderTop: 'none' },
          },
          {
            key: '01040102',
            k0505: '资金执行率',
            parent: '010401',
          },
        ],
      },
      {
        key: '010402',
        k0505: '已完工验收项目数',
        parent: '0104',
      },
      {
        key: '010403',
        k0505: '已获得收益(万元)',
        parent: '0104',
      },
    ],
  },
  {
    key: '0105',
    k0505: '农村党员发展、村级后备力量',
    parent: '01',
    children: [
      {
        key: '010501',
        k0505: '行政村申请入党人数',
        parent: '0105',
      },
      {
        key: '010502',
        k0505: '行政村确定入党积极分子人数',
        parent: '0105',
      },
      {
        key: '010503',
        k0505: '行政村流出党员人数',
        parent: '0105',
      },
      {
        key: '010504',
        k0505: '行政村吸收预备党员人数',
        parent: '0105',
      },
      {
        key: '010504',
        k0505: '隐藏',
        parent: '0105',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01050401',
            k0505: '村级后备力量人数',
            parent: '010504',
            style: { borderTop: 'none' },
          },
          {
            key: '01050402',
            k0505: '党员人数',
            parent: '010504',
          },
          {
            key: '01050403',
            k0505: '县乡领导班子成员帮带人数',
            parent: '010504',
          },
        ],
      },
    ],
  },
  {
    key: '0106',
    k0505: '驻村第一书记和驻村干部',
    parent: '01',
    children: [
      {
        key: '010601',
        k0505: '总人数',
        parent: '0106',
      },
      {
        key: '010602',
        k0505: '来源',
        parent: '0106',
        children: [
          {
            key: '01060201',
            k0505: '隐藏',
            parent: '010602',
            style: { borderBottom: 'none', visibility: 'hidden' },
            children: [
              {
                key: '0106020101',
                k0505: '中直',
                parent: '01060201',
                style: { borderTop: 'none' },
              },
              {
                key: '0106020101',
                k0505: '第一书记',
                parent: '01060201',
              },
            ],
          },
          {
            key: '01060202',
            k0505: '隐藏',
            parent: '010602',
            style: { borderBottom: 'none', visibility: 'hidden' },
            children: [
              {
                key: '0106020201',
                k0505: '省直',
                parent: '01060202',
                style: { borderTop: 'none' },
              },
              {
                key: '0106020202',
                k0505: '第一书记',
                parent: '01060202',
              },
            ],
          },
          {
            key: '01060203',
            k0505: '隐藏',
            parent: '010602',
            style: { borderBottom: 'none', visibility: 'hidden' },
            children: [
              {
                key: '0106020301',
                k0505: '市直',
                parent: '01060203',
                style: { borderTop: 'none' },
              },
              {
                key: '0106020302',
                k0505: '第一书记',
                parent: '01060203',
              },
            ],
          },
          {
            key: '01060204',
            k0505: '隐藏',
            parent: '010602',
            style: { borderBottom: 'none', visibility: 'hidden' },
            children: [
              {
                key: '0106020401',
                k0505: '县直',
                parent: '01060204',
                style: { borderTop: 'none' },
              },
              {
                key: '0106020402',
                k0505: '第一书记',
                parent: '01060204',
              },
            ],
          },
          {
            key: '01060205',
            k0505: '乡镇',
            parent: '010602',
          },
        ],
      },
      {
        key: '010603',
        k0505: '年龄结构',
        parent: '0106',
        children: [
          {
            key: '01060301',
            k0505: '35岁及以下人数',
            parent: '010603',
          },
          {
            key: '01060302',
            k0505: '36岁至50岁人数',
            parent: '010603',
          },
          {
            key: '01060303',
            k0505: '51岁至55岁人数',
            parent: '010603',
          },
          {
            key: '01060304',
            k0505: '55岁以上人数',
            parent: '010603',
          },
        ],
      },
      {
        key: '010604',
        k0505: '学历结构',
        parent: '0106',
        children: [
          {
            key: '01060401',
            k0505: '研究生学历人数',
            parent: '010604',
          },
          {
            key: '01060402',
            k0505: '大学本科学历人数',
            parent: '010604',
          },
          {
            key: '01060403',
            k0505: '大学 专科学历人数',
            parent: '010604',
          },
          {
            key: '01060404',
            k0505: '中专、高中、中技学历人数',
            parent: '010604',
          },
          {
            key: '01060405',
            k0505: '初中及以下学历人数',
            parent: '010604',
          },
        ],
      },
      {
        key: '010605',
        k0505: '隐藏',
        parent: '0106',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01060501',
            k0505: '任期未满调整第一书记人数',
            parent: '010605',
            style: { borderTop: 'none' },
          },
          {
            key: '01060502',
            k0505: '调整比例',
            parent: '010605',
          },
        ],
      },
    ],
  },
  {
    key: '0107',
    k0505: '选调生到村任职',
    parent: '01',
    children: [
      {
        key: '010701',
        k0505: '隐藏',
        parent: '0107',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '010701',
            k0505: '应到村任职人数',
            parent: '0107',
            style: { borderTop: 'none' },
          },
          {
            key: '010702',
            k0505: '目前在村在岗人数',
            parent: '0107',
          },
        ],
      },
      {
        key: '010702',
        k0505: '选调单位层级',
        parent: '0107',
        children: [
          {
            key: '01070201',
            k0505: '省级以上',
            parent: '010702',
          },
          {
            key: '01070202',
            k0505: '省级',
            parent: '010702',
          },
          {
            key: '01070203',
            k0505: '市级',
            parent: '010702',
          },
          {
            key: '01070204',
            k0505: '县级',
            parent: '010702',
          },
          {
            key: '01070205',
            k0505: '乡镇',
            parent: '010702',
          },
        ],
      },
      {
        key: '010703',
        k0505: '年龄结构',
        parent: '0107',
        children: [
          {
            key: '01070301',
            k0505: '20岁至25岁人数',
            parent: '010703',
          },
          {
            key: '01070302',
            k0505: '26岁至30岁人数',
            parent: '010703',
          },
          {
            key: '01070303',
            k0505: '30岁及以上人数',
            parent: '010703',
          },
        ],
      },
      {
        key: '010704',
        k0505: '学历结构',
        parent: '0107',
        children: [
          {
            key: '01070401',
            k0505: '博士研究生学历人数',
            parent: '010704',
          },
          {
            key: '01070402',
            k0505: '硕士研究生学历人数',
            parent: '010704',
          },
          {
            key: '01070403',
            k0505: '本科学历人数',
            parent: '010704',
          },
        ],
      },
      {
        key: '010705',
        k0505: '到村任职补助经费使用率',
        parent: '0107',
      },
    ],
  },
  {
    key: '0108',
    k0505: '一村一名大学生',
    parent: '01',
    children: [
      {
        key: '010801',
        k0505: '隐藏',
        parent: '0108',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01080101',
            k0505: '有大学毕业生在村工作的村数',
            parent: '010801',
            style: { borderTop: 'none' },
          },
          {
            key: '01080102',
            k0505: '比例',
            parent: '010801',
          },
        ],
      },
    ],
  },
];

// 发展党员工作情况调度表
export const TableColDepMem = [
  {
    key: '0109',
    k0505: '组织名称',
    parent: '01',
    style: { width: 200 },
  },
  {
    key: '0101',
    k0505: '截至目前总完成数',
    parent: '01',
    // style: { width: 200 },
  },
  {
    key: '0102',
    k0505: '重点群体和薄弱领域发展党员情况',
    parent: '01',
    // style: { width: 200 },
    children: [
      {
        key: '010201',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020101',
            k0505: '产业工人(人)',
            parent: '010201',
            style: { borderTop: 'none' },
            // children: [
            //   {
            //     key: '0102010101',
            //     k0505: '截止目前发展数',
            //     parent: '01020101',
            //     // style: { rowSpan: '1' }
            //   }
            // ]
          },
        ],
      },
      {
        key: '010202',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020201',
            k0505: '农村党员(人)',
            parent: '010202',
            style: { borderTop: 'none' },
            // children: [
            //   {
            //     key: '0102020101',
            //     k0505: '截止目前发展数',
            //     parent: '01020201',
            //   }
            // ]
          },
          {
            key: '01020202',
            k0505: '35岁及以下的青年农民(人)',
            parent: '010202',
            // children: [
            //   {
            //     key: '0102020201',
            //     k0505: '截止目前发展数',
            //     parent: '01020202',
            //   }
            // ]
          },
        ],
      },
      {
        key: '010203',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020301',
            k0505: '高知识群体(人)',
            parent: '010203',
            style: { borderTop: 'none' },
            // children: [
            //   {
            //     key: '0102030101',
            //     k0505: '截止目前发展数',
            //     parent: '01020301',
            //   }
            // ]
          },
          {
            key: '01020302',
            k0505: '高层次人才(人)',
            parent: '010203',
            // children: [
            //   {
            //     key: '0102030201',
            //     k0505: '截止目前发展数',
            //     parent: '01020302',
            //   }
            // ]
          },
          {
            key: '01020303',
            k0505: '35岁及以下的青年高层次人才(人)',
            parent: '010203',
            // children: [
            //   {
            //     key: '0102030301',
            //     k0505: '截止目前发展数',
            //     parent: '01020303',
            //   }
            // ]
          },
        ],
      },
      {
        key: '010204',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020401',
            k0505: '大学生(人)',
            parent: '010204',
            style: { borderTop: 'none' },
            // children: [
            //   {
            //     key: '0102040101',
            //     k0505: '截止目前发展数',
            //     parent: '01020401',
            //   }
            // ]
          },
          {
            key: '01020402',
            k0505: '本科生(人)',
            parent: '010204',
            // children: [
            //   {
            //     key: '0102040201',
            //     k0505: '截止目前发展数',
            //     parent: '01020402',
            //   }
            // ]
          },
          {
            key: '01020403',
            k0505: '研究生(人)',
            parent: '010204',
            // children: [
            //   {
            //     key: '0102040301',
            //     k0505: '截止目前发展数',
            //     parent: '01020403',
            //   }
            // ]
          },
          {
            key: '01020404',
            k0505: '民族院校(人)',
            parent: '010204',
            // children: [
            //   {
            //     key: '0102040401',
            //     k0505: '截止目前发展数',
            //     parent: '01020404',
            //   }
            // ]
          },
          {
            key: '01020405',
            k0505: '民办高校(人)',
            parent: '010204',
            // children: [
            //   {
            //     key: '0102040501',
            //     k0505: '截止目前发展数',
            //     parent: '01020405',
            //   }
            // ]
          },
        ],
      },
      {
        key: '010205',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020501',
            k0505: '非公有制经济组织从业人员(人)',
            parent: '010205',
            style: { borderTop: 'none' },
            // children: [
            //   {
            //     key: '0102050101',
            //     k0505: '截止目前发展数',
            //     parent: '01020501',
            //   }
            // ]
          },
        ],
      },
      {
        key: '010206',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020601',
            k0505: '社会组织从业人员(人)',
            parent: '010206',
            style: { borderTop: 'none' },
            // children: [
            //   {
            //     key: '0102060101',
            //     k0505: '截止目前发展数',
            //     parent: '01020601',
            //   }
            // ]
          },
        ],
      },
    ],
  },
];

// 发展党员工作情况统计表
export const TableColDepMemStatistical = [
  {
    key: '0109',
    k0505: '组织名称',
    parent: '01',
    style: { width: 200 },
  },
  {
    key: '0101',
    k0505: '总量计划(人)',
    parent: '01',
    // style: { width: 200 },
  },
  {
    key: '0102',
    k0505: '结构计划',
    parent: '01',
    // style: { width: 200 },
    children: [
      {
        key: '010201',
        k0505: '35岁及以下(人)',
        parent: '0102',
      },
      {
        key: '010202',
        k0505: '产业工人(含农民工和劳务派遣工)(人)',
        parent: '0102',
        // style: { borderBottom: 'none', visibility: 'hidden' },
      },
      {
        key: '010203',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020301',
            k0505: '农牧渔民(人)',
            parent: '010203',
            style: { borderTop: 'none' },
          },
          {
            key: '01020302',
            k0505: '35岁及以下(人)',
            parent: '010203',
          },
        ],
      },
      {
        key: '010203',
        k0505: '知识分子',
        parent: '0102',
        // style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020401',
            k0505: '企事业单位、社会组织专业技术人员(人)',
            parent: '010203',
            style: { borderTop: 'none' },
          },
          {
            key: '01020402',
            k0505: '省级及以上人才引进和培养支持计划入选人员(人)',
            parent: '010203',
          },
          {
            key: '01020403',
            k0505: '高校中青年教师和学术科研骨干(人)',
            parent: '010203',
          },
          {
            key: '01020404',
            k0505: '高技能人才(人)',
            parent: '010203',
          },
        ],
      },
      {
        key: '010204',
        k0505: '隐藏',
        parent: '0102',
        style: { borderBottom: 'none', visibility: 'hidden' },
        children: [
          {
            key: '01020501',
            k0505: '大学生(人)',
            parent: '010204',
            style: { borderTop: 'none' },
          },
          {
            key: '01020502',
            k0505: '“双一流”建设高校(人)',
            parent: '010204',
            // style: { borderTop: 'none' },
          },
        ],
      },
      {
        key: '010205',
        k0505: '非公企业(人)',
        parent: '0102',
        // style: { borderBottom: 'none', visibility: 'hidden' },
      },
      {
        key: '010206',
        k0505: '社会组织(人)',
        parent: '0102',
        // style: { borderBottom: 'none', visibility: 'hidden' },
      },
      {
        key: '010207',
        k0505: '新就业群体(人)',
        parent: '0102',
        // style: { borderBottom: 'none', visibility: 'hidden' },
      },
    ],
  },
];

// 党支部联系点工作开展情况统计表
export const TableColNew4: any = [
  {
    key: '01',
    k0505: '党（工）委（市州、县市区、省级单位）名称',
    parent: '-1',
    style: { width: 200 },
  },
  {
    key: '02',
    k0505: '建立党支部联系点（个）',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '领导干部到联系点开展调研（次）',
    parent: '-1',
  },
  {
    key: '04',
    k0505: '年度开展调研少于2次的党支部联系点（个）',
    parent: '-1',
  },
  {
    key: '05',
    k0505: '领导干部到联系点讲党课（次）',
    parent: '-1',
  },
  {
    key: '06',
    k0505: '领导干部到联系点作专题报告（次）',
    parent: '-1',
  },
  {
    key: '07',
    k0505: '年度讲党课或作专题报告少于1次的党支部联系点（个）',
    parent: '-1',
  },
  {
    key: '08',
    k0505: '领导干部为党支部联系点办理实事（件）',
    parent: '-1',
  },
  {
    key: '09',
    k0505: '领导干部本年度内联系点办理实事少于1件的党支部联系点（个）',
    parent: '-1',
  },
  {
    key: '10',
    k0505: '备注',
    parent: '-1',
  },
];

// 党支部联系点工作开展情况明细表
export const TableColNew5: any = [
  {
    key: '01',
    k0505: '姓名',
    parent: '-1',
  },
  {
    key: '02',
    k0505: '类型',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '层级',
    parent: '-1',
  },
  {
    key: '04',
    k0505: '支部联系点名称',
    parent: '-1',
  },
  {
    key: '05',
    k0505: '支部书记姓名及联系电话',
    parent: '-1',
  },
  {
    key: '06',
    k0505: '联系起始时间（20XX年XX月XX日）',
    parent: '-1',
  },
  {
    key: '07',
    k0505: '到联系点开展工作情况',
    parent: '-1',
    children: [
      {
        key: '0701',
        k0505: '开展工作时间（20XX年XX月XX日）',
        parent: '07',
      },
      {
        key: '0702',
        k0505: '开展工作类型（调研/讲党课/作报告/办实事）',
        parent: '07',
      },
      {
        key: '0703',
        k0505: '工作简介（30字左右）',
        parent: '07',
      },
    ],
  },
];

//党员党费交纳统计表
export const TableColNew6: any = [
  {
    key: '01',
    k0505: '党的组织（机构）名称（党支部/党总支/党<工>委）',
    parent: '-1',
  },
  {
    key: '02',
    k0505: '党员人数',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '应交党费合计（元）',
    parent: '-1',
  },
  {
    key: '04',
    k0505: '实交党费合计（元）',
    parent: '-1',
  },
  {
    key: '05',
    k0505: '实交党费低于应交党费的人数',
    parent: '-1',
  },
  {
    key: '06',
    k0505: '联系起始时间（20XX年XX月XX日）',
    parent: '-1',
  },
  {
    key: '07',
    k0505: '备注',
    parent: '-1',
  },
];

// 党员党费交纳明细表
export const TableColNew7: any = [
  {
    key: '01',
    k0505: '所在党支部',
    parent: '-1',
  },
  {
    key: '02',
    k0505: '姓名',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '性别',
    parent: '-1',
  },
  {
    key: '04',
    k0505: '出生年月（xxxx.xx.xx）',
    parent: '-1',
  },
  {
    key: '05',
    k0505:
      '党员类别【1.公务员（参公）岗位党员2.事业岗位党员3.机关工勤岗位党员4.事业工勤岗位党员5.税务系统党员6.固定收入农民党员7.农民党员8.困难党员9.流动（失联）党员10.离退党员（退休党支部）11.企业党员（非公党支部）】',
    parent: '-1',
  },
  {
    key: '06',
    k0505: '交费基数（元/月）',
    parent: '-1',
  },
  {
    key: '07',
    k0505: '应交纳党费（元）',
    parent: '-1',
  },
  {
    key: '08',
    k0505: '实交纳党费（元）',
    parent: '-1',
  },
  {
    key: '09',
    k0505: '实交党费是否低于应交党费（是/否）',
    parent: '-1',
  },
  {
    key: '10',
    k0505: '备注',
    parent: '-1',
  },
];

//基层党组织开展民主评议党员情况统计表
export const TableColNew8: any = [
  {
    key: '01',
    k0505: '单位名称',
    parent: '-1',
    style: { width: 200 },
  },
  {
    key: '02',
    k0505: '参加民主评议党员总数（人）',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '0201',
        k0505: '参加民主评议党员总数（人）',
        parent: '02',
        style: { borderTop: 'none', visibility: 'hidden' },
      },
      {
        key: '0202',
        k0505: '被评为“优秀”党员人数（人）',
        parent: '02',
        // style: { borderBottom: 'none', visibility: 'hidden' },
      },
      {
        key: '0203',
        k0505: '被评为“合格”党员人数（人）',
        parent: '02',
      },
      {
        key: '0204',
        k0505: '被评为“基本合格”党员人数（人）',
        parent: '02',
      },
      {
        key: '0205',
        k0505: '被评为“不合格”党员人数（人）',
        parent: '02',
      },
      {
        key: '0206',
        k0505: '不评定等次人数（人）',
        parent: '02',
      },
    ],
  },
  {
    key: '03',
    k0505: '组织处置人数（人）',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '0301',
        k0505: '组织处置人数（人）',
        parent: '03',
        style: { borderTop: 'none', visibility: 'hidden' },
      },
      {
        key: '0302',
        k0505: '限期改正（人）',
        parent: '03',
        // style: { borderBottom: 'none', visibility: 'hidden' },
      },
      {
        key: '0303',
        k0505: '劝其退党（人）',
        parent: '03',
      },
      {
        key: '0304',
        k0505: '劝而不退除名（人）',
        parent: '03',
      },
      {
        key: '0305',
        k0505: '自行脱党除名（人）',
        parent: '03',
      },
      {
        key: '0306',
        k0505: '退党除名（人）',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '已受党纪处分不再进行组织处置',
    parent: '-1',
  }
];

export const config = [
  {
    reportCode: 'excel_1',
    name: '农村党建调度表',
    head: TableCol,
    width: fakeLine(TableCol) * 40 + 200,
    nodeWith: 40,
  },
  {
    reportCode: 'excel_2',
    name: '发展党员工作情况调度表',
    head: TableColDepMem,
    width: fakeLine(TableColDepMem) * 54 + 200,
    nodeWith: 54,
  },
  {
    reportCode: 'excel_3',
    name: '发展党员工作情况统计表',
    head: TableColDepMemStatistical,
    width: fakeLine(TableColDepMemStatistical) * 54 + 200,
    nodeWith: 54,
  },
  // {
  //   reportCode: 'excel_4',
  //   name: '村“两委”班子成员调度表',
  //   head: TableColNew4,
  //   width: fakeLine(TableColNew4) * 100 + 200,
  //   nodeWith: 100,
  // },
  {
    reportCode: 'excel_5',
    name: '党支部联系点工作开展情况统计表',
    head: TableColNew4,
    width: fakeLine(TableColNew4) * 100 + 200,
    nodeWith: 100,
  },
  {
    reportCode: 'excel_6',
    name: '党员党费交纳统计表',
    head: TableColNew6,
    width: fakeLine(TableColNew6) * 120 + 200,
    nodeWith: 120,
  },
  // {
  //   reportCode: 'excel_7',
  //   name: '党员党费交纳明细表',
  //   head: TableColNew7,
  //   width: fakeLine(TableColNew7) * 120 + 200,
  //   nodeWith: 120,
  // },
  // 2025/3/18  添加新表 基层党组织开展民主评议党员情况统计表
  {
    reportCode: 'excel_8',
    name: '基层党组织开展民主评议党员情况统计表',
    head: TableColNew8,
    width: fakeLine(TableColNew8) * 120 + 200,
    nodeWith: 120,
  },
];
