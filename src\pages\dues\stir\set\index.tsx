/**
 * 新增编辑收支
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Al<PERSON>,
  Button,
  Col,
  DatePicker,
  Divider,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Switch,
  Tag,
  Tooltip,
} from 'antd';
import {connect} from "dva";
import moment from 'moment'
import Notice from '../../../../components/Notice';
import { getSession } from '@/utils/session';
import { isEmpty } from '../../../../utils/method';
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 9 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

@connect(({dues,login})=>({dues,login}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
    };
  }
  showModal=()=>{
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
      org
    });
  };
  disabledDate=(current)=>{
    return current && current > moment().endOf('day')||current<moment('2019');
  };

  handleOk=()=>{
    const { data ,onChange } = this.props;
    this.props.form.validateFieldsAndScroll(async(errors, values) => {
      if (errors){
        return
      }
      const { isAllocate,allocateMoney,allocateRatio,...val }=values;
      this.props.dispatch({
        type:'dues/saveFeeAllocate',
        payload:{
          data:{
            code:data['code'],
            isAllocate:isAllocate?'1':'0',
            allocateRatio:allocateRatio*0.01,
            ...val,
            allocateMoney:allocateMoney,
            allocateType:data['allocateMoney']==allocateMoney?'1':'0'
          }
        }
      }).then(res=>{
        if (res['code'] == 0) {
          Notice("操作提示",'保存成功!',"check-circle","green");
          this.handleCancel();
          onChange()
        }else {
          Notice("操作提示",res['message'],"exclamation-circle-o","orange");
        }
      })
    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      data:{},
    });
    this.props.form.resetFields()
  };

  calculate=(v)=>{
    let money=v.target.value.substring(0,v.target.value.length-1);
    const { data }=this.props;
    let stirMoney=data['money']*parseFloat(money)*0.01;
    this.setState({
      stirMoney
    })
  };

  render(){
    const {visible,stirMoney=0}=this.state;
    const {title='', data={},dues={},loading:{effects = {}} = {},children }=this.props;
    const {getFieldDecorator}=this.props.form;
    return (
      <div>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          destroyOnClose
          title={title||''}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={600}
          bodyStyle={{height:'auto',overflow:'auto'}}
        >
          <Form {...formItemLayout}>
            <Row>
              <Col span={24}>
                <Alert
                  message="温馨提示"
                  description={
                    <div>
                      <p>1.该组织目前正式党员<span style={{color:'red'}}>{data['fullMemNum']}</span>人,预备党员
                        <span style={{color:'red'}}>{data['proMemNum']}</span>人
                        其中离退休党员<span style={{color:'red'}}>{data['retirementMemNum']}</span>人,设置标准应收金额
                        <span style={{color:'red'}}>{data['shouldPayMoney']}</span>元,已收金额<span style={{color:'red'}}>{data['money']}</span>元</p>
                      <p>2.党费返还是上级党委从每年的党费收入中拿出一部分资金作为基层党委的党建经费，主要用于党员教育、党员学习、党员活动场所建设等方面，是专款专用。按照离退休支部按每年收缴党费的50％返还，其余基层组织按上一年收缴党费的20％返还。 </p>
                      <p>3.按照离退休支部按每年收缴党费的50％返还，其余基层组织按上一年收缴党费的20％返还。</p>
                    </div>
                  }
                  type="info" showIcon />
              </Col>
              <Col span={24}>
                <FormItem
                  label="下拨比例"
                  style={{marginTop:'20px'}}
                >
                  {getFieldDecorator('allocateRatio', {
                    initialValue:isEmpty(data['allocateRatio'])?0:data['allocateRatio']*100,
                    rules: [
                      { required: true, message: '请选择!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                      <InputNumber
                        min={0}
                        max={100}
                        style={{width:'100%'}}
                        formatter={value => `${value}%`}
                        // parser={value => value.replace('%', '')}
                        placeholder={'请设置下拨比例'}
                        onBlur={(value)=>this.calculate(value)}
                      />
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label={'下拨金额'}
                >
                  {getFieldDecorator('allocateMoney', {
                    initialValue:isEmpty(data['allocateMoney'])?stirMoney:data['allocateMoney'],
                    rules: [
                      { required: true, message: '请填写金额!' },
                      // { validator: this.validFunction }
                    ],
                    normalize: (a, prev) => {
                      if (a && !/^(([1-9]\d*)|0)(\.\d{0,2}?)?$/.test(a)) {
                        if (a === '.') {
                          return '0.';
                        }
                        return prev;
                      }
                      return a;
                    }
                  })(
                    <Input placeholder={'请填写金额'}/>
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label="正式党员"
                >
                  {getFieldDecorator('fullMemNum', {
                    initialValue:isEmpty(data['fullMemNum'])?0:data['fullMemNum'],
                    rules: [
                      { required: true, message: '请选择!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <InputNumber
                      min={0}
                      // max={100}
                      style={{width:'100%'}}
                      formatter={value => `${value}人`}
                      // parser={value => value.replace('%', '')}
                      placeholder={'请设置下拨比例'}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label="预备党员"
                >
                  {getFieldDecorator('proMemNum', {
                    initialValue:isEmpty(data['proMemNum'])?0:data['proMemNum'],
                    rules: [
                      { required: true, message: '请选择!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <InputNumber
                      min={0}
                      // max={100}
                      style={{width:'100%'}}
                      formatter={value => `${value}人`}
                      // parser={value => value.replace('%', '')}
                      placeholder={'请设置下拨比例'}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label="离退休党员"
                >
                  {getFieldDecorator('retirementMemNum', {
                    initialValue:isEmpty(data['retirementMemNum'])?0:data['retirementMemNum'],
                    rules: [
                      { required: true, message: '请输入!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <InputNumber
                      min={0}
                      // max={100}
                      style={{width:'100%'}}
                      formatter={value => `${value}人`}
                      // parser={value => value.replace('%', '')}
                      placeholder={'离退休党员'}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label="是否已拨"
                >
                  {getFieldDecorator('isAllocate', {
                    initialValue:isEmpty(data['isAllocate'])?true:data['isAllocate']=='1',
                    valuePropName:'checked',
                    rules: [
                      { required: true, message: '请选择!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <Switch checkedChildren="是" unCheckedChildren="否"  />
                  )}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </Modal>
      </div>
    );
  }
}
export default Form.create()(index)
