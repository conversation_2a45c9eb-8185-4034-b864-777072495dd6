/**
 * 党员流回
 */

import React from 'react'
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Avatar, Button, Col, Dropdown, Menu, Modal, Row, Input, Radio, DatePicker } from 'antd';
import qs from 'qs';
import {connect} from "dva";
import { isEmpty } from '@/utils/method';
import { getSession } from '@/utils/session';
import DictArea from '@/components/DictArea';
import DictSelect from '@/components/DictSelect';
import OrgSelect from '@/components/OrgSelect'
import styles from './index.less'
import {root} from '@/common/config'
import moment from 'moment'
import DictTreeSelect from '@/components/DictTreeSelect';
import Date from '@/components/Date';
import Tip from '@/components/Tip';

const FormItem=Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 17 },
  },
};
const menuData=[
  {
    code:'1',
    name:'基本信息',
    icon:'star',
  },
  {
    code:'2',
    name:'班子成员',
    icon:'qrcode',
  },
];
@connect(({unit,commonDict,loading})=>({unit,commonDict,loading:loading.effects['unit/getList']}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    let obj=menuData[0];
    this.state={
      visible:false,
      key:obj['code'],
      keyPath:[obj['code']],
      selected:obj,
    };
  }

  showModal=()=>{
    const { data:{ id='' }={},keyword=''} = this.props;
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
    },()=>{
      this.selectMem()
    });
  };
   selectMem=()=>{
     const { data={} }=this.props;
      this.props.dispatch({
        type:'flowMem/detail',
        payload:{
          code:data['code']
        }
      }).then(res=>{
      if (res.code===0){
      this.setState({
          detailInfo:res['data']
        })
    }
    })
  };
  handleOk=()=>{
    this.props.form.validateFieldsAndScroll(async(errors, values) => {
      if (errors){
        return
      }
      const { data={},onChange }=this.props;
      const {backFlowType,org,backFlowJob,backFlowOrgLinkman,backFlowOrgPhone,backFlowReason,backFlowDate,backflow_is_hold  }=values;
      let val={
        code:data['code'],
        backflowDate:isEmpty(backFlowDate)?'':moment(backFlowDate).valueOf(),
        backflowTypeCode:isEmpty(backFlowType)?'':backFlowType['id'],
        backflowTypeName:isEmpty(backFlowType)?'':backFlowType['name'],
        backflowOrgName:isEmpty(org)?'':org[0]['name'],
        backflowOrgOrgCode:isEmpty(org)?'':org[0]['orgCode'],
        backflowOrgCode:isEmpty(org)?'':org[0]['code'],
        backflowOrgLinkman:isEmpty(backFlowOrgLinkman)?'':backFlowOrgLinkman,
        backflowOrgPhone:isEmpty(backFlowOrgPhone)?'':backFlowOrgPhone,
        backflowJobCode:isEmpty(backFlowJob)?'':backFlowJob['id'],
        backflowJobName:isEmpty(backFlowJob)?'':backFlowJob['name'],
        backflowReason:isEmpty(backFlowReason)?'':backFlowReason,
        backflow_is_hold:backflow_is_hold
      };
      this.props.dispatch({
        type:'flowMem/getBackOutMem',
        payload:{
          data:{
            ...val
          }
        }
      }).then(res=>{
        if (res['code']===0){
          Tip.success('操作提示', '操作成功');
          onChange(true);
          this.handleCancel();
        }
      })
    });
  };
  handleCancel=()=>{
    this.setState({
      visible:false
    });
    this.props.form.resetFields()
  };
  disabledDate=(current)=>{
    return current && current > moment().endOf('day');
  };
  validFunction = (rule, value, callback) => {
    let han= /^[\u4e00-\u9fa5]+$/;
    if (value){
      switch (rule.field) {
        case 'backFlowOrgLinkman':
          if (value.length>20){
            return callback('组织联系人不能超过20个字符')
          }else if (!han.test(value)) {
            return callback('联系人名称不合法')
          }
          break;
        case 'backFlowOrgPhone':
          if (!(/^1[345789]\d{9}$/).test(value)){
            return callback('手机号码不合法')
          }
          break;
      }
    }
    callback()
  };

  render() {
    const {visible,detailInfo={}}=this.state;
    const { children,data={} }=this.props;
    const { getFieldDecorator  } = this.props.form;
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          title="党员流回"
          className='out_Modal'
          destroyOnClose
          closable={false}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={700}
        >
          <div className='container'>
            {/*<div className={styles.head}>*/}
            {/*  <div>*/}
            {/*    <p>党员:{detailInfo['memName']}</p>*/}
            {/*    <p>党员类型:{detailInfo['memTypeName']}</p>*/}
            {/*  </div>*/}
            {/*  <div>*/}
            {/*    <p><span>从:</span>{detailInfo['memOrgName']}</p>*/}
            {/*    <p><span>到:</span>{detailInfo['outflowOrgName']}</p>*/}
            {/*  </div>*/}
            {/*</div>*/}
            <Form {...formItemLayout}>
            {/*  <FormItem*/}
            {/*    label={'流回类型'}*/}
            {/*  >*/}
            {/*    {getFieldDecorator('backFlowType', {*/}
            {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
            {/*      rules: [*/}
            {/*        { required: true, message: '请选择流回类型!' },*/}
            {/*        // { validator: this.validFunction }*/}
            {/*      ],*/}
            {/*    })(*/}
            {/*      <DictSelect codeType={'dict_d34'} backType={'object'} placeholder={'请选择流回类型'}/>*/}
            {/*    )}*/}
            {/*  </FormItem>*/}
            {/*  <FormItem*/}
            {/*    label={'流回前党支部'}*/}
            {/*  >*/}
            {/*    {getFieldDecorator('org', {*/}
            {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
            {/*      rules: [*/}
            {/*        { required: true, message: '请选择流回前党支部!' },*/}
            {/*        // { validator: this.validFunction }*/}
            {/*      ],*/}
            {/*    })(*/}
            {/*      <OrgSelect org={root} isPermissionCheck={'0'} placeholder={'请选择党支部'}/>*/}
            {/*    )}*/}
            {/*  </FormItem>*/}
            {/*  <FormItem*/}
            {/*    label={'流回前工作岗位'}*/}
            {/*  >*/}
            {/*    {getFieldDecorator('backFlowJob', {*/}
            {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
            {/*      rules: [*/}
            {/*        { required: true, message: '请选择流回前工作岗位!' },*/}
            {/*        // { validator: this.validFunction }*/}
            {/*      ],*/}
            {/*    })(*/}
            {/*      <DictTreeSelect codeType={'dict_d09'} placeholder={'请选择工作岗位'} parentDisable={true} backType={'object'}/>*/}
            {/*    )}*/}
            {/*  </FormItem>*/}
            {/*  <FormItem*/}
            {/*    label={'流回前支部联系人'}*/}
            {/*  >*/}
            {/*    {getFieldDecorator('backFlowOrgLinkman', {*/}
            {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
            {/*      rules: [*/}
            {/*        { required: true, message: '请选择流回前支部联系人!' },*/}
            {/*        { validator: this.validFunction }*/}
            {/*      ],*/}
            {/*    })(*/}
            {/*      <Input placeholder="请输入" />*/}
            {/*    )}*/}
            {/*  </FormItem>*/}
            {/*  <FormItem*/}
            {/*    label={'流回前支部联系人电话'}*/}
            {/*  >*/}
            {/*    {getFieldDecorator('backFlowOrgPhone', {*/}
            {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
            {/*      rules: [*/}
            {/*        { required: true, message: '请选择流回前支部联系人电话!' },*/}
            {/*        { validator: this.validFunction }*/}
            {/*      ],*/}
            {/*    })(*/}
            {/*      <Input placeholder="请输入" />*/}
            {/*    )}*/}
            {/*  </FormItem>*/}
            {/*  <FormItem*/}
            {/*    label={'流回原因'}*/}
            {/*  >*/}
            {/*    {getFieldDecorator('backFlowReason', {*/}
            {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
            {/*      rules: [*/}
            {/*        { required: true, message: '请填写流回原因!' },*/}
            {/*        // { validator: this.validFunction }*/}
            {/*      ],*/}
            {/*    })(*/}
            {/*      <Input placeholder="请输入" />*/}
            {/*    )}*/}
            {/*  </FormItem>*/}
            {/*  <FormItem*/}
            {/*    label={'党员活动证发放'}*/}
            {/*  >*/}
            {/*    {getFieldDecorator('backflow_is_hold', {*/}
            {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
            {/*      rules: [*/}
            {/*        { required: true, message: '请选择党员活动证是否发放!' },*/}
            {/*        // { validator: this.validFunction }*/}
            {/*      ],*/}
            {/*    })(*/}
            {/*      <RadioGroup>*/}
            {/*        <Radio value={1}>已发放</Radio>*/}
            {/*        <Radio value={0}>未发放</Radio>*/}
            {/*      </RadioGroup>*/}
            {/*    )}*/}
            {/*  </FormItem>*/}
              <FormItem
                label={'停止日期'}
              >
                {getFieldDecorator('backFlowDate', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择停止日期!' },
                    // { validator: this.validFunction }
                    // <DatePicker style={{width:'100%'}} disabledDate={this.disabledDate} placeholder={'请选择日期'}/>
                  ],
                })(

                  <Date disabledDate={this.disabledDate} />
                )}
              </FormItem>
            </Form>
          </div>
        </Modal>
      </React.Fragment>

    )
  }
}
export default Form.create()(index);
