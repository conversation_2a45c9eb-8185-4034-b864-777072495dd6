/**
 * 字典select组件
 */
import React from 'react';
import { Select, TreeSelect } from 'antd';
import { connect } from 'dva';
import _isEmpty from 'lodash/isEmpty';
import { getLocalSession } from '@/utils/session';
import { DownOutlined } from '@ant-design/icons';
import styles from './index.less';
const Option = Select.Option;
interface propsType {
  codeType: string;
  placeholder?: string;
  codeValue?: string;
  codeName?: string;
  onChange?: (any) => void;
  onFocus?: () => void;
  filter?: Function;
  backType?: 'object';
  dispatch?: any;
  commonDict?: any;
  mode?: 'multiple';
  initValue?: any;
  disabled?: boolean;
  searchKey?: Array<string>;
  dicName?: string;
  noDraw?: Array<string>;
  showConstant?: boolean;
  getDictWay?: String;
  itemsDisabled?: Array<string>;
  showMoreBtn?: boolean;
}
//@ts-ignore
@connect(({ commonDict }) => ({ commonDict }), null, null, { forwardRef: true })
export default class index extends React.Component<propsType, any> {
  static clear() {}

  // 唯一实例ID
  private instanceId: string;

  constructor(props) {
    super(props);
    this.state = {
      value: undefined,
      clear: false,
      hasScrollbar: false,
      isAtBottom: false,
    };
    index.clear = this.clearAll;
    // 初始化唯一实例ID
    this.instanceId = `dict-select-${props.codeType}-${Math.random().toString(36).slice(2, 10)}`;
  }
  static defaultProps = {
    codeValue: 'key', //node 唯一key
    codeName: 'name', //node 显示名称
    searchKey: ['key', 'name', 'pinyin'], //默认查询条件
    dicName: 'dicName',
    placeholder: '请选择',
    mode: undefined,
    disabled: false,
    getDictWay: 'commonDict/getDict',
    showMoreBtn: true,
  };
  clearAll = () => {
    this.setState({
      value: undefined,
      clear: true,
    });
  };
  componentDidMount(): void {
    const { codeType, commonDict } = this.props;
    if (!commonDict[codeType]) {
      this.getDict();
    }
  }
  getDict = () => {
    const { codeType, onFocus, dicName, getDictWay } = this.props;
    this.props.dispatch({
      // type:'commonDict/getDict',
      type: getDictWay,
      payload: {
        data: {
          codeType,
          [dicName as string]: codeType,
        },
      },
    });
    if (onFocus) {
      onFocus();
    }
  };
  onChange = (value) => {
    const { codeType, codeValue, onChange, backType, mode } = this.props;
    if (Array.isArray(value)) {
      let data: any = [];
      value.forEach((val) => {
        if (val.includes('_temp')) {
          data.push(val.split('_temp')[0]);
        } else {
          data.push(val);
        }
      });
      value = data;
    } else {
      if (value && value.includes('_temp')) {
        value = value.split('_temp')[0];
      }
    }
    this.setState({ value, clear: !value });
    if (backType === 'object') {
      const data = this.props.commonDict[codeType];
      if (data) {
        if (mode) {
          let back: Array<object> = [];
          for (let ob of value) {
            let find = data.find((obj) => obj[codeValue as string] !== undefined && obj[codeValue as string] === ob);
            if (find) {
              back.push(find);
            }
          }
          if (back.length === 0) {
            onChange && onChange(undefined);
          } else {
            onChange && onChange(back);
          }
        } else {
          let find = data.find((obj) => obj[codeValue as string] !== undefined && obj[codeValue as string] === value);
          if (find && onChange) {
            onChange(Object.assign({}, find));
          } else {
            onChange && onChange(undefined);
          }
        }
      }
    } else if (onChange) {
      onChange(value);
    }
  };
  onSelect = (value, node) => {
    const { dataRef } = node;
    const { codeValue = '', codeType } = this.props;
    const data = this.props.commonDict[codeType];
    let item: any = [];
    let itemTemp: any = getLocalSession(`${codeType}_temp`) || [];
    let find = itemTemp.find((obj) => obj[codeValue] == dataRef[codeValue]);
    if (find) {
      find['onCount']++;
    } else {
      itemTemp.push({ [codeValue]: dataRef[codeValue], onCount: 1 });
    }
    //排序
    itemTemp.sort((a, b) => b['onCount'] - a['onCount']);
    //查询数据并缓存
    itemTemp.forEach((obj, index) => {
      if (index < 3) {
        let find1 = data.find((item) => item[codeValue] == obj[codeValue]);
        if (find1) {
          item.push({ ...find1 });
        }
      }
    });
    localStorage.setItem(codeType, JSON.stringify(item));
    localStorage.setItem(`${codeType}_temp`, JSON.stringify(itemTemp));
    this.setState({
      time: new Date().valueOf(),
    });
  };
  filterOption = (val, node) => {
    const dataRef = node.props;
    // return dataRef['value'].includes(val) || dataRef['children'].includes(val)
    return dataRef['value'].includes(val);
  };
  // 检查滚动位置
  checkScrollPosition = (scrollElement) => {
    if (scrollElement) {
      // 如果滚动位置 + 容器高度 >= 内容总高度 - 1 (允许1px误差)，则认为已滚动到底部
      const isAtBottom = scrollElement.scrollTop + scrollElement.clientHeight >= scrollElement.scrollHeight - 1;
      if (isAtBottom !== this.state.isAtBottom) {
        this.setState({ isAtBottom });
      }
    }
  };
  // 检查是否有滚动条
  checkForScrollbar = () => {
    // 使用特定的类名来定位下拉框
    setTimeout(() => {
      // antd 4.15.3 的下拉框结构不同，需要尝试多个可能的选择器
      const selectors = [
        `.ant-select-dropdown.${this.instanceId} .rc-virtual-list-holder`,
        `.ant-select-dropdown.${this.instanceId} .rc-virtual-list`,
        `.ant-select-dropdown.${this.instanceId} .ant-select-item-list`,
        `.ant-select-dropdown.${this.instanceId} .ant-select-dropdown-content`,
      ];

      let dropdown: Element | null = null;
      for (const selector of selectors) {
        const element = document.querySelector(selector);
        if (element) {
          dropdown = element;
          break;
        }
      }

      if (dropdown) {
        const hasScrollbar = (dropdown as HTMLElement).scrollHeight > (dropdown as HTMLElement).clientHeight;
        if (this.state.hasScrollbar !== hasScrollbar) {
          this.setState({ hasScrollbar });
        }

        // 添加滚动事件监听器
        dropdown.addEventListener('scroll', () => this.checkScrollPosition(dropdown));
      }
    }, 100);
  };
  // 处理点击更多选项按钮
  handleMoreClick = () => {
    // antd 4.15.3 的下拉框结构不同，需要尝试多个可能的选择器
    const selectors = [
      `.ant-select-dropdown.${this.instanceId} .rc-virtual-list-holder`,
      `.ant-select-dropdown.${this.instanceId} .rc-virtual-list`,
      `.ant-select-dropdown.${this.instanceId} .ant-select-item-list`,
      `.ant-select-dropdown.${this.instanceId} .ant-select-dropdown-content`,
    ];

    let scrollContainer: Element | null = null;
    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        scrollContainer = element;
        break;
      }
    }

    if (scrollContainer) {
      // 直接使用scrollTop
      const currentScroll = (scrollContainer as HTMLElement).scrollTop;
      (scrollContainer as HTMLElement).scrollTop = currentScroll + 100;

      // 触发滚动事件确保虚拟列表更新渲染
      setTimeout(() => {
        const event = new Event('scroll', { bubbles: true });
        scrollContainer!.dispatchEvent(event);

        // 检查是否滚动到底部
        this.checkScrollPosition(scrollContainer);
      }, 50);
    }
  };
  render(): React.ReactNode {
    const { codeType, codeValue, codeName, placeholder, mode, initValue, disabled, noDraw = [], filter, showConstant = true, itemsDisabled = [], showMoreBtn = true } = this.props;
    const data = this.props.commonDict[codeType] || [];
    const constant = getLocalSession(codeType) || [];
    const { clear, hasScrollbar } = this.state;
    let value = this.state['value'] || initValue;
    let tempData = [...data].filter((item) => !item?.enabled);
    if (clear) {
      value = undefined;
    }
    if (mode == 'multiple' && !value) {
      value = [];
    }
    if (filter) {
      tempData = filter(tempData);
    }
    let filterConstant = filter ? filter(constant) : constant;
    return (
      <React.Fragment>
        <Select
          allowClear
          showSearch
          mode={mode}
          disabled={disabled}
          style={{ width: '100%' }}
          onFocus={this.getDict}
          onChange={this.onChange}
          onSelect={this.onSelect}
          filterOption={this.filterOption}
          placeholder={placeholder}
          value={value}
          dropdownClassName={`${styles.dropdownClass} dict-select-dropdown ${this.instanceId}`}
          onDropdownVisibleChange={(open) => {
            if (open) {
              this.checkForScrollbar();
            }
          }}
          dropdownRender={(menu) => (
            <div>
              {menu}
              {showMoreBtn && hasScrollbar ? (
                <div className="more-options-button" onClick={this.handleMoreClick}>
                  {this.state.isAtBottom ? (
                    '已到底部'
                  ) : (
                    <React.Fragment>
                      更多选项
                      <DownOutlined className="arrow-icon" />
                    </React.Fragment>
                  )}
                </div>
              ) : (
                <React.Fragment />
              )}
            </div>
          )}
        >
          {mode != 'multiple' && showConstant && filterConstant.length > 0 && (
            <React.Fragment>
              <Option value={'-1'} disabled>
                常用选项
              </Option>
              {filterConstant.map((item, index) => (
                <Option
                  key={`${item[codeValue as string]}_temp`}
                  value={`${item[codeValue as string]}_temp`}
                  dataRef={item}
                  style={index == filterConstant.length - 1 ? { borderBottom: '1px solid #d9d9d9' } : {}}
                  disabled={itemsDisabled.includes(item[codeValue || ''])}
                >
                  {item[codeValue as string]} | {item[codeName as string]}
                </Option>
              ))}
            </React.Fragment>
          )}
          {tempData.length > 0 &&
            tempData.map((obj, index) => {
              const title = (
                <span title={obj['remark']}>
                  {obj[codeValue as string]} | {obj[codeName as string]}
                </span>
              );
              return (
                <Option
                  title={obj[codeValue as string]}
                  value={obj[codeValue as string]}
                  key={obj[codeValue as string] || index}
                  style={{ display: noDraw.includes(obj[codeValue || '']) ? 'none' : '' }}
                  dataRef={obj}
                  disabled={obj?.disabled || itemsDisabled.includes(obj[codeValue || ''])}
                >
                  {title}
                </Option>
              );
            })}
        </Select>
      </React.Fragment>
    );
  }
}
