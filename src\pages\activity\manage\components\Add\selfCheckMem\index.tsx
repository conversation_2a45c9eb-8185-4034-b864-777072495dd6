import React,{Fragment} from 'react';
import { MinusCircleOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Row, Col, Input } from "antd";
import style from './index.less';
import _isEmpty from 'lodash/isEmpty';
import _last from 'lodash/last';
import _trim from 'lodash/trim';
import _isEqual from 'lodash/isEqual';
import { validateLength } from '@/utils/formValidator';
const FormItem = Form.Item;
const formItemLayout1 = {labelCol: {xs: { span: 24 }, sm: { span: 8 }}, wrapperCol: {xs: { span: 24 }, sm: { span: 16 }}};
let uuid = 0;
export default class index extends React.Component<any,any> {
  constructor(props) {
    super(props);
    this.state = {
      arr:[],
      // init:[]
    }
  }
  static getDerivedStateFromProps = (nextProps:any, prevState:any) => {
    const state = [];
    const {init} = nextProps;
    const {_init} = prevState;
    if(!_isEqual(init,_init)){
      state['_init'] = init;
      if(!_isEmpty(init)){
        let numberArr:Array<number> = [];
        for(let i=0; i < init.length;i++){
          numberArr.push(i)
        }
        state['arr'] = numberArr;
      }
    }
    return state;
  };
  name = (rule, value, callback) => {
    if(!_isEmpty(value) && _trim(value).length == 0){
      callback('请正确输入名字')
    }
    if(!_isEmpty(value) &&value.length > 10){
      callback('姓名不能超过10字')
    }
    callback()
  };
  phone = (rule, value, callback) => {
    // if(!_isEmpty(value) && !_trim(value) && value.length >12){callback('手机号码在11位以内')}
    callback()
  };
  remark = (rule, value, callback) => {
    // if(!_isEmpty(value) && !_trim(value) && value.length >31){callback('备注在30位以内')}
    callback()
  };
  // 自定义添加
  selfAdd=()=>{
    const { form } = this.props;
    const {arr} =this.state;
    if(!_isEmpty(arr)){
      uuid = _last(arr)+1;
    }
    const nextKeys = arr.concat(uuid);
    this.setState({
      arr:nextKeys
    })
  };
  // 取消自定义
  canncel = () =>{
    this.setState({
      arr:[]
    })
  };
  // 删除
  selfRemove = (_item) => {
    const {arr} = this.state;
    if (arr.length === 1) {
      return;
    }
    this.setState({
      arr: arr.filter((item)=> item !== _item)
    })
  };
  render() {
    const {type,init} = this.props;
    const { getFieldDecorator } = this.props.form;
    const {arr} = this.state;
    return (
      <Fragment>
        {
          arr.map((item,index)=>{
            return (
              <div key={index} className={style.item}>
                <FormItem
                  label={`姓名`}
                >
                  {getFieldDecorator(`${type}[${item}].name`,{
                    rules: [{  required: true,message: '必填' }, {validator: (...e)=>validateLength(e, 16, 50)}],
                    initialValue:!_isEmpty(init[item])?init[item].name:undefined,
                  })(
                    <Input placeholder="请输入姓名" style={{width:'96%'}}/>
                  )}
                </FormItem>
                {/*<FormItem*/}
                  {/*label={`身份证号`}*/}
                {/*>*/}
                  {/*{getFieldDecorator(`${type}[${item}].idcard`,{*/}
                    {/*rules: [{  required: true,message: '必填' }, {validator: this.remark}],*/}
                    {/*initialValue:!_isEmpty(init[item])?init[item].idcard:undefined,*/}
                  {/*})(*/}
                    {/*<Input placeholder="请输入身份证号" style={{width:'96%'}}/>*/}
                  {/*)}*/}
                {/*</FormItem>*/}
                <FormItem
                  label="手机号"
                >
                  {getFieldDecorator(`${type}[${item}].phone`,{
                    rules: [{  required: true,message: '必填' }, {validator: this.phone}],
                    // normalize: (v, prev) => {
                    //   if (v && !/^(([1-9]\d*)|0)(\.\d{0,2}?)?$/.test(v)) {
                    //     if (v === '.') {
                    //       return '0.';
                    //     }
                    //     return prev;
                    //   }
                    //   return v;
                    // },
                    initialValue:!_isEmpty(init[item])?init[item].phone:undefined,
                  })(
                    <Input placeholder="请输入手机号" style={{width:'96%'}}/>
                  )}
                </FormItem>
                <FormItem
                  label={`备注`}
                >
                  {getFieldDecorator(`${type}[${item}].remark`,{
                    rules: [{  required: false,message: '必填' }, {validator: this.remark}],
                    initialValue:!_isEmpty(init[item])?init[item].remark:undefined,
                  })(
                    <Input placeholder="请输入备注" style={{width:'96%'}}/>
                  )}
                </FormItem>
                <div>
                  {arr.length > 1 ? (
                    <MinusCircleOutlined onClick={() => this.selfRemove(item)} />
                  ) : <div/>}
                </div>
              </div>
            );
          })
        }
        {_isEmpty(arr)? <a onClick={this.selfAdd}>手动录入党员</a>  :  <Fragment><a onClick={this.selfAdd}>继续添加</a>&nbsp;&nbsp;&nbsp;<a onClick={this.canncel}>取消添加</a></Fragment>}
      </Fragment>
    );
  }
}
