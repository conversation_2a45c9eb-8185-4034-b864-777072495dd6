import React, { useEffect, useState } from 'react';
import styles from './index.less';
import ListTable from '@/components/ListTable';
import { Button, Divider, Input, Modal, Select, TreeSelect,Popconfirm, DatePicker } from 'antd';
import { PlusSquareOutlined } from '@ant-design/icons';
import WhiteSpace from '@/components/WhiteSpace';
import DictTreeSelect from '@/components/DictTreeSelect';
import moment from 'moment';
const Option=Select.Option;
const operatorList = [
  {
    label: '不等于',
    rule: '!=',
    value: 'notEqual',
    type: ['NUMBER', 'VARCHAR2', 'DATE', 'BOOLEAN'],
  },
  {
    label: '一般应当等于',
    rule: '=',
    value: 'eq',
    type: ['NUMBER', 'VARCHAR2', 'DATE', 'BOOLEAN'],
  },
  {
    label: '小于',
    rule: '<',
    value: 'LessThan',
    type: ['NUMBER'],
  },
  {
    label: '大于',
    rule: '>',
    value: 'more',
    type: ['NUMBER'],
  },
  {
    label: '小于等于',
    rule: '<=',
    value: 'LessThanEq',
    type: ['NUMBER'],
  },
  {
    label: '大于等于',
    rule: '>=',
    value: 'moreEq',
    type: ['NUMBER'],
  },
  {
    label: '包含(值)',
    rule: '包含',
    value: 'contain',
    type: ['CODE'],
  },
  {
    label: '不包含(值)',
    rule: '不包含',
    value: 'notContain',
    type: ['CODE'],
  },
];
export default function(props:{list?:Array<any>,onChange?:any,desc?:Array<any>,codeTable?:Array<any>,codeTableCol?:Array<any>,showDesc?:boolean,commonDict?:any}){
  let [list,setList]:Array<any>=useState([{}]);
  const {desc=[],codeTable=[],codeTableCol=[],onChange,showDesc=true}=props;
  useEffect(()=>{
    setList(props.list && props.list.length>0 ? props.list : [{}]);
    getDesc(props.list && props.list.length>0 ? props.list : [{}]);
  },[JSON.stringify(props.list)])
  const itemInfoChange=(val,index)=>{
    let data=[...list];
    const item:any=codeTableCol.find(obj=>obj['id']==val);
    data[index]['compareId']=val;
    if(item){
      data[index]['compareCol']=item['colName'];
      data[index]['infoItemCode']=item['colLectionCode'];
      data[index]['infoItemType']=item['colType'];
    }else{
      data[index]['compareCol']='';
      data[index]['infoItemCode']='';
      data[index]['infoItemType']='';
    }
    data[index]['compareValue']=undefined;
    data[index]['compareType']=undefined;
    setList(data);
  }
  const valChange=async (val,key,index)=>{
    let data=[...list];
    data[index][key]=val;
    setList(data);
  }
  const valRest=(val,key,index)=>{
    let data=[...list];
    data[index][key]=val;
    setList(data);
  }
  const del=async (index)=>{
    let data:any=[];
    for (let i = 0; i < list.length; i++) {
      if(i!=index){
        data.push(list[i]);
      }
    }
    getDesc(data);
  };
  const add=async ()=>{
    let data=[...list];
    data.push({});
    setList([...data])
  };
  const getDesc=(data?)=>{
    let msg:any=[];
    for (const obj of data) {
      if(obj['infoSetName']){
        let str='';
        str+=`${obj['infoSetName']} `;
        if(obj['compareCol']){
          str+=`${obj['compareCol']} `;
        }
        if(obj['compareType']){
          let find = operatorList.find(ob=>ob['value']==obj['compareType']);
          if(find){
            str+=`${find['label']} `;
          }
        }
        if(obj['compareNames']){
          str+=`${obj['compareNames'].join('，')} `;
        }
        obj['desc']=str;
        msg.push(str);
      }
    }
    onChange && onChange(data,msg);
  }
  const columns=[
    {
      title:'序号',
      dataIndex:'num',
      width: 80,
      align: 'center',
      render: (text, record, index) => {
        return index + 1;
      },
    },
    {
      title:'信息集',
      dataIndex:'infoSetValue',
      width: 160,
      render: (text, record, index) => {
        return (
          <Select className={styles.select} style={{ width: 150 }} allowClear size={'small'} value={record['infoSetValue']} onChange={(value, option)=>{
            valRest(undefined,'compareId',index);
            valRest(undefined,'compareType',index);
            valRest(undefined,'compareValue',index);
            valChange(value,'infoSetValue',index);
            let find = codeTable.find(obj=>obj['id']==value);
            if(find){
              valChange(find['tableName'],'infoSetName',index)
            }
          }}>
            {
              codeTable.map(i => {
                return (
                  <Option className={styles.select} value={i['id']} key={i['id']}>{i['tableName']}</Option>
                );
              })
            }
          </Select>
        );
      },
    },
    {
      title:'信息项',
      dataIndex:'compareId',
      width: 160,
      render: (text, record, index) => {
        return (
          <Select
            className={styles.select}
            style={{ width: 150 }}
            allowClear
            size={'small'}
            value={record['compareId']}
            onChange={(val,option) => {
              itemInfoChange(val,index)
            }}
          >
            {
              codeTableCol.filter(obj=>obj['tableName']==record['infoSetName']).map(i => {
                return (
                  <Option className={styles.select} value={i.id} key={i.id} >{i['colName']}</Option>
                );
              })
            }
          </Select>
        );
      },
    },
    {
      title:'运算符',
      dataIndex:'compareType',
      width: 100,
      render: (text, record, index) => {
        return (
          <Select allowClear size={'small'} style={{ width: 90}}
                  value={record['compareType']}
                  onChange={(val) => {
                    valChange(val,'compareType',index);
                  }}
          >
            {
              operatorList.map(i => {
                return (
                  <Option className={styles.select} value={i.value} key={i.value}>{i.label}</Option>
                );
              })
            }
          </Select>
        );
      },
    },
    {
      title:'数值',
      dataIndex:'compareValue',
      width: 360,
      render: (text, record, index) => {
        if (record['infoItemType'] === 'lable') {
          return (
            <div key={record['infoItemCode']}>
              <DictTreeSelect
                showConstant={false}
                extendProps={{showCheckedStrategy: TreeSelect.SHOW_PARENT}}
                treeCheckable={true}
                initValue={record['compareValue'] || undefined}
                codeType={record['infoItemCode']}
                size={'small'}
                placeholder={record['remark']}
                style={{ width: '100%' }}
                onChange={(val, label, extra) => {
                  if(val.length>0){
                    const data=props['commonDict'][`${record['infoItemCode']}`];
                    let names:any=[];
                    let array=val;
                    data.filter(i => val.includes(i.key)).forEach(i=>{
                      names.push(i.name)
                    });
                    array.forEach(key => {
                      data.filter(i => `${i.key}`.startsWith(key)).forEach(i=>{
                        array.push(i.key);
                      });
                    });
                    array=array.concat(val);
                    array=[...new Set(array)];
                    names=[...new Set(names)];
                    if(Array.isArray(val)){
                      valChange(array,'compareValue',index);
                    }else{
                      valChange([val],'compareValue',index);
                    }
                    valChange(names,'compareNames',index);
                  }else{
                    valChange([],'compareValue',index);
                    valChange([],'compareNames',index);
                  }
                }}
              />
            </div>
          );
        }
        if(record['infoItemType']=='date'){
          return (
            <DatePicker
              value={record['compareValue'] && record['compareValue'].length>0 ? moment(record['compareValue'][0]) : undefined }
              onChange={(val)=>{
                valChange([val.valueOf()],'compareValue',index);
                valChange([val.format('YYYY.MM.DD')],'compareNames',index);
              }}
              style={{width:'100%'}}
            />
          )
        }
        if(record['infoItemType']=='boolean'){
          return (
            <Select
              value={record['compareValue'] && record['compareValue'].length>0 ? record['compareValue'][0] : undefined }
              onChange={(val)=>{
                valChange([val],'compareValue',index);
                valChange([val=='1' ? '是' : '否'],'compareNames',index);
              }}
              placeholder={'请选择'}
              style={{width:'100%'}}
            >
              <Select.Option value={'1'}>是</Select.Option>
              <Select.Option value={'2'}>否</Select.Option>
            </Select>
          )
        }
        return (
          <Input
            style={{ width: '100%' }}
            allowClear
            size={'small'}
            type={record['compareValue']=='年龄' ? "number" : 'text'}
            value={record['compareValue']=='年龄' ? record['ageValue'] : record['compareValue']}
            placeholder={record['remark']}
            onChange={(val)=>{
              valChange([val.target.value],'compareValue',index);
              valChange([val.target.value],'compareNames',index);
            }}
          />
        );
      },
    },
    {
      title:'操作',
      dataIndex:'8',
      width: 60,
      render:(text, record, index)=>{
        return (
          <Popconfirm title={'是否确认删除？'} onConfirm={()=>del(index)}>
            <a className={'del'}>删除</a>
          </Popconfirm>
        );
      },
    },
  ]
  return(
    <React.Fragment>
      {
        showDesc && <React.Fragment>
          <div style={{minHeight:180,border: '1px solid #d9d9d9',padding:'12px'}}>
            {
              desc.map((msg,index)=>{
                return(
                  <p key={index} style={{margin:'unset'}}>
                    {index+1}.{msg}
                  </p>
                )
              })
            }
          </div>
          <WhiteSpace/>
          <WhiteSpace/>
        </React.Fragment>
      }
      <div className={styles.table} onBlur={()=>getDesc(list)}>
        <ListTable columns={columns} data={list} pagination={false}/>
        <Button style={{marginTop: 10, height: 43}} type="dashed" onClick={add} block>
          <PlusSquareOutlined/>
          增加条件
        </Button>
      </div>
    </React.Fragment>
  )
}
