/**
 * 民主评议
 **/
import React from 'react'
import { connect } from "dva";
import { PlusOutlined } from '@ant-design/icons';
import ListTable from '@/components/ListTable'
import { Button, Collapse, Popconfirm, Space, Divider, message } from 'antd';

import AddAppraisal from './addAppraisal';
import MemSelect from '@/components/MemSelect';
import head from '@/assets/head.jpg';
import styles from './leader.less';

const Panel = Collapse.Panel;
export default class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      type: 'add',
      dataInfo: {}
    }
  }

  componentDidMount() {
    this.action();
    this.findYear()
  }
  action = () => {
    const { memBasic: { basicInfo: { code: memCode = '' } = {} } = {} } = this.props;
    if (memCode) {
      this.props.dispatch({
        type: 'memAppraisal/personList',
        payload: {
          memCode: memCode,
          pageNum: 1,
          pageSize: 100
        },
      });
    }
  };
  add = () => {
    if (this.state.years.length>0) {
      this['addAppraisal'].showModal()
    } else {
      message.error('请先在组织民主评议选择年份')
    }

  }
  del = async (e) => {
    // e.stopPropagation();
    const obj = await this.props.dispatch({
      type: 'memAppraisal/personRemove',
      payload: {
        code: e['code']
      }
    });
    if (obj && obj['code'] === 0) {
      this.action();
    }
  };
  edits = (e) => {
    this.setState({
      type: 'edit',
      dataInfo: { ...e }
    })
    this['addAppraisal'].showModal()
  }

  changePage = (val, key) => {

  }
  findYear = async () => {
    const { memBasic: { basicInfo = {} } = {} } = this.props;
    let obj = await this.props.dispatch({
      type: 'memAppraisal/year',
      payload: {
        memCode: basicInfo?.code
      }
    })
    if (obj?.code === 0)
      this.setState({
        years: obj?.data
      })

  }
  render() {
    const { type } = this.state;
    const { list = [], pagination = {} } = this.props.memAppraisal;
    const { pageSize = 0, totalRow = 0, page = 1, pageNumber = 1 } = pagination
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 50,
        render: (text, record, index) => {
          return ((pageNumber - 1) * pageSize) + index + 1
        }
      },
      {
        title: '评议年份',
        dataIndex: 'year',
        width: 150,
      },
      {
        title: '评议结果',
        dataIndex: 'result',
        width: 200,
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 80,
        render: (text, record) => {
          return (
            <React.Fragment>
              <a onClick={() => this.edits(record)}>修改</a>
              <Divider type="vertical" />
              <Popconfirm title="是否删除该?" onConfirm={() => this.del(record)} okText="是" cancelText="否">
                <a >删除</a>
              </Popconfirm>
            </React.Fragment>
          )
        },
      }
    ];
    return (
      <div style={{ padding: '0 20px' }}>
        {/*<AddOrEdit/>*/}
        <AddAppraisal
          queryList={this.action}
          title={type === 'edit' ? '编辑' : '新增'}
          wrappedComponentRef={(e) => this['addAppraisal'] = e}
          {...this.props}
          {...this.state}
        />

        <Button type="primary" icon={<PlusOutlined />} style={{ marginBottom: 10 }} onClick={this.add}>添加民主评议</Button>
        <ListTable
          columns={columns}
          data={list}

          pagination={pagination}
          onPageChange={this.changePage} />
      </div>
    );
  }
}
