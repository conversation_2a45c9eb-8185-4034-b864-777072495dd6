import React, { useEffect, useRef, useState } from 'react';
import request from '@/utils/request';
import { Space,Select } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import ListTable from '@/components/ListTable';
import Tree from '../components/tree';
import Cond from '../components/cond';
import { getDictList } from '@/services';
import tip from '@/components/Tip';
export default function(props) {
  const [tree,setTree]=useState([]);
  const [list,setList]:any=useState([]);
  const [select,setSelect]=useState([]);
  const [selKey,setSelKey]=useState();
  const [treeKey,setTreeKey]:any=useState([]);
  const condRef=useRef();
  useEffect(()=>{
    request('/api/table/tableSelect').then(res=>{
      if(res['code']=='0'){
        setTree(res['data']);
        if(res['data'].length>0){
          const item=res['data'][0];
          treeChange(item);
          setTreeKey([item['id']]);
        }
      }
    })
  },[]);
  const treeChange=(item)=>{
    setSelect([]);
    request(`/api/table/tableFind?id=${item['id']}`).then(res=>{
      if(res['code']=='0'){
        setSelect( res['data']);
        setList([]);
      }
    })
  }
  const getDict=(val)=>{
    if(val){
      let find = select.find(obj=>obj['id']==val);
      if(find){
        getDictList({
          data:{
            dicName:find['colLectionCode']
          }
        }).then(res=>{
          if(res['code']=='0'){
            setList(res['data'])
          }
        })
      }
    }else{
      setList([]);
    }
  }
  const selChange=(val)=>{
    let find = select.find(obj=>obj['id']==val);
    setSelKey(val);
    if(find){
      if(find['colType']=='lable'){
        request(`/api/table/tableColonfig?colId=${find['id']}&dictName=${find['colLectionCode']}`).then(res=>{
          if(res['code']=='0'){
            // setSelect( res['data']);
            setList(res['data']);
          }
        })
      }else{
        request(`/api/table/tableColonfig?colId=${find['id']}`).then(res=>{
          if(res['code']=='0'){
            // setSelect( res['data']);
            setList([res['data']]);
          }
        })
      }
    }
  };
  const rowClick=(record)=>{
    // @ts-ignore
    condRef.current && condRef.current.open({...record,colId:selKey});
  }
  const columns=[
    {
      title:'序号',
      dataIndex:'num',
      width: 80,
      align: 'center',
      render: (text, record, index) => {
        return index + 1;
      },
    },
    {
      title:'信息项',
      dataIndex:'colName',
      width: 160,
      render:(text,record)=>{
        return record['colName'] || record['col_name'] || record['name']
      }
    },
    {
      title:'条件',
      dataIndex:'data',
      render:(text)=>{
        if(text){
          // let data = JSON.parse(text);
          let msg:any=[];
          for(let obj of text){
            let temp = JSON.parse(obj);
            msg.push(temp['desc']);
          }
          return msg.map((msg,index)=>{
            return(
              <p key={index} style={{margin:'unset'}}>
                {index+1}.{msg}
              </p>
            )
          })
        }
        return ''
      }
    },
  ];
  return(
    <div style={{display:'flex',height:'100%'}}>
      <Cond ref={condRef} callback={()=>selChange(selKey)}/>
      <div style={{width:200,borderRight:'1px solid #f2f2f2',marginRight:12}}>
        <WhiteSpace/>
        <Tree selectedKeys={treeKey} data={tree} onChange={treeChange}/>
      </div>
      <div style={{width:'100%',padding:'12px 0'}}>
        <Space>
          <Space>
            字段：
            <Select value={selKey} style={{width:200}} onChange={(val,item)=>selChange(val)} allowClear placeholder={'请选择'}>
              {
                select.map(item=><Select.Option value={item['id']}>{item['colName']}</Select.Option>)
              }
            </Select>
          </Space>
        </Space>
        <WhiteSpace/>
        <ListTable
          columns={columns}
          data={list}
          scroll={{y:'78vh'}}
          onRow={record => {
            return {
              onClick: event => rowClick(record), // 点击行
              };
          }}
          pagination={false}
        />
      </div>
    </div>
  )
}
