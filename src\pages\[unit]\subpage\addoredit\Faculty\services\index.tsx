import request from "@/utils/request";
import qs from 'qs';

// 二级院系列表
export function getList(params) {
  return request(`/api/unit/secondary/getList`,{
    method:'POST',
    body:params,
  });
}

// 新增二级院系
export function addSecondary(params) {
  return request(`/api/unit/secondary/addSecondary`,{
    method:'POST',
    body:params,
  });
}

// 编辑二级院系
export function updateSecondary(params) {
  return request(`/api/unit/secondary/updateSecondary`,{
    method:'POST',
    body:params,
  });
}

// 删除二级院系
export function delSecondary(params) {
  return request(`/api/unit/secondary/delSecondary?${qs.stringify(params)}`,{
    method:'GET',
  });
}