import React, { Fragment, useImperativeHandle, useState, useRef, useEffect } from 'react';
import Date from '@/components/Date';
import { Form, Input, Modal } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import { updateUnitCommittee } from '@/pages/[unit]/services';
import Tip from '@/components/Tip';
import moment from 'moment';
import { PanelTable } from '@/components/CollapseTable';
import { electList, electDel, residentList, residentDel } from './services';
import { getList } from '../Faculty/services';
import _isEmpty from 'lodash/isEmpty'

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const formItemLayout2 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const index = (props: any, ref) => {
  const { title = '标题', onOK, isVillageCommunity = false } = props;
  const PanelTableRef: any = useRef();
  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleCancel = () => {
    setRecord({});
    setVisible(false);
    form.resetFields();
  };
  const onFinish = async (e) => {
    setConfirmLoading(true);
    const { code = 500 } = await updateUnitCommittee({
      data: {
        ...record,
        ...e,
        endDate: e.endDate ? moment(e.endDate).valueOf() : '',
      },
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOK && onOK(record);
    }
  };

  const getLists = (record: any) => {

  };

  useImperativeHandle(ref, () => ({
    open: (item, key) => {
      setRecord({...item, key});
      // getLists(query);
    },
    getList:()=>{
      PanelTableRef.current.getList({pageNum:1})
    },
    clear: () => {
      // clear();
    },
  }));

  useEffect(()=>{
    if(!visible && !_isEmpty(record)) {
      setVisible(true);
    }
  },[JSON.stringify(record)])
  return (
    <Modal
      title={'轮次内历史任职'}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={830}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      <PanelTable
        ref={PanelTableRef}
        data={record}
        mainsListAction={residentList}
        mainsActionOtherQueries={{ unitCode: props.unit.basicInfo.code, leave: 1, electCode: record['code'] }}
        add={() => {
          return (
            <React.Fragment>
            </React.Fragment>
          )
        }}
          // items 人员信息 , record 届次信息
        linkEdit={(items) => props.linkEdit && props.linkEdit(items, record)} />
    </Modal>
  );
};
export default React.forwardRef(index);
