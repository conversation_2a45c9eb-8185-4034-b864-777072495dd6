/*
* 活动管理 -- 基本信息
* */
import React,{Fragment} from 'react';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Divider, Popconfirm, Menu, Button, Input, Dropdown, Switch } from 'antd';
import RuiFilter from 'src/components/RuiFilter';
import ListTable from 'src/components/ListTable';
import NowOrg from 'src/components/NowOrg';
import ExportInfo from '@/components/Export';
import WhiteSpace from '@/components/WhiteSpace';
import Details from '../Details';
import AcModal from '../Modal';
import Tip from '@/components/Tip';
import _isNumber from 'lodash/isNumber';
import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';
import {withContext} from 'src/utils/global.jsx';
import {connect} from "dva";
import {_history as router} from "@/utils/method";
import {getSession} from "@/utils/session";
import moment from 'moment';
import qs from 'qs';
import QrCode from '../qrcode';
import {setListHeight} from "@/utils/method";
const {Search} = Input;
@withContext
@connect(({activityManage,commonDict,loading})=>({activityManage,commonDict,loading}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state={
      filterHeight:100,
      filterChecked:{}
    }
  }
  componentDidMount(): void {
    setListHeight(this);
  }

  // 筛选
  filterChange=(val)=>{
    this.setState({filterChecked:val});
    this.props.dispatch({
      type:'activityManage/updateState',
      payload:{
        filter:val
      }
    });
    this.action()
  };
  // 分页
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  action=(val?:object)=>{
    const {pagination={}}=this.props.activityManage;
    const {current = 1,pageSize = 10} = pagination;
    const org=getSession('org') || {};
    this.props.dispatch({
      type:'activityManage/getList',
      payload:{
        data:{
          org_org_code:org['orgCode'],
          pageNum:current,
          pageSize,
          ...val
        }
      }
    })
  };
  search=(value)=>{
    this.props.dispatch({
      type:'activityManage/updateState',
      payload:{
        memName:value
      }
    });
    this.action();
    // this.setState({
    //   search:{memName:value}
    // },()=>this.action());
  };
  // 人员编辑
  addOrEdit=async(record)=>{

  };

  // 人员删除
  del=async(record)=>{
    const {code} = record;
    if(!_isEmpty(code)){
      const res = await this.props.dispatch({
        type:'activityManage/del',
        payload:{
          data:{code}
        }
      });
      const {code:resCode} = res;
      if(resCode === 0){
        Tip.success('操作提示','删除成功');
        this.action()
      }
    }
  };

  addNew=({isSupplt,isEdit})=>{
    this['AcModal'] && this['AcModal'].open({isSupplt,isEdit});
  };
  edit= async ({isSupplt,isEdit},code)=>{
    const res = await this.props.dispatch({
      type:'activityManage/getDtail',
      payload:{
        type:2,
        code
      }
    });
    if(res === 0){
      this['AcModal'] && this['AcModal'].open({isSupplt,isEdit});
    }
  };
  // 查看当组织层级
  lookOrgs=(record)=>{

  };
  // 导出
  exportInfo= async ()=>{

  };
  openDtail= async (code)=>{
    const res = await this.props.dispatch({
      type:'activityManage/getDtail',
      payload:{
        type:2,
        code
      }
    });
    if(res === 0){
      this['AcDetails'] && this['AcDetails'].open();
    }
  };
  componentWillUnmount(): void {

  }
  checkCode=(record)=>{
    this['qrcode'].open(record);
  };
  render(): React.ReactNode {
    const {activityManage ={},loading:{effects = {}} ={},commonDict} = this.props;
    const {list, pagination,memName,filter} = activityManage;
    const {current = 1,pageSize = 10} = pagination;
    const {filterHeight}=this.state;

    const filterData = [
      {
        key:'activityType',name:'活动类别',value:commonDict['dict_activity_type_tree'],
      },
      {
        key:'status',name:'活动状态',value:[{key:1,name:'未开始'},{key:2,name:'进行中'},{key:3,name:'已结束'}],
      },
    ];
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:60,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'活动名称',
        dataIndex:'name',
        width:120,
        // width:100,
        // render:(text,record)=>{
        //   return(
        //     <a onClick={()=>this.addOrEdit(record)} >{text}</a>
        //   )
        // }
      },
      {
        title:'举行时间',
        dataIndex:'holdTime',
        width:90,
        render:(text)=>{
          return (
            <span> {_isNumber(text) ? moment(text).format('YYYY-MM-DD') : ''} </span>
          )
        }
      },
      {
        title:'结束时间',
        dataIndex:'endTime',
        width:90,
        render:(text)=>{
          return (
            <span> {_isNumber(text) ? moment(text).format('YYYY-MM-DD') : ''} </span>
          )
        }
      },
      {
        title:'地址',
        width:120,
        dataIndex:'address',
      },
      {
        title:'活动类型',
        width:220,
        dataIndex:'typeNames',
        render:(text)=>{
          let texts = '';
          if(_isArray(text)){
            texts = text.toString()
          }
          return(
            <div>{texts}</div>
          )
        }
      },
      {
        title:'所在组织',
        width:150,
        dataIndex:'orgShortName',
      },
      {
        title:'活动状态',
        width:90,
        dataIndex:'status',
        render:(text)=>{
          let text1 = '';
          switch (text) {
            case 1:
              text1 = '未开始';
              break;
            case 2:
              text1 = '进行中';
              break;
            case 3:
              text1 = '已结束';
              break;
          }
          return (
            <span>{text1}</span>
          )
        }
      },
      {
        title:'操作',
        width:120,
        dataIndex:'action',
        render:(text,record)=>{
          const {status,code,canReview} = record;
          let isSupplt = false;
          if(status === 3){
            isSupplt = true;
          }
          return(
            <span>
              {
                status !== 2 &&
                  <Fragment>
                    <a onClick={()=>this.edit({isSupplt,isEdit: true},code)}>编辑</a>
                    <Divider type="vertical"/>
                  </Fragment>
              }
              {
                canReview === '1' &&
                  <Fragment>
                    <a onClick={()=>this.checkCode(record)}>二维码</a>
                    <Divider type="vertical"/>
                  </Fragment>
              }
              <a onClick={()=>this.openDtail(code)}>详情</a>
              {
                status !== 2 &&
                  <Fragment>
                    <Divider type="vertical"/>
                    <Popconfirm title={'是否删除？'} onConfirm={()=>this.del(record)}>
                      <a className={'del'} >删除</a>
                    </Popconfirm>
                  </Fragment>
              }
            </span>
          )
        },
      },
    ];

    return (
      <Fragment>
        <NowOrg
          extra={
            <React.Fragment>
              <Button htmlType={'button'} icon={<LegacyIcon type={'plus'} />} onClick={()=>this.addNew({isSupplt:false,isEdit:false})}>预录入活动</Button>
              <Button htmlType={'button'} type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={()=>this.addNew({isSupplt:true,isEdit:false})} style={{marginLeft:16}}>补录活动</Button>
              <Search style={{width:200,marginLeft:16}} onSearch={this.search} placeholder={'请输入检索关键词'}/>
            </React.Fragment>
          }
        />
        <RuiFilter
          data={filterData}
          openCloseChange={()=>setListHeight(this,20)}
          onChange={this.filterChange}
        />
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:filterHeight}} columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
        <AcModal ref={e=> this['AcModal'] = e} {...this.props} onClose={this.action}/>
        <Details ref={e=>this['AcDetails'] = e} {...this.props}/>
        <QrCode ref={e=> this['qrcode'] = e}/>
        {/*<ExportInfo wrappedComponentRef={e=>this['memExportInfo'] = e}*/}
                    {/*tableName={'ccp_mem'}*/}
                    {/*tableListQuery={{memName,...filter,searchType:1,memOrgCode:org['orgCode']}}*/}
                    {/*action={'/api/data/mem/exportData'}*/}
        {/*/>*/}

      </Fragment>
    );
  }
}
