import React from 'react';
import {Modal} from 'antd';
import Last from '../last';
import Tip from '@/components/Tip';
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      modalVisible:false,
      info:{}
    }
  }
  open=(val)=>{
    this.setState({modalVisible:true,info:val})
  };
  destroy=()=>{
    this.setState({
      modalVisible:false,
      info:{}
    });
    this.props.dispatch({
      type: 'memMultiple/updateState',
      payload:{
        multipleInfo:{}
      }
    })
  };
  cancel=()=>{
    this.destroy();
  };
  submit= async (val)=> {
    const {onsubmit} = this.props;
    const res = await this.props.dispatch({
      type: 'memMultiple/save',
      payload: { data: { ...val }, type: 'edit' }
    });
    const { code = 500 } = res;
    if(code === 0 ){
      Tip.success('操作提示','操作成功');
      this.cancel();
      onsubmit && onsubmit();
    }
  };
  render(): React.ReactNode {
    const {loading:{effects = {}} ={}} = this.props;
    const {modalVisible,info} = this.state;
    return (
      <div>
        <Modal
          title={'编辑'}
          destroyOnClose
          visible={modalVisible}
          onCancel={this.cancel}
          width={'800px'}
          // confirmLoading={effects['memAbroad/save']}
          footer={null}
        >
          <Last memInfo={info} submit={this.submit} {...this.props}/>
        </Modal>
      </div>
    )
  }
}
