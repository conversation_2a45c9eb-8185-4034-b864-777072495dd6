import React, { Fragment } from 'react';
import { Checkbox, Col, Form, Input, Radio, Row, InputNumber, Select } from 'antd';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import Date from '@/components/Date';
import moment from 'moment';

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const formItemLayout2 = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const CheckboxGroup = Checkbox.Group;
const genderOptions = [
  { label: '男', value: '1' },
  { label: '女', value: '0' },
];

const { Search } = Input;
const index = (props) => {
  const { form } = props;
  const { query = {} } = props.location || {};
  const rangeTimeRender = ({ pre, fix, label }: any = {}) => {
    return (
      <Form.Item label={label} style={{ marginBottom: 0 }}>
        <Form.Item name={pre} style={{ display: 'inline-block', width: '44%' }} rules={[{ required: false, message: '请输入开始时间' }]}>
          <Date
            onChange={(e) => {
              form.setFieldsValue({
                [pre]: e ? moment(e).format('YYYY.MM.DD') : undefined,
              });
            }}
          />
        </Form.Item>
        <div
          style={{
            display: 'inline-block',
            width: '12%',
            height: '100%',
            marginTop: 6,
            textAlign: 'center',
          }}
        >
          至
        </div>
        <Form.Item name={fix} style={{ display: 'inline-block', width: '44%' }} rules={[{ required: false, message: '请输入结束时间' }]}>
          <Date
            onChange={(e) => {
              form.setFieldsValue({
                [fix]: e ? moment(e).format('YYYY.MM.DD') : undefined,
              });
            }}
          />
        </Form.Item>
      </Form.Item>
    );
  };

  const onFinish = async (value) => {
    let _val: any = { ...value };
    for (let item in _val) {
      if (_val[item] === 'all') {
        _val[item] = undefined;
      }
    }
    form.setFieldsValue(value);
    props.onOK && props.onOK(_val);
  };
  // 发展党员的查询
  let isDevelop = false;
  if (query.lockObject == '2') {
    isDevelop = true;
  }
  return (
    <div style={{ marginBottom: 10 }}>
      {query.lockObject < '3' && (
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Fragment>
            <div>基本信息</div>
            <hr />
            <Row>
              <Col span={8}>
                <Form.Item {...formItemLayout2} label={'年龄'} style={{ marginBottom: 0 }}>
                  <Form.Item name={'agePre'} style={{ display: 'inline-block' }}>
                    <InputNumber style={{ width: 50 }} min={0} max={200} />
                  </Form.Item>
                  <Form.Item style={{ display: 'inline-block' }}>
                    <div
                      style={{
                        width: 40,
                        textAlign: 'center',
                        borderLeft: 0,
                        pointerEvents: 'none',
                        background: 'white',
                      }}
                    >
                      到
                    </div>
                  </Form.Item>
                  <Form.Item name={'ageFix'} style={{ display: 'inline-block' }}>
                    <InputNumber style={{ width: 50 }} min={0} max={200} />
                  </Form.Item>
                  <Form.Item style={{ display: 'inline-block' }}>
                    <div
                      style={{
                        width: 40,
                        borderLeft: 0,
                        textAlign: 'center',
                        pointerEvents: 'none',
                        background: 'white',
                      }}
                    >
                      岁
                    </div>
                  </Form.Item>
                </Form.Item>
              </Col>
              <Col span={8}>{rangeTimeRender({ pre: 'birthdayPre', fix: 'birthdayFix', label: '出生年月' })}</Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'ageTime'} label={'年龄计算截至时间'} initialValue={moment().format('YYYY.MM')}>
                  <Input style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'sexCode'} label={'性别'}>
                  <CheckboxGroup options={genderOptions} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d06Code'} label={'民族'}>
                  <DictTreeSelect codeType={'dict_d06'} placeholder={'党员民族'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>

              {!isDevelop && (
                <Col span={8}>
                  <Form.Item {...formItemLayout2} name={'d08Code'} label={'人员类别'}>
                    <DictTreeSelect
                      codeType={'dict_d08'}
                      placeholder={'人员类别'}
                      treeCheckable={true}
                      filter={(data) => {
                        return data.filter((e) => e.key == '1' || e.key == '2');
                      }}
                      parentDisable={true}
                    />
                  </Form.Item>
                </Col>
              )}

              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d09Code'} label={'工作岗位'}>
                  <DictTreeSelect codeType={'dict_d09'} placeholder={'工作岗位'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d21Code'} label={'一线情况'}>
                  <DictTreeSelect codeType={'dict_d21'} placeholder={'一线情况'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d27Code'} label={'加入党组织方式'}>
                  <DictTreeSelect codeType={'dict_d27'} placeholder={'加入党组织方式'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d89Code'} label={'政治面貌'}>
                  <DictTreeSelect treeCheckable={true} parentDisable={true} codeType={'dict_d89'} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d11Code'} label={'进入支部类型'}>
                  <DictTreeSelect codeType={'dict_d11'} placeholder={'进入支部类型'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                {rangeTimeRender({
                  pre: 'applyDatePre',
                  fix: 'applyDateFix',
                  label: '申请入党时间',
                })}
              </Col>
              <Col span={8}>
                {rangeTimeRender({
                  pre: 'activeDatePre',
                  fix: 'activeDateFix',
                  label: '确定积极分子时间',
                })}
              </Col>
              <Col span={8}>
                {rangeTimeRender({
                  pre: 'objectDatePre',
                  fix: 'objectDateFix',
                  label: '确定发展对象时间',
                })}
              </Col>
              <Col span={8}>
                {rangeTimeRender({
                  pre: 'joinOrgDatePre',
                  fix: 'joinOrgDateFix',
                  label: '接收预备党员时间',
                })}
              </Col>

              {!isDevelop && (
                <Col span={8}>
                  {rangeTimeRender({
                    pre: 'fullMemberDatePre',
                    fix: 'fullMemberDateFix',
                    label: '成为正式党员时间',
                  })}
                </Col>
              )}

              <Col span={8}>
                <Form.Item {...formItemLayout2} label={'姓名'} name={'memName'} style={{ marginBottom: 0 }}>
                  <Input placeholder="可输入多个，用逗号隔开" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} label={'身份证'} name={'idCard'} style={{ marginBottom: 0 }}>
                  <Input placeholder="可输入多个，用逗号隔开" style={{ width: '100%' }} />
                </Form.Item>
              </Col>

              {!isDevelop && (
                <Col span={8}>
                  <Form.Item {...formItemLayout2} name={'d49Code'} label={'党费缴纳情况'}>
                    <DictTreeSelect codeType={'dict_d49'} placeholder={'党费缴纳情况'} treeCheckable={true} parentDisable={true} />
                  </Form.Item>
                </Col>
              )}
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d22Code'} label={'党内职务'}>
                  <DictTreeSelect codeType={'dict_d22'} placeholder={'党内职务'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d25Code'} label={'行政职务'}>
                  <DictTreeSelect codeType={'dict_d25'} placeholder={'行政职务'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d07Code'} label={'学历'}>
                  <DictTreeSelect codeType={'dict_d07'} placeholder={'学历'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
            </Row>
          </Fragment>
        </Form>
      )}
      {/* 流动党员信息 */}
      {query.lockObject === '3' && (
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <div>基本信息</div>
          <hr />
          <Row>
            <Col span={12}>
              <Form.Item name={'memName'} label={'姓名'}>
                <Input maxLength={10} placeholder="可输入多个，用逗号隔开" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={'lostContactCode'} label={'失去联系情形'}>
                <DictTreeSelect codeType={'dict_d18'} treeCheckable={true} parentDisable={true} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={'memSexCode'} label={'性别'}>
                <CheckboxGroup options={genderOptions} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={'inOrgLifeCode'} label={'组织生活情况'}>
                <DictSelect codeType={'dict_d152'} mode="multiple" />
              </Form.Item>
            </Col>
            <Col span={12}>
              {rangeTimeRender({
                pre: 'outTimeStart',
                fix: 'outTimeEnd',
                label: '流出日期',
              })}
            </Col>
            <Col span={12}>
              <Form.Item name={'memD09Code'} label={'人员工作岗位'}>
                <DictTreeSelect codeType={'dict_d09'} treeCheckable={true} parentDisable={true} />
              </Form.Item>
            </Col>
            <Col span={12}>
              {rangeTimeRender({
                pre: 'flowBackTimeStart',
                fix: 'flowBackTimeEnd',
                label: '流回日期',
              })}
            </Col>
            <Col span={12}>
              <Form.Item name={'outOrgD04Code'} label={'流出地党组织单位类别'}>
                <DictTreeSelect codeType={'dict_d04'} treeCheckable={true} parentDisable={true} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={'flowReasonCode'} label={'流动原因'}>
                <DictSelect codeType={'dict_d146'} mode="multiple" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={'inOrgD04Code'} label={'流入地党组织单位类别'}>
                <DictTreeSelect codeType={'dict_d04'} treeCheckable={true} parentDisable={true} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={'flowTypeCode'} label={'流动类型'}>
                <DictTreeSelect codeType={'dict_d34'} treeCheckable={true} parentDisable={true} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={'flowStep'} label={'管理情况'}>
                <Select placeholder="请选择">
                  <Select.Option value="all">全选</Select.Option>
                  <Select.Option value="2">已纳入支部</Select.Option>
                  <Select.Option value="1">未纳入支部</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item initialValue={'1'} name={'flow'} label={'查询类别'}>
                <Select placeholder="请选择">
                  <Select.Option value="1">流出查询</Select.Option>
                  <Select.Option value="2">流入查询</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      )}
      {query.lockObject > '3' && (
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Fragment>
            <div>基本信息</div>
            <hr />
            <Row>
              <Col span={8}>
                <Form.Item {...formItemLayout2} label={'年龄'} style={{ marginBottom: 0 }}>
                  <Form.Item name={'ageStart'} style={{ display: 'inline-block' }}>
                    <InputNumber style={{ width: 50 }} min={0} max={200} />
                  </Form.Item>
                  <Form.Item style={{ display: 'inline-block' }}>
                    <div
                      style={{
                        width: 40,
                        textAlign: 'center',
                        borderLeft: 0,
                        pointerEvents: 'none',
                        background: 'white',
                      }}
                    >
                      到
                    </div>
                  </Form.Item>
                  <Form.Item name={'ageEnd'} style={{ display: 'inline-block' }}>
                    <InputNumber style={{ width: 50 }} min={0} max={200} />
                  </Form.Item>
                  <Form.Item style={{ display: 'inline-block' }}>
                    <div
                      style={{
                        width: 40,
                        borderLeft: 0,
                        textAlign: 'center',
                        pointerEvents: 'none',
                        background: 'white',
                      }}
                    >
                      岁
                    </div>
                  </Form.Item>
                </Form.Item>
              </Col>
              <Col span={8}>{rangeTimeRender({ pre: 'birthdayBegin', fix: 'birthdayEnd', label: '出生年月' })}</Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'ageTime'} label={'年龄计算截至时间'} initialValue={moment().format('YYYY.MM')}>
                  <Input style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'sexCode'} label={'性别'}>
                  <CheckboxGroup options={genderOptions} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d06Code'} label={'民族'}>
                  <DictTreeSelect codeType={'dict_d06'} placeholder={'党员民族'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d07Code'} label={'学历'}>
                  <DictTreeSelect codeType={'dict_d07'} placeholder={'学历'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d09Code'} label={'工作岗位'}>
                  <DictTreeSelect codeType={'dict_d09'} placeholder={'工作岗位'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d21Code'} label={'一线情况'}>
                  <DictTreeSelect codeType={'dict_d21'} placeholder={'一线情况'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d08Code'} label={'发展党员类型'}>
                  <DictTreeSelect
                    codeType={'dict_d08'}
                    placeholder={'发展党员类型'}
                    treeCheckable={true}
                    parentDisable={true}
                    filter={(data) => {
                      if (query.lockObject == 6) {
                        return data.filter((it) => it.key == '3');
                      }
                      if (query.lockObject == 5) {
                        return data.filter((it) => ['3', '4'].includes(it.key));
                      }
                      if (query.lockObject == 4) {
                        return data.filter((it) => ['3', '4', '5'].includes(it.key));
                      }
                      return data;
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d19Code'} label={'专业技术职务'}>
                  <DictTreeSelect codeType={'dict_d19'} placeholder={'专业技术职务'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item {...formItemLayout2} name={'d20Code'} label={'新社会阶层'}>
                  <DictTreeSelect codeType={'dict_d20'} placeholder={'新社会阶层'} treeCheckable={true} parentDisable={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                {rangeTimeRender({
                  pre: 'applyDateStart',
                  fix: 'applyDateEnd',
                  label: '申请入党时间',
                })}
              </Col>
              <Col span={8}>
                {rangeTimeRender({
                  pre: 'activeDateStart',
                  fix: 'activeDateEnd',
                  label: '确定积极分子时间',
                })}
              </Col>
              <Col span={8}>
                {rangeTimeRender({
                  pre: 'objectDateStart',
                  fix: 'objectDateEnd',
                  label: '确定发展对象时间',
                })}
              </Col>

              <Col span={8}>
                <Form.Item {...formItemLayout2} label={'姓名'} name={'name'} style={{ marginBottom: 0 }}>
                  <Input placeholder="可输入多个，用逗号隔开" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} label={'身份证'} name={'idcard'} style={{ marginBottom: 0 }}>
                  <Input placeholder="可输入多个，用逗号隔开" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item {...formItemLayout2} label={'电话'} name={'phone'} style={{ marginBottom: 0 }}>
                  <Input placeholder="可输入多个，用逗号隔开" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </Fragment>
        </Form>
      )}
    </div>
  );
};
export default index;
