/**
 * 这是一个备份文件
 */
import React, { Fragment } from 'react';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, Input, Button, Switch, Row, DatePicker, InputNumber, Alert, Select } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import WhiteSpace from '@/components/WhiteSpace';
import DictArea from '@/components/DictArea';
import moment from 'moment';
import LinkedOrg from './linkedOrg';
import { getSession } from '@/utils/session';
import { connect } from 'dva';
import { findDictCodeName, formLabel, getCredit } from '@/utils/method';
import _isNumber from 'lodash/isNumber';
import style from './basic.less';
import { validateLength } from '@/utils/formValidator';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
};
const formItemLayout3 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const formItemLayout4 = {
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
};

// @ts-ignore
@connect(({ loading }) => ({
  unitAdd: loading.effects['unit/add'],
  unitUpdate: loading.effects['unit/update'],
}))
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      isCreateOrg: 0,
    };
  }
  handleSubmit = () => {
    const { basicInfo = {} } = this.props.unit;
    const org = getSession('org') || {};
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        let obj = undefined;
        // ['d04Code', 'd05Code', 'd35Code', 'd48Code','d95Code'].map((obj) => {
        //   let key = obj.split('C')[0];
        //   if (typeof val[obj] === 'object') {
        //     val[`${key}Name`] = val[obj]['name'];
        //     val[obj] = val[obj]['key'];
        //   }
        // });

        val = findDictCodeName(
          ['d04', 'd05', 'd35', 'd95', 'd112', 'd109', 'd81', 'd95', 'd110', 'd111'],
          val,
          basicInfo,
        );
        // if(val['d04Code'] && val['d04Code']['id']){
        //   val['d04Name']=val['d04Code']['name'];
        //   val['d04Code']=val['d04Code']['key'];
        // }
        // if(val['d05Code'] && val['d05Code']['id']){
        //   val['d05Name']=val['d05Code']['name'];
        //   val['d05Code']=val['d05Code']['key'];
        // }
        // if(val['d35Code'] && val['d35Code']['id']){
        //   val['d35Name']=val['d35Code']['name'];
        //   val['d35Code']=val['d35Code']['key'];
        // }
        // if(val['d48Code'] && val['d48Code']['id']){
        //   val['d48Name']=val['d48Code']['name'];
        //   val['d48Code']=val['d48Code']['key'];
        // }
        Object.keys(val).map((obj) => {
          // if (typeof val[obj] == 'boolean') {
          //   if (val[obj]) {
          //     val[obj] = 1;
          //   } else {
          //     val[obj] = 0;
          //   }
          // }
          if (typeof val[obj] == 'boolean') {
            if (val[obj] == 1) {
              val[obj] = 1;
            } else if (val[obj] == 0) {
              val[obj] = 0;
            }
          }
        });
        if (
          !val['isCreateOrg'] &&
          val['manageUnitOrgCode'] &&
          typeof val['manageUnitOrgCode'] === 'object'
        ) {
          val['manageOrgCode'] = val['manageUnitOrgCode'][0]['code'];
          val['manageOrgName'] = val['manageUnitOrgCode'][0]['name'];
          val['manageUnitOrgCode'] = val['manageUnitOrgCode'][0]['orgCode'];
        }
        val['createUnitOrgCode'] = org['orgCode'];
        val['createOrgZbCode'] = org['zbCode'];
        if (val['linkedDTOList']) {
          let data: Array<object> = [];
          for (let obj of val['linkedDTOList']) {
            const { org = {} } = obj;
            if (org['code']) {
              //新增的关联组织
              data.push({
                orgCode: org['code'],
                orgName: org['name'],
                orgType: org['d01Code'],
                orgTypeName: org['d01Name'],
                linkedOrgCode: org['orgCode'],
                orgTypeCode: org['orgType'],
                isOrgMain: obj['isOrgMain'],
              });
            } else {
              //已关联的组织
              data.push(obj);
            }
          }
          val['linkedDTOList'] = data;
        }
        if (val['onPostNum'] < val['b30A12']) {
          Tip.error('提示', '党政机关工作人数必须小于在岗职工数');
          return;
        }
        if (val['zaigangGaoji'] > val['tecNum']) {
          Tip.error('提示', '专业技术人员中含高级职称必须小于在岗专业技术人员数');
          return;
        }
        if (val['communityWorkersSalary'] > 200) {
          Tip.error('操作提示', '全部社区工作者年工资总额（万元）不能大于200');
          return;
        }
        if (val['communitySecretarySalary'] > 200) {
          Tip.error('操作提示', '社区党组织书记年工资总额（万元）不能大于200');
          return;
        }
        if (basicInfo['code']) {
          obj = await this.props.dispatch({
            type: 'unit/update',
            payload: {
              data: {
                ...basicInfo,
                ...val,
              },
            },
          });
        } else {
          obj = await this.props.dispatch({
            type: 'unit/add',
            payload: {
              data: {
                ...val,
              },
            },
          });
        }
        if (obj && obj['code'] === 0) {
          Tip.success('操作提示', basicInfo['code'] ? '修改成功' : '新增成功');
          if (!basicInfo['code']) {
            this.props.close();
          } else {
            this.props.dispatch({
              type: 'unit/findOrg',
              payload: {
                code: basicInfo['code'],
              },
            });
          }
        }
      }
    });
  };
  linkedChange = (val) => {
    this.props.form.setFieldsValue({
      linkedDTOList: val,
    });
  };
  renderItem = (item, formItemLayout = {}) => {
    const { tipMsg = {} } = this.props;
    const { basicInfo = {} } = this.props.unit;
    const { getFieldDecorator } = this.props.form;
    const {
      label,
      code,
      type,
      codeType,
      rules,
      boolText = {},
      backType = undefined,
      filter,
    } = item;
    let node = <Input />;
    switch (`${type}`) {
      case 'boolean':
        // node = <Switch defaultChecked={basicInfo[code] == 1} />;
        node = (
          <Select style={{ width: '100%' }}>
            <Select.Option value={1}>{boolText?.yes || '是'}</Select.Option>
            <Select.Option value={0}>{boolText?.no || '否'}</Select.Option>
          </Select>
        );
        break;
      case 'boolean2':
        // node = <Switch defaultChecked={basicInfo[code] == 1} />;
        node = (
          <Select style={{ width: '100%' }}>
            <Select.Option value={1}>是</Select.Option>
            <Select.Option value={0}>否</Select.Option>
            <Select.Option value={2}>未配备</Select.Option>
          </Select>
        );
        break;
      case 'dict':
        node = (
          <DictTreeSelect
            codeType={codeType}
            initValue={basicInfo[code]}
            backType={backType}
            parentDisable={true}
            filter={filter}
          />
        );
        break;
      case 'number':
        node = <InputNumber style={{ width: '100%' }} min={0} />;
        break;
    }
    return (
      <Col span={12}>
        <Row className={style.items}>
          <Col
            className={`${
              rules.find((it) => it.required) ? style.label : style.label2
            } ant-col ant-col-xs-24 ant-col-sm-10`}
          >
            {formLabel(label, tipMsg[code])}
          </Col>
          <Col span={12} className={style.desc}>
            <FormItem
              key={code}
              {...formItemLayout4}
              // label={formLabel(label, tipMsg[code])}
            >
              {getFieldDecorator(code, {
                initialValue: `${type}` == 'boolean' ? basicInfo[code] : basicInfo[code],
                rules,
              })(node)}
            </FormItem>
          </Col>
        </Row>
        {/*<FormItem key={code} {...formItemLayout} label={formLabel(label, tipMsg[code])}>*/}
        {/*  {getFieldDecorator(code, {*/}
        {/*    initialValue: `${type}` == 'boolean' ? basicInfo[code]: basicInfo[code],*/}
        {/*    rules,*/}
        {/*  })(node)}*/}
        {/*</FormItem>*/}
      </Col>
    );
  };
  renderExtra = () => {
    const { getFieldValue } = this.props.form;
    let obj = getFieldValue('d04Code');
    let val = obj;
    if (typeof obj == 'object') {
      val = obj['key'];
    }
    let res: any = [];
    if (`${val}`.startsWith('1') || `${val}`.startsWith('2')) {
      let exData = [
        {
          label: '是否政府工作部门',
          code: 'isDfzgbm',
          type: 'boolean',
          rules: [{ required: false, message: '请输入是否政府工作部门' }],
        },
        {
          label: '政府工作部门建立党组（党委）情况',
          code: 'd80Code',
          type: 'dict',
          codeType: 'dict_d80',
          rules: [{ required: true, message: '请输入政府工作部门建立党组（党委）情况' }],
        },
        {
          label: '在岗职工数（人）',
          code: 'onPostNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗职工数（人）' }],
        },
        {
          label: '党政机关工作人员',
          code: 'b30A12',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入党政机关工作人员' }],
        },
        {
          label: '在职党员数（人）',
          code: 'memberNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在职党员数（人）' }],
        },
        {
          label: '35岁以下在岗职工数（人）',
          code: 'underThirtyFiveNum',
          type: 'number',
          codeType: '',
          rules: [{ required: false, message: '请输入35岁以下在岗职工数（人）' }],
        },
        {
          label: '单位负责人',
          code: 'principal',
          type: 'text',
          codeType: '',
          rules: [{ required: false, message: '请输入单位负责人' }],
        },
        {
          label: '单位负责人是否是党员',
          code: 'isUnitMem',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '输入单位负责人是否是党员' }],
        },
        {
          label: '是否有“两代表一委员”',
          code: 'isCommitteeRepresent',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '输入是否有“两代表一委员”' }],
        },
        {
          label: '为上级党建工作联系点',
          code: 'isOrgContact',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请输入是否为上级党建工作联系点' }],
        },
        {
          label: '建立党员志愿者队伍',
          code: 'isVolTeam',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请输入是否建立党员志愿者队伍' }],
        },
      ];
      exData.forEach((item, index) => {
        let node = this.renderItem(item);
        if (index == 0) {
          res.push(
            <Col span={24}>
              <div style={{ padding: '0 4%' }}>
                <Alert message="提示：以下信息项为单位党统扩展数据信息集。" type="info" showIcon />
                <WhiteSpace />
              </div>
            </Col>,
          );
        }
        res.push(node);
      });
    }

    if (`${val}`.startsWith('3')) {
      let data = [
        // {label:'是否建立党组',code:'1',type:'dict',codeType:'dict_d09',rules:[{ required: true, message: '请输入单位名称' }]},
        {
          label: '在岗职工数（人）',
          code: 'onPostNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗职工数（人）' }],
        },
        {
          label: '在职党员数（人）',
          code: 'memberNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在职党员数（人）' }],
        },
        {
          label: '在岗专业技术人员数（人）',
          code: 'tecNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
        },
        {
          label: '在岗专业技术人员（高级职称）（人）',
          code: 'zaigangGaoji',
          type: 'number',
          codeType: '',
          rules: [{ required: false, message: '请输入在岗专业技术人员（高级职称）（人）' }],
        },
        {
          label: '35岁以下在岗职工数（人）',
          code: 'underThirtyFiveNum',
          type: 'number',
          codeType: '',
          rules: [{ required: false, message: '请输入35岁以下在岗职工数（人）' }],
        },
        {
          label: '为上级党建工作联系点',
          code: 'isOrgContact',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '建立党员志愿者队伍',
          code: 'isVolTeam',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '是否有“两代表一委员”',
          code: 'isCommitteeRepresent',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '单位负责人是否是党员',
          code: 'isUnitMem',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '单位负责人',
          code: 'principal',
          type: 'text',
          codeType: '',
          rules: [{ required: false, message: '请输入单位负责人' }],
        },
      ];
      if (`${val}`.startsWith('32') || `${val}`.startsWith('34')) {
        data = data.concat([
          {
            label: '是否实行党组织领导的院（所）长负责制',
            code: 'orgLeaderCharge',
            type: 'boolean',
            codeType: '',
            rules: [{ required: false, message: '请选择' }],
          },
          {
            label: '公益分类',
            code: '1',
            type: 'dict',
            codeType: 'dict_d09',
            rules: [{ required: false, message: '请选择' }],
          },
        ]);
      }
      data.forEach((item, index) => {
        let node = this.renderItem(item);
        if (index == 0) {
          res.push(
            <Col span={24}>
              <div style={{ padding: '0 4%' }}>
                <Alert message="提示：以下信息项为单位党统扩展数据信息集。" type="info" showIcon />
                <WhiteSpace />
              </div>
            </Col>,
          );
        }
        res.push(node);
      });
    }

    if (`${val}`.startsWith('4')) {
      let exData = [
        {
          label: '企业规模',
          code: 'd17Code',
          type: 'dict',
          codeType: 'dict_d17',
          rules: [{ required: true, message: '请输入企业规模' }],
        },
        {
          label: '经济类型',
          code: 'd16Code',
          type: 'dict',
          codeType: 'dict_d16',
          rules: [{ required: true, message: '请输入经济类型' }],
        },
        {
          label: '企业隶属关系',
          code: 'd79Code',
          type: 'dict',
          codeType: 'dict_d79',
          rules: [{ required: true, message: '请输入企业隶属关系' }],
        },
        {
          label: '在岗职工数（人）',
          code: 'onPostNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗职工数（人）' }],
        },
        `${val}`.startsWith('411') || `${val}`.startsWith('412')
          ? {
              label: '在职党员数（人）',
              code: 'memberNum',
              type: 'number',
              codeType: '',
              rules: [{ required: true, message: '请输入在职党员数（人）' }],
            }
          : {},
        {
          label: '在岗专业技术人员数（人）',
          code: 'tecNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
        },
        {
          label: '在岗职工工人（工勤技能）数',
          code: 'workerNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗职工工人（工勤技能）数' }],
        },
        {
          label: '35岁以下在岗职工数（人）',
          code: 'underThirtyFiveNum',
          type: 'number',
          codeType: '',
          rules: [{ required: false, message: '请输入35岁以下在岗职工数（人）' }],
        },
        {
          label: '是否有“两代表一委员”',
          code: 'isCommitteeRepresent',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '是否建有工会',
          code: 'isWorkUnion',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '工会负责人是否是党员',
          code: 'isWorkUnionMember',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '是否建有共青团',
          code: 'isTeenager',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '共青团负责人是否是党员',
          code: 'isTeenagerMember',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '是否建有妇联',
          code: 'isWomenFederation',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '妇联负责人是否是党员',
          code: 'isWomenFederationMember',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '单位负责人',
          code: 'principal',
          type: 'text',
          codeType: '',
          rules: [{ required: false, message: '请输入单位负责人' }],
        },
      ];
      exData.forEach((item, index) => {
        if (item['label']) {
          let node = this.renderItem(item);
          if (index == 0) {
            res.push(
              <Col span={24}>
                <div style={{ padding: '0 4%' }}>
                  <Alert
                    message="提示：以下信息项为单位党统扩展数据信息集。"
                    type="info"
                    showIcon
                  />
                  <WhiteSpace />
                </div>
              </Col>,
            );
          }
          res.push(node);
        }
      });
    }
    if (`${val}`.startsWith('5')) {
      let exData = [
        {
          label: '在岗职工数（人）',
          code: 'onPostNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗职工数（人）' }],
        },
        {
          label: '在岗专业技术人员数（人）',
          code: 'tecNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
        },
        {
          label: '35岁以下在岗职工数（人）',
          code: 'underThirtyFiveNum',
          type: 'number',
          codeType: '',
          rules: [{ required: false, message: '请输入35岁以下在岗职工数（人）' }],
        },
        {
          label: '专职工作人员数（人）',
          code: 'zzgzry',
          type: 'number',
          codeType: '',
          rules: [{ required: false, message: '请输入专职工作人员数（人）' }],
        },
        {
          label: '单位负责人',
          code: 'principal',
          type: 'text',
          codeType: '',
          rules: [{ required: false, message: '请输入单位负责人' }],
        },
        {
          label: '单位负责人是否是党员',
          code: 'isUnitMem',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '为上级党建工作联系点',
          code: 'isOrgContact',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '建立党员服务机构',
          code: 'isOrgService',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '建立党员志愿者队伍',
          code: 'isVolTeam',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '是否有“两代表一委员”',
          code: 'isCommitteeRepresent',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '是否选派党建工作指导员或联络员',
          code: 'isOrgInstructor',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
        {
          label: '是否行业协会商会',
          code: 'isDecouplIndustry',
          type: 'boolean',
          codeType: '',
          rules: [{ required: false, message: '请选择' }],
        },
      ];
      exData.forEach((item, index) => {
        if (item['label']) {
          let node = this.renderItem(item);
          if (index == 0) {
            res.push(
              <Col span={24}>
                <div style={{ padding: '0 4%' }}>
                  <Alert
                    message="提示：以下信息项为单位党统扩展数据信息集。"
                    type="info"
                    showIcon
                  />
                  <WhiteSpace />
                </div>
              </Col>,
            );
          }
          res.push(node);
        }
      });
    }
    if (`${val}`.startsWith('9')) {
      let exData: any = [];
      if (`${val}`.startsWith('91') || `${val}`.startsWith('92')) {
        exData = exData.concat([
          {
            label: '建立党员服务机构',
            code: 'isOrgService',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '请选择' }],
          },
          {
            label: '建立党员志愿者队伍',
            code: 'isVolTeam',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '请选择' }],
          },
          {
            label: '为上级党建工作联系点',
            code: 'isOrgContact',
            type: 'boolean',
            codeType: '',
            rules: [{ required: false, message: '请选择' }],
          },
          `${val}`.startsWith('911')
            ? {
                label: '是否建立工委',
                code: 'isWorkeCommittee',
                type: 'boolean',
                codeType: '',
                rules: [{ required: false, message: '请选择' }],
              }
            : {},
        ]);
      }
      if (`${val}`.startsWith('92')) {
        exData = exData.concat([
          {
            label: '户数（户）',
            code: 'houseNum',
            type: 'number',
            codeType: '',
            rules: [{ required: false, message: '请输入户数（户）' }],
          },
          {
            label: '户籍人口（人）',
            code: 'housePersonNum',
            type: 'number',
            codeType: '',
            rules: [{ required: false, message: '请输入户籍人口（人）' }],
          },
          {
            label: '常住人口（人）',
            code: 'permanentPopulation',
            type: 'number',
            codeType: '',
            rules: [{ required: false, message: '请输入常住人口（人）' }],
          },
          {
            label: '上年度当地农村居民人均可支配收入（元）',
            code: 'lastYearIncome',
            type: 'number',
            codeType: '',
            rules: [{ required: false, message: '请输入上年度当地农村居民人均可支配收入（元）' }],
          },
          {
            label: '第一书记姓名',
            code: 'firstSecName',
            type: 'text',
            codeType: '',
            rules: [
              { required: false, message: '请输入第一书记姓名' },
              { validator: (...e) => validateLength(e, 16, 50) },
            ],
          },
          {
            label: '第一书记公民身份证',
            code: 'firstSecId',
            type: 'text',
            codeType: '',
            rules: [{ required: false, message: '请输入第一书记公民身份证' }],
          },
          {
            label: '第一书记任职开始日期',
            code: 'startDate',
            type: 'date',
            codeType: '',
            rules: [{ required: false, message: '请输入第一书记任职开始日期' }],
          },
          {
            label: '第一书记任期（年）',
            code: 'totalYear',
            type: 'text',
            codeType: '',
            rules: [{ required: false, message: '请输入第一书记任期（年）' }],
          },
        ]);
      }
      if (`${val}`.startsWith('93') || `${val}`.startsWith('94') || `${val}`.startsWith('95')) {
        exData = exData.concat([
          {
            label: '在岗职工数（人）',
            code: 'onPostNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '请输入在岗职工数（人）' }],
          },
          {
            label: '35岁以下在岗职工数（人）',
            code: 'underThirtyFiveNum',
            type: 'number',
            codeType: '',
            rules: [{ required: false, message: '请输入35岁以下在岗职工数（人）' }],
          },
          {
            label: '在岗专业技术人员数（人）',
            code: 'tecNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
          },
        ]);
      }
      exData.forEach((item, index) => {
        if (item['label']) {
          let node = this.renderItem(item);
          if (index == 0) {
            res.push(
              <Col span={24}>
                <div style={{ padding: '0 4%' }}>
                  <Alert
                    message="提示：以下信息项为单位党统扩展数据信息集。"
                    type="info"
                    showIcon
                  />
                  <WhiteSpace />
                </div>
              </Col>,
            );
          }
          res.push(node);
        }
      });
    }
    //公立医院
    if (`${val}`.startsWith('341')) {
      let data = [
        {
          label: '领导体制已写入医院章程的',
          code: 'ldtzyyzc',
          type: 'boolean',
          rules: [{ required: true, message: '请选择' }],
        },
        {
          label: '是否党建工作要求写入医院章程',
          code: 'isPartyWorkWrite',
          type: 'boolean',
          rules: [{ required: true, message: '请选择' }],
        },
        {
          label: '是否开展基层党建述职评议考核',
          code: 'isOpenOrgAssess',
          type: 'boolean',
          rules: [{ required: true, message: '请选择' }],
        },
        {
          // label: '是否党委书记、院长分设',
          label: '是否党组织书记、院长分设',
          code: 'isLeaderSeparate',
          type: 'boolean',
          rules: [{ required: true, message: '请选择' }],
        },
        {
          label: '是否院长系中共党员',
          code: 'leaderIsGcdy',
          type: 'boolean',
          rules: [{ required: true, message: '请选择' }],
        },
        {
          label: '是否院长担任党委副书记',
          code: 'isLeaderDeputySecretary',
          type: 'boolean',
          rules: [{ required: true, message: '请选择' }],
        },
        {
          label: '医院内设机构党支部（个）',
          code: 'isSetOrgParty',
          type: 'number',
          rules: [{ required: true, message: '请输入医院内设机构党支部（个）' }],
        },
        {
          label: '党支部书记是内设机构负责人（人）',
          code: 'secretaryIsInsideLeader',
          type: 'number',
          rules: [{ required: true, message: '请输入党支部书记是内设机构负责人（人）' }],
        },
        {
          label: '党支部书记是“双带头人”的（人）',
          code: 'sjsdtr',
          type: 'number',
          rules: [{ required: true, message: '请输入党支部书记是“双带头人”的（人）' }],
        },
        {
          label: '本年度党支部书记参加培训人次（人）',
          code: 'yearTrainOrgMem',
          type: 'number',
          rules: [{ required: true, message: '请输入本年度党支部书记参加培训人次（人）' }],
        },
        {
          label: '本年度任届期满的内设机构党支部（个）',
          code: 'isYearExpirationOrg',
          type: 'number',
          rules: [{ required: true, message: '请输入本年度任届期满的内设机构党支部（个）' }],
        },
        {
          label: '本年换届（个）',
          code: 'isYearOrgChange',
          type: 'number',
          rules: [{ required: true, message: '请输入本年换届（个）' }],
        },
        {
          label: '本年度发展党员（人）',
          code: 'yearDevelopMem',
          type: 'number',
          rules: [{ required: true, message: '请输入本年度发展党员（人）' }],
        },
        {
          label: '本年度发展卫生技术人员党员（人）',
          code: 'yearDevelopMemMedicine',
          type: 'number',
          rules: [{ required: true, message: '请输入本年度发展卫生技术人员党员（人）' }],
        },
        {
          label: '本年度列为入党积极分子（人）',
          code: 'rdjjfz',
          type: 'number',
          rules: [{ required: true, message: '请输入本年度列为入党积极分子（人）' }],
        },
      ];
      data.forEach((item, index) => {
        let node = this.renderItem(item);
        if (index == 0) {
          res.push(
            <Col span={24}>
              <div style={{ padding: '0 4%' }}>
                <Alert message="提示：公立医院需维护以下信息项维护。" type="info" showIcon />
                <WhiteSpace />
              </div>
            </Col>,
          );
        }
        res.push(node);
      });
    }
    //党统数据流（手填项）信息集
    if (obj) {
      let data = [
        {
          label: '吸收未转入组织关系的党员',
          code: 'b301',
          type: 'boolean',
          rules: [{ required: true, message: '请选择' }],
        },
      ];
      if (`${val}`.startsWith('331')) {
        data = data.concat([
          {
            label: '在校研究生数',
            code: 'b71',
            type: 'number',
            rules: [{ required: true, message: '请输入在校研究生数' }],
          },
          {
            label: '在校本科生数',
            code: 'b72',
            type: 'number',
            rules: [{ required: true, message: '请输入在校本科生数' }],
          },
          {
            label: '在校专科生数',
            code: 'b78',
            type: 'number',
            rules: [{ required: true, message: '请输入在校专科生数' }],
          },
          {
            label: '高等学校教师数',
            code: 'b75',
            type: 'number',
            rules: [{ required: true, message: '请输入高等学校教师数' }],
          },
          {
            label: '高等学校女教师数',
            code: 'b76',
            type: 'number',
            rules: [{ required: true, message: '请输入高等学校女教师数' }],
          },
          {
            label: '高等学校35岁及以下教师',
            code: 'b77',
            type: 'number',
            rules: [{ required: true, message: '请输入高等学校35岁及以下教师' }],
          },
        ]);
      }
      if (`${val}`.startsWith('332')) {
        data = data.concat([
          {
            label: '在校中专生数',
            code: 'b73',
            type: 'number',
            rules: [{ required: true, message: '请输入在校中专生数' }],
          },
          {
            label: '在校高中、中技生数',
            code: 'b74',
            type: 'number',
            rules: [{ required: true, message: '请输入在校高中、中技生数' }],
          },
        ]);
      }
      if (`${val}`.startsWith('911')) {
        data = data.concat([
          {
            label: '街道干部数',
            code: 'b61',
            type: 'number',
            rules: [{ required: true, message: '请输入街道干部数' }],
          },
          {
            label: '街道干部35岁及以下',
            code: 'b62',
            type: 'number',
            rules: [{ required: true, message: '请输入街道干部35岁及以下' }],
          },
          {
            label: '街道干部36至54岁',
            code: 'b63',
            type: 'number',
            rules: [{ required: true, message: '请输入街道干部36至54岁' }],
          },
          {
            label: '街道干部55岁及以上',
            code: 'b64',
            type: 'number',
            rules: [{ required: true, message: '请输入街道干部55岁及以上' }],
          },
          {
            label: '街道干部大专及以上',
            code: 'b65',
            type: 'number',
            rules: [{ required: true, message: '请输入街道干部大专及以上' }],
          },
          {
            label: '街道干部高中中专及以下',
            code: 'b66',
            type: 'number',
            rules: [{ required: true, message: '请输入街道干部高中中专及以下' }],
          },
          {
            label: '街道干部公务员人数',
            code: 'b67',
            type: 'number',
            rules: [{ required: true, message: '请输入街道干部公务员人数' }],
          },
          {
            label: '街道干部事业人员',
            code: 'b68',
            type: 'number',
            rules: [{ required: true, message: '请输入街道干部事业人员' }],
          },
          {
            label: '街道干部其他身份',
            code: 'b69',
            type: 'number',
            rules: [{ required: true, message: '请输入街道干部其他身份' }],
          },
          {
            label: '是否实行兼职委员制',
            code: 'b610',
            type: 'number',
            rules: [{ required: true, message: '请输入是否实行兼职委员制' }],
          },
        ]);
      }
      if (`${val}`.startsWith('96')) {
        data = data.concat([
          {
            label: '本年召开党委全委会数',
            code: 'b3212',
            type: 'number',
            rules: [{ required: true, message: '请输入本年召开党委全委会数' }],
          },
          {
            label: '为第一书记安排不低于1万元工作经费的村',
            code: 'bZT412',
            type: 'number',
            rules: [{ required: true, message: '请输入为第一书记安排不低于1万元工作经费的村' }],
          },
          {
            label: '未完成“五小”建设的乡镇',
            code: 'bZT429',
            type: 'number',
            rules: [{ required: true, message: '请输入未完成“五小”建设的乡镇' }],
          },
          {
            label: '向建制村选派第一书记数',
            code: 'bZT410',
            type: 'number',
            rules: [{ required: true, message: '请输入向建制村选派第一书记数' }],
          },
          {
            label: '暂无活动场所的建制村',
            code: 'bZT426',
            type: 'number',
            rules: [{ required: true, message: '请输入暂无活动场所的建制村' }],
          },
          {
            label: '纪委常委数',
            code: 'b3210',
            type: 'number',
            rules: [{ required: true, message: '请输入纪委常委数' }],
          },
          {
            label: '本年各级培训第一书记（人次）',
            code: 'bZT411',
            type: 'number',
            rules: [{ required: true, message: '请输入本年各级培训第一书记（人次）' }],
          },
          {
            label: '本年领导班子民主生活会参加人员数',
            code: 'b3215',
            type: 'number',
            rules: [{ required: true, message: '请输入本年领导班子民主生活会参加人员数' }],
          },
          {
            label: '是否已召开年会',
            code: 'b325',
            type: 'boolean',
            rules: [{ required: true, message: '请输入是否已召开年会' }],
          },
          {
            label: '提拔使用或晋级的第一书记数',
            code: 'bZT414',
            type: 'number',
            rules: [{ required: true, message: '请输入提拔使用或晋级的第一书记数' }],
          },
          {
            label: '是否本年已换届',
            code: 'b323',
            type: 'boolean',
            rules: [{ required: true, message: '请输入是否本年已换届' }],
          },
          {
            label: '党委候补委员数',
            code: 'b3211',
            type: 'number',
            rules: [{ required: true, message: '请输入党委候补委员数' }],
          },
          {
            label: '建制村运转经费合计（万元/年）',
            code: 'bZT416',
            type: 'number',
            rules: [{ required: true, message: '请输入建制村运转经费合计（万元/年）' }],
          },
          {
            label: '活动场所面积200㎡以上的建制村',
            code: 'bZT427',
            type: 'number',
            rules: [{ required: true, message: '请输入活动场所面积200㎡以上的建制村' }],
          },
          {
            label: '是否本年任届期满',
            code: 'b322',
            type: 'boolean',
            rules: [{ required: true, message: '请输入是否本年任届期满' }],
          },
          {
            label: '村党组织书记报酬合计（万元/年）',
            code: 'bZT418',
            type: 'number',
            rules: [{ required: true, message: '请输入村党组织书记报酬合计（万元/年）' }],
          },
          {
            label: '村党组织书记报酬低于农村居民人均可支配收入两倍标准的县',
            code: 'bZT420',
            type: 'boolean',
            rules: [
              {
                required: true,
                message: '请选择',
              },
            ],
          },
          {
            label: '纪委委员数',
            code: 'b329',
            type: 'number',
            rules: [{ required: true, message: '请输入纪委委员数' }],
          },
          {
            label: '因工作不胜任召回调整的第一书记数',
            code: 'bZT415',
            type: 'number',
            rules: [{ required: true, message: '请输入因工作不胜任召回调整的第一书记数' }],
          },
          {
            label: '本届换届选举代表数',
            code: 'b326',
            type: 'number',
            rules: [{ required: true, message: '请输入本届换届选举代表数' }],
          },
          {
            label: '从村党组织书记中选拔乡镇领导干部数',
            code: 'bZT49',
            type: 'number',
            rules: [{ required: true, message: '请输入从村党组织书记中选拔乡镇领导干部数' }],
          },
          {
            label: '本年新建或改扩建活动场所',
            code: 'bZT428',
            type: 'number',
            rules: [{ required: true, message: '请输入本年新建或改扩建活动场所' }],
          },
          {
            label: '是否试行党代会常任制',
            code: 'b324',
            type: 'boolean',
            rules: [{ required: true, message: '请输入是否试行党代会常任制' }],
          },
          {
            label: '建制村办公经费合计（万元/年）',
            code: 'bZT417',
            type: 'number',
            rules: [{ required: true, message: '请输入建制村办公经费合计（万元/年）' }],
          },
          {
            label: '党委委员数',
            code: 'b327',
            type: 'number',
            rules: [{ required: true, message: '请输入党委委员数' }],
          },
          {
            label: '从村党组织书记中录用公务员和事业编制工作人员',
            code: 'bZT48',
            type: 'number',
            rules: [
              { required: true, message: '请输入从村党组织书记中录用公务员和事业编制工作人员' },
            ],
          },
          {
            label: '落实正常离任村党组织书记生活补贴的县',
            code: 'bZT422',
            type: 'number',
            rules: [{ required: true, message: '请输入落实正常离任村党组织书记生活补贴的县' }],
          },
          {
            label: '派出单位落实责任、项目、资金捆绑的村',
            code: 'bZT413',
            type: 'number',
            rules: [{ required: true, message: '请输入派出单位落实责任、项目、资金捆绑的村' }],
          },
          {
            label: '村干部基本报酬和村级组织办公经费合计低于9万元的县',
            code: 'bZT419',
            type: 'number',
            rules: [
              {
                required: true,
                message: '请输入村干部基本报酬和村级组织办公经费合计低于9万元的县',
              },
            ],
          },
          {
            label: '本年召开领导班子民主生活会',
            code: 'b3214',
            type: 'number',
            rules: [{ required: true, message: '请输入本年召开领导班子民主生活会' }],
          },
          {
            label: '无集体经济收入的建制村数',
            code: 'bZT425',
            type: 'number',
            rules: [{ required: true, message: '请输入无集体经济收入的建制村数' }],
          },
          {
            label: '为村党组织书记办理养老保险的县',
            code: 'bZT421',
            type: 'boolean',
            rules: [{ required: true, message: '请选择' }],
          },
          {
            label: '落实村民小组长误工补贴的县',
            code: 'bZT424',
            type: 'boolean',
            rules: [{ required: true, message: '请选择' }],
          },
          {
            label: '落实农村公共服务运行维护支出或服务群众专项经费的县',
            code: 'bZT423',
            type: 'boolean',
            rules: [
              {
                required: true,
                message: '请选择',
              },
            ],
          },
          {
            label: '党委常委数',
            code: 'b328',
            type: 'number',
            rules: [{ required: true, message: '请输入党委常委数' }],
          },
          {
            label: '本年内参加党委全委会委员数',
            code: 'b3213',
            type: 'number',
            rules: [{ required: true, message: '请输入本年内参加党委全委会委员数' }],
          },
        ]);
      }
      data.forEach((item, index) => {
        let node = this.renderItem(item, `${val}`.startsWith('96') ? formItemLayout2 : {});
        if (index == 0) {
          res.push(
            <Col span={24}>
              <div style={{ padding: '0 4%' }}>
                <Alert
                  message="提示：以下信息项为单位党统数据流（手填项）信息集，公推直选和评议部分请在组织扩展信息中维护。"
                  type="info"
                  showIcon
                />
                <WhiteSpace />
              </div>
            </Col>,
          );
        }
        res.push(node);
      });
    }
    return res;
  };
  renderNewExtra = () => {
    const { tipMsg } = this.props;
    const { basicInfo = {} } = this.props.unit;
    const { getFieldValue, getFieldDecorator } = this.props.form;
    let obj = getFieldValue('d04Code');
    let val = obj;
    if (typeof obj == 'object') {
      val = obj['key'];
    }
    let res: any = [];
    if (`${val}`.startsWith('1') || `${val}`.startsWith('2')) {
      let exData = [
        {
          label: '在岗职工数（人）',
          code: 'onPostNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗职工数（人）' }],
        },
        {
          label: '党政机关工作人员',
          code: 'b30A12',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入党政机关工作人员' }],
        },
      ];
      exData.forEach((item, index) => {
        let node = this.renderItem(item);
        res.push(node);
      });
    }

    if (`${val}`.startsWith('3')) {
      if (`${val}`.startsWith('32')) {
        let exData = [
          {
            label: '在岗职工数（人）',
            code: 'onPostNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '请输入在岗职工数（人）' }],
          },
          {
            label: '在岗专业技术人员数（人）',
            code: 'tecNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
          },
          {
            label: '专业技术人员中含高级职称（人）',
            code: 'zaigangGaoji',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '专业技术人员中含高级职称（人）' }],
          },
          // {
          //   label: '是否实行院（所）长负责制',
          //   code: 'hasDeanResponsibilitySystem',
          //   type: 'boolean',
          //   codeType: '',
          //   rules: [{ required: true, message: '是否实行院（所）长负责制' }],
          // },
          {
            label: '院所长负责制情况',
            code: 'd112Code',
            type: 'dict',
            codeType: 'dict_d112',
            rules: [{ required: true, message: '院所长负责制情况' }],
          },
        ];
        // if(getFieldValue('hasDeanResponsibilitySystem') !== 1){
        //   exData = exData.filter(it=>it.code != 'd112Code');
        // }
        exData.forEach((item, index) => {
          let node = this.renderItem(item);
          res.push(node);
        });
      }

      if (`${val}`.startsWith('33')) {
        let exData = [
          {
            label: '在岗职工数（人）',
            code: 'onPostNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '请输入在岗职工数（人）' }],
          },
          {
            label: '在岗专业技术人员数（人）',
            code: 'tecNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
          },
          {
            label: '专业技术人员中含高级职称（人）',
            code: 'zaigangGaoji',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '专业技术人员中含高级职称（人）' }],
          },
        ];
        exData.forEach((item, index) => {
          let node = this.renderItem(item);
          res.push(node);
        });
        if (`${val}`.startsWith('331')) {
          let exData = [
            {
              label: '是否设立常委会',
              code: 'hasStandingCommittee',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '是否设立常委会' }],
            },
            {
              label: '办校类别',
              code: 'd109Code',
              type: 'dict',
              codeType: 'dict_d109',
              // backType:'object',
              rules: [{ required: true, message: '办校类别' }],
            },
            {
              label: '是否向地方党委和主管部委专题报告党委领导下的校长负责制执行',
              code: 'hasReportImplementation',
              type: 'boolean',
              codeType: '',
              rules: [
                {
                  required: true,
                  message: '是否向地方党委和主管部委专题报告党委领导下的校长负责制执行',
                },
              ],
            },
            {
              label: '是否修订党委全委会、常委会和校长办公会议事规则',
              code: 'hasOfficeProcedure',
              type: 'boolean',
              codeType: '',
              rules: [
                { required: true, message: '是否修订党委全委会、常委会和校长办公会议事规则' },
              ],
            },
            {
              label: '学校党委书记是否向地方党委述职',
              code: 'schoolHasReportsLocal',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '学校党委书记是否向地方党委述职' }],
            },
            {
              label: '是否组织开展二级院（系）党组织书记向学校党委述职',
              code: 'hasSecretaryUniversityCommittee',
              type: 'boolean',
              codeType: '',
              rules: [
                { required: true, message: '是否组织开展二级院（系）党组织书记向学校党委述职' },
              ],
            },
            {
              label: '校长是否中共党员',
              code: 'hasPresidentPartyMember',
              type: 'boolean2',
              codeType: '',
              rules: [{ required: true, message: '校长是否中共党员' }],
            },
            {
              label: '校长是否担任党委副书记',
              code: 'hasDeputyPartySecretary',
              type: 'boolean2',
              codeType: '',
              rules: [{ required: true, message: '校长是否担任党委副书记' }],
            },
          ];
          exData.forEach((item, index) => {
            let node = this.renderItem(item);
            res.push(node);
          });
        }
      }

      if (`${val}`.startsWith('341') || `${val}`.startsWith('342') || `${val}`.startsWith('343')) {
        let exData = [
          {
            label: '在岗职工数（人）',
            code: 'onPostNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '请输入在岗职工数（人）' }],
          },
          {
            label: '在岗专业技术人员数（人）',
            code: 'tecNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
          },
          {
            label: '专业技术人员中含高级职称（人）',
            code: 'zaigangGaoji',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '专业技术人员中含高级职称（人）' }],
          },
          {
            label: '公益类别',
            code: 'd81Code',
            type: 'dict',
            codeType: 'dict_d81',
            // backType:'object',
            rules: [{ required: true, message: '公益类别' }],
          },
        ];
        exData.forEach((item, index) => {
          let node = this.renderItem(item);
          res.push(node);
        });
        if (`${val}`.startsWith('341')) {
          let exData = [
            {
              label: '医院等级',
              code: 'd95Code',
              type: 'dict',
              codeType: 'dict_d95',
              // backType:'object',
              rules: [{ required: true, message: '医院等级' }],
            },
            // {
            //   label: '办院级别',
            //   code: 'd110Code',
            //   type: 'dict',
            //   codeType: 'dict_d110',
            //   // backType:'object',
            //   rules: [{ required: true, message: '办院级别' }],
            // },
            {
              label: '办院类型',
              code: 'd111Code',
              type: 'dict',
              codeType: 'dict_d111',
              // backType:'object',
              rules: [{ required: true, message: '办院类型' }],
            },
            {
              label: '是否已实行党委领导下的院长负责制',
              code: 'hasResponsibilitySystem',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '是否已实行党委领导下的院长负责制' }],
            },
            {
              label: '领导体制是否已写入医院章程',
              code: 'ldtzyyzc',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '领导体制是否已写入医院章程' }],
            },
            {
              label: '党建工作要求是否写入医院章程',
              code: 'isPartyWorkWrite',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '党建工作要求是否写入医院章程' }],
            },
            {
              label: '是否开展基层党建述职评议考核',
              code: 'isOpenOrgAssess',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '是否开展基层党建述职评议考核' }],
            },
            {
              // label: '是否党委书记、院长分设',
              label: '是否党组织书记、院长分设',
              code: 'isLeaderSeparate',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '是否党组织书记、院长分设' }],
            },
            {
              label: '院长是否中共党员',
              code: 'hasDeanPartyMember',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '院长是否中共党员' }],
            },
            {
              label: '院长是否担任党委副书记',
              code: 'hasDeanPartySecretary',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '院长是否担任党委副书记' }],
            },
          ];

          // 是否党委书记.院长分设，后面的院长是否中共党员，院长是否担任党委副书记不显示
          if (
            !_isNumber(getFieldValue('isLeaderSeparate')) ||
            getFieldValue('isLeaderSeparate') == 0
          ) {
            let arr = ['hasDeanPartyMember', 'hasDeanPartySecretary'];
            exData = exData.filter((it) => !arr.includes(it.code));
          }

          exData.forEach((item, index) => {
            let node = this.renderItem(item);
            res.push(node);
          });
        }
      }

      if (
        `${val}`.startsWith('35') ||
        `${val}`.startsWith('36') ||
        `${val}`.startsWith('37') ||
        `${val}`.startsWith('38') ||
        `${val}`.startsWith('39')
      ) {
        let exData = [
          {
            label: '在岗职工数（人）',
            code: 'onPostNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '请输入在岗职工数（人）' }],
          },
          {
            label: '在岗专业技术人员数（人）',
            code: 'tecNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
          },
          {
            label: '专业技术人员中含高级职称（人）',
            code: 'zaigangGaoji',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '专业技术人员中含高级职称（人）' }],
          },
        ];
        exData.forEach((item, index) => {
          let node = this.renderItem(item);
          res.push(node);
        });
      }
    }

    if (`${val}`.startsWith('4')) {
      let exData = [
        {
          label: '在岗职工数（人）',
          code: 'onPostNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗职工数（人）' }],
        },
        {
          label: '在岗专业技术人员数（人）',
          code: 'tecNum',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
        },
        {
          label: '专业技术人员中含高级职称（人）',
          code: 'zaigangGaoji',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '专业技术人员中含高级职称（人）' }],
        },
        {
          label: '行业分类',
          code: 'd114Code',
          type: 'dict',
          codeType: 'dict_d114',
          rules: [{ required: true, message: '行业分类' }],
        },
      ];
      exData.forEach((item, index) => {
        let node = this.renderItem(item);
        res.push(node);
      });

      if (`${val}`.startsWith('41')) {
        let exData = [
          {
            label: '是否配备专职党务工作人员',
            code: 'hasPartyWork',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否配备专职党务工作人员' }],
          },
          {
            label: '是否配备专职副书记',
            code: 'hasMajorDeputySecretary',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否配备专职副书记' }],
          },
          {
            label: '企业规模',
            code: 'd17Code',
            type: 'dict',
            codeType: 'dict_d17',
            rules: [{ required: true, message: '企业规模' }],
          },
          {
            label: '经济类型控制',
            code: 'd16Code',
            type: 'dict',
            codeType: 'dict_d16',
            rules: [{ required: true, message: '经济类型控制' }],
            filter: (res) => {
              let arr = res.filter((it) => it.key === '1' || it.key === '2');
              return res;
            },
          },
          {
            label: '是否企业本级',
            code: 'hasFirmLevel',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否企业本级' }],
          },
          {
            label: '本级企业名称',
            code: 'firmLevelName',
            type: 'text',
            codeType: '',
            rules: [{ required: true, message: '本级企业名称' }],
          },
          {
            label: '企业级别',
            code: 'd115Code',
            type: 'dict',
            codeType: 'dict_d115',
            rules: [{ required: true, message: '企业级别' }],
          },
          {
            label: '是否建立董事会',
            code: 'hasDirectors',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否建立董事会' }],
          },
          {
            label: '董事长是否担任党组织书记',
            code: 'hasChairmanSecretary',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '董事长是否担任党组织书记' }],
          },
          {
            label: '董事长是否由上级企业有关负责人兼任',
            code: 'hasResponsiblePerson',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '董事长是否由上级企业有关负责人兼任' }],
          },
          {
            label: '党建工作经费是否按上年度工资总额一定比例纳入企业管理费用',
            code: 'hasProportionateFunding',
            type: 'boolean',
            codeType: '',
            rules: [
              {
                required: true,
                message: '党建工作经费是否按上年度工资总额一定比例纳入企业管理费用',
              },
            ],
          },
          {
            label: '人事管理和基层党建是否由一个部门抓',
            code: 'hasBranchToCatch',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '人事管理和基层党建是否由一个部门抓' }],
          },
          {
            label: '人事管理和基层党建是否由一个领导管',
            code: 'hasByLeader',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '人事管理和基层党建是否由一个领导管' }],
          },
          {
            label: '党务工作人员和经营管理人员是否同职级同待遇',
            code: 'hasSameTreatment',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '党务工作人员和经营管理人员是否同职级同待遇' }],
          },
          {
            label: '是否上市公司',
            code: 'hasPublicCompany',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否上市公司' }],
          },
          {
            label: '党建工作是否写入公司章程',
            code: 'hasArticlesIncorporation',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '党建工作是否写入公司章程' }],
          },
          {
            label: '是否党组织研究讨论作为董事会、经理层决策重大问题前置程序',
            code: 'hasPrepositionalProcedure',
            type: 'boolean',
            codeType: '',
            rules: [
              {
                required: true,
                message: '是否党组织研究讨论作为董事会、经理层决策重大问题前置程序',
              },
            ],
          },
          {
            label: '分支机构数',
            code: 'branches',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '分支机构数' }],
          },
          {
            label: '已建立党组织',
            code: 'haveBeenEstablished',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '已建立党组织' }],
          },
          {
            label: '基层党组织数量',
            code: 'partyOrganizationNum',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '基层党组织数量' }],
          },
          {
            label: '党员数',
            code: 'partyMembers',
            type: 'number',
            codeType: '',
            rules: [{ required: true, message: '党员数' }],
          },
        ];

        // 是否配备专职党务工作人员为是得时候，才展示改字段
        if (getFieldValue('hasPartyWork') != 1) {
          exData = exData.filter((it) => it.code != 'hasMajorDeputySecretary');
        }

        // 是否企业本级为否的时候，才展增加展现填写此字段
        if (getFieldValue('hasFirmLevel') === 1) {
          exData = exData.filter((it) => it.code != 'firmLevelName');
          exData = exData.filter((it) => it.code != 'hasResponsiblePerson');
        }

        // 当分支机构数大于0的时候，展现该字段
        if (!(getFieldValue('branches') > 0)) {
          exData = exData.filter((it) => it.code != 'partyMembers');
          exData = exData.filter((it) => it.code != 'partyOrganizationNum');
          exData = exData.filter((it) => it.code != 'haveBeenEstablished');
        }

        exData.forEach((item, index) => {
          let node = this.renderItem(item);
          res.push(node);
        });
      } else {
        let exData = [
          {
            label: '是否依托组织部门成立的非公党工委',
            code: 'hasNonPublicParty',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否依托组织部门成立的非公党工委' }],
          },
          {
            label: '是否设立专门办事机构的非公党工委',
            code: 'hasSpecialAgencies',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否设立专门办事机构的非公党工委' }],
          },
          {
            label: '办事机构工作人员编制数',
            code: 'staffOfficeNumbers',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '办事机构工作人员编制数' }],
          },
          {
            label: '组织部门（非公党工委）直接联系的非公企业党组织数',
            code: 'nonPublicEnterprises',
            type: 'number',
            codeType: '',
            rules: [
              { required: true, message: '组织部门（非公党工委）直接联系的非公企业党组织数' },
            ],
          },
          // {
          //   label: '法定代表人是否党员',
          //   code: 'hasRepresentative',
          //   type: 'boolean',
          //   codeType: '',
          //   rules: [{ required: true, message: '法定代表人是否党员' }],
          // },
          // {
          //   label: '是否兼任企业党组书记',
          //   code: 'hasProperSecretary',
          //   type: 'boolean',
          //   codeType: '',
          //   rules: [{ required: true, message: '是否兼任企业党组书记' }],
          // },
          // {
          //   label: '是否党建工作指导员联系',
          //   code: 'hasInstructorContact',
          //   type: 'boolean',
          //   codeType: '',
          //   rules: [{ required: true, message: '是否党建工作指导员联系' }],
          // },
          // {
          //   label: '党建工作指导员人数',
          //   code: 'jobInstructorNumber',
          //   type: 'number',
          //   codeType: '',
          //   rules: [{ required: true, message: '党建工作指导员人数' }],
          // },
          // {
          //   label: '是否建立工会或共青团组织',
          //   code: 'hasUnionOrganization',
          //   type: 'boolean',
          //   codeType: '',
          //   rules: [{ required: true, message: '是否建立工会或共青团组织' }],
          // },
          // {
          //   label: '吸收未转入组织关系的党员建立党组织数',
          //   code: 'absorbedTissueNumber',
          //   type: 'number',
          //   codeType: '',
          //   rules: [{ required: true, message: '吸收未转入组织关系的党员建立党组织数' }],
          // },
          // {
          //   label: '未转组织关系党员数',
          //   code: 'notTurnedParty',
          //   type: 'number',
          //   codeType: '',
          //   rules: [{ required: true, message: '未转组织关系党员数' }],
          // },
          // {
          //   label: '主要负责人是否党员',
          //   code: 'hasHeadParty',
          //   type: 'boolean',
          //   codeType: '',
          //   rules: [{ required: true, message: '主要负责人是否党员' }],
          // },
        ];
        const { basicInfo: { linkedDTOList = [] } = {} } = this.props.unit;
        let find = linkedDTOList.find((it) => (it?.orgType || '').startsWith('2'));
        if (!find) {
          let arr = [
            'hasNonPublicParty',
            'hasSpecialAgencies',
            'staffOfficeNumbers',
            'nonPublicEnterprises',
          ];
          exData = exData.filter((it) => !arr.includes(it.code));
        }
        // // 当法定代表人是否党员选择是的时候增加此字段
        // if(getFieldValue('hasRepresentative') != 1){
        //   exData = exData.filter(it=>it.code != 'hasProperSecretary');
        // }
        // // 当吸收未转入组织关系的党员建立党组织数大于0得时候，增加此字段
        // if(!(getFieldValue('absorbedTissueNumber') > 0)){
        //   exData = exData.filter(it=>it.code != 'notTurnedParty');
        // }
        exData.forEach((item, index) => {
          let node = this.renderItem(item);
          res.push(node);
        });
      }
    }

    if (`${val}`.startsWith('5')) {
      let exData = [
        {
          label: '从业人员数',
          code: 'employeesNumber',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '从业人员数' }],
        },
        {
          label: '是否党建工作指导员联系',
          code: 'hasInstructorContact',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否党建工作指导员联系' }],
        },
        {
          label: '是否建立工会或共青团组织',
          code: 'hasUnionOrganization',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否建立工会或共青团组织' }],
        },
        {
          label: '吸收未转入组织关系党员数',
          code: 'notTurnedParty',
          type: 'number',
          codeType: '',
          rules: [{ required: true, message: '吸收未转入组织关系党员数' }],
        },
        {
          label: '是否主要负责人担任党组织书记',
          code: 'hasOrganizationSecretary',
          type: 'boolean',
          codeType: '',
          rules: [{ required: true, message: '是否主要负责人担任党组织书记' }],
        },
      ];
      exData.forEach((item, index) => {
        let node = this.renderItem(item);
        res.push(node);
      });
      if (`${val}`.startsWith('51') || `${val}`.startsWith('52') || `${val}`.startsWith('53')) {
        let exData = [
          {
            label: '是否脱钩行业协会商户',
            code: 'isDecouplIndustry',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否脱钩行业协会商户' }],
          },
        ];
        exData.forEach((item, index) => {
          let node = this.renderItem(item);
          res.push(node);
        });
        if (`${val}`.startsWith('522') || `${val}`.startsWith('521')) {
          let exData = [
            {
              label: '在岗职工数',
              code: 'onPostNum',
              type: 'number',
              codeType: '',
              rules: [{ required: true, message: '在岗职工数' }],
            },
            {
              label: '专业技术人员数',
              code: 'technicalPersonnel',
              type: 'number',
              codeType: '',
              rules: [{ required: true, message: '专业技术人员数' }],
            },
            {
              label: '党员中高级职称人员数',
              code: 'partySeniorTitle',
              type: 'number',
              codeType: '',
              rules: [{ required: true, message: '党员中高级职称人员数' }],
            },
          ];
          exData.forEach((item, index) => {
            let node = this.renderItem(item);
            res.push(node);
          });
        }
      }
    }

    if (`${val}`.startsWith('9')) {
      if (`${val}`.startsWith('92')) {
        let exData = [
          {
            label: '是否建档立卡贫困村',
            code: 'hasPoorVillage',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否建档立卡贫困村' }],
          },
          {
            label: '是否实行“四议两公开”工作法',
            code: 'hasFourTwoOpenWork',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否实行“四议两公开”工作法' }],
          },
          {
            label: '是否成立村务监督委员会或其他村务监督机构',
            code: 'hasCommunitySupervisory',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否成立村务监督委员会或其他村务监督机构' }],
          },
        ];
        exData.forEach((item, index) => {
          let node = this.renderItem(item);
          res.push(node);
        });
        if (`${val}`.startsWith('921') || `${val}`.startsWith('922')) {
          let exData = [
            {
              label: '是否落实社区事务准入制度',
              code: 'hasCommunityAccess',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '是否落实社区事务准入制度' }],
            },
            {
              label: '建立党群服务中心',
              code: 'isOrgService',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '建立党群服务中心' }],
            },
            {
              label: '实行与驻区单位党建联建共建',
              code: 'hasJointUnits',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '实行与驻区单位党建联建共建' }],
            },
            {
              label: '是否按不低于上年度当地社会平均工资水平确定报酬',
              code: 'hasLowerSocial',
              type: 'boolean',
              codeType: '',
              rules: [
                { required: true, message: '是否按不低于上年度当地社会平均工资水平确定报酬' },
              ],
            },
            {
              label: '全部社区工作者年工资总额（万元）',
              code: 'communityWorkersSalary',
              type: 'number',
              addonAfter: '万元',
              codeType: '',
              rules: [{ required: true, message: '全部社区工作者年工资总额（万元）' }],
            },
            {
              label: '社区党组织书记年工资总额（万元）',
              code: 'communitySecretarySalary',
              type: 'number',
              addonAfter: '万元',
              codeType: '',
              rules: [{ required: true, message: '社区党组织书记年工资总额（万元）' }],
            },
            {
              label: '党建工作指导员数',
              code: 'communityBuildingNumber',
              type: 'number',
              // addonAfter:'万元',
              codeType: '',
              rules: [{ required: true, message: '党建工作指导员数' }],
            },
            {
              label: '是否建立社区工作者岗位等级序列',
              code: 'hasCommunityPositions',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '是否建立社区工作者岗位等级序列' }],
            },
            {
              label: '办公用房面积',
              code: 'communityOfficeSpace',
              type: 'number',
              addonAfter: '平方米',
              codeType: '',
              rules: [{ required: true, message: '办公用房面积' }],
            },
            {
              label: '是否实行兼职委员制',
              code: 'hasParttimeSystem',
              type: 'boolean',
              codeType: '',
              rules: [{ required: true, message: '是否实行兼职委员制' }],
            },
          ];
          exData.forEach((item, index) => {
            let node = this.renderItem(item);
            res.push(node);
          });
        }
      }
      if (`${val}`.startsWith('911')) {
        let exData = [
          {
            label: '是否建立志愿者服务机构',
            code: 'hasVolunteerOrganization',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否建立志愿者服务机构' }],
          },
          {
            label: '是否建立党员志愿者队伍',
            code: 'isVolTeam',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否建立党员志愿者队伍' }],
          },
          {
            label: '是否赋予区域综合管理权、对上级部门派驻机构负责人的人事考核权等权力',
            code: 'hasExaminationPower',
            type: 'boolean',
            codeType: '',
            rules: [
              {
                required: true,
                message: '是否赋予区域综合管理权、对上级部门派驻机构负责人的人事考核权等权力',
              },
            ],
          },
          {
            label: '是否取消招商引资等职能',
            code: 'hasCancelInvestmentPromotion',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否取消招商引资等职能' }],
          },
          {
            label: '是否整合职能统筹设置党政内设工作机构',
            code: 'hasWorkMechanism',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否整合职能统筹设置党政内设工作机构' }],
          },
          {
            label: '是否组织委员是否纳入县级党委管理',
            code: 'hasIncludedCommittee',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否组织委员是否纳入县级党委管理' }],
          },
          {
            label: '是否建立党群服务中心',
            code: 'hasGroupServiceCenter',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否建立党群服务中心' }],
          },
          {
            label: '是否实行与驻区单位党建联建共建',
            code: 'hasPartyBuildEndeavor',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否实行与驻区单位党建联建共建' }],
          },
          {
            label: '是否实行兼职委员制',
            code: 'b610',
            type: 'boolean',
            codeType: '',
            rules: [{ required: true, message: '是否实行兼职委员制' }],
          },
        ];
        exData.forEach((item, index) => {
          let node = this.renderItem(item);
          res.push(node);
        });
      }
    }

    return res;
  };
  validator = (rule, value, callback) => {
    if (value && getCredit(value) === 'Error') {
      callback('社会信用代码格式错误');
    } else {
      callback();
    }
  };
  render() {
    const { unitAdd, unitUpdate, tipMsg = {} } = this.props;
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { basicInfo = {} } = this.props.unit;
    let isCreateOrg = getFieldValue('isCreateOrg');
    if (isCreateOrg === undefined) {
      isCreateOrg = basicInfo['isCreateOrg'];
    }
    return (
      <Form {...formItemLayout3}>
        <FormItem label={formLabel('单位名称', tipMsg['name'])} {...formItemLayout}>
          {getFieldDecorator('name', {
            initialValue: basicInfo['name'],
            rules: [{ required: true, message: '请输入单位名称' }],
          })(<Input placeholder={'单位名称'} />)}
        </FormItem>

        <Row>
          <Col span={12}>
            <FormItem label={formLabel('单位类别', tipMsg['d04Code'])}>
              {getFieldDecorator('d04Code', {
                initialValue: basicInfo['d04Code'],
                rules: [{ required: true, message: '请选择单位类别' }],
              })(
                <DictTreeSelect
                  backType={'object'}
                  initValue={basicInfo['d04Code']}
                  codeType={'dict_d04'}
                  placeholder={'单位类别'}
                  parentDisable={true}
                />,
              )}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem label={formLabel('社会信用代码', tipMsg['creditCode'])}>
              {getFieldDecorator('creditCode', {
                initialValue: basicInfo['creditCode'],
                validateTrigger: ['onBlur'],
                rules: [
                  { required: true, message: '请输入社会信用代码' },
                  { validator: this.validator },
                ],
              })(<Input placeholder={'社会信用代码'} />)}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem label={formLabel('隶属关系', tipMsg['d35Code'])}>
              {getFieldDecorator('d35Code', {
                initialValue: basicInfo['d35Code'],
                rules: [{ required: true, message: '请选择隶属关系' }],
              })(
                <DictTreeSelect
                  backType={'object'}
                  initValue={basicInfo['d35Code']}
                  codeType={'dict_d35'}
                  placeholder={'隶属关系'}
                  parentDisable={true}
                />,
              )}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem label={formLabel('所在地区', tipMsg['d48Code'])}>
              {getFieldDecorator('d48Code', {
                initialValue: basicInfo['d48Code'],
                rules: [{ required: false, message: '请选择所在地区' }],
              })(
                <DictArea
                  placeholder={'所在地区'}
                  onChange={(val, obj) => this.props.form.setFieldsValue({ d48Name: obj.name })}
                  //  initValue={basicInfo['d48Name']}
                  //  backType={'object'}
                />,
              )}
              {getFieldDecorator('d48Name')(<Input style={{ display: 'none' }} />)}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem label={formLabel('是否法人单位', tipMsg['isLegal'])}>
              {getFieldDecorator('isLegal', {
                initialValue: basicInfo['isLegal'],
                rules: [{ required: true, message: '请选择是否法人单位' }],
              })(
                // <Switch defaultChecked={basicInfo['isLegal'] === 1} />
                <Select style={{ width: '100%' }}>
                  <Select.Option value={1}>是</Select.Option>
                  <Select.Option value={0}>否</Select.Option>
                </Select>,
              )}
            </FormItem>
          </Col>

          {(function () {
            let fieldValue = getFieldValue('d04Code');
            let val = fieldValue;
            if (typeof fieldValue == 'object') {
              val && (val = fieldValue['key']);
            }
            if (val && val.toString().startsWith('4')) {
              return (
                <Col span={12}>
                  <FormItem label={formLabel('是否分支机构', tipMsg['isBranch'])}>
                    {getFieldDecorator('isBranch', {
                      initialValue: basicInfo['isBranch'],
                      rules: [{ required: false, message: '' }],
                    })(
                      // <Switch defaultChecked={basicInfo['isBranch'] === 1} />
                      <Select style={{ width: '100%' }}>
                        <Select.Option value={1}>是</Select.Option>
                        <Select.Option value={0}>否</Select.Option>
                      </Select>,
                    )}
                  </FormItem>
                </Col>
              );
            }
          })()}

          <Col span={24}>
            <div style={{ padding: '0 4%' }}>
              <Alert
                message="提示：垂直管理部门是直接由市级直接管理，如公安局、税务局等。机关类型的垂直管理部门24表出数仅出在机关汇总栏（10栏），不出在地市州盟栏。"
                type="info"
                showIcon
              />
              <WhiteSpace />
            </div>
          </Col>
          <Col span={12}>
            <FormItem label={formLabel('是否垂直管理部门', tipMsg['isCzglbm'])}>
              {getFieldDecorator('isCzglbm', {
                initialValue: basicInfo['isCzglbm'],
                rules: [{ required: true, message: '请选择是否垂直管理部门' }],
              })(
                // <Switch defaultChecked={basicInfo['isCzglbm'] === 1} />
                <Select style={{ width: '100%' }}>
                  <Select.Option value={1}>是</Select.Option>
                  <Select.Option value={0}>否</Select.Option>
                </Select>,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label={formLabel('单位地址', tipMsg['address'])}>
              {getFieldDecorator('address', {
                initialValue: basicInfo['address'],
                rules: [{ required: false, message: '请输入单位地址' }],
              })(<Input placeholder={'单位地址'} />)}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem label={formLabel('是否建立党组织', tipMsg['isCreateOrg'])}>
              {getFieldDecorator('isCreateOrg', {
                initialValue: basicInfo['isCreateOrg'] || 0,
                rules: [{ required: true, message: '是否建立党组织' }],
              })(
                <Select style={{ width: '100%' }} disabled={true}>
                  <Select.Option value={1}>是</Select.Option>
                  <Select.Option value={0}>否</Select.Option>
                </Select>,
              )}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem label={formLabel('单位电话', tipMsg['telephone'])}>
              {getFieldDecorator('telephone', {
                initialValue: basicInfo['telephone'],
                rules: [
                  { required: false, message: '请输入单位电话' },
                  {
                    pattern: new RegExp(
                      '((\\d{11})|^((\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1})|(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1}))$)',
                    ),
                    message: '请输入正确的联系电话',
                  },
                ],
              })(<Input placeholder={'单位电话号码'} />)}
            </FormItem>
          </Col>

          {!isCreateOrg ? (
            <React.Fragment>
              {/* <Col span={12}>
                <FormItem label={formLabel('党建指导组织', tipMsg['manageUnitOrgCode'])}>
                  {getFieldDecorator('manageUnitOrgCode', {
                    initialValue: basicInfo['manageUnitOrgCode'],
                    rules: [{ required: false, message: '请选择党建指导组织' }],
                  })(
                    <OrgSelect
                      initValue={basicInfo['manageOrgName']}
                      placeholder={'党建指导组织'}
                    />,
                  )}
                </FormItem>
              </Col> */}
            </React.Fragment>
          ) : (
            <Fragment>
              <Col span={12}>
                <FormItem label={formLabel('建立党组织情况', tipMsg['d05Code'])}>
                  {getFieldDecorator('d05Code', {
                    initialValue: basicInfo['d05Code'],
                    rules: [{ required: true, message: '请选择建立党组织情况' }],
                  })(
                    <DictTreeSelect
                      backType={'object'}
                      initValue={basicInfo['d05Code']}
                      codeType={'dict_d05'}
                      placeholder={'建立党组织情况'}
                      parentDisable={true}
                    />,
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label={formLabel('关联组织', tipMsg['linkedDTOList'])}
                  {...formItemLayout}
                >
                  {getFieldDecorator('linkedDTOList', {
                    initialValue: basicInfo['linkedDTOList'],
                    // rules: [{ required: true, message: '请选择关联组织' }],
                  })(
                    <LinkedOrg
                      data={basicInfo['linkedDTOList']}
                      orgSelectDisabled={true} //单位查看组织的信息，不能有修改
                      addDisabled={true}
                      delDisabled={true}
                      onChange={this.linkedChange}
                    />,
                  )}
                </FormItem>
              </Col>
            </Fragment>
          )}

          {(function (_this) {
            let fieldValue = getFieldValue('d04Code');
            let val = fieldValue;
            if (typeof fieldValue == 'object') {
              val && (val = fieldValue['key']);
            }
            let flag =
              val &&
              (val.toString().startsWith('1') ||
                val.toString().startsWith('2') ||
                val.toString().startsWith('3') ||
                val.toString().startsWith('4') ||
                val.toString().startsWith('5') ||
                val.toString().startsWith('9'));
            // 当单位单位类别不属于以上类型，使用原来的
            if (!flag) {
              return (
                <Fragment>
                  <Col span={12}>
                    <FormItem
                      label={formLabel('主要负责人担任党组织书记', tipMsg['mainPartySecretary'])}
                    >
                      {getFieldDecorator('mainPartySecretary', {
                        initialValue: basicInfo['mainPartySecretary'],
                      })(
                        // <Switch defaultChecked={basicInfo['mainPartySecretary'] === 1} />
                        <Select style={{ width: '100%' }}>
                          <Select.Option value={1}>是</Select.Option>
                          <Select.Option value={0}>否</Select.Option>
                        </Select>,
                      )}
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem
                      label={formLabel('法定代表人兼任党组织书记', tipMsg['legalIsSecretary'])}
                    >
                      {getFieldDecorator('legalIsSecretary', {
                        initialValue: basicInfo['legalIsSecretary'],
                        rules: [{ required: false, message: '' }],
                      })(
                        // <Switch defaultChecked={basicInfo['legalIsSecretary'] === 1} />
                        <Select style={{ width: '100%' }}>
                          <Select.Option value={1}>是</Select.Option>
                          <Select.Option value={0}>否</Select.Option>
                        </Select>,
                      )}
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem label={formLabel('配备专职党务工作人员', tipMsg['hasMajorWorker'])}>
                      {getFieldDecorator('hasMajorWorker', {
                        initialValue: basicInfo['hasMajorWorker'],
                        rules: [{ required: false, message: '' }],
                      })(
                        // <Switch defaultChecked={basicInfo['hasMajorWorker'] === 1} />
                        <Select style={{ width: '100%' }}>
                          <Select.Option value={1}>是</Select.Option>
                          <Select.Option value={0}>否</Select.Option>
                        </Select>,
                      )}
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem
                      label={formLabel('配备专职副书记', tipMsg['hasMajorDeputySecretary'])}
                    >
                      {getFieldDecorator('hasMajorDeputySecretary', {
                        initialValue: basicInfo['hasMajorDeputySecretary'],
                        rules: [{ required: false, message: '' }],
                      })(
                        // <Switch defaultChecked={basicInfo['hasMajorDeputySecretary'] === 1} />
                        <Select style={{ width: '100%' }}>
                          <Select.Option value={1}>是</Select.Option>
                          <Select.Option value={0}>否</Select.Option>
                        </Select>,
                      )}
                    </FormItem>
                  </Col>
                  {_this.renderExtra()}
                </Fragment>
              );
            } else {
              return _this.renderNewExtra();
            }
          })(this)}
        </Row>

        <div style={{ textAlign: 'center' }}>
          <WhiteSpace />
          <Button
            type={'primary'}
            htmlType={'submit'}
            icon={<LegacyIcon type={'check'} />}
            onClick={this.handleSubmit}
            style={{ marginRight: 16 }}
            loading={basicInfo['code'] ? unitUpdate : unitAdd}
          >
            保存
          </Button>
          <Button
            danger
            type={'primary'}
            htmlType={'button'}
            icon={<LegacyIcon type={'delete'} />}
            onClick={() => this.props.close()}
          >
            取消
          </Button>
        </div>
      </Form>
    );
  }
}
export default Form.create()(index);
