import React, { useState, useEffect, useRef, Fragment } from 'react';
import LuckyExcel from 'luckyexcel';
import { exportExcel } from './export.js';
import { Upload, Button, Tabs, Spin } from 'antd';
import { withContext } from '@/utils/global';
import { getSession } from '@/utils/session';
import { connect } from "dva";
import { getOrgReportList, saveOrgReport, updateOrgReport, publishOrgReport } from '../services';
import OrgSelect from '@/components/OrgSelect';
import TableSelect from '@/components/TableSelect';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import './index.less';

const { TabPane } = Tabs;

const showtoolbarConfig = {
  undoRedo: true, //撤销重做，注意撤消重做是两个按钮，由这一个配置决定显示还是隐藏
  paintFormat: true, //格式刷
  currencyFormat: true, //货币格式
  percentageFormat: true, //百分比格式
  numberDecrease: true, // '减少小数位数'
  numberIncrease: true, // '增加小数位数
  moreFormats: true, // '更多格式'
  font: true, // '字体'
  fontSize: true, // '字号大小'
  bold: true, // '粗体 (Ctrl+B)'
  italic: true, // '斜体 (Ctrl+I)'
  strikethrough: true, // '删除线 (Alt+Shift+5)'
  underline: true, // '下划线 (Alt+Shift+6)'
  textColor: true, // '文本颜色'
  fillColor: true, // '单元格颜色'
  border: false, // '边框'
  mergeCell: true, // '合并单元格'
  horizontalAlignMode: true, // '水平对齐方式'
  verticalAlignMode: true, // '垂直对齐方式'
  textWrapMode: true, // '换行方式'
  textRotateMode: true, // '文本旋转方式'
  image: true, // '插入图片'
  link: true, // '插入链接'
  chart: false, // '图表'（图标隐藏，但是如果配置了chart插件，右击仍然可以新建图表）
  postil: true, //'批注'
  pivotTable: true,  //'数据透视表'
  function: true, // '公式'
  frozenMode: true, // '冻结方式'
  sortAndFilter: true, // '排序和筛选'
  conditionalFormat: true, // '条件格式'
  dataVerification: true, // '数据验证'
  splitColumn: true, // '分列'
  screenshot: false, // '截图'
  findAndReplace: true, // '查找替换'
  protection: false, // '工作表保护'
  print: false, // '打印'
};
const notShowtoolbarConfig = {
  allowCopy: false, // 是否允许拷贝
  showtoolbar: false, // 是否显示工具栏
  showinfobar: false, // 是否显示顶部信息栏
  allowEdit: false, // 是否允许前台编辑
  enableAddRow: false, // 允许增加行
  enableAddCol: false, // 允许增加列
  userInfo: false, // 右上角的用户信息展示样式
  showRowBar: false, // 是否显示行号区域
  showColumnBar: false, // 是否显示列号区域
  showtoolbarConfig: {
    undoRedo: false, //撤销重做，注意撤消重做是两个按钮，由这一个配置决定显示还是隐藏
    paintFormat: false, //格式刷
    currencyFormat: false, //货币格式
    percentageFormat: false, //百分比格式
    numberDecrease: false, // '减少小数位数'
    numberIncrease: false, // '增加小数位数
    moreFormats: false, // '更多格式'
    font: false, // '字体'
    fontSize: false, // '字号大小'
    bold: false, // '粗体 (Ctrl+B)'
    italic: false, // '斜体 (Ctrl+I)'
    strikethrough: false, // '删除线 (Alt+Shift+5)'
    underline: false, // '下划线 (Alt+Shift+6)'
    textColor: false, // '文本颜色'
    fillColor: false, // '单元格颜色'
    border: false, // '边框'
    mergeCell: false, // '合并单元格'
    horizontalAlignMode: false, // '水平对齐方式'
    verticalAlignMode: false, // '垂直对齐方式'
    textWrapMode: false, // '换行方式'
    textRotateMode: false, // '文本旋转方式'
    image: false, // '插入图片'
    link: false, // '插入链接'
    chart: false, // '图表'（图标隐藏，但是如果配置了chart插件，右击仍然可以新建图表）
    postil: false, //'批注'
    pivotTable: false,  //'数据透视表'
    function: false, // '公式'
    frozenMode: false, // '冻结方式'
    sortAndFilter: false, // '排序和筛选'
    conditionalFormat: false, // '条件格式'
    dataVerification: false, // '数据验证'
    splitColumn: false, // '分列'
    screenshot: false, // '截图'
    findAndReplace: false, // '查找替换'
    protection: false, // '工作表保护'
    print: false, // '打印'
  },
};

const index = (props: any) => {
  const org: any = getSession('org');
  const { pageType, login }: any = props;
  const checkRef: any = useRef();
  const [checkBackCellData, setCheckBackCellData] = useState<any>({}); // 反查cell的数据
  const [activeKey, setActiveKey] = useState<any>('0');
  const [dataInfo, setDataInfo] = useState<any>([]);
  const [sheet, setSheet] = useState<any>({});
  const [loading, setLoading] = useState<any>(false);

  const getSheetConfig = (pageType) => {
    let config = {
      container: 'luckysheet', //luckysheet为容器id
      lang: 'zh', // 设定表格语言
      title: '1233', // 设定表格名称
      allowUpdate: false,
      updateUrl: false,
      showtoolbarConfig,
      showsheetbarConfig: {
        add: false, //新增sheet
        menu: false, //sheet管理菜单
        sheet: false //sheet页显示
      },
      hook: {
        cellMousedown: (item, position) => {
          setCheckBackCellData({ item, position })
        },
        // cellEditBefore: (...val) => {
        //   console.log("🚀 ~ file: index.tsx ~ line 66 ~ QuickStart ~ componentDidMount ~ val", val)
        // }
      }
    };
    if (pageType === 'readOnly') {
      config = {
        ...config,
        ...notShowtoolbarConfig,
      }
    }
    return config;
  }
  const getCellRowName = (index) => {
    let arr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
    return arr[index];
  }
  const uploadExcel = (files) => {
    //  const files = files.target.files;
    if (files == null || files.length == 0) {
      alert('No files wait for import');
      return;
    }
    let name = files[0].name;
    let suffixArr = name.split('.'),
      suffix = suffixArr[suffixArr.length - 1];
    if (suffix != 'xlsx') {
      alert('Currently only supports the import of xlsx files');
      return;
    }
    LuckyExcel.transformExcelToLucky(
      files[0],
      function (exportJson, luckysheetfile) {
        if (exportJson.sheets == null || exportJson.sheets.length == 0) {
          alert(
            'Failed to read the content of the excel file, currently does not support xls files!',
          );
          return;
        }
        sheet.destroy();

        sheet.create({
          container: 'luckysheet', //luckysheet is the container id
          showinfobar: false,
          data: exportJson.sheets,
          lang: 'zh', // 设定表格语言
          title: exportJson.info.name,
          userInfo: exportJson.info.name.creator,
          showtoolbarConfig,
        });
      },
    );
  };
  const beforeUpload = (file) => {
    let _this = this;
    return new Promise(resolve => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        uploadExcel([file])
        resolve;
      };
    });
  }
  const tabsCallback = (activeKey) => {
    const { pageType }: any = props;
    setActiveKey(activeKey);
    sheet.destroy();
    let final = _get(dataInfo, `[0].excelContent`, [{}]);
    let option: any = getSheetConfig(pageType);
    sheet.create({
      ...option,
      data: final,
    });
  }
  const send = async (data) => {
    let item = _get(dataInfo, `[${parseInt(activeKey)}]`, {});
    if (!_isEmpty(data)) {
      const { code = 500 } = await publishOrgReport({
        data: {
          code: item.code,
          codeList: data.map(it => it.code)
        }
      });
      if (code === 0) {
        Tip.success('操作提示', '操作成功')
      }
    } else {
      Tip.info('操作提示', '请勾选相应的组织', 3);
    }
  }
  const checkBack = () => {
    const { item = {}, position = {} } = checkBackCellData;
    if (!_isEmpty(item) && !_isEmpty(position)) {
      checkRef.current.open();
    } else {
      Tip.info('操作提示', '请选择要反查的单元格', 3)
    }
  }
  const save = async (allData) => {
    let item = _get(dataInfo, `[${parseInt(activeKey)}]`, {});
    let url = _isEmpty(item.code) ? saveOrgReport : updateOrgReport;
    const org = getSession('org') || { code: '' }
    const { code = 500 } = await url({
      data: {
        code: _isEmpty(item) ? org.code : item.code,
        excelContent: allData
      }
    });
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      sheet.destroy();
      getSheetInfo(parseInt(activeKey));
    }
  }
  const getSheetInfo = async (PageIndex) => {
    const { pageType = undefined }: any = props;
    const org = getSession('org') || { code: '' }
    setLoading(true);
    const { code = 500, data = [] } = await getOrgReportList({ data: { code: org.code } });
    setLoading(false);
    setDataInfo(data);
    let final = [];
    if (code == 0) {
      if (!_isEmpty(_get(data, `[${PageIndex}].excelContent`, []))) {
        final = _get(data, `[${PageIndex}].excelContent`, []);
      }
    }
    sheet.destroy();
    let option: any = getSheetConfig(pageType);
    if (!_isEmpty(final)) {
      option.data = final;
    } else {
      option.data = [{}];
    }
    sheet.create(option);
  };

  useEffect(() => {
    let sheet = window.luckysheet;
    setSheet(sheet);
  }, []);
  useEffect(() => {
    if (!_isEmpty(sheet)) {
      setDataInfo([]);
      setActiveKey('0');
      setCheckBackCellData({});

      getSheetInfo(0);
    }
  }, [JSON.stringify(org), sheet])

  // 反查列
  const col = [
    {
      title: '组织名称',
      dataIndex: 'n',
    },
    {
      title: '数量',
      dataIndex: 'v',
    },
  ];
  let item = _get(dataInfo, `[${parseInt(activeKey)}]`, {});
  return (
    <div>
      <Spin spinning={loading}>
        {
          !_isEmpty(dataInfo) &&
          <Fragment>
            <Tabs activeKey={activeKey} onChange={tabsCallback}>
              {
                !_isEmpty(dataInfo) && dataInfo.map((it, index) => <TabPane tab={`表${index + 1}`} key={`${index}`} />)
              }
            </Tabs>
          </Fragment>
        }

        <div style={{ margin: '10px' }} >
          {/* <div style={{ display: 'inline-block' }}>
          <Upload beforeUpload={this.beforeUpload} multiple={false} accept=".xlsx">
            <Button style={{ marginRight: 8 }}>上传</Button>
          </Upload>
        </div> */}

          <Button
            onClick={() => {
              let allData = sheet.getAllSheets();
              exportExcel(allData, '下载');
            }} style={{ marginRight: 8 }}>下载</Button>

          <Button onClick={checkBack} style={{ marginRight: 8 }}>反查</Button>

          {
            pageType !== 'readOnly' ?
              <Fragment>
                {
                  item.publish != '1' &&
                  <Button type={'primary'}
                    onClick={() => {
                      sheet.exitEditMode();
                      let allData = sheet.getAllSheets();
                      save(allData);
                    }} style={{ marginRight: 8 }}>保存</Button>
                }
              </Fragment> :
              <Fragment>
                <OrgSelect
                  onChange={(data) => send(data)}
                  multiple={true}
                >
                  <Button type={'primary'} style={{ marginRight: 8 }}>发布</Button>
                </OrgSelect>
              </Fragment>
          }

          <TableSelect
            ref={checkRef}
            columns={col}
            title={`数据反查(单元格:${getCellRowName(_get(checkBackCellData, 'position.c', 0))}${_get(checkBackCellData, 'position.r', 0) + 1})`}
            action={'/api/orgreport/counterCheck'}
            method={'POST'}
            hasSelect={false}
            payload={{
              code: _get(dataInfo, `[${parseInt(activeKey)}].code`, undefined),
              r: _get(checkBackCellData, 'position.r', undefined),
              c: _get(checkBackCellData, 'position.c', undefined),
            }}
            // rowSelectionType={'checkbox'}
            rowKey={'c'}
            renderTableQuery={(res: any) => {
              const {
                data: { list: records = [], pageNumber: current = 1, pageSize: size = 10, totalRow: total = 0 } = {},
              } = res;
              return {
                list: records,
                pagination: { pageSize: size, total: total, current, pageNum: current },
              };
            }}
            // renderSearch={({
            //   list,
            //   query,
            //   selectedItems,
            //   selectedRowKeys,
            //   form,
            //   searchCallBack,
            // }: any) => {
            //   return <FormComp onFinish={searchCallBack}/>;
            // }}
            onSearchCallBack={(val: any) => {
              return val;
            }}
            onSubmit={({ selectedRowKeys, selectedItems, query }: any) => {
              // setInitValueTB(selectedItems);
              // form.setFieldsValue({
              //   memId: selectedItems
              // });
              return 0;
            }}
            // inputValueRender={(val: any) => {
            //   return val.name;
            // }}
            hasOwnShowComp={true}
          />

        </div>
      </Spin>
      <div id={"luckysheet"} style={{
        margin: '0px',
        padding: '0px',
        position: 'relative',
        width: '100%',
        height: '600px',
      }}></div>
    </div>
  );
};

export default index;
