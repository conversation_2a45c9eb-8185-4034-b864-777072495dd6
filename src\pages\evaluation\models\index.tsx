import {listPageModel, ListPageStore} from "@/utils/common-model";
import modelExtend from "dva-model-extend";
import {
  getHreMeetingsAndOneClass,
  getThematicPartyDay,
  getTwoCommittee
} from  "../service"
import Notice from '../../../components/Notice';
import { getSession } from '@/utils/session';
const flowMem=modelExtend(listPageModel,{
  namespace:'evaluation',
  state:{
  },
  subscriptions:{
    setup({ dispatch, history }) {
      history.listen(location => {

      });
    }
  },
  effects:{

    *list({payload},{call,put}){
      const { tab,...val }=payload;
      let info;
      switch (tab) {
        case '1':
          // info=yield call(getHreMeetingsAndOneClass, val);
          break;
        case '2':
          info=yield call(getHreMeetingsAndOneClass, val);
          break;
        case '3':
          info=yield call(getThematicPartyDay, val);
          break;
        case '4':
          // info=yield call(getHreMeetingsAndOneClass, val);
          break;
        case '5':
          // info=yield call(getHreMeetingsAndOneClass, val);
          break;
      }

      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'querySuccess',
          payload: {list:list,pagination:pagination}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *teamList({payload},{call,put}){
      const { tab,...val }=payload;
      let info;
      switch (tab) {
        case '1':
          // info=yield call(getHreMeetingsAndOneClass, val);
          break;
        case '2':
          // info=yield call(getHreMeetingsAndOneClass, val);
          break;
        case '3':
          // info=yield call(getThematicPartyDay, val);
          break;
        case '4':
          info=yield call(getTwoCommittee, val);
          break;
        case '5':
          // info=yield call(getHreMeetingsAndOneClass, val);
          break;
      }

      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'querySuccess',
          payload: {list:list,pagination:pagination}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *remove({payload},{call,put}){
      yield put({
        type: 'querySuccess',
        payload: {list:[],pagination:{

          }}
      });
    }

  },
  reducers: {
    success(state, {payload}) {
      return {...state, ...payload};
    },
  }
});
export default flowMem
