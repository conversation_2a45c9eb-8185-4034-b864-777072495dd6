import request from '@/utils/request';
import qs from 'qs';

export function collectiveEconomicGetList(params) {
  console.log('collectiveEconomicGetList新的====', params);
  return request(`/api/unit/collectiveEconomic/getList`, {
    method: 'POST',
    body: params,
  });
}

export function collectiveEconomicDelete(params) {
  return request(`/api/unit/collectiveEconomic/delete?${qs.stringify(params)}`);
}

export function collectiveEconomicDetails(params) {
  console.log('collectiveEconomicDetails====', params);

  return request(`/api/unit/collectiveEconomic/details?${qs.stringify(params)}`);
}

export function collectiveEconomicAdd(params) {
  console.log('collectiveEconomicAdd====',params);
  
  return request(`/api/unit/collectiveEconomic/add`, {
    method: 'POST',
    body: params,
  });
}
