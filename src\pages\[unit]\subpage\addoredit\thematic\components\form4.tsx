// 城市基层党建工作有关情况
import React, { Fragment, useEffect, useState } from 'react';
import { Button, Form, Row, Col, Switch } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import { formItemLayout, formItemLayout2, formItemLayout3 } from './config';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import _cloneDeep from 'lodash/cloneDeep';
import Tip from '@/components/Tip';
import { saveForm4, findForm } from '@/pages/[unit]/services/thematic';

const index = (props: any) => {
  const [form] = Form.useForm();
  const { selectTab, basicInfo } = props;
  const { code: unitCode = '' } = basicInfo;
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);

  const onFinish = async (e) => {
    setLoading(true);
    let val = _cloneDeep(e);
    [
      'hasPowerStreets',
      'hasCancelFunctionalStreet',
      'hasIntegrationInstitutionStreet',
      'hasManagedStreet',
      'hasAssessmentStreet',
      'hasSetServiceCenter',
      'hasImplementFactoryBuild',
      'hasEstablishJointSystem',
    ].map((it) => {
      val[it] = val[it] ? 1 : 0;
    });
    const { code = 500 } = await saveForm4({
      data: {
        unitCode,
        ...val,
      },
    });
    setLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '修改成功');
    }
  };
  const getInfo = async () => {
    const { code = 500, data = {} } = await findForm({
      unitCode,
      type: '4',
    });
    if (code === 0) {
      setQuery(data);
      form.setFieldsValue(data);
    }
  };
  useEffect(() => {
    getInfo();
  }, []);

  return (
    <Fragment>
      <Form form={form} {...formItemLayout3} onFinish={onFinish}>
        <Row>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasPowerStreets"
              label="赋予区域综合
              管理权、对上级部门派驻机构负责人的人事考核权等权
              力的街道"
              initialValue={query['hasPowerStreets'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
            {/* <Checkbox.Group options={options} onChange={() => {}} /> */}
          </Col>
          {/* <Col span={24}>
            <div style={{ padding: '0 4%' }}>
              <Divider plain>来源</Divider>
            </div>
          </Col> */}
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasCancelFunctionalStreet"
              label="取消招商引资等职能的街道"
              initialValue={query['hasCancelFunctionalStreet'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasIntegrationInstitutionStreet"
              label="整合职能统筹设置党政内设工作机构的街道"
              initialValue={query['hasIntegrationInstitutionStreet'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasManagedStreet"
              label="组织委员纳入县级党委管理的街道"
              initialValue={query['hasManagedStreet'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasAssessmentStreet"
              label="由县级党委和政府统筹安排检查考核的街道"
              initialValue={query['hasAssessmentStreet'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasSetServiceCenter"
              label="建立党群服务中心"
              initialValue={query['hasSetServiceCenter'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasImplementFactoryBuild"
              label="实行与驻区单位党建联建共建"
              initialValue={query['hasImplementFactoryBuild'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasEstablishJointSystem"
              label="市、区	、
              街道、社区均建立党建联席会议制度的城市"
              initialValue={query['hasEstablishJointSystem'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
        </Row>
        <div style={{ textAlign: 'center' }}>
          <WhiteSpace />
          <Button
            type={'primary'}
            htmlType={'submit'}
            icon={<LegacyIcon type={'check'} />}
            // onClick={() => {}}
            style={{ marginRight: 16 }}
            loading={loading}
          >
            保存
          </Button>
          {/* <Button
            type={'primary'}
            danger
            htmlType={'button'}
            icon={<LegacyIcon type={'delete'} />}
            onClick={() => {}}
          >
            取消
          </Button> */}
        </div>
      </Form>
    </Fragment>
  );
};
export default index;
