import React, { Fragment, useEffect, useState, useImperativeHandle } from 'react';
import styles from './index.less';
import { Tooltip, Modal } from 'antd';
import _isEmpty from 'lodash/isEmpty';

const TimeLine = (props) => {
  const { data: _data = [] } = props;
  const [click, setClick] = useState<any>({});
  const onClick = (it) => {
    setClick(it);
  };
  useEffect(() => {
    if (!_isEmpty(_data)) {
      setClick(_data[0]);
    }
  }, [JSON.stringify(_data)]);
  return (
    <Fragment>
      <div className={styles.page}>
        <div className={styles.person}>
          <div className={styles.clear}>
            <div className={styles.experience}>
              {!_isEmpty(_data) && _data.map((item, index) => {
                let num = 100 / _data.length;
                let len = index * num + num / 4;
                return (
                  <div key={index} style={{ left: `${len}%` }}>
                    <label className={click.value === item.value ? styles.clickItem : styles.default}
                      onClick={() => onClick(item)}>
                      {item['value']}
                    </label>
                    <span className={click.value === item.value ? styles.clickItemSpan : styles.itemSpan} />
                  </div>
                );
              })}
            </div>
          </div>
          <div className={styles.personCont}>
            {
              !_isEmpty(click) && <Fragment>
                <Tooltip title={click.desc}>
                  <div className={styles.desc}>{click.desc || ''}</div>
                </Tooltip>
              </Fragment>
            }
          </div>
        </div>
      </div>
    </Fragment>
  );
};
export default TimeLine;

interface Interface {
  title?: string,
  width?: string,
  onOK?: Function,
  timeLineAction: Function,
}
const ModalTL = React.forwardRef((props: Interface, ref) => {
  const {
    title = '时间轴',
    width = 1200,
    onOK,
    timeLineAction,
  } = props;
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState([]);
  const [query, setQurey] = useState<any>({});
  const [pagination, setPagination] = useState({ pageSize: 10, total: 0, pageNum: 1 });
  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      setQurey(query);
    },
    clear: () => {
      // clear();
    },
  }));
  useEffect(() => {
    if (!_isEmpty(query)) {
      getLists({ pageNum: 1 }).then();
    }
  }, [JSON.stringify(query)])
  const getLists = async (p = {}) => {
    const {
      code = 500,
      data = [],
      // data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await timeLineAction({
      code: query.code,
      ...p,
      // data: {
      //   pageSize: pagination.pageSize,
      //   pageNum: pagination.pageNum,
      // }
    });
    if (code === 0) {
      setList(data);
      // setPagination({ pageNum, total, pageSize });
    }
  };
  const handleOk = () => {
    onOK && onOK(query);
    handleCancel();
  };
  const handleCancel = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setQurey({});
    setList([]);
    setPagination({ pageSize: 10, total: 0, pageNum: 1 })
  };
  return (
    <Fragment>
      <Modal
        title={title}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={width}
        footer={null}
        destroyOnClose={true}
      >
        <TimeLine data={list}/>
      </Modal>
    </Fragment>
  )
});
export { ModalTL };
