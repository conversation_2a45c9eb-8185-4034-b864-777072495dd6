import React, { Fragment, useState, useEffect, useImperativeHandle, useRef } from 'react'
import { Tabs, Checkbox, Row, Col, Button, Divider, Modal, Alert, Form, Popconfirm, Input, } from 'antd';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import { getLockFiledList, saveLockFiled, lock, unlock, msg } from './service';
import { getSession } from '@/utils/session';
import { LockOutlined, UnlockOutlined } from '@ant-design/icons';
const { Group } = Checkbox;
const { TextArea } = Input;
const TabPane = Tabs.TabPane;

// 解锁的弹框
const LockReason = React.forwardRef((props:any, ref) => {
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 },
    },
  };
  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleCancel = () => {
    setRecord({});
    setVisible(false);
    setConfirmLoading(false);
    form.resetFields();
  };
  const onFinish = async (e) => {
    if(props.onFinish){
      setConfirmLoading(true);
      let res = await props.onFinish(e);
      setConfirmLoading(false);
    }
  };
  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      setRecord(query);
      if (query) {
        form.setFieldsValue({ ...query });
      }
    },
    close:()=>{
      handleCancel()
    },
    clear: () => {
      // clear();
    },
  }));
  return (
    <Modal
      title={'解锁原因'}
      visible={visible}
      // onOk={() => {
      //   form.submit()
      // }}
      onCancel={handleCancel}
      width={600}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
      footer={[
        <Button key={1} htmlType={'button'} onClick={handleCancel} style={{ marginRight: '5px' }}>取消</Button>,
        <Button key={2} htmlType={'button'} type={'primary'} loading={confirmLoading} onClick={() => { form.submit() }}>确定</Button>
        // <Popconfirm placement="topRight" title={<span>
        //   本功能仅用于原库中党员入党时间有误的修正，请谨慎操作。
        // </span>} onConfirm={() => { form.submit() }}>
        // </Popconfirm>,
      ]}
    >
      {
        visible &&
        <Fragment>
          <Form form={form} {...formItemLayout} onFinish={onFinish}>
            <Form.Item name='unlockReason'
              label="解锁原因"
              rules={[{ required: true, message: '解锁原因' },
                //  { validator: TimeValidator }
              ]}
            >
              <TextArea showCount maxLength={20} />
            </Form.Item>
          </Form>
        </Fragment>
      }

    </Modal>
  )
});

const TableActionMenu = (props: any) => {
  const { lockOrUnlock, record, type } = props;
  const ModalRef: any = useRef();
  const onClick = async () => {
    let url = _isEmpty(record.lockFields) ? lock : unlock;
    const { code = 500, data = {} } = await url({
      data: {
        lockObject: type,
        code: record.code,
      }
    });
    if (code == 0) {
      Tip.success('操作提示', '操作成功');
      lockOrUnlock(record);
    }
  }
  const _unlock = () => {
    if (true) {
      // 弹框
      ModalRef.current.open();
    } else {
      // 直接请求
      onClick()
    }
  }
  return (
    <Fragment>
      {
        _isEmpty(record.lockFields) ?
          <Popconfirm
            title={<div style={{ width: '300px' }}>信息经锁定后，将只能由具有解锁权限的上级党组织授权才能解锁，是否进行锁定？</div>}
            placement="topRight"
            onConfirm={() => onClick()}>
            <a className={'del'}><span style={{ color: 'green' }}>锁定</span></a>
          </Popconfirm> :
          <Fragment>
            <a onClick={_unlock}>{<span style={{ color: 'red' }}>解锁</span>}</a>
            <LockReason ref={ModalRef} {...props} onFinish={async (e)=>{
              const { code = 500, data = {} } = await unlock({
                data: {
                  lockObject: type,
                  code: record.code,
                  unlockReason:e.unlockReason
                }
              });
              if (code == 0) {
                Tip.success('操作提示', '操作成功');
                ModalRef.current.close()
                lockOrUnlock(record);
                return code;
              }
            }}/>
          </Fragment>
      }
    </Fragment>
  )
};

const LockMsg = React.forwardRef((props: any, ref) => {
  const { basicInfo = {} } = props;
  const [info, setInfo] = useState<any>({});
  const getLockName = (type) => {
    let name = '';
    let icon: any = <LockOutlined />;
    switch (type) {
      case '2':
        name = '解锁';
        icon = <UnlockOutlined />;
        break;
      case '3':
        name = '锁定';
        icon = <LockOutlined />
        break;
    }
    return { name, icon };
  }

  const getInfo = async (val: any) => {
    const { code = 500, data = [] } = await msg({
      data: {
        unlockCode: val.code,
        code: getSession('org').code,
        unlockObject: val.unlockObject,
      }
    });
    if (code === 0) {
      setInfo(data);
    }
  };
  useEffect(() => {
    if (basicInfo.code) {
      getInfo(basicInfo)
    }
  }, [JSON.stringify(basicInfo)]);

  useImperativeHandle(ref, () => ({
    getInfos: async (val: any) => getInfo(val),
  }));
  let { icon = '', name = '' } = getLockName(info.type) || {};
  if (_isEmpty(info)) {
    return '';
  }
  // return (
  //   <span style={{fontSize:14}}>
  //     {icon} 该信息由 {info.memAccount} {name}
  //   </span>
  // )
  return (
    <Alert
      message={`${info}`}
      type="info"
      showIcon
    />
  )
});

const lockMem = [
  { key: 'name', value: '姓名', checked:true }, // 必选
  { key: 'sexCode', value: '性别', checked:true }, // 必选
  { key: 'idcard', value: '身份证号', checked:true }, // 必选
  { key: 'birthday', value: '出生日期', checked:true }, // 必选
  { key: 'd48Code', value: '籍贯', checked:true }, // 必选
  { key: 'd06Code', value: '民族', checked:true }, // 必选
  { key: 'd09Code', value: '岗位' },
  { key: 'd07Code', value: '学历' },
  { key: 'phone', value: '联系电话' },
  { key: 'd21Code', value: '一线情况' },
  { key: 'd27Code', value: '加入党组织方式', checked:true }, // 必选
  { key: 'd49Code', value: '党费交纳情况' },
  { key: 'homeAddress', value: '家庭住址' },
  { key: 'applyDate', value: '申请入党时间', checked:true }, // 必选
  { key: 'activeDate', value: '确定积极分子时间', checked:true }, // 必选
  { key: 'objectDate', value: '确定发展对象时间', checked:true }, // 必选
  { key: 'joinOrgDate', value: '确定为预备党员时间', checked:true }, // 必选
  { key: 'fullMemberDate', value: '成为正式党员日期', checked:true }, // 必选
];

const lockOrg = [
  { key: 'name', value: '组织全称' },
  { key: 'd01Code', value: '组织类别' },
  { key: 'd03Code', value: '隶属关系' },
  { key: 'contacter', value: '组织联系人' },
  { key: 'contactPhone', value: '联系电话' },
  { key: 'createDate', value: '建立日期' },
  { key: 'postCode', value: '邮政编码' },
  { key: 'postAddress', value: '通讯地址' },
];

const lockUnit = [
  { key: 'name', value: '单位名称' },
  { key: 'd04Code', value: '单位类别' },
  { key: 'd35Code', value: '隶属关系' },
  { key: 'isLegal', value: '是否独立法人单位' },
  { key: 'creditCode', value: '社会信用代码' },
  { key: 'isCzglbm', value: '是否垂直管理部门' },
  { key: 'isCreateOrg', value: '是否建立党组织' },
  { key: 'onPostNum', value: '在岗职工数（人）' },
  { key: 'b30A12', value: '党政机关工作人员' },
];

const tabs = [
  { key: '1', title: '人员信息' },
  { key: '3', title: '组织信息' },
  { key: '2', title: '单位信息' },
];
const index = () => {
  const [menu, setMenu] = useState([]);
  const [fixedMenu, setFixedMenu] = useState([]);
  const [type, setType] = useState('1');
  const [check, setCheck] = useState([]);
  const [fixed, setFixed] = useState([]); 
  const [allCheckInfo, setallCheckInfo] = useState<any>({});
  const [saveLoading, setSaveLoading] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = React.useState(false);
  const [time, setTime] = useState(+new Date());

  const getAllcheckValue = (type) => {
    let arr: any = [];
    switch (type) {
      case '1':
        arr = lockMem.map(it => it.key);
        break;
      case '3':
        arr = lockOrg.map(it => it.key);
        break;
      case '2':
        arr = lockUnit.map(it => it.key);
        break;
    }
    return arr;
  }
  const getcheckedInfo = async () => {
    const { code = 500, data = [] } = await getLockFiledList({});
    if (code === 0) {
      setallCheckInfo(data)
    }
  }
  const onChange = (val) => {
    setType(val);
    setTime(+new Date());
  }
  const save = async () => {
    setSaveLoading(true);
    const { code = 500 } = await saveLockFiled({
      data: {
        lockObject: type,
        filedList: check
      }
    });
    setSaveLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      getcheckedInfo();
    }
  };
  const checkBoxChange = (val) => {
    let arr: any = getAllcheckValue(type);
    setCheck(val);
    setIndeterminate(val.length < arr.length && val.length != 0);
    setCheckAll(val.length === arr.length);
  };
  const onCheckAllChange = e => {
    // let arr: any = getAllcheckValue(type);
    let arr:any = menu.map((it:any)=>it.key);
    setCheck(e.target.checked ? arr : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };

  useEffect(() => {
    getcheckedInfo();
  }, []);
  // useEffect(() => {
  //   let menu: any = [];
  //   switch (type) {
  //     case '1':
  //       menu = lockMem;
  //       break;
  //     case '3':
  //       menu = lockOrg;
  //       break;
  //     case '2':
  //       menu = lockUnit;
  //       break;
  //   }
  //   setMenu(menu);
  // }, [type]);
  useEffect(() => {
    if (!_isEmpty(allCheckInfo)) {
      let arr: any = getAllcheckValue(type);
      let { field = [], fixedField = [] } = allCheckInfo.find(it => it.lockObject == type) || {};
      setFixed(fixedField)
      // setFixedMenu();
      let menu: any = [];
      switch (type) {
        case '1':
          menu = lockMem;
          break;
        case '3':
          menu = lockOrg;
          break;
        case '2':
          menu = lockUnit;
          break;
      }
      let _menu = menu.filter(it=>!fixedField.includes(it.key))
      let fixedMenu = menu.filter(it=>fixedField.includes(it.key))
      setMenu(_menu);
      setFixedMenu(fixedMenu);

      setCheck(field);
      setIndeterminate(!_isEmpty(field) && (field.length < arr.length));
      setCheckAll(!_isEmpty(field) && (field.length == arr.length));
    }
  }, [JSON.stringify(allCheckInfo), type, JSON.stringify(getSession('org').code)]);

  return (
    <div style={{ height: '100%', overflow: 'hidden' }} key={time}>
      <Tabs activeKey={type} onChange={onChange}>
        {
          !_isEmpty(tabs) && tabs.map(item => <TabPane tab={item['title']} key={item['key']} />)
        }
      </Tabs>
      <div style={{ width: '100%' }}>
        <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
          全选
        </Checkbox>
        <Divider />
        <Group style={{ width: '100%' }} value={fixed}>
          <Row>
            {
              !_isEmpty(fixedMenu) && fixedMenu.map((item, index) => {
                return (
                  <Fragment>
                    {/* 固定选项 */}
                    <Col span={4} key={index}><Checkbox disabled={true} checked={true} value={item['key']}>{item['value']}</Checkbox></Col>
                  </Fragment>
                )
              })
            }
          </Row>
        </Group>

        <Group style={{ width: '100%' }} onChange={checkBoxChange} value={check}>
          <Row>
            {
              !_isEmpty(menu) && menu.map((item, index) => {
                return (
                  <Fragment>
                    {/* 固定选项 */}
                    {/* <Col span={4} key={index}><Checkbox disabled={item['checked']} checked={item['checked']} value={item['key']}>{item['value']}</Checkbox></Col> */}
                    {/* 非固定选项 */}
                    <Col span={4} key={index}><Checkbox value={item['key']} disabled={fixed.includes(item['key'])}>{item['value']}</Checkbox></Col>
                  </Fragment>
                )
              })
            }
          </Row>
        </Group>
        <div style={{ marginTop: '10px', textAlign: 'center' }}>
          <Button type={'primary'} onClick={save} loading={saveLoading}>保存</Button>
        </div>
      </div>
    </div>
  )
}
export { TableActionMenu, lockMem, LockMsg };
export default index
