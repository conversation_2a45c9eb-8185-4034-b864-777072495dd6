/**
 * 流动党员
 */
import React, { Fragment } from 'react';
import { connect } from 'dva';
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Col, Divider, Input, Modal, Popconfirm, Row, Tabs } from 'antd';
import NowOrg from 'src/components/NowOrg';
import OutRegistrationDetail from './outRegistrationDetail';
import moment from 'moment';
import { isEmpty, setListHeight } from '@/utils/method';
import OutWithdraw from './outWithdraw';
import { getSession } from '@/utils/session';
import styles from './index.less';
import { tableColConfig } from '@/services';
import ExportInfo from '@/components/Export';
import { changeMsgTip } from '@/utils/method';

const Search = Input.Search;
const TabPane = Tabs.TabPane;

@connect(({ unit, commonDict, loading, flowMem }) => ({
  flowMem,
  unit,
  commonDict,
  loading: loading.effects['unit/getList'],
}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {},//筛选器
      search: {},//搜索框
      view: false,
      subordinate:getSession('subordinate')
    };
  }

  addOrEdit = (record?: object) => {
    this['RegistrationDetail'].showModal();
  };
  export = () => {

  };

  componentDidMount() {
    const org = getSession('org') || {};
    this.setState({ orgCode: org['orgCode'] });
    setListHeight(this);
    this.selectList(1, 10, org['orgCode']);
    this.getMsg();
  }
  // 获取tipMsg
  getMsg=()=>{
    tableColConfig({id:'ccp_mem_flow'}).then(res=>{
      if(res['code']=='0'){
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg:msg,
        });
      }
    });
  }
  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org = getSession('org') || {};
    const subordinate = getSession('subordinate') || '0';
    if ((!isEmpty(this.state['orgCode']) && this.state['orgCode'] !== org['orgCode']) || (subordinate !== this.state.subordinate)) {
      this.setState({
        orgCode: org['orgCode'],
        subordinate
      }, () => {
        this.selectList(1, 10, org['orgCode']);
      });
    }
  }

  selectList = (pageNum = 1, size = 10, code = '') => {
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'flowMem/list',
      payload: {
        pageNum: pageNum,
        pageSize: size,
        orgCode: org['orgCode'],
        ...this.state.filter,
        ...this.state.search,
      },
    });
  };

  onPageChange = (page, pageSize) => {
    this.selectList(page, pageSize, this.state.orgCode);
    // let {query}=this.props.location;
    // router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  filterChange = (val) => {
    const org = getSession('org') || {};
    this.setState({
      filter: val,
    }, () => this.selectList(1, 10, org['orgCode']));
  };
  action = (val?: any) => {
    // const {search,filter}=this.state;
    // const {pagination={}}=this.props.unit;
    // const {current,pageSize}=pagination;
    // const {query}=this.props.location;
    // const org=getSession('org')||{};
    // this.props.dispatch({
    //   type:'unit/getList',
    //   payload:{
    //     data:{
    //       mainUnitOrgCode:org['orgCode'],
    //       manageUnitOrgCode:org['orgCode'],
    //       isCreateOrg:0,
    //       pageNum:current,
    //       pageSize,
    //       ...query,
    //       ...search,
    //       ...filter,
    //       ...val,
    //     }
    //   }
    // })
  };
  confirm = (record) => {
    this.props.dispatch({
      type: 'flowMem/del',
      payload: {
        data: {
          code: record['code'],
        },
      },
    }).then(res => {
      if (res['code'] === 0) {
        this.selectList(1, 10, this.state['orgCode']);
      }
    });
  };
  search = (value) => {
    this.setState({
      search: { unitName: value },
    }, () => this.selectList());
  };
  view = (record) => {
    this.setState({ view: true });
    this.props.dispatch({
      type: 'flowMem/detail',
      payload: {
        code: record['code'],
      },
    }).then(res => {
      if (res.code === 0) {
        this.setState({
          detailInfo: res['data'],
        });
      }
    });
  };
  goBack = (record) => {
    this.setState({ record });
    this['OutWithdraw'].showModal();
  };
  handleOk = () => {
    this.setState({ view: false });
  };
  handleCancel = () => {
    this.setState({ view: false });
  };

  changeList = (v) => {
    if (v) {
      this.selectList(1, 10, this.state['orgCode']);
    }
  };
  exportInfo= async ()=>{
    this.setState({
      flowBackDownload:true,
    })
    await this['flowBack'].submitNoModal();
    this.setState({
      flowBackDownload:false,
    })
  };

  render() {
    const { loading, commonDict, flowMem: { list = [], pagination = {} } } = this.props;
    const { dataInfo, filterHeight, detailInfo = {} } = this.state;
    const org = getSession('org') || {};
    const columns = [
      {
        title: '姓名',
        dataIndex: 'memName',
        width: 200,
      },
      {
        title: '流出组织',
        dataIndex: 'memOrgName',
        width: 300,
      },
      {
        title: '流入组织',
        dataIndex: 'outflowOrgName',
        width: 300,
      },
      {
        title: '流动类型',
        dataIndex: 'isProvOutName',
        width: 200,
      },
      {
        title: '流出时间',
        dataIndex: 'outflowDate',
        width: 200,
        render: (text) => {
          return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
        },
      },
      {
        title: '匹配情况',
        dataIndex: 'matchSituation',
        width: 200,
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 180,
        render: (text, record) => {
          const { flowStatus = '' } = record;
          return (
            <React.Fragment>
              <a href={'#'} onClick={() => this.view(record)}>查看</a>
              <Divider type="vertical"/>
              {
                flowStatus !== 2 &&
                <Fragment>
                  <a href={'#'} onClick={() => this.goBack(record)}>停止流动</a>
                  <Divider type="vertical"/>
                </Fragment>
              }
              <Popconfirm title="确定要删除吗？" onConfirm={() => this.confirm(record)}>
                <a href={'#'} className='del'>删除</a>
              </Popconfirm>
            </React.Fragment>
          );
        },
      },
    ];
    const filterData = [
      {
        key: 'sexCodeList', name: '人员性别', value: [{ key: '1', name: '男' }, { key: '0', name: '女' }],
      },
      {
        key: 'd09CodeList', name: '工作岗位', value: commonDict[`dict_d09_tree`],
      },
      {
        key: 'd07CodeList', name: '学历教育', value: commonDict[`dict_d07_tree`],
      },
      {
        key: 'd41CodeList', name: '原因类型', value: commonDict[`dict_d41_tree`],
      },
    ];


    return (
      <div className={styles.showHead} style={{ height: '100%', overflow: 'hidden' }}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        <OutWithdraw onChange={this.changeList} data={this.state['record']} filterHeight={filterHeight}
                     wrappedComponentRef={(e) => this['OutWithdraw'] = e}/>
        <OutRegistrationDetail onChange={this.changeList} wrappedComponentRef={(e) => this['RegistrationDetail'] = e} tipMsg={this.state['tipMsg']}/>
        <NowOrg extra={
          <React.Fragment>
             <Button onClick={this.exportInfo} loading={this.state.flowBackDownload}>导出</Button>
            <Button type={'primary'} icon={<LegacyIcon type={'plus'}/>} style={{marginLeft:16}} onClick={() => this.addOrEdit()}>流出登记</Button>
            {/*<Search style={{width:200,marginLeft:16}} onSearch={this.search}/>*/}
          </React.Fragment>
        }/>
        <RuiFilter data={filterData}
                   openCloseChange={() => setListHeight(this, 20)}
                   onChange={this.filterChange}/>
        <WhiteSpace/>
        <WhiteSpace/>

        <ListTable scroll={{ y: filterHeight }}
         
          columns={columns} data={list} pagination={pagination}
                   onPageChange={this.onPageChange}/>
        <Modal
          title={'流出记录查看'}
          destroyOnClose
          closable={false}
          className='view_Modal'
          visible={this.state['view']}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          footer={false}
          width={700}
        >
          <div>
            <div className={styles.head}>
              <div>
                <p>党员:{detailInfo['memName']}</p>
                <p>党员类型:{detailInfo['memTypeName']}</p>
              </div>
              <div>
                <p><span>从:</span>{detailInfo['memOrgName']}</p>
                <p><span>到:</span>{detailInfo['outflowOrgName']}</p>
              </div>
            </div>
            <div>
              <Row>
                <Col span={6} className={styles.col_text}>是否明确流入党组织:</Col><Col span={18}
                                                                               className={styles.col_val}>{detailInfo['isExplicitInflowOrg'] === 1 ? '已明确' : '未明确'}</Col>
                <Col span={6} className={styles.col_text}>流动去向:</Col><Col span={18}
                                                                          className={styles.col_val}>{detailInfo['outflowAreaName']}</Col>
                <Col span={6} className={styles.col_text}>流动类型:</Col><Col span={18}
                                                                          className={styles.col_val}>{detailInfo['isProvOutName']}</Col>
                <Col span={6} className={styles.col_text}>外出原因类型:</Col><Col span={18}
                                                                            className={styles.col_val}>{detailInfo['outflowReasonName']}</Col>
                <Col span={6} className={styles.col_text}>流出党组织联系人:</Col><Col span={18}
                                                                              className={styles.col_val}>{detailInfo['outflowOrgLinkman']}</Col>
                <Col span={6} className={styles.col_text}>流出党组织联系方式:</Col><Col span={18}
                                                                               className={styles.col_val}>{detailInfo['outflowOrgPhone']}</Col>
                {/* <Col span={6} className={styles.col_text}>流动原因:</Col><Col span={18}
                                                                          className={styles.col_val}>{detailInfo['outflowReason']}</Col> */}
                <Col span={6} className={styles.col_text}>流动党员活动证:</Col><Col span={18}
                                                                             className={styles.col_val}>{detailInfo['isHold'] === 1 ? '已发放' : '未发放'}</Col>
              </Row>
            </div>
          </div>
        </Modal>
        <ExportInfo wrappedComponentRef={e=>this['flowBack'] = e}
                    tableName={''}
                    noModal={true}
                    tableListQuery={{...this.state.filter,...this.state.search,orgCode:org['orgCode']}}
                    action={'/api/flowmem/exportOutMemList'}
        />
      </div>
    );
  }
}
