import React, { useImperativeHandle } from 'react'
import { connect } from "dva";
import _isEmpty from 'lodash/isEmpty';
const Index = (props: any, ref) => {
  const { commonDict = {} } = props || {};
  useImperativeHandle(ref, () => ({
    checkDictValue: (val: any) => {
      return check(val);
    },
  }));
  const check = (regs) => {
    const { val, dictList } = regs;
    if (!_isEmpty(dictList)) {
      let wrongFromKey: any = undefined;
      let itemName: any = undefined;
      let dictName: any = undefined;
      let findWrong = dictList.find(item => {
        if (typeof item === 'string') {
          itemName = item.endsWith('Code') ? item : `${item}Code`;
          dictName = item.endsWith('Code') ? `dict_${item.substring(0, item.length - 4)}` : `dict_${item}`;
        } else {
          const { formKey = '', dictName: _dictName = '' } = item;
          itemName = formKey;
          dictName = _dictName;
        }

        let formValue = val[itemName];

        if (commonDict[dictName] && formValue) {
          let _find = commonDict[dictName].find(it => it.key == formValue);
          if (!_find) {
            wrongFromKey = itemName;
          }
          return !_find;
        } else {
          return false;
        }
      });
      return {
        flag: !findWrong,
        fromKey: wrongFromKey,
      }
    }
  }
  return null;
}

export default connect(({ commonDict }: any) => ({ commonDict }), undefined, undefined, { forwardRef: true })(React.forwardRef(Index))
