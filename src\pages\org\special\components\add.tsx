import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Form, Input, Modal, Radio, Switch, Select, InputNumber } from 'antd';
import DictSelect from '@/components/DictSelect';
import LongLabelFormItem from '@/components/LongLabelFormItem'
import Date from '@/components/Date';
import { getSession } from '@/utils/session';
import LinkedOrg from '@/pages/[unit]/subpage/addoredit/linkedOrg';
import LinkedSpecialOrg from './linkedOrg';
import moment from 'moment';
import Tip from '@/components/Tip';
import { natureAdd, natureDetails } from '@/pages/org/services/org';
import DictTreeSelect from '@/components/DictTreeSelect';
import _cloneDeep from 'lodash/cloneDeep';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const { TextArea } = Input;
const RadioGroup = Radio.Group;
const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [thingCode, setThingCode] = useState();
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [list, setList] = useState([]);
  const [pagination, setPagination] = useState({ pageSize: 10, total: 0, page: 1 });
  const [tableLoading, setTableLoading] = useState(false);
  const {
    width = 800,
  } = props;

  useImperativeHandle(ref, () => ({
    open: query => {
      setThingCode(undefined);
      if (query) {
        setThingCode(query);
        getDetail(query);
        // setDataInfo(query);
        // form.setFieldsValue(query);
      }
      open();
    },
  }));
  const getDetail = async (code) => {
    const { code: resCode = 500, data } = await natureDetails({ code });
    if (resCode === 0) {
      let _data = {
        ...data,
        associatedOrganization: (data?.associatedOrganization || []).map((it, index) => {
          return { ...it, id: moment().valueOf() + index }
        }),
      }
      setDataInfo(_data);
      form.setFieldsValue(_data);
    }
  };

  const open = () => {
    setVisible(true);
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const changeLinkInfoToForm = (e: any, key: any) => {
    if (e[key]) {
      let data: Array<object> = [];
      for (let obj of e[key]) {
        const { org = {} } = obj;
        if (org['code']) {
          //新增的关联组织
          data.push({
            code: org['code'],
            orgName: org['industryName'] || org['name'],
            orgType: org['d01Code'] || '1',
            orgTypeName: org['d01Name'] || '1',
            linkedOrgCode: org['code'],
            orgTypeCode: org['orgType'] || '1',
            isOrgMain: obj['isOrgMain'],
          });
        } else {
          //已关联的组织
          data.push(obj);
        }
      }
      e[key] = data;
    }
    return e;
  };
  const onFinish = async (e) => {
    let vals: any = _cloneDeep(e);
    const { onOK } = props;
    const { code: orgCode = '', orgCode: industryOrgCode = '' } = getSession('org') || { code: '', orgCode: '' };

    vals = changeLinkInfoToForm(vals, 'associatedOrganization');
    let val = {
      ...vals,
      code: thingCode,
      orgCode,
      industryOrgCode,
    };

    setConfirmLoading(true);
    const { code = 500 } = await natureAdd({
      data: {
        ...val,
      }
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  return (
    <Fragment>
      <Modal
        title={'行业党组织'}
        visible={visible}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
      >
        {
          visible && <Fragment>
            <Form form={form} {...formItemLayout} onFinish={onFinish}>

              <Form.Item name='industryOrgName'
                label="组织名称"
                rules={[{ required: true, message: '请输入组织名称' }]}
              >
                <Input />
              </Form.Item>

              <Form.Item name='industryOrgType'
                label="组织类别"
                rules={[{ required: true, message: '请选择组织类别' }]}
              >
                <DictTreeSelect codeType={'dict_d122'} initValue={dataInfo['industryOrgType'] || ''} parentDisable={true}/>
              </Form.Item>

              <Form.Item name='industryClassification'
                label="行业分类"
                rules={[{ required: true, message: '请选择行业分类' }]}
              >
                <DictTreeSelect initValue={dataInfo['industryClassification']} codeType={'dict_d96'} placeholder={'行业分类'} parentDisable={true} />
              </Form.Item>

              <Form.Item name='subordinateLevel'
                label="所属层级"
                rules={[{ required: true, message: '请选择所属层级' }]}
              >
                <DictTreeSelect initValue={dataInfo['subordinateLevel']} codeType={'dict_d132'} placeholder={'所属层级'} parentDisable={true} />
              </Form.Item>

              <Form.Item name='membershipFunction'
                label="隶属关系"
                rules={[{ required: true, message: '请选择隶属关系' }]}
              >
                <DictTreeSelect initValue={dataInfo['membershipFunction']} codeType={'dict_d133'} placeholder={'隶属关系'} parentDisable={true} />
              </Form.Item>

              <LongLabelFormItem label={'书记是否由行业主管部门党员负责同志担任'}
                required={true}
                code={'hasSecretaryIndustry'}
                tipMsg={{}}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item name={code}
                      {...formItemLayout}
                      rules={[{ required: true, message: '请选择书记是否由行业主管部门党员负责同志担任' }]}
                    >
                      <Select placeholder={'请选择'} style={{ width: '100%' }}>
                        <Select.Option value={1}>是</Select.Option>
                        <Select.Option value={0}>否</Select.Option>
                      </Select>
                    </Form.Item>
                  )
                }} />

              <Form.Item name='workerNumber'
                label="专职工作人员数"
                rules={[{ required: true, message: '专职工作人员数' }]}
              >
                <InputNumber max={99999999} min={0} style={{ width:'100%' }} />
              </Form.Item>

              <Form.Item name='hasPartyOrganizations'
                label="是否有所属党组织"
                rules={[{ required: true, message: '是否有所属党组织' }]}
              >
                <Select placeholder={'请选择'}>
                  <Select.Option value={1}>是</Select.Option>
                  <Select.Option value={0}>否</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item noStyle
                shouldUpdate={(prevValues, currentValues) => prevValues.hasPartyOrganizations !== currentValues.hasPartyOrganizations}
              >
                {({ getFieldValue }) => {
                  return (getFieldValue('hasPartyOrganizations') && getFieldValue('hasPartyOrganizations') == 1) ? (
                    <Form.Item
                      label="关联组织"
                      name='associatedOrganization'
                      rules={[{ required: true, message: '关联组织' }]}
                    >
                      <LinkedOrg data={dataInfo['associatedOrganization']}
                        renderTableCol={(e) => {
                          return e.filter(it => it.dataIndex !== 'isOrgMain')
                        }}
                        onChange={(val: any) => {
                          form.setFieldsValue({
                            associatedOrganization: val,
                          });
                        }} />
                    </Form.Item>
                  ) : null
                }}
              </Form.Item>

            </Form>
          </Fragment>
        }
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
