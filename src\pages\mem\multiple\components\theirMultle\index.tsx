import React, { Fragment } from 'react';
import { Divider, Input, Popconfirm, Button } from 'antd';
import qs from 'qs';
import NowOrg from 'src/components/NowOrg';
import {_history as router} from "@/utils/method";

import { getSession } from '@/utils/session';
import { connect } from 'dva';
import moment from 'moment';
import ListTable from 'src/components/ListTable';
import RuiFilter from '@/components/RuiFilter';
import WhiteSpace from '@/components/WhiteSpace';
import _isNumber from 'lodash/isNumber';
import Tip from '@/components/Tip';
import { setListHeight } from '@/utils/method';
const {Search} = Input;
@connect(({memMultiple,commonDict,loading})=>({memMultiple,commonDict,loading}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      filterHeight:100,
    }
  }
  componentDidMount(): void {
    setListHeight(this);
  }

  // 筛选
  filterChange=(val)=>{
    this.props.dispatch({
      type:'memMultiple/updateState',
      payload:{
        filter:val
      }
    });
    this.action()
  };
  // 分页
  onPageChange=(page,pageSize)=>{
    const {query ={}}=this.props.location || {};
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  action=(val?:object)=>{
    const {pagination={}}=this.props.memMultiple;
    const {current,pageSize}=pagination;
    const org=getSession('org') || {};
    this.props.dispatch({
      type:'memMultiple/getList',
      payload:{
        data:{
          grapPung:1,
          memOrgCode:org['orgCode'],
          pageNum:current,
          pageSize,
          ...val
        }
      }
    })
  };
  search=(value)=>{
    this.props.dispatch({
      type:'memMultiple/updateState',
      payload:{
        memName:value
      }
    });
    this.action();
  };

  // 关联外支部党员
  addNew=()=>{

  };
  // 修改
  edit=(record)=>{

  };
  // 取下关联
  del= async (record)=>{
    if(record) {
      const res = await this.props.dispatch({
        type: 'memMultiple/del',
        payload: {
          code: record['code']
        }
      });
      const { code = 500 } = res || {};
      if(code === 0){
        Tip.success('操作提示','操作成功');
        this.action();
      }
    }
  };
  render() {
    const {loading:{effects = {}}={},memMultiple} = this.props;
    const {list, pagination} = memMultiple;
    const {current,pageSize} = pagination;
    const {filterHeight} = this.state;
    const filterData = [
      {
        key:'sexCodeList',name:'人员性别',value:[{key:'1',name:'男'},{key:'0',name:'女'}],
      },
      {
        key:'d08CodeList',name:'人员类型',value:[{key:'1',name:'正式党员'},{key:'2',name:'预备党员'}],
      },
    ];
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:60,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'姓名',
        dataIndex:'memName',
        width:100,
        render:(text,record)=>{
          return(
            <a onClick={()=>this.edit(record)} >{text}</a>
          )
        }
      },
      {
        title:'性别',
        dataIndex:'sexCode',
        width:100,
        render:(text)=>{
          return (
            <span> {text === '1' ?  '男' : '女'} </span>
          )
        }
      },
      {
        title:'党员类型',
        dataIndex:'d08Name',
        width:100,
      },
      {
        title:'党员所在党组织',
        width:160,
        dataIndex:'memCurrOrgName',
      },
      {
        title:'党员关联党组织',
        width:160,
        dataIndex:'joinOrgName',
      },
      {
        title:'进入管理类型',
        width:100,
        dataIndex:'joinOrgTypeName',
      },
      {
        title:'进入管理日期',
        width:100,
        dataIndex:'joinOrgDate',
        render:(text)=>{
          return(<div>{_isNumber(text)? moment(text).format('YYYY-MM-DD') : ''}</div>)
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        width:220,
        render:(text,record)=>{
          return(
            <span>
              <Popconfirm title="确定取消关联？" onConfirm={()=>this.del(record)}>
               <a className={'del'} >取消关联</a>
              </Popconfirm>
            </span>
          )
        },
      },
    ];
    return (
      <div>
        <NowOrg
          extra={
            <React.Fragment>
              <Search style={{width:200,marginLeft:16}} onSearch={this.search} placeholder={'请输入检索关键词'}/>
            </React.Fragment>
          }
        />
        <RuiFilter
          data={filterData}
          onChange={this.filterChange}
          openCloseChange={()=>setListHeight(this,20)}
        />
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:filterHeight}} columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
      </div>
    );
  }
}
