/**
 * 添加班子成员
 */
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Input, Modal, Radio, Row, Select, Switch, InputNumber, Button } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import MemSelect from '@/components/MemSelect';
import Tip from '@/components/Tip';
import UploadComp, { getInitFileList, fitFileUrlForForm } from '@/components/UploadComp';
import DictSelect from '@/components/DictSelect';
import { getSession } from "@/utils/session";
import moment from 'moment';
import { findDictCodeName, formLabel, getIdCardInfo, correctIdcard } from '@/utils/method';
import Date from '@/components/Date';
import YN from '@/components/YesOrNoSelect';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import _isArray from 'lodash/isArray';
import { getOrgCommitteeSrcUnit } from '../../../services';
import _isNumber from 'lodash/isNumber';
import ListTable from '@/components/ListTable';

const FormItem = Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      d022CodeDisabled: [],
      visible1: false,
      idCard: '',
      list: [],
      params: {},
    }
  }
  showModal = (edit = false) => {
    console.log("showModal", edit);
    let { children, title, dataInfo = {}, elect = {}, tipMsg = {}, isVillageCommunity = false, isHasMiddleManagement = true, loading: { effects = {} } = {} } = this.props;
    const { newdataInfo = {}, list = [] } = this.state;
    let info = { ...dataInfo, ...newdataInfo }
    console.log(info, 'infoinfo')
    if (info) {
      this.setState({
        visible: true,
        idCard: info?.memIdcard,
        readonly: edit
      });
    } else {
      this.setState({
        visible: true,
        readonly: edit
      });
    }
  };

  // 根据姓名查找人员信息，用于页面回显
  getSelectMemInfo = async (memInfo) => {
    const { dataInfo = {} } = this.props;
    const { basicInfo = {} } = this.props.org;
    this.setState({
      idCard: memInfo[0]?.idcard || '',
    })
    const { code = 500, data = {} } = await getOrgCommitteeSrcUnit({
      orgCode: basicInfo?.code,
      memCode: memInfo[0]?.code || undefined,
    })
    if (code == 0) {
      if (!_isEmpty(data)) {
        let arr = Object.keys(data)
        arr.map((item) => {
          dataInfo[item] = data[item];
        })
        this.setState({
          newdataInfo: dataInfo
        })
      }
    }
  }

  handleOk = () => {
    const { elect, dataInfo = {}, iteListData = [] } = this.props;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if (val['memName'] != dataInfo['memName'] || val['memIdcard'] != dataInfo['memIdcard']) {
          let result = await correctIdcard(val['memName'], val['memIdcard']);
          if (result['code'] != '200') {
            this.props.form.setFields({
              memIdcard: {
                value: val['memIdcard'],
                errors: [new Error('经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')]
              }
            })
            Tip.error('操作提示', '经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')
            return
          } else {
            val['idCardReason'] = result['reason']
            val['idCardReasonName'] = result['reasonName']
          }
        }
        if (val['reward'] > 200) {
          Tip.error('操作提示', '报酬（万元/年）不能大于200')
          return
        }
        // 图片
        if (!_isEmpty(val['photoPath'])) {
          console.log('photoPath===', val['photoPath']);
          if (_isArray(val['photoPath'])) {
            val['fileName'] = val['photoPath'][0].name || ''
            val['photoPath'] = fitFileUrlForForm(val['photoPath'])
          }
        } else {
          val['fileName'] = undefined;
          val['photoPath'] = undefined;
        }

        let obj = undefined;

        val = findDictCodeName(
          ['d51', 'd07', 'd121', 'd138', 'd144', 'd89'],
          val,
          dataInfo,
        );
        if (val['d022Code'] && typeof (val['d022Code']) == 'object') {
          let tag = false;
          val['d022Code'].map((it: any) => {
            if (typeof (it) == 'string') {
              tag = true;
            }
          })
          if (tag) {
            val['d022Code'] = val['d022Code'].toString()
          } else {
            val['d022Code'] = val['d022Code'].map(i => i.value).toString();
          }
        }

        // 是否为五方面人员选否时清空班子成员来源的值
        if (val['whetherItIsFromFiveAspects'] === 0) {
          val['d138Code'] = undefined;
          val['d138Name'] = undefined;
        }
        // 是否村任职选调生选否的时候清空选调单位层级的值
        if (val['hasVillageTransferStudent'] === 0) {
          val['d144Code'] = undefined;
          val['d144Name'] = undefined;
          val['isDoubleFirst'] = undefined;
        }

        // ['d022Code','d51Code','d07Code'].map(obj=>{
        //   let key=obj.split('C')[0];
        //   if(typeof val[obj] === 'object' ){
        //     val[`${key}Name`]=val[obj]['name'];
        //     val[obj]=val[obj]['key']
        //   }
        // });
        ['hasMiddleManagement', 'memTypeCode'].forEach(key => {
          if (val[key]) {
            val[key] = 1;
          } else {
            val[key] = 0;
          }
        })
        if (val['sexCode'] == 1) {
          val['sexName'] = '男'
        } else if (val['sexCode'] == 0) {
          val['sexName'] = '女'
        }
        ['startDate', 'endDate'].map(obj => {
          if (val[obj]) {
            val[obj] = val[obj].valueOf();
          }
        });
        if (val['memCode'] && typeof val['memCode'] === 'object') {
          val['memCode'] = val['memCode'][0]['code'];
          const find = iteListData.find(obj => obj['memCode'] == val['memCode']);
          if (find) {
            return Tip.warning('操作提示', '该届次中已存在该党员任职信息');
          }
        }
        let data = {}
        val['electCode'] = elect['code'];
        if (dataInfo['code']) {
          data = {
            ...dataInfo,
            ...val
          }
        } else {
          data = {
            ...val
          }
        }

        this.setState({
          params: data
        })
        this.confirmAgain()

      }
    });
  };
  handleCancel = () => {
    this.props.onClose();
    this.setState({
      visible: false,
      d022CodeDisabled: [],
      newdataInfo: {},
      list: [],
      idCard: '',
      params: {}
    });
  };
  confirmAgain = (pageNum = 1, pageSize = 100) => {
    this.props.dispatch({
      type: 'org/getListByIdcard',
      payload: {
        idcard: this.state.idCard,
        pageNum,
        pageSize,
      }
    }).then(res => {
      this.setState({
        visible1: true,
        list: res?.data?.list || []
      })
    })
  }
  handleOk1 = async () => {
    const { elect, dataInfo = {}, iteListData = [] } = this.props;
    const { params } = this.state;
    let obj = undefined
    if (dataInfo['code']) {
      obj = await this.props.dispatch({
        type: 'org/itteeUp',
        payload: {
          data: {
            ...params
          }
        }
      });
    } else {
      obj = await this.props.dispatch({
        type: 'org/itteeAdd',
        payload: {
          data: {
            ...params
          }
        }
      });
    }

    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', dataInfo['code'] ? '修改成功' : '新增成功');
      this.handleCancel1();
      this.handleCancel();
      this.props.queryList();
    }
  }
  handleCancel1 = () => {
    this.setState({
      visible1: false,

    });
  };
  validatorIdcard = async (rule, value, callback) => {
    if (!value) {
      callback('身份证必填');
    }
    if (value && value.length !== 18 && process.env.idCheck != 'false') {
      callback('身份证应该为18位');
    }
    if (getIdCardInfo(value) === 'Error') {
      callback('身份证格式错误,请核对身份证图片');
    } else {
      // let fieldValue = this.props.form.getFieldValue('memName');
      // let res=await geitCard({idCard:value,name:fieldValue});
      callback()
    }
  };
  getIDinfo = (e) => {
    const { target: { value = '' } = {} } = e || {};
    let info = getIdCardInfo(value);
    if (value != undefined && info !== 'Error') {
      this.props.form.setFieldsValue({
        sexCode: info[2] === '女' ? '0' : '1',
        birthday: info[1] ? moment(info[1], 'YYYY-MM-DD') : undefined,
      });
    }
  };

  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    const { basicInfo = {} } = nextProps.org;
    const state = {};
    const { dataInfo: { d022Code = '' } = {} } = nextProps;
    const { _d022Code } = prevState;
    // 初始化
    if (_d022Code != d022Code) {
      state['_d022Code'] = d022Code;
      if (d022Code) {
        let arr = d022Code.split(',');
        if (basicInfo.d01Code == '61' || `${basicInfo.d01Code}`.startsWith('2')) {
          if (arr.includes('1')) {
            state['d022CodeDisabled'] = ['2', '3', '31', '32', '33', '34', '35'];
          }
          let disable = [];
          if (arr.includes('2') && arr.includes('3')) {
            state['d022CodeDisabled'] = [...disable, '1'];
          }
          if (arr.includes('3')) {
            state['d022CodeDisabled'] = [...disable, '1', '31', '32', '33', '34', '35']
          }
          if (arr.find(it => it.startsWith('3')) && !arr.includes('3')) {
            state['d022CodeDisabled'] = [...disable, '1', '3']
          }
        } else {
          if (arr.includes('1')) {
            state['d022CodeDisabled'] = ['2', '3', '31', '32', '33', '34', '35'];
          } else {
            state['d022CodeDisabled'] = ['1'];
          }
        }
      } else {
        state['d022CodeDisabled'] = [];
      }
    }
    return state;
  }

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    let { children, title, dataInfo = {}, elect = {}, tipMsg = {}, isVillageCommunity = false, isHasMiddleManagement = true, loading: { effects = {} } = {} } = this.props;
    const { newdataInfo = {}, list = [], readonly = false } = this.state;
    dataInfo = { ...dataInfo, ...newdataInfo }
    const org = getSession('org') || {};
    const { basicInfo = {} } = this.props.org;
    // 15.当单位类别为村（社区）以及党组织关联的单位类别为村（社区）的时候，编辑和录入班子成员的时候，增加信息项目：是否村任职选调生（选择框、必填）
    const { linkedDTOList = [], linkedDTOListUpOrg = [] } = basicInfo;
    let unitIsVillage = false;
    if (!_isEmpty([...linkedDTOList].find(it => it?.unit?.d04Code.startsWith('92')))) {
      unitIsVillage = true;
    }
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 58,
        render: (text, record, index) => {
          return index + 1
        }
      },
      {
        title: '职务名称',
        dataIndex: 'd022Name',
      },
      {
        title: '任职开始时间',
        dataIndex: 'startDate',
        render: (text, record) => {
          return (<div>{!_isNumber(text) ? '' : moment(text).format('YYYY-MM-DD')}</div>)
        }
      },
      {
        title: '任职结束时间',
        dataIndex: 'endDate',
        render: (text, record) => {
          return (<div>{!_isNumber(text) ? '' : moment(text).format('YYYY-MM-DD')}</div>)
        }
      },
      // {
      //   title: '职务级别',
      //   dataIndex: 'd51Name',
      // },
      {
        title: '任职所在党组织/单位名称',
        dataIndex: 'positionOrgName',
      },
    ];
    return (
      <React.Fragment>
        {
          children ? React.cloneElement(children as any, {
            onClick: () => {
              this.showModal(false)
            },
            key: 'container'
          }) : null
        }
        {
          this.state.visible &&
          <Modal
            title={title || "请输入标题"}
            visible={this.state.visible}
            onOk={() => this.handleOk()}
            onCancel={this.handleCancel}
            width={1200}
            className='add_member_modal'
            maskClosable={false}
            footer={
              <Fragment>
                <Button key="back" onClick={this.handleCancel}>取消</Button>
                {!readonly && <Button key="submit" type="primary" onClick={() => this.handleOk()}>确定</Button>}
              </Fragment>
            }
          >
            <Form {...formItemLayout}>
              <Row>
                <Col span={24}>
                  <FormItem
                    label={formLabel('任职党组织名称', tipMsg['orgName'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('orgName', {
                      initialValue: basicInfo['name'], // 取值：基本信息里的 组织全称
                      rules: [{ required: true, message: '请输入任职党组织名称' }],
                    })(
                      <Input placeholder={'任职党组织名称'} disabled />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={formLabel('是否本党组织党员', tipMsg['memTypeCode'])}
                  >
                    {getFieldDecorator('memTypeCode', {
                      // valuePropName:'checked',
                      initialValue: dataInfo['memTypeCode'] !== undefined ? +dataInfo['memTypeCode'] : 1,
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <Select placeholder={'请选择'} disabled={readonly}>
                        <Select.Option value={1}>是</Select.Option>
                        <Select.Option value={0}>否</Select.Option>
                      </Select>
                    )}
                  </FormItem>
                </Col>
                {
                  (function (_this) {
                    const { props } = _this;
                    const memTypeCode = props.form.getFieldValue('memTypeCode');
                    return (
                      <React.Fragment>
                        {
                          memTypeCode == 0 ? <React.Fragment>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('人员姓名', tipMsg['memName'])}
                              >
                                {getFieldDecorator('memName', {
                                  initialValue: dataInfo['memName'],
                                  rules: [{ required: true, message: '请输入人员姓名' }],
                                })(
                                  <Input placeholder="请输入人员姓名" disabled={readonly} />
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('性别', tipMsg['sexCode'])}
                              >
                                {getFieldDecorator('sexCode', {
                                  initialValue: dataInfo['sexCode'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <Radio.Group disabled={readonly}>
                                    <Radio value={'1'}>男</Radio>
                                    <Radio value={'0'}>女</Radio>
                                  </Radio.Group>
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('身份证', tipMsg['memIdcard'])}
                              >
                                {getFieldDecorator('memIdcard', {
                                  initialValue: dataInfo['memIdcard'],
                                  rules: [
                                    { required: true, message: '请输入身份证' },
                                    { validator: _this.validatorIdcard },
                                  ],
                                })(
                                  <Input placeholder="请输入身份证" onBlur={_this.getIDinfo} disabled={readonly} />
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('出生日期', tipMsg['birthday'])}
                              >
                                {getFieldDecorator('birthday', {
                                  initialValue: dataInfo['birthday'] !== undefined ? moment(dataInfo['birthday'] * 1) : undefined,
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <Date disabled={readonly} />
                                )}
                              </FormItem>
                            </Col>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('政治面貌', tipMsg['d89Code'])}
                              >
                                {getFieldDecorator('d89Code', {
                                  rules: [{ required: true, message: '政治面貌' }],
                                  initialValue: _isEmpty(dataInfo) ? undefined : dataInfo['d89Code'],
                                })(
                                  <DictSelect backType={'object'}
                                    // ref={e=>_this['politicsCode'] = e}
                                    initValue={_isEmpty(dataInfo) ? undefined : dataInfo['d89Code'] ? dataInfo['d89Code'] : undefined}
                                    codeType={'dict_d89'}
                                    placeholder="请选择"
                                    disabled={readonly}
                                  // mode={'multiple'}
                                  // filter={(data)=>{
                                  //   if(bigThan28){
                                  //     return data.filter(it=>it.key !== '12');
                                  //   }
                                  //   return data;
                                  // }}
                                  // onChange={(e)=>{ handlePoliticsCodeChange && handlePoliticsCodeChange(e)
                                  // }}
                                  />
                                )}
                              </FormItem>
                            </Col>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('学历情况', tipMsg['d07Code'])}
                              >
                                {getFieldDecorator('d07Code', {
                                  initialValue: dataInfo['d07Code'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <DictTreeSelect backType={'object'} codeType={'dict_d07'} initValue={dataInfo['d07Name']} placeholder="请选择" parentDisable={true} disabled={readonly} />
                                )}
                              </FormItem>
                            </Col>
                          </React.Fragment> : <Col span={12}>
                            <FormItem
                              label={formLabel('党员姓名', tipMsg['memCode'])}
                            >
                              {getFieldDecorator('memCode', {
                                initialValue: dataInfo['memCode'],
                                rules: [{ required: true, message: '请选择' }],
                              })(
                                <MemSelect initValue={dataInfo['memName']} placeholder="请选择党员" disabled={readonly} onChange={(e) => {
                                  // 党组织班子新增选择党员后调用接口查找党员相关信息回显：
                                  _this.getSelectMemInfo(e)

                                }} />
                              )}
                            </FormItem>
                          </Col>
                        }
                      </React.Fragment>
                    )
                  })(this)
                }
                {/* <Col span={12}>
                  <FormItem
                    label={ formLabel('职务',tipMsg['currentPositionJob']) }
                  >
                    {getFieldDecorator('currentPositionJob', {
                      initialValue:dataInfo['currentPositionJob'],
                      rules: [{ required: true, message: '职务' }],
                    })(
                      <Input placeholder="职务" maxLength={100}/>
                    )}
                  </FormItem>
                </Col> */}
                <Col span={12}>
                  <FormItem
                    label={formLabel('党内职务', tipMsg['d022Code'])}
                  >
                    {
                      getFieldDecorator('d022Code', {
                        initialValue: dataInfo['d022Code'],
                        rules: [{ required: true, message: '请选择党内职务' }],
                      })(
                        <DictTreeSelect
                          ref={(e) => this['d022Code'] = e}
                          treeCheckable={true}
                          initValue={dataInfo['d022Code'] ? dataInfo['d022Code'].split(',') : undefined}
                          // backType={'object'}
                          codeType={'dict_d22'}
                          placeholder="请选择"
                          disabled={readonly}
                          parentDisable={(basicInfo.d01Code == '61' || `${basicInfo.d01Code}`.startsWith('2')) ? false : true}
                          // parentDisable={basicInfo.d01Code == '61' ? false : true}
                          itemsDisabled={this.state.d022CodeDisabled}
                          extendProps={{ treeCheckStrictly: basicInfo.d01Code == '61' || `${basicInfo.d01Code}`.startsWith('2') }}
                          onChange={
                            // [1.]书记单选，[2.3.]其他两类能混合选。d01Code='61'（党委）时，能分别选3和3的子节点

                            // 1.党委61（城市街道工委，2开头得），911；2.党总支62，921；3党支部63开头，931，932；
                            // 1.种情况：
                            // 值勾选1，（字典代码表2，3，31-35都不可选），
                            // 值勾选2（字典代码表1不可选，（31-35可以多选且3不可选）或（勾选3且不可选31-35）），
                            // 值勾选3，（字典代码表1，31-35不可选，2可选，）
                            // 值勾选31-35勾选多选，（字典代码表3和1不可选，2可选）
                            // 2，3情况：和以前是一样得
                            (e) => {
                              e = e.map(it => {
                                if (typeof it != 'string') {
                                  return it.value
                                }
                                return it
                              });
                              if (e && e.length > 0) {
                                if (basicInfo.d01Code == '61' || `${basicInfo.d01Code}`.startsWith('2')) {
                                  let disabled: any = [];
                                  if (e.includes('1')) {
                                    disabled = ['2', '3', '31', '32', '33', '34', '35'];
                                  }
                                  if (e.includes('2')) {
                                    disabled = [...disabled, '1']
                                  }
                                  if (e.includes('3')) {
                                    disabled = [...disabled, '1', '31', '32', '33', '34', '35']
                                  }
                                  if (e.find(it => it.startsWith('3')) && !e.includes('3')) {
                                    disabled = [...disabled, '1', '3']
                                  }
                                  this.setState({ d022CodeDisabled: disabled })
                                } else {
                                  if (e.includes('1')) {
                                    this.setState({ d022CodeDisabled: ['2', '3', '31', '32', '33', '34', '35'] })
                                  }
                                  if (!e.includes('1')) {
                                    this.setState({ d022CodeDisabled: ['1'] })
                                  }
                                }
                              } else {
                                this.setState({ d022CodeDisabled: [] })
                              }
                            }
                          }
                        />
                      )}
                  </FormItem>
                </Col>

                {
                  (function (_this) {
                    let val = _this.props.form.getFieldValue('d022Code');
                    if (typeof val == 'object') {
                      val && (val = val['key']);
                    }
                    if (val == '1' && isHasMiddleManagement) {
                      return (
                        <Col span={12}>
                          <FormItem
                            label={formLabel('是否中层管理人员', tipMsg['hasMiddleManagement'])}
                          >
                            {getFieldDecorator('hasMiddleManagement', {
                              valuePropName: 'checked',
                              initialValue: dataInfo['hasMiddleManagement'] !== undefined ? dataInfo['hasMiddleManagement'] == 1 : false,
                              rules: [{ required: true, message: '请选择' }],
                            })(
                              <Switch disabled={readonly} />
                            )}
                          </FormItem>
                        </Col>
                      )
                    }
                  })(this)
                }

                {/*<Col span={24}>*/}
                {/*  <FormItem*/}
                {/*    label={ formLabel('职务级别',tipMsg['d51Code']) }*/}
                {/*    {...formItemLayout1}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('d51Code', {*/}
                {/*      initialValue:dataInfo['d51Code'],*/}
                {/*      rules: [{ required: true, message: '请选择职务级别' }],*/}
                {/*    })(*/}
                {/*      <DictTreeSelect initValue={dataInfo['d51Code']} backType={'object'} codeType={'dict_d51'} placeholder="请选择" parentDisable={true}/>*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                <Col span={12}>
                  <FormItem
                    label={formLabel('党内任职起始日期', tipMsg['startDate'])}
                  >
                    {getFieldDecorator('startDate', {
                      initialValue: dataInfo['startDate'] ? moment(dataInfo['startDate']) : elect['tenureStartDate'] ? moment(elect['tenureStartDate']) : undefined,
                      rules: [{ required: true, message: '请选择党内任职起始日期' }],
                      // <DatePicker placeholder="请选择" style={{width:'100%'}}/>
                    })(

                      <Date disabled={readonly} />
                    )}
                  </FormItem>
                </Col>
                {/*<Col span={12}>*/}
                {/*  <FormItem*/}
                {/*    label={ formLabel('党内任职结束日期',tipMsg['endDate']) }*/}
                {/*  >*/}
                {/*    {getFieldDecorator('endDate', {*/}
                {/*      initialValue:dataInfo['endDate'] ? moment(dataInfo['endDate']) : elect['tenureEndDate'] ? moment(elect['tenureEndDate']) : undefined,*/}
                {/*      rules: [{ required: true, message: '请选择党内任职结束日期' }],*/}
                {/*      // <DatePicker placeholder="请选择" style={{width:'100%'}}/>*/}
                {/*    })(*/}
                {/*      <Date />*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                <Col span={12}>
                  <FormItem
                    label={formLabel('决定或批准任职的文号', tipMsg['fileNumber'])}
                  >
                    {getFieldDecorator('fileNumber', {
                      initialValue: dataInfo['fileNumber'],
                      rules: [{ required: false, message: '请输入决定或批准任职的文号' }],
                    })(
                      <Input placeholder="决定或批准任职的文号" disabled={readonly} />
                    )}
                  </FormItem>
                </Col>

                {
                  isVillageCommunity &&
                  <Fragment>
                    <Col span={12}>
                      <FormItem
                        label={formLabel('是否参加城镇职工养老保险', tipMsg['endowmentInsuranceForUrbanEmployees'])}
                      >
                        {getFieldDecorator('endowmentInsuranceForUrbanEmployees', {
                          initialValue: _isEmpty(dataInfo) ? undefined : dataInfo['endowmentInsuranceForUrbanEmployees'],
                          rules: [{ required: true, message: '是否参加城镇职工养老保险' }],
                        })(
                          <Select style={{ width: '100%' }} disabled={readonly}>
                            <Select.Option value={1}>是</Select.Option>
                            <Select.Option value={0}>否</Select.Option>
                          </Select>
                        )}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label={formLabel('人员来源', tipMsg['d121Code'])}
                      >
                        {getFieldDecorator('d121Code', {
                          initialValue: dataInfo['d121Code'],
                          rules: [{ required: true, message: '人员来源' }],
                        })(
                          <DictTreeSelect initValue={_isEmpty(dataInfo) ? undefined : _isEmpty(dataInfo['d121Code']) ? undefined : dataInfo['d121Code'].split(',')}
                            backType={'object'}
                            codeType={'dict_d121'}
                            placeholder="请选择" parentDisable={true}
                            disabled={readonly}
                          // treeCheckable={true}
                          />
                        )}
                      </FormItem>
                    </Col>

                    <Col span={12}>
                      <FormItem
                        label={formLabel('是否参加县级集中轮训', tipMsg['hasPartTraining'])}
                      >
                        {getFieldDecorator('hasPartTraining', {
                          initialValue: dataInfo['hasPartTraining'],
                          rules: [{ required: true, message: '是否参加县级集中轮训' }],
                        })(
                          <Select disabled={readonly}>
                            <Select.Option value={1}>是</Select.Option>
                            <Select.Option value={0}>否</Select.Option>
                          </Select>
                        )}
                      </FormItem>
                    </Col>
                  </Fragment>
                }
                {
                  unitIsVillage &&
                  <Fragment>
                    {/* <Col span={12}>
                      <FormItem
                        label={formLabel('是否村任职选调生', tipMsg['hasVillageTransferStudent'])}
                      >
                        {getFieldDecorator('hasVillageTransferStudent', {
                          initialValue: dataInfo['hasVillageTransferStudent'],
                          rules: [{ required: true, message: '请选择' }],
                        })(
                          <YN init={dataInfo['hasVillageTransferStudent']} disabled={readonly} />
                        )}
                      </FormItem>
                    </Col> */}
                    {/* { //6、是否村任职选调生 选择是的时候，要弹出来一个填写框：选调单位层级
                      getFieldValue('hasVillageTransferStudent') == 1 &&
                      (
                        <React.Fragment>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('选调单位层级', tipMsg['d144Code'])}
                            >
                              {getFieldDecorator('d144Code', {
                                initialValue: dataInfo['d144Code'],
                                rules: [{ required: true, message: '请选择' }],
                              })(
                                <DictTreeSelect backType={'object'} codeType={'dict_d144'} initValue={dataInfo['d144Code']} placeholder="请选择" parentDisable={true} disabled={readonly} />
                              )}
                            </FormItem>
                          </Col>
                          是否村任职选调生 选择是的时候，要弹出来一个填写框：双一流大学
                          <Col span={12}>
                            <FormItem
                              label={formLabel('是否双一流大学生', tipMsg['isDoubleFirst'])}
                            >
                              {getFieldDecorator('isDoubleFirst', {
                                initialValue: dataInfo['isDoubleFirst'],
                                rules: [{ required: true, message: '请选择' }],
                              })(
                                <YN init={dataInfo['isDoubleFirst']} disabled={readonly} />
                              )}
                            </FormItem>
                          </Col>
                        </React.Fragment>
                      )
                    } */}
                    <Col span={12}>
                      <FormItem
                        label={formLabel('报酬（万元/年）', tipMsg['reward'])}
                      >
                        {getFieldDecorator('reward', {
                          initialValue: dataInfo['reward'],
                          rules: [{ required: true, message: '报酬（万元/年）' }],
                        })(
                          <InputNumber style={{ width: '100%' }} min={0} precision={2} />
                        )}
                      </FormItem>
                    </Col>
                  </Fragment>
                }

                {
                  // 当党组织关联的主单位类别为乡镇（912开头）单位的时候，添加班子成员和编辑班子成员的时候，需要增加一个信息项目：班子成员来源
                  !_isEmpty([...linkedDTOList, ...linkedDTOListUpOrg].find(it => it?.unit?.d04Code.startsWith('912'))) &&
                  <Fragment>
                    {/* <Col span={12}>
                        <FormItem
                          label={formLabel('是否为五方面人员', tipMsg['whetherItIsFromFiveAspects'])}
                        >
                          {getFieldDecorator('whetherItIsFromFiveAspects', {
                            initialValue: _isEmpty(dataInfo) ? 0 : dataInfo['whetherItIsFromFiveAspects'],
                            rules: [{ required: true, message: '是否为五方面人员' }],
                          })(
                            <Select style={{ width: '100%' }}>
                              <Select.Option value={1}>是</Select.Option>
                              <Select.Option value={0}>否</Select.Option>
                            </Select>
                          )}
                        </FormItem>
                      </Col> */}

                    {/* 乡镇班子成员删除属性  是否为五方面人员
                      乡镇班子成员来源  新增 6 非五方面人员 选6时为单选，其余为多选
                      */}

                    <Col span={12}>
                      <FormItem
                        label={formLabel('班子成员来源', tipMsg['d138Code'])}
                      >
                        {getFieldDecorator('d138Code', {
                          initialValue: _isEmpty(dataInfo) ? undefined : dataInfo['d138Code'],
                          rules: [{ required: true, message: '请选择' }],
                        })(
                          <DictTreeSelect
                            initValue={_isEmpty(dataInfo) ? undefined : dataInfo['d138Code'] ? dataInfo['d138Code'].split(',') : undefined}
                            backType={'object'}
                            codeType={'dict_d138'}
                            treeCheckable={true}
                            placeholder="请选择"
                            parentDisable={true}
                            disabled={readonly}
                            filter={(data) => {
                              let selectItem = getFieldValue('d138Code')
                              let itemLastKey = '';
                              if (_isArray(selectItem)) {
                                itemLastKey = selectItem[selectItem.length - 1]?.key || '';
                              }
                              if (typeof selectItem == 'string') {
                                let itemArr = selectItem.split(',');
                                itemLastKey = itemArr[itemArr.length - 1]
                              }
                              if (itemLastKey == '6') {
                                data = data.filter(it => it.key == '6')
                              }
                              if (itemLastKey && (itemLastKey != '6')) {
                                data = data.filter(it => it.key != '6')
                              }
                              return data
                            }}
                          />
                        )}
                      </FormItem>
                    </Col>
                  </Fragment>
                }
                <Col span={24}>
                  <FormItem
                    label={formLabel('党内职务说明', tipMsg['dutyExplain'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('dutyExplain', {
                      initialValue: dataInfo['dutyExplain'],
                      rules: [{ required: false, message: '请输入党内职务说明' }],
                    })(
                      <TextArea rows={4} placeholder={'党内职务说明'} disabled={readonly} />
                    )}
                  </FormItem>
                </Col>
                {/* <Col span={24}>
                  <FormItem
                    label={formLabel('上传头像', tipMsg['photoPath'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('photoPath', {
                      initialValue: dataInfo['photoPath'],
                      rules: [{ required: false, message: '' }],
                    })(
                      <UploadComp action='/api/base/putFile' buttonText='选择图片' accept='.jpg,.png,.jpeg' files={getInitFileList(dataInfo['photoPath'])} maxLen={1} disabled={readonly} />
                    )}
                  </FormItem>
                </Col> */}
              </Row>
            </Form>
            <Modal
              title={"任职情况"}
              visible={this.state.visible1}
              onOk={() => this.handleOk1()}
              onCancel={this.handleCancel1}
              width={1000}
              className='add_member_modal'
              maskClosable={false}
            >
              <ListTable columns={columns} data={list} pagination={false} scroll={{ y: 500 }} disabled={readonly} />
            </Modal>
          </Modal>
        }

      </React.Fragment>
    )
  }
}
export default Form.create<any>()(index);
