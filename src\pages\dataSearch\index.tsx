import React, { Fragment, useRef, useEffect, useState } from 'react';
import { Button, Select, Input, Popconfirm, DatePicker, TreeSelect, Tabs, InputNumber } from 'antd';
import CommonSerchPage from '@/components/CommonSerchPage';
import CustomSearch from '@/components/CustomSearch';
import { tableSelect1, tableAll1,tableSelect,tableAll, combined } from '@/pages/dataSearch/services';
import DictTreeSelect from '@/components/DictTreeSelect';
import moment from 'moment';
import styles from './index.less';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { connect } from 'dva';
import { getSession } from '@/utils/session';
import AddEdit from '@/pages/mem/manage/components/membasic/AddEdit';
import AddDevlop from '@/pages/developMem/develop/components/Add';
import ExportInfo from '@/components/Export';
import { _history as router } from "@/utils/method";
import qs from 'qs';

const TabPane = Tabs.TabPane;
const Option = Select.Option;

const operatorList = [
  {
    label: '不等于',
    rule: '!=',
    value: 'notEqual',
    type: ['NUMBER', 'VARCHAR2', 'DATE', 'BOOLEAN'],
  },
  {
    label: '等于',
    rule: '=',
    value: 'equal',
    type: ['NUMBER', 'VARCHAR2', 'DATE', 'BOOLEAN'],
  },
  {
    label: '小于',
    rule: '<',
    value: 'less',
    type: ['NUMBER', 'DATE'],
  },
  {
    label: '大于',
    rule: '>',
    value: 'greater',
    type: ['NUMBER', 'DATE'],
  },
  {
    label: '小于等于',
    rule: '<=',
    value: 'lessEqual',
    type: ['NUMBER', 'DATE'],
  },
  {
    label: '大于等于',
    rule: '>=',
    value: 'greaterEqual',
    type: ['NUMBER', 'DATE'],
  },
  {
    label: '左包含(值)',
    rule: '左包含',
    value: 'leftLike',
    type: ['VARCHAR2'],
  },
  {
    label: '右包含(值)',
    rule: '右包含',
    value: 'rightLike',
    type: ['VARCHAR2'],
  },
  {
    label: '包含(值)',
    rule: '包含',
    value: 'like',
    type: ['VARCHAR2'],
  },
  {
    label: '包含(值)',
    rule: '包含',
    value: 'in',
    type: ['CODE'],
  },
  {
    label: '不包含(值)',
    rule: '不包含',
    value: 'notIn',
    type: ['CODE'],
  },
  {
    label: '为空',
    rule: 'is null',
    value: 'null',
    type: ['CODE', 'NUMBER', 'VARCHAR2', 'DATE', 'BOOLEAN'],
  },
];

const memTabs = [
  { key: '1', title: '党员信息' },
  { key: '2', title: '发展党员信息' },
];

const index = (props) => {
  const { orgCode = '', code = '' } = getSession('org') || { orgCode: '', code:'' };
  const { query = {} } = props.location || {};

  const searchRef: any = useRef();
  const pageRef: any = useRef();
  const exportInfoRef: any = useRef();
  const addDevlopRef:any = useRef();


  const [treeInfo, setTreeInfo] = useState([]);
  const [codeTableCol, setCodeTableCol] = useState([]);
  const [searchQuery, setSearchQuery] = useState<any>({});

  const transform = (val) => {
    let str = 'VARCHAR2';
    switch (val) {
      case 'lable':
        str = 'CODE';
        break;
      case 'boolean':
        str = 'BOOLEAN';
        break;
      case 'date':
        str = 'DATE';
        break;
        case 'number':
          str = 'NUMBER';
          break;
    }
    return str
  };
  const tableColumns = () => {
    return [
      {
        title: '姓名',
        dataIndex: 'name',
        width: 100,
      },
      {
        title: '性别',
        dataIndex: 'sexCode',
        width: 100,
        render: (text) => {
          return (
            <span> {text === '1' ? '男' : '女'} </span>
          )
        }
      },
      {
        title: '公民身份证',
        dataIndex: 'idcard',
        width: 160,
        render: (text, record) => {
          if (typeof text === 'string' && !_isEmpty(text)) {
            let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
            if (text.indexOf("*") > 0) {
              return text
            }
            return (
              <span>{newVal}</span>
            );
          } else {
            return ''
          }
        }
      },
      {
        title: '电话',
        width: 100,
        dataIndex: 'phone',
        render: (text, record) => {
          return text ? '***********' : ''
        }
      },
      {
        title: '党员类型',
        width: 120,
        dataIndex: 'd08Name',
      },
      {
        title: '所在组织',
        width: 260,
        dataIndex: 'orgName',
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        render: (text, record) => {
          return (
            <span>
              <a onClick={async () => {
                if(query.lockObject == '1'){
                  AddEdit['WrappedComponent'].show();
                  if (record && record['code']) {
                    await props.dispatch({
                      type: 'memBasic/findMem',
                      payload: {
                        code: record['code']
                      }
                    })
                  }
                }
                if(query.lockObject == '2'){
                  const {canEdit:_canEdit = []} = props;
                  addDevlopRef.current.destroy();
                  if (record && record['code']) {
                    await props.dispatch({
                      type: 'memDevelop/findMem',
                      payload: {
                        code: record['code'],
                        type:_get(_canEdit,'[0]',undefined) == 'mems' ? '2' : undefined,
                      }
                    })
                  }

                  addDevlopRef.current.open({ canEdit:true, editType:'default' });
                }

              }}>编辑</a>
            </span>
          )
        },
      },
    ];
  };
  const getDict = async () => {
    const res = await tableSelect({});
    const res3 = await tableAll({});
    if (res['code'] == 0) {
      setTreeInfo(res['data']);
    }

    if (res3['code'] == 0) {
      let data: any = [];
      for (let k in res3['data']) {
        const item = res3['data'][k];
        if (Array.isArray(item)) {
          for (let obj of item) {
            data.push({
              ...obj,
              tableName: k,
            })
          }
        }
      }
      setCodeTableCol(data)
    }
  };
  // 导出
  const exportInfo = async () => {
    exportInfoRef.current.open();
  };
  const otherBtnsFunc = ({ list, keyword }: any = {}) => {
    return (
      <Fragment>
        <Button onClick={() => {
          searchRef.current.open();
        }}>自定义查询条件</Button>
        <Button htmlType={'button'} onClick={exportInfo} disabled={_isEmpty(list)}>导出</Button>
      </Fragment>
    )
  };
  const getSearchCallBack = (val, ohter) => {
    setSearchQuery({ queryDtos: val, orgLevelCode: orgCode, type:query.lockObject,orgCode:code,...ohter });
    return { code: 0, value: { queryDtos: val, pageNum: 1, orgLevelCode: orgCode, type:query.lockObject,...ohter } };
  }
  const onSearch = (val, other) => {
    const { list = [] } = val || { list: [] };
    pageRef.current.search(list, other);
  }
  const itemInfoChange = (val, record, index, list, setList) => {
    let data = [...list];
    const item: any = codeTableCol.filter(obj => obj['tableName'] == record['infoSetName']).find(obj => obj['colCode'] == val);
    data[index]['infoItem'] = val;
    if (item) {
      data[index]['infoItemName'] = item['colName'];
      data[index]['infoItemCode'] = item['colLectionCode'];
      data[index]['infoItemType'] = item['colType'];
    } else {
      data[index]['infoItemName'] = '';
      data[index]['infoItemCode'] = '';
      data[index]['infoItemType'] = '';
    }
    data[index]['value'] = undefined;
    data[index]['operator'] = undefined;
    setList(data);
  }
  const renderTableCol = (p?) => {
    const { valChange, del, valRest, setList, list } = p || {};
    return [
      {
        title: '左括号',
        dataIndex: 'leftBrackets',
        width: 60,
        render: (text, record, index) => {
          return (
            <Select allowClear size={'small'} style={{ width: 50 }} value={text} onChange={(val) => valChange(val, 'leftBrackets', index)}>
              <Option value="leftBrackets">{'('}</Option>
            </Select>
          );
        },
      },
      {
        title: '信息集',
        dataIndex: 'infoSetValue',
        width: 160,
        render: (text, record, index) => {
          return (
            <Select className={styles.select} style={{ width: 150 }} allowClear size={'small'} value={record['infoSetValue']} onChange={(value, option) => {
              valRest(undefined, 'infoItem', index);
              valRest(undefined, 'infoItemName', index);
              valRest(undefined, 'operator', index);
              valRest(undefined, 'value', index);
              valRest(undefined, 'compareNames', index);
              valChange(value, 'infoSetValue', index);
              let find = treeInfo.find(obj => obj['tableCode'] == value);
              if (find) {
                valChange(find['tableName'], 'infoSetName', index);
              }
            }}>
              {
                treeInfo.map(i => {
                  return (
                    <Option className={styles.select} value={i['tableCode']} key={i['tableCode']}>{i['tableName']}</Option>
                  );
                })
              }
            </Select>
          );
        },
      },
      {
        title: '信息项',
        dataIndex: 'infoItem',
        width: 160,
        render: (text, record, index) => {
          let arr = codeTableCol.filter(obj => obj['tableName'] == record['infoSetName']);
          return (
            <Select
              className={styles.select}
              style={{ width: 150 }}
              allowClear
              size={'small'}
              value={record['infoItem']}
              onChange={(val, option) => {
                itemInfoChange(val, record, index, list, setList)
              }}
            >
              {
                arr.map((i: any,) => {
                  return (
                    <Option className={styles.select} value={i.colCode} key={i.id} >{i['colName']}</Option>
                  );
                })
              }
            </Select>
          );
        },
      },
      {
        title: '运算符',
        dataIndex: 'operator',
        width: 100,
        render: (text, record, index) => {
          operatorList.map(it => {
          })

          return (
            <Select allowClear size={'small'} style={{ width: 90 }}
              value={record['operator']}
              onChange={(val) => {
                valChange(val, 'operator', index);
              }}
            >
              {
                operatorList.filter(i => i.type.includes(transform(record.infoItemType))).map(i => {
                  return (
                    <Option className={styles.select} value={i.value} key={i.value}>{i.label}</Option>
                  );
                })
              }
            </Select>
          );
        },
      },
      {
        title: '值',
        dataIndex: 'value',
        width: 360,
        render: (text, record, index) => {
  
          if (record['operator'] === 'null') {
            return (
              <Input
                style={{ width: '100%' }}
                disabled
              />
            );
          }
          if (record['infoItemType'] === 'number') {
            return (
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                
              />
            );
          }
          if (record['infoItemType'] === 'lable') {
            return (
              <div key={`${record['infoItemCode']}_${index}`} style={{ width: '100%' }}>
                <DictTreeSelect
                  showConstant={false}
                  extendProps={{ showCheckedStrategy: TreeSelect.SHOW_PARENT }}
                  treeCheckable={true}
                  initValue={record['value'] ? record['value'].split(',') : undefined}
                  codeType={record['infoItemCode']}
                  size={'small'}
                  placeholder={record['remark']}
                  style={{ width: '100%' }}
                  onChange={(val, label, extra) => {
                    console.log('难道初始化执行了change')
                    if (val.length > 0) {
                      const data = props['commonDict'][`${record['infoItemCode']}`];
                      let names: any = [];
                      let array = val;
                      data.filter(i => val.includes(i.key)).forEach(i => {
                        names.push(i.name)
                      });
                      array.forEach(key => {
                        data.filter(i => `${i.key}`.startsWith(key)).forEach(i => {
                          array.push(i.key);
                        });
                      });
                      array = array.concat(val);
                      array = [...new Set(array)];
                      names = [...new Set(names)];
                      if (Array.isArray(val)) {
                        valChange(array.toString(), 'value', index);
                      } else {
                        valChange([val].toString(), 'value', index);
                      }
                      valChange(names.toString(), 'compareNames', index);
                    } else {
                      valChange([].toString(), 'value', index);
                      valChange([].toString(), 'compareNames', index);
                    }
                  }}
                />
              </div>
            );
          }
          if (record['infoItemType'] == 'date') {
            return (
              <DatePicker
                value={text && text.length > 0 ? moment(text) : undefined}
                onChange={(val: any) => {
                  valChange([val.format('YYYY.MM.DD')].toString(), 'value', index);
                  valChange([val.format('YYYY.MM.DD')].toString(), 'compareNames', index);
                }}
                style={{ width: '100%' }}
              />
            )
          }
          if (record['infoItemType'] == 'boolean') {
            return (
              <Select
                // value={record['value'] && record['value'].length > 0 ? record['value'][0] : undefined}
                value={text && text.length > 0 ? text : undefined}
                onChange={(val) => {
                  valChange([val].toString(), 'value', index);
                  valChange([val == '1' ? '是' : '否'].toString(), 'compareNames', index);
                }}
                placeholder={'请选择'}
                style={{ width: '100%' }}
              >
                <Select.Option value={'1'}>是</Select.Option>
                <Select.Option value={'2'}>否</Select.Option>
              </Select>
            )
          }
          return (
            <Input
              style={{ width: '100%' }}
              allowClear
              size={'small'}
              type={record['value'] == '年龄' ? "number" : 'text'}
              value={record['value'] == '年龄' ? record['ageValue'] : record['value']}
              placeholder={record['remark']}
              onChange={(val) => {
                valChange([val.target.value].toString(), 'value', index);
                valChange([val.target.value].toString(), 'compareNames', index);
              }}
            />
          );
        },
      },
      {
        title: '右括号',
        dataIndex: 'rightBrackets',
        width: 60,
        render: (text, record, index) => {
          return (
            <Select className={styles.select} allowClear size={'small'} style={{ width: 50 }} value={text} onChange={(val) => valChange(val, 'rightBrackets', index)}>
              <Option className={styles.select} value="rightBrackets">{')'}</Option>
            </Select>
          );
        },
      },
      {
        title: '关系',
        dataIndex: 'relation',
        width: 90,
        render: (text, record, index) => {
          return (
            <Select className={styles.select} allowClear size={'small'} style={{ width: 75 }} value={text} onChange={(val) => valChange(val, 'relation', index)}>
              <Option className={styles.select} value="and">并且</Option>
              <Option className={styles.select} value="or">或者</Option>
            </Select>
          );
        },
      },
      {
        title: '操作',
        dataIndex: '8',
        width: 60,
        render: (text, record) => {
          return (
            <Popconfirm title={'是否确认删除？'} onConfirm={() => del(record._key)}>
              <a className={'del'}>删除</a>
            </Popconfirm>
          );
        },
      },
    ]
  }
  const getDesc = (data, setMsg) => {
    let msg: any = [];
    for (const obj of data) {
      if (obj['infoSetName']) {
        let str = '';
        if (obj.leftBrackets) {
          str += '(';
        }
        str += `${obj['infoSetName']} `;
        if (obj['infoItemName']) {
          str += `${obj['infoItemName']} `;
        }
        if (obj['operator']) {
          let find = operatorList.find(ob => ob['value'] == obj['operator']);
          if (find) {
            str += `${find['label']} `;
          }
        }
        if (obj['compareNames']) {
          str += `${obj['compareNames']} `;
        }
        if (obj.rightBrackets) {
          str += ')';
        }
        if (obj.relation) {
          str += ` ${obj.relation} `;
        }
        obj['desc'] = str;
        msg.push(str);
      }
    }
    setMsg(msg);
  }
  const tabChange = (val) => {
    const { query } = props.location;
    setSearchQuery([]);
    pageRef.current.restForm();
    router.push(`?${qs.stringify({ ...query, lockObject: val })}`)
  }

  useEffect(() => {
    getDict()
  }, [])
  return (
    <Fragment>
      {/* <Tabs activeKey={query.lockObject} onChange={tabChange}>
        {
          !_isEmpty(memTabs) && memTabs.map(item => <TabPane tab={item['title']} key={item['key']} />)
        }
      </Tabs> */}
      <CommonSerchPage
        ref={pageRef}
        isDefaultForm={false}
        {...props}
        tableColumns={tableColumns}
        otherBtnsFunc={otherBtnsFunc}
        getSearchCallBack={getSearchCallBack}
        tableAction={combined}
      />
      <CustomSearch ref={searchRef} onOk={onSearch} renderTableCol={renderTableCol} getDesc={getDesc} />
      <AddEdit destroy={() => onSearch({list:searchQuery.queryDtos }, { pageNum: 1 })} menuDataKey={['1']} />
      <AddDevlop wrappedComponentRef={addDevlopRef} onsubmit={()=>{}} {...props} tipMsg={{}} />
      <ExportInfo
        wrappedComponentRef={exportInfoRef}
        // wrappedComponentRef={e => this['memExportInfo'] = e}
        tableName={query.lockObject == '1'? 'ccp_mem' :'ccp_develop_step_log'}
        tableListQuery={{ ...searchQuery, type: query.lockObject }}
        action={'/api/search/exportCombined'}
      />


    </Fragment>
  )
}

export default connect(({ commonDict, memBasic, memDevelop }: any) => ({ commonDict, memBasic, memDevelop}), undefined, undefined, { forwardRef: true })(React.forwardRef(index))
