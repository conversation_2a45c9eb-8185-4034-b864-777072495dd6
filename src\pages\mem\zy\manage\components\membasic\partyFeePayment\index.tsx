/**
 * 党费交纳情况
 */
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Input, Select, Form, Modal, Upload, Col, Row } from 'antd';
import ListTable from '@/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 13 },
  },
};
const index = (props: any) => {
  const [form] = Form.useForm();
  const [listLoading, setListLoading] = useState(false);
  const [pagination, setPagination] = useState<any>({ pageSize: 20, current: 1, total: 0 });
  const [listData, setListData] = useState([]);
  const columns = [
    // {
    //   title: '序号',
    //   dataIndex: 'num',
    //   width: 50,
    //   align: 'center',
    //   render: (text, record, index) => {
    //     return (pagination['current'] - 1) * pagination['pageSize'] + index + 1;
    //   },
    // },
    {
      title: '一月',
    //   width: 100,
      align: 'center',
      dataIndex: 'year',
    },
    {
        title: '二月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
      {
        title: '三月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
      {
        title: '四月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
      {
        title: '五月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
      {
        title: '六月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
      {
        title: '七月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
      {
        title: '八月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
      {
        title: '九月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
      {
        title: '十月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
      {
        title: '十一月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
      {
        title: '十二月',
      //   width: 100,
        align: 'center',
        dataIndex: 'year',
      },
  ];

  const hadndleFinish = (vals: any) => {};
  return (
    <div>
      <div>
        <div style={{ display:'flex', justifyContent:'space-between' }}>
          <div style={{display:'flex'}}>
            <div style={{marginRight:'30px'}}>
              党员姓名：张三
            </div>
            <div style={{marginRight:'30px'}}>
              所在组织：XXXXXXXXXXXX党支部
            </div>
            <div style={{marginRight:'30px'}}>
              起交时间：2022-04
            </div>
          </div>
          <div>
          <Select style={{ width: '90px' }} defaultValue="2022">
                <Select.Option value={'2018'}>2018</Select.Option>
                <Select.Option value={'2019'}>2019</Select.Option>
                <Select.Option value={'2020'}>2020</Select.Option>
                <Select.Option value={'2021'}>2021</Select.Option>
                <Select.Option value={'2022'}>2022</Select.Option>
              </Select>
          </div>
        </div>
        {/* <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
          <Row>
            <Col span={4}>
              <Form.Item label="党员姓名" name={'aaa'}>
                <div>张三</div>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="所在组织" name={'aaa'}>
                <div>XXXXX党支部</div>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="起交时间" name={'aaa'}>
                <div>2022-04</div>
              </Form.Item>
            </Col>
            <Col span={8} style={{ textAlign: 'right' }}>
              <Select style={{ width: '90px' }} defaultValue="2022">
                <Select.Option value={'2018'}>2018&nbsp;&nbsp;</Select.Option>
                <Select.Option value={'2019'}>2019&nbsp;&nbsp;</Select.Option>
                <Select.Option value={'2020'}>2020&nbsp;&nbsp;</Select.Option>
                <Select.Option value={'2021'}>2021&nbsp;&nbsp;</Select.Option>
                <Select.Option value={'2022'}>2022&nbsp;&nbsp;</Select.Option>
              </Select>
            </Col>
          </Row>
        </Form> */}
        <WhiteSpace/>
        <ListTable
          
          columns={columns}
          data={listData}
          pagination={pagination}
          //   onPageChange={onPageChange}
          rowKey={'code'}
        />
      </div>
    </div>
  );
};

export default index;
