.flow {
    // padding: 5px 0;
    // padding-top: 14px;
    // border: 1px solid #D8D8D8;
    width: 1654px;
    height: 210px;
    position: relative;
    box-sizing: border-box;
.label {
    margin-right: 10px;
    border-radius: 4px;
    opacity: 1;
    display: inline-block;
    padding: 10px;
    background: rgba(24, 144, 255, 0.1);
    box-sizing: border-box;
    border: 1px solid #1890FF;
    font-family: Source <PERSON>;
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0em;
    color: #1890FF;
    vertical-align: middle;
    cursor: pointer;
}
    .btn {
        border-radius: 4px;
        opacity: 1;
        display: inline-block;
        padding: 10px;
        background: rgba(24, 144, 255, 0.1);
        box-sizing: border-box;
        border: 1px solid #1890FF;
        font-family: Source <PERSON>;
        font-size: 16px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0em;
        color: #1890FF;
        vertical-align: middle;
        cursor: pointer;
        text-align: center;
    }
    .btn1 {
        border-radius: 4px;
        opacity: 1;
        display: inline-block;
        padding: 10px;
        background: rgba(244, 150, 9, 0.1);
        box-sizing: border-box;
        border: 1px solid #F49609;
        font-family: Source Han Sans;
        font-size: 16px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0em;
        color: #F49609;
        vertical-align: middle;
        cursor: pointer;
        text-align: center;
    }
    .btn2 {
        border-radius: 4px;
        opacity: 1;
        display: inline-block;
        padding: 10px;
        background: rgba(255, 0, 0, 0.1);
        box-sizing: border-box;
        border: 1px solid #FF0000;
        font-family: Source Han Sans;
        font-size: 16px;
        font-weight: 500;
        line-height: normal;
        letter-spacing: 0em;
        color: #FF0000;
        vertical-align: middle;
        cursor: pointer;
        text-align: center;
    }
    .btnbox {
        display: inline-block;
        border-radius: 4px;
        opacity: 1;
        padding: 28px 20px;
        box-sizing: border-box;
        border: 1px solid #D8D8D8;
        position: relative;
    }
    .bordertext {
        display: inline-block;
        background: #1890FF;
        color: #fff;
        border-radius: 32px;
        position: absolute;
        top: -12px;
        left: 310px;
        padding: 0 4px;
        // transform: translateX(-50%);
    }
    .bordertext1 {
        display: inline-block;
        padding: 0 4px;
        background: #fff;
        color: #1890FF;
        position: absolute;
        bottom: 33px;
        left: 600px;
        // transform: translateX(-50%);
    }
    .bordertext2 {
        color: #1890FF;
        position: absolute;
        bottom: 28px;
        left: 16px;
    }
    .bordertext3 {
        position: absolute;
        top: 5px;
        top: -13px;
        left: 440px;
        font-size: 18px;
        font-weight: 500;
        background: #1890FF;
        color: #fff;
        border-radius: 32px;
        padding: 2px 5px;
        font-size: 14px;
    }
    .bordertext4 {
        position: absolute;
        top: -1px;
        left: 50%;
        transform: translateX(-50%);
        font-weight: 500;
        background: #1890FF;
        color: #fff;
        border-bottom-left-radius: 10px;
        padding: 0px 5px;
        border-bottom-right-radius: 10px
    }
    .bordertext5 {
        width: 100%;
        text-align: center;
        position: absolute;
        bottom: 0;
        color: #1890FF;
        font-size: 12px;
    }
    .bordertext6 {
        position: absolute;
        top: -7px;
        left: 98px;
        color: #1890FF;
        font-size: 12px;
        background: #fff;
    }
    .bordertext7 {
        position: absolute;
        top: 4px;
        left: 68px;
        color: #1890FF;
        font-size: 12px;
    }
    .bordertext8 {
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        color: #fff;
        background: #1890FF;
        font-size: 14px;
        writing-mode: vertical-rl;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        height: 60px;
        display: flex;
        justify-content: center;
    }
    .btnbox1 {
        display: inline-block;
        vertical-align: middle;
    }
    .btnbox2 {
        display: inline-block;
        border-radius: 4px;
        opacity: 1;
        padding:  25px 8px;
        box-sizing: border-box;
        border: 1px solid #D8D8D8;
        position: relative;
    }
    .btnbox3 {
        display: inline-block;
        border-radius: 4px;
        opacity: 1;
        padding: 25px 15px 10px 15px;
        box-sizing: border-box;
        border: 1px solid #D8D8D8;
        position: relative;
    }
    .btnbox4 {
        display: inline-block;
        border-radius: 4px;
        opacity: 1;
        padding:  25px 10px 10px 33px;
        box-sizing: border-box;
        border: 1px solid #D8D8D8;
        position: relative;
    }
    .line {
        vertical-align: middle;
        display: inline-block;
        flex: 1;
        position: relative;
        >img {
            width: 100%;
            height: 100%;
         } 
        .linec {
            position: relative;
            bottom: -6px;
        }
        .linec1 {
            position: relative;
            top: -6px;
        }
       
    }
    .lineCorner {
        display: inline-block;
        flex: 1;
        position: relative;
        top: 6px;
        vertical-align: text-top;
        >img {
            width: 100%;
            height: 100%;
         } 
    }
    .lineCorner1 {
        display: inline-block;
        flex: 1;
        position: relative;
        top: 6px;
        vertical-align: text-top;
        >img {
            width: 100%;
            height: 100%;
         } 
    }
    .linec2 {
        width: 255px;
     position: absolute;     
     top: 22px;
     left: 70px; 
     >img {
        width: 100%;
        height: 100%;
     } 
    }
    .linec3 {
        width: 255px;
        position: absolute; 
        right: 78px;    
        >img {
            width: 100%;
            height: 100%;
         }   
       }
       .linec4 {
        // width: 520px;
        position: absolute; 
        right: 78px;
        bottom: 12px; 
        >img {
            width: 100%;
            height: 100%;
         }   
       }
       .linec5 {
        width: 520px;
        position: absolute; 
        left: 69px;
        bottom: 23px;  
        >img {
            width: 100%;
            height: 100%;
         }   
       }
       .linec6 {
        width: 500px;
        height: 10px;
        display: flex;
        position: absolute; 
        left: 83px;
        bottom: 15px;
        transform: scaleX(-1);
        >img {
            // width: 100%;
            // height: 100%;
            flex: 1;
            object-fit: fill;
         }   
         &::after {
            content: '';
            width: 2px;
            height: 5px;
            position: absolute;
            left: 188px;
            top: 2px;
            background-color: #82abd2;
         }
         &::before {
            content: '';
            width: 2px;
            height: 5px;
            position: absolute;
            left: 339px;
            top: 2px;
            background-color: #82abd2;
         }
       }
       .linec7 {
        width: 115px;
        position: relative;
        >img {
            width: 100%;
            height: 100%;
         }   
       }
       .linec8 {
        position: absolute; 
        left: 60px;
        top: 12px;
        >img {
            width: 100%;
            height: 100%;
         }   
       }
    .bot {
        display: inline-block;
        background: url(../../../../../assets/mem/line8.png) no-repeat;
        height: 180px;
        background-size: 93% 35%;
        background-position-x: 40%;
        background-position-y: 73px;
        position: relative;
    }
    .checkbtn {
        padding: 10px;
        background: #1890FF;
        box-sizing: border-box;
        border: 1px solid #1890FF;
        box-shadow: 0px 4px 10px 0px rgba(24, 144, 255, 0.5);
        color: #fff;
        cursor: pointer;
    }
    .checkbtn1 {
        padding: 10px;
        background: #F49609;
        box-sizing: border-box;
        border: 1px solid #F49609;
        box-shadow: 0px 4px 10px 0px rgba(244, 150, 9, 0.5);
        color: #fff
    }
    .checkbtn2 {
        padding: 10px;
        background: #FF0000;
        box-sizing: border-box;
        border: 1px solid #FF0000;
        box-shadow: 0px 4px 10px 0px rgba(255, 0, 0, 0.5);
        color: #fff
    }
}
.bottombox {
    display: inline-block;
    position: absolute;
    right: 32px;
    bottom: 20px;

}
.bottombox1 {
    display: inline-block;
    position: absolute;
    bottom: 10px;
    right: 35px;
}
.textcss {
 display: inline-block;
 color: #1890FF;
 font-size: 12px;
 padding-left: 10px;
 vertical-align: middle;
}

