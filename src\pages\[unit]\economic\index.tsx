/**
 * 单位管理
 */
import React from 'react';
import {connect} from "dva";
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import {_history as router} from "@/utils/method";
import qs from 'qs';
import AddOrEdit from './components/addoredit';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import {Button, Divider, Input, Popconfirm, Tabs} from 'antd';
import NowOrg from 'src/components/NowOrg';
import {getSession} from "@/utils/session";
import {setListHeight} from "@/utils/method";
import ExportInfo from '@/components/Export/index';
import Tip from '@/components/Tip';

const Search = Input.Search;
const TabPane = Tabs.TabPane;

// @ts-ignore
@connect(({unitIn,commonDict,loading})=>({unitIn,commonDict,loading:loading.effects['unitIn/getList']}))
export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      dataInfo:undefined,
      filter:{},//筛选器
      search:{},//搜索框
    };
  }
  addOrEdit=(record?:object)=>{
    AddOrEdit['WrappedComponent'].clear();
    if(record && record['code']){
      this.props.dispatch({
        type:'unitIn/findOrg',
        payload:{
          code:record['code']
        }
      }).then(res=>{
        AddOrEdit['WrappedComponent'].show();
      });
    }else{
      AddOrEdit['WrappedComponent'].show();
    }
  };

  componentDidMount() {
    setListHeight(this)
  }
  componentWillUnmount(): void {
    this.props.dispatch({
      type:'unitIn/destroy',
    })
  }
  onPageChange=(page,pageSize)=>{
    console.log(page,pageSize);
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  filterChange=(val)=>{
    this.props.dispatch({
      type:'unitIn/updateState',
      payload:{
        filter:val
      }
    });
    this.action();
    // this.setState({
    //   filter:val,
    // },()=>this.action());
  };
  action=(val?:any)=>{
    const {pagination={}}=this.props.unitIn;
    const {current=1,pageSize=10}=pagination;
    const {query}=this.props.location;
    const org=getSession('org') || {};
    this.props.dispatch({
      type:'unitIn/getList2',
      payload:{
        data:{
          orgCode:org['orgCode'],
          pageNum:current,
          pageSize,
          ...query,
          ...val,
        }
      }
    })
  };
  confirm=async (record)=>{
    const {code = ''} = record;
    let { code:resCode = 500 }=await this.props.dispatch({
      type:'unitIn/del2',
      payload:{code}
    });
    if(resCode === 0){
      Tip.success('操作提示', '删除成功');
      this.action();
    }
    // console.log(record,'confirm');
    // let obj=await this.props.dispatch({
    //   type:'unitIn/del',
    //   payload:{}
    // });
  };
  search=(value)=>{
    this.props.dispatch({
      type:'unitIn/updateState',
      payload:{
        unitName:value
      }
    });
    const {pagination={}}=this.props.unitIn;
    const {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:1,pageSize:pagination['pageSize']})}`)
    // this.action();
    // this.setState({
    //   search:{unitInName:value}
    // },()=>this.action());
  };
  searchClear=(e)=>{
    if(!e.target.value){
      this.props.dispatch({
        type:'unitIn/updateState',
        payload:{ unitName:undefined }
      });
      this.action();
    }
  };
  exportInfo= async ()=>{
    this.setState({
      economicDownload:true,
    })
    await this['economic'].submitNoModal();
    this.setState({
      economicDownload:false,
    })
  };
  render() {
    // console.log(this.props.commonDict)
    const {list,pagination={},unitName,filter}=this.props.unitIn;
    const {current,pageSize}=pagination;
    const {loading}=this.props;
    const {dataInfo,filterHeight}=this.state;
    const columns=[
      {
        title:'单位名称',
        dataIndex:'unitName',
        width:200,
      },
      {
        title:'产业名称',
        dataIndex:'industryName',
        width:200,
        render:(text,record)=><a onClick={()=>this.addOrEdit(record)} >{text}</a>
      },
      {
        title:'发展经济类型',
        dataIndex:'d128Name',
        width:200,
      },
      {
        title:'采取组织形式',
        dataIndex:'d129Name',
        width:200,
      },
      {
        title:'操作',
        dataIndex:'action',
        width:100,
        render:(text,record)=>{
          return(
            <span>
              <a onClick={()=>this.addOrEdit(record)}>编辑</a>
              <Divider type="vertical"/>
              <Popconfirm title="确定要删除吗？" onConfirm={()=>this.confirm(record)}>
               <a className={'del'}>删除</a>
              </Popconfirm>
            </span>
          )
        },
      },
    ];
    // const filterData=[
    //   {
    //     key:'d04CodeList',name:'单位类别',value:this.props.commonDict[`dict_d04_tree`],
    //   },
    //   {
    //     key:'d05CodeList',name:'组织情况',value:this.props.commonDict[`dict_d05_tree`],
    //   },
    //   {
    //     key:'d35CodeList',name:'隶属关系',value:this.props.commonDict[`dict_d35_tree`],
    //   },
    // ];
    const org = getSession('org') || {};
    return (
      <div>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        {/*<DictArea/>*/}
        <AddOrEdit dataInfo={dataInfo}/>
        <NowOrg extra={
          <React.Fragment>
            <Button onClick={this.exportInfo} loading={this.state.economicDownload}>导出</Button>
            <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={()=>this.addOrEdit()} style={{marginLeft:16}}>新增集体经济情况</Button>
            <Search style={{width:200,marginLeft:16}} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'}/>
          </React.Fragment>
        }/>
        {/*<RuiFilter data={filterData} onChange={this.filterChange}/>*/}
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:filterHeight}} columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
        <ExportInfo wrappedComponentRef={e=>this['economic'] = e}
                    tableName={''}
                    noModal={true}
                    tableListQuery={{unitName,...filter,orgCode:org['orgCode']}}
                    action={'/api/unit/collectiveEconomic/export'}
        />
      </div>
    );
  }
}
