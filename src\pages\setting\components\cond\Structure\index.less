.query {
  .noLabel {
    :global(.ant-form-item-label label::after){
      content: '';
    }
  }
  .noHeight {
    :global(.ant-form-item-control) {
      line-height: 13px;
      color: red;
    }
  }
  .inlineItem {
    display: inline-block;
    margin: 0 !important;
    input {
      padding: 6px;
    }
  }
  .center {
    margin: 0 5px;
  }
  .end {
    margin-left: 5px;
  }
  .operation {
    overflow: hidden;
    margin-bottom: 16px;
    button {
      margin: 0 0 0 16px;
    }
    .left {
      float: left;
    }
    .right {
      float: right;
    }
  }
  :global(.ant-form-item) {
    margin-bottom: 5px;
  }
}

.appointmentModal, .appointment {
  .operation {
    overflow: hidden;
    margin-bottom: 16px;
    button {
      margin: 0 10px;
    }
  }
  :global(.ant-modal-body) {
    overflow: hidden;
    padding: 10px 24px;
    max-height: 600px;
  }
  .left {
    display: inline-block;
    width: 220px;
    height: 530px;
    overflow: auto;
  }
  .right {
    display: inline-block;
    vertical-align: top;
    width: 1020px;
    height: 530px;
    padding-left: 5px;
    .table {
      height: 410px;
      overflow: auto;
    }
    :global {
      .ant-select {
        width: 150px;
      }
      .ant-table-tbody td {
        padding: 5px 5px !important;
      }
      .ant-table-wrapper .ant-btn {
        height: 24px;
        width: 24px;
        padding: 0;
      }
      .ant-divider-vertical {
        margin: 0 4px;
      }
    }
  }
}
.select {
  :global(.ant-select-selection) {
    font-size: 14px !important;
  }
  font-size: 14px !important;
}

.queryTable {
  :global {
    .ant-table-fixed-header .ant-table-scroll .ant-table-header {
      &::-webkit-scrollbar {
        background-color: rgb(242, 242, 242);
        border: 1px solid rgb(232, 232, 232);
        border-left: none;
      }
      th:last-child {
        border-right: 1px solid rgb(242, 242, 242);
      }
    }
  }
}


