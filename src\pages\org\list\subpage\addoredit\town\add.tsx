import React, { Fragment, useImperativeHandle, useState, useEffect } from 'react';
import { Form, Input, Modal, InputNumber, Row, Col, Select, Button } from 'antd';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import DictTreeSelect from '@/components/DictTreeSelect';
import Tip from '@/components/Tip';
import Notice from '@/components/Notice';
import Date from '@/components/Date';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import moment from 'moment';
import { townshipAdd } from '@/pages/org/services/org';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const nowYear = new Array(5).fill('').map((it, index) => moment().subtract(index, 'year').format('YYYY'));

const index = (props: any, ref) => {
  const { org: { basicInfo = {} } = {}, onOK } = props;
  const [form] = Form.useForm();
  const org = JSON.parse(sessionStorage.getItem('org') || '{}');
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [title, setTitle] = useState('新增');

  useImperativeHandle(ref, () => ({
    open: (dataInfo) => {
      open(dataInfo);
    },
  }));
  const open = (dataInfo) => {
    setVisible(true);
    if (!_isEmpty(dataInfo)) {
      setTitle('编辑');
      setDataInfo(dataInfo);
      form.setFieldsValue({
        ...dataInfo,
      });
    }
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    setConfirmLoading(true);
    const { code: resCode = 500 } = await townshipAdd({
      data: {
        ...e,
        orgCode: basicInfo?.code,
        code: dataInfo?.code,
      },
    });
    setConfirmLoading(false);
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  const renderItem = (item) => {
    const { tipMsg = {} } = props;
    const { basicInfo = {} } = props.org;
    const { label, code, type, codeType, rules, boolText = {}, backType = undefined, filter } = item;
    let node = <Input />;
    switch (`${type}`) {
      case 'boolean':
        node = (<Select style={{ width: '100%' }} >
          <Select.Option value={1}>{boolText?.yes || '是'}</Select.Option>
          <Select.Option value={0}>{boolText?.no || '否'}</Select.Option>
        </Select>);
        break;
      case 'boolean2':
        node = (<Select style={{ width: '100%' }} >
          <Select.Option value={1}>是</Select.Option>
          <Select.Option value={0}>否</Select.Option>
          <Select.Option value={2}>未配备</Select.Option>
        </Select>);
        break;
      case 'dict':
        node = <DictTreeSelect codeType={codeType} initValue={dataInfo[code]} backType={backType} parentDisable={true} filter={filter} />;
        break;
      case 'number':
        node = <InputNumber style={{ width: '100%' }} min={0} />;
        break;
      case 'date':
        node = <Date />;
        break;
    }
    return (
      <Col span={24} key={code}>
        <LongLabelFormItem
          label={label}
          required={true}
          code={code}
          tipMsg={tipMsg}
          formItemLayout={formItemLayout}
          formItem={(formItemLayout, code) => {
            return (
              <Form.Item
                name={code}
                {...formItemLayout}
                rules={[{ required: true, message: '请填写' }]}
              >
                {node}
              </Form.Item>
            )
          }} />
      </Col>
    );
  };
  const renderFormItems = () => {
    let res: any = [];

    let exData = [
      {
        label: '从乡镇事业编制人员中选拔乡镇领导干部(人)',
        code: 'leadCadresTowns',
        type: 'number',
        codeType: '',
        rules: [{ required: true, message: '从乡镇事业编制人员中选拔乡镇领导干部(人)' }],
      },
      {
        label: '从到村任职过的选调生中选拔乡镇领导干部(人)',
        code: 'villageLeadCadres',
        type: 'number',
        codeType: '',
        rules: [{ required: true, message: '从到村任职过的选调生中选拔乡镇领导干部(人)' }],
      },
      {
        label: '从第一书记中选拔乡镇领导干部(人)',
        code: 'firstSecretaryCadres',
        type: 'number',
        codeType: '',
        rules: [{ required: true, message: '从第一书记中选拔乡镇领导干部(人)' }],
      },
      {
        label: '从驻村工作队员中选拔乡镇领导干部(人)',
        code: 'villageLeaders',
        type: 'number',
        codeType: '',
        rules: [{ required: true, message: '从驻村工作队员中选拔乡镇领导干部(人)' }],
      },
    ];
    exData.forEach((item, index) => {
      let node = renderItem(item);
      res.push(node);
    });
    return res;
  }


  return (
    <Fragment>
      <Modal
        title={title}
        destroyOnClose
        visible={visible}
        confirmLoading={confirmLoading}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={'800px'}
      >
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Row>
            <Col span={24}>
              <Form.Item name='year'
                label='年度'
                rules={[{ required: true, message: '请填写' }]}
              >
                <Select style={{ width: '100%' }} >
                  {nowYear.map(it => <Select.Option value={it}>{it}</Select.Option>)}
                </Select>
              </Form.Item>
            </Col>
            {renderFormItems()}
          </Row>
        </Form>
      </Modal>
      {/* <div style={{ textAlign: 'center' }}><Button type={'primary'} onClick={() => form.submit()}>保存</Button></div> */}
    </Fragment>
  );
};
export default React.forwardRef(index);
