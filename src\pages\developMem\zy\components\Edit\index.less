.btns {
    text-align: center;
  }
  .flex {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .flexbox {
    flex: 1;
    display: flex;
    height: 100%;
    justify-content: space-evenly;
   
    .flexbox_l {
      // width: 500px;
      flex: 1;
      margin: 0 20px;
      border:1px dashed #ccc;
      display: flex;
      justify-content: center;
      align-items: end;
      position: relative;
      .delfile {
        display: none;
      }
      .previewImage {
        width: 100%;
        height: 100%;
      
      }
      &:hover {
        .previewImage{
          opacity: 0.5;
        }
  .delfile {
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
      
      }
    }
    .left {
      width: 800px;
      display: flex;
      flex-direction: column;
      border: 1px solid #C6D7E7;
      border-radius: 8px;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
      position: relative;
      .load {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(255,255,255,.7);
      }
    }
  }
  .flexboxTit {
    height: 56px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #D8D8D8;
    >div:nth-child(1) {
      max-width: 380px;
      color: #3D3D3D;
      font-size: 20px;
      font-weight: 500;
      position: relative;
      padding-left: 33px;
      padding-right: 20px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      &::before{
          content: '';
          position: absolute;
          left: 21px;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 22px;
          background: #1890FF;
      }
    }
    .flexboxTip {
      background: rgba(255, 81, 0, 0.2);
      border-radius: 4px;
      align-items: center;
      padding: 2px 8px;
      color: #FF5100;
      font-family: AlibabaPuHuiTi;
      font-size: 16px;
      font-weight: normal;
    }
  }
  .imglist {
   flex: 1;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    overflow: auto;
    .previewImage {
      width: 200px;
      height: 300px;
      position: relative;
      margin: 5px 20px 0 20px;
      .preview {
        position: absolute;
        top: 5px;
        right: 5px;
        display: none;
      }
      .cropper {
        position: absolute;
        top: 5px;
        right: 60px;
        display: none;
      }
      // .wm {
      //   width: 200px;
      //   height: 300px;
      //   >img {
      //     width: 100%;
      //     height: 100%;
      //   }
      // }
  
        .simg {
          width: 100%;
          height: 100%;
        }
      &:hover { 
        .preview {
          display: block;
           cursor: pointer;
        }
        .cropper {
          display: block;
          cursor: pointer;
        }
      }
      &:after {
        content: attr(data-watermark); /* 使用HTML元素的data-watermark属性作为水印内容 */
        position: absolute;
        top: 10px; /* 根据需要调整 */
        left: 10px; /* 根据需要调整 */
        font-size: 12px; /* 根据需要调整 */
        color: rgba(190, 29, 29, 0.5); /* 水印颜色和透明度 */
        pointer-events: none; /* 防止水印阻挡鼠标事件 */
      }
      :global(.ant-skeleton) {
          width: 100% !important;
          height: 100% !important;
      }
      :global(.ant-progress) {
        position: absolute;
        left: 0;
        top: 60%;
     
      }
    }
    .cImage {
      border: 1px dashed #287fe2;
    }
    .cicon {
      color: rgb(17, 146, 103);
      position: absolute;
      left: 5px;
      right: 5px;
    }
    &::-webkit-scrollbar {
      width: 2px;
  }
 
  }
  .progress {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255,255,255,.7);
  }
  .upbtn {
    flex: 1;
    text-align: right;
    padding-right: 12px;
  }
  .oprationlist {
    width: 420px;
    overflow-y: auto;
    border: 1px solid #C6D7E7;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
    &::-webkit-scrollbar {
      width: 2px;
    }
    table{
      width: 400px;
      margin: 10px auto 10px;
      td {
        border: 1px solid #EBEEF5;
        padding: 13px 12px;
      
        font-family: Source Han Sans;
  font-size: 14px;
  font-weight: normal;
      }
      .label {
        background: #F5F7FA;
        width: 130px;
      }
    }
  }
  .fzdy {
    background: rgb(24, 144, 255);
    color: rgb(255, 255, 255);
    display: inline-block;
    padding: 2px 5px;
    border-radius: 5px;
  }
  .changeicon {
    position: absolute;
    left: -30px;
    top: 50%;
    transform: translate(0, -50%);
    cursor: pointer;
  }
  .changeicon1 {
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translate(0, -50%);
    cursor: pointer;
  }
  .editLeft {
    height: 600px;
    overflow-y: auto;
    border: 1px solid #C6D7E7;
    border-radius: 8px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
    &::-webkit-scrollbar {
      width: 2px;
  }
  }

  .sliders{
    position: relative;
    max-width: 200px;
    min-width: 200px;
    margin: 20px 0 20px -15px;
    padding-left: 30px;
    line-height: 40px;
    border-radius: 0 5px 5px 0;
    font-size: 15px;
    font-weight: bold;
    color: #fff;
    background-color: #40a9ff;
    overflow: hidden;
    white-space: nowrap;
    &:after{
      content: ' ';
      position: absolute;
      top: 40px;
      left: 0;
      width: 0;
      height: 0;
      border-width: 7px;
      border-style: solid;
      border-color: #2185D0 #2185D0 transparent transparent;
    }
  }
  :global{
    .ReactCrop__child-wrapper {
      height: 690px !important;
    }
  }