import React from 'react';
import _isEmpty from 'lodash/isEmpty';
export const configList = [
  // 人员概况
  {
    key:'1001',
    value:{
      icon:'team',
      coverImg:require('./assets/mem/dangyuan.jpg'),
      iconColor:'#17C1C5',
      title:'人员总数',
      suffix:'人',
      action:'/api/chart/mem/getMemTotal',
      end:(val)=>{
        if(val){
          const {total} = val;
          return total;
        }else {
          return 0
        }
      }
    },
  },
  {
    key:'1002',
    value:{
      icon:'hourglass',
      coverImg:require('./assets/mem/zhengshi.jpg'),
      iconColor:'#6F79C1',
      title:'正式党员',
      suffix:'人',
      action:'/api/chart/mem/getFullMemTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          val.forEach(item=>{
            if(item['d08Code'] === '1'){
              num =  item['count']
            }
          });
        }
        return num;
      },
      content:(data)=>{
        let num = 0;
        if(!_isEmpty(data)) {
          data.forEach(item => {
            if (item['d08Code'] === '2') {
              num = item['count']
            }
          });
        }
        return(
          <div>
            <span>预备党员{num}人</span>
          </div>
        )},
    },
  },
  {
    key:'1003',
    value:{
      icon:'minus-square-o',
      coverImg:require('./assets/mem/shaomin.jpg'),
      iconColor:'#00A0FF',
      title:'少数民族党员',
      suffix:'人',
      action:'/api/chart/mem/getMinorityTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          const {minorityTotal = 0} = val || {};
          num = minorityTotal;
        }
        return num;
      },
      content:(data)=>{
        let num = 0;
        if(!_isEmpty(data)){
          const {dzTotal = 0} = data || {};
          num = dzTotal;
        }
        return(
          <div>
            <span>大专及以上学历党员{num}人</span>
          </div>
        )
      },
    },
  },
  // 组织概况
  {
    key:'2001',
    value:{
      icon:'home',
      coverImg:require('./assets/org/zuzhi.jpg'),
      iconColor:'#17C1C5',
      title:'党组织',
      suffix:'个',
      action:'/api/chart/org/getOrgTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          const {orgTotal = 0} = val || {};
          num = orgTotal;
        }
        return num;
      },
    },
  },
  {
    key:'2002',
    value:{
      icon:'bell',
      coverImg:require('./assets/org/jieman.jpg'),
      iconColor:'#6F79C1',
      title:'即将届满',
      suffix:'个',
      action:'/api/chart/org/getOrgElectTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          const {orgElectTotal = 0} = val || {};
          num = orgElectTotal;
        }
        return num;
      },
      content:(data)=>{
        let num = 0;
        if(!_isEmpty(data)){
          const {dzTotal = 0} = data || {};
          num = dzTotal;
        }
        return(
          <div>
            <span>党组织7天内届满</span>
          </div>
        )
      },
    },
  },
  {
    key:'2003',
    value:{
      icon:'home',
      coverImg:require('./assets/org/danwei.jpg'),
      iconColor:'#00A0FF',
      title:'单位',
      suffix:'个',
      action:'/api/chart/unit/getUnitTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          const {unitTotal = 0} = val || {};
          num = unitTotal;
        }
        return num;
      },
    },
  },
  {
    key:'2004',
    value:{
      icon:'hourglass',
      coverImg:require('./assets/mem/zhengshi.jpg'),
      iconColor:'#6F79C1',
      title:'正式党员',
      suffix:'人',
      action:'/api/chart/mem/getFullMemTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          val.forEach(item=>{
            if(item['d08Code'] === '1'){
              num =  item['count']
            }
          });
        }
        return num;
      },
      content:(data)=>{
        let num = 0;
        if(!_isEmpty(data)) {
          data.forEach(item => {
            if (item['d08Code'] === '2') {
              num = item['count']
            }
          });
        }
        return(
          <div>
            <span>预备党员{num}人</span>
          </div>
        )},
    },
  },
  // 单位概况
  {
    key:'3001',
    value:{
      icon:'team',
      coverImg:require('./assets/mem/dangyuan.jpg'),
      iconColor:'#17C1C5',
      title:'转接总人数',
      suffix:'人',
      action:'/api/chart/mem/getMemTotal',
      end:(val)=>{
        if(val){
          const {total} = val;
          return total;
        }else {
          return 0
        }
      }
    },
  },
  {
    key:'3002',
    value:{
      icon:'team',
      coverImg:require('./assets/mem/dangyuan.jpg'),
      iconColor:'#17C1C5',
      title:'转出总人数',
      suffix:'人',
      action:'/api/chart/mem/getMemTotal',
      end:(val)=>{
        if(val){
          const {total} = val;
          return total;
        }else {
          return 0
        }
      },
      content:(data)=>{
        let num = 0;
        if(!_isEmpty(data)) {
          data.forEach(item => {
            if (item['d08Code'] === '2') {
              num = item['count']
            }
          });
        }
        return(
          <div>
            <span>省外转出23783人</span>
            <span>省内转出299361人</span>
          </div>
        )},
    },
  },
  {
    key:'3003',
    value:{
      icon:'team',
      coverImg:require('./assets/mem/dangyuan.jpg'),
      iconColor:'#17C1C5',
      title:'转入总人数',
      suffix:'人',
      action:'/api/chart/mem/getMemTotal',
      end:(val)=>{
        if(val){
          const {total} = val;
          return total;
        }else {
          return 0
        }
      },
      content:(data)=>{
        let num = 0;
        if(!_isEmpty(data)) {
          data.forEach(item => {
            if (item['d08Code'] === '2') {
              num = item['count']
            }
          });
        }
        return(
          <div>
            <span>省外转入26042人</span>
            <span>省内转入299318人</span>
          </div>
        )},
    },
  },
  {
    key:'3004',
    value:{
      icon:'team',
      coverImg:require('./assets/mem/dangyuan.jpg'),
      iconColor:'#17C1C5',
      title:'整建制转',
      suffix:'人',
      action:'/api/chart/mem/getMemTotal',
      end:(val)=>{
        if(val){
          const {total} = val;
          return total;
        }else {
          return 0
        }
      },
      content:(data)=>{
        let num = 0;
        if(!_isEmpty(data)) {
          data.forEach(item => {
            if (item['d08Code'] === '2') {
              num = item['count']
            }
          });
        }
        return(
          <div>
            <span>整建制转入124591人</span>
            <span>整建制转出124583人</span>
          </div>
        )},
    },
  },
];

export const chartsConfigList = [
  {
    key:'1004',
    value:{
      tit:'人员详情',
      chartId:'memDetailsCharts',
      chartType:'ringR',
      xAxisCode:'d08Name',
      yAxisCode:'count',
      extraG2ChartConfig:{padding:[50,20,55,0]},
      action:'/api/chart/mem/getFullMemTotal',
    }
  },
  {
    key:'1005',
    value:{
      tit:'男女比例',
      chartId:'memProportionsCharts',
      chartType:'horizontalBar',
      xAxisCode:'sexName',
      yAxisCode:'count',
      extraG2ChartConfig:{padding:[50,50,60,60]},
      action:'/api/chart/mem/getSexRatioTotal',
    }
  },
  {
    key:'1006',
    value:{
      tit:'学历情况',
      chartId:'memEduCharts',
      chartType:'coord',
      xAxisCode:'d07Name',
      yAxisCode:'count',
      extraG2ChartConfig:{padding:[50,20,130,0]},
      action:'/api/chart/mem/getEducationRatioTotal',
    }
  },
];
