import React from 'react';
import ListTable from '@/components/ListTable';
import ListFilter from '@/components/ListFilter';
import NowOrg from '@/components/NowOrg';
import { Button, Input, Divider, Popconfirm, Modal, Tabs } from 'antd';
import { connect } from 'dva';
import moment from  'moment';
import { isEmpty, setListHeight } from '@/utils/method';
import { getSession } from '@/utils/session';
const Search=Input.Search;
const TabPane = Tabs.TabPane;
@connect(({user,login})=>({
  user,
  login
}))
export default class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      orgCode:''
    }
  }
  componentDidMount = () => {
    let org=getSession('org')|| {};
    this.setState({
      orgCode:org['orgCode']
    },()=>{
      this.onPage();
    });
    setListHeight(this);
  };
  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org=getSession('org') || {};
    if (!isEmpty(this.state['orgCode'])&&this.state['orgCode']!==org['orgCode']) {
      this.setState({
        orgCode:org['orgCode']
      },()=>{
        this.onPage();
      })
    }
  }
  onPage = ( pageNum=1,pageSize=10) => {
    this.props.dispatch({
      type:'user/getByPage',
      payload:{
        data:{
          pageNumber:pageNum,
          pageSize:pageSize,
          code:this.state['orgCode'],
          keyWord:this.state['keyWord']
        }
      }
    })
  };

  changePage=(v,k)=>{
    this.onPage(v,k);
    this.setState({page:v})
  };
  isSearch = (v) => {
    this.setState({keyWord:v},()=>{
      this.onPage()
    })
  };
  isChange=(e)=>{
    if (isEmpty(e.target.value)) {
      this.setState({keyWord:e.target.value},()=>{
        this.onPage()
      })
    }
  };

  render(): React.ReactNode {
    const { filterHeight }=this.state;
    const { user:{ list2=[],pagination2:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={} }={} } =this.props;
    const columns=[
      {
        title:'用户账户',
        dataIndex:'account',
        width:200,
      },
      // {
      //   title:'登录次数',
      //   dataIndex:'count',
      //   width:200,
      // },
      {
        title:'所在单位',
        dataIndex:'managerOrgName',
        width:400,
      },
      // {
      //   title:'用户登录IP',
      //   dataIndex:'ip',
      //   width:200,
      // },
      {
        title:'登录状态',
        dataIndex:'isOk',
        width:200,
        render:(text,record)=>{
          return <span>{text===0?'失败':'成功'}</span>
        }
      },
      {
        title:'创建时间',
        dataIndex:'createTime',
        width:200,
        render:text => {
          return <span>{moment(text).format('YYYY-MM-DD')}</span>
        }
      },
    ];

    return(
      <div id={'logList'}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        <ListFilter>
          <div style={{textAlign:'right'}}>
            <Search
              placeholder="请输入检索关键词"
              onSearch={value => this.isSearch(value)}
              onChange={(value)=>this.isChange(value)}
              style={{ width: 200 }}
            />
          </div>
        </ListFilter>
        <ListTable  scroll={{y:filterHeight}} columns={columns} data={list2} pagination={{pageSize,total:totalRow,page,current:pageNumber}} onPageChange={this.changePage}/>
      </div>
    );
  }
}
