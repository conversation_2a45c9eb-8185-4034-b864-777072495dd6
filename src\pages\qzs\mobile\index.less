.bg {
  background: url('../../../assets/qzs/bg1.webp') no-repeat;
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.box {
  height: 500px;
  display: flex;
  align-items: center;
  flex-direction: column;
  .logo {
    width: 269px;
    height: 64px;
    margin-bottom: 20px;
  }

  .form {
    margin-bottom: 100px;

    .zzsrm {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .zzsrmItem {
        width: 34%;
      }
      .zzsrmItemLine {
        margin-bottom: 30px;
        font-size: 25px;
      }
    }

    .btn {
      background-color: #be0c10;
      border-radius: 78px;

      cursor: pointer;

      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 32px;
      color: #fff9e7;
      text-align: center;
    }
  }

  :global {
    .ant-form-item {
      margin-bottom: 30px;
      font-size: 30px;
    }
    .ant-form-item-control-input-content {
      > input {
        font-size: 30px;
        background: transparent !important;
        border: 2px solid #c1a984;
        border-radius: 78px;
      }
    }
  }

  :global {
    .ant-tabs-tab {
      font-size: 16px;
      color: #be0c10 !important;
      font-weight: bolder !important;
    }
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #be0c10 !important;
      font-weight: bolder !important;
    }
    .ant-tabs-ink-bar {
      background: #be0c10 !important;
    }
  }
}
