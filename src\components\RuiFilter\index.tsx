/**
 * 模块名
 */
import React, { Fragment, ReactNode } from 'react';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Card, DatePicker } from 'antd';
import TagTree from './tagTree';
import styles from './index.less';
import Date from '@/components/Date';
import { _history as router, setListHeight } from '@/utils/method';
interface tag {
  key: string | number,
  name: string,
  parent?: string,
}
interface tagItem {
  key: string,
  name: string,
  value: Array<tag>,
}
interface pType {
  data?: Array<tagItem>,
  onChange?: (value: object) => void,
  showLine?: any,
  openCloseChange?: Function
}

const { RangePicker } = DatePicker;
export default class index extends React.Component<pType, any> {
  static defaultProps = {
    showLine: 1, // 筛选框默认显示几行
  };

  mutationObserver: MutationObserver | null = null;

  constructor(props) {
    super(props);
    this.state = {
      query: {},
      active: false,
      time1: undefined,//判断是否和前一个值一样
      time2: undefined
    }
  }
  componentDidMount(): void {
    let element = document.getElementById('ruiFilter');
    const _this = this;

    // 使用 MutationObserver 替代已废弃的 DOMNodeInserted
    if (element && 'MutationObserver' in window) {
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            _this.triggerResize();
          }
        });
      });

      observer.observe(element, {
        childList: true,
        subtree: true
      });

      // 保存 observer 实例以便在组件卸载时清理
      this.mutationObserver = observer;
    }
  }

  componentWillUnmount(): void {
    // 清理 MutationObserver
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }
  }

  triggerResize = () => {
    const { openCloseChange } = this.props;
    const e = document.createEvent("Event");
    e.initEvent("resize", true, true);
    if (e) {
      e.preventDefault();
      // window.dispatchEvent(e);
      openCloseChange && openCloseChange()
    }
  };
  tagChange = (val, key) => {
    console.log("🚀 ~ index ~ valueType:", key, val)
    let { query } = this.state;
    const { onChange } = this.props;
    query = { ...query, [key]: val };
    this.setState({
      query
    });
    onChange && onChange(query);
  };

  dateChange = (value, key, type) => {
    const { time1, time2 } = this.state
    const qtime = this.state.query[key]
    // console.log("dateChange", time1, time2, value, qtime);
    if (type == '1') {
      if (time1 != value || time1 != qtime) {
        this.setState({
          time1: value
        }, () => {
          this.tagChange(value, key)
        })
      }
    }
    if (type == '2') {
      if (time2 != value || time2 != qtime) {
        this.setState({
          time2: value
        }, () => {
          this.tagChange(value, key)
        })
      }
    }
    // console.log("dateChange", time1, time2, value, this.state);
  }


  renderFilter = (data) => {
    const { active } = this.state;
    const { showLine } = this.props;
    return data.map((obj, index) => {
      // type == 2 的时候为一个时间选择器
      const { type = 1 } = obj
      return (
        <div className={index > showLine - 1 ? `${styles.tagTree} ${active ? styles.tagMoreActive : styles.tagMore}` : styles.tagTree} key={index}>
          <div>
            <h6>{obj['name']}</h6>
          </div>
          {type == 1 && <TagTree data={obj['value']} onChange={(val) => this.tagChange(val, obj['key'])} />}
          {type == 2 && (
            //流动党员  新增时间选择器
            <div>
              <div style={{ display: 'flex' }}>
                <Date style={{ flex: 0.6 }} placeholder='开始时间'
                  onChange={(e) => this.dateChange(e, obj['textArr'][0], 1)}
                ></Date>
                &nbsp;&nbsp; <span>至</span> &nbsp;&nbsp;
                <Date style={{ flex: 0.6 }} placeholder='结束时间'
                  onChange={(e) => this.dateChange(e, obj['textArr'][1], 2)}
                ></Date>
              </div>
            </div>
          )
          }
        </div>
      )
    })
  };

  render() {
    const { data = [], showLine } = this.props;
    const { active } = this.state;
    return (
      <Card id={'ruiFilter'} style={{ width: '100%' }} bodyStyle={{ padding: 18 }}>
        <div className={styles.card}>
          <div>
            {
              data && this.renderFilter(data)
            }
          </div>
          {
            data.length > showLine && (
              <div
                className={styles.more}
                onClick={
                  () => {
                    this.setState({ active: !active }, () => {
                      setTimeout(() => {
                        this.triggerResize();
                      }, 500)
                    })
                  }
                }
              >
                {
                  active ? <span>收起 <UpOutlined /></span> : <span>更多 <DownOutlined /></span>
                }
              </div>
            )
          }
        </div>

      </Card>
    );
  }
}
