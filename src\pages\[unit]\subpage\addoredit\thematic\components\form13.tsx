import React, { Fragment, useEffect, useState } from 'react';
import { Button, Col, Form, InputNumber, Row, Switch } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import { formItemLayout3 } from './config';
import { findZtDataByCode, saveZt13Data } from '@/pages/[unit]/services/thematic';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import Tip from '@/components/Tip';
import { Icon as LegacyIcon } from '@ant-design/compatible';

const index = (props: any) => {
  const { selectTab, basicInfo } = props;
  const { code: unitCode = '' } = basicInfo;
  const [form] = Form.useForm();
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);

  const onFinish = async (e) => {
    setLoading(true);
    let val = _cloneDeep(e);
    [
      'isAttachTwonewCommittee',
      'isAttachOrganCommittee',
      'isAttachIndustryDepartment',
      'isSecretaryByDepartment',
    ].map((it) => {
      val[it] = val[it] ? 1 : 0;
    });
    const { code = 500 } = await saveZt13Data({
      data: {
        unitCode,
        ...val,
      },
    });
    setLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '修改成功');
      getInfo();
    }
  };
  const getInfo = async () => {
    const { code = 500, data = {} } = await findZtDataByCode({
      unitCode,
      type: '13',
    });
    if (code === 0) {
      setQuery(data);
      form.setFieldsValue(data);
    }
  };
  useEffect(() => {
    getInfo();
  }, []);
  return (
    <Fragment>
      <Form form={form} {...formItemLayout3} onFinish={onFinish}>
        <Row>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isAttachTwonewCommittee"
              initialValue={_get(query, 'isAttachTwonewCommittee', false)}
              label="隶属于两新工委的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isAttachOrganCommittee"
              initialValue={_get(query, 'isAttachOrganCommittee', false)}
              label="隶属于机关工委的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isAttachIndustryDepartment"
              initialValue={_get(query, 'isAttachIndustryDepartment', false)}
              label="隶属于行业主管部门党组织的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isSecretaryByDepartment"
              initialValue={_get(query, 'isSecretaryByDepartment', false)}
              label="书记由行业主管部门党员负责同志担任"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="fullTimeWorkerNum" label="专职工作人员数" rules={[{ required: true }]}>
            <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <div style={{ width: '100%', textAlign: 'center' }}>
            <WhiteSpace />
            <Button
              icon={<LegacyIcon type={'check'} />}
              type={'primary'}
              onClick={() => form.submit()}
              loading={loading}
            >
              保存
            </Button>
          </div>
        </Row>
      </Form>
    </Fragment>
  );
};
export default index;
