import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {Modal} from 'antd';
import moment from 'moment';
import Tip from '@/components/Tip';
import _isNumber from 'lodash/isNumber';
import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';
import _isString from 'lodash/isString';
import Date from '@/components/Date';
import {updateRatification} from '@/pages/developMem/services/index'

const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      memInfo:{},
      visible:false,
      timeKey:moment().valueOf(),
    }
  }
  handleOk=()=>{
    const {submit,memDevelop:{basicInfo = {}}={}} = this.props;
    this.props.form.validateFieldsAndScroll( async (err,val)=>{
      if(!err){
        let deadTime=val["deadTime"].valueOf();
        let ratificationTime=val["ratificationTime"].valueOf();
        // 追认中共党员
        const {code = 500} = await updateRatification({
          data:{
            deadTime,
            ratificationTime,
            code:this.state.memInfo.code || "",
          }
        });
        if(code === 0){
          this.handleCancel();
          Tip.success('操作提示','操作成功');
          submit && submit();
        }
      }
    })
  };
  handleCancel=()=>{
    this.setState({visible:false});
    this.destroy();
  };
  // 时间限制
  disabledTomorrow=(current)=>{
    const {memInfo:{ activeDate = '' }={}} = this.state;
    const cu = moment(current);
    const start = moment(activeDate).endOf('day');
    // console.log(start.format('YYYY-MM-DD'),'123')
    const end = moment();
    if(_isNumber(activeDate)){
      return current && (cu.isBefore(start) || cu.isSame(start) || cu.isAfter(end) || cu.isSame(end))
    }else {
      return false
    }
  };
  open=(record)=>{
    this.setState({visible:true,memInfo:record,timeKey:moment().valueOf()})
    // this.props.dispatch({
    //   type:'memDevelop/findMem',
    //   payload:{
    //     code:record['code']
    //   }
    // })
  };
  destroy=()=>{
   this.setState({
     memInfo:{},
   })
  };
  render() {
    const {form,loading:{effects = {}}={}} = this.props;
    const { getFieldDecorator } = form;
    const {visible} = this.state;
    return (
      <Modal
        destroyOnClose
        title="追认中共党员"
        visible={visible}
        onOk={this.handleOk}
        maskClosable={false}
        onCancel={this.handleCancel}
        confirmLoading={effects['memDevelop/toObject']}
        width={400}
      >
        {
          visible &&
            <Fragment key={this.state.timeKey}>
              <Form>
                <FormItem
                  label="死亡时间"
                  {...formItemLayout}
                >
                  {getFieldDecorator('deadTime', {
                    rules: [{ required: true, message: '死亡时间' }],
                    // initialValue:hasMemValue,
                  })(
                    <Date disabledDate={this.disabledTomorrow}/>
                  )}
                </FormItem>
                <FormItem
                  label="追认时间"
                  {...formItemLayout}
                >
                  {getFieldDecorator('ratificationTime', {
                    rules: [{ required: true, message: '追认时间' }],
                    // initialValue:hasMemValue,
                  })(
                    <Date disabledDate={this.disabledTomorrow}/>
                  )}
                </FormItem>
              </Form>
            </Fragment>
        }
      </Modal>
    );
  }
}
export default Form.create()(index);
