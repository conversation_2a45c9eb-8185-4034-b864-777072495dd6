/**
 * 党组织管理-基本信息
 */
import React, { Fragment } from 'react';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Col, DatePicker, Input, Radio, Row, Switch, Tooltip, Select, InputNumber, Modal } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import moment from 'moment';
import LinkedUnit from './linkedUnit';
import { NumberReg } from "@/utils/validator";
import { connect } from "dva";
import { QuestionCircleOutlined } from '@ant-design/icons';
import { formLabel, formTip, isEmpty, jsonToTree, treeToList, findDictCodeName } from '@/utils/method';
import Date from '@/components/Date';
import YN from '@/components/YesOrNoSelect';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import _isEqual from 'lodash/isEqual';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import _trim from 'lodash/trim'
import _uniqBy from 'lodash/uniqBy'
import _differenceBy from 'lodash/differenceBy'
import DictSelect from '@/components/DictSelect';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import LinkedSpecialOrg from '@/pages/org/special/components/linkedOrg';
import { getSession, getLocalSession } from '@/utils/session';
import { tipsForChangingOrgType, superUnitOrgLinked } from '@/pages/org/services';
import { getMainUnitByOrg } from '@/pages/org/services/org';
import { LockMsg } from '@/pages/user/lock';
import { normalList } from '@/services';
import { validateLength, validateMobilePhoneNumber } from '@/utils/formValidator';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const formItemLayout3 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
};
// @ts-ignore
@connect(({ loading, commonDict }) => ({ orgAdd: loading.effects['org/add'], orgUpdate: loading.effects['org/update'], commonDict }))
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      timeKey: moment().valueOf(),
      basicInfoCollectiveEconomy: [],
      linkedDTOListLh: [],
      timeKey2: moment().valueOf(),
    };
  }
  componentDidMount() {
    this.setState({ timeKey: moment().valueOf() });
    const { basicInfo = {} } = this.props.org;
    if (basicInfo?.d02Code === '2') {
      this.getUpOrgLinkMainUnit()
    }

    // 关联单位 d02Code 选择 与上级党组织所在单位建立联合党支部 时，获取上级党组织列表
    if (basicInfo?.d02Code === '4') {
      this.handleClick()
    }
  }

  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const { org: { basicInfo = {} } = {} } = nextProps;
    const { _basicInfo } = prevState;
    if (!_isEqual(_basicInfo, basicInfo)) {
      state['linkedDTOListLh'] = basicInfo['linkedDTOList']
      state['linkedDTOList_old'] = _cloneDeep(basicInfo['linkedDTOList'])
      state['_basicInfo'] = basicInfo;
      // 这点需要对form表单重新赋值，因为194和195传入的值和返回的值会不一样
      if (basicInfo?.d01Code && basicInfo.d01Code != '25') {  //梁才--组织类别为2开头时国民经济赋默认值S
        if (basicInfo.d01Code.startsWith('1') || basicInfo.d01Code.startsWith('2')) {
          state['d194CodeSatate'] = 'S'
          state['d195CodeSatate'] = 'V0000';
        } else {
          state['d194CodeSatate'] = undefined
          state['d195CodeSatate'] = undefined;
        }
      } else {
        state['d194CodeSatate'] = undefined
        state['d195CodeSatate'] = undefined;
      }



      state['d194CodeKey'] = moment().valueOf();
      state['d195CodeKey'] = moment().valueOf();
      nextProps.form.setFieldsValue({
        ...basicInfo
      })
      state['basicInfoCollectiveEconomy'] = (basicInfo?.collectiveEconomy || []).map((it, index) => {
        return { ...it, id: moment().valueOf() + index }
      })
    }
    return state;
  };

  changeLinkInfoToForm = (e: any, key: any) => {
    if (e[key]) {
      let data: Array<object> = [];
      for (let obj of e[key]) {
        const { org = {} } = obj;
        if (org['code']) {
          //新增的关联组织
          data.push({
            code: org['code'],
            orgName: org['industryName'] || org['name'],
            orgType: org['d01Code'] || '1',
            orgTypeName: org['d01Name'] || '1',
            linkedOrgCode: org['code'],
            orgTypeCode: org['orgType'] || '1',
            isOrgMain: obj['isOrgMain'],
          });
        } else {
          //已关联的组织
          data.push(obj);
        }
      }
      e[key] = data;
    }
    return e;
  };
  handleSubmit = () => {
    const { basicInfo = {} } = this.props.org;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        // 集体经济
        val = this.changeLinkInfoToForm(val, 'collectiveEconomy');
        if (val['collectiveEconomy'] && val['d134Code'] == '3') {
          let length = [...new Set(val['collectiveEconomy'].map(it => it.code))].length;
          if (length < val['collectiveEconomy'].length) {
            this.props.form.setFields({
              collectiveEconomy: { errors: [new Error('不能选择重复的集体经济')] }
            });
            return;
          }
          let find = val['collectiveEconomy'].find(it => !_isEmpty(it.code));
          if (!find) {
            this.props.form.setFields({
              collectiveEconomy: { errors: [new Error('集体经济不能为空')] }
            });
            return;
          }
        }

        if (val['linkedDTOList']) {
          let find = val['linkedDTOList'].find(it => !_isEmpty(it.unit));
          if (!find) {
            this.props.form.setFields({
              linkedDTOList: { errors: [new Error('关联单位不能为空')] }
            });
            return;
          }
          let length = [...new Set(val['linkedDTOList'].map(it => it.unit.code))].length;
          if (length < val['linkedDTOList'].length) {
            this.props.form.setFields({
              linkedDTOList: { errors: [new Error('不能选择重复单位')] }
            });
            return;
          }
          // 选择法人单位，只能选一个单位
          if (val['d02Code'] == '1' && val['linkedDTOList'].length !== 1) {
            this.props.form.setFields({
              linkedDTOList: { errors: [new Error('只能添加一个独立单位')] }
            });
            return;
          }
          // 联合党支部，可以有多个单位，但是只能有一个主单位
          if (val['d02Code'] == '3') {
            let arr = val['linkedDTOList'].filter(it => it.isUnitMain == 1) || [];
            if (arr?.length > 1) {
              this.props.form.setFields({
                linkedDTOList: { errors: [new Error('只能有一个主单位')] }
              });
              return;
            }
            if (val['linkedDTOList'].length > 5) {
              Tip.info('信息提示', '关联单位超过5个');
            }
          }
        }
        let obj = undefined;
        let parentOrgCode = undefined;
        ['d01Code', 'd03Code', 'd134Code'].map(obj => {
          let key = obj.split('C')[0];
          if (typeof val[obj] === 'object') {
            // val[`${key}Name`]=val[obj]['name'];
            val[obj] = val[obj]['key']
          }
        });
        ['isRetire', 'isFlow', 'hasRepresentative', 'hasProperSecretary', 'hasInstructorContact', 'hasUnionOrganization', 'hasHeadParty', 'hasMoveOrg'].map(obj => {
          if (val[obj] != undefined) {
            if (val[obj]) {
              val[obj] = 1;
            } else {
              val[obj] = 0
            }
          }
        });
        if (val['parentCode'] instanceof Array) {
          parentOrgCode = val['parentCode'][0]['orgCode'];
          val['parentCode'] = val['parentCode'][0]['code'];
        }
        if (val['createDate']) {
          val['createDate'] = moment(val['createDate']).valueOf();
        }
        if (val['linkedDTOList']) {
          let data: Array<object> = [];
          for (let obj of val['linkedDTOList']) {
            const { unit = {} } = obj;
            data.push({ unitCode: unit['code'], unitName: unit['name'], unitType: unit['d04Code'], unitTypeName: unit['d04Name'], isUnitMain: obj['isUnitMain'] })
          }
          val['linkedDTOList'] = data;
        }
        if (val['d02Code'] === '2' && val['d02Code2']) {
          val['d02Code'] = val['d02Code2'];
        }

        val = findDictCodeName(['d194', 'd195'], val, basicInfo)

        if (_isEmpty(val.d194Code)) {
          val.d194Code = ''
          val.d194Name = ''
        }
        if (_isEmpty(val.d195Code)) {
          val.d195Code = ''
          val.d195Name = ''
        }
        if (val['d195Code'] == 'V0000') {
          val['d195Name'] = "无"
        }
        if (val['d194Code'] == 'S') {
          val['d194Name'] = "公共管理、社会保障和社会组织"
        }

        // 增加逻辑
        // 当出现不可选中的d194和d195传空
        let dis = this.getGMJJDisabled()
        // if (dis) {
        //   val.d194Code = ''
        //   val.d194Name = ''
        //   val.d195Code = ''
        //   val.d195Name = ''
        // }
        //lc- 强制赋默认值
        // const { commonDict } = this.props
        // if (val.d01Code) {
        //   if (val.d01Code.startsWith('2')) {
        //     val.d194Code = commonDict.dict_d194.find(i => i.key == 'S').key
        //     val.d194Name = commonDict.dict_d194.find(i => i.key == 'S').name
        //   }
        // } else {
        //   if (basicInfo.d01Code) {
        //     if (basicInfo.d01Code.startsWith('2')) {
        //       val.d194Code = commonDict.dict_d194.find(i => i.key == 'S').key
        //       val.d194Name = commonDict.dict_d194.find(i => i.key == 'S').name
        //     }
        //   }
        // }

        if (basicInfo['code']) {
          // 屏蔽了props上面的删除，使用深克隆的方式删除linkedDTOList和d02Code
          // delete basicInfo['linkedDTOList'];
          // delete basicInfo['d02Code'];
          let _basicInfo = _cloneDeep(basicInfo);
          delete _basicInfo['linkedDTOList'];
          delete _basicInfo['d02Code'];
          console.log({
            ..._basicInfo,
            ...val
          }, '1111111111111111111')
          obj = await this.props.dispatch({
            type: 'org/update',
            payload: {
              data: {
                ..._basicInfo,
                ...val
              }
            }
          })
        } else {
          obj = await this.props.dispatch({
            type: 'org/add',
            payload: {
              data: {
                ...val
              }
            }
          })
        }
        if (obj && obj['code'] === 0) {
          Tip.success('操作提示', basicInfo['code'] ? '修改成功' : '新增成功');
          if (!basicInfo['code']) {//新增完成后关闭窗口
            this.props.close({ colseType: 'add' });
            this.props.dispatch({
              type: 'common/getTree',
              payload: {
                data: {
                  orgCodeList: [parentOrgCode],
                  excludeOrgCodeList: []
                }
              }
            })
          } else {
            // 编辑点保存后也要请求一遍树
            this.props.dispatch({
              type: 'common/getTree',
              payload: {
                data: {
                  orgCodeList: [basicInfo.orgCode],
                  excludeOrgCodeList: []
                }
              }
            })

            this.props.dispatch({
              type: 'org/findOrg',
              payload: {
                code: basicInfo['code'],
              },
            })
          }
        }

        if (val?.d02Code == '2') {
          this.getUpOrgLinkMainUnit();
        }
      }
    })
  };
  orgName = (rule, value, callback) => {
    if (value) {
      if (!((value.startsWith('中共') || value.startsWith('中国共产党')) && value.endsWith('委员会'))) {
        callback('组织名称需以中共开头或中国共产党开头及委员会结尾')
      }

      validateLength([rule, value, callback], 100, 300)
      // if(value.length > 100) {
      //   callback('长度不能超过100个字符')
      // }
      callback();
      // let reg = /^(中共|中国共产党)[\u4e00-\u9fa5_a-zA-Z0-9]*委员会$/;
      // if (!reg.test(value)) {
      // }
    }
  };
  linkedChange = (val) => {  //d35Code
    const [obj = {}] = val || [];
    const { unit } = obj || {};
    if (!isEmpty(unit)) {
      // 修改变更关联单位后页面显示的关联单位不变的问题
      let newVal = val.map((item) => {
        item.unitCode = item?.unit?.code;
        item.unitName = item?.unit?.name;
        item.unitType = item?.unit?.d04Code;
        item.unitTypeName = item?.unit?.d04Name;
        return item
      })
      // data.push({ unitCode: unit['code'], unitName: unit['name'], unitType: unit['d04Code'], unitTypeName: unit['d04Name'], isUnitMain: obj['isUnitMain'], d16Code: unit['d16Code'], d114Code: unit['d114Code'] })
      this.setState({
        linkedDTOListLh: newVal,
        timeKey2: moment().valueOf()
      })
      // // 当选择的单位会产生国名经济信息，获取当前单位国名经济值赋值
      // let arr = newVal.filter(it=> it.isUnitMain)
      // let flag = this.showGUOMINGJINGJI(arr)
      // if(flag){
      //   let d194Code = _get(arr,'[0].unit.d194Code')
      //   let d194Name = _get(arr,'[0].unit.d194Name')
      //   let d195Code = _get(arr,'[0].unit.d195Code')
      //   let d195Name = _get(arr,'[0].unit.d195Name')
      //   this.setState({
      //     d194CodeSatate:d194Code,
      //     d195CodeSatate:d195Code,
      //     d195CodeKey:moment().valueOf(),
      //     d194CodeKey:moment().valueOf()
      //   },()=>{
      //     this.props.form.setFieldsValue({
      //       d194Code:d194Code,
      //       d195Code:d195Code,
      //       d194Name:d194Name,
      //       d195Name:d195Name
      //     })
      //   })
      // }
    }

    // if(!_isEmpty(val) && _isArray(val)){
    //   let data: Array<object> = [];
    //   val.map((item,index)=>{
    //     const { unit } = item || {};
    //     if(!isEmpty(unit)){
    //       data.push({ unitCode: item?.unitCode ? item?.unitCode : unit['code'], unitName: item?.unitName ? item?.unitName : unit['name'], unitType: item?.unitType ? item?.unitType : unit['d04Code'], unitTypeName:item?.unitTypeName ? item?.unitTypeName :  unit['d04Name'], isUnitMain: item['isUnitMain'], self:item['self'] })
    //     }else{
    //       data.push(item)
    //     }
    //   })
    //   this.setState({
    //     linkedDTOListLh:data,
    //     timeKey2:moment().valueOf()
    //   })
    //   console.log('linkedDTOListLh===',data);
    // }

    // this.setState({
    //   linkedDTOListLh:val,
    //   timeKey2:moment().valueOf()
    // })
    this.props.form.setFieldsValue({
      linkedDTOList: val
    })
  };
  // 获取党组织的关联单位
  getLinkedDTOList = () => {
    const { basicInfo = {} } = this.props.org;
    let val = this.props.form.getFieldValue('linkedDTOList') || [];
    if (this.props.form.getFieldValue('d02Code') == '2') {
      val = basicInfo['linkedDTOListUpOrg'] || [];
    }
    return val;
  }

  renderJiTi = () => {
    const { tipMsg = {} } = this.props;
    const { getFieldDecorator } = this.props.form;
    const { basicInfo = {} } = this.props.org;

    let val = this.getLinkedDTOList();
    // 组织的关联单位的隶属关系 91社区 92建制村
    let find = val.find(it => (it?.unit?.d35Code || 'zzzzz').startsWith('91') || (it?.unit?.d35Code || 'zzzzz').startsWith('92'));
    if (find) {
      let d134CodeVals = this.props.form.getFieldValue('d134Code');
      if (typeof d134CodeVals == 'object') {
        d134CodeVals && (d134CodeVals = d134CodeVals['key']);
      }
      return (
        <React.Fragment>
          <Col span={24}>
            <FormItem label={formLabel('党组织类型', tipMsg['d134Code'])}>
              {getFieldDecorator('d134Code', {
                initialValue: basicInfo['d134Code'] || '',
                rules: [
                  { required: true, message: '请选择!' },
                ],
              })(
                <DictTreeSelect
                  initValue={basicInfo['d134Code']}
                  codeType={'dict_d134'}
                  placeholder={'党组织类型'}
                />
              )}
            </FormItem>
          </Col>
          <Col span={24}>
            {/* 2.当党组织类型是建在集体经济产业链上党组织，增加关联集体经济产业选择，效果如下: */}
            {
              d134CodeVals == '3' &&
              <FormItem
                label={formLabel('集体经济情况', tipMsg['collectiveEconomy'])}
              >
                {getFieldDecorator('collectiveEconomy', {
                  initialValue: this.state.basicInfoCollectiveEconomy,
                  rules: [{ required: true, message: '必填' }],
                })(
                  <LinkedSpecialOrg data={this.state.basicInfoCollectiveEconomy}
                    renderTableCol={(e) => {
                      return e.filter(it => it.dataIndex !== 'isOrgMain')
                    }}
                    onChange={(val: any) => {
                      this.props.form.setFieldsValue({
                        collectiveEconomy: val,
                      });
                    }} />
                )}
              </FormItem>
            }
          </Col>
        </React.Fragment>
      )
    }
  };
  changeD01Msg = async (val) => {
    let d01Code = val
    if (typeof val === 'object') {
      d01Code = val?.key || undefined
    }
    const { basicInfo = {} } = this.props.org;
    const { code = 500, data = [] } = await tipsForChangingOrgType({ data: { code: basicInfo['code'], d01Code } });
    if (code === 0 && !_isEmpty(data)) {
      Modal.info({
        title: '信息提示',
        content: (
          <div>
            <div>该操作会导致以下信息被删除，且无法找回，请务必谨慎操作！！！</div>
            {data && data.map(it => {
              return (
                <div>{it.value}: {it.desc}</div>
              )
            })}
          </div>
        ),
        onOk() { },
      })
      // Tip.info('信息提示', <div>
      //   <div>以下信息会进入历史信息</div>
      //   {data && data.map(it=>{
      //     return (
      //       <div>{it.value}: {it.desc}</div>
      //     )
      //   })}
      // </div>,5);
    }
  }
  // 乡镇修改关联单位的信息时，提示谨慎操作
  d02CodeChange = () => {
    const { form, org: { basicInfo = {} } = {} } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    let linkedDTOListArr = getFieldValue('linkedDTOList') || basicInfo['linkedDTOList'];
    // let linkedDTOListArr = this.getLinkedDTOList();
    let d04Code = linkedDTOListArr && linkedDTOListArr.map(item => {
      return item.unit?.d04Code
    }) || '';
    //乡镇912
    if (`${d04Code}`.startsWith('912')) {
      Modal.info({
        title: '信息提示',
        content: (
          <div>该操作会导致党代表信息被删除，且无法找回， 请务必谨慎操作！！！</div>
        ),
        // onOk() {},
      })
    }
  }
  renderD01Code = (_this) => {
    const { tipMsg = {} } = _this.props;
    const { getFieldDecorator } = _this.props.form;
    const { basicInfo = {} } = _this.props.org;
    const { lockFields = [] } = basicInfo;
    let fieldValue = _this.props.form.getFieldValue('parentCode');
    let pD01Code;
    if (typeof fieldValue === 'object' && fieldValue.length > 0) {
      pD01Code = fieldValue[0]['d01Code']
      if (pD01Code) {
        pD01Code = pD01Code.split('');
      }
    }
    // 判断编辑时，d01code是63 12 13 14开头的
    let checkArr = [
      // '63','12','13','14'
    ];
    let flag = basicInfo?.id && basicInfo?.d01Code && checkArr.find(it => `${basicInfo['d01Code']}`.startsWith(it));
    return (
      <FormItem
        label={formLabel('组织类别', tipMsg['d01Code'])}
      >
        {getFieldDecorator('d01Code', {
          initialValue: basicInfo['d01Code'],
          rules: [{ required: true, message: '请选择组织类别' }],
        })(
          <DictTreeSelect
            ref={e => _this['d01Ref'] = e}
            disabled={lockFields.includes('d01Code')}
            backType={'object'}
            initValue={basicInfo['d01Code']}
            codeType={'dict_d01'}
            // disabled={(basicInfo?.id && basicInfo?.d01Code) ? !flag : false}
            placeholder={'组织类别'}
            parentDisable={true}
            onChange={(val) => {
              if (val.key.startsWith('2') && val.key != '25') {
                this.setState({
                  d194CodeSatate: 'S',
                  d195CodeSatate: 'V0000'
                })
                this.props.form.setFieldsValue({
                  d194Code: 'S',
                  d195Code: 'V0000'
                })
              } else {
                this.setState({
                  d194CodeSatate: basicInfo['d194Code'],
                  d195CodeSatate: basicInfo['d195Code']
                })
                this.props.form.setFieldsValue({
                  d194Code: basicInfo['d194Code'],
                  d195Code: basicInfo['d195Code']
                })
              }
              // // 当存在从联合党支部切换到其他组织类型时，linkedDTOListLh需删除self = false的上级单位
              // let unitlist = _cloneDeep(this?.state?.linkedDTOListLh) || []
              // let code = val?.key || val
              // if(code != '632' && code != '634' && unitlist?.find?.(it=>!it.self)){
              //   unitlist = unitlist.filter(it=>it.self)
              //   this.setState({
              //     linkedDTOListLh:unitlist,
              //     timeKey2:moment().valueOf()
              //   })
              //   this.props.form.setFieldsValue({
              //     linkedDTOList:unitlist,
              //     d02Code:undefined
              //   })
              // }

              // 当编辑时，组织类型变换，增加提示
              basicInfo['code'] && _this.changeD01Msg(val)
            }}
            filter={data => {
              if (pD01Code) {
                return data.filter(obj => obj['key'] >= pD01Code[0] && !obj['key'].startsWith('8'));
                // let res = treeToList([...filter])
                // return jsonToTree(res.filter(obj => obj['key'].length != pD01Code.length || (obj['key'].length == pD01Code.length && obj['key'] >= pD01Code.join(''))), 'parent', 'key');
              }
              // 子集能相互选择
              if (flag) {
                let a = _cloneDeep(data).filter(obj => obj['key'] == basicInfo['d01Code'].substring(0, 1));
                if (!isEmpty(_get(a, '[0].children'))) {
                  a[0].children = a[0].children.filter(obj => obj['key'] == basicInfo['d01Code'].substring(0, 2));
                }
                return a;
              }
              return data
            }}
          />
        )}
      </FormItem>
    )
  };
  renderD03Code = (_this) => {
    const { tipMsg = {} } = _this.props;
    const { getFieldDecorator } = _this.props.form;
    const { basicInfo = {} } = _this.props.org;
    const { lockFields = [] } = basicInfo;
    let d01Code = _this.props.form.getFieldValue('d01Code');
    if (typeof d01Code == 'object') {
      d01Code && (d01Code = d01Code['key']);
    }
    if (`${d01Code}`.startsWith('2')) {
      return (
        <FormItem
          label={formLabel('隶属关系', tipMsg['d03Code'])}
        >
          {getFieldDecorator('d03Code', {
            initialValue: basicInfo['d03Code'],
            rules: [{ required: true, message: '请选择隶属关系' }],
          })(
            <DictTreeSelect
              disabled={lockFields.includes('d03Code')}
              backType={'object'}
              initValue={basicInfo['d03Code']}
              codeType={'dict_d03'}
              placeholder={'隶属关系'}
              parentDisable={true} />
          )}
        </FormItem>
      )
    }
  }
  renderIsRetire = (_this) => {
    const { tipMsg = {} } = _this.props;
    const { getFieldDecorator } = _this.props.form;
    const { basicInfo = {} } = _this.props.org;
    let val = this.getLinkedDTOList();
    let d01Code = _this.props.form.getFieldValue('d01Code');
    if (typeof d01Code == 'object') {
      d01Code && (d01Code = d01Code['key']);
    }
    // 离退休党组织显示只能是基层党支部的时候才显示该项目
    // 党委、党总支 也要加 是否离退休党组织、是否流动党员党组织
    if (`${d01Code}`.startsWith('6') || d01Code == '931') {
      return (
        <Fragment>
          <Col span={12}>
            <FormItem
              label={formLabel('是否离退休党组织', tipMsg['isRetire'])}
              {...formItemLayout2}
            >
              {getFieldDecorator('isRetire', {
                initialValue: basicInfo['isRetire'] || 0,
                valuePropName: 'checked',
                rules: [{ required: false, message: '是否离退休党组织' }],
              })(
                <Switch />
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label={formLabel('是否流动党员党组织', tipMsg['isFlow'])}
              {...formItemLayout2}
            >
              {getFieldDecorator('isFlow', {
                initialValue: basicInfo['isFlow'] || 0,
                valuePropName: 'checked',
                rules: [{ required: false, message: '是否流动党员党组织' }],
              })(
                <Switch />
              )}
            </FormItem>
          </Col>
        </Fragment>
      )
    }
  };
  renderHasMoveOrg = (_this) => {
    const { tipMsg = {}, form, org: { basicInfo = {} } = {} } = _this.props;
    const { getFieldDecorator, getFieldValue } = form;
    let fieldValue = getFieldValue('d01Code');
    if (fieldValue && typeof fieldValue == 'object') {
      fieldValue = fieldValue['key']
    }
    // const { linkedDTOList = [] } = basicInfo || {};
    let val = this.getLinkedDTOList();
    // 组织管理，党组织编辑中，当党组织关联的单位是村或者是社区的时候，才展示填写当前信息项目
    let find = val.find(it => `${it?.unit?.d04Code}`?.startsWith('92') && it?.isUnitMain);
    // 显示逻辑和离退休一样
    // if (`${fieldValue}`.startsWith('63') || fieldValue == '931') {
    if (find) {
      return (
        <Col span={12}>
          <LongLabelFormItem label={'是否易地扶贫搬迁党组织'}
            // required={true}
            code={'hasMoveOrg'}
            tipMsg={tipMsg}
            formItemLayout={formItemLayout2}
            formItem={(formItemLayout, code) => {
              return (
                <FormItem {...formItemLayout}
                >
                  {getFieldDecorator(code, {
                    initialValue: basicInfo[code] || 0,
                    valuePropName: 'checked',
                    rules: [{ required: false, message: '是否易地扶贫搬迁党组织' }],
                  })(
                    // <Select placeholder={'请选择'}>
                    //   <Select.Option value={1}>是</Select.Option>
                    //   <Select.Option value={0}>否</Select.Option>
                    // </Select>
                    <Switch />
                  )}
                </FormItem>
              )
            }} />
        </Col>
      )
    }
  };

  showGUOMINGJINGJI = (val = []) => {
    const { form, org: { basicInfo = {} } = {} } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    // 获取主单位
    let newval = val.filter(it => it.isUnitMain);

    // 1、当党组织的（单位属性为行政村 923 922、是法人单位、国民经济行业为村民自治组织 S9620 或社区居民自治组织 S9610）
    // 三个条件同时满足，展示国民经济行业和生产性服务行业字段，并且可以进行修改和维护。
    const find1 = newval.find(it => {
      let d04 = `${it?.unit?.d04Code}`;
      let isLegal = it?.unit?.isLegal;
      let d194Code = `${it?.unit?.d194Code}`;
      //  return (d04 == '923'|| d04 == '922') && isLegal == 1 && (d194Code == 'S9620' || d194Code == 'S9610');
      return (d04 == '923' || d04 == '922') && (d194Code == 'S9620' || d194Code == 'S9610');
    });
    // 2、当党组织的（单位属性为教育大类中331、是法人单位、国国民经济行业是普通高等教育 P8341）
    // 三个条件同时满足，展示国民经济行业和生产性服务行业字段，并且可以进行修改和维护。
    const find2 = newval.find(it => {
      let d04 = `${it?.unit?.d04Code}`;
      let isLegal = it?.unit?.isLegal;
      let d194Code = `${it?.unit?.d194Code}`;
      // return d04?.startsWith('331') && isLegal == 1 && d194Code.startsWith('P8341')
      return d04?.startsWith('331') && d194Code.startsWith('P8341')
    })
    // 3、当党组织的（单位属性为当单位基础信息中单位类别是教育大类中教育大类中332、333、334、335、是法人单位、国国民经济行业是除普通高等教育以外的 中等教育 P833，  普通高中教育 P8334，初等教育 P832，学前教育 P831，）
    // 三个条件同时满足，展示国民经济行业和生产性服务行业字段，并且可以进行修改和维护。
    const find3 = newval.find(it => {
      let d04 = `${it?.unit?.d04Code}`;
      let isLegal = it?.unit?.isLegal;
      let d194Code = `${it?.unit?.d194Code}`;
      return (d04?.startsWith('332') || d04?.startsWith('333') || d04?.startsWith('334') || d04?.startsWith('335'))
        && (d194Code.startsWith('P833') || d194Code.startsWith('P8334') || d194Code.startsWith('P832') || d194Code.startsWith('P831'))
    })
    return find1 || find2 || find3
  }

  getGMJJDisabled = () => {
    const { tipMsg = {}, form, org: { basicInfo = {} } = {} } = this.props;
    const { lockFields = [] } = basicInfo || {};
    const { getFieldDecorator, getFieldValue } = form;
    let fieldValue = getFieldValue('d01Code');
    if (fieldValue && typeof fieldValue == 'object') {
      fieldValue = fieldValue['key']
    }

    let d02Code = getFieldValue('d02Code')

    let val = this.getLinkedDTOList();

    if (getFieldValue('d02Code') == '2') {
      val = [{ unit: this.state.upOrgLinkMainUnit, isUnitMain: 1 }]
    }
    const hasChangedD01Code = basicInfo?.d01Code != fieldValue;
    const hasChangedD02Code = basicInfo?.d02Code != d02Code;

    // const oldmain = basicInfo?.linkedDTOList?.find(it=>it.isUnitMain == 1)
    const oldmain = this.state.linkedDTOList_old?.find(it => it.isUnitMain == 1)
    const newmain = val.find(it => it.isUnitMain == 1)
    const hasChangedDTOList = basicInfo?.d02Code == '2' ? false : !_isEqual(oldmain?.unit?.code, newmain?.unit?.code)


    // 修改：单位逻辑修改，第一次初始值是否显示按以下逻辑：
    // 1 当d02code为1永远不显示
    // 2 当d02code为2，使用接口getMainUnitByOrg返回信息判断是否显示
    // 3 当d02code为3或者为4时，使用接口findByCode的linkedDTOList判断是否显示
    // 4 当d01Code d02Code 和linkedDTOList发生改变时，永远不显示

    // 当以上3个条件发生表单修改时，那么永远disabled
    let changed = hasChangedD01Code || hasChangedD02Code || hasChangedDTOList;
    let flag = this.showGUOMINGJINGJI(val || [])
    return changed || !(d02Code != '1' && flag)
  }

  renderGUOMINJINGJIOrg = (_this) => {
    const { tipMsg = {}, form, org: { basicInfo = {} } = {} } = _this.props;
    const { lockFields = [] } = basicInfo || {};
    const { getFieldDecorator, getFieldValue } = form;
    let disabled = _this.getGMJJDisabled();

    if (true) {
      return (
        <React.Fragment>
          <Col span={12}>
            <FormItem  {...formItemLayout2} label={formLabel('国民经济行业', tipMsg['d194Code'])}>
              {getFieldDecorator('d194Code', {
                initialValue: this.state.d194CodeSatate || basicInfo['d194Code'],
                rules: [{ required: !disabled, message: '请选择国民经济行业' }],
              })(
                <DictTreeSelect
                  key={this.state.d194CodeKey}
                  backType={'object'}
                  initValue={this.state.d194CodeSatate || basicInfo['d194Code']}
                  disabled={disabled || lockFields.includes('d194Code')}
                  codeType={'dict_d194'}
                  placeholder={'国民经济行业'}
                  parentDisable={true}
                  showModalIcon={!disabled}
                  onChange={async (e) => {
                    const res = await normalList({ data: { tableCode: 'ccp_unit', colCode: 'd194Code', compareColCode: 'd195Code', colValue: e.key } })
                    if (res.code == 0 && !_isEmpty(res.data)) {
                      let key = Object.keys(res.data)?.[0];
                      let name = res.data[key];
                      this.setState({
                        d195CodeSatate: key,
                        d195CodeKey: moment().valueOf()
                      })
                      this.props.form.setFieldsValue({
                        d195Code: key,
                        d195Name: name
                      })
                    }
                  }}
                />,
              )}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem  {...formItemLayout2} label={formLabel('生产性服务行业', tipMsg['d195Code'])}>
              {getFieldDecorator('d195Code', {
                initialValue: basicInfo['d195Code'] || 'V0000',
                rules: [{ required: !disabled, message: '请选择生产性服务行业' }],
              })(
                <DictTreeSelect
                  key={this.state.d195CodeKey}
                  disabled={disabled || lockFields.includes('d195Code')}
                  backType={'object'}
                  initValue={this.state.d195CodeSatate || basicInfo['d195Code']}
                  codeType={'dict_d195'}
                  placeholder={'生产性服务行业'}
                  parentDisable={true}
                  showModalIcon={!disabled}
                />,
              )}
            </FormItem>
          </Col>
          {getFieldDecorator('d194Name', {
            rules: [{ required: false, message: '' }],
            initialValue: basicInfo['d194Name'],
          })(
            <Input style={{ display: 'none' }} disabled />,
          )}
          {getFieldDecorator('d195Name', {
            rules: [{ required: false, message: '' }],
            initialValue: basicInfo['d195Name'],
          })(
            <Input style={{ display: 'none' }} disabled />,
          )}
        </React.Fragment>
      )
    }
  };
  renderHasLiaisonAgencies = (_this) => {
    const { tipMsg = {}, form, org: { basicInfo = {} } = {} } = _this.props;
    const { getFieldDecorator, getFieldValue } = form;
    let fieldValue = getFieldValue('d01Code');
    if (fieldValue && typeof fieldValue == 'object') {
      fieldValue = fieldValue['key']
    }
    if (`${fieldValue}`.startsWith('1')) {
      return (
        <Col span={12}>
          <LongLabelFormItem label={'是否建立党代表联络工作机构'}
            required={true}
            code={'hasLiaisonAgencies'}
            tipMsg={tipMsg}
            formItemLayout={formItemLayout2}
            formItem={(formItemLayout, code) => {
              return (
                <FormItem {...formItemLayout}
                >
                  {getFieldDecorator(code, {
                    rules: [{ required: true, message: '是否建立党代表联络工作机构' }],
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo[code],
                  })(
                    <Select style={{ width: '100%' }}>
                      <Select.Option value={1}>是</Select.Option>
                      <Select.Option value={0}>否</Select.Option>
                    </Select>
                  )}
                </FormItem>
              )
            }} />
        </Col>
      )
    }
    return null;
  };
  renderHasAnnualMeet = (_this) => {
    const { tipMsg = {}, form, org: { basicInfo = {} } = {} } = _this.props;
    const { getFieldDecorator, getFieldValue } = form;
    let d02Code = getFieldValue('d02Code');
    let val = _this.getLinkedDTOList();
    //关联单位不是选择的“2.与上级党组织相同”
    // if (d02Code !== '2') {
    // 只有关联单位是乡镇912，并且是本级(不选择与上级相同)的时候才出现
    let find = val.find(it => (it?.unit?.d04Code || 'zzzzz').startsWith('912') && d02Code !== '2' && d02Code !== '4');
    if (find) {
      return (
        <Col span={12}>
          <FormItem
            label={formLabel('是否试行乡镇党代会年会制', tipMsg['hasAnnualMeet'])}
            {...formItemLayout3}
          >
            {getFieldDecorator('hasAnnualMeet', {
              initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['hasAnnualMeet'],
              rules: [{ required: true, message: '是否试行乡镇党代会年会制' }],
            })(
              <Select style={{ width: '100%' }}>
                <Select.Option value={1}>是</Select.Option>
                <Select.Option value={0}>否</Select.Option>
              </Select>
            )}
          </FormItem>
        </Col>
      )
    }
  }
  // 获取上级党组织列表
  handleClick = async (p?: any) => {
    let orgType = this.props.form.getFieldValue('d01Code');
    if (typeof (orgType) === 'object') {
      orgType = orgType.key
    }
    if (orgType == '632' || orgType == '634') {
      const { basicInfo: { code: orgCode = '' } = {} } = this.props.org;
      const { linkedDTOListLh = [] } = this.state;
      const { code }: any = getSession('org') || {};
      const { code: resCode = 500, data = [] } = await superUnitOrgLinked({
        code: orgCode || code,
        added: _isEmpty(orgCode) ? true : undefined,
      })
      if (resCode === 0) {
        this.setState({
          superUnitOrgLinkedList: data, // 上级党组织
        })
        if (p === 'add') {
          let newArr = _uniqBy([...linkedDTOListLh, ...data], (obj) => obj.unitCode) || [];
          // 需要回显原来的住单位,在切换关联单位 3和4
          let oldmainunit = this.state?.linkedDTOList_old?.find(it => it.isUnitMain == 1)?.unit
          if (oldmainunit) {
            newArr = newArr.map(it => {
              if (oldmainunit?.code == it?.unit?.code) {
                return {
                  ...it,
                  isUnitMain: 1
                }
              }
              return it
            })
          }
          this.setState({ linkedDTOListLh: newArr, timeKey2: moment().valueOf() });
          this.props.form.setFieldsValue({
            linkedDTOList: newArr
          })
        }
        if (p === 'reduce') {
          let resArr = _differenceBy(linkedDTOListLh, data, (obj: any) => obj.unitCode) || [];
          this.setState({ linkedDTOListLh: resArr, timeKey2: moment().valueOf() });
          this.props.form.setFieldsValue({
            linkedDTOList: resArr
          })
        }
      }
    }
  }
  // 关联单位点击与上级党组织相同时，获取上级党组织关联单位的主单位，d04Code判断单位类别
  getUpOrgLinkMainUnit = async () => {
    const { basicInfo: { code: orgCode = '' } = {} } = this.props.org;
    const { code }: any = getSession('org') || {};
    const { code: resCode = 500, data = {} } = await getMainUnitByOrg({
      orgCode: orgCode || code
    })
    if (resCode === 0) {
      this.setState({
        upOrgLinkMainUnit: data
      })
      // // 与上级党组织相同时，需改变国民经济赋值
      // let arr = [{unit:data}]
      // let flag = this.showGUOMINGJINGJI(arr)
      // if(flag){
      //   let d194Code = _get(arr,'[0].unit.d194Code')
      //   let d194Name = _get(arr,'[0].unit.d194Name')
      //   let d195Code = _get(arr,'[0].unit.d195Code')
      //   let d195Name = _get(arr,'[0].unit.d195Name')
      //   this.setState({
      //     d194CodeSatate:d194Code,
      //     d195CodeSatate:d195Code,
      //     d195CodeKey:moment().valueOf(),
      //     d194CodeKey:moment().valueOf()
      //   },()=>{
      //     this.props.form.setFieldsValue({
      //       d194Code:d194Code,
      //       d195Code:d195Code,
      //       d194Name:d194Name,
      //       d195Name:d195Name
      //     })
      //   })
      // }
    } else {
      this.setState({
        upOrgLinkMainUnit: {}
      })
    }
  }

  render() {
    const { orgAdd, orgUpdate, tipMsg = {}, commonDict } = this.props;
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { basicInfo = {} } = this.props.org;
    const { lockFields = [] } = basicInfo || {};
    return (
      <Form {...formItemLayout} key={this.state.timeKey}>
        <LockMsg basicInfo={{ ...basicInfo, unlockObject: '3' }} />
        <FormItem
          label={formLabel('上级组织', tipMsg['parentCode'])}
        >
          {getFieldDecorator('parentCode', {
            initialValue: basicInfo['parentCode'] || (getSession('org') ? [getSession('org')] : undefined), // 当新增时 自动带入树的信息
            rules: [{ required: true, message: '请选择上级组织' }],
          })(
            <OrgSelect
              orgTypeList={['1', '2', '5']} org={basicInfo['code'] ? basicInfo : undefined}
              initValue={basicInfo['parentOrgName'] || (getSession('org')?.['name'] || undefined)}
              disabled={!!basicInfo['parentCode']}
              placeholder={'上级组织'}
              onChange={() => {
                this.props.form.setFieldsValue({ d01Code: undefined })
                this['d01Ref'] && this['d01Ref'].clearAll()
              }}
            />
          )}
        </FormItem>

        <FormItem
          label={formLabel('组织全称', tipMsg['name'])}
        >
          {getFieldDecorator('name', {
            initialValue: basicInfo['name'],
            rules: [{ required: true, message: '请输入组织全称' }, { validator: this.orgName }],
          })(
            <Input placeholder={'组织全称'} disabled={lockFields.includes('name')} />
          )}
        </FormItem>

        {/*<FormItem*/}
        {/*  label={ formLabel('组织简称',tipMsg['shortName']) }*/}
        {/*>*/}
        {/*  {getFieldDecorator('shortName', {*/}
        {/*    initialValue:basicInfo['shortName'],*/}
        {/*    rules: [{ required: true, message: '请输入组织简称' }],*/}
        {/*  })(*/}
        {/*    <Input placeholder={'组织简称'} />*/}
        {/*  )}*/}
        {/*</FormItem>*/}

        {/* 组织类别 */}
        {this.renderD01Code(this)}

        {/* 隶属关系 */}
        {this.renderD03Code(this)}

        <Row>
          <Col span={12}>
            <FormItem
              label={formLabel('组织代码', tipMsg['zbCode'])}
              {...formItemLayout2}
            >
              {getFieldDecorator('zbCode', {
                initialValue: basicInfo['zbCode'],
                rules: [{ required: false, message: '组织代码' }],
              })(
                <Input placeholder={'组织代码'} disabled={true} />
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label={formLabel('行政区划', tipMsg['administrativeRegion'])}
              {...formItemLayout2}
            >
              {getFieldDecorator('administrativeRegion', {
                initialValue: basicInfo['administrativeRegion'],
                rules: [{ required: true, message: '行政区划' }],
              })(
                <DictTreeSelect
                  parentDisable={true}
                  placeholder={'请选择行政区域'}
                  initValue={basicInfo['administrativeRegion'] || undefined}
                  // 2025/2/26  陈实  党组织编辑，禁止编辑行政区划
                  disabled={!!basicInfo['parentCode']}
                  codeType={'dict_d151'}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label={formLabel('维护时间', tipMsg['updateTime'])}
              {...formItemLayout2}
            >
              {getFieldDecorator('updateTime', {
                initialValue: basicInfo['updateTime'] || undefined,
                rules: [{ required: false, message: '维护时间' }],
              })(
                <Date placeholder={'维护时间'} disabled={true} mode={'YYYY-MM-DD'} />
              )}
            </FormItem>
          </Col>

          {(function (_this) {
            let d01Code = _this.props.form.getFieldValue('d01Code');
            if (typeof d01Code == 'object') {
              d01Code && (d01Code = d01Code['key']);
            }
            if (!(`${d01Code}`.startsWith('1') || `${d01Code}`.startsWith('2'))) {
              return (
                <Col span={12}>
                  <FormItem
                    label={formLabel('组织书记', tipMsg['secretary'])}
                    {...formItemLayout2}
                  >
                    {getFieldDecorator('secretary', {
                      initialValue: basicInfo['secretary'],
                      rules: [{ required: false, message: '组织书记' }],
                    })(
                      <Input placeholder={'组织书记'} disabled />
                    )}
                  </FormItem>
                </Col>
              )
            }
          })(this)}


          <Col span={12}>
            <FormItem
              label={formLabel('组织联系人', tipMsg['contacter'])}
              {...formItemLayout2}
            >
              {getFieldDecorator('contacter', {
                initialValue: basicInfo['contacter'],
                rules: [{ required: true, message: '组织联系人' }, { validator: (...e) => validateLength(e, 16, 50) }],
              })(
                <Input placeholder={'组织联系人'} disabled={lockFields.includes('contacter')} />
              )}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('联系电话', tipMsg['contactPhone'])}
              {...formItemLayout2}
            >
              {getFieldDecorator('contactPhone', {
                getValueFromEvent: e => _trim(e.target.value),
                initialValue: basicInfo['contactPhone'],
                // rules: [{ required: true, message: '联系电话' }, { pattern: new RegExp('((\\d{11})|^((\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1})|(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1}))$)'), message: '请输入正确的联系电话' }],
                rules: [{ required: true, message: '联系电话' }, { validator: validateMobilePhoneNumber }],
              })(
                <Input placeholder={'联系电话'} disabled={lockFields.includes('contactPhone')} />
              )}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('建立日期', tipMsg['createDate'])}
              {...formItemLayout2}
            >
              {getFieldDecorator('createDate', {
                initialValue: basicInfo['createDate'] ? moment(basicInfo['createDate']) : undefined,
                rules: [{ required: false, message: '建立日期' }],
              })(
                <Date disabled={lockFields.includes('createDate')} />
              )}
            </FormItem>
          </Col>

          {/* 是否离退休党组织 */}
          {this.renderIsRetire(this)}

          {/* <Col span={12}>
            <FormItem
              label={ formLabel('是否流动党员党组织',tipMsg['isFlow']) }
              {...formItemLayout2}
            >
              {getFieldDecorator('isFlow', {
                initialValue:basicInfo['isFlow'] || 0,
                valuePropName:'checked',
                rules: [{ required: false, message: '是否流动党员党组织' }],
              })(
                <Switch/>
              )}
            </FormItem>
          </Col> */}

          {/* 基层党组织的基础信息中增加是否易地扶贫搬迁党组织 */}
          {this.renderHasMoveOrg(this)}


          {/* :党组织 关联单位为学校的、或是离退休党组织的, 显示国民经济行业 和 生产性服务行业 */}
          {/* 关联单位同时满足行政村、法人单位，也需要显示国民经济行业 和 生产性服务行业 */}
          {this.renderGUOMINJINGJIOrg(this)}


          {
            (function () {
              // let fieldValue = getFieldValue('d01Code');
              // if(fieldValue && typeof fieldValue=='object'){
              //   fieldValue=fieldValue['key']
              // }
              // if(`${fieldValue}`.startsWith('2')){
              //   return(
              //     <React.Fragment>
              //       <Col span={12}>
              //         <FormItem
              //           label={ formLabel('法定代表人是否党员',tipMsg['isFlow']) }
              //           {...formItemLayout2}
              //         >
              //           {getFieldDecorator('hasRepresentative', {
              //             initialValue:basicInfo['hasRepresentative'],
              //             // valuePropName:'checked',
              //             rules: [{ required: true, message: '法定代表人是否党员' }],
              //           })(
              //             <Select placeholder={'请选择'}>
              //               <Select.Option value={1}>是</Select.Option>
              //               <Select.Option value={0}>否</Select.Option>
              //             </Select>
              //           )}
              //         </FormItem>
              //       </Col>
              //       {/*当法定代表人是否党员选择是的时候增加此字段*/}
              //       {
              //         getFieldValue('hasRepresentative') ? <Col span={12}>
              //           <FormItem
              //             label={ formLabel('是否兼任企业党组书记',tipMsg['hasProperSecretary']) }
              //             {...formItemLayout2}
              //           >
              //             {getFieldDecorator('hasProperSecretary', {
              //               initialValue:basicInfo['hasProperSecretary'],
              //               // valuePropName:'checked',
              //               rules: [{ required: true, message: '是否兼任企业党组书记' }],
              //             })(
              //               <Select placeholder={'请选择'}>
              //                 <Select.Option value={1}>是</Select.Option>
              //                 <Select.Option value={0}>否</Select.Option>
              //               </Select>
              //             )}
              //           </FormItem>
              //         </Col> : null
              //       }
              //       <Col span={12}>
              //         <FormItem
              //           label={ formLabel('是否党建工作指导员联系',tipMsg['hasInstructorContact']) }
              //           {...formItemLayout2}
              //         >
              //           {getFieldDecorator('hasInstructorContact', {
              //             initialValue:basicInfo['hasInstructorContact'],
              //             // valuePropName:'checked',
              //             rules: [{ required: true, message: '是否党建工作指导员联系' }],
              //           })(
              //             <Select placeholder={'请选择'}>
              //               <Select.Option value={1}>是</Select.Option>
              //               <Select.Option value={0}>否</Select.Option>
              //             </Select>
              //           )}
              //         </FormItem>
              //       </Col>

              //       <Col span={12}>
              //         <FormItem
              //           label={ formLabel('党建工作指导员人数',tipMsg['jobInstructorNumber']) }
              //           {...formItemLayout2}
              //         >
              //           {getFieldDecorator('jobInstructorNumber', {
              //             initialValue:basicInfo['jobInstructorNumber'] || 0,
              //             rules: [{ required: true, message: '党建工作指导员人数' }],
              //           })(
              //             <InputNumber min={0} style={{width:'100%'}}/>
              //           )}
              //         </FormItem>
              //       </Col>

              //       <Col span={12}>
              //         <FormItem
              //           label={ formLabel('是否建立工会或共青团组织',tipMsg['isFlow']) }
              //           {...formItemLayout2}
              //         >
              //           {getFieldDecorator('hasUnionOrganization', {
              //             initialValue:basicInfo['hasUnionOrganization'],
              //             // valuePropName:'checked',
              //             rules: [{ required: true, message: '是否建立工会或共青团组织' }],
              //           })(
              //             <Select placeholder={'请选择'}>
              //               <Select.Option value={1}>是</Select.Option>
              //               <Select.Option value={0}>否</Select.Option>
              //             </Select>
              //           )}
              //         </FormItem>
              //       </Col>

              //       <Col span={12}>
              //         <FormItem
              //           label={ formLabel('吸收未转入组织关系党员建立党组织数',tipMsg['absorbedTissueNumber']) }
              //           {...formItemLayout2}
              //         >
              //           {getFieldDecorator('absorbedTissueNumber', {
              //             initialValue:basicInfo['absorbedTissueNumber'] || 0,
              //             // valuePropName:'checked',
              //             rules: [{ required: true, message: '吸收未转入组织关系党员建立党组织数' }],
              //           })(
              //             <InputNumber min={0} style={{width:'100%'}}/>
              //           )}
              //         </FormItem>
              //       </Col>
              //       {/*当吸收未转入组织关系的党员建立党组织数大于0得时候，增加此字段*/}
              //       {
              //         getFieldValue('absorbedTissueNumber')>0 ? <Col span={12}>
              //           <FormItem
              //             label={ formLabel('未转组织关系党员数',tipMsg['notTurnedParty']) }
              //             {...formItemLayout2}
              //           >
              //             {getFieldDecorator('notTurnedParty', {
              //               initialValue:basicInfo['notTurnedParty'] || 0,
              //               // valuePropName:'checked',
              //               rules: [{ required: true, message: '未转组织关系党员数' }],
              //             })(
              //               <InputNumber min={0} style={{width:'100%'}}/>
              //             )}
              //           </FormItem>
              //         </Col> : null
              //       }

              //       <Col span={12}>
              //         <FormItem
              //           label={ formLabel('主要负责人是否党员',tipMsg['hasHeadParty']) }
              //           {...formItemLayout2}
              //         >
              //           {getFieldDecorator('hasHeadParty', {
              //             initialValue:basicInfo['hasHeadParty'],
              //             // valuePropName:'checked',
              //             rules: [{ required: true, message: '主要负责人是否党员' }],
              //           })(
              //             <Select placeholder={'请选择'}>
              //               <Select.Option value={1}>是</Select.Option>
              //               <Select.Option value={0}>否</Select.Option>
              //             </Select>
              //           )}
              //         </FormItem>
              //       </Col>

              //     </React.Fragment>
              //   )
              // }
              // return null
            })()
          }

          {/* 是否建立党代表联络工作机构 */}
          {this.renderHasLiaisonAgencies(this)}

          {/* { (getFieldValue("d01Code") && (getFieldValue("d01Code")["key"]=="131" || getFieldValue("d01Code")["key"]=="132" || getFieldValue("d01Code")["key"]=="133" || getFieldValue("d01Code")["key"]=="134")) &&
            <Col span={12}>
            <FormItem
              label={ formLabel('是否建立党代表联络工作机构',tipMsg['hasLiaisonAgencies']) }
              {...formItemLayout3}
            >
              {getFieldDecorator('hasLiaisonAgencies', {
                initialValue: _isEmpty(basicInfo)? undefined : _isNumber(basicInfo['hasLiaisonAgencies']) ? basicInfo['hasLiaisonAgencies'].toString() : undefined,
                rules: [{ required: false, message: '是否建立党代表联络工作机构' }],
              })(
                <Select style={{width:'100%'}}>
                  <Select.Option value="1">是</Select.Option>
                  <Select.Option value="0">否</Select.Option>
                </Select>
              )}
            </FormItem>
          </Col>} */}

          {/* // 当组织关联得单位类别是乡或者镇得时候:基本信息增加一个:是否试行乡镇党代会年会 */}
          {this.renderHasAnnualMeet(this)}


          {
            (function (_this) {
              let d01Code = _this.props.form.getFieldValue('d01Code');
              if (typeof d01Code == 'object') {
                d01Code && (d01Code = d01Code['key']);
              }
              // 当组织类别为中共各级委员会、中共各级工作委员会大类及其子类的时候，关联单位以及选择单位这两个东西隐藏，没有关联单位这一个操作,放出25
              if (!(`${d01Code}`.startsWith('1') || (`${d01Code}`.startsWith('2') && `${d01Code}` != '25'))) {
                let noIncludeLianHe = ['61', '62', '631', '633', '911', '921', '931', '25', 'A'];
                return (
                  <Fragment>
                    <Col span={24}>
                      <FormItem
                        label={formLabel('关联单位', tipMsg['d02Code'])}
                      >
                        {getFieldDecorator('d02Code', {
                          initialValue: basicInfo['d02Code'] ? basicInfo['d02Code'].startsWith('2') ? '2' : basicInfo['d02Code'] : "1",
                          rules: [{ required: true, message: '请选择' }],
                        })(
                          <Radio.Group buttonStyle="solid" onChange={() => {
                            _this.d02CodeChange()
                          }}>
                            { // 联合党支部不能选择这些
                              // 组织类别选择 其他（文件夹）时显示:单独建立党组织、与上级党组织相同
                              (`${d01Code}` == 'A' || !(`${d01Code}` == '632' || `${d01Code}` == '932' || `${d01Code}` == '634')) && <Fragment>
                                <Radio.Button onClick={() => { _this.handleClick('reduce') }} value="1">单独建立党组织</Radio.Button>
                                <Radio.Button value="2" onClick={() => { _this.getUpOrgLinkMainUnit() }}>与上级党组织相同</Radio.Button>
                              </Fragment>
                            }
                            {
                              // 党委和党总支部,党支部 选择关联单位的时候不应该可以选择联合支部
                              !(noIncludeLianHe.includes(`${d01Code}`)) &&
                              <Fragment>
                                {/* 点击 不与上级党组织所在单位建立联合党支部，调用接口superUnitOrgLinked，把返回的上级数据从 linkedDTOList 移除 */}
                                <Radio.Button onClick={() => { _this.handleClick('reduce') }} value="3">不与上级党组织所在单位建立联合党支部</Radio.Button>
                                {/* 新增时：点击 与上级党组织所在单位建立联合党支部 ， 获取上级党组织列表并展示在 选择单位 里；编辑时：获取的列表与原linkedDTOList数据合并 */}
                                <Radio.Button onClick={() => { _this.handleClick('add') }} value="4">与上级党组织所在单位建立联合党支部</Radio.Button>
                              </Fragment>
                            }
                          </Radio.Group>
                        )}
                      </FormItem>
                    </Col>
                    {/* {
                      (function(props){
                        let value=props.form.getFieldValue('d02Code');
                        if(value==='2' || value.startsWith('2')){
                          return(
                            <Col span={24}>
                              <FormItem
                                label={ formLabel('法人单位情况',tipMsg['d02Code2']) }
                              >
                                {getFieldDecorator('d02Code2', {
                                  initialValue:basicInfo['d02Code'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <Radio.Group buttonStyle="solid"
                                              // disabled={!!basicInfo['code']} 编辑不要判断
                                  >
                                    <Radio.Button value="21">与上级党组织在同一法人单位</Radio.Button>
                                    <Radio.Button value="22">与上级党组织不在同一法人单位</Radio.Button>
                                  </Radio.Group>
                                )}
                              </FormItem>
                            </Col>
                          )
                        }
                      })(_this.props)
                    } */}
                    {
                      (function (_this) {
                        let d02Code = _this.props.form.getFieldValue('d02Code');
                        // let d02Code2 = _this.props.form.getFieldValue('d02Code2');
                        if (d02Code == '2'
                          //  && d02Code2 == '21'
                        ) {
                          return null
                        } else {
                          return (
                            <Col span={24}>
                              <FormItem
                                label="选择单位"
                              >
                                {getFieldDecorator('linkedDTOList', {
                                  initialValue: basicInfo['linkedDTOList'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <React.Fragment key={_this.state.timeKey2}>
                                    <LinkedUnit isCreateOrg={undefined} data={_this.state.linkedDTOListLh} onChange={_this.linkedChange}
                                      disabledColFunc={(record) => {
                                        const { mainOrgName = '' } = record || {};
                                        return !!mainOrgName;
                                      }}
                                      org={(basicInfo?.code && basicInfo?.orgCode) ? { code: basicInfo?.code, orgCode: basicInfo?.orgCode } : getSession('org')}
                                    />
                                  </React.Fragment>
                                )}
                              </FormItem>
                            </Col>
                          )
                        }
                      })(_this)
                    }
                  </Fragment>
                )
              }
            })(this)
          }

          {/* 当党组织里面关联单位为类型为：单独建立党组织、不与上级党组织建立联合党支部、与上级党组织建立联合党支部并且关联的单位中主单位是
              （企业[单位类别4开头的]并且经济控制类型是私有经济[d16Code是345开头的）or（单位行业分类互联网（是1）的时候d114Code)         （企业and 私有经济 ）or（企业 and 互联网）
              增加显示填写：是否由企业中高层管理人员担任党组织书记[必填]  hasSecretaryHighLevel
          */}
          {
            (function (_this) {
              let val = _this.getLinkedDTOList(); // 选择单位
              let d01Code = _this.props.form.getFieldValue('d01Code'); // 组织类别
              let d02Code = _this.props.form.getFieldValue('d02Code'); // 关联单位
              // const { upOrgLinkMainUnit: { d04Code = '', code='' } = {} } = _this.state; // 点击 与上级党组织相同 时，上级党组织关联的主单位的单位类别d04Code
              const { superUnitOrgLinkedList: [item = {}] = [] } = _this.state; // 点击 与上级党组织相同 时，上级党组织关联的主单位的单位类别d04Code
              if (typeof d01Code == 'object') {
                d01Code && (d01Code = d01Code['key']);
              }
              if (d02Code === '1' || d02Code === '3' || d02Code === '4') {
                //  为什么这么写：新增的 关联单位 的数据外层没有d16Code、d114Code，从unit里取；编辑时，已有的 关联单位，外层列表有d16Code、d114Code
                let find1 = val.find(it => (it?.isUnitMain === 1) && (it?.unit?.d04Code || 'zzzzz').startsWith('4') && (((it?.d16Code || '').startsWith('3') || (it?.unit?.d16Code || '').startsWith('3')) || ((it?.d16Code || '').startsWith('4') || (it?.unit?.d16Code || '').startsWith('4')) || ((it?.d16Code || '').startsWith('5') || (it?.unit?.d16Code || '').startsWith('5'))));
                let find2 = val.find(it => (it?.isUnitMain === 1) && (it?.unit?.d04Code || 'zzzzz').startsWith('4') && ((it?.d114Code || 'zzzzz').startsWith('1') || (it?.unit?.d114Code || 'zzzzz').startsWith('1')));
                //  如果是与上级党组织所在单位建立联合党支部，并且选择了上级的主单位为他自己的主单位，不显示 hasSecretaryHighLevel 这个字段
                let find3 = false;
                if (d02Code === '4') {
                  let obj = val.find(it => (it?.isUnitMain === 1) && (it?.unit?.code === item?.unit?.code));
                  if (obj) {
                    find3 = true
                  }
                }
                if ((find1 || find2) && !find3) {
                  return (
                    <Fragment>
                      <Col span={12}>
                        <LongLabelFormItem label={'是否由企业中高层管理人员担任党组织书记'}
                          required={true}
                          code={'hasSecretaryHighLevel'}
                          tipMsg={tipMsg}
                          formItemLayout={formItemLayout2}
                          formItem={(formItemLayout, code) => {
                            return (
                              <FormItem {...formItemLayout}
                              >
                                {getFieldDecorator(code, {
                                  rules: [{ required: true, message: '是否由企业中高层管理人员担任党组织书记' }],
                                  initialValue: basicInfo[code],
                                })(
                                  <YN init={basicInfo[code]} />
                                )}
                              </FormItem>
                            )
                          }} />
                      </Col>
                    </Fragment>
                  )
                }
              }
            })(this)
          }
          {/* 当党组织里面关联单位为类型为：单独建立党组织、不与上级党组织建立联合党支部、与上级党组织建立联合党支部并且关联的单位中主单位是社会组织（单位类别为5开头）的时候，
            增加显示填写：上级党组织是否全额下拨本党组织上缴党费[必填]、上级党组织是否给予经费支持[必填]、是否自有党组织活动场所[必填]
          */}
          {
            (function (_this) {
              let val = _this.getLinkedDTOList(); // 选择单位
              let d01Code = _this.props.form.getFieldValue('d01Code'); // 组织类别
              let d02Code = _this.props.form.getFieldValue('d02Code'); // 关联单位
              const { superUnitOrgLinkedList: [item = {}] = [] } = _this.state; // 与上级党组织相同 时
              if (typeof d01Code == 'object') {
                d01Code && (d01Code = d01Code['key']);
              }
              if (d02Code === '1' || d02Code === '3' || d02Code === '4') {
                let find = val.find(it => (it?.isUnitMain === 1) && (it?.unit?.d04Code || 'zzzzz').startsWith('5'));
                {/* 与上级党组织所在单位建立联合党支部，上级单位是主单位的，不显示这几项 */ }
                let find2 = false;
                if (d02Code === '4') {
                  let obj = val.find(it => (it?.isUnitMain === 1) && (it?.unit?.code === item?.unit?.code));
                  if (obj) {
                    find2 = true
                  }
                }
                if (find && !find2) {
                  return (
                    <Fragment>
                      <Col span={12}>
                        <LongLabelFormItem label={'上级党组织是否全额下拨本党组织上缴党费'}
                          required={true}
                          code={'hasPullDownAllPartyFee'}
                          tipMsg={tipMsg}
                          formItemLayout={formItemLayout2}
                          formItem={(formItemLayout, code) => {
                            return (
                              <FormItem {...formItemLayout}
                              >
                                {getFieldDecorator(code, {
                                  rules: [{ required: true, message: '上级党组织是否全额下拨本党组织上缴党费' }],
                                  initialValue: basicInfo[code],
                                })(
                                  <YN init={basicInfo[code]} />
                                )}
                              </FormItem>
                            )
                          }} />
                      </Col>
                      <Col span={12}>
                        <LongLabelFormItem label={'上级党组织是否给予经费支持'}
                          required={true}
                          code={'hasGiveFundsSupport'}
                          tipMsg={tipMsg}
                          formItemLayout={formItemLayout2}
                          formItem={(formItemLayout, code) => {
                            return (
                              <FormItem {...formItemLayout}
                              >
                                {getFieldDecorator(code, {
                                  rules: [{ required: true, message: '上级党组织是否给予经费支持' }],
                                  initialValue: basicInfo[code],
                                })(
                                  <YN init={basicInfo[code]} />
                                )}
                              </FormItem>
                            )
                          }} />
                      </Col>
                      <Col span={12}>
                        <LongLabelFormItem label={'是否自有党组织活动场所'}
                          required={true}
                          code={'hasOwnActivityPlace'}
                          tipMsg={tipMsg}
                          formItemLayout={formItemLayout2}
                          formItem={(formItemLayout, code) => {
                            return (
                              <FormItem {...formItemLayout}
                              >
                                {getFieldDecorator(code, {
                                  rules: [{ required: true, message: '是否自有党组织活动场所' }],
                                  initialValue: basicInfo[code],
                                })(
                                  <YN init={basicInfo[code]} />
                                )}
                              </FormItem>
                            )
                          }} />
                      </Col>
                    </Fragment>
                  )
                }
              }
            })(this)
          }

          {/*{*/}
          {/*  d02Code == '2' && d02Code2 == '21' ? null :*/}
          {/*    <Col span={24}>*/}
          {/*      <FormItem*/}
          {/*        label="选择单位"*/}
          {/*      >*/}
          {/*        {getFieldDecorator('linkedDTOList', {*/}
          {/*          initialValue:basicInfo['linkedDTOList'],*/}
          {/*          rules: [{ required: true, message: '请选择' }],*/}
          {/*        })(*/}
          {/*          <React.Fragment>*/}
          {/*            <LinkedUnit data={basicInfo['linkedDTOList']} onChange={this.linkedChange}/>*/}
          {/*          </React.Fragment>*/}
          {/*        )}*/}
          {/*      </FormItem>*/}
          {/*    </Col>*/}
          {/*}*/}

          {
            (function (_this) {
              let val = _this.getLinkedDTOList();
              let d01Code = _this.props.form.getFieldValue('d01Code');
              if (typeof d01Code == 'object') {
                d01Code && (d01Code = d01Code['key']);
              }
              if (!_isEmpty(val)) {
                // 当关联单位是公立医院，党组织类型是党支部的时候组织组织基础信息编
                let find = val.find(it => (it?.unit?.d04Code || 'zzzzz').startsWith('341') && `${d01Code}`.startsWith('63'));
                if (find) {
                  return (
                    <Fragment>
                      <Col span={12}>
                        <FormItem
                          label="党支部书记是否“双带头人”"
                          {...formItemLayout2}
                        >
                          {getFieldDecorator('hasSjsdtr', {
                            rules: [{ required: true, message: '党支部书记是否“双带头人”' }],
                            initialValue: basicInfo['hasSjsdtr'],
                          })(
                            <YN init={basicInfo['hasSjsdtr']} />
                          )}
                        </FormItem>
                      </Col>
                      <Col span={12}>
                        <LongLabelFormItem label={'党支部书记是否内设机构负责人'}
                          required={true}
                          code={'hasSecretaryisinsideleader'}
                          tipMsg={tipMsg}
                          formItemLayout={formItemLayout2}
                          formItem={(formItemLayout, code) => {
                            return (
                              <FormItem {...formItemLayout}
                              >
                                {getFieldDecorator(code, {
                                  rules: [{ required: true, message: '党支部书记是否内设机构负责人' }],
                                  initialValue: basicInfo[code],
                                })(
                                  <YN init={basicInfo[code]} />
                                )}
                              </FormItem>
                            )
                          }} />
                        {/* <FormItem*/}
                        {/*  label="党支部书记是否内设机构负责人"*/}
                        {/*  {...formItemLayout2}*/}
                        {/*>*/}
                        {/*  {getFieldDecorator('hasSecretaryisinsideleader', {*/}
                        {/*    rules: [{ required: true, message: '党支部书记是否内设机构负责人' }],*/}
                        {/*    initialValue: basicInfo['hasSecretaryisinsideleader'],*/}
                        {/*  })(*/}
                        {/*    <YN init={basicInfo['hasSecretaryisinsideleader']}/>*/}
                        {/*  )}*/}
                        {/*</FormItem>*/}
                      </Col>
                      <Col span={12}>
                        <FormItem
                          label={formLabel('本年度发展卫生技术人员党员', tipMsg['yearDevelopMemMedicine'])}
                          {...formItemLayout2}
                        >
                          {getFieldDecorator('yearDevelopMemMedicine', {
                            initialValue: basicInfo['yearDevelopMemMedicine'],
                            rules: [{ required: false, message: '本年度发展卫生技术人员党员' }],
                          })(
                            <InputNumber placeholder={'本年度发展卫生技术人员党员'} style={{ width: '100%' }} />
                          )}
                        </FormItem>
                      </Col>
                    </Fragment>
                  )
                }
              }
            })(this)
          }

          {
            (function (_this) {
              let val = _this.getLinkedDTOList();
              let d01Code = _this.props.form.getFieldValue('d01Code');
              if (typeof d01Code == 'object') {
                d01Code && (d01Code = d01Code['key']);
              }
              if (!_isEmpty(val)) {
                // 当关联主单位以32开头  党支部 ，出现 1.党支部书记是否“双带头人” 2. 党支部书记是否内设机构负责人
                let find = val.find(it => (it?.unit?.d04Code || 'zzzzz').startsWith('32') && `${d01Code}`.startsWith('63'));
                if (find) {
                  return (
                    <Fragment>
                      <Col span={12}>
                        <FormItem
                          label="党支部书记是否“双带头人”"
                          {...formItemLayout2}
                        >
                          {getFieldDecorator('hasSjsdtr', {
                            rules: [{ required: true, message: '党支部书记是否“双带头人”' }],
                            initialValue: basicInfo['hasSjsdtr'],
                          })(
                            <YN init={basicInfo['hasSjsdtr']} />
                          )}
                        </FormItem>
                      </Col>
                      <Col span={12}>
                        <LongLabelFormItem label={'党支部书记是否内设机构负责人'}
                          required={true}
                          code={'hasSecretaryisinsideleader'}
                          tipMsg={tipMsg}
                          formItemLayout={formItemLayout2}
                          formItem={(formItemLayout, code) => {
                            return (
                              <FormItem {...formItemLayout}
                              >
                                {getFieldDecorator(code, {
                                  rules: [{ required: true, message: '党支部书记是否内设机构负责人' }],
                                  initialValue: basicInfo[code],
                                })(
                                  <YN init={basicInfo[code]} />
                                )}
                              </FormItem>
                            )
                          }} />
                      </Col>
                    </Fragment>
                  )
                }
              }
            })(this)
          }

          {
            (function (_this) {
              let val = _this.getLinkedDTOList();
              let d01Code = _this.props.form.getFieldValue('d01Code');
              let d02Code = _this.props.form.getFieldValue('d02Code');
              const { upOrgLinkMainUnit: { d04Code = '' } = {} } = _this.state; // 与上级党组织相同时，上级党组织关联的主单位的单位类别
              if (typeof d01Code == 'object') {
                d01Code && (d01Code = d01Code['key']);
              }
              if (!_isEmpty(val) || d02Code === '2') {
                // 1.组织类型为支部时，单独建立党组织时，关联主单位为高校 民办普通高校 ：显示支部类型
                let find = val.find(it => ((it?.unit?.d04Code || 'zzzzz').startsWith('331') || (it?.unit?.d04Code || 'zzzzz').startsWith('5211')) && it?.isUnitMain === 1 && `${d01Code}`.startsWith('631') && d02Code === '1');
                // 2.组织类型为支部时，与上级党组织相同，上级党组织关联的主单位是高校：显示支部类型
                let find1 = false;
                if ((d04Code.startsWith('331') || d04Code.startsWith('5211')) && `${d01Code}`.startsWith('631') && d02Code === '2') {
                  find1 = true
                } else {
                  find1 = false
                }
                // 3.组织类型为联合支部时，关联的主单位是高校时：显示支部类型
                let find2 = val.find(it => ((it?.unit?.d04Code || 'zzzzz').startsWith('331') || (it?.unit?.d04Code || 'zzzzz').startsWith('5211')) && it?.isUnitMain === 1 && (`${d01Code}`.startsWith('632') || `${d01Code}`.startsWith('634')));
                if (find || find1 || find2) {
                  return (
                    <Fragment>
                      <Col span={12}>
                        <FormItem label={formLabel('支部类型', tipMsg['d113Code'])}
                          {...formItemLayout2}>
                          {getFieldDecorator('d113Code', {
                            initialValue: basicInfo['d113Code'] || '',
                            rules: [
                              { required: true, message: '请选择!' },
                            ],
                          })(
                            <DictSelect
                              initValue={basicInfo['d113Code']}
                              codeType={'dict_d113'}
                              placeholder={'支部类型'}
                            />
                          )}
                        </FormItem>
                      </Col>
                      {
                        (function () {
                          let val = _this.props.form.getFieldValue('d113Code');
                          if (typeof val == 'object') {
                            val && (val = val['key']);
                          }
                          if (val === '1') {
                            return (
                              <Fragment>
                                <Col span={12}>
                                  <FormItem
                                    label="教师党支部书记是否是“双带头人”"
                                    {...formItemLayout2}
                                  >
                                    {getFieldDecorator('hasTeachersDoubleLeaders', {
                                      rules: [{ required: true, message: '教师党支部书记是否是“双带头人”' }],
                                      initialValue: basicInfo['hasTeachersDoubleLeaders'],
                                    })(
                                      <YN init={basicInfo['hasTeachersDoubleLeaders']} />
                                    )}
                                  </FormItem>
                                </Col>
                                {/* <Col span={12}>
                                  <FormItem
                                    label={formLabel('本年度院系党委书记参加培训人次', tipMsg['yearTraining'])}
                                    {...formItemLayout2}
                                  >
                                    {getFieldDecorator('yearTraining', {
                                      initialValue: basicInfo['yearTraining'],
                                      rules: [{ required: false, message: '本年度院系党委书记参加培训人次' }],
                                    })(
                                      <InputNumber placeholder={'本年度院系党委书记参加培训人次'} style={{ width: '100%' }} />
                                    )}
                                  </FormItem>
                                </Col> */}
                                {/* <Col span={12}>
                                  <FormItem
                                    label={formLabel('本年度毕业生党员', tipMsg['graduatePartyMember'])}
                                    {...formItemLayout2}
                                  >
                                    {getFieldDecorator('graduatePartyMember', {
                                      initialValue: basicInfo['graduatePartyMember'],
                                      rules: [{ required: false, message: '本年度毕业生党员' }],
                                    })(
                                      <InputNumber placeholder={'本年度毕业生党员'} style={{ width: '100%' }} />
                                    )}
                                  </FormItem>
                                </Col> */}
                              </Fragment>
                            )
                          }
                        })()
                      }


                    </Fragment>
                  )
                }
              }
            })(this)
          }
          {
            (function (_this) {
              let val = _this.getLinkedDTOList();
              // 党组织基础信息中，当党组织关联单位性质是社会组织的时候党组织基础信息中增加信息项
              // 主单位是社会组织时这些选项出来了， 主单位不是社会组织，这些选项不要。
              // 20240201增加判断 d02Code != 2 (本级才显示, 下级的不显示)
              let d02Code = _this.props.form.getFieldValue('d02Code');

              let find = val.find(it => ((it?.unit?.d04Code || 'zzzzz').startsWith('5') && it?.isUnitMain == 1));
              if (find && (d02Code != 2)) {
                return (
                  <Fragment>
                    <Col span={12}>
                      <FormItem
                        label="是否配备专职党务工作者"
                        {...formItemLayout2}
                      >
                        {getFieldDecorator('hasPartyAffairsWorkers', {
                          rules: [{ required: true, message: '是否配备专职党务工作者' }],
                          initialValue: basicInfo['hasPartyAffairsWorkers'],
                        })(
                          <YN init={basicInfo['hasPartyAffairsWorkers']} />
                        )}
                      </FormItem>
                    </Col>
                    {/* <Col span={12}>
                      <FormItem
                        label="党建工作指导员数"
                        {...formItemLayout2}
                      >
                        {getFieldDecorator('buildWorkInstructors', {
                          rules: [{ required: true, message: '党建工作指导员数' }],
                          initialValue: basicInfo['buildWorkInstructors'],
                        })(
                          <InputNumber min={0} style={{ width: '100%' }} />
                        )}
                      </FormItem>
                    </Col> */}
                    {/* 登记级别，这个应该是单位的属性，不应该是党组织的属性，需要改在单位上 */}
                    {/* <Col span={12}>
                      <FormItem label={formLabel('登记级别', tipMsg['d118Code'])}
                        {...formItemLayout2}>
                        {getFieldDecorator('d118Code', {
                          initialValue: basicInfo['d118Code'] || '',
                          rules: [
                            { required: true, message: '请选择!' },
                          ],
                        })(
                          <DictSelect
                            initValue={basicInfo['d118Code']}
                            codeType={'dict_d118'}
                            placeholder={'登记级别'}
                          />
                        )}
                      </FormItem>
                    </Col> */}
                    <Col span={12}>
                      <FormItem label={formLabel('党组织书记情况', tipMsg['d119Code'])}
                        {...formItemLayout2}>
                        {getFieldDecorator('d119Code', {
                          initialValue: basicInfo['d119Code'] || '',
                          rules: [
                            { required: true, message: '请选择!' },
                          ],
                        })(
                          <DictSelect
                            initValue={basicInfo['d119Code']}
                            codeType={'dict_d119'}
                            placeholder={'党组织书记情况'}
                          />
                        )}
                      </FormItem>
                    </Col>
                  </Fragment>
                )
              }
            })(this)
          }

          {/* {
            (function (_this) {
              let d01Code = _this.props.form.getFieldValue('d01Code');
              if (typeof d01Code == 'object') {
                d01Code && (d01Code = d01Code['key']);
              }
              let hasWorkingBody = _this.props.form.getFieldValue('hasWorkingBody');
              // 党组织基础信息当组织类别为中共各级委员会中
              if (`${d01Code}`.startsWith('1')) {
                return (
                  <Fragment>
                    <Col span={12}>
                      <LongLabelFormItem label={'是否依托组织部门成立非公党工委'}
                        required={true}
                        code={'hasWorkCommittee'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '是否依托组织部门成立非公党工委' }],
                                initialValue: basicInfo[code],
                              })(
                                <YN init={basicInfo[code]} />
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>
                    <Col span={12}>
                      <LongLabelFormItem label={'非公党工委是否设立专门办事机构'}
                        required={true}
                        code={'hasWorkingBody'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '非公党工委是否设立专门办事机构' }],
                                initialValue: basicInfo[code],
                              })(
                                <YN init={basicInfo[code]} />
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>
                    {
                      hasWorkingBody == 1 &&
                      <Col span={12}>
                        <LongLabelFormItem label={'非公党工委办事机构工作人员编制（个）'}
                          required={true}
                          code={'workingBodyNumber'}
                          tipMsg={tipMsg}
                          formItemLayout={formItemLayout2}
                          formItem={(formItemLayout, code) => {
                            return (
                              <FormItem {...formItemLayout}
                              >
                                {getFieldDecorator(code, {
                                  rules: [{ required: true, message: '非公党工委办事机构工作人员编制（个）' }],
                                  initialValue: basicInfo[code],
                                })(
                                  <InputNumber min={0} style={{ width: '100%' }} />
                                )}
                              </FormItem>
                            )
                          }} />
                      </Col>
                    }
                    <Col span={12}>
                      <LongLabelFormItem label={'组织部门（非公党工委）直接管理的非公企业党组织数'}
                        required={true}
                        code={'manageOrganizationNumber'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '组织部门（非公党工委）直接管理的非公企业党组织数' }],
                                initialValue: basicInfo[code],
                              })(
                                <InputNumber min={0} style={{ width: '100%' }} />
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>
                    <Col span={12}>
                      <LongLabelFormItem label={'组织部门（非公党工委）直接联系的非公企业党组织数'}
                        required={true}
                        code={'connectOrganizationNumber'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '组织部门（非公党工委）直接联系的非公企业党组织数' }],
                                initialValue: basicInfo[code],
                              })(
                                <InputNumber min={0} style={{ width: '100%' }} />
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>

                    <Col span={12}>
                      <LongLabelFormItem label={'财政专项列支非公企业党建工作经费（万元）'}
                        required={true}
                        code={'fiscalFunds'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '财政专项列支非公企业党建工作经费（万元）' }],
                                initialValue: basicInfo[code],
                              })(
                                <InputNumber min={0} style={{ width: '100%' }} />
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>
                    <Col span={12}>
                      <LongLabelFormItem label={'党费拨补非公企业党建工作经费 （万元）'}
                        required={true}
                        code={'partyExpenses'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '党费拨补非公企业党建工作经费 （万元）' }],
                                initialValue: basicInfo[code],
                              })(
                                <InputNumber min={0} style={{ width: '100%' }} />
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>
                    <Col span={12}>
                      <LongLabelFormItem label={'非公企业集聚区综合性党群活动服务中心（个）'}
                        required={true}
                        code={'activityServiceCenter'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '非公企业集聚区综合性党群活动服务中心（个）' }],
                                initialValue: basicInfo[code],
                              })(
                                <InputNumber min={0} style={{ width: '100%' }} />
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>
                    <Col span={12}>
                      <LongLabelFormItem label={'新建立非公企业集聚区综合性党群活动服务中心（个）'}
                        required={true}
                        code={'newActivityServiceCenter'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '新建立非公企业集聚区综合性党群活动服务中心（个）' }],
                                initialValue: basicInfo[code],
                              })(
                                <InputNumber min={0} style={{ width: '100%' }} />
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>
                  </Fragment>
                )
              }
            })(this)
          } */}

          {/* 当党组织关联的单位的单位类别是912乡镇、91街道、92村和社区时，增加以下信息项目: */}
          {this.renderJiTi()}

          <Col span={12}>
            <FormItem
              label={formLabel('邮政编码', tipMsg['postCode'])}
              {...formItemLayout2}
            >
              {getFieldDecorator('postCode', {
                initialValue: basicInfo['postCode'],
                rules: [{ required: false, message: '邮政编码' }, { pattern: NumberReg.int(6), message: '请输入正确邮政编码' }],
              })(
                <Input placeholder={'邮政编码'} disabled={lockFields.includes('postCode')} />
              )}
            </FormItem>
          </Col>


          <Col span={12}>
            <FormItem
              label={formLabel('传真号码', tipMsg['faxNumber'])}
              {...formItemLayout2}
            >
              {getFieldDecorator('faxNumber', {
                initialValue: basicInfo['faxNumber'],
                rules: [{ required: false, message: '传真号码' }, { pattern: new RegExp('((\\d{11})|^((\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1})|(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1}))$)'), message: '请输入正确的传真号码' }],
              })(
                <Input placeholder={'传真号码'} style={{ width: '100%' }} />
              )}
            </FormItem>
          </Col>
          {/* 16.当党组织类别为县级委员会的时候，增加信息项目：到村任职补助经费使用率（数字、不能大于100，必填） */}
          {
            (function (_this) {
              let d01Code = _this.props.form.getFieldValue('d01Code');
              if (typeof d01Code == 'object') {
                d01Code && (d01Code = d01Code['key']);
              }
              if (`${d01Code}`.startsWith('14')) {
                return (
                  <Col span={12}>
                    <LongLabelFormItem label={'到村任职补助经费使用率（%）'}
                      required={true}
                      code={'toTheVillageOfficeSubsidyFundsUtilizationRate'}
                      tipMsg={tipMsg}
                      formItemLayout={formItemLayout2}
                      formItem={(formItemLayout, code) => {
                        return (
                          <FormItem {...formItemLayout}
                          >
                            {getFieldDecorator(code, {
                              rules: [{ required: true, message: '到村任职补助经费使用率（%）' }],
                              initialValue: basicInfo['toTheVillageOfficeSubsidyFundsUtilizationRate'],
                            })(
                              <InputNumber min={0} max={100} placeholder={'到村任职补助经费使用率'} style={{ width: '100%' }} />
                            )}
                          </FormItem>
                        )
                      }} />
                  </Col>
                )
              }
            })(this)
          }
          {/* 组织类别以13,14开头的，新增选项：是否建立党建引领基层治理领导协调机制 */}
          {/* 组织类别以13,14开头的加：市、区、街道、社区是否均建立党建联席会议制度  hasBuildJointMeeting（ 1是，0否） */}
          {
            (function (_this) {
              let d01Code = _this.props.form.getFieldValue('d01Code');
              if (typeof d01Code == 'object') {
                d01Code && (d01Code = d01Code['key']);
              }
              if (`${d01Code}`.startsWith('13') || `${d01Code}`.startsWith('14')) {
                return (
                  <Fragment>
                    <Col span={12}>
                      <LongLabelFormItem label={'是否建立党建引领基层治理领导协调机制'}
                        required={true}
                        code={'hasLeadingBasicCoordinating'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '是否建立党建引领基层治理领导协调机制' }],
                                initialValue: basicInfo['hasLeadingBasicCoordinating'] || undefined,
                              })(
                                <Select style={{ width: '100%' }}>
                                  <Select.Option value="1">是</Select.Option>
                                  <Select.Option value="0">否</Select.Option>
                                </Select>
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>
                    <Col span={12}>
                      <LongLabelFormItem label={'市、区、街道、社区是否均建立党建联席会议制度'}
                        required={true}
                        code={'hasBuildJointMeeting'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '市、区、街道、社区是否均建立党建联席会议制度' }],
                                initialValue: basicInfo['hasBuildJointMeeting'] || undefined,
                              })(
                                <Select style={{ width: '100%' }}>
                                  <Select.Option value="1">是</Select.Option>
                                  <Select.Option value="0">否</Select.Option>
                                </Select>
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>
                    {/* 组织类别14开头的加：是否社区党组织书记实行县级党委备案管理的区  hasSecretaryBuildCountyFiling（ 1是，0否） */}
                    {`${d01Code}`.startsWith('14') && <Col span={12}>
                      <LongLabelFormItem label={'是否社区党组织书记实行县级党委备案管理的区'}
                        required={true}
                        code={'hasSecretaryBuildCountyFiling'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout2}
                        formItem={(formItemLayout, code) => {
                          return (
                            <FormItem {...formItemLayout}
                            >
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '是否社区党组织书记实行县级党委备案管理的区' }],
                                initialValue: basicInfo['hasSecretaryBuildCountyFiling'] || undefined,
                              })(
                                <Select style={{ width: '100%' }}>
                                  <Select.Option value="1">是</Select.Option>
                                  <Select.Option value="0">否</Select.Option>
                                </Select>
                              )}
                            </FormItem>
                          )
                        }} />
                    </Col>}
                  </Fragment>
                )
              }
            })(this)
          }
        </Row>

        <FormItem
          label={formLabel('通讯地址', tipMsg['postAddress'])}
        >
          {getFieldDecorator('postAddress', {
            initialValue: basicInfo['postAddress'],
            rules: [{ required: false, message: '' }],
          })(
            <Input placeholder={'通讯地址'} disabled={lockFields.includes('postAddress')} />
          )}
        </FormItem>

        {
          !this.props.hideSave &&
          <div style={{ textAlign: 'center' }}>
            <Button type={'primary'} htmlType={'submit'} icon={<LegacyIcon type={'check'} />} onClick={this.handleSubmit} style={{ marginRight: 16 }} loading={basicInfo['code'] ? orgUpdate : orgAdd}>保存</Button>
            <Button type={'primary'} danger htmlType={'button'} icon={<LegacyIcon type={'delete'} />} onClick={() => this.props.close({})}>取消</Button>
          </div>
        }

      </Form>
    );
  }
}
export default Form.create<any>()(index);
