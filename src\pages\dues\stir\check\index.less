//:global .PE {margin: 0 auto;margin-top: 20px;box-shadow: 10px 10px 5px #ccc;padding: 10px;border-radius: 8px;border: 1px solid #ccc; }

:global .PE .ant-progress-outer {width: 518px;}
:global .PE .ant-progress-text {width: 36px;}
//:global .PE .stop {
//  -webkit-animation-play-state:paused!important;
//  -moz-animation-play-state:paused!important;
//}
/*:global .PE .ant-table-tbody {margin-top: 10px;}*/
:global .PE .ant-table-tbody td { padding: 10px 8px;height: 43px;}
:global .PE .ball {
  background-color: rgba(0,0,0,0);
  border:5px solid rgba(16,142,233,0.9);
  opacity:.9;
  border-top:5px solid rgba(0,0,0,0);
  border-left:5px solid rgba(0,0,0,0);
  border-radius:50px;
  box-shadow: 0 0 10px #2187e7;
  width:100px;
  height:100px;
  margin:0 auto;
  -moz-animation:spin .5s infinite linear;
  -webkit-animation:spin .5s infinite linear;
}
:global .PE .ball1 {
  background-color: rgba(0,0,0,0);
  border:5px solid rgba(16,142,233,0.9);
  opacity:.9;
  border-top:5px solid rgba(0,0,0,0);
  border-left:5px solid rgba(0,0,0,0);
  border-radius:50px;
  box-shadow: 0 0 5px #2187e7;
  width:80px;
  height:80px;
  margin:0 auto;
  position:relative;
  top:-90px;
  -moz-animation:spinoff .5s infinite linear;
  -webkit-animation:spinoff .5s infinite linear;
}
:global .mem5IDcardItem .ant-form-item {margin: 0!important;}
.bannerInfo {width: 900px;margin: 0 auto;}
/*.PEmain {height: 700px;}*/
.PEtit {height: 130px;margin: 0 auto;border-radius: 8px;}
.radar {width: 130px;height: 100px;float: left}
.PEtitRight {height: auto;padding:16px;width: 586px; float: left}
.PEprogress {margin-top: 10px;}
.PEing {font-size: 19px;font-weight: bold;color: #566663;margin-bottom: 10px;}
.sure {width: 100px;height: 123px;text-align: center;float: left}
.container {margin: 0 auto; overflow: hidden;width: 130px;height: 130px;}
.content {width: 130px;height: 130px;margin-top: 10px;margin-left: -10px;}
.point {position: relative;top: -146px;font-size: 23px;left: calc(50% - 40px);width: 80px;text-align: center}
.filter {font-size: 18px;margin: 10px -3px; color:#2e2e2e;padding:0 15px;background:#D7DBDE;height: 40px;line-height: 40px;}
.nextStep {width: 900px;margin: 20px auto;text-align: right;padding-right: 32px;}
