import React, { Fragment } from 'react';
import { connect } from 'dva';
import { getSession } from '@/utils/session';
import NowOrg from '@/components/NowOrg';
import { DownOutlined, WarningTwoTone } from '@ant-design/icons';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Dropdown, Input, Popconfirm, Menu, Space, message, Modal, Descriptions } from 'antd';
import RuiFilter from '@/components/RuiFilter';
import WhiteSpace from '@/components/WhiteSpace';
import ListTable from '@/components/ListTable';
import AddDevlop from '../Add';
import AddorEdit from '../AddorEdit'
import EditDevlop from '../Edit'
import ElectronicArchives from '../electronicArchives'
import OrgLevel from '@/pages/mem/manage/components/membasic/OrgLevel';
import ToActive from '../ToActive';
import ToObject from '../ToObject';
import Fire from '../Fire';
import MemChange from '../MemChange';
import ToPreparation from '../ToPreparation';
import AddPartyMember from '../AddPartyMember';
import CancelReason from '../CancelReason';
import CancelLevel from '../CancelLevel';
import Tip from '@/components/Tip';
import qs from 'qs';
import { _history as router } from "@/utils/method";
import { setListHeight } from '@/utils/method';
import ExportInfo from '@/components/Export';
import _get from 'lodash/get';
import { tableColConfig } from '@/services';
import { importExcelDevelop, exportReadyData, zyImportExcelDevelop, findAuditProcess } from '@/pages/developMem/services'
import { changeMsgTip, fileDownloadHeader, preLoadDicts } from '@/utils/method';
import UploadComp from '@/components/UploadComp';
import { ButtonDisabled } from '@/common/config.js'
import _isEmpty from 'lodash/isEmpty';
import Date from '@/components/Date';
import tip from '@/components/Tip';
import { History } from 'umi';
import Flow from '../flow';
import End from '../end'
import Info from '../Add/info'
import Extend from '../extend'
import { unlockExtendApproval } from '@/pages/developMem/services'

const { Search } = Input;
// @ts-ignore
@connect(({ memDevelop, commonDict, loading }) => ({ memDevelop, commonDict, loading }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      filterHeight: 100,
      processNode: []
    }
  }
  componentDidMount(): void {
    setListHeight(this, this.props.hight);

    // 预加载编辑字典表
    preLoadDicts([], this.props.dispatch);

    this.getMsg();
  }
  // 筛选
  filterChange = (val) => {
    this.setState({
      filterVal: val
    })
    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        filter: val,
      }
    });
    const { pagination = {} } = this.props.memDevelop;
    const { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`)
    // this.action()
  };
  search = (value) => {
    this.setState({
      searchVal: value
    })
    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        memName: value,
      }
    });
    const { pagination = {} } = this.props.memDevelop;
    const { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`)
    // this.action();
  };
  searchClear = (e) => {
    const { location: { pathname = '' } = {} } = router;
    this.setState({
      searchVal: e.target.value
    })
    if (!e.target.value) {
      this.props.dispatch({
        type: 'memDevelop/updateState',
        payload: {
          memName: undefined,
        },
      });
      if (pathname == '/developMem/zy/mems') {
        this.action({ editType: 'benNian' });
      } else {
        this.action();
      }

    }
  };
  action = (val?: object) => {
    const { pagination = {} } = this.props.memDevelop;
    const { current, pageSize } = pagination;
    const { canEdit = [] } = this.props;
    const [type] = canEdit;
    const org = getSession('org') || {};
    const { editType = '' } = val || {};
    // this.props.dispatch({
    //   type: 'memDevelop/updateState',
    //   payload: {
    //     processNode: ''
    //   }
    // });
    let url = editType == 'benNian' ? 'memDevelop/getListMems' : 'memDevelop/getzyList';
    this.props.dispatch({
      type: url,
      payload: {
        data: {
          memOrgCode: org['orgCode'],
          pageNum: current || 1,
          pageSize,
          type,
          ...val
        }
      }
    })
    this.getFlowCount()
  };
  getFlowCount = () => {
    const { pagination = {} } = this.props.memDevelop;
    const { current, pageSize } = pagination;
    const { canEdit = [] } = this.props;
    const [type] = canEdit;
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memDevelop/flowcount',
      payload: {
        data: {
          memOrgCode: org['orgCode'],
          type,
        }
      }
    })
  }
  // 分页
  onPageChange = (page, pageSize) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`)
  };
  // 获取tipMsg
  getMsg = () => {
    tableColConfig({ id: 'ccp_mem_develop' }).then(res => {
      if (res['code'] == '0') {
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg: msg,
        });
      }
    });
  }
  // 新增
  addNew = (obj) => {
    let org = getSession('org') || {}
    this['addDevlop'].destroy();
    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        basicInfo: {
          d01Code: org['d01Code'],
          orgName: org['name'],
          orgCode: org['code'],
          orgZbCode: org['zbCode'],
          developOrgCode: org['orgCode'],
        }
      }
    })
    this['addDevlop'].open({ canEdit: true, isAdd: true, processNode: 'RD_1' });
  };
  openAdd = async (record, canEdit = false, editType = 'default') => {
    const { id, ...other } = record
    this['addDevlop'].destroy();
    // if(record && record['code']&&record['processNode'] == 'RD_1') {
    await this.props.dispatch({
      type: 'memDevelop/zyfindByCode',
      payload: {
        code: record['code'],
      }
    })
    // }
    this['addDevlop'].open({ canEdit: true, ...other });
  }
  openInfo = async (record, canEdit = false, editType = 'default') => {
    await this.props.dispatch({
      type: 'memDevelop/zyfindByCode',
      payload: {
        code: record['code'],
      }
    })
    const { memDevelop: { basicInfo = {} } = {} } = this.props;
    this['Info'].showModal({ data: basicInfo, file: { value: basicInfo?.filesList, name: record['name'] }, rowCode: record?.code, row: record })
  }
  openInfo1 = async (record, canEdit = false, editType = 'default') => {
    await this.props.dispatch({
      type: 'memDevelop/zyfindByCode',
      payload: {
        code: record['code'],
      }
    })
    const { memDevelop: { basicInfo = {} } = {} } = this.props;
    this['Info'].showModal({ data: basicInfo, file: { value: [], name: record['name'] }, rowCode: record?.code, row: record })
  }
  digitalArchives = async (record) => {
    this['editDevlop'].destroy();
    if (record && record['code']) {
      await this.props.dispatch({
        type: 'memDevelop/zyfindByCode',
        payload: {
          code: record['code'],
        }
      })
    }
    this['editDevlop'].open({ ...record });

  }
  tobook = (records) => {
    this['ElectronicArchives'].showModal(records['code'])
  }
  // 编辑
  edit = async (record, canEdit = false, editType = 'default') => {
    const { canEdit: _canEdit = [] } = this.props;
    this['addorEdit'].destroy();
    if (record && record['code']) {
      await this.props.dispatch({
        type: 'memDevelop/findMem',
        payload: {
          code: record['code'],
          type: _get(_canEdit, '[0]', undefined) == 'mems' ? '2' : undefined,
        }
      })
    }
    let mem = _get(_canEdit, '[0]', undefined)
    this['addorEdit'].open({ canEdit, editType: mem == 'mems' ? 'benNian' : editType, processNode: record['processNode'] });
  };
  // 查看当组织层级
  lookOrgs = (record) => {
    this['OrgLevel'].open(record);
  };
  // 本年人员删除
  del = async (record, leaveOrgDate?) => {
    if (leaveOrgDate) {
      const { code } = record;
      if (code) {
        const res = await this.props.dispatch({
          type: 'memDevelop/delDevelop',
          payload: {
            code,
            leaveOrgDate: leaveOrgDate.valueOf(),
          }
        });
        const { code: resCode = 500 } = res || {};
        if (resCode === 0) {
          Tip.success('操作提示', '删除成功');
          this.action({ editType: 'benNian' });
        }
      }
    }
  };
  operating = (record, type) => {
    switch (type) {
      case '1':
        this['ToActive'].open(record);
        break;
      case '2':
        this['ToObject'].open(record);
        break;
      case '3':
        this['CancelLevel'].open(record);
        break;
      case '4':
        this['ToPreparation'].open(record);
        break;
      case '5':
        this['CancelLevel'].open(record);
        break;
      case '6':
        this['AddPartyMember'].open(record);
        break;
      case '7':
        // this.del(record);
        this['CancelReason'].open(record);
        break;
      default:
        break;
    }
  };
  componentWillUnmount(): void {
    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        memName: undefined,
        filter: {},
        processNode: [],
      }
    })
    // this.props.dispatch({
    //   type:'memDevelop/destroy',
    // })
  }
  // 导出
  exportInfo = async () => {
    this['developExportInfo'].open();
  };
  excelDownLoad = () => {
    let dataApi = sessionStorage.getItem('dataApi') || "";
    fileDownloadHeader('/api/public/excelftl/developUploadExcel.xlsm', 'developUploadExcel.xlsm', { dataApi });
  }
  allUpload = async (fileList, file) => {
    const { response: { code = 500, data = [] } = {} } = file || {};
    if (code === 0) {
      const { url = '' } = data[0] || {};
      const { code: code2 = 500 } = await zyImportExcelDevelop({
        data: {
          excelFile: url,
          orgCode: "",
        }
      });
      //  if (code2 ==0) {
      //    message.success('上传成功')
      //  }
    }
  }
  clickFlow = (obj) => {
    //流程只返回一个节点数组，所有流程信息都从列表record取
    const { pagination = {} } = this.props.memDevelop;
    const { query } = this.props.location;

    const org = getSession('org') || { d01Code: '' };
    // if (obj.type == 'RD_1' && (org.d01Code.startsWith('63') || ['931', '932'].includes(org.d01Code))) {

    // }
    console.log(obj, 'objobjobj')
    this.setState({
      processNode: obj.type,
    })

    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        processNode: obj.type
      }
    });
    router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`)

  }
  Unlock = async (record) => {
    const { code = 500, data = {} } = await unlockExtendApproval({ data: { processNode: record.processNode, digitalLotNo: record.digitalLotNo } })
    if (code == 0) {
      Tip.success('操作提示', '解锁成功')
      this.action()
    }
  }
  getReview = async (record) => {
    const { code, data } = await findAuditProcess({ data: { digitalLotNo: record.digitalLotNo, processNode: record.processNode } })
    if (code == 0) {
      this.setState({
        showAuditResult: true,
        review: data
      })
    }
  }
  getActionBtn = (record) => {
    const { processNode = '', lastCode = '' } = record;
    let key = lastCode || processNode
    let node: any = null
    switch (key) {
      case 'RD_1':
        node = <a onClick={() => this.openAdd(record, true)}>上传入党申请资料</a>
        break;
      case 'RD_2_1':
      case 'RD_2_2':
      case 'RD_2_3':
        node = <a onClick={() => this.openAdd(record, true)}>上传谈话记录</a>
        break;
      case 'RD_4':
        node = <a onClick={() => this.openAdd(record, true)}>确定积极分子</a>
        break;
      case 'JJ_2':
      case 'JJ_4':
        node = <a onClick={() => this.openAdd(record, true)}>第一次考察</a>
        break;
      case 'JJ_3':
      case 'JJ_4':
        node = <a onClick={() => this.openAdd(record, true)}>第二次考察</a>
        break;
      case 'JJ_4':
        node = <a onClick={() => this.openAdd(record, true)}>持续考察</a>
        break;
      case 'JJ_5':
        node = (
          <React.Fragment>
            <a onClick={() => this.openAdd(record, true)}>持续考察</a>
            <Divider type="vertical" />
            <a onClick={() => {
              this['End'].showModal({ code: record.code })
            }}>考察完毕</a>
          </React.Fragment>
        )
        break;
      case 'JJ_6':
        node = record['approve'] != false && <a onClick={() => this.openAdd(record, true)}>上传材料</a>
        break;
      case 'JJ_7':
        node = <a onClick={() => this.openAdd(record, true)}>确定发展对象</a>
        break;
      case 'FZ_1':
        node = <a onClick={() => this.openAdd(record, true)}>支委会审查</a>
        break;
      case 'FZ_2':
        node = (
          record['approve'] != false && <a onClick={() => this.openInfo({ ...record, name: '党总支审查' })}>党总支审查</a>
        )
        break;
      case 'FZ_3_1':
      case 'FZ_3_2':
      case 'FZ_3_3':
        if (record['approve'] != false) {
          node = <a onClick={() => this.openAdd(record, true)}>基层党委审查</a>
        }
        break;
      case 'FZ_4':
        node = (
          record['approve'] != false && <a onClick={() => this.openInfo({ ...record, name: '审查' })}>审查</a>
        )
        break;
      case 'FZ_5_1':
      case 'FZ_5_2':
      case 'FZ_5_3':
        node = <a onClick={() => this.openAdd(record, true)}>上传接收预备党员预审请示</a>
        break;
      case 'FZ_6_1':
      case 'FZ_6_2':
        if (record['approve'] != false) {
          node = (
            <React.Fragment>
              <a onClick={() => this.openAdd(record, true)}>上传材料</a>
              <Divider type="vertical" />
              <a onClick={() => {
                this['Extend'].showModal(record)
              }}>延长审批</a>
            </React.Fragment>
          )
        }
        break;
      case 'FZ_6_3':
        if (record['approve'] != false) {
          if (record?.isAutoLock == '1') {
            node = (
              <Popconfirm
                title="是否解锁当前数据"
                onConfirm={() => this.Unlock(record)}
                okText="是" cancelText="否"
              >
                <a >解除锁定</a>
              </Popconfirm>
            )
          } else {
            node = (
              <React.Fragment>
                <a onClick={() => this.openAdd(record, true)}>上传材料</a>
                <Divider type="vertical" />
                <a onClick={() => {
                  this['Extend'].showModal(record)
                }}>延长审批</a>
              </React.Fragment>
            )
          }
        }
        break;
      case 'FZ_6_4':
        if (record['approve'] != false) {
          node = (
            <React.Fragment>
              <a onClick={() => this.openAdd(record, true)}>上传材料</a>
              <Divider type="vertical" />
              <a onClick={() => {
                this['Extend'].showModal(record)
              }}>延长说明</a>
            </React.Fragment>
          )
        }
        break;
      case 'FZ_7':
        node = <a onClick={() => this['ToPreparation'].open(record)}>接收预备党员</a>
        break;
      case 'FZ_8':
        node = (
          <React.Fragment>
            {
              record?.auditStatus == 0 &&
              <a onClick={() => this.openInfo1({ ...record, name: '县委审核申请' })}>提交县委审核申请</a>
            }
            {
              record?.auditStatus == 3 &&
              <React.Fragment>
                <a onClick={() => this.openInfo1({ ...record, name: '县委审核申请' })}>提交县委审核申请</a>
                {/* <Divider type="vertical" />
                <a onClick={() => this.getReview(record)}>查看审核结果</a> */}
              </React.Fragment>
            }
          </React.Fragment>
        )
        break;
    }
    return node;
  }
  getfire = (record) => {
    let node: any = null
    const pidArr: any = getSession('pid') || [];
    console
    if (pidArr.includes(94)) {
      if (record?.processNode == 'FZ_8') {
        if (record?.auditStatus != 1) {
          node = <Menu.Item>
            <a onClick={() => {
              this['Fire'].open(record);
            }}>火线入党</a>
          </Menu.Item>
        }
      } else {
        node = <Menu.Item>
          <a onClick={() => {
            this['Fire'].open(record);
          }}>火线入党</a>
        </Menu.Item>
      }
    }
    return node
  }
  render() {
    const { memDevelop = {}, loading: { effects = {} } = {}, commonDict, canEdit = [] } = this.props;
    const { list, pagination = {}, memName, filter, flowCount = {} } = memDevelop;
    const { current = 0, pageSize = 10 } = pagination;
    const { filterHeight, flowData, flowCountData = {}, processNode, review = {} } = this.state;
    let filterData = [
      {
        key: 'd09CodeList', name: '工作岗位', value: commonDict['dict_d09_tree'],
      },
      {
        key: 'sexCodeList', name: '人员性别', value: [{ key: '1', name: '男' }, { key: '0', name: '女' }],
      },
      {
        key: 'd08CodeList', name: '人员类型', value: this.props.d08CodeList,
      },
      {
        key: 'd07CodeList', name: '学历教育', value: commonDict['dict_d07_tree'],
      },
      {
        key: 'd194CodeList', name: '国民经济行业', value: this.props.commonDict[`dict_d194_tree`],
      },
      {
        key: 'd195CodeList', name: '生产性服务行业', value: this.props.commonDict[`dict_d195_tree`],
      },
    ];
    if (_isEmpty(this.props.d08CodeList)) {
      filterData = filterData.filter(it => it.key !== 'd08CodeList');
    }
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 60,
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1
        }
      },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 100,
        render: (text, record) => {
          const { d08Code = '' } = record;
          let _canEdit = false;
          if (canEdit.includes(d08Code)) {
            _canEdit = true
          }
          const { location: { pathname = '' } = {} } = router;
          if (pathname === '/developMem/zy/apply') {
            // 在入党申请人查询界面加一个图标，申请入党时间与当前时间达到3个月的显示出来，鼠标悬停图标上显示：该入党申请人递交入党申请书已达3个月，请及时研究。
            return (
              <div>{record['joinMessage'] ? <WarningTwoTone style={{ fontSize: '20px' }} twoToneColor={'#faad14'} title={record['joinMessage']} /> : ''} <a onClick={() => this.edit(record, _canEdit)} >{text}</a></div>
            )
          }
          if (pathname === '/developMem/zy/active') {
            // 在积极分子查询界面加一个图标，成为积极分子时间与当前时间达到1年的显示出来，鼠标悬停图标上显示：该入党申请人培养期已满1年，请及时研究。
            return (
              <div>{record['activistMessage'] ? <WarningTwoTone style={{ fontSize: '20px' }} twoToneColor={'#faad14'} title={record['activistMessage']} /> : ''} <a onClick={() => this.edit(record, _canEdit)} >{text}</a></div>
            )
          }
          return (
            <a onClick={() => this.edit(record, _canEdit)} >{text}</a>
          )
        }
      },
      {
        title: '性别',
        dataIndex: 'sexName',
        width: 100,
        // render: (text) => {
        //   return (
        //     <span> {text === '1' ? '男' : '女'} </span>
        //   )
        // }
      },
      {
        title: '公民身份证',
        dataIndex: 'idcard',
        width: 160,
        render: (text, record) => {
          if (typeof text === 'string' && !_isEmpty(text)) {
            // let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
            let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z\(\)\[\]]{4})/, "$1***********$2"); //增加港澳台身份证马赛克
            if (text.indexOf("*") > 0) {
              return text
            }
            return (
              <span>{newVal}</span>
            );
          } else {
            return ''
          }
        }
      },
      {
        title: '电话',
        width: 100,
        dataIndex: 'phone',
        render: (text, record) => {
          if (typeof text === 'string' && !_isEmpty(text)) {
            let newVal = text.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
            if (text.indexOf("*") > 0) {
              return text
            }
            return (
              <span>{newVal}</span>
            );
          } else {
            return ''
          }
        }
      },
      {
        title: '党员类型',
        width: 120,
        dataIndex: 'd08Name',
      },
      {
        title: '所在组织',
        width: 260,
        dataIndex: 'orgName',
        render: (text, record) => {
          return (
            <a onClick={() => this.lookOrgs(record)}>{text}</a>
          )
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 250,
        render: (text, record) => {
          const { d08Code = '' } = record;
          let menuArr = [
            // { key: '1', value: '确定积极分子' },
            { key: '6', value: '追认中共党员' },
            { key: '7', value: '取消入党申请人', confirmTitle: '是否取消入党申请人' },
          ];
          let menuArr2 = [
            // { key: '2', value: '确定发展对象' },
            { key: '3', value: '取消积极分子' },
            { key: '6', value: '追认中共党员' },
          ];
          let menuArr3 = [
            // { key: '4', value: '接收预备党员' },
            { key: '5', value: '取消发展对象' },
            { key: '6', value: '追认中共党员' },
          ];
          let final = [];
          // 权限列表有94	才显示接收预备党员、追认中共党员、火线入党
          const pidArr: any = getSession('pid') || [];
          if (d08Code === '5') {
            if (pidArr.includes(94)) {
              final = menuArr;
            } else {
              final = menuArr.filter((item => item?.key !== '6'));
            }
          }
          if (d08Code === '4') {
            if (pidArr.includes(94)) {
              final = menuArr2;
            } else {
              final = menuArr2.filter((item => item?.key !== '6'));
            }
          }
          if (d08Code === '3') {
            if (pidArr.includes(94)) {
              final = menuArr3;
            } else {
              final = menuArr3.filter((item => item?.key !== '4' && item?.key !== '6'));
            }
          }
          if (record?.auditStatus == 1) {
            final = menuArr3.filter((item => item?.key !== '5' && item?.key !== '6'));
          }
          if (canEdit.includes('mems')) {
            return <div />
          }
          // 本年度发展党员的编辑、删除功能，屏蔽
          // if (canEdit.includes('mems')) {
          //   return (
          //     <div>
          //       <a onClick={() => this.edit(record, true, 'benNian')}>编辑</a>
          //       <Divider type="vertical" />
          //       {/* <Popconfirm title={'是否删除？'} onConfirm={() => this.del(record)}>
          //         <a className={'del'} >删除</a>
          //       </Popconfirm> */}
          //       <a className={'del'} onClick={()=>{
          //         let leaveOrgDate;
          //         Modal.confirm({
          //           title: '请填写删除时间',
          //           icon: <div />,
          //           content: <Date onChange={(e:any)=>{
          //             leaveOrgDate = e;
          //           }} />,
          //           onOk: () => {
          //             return new Promise<void>((resolve, reject)=>{
          //               setTimeout(() => {
          //                 if(leaveOrgDate){
          //                   this.del(record,leaveOrgDate);
          //                   resolve();
          //                 }else{
          //                   tip.error('操作提示','请填写删除时间')
          //                   reject();
          //                 }
          //               }, 150);
          //             })
          //           },
          //           okText: '确定',
          //           cancelText: '取消',
          //         });
          //       }} >删除</a>
          //     </div>
          //   );
          // }
          let memcanedit = _get(canEdit, '[0]', undefined) == 'mems' ? '2' : _get(canEdit, '[0]', undefined)
          // if (!memcanedit.includes(d08Code)) {
          //   return <div />;
          // }
          return (
            <span>
              {/* <a onClick={() => this.tobook(record)}>电子档案</a>
              <Divider type="vertical" /> */}
              <a onClick={() => this.digitalArchives(record)}>档案管理</a>
              <Divider type="vertical" />
              {
                memcanedit.includes(d08Code) && <React.Fragment>
                  <a onClick={() => this.edit(record, true)}>编辑</a>
                  <Divider type="vertical" />
                  {
                    !memcanedit.includes('2') &&
                    <Dropdown overlay={(
                      <Menu>
                        {
                          final.map((item, index) => {
                            return (
                              // <Fragment>
                              //   {
                              //     item.confirmTitle ? (
                              //       <Menu.Item key={index}>
                              //         <Popconfirm title={item.confirmTitle} onConfirm={() => this.operating(record, item['key'])}>
                              //           <a>{item['value']}</a>
                              //         </Popconfirm>
                              //       </Menu.Item>
                              //     ) : (
                              //       <Menu.Item key={index}>
                              //         <a onClick={() => this.operating(record, item['key'])}>{item['value']}</a>
                              //       </Menu.Item>
                              //     )
                              //   }
                              // </Fragment>
                              <Fragment>
                                <Menu.Item key={index}>
                                  <a onClick={() => this.operating(record, item['key'])}>{item['value']}</a>
                                </Menu.Item>
                              </Fragment>
                            )
                          })
                        }
                        {

                          this.getfire(record)
                        }

                      </Menu>
                    )}>
                      <a className="ant-dropdown-link">
                        业务操作 <DownOutlined />
                      </a>
                    </Dropdown>
                  }
                  <Divider type="vertical" />
                </React.Fragment>
              }
              {
                this.getActionBtn(record)
              }

              {/* <a onClick={() => this.openAdd(record, true)}>{flowData?.btntext}</a> */}
            </span>
          );
        },
      },
    ];
    const org = getSession('org') || { d01Code: '' };
    const { d01Code = '' } = org || {};

    return (
      <div style={{ height: '100%', overflow: 'hidden' }}>
        <NowOrg
          extra={
            <React.Fragment>
              <Space>
                <a onClick={() => {
                  let dataApi = sessionStorage.getItem('dataApi') || "";
                  fileDownloadHeader(`/operationVideo/digitalHandbook.docx`, '数字档案操作手册.docx', { dataApi });
                }} style={{ color: 'red', borderBottom: '1px solid red',fontSize: '18px',fontWeight: 'bold',display: 'inline-block',width: '144px'}}>数字档案操作手册</a>
                <Button htmlType={'button'} onClick={this.exportInfo} disabled={_isEmpty(list)}>导出</Button>
                {
                  _get(canEdit, '[0]', 0) === '5' &&
                  <React.Fragment>

                    {
                      !ButtonDisabled.statistics2021 && (d01Code === '631' || d01Code === '632' || d01Code === '634' || d01Code === '931' || d01Code === '932') &&
                      <React.Fragment>
                        <Button htmlType={'button'} onClick={this.excelDownLoad}>导入模板下载</Button>
                        {/* <Button htmlType={'button'} onClick={this.allUpload}>批量导入</Button> */}
                        <UploadComp action='/api/export/rdsqr' accept=".xls,.xlsx,.xlsm" buttonText={'导入'} maxLen={1} showUploadList={false} onChange={this.allUpload} />
                      </React.Fragment>
                    }
                    {
                      (org.d01Code.startsWith('63') || ['931', '932'].includes(org.d01Code)) &&
                      <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={() => this.addNew({})}>新增入党申请人</Button>
                    }
                    {
                      // org['isLeaf'] == 1 &&
                      <Button htmlType={'button'} onClick={() => {
                        this['MemChange'].open();
                      }}>人员调整</Button>
                    }

                  </React.Fragment>
                }
              </Space>
              {
                _get(canEdit, '[0]', 0) === 'mems' && <Button htmlType={'button'} disabled={_isEmpty(list)} onClick={async () => {
                  const { canEdit = [] } = this.props;
                  const [type] = canEdit;
                  const { searchVal = '', filterVal = {} } = this.state;
                  const { code = 500, data: { url = '' } = {} } = await exportReadyData({
                    data: {
                      memOrgCode: org['orgCode'],
                      pageNum: current,
                      pageSize,
                      type,
                      memName: searchVal || undefined,
                      idcard: searchVal || undefined,
                      ...filterVal
                    }
                  });
                  if (code === 0) {
                    let dataApi = sessionStorage.getItem('dataApi') || "";
                    fileDownloadHeader(`/api${url}`, undefined, { dataApi });
                  }
                }} style={{ marginLeft: 16 }}>导出</Button>
              }
              {/* {
                !ButtonDisabled.statistics2021 && _get(canEdit, '[0]', 0) === '5' && (d01Code === '631' || d01Code === '632' || d01Code === '634' || d01Code === '931' || d01Code === '932') &&
                <Button htmlType={'button'} type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.addNew} style={{ marginLeft: 16 }}>新增入党申请</Button>
              } */}
              <Search style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'} />
            </React.Fragment>
          }
        />
        <Flow onChange={this.clickFlow} data={flowCount} />
        <End ref={e => this['End'] = e} upList={this.action} />
        <Info ref={e => this['Info'] = e} upList={this.action} />
        <RuiFilter
          data={filterData}
          openCloseChange={() => setListHeight(this, this.props.hight)}
          onChange={this.filterChange}
        />
        <WhiteSpace />
        <WhiteSpace />
        <ListTable scroll={{ y: filterHeight }} columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange} />
        <AddDevlop wrappedComponentRef={e => this['addDevlop'] = e} onsubmit={this.action} {...this.props} tipMsg={this.state.tipMsg} />
        <AddorEdit wrappedComponentRef={e => this['addorEdit'] = e} onsubmit={this.action} {...this.props} tipMsg={this.state.tipMsg} />
        <EditDevlop wrappedComponentRef={e => this['editDevlop'] = e} onclose={this.action} {...this.props} tipMsg={this.state.tipMsg} />
        <OrgLevel {...this.props} ref={e => this['OrgLevel'] = e} />
        <ToActive {...this.props} wrappedComponentRef={e => this['ToActive'] = e} submit={this.action} />
        <ToObject {...this.props} wrappedComponentRef={e => this['ToObject'] = e} submit={this.action} />
        <ToPreparation {...this.props} wrappedComponentRef={e => this['ToPreparation'] = e} submit={this.action} />
        <Fire {...this.props} wrappedComponentRef={e => this['Fire'] = e} submit={this.action} />
        <CancelLevel {...this.props} wrappedComponentRef={e => this['CancelLevel'] = e} submit={this.action} />
        <ExportInfo wrappedComponentRef={e => this['developExportInfo'] = e}
          tableName={'ccp_mem_develop'}
          tableListQuery={{ memName, ...filter, searchType: 1, memOrgCode: org['orgCode'], processNode }}
          action={'/api/zunyi/data/develop/exportData'}
        />
        <AddPartyMember {...this.props} wrappedComponentRef={e => this['AddPartyMember'] = e} submit={this.action} />
        <CancelReason {...this.props} wrappedComponentRef={e => this['CancelReason'] = e} submit={this.action} />
        <MemChange ref={e => this['MemChange'] = e} onOK={this.action} />
        <Extend ref={e => this['Extend'] = e} onoK={this.action} />
        <ElectronicArchives ref={e => this['ElectronicArchives'] = e} onoK={this.action} />
        <Modal
          title='查看审核结果'
          visible={this.state.showAuditResult}
          width={800}
          onCancel={() => {
            this.setState({ showAuditResult: false })
          }
          }
          footer={[
            <Button onClick={() => {
              this.setState({ showAuditResult: false })
            }}>取消</Button>
          ]}
        >
          <Descriptions title="" column={1}>
            <Descriptions.Item label="审核结果">{review?.status == '0' ? '不通过' : '通过'}</Descriptions.Item>
            {
              review?.status == '0' &&
              <Descriptions.Item label="不通过原因">{review?.reason}</Descriptions.Item>
            }
          </Descriptions>
        </Modal>
      </div>
    );
  }
}

