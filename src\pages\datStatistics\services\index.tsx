import request from '@/utils/request';
import qs from 'qs';
export async function workSupervisionNew(params) {
  return request('/api/chart/workSupervisionNew', {
    method: 'POST',
    body: params
  });
}
export async function peggingWorkSupervisionNew(params) {
  return request('/api/chart/peggingWorkSupervisionNew', {
    method: 'POST',
    body: params
  });
}
export async function peggingExcelNew(params) {
  return request('/api/chart/peggingExcelNew', {
    method: 'POST',
    body: params
  });
}
//2025/3/17 流入流出督查表接口
export async function FlowExcelNew(params) {
  return request('/api/memFlowInspectionForm/InquireReportGetDat', {
    method: 'POST',
    body: params
  });
}

export async function grassRootsOrgLifeExcel(params) {
  return request('/api/chart/grassRootsOrgLifeExcel', {
    method: 'POST',
    body: params
  });
}
export async function reportNewExcel(params) {
  return request('/api/chart/reportNewExcel', {
    method: 'POST',
    body: params
  });
}
export async function memFlowInspectionForm(params) {
  console.log('params===', params);
  // return request('/api/chart/memFlowInspectionForm', {
  return request('/api/memFlowInspectionForm/realTimeGetData', {
    method: 'POST',
    body: params
  });
}
export async function reportExcel(params) {
  return request('/api/chart/reportExcel', {
    method: 'POST',
    body: params
  });
}
export async function peggingExcel(params) {
  return request('/api/chart/peggingExcel', {
    method: 'POST',
    body: params
  });
}

export async function grassPartyOrganization(params) {
  return request('/api/chart/orgAppraisalCount', {
    method: 'POST',
    body: params
  });
}

export async function supportExcel(params) {
  return request('/api/chart/supportExcel', {
    method: 'POST',
    body: params
  });
}

export async function exportExcel(params) {
  return request('/api/chart/exportExcel', {
    method: 'POST',
    body: params
  }, 'file');
}
// 党组织设置情况督查表
export async function superviseTable(params) {
  return request('/api/chart/workSupervision', {
    method: 'POST',
    body: params
  });
}
// 党组织设置情况督查表-导出  
export async function exportWorkSupervisionExcel(params) {
  return request('/api/chart/exportWorkSupervisionExcel', {
    method: 'POST',
    body: params
  }, 'file');
}
export async function exportMemFlowInspectionForm(params) {
  return request('/api/chart/exportMemFlowInspectionForm', {
    method: 'POST',
    body: params
  }, 'file');
}
// 党组织设置情况督查表-反查
export async function peggingWorkSupervision(params) {
  return request('/api/chart/peggingWorkSupervision', {
    method: 'POST',
    body: params
  });
}
// 党组织设置情况督查表-反查-导出  
export async function exportCheck(params) {
  return request('/api/chart/exportWorkSupervisionPeggingExcel', {
    method: 'POST',
    body: params
  }, 'file');
}
// 流动党员情况督查表-反查-导出  
export async function exportFlowCheck(params) {
  return request('/api/memFlowInspectionForm/inquireExportData', {
    method: 'POST',
    body: params
  }, 'file');
}
//基层党组织开展民主评议党员情况统计表 导出
export async function exportGrassRootsOrgLifeExcel(params) {
  return request('/api/chart/exportOrgAppraisalCount', {
    method: 'POST',
    body: params
  }, 'file');
}
//基层党组织开展民主评议党员情况统计表 反查
export async function grassPegging(params) {
  return request('/api/chart/peggingOrgAppraisalCount ', {
    method: 'POST',
    body: params
  }, 'file');
}