/**
 * 添加党小组
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { DatePicker, Input, Modal, InputNumber, Select } from 'antd';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _cloneDeep from 'lodash/cloneDeep';
import Tip from '@/components/Tip';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import { formLabel } from '@/utils/method';
import Date from '@/components/Date';
const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
class index extends React.Component<any, any> {
  static showModal() {}
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
    index.showModal = this.showModal;
  }
  showModal = () => {
    const { children, title, dataInfo = {} } = this.props;
    this.setState({
      visible: true,
      d85: undefined,
      dresult: dataInfo['d08Code'] == '2' ? '8' : '',
    });
  };

  handleOk = () => {
    const { basicInfo = {} } = this.props.org;
    const { children, title, dataInfo = {} } = this.props;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        let obj = undefined;
        if (val['createDate']) {
          val['createDate'] = val['createDate'].valueOf();
        }
        if (val['stopPartyDate']) {
          val['stopPartyDate'] = val['stopPartyDate'].valueOf();
        }
        if (val['handlingReasons']) {
          val['handlingReasons'] = val['handlingReasons'].join(',');
        }
        let data = {...dataInfo,...val}
        let data1 = {...val,...dataInfo}
        // 当result不等于5的时候,不传递组织处置情况(situation)和出党时间(stopPartyDate) 王国超需求
        if(data['result'] && data['result'] != 5){
          if(data['situation'] && data['situation']){
            delete data.situation
            delete data.stopPartyDate
          }
        }
        // console.log("🚀 ~ index ~ this.props.form.validateFieldsAndScroll ~ val:", data,data1)
        if (title == '编辑') {
          obj = await this.props.dispatch({
            type: 'org/personUpdate',
            payload: {
              // data: {
              //   ...dataInfo,
              //   ...val,
              // },
              data: data
            },
          });
        } else {
          obj = await this.props.dispatch({
            type: 'org/personSave',
            payload: {
              // data: {
              //   ...val,
              //   ...dataInfo,
              // },
              data: data1
            },
          });
        }

        if (obj && obj['code'] === 0) {
          Tip.success('操作提示', dataInfo['code'] ? '修改成功' : '新增成功');
          if (title == '编辑') {
            this.props.editForm();
          }
          this.props.queryList([`${dataInfo['key']}`]);
          this.handleCancel();
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      dresult: '',
    });
  };

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { children, title, dataInfo = {}, tipMsg = {} } = this.props;
    const { d85, dresult } = this.state;
    // if(!d85 && dataInfo['code']){
    //   this.setState({
    //     d85:dataInfo['result']
    //   })
    // }
    return (
      <React.Fragment>
        {children
          ? React.cloneElement(children as any, {
              onClick: this.showModal,
              key: 'container',
            })
          : null}
        {this.state.visible && (
          <Modal
            title={title || '请输入标题'}
            visible={this.state.visible}
            onOk={() => this.handleOk()}
            onCancel={this.handleCancel}
            width={800}
            className="add_randp_modal"
            maskClosable={false}
            destroyOnClose
          >
            <Form {...formItemLayout1}>
              <FormItem label={formLabel('评议结果', tipMsg['result'])}>
                {getFieldDecorator('result', {
                  initialValue: dataInfo['result'] || dresult,
                  rules: [{ required: true, message: '请选择评议结果' }],
                })(
                  <DictSelect
                    codeType={'dict_d85'}
                    initValue={dataInfo['result'] || dresult}
                    onChange={(e) => {
                      this.setState({ d85: e });
                    }}
                    filter={(data) => {
                      if (dataInfo['d08Code'] && dataInfo['d08Code'] == '2') {
                        return data.filter((item) => item.key == '8');
                      }
                      return data;
                    }}
                  />,
                )}
              </FormItem>

              <FormItem label={formLabel('评议原因', tipMsg['reason'])}>
                {getFieldDecorator('reason', {
                  initialValue: dataInfo['reason'],
                  rules: [{ required: false, message: '评议原因' }],
                })(<TextArea rows={2} />)}
              </FormItem>
              <FormItem label={formLabel('上级党组织意见', tipMsg['opinion'])}>
                {getFieldDecorator('opinion', {
                  initialValue: dataInfo['opinion'],
                  rules: [{ required: false, message: '上级党组织意见' }],
                })(<TextArea rows={2} />)}
              </FormItem>
              {(function (state) {
                let result = getFieldValue('result');
                if (
                  (state.d85 && ['5'].includes(state.d85)) ||
                  (!state.d85 && ['5'].includes(result))
                ) {
                  // 组织处置情况
                  let obj = getFieldValue('situation');
                  let val = obj;
                  if (typeof obj == 'object') {
                    val = obj['key'];
                  }
                  return (
                    <React.Fragment>
                      <FormItem label={formLabel('组织处置情况', tipMsg['situation'])}>
                        {getFieldDecorator('situation', {
                          initialValue: dataInfo['situation'],
                          rules: [{ required: true, message: '组织处置情况' }],
                        })(
                          <DictTreeSelect
                            parentDisable={true}
                            codeType={'dict_d29'}
                            initValue={dataInfo['situation'] || ''}
                            filter={(data) => {
                              return data.filter((it) => it.key.startsWith('C3'));
                            }}
                          />,
                        )}
                      </FormItem>
                      {/* 评议结果不合格 + 组织处置情况选择C34，C35，C36，C37，C38时，添加一个停止党籍时间（stopPartyDate 必填，时间戳，不能大于当前时间） */}
                      {['C34', 'C35', 'C36', 'C37', 'C38', 'C341', 'C351'].includes(
                        getFieldValue('situation'),
                      ) && (
                        <FormItem label={formLabel('出党时间', tipMsg['stopPartyDate'])}>
                          {getFieldDecorator('stopPartyDate', {
                            initialValue: dataInfo['stopPartyDate'],
                            rules: [{ required: true, message: '出党时间' }],
                          })(<Date />)}
                        </FormItem>
                      )}
                      {/* 组织处置情况选C39不予处置后 不显示组织处置原因 */}
                      {/* { !(['C39'].includes(getFieldValue('situation'))) && (
                                        <FormItem
                                          label={formLabel('组织处置原因',tipMsg['handlingReasons'])}
                                        >
                                          {getFieldDecorator('handlingReasons', {
                                            initialValue: dataInfo['handlingReasons']!=undefined ? dataInfo['handlingReasons'].split(',') : [],
                                            rules: [{ required: true, message: '组织处置原因' }],
                                          })(
                                            <DictTreeSelect
                                              treeCheckable={true}
                                              parentDisable={true}
                                              codeType={'dict_d301'}
                                              initValue={dataInfo['handlingReasons']!=undefined ? dataInfo['handlingReasons'].split(',') : []}
                                              filter={(data)=>{
                                                let newData = data;
                                                const onlyOneClass = (arr)=>{
                                                   // 大类互斥，只能在一个大类下多选
                                                  let itemArr = getFieldValue('handlingReasons');
                                                  let itemArrLastKey = '';
                                                  if(_isArray(itemArr)){
                                                    itemArrLastKey = itemArr[itemArr.length - 1] || '';
                                                  }
                                                  if(typeof itemArr == 'string'){
                                                    let itemArr1 = itemArr.split(',');
                                                    itemArrLastKey = itemArr1[itemArr1.length - 1];
                                                  }
                                                  if(itemArrLastKey.startsWith('3')){
                                                    arr = arr.filter(it=>it.key.startsWith('3'))
                                                  }else if(itemArrLastKey.startsWith('9')){
                                                    arr = arr.filter(it=>it.key.startsWith('9'))
                                                  }
                                                  return arr;
                                                }
                                                // 退党除名	原因只能是其他原因，即上表9其他原因
                                                if(`${val}` == 'C36'){
                                                  newData = data.filter(it=>it.key.startsWith('9'))
                                                }else if(`${val}` == 'C37'){
                                                  // 自行脱党除名	原因只能是组织纪律散漫和其他原因，即上表的35和9其他原因
                                                  let datas = _cloneDeep(data).filter(it=> it.key == '9' || it.key == '3');
                                                  let find = datas.find(it=>it.key == '3');
                                                  if(find){
                                                    let children = find.children.filter(it=>it.key === '35');
                                                    find.children = children;
                                                  }
                                                  newData = datas;
                                                }else{
                                                  // 原因只能是组织处置和其他的原因，即上表的3开头和9其他原因
                                                  newData = data.filter(it=>it.key.startsWith('3')|| it.key.startsWith('9'));
                                                }
                                                newData = onlyOneClass(newData)
                                                return newData;
                                              }}
                                            />
                                          )}
                                        </FormItem>
                                      )} */}
                    </React.Fragment>
                  );
                }
              })(this.state)}
            </Form>
          </Modal>
        )}
      </React.Fragment>
    );
  }
}
export default Form.create<any>({})(index);
