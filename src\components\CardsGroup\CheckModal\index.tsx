import React from 'react';
import {Modal, Checkbox, Row, Col, Alert} from 'antd';
import style from './index.less';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import {arrSort} from '@/utils/method';
import {getDictList} from '@/services/index';
import {saveChart,findByChartType} from '../services';
const CheckboxGroup = Checkbox.Group;
interface Interface {

}
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible:false,
      final:[],
      checked:[],
      main:{},
      loading:false,
    }
  }

  static getDerivedStateFromProps = (nextProps:any, prevState:any) => {
    const state = [];
    const {data,parent,checked,checkInfoArr} = nextProps;
    const {_data,_checked} = prevState;
    if(!_isEqual(data,_data) || !_isEqual(checked,_checked)){
      state['_data'] = data;
      state['final'] = data;
      state['_checked'] = checked;
      state['checked'] = checked;
      state['main'] = parent;
    }
    return state;
  };
  handleOk=async()=>{
    const {submit} = this.props;
    const {checked,final,main} = this.state;
    const [it] = main;
    const {para_name,paraName} = it ||{};
    let payload:Array<object> = [];
    if(!_isEmpty(checked) && (para_name || paraName)){
      checked.forEach(it=>{
        final.forEach(item=>{
          if(it === item['key']){
            payload.push({key:item['key'],name:item['name']})
          }
        })
      });
      this.setState({loading:true});
      const res = await saveChart({
        data:{
          [`${para_name || paraName}`]:payload
        }
      });
      this.setState({loading:false});
      const {code = 500,data} = res;
      if(code === 0){
        submit && submit(arrSort(checked));
        this.handleCancel();
      }
    }else {
      Tip.warning('提示','至少勾选一个卡片')
    }

  };
  handleCancel=()=>{
    this.destroy();
    this.setState({visible:false});
  };
  open= async () => {
    this.setState({visible:true})
  };
  destroy=()=>{
    const {checked} = this.props;
    this.setState({
      checked,
      loading:false,
    })
  };
  onChange=(val)=>{
    this.setState({checked:val})
  };

  render(): React.ReactNode {
    const {visible,final,checked,loading} = this.state;
    let finalCard = [];
    let finalChart = [];
    if(!_isEmpty(final)){
      finalCard = final.filter(item=>item['type'] === 'card');
      finalChart = final.filter(item=>item['type'] === 'chart');
    }
    return (
      <Modal
          destroyOnClose
          title="编辑"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          confirmLoading={loading}
          width={1000}
          bodyStyle={{height:560,overflow:'auto'}}
        >
          <div className={style.box}>
            <Alert message="操作提示：以下模块为系统可选卡片，可自定义卡片显示隐藏。" type="info" showIcon/>
            <CheckboxGroup
              onChange={this.onChange}
              value={checked}
            >
              <Row gutter={16}>
                {
                  !_isEmpty(finalCard) && finalCard.map(item=>{
                    const {value:{ coverImg = ''}={}} = item || {};
                    return (
                      <Col span={8} key={item['key']} style={{marginTop:'10px'}}>
                        <Checkbox value={item['key']} key={item['key']}>
                          {
                            coverImg ? <img src={coverImg} style={{width:'100%',height:'117px'}}/> : <span>{item['name'] || ''}</span>
                          }
                        </Checkbox>
                      </Col>
                    )
                  })
                }
              </Row>
              <Row gutter={16}>
                {
                  !_isEmpty(finalChart) && finalChart.map(item=>{
                    const {value:{ coverImg = ''}={}} = item || {};
                    return (
                      <Col span={8} key={item['key']} style={{marginTop:'10px'}}>
                        <Checkbox value={item['key']} key={item['key']}>
                          {
                            coverImg ? <img src={coverImg} style={{width:'100%',height:'210px'}}/> : <span>{item['name'] || ''}</span>
                          }
                        </Checkbox>
                      </Col>
                    )
                  })
                }
              </Row>

            </CheckboxGroup>
          </div>
        </Modal>
    )
  }
}
