import React from 'react';
import ListTable from '@/components/ListTable';
import { Tabs, Input, Select, Tree, Modal, Switch, Button, Checkbox, Popconfirm, Spin } from 'antd';
import moment from 'moment'
import { connect } from 'dva';
import _get from 'lodash/get'
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import { isEmpty, changeListPayQuery, fileDownloadHeader } from '@/utils/method';
import { ExclamationCircleOutlined } from '@ant-design/icons';
// import { success } from '@/components/Notice';
import Tip from '@/components/Tip';
import Pegging from '../text/components/Pegging';
import ExtractDataModal from './components/extractDataModal'
import { checkTable } from './services';
import { getSession } from "@/utils/session";
import SpinProgress from '@/components/SpinProgress';
import CheckModal from './components/checkModal';
import { exportNestedTable } from './services'
const { TabPane } = Tabs;
const { Option } = Select;
const TreeNode = Tree.TreeNode;
const { TextArea } = Input;
const { Search } = Input;
//@ts-ignore

const typeArr = [
  'html_1',
  'html_2',
  'html_2_4',
  'html_3',
  'html_4',
  'html_5',
  'html_6',
  'html_7',
  'html_8',
  'html_9',
  'html_10',
  'html_11',
  'html_12',
  'html_13',
  'html_14',
  'html_15',
  'html_16',
  'html_16_3',
  'html_17',
  'html_18',
  'html_18_1',
  'html_19',
  'html_19_1',
  'html_20',
  'html_21',
  'html_22',
  'html_23',
  'html_24',
  'html_24_1',
  'html_24_2',
  'html_25',
  'html_26',
  'html_27',
  'html_28',
  'html_29',
  'html_30',
  'html_31',
  'html_32',
  'html_33',
  'html_34',
  'html_35',
  'html_36',
  'html_37',
  'html_38',
  'html_38_0',
  'html_39',
  'html_39_0',
  'html_40',
  'html_41',
  'html_42',
  'html_43',
  'html_44',
  'html_45',
  'html_46',
  'html_47',
  'html_48',
  'html_49',
  'html_50',
  'html_51',
  'html_52',
  'html_53',
  'html_54',
  'html_55'
]
@connect(({ tmwTable, loading }) => ({ tmwTable, loading: loading.effects }), null, null, { forwardRef: true })
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    // 手动初始化key
    const { initKeys } = props;
    this.state = {
      visible: false,
      modalTableLoading: false,
      timeKey: +new Date(),
      tbCheckLoading: false,
      // key: isEmpty(initKeys) ? '20220630_1.html' : initKeys[0],
      selectHtml: this.selectHtml,
      TreeListCodes: []
    }
  }

  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const { tmwTable: { TreeList = [] } = {} } = nextProps;
    const org = JSON.parse(sessionStorage.getItem('org') || '{}')
    const { _org, key, selectHtml, _TreeList } = prevState;
    // if (!_isEqual(org, _org)) {
    //   state['_org'] = org;
    //   selectHtml(key);
    // }
    if (!_isEqual(TreeList, _TreeList) && !_isEmpty(TreeList)) {
      state['_TreeList'] = TreeList;
      state['TreeListCodes'] = TreeList.filter(it=>{
       return it.type !== '2' && it.type !== '3'
      }).map(it=>it.levelCode);
      // TreeList.map(it => {
      //   if(it.type !== '2' && it.type !== '3'){
      //     state['TreeListCodes'].push(it.levelCode)
      //   }
      // });
      state['key'] = state['TreeListCodes'][0];
    }
    if (!_isEqual(org, _org) && key) {
      state['_org'] = org;
      let find = TreeList.find(tree => tree.levelCode == key);
      if (find) {
        selectHtml(find.levelCode, find.type);
      }
    }
    return state;
  };

  componentDidMount = () => {
    const { key, list = [] } = this.state;
    // this.select(key);
    // console.log('arr==',this.state['TreeListCodes'],'key==',this.state['key']);

    // if(this.state['key']){
    //   this.selectHtml(this.state['key'])
    // }
    let script = document.createElement("script"), _this = this;
    script.type = "text/javascript";
    script.src = '/js/check.js';
    document.body.appendChild(script);
    window.addEventListener('message', async function (e) {
      const { data } = e;
      if (data && typeof data == 'string') {
        const { tableCellIndex, tableName } = JSON.parse(data);
        let txt = tableCellIndex.split('_');
        let name = tableName.split('_');
        let type = name.includes('replenish') ? '2' : '1'
        txt = txt.slice(1, txt.length);
        _this.setState({
          tableRow: txt[0],
          tableColumn: txt[1],
          type,
          name,
          showButtonModal: true,
        }, () => {
          // Modal.confirm({
          //   title: '操作',
          //   // icon: <ExclamationCircleOutlined />,
          //   // content: '请选择以下操作',
          //   okText: '数据反查',
          //   cancelText: '数据提取',
          //   maskClosable: true,
          //   onOk() {
          //     _this.findVerData(1, 10, name.join('-')).then(({ list = [] }) => {
          //       _this['peg'] && _this['peg'].setState({
          //         visible: true
          //       })
          //     });
          //   },
          //   onCancel() {
          //     const { tableRow, tableColumn, key, type } = _this.state;
          //     // const { type='', treeLevel, treeOrg, memType } = this.props.annualstats;
          //     // const { treeOrg= } =this.props.positionNum;
          //     const org = JSON.parse(sessionStorage.getItem('org') || '{}')
          //     let params = {
          //       reportCode: key,
          //       rowIndex: tableRow,
          //       colIndex: tableColumn,
          //       orgCode: org?.code,
          //       orgLevelCode: org?.orgCode,
          //       type
          //     };
          //     _this['ExtractDataModal'].open(params);
          //   },
          // });
        });
      }
    })
  };

  componentWillUnmount(){
    // this.setState({
    //   key:undefined,
    //   TreeListCodes:undefined,
    //   _TreeList:undefined,
    //   _org:undefined,
    //   timeKey: +new Date(),
    // });
    this.setState= () => { return }
  }

  selectHtml = (val) => {
    const org = JSON.parse(sessionStorage.getItem('org') || '{}')
    this.props.dispatch({
      type: 'tmwTable/queryExcelConfigReturnHtml',
      payload: {
        reportCode: val,
        orgCode: org?.code,
        orgLevelCode: org?.orgCode,
      }
    }).then(res => {
      this.setState({
        Html: res,
        timeKey: +new Date(),
      }, () => {
        // 设置单位名称
        if (document.getElementById('unitName')) {
          document.getElementById('unitName').innerText = `填报单位：${org.name}`;
        }
      })
    })
  };

  onSelect = (key) => {
    this.setState({
      key,
    });
    this.selectHtml(key);
    // const { node: { props: { dataRef = {} } = {} } = {} } = e || {};
    // const val = { ...dataRef };
    // const { tab } = this.state;
    // delete val['children'];

    // this.props.dispatch({
    //   type: 'tmwTable/updateState',
    //   payload: {
    //     treeOrg: val,
    //   }
    // });
    // if (!isEmpty(levelCode)) {
    //   this.select(levelCode[0]);
    // }

    // this.selectHtml(levelCode[0]);
    // this.setState({
    //   selectedKeys: levelCode,
    //   key: levelCode[0]
    // })
  };
  select = (code) => {
    this.props.dispatch({
      type: 'tmwTable/select',
      payload: {
        reportCode: code
      }
    }).then(res => {
      const { code = 500, data = {} } = res;
      const { rowCell = [], colCell = [], tableIndexConfigList = [], replenishConfig: { lineConfigList = [], hasReplenish = undefined, queryType = undefined } = {} } = data;
      if (code === 0) {
        this.setState({
          rowCell,
          colCell,
          tableIndexConfigList,
          lineConfigList,
          hasReplenish,
          queryType
        })
      }
    })
  };

  findVerData = async (pageNum?, pageSize?, levelCode?) => {
    const { tableRow, tableColumn, key, type } = this.state;
    // const { type='', treeLevel, treeOrg, memType } = this.props.annualstats;
    // const { treeOrg= } =this.props.positionNum;
    let lastCode = levelCode || this.state['levelCode'];
    const org = JSON.parse(sessionStorage.getItem('org') || '{}')
    let params = {
      reportCode: key,
      rowIndex: tableRow,
      colIndex: tableColumn,
      orgCode: org?.code,
      orgLevelCode: org?.orgCode,
      pageNum: pageNum || 1,
      pageSize: pageSize || 10,
      type,
      year: window.location.pathname === '/annualStatistics/old' ? '20211231' : undefined,
    };
    // if (type == 'a') {
    //   if (memType === '3') {
    //     params['orgCode'] = treeOrg['orgCode'] + `,${treeOrg['unitId']}`;
    //   } else {
    //     params['orgCode'] = treeOrg['levelCode'];
    //   }
    // }
    // if (type == 'b') {
    //   params['countLevel'] = treeLevel['code'];
    // }
    this.setState({
      modalTableLoading: true,
    })
    const obj = await this.props.dispatch({
      type: 'tmwTable/findVerData',
      payload: { data: { ...params } }
    });
    const { data = {} } = obj;
    let changeList = changeListPayQuery(data || { list: [] });
    this.setState({
      params,
      ...changeList,
      levelCode: lastCode,
      modalTableLoading: false,
      tableType: data ? data['type'] : undefined,
    });
    return {
      ...changeList
    }
  };
  print = () => {
    const el: any = window.document.getElementById('page');
    const iframe: any = document.createElement('IFRAME');
    let doc: any = null;
    iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:500px;top:500px;');
    document.body.appendChild(iframe);
    doc = iframe.contentWindow.document;
    doc.write(el.innerHTML);
    doc.close();
    // 获取iframe的焦点，从iframe开始打印
    iframe.contentWindow.focus();
    iframe.contentWindow.print();
    if (navigator.userAgent.indexOf("MSIE") > 0) {
      document.body.removeChild(iframe);
    }
  }
  nextPage = (type) => {
    const { TreeListCodes = [], key } = this.state;
    if (!_isEmpty(TreeListCodes)) {
      let _key = type == 'add' ? TreeListCodes[TreeListCodes.indexOf(this.state.key) + 1] : TreeListCodes[TreeListCodes.indexOf(this.state.key) - 1]
      this.setState({
        key: _key,
      });
      this.selectHtml(_key);
    }
  }
  disabledNextPageBtn = (type) => {
    const { TreeListCodes = [] } = this.state;
    if (!_isEmpty(TreeListCodes)) {
      if (TreeListCodes.indexOf(this.state.key) == 0 && type == 'pre') {
        return true;
      }
      if (TreeListCodes.indexOf(this.state.key) == TreeListCodes.length - 1 && type == 'add') {
        return true;
      }
    } else {
      return true
    }
  };
  // 报表校核
  check = async () => {
    let org: any = getSession('org') || {};
    this.setState({ tbCheckLoading: true });
    const { code = 500, data = '' } = await checkTable({ orgCode: org.orgCode, type: this.state.key });
    if (code === 0) {
      this.setState({ tbCheckLoading: false });
      this['CheckModal'].open(data);
    }

    // const { code = 500, data: { key = '' } = {} } = await checkTable({ orgCode: org.orgCode, type: this.state.key });
    // if(code === 0){
    //   this.setState({ tbCheckLoading: true, progressType: 'check' });
    //   this['SpinProgress'].getProgress(key,'niandu');
    // }

    // if (code === 0) {
    //   this.setState({ tbCheckLoading: true, progressType: 'check' });
    //   let _this = this;
    //   Modal.confirm({
    //     title: '校核完成',
    //     content: `${data}`,
    //     onOk() {
    //       _this.setState({ tbCheckLoading: false});
    //       // console.log(window.open(`/annualStatistics/checkResultPage?type=${key}&orgCode=${org.orgCode}`));ssssss
    //     },
    //     // okText: '查看',
    //     onCancel() {},
    //   });
    // }
  };
  // 报表导出
  newDownLoad = async () => {
    let org: any = getSession('org') || {};
    this.setState({ tbCheckLoading: true });
    const { code = 500 } = await exportNestedTable({
      orgLevelCode: org.orgCode,
      orgCode: org.code,
      // reportYear:moment().format('YYYY')
      year: window.location.pathname === '/annualStatistics/old' ? '20211231' : undefined,
    });
    this.setState({ tbCheckLoading: false });
    if (code == 0) {
      Tip.success('操作提示', '导出成功')
    }
  }
  progressCallback = (res) => {
    const { progressType, key } = this.state;
    let org: any = getSession('org') || {};
    switch (progressType) {
      case 'download':
        const { code: codes = undefined } = res;
        if (codes === '2') {
          this.setState({ tbCheckLoading: false });
          fileDownloadHeader(`/api${res.url}`, res['url'].split('/')[2]);
          Modal.success({
            content: '导出成功',
          });
        } else {
          Modal.error({
            title: '导出失败',
          });
        }
        break;
      case 'check':
        this.setState({ tbCheckLoading: false });
        // const {
        //   positionNum:{ treeOrg:{parentCode = '' ,levelCode:levelCodes = ''} = {} }={},
        //   annualstats:{memType = '', treeOrg:{ levelCode = '',unitId = undefined, id = undefined }={} }={}
        // } = this.props;
        // let orgCode = levelCode;
        // if(memType === '3'){
        //   orgCode = `${id},${unitId}`;
        // }
        const { code = 500, data: { haveErr = true, errSize = 0 } = {} } = res;

        if (code === '2') {
          if (haveErr) {
            this.setState({
              checkPass: false
            });
            Modal.confirm({
              title: '校核完成',
              content: `共${errSize}个错误未通过数据校核，请点击查看校核结果进行修改！`,
              onOk() {
                console.log(window.open(`/annualStatistics/checkResultPage?type=${key}&orgCode=${org.orgCode}`));
              },
              okText: '查看',
              onCancel() { },
            });
          } else {
            this.setState({
              checkPass: true
            });
            Modal.success({
              content: '校核完成，未找到错误.',
            });
          }
        }
        if (code === '3') {
          this.setState({
            checkPass: false
          });
          Modal.error({
            title: '校核失败',
          });
        }
        break;
    }
  };
  render(): React.ReactNode {
    const { rowCell, colCell, tableIndexConfigList, key = '', modalTableLoading, lineConfigList, hasReplenish, queryType, datas = {}, list = [], pagination, tableType, tbCheckLoading, params } = this.state;
    // 增加otherExportPageInfo，便于往年报表导入
    const { tmwTable: { TreeList = [] } = {}, otherExportPageInfo, pegging = true } = this.props;
    return (
      <React.Fragment>
        <div style={{ height: '100%' }}>
          <Spin spinning={this.props.loading['tmwTable/queryExcelConfigReturnHtml'] || tbCheckLoading}>
            <div id={'rightDiv'}>
              <Select style={{ width: 300 }} onChange={this.onSelect} value={key}>
                {TreeList && TreeList.map(tree => {
                  if(tree.type !== '3' && tree.type !== '2'){
                    return (
                      <Option key={tree.levelCode} value={tree.levelCode}>{tree.shortName}</Option>
                    )
                  }
                })}
              </Select>

              <div style={{ display: 'inline-block', marginTop: 4 }}>
                <Button style={{ marginLeft: 10 }} onClick={() => this.nextPage('pre')} disabled={this.disabledNextPageBtn('pre')}>上一表</Button>
                <Button style={{ marginLeft: 10 }} onClick={() => this.nextPage('add')} disabled={this.disabledNextPageBtn('add')}>下一表</Button>
                <Button onClick={this.print} style={{ marginLeft: 10 }}>打印</Button>
                {
                  otherExportPageInfo ? otherExportPageInfo({ state: this.state, props: this.props }) :
                    <React.Fragment>
                      <Button onClick={this.check} type={'primary'} style={{ marginLeft: 10 }} loading={tbCheckLoading}>报表校核</Button>
                      <Button onClick={this.newDownLoad} type={'primary'} style={{ marginLeft: 10 }} loading={tbCheckLoading}>报表导出</Button>
                    </React.Fragment>
                }
              </div>
              <SpinProgress ref={e => this['SpinProgress'] = e}
                callback={(res) => this.progressCallback(res)}>
                <div key={this.state.timeKey} id={'page'} style={{ marginTop: 50, textAlign: 'center' }} dangerouslySetInnerHTML={{ __html: this.state['Html'] }} />
              </SpinProgress>
            </div>
          </Spin>
          {
            pegging &&
            <Pegging tableType={tableType}
              list={list}
              pagination={pagination}
              pageChange={this.findVerData}

              params={{ ...params, reportType: tableType }} ref={e => this['peg'] = e} />
          }
        </div>
        <CheckModal ref={e => this['CheckModal'] = e} htmlType={this.state.key} />
        <ExtractDataModal ref={e => this['ExtractDataModal'] = e} />
        {/* {
          this.state.showButtonModal && <React.Fragment>
            <div style={{width:300,height:50, background: '#fff',position: 'absolute',left:'45%',top:'40%',border: '1px solid', borderRadius:10, textAlign: 'center'}}>
              <Button onClick={() => {

              }}>数据提取</Button>
              <Button type={'primary'} style={{marginLeft:10}} onClick={() => {

              }}>数据反查</Button>
            </div>
          </React.Fragment>
        } */}
        <Modal
          visible={this.state.showButtonModal}
          closable={false}
          style={{
            position: 'absolute',
            left: '50%',
            top: '40%',
          }}
          onCancel={() => {
            this.setState({ showButtonModal: false });
          }}
          width={230}
          bodyStyle={{ padding: 0 }}
          footer={[
            <Button onClick={() => {
              const { tableRow, tableColumn, key, type } = this.state;
              const org = JSON.parse(sessionStorage.getItem('org') || '{}')
              let params = {
                reportCode: key,
                rowIndex: tableRow,
                colIndex: tableColumn,
                orgCode: org?.code,
                orgLevelCode: org?.orgCode,
                type
              };
              this['ExtractDataModal'].open(params);
              this.setState({ showButtonModal: false });
            }}>数据提取</Button>,
            <Button type={'primary'} style={{ marginLeft: 10 }} onClick={() => {
              this.findVerData(1, 10, this.state.name.join('-')).then(({ list = [] }) => {
                this['peg'] && this['peg'].setState({
                  visible: true,
                  showButtonModal: false,
                })
              });
            }}>数据反查</Button>
          ]}>
        </Modal>
      </React.Fragment>
    );
  }
}
