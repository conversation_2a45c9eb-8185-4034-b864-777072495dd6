import React from 'react';
import {SortableContainer, SortableElement} from 'react-sortable-hoc';

interface propsType {
  items: Array<any>,
  onSortEnd?: (any) => void,
  render?: (...any) => any
}

export default class sort extends React.Component<propsType, any> {
  constructor(props) {
    super(props);
    this.state = {};
  };

  renderItem = (value, index) => {
    if (this.props.render) {
      return this.props.render(value, index);
    }
    return (
      <div style={{zIndex: 9999}}>{value}</div>
    );
  };
  render(): React.ReactNode {
    const { items, onSortEnd} = this.props;
    const SortableItem = SortableElement(({value, sortIndex}) => this.renderItem(value, sortIndex));

    const SortableList = SortableContainer(({items}) => {
      return (
        <div>
          {items.map((value, index) => (
            <SortableItem key={`item-${index}`} sortIndex={index} index={index} value={value} />
          ))}
        </div>
      );
    });

    return (
      <SortableList axis={'xy'} items={items} onSortEnd={onSortEnd} />
    );
  }
}
