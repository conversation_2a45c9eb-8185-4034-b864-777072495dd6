import React, { useRef } from 'react';
import domtoimage from 'dom-to-image';
const useDomToimage = (
  props?: any,
): [React.LegacyRef<HTMLDivElement>, (name: string) => void, () => void] => {
  const imgs: any = useRef();
  const download = (name = 'info') => {
    const targetDom = imgs.current;
    var scale = 1; //根据你的缩放比例设置
    domtoimage
      .toBlob(targetDom, {
        width: targetDom.getBoundingClientRect().width * scale,
        height: targetDom.getBoundingClientRect().height * scale,
        style: {
          transform: 'scale(' + scale + ')',
          transformOrigin: 'top left',
        },
      })
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        //输出图片的Base64,dataUrl
        // 下载到PC
        const a = document.createElement('a'); // 生成一个a元素
        const event = new MouseEvent('click'); // 创建一个单击事件
        a.download = name; // 设置图片名称没有设置则为默认
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
        props?.callBack?.();
      })
      .catch(function (error) {
        console.error('oops, something went wrong!', error);
      });
  };

  return [imgs, download];
};

export default useDomToimage;
