import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input, Alert } from 'antd';
import { connect } from 'dva';
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};
import { isEmpty } from '@/utils/method.js';
import Notice from '@/components/Notice';

@connect(({ user }) => ({
  user
}), undefined, undefined, { forwardRef: true })
class index extends React.Component<any, { visible: boolean,isqz:any,isChange: boolean}> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      isqz: undefined,
      isChange: false
    }
  }

  showModal = (key?:any) => {
    this.setState({
      visible: true,
      isqz:key
    })
  };
  validFunction = (rule, value, callback) => {
    if (value) {
      switch (rule.field) {
        case 'password':
          if (value.length < 6 || value.length > 12) {
            return callback('密码长度不足')
          } else if (!(/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/).test(value)) {
            return callback('格式有误')
          } else if ((/\s+/g).test(value)) {
            return callback('密码不能包含空格')
          }
          break;
      }
    }
    callback()
  };
  handleOk = (account) => {
    // const {handleOk}=this.props;
    // if(handleOk){
    //   handleOk();
    // }
    this.props.form.validateFieldsAndScroll(async (errors, values) => {
      if (errors) {
        return;
      }
      if(this.state.isqz){
        values['isLoginUpdatePassword'] = '1'
      }
      console.log(values,'valuesvalues')
      this.props.dispatch({
        type: 'user/upPassword',
        payload: {
          data: {
            ...values,
            id: account,
          }
        }
      }).then(res => {
        if (res['code'] === 0) {
          Notice("操作提示", '密码修改成功!', "check-circle", "green");
          this.setState({
            isChange: true //判断是不是修改密码成功了
          },()=>{
            this.handleCancel();
          })
         
        } else {
          Notice("操作提示", res['message'], "exclamation-circle-o", "orange");
        }
      });
    });

  };
  handleCancel = () => {
    this.setState({ 
      visible: false,
    }); this.props.form.resetFields()
    let { isclear } = this.props;
    const { isChange } = this.state;
      isclear&&isclear(isChange)
  };
  render(): React.ReactNode {
    let { children, account } = this.props;
    const { getFieldDecorator } = this.props.form;
    const { isqz } = this.state;
    return (
      <React.Fragment>
        {
          children ? React.cloneElement(children as any, {
            onClick: this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          title="修改密码"
          visible={this.state.visible}
          onOk={() => this.handleOk(account.id)}
          onCancel={this.handleCancel}
          width={500}
          maskClosable={false}
        >
          <Form {...formItemLayout}>
            {
              isqz&&
              <Alert
              message="温馨提示"
              description="因长时间未修改密码，为了系统安全，请先修改密码后重新登录"
              type="warning"
              showIcon
            />
            }
            <Alert
              message="温馨提示"
              description={
                <div>
                  <p>密码必须含有大写字母，小写字母，数字，特殊符号中的三种及以上，长度为6-12位</p>
                </div>
              }
              type="info"
              showIcon
            />
            {/* <FormItem
              label={'姓名'}
            >
              {getFieldDecorator('name', {
                // rules: [{ required: true, message: '请输入!' }],
              })(
                <span>{account.name}</span>
              )}
            </FormItem> */}
            <FormItem
              style={{ marginTop: '10px' }}
              label={'新密码'}
            >
              {getFieldDecorator('password', {
                rules: [{ required: true, message: '请输入!' }, { validator: this.validFunction }],

              })(
                <Input placeholder="请输入新密码" />
              )}
            </FormItem>
          </Form>
        </Modal>
      </React.Fragment>
    );
  }
}
export default Form.create()(index);
