import React from 'react';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Alert, Button } from 'antd';
import { getSession } from '@/utils/session';
import { getIdCardInfo,correctIdcard } from '@/utils/method';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {

    }
  }
  firstSubmit=()=>{
    const {submit} = this.props;
    const org = getSession('org') || {} ;
    this.props.form.validateFieldsAndScroll(async(err, val) => {
      if (!err) {
        let result =await correctIdcard(val['name'],val['idcard']);
        if(result['code']!='200'){
          this.props.form.setFields({
            idcard:{
              value:val['idcard'],
              errors:[new Error('经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')]
            }
          })
          Tip.error('操作提示','经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')
          return
        }else{
          val['idCardReason']=result['reason']
          val['idCardReasonName']=result['reasonName']
        }
        const res = await this.props.dispatch({
          type:'memMultiple/findMem',
          payload:{...val,orgCode:org['orgCode']}
        });
        submit && submit(res);
      }
    });
  };
  validatorIdcard =async (rule, value, callback) => {
    if (!value) {
      callback('身份证必填');
    }
    if (value && value.length !== 18 && process.env.idCheck != 'false') {
      callback('身份证应该为18位');
    }
    if (getIdCardInfo(value) === 'Error') {
      callback('身份证格式错误,请核对身份证图片');
    } else {
      // let fieldValue = this.props.form.getFieldValue('name');
      // let res=await geitCard({idCard:fieldValue,name:value});
      callback()
    }
  };
  render() {
    const {form,loading:{effects = {}} ={}} = this.props;
    const { getFieldDecorator } = form;
    return (
      <div>
        <Alert message="注：流动党员请到流动党员子系统进行流入登记。" type="info" showIcon />
        <div style={{marginBottom:'10px'}}/>
        <Form {...formItemLayout}>
          <FormItem
            label="党员姓名"
          >
            {getFieldDecorator('name', {
              rules: [{ required: true, message: '党员姓名' }],
            })(
              <Input placeholder={'党员姓名'}/>
            )}
          </FormItem>
          <FormItem
            label="身份证号"
          >
            {getFieldDecorator('idcard', {
              rules: [{ required: true, message: '身份证号' },{ validator: this.validatorIdcard },],
            })(
              <Input placeholder={'身份证号'}/>
            )}
          </FormItem>
          <div style={{textAlign:'center'}}>
            <Button type={'primary'} htmlType={'submit'} icon={<LegacyIcon type={'right'} />} onClick={this.firstSubmit} loading={effects['memMultiple/findMem']}>下一步</Button>
          </div>
        </Form>
      </div>
    );
  }
}
export default Form.create()(index);
