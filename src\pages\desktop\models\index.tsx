import modelExtend from 'dva-model-extend';
import { listPageModel } from 'src/utils/common-model';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import { getSession } from "@/utils/session";
import { getMemTotal, getOrgTotal, memberRecruitment, paymentSituation, activitySituation, getFlowJurisdiction } from '../services';
const desktop = modelExtend(listPageModel, {
  namespace: "desktop",
  state: {
    abroadInfo: {},
    list: [],
    pagination: {},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if (pathname === '/desktop') {
          const { managerOrgCode = '' } = getSession('roles') || { managerOrgCode: '' };
          const userInfo = getSession("user") || { id: '' }
          console.log(userInfo, "userInfouserInfouserInfouserInfouserInfo");
          dispatch({ type: 'getMemTotal', payload: { data: { orgCode: managerOrgCode, } } });
          dispatch({ type: 'getOrgTotal', payload: { data: { orgCode: managerOrgCode, } } });
          dispatch({ type: 'memberRecruitments', payload: { data: { orgCode: managerOrgCode, } } });
          dispatch({ type: 'paymentSituation', payload: { data: { memOrgOrgCode: managerOrgCode, } } });
          dispatch({ type: 'activitySituation', payload: { memOrgOrgCode: managerOrgCode } });
          dispatch({ type: 'getFlowJurisdiction', payload: { userId: userInfo?.id } });
        }
      });
    }
  },
  effects: {
    // 人员总数
    *getMemTotal({ payload }, { call, put }) {
      const { data = [] } = yield call(getMemTotal, payload);
      let total = 0;
      if (!_isEmpty(data) && _isArray(data)) {
        total = data.find(item => item['d08Name'] === '党员总数').count
      }
      yield put({
        type: 'updateState',
        payload: {
          memTotal: total
        }
      })
    },
    // 组织总数
    *getOrgTotal({ payload }, { call, put }) {
      const { data = [] } = yield call(getOrgTotal, payload);
      let total = 0;
      if (!_isEmpty(data) && _isArray(data)) {
        total = data.find(item => item['orgTypeName'] === '党组织总数').count
      }
      yield put({
        type: 'updateState',
        payload: {
          orgTotal: total
        }
      })
    },
    // 发展党员
    *memberRecruitments({ payload }, { call, put }) {
      const { code = 500, data = {} } = yield call(memberRecruitment, payload);
      if (code === 0) {
        yield put({
          type: 'updateState',
          payload: {
            devMem: data
          }
        })
      }
    },
    // 党费
    *paymentSituation({ payload }, { call, put }) {
      const { code = 500, data = {} } = yield call(paymentSituation, payload);
      if (code === 0) {
        yield put({
          type: 'updateState',
          payload: {
            fee: data
          }
        })
      }
    },
    // 活动
    *activitySituation({ payload }, { call, put }) {
      const { code = 500, data = {} } = yield call(activitySituation, payload);
      if (code === 0) {
        yield put({
          type: 'updateState',
          payload: {
            activity: data
          }
        })
      }
    },
    // 获取是否为顶级权限  用于判断  流动党员 流入管理 县级流入库 退回按钮
    *getFlowJurisdiction({ payload }, { call, put }) {
      const { code = 500, data = {} } = yield call(getFlowJurisdiction, payload);
      if (code === 0) {
        // console.log("flowflowflowflowflowflowflowflowflow", JSON.stringify(data));
        sessionStorage.setItem('flowJurisdiction', JSON.stringify(data))
        yield put({
          type: 'updateState',
          payload: {
          }
        })
      }
    },
  }
});
export default desktop;
