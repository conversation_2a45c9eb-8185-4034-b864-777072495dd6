/**
 * 党员管理services
 */
import request from "@/utils/request";
import qs from 'qs';



export function memLockedList(params) {
  return request(`/api/lock/locked`, {
    method: 'POST',
    body: params,
  });
}

export function memUnlocked(params) {
  return request(`/api/lock/unlocked`, {
    method: 'POST',
    body: params,
  });
}

export function memBatchLock(params) {
  return request(`/api/lock/batchLock`, {
    method: 'POST',
    body: params,
  });
}


export function lockFiled(params) {
  return request(`/api/mem/lockFiled`, {
    method: 'POST',
    body: params,
  });
}

export function joinPartyRevise(params) {
  return request(`/api/mem/joinPartyRevise`, {
    method: 'POST',
    body: params,
  });
}

export function findMemResume(params) {
  return request(`/api/mem/findMemResume?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

export function findMemCorrelation(params) {
  return request(`/api/mem/findMemCorrelation?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function backToProbationary(params) {
  return request(`/api/mem/backToProbationary?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function zybackToProbationary(params) {
  return request(`/api/zunyi/mem/new/backToProbationary?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

export function backToDevelopMem(params) {
  return request(`/api/mem/backToDevelopMem?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

export function lostContact(params) {
  return request(`/api/mem/lostContact`, {
    method: 'POST',
    body: params,
  });
}

export function getList(params) {
  let action = `/api/mem/getList`
  if (window.location.pathname === '/developMem/out') {
    action = `/api/mem/develop/getList`
  }
  return request(action, {
    method: 'POST',
    body: params,
  });
}

export function getzylist(params) {
  return request(`/api/zunyi/mem/new/list`, {
    method: 'POST',
    body: params,
  });
}
export function addMem(params) {
  return request(`/api/mem/addMem`, {
    method: 'POST',
    body: params,
  });
}
export function zyaddMem(params) {
  return request(`/api/zunyi/mem/new/addMem`, {
    method: 'POST',
    body: params,
  });
}

export function findMem(params) {
  return request(`/api/mem/findByCode?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function updateMem(params) {
  return request(`/api/mem/updateMem`, {
    method: 'POST',
    body: params,
  });
}
export function getOrgLevelList(params) {
  return request(`/api/mem/getMemOrgList?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function findByNameAndIdcard(params) {
  return request(`/api/mem/findByNameAndIdcard?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
// 转正
export function becomeFullMem(params) {
  return request(`/api/mem/becomeFullMem`, {
    method: 'POST',
    body: params,
  });
}
// 延长预备期
export function prolongationMem(params) {
  return request(`/api/mem/prolongationMem`, {
    method: 'POST',
    body: params,
  });
}
// 撤销延长预备期
export function repealProlongationMem(params) {
  return request(`/api/mem/repealProlongationMem`, {
    method: 'POST',
    body: params,
  });
}

// 根据人员获取职务信息列表
export function getListByMemCode(params) {
  return request(`/api/unit/committee/getListByMemCode?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

// 根据人员获取职务信息列表
export function partyPosition(params) {
  return request(`/api/committee/getListByMemCode?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

export function getErrorList(params) {
  return request(`/api/ykz/correction/list`, {
    method: 'POST',
    body: params
  });
}
export function errorChangeState(params) {
  return request(`/api/ykz/correction/changeState?${qs.stringify(params)}`, {
    method: 'POST',
    body: params
  });
}
export function findHistoryByCode(params) {
  return request(`/api/mem/findHistoryByCode?${qs.stringify(params)}`,{
    method:'Get',
  });
}

export function flowcountdy(params) {
  return request(`/api/zunyi/mem/new/count`,{
     method:'POST',
    body:params,
  });
}
export function zymemInfo(params) {
  return request(`/api/zunyi/mem/new/memInfo?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function findAuditProcess(params) {
  return request(`/api/zunyi/process/findAuditProcess`,{
     method:'POST',
    body:params,
  });
}

export function findNewFillAudit(params) {
  return request(`/api/zunyi/digitalaudit/findNewFillAudit?${qs.stringify(params)}`,{
    method:'GET',
  });
}