/**
 * 单位-编辑-政府班子成员
 **/
import React, { Fragment } from 'react';
import ListTable from '@/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { <PERSON><PERSON>, Collapse, Divider, Popconfirm, Tooltip, Modal } from 'antd';
import AddMember from '../addMember';
import { connect } from "dva";
import moment from 'moment';
import Tip from '@/components/Tip';
import AddJcInfo from '../addJcInfo';
import { PlusOutlined } from '@ant-design/icons';
import styles from '@/pages/org/list/subpage/addoredit/leader.less';
import head from '@/assets/head.jpg';
import { delElect, committeeBackOut, getElectList, itteeList, delUnitCommittee } from '@/pages/[unit]/services'
import EndOfTerm from '../end';
import CTbale, { PanelTable, PanelListTable } from '@/components/CollapseTable';
// @ts-ignore
@connect(({ unit }) => ({ unit }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: {},
      memList: [],
      memInfo: {},
      editMemTypeWay: 'default',
      currentClickJc:{} // 点击“届内历史任职”时，保存当前点击的届次
    }
  }
  componentDidMount(): void {
    // this.action();
  }
  addOrEdit = async (item, edit = false) => {
    let obj = await this.props.dispatch({
      type: 'unit/unitFind',
      payload: {
        code: item['code']
      }
    });
    if (obj && obj['code'] === 0) {
      this.setState({
        memInfo: obj['data'],
      }, () => {
        this['AddMember'].showModal(edit);
      });
    }
  };
  confirm = async (item, index) => {
    let obj = await this.props.dispatch({
      type: 'unit/delite',
      payload: {
        code: item['code']
      }
    });
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', '删除成功');
      const { paginationIte } = this.props.unit;
      const { page, pageSize } = paginationIte;
      this.collapseChange(index.toString())
    }
  };
  action = (page?: number, pageSize?: number) => {
    const { basicInfo = {} } = this.props.unit;
    this.props.dispatch({
      type: 'unit/getElectList',
      payload: {
        pageNum: page || 1,
        // pageSize:pageSize || 10,
        pageSize: 100,
        unitCode: basicInfo['code']
      }
    })
  };
  onPageChange = (page, pageSize) => {
    this.action(page, pageSize)
  };
  del = async (e, item) => {
    e.stopPropagation();
    const { code = 500 } = await delElect({ electCode: item['code'] });
    if (code === 0) {
      // this.action();
      this['CTbale'].getList();
      Tip.success('操作提示', '删除成功');
    }
  };
  getHistoryList = async (item, key) => {
    const { basicInfo: { code: unitCode = '' } = {} } = this.props.unit;
    const payload = {
      pageNum: 1,
      pageSize: 100,
      unitCode,
      leave: 1,
      electCode: item['code']
    }
    const { code = 500, data: { list = [] } = {} } = await this.props.dispatch({
      type: 'unit/itteeList',
      payload: {
        ...payload,
      }
    })
    if (code == 0) {
      this.setState({
        hisData: list,
        visible: true,
        _key: key,
        currentClickJc: item
      })
    }
  };
  reSetHistoryList = async (item, key) => {
    const { basicInfo: { code: unitCode = '' } = {} } = this.props.unit;
    const payload = {
      pageNum: 1,
      pageSize: 100,
      unitCode,
      leave: 1,
      electCode: item['code']
    }
    const { code = 500, data: { list = [] } = {} } = await this.props.dispatch({
      type: 'unit/itteeList',
      payload: {
        ...payload,
      }
    })
    if (code == 0) {
      this.setState({
        hisData: list,
        _key: key
      })
    }
  };
  header = (item, key) => {
    const isHistory = item?.isHistory == 0
    return (
      <span className={styles.header}>
        <span>换届日期：{item['tenureStartDate'] && moment(item['tenureStartDate']).format('YYYY-MM-DD')}~{item['tenureEndDate'] && moment(item['tenureEndDate']).format('YYYY-MM-DD')}</span>
        {/*<span>选举方式：{item['electName']}</span>*/}
        <div>
          <a onClick={(e) => {
            e.stopPropagation();
            this['CTbale'].setActiveKeys(key)
            this.getHistoryList(item, key);
          }}>届内历史任职</a>
          {isHistory && <a onClick={(e) => this.editJc(e, item)}>编辑</a>}
          {/* 1、 党组织班子和单位的行政班子， 届次的删除按钮 ， 屏蔽了。 --王察 */}
          {/* <div style={{display: 'inline-block'}} onClick={(e:any)=>e.stopPropagation()}>
           <Popconfirm title="是否删除该信息?" onConfirm={(e)=>this.del(e,item)} okText="是" cancelText="否" onCancel={(e:any)=>e.stopPropagation()}>
             <a className={'del'} onClick={(e)=>e.stopPropagation()}>删除</a>
           </Popconfirm>
           </div> */}
        </div>
      </span>
    )
  };
  editJc = (e, item) => {
    e.stopPropagation();
    this.setState({ type: 'edit', dataInfo: item }, () => this['AddJcInfo'].showModal());
  };

  collapseChange = async (key, history = false) => {
    const keys = [...key].pop();
    const { iteList = [], basicInfo: { code: unitCode = '' } = {} } = this.props.unit;
    let item = iteList[keys];
    this.setState({ _key: keys });
    const payload = {
      pageNum: 1,
      pageSize: 100,
      unitCode,
      leave: history ? 1 : 0,
      electCode: item['code']
    }
    const { code = 500, data: { list = [] } = {} } = await this.props.dispatch({
      type: 'unit/itteeList',
      payload: {
        ...payload,
      }
    })
    if (code === 0) {
      if (history) {
        this.setState({
          hisData: list,
          visible: true,
        })
      } else {
        this.setState({
          dataInfo: item,
          memList: list
        })
      }
    }
  };
  clos = () => {
    this.setState({
      visible: false,
      hisData: undefined,
      currentClickJc: {}
    })
  }
  end = (item, key) => {
    this['EndOfTermRef'].open({ ...item, key });
  };
  render() {
    const { iteList = [], paginationIte, basicInfo: { code: unitCode = '', d04Code = '' } = {} } = this.props.unit;
    const { dataInfo = {}, type, memList, memInfo = {}, visible, hisData = [] } = this.state;

    let memTitleText = '班子成员';
    if (d04Code == '921' || d04Code == '922') {
      memTitleText = '居委会成员';
    }
    if (d04Code == '923') {
      memTitleText = '村委会成员';
    }
    if (d04Code.startsWith('4')) {
      memTitleText = '领导成员';
    }
    return (
      <div style={{ padding: '0 20px' }}>
        <Modal
          title={'届内历史任职'}
          visible={visible}
          onOk={this.clos}
          onCancel={this.clos}
          width={630}
          bodyStyle={{ maxHeight: '60vh', overflow: 'auto' }}
        >
          {
            hisData.map((item, ind) => {
              return (
                <div key={ind} className={styles.panel_body}>
                  <Tooltip title={item['d022Name']}>
                    <div><img src={head} style={{ width: 128, height: 158 }} /></div>
                  </Tooltip>
                  <div>
                    <h4>{item.memName}</h4>
                    <h4>
                      {/* 2025/3/25 内历史任职中的编辑直接不要，只能撤销  */}
                      {/* <a onClick={() => {
                         this.setState({editMemTypeWay:'history'})
                         this.addOrEdit(item);
                       }} style={{marginRight:4}}>编辑</a> */}
                      { this.state?.currentClickJc?.isHistory != 1 ? <a onClick={async () => {
                        const { code = 500 } = await committeeBackOut({ data: item });
                        if (code === 0) {
                          this[`PanelTable_${this.state._key}`].getList();
                          this.setState({
                            hisData: this.state.hisData.filter(it => it.code != item.code),
                            _key: undefined,
                          })
                        }
                      }}>撤销</a> : ''}
                    </h4>
                  </div>
                </div>
              )
            })
          }
        </Modal>
        <AddJcInfo
          {...this.props}
          type={type}
          title={type === 'edit' ? '编辑届次信息' : '新增届次信息'}
          wrappedComponentRef={(e) => this['AddJcInfo'] = e}
          onClose={() => this.setState({ dataInfo: undefined, type: undefined })}
          dataInfo={this.state.dataInfo}
          queryList={() => {
            this['CTbale'].getList()
          }}
        >
          <Button type="primary" icon={<PlusOutlined />} style={{ marginBottom: '10px' }}>添加届次信息</Button>
        </AddJcInfo>

        <CTbale
          tableActionOtherQueries={{ unitCode: this.props.unit.basicInfo.code }}
          tableListAction={getElectList}
          ref={e => this['CTbale'] = e}
          mains={(item, index) => {
            const isHistory = item?.isHistory == 0
            return (
              <React.Fragment>
                <AddMember
                  key={index}
                  title={memInfo['code'] ? (isHistory ? `编辑${memTitleText}` : `查看${memTitleText}`) : `新增${memTitleText}`}
                  wrappedComponentRef={(e) => this['AddMember'] = e}
                  onClose={() => this.setState({ dataInfo: undefined, type: undefined, memInfo: {} })}
                  queryList={() => {
                    this[`PanelTable_${index}`].getList();
                    this.reSetHistoryList(item, index);
                  }}
                  {...this.props}
                  {...this.state}
                  dataInfo={item}
                >
                  {isHistory && <Button type="primary" style={{ marginRight: 10 }}>添加人员</Button>}
                </AddMember>
                <div style={{ height: 10 }}></div>
                <PanelListTable
                  data={item}
                  mainsListAction={itteeList}
                  mainsActionOtherQueries={{ unitCode: this.props.unit.basicInfo.code, leave: 0, electCode: item['code'] }}
                  ref={e => this[`PanelTable_${index}`] = e}
                  columns={[
                    {
                      title: '姓名',
                      dataIndex: 'memName',
                      width: 100,
                    },
                    {
                      title: '是否本单位党员',
                      dataIndex: 'memTypeCode',
                      width: 100,
                      align: 'center',
                      render: (text) => {
                        return text == 1 ? '是' : '否'
                      }
                    },
                    {
                      title: '行政职务',
                      dataIndex: 'd25Name',
                      width: 100,
                      align: 'center',
                    },
                    {
                      title: '任职时间',
                      dataIndex: 'startDate',
                      width: 100,
                      align: 'center',
                      render: (text, record) => {
                        if (text) {
                          return text ? moment(text).format('YYYY-MM-DD') : ''
                        }
                      },
                    },
                    {
                      title: '任职到期时间',
                      dataIndex: 'empEndDate',
                      width: 100,
                      align: 'center',
                      render: (text, record) => {
                        if (text) {
                          return text ? moment(text).format('YYYY-MM-DD') : ''
                        }
                      },
                    },
                    {
                      title: '是否参加养老保险',
                      dataIndex: 'endowmentInsuranceForUrbanEmployees',
                      width: 100,
                      align: 'center',
                      render: (text) => {
                        return text == 1 ? '是' : '否'
                      }
                    },
                    {
                      title: '人员来源',
                      dataIndex: 'd0121Name',
                      width: 100,
                      align: 'center',
                    },
                    {
                      title: '操作',
                      dataIndex: 'action',
                      width: 200,
                      render: (text, record) => {
                        return (
                          <div>
                            {!isHistory && <Fragment>
                              <a onClick={() => {
                                this.setState({ editMemTypeWay: 'default' })
                                this.addOrEdit(record, true);
                              }}>编辑</a>
                            </Fragment>}
                            {isHistory && <Fragment>
                              <a onClick={() => {
                                this.setState({ editMemTypeWay: 'default' })
                                this.addOrEdit(record);
                              }}>编辑</a>
                              <Divider type="vertical" />
                              <a onClick={() => this.end(record, index)}>转为历史任职</a>
                              <Divider type="vertical" />
                              <Popconfirm title="删除仅针对于错误录入的情况，离任请选择转为历史任职" onConfirm={async () => {
                                const { code = 500 } = await delUnitCommittee({ code: record['code'] });
                                if (code === 0) {
                                  Tip.success('操作提示', '操作成功');
                                  this[`PanelTable_${index}`].getList();
                                }
                              }} okText="是" cancelText="否">
                                <a onClick={e => e.stopPropagation()}>删除</a>
                              </Popconfirm>
                            </Fragment>}
                          </div>
                        )
                      },
                    },
                  ]}
                />
              </React.Fragment>

            )
            return (
              <PanelTable
                key={index}
                ref={e => this[`PanelTable_${index}`] = e}
                data={item}
                mainsListAction={itteeList}
                mainsActionOtherQueries={{ unitCode: this.props.unit.basicInfo.code, leave: 0, electCode: item['code'] }}
                add={() => {
                  return (
                    <AddMember
                      key={index}
                      title={memInfo['code'] ? `编辑${memTitleText}` : `新增${memTitleText}`}
                      wrappedComponentRef={(e) => this['AddMember'] = e}
                      onClose={() => this.setState({ dataInfo: undefined, type: undefined, memInfo: {} })}
                      queryList={() => {
                        this[`PanelTable_${index}`].getList();
                        this.reSetHistoryList(item, index);
                      }}
                      {...this.props}
                      {...this.state}
                      dataInfo={item}
                    >
                      <div className={styles.add}><PlusOutlined style={{ fontSize: '50px', transform: 'translateY(100%)' }} /></div>
                    </AddMember>
                  )
                }}
                linkEdit={(items) => {
                  return (
                    <React.Fragment key={index}>
                      <a onClick={() => {
                        this.setState({ editMemTypeWay: 'default' })
                        this.addOrEdit(items);
                      }}>编辑</a>
                      <a onClick={() => this.end(items, index)}>转为历史任职</a>
                      <Popconfirm title="删除仅针对于错误录入的情况，离任请选择转为历史任职" onConfirm={async () => {
                        const { code = 500 } = await delUnitCommittee({ code: items['code'] });
                        if (code === 0) {
                          Tip.success('操作提示', '操作成功');
                          this[`PanelTable_${index}`].getList();
                        }
                      }} okText="是" cancelText="否">
                        <a onClick={e => e.stopPropagation()}>删除</a>
                      </Popconfirm>
                    </React.Fragment>
                  )
                }} />
            )
          }}
          panelHeader={(obj, listLndex) => {
            return this.header(obj, listLndex)
          }} />

        <EndOfTerm ref={e => this['EndOfTermRef'] = e}
          onOK={(e) => {
            this[`PanelTable_${e.key}`].getList();
          }} />
        {/*<WhiteSpace/>*/}
        {/*<WhiteSpace/>*/}
        {/*<ListTable columns={columns} data={iteList} pagination={paginationIte} onPageChange={this.onPageChange}/>*/}
      </div>
    );
  }
}
