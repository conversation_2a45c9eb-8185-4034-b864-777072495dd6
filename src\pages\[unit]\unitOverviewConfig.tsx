import React from 'react';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _ceil from 'lodash/ceil';

export const cardConfig = [
  {
    key:'3001',
    value:{
      icon:'home',
      coverImg:require('@/components/CardsGroup/assets/unit/danwei.jpg'),
      iconColor:'#17C1C5',
      title:'单位数',
      suffix:'个',
      action:'/api/chart/unit/getUnitTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['isCreateOrgName'] === '单位总数'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['isCreateOrg'] === 1){zNum = item['count']}
            if(item['isCreateOrg'] === 0){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>已建立党组织:{zNum}个</div>
            <div>未建立党组织:{yNum}个</div>
          </div>
        )
      }
    },
  },
];
export const chartConfig = [
  {
    key:'3002', // 单位类别
    value:{
      coverImg:require('@/components/CardsGroup/assets/unit/chart_leibie.jpg'),
      action:'/api/chart/unit/getUnitTypeTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d04Name']);
            arr.push({
              name:item['d04Name'],
              value:item['count']
            })
          });
          arr = arr.filter(item=>item['name'] !== '党员总数');

        }
        return {
          title : {
            text: '单位类别',
            // subtext: '纯属虚构',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '单位类别',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
    }
  },
  {
    key:'3003', // 建立党组织情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/unit/chart_jianli.jpg'),
      action:'/api/chart/unit/getD05CodeTotal',
      option:(val)=>{
        // console.log(val,'val')
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d05Name']);
            arr.push({
              name:item['d05Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '建立党组织情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            // orient: 'vertical',
            left: 'center',
            top:'bottom',
            data: arrName
          },
          series: [
            {
              name:'建立党组织情况',
              type:'pie',
              center: ['50%', '45%'],
              radius: ['40%', '60%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '24',
                    fontWeight: 'bold'
                  }
                }
              },
              labelLine: {
                normal: {
                  show: true
                }
              },
              data:arr
            }
          ]
        }
      },
    }
  },
  {
    key:'3004', //单位隶属关系情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/unit/chart_lishu.jpg'),
      action:'/api/chart/unit/getD35CodeTotal',
      option:(val)=>{
        let arrName:Array<string> = [];
        let arrValue:Array<string> = [];
        let arrPersent:Array<number> = [];
        let all = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d35Name']);
            arrValue.push(item['count']);
            all += item['count'];
          });
          val.forEach(item=>{
            arrPersent.push(_ceil(item['count']/all*100,2))
          })
        }
        arrName = arrName.reverse();
        arrValue = arrValue.reverse();
        arrPersent = arrPersent.reverse();
        return {
          title : {
            text: '单位隶属关系情况',
            x:'left'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter:  function (items = []) {
              let num = 0;
              let numName = '';
              let numVal = 0;
              if(_isArray(items) && !_isEmpty(items)){
                const [item] = items;
                const {name = '',value = 0} = item || {};
                numName = name;
                numVal = value;
                if(!_isEmpty(val) && _isArray(val)){
                  val.forEach(item=>{
                    if(item['d35Name'] === name){
                      num = item['count']
                    }
                  });
                }
              }
              return `${numName}:<br/> ${num}个 ${numVal}%`;
            }
          },
          grid: {
            left: '0%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            "axisLabel": {
              "interval": 0,
              formatter: '{value}%',
            }
          },
          yAxis: {
            type: 'category',
            data: arrName
          },
          series: [{
            name: '单位隶属关系情况',
            type: 'bar',
            data: arrPersent,
            itemStyle: {
              color: function(params) {
                let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                return colorList[params.dataIndex];
              }
            },
          }]
        }
      },
    }
  },
];
