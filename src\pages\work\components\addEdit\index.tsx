import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Modal, Radio, Upload } from "antd";
import Editor from '@/components/Editor';
import { connect } from "dva";
import Tip from '@/components/Tip';
import { getSession } from "@/utils/session";
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import UploadComp, { getInitFileList } from '@/components/UploadComp';
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
// @ts-ignore
@connect(({ workTrend }) => ({ workTrend }), undefined, undefined, { forwardRef: true })
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false
    }
  }
  handleOk = () => {
    this.props.form.validateFields(async (err, val) => {
      if (!err) {
        if (val['titlePhotoList']) {
          let temp: any = [];
          val['titlePhotoList'].forEach(obj => {
            if (obj['response'] && obj['response']['code'] == '0') {
              temp.push(obj['response']['data'][0])
            } else {
              temp.push(obj)
            }
          })
          val['titlePhotoList'] = temp;
        }
        const org = getSession('org') || {};
        const { title, editObj = {} } = this.props;
        let obj = undefined;
        if (editObj['code']) {
          obj = await this.props.dispatch({
            type: 'workTrend/updated',
            payload: {
              data: {
                ...editObj,
                ...val
              }
            }
          });
        } else {
          obj = await this.props.dispatch({
            type: 'workTrend/add',
            payload: {
              data: {
                createOrgCode: org['code'],
                createOrgOrgCode: org['orgCode'],
                type: 1,
                ...val
              }
            }
          });
        }
        if (obj && obj['code'] === 0) {
          Tip.success('操作提示', editObj['code'] ? '修改成功' : '新增成功');
          this.handleCancel();
          this.props.refresh();
        }
      }
    });

  };
  handleCancel = () => {
    this.setState({
      visible: false,
    })
  };
  imgChange = (list) => {
    // this.setState({
    //   imgList:list
    // })
  };
  render() {
    const { visible, imgList = [] } = this.state;
    const { getFieldDecorator } = this.props.form;
    const { title, editObj = {}, isAudit = false } = this.props;
    const org = getSession('org') || {};
    const uploadButton = (
      <div>
        {/*{loading ? <LoadingOutlined /> : <PlusOutlined />}*/}
        <PlusOutlined />
        <div style={{ marginTop: 8 }}>上传图片</div>
      </div>
    );
    if (editObj['id'] && editObj['titlePhotoList']) {
      let temp: any = []
      editObj['titlePhotoList'].forEach(item => {
        temp.push({ uid: new Date().valueOf(), thumbUrl: `/api${item['url']}`, ...item })
      })
      editObj['titlePhotoList'] = temp;
    }
    return (
      <div>
        <Modal
          title={title}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1200}
          destroyOnClose={true}
        >
          {
            visible && <Form {...formItemLayout}>
              {
                !editObj['code'] ? <FormItem
                  label="组织名称"
                >
                  {getFieldDecorator('orgName', {
                    initialValue: editObj['orgName'] || org['name'],
                    rules: [{ required: true, message: '请输入组织名称' }],
                  })(
                    <Input placeholder={'请输入组织名称'} disabled={true} />
                  )}
                </FormItem> : null
              }

              <FormItem
                label="标题"
              >
                {getFieldDecorator('tittle', {
                  initialValue: editObj['tittle'],
                  rules: [{ required: true, message: '请输入标题' }],
                })(
                  <Input placeholder={'请输入标题'} disabled={isAudit} />
                )}
              </FormItem>
              {/* <FormItem
                label="封面照片"
              >
                {getFieldDecorator('titlePhotoList', {
                  initialValue:getInitFileList(editObj['titlePhotoList']),
                  rules: [{ required: true, message: '封面照片' }],
                })(
                  <UploadComp
                    listType="picture-card"
                    className="avatar-uploader"
                    accept={'.jpg,.png'}
                    onChange={this.imgChange}
                    maxLen={3}
                    files={getInitFileList(editObj['titlePhotoList'])}
                  >
                    {uploadButton}
                  </UploadComp>
                )}
                <span style={{color:'red',fontSize:14}}>*最多支持三张图片</span>
              </FormItem> */}
              <FormItem
                label="内容"
              >
                {getFieldDecorator('context', {
                  initialValue: editObj['context'],
                  rules: [{ required: true, message: '请输入内容' }],
                })(
                  <Editor id={'e1'} init={editObj['context']} disabled={isAudit} />
                )}
              </FormItem>

              <FormItem
                label="是否发送"
              >
                {getFieldDecorator('isNotice', {
                  initialValue: editObj['isNotice'],
                  rules: [{ required: true, message: '请选择' }],
                })(
                  <Radio.Group disabled={isAudit}>
                    <Radio value={1}>是</Radio>
                    <Radio value={0}>否</Radio>
                  </Radio.Group>
                )}
              </FormItem>

              <FormItem
                label="审核人"
              >
                {getFieldDecorator('checkPerson', {
                  initialValue: editObj['checkPerson'],
                  rules: [{ required: true, message: '请输入审核人' }],
                })(
                  <Input placeholder={'请输入审核人'} disabled={isAudit} />
                )}
              </FormItem>

            </Form>
          }
        </Modal>
      </div>
    )
  }
}


export default Form.create()(index)
