import React, { Fragment } from 'react';
import { Tabs } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import { connect } from 'dva';
import MemBasic from '@/pages/mem/manage/components/membasic';
import OrgList from '@/pages/org/list';
import UnitList from '@/pages/[unit]';
import { _history as router } from '@/utils/method';
import qs from 'qs';
const TabPane = Tabs.TabPane;

export const memTabs = [
  { key: '1', title: '党员信息' },
  { key: '3', title: '组织信息' },
  { key: '2', title: '单位信息' },
];
// @ts-ignore
@connect(({ loading, memBasic }) => ({ loading, memBasic }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      active: '1',
    };
  }
  onChange = (val) => {
    const { query, pathname } = this.props.location;
    router.push(
      `?${qs.stringify({
        ...query,
        pageNum: 1,
        pageSize: 10,
        locked: pathname == '/unlock/locked' ? 1 : 0,
        lockObject: val,
      })}`,
    );
  };
  render(): React.ReactNode {
    const { query = {} } = this.props.location || {};
    return (
      <div style={{ height: '100%', overflow: 'hidden' }}>
        <Tabs activeKey={query.lockObject} onChange={this.onChange}>
          {!_isEmpty(memTabs) &&
            memTabs.map((item) => <TabPane tab={item['title']} key={item['key']} />)}
        </Tabs>
        {query.lockObject == '1' && <MemBasic {...this.props} compType={'lock'} />}
        {query.lockObject == '3' && <OrgList {...this.props} compType={'lock'} />}
        {query.lockObject == '2' && <UnitList {...this.props} compType={'lock'} />}
      </div>
    );
  }
}
