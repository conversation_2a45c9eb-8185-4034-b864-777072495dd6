import React from 'react';
import { getSession } from "@/utils/session";
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import { getRole } from '@/services';
function say(e) {
}
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      setDefaultValue: this.setDefaultValue,
    }
  }
  componentDidMount(): void {
    this.setDefaultValue('1');
    window['receiveMessageFromIndex'] = function ({ data: { col = 0, row = 0, value = [] } = {} } = {}) {
    };
    window.addEventListener("message", window['receiveMessageFromIndex'], false);
  }
  setDefaultValue = async (type) => {
    const org = getSession('org') || {name:''};
    const res = await getRole({});
    let initData: Array<any> = [
      [`${+new Date()}`, '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '1', '2', '3', '4', '5'],
      ['11', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '1', '2', '3', '4', '5'],
      ['12', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '1', '2', '3', '4', '5'],
      ['13', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '1', '2', '3', '4', '5'],
    ];
    initData = initData.map(item => {
      return item.map(it => {
        return parseInt(it)
      })
    });
    let ifarme: any = document.getElementById("child");
    if (!ifarme) {
      return;
    }
    window.onload = function () {
      ifarme.contentWindow.postMessage({ data: initData, org }, "*")
    }
    ifarme.contentWindow.postMessage({ data: initData, org }, "*")
  }
  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    const org = getSession('org') || {}
    let ifarme: any = document.getElementById("child");

    let state = {};
    const { _org, setDefaultValue, _ifarme = null } = prevState;
    if (!_isEqual(org, _org) && (!_isEqual(ifarme, _ifarme) || !_isEmpty(ifarme))) {
      state['_org'] = org;
      state['_ifarme'] = ifarme;
      setDefaultValue();
    }
    return state;
  };
  render() {
    const { data } = this.state;
    return (
      <div style={{ width: '100%', height: '100%' }} >
        <iframe id={'child'} src={'/html/1.html'} frameBorder={0} style={{ width: '100%', height: '100%' }} />
      </div>
    );
  }
}
