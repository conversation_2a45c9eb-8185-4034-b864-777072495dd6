/**
 * 字典modalTree组件
 */
import React from 'react';
import {Tree,Modal,Input} from "antd";
import {connect, onActionFunc} from "dva";
import {AntTreeNodeSelectedEvent} from "antd/lib/tree";
import {treeToList} from "@/utils/method.js";

const TreeNode=Tree.TreeNode;
const Search=Input.Search;
interface propsType{
  codeType:string,
  codeValue?:string,
  codeName?:string,
  dispatch?:any,
  onFocus?:()=>void,
  onOk?:(any)=>void, // 点击确认的回调，和onChange二选其一，参数是整个state
  onChange?:(any)=>void, // 点击确认的回调，和onChange二选其一，参数是整个state.selectedItem
  onSelect?:(selectedKeys: string[], e: AntTreeNodeSelectedEvent)=>void, // 节点改变回调
  disableSelect?:{key:string,value:Array<any>},
  commonDict?:any,
  title:string,
  searchKey?:Array<string>,
  parentDisable?:boolean,
  initValue?:any,
  dicName?:string
}
@connect(({commonDict})=>({commonDict}))
export default class ModalTree extends React.Component<propsType,any> {
  static open(){};
  static close(){};
  static clear(){};
  static defaultProps={
    codeValue:'key',//node 唯一key
    codeName:'name',//node 显示名称
    searchKey:['key','name','pinyin'],//默认查询条件
    dicName:'dicName',
    placeholder:'请选择',
    parentDisable: false,
    searchPlaceholder:'请输入关键词检索',
  };
  static getDerivedStateFromProps(nextProps,prevState){
    const {codeType,codeValue,codeName,initValue}=nextProps;
    let {data,value}=prevState;
    let list=nextProps.commonDict[`${codeType}List`] || nextProps.commonDict[codeType];
    let find=undefined;
    if(!value && initValue && list){
      if(!prevState['initValue']){
        find=list.find(obj=>obj[codeValue]===initValue);
        if(find){
          value=find[codeName];
          return {value,initValue:value,selectedKeys:[initValue]}
        }
      }
    }
    if(data.length===0){
      return {data:nextProps.commonDict[codeType] || []}
    }
    return null;
  }
  constructor(props){
    super(props);
    this.state={
      selectedKeys:[],
      data:[],
    };
    ModalTree.close=this.hidden;
    ModalTree.open=this.show;
    ModalTree.clear=this.clearAll;
  }
  componentWillUnmount(): void {
    this.setState({
      selectedKeys:[],
      data:[],
      value:undefined,
      initValue:undefined,
      selectedItem:undefined,
    })
  }

  componentDidMount(): void {
    const {codeType,commonDict}=this.props;
    if(!commonDict[codeType]){
      this.action();
    }
  }
  clearAll=()=>{
    this.setState({
      selectedKeys:[],
    })
  };
  action=()=>{
    const {codeType,dicName}=this.props;
    this.props.dispatch({
      type:'commonDict/getDictTree',
      payload:{
        data:{
          codeType,
          [dicName as string]:codeType
        }
      }
    });
  };
  handleOk=()=>{
    const {onOk,onChange,codeName}=this.props;
    const {selectedItem}=this.state;
    this.setState({
      value:selectedItem[codeName as string]
    });
    if(onOk){
      onOk(this.state);
    }else{
      onChange && onChange(this.state['selectedItem']);
      this.handleCancel();
    }
  };
  handleCancel=()=>{
    this.hidden();
  };
  show=()=>{
    this.setState({
      visible:true,
    })
  };
  hidden=()=>{
    this.setState({
      visible:false,
    })
  };
  onSelect=(selectedKeys, e)=>{
    const {onSelect,codeName} = this.props;
    onSelect && onSelect(selectedKeys, e);
    let selectedItem={};
    if(selectedKeys.length>0){
      selectedItem=e.node.props.dataRef;
    }
    this.setState({
      selectedKeys,
      selectedItem,
    });
  };
  onSearch=(val)=>{
    const {codeType,searchKey}=this.props;
    if(val){
      const data=this.props.commonDict[codeType] || [];
      if(data.length>0){
        let da=treeToList(data);
        let resData=da.filter(obj=>{
          let res:Array<boolean>=[];
          for(let key of searchKey as any){
            res.push(obj[key].includes(val));
          }
          return res.includes(true)
        });
        this.setState({data:resData});
      }
    }else{
      this.setState({data:this.props.commonDict[codeType] || []})
    }
  };
  inpChange=(e)=>{
    if(!e.target.value){
      const {onSelect} = this.props;
      onSelect && onSelect([], e);
      this.setState({
        value:undefined,
      });
    }
  };
  renderTreeNodes = data =>{
    const {codeName, codeValue, parentDisable} = this.props;
    return data.map((item) => {
      if (item.children) {
        return (
          <TreeNode title={item[codeName as string]} key={item[codeValue as string]} value={item[codeValue as string]} dataRef={{...item,children:[]}} disabled={parentDisable}>
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode title={item[codeName as string]} key={item[codeValue as string]} value={item[codeValue as string]} dataRef={{...item,children:[]}} />;
    });
  };
  render(): React.ReactNode {
    const {title,children}=this.props;
    const {selectedKeys=[],visible,data=[],value}=this.state;

    return(
      <React.Fragment>
        {children ? React.cloneElement(children as any,{
          onClick:this.show
        }) : <Input allowClear readOnly placeholder={'请选择'} value={value} onClick={this.show} onChange={this.inpChange}/>}
        <Modal
          destroyOnClose
          maskClosable={false}
          title={title || '请输入标题'}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
        >
          <div style={{minHeight:500,maxHeight:600,overflow:'auto'}}>
            <Search
              placeholder="请输入关键词检索"
              onSearch={this.onSearch}
              style={{ width: '98%' }}
            />
            <Tree
              defaultExpandAll
              onSelect={this.onSelect}
              selectedKeys={selectedKeys}
            >
              {this.renderTreeNodes(data)}
            </Tree>
          </div>
        </Modal>
      </React.Fragment>
    );
  }
}
