import React, { Fragment } from 'react';
import { connect } from 'dva';
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import { _history as router, setListHeight, isFlowingParty } from '@/utils/method';
import qs from 'qs';
import AddOrEdit from './subpage/addoredit';
import FlowAddOrEdit from './subpage/flowaddoredit';

import HistoryAddOrEdit from './subpage/historyAddoredit';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Dropdown, Input, Menu, Popconfirm, Tabs } from 'antd';
import NowOrg from 'src/components/NowOrg';
import ExportInfo from '@/components/Export';
import { getSession } from '@/utils/session';
import Merge from './components/merge';
import MoveOrg from './components/move';
import BreakUp from './components/break';
import UnDo from './components/undo';
import { findOrgResume, getOrgSortList, updateOrgSort } from '@/pages/org/services'
import { ModalTL } from '@/components/TimeLine';
import { DownOutlined, WarningTwoTone } from '@ant-design/icons';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import { TableActionMenu } from '@/pages/user/lock';
import SortTable from '@/components/SortTable';
import sortSstyles from '@/components/SortTable/index.less';
import moment from 'moment';


const Search = Input.Search;
const TabPane = Tabs.TabPane;
// @ts-ignore
@connect(({ org, commonDict, loading, common }) => ({ common, org, commonDict, loading: loading.effects['org/getList'] }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filterChecked: {},
      searchVal: '',
      activeTab: sessionStorage.getItem('orgListTabType') ? JSON.parse(sessionStorage.getItem('orgListTabType')) : "1",
    };
  }

  addOrEdit = (record?: object) => {
    AddOrEdit['WrappedComponent'].clear();
    if (record && record['code']) {
      this.props.dispatch({
        type: 'org/findOrg',
        payload: {
          code: record['code'],
        },
      }).then(res => {
        AddOrEdit['WrappedComponent'].show();
      });
    } else {
      AddOrEdit['WrappedComponent'].show();
    }
  };
  flowAddOrEdit = (record?: object) => {
    FlowAddOrEdit['WrappedComponent'].clear();
    FlowAddOrEdit['WrappedComponent'].show({ isEdit: true });
    // if (record && record['code']) {
    //   this.props.dispatch({
    //     type: 'org/findOrg',
    //     payload: {
    //       code: record['code'],
    //     },
    //   }).then(res => {
    //     FlowAddOrEdit['WrappedComponent'].show();
    //   });
    // } else {
    //   FlowAddOrEdit['WrappedComponent'].show();
    // }
  }
  historyAddOrEdit = (record?: object) => {
    HistoryAddOrEdit['WrappedComponent'].clear();
    if (record && record['code']) {
      this.props.dispatch({
        type: 'org/findHistoryOrg',
        payload: {
          code: record['code'],
        },
      }).then(res => {
        HistoryAddOrEdit['WrappedComponent'].show();
      });
    } else {
      HistoryAddOrEdit['WrappedComponent'].show();
    }
  };
  merge = (record?: object) => {
    this['mergeRef'].open(record);
  };
  breakUp = (record?: object) => {
    this['breakUpRef'].open(record);
  };
  unDo = (record?: object) => {
    this['unDoRef'].open(record);
  };

  componentDidMount() {
    setListHeight(this, 20);
  }

  onPageChange = (page, pageSize) => {
    console.log(page, pageSize, this.props.org);
    const { activeTab } = this.state
    if (activeTab == "1") {
      let { query } = this.props.location;
      router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`);
    }
  };
  onPageHisChange = (page, pageSize) => {
    console.log(page, pageSize);
    let { query } = this.props.location;
    console.log("🚀 ~ index ~ query:", query)
    // this.action({
    //   pageNum: page,
    //   pageSize,
    // })
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`);
  };
  filterChange = async (val) => {
    console.log("🚀 ~ index ~ filterChange= ~ val:", val)
    const { pagination = {} } = this.props.org;
    const { activeTab = '1' } = this.state
    await this.props.dispatch({
      type: 'org/updateState',
      payload: {
        filter: val,
        pagination: {
          ...pagination,
          current: 1,
        },
      },
    });
    let { query } = this.props.location;
    console.log(query, 'queryquery')
    if (activeTab == "1") {
      router.push(`?${qs.stringify({ ...query, pageNum: 1, })}`);
    } else if (activeTab == "2") {
      router.push(`?${qs.stringify({ ...query, pageNum: 1, })}`);
    }

    // this.action();
  };
  action = (val?: object) => {
    const { activeTab } = this.state
    const { pagination = {} } = this.props.org;
    const { current, pageSize } = pagination;
    const org = getSession('org') || {};
    if (activeTab == "1") {
      this.props.dispatch({
        type: 'org/getList',
        payload: {
          data: {
            orgCode: org['orgCode'],
            pageNum: current,
            pageSize,
            ...val,
          },
        },
      });
    } else if (activeTab == "2") {
      console.log("🚀 ~ index ~ val:", { ...val })
      this.props.dispatch({
        type: 'org/getHistoryList',
        payload: {
          data: {
            orgCode: org['orgCode'],
            pageNum: current,
            pageSize,
            ...val,
          },
        },
      });
    }
  };
  confirm = (record) => {
    console.log(record, 'confirm');
  };

  componentWillUnmount(): void {
    // let { query } = this.props.location;
    // router.push(`?${qs.stringify({ pageSize: query?.pageSize || 10, pageNum: 1 })}`);
    this.props.dispatch({
      type: 'org/destroy',
    });
  }

  search = async (value) => {
    console.log("🚀 ~ index ~ search= ~ value:", value)
    const { activeTab } = this.state;
    const { pagination = {} } = this.props.org;
    const { query } = this.props.location;

    await this.props.dispatch({
      type: 'org/updateState',
      payload: {
        orgName: value,
      },
    });

    if (activeTab == "1") {
      router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`);
    } else if (activeTab == "2") {
      router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`);
    }
  };
  searchClear = (e) => {
    this.setState({
      searchVal: e.target.value
    })
    if (!e.target.value) {
      this.props.dispatch({
        type: 'org/updateState',
        payload: {
          orgName: undefined,
        },
      });
      this.action();
    }
  };
  // 导出
  exportInfo = async () => {
    this['orgExportInfo'].open();
  };
  orgChange = () => {
    this.action();
    setTimeout(() => {
      let org = getSession('org') || {};
      let user = getSession('user') || {};

      let orgList = org['orgCode'];
      let arr: any = [];
      for (let a = orgList.length; a >= user.orgCode.length; a -= 3) {
        arr.push(orgList.substring(0, a));
      }
      console.log(arr,'arrarrarrarrarrarrarrarrarrarr')
      this.props.dispatch({
        type: 'common/updateInitTree',
        payload: {
          data: {
            // orgCodeList:[root['orgCode'],org['orgCode']]
            orgCodeList: arr,
          },
        },
      });
    }, 500);
  };
  openSorts = async () => {
    let org = getSession('org') || {};
    const { code = 500, data = [] } = await getOrgSortList({ data: { orgCode: org['orgCode'] } });
    if (code == 0) {
      SortTable.open()
      this.setState({
        sortData: data.map((it, index) => {
          return { ...it, _sort: index + 1 }
        }),
      })
    }
  };
  sort = async (arr) => {
    // console.log("🚀 ~ file: index.tsx ~ line 177 ~ index ~ sort= ~ arr", arr)
    const para = arr.map((i, key) => {
      return {
        sort: key + 1,
        code: i.code,
      }
    });
    SortTable.loading(true);
    const { code = 500 } = await updateOrgSort({ data: para });
    SortTable.loading(false);
    if (code == 0) {
      Tip.success('操作提示', '操作成功')
    }
    this.openSorts();
  };

  lockOrUnlock = (record) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: 1 })}`)
  };

  tabChangeClick = async (e) => {
    await this.props.dispatch({
      type: 'org/updateState',
      payload: {
        filter: {},
        orgName: '',
        pagination: {
          current: 1,
        },
      },
    });
    this.setState({
      activeTab: e,
      searchVal: '',
    }, () => {
      const { pagination = {} } = this.props.org;
      if (e == '1') {
        sessionStorage.setItem('orgListTabType', JSON.stringify(e))
        router.push(`?${qs.stringify({ pageNum: 1, pageSize: pagination['pageSize'] })}`);
      } else {
        sessionStorage.setItem('orgListTabType', JSON.stringify(e))
        router.push(`?${qs.stringify({ pageNum: 1, pageSize: pagination['pageSize'] })}`);
      }
    });
  }

  render() {
    const { list, pagination = {}, orgName, filter, } = this.props.org;
    const { current = 1, pageSize = 10 } = pagination;
    const { loading, compType } = this.props;
    const { dataInfo, filterHeight, filterChecked, searchVal, activeTab = "1" } = this.state;
    const org = getSession('org') || {};
    console.log("🚀 ~ index ~ render ~ activeTab:", getSession('org'))
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 58,
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1;
        },
      },
      {
        title: '组织名称',
        dataIndex: 'name',
        width: 270,
        render: (text, record) => {
          if (compType == 'lock') {
            return text
          }
          if (activeTab == 1) {
            return <div>{record['changeTheTermOfOffice'] ? <WarningTwoTone style={{ fontSize: '20px' }} twoToneColor={'#faad14'} title='该党组织即将任期届满，请及时维护。' /> : ''} <a onClick={() => this.addOrEdit(record)}>{text}</a></div>
          } else {
            return <div><a onClick={() => this.historyAddOrEdit(record)}>{text}</a></div>
          }
        },
      },
      {
        title: '组织类别',
        width: 200,
        dataIndex: 'd01Name',
      },
      // {
      //   title: '隶属关系',
      //   width: 160,
      //   dataIndex: 'd03Name',
      // },
      {
        title: '联系人',
        width: 100,
        dataIndex: 'contacter',
      },
      {
        title: '创建日期',
        width: 100,
        dataIndex: 'createDate',
        render: (text, record) => {
          return (
            <div>{text ? moment(text).format('YYYY-MM-DD') : ''}</div>
          )
        }
      },
      // {
      //   title: '联系方式',
      //   width: 110,
      //   dataIndex: 'contactPhone',
      //   render: (text, record) => {
      //     return (
      //       <div>{text ? '********' : ''}</div>
      //     )
      //   }
      // },
      {
        title: '党组织书记',
        width: 100,
        dataIndex: 'secretary',
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 120,
        render: (text, record) => {
          if (compType == 'lock') {
            return (
              <TableActionMenu record={record} lockOrUnlock={this.lockOrUnlock} type={'3'} />
            )
          }
          return (
            <span>
              {activeTab == 1 && <Fragment>
                <a onClick={() => this.addOrEdit(record)}>编辑</a>
                <Divider type="vertical" />
                {/*<Popconfirm title="确定要删除吗？" onConfirm={()=>this.confirm(record)}>*/}
                {/* <a className={'del'}>删除</a>*/}
                {/*</Popconfirm>*/}
                <TableActionMenu record={record} lockOrUnlock={this.lockOrUnlock} type={'3'} />
                <Divider type="vertical" />
                <Dropdown
                  overlay={
                    <Menu>
                      {/* <Menu.Item>
                      <a onClick={() => this.merge(record)}>合并</a>
                    </Menu.Item>
                    <Menu.Item>
                      <a onClick={() => this.breakUp(record)}>拆分</a>
                    </Menu.Item> */}
                      <Menu.Item>
                        <Popconfirm title="确定要撤销吗？" onConfirm={() => this.unDo(record)}>
                          <a>撤销</a>
                        </Popconfirm>
                      </Menu.Item>
                      <Menu.Item>
                        <a onClick={() => {
                          this['ModalTL'].open(record);
                        }}>组织时间轴</a>
                      </Menu.Item>
                    </Menu>
                  }
                >
                  <a>更多 <DownOutlined /></a>
                </Dropdown>
              </Fragment>}
              {activeTab == 2 && <Fragment>
                <a onClick={() => this.historyAddOrEdit(record)}>查看</a>
              </Fragment>}
            </span>
          );
        },
      },
    ];
    if (activeTab == 2) {
      columns.splice(6, 0,
        {
          title: '撤销时间',
          width: 100,
          dataIndex: 'date',
          render: (text, record) => {
            return (
              <div>{text ? moment(text).format('YYYY-MM-DD') : ''}</div>
            )
          }
        },
      )
    }
    const filterData = [
      {
        key: 'd01CodeList', name: '组织类别', value: this.props.commonDict[`dict_d01_tree`],
      },
      // {
      //   key: 'd03CodeList', name: '隶属关系', value: this.props.commonDict[`dict_d03_tree`],
      // },
      {
        key: 'd02CodeList', name: '单位情况', value: this.props.commonDict[`dict_d02_tree`],
      },
      {
        key: 'd04CodeList', name: '单位类别', value: this.props.commonDict[`dict_d04_tree`],
      },
      {
        key: 'd194CodeList', name: '国民经济行业', value: this.props.commonDict[`dict_d194_tree`],
      },
      {
        key: 'd195CodeList', name: '生产性服务行业', value: this.props.commonDict[`dict_d195_tree`],
      },
    ];
    return (
      <div style={{ height: '100%', overflow: 'hidden' }}>
        {
          !compType &&
          <Tabs activeKey={activeTab} defaultActiveKey="1"
            onChange={(e) => this.tabChangeClick(e)}
          >
            <TabPane tab="基本信息" key="1" />
            <TabPane tab="历史组织" key="2" />
          </Tabs>
        }
        <AddOrEdit dataInfo={dataInfo} />
        <FlowAddOrEdit dataInfo={dataInfo} />
        <HistoryAddOrEdit dataInfo={dataInfo} />
        <Merge ref={e => this['mergeRef'] = e} callBack={this.orgChange} />
        <BreakUp ref={e => this['breakUpRef'] = e} callBack={this.orgChange} />
        <UnDo ref={e => this['unDoRef'] = e} callBack={this.orgChange} />
        {activeTab == 1 && <Fragment>
          <NowOrg extra={
            <React.Fragment>
              {
                !compType &&
                <React.Fragment>
                  {
                    org.isFlowStatus == 0 && <React.Fragment>
                      <Button htmlType={'button'} onClick={this.openSorts}>排序</Button>
                      <Button htmlType={'button'} onClick={this.exportInfo} style={{ marginLeft: 16 }} disabled={_isEmpty(list)}>导出</Button>
                    </React.Fragment>
                  }
                  <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={() => this.flowAddOrEdit()}
                    style={{ marginLeft: 16 }}>新增流动党组织</Button>
                  {
                    !org['d01Code'].startsWith('8') &&
                    <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={() => this.addOrEdit()}
                      style={{ marginLeft: 16 }}>新增党组织</Button>
                  }
                  {/* <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={() => this.addOrEdit()}
                    style={{ marginLeft: 16 }}>新增党组织</Button> */}
                  {
                    org.isFlowStatus == 0 && (
                      <Button style={{ marginLeft: 16 }} onClick={() => {
                        this['moveOrg'].open();
                      }}>组织架构调整</Button>
                    )
                  }

                </React.Fragment>
              }
              <Search value={searchVal} style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear}
                placeholder={'请输入检索关键词'} />
            </React.Fragment>
          } />
          {
            !compType &&
            <RuiFilter data={filterData}
              onChange={this.filterChange}
              openCloseChange={() => {
                setListHeight(this, 20);
              }} />
          }
          <WhiteSpace />
          <WhiteSpace />
          <ListTable
            rowClassName={(record) => {
              if (record['code'] && record['code'] === org['code']) {
                return 'toptable';
              }
              return '';
            }}
            scroll={{ y: filterHeight }}
            columns={columns}
            data={list}
            pagination={pagination}
            onPageChange={this.onPageChange}
          />
        </Fragment>}
        {activeTab == 2 && <Fragment>
          <NowOrg extra={
            <React.Fragment>
              <Search value={searchVal} style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear}
                placeholder={'请输入检索关键词'} />
            </React.Fragment>
          } />
          {
            !compType &&
            <RuiFilter data={filterData}
              onChange={this.filterChange}
              openCloseChange={() => {
                setListHeight(this, 20);
              }} />
          }
          <WhiteSpace />
          <WhiteSpace />
          <ListTable
            rowClassName={(record) => {
              if (record['code'] && record['code'] === org['code']) {
                return 'toptable';
              }
              return '';
            }}
            scroll={{ y: filterHeight }}
            
            columns={columns}
            data={list}
            pagination={pagination}
            onPageChange={this.onPageHisChange}
          />
        </Fragment>}
        <MoveOrg ref={e => this['moveOrg'] = e} onOK={(e) => {
          let { query } = this.props.location;
          this.onPageChange(query.page, query.pageSize);
          const user = getSession('user') || {}
          const { targetOrgId = [], srcOrgId = [] } = e;
          this.props.dispatch({
            type: 'common/getTree',
            payload: {
              data: {
                orgCodeList: [srcOrgId[0].orgCode.substring(0, srcOrgId[0].orgCode.length - 3), user['orgCode'], targetOrgId[0].orgCode],
                // orgCodeList:[user['orgCode'],],
                excludeOrgCodeList: []
              }
            }
          })
        }} />
        <ModalTL
          ref={e => this['ModalTL'] = e}
          timeLineAction={findOrgResume}
        />
        <ExportInfo wrappedComponentRef={e => this['orgExportInfo'] = e}
          tableName={'ccp_org_all'}
          tableListQuery={{ ...filter, orgName, searchType: 1, orgCode: org['orgCode'] }}
          action={'/api/data/org/exportData'}
          expinfo={[{
            label: '党组织基本信息',
            value: '1'
          }, {
            label: '本年应换届党组织',
            value: '2'
          }, {
            label: '本年应换届但未换届党组织',
            value: '3'
          }, {
            label: '本年已换届党组织',
            value: '4'
          }]}
          expType='type'
          extraFormData={[
            {
              key: '1',
              component: 'switch',
              value: { label: '是否导出所有下级', field: 'isExportAll', initialValue: true, required: false },
            },
          ]}
        />
        <SortTable
          title={'排序'}
          onClose={() => {
            this.orgChange()
          }}
          sortData={this.state.sortData || []}
          onSortEnd={this.sort}
          render={(value, index) => {
            return (
              <div className={sortSstyles.sortItem}>
                <span>
                  <span>{value.name}</span><br />
                  <span>{value._sort}</span>
                </span>
              </div>
            )
          }} />
      </div>
    );
  }
}
