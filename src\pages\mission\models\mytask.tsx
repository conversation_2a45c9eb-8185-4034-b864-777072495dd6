import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {add, del, findOrg, getList, updated,itteeList,addUnitCommittee,updateUnitCommittee,delUnitCommittee,unitFind} from '@/pages/[unit]/services';
import {getSession} from "@/utils/session";
import { changeListPayQuery } from '@/utils/method.js';

const myTask = modelExtend(listPageModel,{
  namespace: "myTask",
  state:{
    basicInfo:{},
    basicInfoData:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
      //   if(pathname==='/unit'){
      //     let org=getSession('org') || {};
      //     let defaultParas={
      //       pageNum:1,
      //       pageSize:10,
      //       isCreateOrg:1,
      //     };
      //     const dictData=['dict_d04','dict_d05','dict_d35'];
      //   for(let obj of dictData){
      //     dispatch({
      //       type:'commonDict/getDictTree',
      //       payload:{
      //         data:{
      //           dicName:obj
      //         }
      //       }
      //     });
      //   }
      //   dispatch({
      //     type:'getList',
      //     payload:{
      //       data:{
      //         mainUnitOrgCode:org['orgCode'],
      //         manageUnitOrgCode:org['orgCode'],
      //         ...defaultParas,
      //         ...query,
      //       }
      //     }
      //   })
      // }s
      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put,select }) {
      const {filter,unitName}=yield select(state=>state['unit']);
      const {data={}} = yield call(getList, {data:{...payload['data'],...filter,unitName}});
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      });
    }
  }
});
export default myTask;
