.content {
display: flex;
justify-content: center;
.rows {
    padding: 6px;
    display: flex;
    align-items: center;
    >div:nth-child(1) {
        width: 36px;
        height: 6px;
    }
    >div:nth-child(2) {
        padding-left: 10px;
        color: #F49609;
    }
}
}
:global {
    .ant-popover-inner-content {
        background: #FDF4E6;
        box-sizing: border-box;
        border: 1px solid #F49609;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.15);
    }
}