import React from 'react';
import style from './index.less';
import './index.less';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
// import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Card, Input, Row, Spin, Form, Modal, Select, message } from 'antd';
import { connect } from 'dva';
import dh from '@/assets/dh.png';
import lantern from '@/assets/lantern.png';
import header from '@/assets/header.png';
import { SKFKEY } from '@/utils/fiseckey';
import Tip from '@/components/Tip';
import { getMpkEYUserName, getMpkEYUserRandomNumbers, getMpkEYUserVerifySignedData } from '../../services/index';

// import gmCrypt from 'gm-crypt';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 17 },
  },
};
const Option = Select.Option;
import { _history } from '@/utils/method';
// @ts-ignore
@connect(({ login, loading }) => ({
  login,
  loading,
}))
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      time: new Date().valueOf(),
      timer: undefined, //定时器获取密评的key
      key: undefined, //选择的序列号  如果只有一个 就固定的
      mpKeyList: [], // key里面的序列号 因为可以是多个
      tipCount: 0, //用来判断是否提示u盘插入过 0表示没插入过 1表示插入过
      resCount: 1, //发送请求获取序列码帮的用户名的次数
    };
  }
  // 新增密评
  //新增定时任务获取密评的key  通过u盘插入获取
  getIntervalKey = () => {
    const { setFieldsValue } = this['formRef'];
    try {
      let mpKey = SKFKEY.SKF_EnumDev();
      let mpKeyList: any = this.regexMatch(mpKey);
      const timer = setInterval(() => {
        let { tipCount, resCount } = this.state;
        mpKey = SKFKEY.SKF_EnumDev();
        mpKeyList = this.regexMatch(mpKey); // 正则匹配 序列号
        if (mpKeyList.length <= 0) {
          resCount = 1;
        } else {
          this.setState(
            {
              key: mpKeyList[0][0],
            },
            () => {
              if (resCount == 1) {
                this.getUserKeyName();
                resCount = 0;
              }
            },
          );
          tipCount = 1;
        }
        this.setState(
          {
            tipCount,
            mpKeyList,
            resCount,
          },
          () => {
            // 这里是因为当插入过u盘 才会进行这样的提示  没插入过就是tipCount=0的时候 就不会提示
            if (this.state.mpKeyList.length <= 0 && tipCount == 1) {
              setFieldsValue({ account: undefined });
              Tip.warning('提示', '密评u盘已拔出');
              this.setState({ tipCount: 0 });
            }
            console.log(this.state);
          },
        );
      }, 5000);
      // 进入登录页就运行一次
      this.setState(
        {
          timer: timer,
          mpKeyList,
          key: mpKeyList[0] ? mpKeyList[0][0] : undefined,
        },
        () => {
          if (this.state.resCount == 1) {
            this.getUserKeyName();
            this.setState({ resCount: 0 });
          }
        },
      );
    } catch (error) {
      console.log('连接密评失败');
    }
  };

  // 通过对应的key去获取用户名 并且用户名不能修改
  getUserKeyName = async () => {
    try {
      const { setFieldsValue } = this['formRef'];
      const { key } = this.state;
      if (!key) {
        return;
      }
      const { data, code } = await getMpkEYUserName({
        ukey: key,
      });
      if (code == 0 && data) {
        setFieldsValue({ account: data.account });
      } else {
        Tip.warning('提示', '该ukey并未绑定用户');
      }
    } catch (error) {
      console.log('获取密评用户名失败');
    }
  };

  //正则匹配函数
  regexMatch = (str) => {
    const regex = /[A-Z]\d{10}[A-Z]\d{4}/g;
    const data = [...str.matchAll(regex)];
    console.log('🚀 ~ index ~ data:', data);
    return data;
  };

  confidentialEvaluation = () => {
    this.getIntervalKey();
  };

  componentDidMount(): void {
    this.confidentialEvaluation();
    sessionStorage.clear();
    this.getImg();
    const { query } = _history.location;
    if (query['appCode'] && query['token']) {
      this.props.dispatch({
        type: 'login/login',
        payload: {
          data: {
            appcode: query['appCode'],
            ...query,
          },
        },
      });
    }
    this.props.dispatch({
      type: 'common/clear',
      payload: {},
    });
    this.props.dispatch({
      type: 'login/clear',
      payload: {},
    });
    if (process.env.idCheck == 'false') {
      Modal.confirm({
        title: '提示',
        icon: <div />,
        content:
          '该入口仅用于身份证无法通过校验的人员录入，从该入口保存的人员将会在系统中进行特殊标记，2022年需要对这些人员的信息进行核实，正常状态人员请勿通过此入口进行保存。请务必谨慎操作！',
        okText: '确定',
        // cancelText: '取消',
      });
    }
  }
  getImg = () => {
    fetch(`/api/login/code`).then((res) => {
      if (res.headers.get('captcha')) {
        this.setState({
          uuid: res.headers.get('captcha'),
        });
      }
      res.blob().then((blob) => {
        let elementById = document.getElementById('img');
        if (elementById) {
          elementById.src = URL.createObjectURL(blob);
        }
      });
    });
  };

  // sm4Encode(context) {
  //   const SM4 = require("gm-crypt").sm4;
  //   const sm4Config = {
  //     key: 'GJwsXX_BzW=gJWJW',
  //     mode: "ecb", // 加密的方式有两种，ecb和cbc两种，也是看后端如何定义的，不过要是cbc的话下面还要加一个iv的参数，ecb不用
  //     cipherType: "Base64" //
  //   };
  //   const sm4 = new SM4(sm4Config);
  //   const encryptData = sm4.encrypt(context);
  //   return encryptData;
  // }

  // 密评登录流程
  mpLogin = async () => {
    const { key } = this.state;
    const pass = '11111111';
    try {
      const handle = SKFKEY.SKF_ConnectDev(key, 'F-OLD'); //获取设备句柄
      const EnumApplication = SKFKEY.SKF_EnumApplication(handle).split('||'); //获取应用名 枚举后去第一个
      const OpenApplication = SKFKEY.SKF_OpenApplication(handle, EnumApplication[0]); //链接应用 应用句柄
      // const VerifyPIN = SKFKEY.SKF_VerifyPIN(OpenApplication, 1, pass)  //0：管理员，1：操作员
      const EnumContainer = SKFKEY.SKF_EnumContainer(OpenApplication).split('||'); // 接口去枚举应用中存储证书的容器
      const OpenContainer = SKFKEY.SKF_OpenContainer(OpenApplication, EnumContainer[0]); //容器句柄
      const randomNumber = await getMpkEYUserRandomNumbers({ len: 32 }); //后端返回 获取32为的随机数
      const pid = '31323334353637383132333435363738'; //签名者id写死
      const pubkey = SKFKEY.SKF_ExportPublicKey(OpenContainer, 1); //导出秘钥
      const hash = SKFKEY.SKF_DigestInit(handle, 1, pubkey, pid); //计算hash
      const result = SKFKEY.SKF_Digest(hash, randomNumber.data); //数据预处理
      const ECCSignData = SKFKEY.SKF_ECCSignData(OpenContainer, result); //对预处理的数据进行签名处理得到签名值
      let ExportCertificate: string = SKFKEY.SKF_ExportCertificate(OpenContainer, 1); //将签名证书导出
      const beginPattern = /-----BEGIN CERTIFICATE-----\r?\n?/;
      const endPattern = /\r?\n?-----END CERTIFICATE-----\r?\n?/;

      // 替换开始和结束标记
      let cleanedCert = ExportCertificate.replace(beginPattern, '');
      ExportCertificate = cleanedCert.replace(endPattern, '');
      console.log(ExportCertificate, randomNumber, ECCSignData);
      const verify = await getMpkEYUserVerifySignedData({
        data: {
          certSignStr: ExportCertificate,
          inDataStr: randomNumber.data,
          signBytesStr: ECCSignData,
        },
      }); //发送请求到后端验证
      return verify;
    } catch (error) {
      // Tip.warning("提示", "网络请求失败,请重试")
      return { code: 9999, data: 9999 };
    }
  };

  handleOk = async (values) => {
    // let a = this.sm4Encode('123456')
    const { mpKeyList, key } = this.state;
    if (mpKeyList.length > 0) {
      // 插入密评u盘 key存在时走这
      const { data, code } = await this.mpLogin();
      if (code != 0 || data != 0) {
        Tip.warning('提示', 'ukey认证失败');
        return;
      }
    }
    // 之前正常登录逻辑
    const { uuid } = this.state;
    const CryptoJS = require('crypto-js');
    let encryptedData = CryptoJS.AES.encrypt(values['password'], CryptoJS.enc.Utf8.parse('AESNBHB3ZA==HKXt'), { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
    values['password'] = encryptedData.toString();
    this.props
      .dispatch({
        type: 'login/login',
        payload: {
          data: {
            ...values,
            uuid,
          },
        },
      })
      .then(({ code }: any) => {
        if (code !== 0) {
          this.getImg();
        }
      });

    clearInterval(this.state.timer);
    this.setState({
      timer: undefined,
    });
  };
  render(): React.ReactNode {
    const { loading: { effects = {} } = {}, login } = this.props;
    // const {getFieldDecorator} = this.props.form;
    const { time, mpKeyList } = this.state;
    const mpKeyListLength = mpKeyList.length;
    const packjson = require('../../../package.json');
    console.log(packjson, 'oooooooooooooppppppppppppp');
    return (
      <div className={style.page}>
        <div className={style.header}>
          <div className={style.icon}></div>
          <div className={style.top} />
        </div>
        <div className={style.center}>
          <div className={style.login}>
            {/* <p className={style.headers}><div className={style.lin}/>欢迎登陆</p> */}
            <p className={style.headers}>
              {' '}
              欢 迎 登 录 <span style={{ fontSize: 18 }}>V{packjson.version}</span>
            </p>
            <Form {...formItemLayout} ref={(e) => (this['formRef'] = e)} onFinish={this.handleOk}>
              {mpKeyListLength > 1 && (
                <Form.Item
                  initialValue={mpKeyList[0][0]}
                  label={
                    <div>
                      <img src={require('@/assets/user.png')} style={{ width: 30 }} />序 列 号
                    </div>
                  }
                  name={'mpkey'}
                  rules={[{ required: true, message: '请 选 择 序 列 号' }]}
                >
                  <Select
                    onChange={(e) =>
                      this.setState({ key: e }, () => {
                        this.getUserKeyName();
                      })
                    }
                    placeholder={'请 选 择 序 列 号'}
                  >
                    {mpKeyList.map((item) => {
                      return (
                        <Option key={item[0]} value={item[0]}>
                          {item[0]}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              )}
              <Form.Item
                label={
                  <div>
                    <img src={require('@/assets/user.png')} style={{ width: 30 }} />用 户 名
                  </div>
                }
                name={'account'}
                rules={[{ required: true, message: '请 输 入 用 户 名 称' }]}
              >
                <Input disabled={mpKeyListLength > 0} placeholder={'请 输 入 用 户 名 称'} allowClear />
              </Form.Item>
              <Form.Item
                label={
                  <div>
                    <img src={require('@/assets/pass.png')} style={{ width: 30, marginRight: 8 }} />密<div style={{ width: '1.6em', display: 'inline-block' }} />码
                  </div>
                }
                name={'password'}
                rules={[{ required: true, message: '请 输 入 用 户 密 码' }]}
              >
                <Input placeholder={'请 输 入 用 户 密 码'} type="password" allowClear />
              </Form.Item>
              <Form.Item
                label={
                  <div>
                    <img src={require('@/assets/pass.png')} style={{ width: 30 }} />验 证 码
                  </div>
                }
                name={'captchaCode'}
                rules={[{ required: true, message: '请 输 入 验 证 码' }]}
              >
                <Input placeholder={'请 输 入 验 证 码'} allowClear onChange={(e) => this['formRef'].setFieldsValue({ captchaCode: e.target.value })} />
                <div
                  className={style.numberCode}
                  onClick={() => {
                    this.getImg();
                    this.setState({ time: new Date().valueOf() });
                  }}
                >
                  <img id={'img'} className={style.yzm} />
                </div>
              </Form.Item>
              <Row>
                <Button
                  shape="round"
                  htmlType={'submit'}
                  type={'primary'}
                  danger
                  size={'large'}
                  style={{ width: '100%', fontWeight: 'bold', fontSize: 18 }}
                  loading={effects['login/login']}
                >
                  登 录
                </Button>
              </Row>
            </Form>
          </div>
        </div>
      </div>
    );
  }
}
export default index;
