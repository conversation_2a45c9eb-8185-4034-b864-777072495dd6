// 停止党籍
import React, { useState, Fragment, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import { Button, Modal, Form } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import { findDictCodeName, unixMoment } from '@/utils/method.js';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import Date from '@/components/Date';
import { handleStop } from '@/pages/mem/services/memAbroad';
const Index = forwardRef((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [recordData, setRecordData] = useState({});

  const [form] = Form.useForm();

  const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 12 },
  };

  useImperativeHandle(ref, () => ({
    open: (record: any) => {
      setVisible(true);
      setRecordData(record);
    },
  }));
  const handleCancel = () => {
    setVisible(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const onFinish = async (value: any) => {
    value = unixMoment(['stopPartyDate'], value);
    value = findDictCodeName(['d033'], value);
    // return
    setConfirmLoading(true);
    const res = await handleStop({
      data: {
        code: recordData.code,
        abroadOrgCode: recordData.abroadOrgCode, //abroadOrgCode	人员所属组织层级码
        ...value,
      },
    });
    setConfirmLoading(false);
    if (res.code === 0) {
      setVisible(false);
      // 如果是选了停止党籍，关闭基本信息编辑页面，且刷新外面列表
      if(props?.callBack){
       props.callBack()
      }
    }
  };
  return (
    <Modal title="停止党籍" destroyOnClose visible={visible} onOk={handleOk} onCancel={handleCancel} width={'800px'} confirmLoading={confirmLoading}>
      <Form form={form} {...formItemLayout} onFinish={onFinish}>
        <Form.Item
          name="stopPartyDate"
          label="停止党籍时间"
          rules={[{ required: true, message: '请输入停止党籍时间' }]}
          // initialValue={}
        >
          <Date />
        </Form.Item>
        {/* 停止党籍原因[使用出国（境）目的定居（23）下属子类] */}
        <Form.Item
          name="d033Code"
          label="停止党籍原因"
          rules={[{ required: true, message: '停止党籍原因' }]}
          // initialValue={}
        >
          <DictTreeSelect
            parentDisable={true}
            codeType={'dict_d33'}
            //   initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d037Code']}
            backType={'object'}
            filter={(data) => {
              return data.filter((item) => item.key.startsWith('2'));
            }}
            noDraw={['21','22','29']}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
});
export default Index;
