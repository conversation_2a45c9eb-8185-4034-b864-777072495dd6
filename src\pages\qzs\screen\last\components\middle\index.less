@width: 3456px;
@height: 1152px;

@cardHeight: 873.6px;
@leftCardWith: 980px;
@midCardWith: 1120px;
@rightCardWith: 980px;

.box {
  background: url('../../../../../../assets/qzs/midbg.png') no-repeat;
  background-size: 100% 100%;
  width: @width;
  height: @height;
  overflow: hidden;
  position: relative;
  padding: 117px 128px 0 128px;
  transform: scale(0.555, 0.55);
  transform-origin: 0 0;
  .head {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    .btn {
      font-size: 26px;
      height: 50px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      border-radius: 6px;
      background: #be0c10;
      color: #ffe276;
    }
  }
  .body {
    display: flex;
    justify-content: space-between;
    .left {
      .box1 {
        background: url('../../../../../../assets/qzs/midleft.png') no-repeat;
        background-size: 100% 100%;
        width: @leftCardWith;
        height: @cardHeight;
        padding: 50px;
        .box1top {
          margin-top: 80px;
          display: flex;
          .box1topl {
            width: 40%;
            display: flex;
            justify-content: center;
            // padding-left: 52px;
            position: relative;
            align-items: center;
            .photo {
              width: 212px;
              height: 322px;
            }
            .jiangzhang {
              position: absolute;
              bottom: -3px;
              right: 56px;
              width: 55px;
              height: 46px;
            }
          }
          .box1topr {
            width: 60%;
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 22px;
            color: #323232;
            position: relative;

            .text {
              position: relative;
              top: 40px;
              font-size: 40px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              text-align: justify;
              -webkit-line-clamp: 4;
              /*! autoprefixer: off */
              -webkit-box-orient: vertical;
              /* autoprefixer: on */
            }

            > img {
              position: absolute;
              bottom: 25px;
              left: 0;
              width: 100%;
            }
          }
        }
        .name {
          width: 40%;
          margin-top: 20px;
          font-family: Source Han Serif SC;
          font-weight: 800;
          font-size: 75px;
          color: #be0c10;
          text-align: center;
          // position: relative;
          // left: 98px;
          // width: 302px;
        }
        .box1bot {
          // left: -32px;
          // position: relative;
          width: 624px;
          .desc {
            margin-top: 10px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 36px;
            color: #323232;
            position: relative;
            left: 40px;

            display: flex;

            > div:first-child {
              width: 217px;
              display: inline-block;
              text-align-last: justify;
              text-align: justify;
            }
            > div:last-child {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              /*! autoprefixer: off */
              -webkit-box-orient: vertical;
              /* autoprefixer: on */
            }
          }
        }
      }
    }
    .mid {
      position: relative;
      flex: 1;
      padding: 0 54px;
      .midicon {
        position: absolute;
        right: 86px;
        top: 30px;
      }
      .scroll {
        width: 1400px;
        height: 300px;
        position: absolute;
        left: -250px;
        top: 500px;
        .stk {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
        }
        .scorllItem {
          width: 380px;
          height: 300px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          position: relative;
          .scorllItemph {
            width: 366px;
            height: 209px;
            position: absolute;
            left: 7px;
          }
          .scorllItembg {
            width: 100%;
            height: 100%;
          }
        }
        // > div > div {
        //   width: 1000px !important;
        // }
      }
    }
    .right {
      .rightbg {
        z-index: 3;
        width: @rightCardWith;
        height: @cardHeight;
        position: relative;
        // background: #fffbf0;
        // border-radius: 18px;
        // border: 2px solid #be0c10;

        display: flex;
        align-items: center;
        flex-direction: column;
        padding: 20px 0 0 0;

        .more50 {
          position: absolute;
          left: calc(50% - 64px);
          top: 214px;
          z-index: 2;
          transform: scale(1.5);
        }

        .text {
          height: 492px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 37px;
          color: #62471e;
          line-height: 41px;
          text-align: justify;
          z-index: 3;

          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 12;
          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
          /* autoprefixer: on */
          > p {
            margin: 0;
          }
        }
        .sign {
          position: absolute;
          bottom: 145px;
          right: 22px;
          z-index: 3;
          > img {
            width: 283px;
            height: 112px;
          }
        }
        .logos {
          z-index: 3;
          position: absolute;
          bottom: 67px;
          right: 40px;
          text-align: right;
          font-size: 27px;
          color: #62471e;
          font-family: Microsoft YaHei;
        }
      }
    }
  }

  .bot {
    width: @width;
    height: 344px;
    background: url('../../../../../../assets/qzs/midbot.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    bottom: 0;
    left: 0;
  }
}

.birthday {
  z-index: 4;
  width: 200px;
  position: absolute;
  right: -254px;
  bottom: 89px;
  font-size: 20px;
  cursor: pointer;
}
