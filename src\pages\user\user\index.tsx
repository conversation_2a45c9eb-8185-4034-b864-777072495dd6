import React from 'react';
import ListTable from '@/components/ListTable';
import ListFilter from '@/components/ListFilter';
import NowOrg from '@/components/NowOrg';
import { FormOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Input,
  Divider,
  Popconfirm,
  Modal,
  Switch,
  Drawer,
  Popover,
  Menu,
  Dropdown,
  Tabs,
} from 'antd';
import AddEdit from './addEdit';
import ChangeOassword from './changePassword';
import BulkChanges from './bulkChanges';
import UserRole from './userRole';
import styles from "./index.less";
import { connect } from 'dva';
import moment from 'moment';
import { isEmpty, setListHeight } from '@/utils/method';
import {getSession} from "@/utils/session";
const TabPane = Tabs.TabPane;
const Search=Input.Search;
@connect(({user,login})=>({
  user,
  login
}))
export default class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      keyword:'',
      orgCode:'',
      type:'add',
      visible:false,
      menuTreeData:[]
    }
  }
  //JSON.parse(sessionStorage.getItem('org') || "")['code']||
  componentDidMount = () => {
  let org=getSession('org')|| {};
  this.setState({
    orgCode:org['orgCode']
  },()=>{
    this.onPage();
  });
   setListHeight(this);
  };
  componentWillReceiveProps =(nextProps: Readonly<any>, nextContext: any) => {
      if (!isEmpty(this.state.orgCode)&&this.state.orgCode!==JSON.parse(sessionStorage.org)['orgCode']) {
        this.setState({orgCode:JSON.parse(sessionStorage.org)['orgCode']},()=>{
          if (isEmpty(this.state.keyowrd)){
            this.onPage();
          } else {
            this.getSearch(1,10)
          }
        })
      }
  };
  onPage = ( pageNum=1,size=10) => {
    this.props.dispatch({
      type:'user/list',
      payload:{
        data:{
          pageNumber:pageNum,
          pageSize:size,
          code:this.state.orgCode,
        }
      }
    })
  };
  onChange = (page) => {
    this.onPage(page);
  };
  confirm = (record) => {
    this.props.dispatch({
      type:'user/delete',
      payload:{
        data:{
          id:record.id
        }
      }
    }).then(res=>{
      if (res.code===0){
        if (isEmpty(this.state.keyowrd)) {
          this.onPage();
        }else {
          this.getSearch(1,10)
        }
      }
    })
  };
  cancel = () => {
  };
  isSearch = (value) => {
    this.setState({keyword:value},()=>{
      if (isEmpty(value)){
        this.onPage();
      } else {
        this.getSearch(1,10)
      }
    })
  };
  isChange=(e)=>{
    if (isEmpty(e.target.value)) {
      this.setState({keyword:e.target.value},()=>{
        this.getSearch(1,10)
      })
    }
  };
  getSearch=  ( pageNum=1,size=10)=>{
    let val =  {
      pageNum:pageNum,
      pageSize:size,
      orgCode:this.state.orgCode,
      keyword:this.state['keyword']
    };
     for (let o in val) {
      if (isEmpty(val[o])){
        delete val[o]
      }
    }
    this.props.dispatch({
      type:'user/getSearchUserByKeyword',
      payload:{
        ...val
      }
    })
  };
  onLock=(checked,record)=>{
    if (checked){
      this.props.dispatch({
        type:'user/isUnLocks',
        payload:{
          data:{
            id:record.id
          }
        }
      }).then(res=>{
        if (res.code===0){
          if (isEmpty(this.state.keyowrd)) {
            this.onPage(this.state['page'],this.state['pageNum']);
          }else {
            this.getSearch(this.state['page'],10)
          }
        }
      })
    }else {
      this.props.dispatch({
        type:'user/isLocks',
        payload:{
          data:{
            id:record.id
          }
        }
      }).then(res=>{
        if (res.code===0){
          if (isEmpty(this.state.keyowrd)) {
            this.onPage(this.state['page'],this.state['pageNum']);
          }else {
            this.getSearch(this.state['page'],10)
          }
        }
      })
    }
  };
  changePage=(v,k)=>{
    this.onPage(v,k);
    this.setState({page:v,pageNum:k})
  };
  onClose=()=>{
    this.setState({visible:false})
  };
  openDrawer=(r)=>{
    this.setState({
      visible:true,
      manageInfo:r['manages'],
    })
  };
  roleUp=(item)=>{
    this.setState({upItem:item});
    this.props.dispatch({
      type:'user/permissionList',
      payload:{
      }
    }).then(res=>{
      if (res.code===0){
        const { data }=res;
        this.setState({menuTreeData:data})
      }
    });
    UserRole['WrappedComponent'].open();
  };
  UserRoleOk=async (val)=>{
    if (isEmpty(this.state.keyowrd)){
      this.onPage();
    } else {
      this.getSearch(1,10)
    }
  };

  render(): React.ReactNode {
    const { user:{ list=[],pagination:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={},isUp=null}={},login:{userRole=[]}, loading:{effects = {}} = {}} =this.props;
    const { type,id,menuTreeData,manageInfo ,filterHeight}=this.state;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return ((page-1)*pageSize)+index+1
        }
      },
      // {
      //   title:'用户姓名',
      //   dataIndex:'name',
      //   width:100,
      // },
      {
        title:'登录名',
        dataIndex:'account',
        width:100,
      },
      // {
      //   title:'创建用户单位',
      //   dataIndex:'orgName',
      //   render:text=>{
      //     return ( <span>{text || ''}</span>)
      //   }
      // },
      {
        title:'管理单位',
        dataIndex:'manages',
        width:250,
        render:(text,r, index)=>{
          return (
            <span key={index}>
              {
                text.map((item,i)=>{
                  if(item.isDefault===1){
                    return  <a key={index} onClick={()=>this.openDrawer(r)}>{item.managerOrgName}</a>
                  }
                })
              }
            </span>
          )
        }
      },
      {
        title:'关联党员',
        dataIndex:'memName',
        width:100,
      },
      // {
      //   title:'登录次数',
      //   dataIndex:'loginCount',
      //   width:100,
      // },
      // {
      //   title:'登录ip',
      //   dataIndex:'' +
      //     '',
      //   width:150,
      // },
      {
        title:'状态',
        dataIndex:'isLock',
        width:100,
        render:(text,record)=>{
          return (
            <span>
            {
              record.manages[0]['roleType']!==1&&
              <Switch key={new Date().valueOf()} checkedChildren="锁定" unCheckedChildren="解锁" defaultChecked={record.isLock===0} onChange={(e)=>this.onLock(e,record)} />
            }
          </span>
          )
        }
      },
      {
        title:'其他权限情况',
        dataIndex:'a',
        width:100,
        align:'center',
        render:(text,record) => {
          const { manages = [] } = record || {};
          const {permissionCode = ''} = manages?.[0] || {}
          // 92 补录 93 错误录入 94 接收预备党员 95 线下组织关系转接
          let arr:any = [];
          let code95 = permissionCode.substr(94,1)
          let code94 = permissionCode.substr(93,1)
          let code93 = permissionCode.substr(92,1)
          let code92 = permissionCode.substr(91,1)
          if(code92 == '1') arr.push('补录党员权限');
          if(code93 == '1') arr.push('错误录入权限');
          if(code95 == '1') arr.push('线下转接权限');
          if(code94 == '1') arr.push('接收预备党员权限');
          return (
            <div style={{textAlign:'center'}}>
              {
                (arr?.length || 0) > 0 && arr.map((it,index)=>(
                  <div key={index} style={{marginBottom:4}}>{it}</div>
                ))
              }
            </div>
          )
        }
      },
      {
        title:'最近登录时间',
        dataIndex:'lastLoginTime',
        width:150,
        render:(text)=>{
          return <span>{isEmpty(text)?'':moment(text).format('YYYY-MM-DD')}</span>
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        width:140,
        render:(text,record,index)=>{
          return(
            <React.Fragment>
              {
                record.manages[0]['roleType']!==1&&
                <React.Fragment>
                  <AddEdit
                    key={index}
                    orgCode={this.state.orgCode}
                    title={ `编辑用户`}
                    keyword={this.state['keyword']}
                    data={record}>
                    <a href="javascript:">编辑</a>
                  </AddEdit>
                </React.Fragment>
              }
              {
                record.manages[0]['roleType']!==1&&
                <Divider type="vertical"/>
              }
              <ChangeOassword account={record}>
                <a href="javascript:">重置密码</a>
              </ChangeOassword>
              <Divider type='vertical'/>
              <a href='javascript:' onClick={()=>this.roleUp(record)}>权限</a>
              {/*{*/}
              {/*  record.manages[0]['roleType']!==1&&*/}
              {/*  <Divider type='vertical'/>*/}
              {/*}*/}
              {/*{*/}
              {/*  record.manages[0]['roleType']!==1&&*/}
              {/*  <Popconfirm title="确定删除?" onConfirm={()=>this.confirm(record)} onCancel={this.cancel}>*/}
              {/*    <a href="javascript:" className={'del'}>删除</a>*/}
              {/*  </Popconfirm>*/}
              {/*}*/}
              {
                record.manages[0]['roleType']!==1 &&
                <React.Fragment>
                  <Divider type='vertical'/>
                  <Popconfirm title="确定删除?" onConfirm={()=>this.confirm(record)} onCancel={this.cancel}>
                    <a href="javascript:" className={'del'}>删除</a>
                  </Popconfirm>
                </React.Fragment>

              }

            </React.Fragment>
          )
        }
      },
    ];
    const column=[
      {
        title:'序号',
        dataIndex:'num',
        render:(text,record,index)=>{
          return ((page-1)*pageSize)+index+1
        }
      },
      {
        title:'管理单位',
        dataIndex:'managerOrgName',
      },
    ];

    return (
      <div id={'userList'} style={{height:'100%'}}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        <NowOrg  extra={
          <ListFilter>
            <Search
              placeholder="请输入检索关键词"
              onSearch={value => this.isSearch(value)}
              onChange={(value)=>this.isChange(value)}
              style={{ width: 200 }}
              className={styles.filter}
            />
            <AddEdit
              title={ '新增用户'}
              orgCode={this.state.orgCode}
              data={id}
              keyword={this.state['keyword']}
            >
              {/* <Button type="primary" icon={<PlusOutlined />} style={{marginRight:8}}>新增用户</Button> */}
              </AddEdit>
            <BulkChanges keyword={this.state['keyword']} orgCode={this.state.orgCode}><Button type="primary" icon={<FormOutlined />} >批量修改</Button></BulkChanges>
          </ListFilter>
        }/>
        <UserRole menuData={menuTreeData} userRole={userRole} editObj={this.state['upItem']} onOK={this.UserRoleOk} onExpand={()=>{console.log(12333)}}/>
        <ListTable
          columns={columns}
          data={list}
          scroll={{y:filterHeight}}
          pagination={{pageSize,total:totalRow,page,current:pageNumber}}
          onPageChange={this.changePage}/>
        <Drawer
          title="管理单位"
          placement="right"
          closable={false}
          onClose={this.onClose}
          width={500}
          visible={this.state.visible}
        >
          <div>
            {/*{*/}
              {/*this.state.manageInfo&&this.state.manageInfo.map((item,index)=>{*/}
                {/*return  <p key={index}>{item.managerOrgName}</p>*/}
              {/*})*/}
            {/*}*/}
            <ListTable columns={column} data={this.state.manageInfo}/>
          </div>
        </Drawer>
      </div>
    );
  }
}
