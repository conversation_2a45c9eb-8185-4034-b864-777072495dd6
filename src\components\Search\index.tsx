import React from 'react';
import {Input} from "antd";


const Search = Input.Search;
interface pType {
  onChange?:Function,
  style?:object,
  placeholder?:string
}
export default class index extends React.Component<pType,any>{
  search=(value)=>{
    const {onChange}=this.props;
    onChange && onChange(value);
  };
  searchClear=(e)=>{
    if(!e.target.value){
      const {onChange}=this.props;
      onChange && onChange(undefined);
    }
  };
  render(){
    const {style,placeholder}=this.props;
    return(
      <Search style={style ? style : {width:200,marginLeft:16}} onSearch={this.search} onChange={this.searchClear} placeholder={placeholder ? placeholder : '请输入检索关键词'}/>
    )
  }
}
