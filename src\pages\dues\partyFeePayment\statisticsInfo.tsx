/**
 * 党费交纳-统计信息
 * */
import React, { useState, useRef, useEffect } from 'react';
import {
  Input,
  Select,
  Form,
  Modal,
  Tabs,
  Button,
  Divider,
  Popconfirm,
  Space,
  Tooltip,
} from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import * as echarts from 'echarts';
import ListTable from 'src/components/ListTable';
import NowOrg from '@/components/NowOrg';
import styles from './index.less';
const TabPane = Tabs.TabPane;
const index = (props: any) => {
  const [btnType, setBtnType]: any = useState(Array(12).fill('default'));
  const [yearOptions, setYearOptions] = useState([
    { value: '2018', label: '2018' },
    { value: '2019', label: '2019' },
    { value: '2020', label: '2020' },
    { value: '2021', label: '2021' },
    { value: '2022', label: '2022' },
    { value: '2023', label: '2023' },
  ]);
  const [year1, setYear1] = useState('2023');
  const [year2, setYear2] = useState('2023');
  const [year3, setYear3] = useState('2023');
  const getList = async (p?: any) => {
    // const {
    //   code: resCode = 500,
    //   data: { list = [], pageNumber = 1, pageSize = 10, totalPage = 0 } = {},
    // } = await orgLifeList({
    //   data:{
    //     orgCode,
    //   pageNum: 1,
    //   pageSize: 10,
    //   activityName,
    //   startTime,
    //   endTime,
    //   ...d158Code,
    //   ...p,
    //   }
    // });
    // if (resCode === 0) {
    //   setListData(list);
    //   setPagination({ current: pageNumber, total: totalPage, pageSize });
    // }
  };
  const memEchart = () => {
    type EChartsOption = echarts.EChartsOption;

    let chartDom = document.getElementById('mem')!;
    let myChart = echarts.init(chartDom);
    let option: EChartsOption;
    option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {},
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          axisTick: {
            alignWithLabel: true,
          },
          nameGap: 50,
          data: [
            '1月',
            '2月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月',
          ],
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: false,
          },
          axisTick: false,
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
          },
        },
      ],
      series: [
        {
          name: '未交人数',
          type: 'bar',
          color: '#19b16b',
          barWidth: '50%',
          stack: 'Ad',
          data: [0, 1, 0, 2, 5, 2, 3, 0, 0, 1, 4, 3],
        },
        {
          name: '应交人数',
          type: 'bar',
          color: '#49a3e3',
          barWidth: '50%',
          stack: 'Ad',
          data: [52, 50, 48, 50, 47, 52, 51, 50, 52, 51, 49, 50],
        },
      ],
    };
    option && myChart.setOption(option);
  };
  const moneyEchart = () => {
    type EChartsOption = echarts.EChartsOption;

    let chartDom = document.getElementById('money')!;
    let myChart = echarts.init(chartDom);
    let option: EChartsOption;
    option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {},
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          axisTick: {
            alignWithLabel: true,
          },
          nameGap: 50,
          data: [
            '1月',
            '2月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月',
          ],
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: false,
          },
          axisTick: false,
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
          },
        },
      ],
      series: [
        {
          name: '未交金额',
          type: 'bar',
          color: '#19b16b',
          barWidth: '50%',
          stack: 'Ad',
          data: [0, 100, 0, 200, 500, 200, 300, 0, 0, 100, 400, 300],
        },
        {
          name: '应交金额',
          type: 'bar',
          color: '#49a3e3',
          barWidth: '50%',
          stack: 'Ad',
          data: [5200, 5000, 4800, 5000, 4700, 5200, 5100, 5000, 5200, 5100, 4900, 5000],
        },
      ],
    };
    option && myChart.setOption(option);
  };
  useEffect(() => {
    setTimeout(() => {
      memEchart();
      moneyEchart();
    }, 50);
  }, []);

  return (
    <div>
      <NowOrg />
      <div style={{ display: 'flex', justifyContent: 'space-between', margin: '20px 0' }}>
        <div>
          {Array(12)
            .fill('1')
            .map((item, index) => {
              return (
                <Tooltip
                  title={
                    year1 === `${moment().year()}` && index >= moment().month()
                      ? '当前数据尚未完成结算统计'
                      : undefined
                  }
                >
                  <Button
                    disabled={year1 === `${moment().year()}` && index >= moment().month()}
                    type={btnType[index]}
                    onClick={(e) => {
                      console.log('点击按钮~~~~');

                      let newArr = btnType.map((str, order) => {
                        if (index === order) {
                          return 'primary';
                        } else {
                          return 'default';
                        }
                      });
                      setBtnType(newArr);
                    }}
                    style={{ marginRight: '20px', padding: '0 20px' }}
                  >
                    {index + 1}月
                  </Button>
                </Tooltip>
              );
            })}
        </div>
        <Select
          options={yearOptions}
          value={year1}
          onChange={(e) => {
            setYear1(e);
          }}
          style={{ width: '90px', height: '32px' }}
        ></Select>
      </div>
      <div className={styles.cardBox}>
        <div className={styles.cardRow}>
          <div className={styles.cardItem}>
            <div className={styles.icon}>
              <Tooltip
                overlayStyle={{ width: '100px' }}
                placement="bottomRight"
                title={'指除免交党费外的所有党员人数'}
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div>
              <div>应交人数(人)</div>
              <div className={styles.num1}>52</div>
            </div>
          </div>
          <div className={styles.cardItem}>
            <div className={styles.icon}>
              <Tooltip
                overlayStyle={{ width: '100px' }}
                placement="bottomRight"
                title={'指已添加交费记录的党员人数'}
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div>
              <div>已交人数(人)</div>
              <div className={styles.num1}>49</div>
            </div>
          </div>
          <div className={styles.cardItem}>
            <div className={styles.icon}>
              <Tooltip
                overlayStyle={{ width: '100px' }}
                placement="bottomRight"
                title={'指未添加交费记录的党员人数，包含未设置党费标准的党员'}
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div>
              <div>未交人数(人)</div>
              <div className={styles.num2}>3</div>
            </div>
          </div>
          <div className={styles.cardItem}>
            <div className={styles.icon}>
              <Tooltip
                overlayStyle={{ width: '100px' }}
                placement="bottomRight"
                title={'指从当前月份往前算连续6个月未交纳党费的人数'}
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div>
              <div>连续6个月未交人数(人)</div>
              <div className={styles.num2}>2</div>
            </div>
          </div>
          <div className={styles.cardItem}>
            <div className={styles.icon}>
              <Tooltip
                overlayStyle={{ width: '100px' }}
                placement="bottomRight"
                title={'指设置免交党费的党员人数'}
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div>
              <div>免交人数(人)</div>
              <div className={styles.num2}>1</div>
            </div>
          </div>
        </div>
        <div className={styles.cardRow}>
          <div className={styles.cardItem}>
            <div className={styles.icon}>
              <Tooltip
                overlayStyle={{ width: '100px' }}
                placement="bottomRight"
                title={'指除免交党费外的所有党员的应交金额数'}
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div>
              <div>应交金额（元）</div>
              <div className={styles.num1}>1200.00</div>
            </div>
          </div>
          <div className={styles.cardItem}>
            <div className={styles.icon}>
              <Tooltip
                overlayStyle={{ width: '100px' }}
                placement="bottomRight"
                title={'指已交纳党费的金额数'}
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div>
              <div>已交金额(元)</div>
              <div className={styles.num1}>1150.00</div>
            </div>
          </div>
          <div className={styles.cardItem}>
            <div className={styles.icon}>
              <Tooltip
                overlayStyle={{ width: '100px' }}
                placement="bottomRight"
                title={'指还未交纳党费的金额数'}
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div>
              <div>未交金额(元)</div>
              <div className={styles.num2}>50</div>
            </div>
          </div>
        </div>
        <div className={styles.remark}>
          *备注：每月最后一天汇总统计数据，下月1日刷新当月数据面板
        </div>
      </div>
      <div>
        <div style={{ margin: '20px 0' }}>
          <span style={{ fontSize: '18px', marginRight: '20px' }}>党费交纳人数概览</span>
          <Select
            options={yearOptions}
            onChange={(e) => {
              setYear2(e);
            }}
            value={year2}
            style={{ width: '90px' }}
            defaultValue="2023"
          ></Select>
        </div>
        <div id="mem" style={{ width: '100%', height: '300px' }}></div>
      </div>
      <div>
        <div style={{ margin: '20px 0' }}>
          <span style={{ fontSize: '18px', marginRight: '20px' }}>党费交纳金额概览</span>
          <Select
            options={yearOptions}
            value={year3}
            onChange={(e) => {
              setYear3(e);
            }}
            style={{ width: '90px' }}
            defaultValue="2023"
          ></Select>
        </div>
        <div id="money" style={{ width: '100%', height: '300px' }}></div>
      </div>
    </div>
  );
};

export default index;
