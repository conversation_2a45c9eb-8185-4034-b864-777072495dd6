/**
 * 模块名
 */
import React from 'react';
import { connect } from "dva";
import { DownCircleFilled, UpCircleFilled } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Alert, Button, Col, Input, Modal, Row, Timeline } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import OrgSelect from '@/components/OrgSelect';
import styles from './index.less';
import Tip from '@/components/Tip';
import moment from 'moment';
import { getSession } from "@/utils/session";
import { root } from "@/common/config";
import { bool } from "prop-types";
import { isEmpty } from 'lodash';
import EditDevlop from '@/pages/developMem/zy/components/Edit'
import ElectronicArchives from '@/pages/developMem/zy/components/electronicArchives'
import EditMem from './AddorEdit'

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

@connect((({ transferOut }) => ({ transferOut })), undefined, undefined, { forwardRef: true })
class TransferBet extends React.Component<any, any> {
  static open() { }
  static close() { }
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      visibleM2: false,
      key: new Date().valueOf(),
    };
    TransferBet.open = this.open;
  }
  handleOk = () => {

  };
  handleCancel = () => {
    this.props.refresh();
    this.setState({
      visible: false,
      visibleM2: false,
      listRow: {},
    })
  };
  open = (listRow = {type:''}) => {
    this.setState({
      visible: true,
      listRow: listRow
    })
  };
  memChange = (data) => {
    this.setState({
      memData: data,
    })
  };
  del = (item) => {
    let { memData } = this.state;
    memData = memData.filter(obj => obj['id'] !== item['id']);
    this.setState({
      memData
    });
    let names: Array<string> = [];
    for (let obj of memData) {
      names.push(obj['name'])
    }
    this['mem'].setState({
      value: names.join('，'),
    });
  };
  duesChange = (e, item) => {
    const { value } = e.nativeEvent.target;
    let { memData } = this.state;
    let findIndex = memData.findIndex(obj => obj['id'] === item['id']);
    item['dues'] = value;
    memData[findIndex] = item;
    this.setState({
      memData,
    })
  };
  dateChange = (data, dateString, item) => {
    let { memData } = this.state;
    let findIndex = memData.findIndex(obj => obj['id'] === item['id']);
    item['duesTime'] = data;
    memData[findIndex] = item;
    this.setState({
      memData,
    })
  };
  obtain = () => {
    const { type, transferId } = this.props;
    if (type) {
      if (type === 'out') {
        this.props.dispatch({
          type: 'transferOut/outDetail',
          payload: {
            transferId: transferId,
          }
        })
      } else {
        this.props.dispatch({
          type: 'transferOut/inDetail',
          payload: {
            transferId: transferId,
          }
        })
      }
    }
  };
  back = async () => {//退回
    const { transferId } = this.props;
    const { reason } = this.state;
    this.setState({
      backLoading: true,
    })
    const obj = await this.props.dispatch({
      type: 'transferOut/back',
      payload: {
        data: {
          id: transferId,
          reason: reason || '党员信息有误，请核实'
        }
      }
    });
    this.setState({
      backLoading: false,
    })
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', '退回成功');
      this.obtain();
      this.handleM2Cancel();
    }
  };
  openEdit = (item, isLast?: boolean, leftData?: any) => {//打开编辑
    console.log(isLast,'isLast')
    const { transferId } = this.props;
    this.setState({
      transferObj: item,
    },()=>{
      this.props.dispatch({
        type: 'transferOut/findMem',
        payload: {
          transferId: transferId
        }
      })
    })
    this['editMem'] && this['editMem'].open();
  }
  apply = async (item, isLast?: boolean, leftData?: any) => {//通过 isLast 是否目的组织最后的节点  leftData 源组织数据
    const { transferId } = this.props;
 

    const { transDetail = {} } = this.props.transferOut;
    this.setState({
      applyLoading: true,
    })
    const obj = await this.props.dispatch({
      type: 'transferOut/apply',
      payload: {
        data: {
          id: transferId,
          applyOrgId: item['orgId'],
        }
      }
    });
    this.setState({
      applyLoading: false,
    })
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', '通过成功');
      this.obtain();
      //如果是最后一个节点 同时时整建制 刷新树
      if (!transDetail['memName'] && isLast) {
        this.props.dispatch({
          type: 'common/getTree',
          payload: {
            data: {
              // orgCodeList:[item['orgCode'],leftData[0]['orgCode']],
              orgCodeList: [item['orgCode']],
              excludeOrgCodeList: []
            }
          }
        });
      }
    }
  };
  handleM2Cancel = () => {
    this.setState({
      visibleM2: false,
    })
  };
  handleM3Cancel = () => {
    this.setState({
      visibleM3: false,
    })
  };
  changeTargetOrg = async () => {//变更目的组织
    const { targetOrg } = this.state;
    const { transferId } = this.props;
    if (targetOrg) {
      const obj = await this.props.dispatch({
        type: 'transferOut/changeTargetOrg',
        payload: {
          data: {
            id: transferId,
            targetOrgId: targetOrg[0]['code'],
            targetOrgName: targetOrg[0]['name'],
            reason: '测试',
          }
        }
      });
      if (obj && obj['code'] === 0) {
        Tip.success('操作提示', '变更目的组织成功');
        this.obtain();
        this.handleM3Cancel();
      }
    }
  };
  digitalArchives = async (record) => {
    // console.log(this.props.memCode,'this.props.memCodethis.props.memCode')
    // this['editDevlop'].destroy();
    // await this.props.dispatch({
    //   type: 'memBasic/findMem',
    //   payload: {
    //     code: this.props.memCode
    //   }
    // })
    // this['editDevlop'].open({ code: this.props.memCode });
    this['ElectronicArchives'].showModal(this.props.memCode, this.props.transferId)

  }
  render() {
    const { visible, visibleM2, backLoading, visibleM3, reason, exclude, org,transferObj={} } = this.state;
    const { isHistory = false, memCode = '', type } = this.props;//是否是历史列表链接过来的
    const { transDetail = {} } = this.props.transferOut;
    const srcStepList = transDetail['srcStepList'] || [], targetStepList = transDetail['targetStepList'] || [], commonNodeStep = transDetail['commonNodeStep'] || [];
    const pass = [1, 4];//代表已通过
    const roles = getSession('roles') || {};
    const systemBlock = sessionStorage.getItem('systemBlock') || "";
    let isflag = false
    if (systemBlock == 'zy') {
      if (transDetail['transferTypeCode'] == '212' || transDetail['transferTypeCode'] == '124' || transDetail['transferTypeCode'] == '125') {
        isflag = false
      } else {
        isflag = true
      }
    }
    return (
      <div>
        <Modal
          title="退回原因"
          destroyOnClose
          maskClosable={false}
          visible={visibleM2}
          onOk={this.back}
          onCancel={this.handleM2Cancel}
          confirmLoading={backLoading}
          style={{ zIndex: 99 }}
        >
          <Input defaultValue={reason || '党员信息有误，请核实'} onChange={(e) => { this.setState({ reason: e.target.value }) }} />
        </Modal>
        <Modal
          title="变更目的组织"
          destroyOnClose
          maskClosable={false}
          visible={visibleM3}
          onOk={this.changeTargetOrg}
          onCancel={this.handleM3Cancel}
        >
          <OrgSelect orgTypeList={transDetail['memName'] ? ['3', '4'] : ['1', '2', '5']} org={org} exclude={exclude ? [exclude['orgCode']] : []} isPermissionCheck={'0'} onChange={(val) => { this.setState({ targetOrg: val }) }} />
        </Modal>
        <Modal
          destroyOnClose
          title="转接详情"
          visible={visible}
          onCancel={this.handleCancel}
          width={1254}
          footer={false}
          bodyStyle={{ height: 600, overflow: 'auto', }}
        >
          <div>
            <Alert message="组织关系转接需逐层审批，上级可代下级审批。" type="info" showIcon />
            <WhiteSpace />
            <div style={{ fontWeight: 'bolder', color: 'rgba(0,0,0,.9)' }}>
              基本信息
            </div>
            <WhiteSpace />
            <Form {...formItemLayout} style={{ background: '#F7F7F7' }} className={styles.detailForm}>
              {
                transDetail['memName'] && <React.Fragment>
                  <Row>
                    <Col span={12}>
                      <FormItem
                        label="党员姓名"
                      >
                        <span>{transDetail['memName']}</span>
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label="联系方式"
                      >
                        <span>{transDetail['phone']}</span>
                      </FormItem>
                    </Col>
                    {/* <Col span={12}>
                      <FormItem
                        label="党员性质"
                      >
                        <span>{transDetail['memType']}</span>
                      </FormItem>
                    </Col> */}
                  </Row>
                  {/* <Row>
                    <Col span={12}>
                      <FormItem
                        label="身份证号"
                      >
                        <span>{transDetail['idCard']}</span>
                      </FormItem>
                    </Col>
                  </Row> */}
                  {/* <Row>
                    <Col span={12}>
                      <FormItem
                        label="入党时间"
                      >
                        <span>{transDetail['inTime'] ? moment(transDetail['inTime']).format('YYYY-MM-DD') : null}</span>
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label="转正时间"
                      >
                        <span>{transDetail['turnTime'] ? moment(transDetail['turnTime']).format('YYYY-MM-DD') : null}</span>
                      </FormItem>
                    </Col>
                  </Row> */}
                  <Row>
                    <Col span={12}>
                      <FormItem
                        label="党费标准"
                      >
                        <span>{transDetail['feeStandard']}(元/月)</span>
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label="党费最后交纳月份"
                      >
                        <span>{transDetail['feeEndTime'] ? moment(transDetail['feeEndTime']).format('YYYY-MM') : null}</span>
                      </FormItem>
                    </Col>
                  </Row>
                </React.Fragment>
              }
              <Row>
                <Col span={12}>
                  <FormItem
                    label="转出党组织"
                  >
                    <span>{transDetail['outOrgName']}</span>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="转入党组织"
                  >
                    <span>{transDetail['inOrgName']}</span>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="转接类型"
                  >
                    <span>{transDetail['transferTypeName']}</span>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="转接原因"
                  >
                    <span>{transDetail['transferReason'] || transDetail['d146Name']}</span>
                  </FormItem>
                </Col>
                {
                  transDetail && transDetail['letterNumber'] &&
                  <Col span={12}>
                    <FormItem
                      label="介绍信唯一编码"
                    >
                      <span>{transDetail['letterNumber']}</span>
                    </FormItem>
                  </Col>
                }
              </Row>
            </Form>
            <WhiteSpace />

            <div style={{ fontWeight: 'bolder', color: 'rgba(0,0,0,.9)' }}>
              档案情况
            </div>
            <WhiteSpace />
            <Form {...formItemLayout} style={{ background: '#F7F7F7' }} className={styles.detailForm}>
              <Row>
                <Col span={12}>
                  <FormItem
                    label="档案处理方式"
                  >
                    <span>{transDetail['d92Name']}</span>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="备注"
                  >
                    <span>{transDetail['remark']}</span>
                  </FormItem>
                </Col>

              </Row>
              {
                isflag && <div style={{ position: 'relative', top: '-56px', left: '93%', display: 'inline-block' }} onClick={() => this.digitalArchives({ memCode: this.props.memCode })}><span style={{ color: '#1890FF', cursor: 'pointer' }}>查看档案</span></div>
              }
            </Form>
            <WhiteSpace />

            <div style={{ fontWeight: 'bolder', color: 'rgba(0,0,0,.9)' }}>
              转接进度
            </div>

            {
              transDetail['transferTypeCode'] !== "123" ? <React.Fragment>
                <div className={styles.transfer}>
                  <div className={styles.header}>
                    <span>{transDetail['commonNodeName']}</span>
                    {
                      !isHistory && commonNodeStep['status'] === 0 && `${commonNodeStep['orgCode']}`.startsWith(roles['managerOrgCode']) && <div>
                        {/*<Button type="danger" size={'small'} style={{marginRight:8}} onClick={()=>this.setState({visibleM3:true,exclude:commonNodeStep,org:root})}>变更目的组织</Button>*/}
                        <Button type="danger" size={'small'} style={{ marginRight: 8 }} onClick={() => this.setState({ visibleM2: true })} loading={this.state.backLoading}>退回</Button>
                        <Button type="primary" size={'small'} onClick={() => { this.apply({ orgId: transDetail['commonNodeId'] }) }} loading={this.state.applyLoading}>通过</Button>
                      </div>
                    }
                  </div>
                </div>
                <div>
                  <div>
                    {/*左侧审批节点*/}
                    <Timeline mode="right" style={{ float: 'left', width: 300 }}>
                      {
                        srcStepList && [...srcStepList].reverse().map((obj, index) => {
                          let text = '';
                          let showBtn = false;
                          if (obj['orgCode'] && obj['orgCode'].toString().startsWith(roles['managerOrgCode'])) {
                            showBtn = true;
                          }
                          switch (obj['status']) {
                            case -1:
                              text = '待审核';
                              break;
                            case 0:
                              text = '待审核';
                              break;
                            case 1:
                              text = '已通过';
                              break;
                            case 2:
                              text = '已退回';
                              break;
                          }
                          const style = pass.includes(obj['status']) ? { color: 'green' } : obj['status'] == '2' ? { color: 'red' } : { color: 'orange' };
                          const iconStyle = pass.includes(obj['status']) ? { color: 'green' } : { color: 'orange' };
                          let reasonObj = obj['logList'] && obj['logList'].length > 0 && [...obj['logList']].pop();
                          let statusTime;
                          if (pass.includes(obj['status']) && !isEmpty(obj.logList)) {
                            let list = obj.logList.sort((a, b) => b.createTime - a.createTime);
                            statusTime = list?.[0]?.createTime;
                          }
                          return (
                            <Timeline.Item className={styles.tLine} key={obj['orgId']} dot={<UpCircleFilled style={iconStyle} />}>
                              {/*是否通过文字描述*/}
                              <div style={style}>
                                {
                                  reasonObj && obj['status'] != '0' ? reasonObj['reason'] : `（${text}）`
                                }
                              </div>
                              {obj['orgName']}
                              <div>
                                联系方式：{obj['contacter']} {obj['contactPhone']}
                              </div>
                              {
                                statusTime &&
                                <div>
                                  审批时间：{moment(statusTime).format('YYYY年MM月DD日hh时mm分')}
                                </div>
                              }
                              {
                                !isHistory && obj['status'] == '0' && showBtn && <div>
                                  {/* {
                                  transDetail['transferTypeCode'] !== "223" &&  <Button type="danger" size={'small'} style={{marginRight:8}} onClick={()=>this.setState({visibleM3:true,exclude:obj,org:root})}>变更目的组织</Button>
                                  } */}
                                  <Button type="danger" size={'small'} style={{ marginRight: 8 }} onClick={() => this.setState({ visibleM2: true })} loading={this.state.backLoading}>退回</Button>
                                  <Button type="primary" size={'small'} onClick={() => { this.apply(obj) }} loading={this.state.applyLoading}>通过</Button>
                                </div>
                              }
                            </Timeline.Item>
                          );
                        })
                      }
                    </Timeline>
                  </div>
                  <div>
                    {/*右侧审批节点*/}
                    <Timeline style={{ float: 'right', width: 304 }}>
                      {
                        targetStepList && targetStepList.map((obj, index) => {
                          let text = '', showBtn = false, lastNode = false;
                          if (obj['orgCode'] && obj['orgCode'].toString().startsWith(roles['managerOrgCode'])) {
                            showBtn = true;
                          }
                          if (index == targetStepList.length - 1) {
                            lastNode = true
                          }
                          switch (obj['status']) {
                            case -1:
                              text = '待审核';
                              break;
                            case 0:
                              text = '待审核';
                              break;
                            case 1:
                              text = '已通过';
                              break;
                            case 2:
                              text = '已退回';
                              break;
                          }
                          const style = pass.includes(obj['status']) ? { color: 'green' } : obj['status'] == '2' ? { color: 'red' } : { color: 'orange' };
                          const iconStyle = pass.includes(obj['status']) ? { color: 'green' } : { color: 'orange' };
                          let reasonObj = obj['logList'] && obj['logList'].length > 0 && [...obj['logList']].pop();
                          let statusTime;
                          if (pass.includes(obj['status']) && !isEmpty(obj.logList)) {
                            let list = obj.logList.sort((a, b) => b.createTime - a.createTime);
                            statusTime = list?.[0]?.createTime;
                          }
                          return (
                            <Timeline.Item className={styles.tLine} key={obj['orgId']} dot={<DownCircleFilled style={iconStyle} />}>
                              <div style={style}>
                                {
                                  reasonObj && obj['status'] != '0' ? reasonObj.reason : `（${text}）`
                                }
                              </div>
                              {obj['orgName']}
                              <div>
                                联系方式：{obj['contacter']} {obj['contactPhone']}
                              </div>
                              {
                                statusTime &&
                                <div>
                                  审批时间：{moment(statusTime).format('YYYY年MM月DD日hh时mm分')}
                                </div>
                              }
                              {
                                !isHistory && obj['status'] == '0' && showBtn && <div>
                                  {
                                    transDetail['transferTypeCode'] == '124' && transDetail['isCheck'] == '1' &&
                                    <Button type="danger" size={'small'} style={{ marginRight: 8 }} onClick={() => this.setState({ visibleM3: true, exclude: undefined, org: { ...obj, code: obj['orgId'] } })}>选择转入党支部</Button>
                                  }
                                  <Button type="danger" size={'small'} style={{ marginRight: 8 }} onClick={() => this.setState({ visibleM2: true })} loading={this.state.backLoading}>退回</Button>
                                  {
                                    !(transDetail['transferTypeCode'] == '124' && transDetail['isCheck'] == '1') &&
                                    <Button type="primary" size={'small'} 
                                    onClick={() => { 
                                      if(lastNode) {
                                        console.log('listRow===',this.state.listRow);
                                        // return
                                        const {type = ''} = this.state.listRow;
                                        if( ['117', '212'].includes(type)){
                                          // 整建制的是整个单位，没得单独党员信息
                                          this.apply(obj, lastNode, srcStepList)
                                        }else{
                                          this.openEdit(obj, lastNode, srcStepList) 
                                        }
                                        
                                      } else {
                                        this.apply(obj, lastNode, srcStepList) 
                                      }
                                      // this.apply(obj, lastNode, srcStepList) 
                                      
                                    }} 
                                    loading={this.state.applyLoading}>通过</Button>
                                  }
                                </div>
                              }
                            </Timeline.Item>
                          );
                        })
                      }
                    </Timeline>
                  </div>
                </div>
              </React.Fragment> :
                <React.Fragment>
                  <WhiteSpace />
                  <Timeline style={{ float: 'left', width: 304 }}>
                    {
                      targetStepList && targetStepList.map((obj, index) => {
                        let text = '', showBtn = false;
                        if (obj['orgCode'] && obj['orgCode'].toString().startsWith(roles['managerOrgCode'])) {
                          showBtn = true;
                        }
                        switch (obj['status']) {
                          case -1:
                            text = '待审核';
                            break;
                          case 0:
                            text = '待审核';
                            break;
                          case 1:
                            text = '已通过';
                            break;
                          case 2:
                            text = '已退回';
                            break;
                        }
                        const style = pass.includes(obj['status']) ? { color: 'green' } : obj['status'] == '2' ? { color: 'red' } : { color: 'orange' };
                        const iconStyle = pass.includes(obj['status']) ? { color: 'green' } : { color: 'orange' };
                        let reasonObj = obj['logList'] && obj['logList'].length > 0 && [...obj['logList']].pop();
                        return (
                          <Timeline.Item className={styles.tLine} key={obj['orgId']} dot={<DownCircleFilled style={iconStyle} />}>
                            <div style={style}>
                              {
                                reasonObj && obj['status'] != '0' ? reasonObj.reason : `（${text}）`
                              }
                            </div>
                            {obj['orgName']}
                            {
                              !isHistory && obj['status'] == '0' && showBtn && <div>
                                {/*{*/}
                                {/*  index!==targetStepList.length-1 && <Button type="danger" size={'small'} style={{marginRight:8}} onClick={()=>this.setState({visibleM3:true,exclude:undefined,org:{...obj,code:obj['orgId']}})}>变更目的组织</Button>*/}
                                {/*}*/}
                                <Button type="danger" size={'small'} style={{ marginRight: 8 }} onClick={() => this.setState({ visibleM2: true })} loading={this.state.backLoading}>退回</Button>
                                <Button type="primary" size={'small'} onClick={() => { 
                                  this.apply(obj)
                                  // this.openEdit(obj)
                                   }} loading={this.state.applyLoading}>通过</Button>
                              </div>
                            }
                          </Timeline.Item>
                        );
                      })
                    }
                  </Timeline>
                </React.Fragment>
            }
          </div>
        </Modal>
        <ElectronicArchives ref={e => this['ElectronicArchives'] = e} />
        <EditDevlop wrappedComponentRef={e => this['editDevlop'] = e} istransfer={true} onsubmit={() => { }} {...this.props} tipMsg={this.state.tipMsg} />
        <EditMem wrappedComponentRef={e => this['editMem'] = e} {...this.props} goback={()=>this.apply(transferObj)}/>
      </div>
    );
  }
}
export default Form.create()(TransferBet)
