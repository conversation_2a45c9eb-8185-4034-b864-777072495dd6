import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Input, Modal, Radio, Row, Switch, InputNumber, Select, } from "antd";
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import MemSelect from '@/components/MemSelect';
import Tip from '@/components/Tip';
import moment from 'moment';
import YN from '@/components/YesOrNoSelect';
import { formLabel, getIdCardInfo, correctIdcard } from '@/utils/method.js';
import Date from '@/components/Date';
import { residentUpdate, residentSave } from './services';
import _isEmpty from 'lodash/isEmpty';
import { validateLength } from '@/utils/formValidator';

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      key: '' //PanelTable的index
    }
  }
  showModal = (record) => {
    const { key } = record;
    this.setState({
      visible: true,
      key
    });
  };

  handleOk = () => {
    const { basicInfo = {} } = this.props.unit;
    const { children, title, memInfo = {}, dataInfo = {} } = this.props;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if (val['memName'] != memInfo['memName'] || val['memIdcard'] != memInfo['memIdcard']) {
          let result = await correctIdcard(val['memName'], val['memIdcard']);
        if (result['code'] != '200') {
          this.props.form.setFields({
            memIdcard: {
              value: val['memIdcard'],
              errors: [new Error('经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')]
            }
          })
          Tip.error('操作提示', '经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')
          return
        } else {
          val['idCardReason'] = result['reason']
          val['idCardReasonName'] = result['reasonName']
        }
        }
        let obj = undefined;
        ['d25Code', 'd26Code', 'd07Code', 'd139Code', 'd140Code', 'd141Code', 'd144Code', 'd197Code'].map(obj => {
          if (val[obj] && typeof val[obj] === 'object') {
            let key = obj.split('C')[0];
            val[`${key}Name`] = val[obj]['name'];
            val[obj] = val[obj]['key']
          }
        });

        // 是否村任职选调生选否的时候清空选调单位层级的值
        if(val['hasVillageTransferStudent'] === 0){
          val['d144Code'] = undefined;
          val['d144Name'] = undefined;
        }

        ['startDate', 'residentDate', 'memBirthday', 'residentDate'].map(obj => {
          if (val[obj]) {
            val[obj] = val[obj].valueOf();
          }
        });
        if (val['memCode'] && typeof val['memCode'] === 'object') {
          val['memName'] = val['memCode'][0]['name'];
          val['memCode'] = val['memCode'][0]['code']
        }
        // ['isIncumbent', 'd139Code'].map(obj => {
        //   if (val[obj]) {
        //     val[obj] = 1;
        //   } else {
        //     val[obj] = 0
        //   }
        // });
        val['unitCode'] = basicInfo['code'];

        if (val['sexCode']) {
          val['sexName'] = val['sexCode'] == '0' ? '女' : '男'
        }
        let type = residentSave;
        if (memInfo['id']) {
          type = residentUpdate;
        }
        const { code = 500 } = await type({
          data: {
            ...memInfo,
            // electCode: dataInfo['code'],
            unitCode: basicInfo['unitCode'],
            ...val
          }
        })


        // obj = await this.props.dispatch({
        //   type,
        //   payload: {
        //     data: {
        //       ...memInfo,
        //       electCode: dataInfo['code'],
        //       ...val
        //     }
        //   }
        // });
        if (code === 0) {
          Tip.success('操作提示', memInfo['code'] ? '修改成功' : '新增成功');
          this.props.queryList();
          this.handleCancel();
        }
      }
    });
  };
  handleCancel = () => {
    this.props.onClose();
    this.setState({
      visible: false,
    });
  };
  onStartChange = (value) => {
    this.setState({
      startValue: value
    })
  };
  onEndChange = (value) => {
    this.setState({
      endValue: value
    })
  };
  disabledresidentDate = (endValue) => {
    const startValue = this.state.startValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.endOf('day') <= startValue.endOf('day');
  };
  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.endOf('day') <= startValue.endOf('day');
  };
  validatorIdcard = async (rule, value, callback) => {
    if (!value) {
      callback('身份证必填');
    }
    if (value && value.length !== 18 && process.env.idCheck != 'false') {
      callback('身份证应该为18位');
    }
    if (getIdCardInfo(value) === 'Error') {
      callback('身份证格式错误,请核对身份证图片');
    } else {
      // let fieldValue = this.props.form.getFieldValue('memName');
      // let res =await geitCard({idCard:fieldValue,name:value});
      callback()
    }
  };
  getIDinfo = (e) => {
    const { target: { value = '' } = {} } = e || {};
    let info = getIdCardInfo(value);
    if (value && info !== 'Error') {
      this.props.form.setFieldsValue({
        sexCode: info[2] === '女' ? '0' : '1',
        memBirthday: info[1] ? moment(info[1], 'YYYY-MM-DD') : undefined,
      });
    }
  };
  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { children, title, dataInfo = {}, tipMsg = {}, memInfo = {} } = this.props;
    const { basicInfo = {} } = this.props.unit;
    const { d04Code = '' } = basicInfo;
    return (
      <React.Fragment>
        {
          children ? React.cloneElement(children as any, {
            onClick: this.showModal,
            key: 'container'
          }) : null
        }
        {
          this.state.visible &&
          <Modal
            title={title || "请输入标题"}
            visible={this.state.visible}
            onOk={this.handleOk}
            onCancel={this.handleCancel}
            width={1000}
            className='add_member_modal'
            maskClosable={false}
          >
            <Form {...formItemLayout}>
              <Row>
                <Col span={24}>
                  <FormItem
                    label={formLabel('党员情况', tipMsg['d139Code'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('d139Code', {
                      initialValue: memInfo['d139Code'],
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <DictTreeSelect backType={'object'} codeType={'dict_d139'} initValue={memInfo['d139Code']} placeholder="请选择" parentDisable={true} />
                    )}
                  </FormItem>
                </Col>

                {
                  (function (_this) {
                    const { props } = _this;
                    let d139Code = props.form.getFieldValue('d139Code');
                    if (d139Code?.key) {
                      d139Code = d139Code?.key
                    }
                    return (
                      <React.Fragment>
                        {
                          d139Code != '1' ? <React.Fragment>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('人员姓名', tipMsg['memName'])}
                              >
                                {getFieldDecorator('memName', {
                                  initialValue: memInfo['memName'],
                                  rules: [{ required: true, message: '请输入人员姓名' }, {validator: (...e)=>validateLength(e, 16, 50)} ],
                                })(
                                  <Input placeholder="请输入人员姓名" />
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('性别', tipMsg['sexCode'])}
                              >
                                {getFieldDecorator('sexCode', {
                                  initialValue: memInfo['sexCode'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <RadioGroup>
                                    <Radio value={'1'}>男</Radio>
                                    <Radio value={'0'}>女</Radio>
                                  </RadioGroup>
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('身份证', tipMsg['memIdcard'])}
                              >
                                {getFieldDecorator('memIdcard', {
                                  initialValue: memInfo['memIdcard'],
                                  rules: [
                                    { required: true, message: '请输入身份证' },
                                    { validator: _this.validatorIdcard },
                                  ],
                                })(
                                  <Input placeholder="请输入身份证" onBlur={_this.getIDinfo} />
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('出生日期', tipMsg['memBirthday'])}
                              >
                                {getFieldDecorator('memBirthday', {
                                  initialValue: memInfo['memBirthday'] !== undefined ? moment(memInfo['memBirthday'] * 1) : undefined,
                                  rules: [{ required: true, message: '请输入' }],
                                })(
                                  <Date />
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('学历情况', tipMsg['d07Code'])}
                              >
                                {getFieldDecorator('d07Code', {
                                  initialValue: memInfo['d07Code'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <DictTreeSelect backType={'object'} codeType={'dict_d07'} initValue={memInfo['d07Name']} placeholder="请选择" parentDisable={true} />
                                )}
                              </FormItem>
                            </Col>
                          </React.Fragment> :
                            <React.Fragment>
                              <Col span={12}>
                                <FormItem
                                  label={formLabel('党员姓名', tipMsg['memCode'])}
                                >
                                  {getFieldDecorator('memCode', {
                                    initialValue: memInfo['memCode'],
                                    rules: [{ required: true, message: '请选择' }],
                                  })(
                                    <MemSelect initValue={memInfo['memName']} placeholder="请选择党员" />
                                  )}
                                </FormItem>
                              </Col>
                            </React.Fragment>

                        }
                      </React.Fragment>
                    )
                  })(this)
                }

                <Col span={12}>
                  <FormItem
                    label={formLabel('人员身份', tipMsg['d140Code'])}
                  >
                    {getFieldDecorator('d140Code', {
                      initialValue: memInfo['d140Code'],
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <DictTreeSelect backType={'object'} codeType={'dict_d140'} initValue={memInfo['d140Code']} placeholder="请选择" parentDisable={true} />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={formLabel('人员来源', tipMsg['d141Code'])}
                  >
                    {getFieldDecorator('d141Code', {
                      initialValue: memInfo['d141Code'],
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <DictTreeSelect backType={'object'} codeType={'dict_d141'} initValue={memInfo['d141Code']} placeholder="请选择" parentDisable={true} />
                    )}
                  </FormItem>
                </Col>
                {/* <Col span={12}>
                  <FormItem
                    label={formLabel('是否村任职选调生', tipMsg['hasVillageTransferStudent'])}
                  >
                    {getFieldDecorator('hasVillageTransferStudent', {
                     initialValue: memInfo['hasVillageTransferStudent'],
                      rules: [{ required: true, message: '是否村任职选调生' }],
                    })(
                      <Select style={{ width: '100%' }}>
                        <Select.Option value={1}>是</Select.Option>
                        <Select.Option value={0}>否</Select.Option>
                      </Select>
                    )}
                  </FormItem>
                </Col> */}
                {/* { //6、是否村任职选调生 选择是的时候，要弹出来一个填写框：选调单位层级
                  getFieldValue('hasVillageTransferStudent') == 1 &&
                  (
                    <Col span={12}>
                  <FormItem
                    label={formLabel('选调单位层级', tipMsg['d144Code'])}
                  >
                    {getFieldDecorator('d144Code', {
                      initialValue: memInfo['d144Code'],
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <DictTreeSelect backType={'object'} codeType={'dict_d144'} initValue={memInfo['d144Code']} placeholder="请选择" parentDisable={true} />
                    )}
                  </FormItem>
                </Col>
                  )
                } */}
                <Col span={12}>
                  <FormItem
                    label={formLabel(' 驻村开始时间', tipMsg['startDate'])}
                  >
                    {getFieldDecorator('startDate', {
                      initialValue: memInfo['startDate'] ? moment(memInfo['startDate']) : dataInfo['startDate'] ? moment(dataInfo['startDate']) : undefined,
                      rules: [{ required: true, message: '驻村开始时间' }],
                    })(
                      <Date />,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={formLabel('预计驻村结束时间', tipMsg['residentDate'])}
                  >
                    {getFieldDecorator('residentDate', {
                      initialValue: memInfo['residentDate'] ? moment(memInfo['residentDate']) : dataInfo['residentDate'] ? moment(dataInfo['residentDate']) : undefined,
                      rules: [{ required: true, message: '预计驻村结束时间' }],
                    })(
                      <Date isDefaultEnd={false}/>,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={formLabel('派出单位名称及职务', tipMsg['dispatchPosition'])}
                  >
                    {getFieldDecorator('dispatchPosition', {
                      initialValue: memInfo['dispatchPosition'],
                      rules: [{ required: true, message: '派出单位名称及职务' }],
                    })(
                      <Input placeholder="派出单位名称及职务" />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={formLabel('选派层级', tipMsg['d197Code'])}
                  >
                    {getFieldDecorator('d197Code', {
                      initialValue: memInfo['d197Code'],
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <DictSelect backType={'object'} codeType={'dict_d197'} initValue={memInfo['d197Code']} placeholder="请选择" />
                    )}
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </Modal>
        }

      </React.Fragment>
    )
  }
}
export default Form.create()(index);
