/**
 * 新增/编辑 组织生活
 */
import React, { useState, useImperativeHandle, Fragment, useRef, useEffect } from 'react';
import { Input, Form, Modal, Select, Upload, message, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import moment from 'moment';
import { compressAccurately } from 'image-conversion'; // 压缩图片插件
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _isObject from 'lodash/isObject';
import _isString from 'lodash/isString';
import _difference from 'lodash/difference';
import { getSession } from '@/utils/session';
import Tip from '@/components/Tip';
import DateTime from '@/components/Date';
import OrgSelect from '@/components/OrgSelect';
import DictSelect from '@/components/DictSelect';
import MemSelect from '@/components/MemSelect';
import { addOrgLife, updateOrgLife, findOrgLifeById, teamList } from '../services';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
const index = (props: any, ref: any) => {
  const { code = undefined, orgCode = undefined, d01Code = undefined } = getSession('org') || {
    code: undefined,
    orgCode: undefined,
    isLeaf: undefined,
    d01Code: undefined,
  };
  const [form] = Form.useForm();
  const memCodeListRef = useRef<any>();
  const leaveMemCodeListRef = useRef<any>();
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('新增组织生活');
  const [modalType, setModalType] = useState('add');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [basinInfo, setBasinInfo] = useState<any>({});
  const [fileList, setFileList] = useState<any>([]);
  const [previewImage, setPreviewImage] = useState('');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewTitle, setPreviewTitle] = useState('');
  const [showSpeaker, setShowSpeaker] = useState(false); // 是否显示党课主讲人
  const [d158CodeNoDraw, setD158CodeNoDraw] = useState<any>([]);
  const [showJoinOrgCodeList, setShowJoinOrgCodeList] = useState(false); // 是否显示 参与党支部
  const [joinOrg, setJoinOrg]: any = useState([]); // 参与党支部
  const [d01, setD01] = useState(d01Code);
  const [key, setKey]: Array<any> = useState(new Date().valueOf());
  const [resetKey, setresetKey] = useState(new Date().valueOf());
  const [selectedRows, setSelectedRows]: Array<any> = useState([]); //参与人员选中项 初始值
  const [selectedRows1, setSelectedRows1]: Array<any> = useState([]); // 请假人员选中项 初始值
  const [selectedRowsOrg, setSelectedRowsOrg]: Array<any> = useState([]); //参与党支部选中项 初始值
  const [groupList, setGroupList]: Array<any> = useState([]); // 党小组

  const upProps = {
    // multiple: true,
    // showUploadList: false,
    name: 'file',
    action: `/api/base/upload`,
    accept: '.jpg,.png,.jpeg',
    headers: {
      Authorization: sessionStorage.getItem('token') || '',
      dataApi: sessionStorage.getItem('dataApi') || '',
    },
  };
  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>点击上传图片</div>
    </div>
  );

  useImperativeHandle(ref, () => ({
    open: (query: any, type: string) => {
      if (query?.code) {
        if (type === 'edit') {
          setTitle('编辑组织生活');
        }
        if (type === 'readOnly') {
          setTitle('查看组织生活');
        }
        getBasicInfo(query?.code);
      } else {
        setTitle('新增组织生活');
        setJoinOrg([getSession('org')]);
      }
      setBasinInfo(query);
      setModalType(type);
      form.setFieldsValue(query);
      setVisible(true);
    },
  }));
  const getBasicInfo = async (code?: any) => {
    const { code: resCode = 500, data = {} } = await findOrgLifeById({ code });
    if (resCode == 0) {
      setBasinInfo(data);
      // 有党小组请求当小组列表接口
      if (data.d158Code.includes('2')) {
        getGroupList(data.orgLevelCode);
      }
      // 组装参与党支部[{code:'',name:'',orgCode:''}]
      if (data?.joinOrgCodeList && data?.joinOrgCodeListName && data?.joinOrgLevelCodeList) {
        let codeArr = data?.joinOrgCodeList.split(',');
        let nameArr = data?.joinOrgCodeListName.split(',');
        let orgCodeArr = data?.joinOrgLevelCodeList.split(',');
        let orgArr: Array<any> = [];
        codeArr.map((item: string, index) => {
          orgArr.push({ code: item });
        });
        nameArr.map((item: string, index) => {
          orgArr[index].name = item;
        });
        orgCodeArr.map((item: string, index) => {
          orgArr[index].orgCode = item;
        });
        setSelectedRowsOrg(orgArr);
      }
      // 组装参与人员[{code:'',name:''}]
      if (data?.memCodeList && data?.memCodeListName) {
        let codeArr = data?.memCodeList.split(',');
        let nameArr = data?.memCodeListName.split(',');
        let memArr: Array<any> = [];
        codeArr.map((item: string, index) => {
          memArr.push({ code: item });
        });
        nameArr.map((item: string, index) => {
          memArr[index].name = item;
        });
        setSelectedRows(memArr);
      }
      // 组装请假人员[{code:'',name:''}]
      if (data?.leaveMemCodeList && data?.leaveMemCodeListName) {
        let codeArr = data?.leaveMemCodeList.split(',');
        let nameArr = data?.leaveMemCodeListName.split(',');
        let memArr: Array<any> = [];
        codeArr.map((item: string, index) => {
          memArr.push({ code: item });
        });
        nameArr.map((item: string, index) => {
          memArr[index].name = item;
        });
        setSelectedRows1(memArr);
      }
      // 根据参与党支部设置参与人员可选支部过滤
      if (data?.joinOrgLevelCodeList) {
        let arr = data?.joinOrgLevelCodeList.split(',').map((item, index) => {
          return { orgCode: item };
        });
        setJoinOrg(arr);
      }
      if (`${data?.d158Code}`.includes('4')) {
        setShowSpeaker(true);
      }
      setD01(data?.d01Code || '');
      if (data?.hasMoreOrgJoin) {
        let e = data?.hasMoreOrgJoin;
        setShowJoinOrgCodeList(e === '1');
      }
      if (
        `${data?.d158Code}`.includes('1') ||
        `${data?.d158Code}`.includes('4') ||
        `${data?.d158Code}`.includes('5') ||
        `${data?.d158Code}`.includes('6')
      ) {
        setD158CodeNoDraw(['2', '3']);
      } else if (`${data?.d158Code}`.includes('2')) {
        setD158CodeNoDraw(['1', '3', '4', '5', '6']);
      } else if (`${data?.d158Code}`.includes('3')) {
        setD158CodeNoDraw(['1', '2', '4', '5', '6']);
      } else {
        initAcType(data?.d01Code);
      }
      let newData = { ...data };
      if (newData?.d158Code) {
        newData.d158Code = newData?.d158Code.split(',');
      }
      // 图片
      if (!_isEmpty(newData['filePath'])) {
        let imgObj = newData['filePath'];
        let temp: any = [];
        let xhr = new XMLHttpRequest();
        //GET请求,请求路径url,async(是否异步)
        xhr.open('GET', `/api${imgObj['url']}`, true);
        xhr.setRequestHeader('Authorization', sessionStorage.getItem('token') || '');
        xhr.setRequestHeader('dataApi', sessionStorage.getItem('dataApi') || '');
        //设置响应类型为 blob
        xhr.responseType = 'blob';
        //发送请求
        xhr.send();
        //关键部分
        xhr.onload = function (e) {
          //如果请求执行成功
          if (this.status === 200) {
            let blob = this.response;
            blobToBase64(blob).then((res) => {
              temp.push({ uid: new Date().valueOf(), thumbUrl: res, ...imgObj });
              setFileList(temp);
            });
          }
        };
      }
      form.setFieldsValue(newData);
    }
  };
  const hadndleFinish = async (vals: any) => {
    const { onOk } = props;
    // console.log('vals===', vals);
    // return;
    setConfirmLoading(true);
    let url = addOrgLife;
    if (basinInfo?.code) {
      url = updateOrgLife;
    }
    // 活动类型
    if (vals?.d158Code) {
      if (_isArray(vals?.d158Code)) {
        let codeArr: any = [];
        let nameArr: any = [];
        vals?.d158Code.map((item: any, index) => {
          if (item?.key) {
            codeArr.push(item['key']);
            nameArr.push(item['name']);
          } else {
            codeArr.push(item);
            nameArr = basinInfo?.d158Name || [];
          }
        });
        vals.d158Name = nameArr.toString();
        vals.d158Code = codeArr.toString();
        // 党课类型不包含4，去掉党课主讲人
        if (!codeArr.includes('4')) {
          vals.speaker = undefined;
        }
      }
    }
    if (vals?.activityTime) {
      vals.activityTime = moment(vals?.activityTime).valueOf() || undefined;
    }
    // 是否多个党支部参与
    if (vals?.hasMoreOrgJoin == '0') {
      vals.joinOrgCodeList = undefined;
    }
    // 参与党支部
    if (vals?.joinOrgCodeList) {
      if (_isArray(vals?.joinOrgCodeList)) {
        let codeArr: any = [];
        let nameArr: any = [];
        let orgCodeArr: any = [];
        vals?.joinOrgCodeList.map((item: any, index) => {
          if (item?.code && item?.name && item?.orgCode) {
            codeArr.push(item['code']);
            nameArr.push(item['name']);
            orgCodeArr.push(item['orgCode']);
          } else {
            codeArr.push(item);
            nameArr = basinInfo?.joinOrgCodeListName || [];
          }
        });
        vals.joinOrgCodeListName = nameArr.toString();
        vals.joinOrgCodeList = codeArr.toString();
        vals.joinOrgLevelCodeList = orgCodeArr.toString();
      } else {
        vals.joinOrgCodeListName = basinInfo?.joinOrgCodeListName;
        vals.joinOrgLevelCodeList = basinInfo?.joinOrgLevelCodeList;
      }
    }
    // 参与人员
    if (vals?.memCodeList) {
      if (_isArray(vals?.memCodeList)) {
        let codeArr: any = [];
        let nameArr: any = [];
        vals?.memCodeList.map((item: any, index) => {
          if (item?.code && item?.name) {
            codeArr.push(item['code']);
            nameArr.push(item['name']);
          } else {
            codeArr.push(item);
            nameArr = basinInfo?.memCodeListName || [];
          }
        });
        vals.memCodeListName = nameArr.toString();
        vals.memCodeList = codeArr.toString();
      } else {
        vals.memCodeListName = basinInfo?.memCodeListName;
      }
    }
    // 请假人员
    if (vals?.leaveMemCodeList) {
      if (_isArray(vals?.leaveMemCodeList)) {
        let codeArr: any = [];
        let nameArr: any = [];
        vals?.leaveMemCodeList.map((item: any, index) => {
          if (item?.code && item?.name) {
            codeArr.push(item['code']);
            nameArr.push(item['name']);
          } else {
            codeArr.push(item);
            nameArr = basinInfo?.leaveMemCodeListName || [];
          }
        });
        vals.leaveMemCodeListName = nameArr.toString();
        vals.leaveMemCodeList = codeArr.toString();
      } else {
        vals.leaveMemCodeListName = basinInfo?.leaveMemCodeListName;
      }
    }

    // 比较 参与人员、请假人员 是否有相同
    let memCodeArr = vals?.memCodeList ? vals?.memCodeList.split(',') : [];
    let leaveMemCodeArr = vals?.leaveMemCodeList ? vals?.leaveMemCodeList.split(',') : [];
    let filterArr = _difference(memCodeArr, leaveMemCodeArr);
    if (filterArr.length < memCodeArr.length) {
      message.error('参与人员与请假人员不能有相同，请修改');
      setConfirmLoading(false);
      return;
    }

    // 上传图片
    if (_isObject(vals?.filePath)) {
      if (!(vals?.filePath?.id && vals?.filePath?.name && vals?.filePath?.url)) {
        const { fileList = [] } = vals?.filePath;
        let fileObj = {};
        if (fileList.length > 0) {
          fileObj = fileList[0];
        } else {
          vals['filePath'] = undefined;
        }
        if (fileObj['response'] && fileObj['response']['code'] == '0') {
          vals['filePath'] = fileObj['response']['data'][0];
        } else {
          vals['filePath'] = undefined;
        }
      }
    }
    // console.log('vals===', vals);
    // return;
    const { code: resCode = 500 } = await url({
      data: {
        orgCode: code,
        orgLevelCode: orgCode,
        d01Code,
        ...basinInfo,
        ...vals,
      },
    });
    setConfirmLoading(false);
    if (resCode == 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOk && onOk();
    }
  };
  const handleCancel = () => {
    setVisible(false);
    form.resetFields();
    setD158CodeNoDraw([]);
    setConfirmLoading(false);
    setFileList([]);
    setPreviewImage('');
    setPreviewTitle('');
    setPreviewOpen(false);
    setBasinInfo({});
    setJoinOrg([]);
    setShowSpeaker(false);
    setSelectedRows([]);
    setSelectedRows1([]);
    setSelectedRowsOrg([]);
    setD01(d01Code);
    setresetKey(new Date().valueOf());
  };

  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file?.thumbUrl ? file.thumbUrl : (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };
  const imgChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };
  const getBase64 = (file): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  const blobToBase64 = (blob) => {
    return new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.onload = (e?: any) => {
        resolve(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    });
  };
  const getGroupList = async (code?) => {
    const res = await teamList({
      orgCode: code ? code : orgCode,
      pageNum: 1,
      pageSize: 100,
    });
    if (res.code == 0) {
      setGroupList(res?.data?.list || []);
    }
  };
  const initAcType = (d01Code) => {
    // 判断是否是支部， 筛选活动类型
    if (
      d01Code === '631' ||
      d01Code === '632' ||
      d01Code === '634' ||
      d01Code === '931' ||
      d01Code === '932'
    ) {
      setD158CodeNoDraw([]);
    } else {
      setD158CodeNoDraw(['2', '3']);
    }
  };
  useEffect(() => {
    setD01(d01Code);
    initAcType(d01Code);
  }, [d01Code, orgCode]);

  return (
    <Modal
      footer={
        modalType === 'readOnly' ? (
          false
        ) : (
          <Fragment>
            <Button onClick={handleCancel}>取消</Button>
            <Button
              type="primary"
              onClick={() => {
                form.submit();
              }}
            >
              确定
            </Button>
          </Fragment>
        )
      }
      maskClosable={false}
      title={title}
      visible={visible}
      // onOk={() => {
      //   form.submit();
      // }}
      onCancel={handleCancel}
      width={'800px'}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
        <Form.Item
          label="活动名称"
          name={'activityName'}
          rules={[{ required: true, message: '活动名称' }]}
        >
          <Input maxLength={50} disabled={modalType === 'readOnly'} />
        </Form.Item>
        <Form.Item
          label="活动类型"
          name={'d158Code'}
          rules={[{ required: true, message: '活动类型' }]}
        >
          <DictSelect
            // codeType={'dict_d158'}
            codeType={'dict_d210'}
            disabled={modalType === 'readOnly'}
            initValue={
              _isEmpty(basinInfo)
                ? undefined
                : _isEmpty(basinInfo['d158Code'])
                  ? undefined
                  : basinInfo['d158Code'].split(',')
            }
            backType="object"
            mode="multiple"
            noDraw={d158CodeNoDraw}
            onChange={(e: any) => {
              let tag = false;
              if (_isArray(e)) {
                e.map((item, index) => {
                  if (item?.key === '4') {
                    tag = true;
                  }
                });
              }
              setShowSpeaker(tag);
              //   1.党员大会、3.党支部委员会及2.党小组会不能同时被两两选中,但可以和其他类型的组织生活一起选中。
              if (!_isEmpty(e)) {
                let selectKey: any = [];
                e.map((it: any, i) => {
                  selectKey.push(it?.key);
                });
                if (
                  selectKey.includes('1') ||
                  selectKey.includes('4') ||
                  selectKey.includes('5') ||
                  selectKey.includes('6')
                ) {
                  setD158CodeNoDraw(['2', '3']);
                } else if (selectKey.includes('2')) {
                  getGroupList(basinInfo?.orgLevelCode);
                  setD158CodeNoDraw(['1', '3', '4', '5', '6']);
                } else if (selectKey.includes('3')) {
                  setD158CodeNoDraw(['1', '2', '4', '5', '6']);
                } else {
                  initAcType(d01Code);
                }
              } else {
                initAcType(d01Code);
              }
            }}
          />
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.activityName !== currentValues.activityName
          }
        >
          {({ getFieldValue }) => {
            let val = getFieldValue('d158Code');
            let flag = false;
            if (typeof val == 'string' && val.includes('2')) {
              flag = true;
            }
            if (Array.isArray(val) && val.find((it) => it.key == '2' || it == '2')) {
              flag = true;
            }
            return flag ? (
              <Form.Item
                label={'党小组'}
                name={'orgGroupCodeList'}
                rules={[{ required: true, message: '活动时间' }]}
              >
                <Select mode={'multiple'} disabled={modalType === 'readOnly'}>
                  {!_isEmpty(groupList) &&
                    groupList.map((it, index) => {
                      return (
                        <Select.Option key={it.code} value={it.code}>
                          {it.name}
                        </Select.Option>
                      );
                    })}
                </Select>
              </Form.Item>
            ) : (
              ''
            );
          }}
        </Form.Item>

        <Form.Item
          label="活动时间"
          name={'activityTime'}
          rules={[{ required: true, message: '活动时间' }]}
        >
          <DateTime disabled={modalType === 'readOnly'} />
        </Form.Item>
        {/* 当活动类型选择到“党课”时，需要输入党课“主讲人”信息（普通输入框）。 */}
        {showSpeaker && (
          <Form.Item
            label="党课主讲人"
            name={'speaker'}
            rules={[{ required: true, message: '党课主讲人' }]}
          >
            <Input maxLength={50} disabled={modalType === 'readOnly'} />
          </Form.Item>
        )}
        {/* 党委或党总支在创建党员大会时，可以选择多个党支部参与。对党支部而言，则无需展示此项功能 */}
        {/* 党支部和临时党支部就是最底层了, 他们下面没有更低的党支部了 就不需要展示是否多个党支部参与了  2023.01.10 */}
        {!(`${d01}`.startsWith('63') || `${d01}`.startsWith('931')) && (
          <Fragment>
            <Form.Item
              label="是否多个党支部参与"
              name={'hasMoreOrgJoin'}
              rules={[{ required: true, message: '是否多个党支部参与' }]}
            >
              <Select
                disabled={modalType === 'readOnly'}
                onChange={(e) => {
                  setShowJoinOrgCodeList(e === '1');
                  if (e == '0') {
                    setJoinOrg([getSession('org')]);
                  }
                  // 切换时清空 参与人员、请假人员
                  form.setFieldsValue({ memCodeList: undefined, leaveMemCodeList: undefined });
                  memCodeListRef?.current?.clearAll();
                  leaveMemCodeListRef?.current?.clearAll();
                  setBasinInfo({
                    ...basinInfo,
                    memCodeListName: undefined,
                    leaveMemCodeListName: undefined,
                  });
                  setKey(new Date().valueOf());
                  setSelectedRows([]);
                  setSelectedRows1([]);
                }}
              >
                <Select.Option value={'1'}>是</Select.Option>
                <Select.Option value={'0'}>否</Select.Option>
              </Select>
            </Form.Item>

            {showJoinOrgCodeList && (
              <Form.Item
                label="参与党支部"
                name={'joinOrgCodeList'}
                rules={[{ required: true, message: '参与党支部' }]}
              >
                <OrgSelect
                  title={basinInfo['joinOrgCodeListName'] || ''}
                  disabled={modalType === 'readOnly'}
                  key={resetKey}
                  orgTypeList={['3', '4']}
                  org={
                    _isEmpty(basinInfo?.orgCode) || _isEmpty(basinInfo?.orgLevelCode)
                      ? getSession('org')
                      : { code: basinInfo?.orgCode, orgCode: basinInfo?.orgLevelCode }
                  }
                  oorg={{
                    orgCode: basinInfo?.orgLevelCode || getSession('org').orgCode,
                    subordinate: 1,
                  }}
                  onChange={(data) => {
                    console.log('参与党支部===', data);

                    setJoinOrg(data);
                    // 清空 参与人员、请假人员
                    form.setFieldsValue({ memCodeList: undefined, leaveMemCodeList: undefined });
                    memCodeListRef?.current?.clearAll();
                    leaveMemCodeListRef?.current?.clearAll();
                    setBasinInfo({
                      ...basinInfo,
                      memCodeListName: undefined,
                      leaveMemCodeListName: undefined,
                    });
                    setKey(new Date().valueOf());
                    setSelectedRows([]);
                    setSelectedRows1([]);
                  }}
                  multiple={true}
                  initValue={
                    _isEmpty(basinInfo)
                      ? undefined
                      : basinInfo['joinOrgCodeListName']
                        ? basinInfo['joinOrgCodeListName'].split(',')
                        : undefined
                  }
                  selectedRows={selectedRowsOrg}
                />
              </Form.Item>
            )}
          </Fragment>
        )}
        <Form.Item
          label="参与人员"
          name={'memCodeList'}
          rules={[{ required: true, message: '参与人员' }]}
        >
          <MemSelect
            title={basinInfo['memCodeListName'] || ''}
            disabled={modalType === 'readOnly'}
            ref={memCodeListRef}
            key={`memCodeList${key}`}
            filterTree={joinOrg || undefined}
            checkType="checkbox"
            initValue={
              _isEmpty(basinInfo)
                ? undefined
                : basinInfo['memCodeListName']
                  ? basinInfo['memCodeListName'].split(',')
                  : undefined
            }
            selectedRows={selectedRows}
            onChange={(e: any) => {
              console.log('参与人员===', e);
              // 参与人员
              setSelectedRows(e);
              let memCodeArr: Array<string> = [];
              if (_isArray(e)) {
                memCodeArr = e.map((item, index) => item?.code);
              }
              // 请假人员
              let leaveMem = form.getFieldValue('leaveMemCodeList');
              let leaveMemCodeArr: Array<string> = [];
              console.log('leaveMem===', leaveMem);
              if (_isArray(leaveMem)) {
                leaveMemCodeArr = leaveMem.map((item, index) => item?.code);
              } else if (_isString(leaveMem)) {
                leaveMemCodeArr = leaveMem.split(',');
              }
              // 比较 参与人员、请假人员 是否有相同
              let filterArr = _difference(memCodeArr, leaveMemCodeArr);
              if (filterArr.length < memCodeArr.length) {
                message.error('参与人员与请假人员不能有相同，请修改');
              }
            }}
          />
        </Form.Item>
        <Form.Item
          label="请假人员"
          name="leaveMemCodeList"
          rules={[{ required: false, message: '请假人员' }]}
        >
          <MemSelect
            title={basinInfo['leaveMemCodeListName'] || ''}
            disabled={modalType === 'readOnly'}
            ref={leaveMemCodeListRef}
            key={`leaveMemCodeList${key}`}
            filterTree={joinOrg || undefined}
            checkType="checkbox"
            initValue={
              _isEmpty(basinInfo)
                ? undefined
                : basinInfo['leaveMemCodeListName']
                  ? basinInfo['leaveMemCodeListName'].split(',')
                  : undefined
            }
            selectedRows={selectedRows1}
            onChange={(e: any) => {
              console.log('请假人员===', e);
              // 请假人员
              setSelectedRows1(e);
              let leaveMemCodeArr: Array<string> = [];
              if (_isArray(e)) {
                leaveMemCodeArr = e.map((item, index) => item?.code);
              }
              // 参与人员
              let mem = form.getFieldValue('memCodeList');
              let memCodeArr: Array<string> = [];
              if (_isArray(mem)) {
                memCodeArr = mem.map((item, index) => item?.code);
              } else if (_isString(mem)) {
                memCodeArr = mem.split(',');
              }
              // 比较 参与人员、请假人员 是否有相同
              let filterArr = _difference(memCodeArr, leaveMemCodeArr);
              if (filterArr.length < memCodeArr.length) {
                message.error('参与人员与请假人员不能有相同，请修改');
              }
            }}
          />
        </Form.Item>
        <Form.Item
          label="活动地点"
          name={'activityLocation'}
          rules={[{ required: false, message: '活动地点' }]}
        >
          <Input maxLength={100} disabled={modalType === 'readOnly'} />
        </Form.Item>
        <Form.Item
          label="活动记录"
          name={'activityRecord'}
          rules={[{ required: true, message: '活动记录' }]}
        >
          <Input.TextArea
            rows={4}
            maxLength={200}
            placeholder="请简要描述活动内容，禁止上传涉密信息及敏感信息"
            disabled={modalType === 'readOnly'}
          />
        </Form.Item>
        {/* <Form.Item
          label="上传图片"
          name={'filePath'}
          //   initialValue={getInitFileList(basinInfo['filePath'])}
          rules={[{ required: false, message: '上传图片' }]}
        >
          <Upload
            disabled={modalType === 'readOnly'}
            {...upProps}
            listType="picture-card"
            fileList={fileList}
            onPreview={handlePreview}
            onChange={imgChange}
            beforeUpload={(file, fileList) => {
              const { name = '', size = 0 } = file;
              let hasChinese = /[\u4e00-\u9fa5]/g.test(name);
              let fileSize: number = file['size'] / 1024 / 1024;
              return new Promise(async (resolve, reject) => {
                // if (hasChinese) {
                //   form.setFields([
                //     {
                //       name: 'filePath',
                //       value: fileList,
                //       errors: ['文件名不能包含中文，否则会上传失败。请修改后重新上传'],
                //     },
                //   ]);
                //   // reject(Upload.LIST_IGNORE);
                //   return Upload.LIST_IGNORE;
                // }
                // 图片大于1M压缩为1M
                if (fileSize > 1) {
                  compressAccurately(file, 1024).then((res) => {
                    resolve(res);
                  });
                } else {
                  resolve(file);
                }
              });
            }}
          >
            {fileList.length >= 1 ? null : uploadButton}
          </Upload>
        </Form.Item> */}
      </Form>
      <Modal
        visible={previewOpen}
        title={previewTitle}
        footer={null}
        onCancel={() => {
          setPreviewOpen(false);
        }}
      >
        <img alt="image" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </Modal>
  );
};
export default React.forwardRef(index);
