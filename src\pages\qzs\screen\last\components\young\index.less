@width: 3456px;
@height: 1152px;

@cardHeight: 873.6px;
@leftCardWith: 1020.4px;
@midCardWith: 1105.4px;
@rightCardWith: 947.7px;

.box {
  background: url('../../../../../../assets/qzs/youngbg.png') no-repeat;
  background-size: 100% 100%;
  width: @width;
  height: @height;
  overflow: auto;
  position: relative;
  padding: 100px 110px 0 110px;
  transform: scale(0.555, 0.55);
  transform-origin: 0 0;
  .head {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    .btn {
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 26px;
      color: #d4171b;
      background: #ffebad;
      border-radius: 6px;
      height: 50px;
    }
  }
  .body {
    display: flex;
    justify-content: space-between;
    .left {
      .box1 {
        width: @leftCardWith;
        height: @cardHeight;
        background: #fffbf0;
        border-radius: 18px;
        border: 2px solid #be0c10;
        padding: 50px;
        .info {
          width: 100%;
          position: relative;

          .name {
            font-family: Source Han Serif SC;
            font-weight: 800;
            font-size: 67.67px;
            color: #be0c10;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            > span {
              margin-left: 42px;
              margin-top: 6px;
              padding: 2px 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 35.85px;
              color: #ffffff;
              display: inline-block;
              background: url('../../../../../../assets/qzs/icon1.png') no-repeat;
              background-size: 100% 100%;
            }
          }

          .desc {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 37.84px;
            color: #62471e;

            display: flex;

            > div:first-child {
              width: 202px;
              display: inline-block;
              text-align-last: justify;
              text-align: justify;
            }
            > div:last-child {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              /*! autoprefixer: off */
              -webkit-box-orient: vertical;
              /* autoprefixer: on */
            }
          }

          .avaBox {
            position: absolute;
            right: 10px;
            top: -76px;
            .cyc {
              width: 219px;
              height: 219px;
              border: 4px solid #fef0cf;
              border-radius: 50%;
              z-index: 3;
              position: relative;
              top: 91px;
              left: -23px;
              overflow: hidden;
              > img {
                position: absolute;
                left: 21px;
                top: 13px;
                width: 170px;
                scale: 1.2;
                height: 200px;
              }
            }
            .avaBoxImg {
              position: absolute;
              top: -15px;
              left: -60px;
              z-index: 2;
            }
          }
          .avt {
            width: 180px;
            height: calc(180px * 1.7);
            position: absolute;
            right: 36px;
            top: -27px;
          }
        }

        .say {
          z-index: 3;
          width: 903.5px;
          height: 378.9px;
          background: url('../../../../../../assets/qzs/say.png') no-repeat;
          background-size: 100% 100%;

          margin-top: 26px;
          position: relative;
          left: -14px;
          padding: 75px 87px;
          font-size: 37.84px;

          .text {
            font-family: Source Han Serif CN;
            font-weight: bold;
            font-size: 28px;
            color: #ffffe0;
            line-height: 46px;

            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            /* autoprefixer: on */
          }
        }
      }
    }
    .mid {
      position: relative;
      .midbg {
        width: @midCardWith;
        height: @cardHeight;
        // background: url('../../../../../../assets/qzs/youngmid.png') no-repeat;
        // background-size: 100% 100%;

        background: #fffbf0;
        border-radius: 27px;
        border: 3px solid #be0c10;

        .trans {
          position: relative;
          top: -131px;

          display: flex;
          align-items: center;
          flex-direction: column;
        }

        .photo {
          margin-top: 40px;
          display: flex;
          width: 87%;
          justify-content: space-between;
          align-items: center;
          .photoItem {
            width: 301.3px;
            > img {
              width: 301.3px;
              height: 201.1px;
            }
            > div {
              text-align: center;

              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 27px;
              color: #62471e;
            }
          }
        }
      }
    }
    .right {
      .rightbg {
        width: @rightCardWith;
        height: @cardHeight;
        background: #fffbf0;
        border-radius: 18px;
        border: 2px solid #be0c10;

        display: flex;
        align-items: center;
        flex-direction: column;
        padding: 20px 0 0 0;
        .text {
          width: 742px;
          height: 546px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 37px;
          color: #62471e;
          line-height: 42px;
          text-align: justify;

          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 13;
          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
          /* autoprefixer: on */

          > p {
            margin: 0;
          }
        }
        .sign {
          position: absolute;
          bottom: 206px;
          right: 184px;
          z-index: 3;
          > img {
            width: 210px;
            height: 70px;
          }
        }
        .logos {
          position: absolute;
          bottom: 128px;
          right: 178px;
          text-align: right;
          z-index: 3;
          font-size: 27px;
          color: #62471e;
          font-family: Microsoft YaHei;
        }
      }
    }
  }

  .bot {
    width: @width;
    height: 366px;
    background: url('../../../../../../assets/qzs/youngbot.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    bottom: 0;
    left: 0;
  }
}

.birthday {
  z-index: 4;
  width: 200px;
  position: absolute;
  left: 52px;
  bottom: 29px;
  font-size: 20px;
  cursor: pointer;
}
.scorllItem {
  width: 350px;
  height: 300px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  .scorllItemph {
    width: 342px;
    height: 242px;
    position: absolute;
    left: 7px;
  }
  .scorllItembg {
    width: 100%;
    height: 100%;
  }
}
.imgs{
  width: 100%;
  padding: 0 20px;
  bottom: 144px;
  position: absolute;
}
.titimg{
  // width: 636px;
  // height: 56px;
}