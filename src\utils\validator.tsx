interface NumberRegClass {
    
}

interface NumberRegConstructorOptions {
    positive: boolean //正数
}

export class NumberReg extends RegExp implements NumberRegClass {

    /**
     * @param  {number} int 整数位
     * @param  {number} decimals 小数位
     * @param  {NumberRegConstructorOptions} options? 参数
     */
    constructor(int: number, decimals: number, options?: NumberRegConstructorOptions) { // 在FormItem的pattern验证中会报错，不晓得为撒子
        const { positive = false } = options || {};
        super(`^(${positive ? '' : '\\-?'})([1-9]\\d{0,${int - 1}}|0)(\\.\\d{0,${decimals - 1}}[1-9])?$`);
    }

    /**
     * @param  {number} int 整数位
     * @param  {number} decimals 小数位
     * @param  {NumberRegConstructorOptions} options? 参数
     */
    static reg(int: number, decimals?: number, options?: NumberRegConstructorOptions): RegExp {
        const { positive = false } = options || {};
        return new RegExp(`^(${positive ? '' : '\\-?'})([1-9]\\d{0,${int - 1}}|0)${decimals ? `(\\.\\d{0,${decimals - 1}}[1-9])` : ''}?$`);
    }

    /**
     * 验证bit位的整数
     * @param  {number} bit 整数位数
     * @returns RegExp
     */
    static int(bit: number): RegExp { //整数
        return new RegExp(`^(\\-?[1-9]\\d{0,${bit - 1}}|0)$`);
    }
}
