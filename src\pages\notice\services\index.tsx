import request from "@/utils/request";
import qs from 'qs';
export function addNotic(params) {
  return request(`/api/notice/addNotic`, {
    method:'POST',
    body:params
  });
}
export function getListByType(params) {
  return request(`/api/notice/getListByType`, {
    method:'POST',
    body:params
  });
}
export function getNoticeByCode(params) {
  return request(`/api/notice/getNoticeByCode`, {
    method:'POST',
    body:params
  });
}

export function findReplyByMessageCode(params) {
  return request(`/api/notice/findReplyByMessageCode`, {
    method:'POST',
    body:params
  });
}

export function delNotice(params) {
  return request(`/api/notice/delNotice`, {
    method:'POST',
    body:params
  });
}
export function stopNotice(params) {
  return request(`/api/notice/stopNotice`, {
    method:'POST',
    body:params
  });
}
export function updateNotice(params) {
  return request(`/api/notice/submitNotice`, {
    method:'POST',
    body:params
  });
}



