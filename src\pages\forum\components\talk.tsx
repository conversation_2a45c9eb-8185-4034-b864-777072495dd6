import React, { Fragment, useState, useRef, useEffect } from 'react';
import { Button, Divider, Tag, Avatar, Comment, Pagination } from 'antd';
import { getSession } from '@/utils/session';
import Tip from '@/components/Tip';
import { feedbackReplyAdd } from '@/pages/forum/services';
import Editors from '@/components/Editor';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import style from './add.less'
import moment from 'moment';
import { DownOutlined, UpOutlined } from '@ant-design/icons';


const index = (props: any) => {
  const { submitFunc, data, pagination, onPageChange } = props;
  const callbackRef: any = useRef();
  const [timeKey, setTimeKey] = useState<any>(+new Date());
  const [callbackComment, setCallbackComment] = useState<any>({});
  const [commentText, setCommentText] = useState('');
  const [listData, setListData] = useState([]);
  const [submitCallbackLoading, setSubmitCallbackLoading] = useState(false);
  const user = getSession('user') || {};

  const ExampleComment = (props: any) => {
    const { item, children } = props;
    let time = _isNumber(item.replyTime) ? moment(item.replyTime).format('YYYY-MM-DD HH:mm:ss') : '';
    return (
      <Fragment>
        <Comment
          actions={[<span key="comment-nested-reply-to" onClick={() => {
            setCallbackComment(item);
          }}>回复</span>]}
          author={<a>{item.itemId == 1 ? item.replyUserName : item.userName} {time}</a>}
          avatar={<Avatar style={{ backgroundColor: '#7265e6', verticalAlign: 'middle', marginRight: 10 }} size="large">{item.itemId == 1 ? item.replyUserName : item.userName}</Avatar>}
          content={
            <div dangerouslySetInnerHTML={{
              __html: item.content,
            }}>
            </div>
          }
        >
          {children}
        </Comment>
      </Fragment>
    )
  };

  const rendrNewComment = (arr) => {
    const showLine = 3;
    if (!_isEmpty(arr)) {
      return (
        <Fragment>
          {
            arr.map((it, index) => {
              const { children, ...other } = it;
              if (!_isEmpty(children)) {
                return <Fragment key={index}>
                  <ExampleComment item={other}>
                    <div style={{ width: '100%', backgroundColor: '#EFEFEF', padding: 8 }}>
                      {
                        (it.open ? children : children.slice(0, showLine)).map((its, index) => {
                          let time = _isNumber(its.replyTime) ? moment(its.replyTime).format('YYYY-MM-DD HH:mm:ss') : '';
                          return (
                            <div key={index}>
                              <div style={{ display: 'flex' }}>
                                <div style={{ marginRight: 4 }}>
                                  <a onClick={() => setCallbackComment(its)}>{its.replyUserName}</a> 回复 {its.userName} :
                                </div>
                                <div style={{ flex: 1 }} className={style.htmls} dangerouslySetInnerHTML={{
                                  __html: its.content,
                                }}></div>
                              </div>
                              <div style={{fontSize:14,color: '#ccc'}}>
                                {time}
                              </div>
                            </div>

                          )
                        })
                      }

                      {
                        children.length > showLine &&
                        <div
                          style={{ width: '100%', cursor: 'pointer', textAlign: 'center', borderTop: '1px solid #ccc', paddingTop: 6 }}
                          key={timeKey}
                          onClick={() => {
                            it.open = !it.open;
                            setListData(listData);
                            setTimeKey(+new Date());
                          }}>
                          {
                            it.open ? <span>收起 <UpOutlined /></span> : <span>更多 <DownOutlined /></span>
                          }
                        </div>
                      }

                    </div>
                  </ExampleComment>
                  <Divider />
                </Fragment>
              } else {
                return <Fragment key={index}>
                  <ExampleComment item={other} />
                  <Divider />
                </Fragment>
              }
            })
          }
        </Fragment>
      )
    }
  };

  useEffect(() => {
    setListData(data);
  }, [JSON.stringify(data)])
  return (
    <Fragment>
      <div>
        <Divider orientation="left">全部评论</Divider>
        {
          _isEmpty(listData) ? <div style={{ textAlign: 'center' }}>暂无</div> : rendrNewComment(listData)
        }
        <div style={{ textAlign: 'right' }}>
          <Pagination size="small" {...pagination} onChange={onPageChange} />
        </div>
        {/* {
        listData.map((it, index) => renderCallBack(it, index))
      } */}
      </div>
      <Divider orientation="left">回复</Divider>
      <div>
        {
          !_isEmpty(callbackComment) &&
          <Fragment>
            回复：
            <Tag
              closable
              color="success"
              visible={!_isEmpty(callbackComment)}
              onClose={() => setCallbackComment({})}
            >
              {callbackComment.itemId == 1 ? callbackComment.replyUserName : callbackComment.userName}
            </Tag>
          </Fragment>
        }
        <div className={style.callback}>
          <Avatar style={{ backgroundColor: '#7265e6', verticalAlign: 'middle', marginRight: 10 }}
            size="large">{user['name'] || ''}</Avatar>
          <div className={style.callbackInput}>
            <Editors
              id={'callback'}
              ref={callbackRef}
              onChange={(val) => {
                setCommentText(val);
              }}
            />
          </div>
        </div>
        <div style={{ textAlign: 'right', marginTop: 10 }}>
          <Button
            type={'primary'}
            loading={submitCallbackLoading}
            disabled={_isEmpty(commentText)}
            onClick={async () => {
              setSubmitCallbackLoading(true);
              const code = await submitFunc({ content: commentText, message: callbackComment });
              setSubmitCallbackLoading(false);
              if (code == 0) {
                setCommentText('');
                callbackRef.current.state.editor.txt.clear();
                setCallbackComment({});
              }
            }}>发送</Button>
        </div>
      </div>
    </Fragment>
  )
};
export default index;
