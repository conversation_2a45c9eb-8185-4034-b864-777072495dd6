import request from '@/utils/request';
import { jsonToTree, treeToList } from '@/utils/method';

const configTreeTest = {
  isLeaf: 'isLeaf',
  getTrees: async (orgCodes = ['052002109'], oldData = []) => {
    function getTree(params) {
      return request(`/api/org/getOrgTree`, {
        method: 'POST',
        body: params,
      });
    }
    const res = await getTree({
      data: {
        excludeOrgCodeList: [],
        orgCodeList: orgCodes,
        subordinate: 1,
      },
    });
    if (res.code == 0) {
      let data = res.data.map((it) => ({ ...it, key: it.orgCode }));
      if (oldData.length > 0) {
        data = data.filter((it, index) => index != 0);
      }
      let newData = jsonToTree(
        [...data, ...treeToList(oldData)],
        'parentCode',
        'code',
        'D574179CC0D947A88E4135960F58C001',
      );
      return newData;
    }
    return [];
  },
  getParentCodes: (current) => {
    let root = '052002109';
    let lv = (current.length - root.length) / 3;
    let arr: any = [];
    for (let i = 0; i < lv; i++) {
      let a = current.substring(0, current.length - i * 3);
      arr.push(a);
    }
    return arr;
  },
};

export default {
  configTreeTest,
};
