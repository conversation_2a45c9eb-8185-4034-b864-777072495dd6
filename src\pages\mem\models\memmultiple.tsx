import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {getList,addMemMultiple,updateMemMultiple,findByCode,delMemMultiple} from '../services/memMultiple';
import {findByNameAndIdcard} from '@/pages/mem/services';
import { getSession } from '@/utils/session';
import { changeListPayQuery } from '@/utils/method.js';
const memAbroad = modelExtend(listPageModel,{
  namespace: "memMultiple",
  state:{
    multipleInfo:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if( pathname === '/mem/multiple'){
          const org=getSession('org') || {};
          const defaultParas={
            pageNum:1,
            pageSize:10,
          };
          dispatch({
            type:'getList',
            payload:{
              data:{
                grapPung:0,
                memOrgCode:org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put , select}) {
      const {filter,memName}=yield select(state=>state['memMultiple']);
      payload['data']={...payload['data'],...filter,memName};
      const {data={}} = yield call(getList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    // 查找人员
    *findMem({ payload }, { call, put }) {
      let res = yield call(findByNameAndIdcard,payload);
      return res;
    },
    *findMemByCode({ payload }, { call, put }) {
      let res = yield call(findByNameAndIdcard,payload);
      return res;
    },
    *getMultipleInfo({ payload }, { call, put }){
      let res = yield call(findByCode,payload);
      const {data = {}} = res || {};
      yield put({
        type: 'updateState',
        payload: {
          multipleInfo:data
        }
      });
      return res;
    },
    *save({ payload }, { call, put }) {
      const {type='',data={}} = payload;
      let res;
      if(type === 'add'){
        res = yield call(addMemMultiple,{data});
      }else {
        res = yield call(updateMemMultiple,{data});
      }
      return res;
    },
    // 删除
    *del({ payload }, { call, put }) {
      const res = yield call(delMemMultiple, payload);
      return res;
    },
    //清除
    *clear({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          list:[],
          pagination:{},
          memName:undefined,
          filter:{},
        }
      })
    },
  }
});
export default memAbroad;
