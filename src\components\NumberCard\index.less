.noPaddingCard {
  :global .ant-card-body {
    padding: 0;
  }
}
.card {
  background-color: white;
}
.cardFlex{
  display: table;
  width: 100%;
  //display: flex;
  //justify-content: center;
  //flex-direction: row;
  //min-height: 140px;
  .cardCell {
    //display: table-cell;
    vertical-align: middle;
    width: 100%;
    .icon {
      float: left;
      width: 64px;
      min-height: 64px;
      line-height: 64px;
      border-radius: 50%;
      text-align: center;
      margin: 10px 10px 10px 20px;
      .imgBox {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        overflow: hidden;
        size: 64px;
        .img {
          width: 100%;
          height: 100%;
          position: relative;
          top: -2px;
        }
      }
    }
    .info {
      width: calc(100% - 110px);
      float: left;
      margin: 0 10px 0 5px;
      .words {
        margin: 5px 0 0 0;
        font-size: 16px;
      }
      .countUpNmber {
        font-size: 24px;
        color: black;
      }
    }
  }
}

@media screen and (max-width: 1470px)  {
  .noPaddingCard {
    .card {
      .cardFlex {
        .cardCell {
          .icon {
            width: 60px;
            min-height: 60px;
            line-height: 60px;
            margin: 5px 5px 5px 10px;
            .imgBox {
              width: 60px;
              height: 60px;
            }
          }
          .info {
            width: calc(100% - 90px);
          }
        }
      }
    }
  }
}
