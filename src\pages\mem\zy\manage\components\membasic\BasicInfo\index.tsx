import React, { Fragment } from 'react';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Row,
  Col,
  Input,
  Radio,
  DatePicker,
  Switch,
  Alert,
  Button,
  Popconfirm,
  Select,
  InputNumber,
  Spin,
  Modal
} from 'antd';
import styles from './index.less';
import Notice from 'src/components/Notice';
import OrgSelect from '@/components/OrgSelect';
import Date from '@/components/Date';
import SearchOrg from '@/components/SearchOrg';
import DictTreeSelect from 'src/components/DictTreeSelect';
import DictSelect from 'src/components/DictSelect';
import Tip from '@/components/Tip';
import {
  findDictCodeName,
  unixMoment,
  timeSort,
  getIdCardInfo,
  correctIdcard,
} from '@/utils/method.js';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _iisArray from 'lodash/isArray';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import _isNumber from 'lodash/isNumber';
import _trim from 'lodash/trim';
import { root, rootParent } from '@/common/config';
import { getSession } from '@/utils/session';
import DictArea from '@/components/DictArea';
import { formLabel } from '@/utils/method';
import YN from '@/components/YesOrNoSelect';
import UnitSelect from '@/components/UnitSelect';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import { getUnitByOrg } from '@/services';
import SearchUnit from '@/components/SearchUnit';
import { treeToList, _history } from '@/utils/method.js';
import CheckDictIsRight from '@/components/CheckDictIsRight';
import { LockMsg } from '@/pages/user/lock';
import { lockArr } from '@/pages/mem/manage/components/membasic/Lock';
import UploadComp, { getInitFileList, fitFileUrlForForm } from '@/components/UploadComp';
import LoadTreeSelect, { config } from '@/components/LoadTreeSelect';
import { getUnitName, normalList } from '@/services';
import { validateLength, validateMobilePhoneNumber } from '@/utils/formValidator';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      d28Disabled: ['14', '16'],
      isOutCity: false,
      hasLost: false,
      beFull: true,
      iSEditMemType: false,
      _basicInfo: {},
      getUnitList: this.getUnitList,
      unitList: [],
      d154CodeNoDraw: [],
    };
  }
  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    const state = {};
    const { memBasic: { basicInfo = {} } = {} } = nextProps;
    const { _basicInfo = {}, getUnitList } = prevState;

    if (!_isEqual(basicInfo?.orgCode, _basicInfo?.orgCode)) {
      state['_basicInfo'] = basicInfo;
      if (basicInfo?.orgCode) {
        getUnitList(basicInfo?.orgCode);
      }
      // // 这点需要对form表单重新赋值，因为194和195传入的值和返回的值会不一样
      // state['d194CodeSatate'] = undefined;
      // state['d195CodeSatate'] = undefined;
      // state['d194CodeKey'] = moment().valueOf();
      // state['d195CodeKey'] = moment().valueOf();
      // nextProps.form.setFieldsValue({
      //   ...basicInfo
      // })
    }
    if (!_isEqual(basicInfo, _basicInfo)) {
      state['_basicInfo'] = basicInfo;
      const { d08Code = '', d18Code = '', d11Code = '', d48Code = '', d48Name = '' } = basicInfo;
      if (d08Code === '2') {
        state['beFull'] = false;
        state['d28Disabled'] = ['11', '12', '13'];
      }
      if (!_isEmpty(d18Code) && d18Code !== '0') {
        state['hasLost'] = true;
      } else {
        state['hasLost'] = false;
      }
      if (!_isEmpty(d08Code)) {
        state['iSEditMemType'] = true;
      } else {
        state['iSEditMemType'] = false;
      }
      if (d11Code === '5') {
        state['isOutCity'] = true;
      } else {
        state['isOutCity'] = false;
      }
      if (d48Code) {
        state['area'] = d48Name;
      }
      getUnitList(basicInfo?.orgCode, basicInfo);
    }

    if (!_isEqual(basicInfo?.id, _basicInfo?.id)) {
      // 初始化 知识分子情况 不渲染的选项
      if (!_isEmpty(basicInfo['d154Code'])) {
        let arr = basicInfo['d154Code'].split(',');
        if (arr.includes('0')) {
          state['d154CodeNoDraw'] = ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B'];
        } else if (arr.includes('B')) {
          state['d154CodeNoDraw'] = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A'];
        } else if (
          ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A'].includes(arr[arr.length - 1])
        ) {
          state['d154CodeNoDraw'] = ['0', 'B'];
        } else {
          state['d154CodeNoDraw'] = [];
        }
      } else {
        state['d154CodeNoDraw'] = [];
      }
    }

    return state;
  };
  getUnitList = async (orgCode, basicInfo = {}) => {
    const {
      code: resCode = 500,
      data: {
        d194Code = '',
        isLegal = '',
        unitOrgLinkedList = [],
        unitInformation = undefined,
        unitInformationCode = undefined,
        d04Code = undefined,
      } = {},
    } = await getUnitByOrg({ orgCode });
    if (resCode === 0) {
      this.props.form.setFieldsValue({
        readingCollege: basicInfo['readingCollege']
          ? basicInfo['readingCollege']
          : unitInformation
            ? unitInformation
            : _get(unitOrgLinkedList, '[0].unitName', undefined),
      });
      this.setState({
        unitInformation,
        unitList: unitOrgLinkedList,
        unitInformationCode,
        unitInformationD04Code: d04Code,
        isLegal: isLegal,
        unitInformationd194Code: d194Code,
      });
    }
  };
  d08CodeOnChange = (e) => {
    const { target: { value = '' } = {} } = e;
    const { form } = this.props;
    let arr: Array<string> = [];
    if (value) {
      switch (value) {
        case '1':
          arr = ['14', '16'];
          this.setState({ beFull: true });
          break;
        case '2':
          arr = ['11', '12', '13'];
          form.setFieldsValue({ fullMemberDate: undefined });
          this.setState({ beFull: false });
          break;
        default:
          break;
      }
      this?.SelectMem?.clearAll && this['SelectMem'].clearAll();
      form.setFieldsValue({ d28Code: undefined });
    }
    this.setState({
      d28Disabled: arr,
    });
  };
  d11OnChange = (code) => {
    const { key = '' } = code;
    if (key === '5') {
      this.setState({ isOutCity: true });
    } else {
      this.setState({ isOutCity: false });
    }
  };
  d18OnChange = (code) => {
    if (code && code['key'] !== '0') {
      this.setState({ hasLost: true });
    } else {
      this.setState({ hasLost: false });
      this.props.form.setFieldsValue({ lostContactDate: undefined });
    }
  };
  // 身份证
  getIDinfo = (e) => {
    const { target: { value = '' } = {} } = e || {};
    let info = getIdCardInfo(value);
    if (!_isEmpty(value) && info !== 'Error') {
      // 大陆身份证自动赋值，港澳台身份证不自动赋值
      if (`${value}`.length === 18 || `${value}`.length === 15) {
        this.setState({
          area: info[0],
        });
        this.props.form.setFieldsValue({
          sexCode: info[2] === '女' ? '0' : '1',
          birthday: info[1] ? moment(info[1], 'YYYY-MM-DD') : undefined,
          d48Code: info[3],
        });
      }
    }
  };
  validatorIdcard = async (rule, value, callback) => {
    if (_isEmpty(value)) {
      callback('身份证必填');
    }

    // if (value && value.length !== 18 && process.env.idCheck != 'false') {
    //   callback('身份证应该为18位');
    // }
    if (value == '000000000000000000' || value == '111111111111111111') {
      callback();
    } else {
    }
    if (getIdCardInfo(value) === 'Error') {
      callback('身份证格式错误,请核对身份证图片');
    } else {
      callback();
    }
  };

  // 姓名校验：不能有空格和·以外的字符
  nameValidator = (rule, value, callback) => {
    let reg = /(^[\u4e00-\u9fa5]{1}[\u4e00-\u9fa5·]{0,18}[\u4e00-\u9fa5]{1}$)/;
    if (reg.test(value)) {
      validateLength([rule, value, callback], 16, 50);
      // callback();
    } else {
      callback('只能是汉字，不能有空格或特殊字符');
    }
  };

  // 时间限制
  disabledTomorrow = (current) => {
    return current && current > moment().endOf('day');
  };
  // 时间先后顺序判断
  TemporalOrder = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { applyDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr1 = [
        { text: '出生日期', value: value },
        { text: '申请入党时间', value: applyDate },
      ];
      if (timeSort(timeArr1, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs || {})) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder1 = (rule, value, callback) => {
    const { memBasic: { basicInfo = {} } = {} } = this.props;
    if (_isEmpty(basicInfo)) {
      if (value) {
        const { form } = this.props;
        const { birthday = undefined, activeDate = undefined } = form.getFieldsValue() || {};
        const errs = form.getFieldsError();
        let text = '';
        const backFunc = (val) => {
          text = val;
        };
        let timeArr1 = [
          { text: '出生日期', value: birthday },
          { text: '申请入党时间', value: value },
          { text: '确定积极分子时间', value: activeDate },
        ];
        if (timeSort(timeArr1, backFunc)) {
          callback(text);
        } else {
          callback();
          for (let obj of Object.entries(errs)) {
            !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
          }
        }
      }
    }
    callback();
  };
  TemporalOrder2 = (rule, value, callback) => {
    const { memBasic: { basicInfo = {} } = {} } = this.props;
    if (_isEmpty(basicInfo)) {
      if (value) {
        const { form } = this.props;
        const { applyDate = undefined, objectDate = undefined } = form.getFieldsValue() || {};
        const errs = form.getFieldsError();
        let text = '';
        const backFunc = (val) => {
          text = val;
        };
        let timeArr2 = [
          { text: '申请入党时间', value: applyDate },
          { text: '确定积极分子时间', value: value },
          { text: '确定发展对象时间', value: objectDate },
        ];
        if (timeSort(timeArr2, backFunc)) {
          callback(text);
        } else {
          callback();
          for (let obj of Object.entries(errs)) {
            !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
          }
        }
      }
    }
    callback();
  };
  TemporalOrder3 = (rule, value, callback) => {
    const { memBasic: { basicInfo = {} } = {} } = this.props;
    if (_isEmpty(basicInfo)) {
      if (value) {
        const { form } = this.props;
        const { joinOrgDate = undefined, activeDate = undefined } = form.getFieldsValue() || {};
        const errs = form.getFieldsError();
        let text = '';
        const backFunc = (val) => {
          text = val;
        };
        let timeArr2 = [
          { text: '确定积极分子时间', value: activeDate },
          { text: '确定发展对象时间', value: value },
          { text: '接收预备党员时间', value: joinOrgDate },
        ];
        if (timeSort(timeArr2, backFunc)) {
          callback(text);
        } else {
          callback();
          for (let obj of Object.entries(errs)) {
            !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
          }
        }
      }
    }
    callback();
  };
  TemporalOrder4 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { objectDate = undefined, fullMemberDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr2 = [
        { text: '确定发展对象时间', value: objectDate },
        { text: '接收预备党员时间', value: value },
        { text: '成为正式党员日期', value: fullMemberDate },
      ];
      let valuee = !_isEmpty(value) && value.valueOf();
      if (
        !_isEmpty(valuee) &&
        ((valuee >= moment('1927-04-27', 'YYYY-MM-DD').valueOf() &&
          valuee <= moment('1945-04-22', 'YYYY-MM-DD').valueOf()) ||
          (valuee >= moment('1969-04-01', 'YYYY-MM-DD').valueOf() &&
            valuee <= moment('1977-08-11', 'YYYY-MM-DD').valueOf()) ||
          (valuee >= moment('1921-07-01', 'YYYY-MM-DD').valueOf() &&
            valuee <= moment('1923-06-09', 'YYYY-MM-DD').valueOf()))
      ) {
        callback();
      } else {
        if (timeSort(timeArr2, backFunc)) {
          callback(text);
        } else {
          callback();
          for (let obj of Object.entries(errs)) {
            !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
          }
        }
      }
    }
    callback();
  };
  TemporalOrder5 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { joinOrgDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };

      let timeArr2 = [
        { text: '接收预备党员时间', value: joinOrgDate },
        { text: '成为正式党员日期', value: value },
      ];
      if (timeSort(timeArr2, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  // 保存
  submit = () => {
    const { memBasic: { basicInfo = {} } = {} } = this.props;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if (val['recoveryBasis'] && typeof val['recoveryBasis'] == 'object') {
          val['recoveryBasis'] = fitFileUrlForForm(val['recoveryBasis']);
        }
        if (val['name'] != basicInfo['name'] || val['idcard'] != basicInfo['idcard']) {
          let result = await correctIdcard(val['name'], val['idcard']);
          if (result['code'] != '200') {
            this.props.form.setFields({
              idcard: {
                value: val['idcard'],
                errors: [
                  new Error(
                    '经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。',
                  ),
                ],
              },
            });
            Tip.error(
              '操作提示',
              '经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。',
            );
            return;
          } else {
            val['idCardReason'] = result['reason'];
            val['idCardReasonName'] = result['reasonName'];
          }
        }
        let timeArr = [
          'birthday',
          'applyDate',
          'activeDate',
          'objectDate',
          'fullMemberDate',
          'lostContactDate',
          'joinOrgPartyDate',
          'joinOrgDate',
          'stopPartyDate',
          'recoverPartyDate',
          'outlandTransferInYear',
          'enterSchoolDate',
          'replenishInputDate',
        ];
        val = unixMoment(timeArr, val);
        //bool改为int
        [
          'isDispatch',
          'isFarmer',
          'hasUnitStatistics',
          'hasUnitProvince',
          'hasExSituPovertyAlleviation',
        ].map((item) => {
          val[`${item}`] = val[`${item}`] == 1 ? 1 : val[`${item}`] == 0 ? 0 : undefined;
        });

        // const checkInfo = this['CheckDictIsRight'].checkDictValue({
        //   dictList:[{formKey:'readingProfessional',dictName:"dict_d88" },'d27'],
        //   val,
        // });
        // if(!checkInfo?.flag){
        //   this.props.form.setFields({
        //     [checkInfo?.fromKey]: { errors: [new Error('请重新审核此项数据是否选择正确')] }
        //   });
        //   return;
        // }

        // 增加字典表的name
        val = findDictCodeName(
          [
            'd49',
            'd18',
            'd19',
            'd20',
            'd21',
            'd07',
            'd09',
            'd11',
            'd27',
            'd126',
            'd28',
            'd06',
            'd89',
            'd04',
            'd88',
            'readingProfessional',
            'd135',
            'd136',
            'd145',
            'd194',
            'd195',
          ],
          val,
          basicInfo,
        );
        val['d08Name'] = val['d08Code'] === '1' ? '正式党员' : '预备党员';
        val['sexName'] = val['sexCode'] === '1' ? '男' : '女';

        // val['unitCode'] =typeof val['unitCode'] == 'object' ? _get(val,'unitCode[0].code') : val?.unitCode

        // 其他政治面貌为空时传空字符串覆盖数据库旧值
        if (_isEmpty(val.d89Code)) {
          val.d89Code = '';
          val.d89Name = '';
        }

        // 增加组织zbcode
        ['orgCode'].map((item) => {
          if (!_isEmpty(_get(val, `${item}[0]`, []))) {
            if (_isEmpty(basicInfo)) {
              val[`orgZbCode`] = val[`${item}`][0]['zbCode'];
              val['memOrgCode'] = val[`${item}`][0]['orgCode'];
              val[`${item}`] = val[`${item}`][0]['code'];
            } else {
              if (_iisArray(val[`${item}`]) && val[`${item}`][0] !== basicInfo[`${item}`]) {
                val[`orgZbCode`] = val[`${item}`][0]['zbCode'];
                val['memOrgCode'] = val[`${item}`][0]['orgCode'];
                val[`${item}`] = val[`${item}`][0]['code'];
              } else {
                val[`orgZbCode`] = basicInfo['orgZbCode'];
                val['memOrgCode'] = basicInfo['memOrgCode'];
              }
            }
          }
        });
        ['branchOrgKey'].map((item) => {
          if (!_isEmpty(_get(val, `${item}[0]`, []))) {
            if (_isEmpty(basicInfo)) {
              val['branchOrgCode'] = val[`${item}`][0]['orgCode'];
              val['branchOrgZbCode'] = val[`${item}`][0]['zbCode'];
              val['branchOrgName'] = val[`${item}`][0]['name'];
              val[`${item}`] = val[`${item}`][0]['code'];
            } else {
              if (_iisArray(val[`${item}`]) && val[`${item}`][0] !== basicInfo[`${item}`]) {
                val['branchOrgCode'] = val[`${item}`][0]['orgCode'];
                val['branchOrgZbCode'] = val[`${item}`][0]['zbCode'];
                val['branchOrgName'] = val[`${item}`][0]['name'];
                val[`${item}`] = val[`${item}`][0]['code'];
              } else {
                val['branchOrgZbCode'] = basicInfo['branchOrgZbCode'];
                val['branchOrgCode'] = basicInfo['branchOrgCode'];
                val['branchOrgName'] = basicInfo['branchOrgName'];
              }
            }
          }
        });

        //  知识分子情况 数组改为字符串
        if (!_isEmpty(val['d154Code'])) {
          if (typeof val['d154Code'] === 'object') {
            let nameArr: any = [];
            let codeArr: any = [];
            val['d154Code'].map((item: any, index) => {
              const { key = '', name = '' } = item;
              nameArr.push(name);
              codeArr.push(key);
            });
            val['d154Name'] = nameArr.toString();
            val['d154Code'] = codeArr.toString();
          } else {
            val['d154Name'] = basicInfo['d154Name'];
          }
        }

        if (val['d195Code'] == 'V0000') {
          val['d195Name'] = '无';
        }

        // 中间交换区
        if (val['hasUnitStatistics'] == 1 && val['_d04Code']) {
          val['d04Code'] = this.state.unitInformationD04Code;
        }
        if (val['hasUnitStatistics'] == 1 && val['__d04Code']) {
          val['d04Code'] = val['__d04Code'];
        }
        val['middleUnitCode'] =
          typeof val['middleUnitCode'] == 'object'
            ? val['middleUnitCode']?.code
            : val['middleUnitCode'];
        if (val['middleUnitCode'] && val.d09Code != '13') {
          val['d194Code'] = val['_d194Code'];
          val['d194Name'] = val['_d194Name'];
          val['d195Code'] = val['_d195Code'];
          val['d195Name'] = val['_d195Name'];
        }
        // 补录 省外组织 只有新增才有
        // code: "30E74C68DBA841D397C76E3C85145D6C"
        // contactPhone: "0851-28612020"
        // contacter: "张火友"
        // createTime: "2021-12-05T16:00:00.000+0000"
        // d01Code: "631"
        // deleteTime: null
        // esId: "1451172820533157888"
        // exchangeKey: "25abbbc1078549f9"
        // id: 246970
        // name: "中共遵义市汇川区大连路街道航天社区414支部委员会"
        // orgCode: "052002102113101102"
        // orgId: null
        // orgType: 3
        // parentCode: "AF41227C69484069ABD446371782E936"
        // parentName: null
        // pinyin: "zgzyshcqdlljdhtsq414zbwyh"
        // shortName: "遵义市汇川区大连路街道航天社区414离退休党支部"
        // updateTime: "2021-12-08T16:00:00.000+0000"
        // zbCode: "1451172820534509568"

        if (!basicInfo?.code) {
          val['provinceOrgName'] =
            typeof val['provinceOrgCode'] == 'object'
              ? val['provinceOrgCode'].name
              : basicInfo.provinceOrgName;
          val['provinceOrgCode'] =
            typeof val['provinceOrgCode'] == 'object'
              ? val['provinceOrgCode'].code
              : basicInfo.provinceOrgCode;
        }
        if (!_isEmpty(val.d09Code)) {
          let _key = val.d09Code;
          //是否劳务派遣工默认为否
          if (
            !(
              `${_key}`.startsWith('016') ||
              `${_key}`.startsWith('025') ||
              `${_key}` == '0313' ||
              `${_key}` == '0323' ||
              `${_key}` == '0333'
            )
          ) {
            val['isDispatch'] = '0';
            val['isFarmer'] = '0';
          }
        }
        if (_isEmpty(val.d194Code)) {
          val.d194Code = '';
          val.d194Name = '';
        }
        if (_isEmpty(val.d195Code)) {
          val.d195Code = '';
          val.d195Name = '';
        }

        const canEdit = this.showGUOMINGJINGJI();
        if (!canEdit) {
          val.d194Code = '';
          val.d194Name = '';
          val.d195Code = '';
          val.d195Name = '';
        }

        if (`${val.d09Code}`.startsWith('3') || `${val.d09Code}`.startsWith('4')) {
          val.d194Code = 'U';
          val.d194Name = '其他';
          val.d195Code = 'V0000';
          val.d195Name = '无';
        }
        console.log('val', val);
        const res = await this.props.dispatch({
          type: 'memBasic/zysave',
          payload: {
            data: { ...val },
            type: _isEmpty(basicInfo?.code) ? 'add' : 'edit',
          },
        });
        const { code = 500, data = {} } = res;
        if (code === 0) {
          Tip.success('操作提示', basicInfo['code'] ? '修改成功' : '新增成功');
          //更新model
          this.props.dispatch({
            type: 'memBasic/updateState',
            payload: {
              basicInfo: data,
            },
          });
          this.setState(
            {
              d194CodeKey: +moment().valueOf(),
              d194CodeSatate: undefined,
              d195CodeKey: +moment().valueOf(),
              d195CodeSatate: undefined,
            },
            () => {
              this.props.form.setFieldsValue({
                ...data,
              });
            },
          );
          // 更新列表
          const { orgCode } = getSession('org') || { orgCode: '' };
          const { query: { pageNum = 1, pageSize = 10 } = {}, pathname } = _history.location;
          !_isEmpty(orgCode) &&
            pathname.startsWith('/mem/manage') &&
            this.props.dispatch({
              type: 'memBasic/getList',
              payload: {
                data: {
                  pageNum: basicInfo['code'] ? pageNum : 1,
                  pageSize: 10,
                  searchType: 1,
                  memOrgCode: orgCode,
                },
              },
            });
        }
      }
    });
  };
  validatorHomeAddress = (rule, value, callback) => {
    if (value && value.length < 8) {
      callback('家庭住址长度不能少于8字符');
    }
    callback();
  };

  getDictValue = (formKey) => {
    const { getFieldValue } = this.props.form;
    let obj = getFieldValue(formKey);
    let val = obj;
    if (typeof obj == 'object') {
      val = obj['key'];
    }
    return val;
  };

  renderAddNewItems = () => {
    const { tipMsg = {} } = this.props;
    const { getFieldDecorator } = this.props.form;
    const { basicInfo = {} } = this.props.memBasic || {};

    // 往年漏统今年补录       需要填写20字以上原因说明
    // 本年省内转入，转出组织前期已转出 ，省综合党务管理系统中已无该党员        需要匹配转出组织
    // 本年省外转入，已在全国党员管理信息系统中接收           需要填写转出组织
    // 停止党籍后恢复党籍         需要输入停止党籍时间和恢复党籍时间
    // 补录时，入党申请时间，确定积极分子时间，发展对象时间为可编辑，非必填
    return (
      <Fragment>
        <Col span={12}>
          <FormItem label={formLabel('补录类型', tipMsg['d135Code'])} {...formItemLayout}>
            {getFieldDecorator('d135Code', {
              initialValue: basicInfo['d135Code'],
              rules: [{ required: true, message: '请选择补录类型' }],
            })(
              <DictTreeSelect
                backType={'object'}
                initValue={basicInfo['d135Code']}
                codeType={'dict_d135'}
                placeholder={'补录类型'}
                parentDisable={true}
                filter={(data) => {
                  return data.filter((e) => e.key == '1' || e.key == '4');
                }}
              />,
            )}
          </FormItem>
        </Col>

        {(function (_this) {
          let d135Code = _this.getDictValue('d135Code');
          console.log(d135Code, 'd135Coded135Coded135Coded135Code');
          // 补录类型选择1时，加个补充录入时间replenishInputDate
          if (d135Code === '1') {
            return (
              <Fragment>
                <Col span={12}>
                  <FormItem
                    label={formLabel('补充录入时间', tipMsg['replenishInputDate'])}
                    {...formItemLayout}
                  >
                    {getFieldDecorator('replenishInputDate', {
                      rules: [
                        { required: true, message: '补充录入时间' },
                        // { validator: this.TemporalOrder1 },
                      ],
                      initialValue:
                        basicInfo['replenishInputDate'] != undefined
                          ? moment(basicInfo['replenishInputDate'])
                          : undefined,
                    })(<Date key={Math.random()} />)}
                  </FormItem>
                </Col>
                <Col span={24}>
                  <FormItem
                    label={formLabel('补充录入原因', tipMsg['replenishInputReason'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('replenishInputReason', {
                      initialValue: basicInfo['replenishInputReason'],
                      rules: [
                        { required: true, message: '请选择补录类型' },
                        {
                          validator: (rule, value, callback) => {
                            if (value && value.length < 20) {
                              callback('原因不能少于20字');
                            }
                            callback();
                          },
                        },
                      ],
                    })(<Input.TextArea rows={3} />)}
                  </FormItem>
                </Col>
              </Fragment>
            );
          }
          if (d135Code === '2') {
            return (
              <Col span={12}>
                <FormItem
                  label={formLabel('省内转入组织', tipMsg['provinceOrgCode'])}
                  {...formItemLayout}
                >
                  {getFieldDecorator('provinceOrgCode', {
                    rules: [
                      { required: true, message: '省内转入组织' },
                      // { validator: this.TemporalOrder1 },
                    ],
                    initialValue: basicInfo['provinceOrgCode'],
                  })(
                    <SearchOrg
                      backType={'object'}
                      params={{ orgTypeList: ['3', '4'] }}
                      style={{ width: '100%' }}
                    />,
                  )}
                </FormItem>
              </Col>
            );
          }
          if (d135Code === '3') {
            return (
              <Col span={12}>
                <LongLabelFormItem
                  label={'省外转出组织名称'}
                  required={true}
                  code={'outsideProvinceName'}
                  tipMsg={tipMsg}
                  formItemLayout={formItemLayout}
                  formItem={(formItemLayout, code) => {
                    return (
                      <FormItem {...formItemLayout}>
                        {getFieldDecorator(code, {
                          rules: [{ required: true, message: '省外转出组织名称' }],
                          initialValue: basicInfo[code],
                        })(<Input />)}
                      </FormItem>
                    );
                  }}
                />
              </Col>
            );
          }
          if (d135Code === '4') {
            return (
              <Fragment>
                <Col span={12}>
                  <FormItem
                    label={formLabel('停止党籍时间', tipMsg['stopPartyDate'])}
                    {...formItemLayout}
                  >
                    {getFieldDecorator('stopPartyDate', {
                      rules: [
                        { required: true, message: '停止党籍时间' },
                        // { validator: this.TemporalOrder1 },
                      ],
                      initialValue:
                        basicInfo['stopPartyDate'] != undefined
                          ? moment(basicInfo['stopPartyDate'])
                          : undefined,
                    })(<Date disabled={basicInfo['code']} key={Math.random()} />)}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label={formLabel('恢复党籍时间', tipMsg['recoverPartyDate'])}
                    {...formItemLayout}
                  >
                    {getFieldDecorator('recoverPartyDate', {
                      rules: [{ required: true, message: '恢复党籍时间' }],
                      initialValue:
                        basicInfo['recoverPartyDate'] != undefined
                          ? moment(basicInfo['recoverPartyDate'])
                          : undefined,
                    })(<Date disabled={basicInfo['code']} />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={formLabel('工作单位及职务', tipMsg['workPost'])}
                    {...formItemLayout}
                  >
                    {getFieldDecorator('workPost', {
                      rules: [{ required: true, message: `请填写工作单位及职务` }],
                      initialValue: basicInfo['workPost'],
                    })(<Input />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={formLabel('恢复党籍依据', tipMsg['recoveryBasis'])}
                    {...formItemLayout}
                  >
                    {getFieldDecorator('recoveryBasis', {
                      rules: [{ required: true, message: `请上传恢复党籍依据` }],
                      initialValue: getInitFileList(basicInfo['recoveryBasis']),
                    })(
                      <UploadComp maxLen={1} files={getInitFileList(basicInfo['recoveryBasis'])} />,
                    )}
                  </FormItem>
                </Col>
              </Fragment>
            );
          }
        })(this)}
      </Fragment>
    );
  };
  phoneValidator = (rule, value, callback) => {
    validateMobilePhoneNumber(rule, value, callback);
    // if(value){
    //   let fixedPhone = /0\d{2,3}-\d{7,8}/;
    //   let mobilePhone = /1[3-9]\d{9}/;
    //   if(fixedPhone.test(value) || mobilePhone.test(value)){
    //     return Promise.resolve()
    //   }else{
    //     return Promise.reject('请输入格式正确的固定电话(如：023-12345678)或手机号(11位数字)')
    //   }
    // }
    // return Promise.reject()
  };
  d194Change = async (p) => {
    let newCode = p;
    if (typeof p === 'object') {
      newCode = p?.key || undefined;
    }
    const res = await normalList({
      data: {
        tableCode: 'ccp_unit',
        colCode: 'd194Code',
        compareColCode: 'd195Code',
        colValue: newCode,
      },
    });
    if (res.code == 0 && !_isEmpty(res.data)) {
      let key = Object.keys(res.data)?.[0];
      let name = res.data[key];
      this.setState({
        d195CodeSatate: key,
        d195CodeKey: moment().valueOf(),
      });
      this.props.form.setFieldsValue({
        d195Code: key,
        d195Name: name,
      });
    }
  };
  showGUOMINGJINGJI = () => {
    const { memBasic: { basicInfo = {} } = {} } = this.props;
    // 人事关系是否在党组织关联单位内
    let val1 = this.props.form.getFieldValue('hasUnitStatistics');
    if (val1 == undefined) {
      val1 = basicInfo['hasUnitStatistics'];
    }
    // 人事关系所在单位是否省内单位
    let val = this.props.form.getFieldValue('hasUnitProvince');
    if (val == undefined) {
      val = basicInfo['hasUnitProvince'];
    }
    const hasChangedHasUnitStatistics =
      basicInfo['hasUnitStatistics'] != this.props.form.getFieldValue('hasUnitStatistics');
    const hasChangedHasUnitProvince =
      basicInfo['hasUnitProvince'] != this.props.form.getFieldValue('hasUnitProvince');
    let hasChangedStatisticalUnit = false;
    // 当第一个值（人事关系是否在党组织关联单位内）改变，直接隐藏
    let hasChanged = hasChangedHasUnitStatistics;
    // 当第一个值未变且为否 判断第二个值（人事关系所在单位是否省内单位）改变
    if (!hasChangedHasUnitStatistics && val1 == 0 && hasChangedHasUnitProvince) {
      hasChanged = true;
    }

    // 同“人事关系所在单位类别”显示逻辑一样
    let flag = val1 == 0 && val == 0;

    let unitInfo: any = {};
    // 有几种获取单位的情况
    // 1 人事关系是在党组织关联单位内
    if (val1 == 1) {
      //所在党支部
      let org = this.props.form.getFieldValue('orgCode');
      let d01Code: any = undefined;
      if (typeof org == 'string') {
        d01Code = basicInfo['d01Code'];
      } else {
        d01Code = _get(org, '[0].d01Code', undefined);
      }
      let isLianhe = (d01Code == '632' || d01Code == '932' || d01Code == '634') && d01Code;
      if (isLianhe) {
        //  1.2 是下拉框选择的单位
        let unitcode =
          this.props.form.getFieldValue('statisticalUnit') || basicInfo['statisticalUnit'];
        let find = this.state?.unitList?.find?.((it) => it.unitCode == unitcode);
        unitInfo = find;

        // 如果改变下拉框值，隐藏
        if (unitcode != basicInfo['statisticalUnit']) {
          hasChangedStatisticalUnit = true;
        }
      } else {
        //  1.1 只有一个单位
        unitInfo = {
          unitInformation: this.state.unitInformation,
          unitInformationCode: this.state.unitInformationCode,
          d04Code: this.state.unitInformationD04Code,
          isLegal: this.state.isLegal,
          d194Code: this.state.unitInformationd194Code,
        };
      }
    }

    // 2 中间交换区
    if (val1 == 0 && val == 1) {
      let code = this.props.form.getFieldValue('middleUnitCode');
      if (typeof code == 'string') {
        unitInfo = {
          middleUnitCode: this.props.form.getFieldValue('middleUnitCode'),
          middleUnitName: this.props.form.getFieldValue('middleUnitName'),
          d04Code: this.props.form.getFieldValue('d04Code'),
        };
      } else {
        unitInfo = code;
      }
    }

    //在加上额外判断条件
    // 1、当党员的党组织（单位属性为行政村、是法人单位、国民经济行业为村民自治组织或社区居民自治组织）三个条件同时满足
    // 2、当党员的党组织（单位属性为教育大类中331、是法人单位、国国民经济行业是普通高等教育）三个条件同时满足
    // 3、当党员的党组织（是教育大类中332、333、334、335；是法人单位、国国民经济行业是除普通高等教育以外的 中等教育，
    // 普通高中教育，初等教育，学前教育，）三个条件同时满足

    const d04 = unitInfo?.d04Code || '';
    const isLegal = unitInfo?.isLegal || 0;
    const d194Code = unitInfo?.d194Code || '';

    const find1 = (d04 == '923' || d04 == '922') && (d194Code == 'S9620' || d194Code == 'S9610');
    const find2 = d04?.startsWith('331') && d194Code.startsWith('P8341');
    const find3 =
      (d04?.startsWith('332') ||
        d04?.startsWith('333') ||
        d04?.startsWith('334') ||
        d04?.startsWith('335')) &&
      (d194Code.startsWith('P833') ||
        d194Code.startsWith('P8334') ||
        d194Code.startsWith('P832') ||
        d194Code.startsWith('P831'));

    let flag2 = find1 || find2 || find3;

    let finalFlag = flag2 || flag;

    // 如果有改变2个是否，且最后不是2个否 hasChangedStatisticalUnit true 或者 hasChanged true 且flag false
    if (hasChangedStatisticalUnit || hasChanged) {
      if (!flag) {
        finalFlag = false;
      }
    }

    // 增加判断工作岗位d09, 13-务工经商人员  国民经济一直显示可选
    const d09Code = this.props.form.getFieldValue('d09Code');
    let _keyd09 = typeof d09Code === 'string' ? d09Code : d09Code?.key;
    if (_keyd09 == '13') {
      finalFlag = true;
    }
    // 当工作岗位(d09Code)选择离退人员，离休干部。退休人员以及学生的时候，直接把更改国民经济国岗位置为灰色，通知把他的值更改为U，
    // 中文显示其他，生产性服务行业置换为V0000，中文值为无提交后端
    if (_keyd09?.startsWith('3') || _keyd09?.startsWith('4')) {
      finalFlag = false;
    }
    return finalFlag;
  };
  render(): React.ReactNode {
    const {
      form,
      memBasic: { basicInfo = {} } = {},
      loading: { effects = {} } = {},
      tipMsg = {},
      hideSave = false,
    } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    const {
      d28Disabled,
      isOutCity,
      hasLost,
      beFull,
      iSEditMemType,
      area,
      d154CodeNoDraw = [],
    } = this.state;
    // 姓名和身份证录入后只能修改一次，根据editIdentityCount判断 
    let { lockFields = [], editIdentityCount = 0 } = basicInfo;
    if (!_iisArray(lockFields) && !_isEmpty(lockFields)) {
      lockFields = [];
    }
    return (
      <Spin spinning={!!effects['memBasic/findMem']}>
        <LockMsg basicInfo={{ ...basicInfo, unlockObject: '1' }} />
        <Row style={{ pointerEvents: basicInfo['findHistory'] ? 'none' : 'auto' }}>
          <Col span={12}>
            <FormItem label={formLabel('姓名', tipMsg['name'])} {...formItemLayout}>
              {getFieldDecorator('name', {
                rules: [
                  { required: true, message: '请输入党员姓名' },
                  { validator: this.nameValidator },
                ],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['name'],
              })(<Input placeholder={'请输入党员姓名'} disabled={lockFields.includes('name') || editIdentityCount > 0} />)}
            </FormItem>
          </Col>
          {getFieldDecorator('code', { initialValue: basicInfo['code'] })(
            <div style={{ display: 'none' }}>123</div>,
          )}
          <Col span={12}>
            <FormItem label={formLabel('性别', tipMsg['sexCode'])} {...formItemLayout}>
              {getFieldDecorator('sexCode', {
                rules: [{ required: true, message: '请选择党员性别' }],
                initialValue: _isEmpty(basicInfo) ? '1' : basicInfo['sexCode'],
              })(
                <RadioGroup disabled={lockFields.includes('sexCode')}>
                  <Radio value={'1'}>男</Radio>
                  <Radio value={'0'}>女</Radio>
                </RadioGroup>,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label={formLabel('身份证号', tipMsg['idcard'])} {...formItemLayout}>
              {getFieldDecorator('idcard', {
                validateTrigger: ['onBlur'],
                rules: [
                  { required: true, message: '请输入身份证号' },
                  { validator: this.validatorIdcard },
                ],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['idcard'],
              })(
                <Input
                  placeholder={'请输入身份证'}
                  max={18}
                  onBlur={this.getIDinfo}
                  disabled={lockFields.includes('idcard') || editIdentityCount > 0 }
                />,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label={formLabel('出生日期', tipMsg['birthday'])} {...formItemLayout}>
              {getFieldDecorator('birthday', {
                rules: [{ required: true, message: '请选择出生日期' }],
                initialValue:
                  basicInfo['birthday'] != undefined ? moment(basicInfo['birthday']) : undefined,
              })(<Date disabled={lockFields.includes('birthday')} />)}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label={formLabel('籍贯', tipMsg['d48Code'])} {...formItemLayout}>
              {getFieldDecorator('d48Code', {
                rules: [{ required: true, message: '请选择党员籍贯' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d48Code'],
              })(
                <DictArea
                  placeholder={'党员籍贯'}
                  disabled={lockFields.includes('d48Code')}
                  onChange={(val, obj) => this.props.form.setFieldsValue({ d48Name: obj.name })}
                />,
              )}
            </FormItem>
            {getFieldDecorator('d48Name')(<Input style={{ display: 'none' }} />)}
          </Col>
          <Col span={12}>
            <FormItem
              label={formLabel('户籍所在地', tipMsg['householdRegister'])}
              {...formItemLayout}
            >
              {getFieldDecorator('householdRegister', {
                rules: [
                  { required: true, message: '请填写户籍所在地' },
                  { validator: (...e) => validateLength(e, 100, 300) },
                ],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['householdRegister'],
              })(
                <Input
                  placeholder={'请填写户籍所在地'}
                  disabled={lockFields.includes('householdRegister')}
                />,
              )}
            </FormItem>
          </Col>
          {(function (_this) {
            const { birthday = undefined, applyDate = undefined } =
              _this?.props?.form?.getFieldsValue() || {};
            let bigThan28 = true;
            if (birthday && applyDate && moment(applyDate) <= moment(birthday).add(28, 'years')) {
              bigThan28 = false;
            }
            if (birthday && !applyDate && moment() <= moment(birthday).add(28, 'years')) {
              bigThan28 = false;
            }
            return (
              <Col span={12}>
                <FormItem label={formLabel('其他政治面貌', tipMsg['d89Code'])} {...formItemLayout}>
                  {getFieldDecorator('d89Code', {
                    rules: [{ required: false, message: '请选择其他政治面貌' }],
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d89Code'],
                  })(
                    <DictSelect
                      backType={'object'}
                      codeType={'dict_d89'}
                      placeholder="请选择"
                      mode={'multiple'}
                      initValue={
                        _isEmpty(basicInfo['d89Code']) ? undefined : basicInfo['d89Code'].split(',')
                      }
                      filter={(data) => {
                        data = data.filter((it) => it.key !== '14');
                        if (bigThan28) {
                          data = data.filter((it) => it.key !== '03');
                        }
                        data = data.filter((it) => it.key !== '12' && it.key !== '13');
                        return data;
                      }}
                    />,
                  )}
                </FormItem>
              </Col>
            );
          })(this)}
          <Col span={12}>
            <FormItem label={formLabel('民族', tipMsg['d06Code'])} {...formItemLayout}>
              {getFieldDecorator('d06Code', {
                rules: [{ required: true, message: '请选择党员民族' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d06Code'],
              })(
                <DictTreeSelect
                  initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d06Code']}
                  codeType={'dict_d06'}
                  placeholder={'党员民族'}
                  parentDisable={true}
                  backType={'object'}
                  disabled={lockFields.includes('d06Code')}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label={formLabel('工作岗位', tipMsg['d09Code'])} {...formItemLayout}>
              {getFieldDecorator('d09Code', {
                rules: [{ required: true, message: '请选择工作岗位' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d09Code'],
              })(
                <DictTreeSelect
                  initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d09Code']}
                  codeType={'dict_d09'}
                  placeholder={'请选择工作岗位'}
                  parentDisable={true}
                  backType={'object'}
                  disabled={lockFields.includes('d09Code')}
                  // itemsDisabled={["11"]}
                  onChange={(e) => {
                    const { key = '' } = e || {};
                    if (key.startsWith('3')) {
                      this['d07Code'].clearAll();
                      form.setFieldsValue({
                        d07Code: undefined,
                      });
                    }
                  }}
                />,
              )}
            </FormItem>
          </Col>

          {
            // 当工作岗位是3学生开头的时候 增加一个时间字段填写：入学时间
            (function (_this) {
              const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
              if (_key && _key.startsWith('3')) {
                return (
                  <Fragment>
                    <Col span={12}>
                      <FormItem
                        label={formLabel('入学时间', tipMsg['enterSchoolDate'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('enterSchoolDate', {
                          rules: [{ required: true, message: '请输入 入学时间' }],
                          initialValue:
                            basicInfo['enterSchoolDate'] != undefined
                              ? moment(basicInfo['enterSchoolDate'])
                              : undefined,
                        })(<Date startTime={'1910.01.01'} disabledDate={_this.disabledTomorrow} />)}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label={formLabel('是否需要自动计算年级', tipMsg['hasCalculationGrade'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('hasCalculationGrade', {
                          rules: [{ required: true, message: '是否需要自动计算年级' }],
                          initialValue: basicInfo['hasCalculationGrade'] === 0 ? 0 : 1,
                        })(
                          <Select style={{ width: '100%' }}>
                            <Select.Option value={1}>是</Select.Option>
                            <Select.Option value={0}>否</Select.Option>
                          </Select>,
                        )}
                      </FormItem>
                    </Col>
                  </Fragment>
                );
              }
            })(this)
          }

          {
            // 岗位和学历的校验判断
            (function (_this) {
              const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;

              let itemsDisabled: Array<string> = [];
              if (_key) {
                if (_key.startsWith('31')) {
                  // 研究生
                  // itemsDisabled = ['11','12','13','14'];
                  itemsDisabled = [];
                }
                if (_key.startsWith('32')) {
                  // 本科
                  itemsDisabled = ['111', '112', '12', '13', '14', '21', '22', '23'];
                }
                if (_key.startsWith('33')) {
                  // 专科
                  itemsDisabled = [
                    '111',
                    '112',
                    '12',
                    '13',
                    '14',
                    '21',
                    '22',
                    '23',
                    '31',
                    '32',
                    '33',
                    '34',
                  ];
                }
                if (_key == '34') {
                  itemsDisabled = [
                    '111',
                    '112',
                    '12',
                    '13',
                    '14',
                    '21',
                    '22',
                    '23',
                    '31',
                    '32',
                    '33',
                    '34',
                  ];
                }
                if (_key == '35') {
                  itemsDisabled = [
                    '111',
                    '112',
                    '12',
                    '13',
                    '14',
                    '21',
                    '22',
                    '23',
                    '31',
                    '32',
                    '33',
                    '34',
                  ];
                }
                if (_key == '36') {
                  itemsDisabled = [
                    '111',
                    '112',
                    '12',
                    '13',
                    '14',
                    '21',
                    '22',
                    '23',
                    '31',
                    '32',
                    '33',
                    '34',
                  ];
                }
              }
              return (
                <Col span={12}>
                  <FormItem label={formLabel('学历', tipMsg['d07Code'])} {...formItemLayout}>
                    {getFieldDecorator('d07Code', {
                      rules: [{ required: true, message: '请选择' }],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d07Code'],
                    })(
                      <DictTreeSelect
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d07Code']}
                        codeType={'dict_d07'}
                        placeholder={'党员学历'}
                        ref={(e) => (_this['d07Code'] = e)}
                        parentDisable={true}
                        disabled={lockFields.includes('d07Code')}
                        itemsDisabled={itemsDisabled}
                        backType={'object'}
                      />,
                    )}
                  </FormItem>
                </Col>
              );
            })(this)
          }
          <Col span={12}>
            <FormItem label={formLabel('学位', tipMsg['d145Code'])} {...formItemLayout}>
              {getFieldDecorator('d145Code', {
                rules: [{ required: true, message: '请选择学位' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d145Code'],
              })(
                <DictTreeSelect
                  initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d145Code']}
                  codeType={'dict_d145'}
                  placeholder={'党员学位'}
                  ref={(e) => (this['d145Code'] = e)}
                  parentDisable={true}
                  disabled={lockFields.includes('d145Code')}
                  backType={'object'}
                />,
              )}
            </FormItem>
          </Col>
          {/* 选择无和其他知识分子只能选一个，选其他的可以选多个 */}
          {
            // 工作岗位 满足以下代码时，不显示知识分子情况（1开头，3开头，514开头，515开头，516开头）。
            (function (_this: any) {
              const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
              if (
                !(
                  `${_key}`.startsWith('1') ||
                  `${_key}`.startsWith('3') ||
                  `${_key}`.startsWith('514') ||
                  `${_key}`.startsWith('515') ||
                  `${_key}`.startsWith('516')
                )
              ) {
                return (
                  <Col span={12}>
                    <FormItem
                      label={formLabel('知识分子情况', tipMsg['d154Code'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('d154Code', {
                        rules: [{ required: true, message: '请选择知识分子情况' }],
                        initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d154Code'],
                      })(
                        <DictSelect
                          initValue={
                            _isEmpty(basicInfo)
                              ? undefined
                              : basicInfo['d154Code']
                                ? basicInfo['d154Code'].split(',')
                                : undefined
                          }
                          codeType={'dict_d154'}
                          placeholder={'知识分子情况'}
                          backType={'object'}
                          mode="multiple"
                          noDraw={d154CodeNoDraw}
                          onChange={(e) => {
                            if (!_isEmpty(e)) {
                              if (e[e.length - 1]?.key == '0') {
                                _this.setState({
                                  d154CodeNoDraw: [
                                    '1',
                                    '2',
                                    '3',
                                    '4',
                                    '5',
                                    '6',
                                    '7',
                                    '8',
                                    '9',
                                    'A',
                                    'B',
                                  ],
                                });
                              } else if (e[e.length - 1]?.key == 'B') {
                                _this.setState({
                                  d154CodeNoDraw: [
                                    '0',
                                    '1',
                                    '2',
                                    '3',
                                    '4',
                                    '5',
                                    '6',
                                    '7',
                                    '8',
                                    '9',
                                    'A',
                                  ],
                                });
                              } else if (
                                ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A'].includes(
                                  e[e.length - 1]?.key,
                                )
                              ) {
                                _this.setState({
                                  d154CodeNoDraw: ['0', 'B'],
                                });
                              } else {
                                _this.setState({
                                  d154CodeNoDraw: [],
                                });
                              }
                            } else {
                              _this.setState({
                                d154CodeNoDraw: [],
                              });
                            }
                          }}
                        />,
                      )}
                    </FormItem>
                  </Col>
                );
              }
            })(this)
          }
          {
            // 前后端增加毕业院校、毕业专业（高学历党员填写）
            (function (_this: any) {
              const { d07Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d07Code === 'string' ? d07Code : d07Code?.key;
              if (!['4', '5', '6', '7', '8', '9'].includes(_key)) {
                return (
                  <Fragment>
                    <Col span={12}>
                      <FormItem label={formLabel('毕业院校', tipMsg['byyx'])} {...formItemLayout}>
                        {getFieldDecorator('byyx', {
                          rules: [{ required: false, message: '毕业院校' }],
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['byyx'],
                        })(<Input placeholder={'毕业院校'} />)}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label={formLabel('毕业专业', tipMsg['d88Code'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('d88Code', {
                          rules: [{ required: false, message: '毕业专业' }],
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d88Code'],
                        })(
                          <DictTreeSelect
                            initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d88Code']}
                            codeType={'dict_d88'}
                            placeholder={'毕业专业'}
                            parentDisable={true}
                            backType={'object'}
                          />,
                        )}
                      </FormItem>
                    </Col>
                  </Fragment>
                );
              }
            })(this)
          }
          {
            // 当工作岗位是工勤岗位（例如:岗位名称中的工勤岗位和工勤技能人员这类岗位），才弹出是否农民工和是否劳务派遣的信息选择项
            // 民办非企业工勤技能人员、社会团体工勤技能人员，选择以后都需要增加是否劳务派遣工和是否农民工
            (function (_this) {
              const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
              if (
                `${_key}`.startsWith('016') ||
                `${_key}`.startsWith('025') ||
                `${_key}` == '0313' ||
                `${_key}` == '0323' ||
                `${_key}` == '0333'
              ) {
                return (
                  <Fragment>
                    <Col span={12}>
                      <FormItem
                        label={formLabel('是否劳务派遣工', tipMsg['isDispatch'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('isDispatch', {
                          rules: [{ required: true, message: '请选择' }],
                          initialValue: basicInfo['isDispatch'],
                          // initialValue: _isEmpty(basicInfo)? undefined : _isNumber(basicInfo['isDispatch']) ? basicInfo['isDispatch'].toString() : undefined,
                        })(<YN init={basicInfo['isDispatch']} />)}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label={formLabel('是否农民工', tipMsg['isFarmer'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('isFarmer', {
                          rules: [{ required: true, message: '是否农民工' }],
                          initialValue: basicInfo['isFarmer'],
                          // initialValue: _isEmpty(basicInfo)? undefined : _isNumber(basicInfo['isFarmer']) ? basicInfo['isFarmer'].toString() : undefined,
                        })(<YN init={basicInfo['isFarmer']} />)}
                      </FormItem>
                    </Col>
                  </Fragment>
                );
              }
            })(this)
          }
          {
            // 在读大学生
            (function (_this) {
              const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
              if (
                `${_key}`.startsWith('31') ||
                `${_key}`.startsWith('32') ||
                `${_key}`.startsWith('33')
              ) {
                return (
                  <Fragment>
                    <Col span={12}>
                      <FormItem
                        label={formLabel('在读院校', tipMsg['readingCollege'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('readingCollege', {
                          rules: [{ required: true, message: '在读院校' }],
                          initialValue: _isEmpty(basicInfo)
                            ? undefined
                            : basicInfo['readingCollege'],
                        })(<Input placeholder={'在读院校'} />)}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label={formLabel('在读专业', tipMsg['readingProfessionalCode'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('readingProfessionalCode', {
                          rules: [{ required: true, message: '在读专业' }],
                          initialValue: _isEmpty(basicInfo)
                            ? undefined
                            : basicInfo['readingProfessionalCode'],
                        })(
                          <DictTreeSelect
                            initValue={
                              _isEmpty(basicInfo) ? undefined : basicInfo['readingProfessionalCode']
                            }
                            codeType={'dict_d88'}
                            placeholder={'在读专业'}
                            parentDisable={true}
                            backType={'object'}
                          />,
                        )}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label={formLabel('学制', tipMsg['educationalSystem'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('educationalSystem', {
                          rules: [{ required: true, message: '学制' }],
                          initialValue: _isEmpty(basicInfo)
                            ? undefined
                            : basicInfo['educationalSystem'],
                        })(<InputNumber placeholder={'学制'} style={{ width: '100%' }} />)}
                      </FormItem>
                    </Col>
                  </Fragment>
                );
              }
            })(this)
          }
          <Col span={12}>
            <FormItem label={formLabel('联系电话', tipMsg['phone'])} {...formItemLayout}>
              {getFieldDecorator('phone', {
                getValueFromEvent: (e) => _trim(e.target.value),
                rules: [
                  { required: true, message: '请输入联系电话' },
                  { validator: this.phoneValidator },
                ],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['phone'],
              })(<Input placeholder={'请输入联系电话'} disabled={lockFields.includes('phone')} />)}
            </FormItem>
          </Col>
          {/* <Col span={12}>
            <FormItem label={formLabel('其他联系电话', tipMsg['otherTel'])} {...formItemLayout}>
              {getFieldDecorator('otherTel', {
                rules: [{ required: false, message: '请输入其他联系电话' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['otherTel'],
              })(<Input placeholder={'请输入其他联系电话'} />)}
            </FormItem>
          </Col> */}
          <Col span={12}>
            <FormItem label={formLabel('所在党支部', tipMsg['orgCode'])} {...formItemLayout}>
              {getFieldDecorator('orgCode', {
                rules: [{ required: true, message: '请选择所在党支部' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['orgCode'],
              })(
                <OrgSelect
                  orgTypeList={['3', '4']}
                  initValue={basicInfo['orgName']}
                  disabled={!!basicInfo['code']}
                  placeholder={'请选择所在党支部'}
                  onChange={(e: any) => {
                    if (!_isEmpty(e)) {
                      const { code, d01Code } = e[0] || {};
                      this.getUnitList(code);
                    }
                  }}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <LongLabelFormItem
              label={'是否易地扶贫搬迁党员'}
              required={true}
              code={'hasExSituPovertyAlleviation'}
              tipMsg={tipMsg}
              formItemLayout={formItemLayout}
              formItem={(formItemLayout, code) => {
                return (
                  <FormItem {...formItemLayout}>
                    {getFieldDecorator(code, {
                      rules: [{ required: true, message: '是否易地扶贫搬迁党员' }],
                      initialValue: basicInfo[code],
                    })(<YN init={basicInfo[code]} />)}
                  </FormItem>
                );
              }}
            />
          </Col>
          <Col span={12}>
            <FormItem label={formLabel('类别', tipMsg['d08Code'])} {...formItemLayout}>
              {getFieldDecorator('d08Code', {
                rules: [{ required: true, message: '请输入党员类别' }],
                initialValue: _isEmpty(basicInfo) ? '1' : basicInfo['d08Code'],
              })(
                <RadioGroup onChange={this.d08CodeOnChange} disabled={iSEditMemType}>
                  <Radio value={'1'}>正式党员</Radio>
                  <Radio value={'2'}>预备党员</Radio>
                </RadioGroup>,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <LongLabelFormItem
              label={'人事关系是否在党组织关联单位内'}
              required={true}
              code={'hasUnitStatistics'}
              tipMsg={tipMsg}
              formItemLayout={formItemLayout}
              formItem={(formItemLayout, code) => {
                return (
                  <FormItem {...formItemLayout}>
                    {getFieldDecorator(code, {
                      rules: [{ required: true, message: '人事关系是否在党组织关联单位内' }],
                      initialValue: basicInfo[code],
                    })(
                      <YN
                        init={basicInfo[code]}
                        onChange={(e) => {
                          // if(e == 1){
                          //     //所在党支部
                          //     let code = undefined;
                          //     let org = this.props.form.getFieldValue('orgCode');
                          //     if(typeof org == 'string'){
                          //       code = basicInfo['orgCode'];
                          //     }else {
                          //       code = _get(org,'[0].code',undefined);
                          //     }
                          //     this.getUnitList(code);
                          // }
                        }}
                      />,
                    )}
                  </FormItem>
                );
              }}
            />
          </Col>
          {(function (_this) {
            let val = _this.props.form.getFieldValue('hasUnitStatistics');
            if (val == 0) {
              return (
                <Col span={12}>
                  <LongLabelFormItem
                    label={'人事关系所在单位是否省内单位'}
                    required={true}
                    code={'hasUnitProvince'}
                    tipMsg={tipMsg}
                    key={moment().valueOf()}
                    formItemLayout={formItemLayout}
                    formItem={(formItemLayout, code) => {
                      return (
                        <FormItem {...formItemLayout}>
                          {getFieldDecorator(code, {
                            rules: [{ required: true, message: '人事关系所在单位是否省内单位' }],
                            initialValue: basicInfo[code],
                          })(
                            <Select style={{ width: '100%' }}>
                              <Select.Option value={1}>是</Select.Option>
                              <Select.Option value={0}>否</Select.Option>
                            </Select>,
                          )}
                        </FormItem>
                      );
                    }}
                  />
                </Col>
              );
            }
          })(this)}
          {(function (_this) {
            //人事关系是否在党组织关联单位内
            let val1 = _this.props.form.getFieldValue('hasUnitStatistics');
            //人事关系所在单位是否省内单位
            let hasUnitProvince = _isNumber(_this.props.form.getFieldValue('hasUnitProvince'))
              ? _this.props.form.getFieldValue('hasUnitProvince')
              : basicInfo['hasUnitProvince'];
            //所在党支部
            let org = _this.props.form.getFieldValue('orgCode');
            let d01Code: any = undefined;
            if (typeof org == 'string') {
              d01Code = basicInfo['d01Code'];
            } else {
              d01Code = _get(org, '[0].d01Code', undefined);
            }
            let isLianhe = (d01Code == '632' || d01Code == '932' || d01Code == '634') && d01Code;

            // 当人事关系是否在党组织关联单位内选择 是
            if (val1) {
              // 并且党员所在党组织非联合党支部的时候，人事关系所在单位名称展示为党员所在党组织关联单位
              if (!isLianhe) {
                return (
                  <Col span={12}>
                    <LongLabelFormItem
                      label={'人事关系所在单位名称'}
                      required={true}
                      code={'unitInformation'}
                      tipMsg={tipMsg}
                      formItemLayout={formItemLayout}
                      formItem={(formItemLayout, code) => {
                        return (
                          <FormItem {...formItemLayout}>
                            {getFieldDecorator(code, {
                              rules: [
                                { required: false, message: '人事关系所在单位名称' },
                                { validator: (...e) => validateLength(e, 100, 300) },
                              ],
                              initialValue: _this.state.unitInformation,
                            })(
                              <Input
                                placeholder={'请填写人事关系所在单位名称'}
                                style={{ width: '100%' }}
                                disabled
                              />,
                            )}
                          </FormItem>
                        );
                      }}
                    />
                    <div style={{ display: 'none' }}>
                      {getFieldDecorator('statisticalUnit', {
                        rules: [{ required: false, message: '' }],
                        initialValue: _this.state.unitInformationCode,
                      })(<Input style={{ display: 'none' }} disabled />)}
                      {getFieldDecorator('_d04Code', {
                        rules: [{ required: false, message: '' }],
                        initialValue: _this.state.unitInformationD04Code,
                      })(<Input style={{ display: 'none' }} disabled />)}
                      {getFieldDecorator('d01Code', {
                        rules: [{ required: false, message: '' }],
                        initialValue: d01Code,
                      })(<Input style={{ display: 'none' }} disabled />)}
                    </div>
                  </Col>
                );
              } else {
                // 党员所在党组织是联合党支部的时候，人事关系所在单位名称变成下拉框
                return (
                  <Col span={12}>
                    <LongLabelFormItem
                      label={'人事关系所在单位名称'}
                      required={true}
                      code={'statisticalUnit'}
                      tipMsg={tipMsg}
                      formItemLayout={formItemLayout}
                      formItem={(formItemLayout, code) => {
                        return (
                          <Fragment>
                            <FormItem {...formItemLayout}>
                              {getFieldDecorator(code, {
                                rules: [{ required: true, message: '人事关系所在单位名称' }],
                                initialValue: basicInfo[code],
                              })(
                                <Select
                                  style={{ width: '100%' }}
                                  onChange={(e) => {
                                    let find = _this.state.unitList.find((it) => it?.unitCode == e);
                                    if (find) {
                                      _this.props.form.setFieldsValue({
                                        __d04Code: find.d04Code,
                                      });
                                    }
                                  }}
                                >
                                  {_this.state.unitList &&
                                    _this.state.unitList.map((it, index) => (
                                      <Select.Option key={index} value={it.unitCode}>
                                        {it.unitName}
                                      </Select.Option>
                                    ))}
                                </Select>,
                              )}
                            </FormItem>
                            {getFieldDecorator('__d04Code', {
                              rules: [{ required: false, message: '' }],
                              initialValue: basicInfo['d04Code'],
                            })(<Input style={{ display: 'none' }} disabled />)}
                            {getFieldDecorator('d01Code', {
                              rules: [{ required: false, message: '' }],
                              initialValue: d01Code,
                            })(<Input style={{ display: 'none' }} disabled />)}
                          </Fragment>
                        );
                      }}
                    />
                  </Col>
                );
              }
            } else {
              //人事关系所在单位是否省内单位选择是的时候，人事关系所在单位名称需要走中间交换区进行搜索
              if (hasUnitProvince == 1) {
                return (
                  <Col span={12}>
                    <LongLabelFormItem
                      label={'人事关系所在单位名称'}
                      required={true}
                      code={'middleUnitCode'}
                      tipMsg={tipMsg}
                      formItemLayout={formItemLayout}
                      formItem={(formItemLayout, code) => {
                        return (
                          <FormItem {...formItemLayout}>
                            {getFieldDecorator(code, {
                              rules: [{ required: true, message: '人事关系所在单位名称' }],
                              initialValue: basicInfo[code],
                            })(
                              <SearchUnit
                                initName={basicInfo['middleUnitName']}
                                initCode={basicInfo['middleUnitCode']}
                                backType={'object'}
                                style={{ width: '100%' }}
                                onChange={(e) => {
                                  _this.props.form.setFieldsValue({
                                    d04Code: e.d04Code,
                                    middleUnitName: e.name,
                                    _d194Code: e.d194Code,
                                    _d194Name: e.d194Name,
                                    _d195Code: e.d195Code,
                                    _d195Name: e.d195Name,
                                  });
                                }}
                                params={{ orgTypeList: ['3', '4'] }}
                              />,
                            )}
                          </FormItem>
                        );
                      }}
                    />
                    <div style={{ display: 'none' }}>
                      {getFieldDecorator('middleUnitName', {
                        rules: [{ required: false, message: '' }],
                        initialValue: basicInfo['middleUnitName'],
                      })(<Input style={{ display: 'none' }} disabled />)}
                      {getFieldDecorator('d04Code', {
                        rules: [{ required: false, message: '' }],
                        initialValue: basicInfo['d04Code'],
                      })(<Input style={{ display: 'none' }} disabled />)}
                      {getFieldDecorator('_d194Code', {
                        rules: [{ required: false, message: '' }],
                        initialValue: basicInfo['_d194Code'],
                      })(<Input style={{ display: 'none' }} disabled />)}
                      {getFieldDecorator('_d194Name', {
                        rules: [{ required: false, message: '' }],
                        initialValue: basicInfo['_d194Name'],
                      })(<Input style={{ display: 'none' }} disabled />)}
                      {getFieldDecorator('_d195Code', {
                        rules: [{ required: false, message: '' }],
                        initialValue: basicInfo['_d195Code'],
                      })(<Input style={{ display: 'none' }} disabled />)}
                      {getFieldDecorator('_d195Name', {
                        rules: [{ required: false, message: '' }],
                        initialValue: basicInfo['_d195Name'],
                      })(<Input style={{ display: 'none' }} disabled />)}
                    </div>
                  </Col>
                );
              } else if (hasUnitProvince == 0) {
                return (
                  <Col span={12}>
                    <LongLabelFormItem
                      label={'人事关系所在单位名称'}
                      required={true}
                      code={'selfUnitName'}
                      tipMsg={tipMsg}
                      formItemLayout={formItemLayout}
                      formItem={(formItemLayout, code) => {
                        return (
                          <FormItem {...formItemLayout}>
                            {getFieldDecorator(code, {
                              rules: [
                                { required: true, message: '人事关系所在单位名称' },
                                { validator: (...e) => validateLength(e, 100, 300) },
                              ],
                              initialValue: basicInfo[code],
                            })(
                              <Input
                                placeholder={'请填写人事关系所在单位名称'}
                                style={{ width: '100%' }}
                              />,
                            )}
                          </FormItem>
                        );
                      }}
                    />
                  </Col>
                );
              }
            }
          })(this)}

          {(function (_this) {
            let val1 = _this.props.form.getFieldValue('hasUnitStatistics');
            if (val1 == undefined) {
              val1 = basicInfo['hasUnitStatistics'];
            }
            let val = _this.props.form.getFieldValue('hasUnitProvince');
            if (val == undefined) {
              val = basicInfo['hasUnitProvince'];
            }
            if (val1 == 0 && val == 0) {
              return (
                <Fragment>
                  <Col span={12}>
                    <LongLabelFormItem
                      label={'人事关系所在单位类别'}
                      required={true}
                      code={'d04Code'}
                      tipMsg={tipMsg}
                      formItemLayout={formItemLayout}
                      formItem={(formItemLayout, code) => {
                        return (
                          <FormItem {...formItemLayout}>
                            {getFieldDecorator(code, {
                              rules: [{ required: true, message: '人事关系所在单位类别' }],
                              initialValue: _isEmpty(basicInfo) ? undefined : basicInfo[code],
                            })(
                              <DictTreeSelect
                                initValue={_isEmpty(basicInfo) ? undefined : basicInfo[code]}
                                codeType={'dict_d04'}
                                placeholder={'请选择'}
                                parentDisable={true}
                                backType={'object'}
                                onChange={async (e) => {
                                  // 更新d194Code
                                  let val1 = getFieldValue('hasUnitStatistics');
                                  let val2 = getFieldValue('hasUnitProvince');
                                  if (val1 != 0 && val2 != 0) {
                                    const res = await normalList({
                                      data: {
                                        tableCode: 'ccp_unit',
                                        colCode: 'd04Code',
                                        colValue: e.key,
                                        compareColCode: 'd194Code',
                                      },
                                    });
                                    if (res.code == 0 && !_isEmpty(res.data)) {
                                      let key = Object.keys(res.data)?.[0];
                                      let name = res.data[key];
                                      _this.setState({
                                        d194CodeSatate: key,
                                        d194CodeKey: moment().valueOf(),
                                      });
                                      _this.props.form.setFieldsValue({
                                        d194Code: key,
                                        d194Name: name,
                                      });
                                      // 更新d195Code
                                      _this.d194Change(key);
                                    }
                                  }
                                }}
                              />,
                            )}
                          </FormItem>
                        );
                      }}
                    />
                  </Col>
                </Fragment>
              );
            }
          })(this)}

          {(function (_this) {
            let canEdit = _this.showGUOMINGJINGJI();


            if (true) {
              return (
                <Fragment>
                  <Col span={12}>
                    <LongLabelFormItem
                      label={'国民经济行业'}
                      required={canEdit}
                      code={'d194Code'}
                      tipMsg={tipMsg}
                      formItemLayout={formItemLayout}
                      formItem={(formItemLayout, code) => {
                        console.log("basicInfo", basicInfo);
                        return (
                          <FormItem {...formItemLayout}>
                            {getFieldDecorator(code, {
                              rules: [{ required: canEdit, message: '国民经济行业' }],
                              initialValue: _isEmpty(basicInfo) ? undefined : basicInfo[code],
                            })(
                              <DictTreeSelect
                                key={_this.state.d194CodeKey}
                                backType={'object'}
                                initValue={
                                  _this.state.d194CodeSatate ||
                                  (_isEmpty(basicInfo) ? undefined : basicInfo[code])
                                }
                                codeType={'dict_d194'}
                                placeholder={'国民经济行业'}
                                showModalIcon={canEdit}
                                disabled={!canEdit}
                                parentDisable={true}
                                onChange={_this.d194Change}
                              />,
                            )}
                          </FormItem>
                        );
                      }}
                    />
                  </Col>
                  <Col span={12}>
                    <FormItem
                      label={formLabel('生产性服务行业', tipMsg['d195Code'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('d195Code', {
                        initialValue: basicInfo['d195Code'] || 'V0000',
                        rules: [{ required: canEdit, message: '请选择生产性服务行业' }],
                      })(
                        <DictTreeSelect
                          backType={'object'}
                          key={_this.state.d195CodeKey}
                          initValue={_this.state.d195CodeSatate || basicInfo['d195Code'] || 'V0000'}
                          codeType={'dict_d195'}
                          placeholder={'生产性服务行业'}
                          showModalIcon={canEdit}
                          disabled={!canEdit}
                          parentDisable={true}
                        />,
                      )}
                    </FormItem>
                  </Col>
                  {getFieldDecorator('d194Name', {
                    rules: [{ required: false, message: '' }],
                    initialValue: basicInfo['d194Name'],
                  })(<Input style={{ display: 'none' }} disabled />)}
                  {getFieldDecorator('d195Name', {
                    rules: [{ required: false, message: '' }],
                    initialValue: basicInfo['d195Name'],
                  })(<Input style={{ display: 'none' }} disabled />)}
                </Fragment>
              );
            }
          })(this)}

          {/* <Col span={23}>
            <Alert
              message="提示：根据党统数据要求，一线情况、专业技术职务、新社会阶层为非必填项，没有请选择无。"
              type="info"
              showIcon
            />
          </Col> */}
          {/* <Col span={24}>
            <div style={{ marginBottom: '10px' }} />
          </Col> */}
          <Col span={12}>
            <FormItem label={formLabel('申请入党时间', tipMsg['applyDate'])} {...formItemLayout}>
              {getFieldDecorator('applyDate', {
                rules: [
                  { required: false, message: '请输入申请入党时间' },
                  // { validator: this.TemporalOrder1 },
                ],
                initialValue:
                  basicInfo['applyDate'] != undefined ? moment(basicInfo['applyDate']) : undefined,
                // <DatePicker disabledDate={this.disabledTomorrow} style={{ width: '100%' }} />
              })(
                <Date
                  startTime={'1910.01.01'}
                  disabledDate={this.disabledTomorrow}
                  disabled={basicInfo['code']}
                // disabled={lockFields.includes('applyDate')}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label={formLabel('确定积极分子时间', tipMsg['activeDate'])}
              {...formItemLayout}
            >
              {getFieldDecorator('activeDate', {
                rules: [
                  { required: false, message: '请输入确定积极分子时间' },
                  // { validator: this.TemporalOrder2 },
                ],
                initialValue:
                  basicInfo['activeDate'] != undefined
                    ? moment(basicInfo['activeDate'])
                    : undefined,
                // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
              })(
                <Date
                  startTime={'1910.01.01'}
                  disabledDate={this.disabledTomorrow}
                  // disabled={lockFields.includes('activeDate')}
                  disabled={basicInfo['code']}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label={formLabel('确定发展对象时间', tipMsg['objectDate'])}
              {...formItemLayout}
            >
              {getFieldDecorator('objectDate', {
                rules: [
                  { required: false, message: '请输入确定发展对象时间' },
                  // { validator: this.TemporalOrder3 },
                ],
                initialValue:
                  basicInfo['objectDate'] != undefined
                    ? moment(basicInfo['objectDate'])
                    : undefined,
                // <DatePicker disabledDate={this.disabledTomorrow} style={{ width: '100%' }} />
              })(
                <Date
                  startTime={'1910.01.01'}
                  disabledDate={this.disabledTomorrow}
                  // disabled={lockFields.includes('objectDate')}
                  disabled={basicInfo['code']}
                />,
              )}
            </FormItem>
          </Col>
          {(function (_this) {
            const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
            let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
            if (!(`${_key}`.startsWith("3") || `${_key}`.startsWith("4"))) {
              return (
                <Col span={12}>
                  <FormItem label={formLabel('一线情况', tipMsg['d21Code'])} {...formItemLayout}>
                    {getFieldDecorator('d21Code', {
                      rules: [{ required: true, message: '请选择一线情况' }],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d21Code'],
                    })(
                      <DictSelect
                        codeType={'dict_d21'}
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d21Code']}
                        backType={'object'}
                        disabled={lockFields.includes('d21Code')}
                      />,
                    )}
                  </FormItem>
                </Col>
              );
            }
          })(this)}
          {/* <Col span={12}>
            <FormItem label={formLabel('一线情况', tipMsg['d21Code'])} {...formItemLayout}>
              {getFieldDecorator('d21Code', {
                rules: [{ required: true, message: '请选择一线情况' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d21Code'],
              })(
                <DictSelect
                  codeType={'dict_d21'}
                  initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d21Code']}
                  backType={'object'}
                  disabled={lockFields.includes('d21Code')}
                />,
              )}
            </FormItem>
          </Col> */}
          {(function (_this) {
            // 012	事业单位管理岗位（含参照管理）
            // 013	事业单位专业技术岗位
            // 014	公有经济控制企业管理岗位
            // 015	公有经济控制企业专业技术岗位
            // 0162	事业单位工勤技能人员
            // 0163	公有制经济控制企业工勤技能人员
            // 02	非公有制单位
            // 03	社会组织
            const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
            let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
            let arr = ['012', '013', '014', '015', '0162', '0163', '02', '03'];
            if (arr.find((it) => `${_key}`.startsWith(it))) {
              return (
                <Col span={12}>
                  <FormItem
                    label={formLabel('专业技术职务', tipMsg['d19Code'])}
                    {...formItemLayout}
                  >
                    {getFieldDecorator('d19Code', {
                      rules: [{ required: true, message: '请选择专业技术职务' }],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d19Code'],
                    })(
                      <DictTreeSelect
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d19Code']}
                        codeType={'dict_d19'}
                        placeholder={'专业技术职务'}
                        parentDisable={true}
                        backType={'object'}
                        onChange={() => {
                          // this['d126Code'].clearAll();
                          // this.props.form.setFieldsValue({d126Code:undefined});
                        }}
                      />,
                    )}
                  </FormItem>
                </Col>
              );
            }
          })(this)}
          {/* {(function(_this){
            const {d19Code = undefined} = _this?.props?.form?.getFieldsValue() || {};
            let _key = typeof d19Code === 'string' ? d19Code : d19Code?.key || '';
            let parentKey = `${_key}`.slice(0, _key.length-1);
            return (
              <Col span={12}>
                <FormItem label={formLabel('专业技术职称', tipMsg['d126Code'])} {...formItemLayout}>
                  {getFieldDecorator('d126Code', {
                    rules: [{ required: true, message: '专业技术职称' }],
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d126Code'],
                  })(
                    <DictTreeSelect
                      initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d126Code']}
                      codeType={'dict_d126'}
                      ref={e=>_this['d126Code']=e}
                      placeholder={'专业技术职称'}
                      parentDisable={true}
                      backType={'object'}
                      showConstant={false}
                      filter={(data)=>{
                        if(parentKey){
                          let find:any= treeToList(data).find(it=>it.key == parentKey) || {};
                          if(find?.children){
                            find.children = find.children.filter(it => it.key >= _key)
                          }
                          return [find];
                        }
                        return data;
                      }}
                    />
                  )}
                </FormItem>
              </Col>
            )
          })(this)} */}

          {(function (_this) {
            // d09Code 社会组织 工作岗位是自由职业人员505和个体工商户中从业人员504， 才能选择新社会阶层
            const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
            let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
            if (
              `${_key}`.startsWith('03') ||
              `${_key}`.startsWith('02') ||
              `${_key}`.startsWith('505') ||
              `${_key}` == '504'
            ) {
              return (
                <Col span={12}>
                  <FormItem label={formLabel('新社会阶层', tipMsg['d20Code'])} {...formItemLayout}>
                    {getFieldDecorator('d20Code', {
                      rules: [{ required: true, message: '请选择新社会阶层' }],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d20Code'],
                    })(
                      <DictTreeSelect
                        codeType={'dict_d20'}
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d20Code']}
                        backType={'object'}
                        parentDisable={true}
                      />,
                    )}
                  </FormItem>
                </Col>
              );
            }
          })(this)}
          {/* <Col span={23}>
            <Alert message="提示：以下信息项仅预备和正式党员需填写。" type="info" showIcon />
          </Col> */}
          {/* <Col span={24}>
            <div style={{ marginBottom: '10px' }} />
          </Col> */}
          <Col span={12}>
            <FormItem
              label={formLabel('接收预备党员时间', tipMsg['joinOrgDate'])}
              {...formItemLayout}
            >
              {getFieldDecorator('joinOrgDate', {
                rules: [
                  { required: true, message: '请选择接收预备党员时间' },
                  // { validator: this.TemporalOrder4 },
                ],
                initialValue:
                  basicInfo['joinOrgDate'] != undefined
                    ? moment(basicInfo['joinOrgDate'])
                    : undefined,
                // <DatePicker disabledDate={this.disabledTomorrow} style={{ width: '100%' }} />
              })(
                <Date
                  startTime={'1910.01.01'}
                  disabledDate={this.disabledTomorrow}
                  // disabled={lockFields.includes('joinOrgDate')}
                  disabled={basicInfo['code']}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label={formLabel('加入党组织方式', tipMsg['d27Code'])} {...formItemLayout}>
              {getFieldDecorator('d27Code', {
                rules: [{ required: true, message: '请选择加入党组织方式' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d27Code'],
              })(
                <DictTreeSelect
                  initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d27Code']}
                  codeType={'dict_d27'}
                  placeholder={'加入党组织方式'}
                  parentDisable={true}
                  backType={'object'}
                  disabled={lockFields.includes('d27Code')}
                />,
              )}
            </FormItem>
          </Col>
          {/* <Col span={23}>
            <Alert
              message="提示：接收预备党员时间时间在2018-01-01以后的人员需填写发展时所在党支部，省外发展请选择贵州省外支部。"
              type="info"
              showIcon
            />
          </Col> */}
          {/* <Col span={24}>
            <div style={{ marginBottom: '10px' }} />
          </Col> */}
          <Col span={12}>
            <FormItem label={formLabel('进入支部类型', tipMsg['d11Code'])} {...formItemLayout}>
              {getFieldDecorator('d11Code', {
                rules: [
                  {
                    required: this.props.isTransfer || _isEmpty(basicInfo['code']),
                    message: '请选择进入支部类型',
                  },
                ],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d11Code'],
              })(
                <DictTreeSelect
                  disabled={basicInfo['code']}
                  codeType={'dict_d11'}
                  initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d11Code']}
                  onChange={this.d11OnChange}
                  backType={'object'}
                  parentDisable={true}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label={formLabel('进入支部日期', tipMsg['joinOrgPartyDate'])}
              {...formItemLayout}
            >
              {getFieldDecorator('joinOrgPartyDate', {
                rules: [{ required: this.props.isTransfer, message: '请选择进入支部日期' }],
                initialValue:
                  basicInfo['joinOrgPartyDate'] != undefined
                    ? moment(basicInfo['joinOrgPartyDate'])
                    : undefined,
                // <DatePicker disabledDate={this.disabledTomorrow} style={{ width: '100%' }} />
              })(<Date disabledDate={this.disabledTomorrow} disabled={!!basicInfo?.code} />)}
            </FormItem>
          </Col>
          {/* <Col span={12}>
            {isOutCity ? (
              <FormItem
                label={formLabel('发展时所在党支部', tipMsg['outBranchOrgName'])}
                {...formItemLayout}
              >
                {getFieldDecorator('outBranchOrgName', {
                  rules: [{ required: false, message: '请选择所在党支部' }],
                  initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['outBranchOrgName'],
                })(<Input placeholder={'请选择所在党支部'} />)}
              </FormItem>
            ) : (
              <FormItem
                label={formLabel('发展时所在党支部', tipMsg['branchOrgKey'])}
                {...formItemLayout}
              >
                {getFieldDecorator('branchOrgKey', {
                  rules: [{ required: false, message: '请选择所在党支部' }],
                  initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['branchOrgKey'],
                })(
                  <OrgSelect
                    orgTypeList={['3', '4']}
                    initValue={basicInfo['branchOrgName']}
                    placeholder={'发展时所在党支部'}
                  />,
                )}
              </FormItem>
            )}
          </Col> */}
          {/* 正式党员才填写 */}
          {getFieldValue('d08Code') == '1' && (
            <Col span={12}>
              <FormItem
                label={formLabel('成为正式党员日期', tipMsg['fullMemberDate'])}
                {...formItemLayout}
              >
                {getFieldDecorator('fullMemberDate', {
                  rules: [
                    // { required: beFull, message: '请选择成为正式党员日期' },
                    { required: true, message: '请选择成为正式党员日期' },
                    // { validator: this.TemporalOrder5 },
                  ],
                  initialValue:
                    basicInfo['fullMemberDate'] != undefined
                      ? moment(basicInfo['fullMemberDate'])
                      : undefined,
                  // <DatePicker
                  //   disabledDate={this.disabledTomorrow}
                  //   style={{ width: '100%' }}
                  //   disabled={!beFull}
                  // />
                })(
                  <Date
                    disabled={!beFull || lockFields.includes('fullMemberDate') || basicInfo.id}
                    key={Math.random()}
                  />,
                )}
                {/* ↑↑↑ 党员编辑时：成为正式党员日期 不能修改 */}
              </FormItem>
            </Col>
          )}
          {(function (_this) {
            const { joinOrgDate = moment(), d08Code = '' } =
              _this?.props?.form?.getFieldsValue() || {};
            // let a = joinOrgDate.valueOf()
            // let joinOrgDateRequired = moment(a).add(1, 'y').isSameOrBefore(moment())
            // 预备期满时间
            const { extendPreparDate = undefined } = basicInfo;
            let isFinish = extendPreparDate && moment(extendPreparDate).isSameOrBefore(moment());
            return (
              // 1正式党员 或者 预备期满 才显示“预备党员转正类型”
              (d08Code == '1' || isFinish) && (
                <Col span={12}>
                  <FormItem
                    label={formLabel('预备党员转正类型', tipMsg['d28Code'])}
                    {...formItemLayout}
                  >
                    {getFieldDecorator('d28Code', {
                      // rules: [{ required: !(!_isEmpty(basicInfo) ? basicInfo['d08Code'] !== '2' : false), message: '请选择预备党员转正类型' }],
                      // rules: [{ required: (!_isEmpty(basicInfo) ? basicInfo['d08Code'] !== '2' : false) ? false : joinOrgDateRequired, message: '请选择预备党员转正类型' }], // 正式党员非必填，预备党员转正类型，现在有两种情况：1、未满一年为非必填2、满一年为必填
                      rules: [
                        { required: _isEmpty(basicInfo.code), message: '请选择预备党员转正类型' },
                      ],
                      initialValue:
                        d08Code == '1'
                          ? !_isEmpty(basicInfo)
                            ? basicInfo['d28Code']
                            : undefined
                          : _isEmpty(basicInfo['d28Code'])
                            ? undefined
                            : basicInfo['d28Code'],
                    })(
                      <DictTreeSelect
                        ref={(e) => (_this['SelectMem'] = e)}
                        // initValue={d08Code=='1' ? !_isEmpty(basicInfo) ?  basicInfo['d28Code'] : undefined : (_isEmpty(basicInfo['d28Code']) ? '14' : basicInfo['d28Code'])}
                        // 这个， 如果没选， 能不能展示为空，给人一种选了的错觉， 导致于客户说对不上，一直在找报表 (2023.01.11)
                        initValue={
                          d08Code == '1'
                            ? !_isEmpty(basicInfo)
                              ? basicInfo['d28Code']
                              : undefined
                            : _isEmpty(basicInfo['d28Code'])
                              ? undefined
                              : basicInfo['d28Code']
                        }
                        codeType={'dict_d28'}
                        placeholder={'预备党员转正类型'}
                        parentDisable={true}
                        disabled={!_isEmpty(basicInfo.code) ? basicInfo['d08Code'] !== '2' : false}
                        // itemsDisabled={d28Disabled}
                        backType={'object'}
                      />,
                    )}
                  </FormItem>
                </Col>
              )
            );
          })(this)}
          <Col span={12}>
            <FormItem label={formLabel('党费交纳情况', tipMsg['d49Code'])} {...formItemLayout}>
              {getFieldDecorator('d49Code', {
                rules: [{ required: true, message: '请选择党费交纳情况' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d49Code'],
              })(
                <DictSelect
                  codeType={'dict_d49'}
                  initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d49Code']}
                  backType={'object'}
                  disabled={lockFields.includes('d49Code')}
                />,
              )}
            </FormItem>
          </Col>
          {/*<Col span={12}>*/}
          {/*  <FormItem label={formLabel('失去联系类型', tipMsg['d18Code'])} {...formItemLayout}>*/}
          {/*    {getFieldDecorator('d18Code', {*/}
          {/*      rules: [{ required: false, message: '请选择失去联系类型' }],*/}
          {/*      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d18Code'],*/}
          {/*    })(*/}
          {/*      <DictSelect*/}
          {/*        codeType={'dict_d18'}*/}
          {/*        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d18Code']}*/}
          {/*        onChange={this.d18OnChange}*/}
          {/*        backType={'object'}*/}
          {/*      />,*/}
          {/*    )}*/}
          {/*  </FormItem>*/}
          {/*</Col>*/}
          {/*<Col span={12}>*/}
          {/*  <FormItem*/}
          {/*    label={formLabel('失去联系时间', tipMsg['lostContactDate'])}*/}
          {/*    {...formItemLayout}*/}
          {/*  >*/}
          {/*    {getFieldDecorator('lostContactDate', {*/}
          {/*      rules: [{ required: hasLost, message: '请选择失去联系时间' }],*/}
          {/*      initialValue: basicInfo['lostContactDate'] != undefined*/}
          {/*        ? moment(basicInfo['lostContactDate'])*/}
          {/*        : undefined,*/}
          {/*      // <DatePicker*/}
          {/*      //   disabledDate={this.disabledTomorrow}*/}
          {/*      //   style={{ width: '100%' }}*/}
          {/*      //   disabled={!hasLost}*/}
          {/*      // />,*/}
          {/*    })(<Date disabledDate={this.disabledTomorrow}/>)}*/}
          {/*  </FormItem>*/}
          {/*</Col>*/}

          {_isEmpty(basicInfo?.code) && !this.props.isTransfer && this.renderAddNewItems()}

          {this.props.isTransfer && (
            <Fragment>
              <Col span={12}>
                <FormItem
                  label={formLabel('转入时间', tipMsg['outlandTransferInYear'])}
                  {...formItemLayout}
                >
                  {getFieldDecorator('outlandTransferInYear', {
                    rules: [
                      { required: true, message: '转入时间' },
                      // { validator: this.TemporalOrder1 },
                    ],
                    initialValue:
                      basicInfo['outlandTransferInYear'] != undefined
                        ? moment(basicInfo['outlandTransferInYear'])
                        : undefined,
                  })(<Date />)}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="转出地区" {...formItemLayout}>
                  {getFieldDecorator('d136Code', {
                    initialValue: basicInfo['d136Code'],
                    rules: [{ required: true, message: '请选择转出地区' }],
                  })(
                    <DictTreeSelect
                      backType={'object'}
                      initValue={basicInfo['d136Code']}
                      codeType={'dict_d136'}
                      placeholder={'转出地区'}
                      parentDisable={true}
                    />,
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label="转出党组织名称" {...formItemLayout}>
                  {getFieldDecorator('outlandTransferOrg', {
                    initialValue: basicInfo['outlandTransferOrg'],
                    rules: [{ required: true, message: '请输入转出党组织名称' }],
                  })(<Input placeholder={'请输入转出党组织名称'} />)}
                </FormItem>
              </Col>
            </Fragment>
          )}

          <Col span={24}>
            <FormItem label={formLabel('家庭住址', tipMsg['homeAddress'])} {...formItemLayout1}>
              {getFieldDecorator('homeAddress', {
                validateTrigger: ['onBlur'],
                rules: [
                  { required: true, message: '请填写家庭住址（8个字及以上）', min: 8 },
                  { validator: (...e) => validateLength(e, 100, 300) },
                ],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['homeAddress'],
              })(
                <Input
                  placeholder={'请填写家庭住址'}
                  minLength={8}
                  disabled={lockFields.includes('homeAddress')}
                />,
              )}
            </FormItem>
          </Col>
          <div style={{ height: '40px', width: '100%' }} />
        </Row>
        {!hideSave && (
          <div className={styles.btns}>
            {_isEmpty(basicInfo) ? (
              <Popconfirm title="提交后党员类型将无法更改。姓名和身份证一旦录入，只允许一次修改，修改后不可再进行修改。请注意核实正确！" onConfirm={this.submit}>
                <Button
                  htmlType={'submit'}
                  type={'primary'}
                  loading={effects['memBasic/save']}
                  key={1}
                >
                  <LegacyIcon type={'save'} />
                  保存
                </Button>
              </Popconfirm>
            ) : (
              <>
                {!basicInfo['findHistory'] && (
                  <Fragment>
                    {
                      (getFieldValue('name') != basicInfo['name'] || getFieldValue('idcard')?.toLowerCase() != basicInfo['idcard']?.toLowerCase())  ? (
                        <Popconfirm title="姓名和身份证一旦录入，只允许一次修改，修改后不可再进行修改。请注意核实正确！" onConfirm={this.submit} okText="确定" cancelText="取消">
                          <Button type="primary">
                            <LegacyIcon type={'save'} />
                            保存
                          </Button>
                        </Popconfirm>
                      ) : (<Button
                        htmlType={'submit'}
                        type={'primary'}
                        onClick={this.submit}
                        loading={effects['memBasic/save']}
                        key={2}
                      >
                        <LegacyIcon type={'save'} />
                        保存
                      </Button>)
                    }
                  </Fragment>
                )}
              </>
            )}
          </div>
        )}
        <CheckDictIsRight ref={(e) => (this['CheckDictIsRight'] = e)} />
      </Spin>
    );
  }
}
export default Form.create<any>()(index);
