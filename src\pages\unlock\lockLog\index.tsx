import React, { useEffect, useState, Fragment, useRef, useImperativeHandle } from 'react';
import ListTable from '@/components/ListTable';
import { Tabs, Divider, Button, Form, Input, Modal } from 'antd';
import { getSession } from '@/utils/session';
import { _history as router } from "@/utils/method";
import { record, refuse, unlock } from '../services';
import { memTabs } from '../locked';
import Tip from '@/components/Tip'
import moment from 'moment';
import NowOrg from '@/components/NowOrg';
import _isNumber from 'lodash/isNumber';
import _isEmpty from 'lodash/isEmpty'
import _get from 'lodash/get';
import qs from 'qs';
import RuiFilter from '@/components/RuiFilter';
const TabPane = Tabs.TabPane;
const { TextArea } = Input;
const { Search } = Input;


const index = (props: any) => {
  const org: any = getSession('org');
  const rejuectRef: any = useRef();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const [searchQuery, setSearchQuery] = useState<any>({ keyword: undefined });
  const [timeKey, setTimeKey] = useState(+new Date());
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 20, total: 0, current: 1 });
  const [searchState, setSearchState] = useState([])

  const { query } = props.location;

  const getPageTypeName = () => {
    if (!_isEmpty(query.lockObject)) {
      let name: any = undefined;
      switch (query.lockObject) {
        case '1':
          name = '人员'
          break;
        case '2':
          name = '单位'
          break;
        case '3':
          name = '组织'
          break;
      }
      return name;
    }
    return undefined;
  };

  const getList = async (p?) => {
    setLoading(true);
    const {
      code = 500,
      data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await record({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.pageNum,
        code: org.code || '',
        lockObject: query.lockObject,
        type: 1,
        ...searchQuery,
        ...p,
        state:searchState?.length > 0 ? searchState : undefined,
      }
    });
    setLoading(false);
    if (code === 0) {
      setList(list);
      setPagination({ pageNum, total, pageSize, current: pageNum });
    }
  };

  const onChange = (val) => {
    setSearchQuery({ keyword: undefined });
    setTimeKey(+new Date())
    router.push(`?${qs.stringify({ ...query, lockObject: val })}`)
  };

  const search = (val) => {
    setSearchQuery({ keyword: val });
  };

  useEffect(() => {
    getList({ pageNum: 1 });
  }, [JSON.stringify(org), query.lockObject, JSON.stringify(searchQuery), JSON.stringify(searchState)] );

  return (
    <div>
      <Tabs activeKey={query.lockObject} onChange={onChange}>
        {
          !_isEmpty(memTabs) && memTabs.map(item => <TabPane tab={item['title']} key={item['key']} />)
        }
      </Tabs>

      <NowOrg
        extra={
          <React.Fragment>
            <Search style={{ width: 200, marginLeft: 16 }} key={timeKey} onSearch={search} placeholder={'请输入检索关键词'} />
          </React.Fragment>
        }
      />
      <RuiFilter
          data={ [{key: 'type', name: '状态类型', value: [{ key: '1', name: '已同意' }, { key: '0', name: '已拒绝' }, { key: '-1', name: '无' }]}] }
          onChange={(e:any)=>{
            setSearchState(e?.type || undefined)
          }}
        />
      <ListTable
        rowKey={'code'}
        pagination={pagination}
        data={list}
        
        onPageChange={(page, pageSize) => {

          getList({ pageNum: page, current: page, pageSize });
        }}
        columns={[
          {
            title: '序号',
            dataIndex: 'num',
            align: 'center',
            width: 50,
            render: (text, record, index) => {
              return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
            },
          },
          {
            title: '操作账号',
            dataIndex: 'memAccount',
            width: 100,
            align: 'center',
          },
          {
            title: `账号关联人员`,
            dataIndex: 'accountNumberRelatedPerson',
            width: 100,
            align: 'center',
          },
          {
            title: '操作类型',
            dataIndex: 'typeName',
            align: 'center',
            width: 100,
            // render: (text, record, index) => {
            //   let _str = '';
            //   switch (text) {
            //     case '1':
            //       _str = `申请${getPageTypeName()}记录`;
            //       break;
            //     case '2':
            //       _str = `解锁${getPageTypeName()}记录`;
            //       break;
            //     case '3':
            //       _str = `锁定${getPageTypeName()}记录`;
            //       break;
            //   }
            //   return _str;
            // }
          },
          {
            title: `锁定/解锁${getPageTypeName()}`,
            dataIndex: 'unlockName',
            align: 'center',
            width: 120,
          },
          {
            title: '操作原因',
            dataIndex: 'unlockReason',
            align: 'center',
            width: 120,
          },
          {
            title: '操作时间',
            dataIndex: 'createTime',
            align: 'center',
            width: 120,
            render: (text, record, index) => {
              return _isNumber(text) ? moment(text).format('YYYY.MM.DD') : ''
            }
          },
          {
            title: '同意/拒绝理由',
            dataIndex: 'reason',
            align: 'center',
            width: 120,
          },
          {
            title: '操作',
            dataIndex: 'createTime',
            align: 'center',
            width: 120,
            render: (text, record, index) => {
              if (!_isEmpty(record.state)) {
                let _text = '';
                switch (record.state) {
                  case '1':
                    _text = '已同意';
                    break;
                  case '0':
                    _text = '已拒绝';
                    break;
                  case '-1':
                    _text = '无';
                    break;
                }
                return _text;
              } else {
                return (
                  <Fragment>
                    <a style={{ color: 'green' }} onClick={async () => {
                      const { code = 500, data = {} } = await unlock({
                        data: {
                          lockObject: query.lockObject,
                          code: record.unlockCode,
                          unlockReason: '',
                          uqCode: record.code,
                        }
                      });
                      if (code == 0) {
                        Tip.success('操作提示', '操作成功');
                        getList({ pageNum: pagination.pageNum })
                      }
                    }}>同意</a>
                    <Divider type="vertical" />
                    <a className={'del'} onClick={() => {
                      rejuectRef.current.open({ ...record });
                    }}>拒绝</a>
                  </Fragment>
                )
              }
            }
          },
        ]} />
      <IgnoreReason ref={rejuectRef} onOK={() => getList({ pageNum: pagination.pageNum })} />
    </div>
  )
}

export default index


const IgnoreReason = React.forwardRef((props: any, ref) => {
  const { onOK } = props;
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 },
    },
  };
  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
    setRecord({});
    form.resetFields();
  };
  const onFinish = async (e) => {
    setConfirmLoading(true);
    const { code = 500 } = await refuse({
      data: {
        code: record.code,
        reason: e.reason
      }
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOK && onOK();
    }
  };
  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      setRecord(query);
      // if (query) {
      //   form.setFieldsValue({ ...query.reqcord });
      // }
    },
    clear: () => {
      // clear();
    },
  }));
  return (
    <Modal
      title={'拒绝'}
      visible={visible}
      // onOk={() => {
      //   form.submit()
      // }}
      onCancel={handleCancel}
      width={600}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
      footer={[
        <Button key={1} htmlType={'button'} onClick={handleCancel} style={{ marginRight: '5px' }}>取消</Button>,
        <Button key={2} htmlType={'button'} type={'primary'} loading={confirmLoading} onClick={() => { form.submit() }}>确定</Button>
        // <Popconfirm placement="topRight" title={<span>
        //   本功能仅用于原库中党员入党时间有误的修正，请谨慎操作。
        // </span>} onConfirm={() => { form.submit() }}>
        // </Popconfirm>,
      ]}
    >
      {
        visible &&
        <Fragment>
          <Form form={form} {...formItemLayout} onFinish={onFinish}>
            <Form.Item name='reason'
              label="拒绝原因"
              rules={[{ required: true, message: '拒绝原因' },
                //  { validator: TimeValidator }
              ]}
            >
              <TextArea showCount maxLength={20} />
            </Form.Item>
          </Form>
        </Fragment>
      }

    </Modal>
  )
});
