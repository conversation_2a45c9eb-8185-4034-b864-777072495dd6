import React, { Fragment, useEffect, useRef, useState } from 'react';
import ListTable from '@/components/ListTable';
import Add from './add';
import Tip from '@/components/Tip';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs } from 'antd';
import { getSession } from '@/utils/session';
import { getList,delSecondary } from './services';

const index = (props) => {
  const newAddRef: any = useRef();
  let filterHeight = `calc(100vh - ${288}px)`;
  const [pagination, setPagination] = useState({ pageSize: 20, current: 1, total: 0 });
  const [listLoading, setListLoading] = useState(false);
  const [list, setList] = useState<any>([]);
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 50,
      align: 'center',
      render: (text, record, index) => {
        return (pagination['current'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    {
      title: '年度',
      width: 200,
      dataIndex: 'time',
    },
    {
      title: '党建工作经费（万元）',
      width: 200,
      dataIndex: 'fiscalFunds',
    },
    {
      title: '党费拨补工作经费（万元）',
      width: 200,
      dataIndex: 'partyExpenses',
    },
    {
      title: '党群活动服务中心（个）',
      width: 200,
      dataIndex: 'activityServiceCenter',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      render: (text, record) => {
        return (
          <div>
            <a onClick={() =>{
              newAddRef.current.open(record);
            }}>编辑</a>
            <Divider type="vertical"/>
            <Popconfirm title="确定要删除吗？" onConfirm={async () => {
              const {code = 500 } = await delSecondary({code:record.code});
              if(code === 0){
                Tip.success('操作提示', '操作成功');
                getLists({ pageNum: 1 });
              }
            }}>
              <a href={'#'} className={'del'}>删除</a>
            </Popconfirm>
          </div>
        )
      },
    }
  ];

  const getLists = async ({pageNum,pageSize:size}:any) => {
    const { unit:{ basicInfo={} } = {},onOK } = props;
    setListLoading(true);
    const {
      code = 500,
      data: { pageNumber: current = 1, pageSize = 20, totalRow: total = 0 ,list=[]} = {},
    } = await getList({
      data: {
        pageSize: size || pagination.pageSize,
        pageNum: pageNum || pagination.current,
        unitCode: basicInfo.code,
      },
    });
    setListLoading(false);
    if (code === 0) {
      setList(list);
      setPagination({ current, total, pageSize });
    }
  };

  useEffect(() => {
    getLists({ pageNum: 1 });
  }, []);

  const addOrEdit = () => {};

  return (
    <Fragment>
      <Button
        type={'primary'}
        icon={<LegacyIcon type={'plus'} />}
        onClick={() => newAddRef.current.open()}
        style={{ marginLeft: 16 }}
      >
        新增场地经费情况
      </Button>
      <div style={{ margin: 16 }}>
        <ListTable
          scroll={{ y: filterHeight }}
          columns={columns}
          data={list}
          pagination={pagination}
          showQuickJumper={true}
          onPageChange={(page, pageSize) => {
            getLists({ pageNum: page, pageSize });
          }}
        />
      </div>
      <Add
        {...props}
        ref={newAddRef}
        onOK={() => {
          getLists({ pageNum: 1 });
        }}
      />
    </Fragment>
  );
};
export default index;
