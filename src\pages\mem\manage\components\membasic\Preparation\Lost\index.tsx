import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Col, Form, Input, Modal, Radio, Row, Switch, Select } from 'antd';
import MemSelect from '@/components/MemSelect';
import Date from '@/components/Date';
import DictTreeSelect from '@/components/DictTreeSelect';
import DictSelect from '@/components/DictSelect';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { addUnitCountryside, updateUnitCountryside } from '@/pages/[unit]/services';
import moment from 'moment';
import Tip from '@/components/Tip';
import UnitSelect from '@/components/UnitSelect';
import { findDictCodeName } from '@/utils/method';
import { lostContact } from '@/pages/mem/services';
import UploadComp, { getInitFileList, fitFileUrlForForm } from '@/components/UploadComp';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const { width = 600 } = props;
  const [showOffTime, setshowOffTime] = useState(false)

  useImperativeHandle(ref, () => ({
    open: (query) => {
      const { record = {} } = query;
      if (record) {
        setDataInfo(record);
        form.setFieldsValue({ ...record });
        setshowOffTime(record?.hasStopParty)
      }
      setVisible(true);
    },
  }));
  const close = () => {
    setVisible(false);
    form.resetFields();
    clear();
  };
  const clear = () => {
    setDataInfo({});
    setConfirmLoading(false);
    form.resetFields();
    setshowOffTime(false)
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    const { onOK } = props;
    if (e['stopBasis'] && typeof e['stopBasis'] == 'object') {
      e['stopBasis'] = fitFileUrlForForm(e['stopBasis'])
    }
    let val = {
      ...e,
      lostContactDate: e.lostContactDate ? moment(e.lostContactDate).valueOf() : '',
      stopPartyDate: e.stopPartyDate ? moment(e.stopPartyDate).valueOf() : '',
      getTouchDate: e.getTouchDate ? moment(e.getTouchDate).valueOf() : '',
    };
    val = findDictCodeName(['d18'], val, {});
    setConfirmLoading(true);
    let url = lostContact;
    const { code = 500 } = await url({
      data: {
        ...val,
        code: dataInfo.code,
      },
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  const handleChange = (e) => {
    setshowOffTime(e)
  }
  return (
    <Fragment>
      <Modal
        title={'失联党员'}
        visible={visible}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
      >
        {visible && (
          <Fragment>
            <Form form={form} {...formItemLayout} onFinish={onFinish}>
              <Row>
                <Col span={24}>
                  <Form.Item
                    name="d18Code"
                    label="失去联系类型"
                    rules={[{ required: true, message: '请选择失去联系类型' }]}
                  >
                    <DictSelect
                      codeType={'dict_d18'}
                      initValue={dataInfo['d18Code']}
                      backType={'object'}
                    />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    name="lostContactDate"
                    label="失去联系时间"
                    rules={[{ required: true, message: '请选择失去联系时间' }]}
                  >
                    <Date />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    name="hasStopParty"
                    label="党籍处理情况"
                    rules={[{ required: true, message: '请选择党籍处理情况' }]}
                  >
                    <Select placeholder={'请选择党籍处理情况'} onChange={handleChange}>
                      <Select.Option value={0}>未停止党籍</Select.Option>
                      <Select.Option value={1}>停止党籍</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                {
                  showOffTime ?
                    <Fragment>
                      <Col span={24}>
                        <Form.Item
                          name="stopPartyDate"
                          label="停止党籍时间"
                          rules={[
                            {
                              required: true,
                              message: '请输入停止党籍时间',
                            },
                          ]}
                        >
                          <Date />
                        </Form.Item>
                      </Col>
                      <Col span={24}>
                        <Form.Item
                          name="workPost"
                          label="工作单位及职务 "
                          rules={[
                            {
                              required: true,
                              message: '请输入工作单位及职务',
                            },
                          ]}
                        >
                          <Input />
                        </Form.Item>
                      </Col>
                      {/* <Col span={24}>
                    <Form.Item
                      name="stopBasis"
                      label="停止党籍文件依据"
                      rules={[
                        {
                          required: false,
                          message: '请上传停止党籍文件依据',
                        },
                      ]}
                    >
                      <UploadComp maxLen={1}/>
                    </Form.Item>
                  </Col> */}
                    </Fragment>
                    : null
                }
                {dataInfo.d18Code ? (
                  <React.Fragment>
                    <Col span={24}>
                      <Form.Item
                        name="hasGetTouch"
                        label="是否取得联系"
                        rules={[{ required: true, message: '请选择是否取得联系' }]}
                      >
                        <Select placeholder={'请选择是否取得联系'} style={{ width: '100%' }} onChange={(val) => { setDataInfo({ ...dataInfo, hasGetTouch: val }) }}>
                          <Select.Option value={1}>是</Select.Option>
                          <Select.Option value={0}>否</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    {(function () {
                      return (
                        <Col span={24}>
                          <Form.Item
                            name="getTouchDate"
                            label="取得联系时间"
                            rules={[
                              {
                                required: form.getFieldValue('hasGetTouch') == 1,
                                message: '请选择取得联系时间',
                              },
                            ]}
                          >
                            <Date />
                          </Form.Item>
                        </Col>
                      );
                    })()}
                  </React.Fragment>
                ) : (
                  <React.Fragment />
                )}
              </Row>
            </Form>
          </Fragment>
        )}
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
