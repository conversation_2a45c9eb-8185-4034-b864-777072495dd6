/**
 * 扩展信息
 */
/**
 * 模块名
 */
import React, { Fragment, useEffect, useState, forwardRef, useImperativeHandle, useRef } from 'react';
import { Button, Popconfirm, Divider, Tabs, Form, Space } from "antd";
import Tip from '@/components/Tip';
import ListTable from '@/components/ListTable';
import MemSelect from '@/components/MemSelect'
import AddTrain from './addTrain';
import moment from 'moment'
import { changeListPayQuery } from '@/utils/method.js';
const FormItem = Form.Item;
const { TabPane } = Tabs;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
};

function index(props, ref) {
    const [form] = Form.useForm();
    const [pagination, setPagination]: any = useState({});
    const [list, setList] = useState([]);
    const { basicInfo } = props.memBasic
  const refs: any = useRef(null)
    useEffect(() => {
        findList()
    }, []);
    const findList = (pageNum = 1, pageSize = 10) => {
        props.dispatch({
            type: 'memTrain/getList',
            payload: {
                data: {
                    memCode: basicInfo?.code,
                    pageNum,
                    pageSize
                }
            }
        }).then(res => {
            const _res = changeListPayQuery(res);
            setList(_res?.list)
            setPagination(_res?.pagination)
            // setPagination({
            //     current: res['current'],
            //     pageSize: res['size'],
            //     total: res['total'],
            // })
        })
    }
    const openAdd = () => {
        refs.current.open({ type: 'add' })
    }
    const edit = (v) => {
        refs.current.open({ type: 'edit', ...v })
    }
    const del = (v) => {
        props.dispatch({
            type: 'memTrain/deleteTrainByCode',
            payload: {
                code: v?.code
            }
        }).then(res => {
            if (res.code === 0) {
                Tip.success('操作提示', '删除成功');
                findList()
            }
        })
    }
    const onPageChange = (page, size) => {
        findList(page, size)
    }
    const checkMem = (e) => {
        let id: any = []
        let memOrgCode: any = []
        e.map(i => {
            id.push(i?.code)
        });
        e.map(i => {
          memOrgCode.push(i?.memOrgCode)
        });

        refs.current.open({ type: 'add',id ,memOrgCode})
    }
    const columns = [
        {
            title: '序号',
            dataIndex: 'num',
            width: 58,
            render: (text, record, index) => {
                return (pagination?.current - 1) * pagination?.pageSize + index + 1
            }
        },
        {
            title: '培训机构',
            dataIndex: 'trainInstitution',
        },
        {
            title: '班次名称',
            dataIndex: 'classOrder',
            width: 110,
        },
        {
            title: '开班日期',
            dataIndex: 'startDate',
            width: 150,
            render: (text) => {
                return moment(text).format('YYYY-MM-DD')
            }
        },
        {
            title: '结业日期',
            dataIndex: 'endDate',
            render: (text) => {
                return moment(text).format('YYYY-MM-DD')
            }
        },
        {
            title: '培训主题',
            dataIndex: 'trainTheme',
        },
        {
            title: '操作',
            dataIndex: 'action',
            width: 100,
            render: (text, record) => {
                return (
                    <span>
                        <a onClick={() => edit(record)}>编辑</a>
                        <Divider type="vertical" />
                        <Popconfirm title="是否移除该信息?" onConfirm={() => del(record)} okText="是" cancelText="否">
                            <a className={'del'}>移除</a>
                        </Popconfirm>
                    </span>
                )
            }
        },
    ];
    return (

        <div>
            <Space style={{ margin: '10px 0' }}>
                <Button onClick={openAdd} type="primary" >添加</Button>
                {/* <MemSelect onChange={checkMem} checkType={'checkbox'}>
                    <Button type="primary">批量补录培训情况</Button>
                </MemSelect> */}
            </Space>
            <AddTrain {...props} ref={refs} onUp={() => findList()} />
            <ListTable columns={columns} data={list} pagination={pagination} onPageChange={onPageChange} />
        </div>
    );
}
export default forwardRef(index);
