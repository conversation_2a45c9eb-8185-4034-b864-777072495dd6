/**
 * 党支部标准化规范化建设达标/示范点情况
 */

import React, { Fragment, useState, useEffect } from 'react';
import { Button, Col, Input, Row, Switch, Form } from 'antd';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import DictSelect from '@/components/DictSelect';
import Tip from '@/components/Tip';
import Date from '@/components/Date';
import { findDictCodeName, formLabel, getCredit } from '@/utils/method';
import { findExampleSiteById, updateExampleSite } from '../../../services';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const index = (props: any) => {
  const { org: { basicInfo: basicData = {} } = {} } = props;
  const { code = '', orgCode = '' } = basicData;
  const [form] = Form.useForm();
  const [basicInfo, setBasicInfo] = useState({});
  const [sfdqk, setSfdqk] = useState(undefined);
  const [hasStandardUp, setHasStandardUp] = useState(false);

  const getBasicInfo = async (p?: any) => {
    const { code: resCode = 500, data = {} } = await findExampleSiteById({ orgCode: code });
    if (resCode == 0) {
      setBasicInfo(data);
      setSfdqk(data.d157Code);
      setHasStandardUp(data.hasStandardUp === '0' ? true : false);
      form.setFieldsValue({ ...data, hasStandardUp: data.hasStandardUp === '0' ? true : false });
    }
  };
  const onFinish = async (values: any) => {
    if (values?.exampleSiteDate) {
      values.exampleSiteDate = moment(values?.exampleSiteDate).valueOf();
    }
    if (values?.acceptTime) {
      values.acceptTime = moment(values?.acceptTime).valueOf();
    }
    if (values?.hasStandardUp) {
      // 0是 1否
      values.hasStandardUp = values?.hasStandardUp ? '0' : '1';
    } else {
      values.hasStandardUp = '1';
      values.acceptTime = undefined;
      values.acceptUnit = undefined;
    }
    if (values.hasStandardUp == '1') {
      values.acceptTime = undefined;
      values.acceptUnit = undefined;
    }
    if (_isEmpty(values?.d157Code)) {
      values.exampleSiteDate = undefined;
      values.identifyUnit = undefined;
      values.d157Name = undefined;
    } else {
      values = findDictCodeName(['d157'], values, basicInfo);
    }
    const { code = 500 } = await updateExampleSite({
      data: {
        ...basicInfo,
        ...values,
      },
    });
    if (code == 0) {
      Tip.success('操作提示', '修改成功');
    }
  };

  useEffect(() => {
    getBasicInfo();
  }, []);

  return (
    <Form {...formItemLayout} onFinish={onFinish} form={form}>
      <Row>
        {/* <Col span={12}>
          <Form.Item
            name="industryOrgName"
            label="是否为示范点"
            rules={[{ required: true, message: '是否为示范点' }]}
          >
            <Select placeholder={'请选择'}>
              <Select.Option value={'1'}>是</Select.Option>
              <Select.Option value={'0'}>否</Select.Option>
            </Select>
          </Form.Item>
        </Col> */}
        <Col span={12}>
          <Form.Item
            //   initialValue={''}
            name="d157Code"
            label="示范点情况"
            rules={[{ required: false, message: '示范点情况' }]}
          >
            <DictSelect
              onChange={(e: any) => {
                setSfdqk(e);
              }}
              codeType={'dict_d157'}
              backType={'object'}
              // mode="multiple"
              initValue={basicInfo['d157Code'] || undefined}
              placeholder={'请选择工作类型'}
            />
          </Form.Item>
        </Col>
        {!_isEmpty(sfdqk) && (
          <Fragment>
            <Col span={12}>
              <Form.Item
                name="exampleSiteDate"
                label="认定为示范点时间"
                rules={[{ required: true, message: '认定为示范点时间' }]}
              >
                <Date />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="identifyUnit"
                label="认定单位"
                rules={[{ required: true, message: '认定单位' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Fragment>
        )}
        <Col span={12}>
          <Form.Item
            name="hasStandardUp"
            label="是否达标"
            rules={[{ required: true, message: '是否达标' }]}
            valuePropName="checked"
          >
            <Switch
              onChange={(e: any) => {
                setHasStandardUp(e);
              }}
            />
          </Form.Item>
        </Col>
        {hasStandardUp && (
          <Fragment>
            <Col span={12}>
              <Form.Item
                name="acceptTime"
                label="达标验收时间"
                rules={[{ required: true, message: '达标验收时间' }]}
              >
                <Date />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="acceptUnit"
                label="达标验收单位"
                rules={[{ required: true, message: '达标验收单位' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Fragment>
        )}
      </Row>
      <div style={{ textAlign: 'center' }}>
        <Button
          type={'primary'}
          htmlType={'submit'}
          icon={<LegacyIcon type={'check'} />}
          style={{ marginRight: 16 }}
          loading={basicInfo['code'] ? false : false}
        >
          保存
        </Button>
        {/* <Button type={'primary'} danger htmlType={'button'} icon={<LegacyIcon type={'delete'} />} onClick={() => props.close({})}>取消</Button> */}
      </div>
    </Form>
  );
};

export default index;
