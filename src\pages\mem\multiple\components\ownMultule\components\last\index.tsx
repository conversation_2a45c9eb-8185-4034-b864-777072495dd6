import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Alert, Button, Row, Col, Divider, DatePicker } from 'antd';
import OrgSelect from '@/components/OrgSelect';
import DictSelect from '@/components/DictSelect';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _isNumber from 'lodash/isNumber';
import _get from 'lodash/get';
import moment from 'moment';
import {findDictCodeName,unixMoment} from '@/utils/method.js'
import Date from '@/components/Date';
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {

    }
  }
  lastSubmit=()=>{
    const {submit,memMultiple:{multipleInfo= {}}={}, memInfo = {}} = this.props;
    this.props.form.validateFieldsAndScroll(async(err, val) => {
      if (!err) {
        val = findDictCodeName(['joinOrgType'],val,multipleInfo);
        val = unixMoment(['joinOrgDate'],val);

        // 新增通过身份证取值，编辑通过code取值
        val['sexName'] = memInfo['sexCode'] === '1' ? '男' : '女';
        val['memName'] = memInfo['name'] || memInfo['memName'] ;

        val['memCode'] = !_isEmpty(multipleInfo) ?  memInfo['memCode'] : memInfo['code'];
        val['memCurrOrgCode'] = memInfo['orgCode'] || memInfo['memCurrOrgCode'];
        val['memCurrOrgName'] = memInfo['orgName'] || memInfo['memCurrOrgName'];
        val['memCurrManyOrgCode'] = memInfo['memOrgCode'] || memInfo['memCurrManyOrgCode'];

        ['joinOrgCode'].map(item=>{
          if(!_isEmpty(_get(val,`${item}[0]`,[]))){
            if(_isEmpty(multipleInfo)){
              val['joinManyOrgCode'] = val[`${item}`][0]['orgCode'];
              val['joinOrgName'] = val[`${item}`][0]['name'];
              val[`${item}`] = val[`${item}`][0]['code'];
            }else {
              if(_isArray(val[`${item}`]) && (val[`${item}`][0] !== multipleInfo[`${item}`])){
                val['joinManyOrgCode'] = val[`${item}`][0]['orgCode'];
                val['joinOrgName'] = val[`${item}`][0]['name'];
                val[`${item}`] = val[`${item}`][0]['code'];
              }else {
                val['joinManyOrgCode'] = multipleInfo['joinManyOrgCode'];
                val['joinOrgName'] = multipleInfo['joinOrgName'];
              }
            }
          }
        });

        submit && submit({...memInfo,...multipleInfo,...val});
      }
    });
  };
  render() {
    const {form,loading:{effects = {}} ={}, memInfo = {}, memMultiple:{multipleInfo = {}}={}} = this.props;
    const { getFieldDecorator } = form;
    let sex_name = memInfo['sexName'];
    if(_isEmpty(sex_name)){
      sex_name =  memInfo['sexCode'] === '1' ? '男' : memInfo['sexCode'] === '0' ? '女' : '暂无';
    }
    return (
      <div>
        <Form {...formItemLayout}>
          <Row>
            <Col span={12}>
              <FormItem
                {...formItemLayout2}
                label="姓名"
              >
                {memInfo['name'] || memInfo['memName'] || '暂无'}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                {...formItemLayout2}
                label="性别"
              >
                {sex_name}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                {...formItemLayout2}
                label="民族"
              >
                {memInfo['d06Name'] || '暂无'}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                {...formItemLayout2}
                label="籍贯"
              >
                {memInfo['d48Name'] || '暂无'}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                {...formItemLayout2}
                label="身份证"
              >
                {memInfo['idcard'] || '暂无'}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                {...formItemLayout2}
                label="人员类别"
              >
                {memInfo['d08Name'] || '暂无'}
              </FormItem>
            </Col>
            <Col span={24}>
              <FormItem
                label="组织关系"
              >
                {memInfo['orgName'] || memInfo['memCurrOrgName'] || '暂无'}
              </FormItem>
            </Col>
          </Row>
          <Divider/>
          <FormItem
            label="进入管理支部"
          >
            {getFieldDecorator('joinOrgCode', {
              rules: [{ required: true, message: '请选择进入管理支部' }],
              initialValue: _isEmpty(multipleInfo)?undefined:multipleInfo['joinOrgCode'],
            })(
              <OrgSelect orgTypeList={['3','4']} placeholder={'进入管理支部'} initValue={_isEmpty(multipleInfo)?undefined:multipleInfo['joinOrgName']} />
            )}
          </FormItem>
          <FormItem
            label="进入管理类型"
          >
            {getFieldDecorator('joinOrgTypeCode', {
              rules: [{ required: true, message: '请选择进入管理类型' }],
              initialValue: _isEmpty(multipleInfo)?undefined:multipleInfo['joinOrgTypeCode'],
            })(
              <DictSelect backType={'object'} codeType={'dict_d57'} placeholder="请选择" initValue={_isEmpty(multipleInfo)?undefined:multipleInfo['joinOrgTypeCode']}/>
            )}
          </FormItem>
          <FormItem
            label="进入管理时间"
          >
            {getFieldDecorator('joinOrgDate', {
              rules: [{ required: true, message: '请选择进入管理时间' }],
              initialValue: !_isNumber(multipleInfo['joinOrgDate'])?undefined:moment(multipleInfo['joinOrgDate']),
              // <DatePicker placeholder={'请选择进入管理时间'} style={{width:'100%'}}/>
            })(
              
              <Date />
            )}
          </FormItem>
          <div style={{textAlign:'center'}}>
            <Button type={'primary'} htmlType={'submit'} onClick={this.lastSubmit} loading={effects['memMultiple/save']}>确定</Button>
          </div>
        </Form>
      </div>
    );
  }
}
export default Form.create()(index);
