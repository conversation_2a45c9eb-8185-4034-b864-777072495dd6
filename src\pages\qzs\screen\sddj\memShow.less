.searchBg {
  background: url('../../../../assets/qzs/bg1.webp') no-repeat;
  background-size: 100% 100%;
}

.memShow {
  .searchBg;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 50px;
  .tit {
    width: 1100px;
    margin-bottom: 20px;
  }
  .box {
    margin-top: 132px;
    display: flex;
    .imgbox {
      margin-top: 88px;
      display: flex;
      flex-direction: column;
      align-items: center;
      > img {
        width: 175px;
        height: 250px;
      }
      .name {
        font-family: Source Han Serif SC;
        font-weight: 800;
        font-size: 30px;
        color: #be0c10;
        margin-bottom: 10px;
      }
      .shengri {
        font-family: Source Han Serif SC;
        font-weight: 800;
        font-size: 26px;
        color: #323232;
        margin-bottom: 10px;
        display: flex;
        width: 380px;
        > div:first-child {
          width: 130px;
          display: inline-block;
          text-align: justify;
          text-align-last: justify;
        }
        > div:last-child {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
          /* autoprefixer: on */
        }
      }
      .jieshaoren {
        font-family: Source Han Serif SC;
        font-weight: 800;
        font-size: 26px;
        color: #323232;
        margin-bottom: 10px;
        display: flex;
        width: 380px;
        > div:first-child {
          display: inline-block;
          width: 130px;
          text-align: justify;
          text-align-last: justify;
        }
        > div:last-child {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
          /* autoprefixer: on */
        }
      }
    }
  }
  .center {
    // background: url('../../../../assets/qzs/box1.png') no-repeat;
    // background-size: 100% 100%;
    width: 1100px;
    height: 790px;
    padding: 30px;
    z-index: 1;
    position: relative;

    font-family: Source Han Serif SC;
    font-weight: 800;
    font-size: 36px;
    color: #be0c10;
    line-height: 50px;
    text-align: left;
    display: flex;
    flex-direction: column;
    :global {
      .w-e-toolbar {
        display: none !important;
      }
      .w-e-text-container {
        height: 100% !important;
        border: none !important;
      }
      .w-e-text::-webkit-scrollbar {
        width: 8px !important;
        height: 8px !important;
      }
      .w-e-text::-webkit-scrollbar-thumb {
        border-radius: 5px !important;
        box-shadow: 0 0 2px transparent !important;
        background-color: #ddd !important;
      }
      .w-e-text::-webkit-scrollbar-track {
        border-radius: 0;
        box-shadow: inset 0 0 2px transparent;
      }
    }
    .info {
      flex: 1;
      overflow: hidden;
      // &:focus {
      //   border: none;
      //   outline: none;
      // }
      > div {
        height: 522px;
      }
    }

    .yulan {
      position: absolute;
      bottom: 94px;
      right: 20px;
    }

    .btnBox {
      text-align: right;
      .btn {
        background: rgb(190, 12, 16);
        color: rgb(255, 238, 174);
        font-size: 26px;
        height: 50px;
        border-radius: 10px;
      }
    }
  }
}
// :global {
//   .w-e-toolbar {
//     display: none !important;
//   }
// }

.signContainer {
  padding: 22px 16px 15px 16px;
  box-sizing: border-box;
  display: flex;

  & .signContent {
    position: relative;
    border: 1px solid #ccc;

    & .signTip {
      color: #ccc;
      font-size: 30px;
      text-align: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  & .canvasContainer {
    border-radius: 10px;
    // background: #fff;
  }
}
.buttonContainer {
  margin-left: 16px;
  display: flex;
  flex-direction: row;
  margin-top: 12px;

  & .clearBtn {
    // width: 164px;
    // font-size: 18px;
    // color: #ac9374;
    // border-radius: 5px;
    // border-radius: 5px;
    // background: none;
    // border: 1px solid #ac9374;

    background: #be0c10;
    color: #ffeeae;
    font-size: 26px;
    height: 50px;
    border-radius: 10px;

    &::before {
      border: none;
    }
  }

  & .signBtn {
    // width: 164px;
    // margin-left: 15px;
    // font-size: 18px;
    // background: linear-gradient(135deg, #d8bb9a 0%, #8b6e4c 100%);
    // border-radius: 5px;
    // color: #fff;
    // border: none;
    margin-left: 20px;
    background: #be0c10;
    color: #ffeeae;
    font-size: 26px;
    height: 50px;
    border-radius: 10px;

    &::before {
      border: none;
    }
  }
}

.muban {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  .mubanTab {
    flex: 1;
    display: flex;
    flex-direction: column;
    :global {
      .ant-tabs-tab {
        background: #be0c10 !important;
        color: white !important;
      }
      .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #ffeeae !important;
      }
    }
    .mubanBody {
      flex: 1;
      overflow: auto;
      font-family: Source Han Serif SC;
      font-weight: 800;
      font-size: 20px;
      color: #be0c10;
      p {
        margin: 0 !important;
      }
    }
  }
  .buttonMubanTab {
    display: flex;
    flex-direction: row;
    margin-top: 12px;
    justify-content: flex-end;

    & .clearBtn {
      // width: 164px;
      // font-size: 18px;
      // color: #ac9374;
      // border-radius: 5px;
      // border-radius: 5px;
      // background: none;
      // border: 1px solid #ac9374;

      background: #be0c10;
      color: #ffeeae;
      font-size: 20px;
      height: 40px;
      border-radius: 10px;

      &::before {
        border: none;
      }
    }

    & .signBtn {
      // width: 164px;
      // margin-left: 15px;
      // font-size: 18px;
      // background: linear-gradient(135deg, #d8bb9a 0%, #8b6e4c 100%);
      // border-radius: 5px;
      // color: #fff;
      // border: none;
      margin-left: 20px;
      background: #be0c10;
      color: #ffeeae;
      font-size: 20px;
      height: 40px;
      border-radius: 10px;

      &::before {
        border: none;
      }
    }
  }
}
