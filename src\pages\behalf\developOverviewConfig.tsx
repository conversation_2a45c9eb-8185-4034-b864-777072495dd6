import React from 'react';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';

export const cardConfig = [
  // {
  //   key:'4001',
  //   value:{
  //     icon:'team',
  //     coverImg:require('@/components/CardsGroup/assets/develop/fazhan.jpg'),
  //     iconColor:'#17C1C5',
  //     title:'发展党员总数',
  //     suffix:'人',
  //     action:'/api/chart/develop/getDevelopTotal',
  //     end:(val)=>{
  //       let num = 0;
  //       if(!_isEmpty(val) && _isArray(val)){
  //         val.forEach(item=>{
  //           if(item['sexName'] === '发展党员总数'){
  //             num = item['count'] ;
  //           }
  //         });
  //       }
  //       return num;
  //     },
  //     content:(data)=>{
  //       let num = 0;
  //       let num2 = 0;
  //       if(!_isEmpty(data) && _isArray(data)) {
  //         data.forEach(item => {
  //           if (item['sexName'] === '男性数量') {
  //             num = item['count'];
  //           }
  //           if(item['sexName'] === '女性数量'){
  //             num2 = item['count'];
  //           }
  //         });
  //       }
  //       return(
  //         <div>
  //           <div>男性党员:{num}人</div>
  //           <div>女性党员:{num2}人</div>
  //         </div>
  //       )
  //     },
  //   },
  // },
  // {
  //   key:'4005',
  //   value:{
  //     icon:'hourglass',
  //     coverImg:require('@/components/CardsGroup/assets/develop/shenqingren.jpg'),
  //     iconColor:'#6F79C1',
  //     title:'入党申请人',
  //     suffix:'人',
  //     action:'/api/chart/develop/getApplicantTotal',
  //     end:(val)=>{
  //       let num = 0;
  //       if(!_isEmpty(val) && _isArray(val)){
  //         val.forEach(item=>{
  //           if(item['type'] === 'total'){
  //             num = item['count'] ;
  //           }
  //         });
  //       }
  //       return num;
  //     },
  //     content:(data)=>{
  //       let num = 0;
  //       let num2 = 0;
  //       if(!_isEmpty(data) && _isArray(data)) {
  //         data.forEach(item => {
  //           if (item['sexCode'] === '1') {
  //             num = item['count'];
  //           }
  //           if(item['sexCode'] === '0'){
  //             num2 = item['count'];
  //           }
  //         });
  //       }
  //       return(
  //         <div>
  //           <div>男性党员:{num}人</div>
  //           <div>女性党员:{num2}人</div>
  //         </div>
  //     )},
  //   },
  // },
];
export const chartConfig = [
  {
    key:'7001', // 男女比例
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_nannvbili.jpg'),
      action:'/api/chart/party/congress/getSexRatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['sexName']);
            arr.push({
              name:item['sexName'],
              value:item['count']
            })
          });
        }
        return {
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01]
          },
          yAxis: {
            type: 'category',
            data:arrName
          },
          title : {
            text: '男女比例',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c}"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series: [
            {
              name: '男女比例',
              type: 'bar',
              data: arr,
              barWidth: '50%',
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ],
        }
      },
    }
  },
  {
    key:'7002', // 学历情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_xueli.jpg'),
      action:'/api/chart/party/congress/getEducationRatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d07Name']);
            arr.push({
              name:item['d07Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '学历情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series: [
            {
              name:'学历情况',
              type:'pie',
              center: ['50%', '60%'],
              radius: ['40%', '60%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '24',
                    fontWeight: 'bold'
                  }
                }
              },
              labelLine: {
                normal: {
                  show: true
                }
              },
              data:arr
            }
          ]
        }
      },
    }
  },
];
