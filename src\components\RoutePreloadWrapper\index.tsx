import React, { useEffect } from 'react';
import { useRoutePreloader } from '@/hooks/useRoutePreloader';

interface RoutePreloadWrapperProps {
  children: React.ReactNode;
  enablePreload?: boolean;
  commonRoutes?: string[];
}

/**
 * 路由预加载包装组件
 * 用于在应用启动时自动预加载常用路由
 */
const RoutePreloadWrapper: React.FC<RoutePreloadWrapperProps> = ({
  children,
  enablePreload = true,
  commonRoutes = []
}) => {
  const {
    getPreloadStats
  } = useRoutePreloader({
    commonRoutes
  });

  useEffect(() => {
    if (!enablePreload) return;

    // 开发环境下显示预加载统计信息
    if (process.env.NODE_ENV === 'development') {
      const logStats = () => {
        const stats = getPreloadStats();
        console.log('📊 路由预加载统计:', stats);
      };

      // 定期输出统计信息
      const interval = setInterval(logStats, 10000); // 每10秒输出一次

      return () => clearInterval(interval);
    }
  }, [enablePreload, getPreloadStats]);

  return <>{children}</>;
};

export default RoutePreloadWrapper;
