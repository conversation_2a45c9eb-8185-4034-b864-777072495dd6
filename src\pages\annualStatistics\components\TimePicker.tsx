import React from "react";
import { DatePicker, Input, Modal } from 'antd';
import moment from 'moment';
import {isEmpty} from '@/utils/method';

interface Interface {
  value?: any,
  size?: 'large' | 'small' | 'default',
  pickerOnChange?:(val:moment.Moment)=>void,
  mode?:"year" | "time" | "date" | "month",
  format?:string,
  onChange?:(any)=>void,
  onBlur?:Function,
  onError?:Function,
  initValue?: any,
  startTime?:moment.Moment,
  endTime?:moment.Moment,
  placeholder?:string,
  disabled?: boolean,
  allowClear?: boolean,
  style?:any,
  regNowDate?:boolean
}

const reg = /(\d{4}\.(0[1-9]|1[0-2])|\d{4}[0-1][0-9][0-3][0-9]|\d{4}[0-1][0-9]|\d{4}-(0[0-9]|1[0-2]))/;
const reg2 = /(\d{4})/;
export default class TimeFormat extends React.Component<Interface,any>{

  static defaultProps = {
    placeholder: '请输入'
  };

  constructor(props){
    super(props);
    this.state={
      value:undefined,
    };
  }
  change=(e)=>{
    const {onChange,format='YYYY.MM.DD'}=this.props;
    let value=e.target.value,lastValue:any=undefined;
    if (value.length > format.length) {
      return;
    }
    this.setState({
      value,
    });
    if(value){
      let mat='YYYYMMDD';
      if(value.includes('.')){
        mat='YYYY.MM.DD';
      }
      lastValue=moment(value,mat);
      if(lastValue.format(format) === 'Invalid Date'){
        lastValue=value;
      }
    }
    onChange && onChange(lastValue);
  };
  format=(e)=>{
    const str=e.target.value;
    let {format='YYYYMMDD',onChange,onBlur,onError,endTime, regNowDate = true}=this.props;
    if(!str){
      onChange && onChange(undefined);
      onBlur && onBlur(undefined);
      return
    }
    if(str.includes('.')){
      format='YYYY.MM.DD';
    };
    if(str.length<8){
      format='YYYY.MM'
    };
    if(str.length<8 && this.props.mode ==='year'){
      format='YYYY'
    };
    let lastValue=moment( str,format ).format(format);
    if(lastValue == 'Invalid date' || lastValue == 'Invalid Date'){
      onChange && onChange(undefined);
      this.setState({
        value:undefined
      });
    }else{
      if (!(reg.test(str) || (reg2.test(str) && this.props.mode === 'year'))) {
        this.setState({
          value:null,
        });
        onChange && onChange(undefined);
        onError && onError('时间格式错误');
      }else{
        if(regNowDate){
          if(endTime!=undefined){
            if(moment(lastValue).endOf('day') > moment(endTime).endOf('day')){
              return this.errMsg();
            }
          }else{
            if(moment(lastValue).endOf('day') > moment().endOf('day')){
              return this.errMsg();
            }
          }
        }
        onBlur && onBlur(lastValue);
        this.setState({
          // value:moment(str,format).format(format),
          value:lastValue
        });
      }
    }
  };
  errMsg=()=>{
    const {onChange}=this.props;
    onChange && onChange(undefined);
    this.setState({
      value:undefined
    });
    return Modal.error({
      title: '错误信息提示',
      content: (
        <div>
          <p>输入日期不能晚于当前日期</p>
        </div>
      ),
      onOk() {},
    })
  };
  static getDerivedStateFromProps = (nextProps:any, prevState:any) => {
    const {value,initValue}=prevState;

    if ('initValue' in nextProps && !nextProps['initValue'] && initValue) {
      console.log(nextProps.initValue,'nextProps.initValue')
      return {value:nextProps.initValue}
    }
    return null
  };
  render(){
    const {placeholder,format='YYYY.MM',disabled,size,allowClear=false,style,initValue}=this.props;
    let lastValue=this.state.value;
    if(lastValue===undefined && initValue!=undefined){
      lastValue=moment( initValue ).format(format);
      if(lastValue === 'Invalid date' || lastValue == 'Invalid Date'){
        lastValue=initValue;
      }
    }

    return(
      <Input
        allowClear={allowClear}
        placeholder={placeholder}
        value={lastValue}
        onChange={this.change}
        onBlur={this.format}
        disabled={disabled}
        size={size}
        style={style}
      />
    )
  }
}
