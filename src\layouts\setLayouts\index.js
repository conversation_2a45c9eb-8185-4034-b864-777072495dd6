import React from 'react';
import { ConfigProvider, Input, Layout, Modal, Form, Spin } from 'antd';
import Avater from '../avater';
import HMenu from '../header';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';
import {connect} from "dva";
import styles from '../index.less';
import {_history} from "@/utils/method";
import Notice from 'src/components/Tip';
import SplitterLayout from 'react-splitter-layout';
import {upPassword} from '../../services';
import {EditOutlined,QuestionCircleOutlined,LoginOutlined} from "@ant-design/icons";

moment.locale('zh-cn');
const { Header, Sider,Content } = Layout;
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};
@connect(({login,common,user})=>({login,common,user}))
class BasicLayout extends React.Component{
  formRef = React.createRef();
  constructor(props){
    super(props);
    this.state={
      header:undefined,
      visible:false,
      org:undefined,
    }
  }
  headerChange=(val)=>{
    this.setState({header:val})
  };
  loadData=async (val)=>{
    return await this.props.dispatch({
      type:'common/getTree',
      payload:{
        data:{
          orgCodeList:val,
          excludeOrgCodeList:[]
        }
      }
    });
  };
  treeSearch=(val)=>{
    this.props.dispatch({
      type:'common/queryTree',
      payload:{
        name:val
      }
    });
  };
  treeChange=(selectedKeys, e)=>{
    console.log(selectedKeys,e)
    const {dataRef}=e.node;
    this.setState({
      org:dataRef
    })
  };
  onChangePW=()=>{
    this.setState({visible:true})
  };
  validFunction=(rule, value, callback)=>{
    if (value){
      switch (rule.field) {
        case 'password':
          // if (value.length < 6 || value.length > 12) {
          //   return callback('密码长度应为6-12个字符');
          // } else if (/[\u4e00-\u9fa5]+/.test(value)) {
          //   return callback('密码不能包含汉字');
          // } else {
          //   this.setState({ newPassword: value });
          // }
          if (value.length < 6 || value.length > 12) {
            return callback('密码长度应为6-12个字符');
          } else if (!(/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/).test(value)) {
            return callback('格式有误')
          } else if ((/\s+/g).test(value)) {
            return callback('密码不能包含空格')
          } else if (/[\u4e00-\u9fa5]+/.test(value)) {
            return callback('密码不能包含汉字');
          } else {
            this.setState({ newPassword: value });
          }
          break;
        case 'isPassword':
          // if (value.length < 6 || value.length > 12) {
          //   return callback('密码长度应为6-12个字符');
          // } else if (/[\u4e00-\u9fa5]+/.test(value)) {
          //   return callback('密码不能包含汉字');
          // } else if (this.state.newPassword !== value) {
          //   return callback('两次密码不匹配');
          // }
          if (value.length < 6 || value.length > 12) {
            return callback('密码长度应为6-12个字符');
          } else if (!(/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/).test(value)) {
            return callback('格式有误')
          } else if ((/\s+/g).test(value)) {
            return callback('密码不能包含空格')
          } else if (/[\u4e00-\u9fa5]+/.test(value)) {
            return callback('密码不能包含汉字');
          } else if (this.state.newPassword !== value) {
            return callback('两次密码不匹配');
          }
          break;
        default:
          break;
      }
    }
    callback()
  };
  handleOk=async (values)=>{
    const id=JSON.parse(sessionStorage.user)['id'];
    const { isPassword,...val }=values;
    const res = await upPassword({
      data:{
        ...val,
        id
      }
    });
    if (res.code===0){
      Notice.info("操作提示",'密码修改成功');
      sessionStorage.clear();
      this.props.dispatch({
        type:'common/clear',
        payload:{},
      });
      this.props.dispatch({
        type:'login/clear',
        payload:{},
      });
      this.handleCancel();
      window.location.replace('/login');
    } else {
      Notice.info("操作提示",'密码修改失败');
    }
  };
  handleCancel=()=>{
    this.formRef.current.resetFields();
    this.setState({
      visible:false
    });
  };
  userMenuOnclick = ({key = ''} = {})=>{
    switch (key) {
      case '1': // 修改密码
        // Notice.info("操作提示",'功能正在升级中');
        this.onChangePW();
        break;
      case '2': // 操作说明
        Notice.info("操作提示",'功能正在升级中');
        break;
      case '3': // 安全退出
        this.props.dispatch({
          type:'common/clear',
          payload:{},
        });
        this.props.dispatch({
          type:'login/clear',
          payload:{},
        });
        this.props.dispatch({
          type:'common/toLogout',
          payload:{
          }
        });
        sessionStorage.clear();
        window.location.replace('/login');
        Notice.success('操作提示','安全退出成功');
        break;
      default:
        break;
    }
  };
  toDesktop=()=>{
    this.setState({
      header:undefined
    });
    window.location.replace('/login');
  };
  onDragEnd=()=>{
    const e = document.createEvent('Event');
    e.initEvent('resize', true, true);
    window.dispatchEvent(e);
  };
  render() {
    const {token,account}=sessionStorage;
    const Menus=[
      {
        name:'基础配置', code:'set_1', icon:'copy', parent:'set', url:'/setting/basic',
      },
      {
        name:'字典配置', code:'set_2', icon:'copy', parent:'set', url:'/setting/dict',
      },
      {
        name:'逻辑配置', code:'set_3', icon:'copy', parent:'set', url:'/setting/logic',
      },
    ];
    const userMenuItems = [
      {key:'1',name:'修改密码',icon:<EditOutlined/>},
      // {key:'2',name:'操作说明',icon:<QuestionCircleOutlined />},
      {key:'3',name:'安全退出',icon:<LoginOutlined />},
    ];
    if(!token && account!=='admin'){
      return window.location.replace('/login');
    }
    return (
      <ConfigProvider locale={zh_CN}>
        <Layout>
          <Header className={styles.header}>
            <div className={styles.logoDiv} onClick={this.toDesktop}>
              {/*<img src={require('@/assets/dh.png')}/>智慧云党建平台*/}
              <img src={require('@/assets/smallLogo.png')} />
            </div>
            <HMenu data={Menus} allData={Menus} headerChange={this.headerChange}/>
            <Avater userMenuItems={userMenuItems} userMenuOnclick={this.userMenuOnclick}/>
          </Header>
          <Layout className={styles.Layout}>
            <div style={{padding: '0 12px', minHeight: 280,background:'#fff',height:'100%',overflow:'auto'}}>
              {this.props.children}
            </div>
          </Layout>
          <Modal
            title="修改密码"
            visible={this.state.visible}
            onOk={()=>this.formRef.current.submit()}
            onCancel={this.handleCancel}
            width={400}
            maskClosable={false}
          >
            {
              this.state.visible &&
              <Form {...formItemLayout} ref={this.formRef} onFinish={this.handleOk}>
                <FormItem label={'旧密码'} name={'oldPassword'}
                  rules={[{required:true,message:'请输入！'}]}
                >
                  <Input.Password placeholder="请输入当前密码" />
                </FormItem>
                <FormItem
                  label={'新密码'}
                  name={'password'}
                  rules={[{ required: true, message: '请输入!' }, { validator: this.validFunction }]}
                >
                  <Input.Password placeholder="请输入新密码" />
                </FormItem>
                <FormItem
                  label={'确认密码'}
                  name={'isPassword'}
                  rules={[{ required: true, message: '请输入!' }, { validator: this.validFunction }]}
                >
                  <Input.Password placeholder="请确认新密码" />
                </FormItem>
              </Form>
            }
          </Modal>
        </Layout>
      </ConfigProvider>
    );
  }
}

export default BasicLayout;
