import React, { useState, useEffect, useRef } from 'react'
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Popconfirm} from 'antd';
import ListTable from '@/components/ListTable';
import Add from './components/Add';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import { nonPublicGetList, delNonPublic } from '@/pages/org/services/org';
const index = (props: any) => {
  const { org: { basicInfo = {} } = {} } = props;
  const addRef:any = useRef();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const [pagination, setPagination] = useState<any>({ pageNum: 1, pageSize: 20, total: 0 });

  const getList = async (p?) => {
    setLoading(true);
    const {
      code = 500,
      data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await nonPublicGetList({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.pageNum,
        orgCode:basicInfo['code'],
        // ...keyword,
        ...p,
      },
    });
    setLoading(false);
    if (code === 0) {
      setList(list);
      setPagination({ pageNum, total, pageSize,current: pageNum});
    }
  };

  useEffect(() => {
    getList({pageNum:1})
  }, [])

  return (
    <div>
      <div style={{marginBottom:10}}>
      <Button type={'primary'} onClick={()=>{
        addRef.current.open();
      }}>新增</Button>
      </div>
      <ListTable
        
        columns={[
          {
            title: '序号',
            dataIndex: 'num',
            align: 'center',
            width: 50,
            render: (text, record, index) => {
              return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
            },
          },
          {
            title: '年度',
            dataIndex: 'year',
            align: 'center',
          },
          {
            title: '组织部门或社会工作部门（非公党工委）直接联系的非公企业党组织数',
            // title: '组织部门（非公党工委）直接管理的非公企业党组织数',
            dataIndex: 'manageOrganizationNumber',
            align: 'center',
          },
          {
            // title: '组织部门（非公党工委）直接联系的非公企业党组织数',
            title: '组织部门或社会工作部门(非公党工委）直接管理的非公企业党组织数',
            dataIndex: 'connectOrganizationNumber',
            align: 'center',
          },
          {
            title: '党费拨补非公企业党建工作经费（万元）',
            dataIndex: 'partyExpenses',
            align: 'center',
          },
          {
            title:'操作',
            dataIndex:'action',
            width:100,
            render:(text,record)=>{
              return(
                <span>
                  <a onClick={()=>{
                    addRef.current.open(record);
                  }}>编辑</a>
                  <Divider type="vertical"/>
                  <Popconfirm title="确定要删除吗？" onConfirm={ async()=>{
                    const {code = 500} = await delNonPublic({code:record.code});
                    if(code === 0){
                      Tip.success('操作提示','删除成功');
                      getList({ pageNum:1})
                    }
                  }}>
                   <a className={'del'}>删除</a>
                  </Popconfirm>
                </span>
              )
            },
          },
        ]}
        data={list}
        pagination={pagination}
        onPageChange={(page, pageSize) => getList({ pageNum: page, pageSize })}
        rowKey={'code'}
      />
      <Add ref={addRef} {...props} onOK={()=>{
        getList({ pageNum:1 })
      }}/>
    </div>
  )
}

export default index;
