import request from "@/utils/request";
import qs from 'qs';


export function add(params) {//工作动态新增
  return request(`/api/trend/addTrend`,{
    method:'POST',
    body:params,
  });
}

export function updateTrend(params) {//工作动态新增
  return request(`/api/trend/updateTrend`,{
    method:'POST',
    body:params,
  });
}

export function getList(params) {//工作动态新增
  return request(`/api/trend/listTrend`,{
    method:'POST',
    body:params,
  });
}

export function pushTrend(params) {//发布工作动态
  return request(`/api/trend/pushTrend`,{
    method:'POST',
    body:params,
  });
}

export function cancelTrend(params) {//撤销工作动态
  return request(`/api/trend/cancelTrend`,{
    method:'POST',
    body:params,
  });
}

export function delTrend(params) {//删除工作动态
  return request(`/api/trend/delTrend`,{
    method:'POST',
    body:params,
  });
}

export function portalTrend(params) {//推送工作动态
  return request(`/api/trend/portalTrend`,{
    method:'POST',
    body:params,
  });
}

export function checkTrend(params) {//审核工作动态
  return request(`/api/trend/checkTrend`,{
    method:'POST',
    body:params,
  });
}

export function checkTrendList(params) {//获取与我相关的审核
  return request(`/api/trend/checkTrendList`,{
    method:'POST',
    body:params,
  });
}

export function afreshTrend(params) {//重新发布动态
  return request(`/api/trend/afreshTrend`,{
    method:'POST',
    body:params,
  });
}

export function findTrendByCode(params) {//重新发布动态
  return request(`/api/trend/findTrendByCode?${qs.stringify(params)}`)
}

