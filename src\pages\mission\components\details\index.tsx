import React, { Fragment } from 'react';
import style from './index.less';
import { Avatar, Modal, Tabs, List, Divider, Popconfirm } from 'antd';

import ListView from '@/components/List';
import Detail from '@/components/Detail';
import Tip from '@/components/Tip';

import _get from 'lodash/get';
import _isNumber from 'lodash/isNumber';
import _isEqual from 'lodash/isEqual';

import moment from 'moment';
import { isEmpty } from '@/utils/method';
import {frequencyData} from '../../config';
import {findCheckList,remindTask,removeTask,checkTask} from '../../services';

const TabPane = Tabs.TabPane;
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible:false,
      record:{},
      show1:true,
      show2:true
    }
  }
  handleCancel=()=>{
    this.destory();
    this.setState({
      visible:false
    })
  };
  destory=()=>{
    this.setState({
      record:{},
      show1:true,
      show2:true
    })
  };
  open=(record)=>{
    this.setState({
      visible:true,
      record
    })
  };
  pass= async (record)=>{
    const {code = ''} = record;
    const res = await checkTask({data:{code,status:3,remark:''}});
    console.log(res,'res');
    const {code:resCode = 500} = res;
    if(record === 0){
      Tip.success('操作提示','操作成功')
    }
  };
  del=(record)=>{

  };
  notice= async (record)=> {
    const {code} = record;
    const res = await remindTask({data:{code}});
    const {code:resCode = 500} = res;
    if(resCode === 0){
      Tip.success('操作提示','操作成功');
    }
  };
  moveOut= async (record)=>{
    const {code} = record;
    const res = await removeTask({data:{code}});
    const {code:resCode =500} = res;
    if(resCode === 0){
      await this.setState({
        show1:false
      });
      this.setState({
        show1:true
      })
    }
  };
  renderItem=(item)=>{
    return (
      <List.Item>
        <div className={style.box}>
          <div>
            {item['code']}
          </div>
          <div className={style.action}>
            <a onClick={()=>this.pass(item)}>通过</a>
            <Divider type="vertical"/>
            <Popconfirm title={'是否退回？'} onConfirm={()=>this.del(item)}>
              <a className={'del'} >退回</a>
            </Popconfirm>
          </div>
        </div>
      </List.Item>
    )
  };
  renderItem2=(item)=>{
    return (
      <List.Item>
        <div className={style.box}>
          <div>
            {item['code']}
          </div>
          <div className={style.action}>
            <a onClick={()=>this.notice(item)}>提醒</a>
            <Divider type="vertical"/>
            <Popconfirm title={'是否移除？'} onConfirm={()=>this.moveOut(item)}>
              <a className={'del'} >移除</a>
            </Popconfirm>
          </div>
        </div>
      </List.Item>
    )
  };
  renderTaskTalk=(record)=>{
    const {show1,show2} = this.state;
    return (
      <Tabs defaultActiveKey="1" >
        <TabPane tab="未提交" key="1">
          {
            show1 &&
            <ListView action={'/api/task/findCheckList'}
                      payload={{type:1,code:record['code']}}
                      renderItem={this.renderItem2}
                      method={'Get'}
            />
          }
        </TabPane>
        <TabPane tab="已提交" key="2">
          {
            show2 &&
            <ListView action={'/api/task/findCheckList'}
                      payload={{type:2,code:record['code']}}
                      renderItem={this.renderItem}
                      method={'Get'}
            />
          }

        </TabPane>
      </Tabs>
    )
  };
  render() {
    const {visible,record} = this.state;
    const {taskStatus} = record;
    const itemsArr = [
      {
        leftTitle:'任务名称', leftInfo:_get(record,'taskName','暂无')
      },
      {
        leftTitle:'开始时间', leftInfo: !_isNumber(_get(record,'startDate',undefined)) ? '暂无' : moment(record['startDate']).format('YYYY年MM月DD日'),
        rightTitle:'结束时间', rightInfo: !_isNumber(_get(record,'endDate',undefined)) ? '暂无' : moment(record['endDate']).format('YYYY年MM月DD日'),
      },
      {
        leftTitle:'任务周期', leftInfo:_get(frequencyData.find(item=>item['key'] === _get(record,'taskCycle','')),'name','暂无')
      },
      {
        leftTitle:'任务分值', leftInfo:_get(record,'taskFraction','暂无')
      },
      {
        leftTitle:'任务备注', leftInfo:_get(record,'taskRemark','暂无'),showAll:true
      },
    ];
    const middleRows = [
      {title:'审核情况', content:this.renderTaskTalk(record)},
      // {title:'这是标题3', content:'可以改变颜色,默认是蓝色', color:'red'}
    ];
    return (
      <Modal
        title={'详情'}
        visible={visible}
        onCancel={this.handleCancel}
        width={1200}
        bodyStyle={{height:600,overflow:'auto',padding:'0 200px',background:'#fff'}}
        footer={null}
      >
        {
          !isEmpty(record) &&
            <Fragment>
              <Detail itemsArr={itemsArr} middleRows={taskStatus !== 2 ? middleRows : []} />
            </Fragment>
        }
      </Modal>
    );
  }
}
