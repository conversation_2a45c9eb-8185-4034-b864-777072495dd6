import React, { useEffect, useImperativeHandle, useState } from 'react';
import { Button, Divider, Form, Input, Modal, Popconfirm, Tag, Transfer } from 'antd';
import OrgSelect from '@/components/OrgSelect';
import ListTable from '@/components/ListTable';
import Tip from '@/components/Tip';
import {memGetList,split} from '../../services/org';
import { changeListPayQuery, isEmpty } from '@/utils/method';
import WhiteSpace from '@/components/WhiteSpace';
function breakUp(props,ref) {
  const [visible,setVisible]=useState(false);
  const [edit,setEdit]=useState({});
  const [list,setList]:any=useState([]);
  const [pagination,setPagination]:any=useState();
  const [keys, setKeys] = useState([]);
  const [rows, setRows] = useState([]);
  const [name, setName]:any = useState();
  useImperativeHandle(ref, () => ({
    open: (val: any) => {
      setEdit(val);
      setVisible(true);
    },
  }));
  const onCancel=()=>{
    setVisible(false);
    setEdit({});
    setList([]);
    setRows([]);
    setKeys([]);
  }
  useEffect(()=>{
    getMem();
  },[JSON.stringify(edit)])
  const getMem = () => {
    if(edit['orgCode']){
      memGetList({
        data:{
          memOrgCode:edit['orgCode'],
          pageNum: 1,
          pageSize: 10,
          searchType: 1,
        }
      }).then(res=>{
        if(res['code']==0){
          let resList = changeListPayQuery(res['data']);
          setList(resList['list']);
          setPagination(resList['pagination']);
        }
      })
    }
  }
  const onOk=()=>{
    const {callBack}=props;
    let memIds:any=[];
    rows.forEach(obj=>{
      memIds.push(obj['id'])
    })
    split({
      data:{
        code:edit['code'],
        name,
        memIds,
      }
    }).then(res=>{
      if(res['code']==0){
        Tip.success('操作提示','组织拆分成功');
        onCancel();
        callBack && callBack();
      }
    })
  }

  const mockData:any = [];
  for (let i = 0; i < 20; i++) {
    mockData.push({
      key: i.toString(),
      title: `content${i + 1}`,
      description: `description of content${i + 1}`,
    });
  }
  const onChange = (keys,rows) => {
    console.log(keys,rows)
    setKeys(keys);
    setRows(rows);
  };
  const columns=[
    {
      title:'名称',
      dataIndex:'name',
    },
    {
      title:'性别',
      dataIndex:'sexCode',
      width:100,
      render:(text)=>{
        return (
          <span> {text === '1' ?  '男' : '女'} </span>
        )
      }
    },
    {
      title:'公民身份证',
      dataIndex:'idcard',
    },
    {
      title:'电话',
      dataIndex:'phone',
    },
  ];

  const rowSelection = {
    selectedRowKeys:keys,
    type:'checkBox',
    onChange: onChange,
  };
  return(
    <Modal
      destroyOnClose
      title={'组织拆分'}
      visible={visible}
      onOk={onOk}
      onCancel={onCancel}
      width={1000}
      bodyStyle={{maxHeight:'80vh',overflow:'auto'}}
    >
      <Form>
        <Form.Item label={'新组织名称'} name={'name'}>
          <Input onBlur={e=>setName(e.target.value)}/>
        </Form.Item>
        <div>
          <div>
            <span className={'del'}>已选择人员：</span>{rows.map(obj=><Tag>{obj['name']}</Tag>)}
          </div>
          <WhiteSpace/>
          <ListTable rowSelection={rowSelection} rowKey={'code'} data={list} columns={columns} pagination={pagination}/>
        </div>
      </Form>
    </Modal>
  )
}
export default React.forwardRef(breakUp)
