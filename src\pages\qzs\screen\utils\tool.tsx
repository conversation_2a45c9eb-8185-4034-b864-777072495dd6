export const changeOrgName = (name) => {
  if (name?.startsWith?.('中共') && name?.endsWith?.('委员会')) {
    return name.slice(2, name.length -3)
  } else {
    return name;
  }
};

export const changeOrgName2 = (name) => {
  const tag1='中共'
  const tag2='中国共产党'
  const tag3='委员会'
  let str=name
  if(str){
    if(str.startsWith(tag1)){
      str=str.slice(2)
    }
    if(str.startsWith(tag2)){
      str=str.slice(5)
    }
    if(str.endsWith(tag3)){
      str=str.slice(0,str.length-3)
    }
    str=str.replace('支部','党支部')
  }
  return str
};
