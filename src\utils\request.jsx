import fetch from 'dva/fetch';
import { ApiAuthError, ApiJsonError, ApiModuleError, ApiServerError } from './errors';
import { _history as router, getPortal } from '@/utils/method';
import { notification } from 'antd';
import { _history } from './method';
import Box from '@/components/Dialog';
import { serviceError } from '../components/Notice';
import { getSession } from './session';
import _toLower from 'lodash/toLower';
import _isEmpty from 'lodash/isEmpty';
const md5 = require('md5');
const xxtea = require('xxtea');
var base64 = require('base-64');
const CryptoJS = require('crypto-js');
import GlobalLoading from '@/components/GlobalLoading';

const pop = {
  error(message, doSomething = () => { }) {
    notification.error({
      message: '操作失败',
      description: message,
      duration: 3,
    });
  },
  destroy() {
    notification.destroy();
  },
};

function parseJSON(response) {
  return new Promise((resolve, reject) => {
    const Type = response.headers.get('Content-Type');
    const disposition = response.headers.get('content-disposition');
    if (disposition && disposition.includes('filename')) {
      response.text().then((text) => {
        serviceError('操作失败', text);
        resolve({ code: 500, text, hiddenMsg: true });
      });
      return;
    }
    if (Type && Type.includes('text/html')) {
      response.text().then((text) => {
        resolve(text);
      });
    } else {
      response
        .json()
        .then((json) => {
          json._SERVER_URL = response.url;
          resolve(json);
        })
        .catch((error) => {
          if (error instanceof SyntaxError) {
            reject(new ApiJsonError(response.url));
          } else {
            reject(error);
          }
        });
    }
  });
}

function checkStatus(response) {
  if (response.status >= 200 && response.status < 300) {
    return response;
  }

  let error;
  if (response.status === 401) {
    error = new ApiAuthError(response.url);
  } else if (response.status === 503) {
    error = new ApiModuleError(response.status, response.url);
  } else {
    error = new ApiServerError(response.status, response.url);
  }
  throw error;
}

function checkError(response) {
  // {
  //     statusCode: number,  // 200:success,300:error,500:warning
  //     message: string,     // u know
  //     data: {},            // 返回单个对象时，从该属性获取
  //     total: number,       // 返回列表时，总条数从该属性获取
  //     datas: []            // 返回列表时，列表数据从该属性获取
  // }

  if (response.code !== 0) {
    let codeData = [1300, 1100, 1203]; //token认证失败 未登录 角色已过期
    if (codeData.includes(response.code)) {
      sessionStorage.clear();
      router.push('/');
    }
    // 在党建桌面请求村社区菜单接口
    if ((response?._SERVER_URL || '').includes('/st/vc/login')) {
      return response;
    }
    if (
      !response['hiddenMsg'] &&
      !['/text', '/annualStatistics', '/annualStatistics/old', '/annualStatistics/half2022', '/archivesAdministration/membersWorkProcedures'].includes(window.location.pathname)
    ) {
      Box.open(response.message);
    }
    // pop.destroy();
    // pop.error(response.message);
    return response;
  } else {
    return response;
  }
}

export default function request(path, options, ...extend) {


  // 显示loading 清镇市屏蔽loading
  if (!options?.hideLoading && !window.location.pathname.startsWith('/qzs')) {
    GlobalLoading.show();
  }

  let url = path;
  const defaultOptions = {
    method: 'GET',
    credentials: 'include',
  };

  const newOptions = { ...defaultOptions, ...options };
  const org = getSession('org') || {};
  if (_toLower(newOptions.method) !== 'get') {
    if (newOptions.body instanceof FormData) {
      newOptions.headers = {
        'Content-Type': 'multipart/form-data',
        ...newOptions.headers,
      };
    } else {
      newOptions.headers = {
        Accept: 'application/json',
        'Content-Type': 'application/json; charset=utf-8',
        ...newOptions.headers,
      };
      if (newOptions.body) {
        const { data = {} } = newOptions.body;

        if (data['subordinate'] == undefined) {
          data['subordinate'] = org['subordinate'] || 0;
        }
        if (data['isFlowStatus'] == undefined) {
          data['isFlowStatus'] = sessionStorage.getItem('isFlowStatus');
        }
        if (process.env.idCheck == 'false') {
          data['isCheck'] = 0;
        }
      }

      let encryptedData = CryptoJS.AES.encrypt(JSON.stringify(newOptions.body), CryptoJS.enc.Utf8.parse('AESNBHB3ZA==HKXt'), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
      });

      // newOptions.body = JSON.stringify(newOptions.body);
      newOptions.body = encryptedData.toString();
    }
  } else {
    // 增加时间戳 避免IE缓存 仅 GET
    const timestamp = new Date().getTime();
    if (url.indexOf('?') !== -1) {
      url += `&subordinate=${org['subordinate'] || 0}&_=${timestamp}`;
    } else {
      url += `?subordinate=${org['subordinate'] || 0}&_=${timestamp}`;
    }
    if (process.env.idCheck == 'false') {
      url += `&isCheck=0`;
    }

    let [_url, _para] = url.split('?');
    let encryptedData = CryptoJS.AES.encrypt(_para, CryptoJS.enc.Utf8.parse('AESNBHB3ZA==HKXt'), { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });

    url = _url + '?' + encodeURIComponent(encryptedData.toString());
  }
  const { exchange } = sessionStorage;
  const Authorization = sessionStorage.getItem('token') || '';
  let dataApi = sessionStorage.getItem('dataApi') || '';
  if (
    (url.includes('/api/org/findOrgByName') ||
      url.includes('/api/unit/getUnitName') ||
      url.includes('/api/org/findOutsideOrgByName') ||
      url.includes('/api/transfer/findOnUniqueCode') ||
      url.includes('/api/org/findApprovalOrg') ||
      url.includes('/api/org/findBranchOrg') ||
      url.includes('/api/org/findTransferOrg') ||
      url.includes('/api/orgFlow/search') ||
      url.includes('/api/transfer/findStatusOnUniqueCode') ||
      url.includes('/api/zyMem/findMemFlowStatus') ||
      url.includes('/api/orgFlow/findBranchOrg') ||
      url.search('/api/across/getProvinces') != -1) &&
    exchange
  ) {
    dataApi = exchange;
  }
  const { pathname } = _history.location;
  if (pathname !== '/login' && !pathname.startsWith('/qzs') && !Authorization) {
    //不是登录页 无权限是 不允许发送请求
    return;
  }

  // 当没有组织树时，不允许请求接口
  if (_isEmpty(org?.code) && pathname != '/login' && pathname != '/desktop' && !pathname.startsWith('/qzs')) {
    return new Promise((resolve, reject) => {
      reject({});
    });
  }

  if (options && options.body && Authorization) {
    let string = Authorization.split('-')[1];
    let pass = xxtea.encrypt(`${md5(string.substr(0, 16) + JSON.stringify(options.body))}@${new Date().getTime()}`, string.substr(16, 16));
    pass = base64.encode(pass);
    newOptions.headers = {
      Sig: pass,
      ...newOptions.headers,
    };
  }
  newOptions.headers = {
    ...newOptions.headers,
    Authorization,
    dataApi,
  };
  // const timeout = newOptions.timeout || config.fetchTimeout;
  if (extend[0] === 'file') {
    GlobalLoading.hide();
    return fetch(url, newOptions)
      .then(checkStatus)
      .then((res) => {
        const disposition = res.headers.get('content-disposition');
        let fileName = '';
        if (disposition) {
          let data = disposition.split(';');
          for (let obj of data) {
            if (obj.includes('filename')) {
              fileName = obj.split('=')[1];
              if (fileName) {
                fileName = decodeURIComponent(fileName);
              }
            }
          }
        }
        res.blob().then((data) => {
          const url = window.URL.createObjectURL(data);
          const a = document.createElement('a');
          document.body.appendChild(a);
          a.href = url;
          a.download = fileName;
          a.click();
          document.body.removeChild(a);
        });
        return res;
      });
  }
  if (extend[0] === 'showImg') {
    return fetch(url, newOptions)
      .then(checkStatus)
      .then((res) => {
        return res.blob();
      })
      .then(
        (blob) =>
          new Promise((callback) => {
            let reader = new FileReader();
            reader.onload = function () {
              callback(this.result);
            };
            reader.readAsDataURL(blob);
          }),
      );
  }
  return fetch(url, newOptions)
    .then(checkStatus)
    .then(parseJSON)
    .then(checkError)
    .catch((err) => {
      throw err;
    })
    .finally(() => {
      // 隐藏loading
      if (!options?.hideLoading) {
        GlobalLoading.hide();
      }
    });
  // return Promise.race([
  //     // fetch(`http://djgb.zt.com.cn:28088${url}`, newOptions)
  //     fetch(url, newOptions)
  //         .then(checkStatus)
  //         .then(parseJSON)
  //         .then(checkError)
  //         .catch(err => {
  //             throw err;
  //         }),
  //     new Promise(function(resolve, reject) {
  //         setTimeout(reject, 30000, ()=>{console.error('超时')});
  //     })
  // ]);
}
