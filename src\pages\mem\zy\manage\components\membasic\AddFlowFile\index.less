.btns {
  text-align: center;
}
.flex {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.flexbox {
  flex: 1;
  display: flex;
  height: 100%;
  justify-content: space-evenly;
  overflow: hidden;
  .flexbox_l {
    // width: 500px;
    max-width: 500px;
    flex: 1;
    margin: 0 20px;
    border:1px dashed #ccc;
    position: relative;
    overflow: auto;
    .imgs {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      margin-top: 20px;
      height: calc(100% - 70px);
      overflow: auto;
    }
    .delfile {
      display: none;
    }
    .previewImage {
      margin: 10px 5px 0 5px;
      width: 30%;
      height: 45%;
      position: relative;
      .preview {
        position: absolute;
        top: 5px;
        right: 5px;
        // background-color: #000;
        border-radius: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        display: none;
      }
      .cropper {
        position: absolute;
        top: 5px;
        right: 60px;
        display: none;
        border-radius: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        display: none;
      }
      >img {
        width: 100%;
        height: 100%;
      }
      &:hover { 
        .preview,.cropper {
          display: block;
           cursor: pointer;
        }
        
      }
      :global(.ant-skeleton) {
        width: 100% !important;
        height: 100% !important;
    }
    :global(.ant-progress) {
      position: absolute;
      left: 0;
      top: 60%;
      // transform: translateY(-50%);
    }
    }
    .cImage {
      border: 1px dashed #287fe2;
    }
    .cicon {
      color: rgb(17, 146, 103);
      position: absolute;
      left: 5px;
      right: 5px;
    }
    .load {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background: rgba(255,255,255,.7);
    }
//     &:hover {
//       .previewImage{
//         opacity: 0.5;
//       }
// .delfile {
//   display: block;
//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%, -50%);
// }
    
//     }
.upbtn {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
}
  }
}
.tables {
  max-height: 660px;
  overflow: auto;
  table {
    width: 960px;
    margin: auto;
    td {
      border: 1px solid #EBEEF5;
      padding: 13px 12px;
      width: 160px;
      font-family: Source Han Sans;
font-size: 14px;
font-weight: normal;
    }
    .label {
      background: #F5F7FA;
    }
  }
}
.imgBox {
  display: flex;
  width: 100%;
  >div {
    margin: 5px 5px 0 5px;
    width: 200px;
    height: 300px;
  }
  img {
    width: 100%;
    height: 100%;
  }
}
.filename {
  width: 100%;
  text-align: center;
}
.fzdy {
  background: rgb(24, 144, 255);
  color: rgb(255, 255, 255);
  display: inline-block;
  padding: 2px 5px;
  border-radius: 5px;
}

.changeicon {
  position: absolute;
  left: -30px;
  top: 50%;
  transform: translate(0, -50%);
  cursor: pointer;
}
.changeicon1 {
  position: absolute;
  right: -30px;
  top: 50%;
  transform: translate(0, -50%);
  cursor: pointer;
}
.tipsbox {
  position: absolute;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  z-index: 2;

}
.tips {
  // text-align: center;
  display: flex;
  justify-content: center;
  .tips1 {
    background: #FF3131;
    border-radius: 5px;
    display: flex;
align-items: center;
padding: 0 8px;
color: #fff;
cursor: pointer;
  }
  .tips2 {
    background: #FF5100;
    border-radius: 5px;
    display: flex;
align-items: center;
padding: 0 8px;
color: #fff;
cursor: pointer;

  }
}
.tipsbox1 {
  padding: 8px 14px;
  border-radius: 4px;
  background: #FFD6D6;
  box-sizing: border-box;
  border: 1px dashed #FF3131;
  color: #FF3131;
}
.tipsbox2 {
  padding: 8px 14px;
border-radius: 4px;
background: #FFEDE5;
color: #FF5100;
}
.tipclose {
  width: 20px;
  height: 20px;
  position: absolute;
  top: 0;
  right: 10px;
  img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
}
.flexboxTip {
  background: rgba(255, 81, 0, 0.2);
  border-radius: 4px;
  align-items: center;
  padding: 2px 8px;
  color: #FF5100;
  font-family: AlibabaPuHuiTi;
  font-size: 16px;
  font-weight: normal;
}