import React, { useRef, useEffect, useState } from 'react'
import styles from './index.less';
import Unfold from './unfold';
import _isEmpty from 'lodash/isEmpty';
import { getIndexCount } from '../../services';
import _get from 'lodash/get';
const index = (props) => {
  const refs: any = useRef(null);
  const [data, setData] = useState([
    {
      "value": "共有党员总数",
      "count": 0
    },
    {
      "value": "共有党的基层组织",
      "count": 0
    },
    {
      "value": "共有城市街道",
      "count": 0
    },
    {
      "value": "共有单位",
      "count": 0
    },
    {
      "value": "共有党组",
      "count": 0
    }
  ]);
  const {orgCode = ''} = JSON.parse(sessionStorage.getItem('user') || '{}')
  const func = () => {
    refs.current.open();
  }
  const items = (infos: any) => {
    const { num = '', des = '', colors = '' } = infos;
    return (
      <div className={styles.card}>
        <div className={styles.main}>
          <div className={styles.bigText}>{num}</div>
          <div>{des}</div>
        </div>
        <div className={styles.more} style={{ backgroundColor: colors }} onClick={func}>更多</div>
      </div>
    )
  };
  const getList = async () => {
    const { code = 500, data = [] } = await getIndexCount({ data: { orgCode: orgCode } });
    if (code === 0) {
      setData(data);
    }
  };
  useEffect(() => {
    if(!_isEmpty(orgCode)){
      getList();
    }
  }, [JSON.stringify(orgCode)]);
  let colorArr = ['#76B0FE','#F88E8B','#F3B646','#2BC099','#4AC2EE']
  return (
    <div className={styles.box}>
      {
        data.map((it, index) => {
          return items({ num: `${it.count} ${index == 0 ? '名' :'个'}`, des: it.value, colors: colorArr[index] })
        })
      }
      <Unfold ref={refs} orgCode={orgCode}/>
    </div>
  )
}

export default index
