import { basicModel } from '@/utils/common-model';
import modelExtend from 'dva-model-extend';
import { getOrgTree, queryOrgTree, password, logout } from '../services/index.js';
import { getSession } from '@/utils/session';

const loginModel = modelExtend(basicModel, {
  namespace: 'common',
  state: {
    authorization: undefined,
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen((location) => {
        // const { pathname, search } = location;
      });
    },
  },
  effects: {
    *initTree({ payload }, { call, put, select }) {
      const org = getSession('org');
      const res = yield call(getOrgTree, payload);
      console.log(payload, 'onCheckOrg');
      if (res['code'] === 0) {
        let obj = { ...res['data'][0] };
        delete obj['children'];
        if (org) {
          obj = org;
        } else {
          sessionStorage.setItem(
            'org',
            JSON.stringify({ ...obj, subordinate: 1 }),
          );
        }
        let mapTree = new Map(),
          mapTreeCode = new Map(); //树转换成map结构
        for (let obj of res['data']) {
          mapTree.set(obj['orgCode'], obj);
          mapTreeCode.set(obj['code'], obj);
        }
        yield put({
          type: 'updateState',
          payload: {
            listTree: res['data'],
            org: obj,
            mapTree,
            mapTreeCode,
          },
        });
      }
    },
    *getTree({ payload }, { call, put, select }) {
      const state = yield select((state) => state['common']);
      const res = yield call(getOrgTree, payload);
      let { mapTree, mapTreeCode } = state;
      for (let obj of res['data']) {
        mapTree.set(obj['orgCode'], obj);
        mapTreeCode.set(obj['code'], obj);
      }
      let data: Array<object> = [];
      for (let obj of mapTreeCode) {
        data.push(obj[1]);
      }
      yield put({
        type: 'updateState',
        payload: {
          listTree: data,
          mapTree,
          mapTreeCode,
        },
      });
      return res;
    },
    *queryTree({ payload }, { call, put, select }) {
      const res = yield call(queryOrgTree, payload);
      // let filterData:Array<object>=[];
      // for(let obj of res['data']){
      //   filterData.push({value:obj['orgCode'],text:obj['shortName']})
      // }
      yield put({
        type: 'updateState',
        payload: {
          filterData: res['data'],
        },
      });
    },
    *updateTree({ payload }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          listTree: undefined,
        },
      });
      yield put({
        type: 'getTree',
        payload,
      });
    },
    *updateInitTree({ payload }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          listTree: undefined,
        },
      });
      yield put({
        type: 'initTree',
        payload,
      });
    },
    *clear({ payload }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          org: undefined,
          listTree: undefined,
          mapTree: undefined,
          mapTreeId: undefined,
        },
      });
    },
  },
});
export default loginModel;
