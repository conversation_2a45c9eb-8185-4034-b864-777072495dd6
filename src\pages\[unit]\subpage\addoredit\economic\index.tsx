// 单位管理-村社区-编辑-集体经济情况
import React, { Fragment, useEffect, useRef, useState } from 'react';
import ListTable from '@/components/ListTable';
import { Button, Divider, Popconfirm, Input } from 'antd';
import moment from 'moment';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import Tip from '@/components/Tip';
import { getSession } from '@/utils/session';
import ExportInfo from '@/components/Export/index';
import AddOrEdit from './components/addoredit';
import {
  collectiveEconomicGetList,
  collectiveEconomicDelete,
  collectiveEconomicDetails,
} from './services';
const Search = Input.Search;
const index = (props: any) => {
  const org = getSession('org') || {};
  const { unit: { basicInfo = {} } = {} } = props;
  const addOrEditRef: any = useRef();
  const [listData, setListData] = useState([]);
  const [pagination, setPagination] = useState<any>({ pageSize: 10, current: 1, total: 0 });
  const [listLoading, setListLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [unitName, setUnitName]: any = useState(undefined);
  const [dataInfo, setDataInfo]: any = useState({});
  const exportRef: any = useRef();
  const columns = [
    // {
    //   title: '单位名称',
    //   dataIndex: 'unitName',
    //   width: 200,
    // },
    {
      title: '集体经济组织名称',
      dataIndex: 'industryName',
      width: 200,
      render: (text, record) => <a onClick={() => addOrEdit(record)}>{text}</a>,
    },
    {
      title: '发展经济类型',
      dataIndex: 'd128Name',
      width: 200,
    },
    {
      title: '采取组织形式',
      dataIndex: 'd129Name',
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      render: (text, record) => {
        return (
          <span>
            <a onClick={() => addOrEdit(record)}>编辑</a>
            <Divider type="vertical" />
            <Popconfirm
              title="确定要删除吗？"
              onConfirm={async () => {
                const { code: resCode = 500 } = await collectiveEconomicDelete({
                  code: record?.code,
                });
                if (resCode === 0) {
                  Tip.success('操作提示', '操作成功');
                  getLists({ pageNum: 1 });
                }
              }}
            >
              <a className={'del'}>删除</a>
            </Popconfirm>
          </span>
        );
      },
    },
  ];

  // 导出
  const exportInfo = async () => {
    setExportLoading(true);
    await exportRef.current.submitNoModal();
    setExportLoading(false);
  };
  const handleSearch = (e) => {
    setUnitName(e || undefined);
    getLists({ unitName: e || undefined });
  };
  const getLists = async (p = {}) => {
    setListLoading(true);
    const {
      code = 500,
      data: { pageNumber: current = 1, pageSize = 20, totalRow: total = 0, list = [] } = {},
    } = await collectiveEconomicGetList({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
        unitCode: basicInfo.code,
        orgCode: org['orgCode'],
        // subordinate:1,
        ...p,
      },
    });
    setListLoading(false);
    if (code === 0) {
      setListData(list);
      setPagination({ current, total, pageSize });
    }
  };

  const addOrEdit = async (record?: any) => {
    await setDataInfo({});
    AddOrEdit['WrappedComponent'].clear();
    if (record && record['code']) {
      const { code: resCode = 500, data = {} } = await collectiveEconomicDetails({
        code: record?.code,
      });
      if (resCode == 0) {
        setDataInfo(data);
        AddOrEdit['WrappedComponent'].show();
      }
    } else {
      AddOrEdit['WrappedComponent'].show();
    }
  };

  useEffect(() => {
    getLists({ pageNum: 1 });
  }, []);
  return (
    <div style={{ margin: '0 16px' }}>
      <div style={{ marginBottom: '10px', display: 'flex', justifyContent: 'end' }}>
        {/* <Button onClick={exportInfo} loading={exportLoading}>
          导出
        </Button> */}
        <Button
          type={'primary'}
          icon={<LegacyIcon type={'plus'} />}
          onClick={() => addOrEdit()}
          style={{ marginLeft: 16 }}
        >
          新增集体经济情况
        </Button>
        <Search
          allowClear
          style={{ width: 200, marginLeft: 16 }}
          onSearch={handleSearch}
          placeholder={'输入名称查找'}
        />
      </div>
      <ListTable
        
        scroll={{ y: 480 }}
        columns={columns}
        data={listData}
        pagination={pagination}
        onPageChange={(page: any, pageSize: any) => {
          getLists({ pageNum: page, pageSize });
        }}
      />
      <AddOrEdit dataInfo={dataInfo} basicInfo={basicInfo} onOk={()=>{getLists()}} />
      <ExportInfo
        wrappedComponentRef={exportRef}
        tableName={''}
        noModal={true}
        tableListQuery={{
          unitName,
          orgCode: org['orgCode'],
          unitCode: basicInfo.code,
          pageSize: pagination.pageSize,
          pageNum: pagination.current,
        }}
        action={'/api/unit/collectiveEconomic/export'}
      />
    </div>
  );
};

export default index;
