import React, { useEffect, useState, useRef } from 'react';
import { CloseOutlined, CheckOutlined } from '@ant-design/icons';
import { Table, Button, Divider } from 'antd';
import ListTable from '@/components/ListTable';
import ModalDetail from './components/modal';
import IgnoreListModal from './components/ignoreListModal';
import { getSession } from '@/utils/session';
import { logic } from '@/pages/dataCheck/services';

const index = () => {
  const modalRef: any = useRef();
  const modalRef2: any = useRef();
  const [mainList, setMainList] = useState([]);
  const [mainPagination, setMainPagination] = useState({ pageNum: 1, pageSize: 5, total: 0, totalPage: 0 });
  const [loading, setLoading] = useState(false);
  const org: any = getSession('org') || {};

  const getmainsList = async (p = {}) => {
    setLoading(true);
    const { code = 500, data = [] } = await logic({
      data: {
        orgCode: org?.orgCode,
        ...p,
      }
    });
    setLoading(false);
    if (code === 0) {
      setMainList(data);
    }
  }
  useEffect(() => {
    // if (org?.orgCode) {
    //   getmainsList({});
    // }
    setMainList([]);
  }, [org?.orgCode]);
  const columns: any = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 50,
    },
    {
      title: '检查条件',
      dataIndex: 'condition',
    },
    {
      title: '是否通过',
      dataIndex: 'hasPass',
      width: 100,
      align: 'center',
      render: (text, record) => {
        return !text ? <CloseOutlined style={{ color: 'red' }} /> : <CheckOutlined style={{ color: 'green' }} />
      }
    },
    {
      title: '未通过数量',
      width: 120,
      align: 'center',
      dataIndex: 'fail',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 200,
      render: (text, record) => {
        return (
          <React.Fragment>
            {
              !record?.hasPass &&
              <a onClick={() => {
                modalRef.current.open(record);
              }}>查看</a>
            }
            {
              record['ignored'] &&
              <React.Fragment>
                {
                  !record?.hasPass &&
                  <Divider type="vertical" />
                }
                <a onClick={() => {
                  modalRef2.current.open(record);
                }}>已忽略信息</a>
              </React.Fragment>
            }

          </React.Fragment>
        )
      },
    },
  ];
  return (
    <div style={{ paddingBottom: 10 }}>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button type={'primary'} onClick={() => {
          getmainsList({});
        }}>检查当前机构</Button>
      </div>
      <Table columns={columns} dataSource={mainList}
       
        rowKey={'id'} />
      {/* <ListTable columns={columns} data={mainList} pagination={mainPagination} onPageChange={(pageNum, pageSize) => getmainsList({pageNum, pageSize})}/> */}
      
      {/* 只点击检查当前机构时才请求/logic这个接口,取消onOk的调用 */}
      <ModalDetail ref={modalRef} onOK={()=>{}} />
      <IgnoreListModal ref={modalRef2} onOK={()=>{}}></IgnoreListModal>
    </div>
  )
}

export default index
