/**
 * 离开党组织services
 */
import request from "@/utils/request";
import qs from 'qs';
export function getList(params) {
  return request(`/api/mem/history/getList`,{
    method:'POST',
    body:params,
  });
}
//离开党组织
export function leaveOrg(params) {
  return request(`/api/mem/leaveOrg`,{
    method:'POST',
    body:params,
  });
}
export function findByCode(params) {
  return request(`/api/mem/reward/findByCode?${qs.stringify(params)}`,{
    method:'Get',
  });
}
export function delMemHistory(params) {
  return request(`/api/mem/history/delMemHistory`,{
    method:'POST',
    body:params,
  });
}
export function reconvertMemHistory(params) {
  return request(`/api/mem/history/reconvertMemHistory`,{
    method:'POST',
    body:params,
  });
}

export function quitParty(params) {
  return request(`/api/mem/history/quitParty`,{
    method:'POST',
    body:params,
  });
}

