import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Form, Input, Modal, InputNumber, Row, Col, Select } from 'antd';
import moment from 'moment';
import Tip from '@/components/Tip';
import { addUnitCommunity, updateUnitCommunity } from './services';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
// import DictSelect from '@/components/DictSelect';
// import OrgSelect from '@/components/OrgSelect';
import MemSelect from '@/components/MemSelect';
// import { findDictCodeName } from '@/utils/method';

const formItemLayout = {
  labelCol: { span: 18 },
  wrapperCol: { span: 4 },
};
const formItemLayout1 = {
  labelCol: { span: 12 },
  wrapperCol: { span: 10 },
};

const index = (props: any, ref) => {
  const { unit: { basicInfo = {} } = {}, onOK } = props;
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [title, setTitle] = useState('新增');
  const [hasFirstSecretary, setHasFirstSecretary] = useState();
  useImperativeHandle(ref, () => ({
    open: (dataInfo) => {
      open(dataInfo);
    },
  }));
  const open = (dataInfo) => {
    setVisible(true);
    if (!_isEmpty(dataInfo)) {
      setTitle('编辑');
      if (_isNumber(dataInfo['year'])) {
        dataInfo['year'] = moment(dataInfo['year']).format('YYYY');
      }
      setHasFirstSecretary(dataInfo.hasFirstSecretary)
      setDataInfo(dataInfo);
      form.setFieldsValue({
        ...dataInfo,
      });
    }
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    // let val = findDictCodeName(['d110'], e, dataInfo);
    if (e) {
      e['year'] = moment(e['year'], 'YYYY').valueOf();
    }
    if (typeof e['firstSecretaryCode'] === 'object') {
      e['firstSecretaryName'] = e['firstSecretaryCode'][0]['name'];
      e['firstSecretaryCode'] = e['firstSecretaryCode'][0]['code'];
    } else {
      e['firstSecretaryName'] = dataInfo['firstSecretaryName'];
    }
    if (e['communityMoneyNum'] > 200) {
      Tip.error('操作提示', '纳入财政预算的工作经费总额（万元）不能大于200')
      return;
    }
    if (e['communityServingPeople'] > 200) {
      Tip.error('操作提示', '全年服务群众专项经费总额（万元）不能大于200')
      return;
    }
    let url = addUnitCommunity;
    if (!_isEmpty(dataInfo)) {
      url = updateUnitCommunity;
    }
    // console.log(val,'val');

    setConfirmLoading(true);
    const { code: resCode = 500 } = await url({
      data: {
        ...e,
        unitCode: basicInfo?.code,
        id: dataInfo?.id,
        code: dataInfo?.code,
      },
    });
    setConfirmLoading(false);
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  const YearValidator = (rule, value, callback) => {
    if (value && (value < 1900 || value > 2100)) {
      return callback('请输入正确年份');
    } else {
      return callback();
    }
  };
  return (
    <Fragment>
      <Modal
        title={title}
        destroyOnClose
        visible={visible}
        confirmLoading={confirmLoading}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={'1000px'}
      >
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Row>
            <Col span={12}>
              <Form.Item
                name="year"
                label="年份"
                rules={[{ required: true, message: '请输入年份' }, { validator: YearValidator }]}
              // initialValue={}
              >
                {/* <DatePicker onChange={() => {}} picker="year" /> */}
                <InputNumber style={{ width: '100%' }} maxLength={4} minLength={4} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="hasFirstSecretary"
                label="是否配备第一书记"
                rules={[{ required: true, message: '请选择是否配备第一书记' }]}
              >
                <Select onChange={(e: any) => setHasFirstSecretary(e)} style={{ width: '100%' }}>
                  <Select.Option value={1}>是</Select.Option>
                  <Select.Option value={0}>否</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            {/* ↑ 当选择为是的时候，才展示信息项： */}
            {
              hasFirstSecretary == 1 &&
              <Fragment>
                <Col span={12}>
                  <Form.Item
                    {...formItemLayout1}
                    name="firstSecretarySelect"
                    label="今年新选派第一书记（人）"
                    rules={[{ required: true, message: '今年新选派第一书记（人）' }]}
                  >
                    <InputNumber precision={0} style={{ width: '100%' }} min={0} max={9999999999} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    {...formItemLayout1}
                    name="firstSecretaryCode"
                    label="现任第一书记"
                    rules={[{ required: true, message: '请输入现任第一书记' }]}
                    initialValue={dataInfo['firstSecretaryCode'] || undefined}
                  >
                    {/* <Input maxLength={50}/> */}
                    <MemSelect
                      initValue={dataInfo['firstSecretaryName'] || undefined}
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="secretaryTrainingNum"
                    label="本年各级培训第一书记（人次）"
                    rules={[{ required: true, message: '请输入本年各级培训第一书记（人次）' }]}
                  >
                    <InputNumber precision={0} style={{ width: '100%' }} min={0} max={9999999999} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="hasThousand"
                    label="是否为第一书记安排不低于1万元工作经费"
                    rules={[{ required: true, message: '请输入是否为第一书记安排不低于1万元工作经费' }]}
                  >
                    <Select style={{ width: '100%' }}>
                      <Select.Option value={1}>是</Select.Option>
                      <Select.Option value={0}>否</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Fragment>
            }
            <Col span={12}>
              <Form.Item
                name="hasBundled"
                label="是否派出单位落实责任、项目、资金捆绑"
                rules={[{ required: true, message: '请输入是否派出单位落实责任、项目、资金捆绑' }]}
              >
                <Select style={{ width: '100%' }}>
                  <Select.Option value={1}>是</Select.Option>
                  <Select.Option value={0}>否</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="promotedNum"
                label="提拔使用或晋级的第一书记数"
                rules={[{ required: true, message: '请输入提拔使用或晋级的第一书记数' }]}
              >
                <InputNumber precision={0} style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="adjustedNum"
                label="因工作不胜任召回调整的第一书记数"
                rules={[{ required: true, message: '请输入因工作不胜任召回调整的第一书记数' }]}
              >
                <InputNumber precision={0} style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="operatingExpenses"
                label="运转经费（万元 ∕ 年）"
                rules={[{ required: true, message: '请输入运转经费（万元 ∕ 年）' }]}
              >
                <InputNumber precision={5} style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="villagePer"
                label="独立单位办公经费（万元 ∕ 年）"
                rules={[{ required: true, message: '请输入独立单位办公经费（万元 ∕ 年）' }]}
              >
                <InputNumber precision={5} style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="secretarySalary"
                label="党组织书记平均报酬（万元 ∕ 年）"
                rules={[{ required: true, message: '请输入党组织书记平均报酬（万元 / 年）' }]}
              >
                <InputNumber precision={5} style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="spaceArea"
                label="活动场所面积（㎡）"
                rules={[{ required: true, message: '请输入活动场所面积（㎡）' }]}
              >
                <InputNumber style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="newExpandArea"
                label="本年新建或改扩建活动场所数量"
                rules={[{ required: true, message: '请输入本年新建或改扩建活动场所数量' }]}
              >
                <InputNumber precision={0} style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="secretaryPartyNum"
                label="村党组织书记中录用公务员数"
                rules={[
                  { required: true, message: '请输入村党组织书记中录用公务员数' },
                ]}
              >
                <InputNumber precision={0} style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="secretaryEmploySybzNum"
                label="村党组织书记中录用事业编制工作人员数"
                rules={[
                  { required: true, message: '请输入村党组织书记中录用事业编制工作人员数' },
                ]}
              >
                <InputNumber precision={0} style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="secretaryPromotedNum"
                label="从党组织书记中选拔乡镇领导干部人员数"
                rules={[
                  { required: true, message: '请输入从党组织书记中选拔乡镇领导干部人员数' },
                ]}
              >
                <InputNumber precision={0} style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            {/* <Col span={12}>
              <Form.Item
                name="registeredPopulation"
                label="户籍人口"
                rules={[
                  { required: true, message: '请输入户籍人口' },
                ]}
              >
                <InputNumber style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="householdRegistration"
                label="户籍数"
                rules={[
                  { required: true, message: '请输入户籍数' },
                ]}
              >
                <InputNumber style={{ width: '100%' }} min={0} max={9999999999} />
              </Form.Item>
            </Col> */}
            {/* 当选择城市社.区、乡镇社.区得时候，以上信息中再单独再增加以下信息 */}
            {
              (basicInfo?.d04Code === '921' || basicInfo?.d04Code === '922')
              // true
              &&
              (<Fragment>
                <Col span={12}>
                  <Form.Item
                    name="communityMoneyNum"
                    label="纳入财政预算的工作经费总额（万元）"
                    rules={[
                      { required: true, message: '请输入纳入财政预算的工作经费总额（万元）' },
                    ]}
                  >
                    <InputNumber precision={5} style={{ width: '100%' }} min={0} max={9999999999} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="communityServingPeople"
                    label="全年服务群众专项经费总额（万元）"
                    rules={[
                      { required: true, message: '请输入全年服务群众专项经费总额（万元）' },
                    ]}
                  >
                    <InputNumber precision={5} style={{ width: '100%' }} min={0} max={9999999999} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="communityMasses"
                    label="是否开展在职党员到报到为群众服务"
                    rules={[
                      { required: true, message: '请输入是否开展在职党员到报到为群众服务' },
                    ]}
                  >
                    <Select style={{ width: '100%' }}>
                      <Select.Option value={1}>是</Select.Option>
                      <Select.Option value={0}>否</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Fragment>
              )}
          </Row>
        </Form>
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
