/**
 * 桌面
 */
import React, { Fragment } from 'react';
import { Avatar, Card, Col, Row, Tabs, Tooltip, List, Button, Modal, Divider, message, Badge } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import style from './index.less';
import NumberCard from './components/Number';
import MoreCard from './components/MoreCard';
import Todo from './components/Todo';
import Feedback from './components/Feedback';
import ListTable from '@/components/ListTable';
import { _history as router, isEmpty } from '@/utils/method';
import { connect } from 'dva';
import { getSession } from '@/utils/session';
import { feedbackList, getAnnouncements, help, messageList, messageView, messageIgnore, userVerifyDecryption, lastUpdateTime } from './services/index';
import Detail from '@/pages/forum/components/add';
import 'intro.js/introjs.css';
import { Steps, Hints } from 'intro.js-react';
import { FileDoneOutlined } from '@ant-design/icons';
import NoticeDel from './noticeDel';
import SystemSet from './components/SystemSet';
import SystemTabItem from './components/SystemTabItem';
import ChangePassword from '../user/user/changePassword';

const { Meta } = Card;
const minAreaData = [
  { x: '00:00', y: 1089 },
  { x: '01:00', y: 63 },
  { x: '02:00', y: 140 },
  { x: '03:00', y: 949 },
  { x: '04:00', y: 273 },
  { x: '05:00', y: 363 },
  { x: '06:00', y: 477 },
  { x: '07:00', y: 391 },
  { x: '08:00', y: 467 },
  { x: '09:00', y: 599 },
  { x: '10:00', y: 518 },
  { x: '11:00', y: 656 },
  { x: '12:00', y: 685 },
  { x: '13:00', y: 739 },
  { x: '14:00', y: 779 },
  { x: '15:00', y: 941 },
  { x: '16:00', y: 949 },
  { x: '17:00', y: 1003 },
  { x: '18:00', y: 1089 },
  { x: '19:00', y: 1030 },
  { x: '20:00', y: 1190 },
  { x: '21:00', y: 1242 },
  { x: '22:00', y: 213 },
  { x: '23:00', y: 1347 },
];
const headItems = [
  { name: 'fileDownLoad', iocn: <FileDoneOutlined />, type: 'antd' },
  // { name: 'find', iocn: require('@/assets/desktop/find.svg') },
  // { name: 'light', iocn: require('@/assets/desktop/light.svg') },
  // { name: 'bell', iocn: require('@/assets/desktop/bell.svg') },
];
// const radioDates2 = [
//   { text: '待办事项', name: 'notdo' },
//   { text: <Badge size="small" count={100}>消息提醒</Badge>, name: 'news' },
//   { text: '问题反馈', name: 'news' },
//   { text: '通知公告', name: 'notice' },
// ];
const radioDates2 = (count?: any) => {
  return [
    {
      text: (
        <Badge offset={[0, -8]} size="small" count={count} style={{ background: '#CA2728' }}>
          消息提醒
        </Badge>
      ),
      name: 'news',
    },
    { text: '待办事项', name: 'notdo' },
    { text: '问题反馈', name: 'news' },
    { text: '通知公告', name: 'notice' },
  ];
};
let DWjobMenu = [
  { text: '党务报告', name: '1', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '2', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '3', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '4', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '5', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '6', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '7', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '8', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '9', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '10', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '11', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '12', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '13', icon: require('@/assets/dh.png') },
  { text: '党务报告', name: '14', icon: require('@/assets/dh.png') },
];
@connect(({ login, desktop, common }) => ({ login, desktop, common }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      height: 100,
      activeKey: '1',
      feedbackArr: [],
      noticeList: [],
      current: 1,
      messagesList: [],
      messageVisible: false,
      modalMessagesList: [],
      pagination: { page: 1, pageSize: 10, total: 0, current: 1 },
      messageDetailVisible: false,
      currentType: '',
      currentMessage: '',
      currentConsumer: '',
      messageCount: 0, // 消息提醒总数
    };
  }

  componentDidMount(): void {
    const { assess, needUpdatePassword, isOpenUpdatePassword } = sessionStorage;
    let height;
    const _this = this;
    const ele = document.getElementById('menus') || {};
    height = ele['offsetHeight'];
    this.setState({ height });
    window.onresize = function () {
      const ele = document.getElementById('menus') || {};
      height = ele['offsetHeight'];
      _this.setState({ height });
    };
    this.getFeedbackList();
    if (needUpdatePassword == 'true' && isOpenUpdatePassword == 'false') {
      return this['changePasswordRef'].showModal({ isqz: true });
    }
    // 获取弹出框信息
    this.getOpenModalData();
    this.getMessageList({ pageNum: 1, pageSize: 10 });

    if (assess == 'true') {
      Modal.confirm({
        title: '提示',
        icon: <div />,
        content: '请修改默认密码',
        okText: '确定',
        cancelText: '取消',
        onCancel: () => {
          Modal.destroyAll();
        },
        onOk: () => {
          Modal.destroyAll();
        },
      });
    }

    this.getUpdateTime();
  }

  getOpenModalData = async () => {
    // 先获取消息提醒列表模态框
    await this.getMessageList(undefined, 'modal');
    // 后获取通知公告，有通知公告，模态框展示在前面，消息提醒在最后
    this.getAnnouncement();
  };

  open = async () => {
    await this.getMessageList(undefined, 'modal');
    this.setState({ messageVisible: true });
  };

  getFeedbackList = async () => {
    const { code = 500, data = {} } = await feedbackList({
      data: {
        pageNum: 1,
        pageSize: 20,
        type: '1',
      },
    });
    if (code === 0) {
      this.setState({ feedbackArr: data.list });
    }
  };

  getAnnouncement = async (pageNum = 1, pageSize = 9, open = true) => {
    const { code = 500, data = {} } = await getAnnouncements({
      pageNum,
      pageSize,
    });
    if (code === 0) {
      if (open && !_isEmpty(this.state.modalMessagesList)) {
        await this.setState({ messageVisible: true });
      }
      await this.setState({ noticeList: data.records, current: data.current });
      if (open) {
        (data.records || []).map((it, index) => {
          this['noticeDel' + index].open(it);
        });
      }
    }
  };

  rightMenusTopClick = (val) => {
    radioDates2(this.state.messageCount).forEach((item, index) => {
      if (val === item['name']) {
        this['rightSlider'].goTo(index);
      }
    });
  };
  menusTopClick = (val) => {
    radioDates.forEach((item, index) => {
      if (val === item['name']) {
        this['mainSlider'].goTo(index);
      }
    });
  };

  menuItemClick = async (item) => {
    // 账户管理特殊处理
    if (item.code == '33') {
      if (this['SystemSet']) {
        this['SystemSet'].open();
      }
      return;
    }
    // 村社区特殊处理
    if (item.code == 'csq') {
      let url = process.env.NODE_ENV == 'development' ? `http://${window.location.hostname}:8001` : window.location.origin;
      const { token, dataApi, user } = sessionStorage;
      window.location.replace(`${url}/station_village/login?token=${token}&dataApi=${dataApi}&user=${encodeURIComponent(user || '')}`);
      return;
    }
    const { children = [] } = item;
    const [first = {}] = children;
    sessionStorage.setItem('menuKey', item['code']);
    sessionStorage.setItem('menuType', item['type']);
    first && sessionStorage.setItem('menuItemKey', first['code']);
    router.push(first['url']);
  };

  cKey = (key) => {
    this.setState({
      activeKey: key,
    });
  };
  renderTodo = (daiban) => {
    return (
      <React.Fragment>
        {!isEmpty(daiban) ? (
          <Fragment>
            {daiban.map((item, index) => (
              <Todo key={index} info={item} />
            ))}
          </Fragment>
        ) : (
          <span
            style={{
              width: '100%',
              textAlign: 'center',
              display: 'inline-block',
              marginTop: '10px',
            }}
          >
            暂无
          </span>
        )}
      </React.Fragment>
    );
  };
  renderFeedback = () => {
    return (
      <React.Fragment>
        {!isEmpty(this.state.feedbackArr) ? (
          <div className={style.feedback}>
            {this.state.feedbackArr.map((item, index) => (
              <Feedback
                key={index}
                info={item}
                onClick={(record: any) => {
                  this['forumDetail'].open({
                    info: { code: record.code },
                    type: 'detail',
                  });
                }}
              />
            ))}
          </div>
        ) : (
          <span
            style={{
              width: '100%',
              textAlign: 'center',
              display: 'inline-block',
              marginTop: '10px',
            }}
          >
            暂无
          </span>
        )}
        <a
          style={{ width: '100%', marginTop: '20px', display: 'block', textAlign: 'center' }}
          onClick={() => {
            router.push('/forum');
          }}
        >
          更多...
        </a>
      </React.Fragment>
    );
  };
  renderNotice = () => {
    return (
      <React.Fragment>
        {this?.state?.noticeList?.length > 0 ? (
          <div>
            <List
              header={false}
              footer={false}
              dataSource={this.state.noticeList}
              renderItem={(item: any, index) => (
                <List.Item key={item.id} onClick={() => this.toDetail(index, item)}>
                  <List.Item.Meta
                    title={<a href="#">{item.title}</a>}
                    // description={item.content}
                  />
                </List.Item>
              )}
            />
            {this.state.noticeList.length == 9 && (
              <div style={{ textAlign: 'center' }} onClick={this.pageChange}>
                <a>更多...</a>
              </div>
            )}
          </div>
        ) : null}
      </React.Fragment>
    );
  };
  heardIconClick = async ({ name = '' }) => {
    // switch (name) {
    //   case 'fileDownLoad':
    //     console.log('文件下载');
    //     window.open('/download/help.zip');
    //     break;
    // }
    const { code = 500, data } = await help({});
    const { url } = data;
    window.open(url);
  };
  pageChange = () => {
    this.state.noticeList.current;
    this.getAnnouncement(this.state.current + 1, 9, false);
  };
  toDetail = (index, item) => {
    this['noticeDel' + index].open(item);
  };

  // 获取消息提醒列表
  getMessageList = async (p?: any, type?: string) => {
    const { code = 500, data = {} } = await messageList({
      data: {
        pageNum: 1,
        pageSize: 10,
        ...p,
      },
    });
    if (code === 0) {
      const { list = [], pageNumber = 1, pageSize = 10, totalRow = 0 } = data;
      this.setState({
        messageCount: totalRow,
      });
      if (type === 'modal') {
        this.setState({
          modalMessagesList: list,
          pagination: { page: pageNumber, total: totalRow, pageSize, current: pageNumber },
        });
      } else {
        this.setState({ messagesList: list });
      }
    }
  };
  // 消息提醒列表
  renderMessageList = () => {
    return (
      <React.Fragment>
        <List
          rowKey={'code'}
          dataSource={this.state.messagesList}
          renderItem={(item: any) => (
            <List.Item>
              <div style={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                <div className={style.messageContent}>{item.message || '暂无'}</div>
                <div style={{ width: '100%', textAlign: 'right' }}>
                  <Button
                    size="small"
                    type="link"
                    onClick={() => {
                      this.messageRead(item);
                    }}
                  >
                    查看
                  </Button>
                  <Divider type="vertical" />
                  <Button
                    size="small"
                    type="link"
                    onClick={() => {
                      this.messageIgnore(item);
                    }}
                  >
                    忽略
                  </Button>
                </div>
              </div>
            </List.Item>
          )}
        />
        <a
          style={{ width: '100%', marginTop: '20px', display: 'block', textAlign: 'center' }}
          onClick={() => {
            this.getMessageList(undefined, 'modal');
            this.setState({ messageVisible: true });
          }}
        >
          查看更多
        </a>
      </React.Fragment>
    );
  };
  messageHandleCancel = () => {
    this.setState({ messageVisible: false, modalMessagesList: [], pagination: { page: 1, pageSize: 10, total: 0 } });
    this.getMessageList();
  };
  messageRead = async (p: any) => {
    const { page = 1, pageSize = 10 } = this.state.pagination;
    const { type = '', message = '', consumer = '', code = '' } = p;
    this.setState({ currentType: type, currentMessage: message, currentConsumer: consumer, messageDetailVisible: true });
    const { code: resCode = 500 } = await messageView({
      data: {
        code: [code],
      },
    });
    if (resCode == 0) {
      // message.success('消息已读成功');
      if (this.state.messageVisible) {
        this.getMessageList({ pageNum: page, pageSize }, 'modal');
      } else {
        this.getMessageList();
      }
    }
  };
  messageIgnore = async (item: any) => {
    const { page = 1, pageSize = 10 } = this.state.pagination;
    const { code } = item;
    const { code: resCode = 500 } = await messageIgnore({
      data: {
        code: [code],
      },
    });
    if (resCode == 0) {
      message.success('操作成功');
      if (this.state.messageVisible) {
        this.getMessageList({ pageNum: page, pageSize }, 'modal');
      } else {
        this.getMessageList();
      }
    }
  };
  // 消息详情
  messageDetailCancel = () => {
    this.setState({ messageDetailVisible: false });
  };
  // 数据更新时间
  getUpdateTime = async (item?: any) => {
    const { code, data = '' } = await lastUpdateTime({});
    if (code == 0) {
      this.setState({
        lastUpdateData: data,
      });
    }
  };
  isclear = (isChange: boolean) => {
    sessionStorage.setItem('isOpenUpdatePassword', 'true');
    if (isChange) {
      sessionStorage.clear();
      this.props.dispatch({
        type: 'common/clear',
        payload: {},
      });
      this.props.dispatch({
        type: 'login/clear',
        payload: {},
      });
      window.location.replace('/login');
    } else {
      this.getOpenModalData();
      this.getMessageList({ pageNum: 1, pageSize: 10 });
    }
  };
  render() {
    const { login, desktop } = this.props;
    const { memTotal, orgTotal, devMem, fee, activity } = desktop;
    const { menuData } = login;
    const { height, activeKey, messageVisible, messageDetailVisible, currentType, currentMessage, currentConsumer, pagination, lastUpdateData = '' } = this.state;
    const user = getSession('user') || {};

    const manyInfos = [
      {
        text: '党员信息完整度',
        name: 'completion',
        value: 100,
        suf: '%',
        chart: 100,
        extraVal: (
          <span className={style.peopleNum}>
            <div className={style.numCell}>
              <span className={style.bigNum}>
                {memTotal || 0}
                <span className={style.text}>人</span>
              </span>
              <div className={style.text}>党员</div>
            </div>
          </span>
        ),
        extraVal2: (
          <span className={style.peopleNum}>
            <div className={style.numCell}>
              <span className={style.bigNum}>
                {orgTotal || 0}
                <span className={style.text}>个</span>
              </span>
              <div className={style.text}>党组织</div>
            </div>
          </span>
        ),
        url: require('@/assets/desktop/dangyuan.png'),
      },
      {
        text: '发展党员情况',
        name: 'online',
        // value: devMem?.total || 0,
        value: 100,
        suf: '',
        chart: minAreaData,
        subText: () => {
          return (
            <span className={style.peopleNum}>
              <div className={style.numCell}>
                <span className={style.bigNum}>
                  {devMem?.d083 || 0}
                  <span className={style.text}>人</span>
                </span>

                <div className={style.text}>发展对象</div>
              </div>
              <div className={style.numCell}>
                <span className={style.bigNum}>
                  {devMem?.d084 || 0}
                  <span className={style.text}>人</span>
                </span>

                <div className={style.text}> 积极分子</div>
              </div>
              <div className={style.numCell}>
                <span className={style.bigNum}>
                  {devMem?.d085 || 0}
                  <span className={style.text}>人</span>
                </span>
                <div className={style.text}> 入党申请人</div>
              </div>
            </span>
          );
        },
        url: require('@/assets/desktop/online.png'),
      },
      // {
      //   text: '党费缴纳情况',
      //   name: 'fee',
      //   value: fee?.money || 0,
      //   suf: '',
      //   chart: 30,
      //   decimalPlaces:2,
      //   prefix: '￥',
      //   extraVal: `已交 ${fee?.pay|| 0}人`,
      //   extraVal2: `未交 ${fee?.notPay|| 0}人`,
      //   url: require('@/assets/desktop/dangfdei.png'),
      // },
      // {
      //   text: '活动情况',
      //   name: 'activity',
      //   value: activity?.activity || 0,
      //   suf: '',
      //   chart: 14,
      //   suffix: '次',
      //   extraVal: `已参加 ${ activity?.attend || 0 }人`,
      //   extraVal2: `未参加${ activity?.notAttend || 0 }人`,
      //   url: require('@/assets/desktop/huodong.png'),
      // },
    ];
    const daiban = [
      // { text: '共有15人有党政处分', icon: 'exclamation', time: '1小时前', color: '#FF6B6A', background: '#FFE8EB' },
      // { text: '截至今日还有5个人事项目报告未上传到资料库中备份', icon: 'bell', time: '1小时前', color: '#89A0FF', background: '#EAEEFF' },
      // { text: '您有一个待参加的的党员大会，请在明天上午10:00准时参加签到。', icon: 'bell', time: '1小时前', color: '#89A0FF', background: '#EAEEFF' },
    ];
    let browserWidth = document.body.clientWidth;

    const steps = [
      {
        element: '.newFishInfo1',
        title: '指引提示',
        intro: <div>指引提示1</div>,
      },
      {
        element: '.newFishInfo2',
        title: '指引提示',
        intro: <div>指引提示2</div>,
      },
    ];
    const packjson = require('../../../package.json');
    const columns = [
      {
        title: '信息分类',
        dataIndex: 'type',
        width: 50,
      },
      {
        title: '消息内容',
        dataIndex: 'message',
        width: 200,
        render: (text, record) => {
          return <div style={{ width: '100%', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>{text}</div>;
        },
      },
      {
        title: '已查看账号',
        dataIndex: 'consumer',
        width: 60,
        render: (text, record) => {
          return <div style={{ width: '100%', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>{text}</div>;
        },
      },
      {
        title: '是否本人查看',
        dataIndex: 'self',
        width: 40,
        align: 'center',
        render: (text, record) => {
          return text ? '是' : '否';
        },
      },
      {
        title: '操作',
        dataIndex: '',
        width: 60,
        align: 'center',
        render: (text, record) => {
          return (
            <Fragment>
              <a
                onClick={() => {
                  this.messageRead(record);
                }}
              >
                查看
              </a>
              <Divider type="vertical" />
              <a
                onClick={() => {
                  this.messageIgnore(record);
                }}
              >
                忽略
              </a>
            </Fragment>
          );
        },
      },
    ];
    const columns2 = [
      {
        title: '信息分类',
        dataIndex: 'type',
        width: 50,
      },
      {
        title: '消息内容',
        dataIndex: 'message',
        width: 200,
        render: (text, record) => {
          return <div style={{ width: '100%', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>{text}</div>;
        },
      },
      {
        title: '已查看账号',
        dataIndex: 'consumer',
        width: 120,
        render: (text, record) => {
          return <div style={{ width: '100%', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>{text}</div>;
        },
      },
      {
        title: '是否本人查看',
        dataIndex: 'self',
        width: 100,
        align: 'center',
        render: (text, record) => {
          return text ? '是' : '否';
        },
      },
      {
        title: '操作',
        dataIndex: '',
        width: 100,
        align: 'center',
        render: (text, record) => {
          return (
            <Fragment>
              <a
                onClick={() => {
                  this.messageRead(record);
                }}
              >
                查看
              </a>
              <Divider type="vertical" />
              <a
                onClick={() => {
                  this.messageIgnore(record);
                }}
              >
                忽略
              </a>
            </Fragment>
          );
        },
      },
    ];
    return (
      <div className={style.bg}>
        <Steps
          enabled={false}
          steps={steps}
          initialStep={0}
          onExit={(...e) => {}}
          options={{
            nextLabel: '下一步',
            prevLabel: '上一步',
            doneLabel: '结束',
            hidePrev: true,
            disableInteraction: true,
            exitOnEsc: false,
            scrollToElement: true,
            exitOnOverlayClick: false,
            overlayOpacity: 0.8,
            positionPrecedence: ['bottom', 'top', 'right', 'left'],
          }}
        />
        <div className={style.head}>
          <div className={style.logoTable}>
            <div className={style.logo}>
              <img src={require('@/assets/icon_h.png')} width={56} height={56} style={{ marginRight: 20 }} />
              <img src={require('@/assets/desktop/title2.png')} height={50} />
              <span style={{ fontSize: 22, fontWeight: 'bold', height: 30, lineHeight: '56px', color: 'white' }}>V{packjson.version}</span>
              {!_isEmpty(lastUpdateData) && (
                <span style={{ fontSize: 16, height: 30, lineHeight: '60px', color: 'white', marginLeft: '10px' }}>数据更新时间：{lastUpdateData}</span>
              )}
            </div>
            <div className={style.btns}>
              {/* 暂时屏蔽操作手册： */}
              {
                // headItems.map((item, index) => {
                //   return (
                //     <div key={index} className={style.btnsIcon} onClick={() => { this.heardIconClick(item) }}>
                //       {/* {item.type === 'antd' ? <span style={{ fontSize: 30, color: 'white' }}>{item['iocn']}</span> : <img src={item['iocn']} />} */}
                //       {item.type === 'antd' ? <span style={{ fontSize: 18, color: 'white', textDecoration: 'underline' }}>帮助手册</span> : <img src={item['iocn']} />}
                //     </div>
                //   );
                // })
              }
              <div className={style.avator}>
                <Avatar style={{ backgroundColor: '#7265e6', verticalAlign: 'middle' }} size="large">
                  {user['memName'] || ''}
                </Avatar>
                <span className={style.avatorName}>
                  <Tooltip title={user['memName']}>
                    {/*{isEmpty(user['name']) ? '' : user['name'].length > 3 ? user['name'].substring(0,3) + '...' : user['name'] }*/}
                    {user['memName']}
                  </Tooltip>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className={style.main}>
          <Row gutter={browserWidth <= 1450 ? 20 : 40}>
            <Col span={18} className={style.left}>
              {/* -----------左侧上--------- */}
              <Row justify="space-between" className={'newFishInfo1'}>
                {/* {
                  manyInfos.map((item, index) =>
                    <Col span={12} key={index} className={index % 2 == 0 ? style.nCard : style.nCard2}>
                      <NumberCard  {...item} />
                    </Col>)
                } */}
                <MoreCard />
              </Row>
              {/* -----------左侧下--------- */}
              <Row className={style.twoRow} style={{ marginTop: '20px' }}>
                <Col id="menus" span={24} className={'newFishInfo2'} style={{ height: '100%' }}>
                  <Card style={{ height: '100%', overflow: 'auto' }} className={style.manyInfosCards} bodyStyle={{ height: '100%' }}>
                    {/* 基础信息及其他子系统第三方系统图标渲染 */}
                    <SystemTabItem menuItemClick={this.menuItemClick}></SystemTabItem>
                  </Card>
                </Col>
              </Row>
            </Col>
            {/* ----------右侧----------- */}
            <Col span={6} className={style.twoRow}>
              <Card style={{ height: '100%', overflow: 'auto' }} className={style.manyInfosCards} bodyStyle={{ height: '100%' }}>
                <Tabs>
                  {radioDates2(this.state.messageCount).map((item, index) => {
                    // 屏蔽 问题反馈
                    if (index !== 2) {
                      return (
                        <Tabs.TabPane tab={item['text']} key={index + 1}>
                          {index == 1 && <div className={style.rightRows}>{this.renderTodo(daiban)}</div>}
                          {index == 0 && <div className={style.rightRows}>{this.renderMessageList()}</div>}
                          {index == 2 && <div className={style.rightRows}>{this.renderFeedback()}</div>}
                          {index == 3 && <div className={style.rightRows}>{this.renderNotice()}</div>}
                        </Tabs.TabPane>
                      );
                    }
                  })}
                </Tabs>
              </Card>
            </Col>
          </Row>
          <div className={style.footer}>@Copyright 2019 重庆南华中天信息技术有限公司 Inc. 保留所有权利</div>
          <Detail ref={(e) => (this['forumDetail'] = e)} />
        </div>
        {/* 系统设置 */}
        <SystemSet ref={(e) => (this['SystemSet'] = e)}></SystemSet>
        {/* 消息提醒-查看更多 */}
        <Modal
          title="消息提醒列表"
          destroyOnClose
          visible={messageVisible}
          // onOk={this.messageHandleOk}
          onCancel={this.messageHandleCancel}
          width={'calc(80vw)'}
          bodyStyle={{
            height: 640,
            overflow: 'auto',
          }}
          footer={false}
        >
          <ListTable
            scroll={{ y: 500 }}
            columns={columns}
            rowKey="code"
            data={this.state.modalMessagesList || []}
            pagination={pagination}
            onPageChange={(pageNum: any, pageSize: any) => {
              this.getMessageList({ pageNum, pageSize }, 'modal');
            }}
          />
        </Modal>

        {this?.state?.noticeList?.length > 0 &&
          this.state.noticeList.map((it, index) => {
            return <NoticeDel ref={(e) => (this['noticeDel' + index] = e)} onOK={() => this.getAnnouncement(1, 9, false)} />;
          })}

        <Modal
          footer={false}
          title="消息详情"
          destroyOnClose
          visible={messageDetailVisible}
          // onOk={this.messageDetailOk}
          onCancel={this.messageDetailCancel}
          width={'calc(40vw)'}
        >
          <div>
            <div style={{ border: '1px solid #E8E8E8', padding: '10px', marginBottom: '16px', borderRadius: '3px' }}>
              <span style={{ fontWeight: 'bold' }}>消息类别：</span>
              {currentType}
            </div>
            <div style={{ border: '1px solid #E8E8E8', padding: '10px', marginBottom: '16px', borderRadius: '3px' }}>
              <span style={{ fontWeight: 'bold' }}>消息内容：</span>
              {currentMessage}
            </div>
            <div style={{ color: 'rgb(129 129 129)' }}>{currentConsumer || '暂无'} 账号已查看</div>
          </div>
        </Modal>
        <ChangePassword account={user} wrappedComponentRef={(e) => (this['changePasswordRef'] = e)} isclear={this.isclear} />
      </div>
    );
  }
}
