/*
 * 人员管理 -- 基本信息
 * */
import React, { Fragment } from 'react';
import { DownOutlined, WarningTwoTone } from '@ant-design/icons';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Divider, Popconfirm, Menu, Button, Input, Dropdown, Switch } from 'antd';
import RuiFilter from 'src/components/RuiFilter';
import ListTable from 'src/components/ListTable';
import NowOrg from 'src/components/NowOrg';
import AddEdit from './AddEdit';
import OrgLevel from './OrgLevel';
import ToPositive from './Preparation/ToPositive';
import DelayMem from './Preparation/Delay';
import CancelMem from './Preparation/Cancel';
import TimeCheck from './TimeCheck';
import Photo from './Photo';
import Archives from './Archives';
import Lost from './Preparation/Lost';
import SddjTemplate from './SddjTemplate';
import ExportInfo from '@/components/Export';
import WhiteSpace from '@/components/WhiteSpace';
import _isEmpty from 'lodash/isEmpty';
import { withContext } from 'src/utils/global.jsx';
import { connect } from 'dva';
import { _history as router } from '@/utils/method';
import { getSession } from '@/utils/session';
import LeaveOrg, { LeaveTable } from './LeaveOrg';
import LockModal from './Lock/index2';
import qs from 'qs';
import { setListHeight, preLoadDicts, isFlowingParty } from '@/utils/method';
import { backToDevelopMem, backToProbationary, findMemResume } from '@/pages/mem/services';
import Tip from '@/components/Tip';
import { ModalTL } from '@/components/TimeLine';
import { ButtonDisabled } from '@/common/config.js';
import { TableActionMenu } from '@/pages/user/lock';
const { Search } = Input;
@withContext
@connect(({ becomeFullMem, commonDict, loading }) => ({ becomeFullMem, commonDict, loading }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      filterHeight: 100,
      filterChecked: {},
    };
  }
  componentDidMount(): void {
    setListHeight(this);

    // 预加载编辑字典表
    preLoadDicts([], this.props.dispatch);

    // 转正字典表
    this.props.dispatch({
      type: 'commonDict/getDict',
      payload: {
        data: {
          dicName: 'dict_d28',
        },
      },
    });
    // this.props.dispatch({
    //   type:'commonDict/getArea',
    //   payload:{
    //     parent:'-1',
    //   }
    // })
  }
  getList = (pageNum = 1, pageSize = 10, orgCode = '') => {
    !_isEmpty(orgCode) &&
      this.props.dispatch({
        type: 'memBasic/getList',
        payload: {
          data: {
            pageNum,
            pageSize,
            searchType: 1,
            memOrgCode: orgCode,
          },
        },
      });
  };
  // 筛选
  filterChange = (val) => {
    this.setState({ filterChecked: val });
    this.props.dispatch({
      type: 'memBasic/updateState',
      payload: {
        filter: val,
      },
    });
    const { pagination = {} } = this.props.memBasic;
    const { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`);
    // this.action()
    // this.setState({
    //   filter:val
    // },()=>this.action())
  };
  // 分页
  onPageChange = (page, pageSize) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`);
  };
  action = (val?: object) => {
    const { pagination = {} } = this.props.memBasic;
    const { current, pageSize } = pagination;
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memBasic/getList',
      payload: {
        data: {
          searchType: 1,
          memOrgCode: org['orgCode'],
          pageNum: current,
          pageSize,
          ...val,
        },
      },
    });
  };
  search = (value) => {
    this.props.dispatch({
      type: 'memBasic/updateState',
      payload: {
        memName: value,
      },
    });
    const { pagination = {} } = this.props.memBasic;
    const { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`);
    // this.action();
    // this.setState({
    //   search:{memName:value}
    // },()=>this.action());
  };
  searchClear = (e) => {
    this.setState({
      searchVal: e.target.value,
    });
    if (!e.target.value) {
      this.props.dispatch({
        type: 'memBasic/updateState',
        payload: {
          memName: undefined,
        },
      });
      this.action();
    }
  };
  // 人员编辑
  addOrEdit = async (record) => {
    // AddEdit['WrappedComponent'].clear();
    // console.log(AddEdit);
    AddEdit['WrappedComponent'].show();
    if (record && record['code']) {
      await this.props.dispatch({
        type: 'memBasic/findMem',
        payload: {
          code: record['code'],
        },
      });
    }
  };

  // 人员删除
  del = (record) => {
    // console.log(record,'人员删除');
    // this['LeaveOrg'].open(record);
    this['LeaveTable'].open(record);
  };
  // 取消关联
  cancel = (record, type) => {
    // console.log(record,'取消关联')
    switch (type) {
      case '1':
        this['ToPositive'].open(record);
        break;
      case '2':
        this['DelayMem'].open(record);
        break;
      case '3':
        this['CancelMem'].open(record);
        break;
      default:
        break;
    }
  };
  addNew = () => {
    let org = getSession('org') || {};
    AddEdit['WrappedComponent'].clear();
    this.props.dispatch({
      type: 'memBasic/updateState',
      payload: {
        basicInfo: {
          d01Code: org['d01Code'],
          orgName: org['name'],
          orgCode: org['code'],
          orgZbCode: org['zbCode'],
          memOrgCode: org['orgCode'],
        },
      },
    });
    AddEdit['WrappedComponent'].show();
  };
  // 查看当组织层级
  lookOrgs = (record) => {
    this['OrgLevel'].open(record);
  };
  // 导出
  exportInfo = async () => {
    this['memExportInfo'].open();
  };

  lock = () => {
    this['LockModal'].open();
  };

  lockOrUnlock = (record) => {
    const { pagination = {} } = this.props.memBasic;
    const { current, pageSize } = pagination;
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: current })}`);
  };

  componentWillUnmount(): void {
    this.props.dispatch({
      type: 'memBasic/updateState',
      payload: {
        memName: undefined,
        filter: {},
      },
    });
  }
  render(): React.ReactNode {
    const { memBasic = {}, loading: { effects = {} } = {}, commonDict, compType } = this.props;
    const { list, pagination, memName, filter } = memBasic;
    const { current, pageSize } = pagination;
    const { filterHeight } = this.state;
    const filterData = [
      {
        key: 'd09CodeList',
        name: '工作岗位',
        value: commonDict['dict_d09_tree'],
      },
      {
        key: 'sexCodeList',
        name: '人员性别',
        value: [
          { key: '1', name: '男' },
          { key: '0', name: '女' },
        ],
      },
      {
        key: 'd08CodeList',
        name: '人员类型',
        value: [
          { key: '1', name: '正式党员' },
          { key: '2', name: '预备党员' },
        ],
      },
      {
        key: 'd07CodeList',
        name: '学历教育',
        value: commonDict['dict_d07_tree'],
      },
      {
        key: 'd194CodeList',
        name: '国民经济行业',
        value: this.props.commonDict[`dict_d194_tree`],
      },
      {
        key: 'd195CodeList',
        name: '生产性服务行业',
        value: this.props.commonDict[`dict_d195_tree`],
      },
    ];
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 60,
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 100,
        render: (text, record) => {
          if (compType == 'lock') {
            return text;
          }
          return (
            // 在党员查询列表里面加一个图标，鼠标悬停后显示，该预备党员预备期已满，请及时维护
            <div>
              {record['message'] ? <WarningTwoTone style={{ fontSize: '20px' }} twoToneColor={'#faad14'} title={record['message']} /> : ''}{' '}
              <a onClick={() => this.addOrEdit(record)}>{text}</a>
            </div>
          );
        },
      },
      {
        title: '性别',
        dataIndex: 'sexCode',
        width: 100,
        render: (text) => {
          return <span> {text === '1' ? '男' : '女'} </span>;
        },
      },
      {
        title: '公民身份证',
        dataIndex: 'idcard',
        width: 160,
        render: (text, record) => {
          if (typeof text === 'string' && !_isEmpty(text)) {
            // let newVal=text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
            let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z\(\)\[\]]{4})/, '$1***********$2'); //增加港澳台身份证马赛克
            if (text.indexOf('*') > 0) {
              return text;
            }
            return <span>{newVal}</span>;
          } else {
            return '';
          }
        },
      },
      {
        title: '电话',
        width: 100,
        dataIndex: 'phone',
        render: (text, record) => {
          if (typeof text === 'string' && !_isEmpty(text)) {
            let newVal = text.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
            if (text.indexOf('*') > 0) {
              return text;
            }
            return <span>{newVal}</span>;
          } else {
            return '';
          }
        },
      },
      {
        title: '党员类型',
        width: 120,
        dataIndex: 'd08Name',
      },
      {
        title: '所在组织',
        width: 260,
        dataIndex: 'orgName',
        render: (text, record) => {
          return <a onClick={() => this.lookOrgs(record)}>{text}</a>;
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 120,
        render: (text, record) => {
          const { d08Code, isExtend, isExtendPrepare } = record;
          let menuArr = [
            { key: '1', value: '预备党员转正' },
            { key: '2', value: '延长预备期' },
            // {key:'3',value:'撤销延长'},
          ];
          if (isExtendPrepare === 1) {
            menuArr = menuArr.filter((it) => it.key != '2');
          }
          if (isExtend !== 1) {
            menuArr = menuArr.filter((item) => item['key'] !== '3');
          }
          // 当此页面作为组件时，会有compType对应的功能
          if (compType == 'lock') {
            return <TableActionMenu record={record} lockOrUnlock={this.lockOrUnlock} type={'1'} />;
          }
          return (
            <span>
              <a onClick={() => this.addOrEdit(record)}>编辑</a>
              {/* <Divider type="vertical"/> */}
              {/*<Popconfirm title={d08Code === '2' ? '是否取消预备党员资格？' : '是否离开党组织？'} onConfirm={()=>this.del(record)}>*/}
              {/*    <a className={'del'} >{d08Code === '2' ? '取消预备党员资格' : '离开党组织'}</a>*/}
              {/*</Popconfirm>*/}
              <Divider type="vertical" />
              <TableActionMenu record={record} lockOrUnlock={this.lockOrUnlock} type={'1'} />
              <Divider type="vertical" />
              <Dropdown
                overlay={
                  <Menu>
                    {d08Code === '2'
                      ? menuArr.map((item, index) => {
                          return (
                            <Menu.Item key={index}>
                              <a onClick={() => this.cancel(record, item['key'])}>{item['value']}</a>
                            </Menu.Item>
                          );
                        })
                      : null}
                    <Menu.Item>
                      <Popconfirm title={'离开党组织仅包含死亡、错误录入，离开本支部请使用关系转接进行处理！'} placement="topRight" onConfirm={() => this.del(record)}>
                        <a className={'del'}>{d08Code === '2' ? '离开党组织' : '离开党组织'}</a>
                      </Popconfirm>
                    </Menu.Item>
                    <Menu.Item>
                      <a
                        onClick={() => {
                          this['Lost'].open({ record });
                        }}
                      >
                        失联党员
                      </a>
                    </Menu.Item>

                    {d08Code === '1' && (
                      <Menu.Item>
                        <Popconfirm
                          title={d08Code === '2' ? '是否预备党员退回到发展党员？' : '是否党员退回到预备党员？'}
                          onConfirm={async () => {
                            let url: any = backToProbationary;
                            if (d08Code == '2') {
                              url = backToDevelopMem;
                            }
                            const { code: resCode = 500 } = await url({ code: record.code });
                            if (resCode === 0) {
                              Tip.success('操作提示', '操作成功');
                              this.action();
                            }
                          }}
                        >
                          <a>{d08Code == '2' ? '预备党员退回到发展党员' : '党员退回到预备党员'}</a>
                        </Popconfirm>
                      </Menu.Item>
                    )}

                    <Menu.Item>
                      <a
                        onClick={() => {
                          this['ModalTL'].open(record);
                        }}
                      >
                        人员时间轴
                      </a>
                    </Menu.Item>

                    <Menu.Item>
                      <a
                        onClick={() => {
                          this['TimeCheck'].open(record);
                        }}
                      >
                        入党时间修正
                      </a>
                    </Menu.Item>

                    {/*{*/}
                    {/*  d08Code == '2' ?*/}
                    {/*    :*/}
                    {/*    <Menu.Item>*/}
                    {/*      <a onClick={()=>{*/}

                    {/*      }}>党员退回到预备党员</a>*/}
                    {/*    </Menu.Item>*/}
                    {/*}*/}

                    {sessionStorage.getItem('dataApi') == '3b571e29df3445a8' && (
                      <Fragment>
                        <Menu.Item>
                          <a
                            onClick={() => {
                              this['Photo'].open(record);
                            }}
                          >
                            照片采集
                          </a>
                        </Menu.Item>

                        <Menu.Item>
                          <a
                            onClick={() => {
                              this['Archives'].open(record, 'step0');
                            }}
                          >
                            档案录入
                          </a>
                        </Menu.Item>

                        <Menu.Item>
                          <a
                            onClick={() => {
                              this['SddjTemplate'].open(record);
                            }}
                          >
                            模板录入
                          </a>
                        </Menu.Item>

                        <Menu.Item>
                          <a
                            onClick={() => {
                              this['Archives'].open(record, 'step12');
                            }}
                          >
                            数据补充
                          </a>
                        </Menu.Item>
                      </Fragment>
                    )}
                  </Menu>
                }
              >
                <a className="ant-dropdown-link">
                  业务操作 <DownOutlined />
                </a>
              </Dropdown>
            </span>
          );
        },
      },
    ];
    const org = getSession('org') || { d01Code: '' };
    const { d01Code = '' } = org || {};
    // 权限列表有92	才显示补录按钮
    const pidArr: any = getSession('pid') || [];

    return (
      <Fragment>
        <NowOrg
          extra={
            <React.Fragment>
              {isFlowingParty() && (
                <>
                  {(d01Code === '631' || d01Code === '632' || d01Code === '634' || d01Code === '931' || d01Code === '932') && !compType && (
                    <Button htmlType={'button'} onClick={this.lock} style={{ marginRight: 16 }}>
                      批量锁定
                    </Button>
                  )}
                  {!compType && (
                    <Button htmlType={'button'} onClick={this.exportInfo} disabled={_isEmpty(list)}>
                      导出
                    </Button>
                  )}
                  {(d01Code === '631' || d01Code === '632' || d01Code === '634' || d01Code === '931' || d01Code === '932') &&
                    !ButtonDisabled.statistics2021 &&
                    !compType &&
                    pidArr.includes(92) && (
                      <Button htmlType={'button'} type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.addNew} style={{ marginLeft: 16 }}>
                        补录党员
                      </Button>
                    )}
                </>
              )}

              <Search style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'} />
            </React.Fragment>
          }
        />
        {!compType && (
          <Fragment>
            <RuiFilter
              data={filterData}
              onChange={this.filterChange}
              openCloseChange={() => {
                setListHeight(this);
              }}
            />
          </Fragment>
        )}
        <WhiteSpace />
        <WhiteSpace />
        <ListTable scroll={{ y: filterHeight }} columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange} />
        <AddEdit getList={this.action} />
        <Lost ref={(e) => (this['Lost'] = e)} onOK={this.action} />
        <LeaveOrg wrappedComponentRef={(e) => (this['LeaveOrg'] = e)} onClose={this.action} />
        <LeaveTable
          ref={(e) => (this['LeaveTable'] = e)}
          onOK={(record) => {
            this['LeaveOrg'].open(record, 'manage');
          }}
        />
        <OrgLevel {...this.props} ref={(e) => (this['OrgLevel'] = e)} />
        <ToPositive wrappedComponentRef={(e) => (this['ToPositive'] = e)} {...this.props} onClose={this.action} />
        <DelayMem wrappedComponentRef={(e) => (this['DelayMem'] = e)} {...this.props} onClose={this.action} />
        <CancelMem wrappedComponentRef={(e) => (this['CancelMem'] = e)} {...this.props} onClose={this.action} />
        <ExportInfo
          wrappedComponentRef={(e) => (this['memExportInfo'] = e)}
          expinfo={[
            {
              label: '党员基本信息',
              value: '1',
            },
            {
              label: '本年内参加民主评议的党员',
              value: '2',
            },
            {
              label: '本年内受纪律处分党员',
              value: '3',
            },
          ]}
          expType="exportType"
          tableName={'ccp_mem'}
          tableListQuery={{ memName, ...filter, searchType: 1, memOrgCode: org['orgCode'] }}
          action={'/api/data/mem/exportData'}
        />
        <ModalTL ref={(e) => (this['ModalTL'] = e)} timeLineAction={findMemResume} />
        <TimeCheck ref={(e) => (this['TimeCheck'] = e)} onOK={this.action} />

        <Photo ref={(e) => (this['Photo'] = e)} onOK={this.action} />
        <Archives ref={(e) => (this['Archives'] = e)} onOK={this.action} />
        <SddjTemplate ref={(e) => (this['SddjTemplate'] = e)} onOK={this.action} />
        <LockModal ref={(e) => (this['LockModal'] = e)} onOK={this.action} />
      </Fragment>
    );
  }
}
