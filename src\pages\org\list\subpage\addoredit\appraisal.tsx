/**
 * 民主评议
 **/
import React from 'react';
import { connect } from 'dva';
import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, Collapse, Modal, Popconfirm, Divider } from 'antd';

import AddAppraisal from './addAppraisal';
import Add<PERSON>erson from './addPerson';
import MemSelect from '@/components/MemSelect';
import ListTable from '@/components/ListTable';
import head from '@/assets/head.jpg';
import styles from './leader.less';
import moment from 'moment';

import Tip from '@/components/Tip';
import CTbale, {PanelTable, PanelListTable} from '@/components/CollapseTable';
import {appraisalList, personList} from '@/pages/org/services/org';
import { selfRowSelectionSate } from '@/utils/method.js';

const Panel = Collapse.Panel;
// @ts-ignore
@connect(({ org }) => ({ org }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      type: 'add',
      visible: false,
      selectedRowKeys: [],
      selectedItems:[],
      personPage: 1,
    };
  }

  findListMem = (pageNum = 1, pageSize = 10, code) => {
    const { basicInfo } = this.props.org;
    if (basicInfo['code']) {
      this.props.dispatch({
        type: 'org/listMem',
        payload: {
          pageNum,
          pageSize,
          code,
        },
      });
    }
  };
  del = async (e, item) => {
    e.stopPropagation();
    const obj = await this.props.dispatch({
      type: 'org/appraisalRemove',
      payload: {
        code: item['code'],
      },
    });
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示','操作成功');
      this['CTbale'].getList();
    }
  };
  header = (item, index) => {
    const { basicInfo } = this.props.org;
    return (
      <span className={styles.header}>
                {/* <span>评议年度：{moment(item['year']).format('YYYY')}</span> */}
                <span>评议时间：{moment(item['startTime']).format('YYYY年MM月DD日')}~{moment(item['endTime']).format('YYYY年MM月DD日')}</span>
                <span>应评议人数：{item['peopleToBeReviewed']}</span>
                <span>已评议人数：{item['peopleReviewed']}</span>
                <span>评议情况：{item['situation'] === 1 ? '开始评议' : '结束评议'}</span>
                <div>
                  <a href={'#'} onClick={(e) => this.editJc(e, item)}>编辑</a>
                  <div style={{display: 'inline-block'}} onClick={(e:any)=>e.stopPropagation()}>
                    <Popconfirm title="是否删除该民主评议?" onConfirm={(e) => this.del(e, item)} okText="是" cancelText="否" onCancel={(e:any) => e.stopPropagation()}>
                        <a href={'#'} className={'del'} onClick={e => e.stopPropagation()}>删除</a>
                    </Popconfirm>
                  </div>
                </div>
            </span>
    );
  };
  editJc = (e, item) => {
    e.stopPropagation();
    this.setState({ type: 'edit', dataInfo: item }, () => {
      this['addAppraisal'].showModal();
    });

  };

  editMem = () => {
    this.setState({ type1: 'editMem' });
    this['AddMember'].showModal();
  };
  add = (e) => {
    this.setState({
      type: 'add',
      dataInfo: undefined,
    }, () => {
      this['addAppraisal'].showModal();
    });
  };
  delGroupMem = (item) => {
    this.props.dispatch({
      type: 'org/personRemove',
      payload: {
        code: item['code'],
      },
    }).then(res => {
      Tip.success('操作提示','操作成功');
      this['PanelTable'].getList();
    });
  };
  //新增个人
  addGroupMem = (val, memList, key) => {
    this.setState({
      type: 'addPerson',
      dataInfo: { memCode: [val[0]?.code], appraisalCode: memList?.code, key: key, orgCode: memList?.orgCode,d08Code:val[0]?.d08Code },
    }, () => {
      this['addPerson'].showModal();
    });
  };
  //新增批量
  addMem = (val, item, key) => {
    let arr = val.map(i => {
      return i.code;
    });
    this.setState({
        type: 'addPerson',
        dataInfo: { memCode: arr, appraisalCode: item?.code, key: key, orgCode: item?.orgCode },
      },
      () => {
        this['addPerson'].showModal();
      });
  };
  //个人编辑
  personEdit = (obj, item, key) => {
    this.setState({
      type: 'editPerson',
      visible: false,
      dataInfo: {
        appraisalCode: obj?.code,
        key: key,
        orgCode: obj?.orgCode,
        ...item,
        code: [item?.code],
      },
    });
    this['addPerson'].showModal();
  };
  //人员批量编辑
  handleOk = () => {
    this.setState({
      type: 'editPerson',
      selectedRowKeys:[],
      selectedItems:[],
      visible: false,
      dataInfo: {
        code: this.state.selectedRowKeys,
        appraisalCode: this.state.appraisalCode,
        key: this.state.dataInfoKey,
        orgCode: this.state.orgCode,
      },
    });
    this['addPerson'].showModal();

  };
  handleCancel = () => {
    this.setState({
      visible: false,
      selectedRowKeys:[],
      selectedItems:[],
      dataInfoKey: undefined,
      appraisalCode: undefined,
      orgCode: undefined,
    });
  };
  onSelectChange = (selectedRowKeys, record) => {
    const { onChange } = this.props;
    this.setState({
      selectedRowKeys, selectedItems: record,

    });

  };
  onPageChange = (page, size) => {
    this.findListMem(page, size, this.state.appraisalCode);
  };

  render() {
    const { type, selectedRowKeys } = this.state;
    const { appList, basicInfo, listMemData, listMemPagination={}} = this.props.org;
    const { org ,tipMsg = {}} = this.props;
    const columns = [
      // {
      //   title:'序号',
      //   dataIndex:'num',
      //   render:(text,record,index)=>{
      //     return (current-1)*pageSize+index+1
      //   }
      // },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 80,
      },
      {
        title: '性别',
        dataIndex: 'sexName',
        width: 80,
      },
      {
        title: '公民身份证',
        dataIndex: 'idcard',
        width: 160,
      },
      {
        title: '电话',
        dataIndex: 'phone',
        width: 100,
      },
      {
        title: '党员类型',
        dataIndex: 'd08Name',
        width: 80,
      },
      {
        title: '所在组织',
        dataIndex: 'orgName',
        width: 200,
      },
    ];
    const rowSelection = {
      selectedRowKeys,
      // onChange: this.onSelectChange,
      // type:checkType ? checkType : 'radio',
      // onSelect: this.onSelect,
      // hideDefaultSelections: true,
      ...selfRowSelectionSate(this)
    };
    return (
      <div style={{ padding: '0 20px' }}>
         <Button type="primary" icon={<PlusOutlined/>} style={{ marginBottom: 10 }} onClick={this.add}>新增民主评议</Button>
         <CTbale
            tableActionOtherQueries={{ orgCode: this.props.org.basicInfo.code }}
            tableListAction={appraisalList}
            ref={e => this['CTbale'] = e}
            mains={(item, index)=>{
              return (
                <React.Fragment>
                  <div style={{marginBottom: 10 }}>
                  <MemSelect org={basicInfo} onChange={(val) => this.addGroupMem(val, item, index)}>
                    <Button type="primary" style={{ marginRight:10}}>添加人员</Button>
                  </MemSelect>
                  <MemSelect org={basicInfo} checkType={'checkbox'} onChange={(val) => this.addMem(val, item, index)}>
                    <Button type="primary" style={{ marginRight:10}}>批量添加</Button>
                  </MemSelect>
                  <Button onClick={ async () => {
                        this.findListMem(1, 10, item['code']);
                        this.setState({
                          visible: true,
                          dataInfoKey: index,
                          appraisalCode: item?.code,
                          orgCode: item?.orgCode,
                        });
                      }} style={{ paddingLeft: '16px' }}>批量修改</Button>
                  </div>

                <PanelListTable
                  data={item}
                  mainsListAction={personList}
                  mainsActionOtherQueries={{ appraisalCode: item['code'], leave: 0 }}
                  ref={e => this['PanelTable'] = e}
                  columns={[
                    {
                      title:'姓名',
                      dataIndex:'memName',
                      width:100,
                    },
                    {
                      title:'评议结果',
                      dataIndex:'resultName',
                      width:150,
                    },
                    {
                      title:'操作',
                      dataIndex:'action',
                      width:130,
                      render:(text,record)=>{
                        return(
                          <div>
                            <a onClick={() =>  this.personEdit(item, record, index)}>编辑</a>
                            <Popconfirm title="是否删除?" onConfirm={() => {
                              this.delGroupMem(record);
                            }} okText="是" cancelText="否">
                               <Divider type="vertical"/>
                              <a style={{ color: 'red' }}>删除</a>
                            </Popconfirm>
                          </div>
                        )
                      },
                    },
                  ]}
                />
                </React.Fragment>
              )
            }}
            panelHeader={(obj, listLndex) => {
              return this.header(obj, listLndex)
            }} />
          {/*<AddOrEdit/>*/}
          <AddAppraisal
            queryList={()=>{
              this['CTbale'].getList();
            }}
            title={type === 'edit' ? '编辑' : '新增'}
            wrappedComponentRef={(e) => this['addAppraisal'] = e}
            {...this.props}
            {...this.state}
          />
        <AddPerson
          queryList={()=>{
            this['PanelTable'].getList();
          }}
          editForm={()=>{
          }}
          title={type === 'editPerson' ? '编辑' : '新增'}
          wrappedComponentRef={(e) => this['addPerson'] = e}
          {...this.props}
          {...this.state} />
        <Modal
          title={'人员选择'}
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
          maskClosable={false}
          destroyOnClose
          footer={[
            <Button onClick={this.handleCancel}>取消</Button>,
            <Button type="primary" onClick={this.handleOk}
                    disabled={!(this.state.selectedRowKeys.length > 0)}>确定</Button>,
          ]}
        >
          <ListTable
            rowSelection={rowSelection}
            columns={columns}
            data={listMemData || []}
            pagination={{...listMemPagination, pageNum:listMemPagination?.pageNumber,total:listMemPagination?.totalRow, current:listMemPagination?.pageNumber}}
            onPageChange={this.onPageChange}
            scroll={{ y: 254 }}
            rowKey={record => record['code']}
          />
        </Modal>
      </div>
    );
  }
}
