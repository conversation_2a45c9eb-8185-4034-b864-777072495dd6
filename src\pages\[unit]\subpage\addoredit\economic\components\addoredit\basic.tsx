/**
 * 模块名
 */
import React from 'react';
import '@ant-design/compatible/assets/index.css';
import { Col, Input, Button, Switch, Row, DatePicker, InputNumber, Alert, Select } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import Tip from '@/components/Tip';
import WhiteSpace from '@/components/WhiteSpace';
import DictArea from '@/components/DictArea';
import { getSession } from '@/utils/session';
import { connect } from 'dva';
import { formLabel, isEmpty } from '@/utils/method';
import { Form } from '@ant-design/compatible';
import DictSelect from '@/components/DictSelect';
import { CheckOutlined, DeleteOutlined } from '@ant-design/icons';
import { collectiveEconomicAdd } from '../../services'
import UnitSelect from '@/components/UnitSelect';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import _get from 'lodash/get';
import moment from 'moment';
import DateTime from '@/components/Date';
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      loading: false
    };
  }
  handleSubmit = () => {
    // const { basicInfo = {} } = this.props.unitIn;
    const { basicInfo = {} } = this.props; // 单位基本信息
    const { dataInfo = {} } = this.props;
    const { code: orgCode = '', orgCode: economicOrgCode = '' } = getSession('org') || { code: '', orgCode: '' };
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if (val.createTime) {
          val.createTime = moment(val.createTime).valueOf()
        }
        ['d128Code', 'd129Code'].forEach(key => {
          if (val[key] != undefined && typeof val[key] == "object") {
            val[key] = val[key]['key']
          }
        })
        if (val['developMoney'] > 200) {
          Tip.error('操作提示', '投入资金（万元）不能大于200')
          return;
        }
        this.setState({ loading: true })
        const { code: resCode = 500 } = await collectiveEconomicAdd({
          data: {
            ...val,
            economicOrgCode: basicInfo?.createUnitOrgCode,
            orgCode: basicInfo?.createOrgCode,
            code: dataInfo?.code,
            unitCode: typeof val['unitCode'] == 'object' ? _get(val, 'unitCode[0].code') : val?.unitCode
          }
        });
        this.setState({ loading: false })
        if (resCode === 0) {
          Tip.success('操作提示', dataInfo['code'] ? '修改成功' : '新增成功');
          if (!dataInfo['code']) {
            this.props.close({ colseType: 'add' });
          } else {
            this.props.dispatch({
              type: 'unitIn/findOrg',
              payload: {
                code: dataInfo['code'],
              },
            });
          }
        }
      }
    })
  };
  render() {
    const { unitAdd, unitUpdate, tipMsg = {}, form, dataInfo } = this.props;
    const { getFieldDecorator } = form;
    // const { basicInfo = {} } = this.props.unitIn;
    const { basicInfo = {} } = this.props;

    return (
      <Form {...formItemLayout}>
        <FormItem label={formLabel('集体经济组织名称', tipMsg['industryName'])}>
          {getFieldDecorator('industryName', {
            initialValue: dataInfo['industryName'] || '',
            rules: [
              { required: true, message: '请输入!' },
            ],
          })(
            <Input placeholder={'集体经济组织名称'} />
          )}
        </FormItem>
        <FormItem label={formLabel('发展经济类型', tipMsg['d128Code'])}>
          {getFieldDecorator('d128Code', {
            initialValue: dataInfo['d128Code'] || '',
            rules: [
              { required: true, message: '请选择!' },
            ],
          })(
            <DictTreeSelect initValue={dataInfo['d128Code']} backType={'object'} codeType={'dict_d128'} placeholder={'发展经济类型'} parentDisable={true} />
          )}
        </FormItem>

        <FormItem label={formLabel('投入资金（万元）', tipMsg['developMoney'])}>
          {getFieldDecorator('developMoney', {
            initialValue: dataInfo['developMoney'] || '',
            rules: [
              { required: true, message: '请输入!' },
            ],
          })(
            <InputNumber placeholder={'投入资金（万元）'} min={0} style={{ width: '100%' }} />
          )}
        </FormItem>
        <FormItem label={formLabel('采取组织形式', tipMsg['d129Code'])}>
          {getFieldDecorator('d129Code', {
            initialValue: dataInfo['d129Code'] || '',
            rules: [
              { required: true, message: '请选择!' },
            ],
          })(
            <DictTreeSelect initValue={dataInfo['d129Code']} backType={'object'} codeType={'dict_d129'} placeholder={'采取组织形式'} parentDisable={true} />
          )}
        </FormItem>
        {/* <FormItem label={formLabel('组织形式金额（万元）', tipMsg['organAmount'])}>
          {getFieldDecorator('organAmount', {
            initialValue:basicInfo['organAmount'] || '',
            rules: [
              { required: true, message: '请选择!' },
            ],
          })(
            <InputNumber placeholder={'组织形式金额（万元）'} min={0} style={{width:'100%'}}/>
          )}
        </FormItem> */}
        <FormItem label={formLabel('受益农村人口数', tipMsg['benefitPopulation'])}>
          {getFieldDecorator('benefitPopulation', {
            initialValue: dataInfo['benefitPopulation'] || '',
            rules: [
              { required: true, message: '请选择!' },
            ],
          })(
            <InputNumber placeholder={'受益农村人口数'} min={0} max={99999999} style={{ width: '100%' }} />
          )}
        </FormItem>

        <LongLabelFormItem label={'村党组织的书记担任村级集体经济组织负责人的村'}
          required={true}
          code={'hasEconomicVillage'}
          tipMsg={tipMsg}
          formItemLayout={formItemLayout}
          formItem={(formItemLayout, code) => {
            return (
              <FormItem {...formItemLayout}
              >
                {getFieldDecorator(code, {
                  rules: [{ required: true, message: '村党组织的书记担任村级集体经济组织负责人的村' }],
                  initialValue: dataInfo[code],
                })(
                  <Select style={{ width: '100%' }}>
                    <Select.Option value={1}>是</Select.Option>
                    <Select.Option value={0}>否</Select.Option>
                  </Select>
                )}
              </FormItem>
            )
          }} />

        <FormItem label={formLabel('创建时间', tipMsg['createTime'])}>
          {getFieldDecorator('createTime', {
            initialValue: dataInfo['createTime'] ? moment(dataInfo['createTime']) : undefined,
            rules: [
              { required: false, message: '请选择!' },
            ],
          })(
            <DateTime />
          )}
        </FormItem>

        <FormItem label={formLabel('单位名称', tipMsg['unitCode'])}>
          {getFieldDecorator('unitCode', {
            initialValue: dataInfo['unitCode'] || basicInfo?.code,
            rules: [
              { required: true, message: '请选择!' },
            ],
          })(
            <UnitSelect disabled={true}
              org={this.state['org'] || getSession('org')}
              isCreateOrg={undefined}
              unitType={["92", "921", "922", "923"]}
              // disabledColFunc={(record)=>{
              //   const {mainOrgName = ''} = record || {};
              //   return !!mainOrgName;
              // }}
              onChange={(val) => {
                const [unit] = val;
                this.props.form.setFieldsValue({
                  unitName: unit.name,
                });
              }}
              initValue={dataInfo['unitName'] ? dataInfo['unitName'] : basicInfo?.name}
            />
          )}
        </FormItem>
        <div style={{ textAlign: 'center' }}>
          <WhiteSpace />
          <Button
            type={'primary'}
            htmlType={'submit'}
            icon={<CheckOutlined />}
            onClick={this.handleSubmit}
            style={{ marginRight: 16 }}
            loading={this.state.loading}
          >
            保存
          </Button>
          <Button
            danger
            type={'primary'}
            htmlType={'button'}
            icon={<DeleteOutlined />}
            onClick={() => this.props.close()}
          >
            取消
          </Button>
        </div>
      </Form>
    );
  }
}
export default Form.create()(index);
