import React, { Fragment } from 'react';
import { Mo<PERSON>,Button } from 'antd';
import { weChatConfig } from '@/common/config.js';
const QRCode = require('qrcode.react');
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible:false
    }
  }
  open=(record)=>{
    this.setState({
      visible:true,
      record
    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      record:{}
    })
  };
  download=()=>{
    const {record:{name = ''}={}} = this.state;
    let canvasElement = document.getElementById('QRCode');
    if(canvasElement){
      let MIME_TYPE = "image/png";
      let imgURL = canvasElement['toDataURL'](MIME_TYPE);
      let dlLink = document.createElement('a');
      dlLink.download = `${name}二维码`;
      dlLink.href = imgURL;
      dlLink.dataset.downloadurl = [MIME_TYPE, dlLink.download, dlLink.href].join(':');
      document.body.appendChild(dlLink);
      dlLink.click();
      document.body.removeChild(dlLink);
    }
  };
  render() {
    const {visible,record} = this.state;
    return (
      <Modal
        destroyOnClose
        title={'查看二维码'}
        visible={visible}
        onCancel={this.handleCancel}
        width={600}
        bodyStyle={{height:'auto',overflow:'auto',textAlign:'center'}}
        footer={null}
      >
        {
          visible && record &&
            <Fragment>
              <QRCode size={200} value={`${weChatConfig['redirect_uri']}activitysign?code=${record['code']}`} id={'QRCode'}/>
              <div>
                <Button onClick={this.download}>下载二维码</Button>
              </div>
            </Fragment>
        }
      </Modal>
    );
  }
}
