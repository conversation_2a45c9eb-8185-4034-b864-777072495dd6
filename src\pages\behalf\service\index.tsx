import request from 'src/utils/request';
import qs from 'qs';

export function getList(params) {
  return request('/api/representative/contact/getList',{
    method:'POST',
    body:params,
  });
}
export function addContact(params) {
  return request('/api/representative/contact/addContact',{
    method:'POST',
    body:params,
  });
}
export function findByCode(params) {
  return request(`/api/representative/contact/findByCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function updateContact(params) {
  return request('/api/representative/contact/updateContact',{
    method:'POST',
    body:params,
  });
}
export function delContact(params) {
  return request(`/api/representative/contact/delContact?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function findByUnitCode(params) {
  return request(`/api/representative/elect/findByUnitCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function checkElectDate(params) {
  return request('/api/representative/elect/checkElectDate',{
    method:'POST',
    body:params,
  });
}
export function addElect(params) {
  return request('/api/representative/elect/addElect',{
    method:'POST',
    body:params,
  });
}
export function getjcList(params) {
  return request('/api/representative/elect/getList',{
    method:'POST',
    body:params,
  });
}

export function delElect(params) {
  return request(`/api/representative/elect/delElect?${qs.stringify(params)}`,{
    method:'GET',
  });
}
//根据组织查找单位
export function getUnitByOrgCode(params) {
  return request(`/api/representative/getUnitByOrgCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function getSubElectList(params) {
  return request(`/api/representative/getSubElectList?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function addRepresentative(params) {
  return request('/api/representative/addRepresentative',{
    method:'POST',
    body:params,
  });
}

export function representativeList(params) {
  return request('/api/representative/getList',{
    method:'POST',
    body:params,
  });
}

export function bfFindByCode(params) {
  return request(`/api/representative/findByCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function representativeUp(params) {
  return request('/api/representative/updateRepresentative',{
    method:'POST',
    body:params,
  });
}

export function delRepresentative(params) {
  return request(`/api/representative/delRepresentative?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function updateElect(params) {
  return request('/api/representative/elect/updateElect',{
    method:'POST',
    body:params,
  });
}
export function getQueryList(params) {
  return request('/api/representative/list',{
    method:'POST',
    body:params,
  });
}

