/**
 * 添加党小组
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { DatePicker, Input, Modal, InputNumber, Select } from "antd";
import moment from 'moment';
import Tip from '@/components/Tip';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import { formLabel, isEmpty } from '@/utils/method';
import _isArray from 'lodash/isArray';

const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;
const formItemLayout1 = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};
class addAppraisal extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      d85: '',
    };
    addAppraisal.showModal = this.showModal;
  }

  static showModal() {
  };

  showModal = () => {
    this.setState({
      visible: true,
    });
  };


    handleOk = () => {
        const { memBasic: { basicInfo = {} } = {} } = this.props;
        const { children, title, dataInfo = {}, type } = this.props;
        this.props.form.validateFieldsAndScroll(async (err, val) => {
            if (!err) {
                let obj = undefined;
                if (val['year']) {
                    val['year'] = val['year'].valueOf();
                }
                if (type == 'edit') {
                    obj = await this.props.dispatch({
                        type: 'memAppraisal/personUpdate',
                        payload: {
                            data: {
                                ...dataInfo,
                                ...val,
                                memCode: basicInfo?.code,
                            }
                        }
                    });
                } else {
                    obj = await this.props.dispatch({
                        type: 'memAppraisal/personSave',
                        payload: {
                            data: {
                                ...val,
                                memCode: basicInfo?.code,
                                orgCode: basicInfo?.orgCode
                            }
                        }
                    });
                }

                if (obj && obj['code'] === 0) {
                    Tip.success('操作提示', dataInfo['code'] ? '修改成功' : '新增成功');
                    this.props.queryList();
                    this.handleCancel();
                }
            }
        });

    };
    handleCancel = () => {
        this.setState({
            visible: false,
        });
    };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { children, title, dataInfo = {}, years = [], type } = this.props;
    return (
      <React.Fragment>
        {
          this.state.visible &&
          <Modal
            title={title || '请输入标题'}
            visible={this.state.visible}
            onOk={() => this.handleOk()}
            onCancel={this.handleCancel}
            width={500}
            className='add_randp_modal1'
            maskClosable={false}
            destroyOnClose
          >
            <Form {...formItemLayout1}>
              <FormItem
                label={formLabel('评议年份')}
              >
                {getFieldDecorator('appraisalCode', {
                  initialValue: dataInfo['appraisalCode'],
                  rules: [{ required: true, message: '请选择批准日期' }],
                })(
                  <Select>
                    {
                      _isArray(years) && !isEmpty(years) && years.map((i, index) => {
                        return (
                          <Option key={index} value={i?.code}>{i?.year}</Option>
                        );
                      })
                    }
                  </Select>,
                )}

                            </FormItem>

                            <FormItem
                                label={formLabel('评议结果')}
                            >
                                {getFieldDecorator('result', {
                                    initialValue: dataInfo['key'],
                                    rules: [{ required: true, message: '请选择批准日期' }],
                                })(
                                    <DictTreeSelect parentDisable={true} codeType={'dict_d85'} initValue={dataInfo['key'] || undefined} onChange={(e) => { this.setState({ d85: e }) }} />
                                )}

                            </FormItem>
                            <FormItem
                                label={formLabel('评议原因')}
                            >
                                {getFieldDecorator('reason', {
                                    initialValue: dataInfo['reason'],
                                    rules: [{ required: false, message: '评议原因' }],
                                })(
                                    <TextArea rows={2} />
                                )}
                            </FormItem>
                            <FormItem
                                label={formLabel('上级党组织意见')}
                            >
                                {getFieldDecorator('opinion', {
                                    initialValue: dataInfo['opinion'],
                                    rules: [{ required: false, message: '上级党组织意见' }],
                                })(
                                    <TextArea rows={2} />
                                )}
                            </FormItem>
                            {
                                ['E', 'F'].includes(this.state.d85) &&
                                <React.Fragment>
                                    <FormItem
                                        label={formLabel('处理情况')}
                                    >
                                        {getFieldDecorator('situation', {
                                            initialValue: dataInfo['situation'],
                                            rules: [{ required: true, message: '处理情况' }],
                                        })(
                                            <DictSelect codeType={'dict_d86'} initValue={dataInfo['situation'] || ''} />
                                        )}
                                    </FormItem>
                                    <FormItem
                                        label={formLabel('处理原因')}
                                    >
                                        {getFieldDecorator('handlingReasons', {
                                            initialValue: dataInfo['handlingReasons'],
                                            rules: [{ required: true, message: '处理原因' }],
                                        })(
                                            <DictSelect codeType={'dict_d87'} initValue={dataInfo['handlingReasons'] || ''} />
                                        )}
                                    </FormItem>
                                </React.Fragment>
                            }

                        </Form>
                    </Modal>
                }

            </React.Fragment>
        )
    }
}
export default Form.create<any>({})(addAppraisal);
