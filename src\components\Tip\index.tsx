/**
 * 模块名
 */
import {notification} from "antd";
import styles from './index.less'
const messageConfig=(type?:string,msg?:string,desc?:string,duration?:number = 1)=>{
  notification[type as string]({
    message: msg || '',
    description: desc || '',
    duration:duration,
    className:styles.tip
  });
};
const tip = {
  error(message, description,duration?) {
    messageConfig('error', message, description,duration);
  },
  success(message, description) {
    messageConfig('success', message, description);
  },
  warning(message, description) {
    messageConfig('warning', message, description);
  },
  info(message, description,duration?) {
    messageConfig('info', message, description,duration);
  },
};

export default tip;
