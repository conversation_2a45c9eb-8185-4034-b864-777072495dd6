import {basicModel} from '@/utils/common-model';
import modelExtend from "dva-model-extend";
import {getDictList,getArea,queryDictList,getAllArea} from '@/services';
import {jsonToTree,unique} from "@/utils/method";

const loginModel = modelExtend(basicModel,{
  namespace: "commonDict",
  state:{

  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, search } = location;
        // dispatch({
        //   type:'getDict',
        //   payload:{
        //     data:{codeType:'ZB79'}
        //   }
        // })
      });
    }
  },
  effects: {
    //查询list类型的字典
    *getDict({ payload }, { call, put,select }) {
      const {data}=payload;
      if(data){
        const {codeType,dicName}=data;
        let state =yield select(state=>state['commonDict']);
        if(!state[codeType || dicName]){
          const {data}=yield call(getDictList,{data:{dicName}});
          if(data){
          let newData=jsonToTree([...data],'parent','key','-1');
            yield put({
              type:'updateState',
              payload:{
                [codeType || dicName]:data,
                [`${codeType || dicName}_tree`]:newData,//DictTreeSelect RuiFilter 使用请勿删除
              }
            })
          }
        }
      }
    },
    //查询tree类型的字典
    *getDictTree({ payload }, { call, put,select }) {
      const {data}=payload;
      if(data){
        const {codeType,dicName}=data;
        let state =yield select(state=>state['commonDict']);
        if(!state[codeType || dicName]){
          const {data}=yield call(getDictList,payload);
          if(data){
            let newData=jsonToTree([...data],'parent','key','-1');
            yield put({
              type:'updateState',
              payload:{
                [codeType || dicName]:data,
                [`${codeType || dicName}_tree`]:newData,
              }
            })
          }
        }
      }
    },
    *getArea({ payload, }, { call, put,select }) {
      const {parent='-1'}=payload;
      const state =yield select(state=>state['commonDict']);
      const {area=[]}=state;
      let {data=[]}=yield call(getArea,payload);
      if(data){
        data=unique(data.concat(area),'key')
        let area_map=new Map();
        for(let obj of data){
          area_map.set(obj['key'],obj);
        }
        let newData=jsonToTree(JSON.parse(JSON.stringify(data)),'parent','key',parent);
        yield put({
          type:'updateState',
          payload:{
            area:data,
            area_tree:newData,
            area_map,
            areaParent:parent
          }
        })
      }
    },
    *getChildArea({ payload }, { call, put,select }) {
      const {parent}=payload;
      let state =yield select(state=>state['commonDict']);
      const {area,area_map,areaParent}=state;
      const obj=yield call(getArea,payload);
      const {data}=obj;
      if(data){
        for(let obj of data){
          if(!area_map.has(obj['key'])){
            area_map.set(obj['key'],obj);
            area.push(obj);
          }
        }
        let newData=jsonToTree(JSON.parse(JSON.stringify(area)),'parent','key',areaParent);
        yield put({
          type:'updateState',
          payload:{
            area:area,
            area_tree:newData,
            area_map,
          }
        });
        return {
          area:area,
          area_tree:newData,
          area_map,
        }
      }
    },
    *getAllChildArea({ payload }, { call, put,select }) {
      const {parent}=payload;
      let state =yield select(state=>state['commonDict']);
      const {area,area_map,areaParent}=state;
      const obj=yield call(getAllArea,{ parent:payload?.parent, keyWord:payload?.keyWord });
      const {data}=obj;
      if(data){
        for(let obj of data){
          if(!area_map.has(obj['key'])){
            area_map.set(obj['key'],{...obj, isGetAllChildren:1, is_leaf:obj?.leaf});
            area.push({...obj, isGetAllChildren:1, is_leaf:obj?.leaf});
          }
        }

        let findParent = area?.find?.(it=>it.key == parent);
        if(findParent){
          findParent.isGetAllChildren = 1;
          findParent.is_leaf = findParent.leaf;
        }

        let newData=jsonToTree(JSON.parse(JSON.stringify(area)),'parent','key',areaParent);
        yield put({
          type:'updateState',
          payload:{
            area:area,
            area_tree:newData,
            area_map,
          }
        });
        return {
          area:area,
          area_tree:newData,
          area_map,
        }
      }
    },
    // 年度统计字典查询接口
    *queryDict({ payload }, { call, put,select }) {
      const {typeCode}=payload;
      let state = yield select(state=>state['commonDict']);

      yield put({
        type:'updateState',
        payload:{
          codeType: state.codeType.concat([typeCode]),
        }
      });

      if(!state[typeCode] && !state.codeType.includes(typeCode)){
        const {data}=yield call(queryDictList,payload);
        if(data){
          yield put({
            type:'updateState',
            payload:{
              [typeCode]:data,
              codeType: state.codeType.concat([typeCode]),
            }
          });
        }
      }
    },
    *queryDictTree({ payload }, { call, put,select }) {
      const {typeCode}=payload;
      let state =yield select(state=>state['commonDict']);
      if(!state[typeCode]){
        const {data}=yield call(queryDictList,payload);
        if(data){
          let newData=jsonToTree([...data],'parent','key','-1');
          yield put({
            type:'updateState',
            payload:{
              [typeCode]:newData,
              [`${typeCode}List`]:data,
            }
          })
        }
      }
    },
  }
});
export default loginModel;
