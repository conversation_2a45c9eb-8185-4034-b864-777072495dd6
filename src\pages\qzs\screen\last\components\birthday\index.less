.birthday {
  :global {
    .ant-alert-icon {
      font-size: 30px;
    }
  }
}

.birthdayItem {
  width: 400px;
  height: 30px;
  line-height: 30px;
  display: inline-flex;
  font-size: 30px;
}
.searchBg {
  //   background: url('../../../../../../assets/qzs/bg1.webp') no-repeat;
  //   background-size: 100% 100%;
}

.searchMem {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 50px;
  .listItem {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #ffffff;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 3px 3px 10px #ccc;
    // cursor: pointer;
    .photo {
      > img {
        width: 73px;
        height: 93px;
      }
      margin-right: 16px;
    }
    .info {
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 18px;
      color: #62471e;

      .name {
        font-family: Source Han Serif SC;
        font-weight: 800;
        font-size: 20px;
        color: #be0c10;
        display: flex;
        align-items: center;
        > div {
          width: 79px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
          /* autoprefixer: on */
        }
        > span {
          text-align: center;
          width: 50px;
          padding: 2px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          display: inline-block;
          background: url('../../../../../../assets/qzs/icon1.png') no-repeat;
          background-size: 100% 100%;
        }
      }
      .desc {
        margin-top: 1px;
        font-size: 12px;
      }
    }
  }
  .pagination {
    margin-top: 10px;
    text-align: right;
    width: 100%;
  }
}
