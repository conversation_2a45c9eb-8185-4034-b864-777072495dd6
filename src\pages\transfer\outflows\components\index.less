.detailForm{
  :global{
    .ant-form-item{
      margin-bottom: 0;
    }
  }
}
.transfer{
  display: table;
  width: 100%;
  &>div{
    display: table-cell;
  }
  .header{
    width: 600px;
    margin: 16px auto;
    display: table;
    text-align: center;
    font-size: 16px;
    color: rgba(0,0,0,.9);
    white-space: nowrap;
    //position: relative;
    &>span{
      padding: 0 24px;
      font-size: 17px;
    }
    &::before{
      border-width: 2px;
      border-left: 2px solid #e8e8e8;
      border-top-left-radius: 5px;
      position: relative;
      top: 50%;
      display: table-cell;
      width: 50%;
      border-top: 2px solid #e8e8e8;
      -ms-transform: translateY(50%);
      transform: translateY(50%);
      content: "";
    }
    &::after{
      border-width: 2px;
      border-right: 2px solid #e8e8e8;
      border-top-right-radius: 5px;
      position: relative;
      top: 50%;
      display: table-cell;
      width: 50%;
      right: -2px;
      border-top: 2px solid #e8e8e8;
      -ms-transform: translateY(50%);
      transform: translateY(50%);
      content: "";
    }
  }

}
.tLine{
  min-height: 90px;
}
.action{
  display: inline-block;
  border: 1px dashed red;
  transform:rotate(9deg);
}
