import React ,{Fragment}from 'react';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Row, Col, Input, Radio, DatePicker, Switch, Alert, Button, InputNumber } from 'antd';
import styles from './index.less';
import Notice from 'src/components/Notice';
import OrgSelect from '@/components/OrgSelect';
import DictTreeSelect from 'src/components/DictTreeSelect';
import DictSelect from 'src/components/DictSelect';
import Tip from '@/components/Tip';
import {getIdCardInfo} from 'src/utils/method.js';
import {timeSort} from '@/utils/method.js';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _iisArray from 'lodash/isArray';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import { root, rootParent } from '@/common/config';
import { getSession } from '@/utils/session';
import { formLabel } from 'src/utils/method';
import { validateLength} from '@/utils/formValidator';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const TextArea = Input.TextArea;
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state={
      isDifficulty_state:false,
      _difficultyInfo:{}
    }
  }
  componentDidMount(): void {
    this.getDifficultyInfo();
  };
  static getDerivedStateFromProps = (nextProps:any, prevState:any) => {
    const state = [];
    const {memDifficulty:{difficultyInfo={}}={}} = nextProps;
    const {_difficultyInfo} = prevState;
    if(!_isEqual(difficultyInfo,_difficultyInfo)){
      state['_difficultyInfo'] = difficultyInfo;
      const {isDifficulty = 0,isDisability = 0, isSeriousDifficulty = 0, isDisease = 0, isLowIncome = 0, isSeriousDisease = 0, isEduCaused = 0 } = difficultyInfo;
      state['isDifficulty_state'] = isDifficulty === 1;
      state['isDisability_state'] = isDisability === 1;
      state['isSeriousDifficulty_state'] = isSeriousDifficulty === 1;
      state['isDisease_state'] = isDisease === 1;
      state['isLowIncome_state'] = isLowIncome === 1;
      state['isSeriousDisease_state'] = isSeriousDisease === 1;
      state['isEduCaused_state'] = isEduCaused === 1;
    }
    return state;
  };
  getDifficultyInfo=()=>{
    const {memBasic:{basicInfo:{code:memCode = ''} = {}}={}} = this.props;
    this.props.dispatch({
      type:'memDifficulty/getInfo',
      payload:{memCode}
    })
  };
  isDifficultyOnChange=(val)=>{
    this.setState({isDifficulty_state:val});
    if(!val){
      this.props.form.resetFields();
    }
  };
  switchOnChange=(e,type,type2?:any)=>{
    
    type2 && this.props.form.setFieldsValue({[`${type2}`]:""});
    this.setState({
      [`${type}_state`]:e,
    },()=>{
      !e && type2 && this[`${type2}`].clearAll();
    });
  };
  submit=()=>{
    const {memBasic:{basicInfo = {}}={},memDifficulty:{difficultyInfo={}}={},onEnd} = this.props;
    this.props.form.validateFieldsAndScroll(async(err, val) => {
      if (!err) {
        // 增加字典表的name
        ['d53','d54','d55','d56'].map(item=>{
          if(!_isEmpty(val[`${item}Code`])){
            if(_isEmpty(difficultyInfo)){
              val[`${item}Name`] = val[`${item}Code`]['name'];
              val[`${item}Code`] = val[`${item}Code`]['key'];
            }else {
              if(val[`${item}Code`] !== difficultyInfo[`${item}Code`]){
                val[`${item}Name`] = val[`${item}Code`]['name'];
                val[`${item}Code`] = val[`${item}Code`]['key'];
              }else {
                val[`${item}Name`] = difficultyInfo[`${item}Name`];
              }
            }
          }
        });
        //bool改为int
        ['isDifficulty','isDisability','isSeriousDifficulty','isDisease','isLowIncome','isSeriousDisease','isEduCaused'].map(item=>{
          val[`${item}`] = val[`${item}`] ? 1 : 0;
        });
        const {code,name,sexCode,memOrgCode,d08Name,d08Code,orgCode,orgName} = basicInfo;
        // console.log(basicInfo,'basicInfo')
        const res = await this.props.dispatch({
          type:'memDifficulty/save',
          payload:{
            data:{
              ...val,
              memCode:code,memName:name,
              sexCode,sexName:sexCode === '1' ? '男' : '女',
              d08Name,d08Code,orgCode,orgName,diffOrgCode:memOrgCode
            },
            type:_isEmpty(difficultyInfo) ? 'add' : 'edit'
          }
        });
        const {code:resCode = 500, data = {}} = res;
        if(resCode === 0){
          Tip.success('操作提示','操作成功');
          onEnd && onEnd();
          // this.props.dispatch({
          //   type:'memDifficulty/updateState',
          //   payload:{difficultyInfo:data}
          // })
        }
      }
    });
  };
  componentDidUnmount(): void {
    this.props.dispatch({
      type:'memDifficulty/clear',
      payload:{}
    })
  }

  render(): React.ReactNode {
    const {form,memDifficulty:{difficultyInfo = {}}={},loading:{effects = {}} = {},tipMsg={}} = this.props;
    const { getFieldDecorator } = form;
    const {
      isDifficulty_state,
      isDisability_state,
      isSeriousDifficulty_state,
      isDisease_state,
      isLowIncome_state,
      isSeriousDisease_state,
      isEduCaused_state
    } = this.state;
    console.log(this.props,'sssssssssss');
    return (
      <Fragment>
        <Row>
          {getFieldDecorator('code', { initialValue: difficultyInfo['code'] })(
            <div style={{display:'none'}}>123</div>
          )}
          <Col span={24}>
            <FormItem
              label={ formLabel('是否困难党员',tipMsg['isDifficulty']) }
              {...formItemLayout1}
            >
              {getFieldDecorator('isDifficulty', {
                rules: [{ required: true, message: '是否困难党员' }],
                initialValue: isDifficulty_state,
              })(
                <Switch checkedChildren="是" unCheckedChildren="否" checked={isDifficulty_state} onChange={this.isDifficultyOnChange}/>
              )}
            </FormItem>
          </Col>
          {
            isDifficulty_state &&
              <Fragment>
                <Col span={24}>
                  <FormItem
                    label={ formLabel('生活困难类型',tipMsg['d53Code']) }
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('d53Code', {
                      rules: [{ required: true, message: '生活困难类型' }],
                      initialValue: _isEmpty(difficultyInfo)? undefined : difficultyInfo['d53Code'],
                    })(
                      <DictSelect codeType={'dict_d53'} ref={e=> this['d53Code'] = e} initValue={_isEmpty(difficultyInfo)?undefined:difficultyInfo['d53Code']} backType={'object'}/>
                    )}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label={ formLabel('是否残疾',tipMsg['isDisability']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('isDisability', {
                      rules: [{ required: false, message: '是否残疾' }],
                      initialValue: isDisability_state,
                    })(
                      <Switch
                        checkedChildren="是"
                        unCheckedChildren="否"
                        checked={isDisability_state}
                        onChange={(e)=>this.switchOnChange(e,'isDisability','d54Code')}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    key={ isDisability_state ? 0 : 1 }
                    label={ formLabel('残疾类型',tipMsg['d54Code']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('d54Code', {
                      rules: [{ required: !!isDisability_state, message: '残疾类型' }],
                      initialValue: _isEmpty(difficultyInfo)? undefined : difficultyInfo['d54Code'],
                    })(
                      <DictSelect
                        codeType={'dict_d54'} backType={'object'}
                        initValue={_isEmpty(difficultyInfo)?undefined:difficultyInfo['d54Code']}
                        disabled={!isDisability_state}
                        ref={e=>this['d54Code'] = e}
                      />
                    )}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label={ formLabel('是否特困',tipMsg['isSeriousDifficulty']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('isSeriousDifficulty', {
                      rules: [{ required: false, message: '是否困难党员' }],
                      initialValue: isSeriousDifficulty_state,
                    })(
                      <Switch
                        checkedChildren="是"
                        unCheckedChildren="否"
                        checked={isSeriousDifficulty_state}
                        onChange={(e)=>this.switchOnChange(e,'isSeriousDifficulty','d55Code')}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    key={isSeriousDifficulty_state ? 2 : 3}
                    label={ formLabel('残疾级别',tipMsg['d55Code']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('d55Code', {
                      rules: [{ required: !!isSeriousDifficulty_state, message: '残疾级别' }],
                      initialValue: _isEmpty(difficultyInfo)? undefined : difficultyInfo['d55Code'],
                    })(
                      <DictSelect
                        codeType={'dict_d55'}
                        ref={e=>this['d55Code'] = e}
                        initValue={_isEmpty(difficultyInfo)?undefined:difficultyInfo['d55Code'] }
                        backType={'object'} disabled={!isSeriousDifficulty_state}
                      />
                    )}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label={ formLabel('是否疾病',tipMsg['isDisease']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('isDisease', {
                      rules: [{ required: false, message: '是否疾病' }],
                      initialValue: isDisease_state,
                    })(
                      <Switch
                        checkedChildren="是"
                        unCheckedChildren="否"
                        checked={isDisease_state}
                        onChange={(e)=>this.switchOnChange(e,'isDisease','d56Code')}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    key={isDisease_state ? 4 : 5}
                    label={ formLabel('疾病类型',tipMsg['d56Code']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('d56Code', {
                      rules: [{ required: !!isDisease_state, message: '疾病类型' }],
                      initialValue: _isEmpty(difficultyInfo)? undefined : difficultyInfo['d56Code'],
                    })(
                      <DictSelect
                        codeType={'dict_d56'}
                        ref={e=> this['d56Code'] = e}
                        initValue={_isEmpty(difficultyInfo)?undefined:difficultyInfo['d56Code']}
                        backType={'object'} disabled={!isDisease_state}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('是否低保',tipMsg['isLowIncome']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('isLowIncome', {
                      rules: [{ required: false, message: '是否低保' }],
                      initialValue: isLowIncome_state,
                    })(
                      <Switch
                        checkedChildren="是"
                        unCheckedChildren="否"
                        checked={isLowIncome_state}
                        onChange={(e)=>this.switchOnChange(e,'isLowIncome')}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('是否重病灾害',tipMsg['isSeriousDisease']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('isSeriousDisease', {
                      rules: [{ required: false, message: '是否重病灾害' }],
                      initialValue: isSeriousDisease_state,
                    })(
                      <Switch
                        checkedChildren="是"
                        unCheckedChildren="否"
                        checked={isSeriousDisease_state}
                        onChange={(e)=>this.switchOnChange(e,'isSeriousDisease')}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('是否子女读书致贫',tipMsg['isEduCaused']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('isEduCaused', {
                      rules: [{ required: false, message: '是否子女读书致贫' }],
                      initialValue: isEduCaused_state,
                    })(
                      <Switch
                        checkedChildren="是"
                        unCheckedChildren="否"
                        checked={isEduCaused_state}
                        onChange={(e)=>this.switchOnChange(e,'isEduCaused')}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('养老金额',tipMsg['pensionAmount']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('pensionAmount', {
                      rules: [{ required: false, message: '请填写养老金额' }],
                      initialValue: _isEmpty(difficultyInfo)? 0 : difficultyInfo['pensionAmount'],
                    })(
                      <InputNumber min={0} placeholder={'请填写养老金额'} style={{width:'100%'}}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('核查单位',tipMsg['checkUnit']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('checkUnit', {
                      rules: [{ required: false, message: '请填写核查单位' }],
                      initialValue: _isEmpty(difficultyInfo)? undefined : difficultyInfo['checkUnit'],
                    })(
                      <Input placeholder={'请填写核查单位'}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('家庭年收入（元）',tipMsg['yearIncome']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('yearIncome', {
                      rules: [{ required: false, message: '请填写家庭年收入（元）' }],
                      initialValue: _isEmpty(difficultyInfo)? 0 : difficultyInfo['yearIncome'],
                    })(
                      <InputNumber min={0} placeholder={'请填写家庭年收入（元）'} style={{width:'100%'}}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={24}>
                  <FormItem
                    label={ formLabel('困难原因',tipMsg['difficultyReason']) }
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('difficultyReason', {
                      rules: [{ required: false, message: '困难原因' }, {validator: (...e)=>validateLength(e, 300, 900)}],
                      initialValue: _isEmpty(difficultyInfo)? undefined : difficultyInfo['difficultyReason'],
                    })(
                      <TextArea placeholder={'请填写困难原因'} rows={3}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={24}>
                  <FormItem
                    label={ formLabel('备注',tipMsg['difficultyRemark']) }
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('difficultyRemark', {
                      rules: [{ required: false, message: '请填写备注' }, {validator: (...e)=>validateLength(e, 300, 900)}],
                      initialValue: _isEmpty(difficultyInfo)? undefined : difficultyInfo['difficultyRemark'],
                    })(
                      <TextArea placeholder={'请填写备注'} rows={3}/>
                    )}
                  </FormItem>
                </Col>
              </Fragment>
          }
        </Row>
        <div className={styles.btns}>
          <Button htmlType={'button'} type={'primary'} onClick={this.submit} loading={effects['memDifficulty/save']}><LegacyIcon type={'save'}/>保存</Button>
        </div>
      </Fragment>
    );
  }
}
export default Form.create()(index);
