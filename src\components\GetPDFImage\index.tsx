import React, { useState } from 'react';
import { extractImagesFromPDF } from '@/utils/extractImagesFromPDF';

const PDFImageExtractor = () => {
  const [images, setImages] = useState([]);

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      const extractedImages = await extractImagesFromPDF(file);
      setImages(extractedImages);
    }
  };

  return (
    <div>
      <input type="file" accept="application/pdf" onChange={handleFileUpload} />
      <div>
        {images.map((img, index) => (
          <div key={index}>
            <p>Page: {img.page}</p>
            <img
              src={`data:image/png;base64,${btoa(
                String.fromCharCode(...new Uint8Array(img.imageBytes))
              )}`}
              alt={`Page ${img.page}`}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default PDFImageExtractor;