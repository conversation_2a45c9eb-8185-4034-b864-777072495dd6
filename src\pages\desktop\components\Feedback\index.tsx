import React from 'react';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Card, Avatar } from 'antd';
import moment from 'moment';
import style from './index.less';
interface Interface {
  info: object;
  onClick?:Function
}
export default class index extends React.Component<Interface, any> {
  constructor(props) {
    super(props);
  }
  render() {
    const { info,onClick } = this.props;
    return (
      <div>
          <Card className={style.daibanCard} style={{ cursor: onClick ? 'pointer' : ''}} onClick={()=>{
          onClick && onClick(info)
          }}>
          {/* <div className={style.avator}></div> */}
          <div className={style.infos}>
            {info['questionsBriefly']}
            <div className={style.desc}>
              更新时间：{moment(info['updateTime']).format('YYYY-MM-DD')}
            </div>
          </div>
        </Card>
      </div>
    );
  }
}
