import React, { useState, useEffect } from 'react';
import { TreeSelect } from 'antd';
import config from './config';
import { jsonToTree, treeToList } from '@/utils/method';
const TreeNode = TreeSelect.TreeNode;

export { config };
const index = (props: any) => {
  const { dropdownStyle, commonDict, notIncludeGZ, disabled, allowClear = true } = props;
  const [actionKey, setActionKey] = useState<any>();
  const [value, setValue] = useState<any>();
  const [treeData, setTreeData] = useState<any>([]);
  const [hasInitTreeData, setHasInitTreeData] = useState<any>(false);

  const loadData = async (node) => {
    const { dataRef } = node.props;
    if (dataRef.children && dataRef.children.length > 0) {
      return new Promise<void>((resolve) => {
        return resolve();
      });
    }
    const data = await props.config.getTrees([dataRef['key']], treeData);
    setTreeData(data);
    return data;
  };

  const onChange = (value) => {
    setValue(value);
    const list = treeToList(treeData);
    let find = list.find((it) => it.key == value);
    props?.onChange?.(value, find);
  };

  const onTreeExpand = (keys) => {
    setActionKey(keys);
  };

  const filterTreeNode = (val, node) => {
    const { searchKey } = props;
    const { dataRef } = node.props;
    let resData = [dataRef['key'].includes(val)];
    for (let obj of searchKey as Array<string>) {
      resData.push(dataRef[obj].includes(val));
    }
    return resData.includes(true);
  };

  const renderTreeNodes = (data) => {
    return data.map((item) => {
      if (item.children) {
        return (
          <TreeNode
            title={`${item['name' || '']}`}
            key={item['key' || '']}
            value={item['key' || '']}
            dataRef={item}
            isLeaf={!!item[props.config.isLeaf]}
            disabled={!item[props.config.isLeaf]}
          >
            {renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          title={`${item['name' || '']}`}
          key={item['key' || '']}
          value={item['key' || '']}
          dataRef={item}
          isLeaf={!!item[props.config.isLeaf]}
          disabled={!item[props.config.isLeaf]}
        />
      );
    });
  };

  const getIndexData = async () => {
    const res = await props.config.getTrees([]);
    setTreeData(res);
    setHasInitTreeData(true);
  };

  const setInitValueAndGetTree = () => {
    let code = props.init;
    setValue(props.init);
    let arr = props.config.getParentCodes(code);
    onTreeExpand([...arr, treeData?.[0]?.key]);
    arr.map(async (it) => {
      const data = await props.config.getTrees([it], treeData);
      setTreeData(data);
    });
  };

  useEffect(() => {
    getIndexData();
  }, []);

  useEffect(() => {
    if (hasInitTreeData) {
      setInitValueAndGetTree();
    }
  }, [props.init, hasInitTreeData]);

  return (
    <TreeSelect
      showSearch
      treeLine
      allowClear={allowClear}
      treeExpandedKeys={actionKey}
      disabled={disabled}
      loadData={loadData}
      onChange={onChange}
      onTreeExpand={onTreeExpand}
      filterTreeNode={filterTreeNode}
      placeholder={'请选择'}
      style={{ width: '100%' }}
      dropdownStyle={dropdownStyle ? dropdownStyle : { maxHeight: 400, overflow: 'auto' }}
      value={value}
    >
      {treeData.length > 0 && renderTreeNodes(treeData)}
    </TreeSelect>
  );
};

export default index;
