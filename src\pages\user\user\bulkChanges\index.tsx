/*
* 用户管理-批量修改
* */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Modal,
  Input,
  Radio,
  Checkbox,
  Row,
  Col,
  Select,
  Tooltip,
  message,
  TreeSelect,
} from 'antd';
import { FormComponentProps } from '@ant-design/compatible/lib/form';
import ListTable from '@/components/ListTable';
import {connect} from 'dva';
import {isEmpty,jsonToTree} from '@/utils/method.js';
const RadioGroup = Radio.Group;
const Option = Select.Option;
const CheckboxGroup = Checkbox.Group;
const TreeNode = TreeSelect.TreeNode;
import styles from './index.less'
import UnitTree from '../unitTree'
import { log } from 'util';
import { getSession } from '@/utils/session';
import Notice from '@/components/Tip';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

interface propsType extends FormComponentProps{
  title?:string,
  handleOk?:()=>void,
}
@connect(({user})=>({
  user
}))
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      pwVsb:true,//修改密码是否禁用
      level:true,//管理层次是否禁用
      condition:true,//状态是否禁用
      nameVsb:true,
      orgVsb:true,
      selectedRowKeys:[],
      getList:true,
      isRead:true,
      orgCode:'',
      destroy:true
    }
  }
//JSON.parse(sessionStorage.getItem('org')||'')['code']||
  showModal=()=>{
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
      destroy:false,
      pwVsb:true,
      level:true,
      condition:true,
      nameVsb:true,
      orgVsb:true,
      getList:true,
      isRead:true,
      orgCode:org['orgCode'],
    },()=>{
      this.getList();
        this.onChange();
    });
  };
  getList = () => {
    this.props.dispatch({
      type:'user/getListing',
      payload:{
      }
    });
  };

  upList=()=>{
    const { orgCode='' }=this.props;
    this.props.dispatch({
      type:'user/list',
      payload:{
        data:{
          pageNumber:1,
          pageSize:10,
          code:orgCode
        }
      }
    })
  };
  upSearch=( pageNum=1,size=10,v)=>{
    this.props.dispatch({
      type:'user/getSearchUserByKeyword',
      payload:{
        pageNum:pageNum,
        pageSize:size,
        orgCode:this.state.orgCode,
        keyword:v
      }
    })
  };

  handleOk=(isEdit)=>{
    this.props.form.validateFieldsAndScroll(async(errors, values) => {
      if (errors) {
        return;
      }
      const { selectedRowInfos,readOnly,selectedRowKeys } = this.state;
      const { data=[] } =this.props.user;
      let name='';
      data.map((item,index)=>{
        if (values.sysLevel===item.CODE_VALUE){
          name=item.CODE_NAME
        }
        return name
      });
      const { read,roleId,...valueInfo} = values;
      if (!isEmpty(selectedRowKeys)){
        let RowInfoId =selectedRowInfos.map((item,index)=>{
          let ovj={
            id:item.id,
            readOnly:readOnly,
            ...valueInfo,
            password:!_isEmpty(valueInfo.password) ? valueInfo.password : undefined,
            manages: [
              {
                roleId:!isEmpty(roleId) ? roleId : _get(item,'manages[0].roleId',undefined),
                managerOrgCode:_get(item,'manages[0].managerOrgCode',undefined),
                managerOrgId:_get(item,'manages[0].managerOrgId',undefined),
                isDefault:_get(item,'manages[0].isDefault',undefined),
              }
            ]
          };
          return ovj
        });
        for (let a in RowInfoId){
          if (RowInfoId[a]===''||RowInfoId[a]===undefined){
            delete RowInfoId[a];
          }
        }
        this.props.dispatch({
          type:'user/getBatchEdit',
          payload:{
            data:[  ...RowInfoId]
          }
        }).then(res=>{
          if (res.code===0){
            if (isEmpty(this.state.keyword)){
              this.upList();
            } else {
              this.upSearch(1,10,this.state.keyword);
            }
            Notice.info("操作提示",'保存成功');
            this.handleCancel();
          }
        });
      } else {
        message.info('请选择需要修改的人');
      }
    });

  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      selectedRowKeys:[],
      destroy:true,
      selectedRowInfos:[]
    });

    this.props.form.resetFields()
  };
  //表单验证----------------------------------
  validFunction = (rule, value, callback) => {
    if (value){
      switch (rule.field) {
        case 'account':
          if (value.length>10){
            return callback('用户名长度不能超过10个字')
          }
          break;
        case 'name':
          if (value.length>5){
            return callback('姓名不能超过5个字')
          }
          break;
        case 'password':
          if (!(/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$/).test(value)){
            return callback('需以字母开头，且含有大写字母，小写字母，数字，特殊符号中的三种及以上，长度为8-16位')
          } else if ((/\s+/g).test(value)){
            return callback('密码不能包含空格')
          }
          break;
      }
    }
    callback()
  };
  onChange = (page=1,size=10) => {
    this.props.dispatch({
      type:'user/listSelect',
      payload:{
        data:{
          pageNumber:page,
          pageSize:size,
          code:this.state.orgCode
        }
      }
    })
  };
  isTrue = (e) => {
    this.setState({pwVsb:!e.target.checked})
  };
  isGetList = (k) => {
    this.setState({getList:!k})
  };
  isCondition = (k) => {
    this.setState({condition:!k})
  };
  isReadOnly = (k) => {
    this.setState({isRead:!k})
  };
  onSelectChange = (selectedRowKeys,infos) => {
    let { selectedRowInfos=[],arrInfos=[] }=this.state;
    // let result:any = [];
    // for(let i = 0; i < arrInfos.length; i++){
    //   let obj = arrInfos[i];
    //   let num = obj.id;
    //   let flag = false;
    //   for(let j = 0; j < infos.length; j++){
    //     let aj = infos[j];
    //     let n = aj.id;
    //     if(n == num){
    //       flag = true;
    //       break;
    //     }
    //   }
    //   if(!flag){
    //     result.push(obj);
    //   }
    // }
    // if (infos.length >= arrInfos.length) {
    //   infos.forEach(i => {
    //     if (!selectedRowInfos.map(i => i.id).includes(i.id)) {
    //       selectedRowInfos.push(i);
    //     }
    //   });
    // }else {
    //   result.forEach(i=>{
    //     selectedRowInfos=selectedRowInfos.filter(it=>it.id!==i['id']);
    //   });
    // }
       infos.forEach(i => {
        if (!selectedRowInfos.map(i => i.id).includes(i.id)) {
           selectedRowInfos.push(i);
         }
      });
    if (selectedRowKeys.length < selectedRowInfos.length) {
      selectedRowInfos=selectedRowInfos.filter((it)=> selectedRowKeys.includes(it.id));
    }
    this.setState({ selectedRowKeys,selectedRowInfos,arrInfos:infos});
  };
  onChangeReadOnly = (e) => {
    this.setState({readOnly:e.target.value})
  };
  changePage=(v,k)=>{
    this.onChange(v,k);
    this.setState({arrInfos:[]})
  };
  renderTreeNodes = data => data.map((item) => {
    if (item.children) {
      return (
        <TreeNode title={item['name']} key={item['id']} value={item['id']} dataRef={item}>
          {this.renderTreeNodes(item.children)}
        </TreeNode>
      );
    }
    return <TreeNode title={item['name']} key={item['id']} value={item['id']} dataRef={item} />;
  });
  roleOnchange=(v)=>{
    this.setState({roleVal:v})
  };
  render(): React.ReactNode {
    let {children,user:{ pagination:{ }={},list1=[],pagination1:{pageSize=0,totalRow=0,page=1,pageNumber=1}={},getList=[] }={}}=this.props;
    let a=jsonToTree(getList,'parent_id','id',"8d089ede5a044ce4ae8b4102fdc10bcc");
    let aa:any=[];
    list1.map((item,index)=>{
        if (item['manages'][0]['isDefault']!==1){
          aa.push(item)
        }
    });
    const { data=[] } = this.props.user;
    const { getFieldDecorator } = this.props.form;
    const {selectedRowKeys,selectedRowInfos,orgName} = this.state;
    let selectedRowInfo=[];
    if (isEmpty(selectedRowInfos)){
      selectedRowInfo=[]
    } else {
      selectedRowInfo=selectedRowInfos.map((item,index)=>{
        if (index===selectedRowInfos.length-1){
          return item.account
        } else {
          return item.account+','
        }
      })
   }
    const columns=[
      {
        title:'姓名',
        dataIndex:'memName',
        width:100,
      },
      {
        title:'登录名',
        dataIndex:'account',
        width:50,
      },
      {
        title:'状态',
        dataIndex:'isLock',
        width:50,
        render:text => {
         return <span>{text===0?'正常':'锁定'}</span>
        }}
      ,
      {
        title:'管理单位',
        dataIndex:'manages',
        width:200,
        render:(text,r, index)=>{
          return (
            <span key={index}>
              {
                 text.map((item,i)=>{
                   if(item.isDefault===1){
                     return  <span key={index}>{item.managerOrgName}</span>
                   }
                 })
              }
            </span>
          )
        }
      },
      // {
      //   title:'管理角色',
      //   dataIndex:'manages',
      //   render:(text,r, index) => {
      //     return (
      //       <span key={(index+1)*new Date().valueOf()}>
      //         {
      //           text.map((item,i)=>{
      //             if(item.isDefault===1){
      //               return  <span key={(index+1)*new Date().valueOf()}>{item.roleName}</span>
      //             }
      //           })
      //         }
      //       </span>
      //     )
      //   }
      // },
    ];
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
    };
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        {
          !this.state.destroy &&
          <Modal
            title='批量修改'
            visible={this.state.visible}
            onOk={this.handleOk}
            onCancel={this.handleCancel}
            width={'70%'}
            maskClosable={false}
          >
            <div>
              <Row>
                <Col span={24} style={{padding:5}}>
                    <span>已选中:{isEmpty(selectedRowInfo)?'暂无':selectedRowInfo}</span>
                </Col>
                <Col span={15}>
                  <ListTable
                    rowKey={record=>record.id}
                    columns={columns}
                    data={list1}
                    scroll={{ y: 500 }}
                    rowSelection={rowSelection}
                    pagination={{pageSize,total:totalRow,page,current:pageNumber}}
                    onPageChange={this.changePage}/>
                </Col>
                <Col span={9} className='col_bulk' style={{paddingLeft:'20px'}}>
                  <Form {...formItemLayout}>
                    <FormItem
                      label={<Checkbox onChange={(e)=>this.isTrue(e)}>重置密码</Checkbox>}
                    >
                      {getFieldDecorator('password', {
                        // initialValue:isEmpty(dataInfo)?null:dataInfo.password,
                        rules: [{ validator: this.validFunction }],

                      })(
                        <Input placeholder="请输入密码" disabled={this.state.pwVsb}/>
                      )}
                    </FormItem>
                    <FormItem
                      label={<Checkbox onChange={()=>this.isGetList(this.state.getList)}>系统角色</Checkbox>}
                    >
                      {getFieldDecorator('roleId', {
                        // initialValue:isEmpty(dataInfo)?null:dataInfo.password,
                        rules: [
                          { validator: this.validFunction }],})
                      (
                        <TreeSelect
                          disabled={this.state.getList}
                        value={this.state['roleVal']}
                        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                        placeholder="请选择角色"
                        onChange={this.roleOnchange}
                        >
                      {
                        a && this.renderTreeNodes(a)
                      }
                        </TreeSelect>
                      )}
                    </FormItem>
                    <FormItem
                      label={<Checkbox onChange={()=>this.isCondition(this.state.condition)}>状&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;态</Checkbox>}
                    >
                      {getFieldDecorator('isLock', {
                        // initialValue:isEmpty(dataInfo)?null:dataInfo.roleId,
                        // rules: [{ required: true, message: '必填!' }],
                      })(
                        <Select disabled={this.state.condition}>
                          <Option value={0}>正常</Option>
                          <Option value={1}>锁定</Option>
                        </Select>
                      )}
                    </FormItem>
                    <FormItem
                      label={<Checkbox onChange={()=>this.isReadOnly(this.state.isRead)}>只&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;读</Checkbox>
                      }
                    >
                      {getFieldDecorator('read', {
                        initialValue: true
                        // rules: [{ required: true, message: '请输入!' }],e
                      })(
                        <React.Fragment>
                          <RadioGroup onChange={this.onChangeReadOnly} disabled={this.state.isRead}>
                            <Radio value={1}>是</Radio>
                            <Radio value={0}>否</Radio>
                          </RadioGroup>
                          <span>（打√表示只能查看内容，无编辑权限）</span>
                        </React.Fragment>
                      )}
                    </FormItem>
                  </Form>
                </Col>
              </Row>
            </div>
          </Modal>
        }
      </React.Fragment>
    );
  }
}
export default Form.create()(index);
