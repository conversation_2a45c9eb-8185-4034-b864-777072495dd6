import request from 'src/utils/request';
import qs from 'qs';

export function add(params) {
  return request('/api/fee/saveFee',{
    method:'POST',
    body:params
  });
}
export function importExcelStand(params) {
  return request('/api/dues/importExcelStand',{
    method:'POST',
    body:params
  });
}
export function importExcelDude(params) {
  return request('/api/dues/importExcelDude',{
    method:'POST',
    body:params
  });
}
export function templateDues(params) {
  return request('/api/dues/templateDues',{
    method:'POST',
    body:params
  });
}
export function templateStand(params) {
  return request('/api/dues/templateStand',{
    method:'POST',
    body:params
  });
}
//列表
export function getList(params) {
  return request('/api/fee/getList',{
    method:'POST',
    body:params
  });
}

export function updateLastPayDate(params) {
  return request('/api/mem/updateLastPayDate',{
    method:'POST',
    body:params
  });
}
export function getPayList(params) {
  return request('/api/fee/order/getList',{
    method:'POST',
    body:params
  });
}

export function getOnePayList(params) {
  return request('/api/fee/getPayList',{
    method:'POST',
    body:params
  });
}

export function getWxpayQRCode(params) {
  return request('/api/fee/order/getWxpayQRCode',{
    method:'POST',
    body:params
  });
}

export function wxPayOrderQuery(params) {
  return request(`/api/fee/order/wxPayOrderQuery?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function getCountList(params) {
  return request('/api/fee/getCountList',{
    method:'POST',
    body:params
  });
}
export function getPayTotalListt(params) {
  return request('/api/fee/order/getPayTotalList',{
    method:'POST',
    body:params
  });
}
export function getListzc(params) {
  return request('/api/fee/disburse/getList',{
    method:'POST',
    body:params
  });
}
export function saveFeeDisburse(params) {
  return request('/api/fee/disburse/saveFeeDisburse',{
    method:'POST',
    body:params
  });
}
export function updateFeeDisburse(params) {
  return request('/api/fee/disburse/updateFeeDisburse',{
    method:'POST',
    body:params
  });
}
export function delFeeDisburse(params) {
  return request(`/api/fee/disburse/delFeeDisburse?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function getWxBillList(params) {
  return request('/api/fee/disburse/getWxBillList',{
    method:'POST',
    body:params
  });
}
export function saveByWxBill(params) {
  return request('/api/fee/disburse/saveByWxBill',{
    method:'POST',
    body:params
  });
}
export function getListxb(params) {
  return request('/api/fee/allocate/getList',{
    method:'POST',
    body:params
  });
}
export function saveFeeAllocate(params) {
  return request('/api/fee/allocate/saveFeeAllocate',{
    method:'POST',
    body:params
  });
}

// 党费交纳-基本信息-列表
export function paymentList(params) {
  return request('/api/dues/list',{
    method:'POST',
    body:params
  });
}
// export function paymentList(params) {
//   return request(`/api/dues/list?${qs.stringify(params)}`,{
//     method:'GET',
//   });
// }
// 设置党费交纳起缴时间（含修改）
export function settingDate(params) {
  return request('/api/dues/settingDate',{
    method:'POST',
    body:params
  });
}
// 设置党费标准（单个设置）
export function settingStandard(params) {
  return request('/api/dues/settingStandard',{
    method:'POST',
    body:params
  });
}
// 设置党费标准（批量设置）
export function batchStandard(params) {
  return request('/api/dues/batchStandard',{
    method:'POST',
    body:params
  });
}
// 批量设置党费标准列表
export function listStandard(params) {
  return request('/api/dues/listStandard',{
    method:'POST',
    body:params
  });
}
// 党费交纳（单个交纳）
export function payDues(params) {
  console.log('params===',params);
  return request('/api/dues/payDues',{
    method:'POST',
    body:params
  });
}
// 党费交纳（批量）
export function batchPayment(params) {
  return request('/api/dues/batchPayment',{
    method:'POST',
    body:params
  });
}
// 批量交纳党费列表
export function listPayment(params) {
  return request('/api/dues/listPayment',{
    method:'POST',
    body:params
  });
}
// 统计信息（党费交纳）
export function statisticsPayment(params) {
  return request('/api/dues/statisticsPayment',{
    method:'POST',
    body:params
  });
}
// 历史党员党费交纳列表
export function historyPayment(params) {
  return request('/api/dues/historyPayment',{
    method:'POST',
    body:params
  });
}