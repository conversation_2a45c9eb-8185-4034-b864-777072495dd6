import React, {Fragment} from 'react';
import {Tabs} from 'antd';
import _isEmpty from 'lodash/isEmpty'
import History from './components/basiclist';

const TabPane = Tabs.TabPane;
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);

  }
  onChange =(val)=>{
    // console.log(val)
  };
  render(): React.ReactNode {
    const memTabs = [
      {key:'1',title:'基本信息',component: <History {...this.props}/>}
    ];
    return (
      <div style={{height:'100%', overflow:'hidden'}}>
        <Tabs defaultActiveKey="1" onChange={this.onChange}>
          {
            !_isEmpty(memTabs) && memTabs.map(item=> <TabPane tab={item['title']} key={item['key']}/>)
          }
        </Tabs>
        <History {...this.props}/>
      </div>
    )
  }
}
