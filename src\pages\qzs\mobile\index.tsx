import React, { useEffect, useRef, useState } from 'react';
import style from './index.less';
import { Form, Input, Tabs, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { findByCode, screenlist } from '../screen/services';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import { _history as router } from '@/utils/method';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 0 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
};
const index = () => {
  const [form] = Form.useForm();

  const [tabKey, setTabKey] = useState<any>('1');

  const input1Ref: any = useRef();
  const input2Ref: any = useRef();
  const input3Ref: any = useRef();

  const inp1_onkeyup = async (e) => {
    let text = e.target.value;
    if (text.length === 3) {
      input2Ref?.current?.focus?.();
    }
  };
  const inp2_onkeyup = async (e) => {
    let text = e.target.value;
    if (text.length === 8) {
      input3Ref?.current?.focus?.();
    }
    if (text.length === 0) {
      input1Ref?.current?.focus?.();
    }
  };
  const inp3_onkeyup = async (e) => {
    let text = e.target.value;
    if (text.length === 0) {
      input2Ref?.current?.focus?.();
    }
  };

  const getList = async (p = {}) => {
    const {
      code: mainResCode = 500,
      data: { list: mainsList = [], ...others } = {},
    } = await screenlist({
      data: {
        pageNum: 1,
        pageSize: 10,
        ...p,
      },
    });
    if (mainResCode == 0) {
      if (mainsList.length == 0) {
        Tip.error('操作提示', '没有找到党员信息');
      }
      if (mainsList.length == 1) {
        sessionStorage.setItem('sddj_mb', JSON.stringify(mainsList[0]));
        router.push('/qzs/mobile/sddj');
      } else {
        router.push(`/qzs/mobile/list?name=${p.name || ''}&idcard=${p.idCard || ''}&zzm=${p.idcard1 || ''}-${
          p.joinOrgDate || ''
        }-${p.lastIdcard || ''}`);
      }
    }
  };

  const hadndleFinish = async (v) => {
    getList(v);
  };

  return (
    <div className={style.bg}>
      <div className={style.box}>
        <img className={style.logo} src={require('../../../assets/qzs/sddj.png')} />
        <Tabs
          activeKey={tabKey}
          onChange={(v) => {
            setTabKey(v);
            form.resetFields();
          }}
        >
          <Tabs.TabPane tab={`通过姓名身份证`} key={'1'}></Tabs.TabPane>
          <Tabs.TabPane tab={`通过政治生日码`} key={'2'}></Tabs.TabPane>
        </Tabs>
        <div className={style.form}>
          <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
            {tabKey == '1' && (
              <React.Fragment>
                <Form.Item name={'name'} rules={[{ required: false, message: '必填' }]}>
                  <Input placeholder="请输入姓名"></Input>
                </Form.Item>
                <Form.Item name={'idCard'} rules={[{ required: false, message: '必填' }]}>
                  <Input placeholder="请输入身份证号"></Input>
                </Form.Item>
              </React.Fragment>
            )}
            {tabKey == '2' && (
              <div className={style.zzsrm}>
                <div className={style.zzsrmItem}>
                  <Form.Item
                    name={'idcard1'}
                    initialValue={'GQZ'}
                    rules={[{ required: false, message: '必填' }]}
                  >
                    <Input
                      placeholder=""
                      ref={input1Ref}
                      maxLength={3}
                      onKeyUp={inp1_onkeyup}
                    ></Input>
                  </Form.Item>
                </div>
                <div className={style.zzsrmItemLine}>-</div>
                <div className={style.zzsrmItem}>
                  <Form.Item name={'joinOrgDate'} rules={[{ required: false, message: '必填' }]}>
                    <Input
                      placeholder=""
                      ref={input2Ref}
                      maxLength={8}
                      onKeyUp={inp2_onkeyup}
                    ></Input>
                  </Form.Item>
                </div>
                <div className={style.zzsrmItemLine}>-</div>
                <div className={style.zzsrmItem}>
                  <Form.Item name={'lastIdcard'} rules={[{ required: false, message: '必填' }]}>
                    <Input
                      placeholder=""
                      ref={input3Ref}
                      maxLength={4}
                      onKeyUp={inp3_onkeyup}
                    ></Input>
                  </Form.Item>
                </div>
              </div>
            )}
          </Form>
          <div
            className={style.btn}
            onClick={() => {
              form.submit();
            }}
          >
            搜 &nbsp; 索
          </div>
        </div>
      </div>
    </div>
  );
};

export default index;
