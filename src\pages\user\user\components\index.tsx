import React from 'react';
import style from './index.less';
import { SearchOutlined } from '@ant-design/icons';
import { AutoComplete, Input, Tree } from 'antd';
import {connect} from "dva";
import {throttle} from '@/utils/method.js';
import {_history as router} from "@/utils/method";
import {getSession} from "@/utils/session";
import qs from 'qs';
import {isNullOrUndefined, isUndefined} from "util";

const TreeNode=Tree.TreeNode;

interface proType {
  dispatch?:any,
  onChange?:(selectedKeys:Array<string>,e:object)=>void,
  onSearch?:(value:string)=>void,
  code?:string,
  rootCode?:string,
  nodeCode?:string,
  nodeName?:string,
  nodeParent?:string,
  listData:Array<any>,
  mapData:Map<string,object>,
  filterData?:Array<any>,
  loadData?:(data:Array<string>,callback?:Function)=>void,
  showSearch?:boolean,
  deductHeight?:any,
  type?:'selector' | 'tree',
  exclude?:Array<string>,
}

@connect()
export default class index extends React.Component<proType,{}> {
  static defaultProps={
    code:'code',
    nodeCode:'orgCode',
    nodeName:'shortName',
    nodeParent:'parentCode',
    listData:[],
    type:'tree',
    mapData:new Map(),
    filterData:[],
    showSearch:true,
  };
  constructor(props){
    super(props);
    this.state = {
      selectedKeys:[],
      expandedKeys:[],
      filterData:[],
      initExpand:false,
    }
  }
  onExpand=(expandedKeys, e)=>{//展开树节点
    this.setState({
      expandedKeys,
    });
  };
  onSelect=(selectedKeys, e)=>{//选中树节点
    const {selected}=e;
    if(selected){//选中执行后面的操作
      const { onChange,type }=this.props;
      onChange && onChange(selectedKeys, e);
      this.setState({
        selectedKeys,
      });
      if(e.node.props){
        if(type==='tree'){
          let org={...e.node.props.dataRef};
          // sessionStorage.setItem('org',JSON.stringify(org));
          const {query}=window['g_history'].location;
          if(query['pageNum']){
            query['pageNum']=1;
          }
          router.push(`?${qs.stringify(query)}`);
          // router.push(`?`);
        }
      }

    }
  };
  onSearch=(val:string)=>{//搜索内容变更
    // console.log(val,'vvvvvvvvvv')
    const {onSearch}=this.props;
    onSearch && throttle(onSearch,val,500,1000)
  };
  handleChange=async (val)=>{//搜索确定节点
    if(val){
      let expandedKeys:Array<string>=[];
      const {loadData,mapData,type,onChange,nodeCode}=this.props;
      if(loadData){
        let len=val.length;
        let num=len/3;
        let data:Array<string>=[];
        for (let i = 0; i < num; i++) {
          let code=val.substring(0,i*3);
          expandedKeys.push(code);
          if(code && !mapData.get(code)){
            data.push(code);
          }
        }
        //@ts-ignore
        let resData:object=await loadData(data);
        if(type==='tree'){
          let obj=mapData.get(val);
          if(!obj && resData){
            obj=resData['data'].find(obj=>obj['orgCode']===val);
          }
          if(obj){
            // sessionStorage.setItem('org',JSON.stringify(obj));
            const {query}=window['g_history'].location;
            if(query['pageNum']){
              query['pageNum']=1;
            }
            router.push(`?${qs.stringify(query)}`);
            onChange && onChange([obj[nodeCode || '']], {node:{props:{dataRef:obj}}});
          }
        }
      }
      this.setState({
        selectedKeys:[val],
        expandedKeys
      })
    }
  };
  static getDerivedStateFromProps(nextProps,prevState){//刷新页面 默认展开树节点及选中节点
    const {nodeCode,listData,type}=nextProps;
    let selectedKeys=[...prevState['selectedKeys']];
    let {expandedKeys,initExpand}=prevState;
    if(listData.length>0 && !initExpand){
      expandedKeys.push(listData[0][nodeCode || '']);
      initExpand=true;
      if(selectedKeys.length===0){
        // if(type==='tree'){
        const org=getSession('org') || {};
        let code=org[nodeCode];
        if(org && code && code.length>0){
          const num = code.length / 3;
          for (let i = 0; i < num; i++) {
            expandedKeys.push(code.substring(0,i*3));
          }
        }
        selectedKeys.push(code);
        // }else{
        //   selectedKeys.push(listData[0][nodeCode]);
        // }
      }
      return({
        expandedKeys,
        selectedKeys,
        initExpand,
      })
    }
    return null;
  }
  renderTreeNodes = (data,rootCode) => {//渲染树节点
    const {code,nodeCode,nodeName,nodeParent,exclude=[]}=this.props;
    const filterData=data.filter(obj=>obj[nodeParent || '']===rootCode);
    return filterData.map((item) => {
      const children=data.find(obj=>obj[nodeParent || '']===item[code || ""]);
      if( exclude.includes(item[nodeCode || ""])){
        return undefined
      }
      if (children) {
        const nextData=data.filter(obj=>obj[nodeParent || '']!==rootCode);
        return (
          <TreeNode title={item[nodeName || ""]} key={item[nodeCode || ""]} isLeaf={item['isLeaf']=='1'} dataRef={item}>
            {this.renderTreeNodes(nextData,item[code || ""])}
          </TreeNode>
        );
      }
      return <TreeNode title={item[nodeName || ""]} key={item[nodeCode || ""]} isLeaf={item['isLeaf']=='1'} dataRef={item} />;
    })
  };
  loadData=(node)=>{//异步加载数据
    const {listData,mapData,code,nodeCode,nodeParent,loadData}=this.props;
    const {dataRef}=node.props;
    const find=listData.find(obj=>obj[nodeParent || '']===dataRef[code || ""]);
    if(find){
      return new Promise(resolve => {
        return resolve();
      })
    }
    return new Promise(async (resolve) => {
      if(loadData){
        await loadData([dataRef[nodeCode || ""]]);
      }
      resolve();
    })
  };
  render(): React.ReactNode {
    const roles=getSession('roles') || {};
    const { listData=[],rootCode=roles['managerOrgCode'],nodeName,code,filterData,nodeCode,showSearch,deductHeight }=this.props;
    let defaultExpandedKeys:Array<string>=[];
    let root=undefined;
    if(listData.length>0){
      defaultExpandedKeys=[listData[0][nodeCode || '']];
      if(rootCode){
        root=listData.find(obj=>obj[code || ""]===rootCode || obj[nodeCode || ""]===rootCode); //组织选择器 人员选择器 需传递父节点 code 或者 orgCode
      }
    }
    return (
      <div className={style.page}>
        {
          showSearch &&
          <React.Fragment>
            <div className={style.tit}>
              机构筛选
            </div>
            <AutoComplete
              dataSource={filterData}
              style={{width:'97.5%',margin:'0 1%'}}
              onSelect={this.handleChange}
              onSearch={this.onSearch}
              placeholder="请输入搜索关键词"
            >
              <Input suffix={<SearchOutlined className="certain-category-icon" />} />
            </AutoComplete>
          </React.Fragment>
        }
        <div style={{height:`calc(100vh - ${deductHeight+140}px)`,overflow:'auto'}}>
          {
            listData.length>0 && <Tree
              onExpand={this.onExpand}
              onSelect={this.onSelect}
              loadData={this.loadData}
              defaultExpandedKeys={defaultExpandedKeys}
              expandedKeys={this.state['expandedKeys']}
              // selectedKeys={this.state['selectedKeys']}
            >
              {
                root ?
                  <TreeNode title={root[nodeName || ""]} key={root[nodeCode || ""]} isLeaf={root['isLeaf']=='1'} dataRef={root} >
                    {
                      this.renderTreeNodes(listData,root[code || ""])
                    }
                  </TreeNode>  :
                  this.renderTreeNodes(listData,rootCode)
              }
            </Tree>
          }
        </div>
      </div>
    );
  }
}
