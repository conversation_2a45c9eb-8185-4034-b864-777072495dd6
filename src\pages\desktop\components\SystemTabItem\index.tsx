import React, { Fragment, useState, useEffect } from 'react';
import { connect } from 'dva';
import { Tabs } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import style from '../../index.less';

const index = (props) => {
  const { menuItemClick = undefined } = props;
  const [radioDates, setRadioDates] = useState<any>([]);

  const getAppItem = () => {
    // 账号管理的系统
    const managementSystem = sessionStorage.getItem('managementSystem') || '';
    const { menuData } = props.login;
    let BasicMenu = _isEmpty(menuData)
      ? []
      : menuData.filter(
          (item) => item['parent'] === '-1' && item['url'] !== '/home' && item['type'] == '1',
        );
    let sonSystem: any = [];
    let radioDates = [
      {
        text: '基础信息',
        name: 'basic',
        component: renderMenuItems(BasicMenu),
      },
    ];
    if (managementSystem.includes('2')) {
      let obj = {
        name: '村社区',
        code: 'csq',
        icon: require('@/assets/desktop/svg/csq.jpg'),
        color: '',
        parent: '-1',
        isRoot: true,
        url: '',
      };
      sonSystem = [...sonSystem, obj];
    }
    if (!_isEmpty(sonSystem)) {
      // radioDates.push({
      //   text: '子系统',
      //   name: 'sonSystem',
      //   component:renderMenuItems(sonSystem),
      // });
    }
    setRadioDates(radioDates)
  };

  const renderMenuItems = (arr) => {
    if (_isEmpty(arr)) {
      return;
    }
    return arr.map((item, index) => {
      return (
        <div key={index} className={style.menuItem}>
          <div onClick={() => menuItemClick(item)} className={style.menuDiv}>
            <div className={style.img}>
              <img src={item['icon']} />
            </div>
          </div>
        </div>
      );
    });
  };

  useEffect(() => {
    // 获取党建及其他系统图标
    getAppItem();
  }, []);

  return (
    <div>
      <Tabs>
        {radioDates.map((item, index) => (
          <Tabs.TabPane tab={item['text']} key={index + 1}>
            <div className={style.hasInfo}>{item.component}</div>
          </Tabs.TabPane>
        ))}
      </Tabs>
    </div>
  );
};

export default connect(({ login }: any) => ({ login }))(index);
