import React, { useState } from 'react';

const BookFlipWithButton = (props, ref) => {
    const [currentPage, setCurrentPage] = useState(0);
    const [isFlipping, setIsFlipping] = useState(false);

    const handleNextPage = () => {
        if (currentPage < pages.length - 1) {
            setIsFlipping(true);
            setTimeout(() => {
                setCurrentPage(prev => prev + 1);
                setIsFlipping(false);
            }, 1000); // 等待翻页动画完成
        }
    };

    const handlePrevPage = () => {
        if (currentPage > 0) {
            setIsFlipping(true);
            setTimeout(() => {
                setCurrentPage(prev => prev - 1);
                setIsFlipping(false);
            }, 1000);
        }
    };

    return (
        <div className="book-container" ref={ref} id={props.ids} key={props.key}>
            {props.children}
        </div>
    );
};

export default React.forwardRef(BookFlipWithButton);