import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {addTransfer, adjustMem, findInByPage, inDetail, memTransferInFromSysOut, transferMem} from '../../services';
import {getSession} from 'src/utils/session';

const org = modelExtend(listPageModel,{
  namespace: "transferIn",
  state:{
    basicInfo:{},
    basicInfoData:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if(pathname==='/transfer/inflows' || pathname==='/transfer/historyin'){
          const org=getSession('org') || {};
          const dictData=['dict_d59'];
          let isHistory=false;
          for(let obj of dictData){
            dispatch({
              type:'commonDict/getDictTree',
              payload:{
                data:{
                  dicName:obj
                }
              }
            });
          }
          const defaultParas={
            pageNum:1,
            pageSize:10,
          };
          if(pathname==='/transfer/historyin'){
            isHistory=true;
          }
          dispatch({
            type:'findInByPage',
            payload:{
              orgId:org['code'],
              isHistory,
              ...defaultParas,
              ...query,
            }
          })
        }
      });
    }
  },
  effects: {
    *findInByPage({payload}, {call, put,select}) {//转入列表
      const {filter,keyWord}=yield select(state=>state['transferIn']);
      payload={...payload,...filter,keyWord};
      const {data = {}} = yield call(findInByPage, {data:payload});
      yield put({
        type: 'updateState',
        payload: {
          inList: data['list'],
          inPagination: {
            current: data['pageNumber'],
            pageSize: data['pageSize'],
            total: data['totalRow'],
          }
        }
      })
    },
    *addTransfer({payload}, {call, put}) {//整建制转接
      return yield call(addTransfer, payload);
    },
    *transferMem({payload}, {call, put}) {//人员转接
      return yield call(transferMem, payload);
    },
    *adjustMem({payload}, {call, put}) {
      return yield call(adjustMem, payload);
    },
    *inDetail({payload}, {call, put}) {
      const {data} = yield call(inDetail, payload);
      yield put({
        type: 'updateState',
        payload: {
          transDetail:data,
        }
      });
    },
    *memTransferInFromSysOut({payload}, {call, put}) {
      return yield call(memTransferInFromSysOut, payload);
    },
  }
});
export default org;
