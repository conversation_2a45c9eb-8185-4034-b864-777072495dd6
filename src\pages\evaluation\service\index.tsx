import request from 'src/utils/request';
// import qs from 'qs';

export function getHreMeetingsAndOneClass(params) {//三会一课
  return request('/api/ledger/political/getHreMeetingsAndOneClass',{
    method:'POST',
    body:params
  });
}
export function getThematicPartyDay(params) {//主题党日
  return request('/api/ledger/political/getThematicPartyDay',{
    method:'POST',
    body:params
  });
}
export function getTwoCommittee(params) {//村社区两委委员
  return request('/api/ledger/groupconstruction/getTwoCommittee',{
    method:'POST',
    body:params
  });
}
//列表
export function outMemList(params) {
  return request(`/api/flowmem/outMemList?pageNum=${params.pageNum}&pageSize=${params.pageSize}&orgCode=${params.orgCode}`,{
    method:'GET',
  });
}
