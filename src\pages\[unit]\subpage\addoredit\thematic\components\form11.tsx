import React, { Fragment, useEffect, useState } from 'react';
import { But<PERSON>, Col, Divider, Form, InputNumber, Row } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import { formItemLayout3 } from './config';
import { findZtDataByCode, saveZt11Data } from '@/pages/[unit]/services/thematic';
import _cloneDeep from 'lodash/cloneDeep';
import Tip from '@/components/Tip';
import { Icon as LegacyIcon } from '@ant-design/compatible';

const index = (props: any) => {
  const { selectTab, basicInfo } = props;
  const { code: unitCode = '' } = basicInfo;
  const [form] = Form.useForm();
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);

  const onFinish = async (e) => {
    setLoading(true);
    let val = _cloneDeep(e);
    if (e['specialFinancial'] > 200) {
      Tip.error('操作提示', '财政专项列支非公企业党建工作经费（万元）不能大于200')
      return;
    }
    if (e['feeSubsidies'] > 200) {
      Tip.error('操作提示', '党费拨补非公企业党建工作经费（万元）不能大于200')
      return;
    }
    const { code = 500 } = await saveZt11Data({
      data: {
        unitCode,
        ...val,
      },
    });
    setLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '修改成功');
      getInfo();
    }
  };
  const getInfo = async () => {
    const { code = 500, data = {} } = await findZtDataByCode({
      unitCode,
      type: '11',
    });
    if (code === 0) {
      setQuery(data);
      form.setFieldsValue(data);
    }
  };
  useEffect(() => {
    getInfo();
  }, []);
  return (
    <Fragment>
      <Form form={form} {...formItemLayout3} onFinish={onFinish}>
        <Row>
          <Col span={24}>
            <Divider plain>机构设置及运行情况</Divider>
          </Col>
          <Col span={12}>
            <Form.Item
              name="relyOrganNonPublicCommitteeNum"
              label="依托组织部门成立的非公党工委（个）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="officesNonPublicCommitteeNum"
              label="设立专门办事机构的非公党工委（个）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="officesStaffingNum"
              label="办事机构工作人员编制（个）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="directManageNonPublicOrganNum"
              label="组织部门（非公党工委）直接管理的非公企业党组织（个）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="directContactNonPublicOrganNum"
              label="组织部门（非公党工委）直接联系的非公企业党组织（个）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Divider plain>党组织书记情况</Divider>
          </Col>
          <Col span={12}>
            <Form.Item name="secretaryNum" label="党组织书记总数" rules={[{ required: true }]}>
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="secretaryHoldMiddleManagerNum"
              label="由企业中层及以上管理人员担任的（人）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="aboveCollegeNum"
              label="大学以上学历的（人）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Divider plain>企业主要负责人情况</Divider>
          </Col>
          <Col span={12}>
            <Form.Item name="principalPartyNum" label="党员（人）" rules={[{ required: true }]}>
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="principalNonPartyNum"
              label="非党员（人）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Divider plain>经费保障情况</Divider>
          </Col>
          <Col span={12}>
            <Form.Item
              name="specialFinancial"
              label="财政专项列支非公企业党建工作经费（万元）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="feeSubsidies"
              label="党费拨补非公企业党建工作经费（万元）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Divider plain>场所保障情况</Divider>
          </Col>
          <Col span={12}>
            <Form.Item
              name="activityServiceCenterNum"
              label="非公企业集聚区综合性党群活动服务中心（个）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="whenTheNewNum" label="当年新建（个）" rules={[{ required: true }]}>
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>

          <div style={{ width: '100%', textAlign: 'center' }}>
            <WhiteSpace />
            <Button
              icon={<LegacyIcon type={'check'} />}
              type={'primary'}
              onClick={() => form.submit()}
              loading={loading}
            >
              保存
            </Button>
          </div>
        </Row>
      </Form>
    </Fragment>
  );
};
export default index;
