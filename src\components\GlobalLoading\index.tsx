import React from 'react';
import { Spin } from 'antd';
import ReactDOM from 'react-dom';

let loadingCount = 0;
let loadingDom: any = null;

const GlobalLoading = {
  show() {
    if (loadingCount === 0) {
      const dom = document.createElement('div');
      dom.setAttribute('id', 'globalLoading');
      dom.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      `;
      document.body.appendChild(dom);
      loadingDom = dom;
      ReactDOM.render(<Spin size="large" />, dom);
    }
    loadingCount++;
  },
  hide() {
    if (loadingCount <= 0) return;
    loadingCount--;
    if (loadingCount === 0) {
      document.body.removeChild(loadingDom);
      loadingDom = null;
    }
  }
};

export default GlobalLoading;