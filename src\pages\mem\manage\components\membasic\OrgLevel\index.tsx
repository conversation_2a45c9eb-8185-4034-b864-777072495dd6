import React from 'react';
import {Table,Drawer} from 'antd';
import _isEmpty from 'lodash/isEmpty';
import {getOrgLevelList} from '../../../../services/index';
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible:false,
      loading:false,
      dataSource:[]
    }
  }

  onClose=()=>{
    this.setState({visible:false})
  };
  open=(val)=>{
    this.getInfo(val);
    this.setState({visible:true})
  };
  getInfo=async(val)=>{
    const {memOrgCode = ''} = val || {};
    if(!_isEmpty(memOrgCode)){
      this.setState({loading:true});
      const res = await getOrgLevelList({memOrgCode});
      this.setState({loading:false});
      const {code = 500,data= []} = res || {};
      if(code === 0){
        this.setState({dataSource:data})
      }
    }

  };
  render() {
    const {visible,dataSource} = this.state;
    const columns = [
      {
        title:'组织名',
        dataIndex:'name',
      },
      {
        title:'简称',
        dataIndex:'shortName',
      },
      {
        title:'组织联系人',
        dataIndex:'contacter',
      },
      {
        title:'组织联系人电话',
        dataIndex:'contactPhone',
      },
    ];
    return (
      <Drawer
        title="组织层级"
        placement="right"
        closable={false}
        onClose={this.onClose}
        visible={visible}
        width={'600px'}
      >
        <Table dataSource={dataSource} columns={columns} rowKey={ (record, index) => `${index}`} pagination={false}/>
      </Drawer>
    );
  }
}
