import React from 'react';
import Tip from '@/components/Tip';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Modal } from 'antd';
const FormItem = Form.Item;
const {TextArea} = Input;
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible:false
    }
  }
  open=(record)=>{
    this.setState({
      visible:true,record
    })
  };
  handleOk=()=>{
    const {onclose} = this.props;
    const {record} = this.state;
    this.props.form.validateFieldsAndScroll(async(errors, values) => {
      if(!errors){
        console.log(record,values,'sss');
        const { replyContext } = values;
        const res = await this.props.dispatch({
          type:'notice/sendMsg',
          payload:{
            data:{
              code:record['code'],
              replyContext,
            }
          }
        });
        const {code = 500} = res || {};
        if(code===0){
          Tip.success('操作提示','操作成功');
          this.handleCancel();
          onclose && onclose()
        }
      }
    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false
    })
  };
  render() {
    const {form} = this.props;
    const {getFieldDecorator} = form;
    const {visible} = this.state;
    return (
      <div>
        <Modal
          title={'回复'}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
          destroyOnClose={true}
        >
          <Form>
            <FormItem
            >
              {getFieldDecorator('replyContext', {
                rules: [{ required: true, message: '请输入回复内容' }],
              })(
                <TextArea placeholder={'请输入回复内容'} rows={3} />
              )}
            </FormItem>
          </Form>
        </Modal>
      </div>
    );
  }
}
export default Form.create()(index);
