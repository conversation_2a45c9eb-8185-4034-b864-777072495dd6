/**
 * 关系转出
 */
import React, { Fragment } from 'react';
import RuiFilter from '@/components/RuiFilter';
import ListTable from '@/components/ListTable';
import { Divider, Input, Tabs, Button } from 'antd';
import NowOrg from "@/components/NowOrg";
import qs from 'qs';
import Details from '../outflows/components/details'; //转接详情
import {connect} from "dva";
import moment from 'moment';
import {getSession} from "@/utils/session";
import Tip from '@/components/Tip';
import WhiteSpace from '@/components/WhiteSpace';
import {setListHeight} from "@/utils/method";
import {_history as router} from "@/utils/method";
import Archives from '../components/archives';
import ExportInfo from '@/components/Export';
import {Letter} from '@/pages/transfer/outflows'

const TabPane=Tabs.TabPane;
const Search=Input.Search;

@connect(({transferOut,commonDict,loading})=>({transferOut,loading:loading.effects['transferOut/findOutByPage'],commonDict:commonDict['dict_d58_tree'],}))
export default class extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      transferId:undefined
    }
  }
  confirm=async (item)=>{
    const obj=await this.props.dispatch({
      type:'transferOut/undo',
      payload:{
        data:{
          id:item['id'],
          reason:'撤销'
        }
      }
    });
    if(obj && obj['code']===0){
      Tip.success('操作提示','撤销成功');
      this.refresh();
    }
  };
  filterChange=(val)=>{
    // 地址栏显示的页码重置为1
    let {query}=this.props.location;
    const { pageNum,pageSize } = this.state;
    router.push(`?${qs.stringify({...query,pageNum: 1, pageSize})}`)
    
    this.props.dispatch({
      type:'transferOut/updateState',
      payload:{
        filter:val
      }
    });
    // this.refresh();
  };
  search=(val)=>{
    // 地址栏显示的页码重置为1
    let {query}=this.props.location;
    const { pageNum,pageSize } = this.state;
    router.push(`?${qs.stringify({...query,pageNum: 1, pageSize})}`)

    this.props.dispatch({
      type:'transferOut/updateState',
      payload:{
        keyWord:val
      }
    });
    // this.refresh({pageNum:1});
  };
  searchClear=(e)=>{
    if(!e.target.value){
      this.props.dispatch({
        type:'transferOut/updateState',
        payload:{ keyWord:undefined }
      });
      this.refresh();
    }
  };
  componentWillUnmount(): void {
    this.props.dispatch({
      type:'transferOut/destroy',
    })
  }
  refresh=(params?:any)=>{//刷新列表
    const {outPagination={}}=this.props.transferOut;
    const org=getSession('org') || {};
    this.props.dispatch({
      type:'transferOut/findOutByPage',
      payload:{
        isHistory:true,
        orgId:org['code'],
        pageNum:outPagination['current'] || 1,
        pageSize:outPagination['pageSize'] || 10,
        ...params,
      }
    });
  };
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
    this.setState({
      pageNum:page,
      pageSize,
    })
  };
  componentDidMount(): void {
    setListHeight(this)
  }
  exportInfo= async ()=>{
    this.setState({
      flowBackDownload:true,
    })
    await this['flowBack'].submitNoModal();
    this.setState({
      flowBackDownload:false,
    })
  };
  render(){
    const {loading,transferOut}=this.props;
    const {outList=[],outPagination=false}=transferOut;
    const {current,pageSize}=outPagination;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'姓名',
        dataIndex:'name',
        width:120,
      },
      {
        title:'申请日期',
        dataIndex:'createTime',
        width:100,
        render:(text)=>{
          return moment(text).format('YYYY-MM-DD')
        }
      },
      {
        title:'源组织',
        dataIndex:'srcOrgName',
        width:200,
      },
      {
        title:'目的组织',
        dataIndex:'targetOrgName',
        width:200,
      },
      {
        title:'转接类型',
        dataIndex:'typeName',
        width:100,
      },
      {
        title:'转接状态',
        dataIndex:'status',
        width:80,
        render:(text)=>{
          switch (text) {
            case 0:
              return '转接中';
            case 1:
              return '已完成';
            case 2:
              return '已撤销';
            case 4:
              return '超期自动退回';
            default:
          }
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        width:120,
        render:(text,record,index)=>{
          return(
            <span>
              <a
                onClick={()=>{
                  this.props.dispatch({
                    type:'transferOut/outDetail',
                    payload:{
                      transferId:record['id']
                    }
                  }).then(res=>
                    this.setState({
                      transferId:record['id'],
                      memCode: record['memId'],
                    },()=>{
                      this['Details'].open()
                    })
                  )
                }}
              >
                详情
              </a>
              {/* {
                record['status'] == 1 &&
                  <Fragment>
                    <Divider type="vertical"/>
                    <a onClick={()=>{
                      if(this['Archives']?.open){
                        this['Archives']?.open(record);
                      }
                    }}>档案管理</a>
                  </Fragment>
              } */}
              <Divider type="vertical"/>
              <Letter {...this.props} record={record} isOut={'1'}/>
            </span>
          )
        }
      },
    ];
    const filterData=[
      {
        key:'types',name:'转接类型',value:this.props.commonDict,
      },
      {
        key:'status',name:'转接状态',value:[{key:1, name:'已完成'},{key:2, name:'已撤销'}, {key:4, name:'超期自动退回'}],
      },
    ];
    const org = getSession('org') || {};
    return(
      <div>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        {/*转接详情*/}
        <Details refresh={this.refresh}  transferId={this.state.transferId} memCode={this.state.memCode} isHistory={true} type={'out'} wrappedComponentRef={e=>this['Details']=e}/>
        <NowOrg extra={
          <React.Fragment>
            <Button onClick={this.exportInfo} loading={this.state.flowBackDownload} style={{marginLeft:16}}>导出</Button>
            <Search style={{width:200,marginLeft:16}} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'}/>
          </React.Fragment>
        }/>
        <RuiFilter data={filterData} onChange={this.filterChange}/>
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:this.state.filterHeight}} columns={columns} data={outList} pagination={outPagination} onPageChange={this.onPageChange}/>
        <Archives ref={e=>this['Archives'] = e} onOK={()=>{
          this.refresh();
        }}/>
         <ExportInfo wrappedComponentRef={e=>this['flowBack'] = e}
                    tableName={''}
                    noModal={true}
                    tableListQuery={{isHistory:true,orgId:org['code'], ...this.props.transferOut.filter,keyWord:this.props.transferOut.keyWord}}
                    action={'/api/transfer/exportOut'}
        />
      </div>
    )
  }
}
