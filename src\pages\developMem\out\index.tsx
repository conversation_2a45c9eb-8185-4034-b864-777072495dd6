/**
 * 关系转出
 */
import React from 'react';
import RuiFilter from '@/components/RuiFilter';
import ListTable from '@/components/ListTable';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs, Modal } from "antd";
import NowOrg from "@/components/NowOrg";
import AdjustMem from './components/adjustMem'; //支部间人员调整
import Transfer from './components/transfer'; //关系转接
import Details from './components/details'; //转接详情
import TransferMemUp from './components/transferMemUp';
import { connect } from "dva";
import moment from 'moment';
import { getSession } from "@/utils/session";
import Tip from '@/components/Tip';
import WhiteSpace from '@/components/WhiteSpace';
// import router from "umi/router";
import { _history as router } from "@/utils/method";
import qs from 'qs';
import { setListHeight,fileDownloadHeader,isFlowingParty } from "@/utils/method";
import ExportInfo from '@/components/Export';
import { ButtonDisabled } from '@/common/config.js'
import { exportLetter } from '@/pages/transfer/services'
import _last from 'lodash/last';
import { ExclamationCircleTwoTone, ExclamationCircleOutlined } from '@ant-design/icons';
import { includes } from 'lodash';
const TabPane = Tabs.TabPane;
const Search = Input.Search;

export const Letter = (props: any) => {
  const { record } = props;
  const onClick = async () => {
    const { code = 500, data = {} } = await exportLetter({ data: { id: record?.id } })
    let dataApi = sessionStorage.getItem('dataApi') || "";
    if (code == 0) {
      Tip.success('操作提示', '下载成功，正在下载...')
      // let url = data.url.split('/');
      // let name = _last(url).split('.')[0]
      // console.log("🚀 ~ file: index.tsx ~ line 40 ~ onClick ~ name", encodeURI(name))
      // console.log("🚀 ~ file: index.tsx ~ line 39 ~ onClick ~ url", url)
      fileDownloadHeader(`/api${data.url}`, _last(data.url.split('/')), { dataApi });
    }
  };
  // 212 117 整建制没有下载介绍信
  if (record['type'] == '212' || record['type'] == '117') {
    return null
  } else {
    return (
      <a onClick={onClick}>下载介绍信</a>
    )
  }
};
@connect(({ memDevelop, commonDict, loading }) => ({ memDevelop, loading: loading.effects['memDevelop/findOutByPage'], commonDict: commonDict['dict_d58_tree'], }))
export default class extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      transferId: undefined,
      messageVisible: false
    }
  }
  confirm = async (item) => {
    const obj = await this.props.dispatch({
      type: 'memDevelop/undo',
      payload: {
        data: {
          id: item['id'],
          reason: '撤销'
        }
      }
    });
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', '撤销成功');
      this.refresh();
    }
  };
  filterChange = (val) => {
    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        filter: val
      }
    });
    this.refresh();
  };
  search = (val) => {
    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        keyWord: val
      }
    });
    this.refresh({ pageNum: 1 });
  };
  searchClear = (e) => {
    if (!e.target.value) {
      this.props.dispatch({
        type: 'memDevelop/updateState',
        payload: { keyWord: undefined }
      });
      this.refresh();
    }
  };
  adjust = () => {//支部间人员调整
    this['AdjustMem'].open();
  };
  addOrEdit = () => {//关系转接
    this['Transfer'].open();
  };
  expFile = () => {
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memDevelop/exportXsl',
      payload: {
        data: {
          isHistory: false,
          orgId: org['code'],
          isOut: 1
        }
      }
    })
  }
  refresh = (params?: any) => {//刷新列表
    const { outPagination = {} } = this.props.memDevelop;
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memDevelop/findOutByPage',
      payload: {
        isHistory: false,
        orgId: org['code'],
        pageNum: outPagination['current'] || 1,
        pageSize: outPagination['pageSize'] || 10,
        ...params,
      }
    });
  };
  onPageChange = (page, pageSize) => {
    console.log(page, pageSize);
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`)
  };
  componentWillUnmount(): void {
    this.props.dispatch({
      type: 'memDevelop/destroy',
    })
  }
  componentDidMount(): void {
    setListHeight(this)
  }
  exportInfo = async () => {
    this.setState({
      flowBackDownload: true,
    })
    await this['flowBack'].submitNoModal();
    this.setState({
      flowBackDownload: false,
    })
  };
  handleOk = () => {
    this.handleCancel();
    this.addOrEdit();
  };
  handleCancel = () => { this.setState({ messageVisible: false }) };
  render() {
    const { loading, memDevelop } = this.props;
    const { outList = [], outPagination = false, filter = {} } = memDevelop;
    const { current, pageSize } = outPagination;
    const org = getSession('org') || {};
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 50,
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1
        }
      },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 120,
      },
      {
        title: '申请日期',
        dataIndex: 'createTime',
        width: 100,
        render: (text) => {
          return moment(text).format('YYYY-MM-DD')
        }
      },
      {
        title: '源组织',
        dataIndex: 'srcOrgName',
        width: 200,
      },
      {
        title: '目的组织',
        dataIndex: 'targetOrgName',
        width: 200,
      },
      {
        title: '转接类型',
        dataIndex: 'typeName',
        width: 100,
      },
      {
        title: '转接状态',
        dataIndex: 'status',
        width: 80,
        render: (text) => {
          switch (text) {
            case 0:
              return '转接中';
            case 1:
              return '已完成';
            case 2:
              return '已撤销';
            default:
          }
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 140,
        render: (text, record, index) => {
          const { id = '' } = record
          return (
            <span>
              <a
                onClick={() => {
                  this.props.dispatch({
                    type: 'memDevelop/outDetail',
                    payload: {
                      transferId: id
                    }
                  }).then(res =>

                    this.setState({
                      transferId: id,
                    }, () => {
                      this['Details'].open()
                    })
                  )
                }}
              >
                {record?.status == 0 ? '操作' : '详情'}
              </a>
              {
                record?.status == 0 &&
                <React.Fragment>
                  <Divider type="vertical" />
                  <Popconfirm title="确定要撤销吗？" onConfirm={() => this.confirm(record)}>
                    <a className={'del'}>撤销</a>
                  </Popconfirm>
                </React.Fragment>
              }
              {
                [1, 2].includes(record?.status) && null
              }
            </span>
          )
        }
      },
    ];
    const filterData = [
      {
        key: 'status', name: '转接状态', value: [
          {
            id: 0,
            name: '转接中'
          },
          {
            id: 1,
            name: '已完成'
          },
          {
            id: 2,
            name: '已撤销'
          },
        ],
      },
    ];
    return (
      <div>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1" />
        </Tabs>
        {/*<AddOrEdit dataInfo={dataInfo}/>*/}
        {/*支部间人员调整*/}
        <AdjustMem refresh={() => this.refresh({ pageNum: 1 })} wrappedComponentRef={e => this['AdjustMem'] = e} />
        {/*关系转接*/}
        <Transfer refresh={() => this.refresh({ pageNum: 1 })} wrappedComponentRef={e => this['Transfer'] = e} />
        {/*转接详情*/}
        <Details refresh={this.refresh} transferId={this.state.transferId} type={'out'} wrappedComponentRef={e => this['Details'] = e} />
        {/*修改党员转接信息*/}
        <TransferMemUp wrappedComponentRef={e => this['TransferMemUp'] = e} />
        <NowOrg extra={
          <React.Fragment>
            {/* <Button onClick={this.exportInfo} loading={this.state.flowBackDownload}>导出</Button> */}
            {
              (!ButtonDisabled.statistics2021 && isFlowingParty())&& <React.Fragment>
                <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.adjust} style={{ marginLeft: 16 }}>支部间人员调整</Button>
                {/* 入党申请人--积极分子转出 省内关系转出按钮  -- 直接屏蔽 2025-1-2 */}
                <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={() => this.addOrEdit()} style={{ marginLeft: 16 }}>省内关系转出</Button>
                <Button icon={<LegacyIcon type={'vertical-align-bottom'} />} onClick={() => this.expFile()} style={{ marginLeft: 16 }}>导出</Button>
              </React.Fragment>
            }
            <Search style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'} />
          </React.Fragment>
        } />
        <RuiFilter data={filterData} onChange={this.filterChange} />
        <WhiteSpace />
        <WhiteSpace />
        <ListTable scroll={{ y: this.state.filterHeight }}
         columns={columns} data={outList} pagination={outPagination} onPageChange={this.onPageChange} />
        <ExportInfo wrappedComponentRef={e => this['flowBack'] = e}
          tableName={''}
          noModal={true}
          tableListQuery={{ isHistory: false, orgId: org['code'], ...this.props.memDevelop.filter, keyWord: this.props.memDevelop.keyWord }}
          action={'/api/activist/transfer/exportOut'}
        />
        <Modal
          destroyOnClose
          title={<div style={{ color: '#1890ff' }}><ExclamationCircleOutlined /> 提示</div>}
          visible={this.state.messageVisible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={500}
        >
          <div>
            根据相关要求，党政机关县处级以上党员领导干部（不含职级公务员和参照管理的企事业单位领导人员等）不通过线上进行组织关系转接，应通过纸质介绍信随干部调动程序一并进行，在本系统中这类党员转接时，转出党组织请选择转出到省外（个人），转入党组织通过关系转入入口进行转接录入。
          </div>
        </Modal>
      </div>
    );
  }
}
