import React, {Component, Fragment} from 'react';
import {Button} from 'antd';
import {Consumer} from './context';
import styles from './top.less';
export default class index extends Component<any, any>{
  constructor(props){
    super(props);
    this.state = {

    };
  }

  render() {
    return (
      <Consumer>
        {
          (val = {}) => {
            //@ts-ignore
            const {title = '', goBack = function() {
              console.warn('没有返回函数');
            }} = val;
            return (
              <Fragment>
                {
                  title &&
                  <div className={styles.main}>
                    <dd className="detail-title">
                      <h2 style={{display: 'inline'}}>{title}</h2>&nbsp;
                    </dd>
                    {/*<Button type="primary" onClick={goBack} className={styles.btn}>返回</Button>*/}
                  </div>
                }
              </Fragment>
            );
          }
        }
      </Consumer>
    );
  }
}
