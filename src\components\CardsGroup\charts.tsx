import React, {Fragment} from 'react';
import {Skeleton} from 'antd';
import style from './index.less';
import request from "@/utils/request";
import {withContext} from '@/utils/global';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import Echarts from '@/components/Echarts';
import { getSession } from '@/utils/session';

interface Interface {
  chartsConfig:{
    option?:object,
    type?:string,
    tit?:string,
    chartId:string,
    chartType:string,
    xAxisCode:string,
    yAxisCode:string,
    zAxisCode?:string,
    extraG2ChartConfig?:object
  },
}
@withContext
export default class Charts extends React.Component<Interface, any> {
  constructor(props) {
    super(props);
    this.state = {
      loading:false,
      dataSource:[],
      getData:(orgCode,startDate,endDate,action)=>{
        request(action,{
          method:'POST',
          body:{
            data:{
              orgCode,
              startDate,
              endDate
            }
          },
        }).then(res=>{
          if(res['code']===0){
            this.setState({
              loading:false,
              dataSource: res['data']
            });
          }
        });
      }
    }
  }
  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const {context,timeRange:{startDate = undefined,endDate = undefined}={},chartsConfig:{action = ''}={}} = nextProps;
    const {_context, getData, _startDate, _endDate } = prevState;
    if(!_isEqual(context, _context) || !_isEqual(startDate,_startDate) || !_isEqual(endDate,_endDate) ){
      state['_context'] = context;
      state['_startDate'] = startDate;
      state['_endDate'] = endDate;
      const { orgCode = ''} = getSession('org') || {orgCode:''}
      state['loading'] = true;
      if(_isEmpty(action)){
        state['loading'] = false;
      }
      action && orgCode && getData(orgCode,startDate,endDate,action);
    }
    return state;
  };
  render(): React.ReactNode {
    const {dataSource,loading} = this.state;
    const {chartsConfig} =this.props;
    const {option, type, tit, chartId, chartType, xAxisCode, yAxisCode, zAxisCode, extraG2ChartConfig} = chartsConfig;
    return (
      <Fragment>
        {
          !_isEmpty(tit) &&
          <div className={style.tit}>
            {tit}
          </div>
        }
        <Skeleton loading={loading} active={true}>
          <Fragment>
            {
              typeof option === 'function' &&
              <Echarts option={option(dataSource)}/>
            }
          </Fragment>
        </Skeleton>
      </Fragment>
    )
  }
}
