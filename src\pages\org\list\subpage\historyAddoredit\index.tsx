import React from 'react'
import {
  CloseOutlined,
  ApartmentOutlined,
  BlockOutlined,
  BranchesOutlined,
  CalendarOutlined,
  ContainerOutlined,
  <PERSON><PERSON><PERSON><PERSON>tOutlined,
  FieldTimeOutlined,
  FileZipOutlined,
  FormOutlined,
  RadarChartOutlined,
  CrownOutlined,
  WhatsAppOutlined,
  FileDoneOutlined
} from '@ant-design/icons';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Avatar, Col, Menu, Modal, Row } from 'antd';
import Basic from './basic';
// import Extension from './extension';
// import WhiteSpace from '@/components/WhiteSpace';
// import Leader from './leader';
// import RewardPunishment from './rewardPunishment';
// import Working from './working';
// import Assess from './assess';
// import Team from './team';
// import Appraisal from './appraisal';
// import Commend from './commend';
// import Area from './area';
// import Party from './party';
// import Training from './training';
// import Feigong from './feigong';
// import Town from './town';
// import Demonstration from './demonstration';
// import Contact from './contact';
import { connect } from "dva";
import { LockMsg } from '@/pages/user/lock';
import { _history as router, changeMsgTip } from '@/utils/method';
import { tableColConfig } from '@/services';
import _isEmpty from 'lodash/isEmpty';

const menuData = [
  {
    code: '1',
    name: '基本信息',
    icon: <ApartmentOutlined />,
  },
  // {
  //   code: '2',
  //   name: '扩展信息',
  //   icon: <BlockOutlined />,
  // },
  // {
  //   code: '3',
  //   name: '班子成员',
  //   icon: <BranchesOutlined />,
  // },
  // {
  //   code: '4',
  //   name: '奖惩信息',
  //   icon: <CalendarOutlined />,
  // },
  // {
  //   code: '5',
  //   name: '考核信息',
  //   icon: <ContainerOutlined />,
  // },
  // {
  //   code: '6',
  //   name: '党小组',
  //   icon: <FieldTimeOutlined />,
  // },
  // {
  //   code: '7',
  //   name: '民主评议',
  //   icon: <RadarChartOutlined />,
  // },
  // {
  //   code: '8',
  //   name: '发出表彰情况',
  //   icon: <PicLeftOutlined />,
  // },
  // {
  //   code: '9',
  //   name: '地方委员会情况',
  //   icon: <FileZipOutlined />,
  // },
  // {
  //   code: '10',
  //   name: '专职党务工作者情况',
  //   icon: <FormOutlined />,
  // },
  // {
  //   code: '11',
  //   name: '党代表情况',
  //   icon: <CrownOutlined />,
  // },
  // {
  //   code: '12',
  //   name: '培训情况',
  //   icon: <CrownOutlined />,
  // },
  // {
  //   code: '13',
  //   name: '乡镇班子选拔情况',
  //   icon: <CrownOutlined />,
  // },
  // {
  //   code: '14',
  //   name: '非公党建情况',
  //   icon: <CrownOutlined />,
  // },
  // {
  //   code: '15',
  //   name: '党支部工作联系点',
  //   icon: <WhatsAppOutlined />,
  // },
  // {
  //   code: '16',
  //   name: '党支部标准化规范化建设达标/示范点情况',
  //   icon: <FileDoneOutlined />,
  // },
];


// @ts-ignore
@connect(({ org, commonDict, loading }) => ({ org, commonDict, orgAdd: loading.effects['org/add'], orgUpdate: loading.effects['org/update'] }))
export default class index extends React.Component<any, any> {
  static show() { };
  static close() { };
  static clear() { };
  constructor(props) {
    super(props);
    let obj = menuData[0];
    this.state = {
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
      tipMsg: {},
    };
    index.show = this.open.bind(this);
    index.close = this.handleCancel.bind(this);
    index.clear = this.destroy.bind(this);
  }
  componentDidMount() {
    tableColConfig({ id: 'ccp_org' }).then(res => {
      if (res['code'] == '0') {
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg: msg,
        });
      }
    });

    tableColConfig({ id: 'ccp_org_reward' }).then(res => {
      if (res['code'] == '0') {
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg_reward: msg,
        });
      }
    });

    tableColConfig({ id: 'ccp_org_committee' }).then(res => {
      if (res['code'] == '0') {
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg_committee: msg,
        });
      }
    });

    tableColConfig({ id: 'ccp_org_reviewers' }).then(res => {
      if (res['code'] == '0') {
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg_reviewers: msg,
        });
      }
    });

  }
  handleOk = () => {
    this.handleCancel({});
  };
  handleCancel = (e) => {
    const basicLoading = this.props.orgAdd || this.props.orgUpdate;
    if (basicLoading) {
      return;
    }
    const { colseType = '' } = e;
    this.destroy();
    const { location: { search = undefined } = {} } = router;
    router.push(_isEmpty(search) || colseType === 'add' ? '?' : search)
    this.props.onClose && this.props.onClose()
  };
  open = () => {
    this.setState({
      visible: true,
    })
  };
  destroy = () => {
    let obj = menuData[0];
    this.setState({
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    });
    this.props.dispatch({//重置model
      type: 'org/updateState',
      payload: {
        basicInfo: {},
      }
    });
  };
  onSelect = (item) => {
    const { key, keyPath } = item;
    const selected = menuData.find(obj => obj['code'] === key);
    this.setState({
      key,
      keyPath,
      selected,
    });
    const { basicInfo = {} } = this.props.org;
    if (basicInfo['code']) {
      this.props.dispatch({
        type: 'org/findHistoryOrg',
        payload: {
          code: basicInfo['code']
        }
      })
    }
  };
  render() {
    const { visible, selected, keyPath, key, tipMsg = {}, tipMsg_reward = {}, tipMsg_committee = {}, tipMsg_reviewers = {} } = this.state;
    const { basicInfo = {} } = this.props.org;
    let bool = true;//菜单禁用
    if (basicInfo['code']) {
      bool = false
    }
    const { d01Code, linkedDTOList = [], d02Code } = basicInfo;
    // 党组织编辑中，当党组织关联单位的单位类别为乡镇的时候并且关连单位情况是独立法人单位，需要增加党代表模块信息填写
    let find = d02Code == '1' && linkedDTOList.find(it => `${it?.unit.d04Code}`?.startsWith('912'));

    let _menuData: any = menuData;
    console.log(menuData, 'menuData')
    if (d01Code != '631' && d01Code != '632' && d01Code != '931' && d01Code != '932') {
      _menuData = _menuData.filter(obj => obj['code'] != '6')
    }
    if (d01Code != '631' && d01Code != '632' && d01Code != '931' && d01Code != '932' && d01Code != '634') {
      _menuData = _menuData.filter(obj => obj['code'] != '7')
    }
    if (!(`${d01Code}`.startsWith('1') || `${d01Code}`.startsWith('2') || d01Code == 61 || d01Code == 911)) {
      _menuData = _menuData.filter(obj => obj['code'] != '8')
    }
    // 党组织管理编辑中，党组织类别为1开头和2开头得不再需要填写班子成员
    // 党组织类型是工委的（2开头），把班子成员放出来
    // 组织类别等于25、6开头、9开头，把班子成员放出来
    if (!(`${d01Code}`.startsWith('6') || `${d01Code}`.startsWith('9') || `${d01Code}` == '25' || `${d01Code}`.startsWith('2'))) {
      _menuData = _menuData.filter(obj => obj['code'] != '3')
    }
    if (!d01Code?.startsWith('1')) {
      _menuData = _menuData.filter(obj => obj['code'] != '9')
    }
    if (!(`${d01Code}`.startsWith('1') || find)) {
      _menuData = _menuData.filter(obj => obj['code'] != '11')
    }
    if (!(`${d01Code}`.startsWith('63') || `${d01Code}`.startsWith('1') || `${d01Code}`.startsWith('61') || `${d01Code}`.startsWith('62') || `${d01Code}` === '911')) {
      _menuData = _menuData.filter(obj => obj['code'] != '12')
    }
    if (!(`${d01Code}`.startsWith('14'))) {
      _menuData = _menuData.filter(obj => obj['code'] != '13')
    }
    if (!(`${d01Code}`.startsWith('1'))) {
      _menuData = _menuData.filter(obj => obj['code'] != '14')
    }
    //党组织类别 选择 其他（文件夹）时：左侧菜单只有基础信息
    if (d01Code == 'A') {
      _menuData = _menuData.filter(obj => obj['code'] === '1')
    }
    // 外部控制只展示某些menu item项
    if (!_isEmpty(this.props.menuDataKey)) {
      _menuData = _menuData.filter(it => this.props.menuDataKey.includes(it.code))
    }
    //党支部才显示：党支部工作联系点、党支部标准化规范化建设达标/示范点情况
    if (!(`${d01Code}`.startsWith('63'))) {
      _menuData = _menuData.filter(obj => obj['code'] !== '15' && obj['code'] !== '16')
    }

    const basicLoading = this.props.orgAdd || this.props.orgUpdate;

    return (
      <Modal
        title=""
        wrapClassName='editModal'
        destroyOnClose
        closable={false}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        footer={false}
        width={'calc(100vw - 100px)'}
      >
        <div className='container' style={{ pointerEvents: basicLoading ? 'none' : 'auto' }}>
          <div className='header'>
            <Row>
              <Col span={4} style={{ textAlign: 'center' }}>
                <Avatar style={{ backgroundColor: '#7265e6', verticalAlign: 'middle' }} size="large">
                  admin
                </Avatar>
              </Col>
              <Col span={15}>
                {/* <h2>{basicInfo['code'] ?
                <div>
                  查看党组织
                </div>
                : '新增党组织'}</h2> */}
                <h2>
                  <div>
                    查看党组织
                  </div></h2>
              </Col>
              <Col span={5} className={'close'}><CloseOutlined onClick={this.handleCancel} /></Col>
            </Row>
          </div>
          <div>
            <Row>
              <Col span={4} style={{ borderRight: '1px solid rgb(233, 233, 233)' }}>
                <div className='slider'>
                  <LegacyIcon type={selected['icon'] || undefined} style={{ marginRight: 8 }} />{selected['name']}
                </div>
                <Menu mode="inline" selectedKeys={keyPath} style={{ border: 'none' }} onSelect={this.onSelect}>
                  {
                    _menuData && _menuData.map((obj, index) => {
                      return (
                        <Menu.Item title={obj['name'] || ''} key={obj['code']} disabled={index > 0 ? bool : false} icon={obj['icon']}>
                          {obj['name']}
                        </Menu.Item>
                      );
                    })
                  }
                </Menu>
              </Col>
              <Col span={20} style={{ paddingTop: 20 }} className='content'>
                {
                  key === '1' && <Basic tipMsg={tipMsg} {...this.props} close={(e) => this.handleCancel(e)} /> //基本信息
                }
                {/* {
                  key === '2' && <Extension {...this.props} close={this.handleCancel} /> //扩展信息
                }
                {
                  key === '3' && <Leader tipMsg={tipMsg_committee} {...this.props} /> //领导班子
                }
                {
                  key === '4' && <RewardPunishment tipMsg={tipMsg_reward} {...this.props} /> //奖惩信息
                }
                {
                  key === '5' && <Assess tipMsg={tipMsg} {...this.props}/> //考核信息
                }
                {
                  key === '6' && <Team tipMsg={tipMsg} {...this.props} /> //党小组
                }
                {
                  key === '7' && <Appraisal tipMsg={tipMsg_reviewers} {...this.props} />
                }
                {
                  key === '8' && <Commend tipMsg={tipMsg} {...this.props} />
                }
                {
                  key === '9' && <Area tipMsg={tipMsg} {...this.props} />
                }
                {
                  key === '10' && <Working  tipMsg={tipMsg} {...this.props} />  // 党务工作者情况
                }
                {
                  key === '11' && <Party  tipMsg={tipMsg} {...this.props} />  // 党代表情况
                }
                {
                  key === '12' && <Training  tipMsg={tipMsg} {...this.props}/>  // 新的培训情况
                }
                {
                  key === '13' && <Town tipMsg={tipMsg} {...this.props}/>  // 乡镇班子换届情况
                }
                {
                  key === '14' && <Feigong tipMsg={tipMsg} {...this.props} />
                }
                {
                  key === '15' && <Contact tipMsg={tipMsg} {...this.props} /> // 党支部工作联系点
                }
                {
                  key === '16' && <Demonstration tipMsg={tipMsg} {...this.props}/> // 党支部标准化规范化建设达标/示范点情况
                }
                <WhiteSpace />
                <WhiteSpace /> */}
              </Col>
            </Row>
          </div>
        </div>
      </Modal>
    );
  }
}
