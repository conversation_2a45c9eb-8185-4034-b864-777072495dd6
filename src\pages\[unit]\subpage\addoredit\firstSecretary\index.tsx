import React, { Fragment } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Divider, Tabs } from 'antd';
import AddMember from './addMember';
import Leave from './leave';
import { connect } from "dva";
import moment from 'moment';
import Tip from '@/components/Tip';
import AddJcInfo from './addJcInfo';
import { PlusOutlined } from '@ant-design/icons';
import styles from '@/pages/org/list/subpage/addoredit/leader.less';
import { electList, electDel, residentList, residentDel, residentBackOut } from './services';
import CTbale, { PanelTable } from '@/components/CollapseTable';
import EndOfTerm from './end';
import History from './history';
import ListTable from '@/components/ListTable';
const { TabPane } = Tabs;
// @ts-ignore
@connect(({ unit }) => ({ unit }))
export default class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: {}, // 届次信息
      memInfo: {}, //人员信息
      activeKey: '0',
      list: [],

      pagination: { page: 1, pageSize: 10, total: 0 }
    }
  }

  // 新增编辑人员信息
  addOrEdit:any = async (item, key) => {
    this.setState({
      memInfo: item,
    }, () => {
      this['AddMember'].showModal({ key });
    });
  };

  // 离开
  handleLeave:any = async (record) => {
    this['Leave'].showModal(record);
  };

  // 删除届次
  del = async (e, item) => {
    e.stopPropagation();
    const { code = 500 } = await electDel({ electCode: item['id'] });
    if (code === 0) {
      // this.action();
      this['CTbale'].getList();
      Tip.success('操作提示', '删除成功');
    }
  };

  // 编辑届次
  editJc = (e, item) => {
    e.stopPropagation();
    this.setState({ type: 'edit', dataInfo: item }, () => this['AddJcInfo'].showModal());
  };

  // 转为历史任职
  end = (item, key) => {
    this['EndOfTermRef'].open({ ...item, key });
  };

  //=============================================

  getList = async (p = {}) => {
    const { unit: { basicInfo: { code: unitCode = '' } = {} } = {}, pageType = '' } = this.props;
    const {
      code = 500,
      data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await residentList({
      pageSize: this.state.pagination.pageSize,
      pageNum: this.state.pagination.page,
      unitCode,
      // type: pageType,
      leave: Number(this.state.activeKey),
      ...p,
    });
    if (code === 0) {
      this.setState({
        list,
        pagination: { page: pageNum, total, pageSize },
      })
    }
  };
  // tab切换
  tabChange=(e='0')=>{
    this.setState({activeKey: e, list:[]})
    this.getList({leave:Number(e)})
  }

  componentDidMount() {
    this.getList();
  }

  render() {
    const { basicInfo: { code: unitCode = '', d04Code = '' } = {} } = this.props.unit;
    const { type, memInfo = {} } = this.state;

    let memTitleText = '驻村干部';
    const columns1 = [
      {
        title: '人员名称',
        dataIndex: 'memName',
        width: 50,
      },
      {
        title: '人员身份',
        dataIndex: 'd140Name',
        width: 50,
      },
      {
        title: '驻村开始时间',
        dataIndex: 'startDate',
        width: 80,
        render: (text) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
          return null;
        },
      },
      {
        title: '预计驻村结束时间',
        dataIndex: 'residentDate',
        width: 80,
        render: (text) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
          return null;
        },
      },
      {
        title: '驻村结束时间',
        dataIndex: 'endDate',
        width: 80,
        render: (text) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
          return null;
        },
      },
      {
        title: '派出单位名称及职务',
        dataIndex: 'dispatchPosition',
        width: 150,
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        render: (text, record) => {
          return (
            <span>
                {this.state.activeKey==='1' ? <Fragment>
                  <a onClick={async () => {
                  const { code = 500 } = await residentBackOut({ data: { id: record.id } });
                  if (code === 0) {
                    Tip.success('操作提示', '操作成功')
                    this.getList();
                  }
                }}>撤销</a>
                  </Fragment> : <Fragment>
                  <a onClick={()=>{this.addOrEdit(record)}}>编辑</a>
                <Divider type="vertical"/>
                  <a onClick={()=>{this.handleLeave(record)}}>离开</a>
                  <Divider type="vertical"/>
                <Popconfirm title="确定要删除吗？" onConfirm={async()=>{

                  const { code = 500 } = await residentDel({ data: { id: record['id'] } });
                  if (code === 0) {
                    Tip.success('操作提示', '操作成功');
                    this.getList();
                  }
                }}>
                 <a className={'del'}>删除</a>
                </Popconfirm>
                  </Fragment>}
              </span>
          );
        },
      },
    ];
    const columns2 = [
      {
        title: '人员名称',
        dataIndex: 'memName',
        width: 50,
      },
      {
        title: '人员身份',
        dataIndex: 'd140Name',
        width: 50,
      },
      {
        title: '驻村开始时间',
        dataIndex: 'startDate',
        width: 80,
        render: (text) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
          return null;
        },
      },
      {
        title: '预计驻村结束时间',
        dataIndex: 'residentDate',
        width: 80,
        render: (text) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
          return null;
        },
      },
      // {
      //   title: '驻村结束时间',
      //   dataIndex: 'endDate',
      //   width: 80,
      //   render: (text) => {
      //     if (text) {
      //       return moment(text).format('YYYY-MM-DD');
      //     }
      //     return null;
      //   },
      // },
      {
        title: '派出单位名称及职务',
        dataIndex: 'dispatchPosition',
        width: 150,
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        render: (text, record) => {
          return (
            <span>
                {this.state.activeKey==='1' ? <Fragment>
                  <a onClick={async () => {
                  const { code = 500 } = await residentBackOut({ data: { id: record.id } });
                  if (code === 0) {
                    Tip.success('操作提示', '操作成功')
                    this.getList();
                  }
                }}>撤销</a>
                  </Fragment> : <Fragment>
                  <a onClick={()=>{this.addOrEdit(record)}}>编辑</a>
                <Divider type="vertical"/>
                  <a onClick={()=>{this.handleLeave(record)}}>离开</a>
                  <Divider type="vertical"/>
                <Popconfirm title="确定要删除吗？" onConfirm={async()=>{

                  const { code = 500 } = await residentDel({ data: { id: record['id'] } });
                  if (code === 0) {
                    Tip.success('操作提示', '操作成功');
                    this.getList();
                  }
                }}>
                 <a className={'del'}>删除</a>
                </Popconfirm>
                  </Fragment>}
              </span>
          );
        },
      },
    ];
    return (
      <div style={{ padding: '0 20px' }}>
        <Tabs activeKey={this.state.activeKey} onChange={this.tabChange}>
          <TabPane tab="现任职表" key="0"/>
          <TabPane tab="历史任职表" key="1"/>
        </Tabs>
        {/* <History ref={e => this['historyRef'] = e} {...this.props} linkEdit={(memInfo, dataInfo) => {
          return (
            <React.Fragment>
              <h4>
                <a onClick={() => {
                  this.setState({ editMemTypeWay: 'history', dataInfo: dataInfo });
                  this.addOrEdit(memInfo, dataInfo.key);
                }} style={{ marginRight: 4 }}>编辑</a>
                <a onClick={async () => {
                  const { code = 500 } = await residentBackOut({ data: { id: memInfo.id } });
                  if (code === 0) {
                    Tip.success('操作提示', '操作成功')
                    this[`PanelTable_${dataInfo.key}`].getList();
                    this['historyRef'].getList();
                  }
                }}>撤销</a>
              </h4>
            </React.Fragment>
          )
        }} /> */}

        {/* <AddJcInfo
          {...this.props}
          type={type}
          title={type === 'edit' ? '编辑轮次信息' : '新增轮次信息'}
          wrappedComponentRef={(e) => this['AddJcInfo'] = e}
          onClose={() => this.setState({ dataInfo: undefined, type: undefined })}
          dataInfo={this.state.dataInfo}
          queryList={() => {
            this['CTbale'].getList()
          }}
        >
          <Button type="primary" icon={<PlusOutlined />} style={{ marginBottom: '10px' }}>添加轮次信息</Button>
        </AddJcInfo> */}
        {this.state.activeKey==='0' && <Button type="primary" onClick={()=>{this.addOrEdit({},'key1')}} icon={<PlusOutlined />} style={{ marginBottom: '10px' }}>添加个人信息</Button>}

        <AddMember
          title={memInfo['code'] ? `编辑${memTitleText}` : `新增${memTitleText}`}
          wrappedComponentRef={(e) => this['AddMember'] = e}
          onClose={() => this.setState({ dataInfo: undefined, type: undefined, memInfo: {} })}
          queryList={() => {
            this.getList()
          }}
          {...this.props}
          {...this.state}
        />
        <Leave
          title={'离开'}
          wrappedComponentRef={(e) => this['Leave'] = e}
          onClose={() => this.getList()}
          {...this.props}
          {...this.state}
        />

        {/* <EndOfTerm ref={e => this['EndOfTermRef'] = e}
          onOK={(e) => {
            this[`PanelTable_${e.key}`].getList();
          }} /> */}

        {/* <CTbale
          tableActionOtherQueries={{ unitCode: this.props.unit.basicInfo.code }}
          tableListAction={electList}
          ref={e => this['CTbale'] = e}
          mains={(item, index) => {
            return (
              <PanelTable
                key={index}
                ref={e => this[`PanelTable_${index}`] = e}
                data={item}
                mainsListAction={residentList}
                mainsActionOtherQueries={{ unitCode: this.props.unit.basicInfo.code, leave: 0, electCode: item['code'] }}
                add={() => {
                  return (
                    <React.Fragment>
                      <div className={styles.add} onClick={() => {
                        this.setState({
                          // 届次信息
                          dataInfo: item,
                          editMemTypeWay: 'default'
                        });
                        this['AddMember'].showModal({ key: index });
                      }}>
                        <PlusOutlined style={{ fontSize: '50px', transform: 'translateY(100%)' }} />
                      </div>
                    </React.Fragment>
                  )
                }}
                linkEdit={(items) => {
                  return (
                    <React.Fragment key={index}>
                      <a onClick={() => {
                        this.setState({ editMemTypeWay: 'default', dataInfo: item })
                        this.addOrEdit(items, index);
                      }}>编辑</a>
                      <a onClick={() => this.end(items, index)}>转为历史任职</a>
                      <Popconfirm title="是否删除?" onConfirm={async () => {
                        const { code = 500 } = await residentDel({ data: { id: items['id'] } });
                        if (code === 0) {
                          Tip.success('操作提示', '操作成功');
                          this[`PanelTable_${index}`].getList();
                        }
                      }} okText="是" cancelText="否">
                        <a onClick={e => e.stopPropagation()}>删除</a>
                      </Popconfirm>
                    </React.Fragment>
                  )
                }} />
            )
          }}
          panelHeader={(item, key) => {
            return (
              <span className={styles.header}>
                <span>轮次日期：{item['tenureStartDate'] && moment(item['tenureStartDate']).format('YYYY-MM-DD')}~{item['tenureEndDate'] && moment(item['tenureEndDate']).format('YYYY-MM-DD')}</span>
                <span>选举方式：{item['electName']}</span>
                <div>
                  <a onClick={(e) => {
                    e.stopPropagation();
                    this['historyRef'].open(item, key);
                  }}>轮次内历史任职</a>
                  <a onClick={(e) => this.editJc(e, item)}>编辑</a>
                  <div style={{ display: 'inline-block' }} onClick={(e: any) => e.stopPropagation()}>
                    <Popconfirm title="是否删除该信息?" onConfirm={(e) => this.del(e, item)} okText="是" cancelText="否" onCancel={(e: any) => e.stopPropagation()}>
                      <a className={'del'} onClick={(e) => e.stopPropagation()}>删除</a>
                    </Popconfirm>
                  </div>
                </div>
              </span>
            )
          }} /> */}
          <ListTable columns={this.state.activeKey === '0' ? columns2 : columns1}
            rowKey='id'
            data={this.state.list}
            pagination={this.state.pagination}
            onPageChange={(page: any, pageSize: any) => {
              this.getList({ pageNum: page, pageSize });
            }}/>
      </div>
    );
  }
}
