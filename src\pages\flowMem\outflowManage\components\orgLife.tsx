// 业务操作-组织生活情况
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Modal, Select } from 'antd';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import DictSelect from '@/components/DictSelect';
import Date from '@/components/Date';
import Tip from '@/components/Tip';
import { inManageOperate } from '../../service/index';

const FormItem = Form.Item;

class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      memInfo: {},
      visible: false,
      timeKey: moment().valueOf(),
      confirmLoading: false,
    };
  }
  handleOk = () => {
    const { onOk } = this.props;
    const { code, inOrgLifeName, inOrgLifeCode } = this.state.memInfo;
    const { modalType = '' } = this.state;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if (typeof val['inOrgLifeCode'] === 'object') {
          val.inOrgLifeName = val.inOrgLifeCode.name;
          val.inOrgLifeCode = val.inOrgLifeCode.key;
        } else {
          val.inOrgLifeName = inOrgLifeName;
          val.inOrgLifeCode = inOrgLifeCode;
        }
        let url: any = undefined;
        if (modalType === 'inFlow') {
          url = inManageOperate;
        }
        if (url) {
          this.setState(
            {
              confirmLoading: true,
            },
            async () => {
              const res = await url({ data: { code, ...val } });
              this.setState({
                confirmLoading: false,
              });
              if (res.code === 0) {
                this.handleCancel();
                Tip.success('操作提示', '操作成功');
                onOk && onOk();
              }
            },
          );
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  open = (type: string, record) => {
    this.setState({ visible: true, memInfo: record, modalType: type, timeKey: moment().valueOf() });
  };
  destroy = () => {
    this.setState({
      memInfo: {},
      modalType: '',
    });
  };
  render() {
    const { form } = this.props;
    const { getFieldDecorator } = form;
    const { visible, confirmLoading, memInfo } = this.state;
    return (
      <Modal
        destroyOnClose
        title="参与组织生活情况"
        visible={visible}
        onOk={this.handleOk}
        maskClosable={false}
        onCancel={this.handleCancel}
        width={500}
        confirmLoading={confirmLoading}
      >
        {visible && (
          <Fragment key={this.state.timeKey}>
            <Form labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
              <FormItem label="请选择参与组织生活情况">
                {getFieldDecorator('inOrgLifeCode', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['inOrgLifeCode'],
                  rules: [{ required: true, message: '请选择参与组织生活情况' }],
                })(
                  <DictSelect
                    initValue={_isEmpty(memInfo) ? undefined : memInfo.inOrgLifeCode}
                    codeType={'dict_d152'}
                    backType="object"
                  />,
                )}
              </FormItem>
              <FormItem label="" colon={false}>
                {getFieldDecorator('inOrgLife', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['inOrgLife'],
                  rules: [{ required: true, message: '请描述参与组织生活情况' }],
                })(
                  <Input.TextArea
                    placeholder="请描述参与组织生活情况"
                    rows={4}
                    maxLength={100}
                    showCount
                  />,
                )}
              </FormItem>
            </Form>
          </Fragment>
        )}
      </Modal>
    );
  }
}
export default Form.create()(index);
