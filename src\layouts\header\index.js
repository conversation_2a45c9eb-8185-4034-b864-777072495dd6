import React from 'react'
import {Menu} from "antd";
import {history} from "umi";
const temp=[
  {
    name:'报表配置', code:'set_1', url:'/text',
  },
  {
    name:'校核配置', code:'set_2', url:'/text/dxg',
  },
  {
    name:'基础配置', code:'set_3', url:'/setting/basic',
  },
  {
    name:'字典配置', code:'set_4', url:'/setting/dict',
  },
  {
    name:'逻辑配置', code:'set_5', url:'/setting/logic',
  },
];
export default class index extends React.Component{
  constructor(props){
    super(props);
    const {menuKey}=sessionStorage;
    this.state={
      selectedKeys:[menuKey]
    }
  }
  onSelect=(item)=>{
    sessionStorage.setItem('menuKey',item['key']);
    const {headerChange}=this.props;
    this.setState({
      selectedKeys:item['keyPath']
    });
    headerChange && headerChange({key:item['key'],keyPath:item['keyPath'],});
    const {allData}=this.props;
    const child=allData.filter(obj=>obj['parent']===item['key']);
    if(child.length>0){
      let obj=child[0];
      sessionStorage.setItem('menuItemKey',obj['code']);
      history.push(obj['url']);
    }else{
      const find=[...allData,...temp].find(obj=>obj['code']===item['key']) || {};
      if(find && find['url']==='/home'){
        sessionStorage.setItem('menuItemKey',undefined);
        history.push(find['url']);
      }else{
        history.push(find['url']);
      }
    }
  };
  render() {
    const {selectedKeys}=this.state;
    const {data}=this.props;
    const {account}=sessionStorage;
    return(
      <Menu
        theme="dark"
        mode="horizontal"
        selectedKeys={selectedKeys}
        onSelect={this.onSelect}
        style={{ lineHeight: '63px',display:'inline-block' }}
      >
        {
          data && data.map((obj,index)=>{
            if((account==='admin' || account==='gys001')  && obj['code']==='26'){
              return (
                <React.Fragment key={index}>
                  {
                    temp.map(obj=><Menu.Item key={obj['code']}>{obj['name']}</Menu.Item>)
                  }
                  <Menu.Item key={obj['code']}>{obj['name']}</Menu.Item>
                </React.Fragment>
              )
            }
            return(
              <Menu.Item key={obj['code']}>{obj['name']}</Menu.Item>
            )
          })
        }
      </Menu>
    )
  }
}
