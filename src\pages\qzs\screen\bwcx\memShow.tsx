import React, { Fragment, useEffect, useRef, useState } from 'react';
import { Button } from 'antd';
import st from './memShow.less';
import { getSession } from '@/utils/session';
import { archivesSave, archivesFind, pullFile, pullFileQz, getImgUrl } from '@/services';
import { findByCode } from '../services';
import _isEmpty from 'lodash/isEmpty';
import ReactSeamlessScroll from 'rc-seamless-scroll';
import { getInitFileList } from '@/components/UploadComp';
import _cloneDeep from 'lodash/cloneDeep';
import { Skeleton } from 'antd';
import moment from 'moment';
import { changeOrgName2 } from '../utils/tool';

function adjustArray(a, m) {
  if (_isEmpty(a)) return a;
  const n = a.length;
  if (n < m) {
    // 补全数组长度到 m
    for (let i = n; i < m; i++) {
      a[i] = a[i % n];
    }
  } else if (n > m) {
    // 截取到 m 位长度
    a.splice(m, n - m);
  }
  return a;
}

const memShow = () => {
  const sessionInfo: any = getSession('bwcx_info');

  const ref = useRef();
  const audioRef = useRef<HTMLAudioElement>(null);
  const [info, setInfo] = useState<any>({});
  const [img, setImg] = useState<any>();
  const [url, setUrl] = useState<any>();
  const [text, setText] = useState<any>();
  const [fileList, setFileList] = useState<any>([]);
  const [selectText, setSelectText] = useState<any>('');
  const [imgDefualt, setImgDefualt] = useState<any>(false);
  const [playMusic, setPlayMusic] = useState(false);

  const getInfo = async (record) => {
    const res = await findByCode({ memCode: record.code });
    if (res.code == 0) {
      const {
        data: {
          archivesPath = [],
          archives = '',
          sutra = '',
          sutraPath = '',
          politicalLifes = [],
        } = {},
      } = res;

      setInfo(res.data);

      if (_isEmpty(res.data)) return;

      if (_isEmpty(res.data.photo)) {
        setImg(require('../../../../assets/head.jpg'));
      } else {
        try {
          pullFileQz({ path: res.data.photo }).then((base64) => {
            setImg(base64);
          });
        } catch (error) {
          console.log('🚀 ~ getList ~ error:', error);
        }
      }

      // 档案原件
      try {
        if (archivesPath?.[0]) {
          pullFileQz({ path: archivesPath?.[0] }).then((file_base64) => {
            setUrl(file_base64);
          });
        } else {
          setImgDefualt(true);
          // setUrl(require('../../../../assets/qzs/defaultBook.jpg'));
        }
      } catch (e) {
        console.log('🚀 ~ getInfo ~ e:', e, '档案原件');
      }

      // // 档案(扫描内容)
      // setText(archives);

      // 经典一句话
      setSelectText(sutra);

      // // 经典一句话截图
      // const sutra_base64 = await pullFile({ path: sutraPath });
      // setJTbase64(sutra_base64);

      // 政治生活照
      try {
        // let photos = getInitFileList(politicalLifes.join(','));
        // for (let i = 0; i < politicalLifes.length; i++) {
        //   const item = photos[i];
        //   const base64 = await pullFile({ path: item.url });
        //   photos[i].thumbUrl = base64;
        //   photos[i].id = +new Date() + i;
        //   photos[i].url = base64;
        // }
        // setFileList(photos);

        let photos = getInitFileList(politicalLifes.join(','));
        let arr = photos.map((item, i) => {
          return () => pullFileQz({ path: item.url });
        });
        const res = await Promise.all(arr.map((it) => it()));
        photos = photos.map((it, index) => {
          return {
            ...it,
            thumbUrl: res[index],
            id: +new Date() + index,
            url: it.url,
          };
        });
        setFileList(photos);
      } catch (e) {
        console.log('🚀 ~ getInfo ~ e:', e, '政治生活照');
      }
    }
  };

  const finList = adjustArray(_cloneDeep(fileList), 10);

  const arr = new Array(10).fill('');

  useEffect(() => {
    getInfo(sessionInfo);
  }, [sessionInfo.code]);

  // 背景音乐
  // useEffect(() => {
  //   if (playMusic && audioRef.current) {
  //     console.log('play music==');
  //     audioRef.current.play().catch(error => {
  //       console.error('Failed to play audio:', error);
  //     });
  //   }
  // }, [playMusic]);

  // useEffect(() => {
  //   const handleUserInteraction = () => {
  //     setPlayMusic(true);
  //   };
  //   window.addEventListener('click', handleUserInteraction);
  //   window.addEventListener('touchstart', handleUserInteraction);

  //   return () => {
  //     if (audioRef.current) {
  //       audioRef.current.pause();
  //     }
  //     window.removeEventListener('click', handleUserInteraction);
  //     window.removeEventListener('touchstart', handleUserInteraction);
  //   };
  // }, [])

  return (
    <div className={st.memShow}>
      <div className={st.center}>
        <img className={st.tit} src={require('../../../../assets/qzs/bwcxtit.png')} alt="" />
        <div className={st.card}>
          <audio
            src="/bgm.mp3"
            ref={audioRef}
            autoPlay
            muted
            loop
            style={{ display: 'none' }}
          ></audio>
          <div className={st.left}>
            <img src={img} alt="" />
            <div className={st.name}>{sessionInfo.name}</div>
            <div className={st.shengri}>
              <div>政治生日</div>：
              <div>{moment(sessionInfo.joinOrgDate).format('YYYY年M月D日')}</div>
            </div>
            <div className={st.jieshaoren}>
              {/* <div>所在党支部</div>：<div>{changeOrgName(info.orgName)}</div> */}
              <div>所在党支部</div>：<div>{changeOrgName2(info.orgName)}</div>
            </div>
            {/* <div className={st.jieshaoren}><span>入党介绍人</span>：张勇、李强</div> */}
            {/* <div className={st.desc}>{selectText}</div> */}
            <div className={st.line}></div>
          </div>
          <div className={st.right}>
            {!imgDefualt ? (
              <img src={url} alt="" />
            ) : (
              <Fragment>
                <img src={require('../../../../assets/qzs/defaultBook.jpg')} alt="" />
                <div className={st.defaultName}>{sessionInfo.name}</div>
              </Fragment>
            )}
          </div>
        </div>
      </div>
      <div className={st.scroll}>
        <div className={st.stk}>
          <Skeleton loading={_isEmpty(finList)} active></Skeleton>
        </div>
        <ReactSeamlessScroll
          list={arr}
          ref={ref}
          hover={true}
          isWatch={true}
          direction={'left'}
          wrapperHeight={200}
          step={0.2}
          // singleWidth={145}
        >
          {arr.map?.((its, index) => {
            const it = finList?.[index];
            if (_isEmpty(it)) {
              return (
                <div style={{ visibility: 'hidden' }} className={st.scorllItem}>
                  {index}
                </div>
              );
            }
            return (
              <React.Fragment key={index}>
                <div className={st.scorllItem}>
                  <img className={st.scorllItemph} src={it?.thumbUrl} alt="" />
                  <img
                    className={st.scorllItembg}
                    src={require('../../../../assets/qzs/midboticon.png')}
                  />
                </div>
              </React.Fragment>
            );
          })}
        </ReactSeamlessScroll>
      </div>
    </div>
  );
};

export default memShow;
