import request from "@/utils/request";
import qs from 'qs';

export function tableSelect(params) {
  return request(`/api/table/tableSelect?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function tableFind(params) {
  return request(`/api/table/tableFind?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function tableAll(params) {
  return request(`/api/table/tableAll?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

export function combined(params) {
  return request(`/api/search/combined`, {
    method: 'POST',
    body: params,
  });
}

export function searchMem(params) {
  console.log('查询params==',params);
  
  return request(`/api/search/searchMem`, {
    method: 'POST',
    body: params,
  });
}

// 流动党员信息查询
export function searchFlowMem(params) {
  console.log('流动党员信息查询params==',params);
  
  return request(`/api/mem/flow/analysisQuery`, {
    method: 'POST',
    body: params,
  });
}

//入党申请人、积极分子、发展对象数据查询

export function dataQuery(params) {
  console.log('流动党员信息查询params==',params);
  
  return request(`/api/mem/develop/dataQuery`, {
    method: 'POST',
    body: params,
  });
}

export function tableSelect1(params) {
  return request(`/api/table/query/tableSelect?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function tableAll1(params) {
  return request(`/api/table/query/tableAll?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
