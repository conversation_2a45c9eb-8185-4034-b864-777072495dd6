/**
 * 考核信息
 **/
import React from 'react';
import ListTable from '@/components/ListTable';
import { DeleteOutlined, EditOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Col, DatePicker, Divider, message, Modal, Row, Popconfirm } from 'antd';
import { connect } from 'dva';
import { formLabel, unixMoment, uuid, fileDownloadHeader } from '@/utils/method';
import Dates from '@/components/Date';
import { addOrUpdateAssess, getListAssess, deleteAssess } from '@/pages/org/services/org';
import UploadComp, { getInitFileList } from '@/components/UploadComp';
import _isEmpty from 'lodash/isEmpty';
import _cloneDeep from 'lodash/cloneDeep'
import _get from 'lodash/get';
import _last from 'lodash/last';
import Tip from '@/components/Tip';
import moment from 'moment';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
// @ts-ignore
@connect(({ user, login }) => ({
  user,
  login
}))
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      edit: {}, // 回显内容
      visible: false,
      list: [],
      pagination: {}
    }
  }
  componentDidMount = () => {
    this.action({ pageNum: 1 });
  };
  action = async (p = {}) => {
    const { org: { basicInfo: { code: orgCode = '' } = {} } = {} } = this.props;
    const { code = 500, data: { list = [], pageNumber = 1, pageSize = 10, totalRow = 0 } = {} } = await getListAssess({
      data: {
        pageNum: 1,
        pageSize: 10,
        orgCode,
        ...p
      }
    });
    if (code == 0) {
      this.setState({
        list,
        pagination: { pageSize, total: totalRow, page: pageNumber, current: pageNumber }
      })
    }
  };
  edit = (record) => {
    this.setState({
      edit: record,
      visible: true,
      title: '编辑'
    })
  };
  suitFile = (key, value) => {
    let val = _cloneDeep(value);
    if (!_isEmpty(val[key])) {
      let temp: any = [];
      val[key].forEach(obj => {
        if (obj['response'] && obj['response']['code'] == '0') {
          temp.push(_get(obj, 'response.data[0].url', ''))
        } else {
          temp.push(obj.url)
        }
      })
      val[key] = temp.toString();
    } else {
      val[key] = undefined;
    }
    return val;
  };
  handleOk = async () => {
    const { org: { basicInfo: { code: orgCode = '' } = {} } = {} } = this.props;
    const { edit } = this.state;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        val = unixMoment(['inspectionTime'], val);
        val = this.suitFile('application', val);
        val = this.suitFile('embodiment', val);
        val = this.suitFile('activitiesReport', val);
        val = this.suitFile('commentSpeech', val);
        val = this.suitFile('rectification', val);

        const { code = 500 } = await addOrUpdateAssess({
          data: {
            code: _get(edit, 'code', undefined),
            orgCode,
            ...val
          }
        });
        if (code === 0) {
          Tip.success('操作提示', '操作成功');
          this.handleCancel();
          this.action({ pageNum: 1 });
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false
    })
  };

  getInitFileList = (val) => {
    let _name = '';
    if (!_isEmpty(val)) {
      _name = _last(val.split('\\') || []);
      return [
        { name: _name, url: val, uid: uuid() }
      ]
    } else {
      return []
    }
  };
  openModal = () => {
    this.setState({
      key: +new Date(),
      edit: {},
      visible: true,
      title: '新增'
    })
  }
  renderTableFile = (text) => {
    return <a onClick={() => {
      // fileDownload(`/api${text}`)
      fileDownloadHeader(`/api${text}`, 'file.docx');

    }}>{text ? _last(text.split('\\') || '') : ''}</a>
  }
  render(): React.ReactNode {
    const { type, title, list, pagination, edit = {} } = this.state;
    const { getFieldDecorator } = this.props.form;
    const { tipMsg = {} } = this.props;
    const columns = [
      {
        title: '考核时间',
        dataIndex: 'inspectionTime',
        render: (text) => {
          return text ? moment(text).format('YYYY.MM.DD') : ''
        }
      },
      {
        title: '实施方案',
        dataIndex: 'embodiment',
        render: (text) => {
          return this.renderTableFile(text)
        }
      },
      {
        title: '述职报告',
        dataIndex: 'activitiesReport',
        render: (text) => {
          return this.renderTableFile(text)
        }
      },
      {
        title: '点评讲话',
        dataIndex: 'commentSpeech',
        render: (text) => {
          return this.renderTableFile(text)
        }
      },
      {
        title: '测评考核结果及运用情况',
        dataIndex: 'application',
        render: (text) => {
          return this.renderTableFile(text)
        }
      },
      {
        title: '整改情况',
        dataIndex: 'rectification',
        render: (text) => {
          return this.renderTableFile(text)
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 180,
        render: (text, record) => {
          return (
            <span>
              <a onClick={() => this.edit(record)}><EditOutlined />编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="确定要删除吗？" onConfirm={async () => {
                const { code = 500 } = await deleteAssess({ code: record.code });
                if (code === 0) {
                  Tip.success('操作提示', '删除成功');
                  this.action({ pageNum: 1 });
                }
              }}>
                <DeleteOutlined style={{ color: 'red' }} /><a className={'del'}>删除</a>
              </Popconfirm>
            </span>
          );
        }
      },
    ];
    return (
      <React.Fragment>
        <Button type="primary" icon={<PlusOutlined />} style={{ margin: 8 }} onClick={this.openModal}>新增考核信息</Button>
        <ListTable columns={columns} data={list} pagination={pagination} onPageChange={(page, pageSize) => {
          this.action({ pageNum: page, pageSize });
        }} />
        <Modal
          title={title || "请输入标题"}
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={900}
          className='assess_modal'
          maskClosable={false}
          destroyOnClose={true}
        >
          {
            this.state.visible &&
            <Form {...formItemLayout}>
              <Row>
                <Col span={12}>
                  <FormItem
                    label={formLabel('考核时间', tipMsg['inspectionTime'])}
                  >
                    {getFieldDecorator('inspectionTime', {
                      rules: [{ required: true, message: '考核时间' }],
                      initialValue: edit['inspectionTime'] ? moment(edit['inspectionTime']).valueOf() : undefined,
                    })(
                      <Dates />
                    )}
                  </FormItem>
                </Col>
                {/* <Col span={12}>
                  <FormItem
                    label={ formLabel('测评考核结果及运用情况',tipMsg['application']) }
                  >
                    {getFieldDecorator('application', {
                      rules: [{ required: true, message: '测评考核结果及运用情况' }],
                      initialValue:getInitFileList(edit['application']),
                    })(
                      <UploadComp maxLen={1} files={getInitFileList(edit['application'])}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('实施方案',tipMsg['embodiment']) }
                  >
                    {getFieldDecorator('embodiment', {
                      rules: [{ required: true, message: '实施方案' }],
                      initialValue:getInitFileList(edit['embodiment']),
                    })(
                      <UploadComp maxLen={1} files={getInitFileList(edit['embodiment'])}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('述职报告',tipMsg['activitiesReport']) }
                  >
                    {getFieldDecorator('activitiesReport', {
                      rules: [{ required: true, message: '述职报告' }],
                      initialValue:getInitFileList(edit['activitiesReport']),
                    })(
                      <UploadComp maxLen={1} files={getInitFileList(edit['activitiesReport'])}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('点评讲话',tipMsg['commentSpeech']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('commentSpeech', {
                      rules: [{ required: false, message: '点评讲话' }],
                      initialValue:getInitFileList(edit['commentSpeech']),
                    })(
                      <UploadComp maxLen={1} files={getInitFileList(edit['commentSpeech'])}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('整改情况',tipMsg['rectification']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('rectification', {
                      rules: [{ required: false, message: '整改情况' }],
                      initialValue:getInitFileList(edit['rectification']),
                    })(
                      <UploadComp maxLen={1} files={getInitFileList(edit['rectification'])}/>
                    )}
                  </FormItem>
                </Col> */}
              </Row>
            </Form>
          }

        </Modal>
      </React.Fragment>
    );
  }
}
export default Form.create<any>()(index);
