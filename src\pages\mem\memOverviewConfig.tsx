import React from 'react';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _ceil from 'lodash/ceil';
import _isNaN from 'lodash/isNaN';
const pathSymbols = {
  women: 'path://M692.288 506.048 636.544 303.552C628.928 280.64 602.048 240.32 556.928 238.4L467.392 238.4c-46.656 1.92-73.344 41.856-79.808 65.216l-55.68 202.304C319.936 550.144 371.072 567.36 383.616 525.888l49.792-186.624 14.016 0-85.632 327.808 80 0 0 246.336c0 44.608 60.608 44.608 60.608 0l0-246.336 18.944 0 0 246.336c0 44.608 58.688 44.608 58.688 0l0-246.336 82.24 0L574.784 339.264l15.872 0 49.856 186.624C652.8 568.32 703.616 550.144 692.288 506.048zM511.872 221.888c38.336 0 69.376-32.384 69.376-72.384 0-39.872-31.04-72.32-69.376-72.32s-69.312 32.448-69.312 72.32C442.56 189.504 473.536 221.888 511.872 221.888z',
  men: 'path://M500.840045 0.014c53.579783-0.691997 82.164667 25.284898 92.126627 60.441755 8.743965 30.841875-3.299987 66.980729-18.776924 80.392674-16.770932 14.533941-32.817867 25.226898-64.545738 25.231898h-15.257939c-12.41995-4.506982-23.101906-11.566953-32.859866-18.186926-7.082971-4.806981-28.079886-26.481893-24.647901-33.449865-4.95198-1.609993-10.336958-33.530864-7.043971-45.773814 6.229975-23.139906 21.899911-49.765798 41.079834-59.26776L500.840045 0.014z m106.799567 311.595737v642.548396c0 19.297922 1.635993 40.805835-5.869976 52.812786-6.147975 9.84496-17.26293 11.625953-28.754883 16.432933-4.291983 1.791993-15.262938-1.029996-17.596929-1.759992-6.394974-1.989992-9.737961-1.007996-14.67394-3.519986-23.508905-11.957952-17.606929-55.279776-17.606929-90.371634l-0.584998-342.691611c-6.437974-0.159999-16.041935-0.938996-20.536917 1.169995 0.133999 95.753612-1.179995 196.313204-1.179995 293.993809v84.497657c0.005 13.653945 2.632989 26.249894-2.33999 36.96485-7.949968 17.113931-33.299865 27.349889-56.92277 19.366922-10.659957-3.593985-19.329922-13.293946-22.300909-22.881907-2.659989-8.599965 0.203999-15.749936-1.759993-26.995891l-0.589998-125.577491-0.579997-533.987836c-5.293979 0.101-7.552969 1.221995-11.737953 2.342991v288.124832c-8.905964 12.714948-16.603933 37.151849-45.769814 28.749883-35.224857-10.139959-25.234898-71.802709-25.234898-116.189529V330.978659c0-66.922729-5.134979-110.255553 41.663831-132.031465 22.371909-10.407958 58.745762-7.629969 90.956632-7.629969h99.756595c25.703896 0 53.965781-0.389998 72.764705 4.109983 16.415933 3.932984 33.031866 16.121935 41.073834 28.756883 17.537929 27.536888 10.563957 111.428548 10.563957 157.262363v175.449289c0 33.643864 2.982988 61.412751-19.952919 72.174708-6.147975 2.885988-19.865919 2.35999-25.815895 0-25.269898-10.021959-22.892907-33.766863-22.886908-68.654722l-0.589997-248.804992H607.639612z',
};

export const cardConfig = [
  {
    key:'1001',
    value:{
      icon:'team',
      coverImg:require('@/components/CardsGroup/assets/mem/dangyuan.jpg'),
      iconColor:'#17C1C5',
      title:'党员总数',
      suffix:'人',
      action:'/api/chart/mem/getMemTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['d08Name'] === '党员总数'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['d08Name'] === '正式党员'){zNum = item['count']}
            if(item['d08Name'] === '预备党员'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>正式党员:{zNum}人</div>
        <div>预备党员:{yNum}人</div>
        </div>
      )
      }
    },
  },
  {
    key:'1002',
    value:{
      icon:'hourglass',
      coverImg:require('@/components/CardsGroup/assets/mem/nannv.jpg'),
      iconColor:'#6F79C1',
      title:'男性比例',
      decimals:2,
      suffix:'%',
      action:'/api/chart/mem/getSexRatioTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['sexName'] === '男'){
              num = item['percent'] ;
            }
          });
        }
        return num
      },
      content:(data)=>{
        let num = 0;
        if(!_isEmpty(data) && _isArray(data)) {
          data.forEach(item => {
            if (item['sexName'] === '女') {
              num = item['percent']
            }
          });
        }
        return(
          <div>
            <span style={{fontSize:"16px"}} >女性比例<span style={{fontSize:"24px"}}>{num}%</span></span>
        </div>
      )},
    },
  },
  {
    key:'1003',
    value:{
      icon:'minus-square-o',
      coverImg:require('@/components/CardsGroup/assets/mem/hanzu.jpg'),
      iconColor:'#00A0FF',
      title:'汉族',
      suffix:'人',
      action:'/api/chart/mem/getNationRatioTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['d06Name'] === '汉族'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['d06Name'] === '少数民族'){zNum = item['count']}
            if(item['d06Name'] === '少数民族比例'){yNum = item['percent']}
          })
        }
        return(
          <div>
            <div>少数民族{zNum}人</div>
        <div>少数民族占比{yNum}%</div>
        </div>
      )
      },
    },
  },
  {
    key:'1008',
    value:{
      icon:'user',
      coverImg:require('@/components/CardsGroup/assets/mem/kunnan.jpg'),
      iconColor:'#f3715c',
      title:'困难党员',
      suffix:'人',
      action:'/api/chart/mem/getMemDifficultTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['d08Name'] === '困难党员总数'){
              num =  item['count']
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['d08Name'] === '正式党员'){zNum = item['count']}
            if(item['d08Name'] === '预备党员'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>正式党员:{zNum}人</div>
        <div>预备党员:{yNum}人</div>
        </div>
      )}
    },
  },
  {
    key:'1009',
    value:{
      icon:'usergroup-add',
      coverImg:require('@/components/CardsGroup/assets/mem/duochong.jpg'),
      iconColor:'#00ae9d',
      title:'多重党员',
      suffix:'人',
      action:'/api/chart/mem/getManyMemTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['d08Name'] === '多重党员总数'){
              num = item['count']
            }
          })
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['d08Name'] === '正式党员'){zNum = item['count']}
            if(item['d08Name'] === '预备党员'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>正式党员:{zNum}人</div>
        <div>预备党员:{yNum}人</div>
        </div>
      )}
    },
  },
  {
    key:'1010',
    value:{
      icon:'solution',
      coverImg:require('@/components/CardsGroup/assets/mem/lishi.jpg'),
      iconColor:'#fdb933',
      title:'历史党员',
      suffix:'人',
      action:'/api/chart/mem/getMemHistoryTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['d08Name'] === '历史党员总数'){
              num = item['count']
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['d08Name'] === '正式党员'){zNum = item['count']}
            if(item['d08Name'] === '预备党员'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>正式党员:{zNum}人</div>
        <div>预备党员:{yNum}人</div>
        </div>
      )}
    },
  },
];
export const chartConfig = [
  {
    key:'1004', // 人员详情
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_renyuan.jpg'),
      action:'/api/chart/mem/getMemTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d08Name']);
            arr.push({
              name:item['d08Name'],
              value:item['count']
            })
          });
          arr = arr.filter(item=>item['name'] !== '党员总数');

        }
        return {
          title : {
            text: '人员详情',
            // subtext: '纯属虚构',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '人员信息',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
    }
  },
  {
    key:'1006', // 学历情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_xueli.jpg'),
      action:'/api/chart/mem/getEducationRatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d07Name']);
            arr.push({
              name:item['d07Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '学历情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series: [
            {
              name:'学历情况',
              type:'pie',
              center: ['50%', '60%'],
              radius: ['40%', '60%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '24',
                    fontWeight: 'bold'
                  }
                }
              },
              labelLine: {
                normal: {
                  show: true
                }
              },
              data:arr
            }
          ]
        }
      },
    }
  },
  {
    key:'1011', // 专科学历以上情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_zhuanke.jpg'),
      action:'/api/chart/mem/getDzTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d07Name']);
            arr.push({
              name:item['d07Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '专科学历以上情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series: [
            {
              name:'专科学历以上情况',
              type:'pie',
              center: ['50%', '60%'],
              radius: ['40%', '60%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '24',
                    fontWeight: 'bold'
                  }
                }
              },
              labelLine: {
                normal: {
                  show: true
                }
              },
              data:arr
            }
          ]
        }
      },
    }
  },
  {
    key:'1012', // 工作岗位分布情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_gongzuo.jpg'),
      action:'/api/chart/mem/getJobRatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d09Name']);
            arr.push({
              name:item['d09Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '工作岗位分布情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '工作岗位分布情况',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr.sort(function(a, b) {
                return a['value'] - b['value']
              }),
              roseType : 'area',
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ],
        }
      },
    }
  },
  {
    key:'1013', // 公有制单位所有分类情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_gongyouzhi.jpg'),
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d09Name']);
            arr.push({
              name:item['d09Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '公有制单位所有分类情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          // legend: {
          //   // orient: 'horizontal',
          //   // left: 'middle',
          //   top:'bottom',
          //   data: arrName
          // },
          series : [
            {
              name: '公有制单位所有分类情况',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr.sort(function(a, b) {
                return a['value'] - b['value']
              }),
              roseType : 'area',
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ],
        }
      },
      action:'/api/chart/mem/getGyJobRatioTotal',
    }
  },
  {
    key:'1014', // 非公有制单位所有分类情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_feigong.jpg'),
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d09Name']);
            arr.push({
              name:item['d09Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '非公有制单位所有分类情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },

          // legend: {
          //   orient: 'vertical',
          //   left: 'right',
          //   top:'middle',
          //   data: arrName
          // },
          series : [
            {
              name: '非公有制单位所有分类情况',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr.sort(function(a, b) {
                return a['value'] - b['value']
              }),
              roseType : 'area',
              // itemStyle: {
              //   emphasis: {
              //     shadowBlur: 10,
              //     shadowOffsetX: 0,
              //     shadowColor: 'rgba(0, 0, 0, 0.5)'
              //   }
              // },
            }
          ],
        }
      },
      action:'/api/chart/mem/getFgyJobRatioTotal',
    }
  },
  {
    key:'1015', // 社会组织所有分类情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_shehui.jpg'),
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d09Name']);
            arr.push({
              name:item['d09Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '社会组织所有分类情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '人员信息',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
      action:'/api/chart/mem/getShzzJobRatioTotal',
    }
  },
  {
    key:'1016', // 农牧渔民所有分类情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_nongmu.jpg'),
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d09Name']);
            arr.push({
              name:item['d09Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '农牧渔民所有分类情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '农牧渔民所有分类情况',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
      action:'/api/chart/mem/getNmymJobRatioTotal1',
    }
  },
  {
    key:'1017', // 党员年龄分布
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_nianling.jpg'),
      action:'/api/chart/mem/getAgeRatioTotal',
      option:(val)=>{
        let arrName:Array<string> = [];
        let arrValue:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['ageGroup']);
            arrValue.push(item['count']);
          });
        }
        return {
          title : {
            text: '党员年龄分布',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arrValue,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'1018', // 党员党龄分布
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_dangling.jpg'),
      action:'/api/chart/mem/getPartyAgeRatioTotal',
      option:(val)=>{
        let arrName:Array<string> = [];
        let arrValue:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['partyAgeGroup']);
            arrValue.push(item['count']);
          });
        }
        return {
          title : {
            text: '党员党龄分布',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arrValue,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'1019',
    value:{
      coverImg:require('@/components/CardsGroup/assets/mem/chart_rudang.jpg'),
      action:'/api/chart/mem/getPartyAgeParagraphRatioTotal',
      option:(val)=>{
          let arrName:Array<string> = [];
          let arrValue:Array<string> = [];
          let arrPersent:Array<number> = [];
          let all = 0;
          if(!_isEmpty(val) && _isArray(val)){
            val.forEach(item=>{
              arrName.push(item['partyAgeParagraph']);
              arrValue.push(item['count']);
              all += item['count'];
            });
            val.forEach(item=>{
              arrPersent.push(_ceil(item['count']/all*100,2))
            })
          }
        return {
          title : {
            text: '党员入党时间分布',
            x:'left'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter:  function (items = []) {
              let num = 0;
              let numName = '';
              let numVal = 0;
              if(_isArray(items) && !_isEmpty(items)){
                const [item] = items;
                const {name = '',value = 0} = item || {};
                numName = name;
                numVal = _isNaN(value) ? 0 : value;
                if(!_isEmpty(val) && _isArray(val)){
                  val.forEach(item=>{
                    if(item['partyAgeParagraph'] === name){
                      num = item['count']
                    }
                  });
                }
              }
              return `${numName}:<br/> ${num}人 ${numVal}%`;
            }
          },
          grid: {
            left: '0%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            "axisLabel": {
              "interval": 0,
              formatter: '{value}%',
            }
          },
          yAxis: {
            type: 'category',
            data: arrName
          },
          series: [{
            name: '2016年占比',
            type: 'bar',
            data: arrPersent,
            itemStyle: {
              color: function(params) {
                let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                return colorList[params.dataIndex];
              }
            },
          }]
        }
      },
    }
  },
];
