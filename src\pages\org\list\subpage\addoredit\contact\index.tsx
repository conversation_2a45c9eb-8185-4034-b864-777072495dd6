/**
 * 党支部工作联系点
 */

import React, { Fragment, useRef, useState, useEffect } from 'react';
import { Button, Input, Select, Collapse, Space, Divider, Popconfirm, Row, Col, Card } from 'antd';
import { PlusOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import ListTable from '@/components/ListTable';
import ExportInfo from '@/components/Export/index';
import head from '@/assets/head.jpg';
import {
  contactMemList,
  deleteContactMem,
  contactWorkList,
  deleteContactWork,
} from '../../../../services';
import AddOrEditMem from './addOrEditMem';
import AddOrEditWork from './addOrEditWork';
const { Panel } = Collapse;
const { Search } = Input;

const index = (props: any) => {
  const { org: { basicInfo = {} } = {} } = props;
  const { code = '', orgCode = '' } = basicInfo;
  const addOrEditMemRef = useRef<any>();
  const addOrEditWorkRef = useRef<any>();
  const downloadRef: any = useRef();
  const [folding, setFolding] = useState(false);
  const [listLoading, setListLoading] = useState(false);
  const [pagination, setPagination] = useState<any>({
    pageNum: 1,
    pageSize: 20,
    total: 0,
    current: 1,
  });
  const [memList, setMemList] = useState([]);
  const [workList, setWorkList] = useState([]);
  const [exportLoading, setExportLoading] = useState(false);
  const [memName, setMemName] = useState<any>(undefined);
  const [year, setYear] = useState<any>(undefined);

  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 50,
      align: 'center',
      render: (text, record, index) => {
        return (pagination['current'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    {
      title: '支部联系人',
      width: 100,
      align: 'center',
      dataIndex: 'name',
      render: (text, record) => {
        return (
          <a
            onClick={() => {
              addOrEditWorkRef.current.open({ memList, record });
            }}
          >
            {text}
          </a>
        );
      },
    },
    {
      title: '工作类型',
      width: 100,
      align: 'center',
      dataIndex: 'd156Name',
    },
    {
      title: '开展时间',
      width: 100,
      align: 'center',
      dataIndex: 'startDate',
      render: (text) => {
        return moment(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '工作记录',
      width: 200,
      align: 'left',
      dataIndex: 'workRecord',
      render: (text) => {
        return (
          <div
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              display: '-webkit-box',
            }}
          >
            {text}
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      align: 'center',
      render: (text, record) => {
        return (
          <div>
            <a
              onClick={() => {
                addOrEditWorkRef.current.open({ memList, record });
              }}
            >
              编辑
            </a>
            <Divider type="vertical" />
            <Popconfirm
              title="确定要删除吗？"
              onConfirm={async () => {
                const { code = 500 } = await deleteContactWork({ code: record.code });
                if (code === 0) {
                  Tip.success('操作提示', '操作成功');
                  getWorkList({ pageNum: 1 });
                }
              }}
            >
              <a href={'#'} className={'del'}>
                删除
              </a>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const getMemList = async (p?: any) => {
    const { code: resCode = 500, data = [] } = await contactMemList({
      orgCode: code,
      ...p,
    });
    if (resCode === 0) {
      setMemList(data);
    }
  };
  const delMem = async (p: any) => {
    const { code: resCode = 500 } = await deleteContactMem({
      code: p?.code,
    });
    if (resCode == 0) {
      Tip.success('操作提示', '删除成功');
      getMemList();
    }
  };
  const getWorkList = async (p?: any) => {
    setListLoading(true);
    const {
      code: resCode = 500,
      data: { list = [], pageNumber = 1, pageSize = 10, totalPage = 0, totalRow = 0 } = {},
    } = await contactWorkList({
      data: {
        orgCode: code,
        pageNum: 1,
        pageSize: 10,
        year,
        name: memName,
        ...p,
      },
    });
    setListLoading(false);
    if (resCode === 0) {
      setWorkList(list);
      setPagination({ current: pageNumber, total: totalRow, pageSize, pageNum: pageNumber });
    }
  };

  const handleExport = async () => {
    setExportLoading(true);
    await downloadRef.current.submitNoModal();
    setExportLoading(false);
  };
  useEffect(() => {
    setMemName(undefined);
    setYear(undefined);
    getMemList();
    getWorkList();
  }, []);

  return (
    <div style={{ margin: '0 20px' }}>
      <Button
        style={{ marginBottom: '10px' }}
        icon={<PlusOutlined />}
        type="primary"
        onClick={() => {
          addOrEditMemRef.current.open();
        }}
      >
        添加党支部联系人
      </Button>
      <Collapse
        defaultActiveKey={['1']}
        onChange={() => {
          setFolding(!folding);
        }}
      >
        <Panel
          // extra={folding ? <UpOutlined /> : <DownOutlined />}
          // showArrow={false}
          header="党支部联系人"
          key="1"
        >
          <Row gutter={22}>
            {memList.map((item: any, index: any) => {
              return (
                <Col span={4} key={item?.code}>
                  <div
                    style={{
                      border: '1px solid #f0f0f0',
                      width: '100%',
                      borderRadius: '2px',
                      overflow: 'hidden',
                      marginBottom: '10px',
                    }}
                  >
                    <img style={{ width: '100%', height: 150 }} alt="头像" src={head} />
                    <div
                      style={{
                        width: '100%',
                        textAlign: 'center',
                        height: '30px',
                        lineHeight: '30px',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis',
                        overflow: 'hidden',
                      }}
                    >
                      {item.name}
                    </div>
                    <div
                      style={{
                        width: '100%',
                        textAlign: 'center',
                        height: '20px',
                        lineHeight: '20px',
                      }}
                    >
                      <a
                        style={{ margin: '0 10px' }}
                        onClick={() => {
                          addOrEditMemRef.current.open(item);
                        }}
                      >
                        编辑
                      </a>
                      <Popconfirm
                        title="确定要删除吗？"
                        onConfirm={() => {
                          delMem(item);
                        }}
                      >
                        <a
                          className={'del'}
                          style={{ margin: '0 10px' }}
                          // onClick={() => {
                          //   delMem(item);
                          // }}
                        >
                          删除
                        </a>
                      </Popconfirm>
                    </div>
                  </div>
                </Col>
              );
            })}
          </Row>
        </Panel>
      </Collapse>

      <Divider />
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Button
          style={{ marginBottom: '10px' }}
          icon={<PlusOutlined />}
          type="primary"
          onClick={() => {
            addOrEditWorkRef.current.open({ memList });
          }}
        >
          添加工作开展情况
        </Button>
        <div>
          <Space>
            <Button disabled={workList.length < 1} loading={exportLoading} onClick={handleExport}>
              导出
            </Button>
            <Search
              placeholder="请输入联系人姓名"
              onSearch={(e: any) => {
                getWorkList({ name: e, pageNum: 1 });
                setMemName(e);
              }}
            />
            <Select
              defaultValue={'all'}
              style={{ width: '100px' }}
              onChange={(e: any) => {
                console.log('e==', e);
                if (e === 'all') {
                  getWorkList({ pageNum: 1, year: undefined });
                  setYear(undefined);
                } else {
                  getWorkList({ pageNum: 1, year: e });
                  setYear(e);
                }
              }}
            >
              <Select.Option value={'2018'}>2018</Select.Option>
              <Select.Option value={'2019'}>2019</Select.Option>
              <Select.Option value={'2020'}>2020</Select.Option>
              <Select.Option value={'2021'}>2021</Select.Option>
              <Select.Option value={'2022'}>2022</Select.Option>
              <Select.Option value={'all'}>全部</Select.Option>
            </Select>
          </Space>
        </div>
      </div>
      <ListTable
        // scroll={{ y: filterHeight }}
        rowKey={'code'}
        
        columns={columns}
        data={workList}
        pagination={pagination}
        // showQuickJumper={true}
        onPageChange={(page: any, pageSize: any) => {
          getWorkList({ pageNum: page, pageSize });
        }}
      />
      <AddOrEditMem
        code={code}
        orgCode={orgCode}
        ref={addOrEditMemRef}
        onOk={() => {
          getMemList();
        }}
      />
      <AddOrEditWork
        code={code}
        orgCode={orgCode}
        ref={addOrEditWorkRef}
        onOk={() => {
          getWorkList({ pageNum: 1 });
        }}
      />
      <ExportInfo
        wrappedComponentRef={downloadRef}
        tableName={''}
        noModal={true}
        tableListQuery={{ orgCode: code, name: memName, year }}
        action={'/api/org/contactWork/outExportXlsx'}
      />
    </div>
  );
};

export default index;
