import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Row,
  Col,
  Input,
  Radio,
  DatePicker,
  Switch,
  Alert,
  Button,
  Popconfirm,
  Select,
  Modal,
  InputNumber,
  Upload,
} from 'antd';
import styles from './index.less';
import Notice from 'src/components/Notice';
import OrgSelect from '@/components/OrgSelect';
import DictTreeSelect from 'src/components/DictTreeSelect';
import DictSelect from 'src/components/DictSelect';
import Tip from '@/components/Tip';
import MemSelect from '@/components/MemSelect';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import {
  findDictCodeName,
  unixMoment,
  timeSort,
  getIdCardInfo,
  formLabel,
  correctIdcard,
} from '@/utils/method';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _iisArray from 'lodash/isArray';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import _isNumber from 'lodash/isNumber';
import _trim from 'lodash/trim';
import { getSession } from '@/utils/session';
import DictArea from '@/components/DictArea';
import Date from '@/components/Date';
import YN from '@/components/YesOrNoSelect';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import { getUnitByOrg } from '@/services';
import SearchUnit from '@/components/SearchUnit';
import { getUnitName, normalList } from '@/services';
import { validateLength } from '@/utils/formValidator';
import style from './index.less'

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const formItemLayout3 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      nowAge: undefined,
      hasLost: false,
      _basicInfo: {},
      modalVisible: false,
      d08Code: '',
      hasAppointment: false,
      isOutSystem_state: false,
      getUnitList: this.getUnitList,
      canEdit: false,
      uparr: [
        {
          id: '1',
          name: '入党申请人资料',
          formList: [],
          value: [
            {
              key: 'file',
              name: '入党申请书',
              fileList: []
            },
            {
              key: 'file1',
              name: '身份证或户口本复印件',
              fileList: []
            },
          ]
        },
        {
          id: '2',
          name: '谈话记录',
          formList: [],
          value: [
            {
              key: 'file',
              name: '党组织派人与入党申请人谈话的记录',
              fileList: []
            },
          ]
        },
        {
          id: '3',
          name: '确定积极分子',
          formList: [
            {
              label: '联系人是否为本组织人员',
              required: true,
              type: 'radio'
            },
            {
              label: '入党积极分子培养联系人',
              required: true,
              type: 'memselect'
            },
            {
              label: '确定积极分子时间',
              required: true,
              type: 'time'
            },
          ],
          value: [
            {
              key: 'file',
              name: '党员（群团组织）推荐入党积极分子登记表',
              fileList: []
            },
            {
              key: 'file1',
              name: '确定为入党积极分子的支委会会议记录、备案',
              fileList: []
            },
            {
              key: 'file2',
              name: '《入党积极分子、发展对象培养教育考察登记表》',
              fileList: []
            },
          ]
        },
        {
          id: '4',
          name: '第一次考察',
          formList: [
          ],
          value: [
            {
              key: 'file',
              name: '入党积极分子、发展对象培养教育考察登记表',
              fileList: []
            },
            {
              key: 'file1',
              name: '入党积极分子思想汇报',
              fileList: []
            },
          ]
        },
        {
          id: '5',
          name: '第二次考察',
          formList: [
          ],
          value: [
            {
              key: 'file',
              name: '入党积极分子、发展对象培养教育考察登记表',
              fileList: []
            },
            {
              key: 'file1',
              name: '入党积极分子思想汇报',
              fileList: []
            },
          ]
        },
        {
          id: '6',
          name: '持续考察',
          formList: [
          ],
          value: [
            {
              key: 'file',
              name: '入党积极分子、发展对象培养教育考察登记表',
              fileList: []
            },
            {
              key: 'file1',
              name: '入党积极分子思想汇报',
              fileList: []
            },
          ]
        },
        {
          id: '7',
          name: '上传材料',
          formList: [
          ],
          value: [
            {
              key: 'file',
              name: '入党积极分子、发展对象培养教育考察登记表',
              fileList: []
            },
            {
              key: 'file1',
              name: '入党积极分子思想汇报',
              fileList: []
            },
          ]
        },
        {
          id: '8',
          name: '上传材料',
          formList: [
            {
              label: '联系人是否为本组织人员',
              required: true,
              type: 'radio'
            },
            {
              label: '培养联系人',
              required: true,
              type: 'memselect'
            },
            {
              label: '确定积极分子时间',
              required: true,
              type: 'time'
            },
          ],
          value: [
            {
              key: 'file',
              name: '综合性政审报告和相关材料',
              fileList: []
            },
            {
              key: 'file1',
              name: '参加短期集中培训的结业证书',
              fileList: []
            },
            {
              key: 'file2',
              name: '发展党员工作有关部门征求意见情况（复印件）',
              fileList: []
            },
            {
              key: 'file3',
              name: '《入党积极分子、发展对象培养教育考察登记表》',
              fileList: []
            },
          ]
        },
      ],
      fileObj: {
        formList: [],
        value: []
      }
    };
  }
  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    const org = getSession('org') || {};
    const state = {};
    const { memDevelop: { basicInfo = {} } = {} } = nextProps;
    const {
      _basicInfo = {},
      getUnitList,
      editType,
      modalVisible,
      unitInformation,
      unitOrgLinkedList,
    } = prevState;

    if (!_isEqual(basicInfo?.orgCode, _basicInfo?.orgCode) && !editType && modalVisible) {
      state['_basicInfo'] = basicInfo;
      if (basicInfo?.orgCode) {
        getUnitList(basicInfo?.orgCode);
      }
    }
    if (!_isEqual(basicInfo?.id, _basicInfo?.id) && modalVisible) {
      state['_basicInfo'] = basicInfo;
      const {
        d08Code = '',
        d18Code = '',
        d19Code = '',
        isOutSystem,
        d48Code = '',
        d48Name = '',
      } = basicInfo;
      state['d08Code'] = d08Code;
      if (!_isEmpty(d18Code) && d18Code !== '0') {
        state['hasLost'] = true;
      } else {
        state['hasLost'] = false;
      }
      if (isOutSystem === 1) {
        state['isOutSystem_state'] = true;
      } else {
        state['isOutSystem_state'] = false;
      }
      if (!_isEmpty(d19Code) && d19Code !== '0') {
        state['hasAppointment'] = true;
      } else {
        state['hasAppointment'] = false;
      }
      if (d48Code) {
        state['area'] = d48Name;
      }
      getUnitList(basicInfo?.orgCode, basicInfo);
    }

    if (!_isEqual(basicInfo?.id, _basicInfo?.id) && modalVisible) {
      // 初始化 知识分子情况 不渲染的选项
      if (!_isEmpty(basicInfo['d154Code'])) {
        let arr = basicInfo['d154Code'].split(',');
        if (arr.includes('0')) {
          state['d154CodeNoDraw'] = ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B'];
        } else if (arr.includes('B')) {
          state['d154CodeNoDraw'] = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A'];
        } else if (
          ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A'].includes(arr[arr.length - 1])
        ) {
          state['d154CodeNoDraw'] = ['0', 'B'];
        } else {
          state['d154CodeNoDraw'] = [];
        }
      } else {
        state['d154CodeNoDraw'] = [];
      }
    }

    return { ...state, org };
  };
  getUnitList = async (orgCode, basicInfo = {}) => {
    const {
      code: resCode = 500,
      data: {
        d194Code = '',
        isLegal = '',
        unitOrgLinkedList = [],
        unitInformation = undefined,
        unitInformationCode = undefined,
        d04Code = undefined,
      } = {},
    } = await getUnitByOrg({ orgCode });
    if (resCode === 0) {
      this.props.form.setFieldsValue({
        readingCollege: basicInfo['readingCollege']
          ? basicInfo['readingCollege']
          : unitInformation
            ? unitInformation
            : _get(unitOrgLinkedList, '[0].unitName', undefined),
      });

      this.setState({
        unitInformation,
        unitList: unitOrgLinkedList,
        unitInformationCode,
        unitInformationD04Code: d04Code,
        isLegal: isLegal,
        unitInformationd194Code: d194Code,
      });
    }
  };
  d08CodeOnChange = (e) => {
    const { key = '' } = e;
    this.setState({ d08Code: key });
  };

  d18OnChange = (code) => {
    if (code && code['key'] !== '0') {
      this.setState({ hasLost: true });
    } else {
      this.setState({ hasLost: false });
      this.props.form.setFieldsValue({ lossDate: undefined });
    }
  };
  d19CodeOnChange = (code) => {
    if (code && code['key'] !== '0') {
      this.setState({ hasAppointment: true });
    } else {
      this.setState({ hasAppointment: false });
      this.props.form.setFieldsValue({ appointmentDate: undefined, appointmentEndDate: undefined });
    }
  };
  // 身份证
  getIDinfo = (e) => {
    const { target: { value = '' } = {} } = e || {};
    let info = getIdCardInfo(value);
    if (!_isEmpty(value) && info !== 'Error') {
      // 大陆身份证自动赋值，港澳台身份证不自动赋值
      if (`${value}`.length === 18 || `${value}`.length === 15) {
        this.setState({
          area: info[0],
        });
        this.props.form.setFieldsValue({
          sexCode: info[2] === '女' ? '0' : '1',
          birthday: info[1] ? moment(info[1], 'YYYY-MM-DD') : undefined,
          d48Code: info[3],
          politicsCode: undefined,
        });
        this['politicsCode'].clearAll();
      }
    }
  };
  validatorIdcard = async (rule, value, callback) => {
    if (_isEmpty(value)) {
      callback('身份证必填');
    }
    // if (value && value.length !== 18 && process.env.idCheck != 'false') {
    //   callback('身份证应该为18位');
    // }
    if (getIdCardInfo(value) === 'Error') {
      callback('身份证格式错误,请核对身份证图片');
    } else {
      // let fieldValue = this.props.form.getFieldValue('name');
      // let res =await geitCard({idCard:value,name:fieldValue});
      callback();
    }
  };

  // 姓名校验：不能有空格和·以外的字符
  nameValidator = (rule, value, callback) => {
    // let reg = /(^[\u4e00-\u9fa5]{1}[\u4e00-\u9fa5\.·。]{0,18}[\u4e00-\u9fa5]{1}$)|(^[a-zA-Z]{1}[a-zA-Z\s]{0,18}[a-zA-Z]{1}$)/
    let reg = /(^[\u4e00-\u9fa5]{1}[\u4e00-\u9fa5·]{0,18}[\u4e00-\u9fa5]{1}$)/;

    if (reg.test(value)) {
      // console.log('通过');
      validateLength([rule, value, callback], 16, 50);
      // callback();
    } else {
      callback('只能是汉字，不能有空格或特殊字符');
    }
  };

  // 时间限制
  disabledTomorrow = (current) => {
    return current && current > moment().endOf('day');
  };
  // 时间先后顺序判断
  TemporalOrder = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { applyDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr1 = [
        { text: '出生日期', value: value },
        { text: '申请入党时间', value: applyDate },
      ];
      if (timeSort(timeArr1, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder1 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { birthday = undefined, activeDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr1 = [
        { text: '出生日期', value: birthday },
        { text: '申请入党时间', value: value },
        { text: '确定积极分子时间', value: activeDate },
      ];
      if (timeSort(timeArr1, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder2 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { applyDate = undefined, objectDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr2 = [
        { text: '申请入党时间', value: applyDate },
        { text: '确定积极分子时间', value: value },
        { text: '确定发展对象时间', value: objectDate },
      ];
      if (timeSort(timeArr2, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder3 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { activeDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr2 = [
        { text: '确定积极分子时间', value: activeDate },
        { text: '确定发展对象时间', value: value },
      ];
      if (timeSort(timeArr2, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder4 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { appointmentDate = undefined, appointmentEndDate = undefined } =
        form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr2 = [
        { text: '聘任开始时间', value: appointmentDate },
        { text: '结束时间', value: appointmentEndDate },
      ];
      if (timeSort(timeArr2, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder5 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      // const {applyDate = undefined} = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '年龄须大于等于18岁';
      // const backFunc = (val) =>{
      //   text = val;
      // };
      // let timeArr1 = [
      //   {text: '出生日期',value:value},
      //   // {text: '申请入党时间',value:applyDate},
      // ];
      const yearsAgo = moment().subtract(18, 'years');
      if (value.isAfter(yearsAgo)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  // 保存
  submit = () => {
    console.log('保存')
    const { memDevelop: { basicInfo = {} } = {}, onsubmit } = this.props;
    this.setState({
      step: 2
    })
    // this.props.form.validateFieldsAndScroll(async (err, val) => {
    //   if (!err) {
    //     if (val['name'] != basicInfo['name'] || val['idcard'] != basicInfo['idcard']) {
    //       let result = await correctIdcard(val['name'], val['idcard']);
    //       if (result['code'] != '200') {
    //         this.props.form.setFields({
    //           idcard: {
    //             value: val['idcard'],
    //             errors: [
    //               new Error(
    //                 '经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。',
    //               ),
    //             ],
    //           },
    //         });
    //         Tip.error(
    //           '操作提示',
    //           '经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。',
    //         );
    //         return;
    //       } else {
    //         val['idCardReason'] = result['reason'];
    //         val['idCardReasonName'] = result['reasonName'];
    //       }
    //     }
    //     val = unixMoment(
    //       [
    //         'birthday',
    //         'applyDate',
    //         'activeDate',
    //         'objectDate',
    //         'appointmentDate',
    //         'appointmentEndDate',
    //         'lossDate',
    //         'joinWorkDate',
    //         'enterSchoolDate',
    //       ],
    //       val,
    //     );
    //     //bool改为int
    //     [
    //       'isHighKnowledge',
    //       'hasWorker',
    //       'hasYoungFarmers',
    //       'isAdvancedModel',
    //       'isDispatch',
    //       'isFarmer',
    //       'isOutSystem',
    //     ].map((item) => {
    //       val[`${item}`] = val[`${item}`] == 1 ? 1 : val[`${item}`] == 0 ? 0 : undefined;
    //     });
    //     // 增加字典表的name
    //     val = findDictCodeName(
    //       [
    //         'd49',
    //         'd18',
    //         'd19',
    //         'd20',
    //         'd21',
    //         'd07',
    //         'd09',
    //         'd11',
    //         'd27',
    //         'd28',
    //         'd126',
    //         'd04',
    //         'd194',
    //         'd195',
    //         'd06',
    //         'd08',
    //         'd60',
    //         'd88',
    //         'readingProfessional',
    //         'politics',
    //         'advancedModel',
    //         'jobNature',
    //         'joinOrg',
    //       ],
    //       val,
    //       basicInfo,
    //     );
    //     val['d08Name'] = basicInfo['d08Name'] ? basicInfo['d08Name'] : '入党申请人';

    //     val['sexName'] = val['sexCode'] === '1' ? '男' : '女';
    //     // 增加组织zbcode
    //     val['orgZbCode'] =
    //       typeof val['orgCode'] === 'object' ? val['orgCode'][0]['zbCode'] : basicInfo['orgZbCode'];
    //     val['developOrgCode'] =
    //       typeof val['orgCode'] === 'object'
    //         ? val['orgCode'][0]['orgCode']
    //         : basicInfo['developOrgCode'];
    //     val['orgName'] =
    //       typeof val['orgCode'] === 'object' ? val['orgCode'][0]['name'] : basicInfo['orgName'];
    //     val['orgCode'] =
    //       typeof val['orgCode'] === 'object' ? val['orgCode'][0]['code'] : basicInfo['orgCode'];

    //     if (val['isOutSystem'] === 0) {
    //       val['developAppliedOrgCode'] =
    //         typeof val['appliedOrgCode'] === 'object'
    //           ? val['appliedOrgCode'][0]['orgCode']
    //           : basicInfo['developAppliedOrgCode'];
    //       val['appliedOrgZbCode'] =
    //         typeof val['appliedOrgCode'] === 'object'
    //           ? val['appliedOrgCode'][0]['zbCode']
    //           : basicInfo['appliedOrgZbCode'];
    //       val['appliedOrgName'] =
    //         typeof val['appliedOrgCode'] === 'object'
    //           ? val['appliedOrgCode'][0]['name']
    //           : basicInfo['appliedOrgName'];
    //       val['appliedOrgCode'] =
    //         typeof val['appliedOrgCode'] === 'object'
    //           ? val['appliedOrgCode'][0]['code']
    //           : basicInfo['appliedOrgCode'];
    //     }

    //     //  知识分子情况 数组改为字符串
    //     if (!_isEmpty(val['d154Code'])) {
    //       if (typeof val['d154Code'] === 'object') {
    //         let nameArr: any = [];
    //         let codeArr: any = [];
    //         val['d154Code'].map((item: any, index) => {
    //           const { key = '', name = '' } = item;
    //           nameArr.push(name);
    //           codeArr.push(key);
    //         });
    //         val['d154Name'] = nameArr.toString();
    //         val['d154Code'] = codeArr.toString();
    //       } else {
    //         val['d154Name'] = basicInfo['d154Name'];
    //       }
    //     }

    //     // 中间交换区
    //     if (val['hasUnitStatistics'] == 1 && val['_d04Code']) {
    //       val['d04Code'] = this.state.unitInformationD04Code;
    //     }
    //     if (val['hasUnitStatistics'] == 1 && val['__d04Code']) {
    //       val['d04Code'] = val['__d04Code'];
    //     }
    //     val['middleUnitCode'] =
    //       typeof val['middleUnitCode'] == 'object'
    //         ? val['middleUnitCode']?.code
    //         : val['middleUnitCode'];
    //     if (val['middleUnitCode'] && val.d09Code != '13') {
    //       val['d194Code'] = val['_d194Code'];
    //       val['d194Name'] = val['_d194Name'];
    //       val['d195Code'] = val['_d195Code'];
    //       val['d195Name'] = val['_d195Name'];
    //     }
    //     if (!_isEmpty(val.d09Code)) {
    //       let _key = val.d09Code;
    //       //是否劳务派遣工默认为否
    //       if (
    //         !(
    //           `${_key}`.startsWith('016') ||
    //           `${_key}`.startsWith('025') ||
    //           `${_key}` == '0313' ||
    //           `${_key}` == '0323' ||
    //           `${_key}` == '0333'
    //         )
    //       ) {
    //         val['isDispatch'] = '0';
    //         val['isFarmer'] = '0';
    //       }
    //     }
    //     if (_isEmpty(val.d194Code)) {
    //       val.d194Code = '';
    //       val.d194Name = '';
    //     }
    //     if (_isEmpty(val.d195Code)) {
    //       val.d195Code = '';
    //       val.d195Name = '';
    //     }

    //     if (val['d195Code'] == 'V0000') {
    //       val['d195Name'] = '无';
    //     }

    //     const canEdit = this.showGUOMINGJINGJI();
    //     if (!canEdit) {
    //       val.d194Code = '';
    //       val.d194Name = '';
    //       val.d195Code = '';
    //       val.d195Name = '';
    //     }

    //     // let url = this.state.editType == 'benNian' ? 'memDevelop/saveYear' : 'memDevelop/save';
    //     // const res = await this.props.dispatch({
    //     //   type: url,
    //     //   payload: {
    //     //     data: { ...val },
    //     //     type: !basicInfo?.id ? 'add' : 'edit',
    //     //   },
    //     // });
    //     // // this.setState({
    //     // //   d194CodeKey:+moment().valueOf(),
    //     // //   d194CodeSatate:undefined,
    //     // //   d195CodeKey:+moment().valueOf(),
    //     // //   d195CodeSatate:undefined,
    //     // // },()=>{
    //     // //   this.props.form.setFieldsValue({
    //     // //     ...data
    //     // //   })
    //     // // })
    //     // const { code = 500, data = {} } = res;
    //     // if (code === 0) {
    //     //   Tip.success('操作提示', basicInfo['code'] ? '修改成功' : '新增成功');
    //     //   this.cancel();
    //     //   onsubmit && onsubmit({ editType: this.state.editType });
    //     // }
    //   }
    // });
  };
  cancel = () => {
    this.setState({ modalVisible: false, d154CodeNoDraw: [] });
    this.destroy();
    this.props.onClose && this.props.onClose();
  };
  open = (e) => {
    const { canEdit, editType = '', type, } = e;
    let arr = this.state.uparr.find(i => i.id == type)

    this.setState({
      modalVisible: true,
      canEdit,
      editType,
      type,
      fileObj: arr
    });
  };
  destroy = () => {
    this.setState({
      hasLost: false,
      _basicInfo: {},
      d08Code: '5',
      hasAppointment: false,
      isOutSystem_state: false,
    });
    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        basicInfo: {},
      },
    });
  };
  isOutSystemOnChange = (value) => {
    // this.props.form.setFieldsValue({
    //   appliedOrgCode:undefined,
    //   appliedOrgName:undefined
    // });
    this.setState({ isOutSystem_state: value == '1' });
  };
  validatorHomeAddress = (rule, value, callback) => {
    if (value && value.length < 8) {
      callback('家庭住址长度不能少于8字符');
    }
    callback();
  };
  showMemsInfo = () => {
    const { tipMsg = {}, memDevelop: { basicInfo = {} } = {} } = this.props;
    const { getFieldDecorator, getFieldValue } = this.props.form;
    return (
      <Fragment>
        <Row>
          <Col span={12}>
            <FormItem
              label={formLabel('入党介绍人', tipMsg['topreIntroductionMem'])}
              {...formItemLayout}
            >
              <Input value={basicInfo['topreIntroductionMem']} disabled={true} />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label={formLabel('支部党员大会讨论通过时间', tipMsg['topreJoinOrgDate'])}
              {...formItemLayout}
            >
              {/* <Input value={basicInfo['topreJoinOrgDate'] ? moment(basicInfo['topreJoinOrgDate']).format('YYYY.MM.DD') : undefined} disabled={true}/> */}
              {getFieldDecorator('topreJoinOrgDate', {
                rules: [{ required: true, message: '支部党员大会讨论通过时间' }],
                initialValue: basicInfo['topreJoinOrgDate'],
              })(<Date />)}
            </FormItem>
          </Col>

          <Col span={12}>
            {/* <FormItem
              label={formLabel('加入党组织方式', tipMsg['joinOrgCode'])}
              {...formItemLayout}
            >
              <Input value={basicInfo['joinOrgCodeName']} disabled={true}/>
            </FormItem> */}
            {/* 编辑发展党员，本年发展党员这里，加入中共组织的类别（名字改成加入党组织方式），改成必填，非灰色 */}
            <FormItem label="加入党组织方式" {...formItemLayout}>
              {getFieldDecorator('joinOrgCode', {
                rules: [{ required: true, message: '加入党组织方式' }],
                initialValue: basicInfo['joinOrgCode'],
              })(
                <DictTreeSelect
                  backType={'object'}
                  codeType={'dict_d27'}
                  placeholder="请选择"
                  // noDraw={noDrawArr}
                  parentDisable={true}
                  initValue={basicInfo['joinOrgCode']}
                // onChange={this.joinOrgChange}
                // filter={(data={})=>{
                //    if(specialChecked) {
                //     let item=[]
                //   item=item.concat(data.filter(obj=>['11'].includes(obj['key'])))
                //     return item
                //    }
                //     return data
                // }}
                />,
              )}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem label={formLabel('进入支部类型', tipMsg['d11Code'])} {...formItemLayout}>
              <Input value={basicInfo['d11Name']} disabled={true} />
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('入党志愿书编号', tipMsg['topreJoinBookNum'])}
              {...formItemLayout}
            >
              <Input value={basicInfo['topreJoinBookNum']} disabled={true} />
            </FormItem>
          </Col>
        </Row>
      </Fragment>
    );
  };
  d194Change = async (p) => {
    let newCode = p;
    if (typeof p === 'object') {
      newCode = p?.key || undefined;
    }
    const res = await normalList({
      data: {
        tableCode: 'ccp_unit',
        colCode: 'd194Code',
        compareColCode: 'd195Code',
        colValue: newCode,
      },
    });
    if (res.code == 0 && !_isEmpty(res.data)) {
      let key = Object.keys(res.data)?.[0];
      let name = res.data[key];
      this.setState({
        d195CodeSatate: key,
        d195CodeKey: moment().valueOf(),
      });
      this.props.form.setFieldsValue({
        d195Code: key,
        d195Name: name,
      });
    }
  };
  showGUOMINGJINGJI = () => {
    const { memDevelop: { basicInfo = {} } = {} } = this.props;
    // 人事关系是否在党组织关联单位内
    let val1 = this.props.form.getFieldValue('hasUnitStatistics');
    if (val1 == undefined) {
      val1 = basicInfo['hasUnitStatistics'];
    }
    // 人事关系所在单位是否省内单位
    let val = this.props.form.getFieldValue('hasUnitProvince');
    if (val == undefined) {
      val = basicInfo['hasUnitProvince'];
    }
    const hasChangedHasUnitStatistics =
      basicInfo['hasUnitStatistics'] != this.props.form.getFieldValue('hasUnitStatistics');
    const hasChangedHasUnitProvince =
      basicInfo['hasUnitProvince'] != this.props.form.getFieldValue('hasUnitProvince');
    let hasChangedStatisticalUnit = false;
    // 当第一个值（人事关系是否在党组织关联单位内）改变，直接隐藏
    let hasChanged = hasChangedHasUnitStatistics;
    // 当第一个值未变且为否 判断第二个值（人事关系所在单位是否省内单位）改变
    if (!hasChangedHasUnitStatistics && val1 == 0 && hasChangedHasUnitProvince) {
      hasChanged = true;
    }

    // 同“人事关系所在单位类别”显示逻辑一样
    let flag = val1 == 0 && val == 0;

    let unitInfo: any = {};
    // 有几种获取单位的情况
    // 1 人事关系是在党组织关联单位内
    if (val1 == 1) {
      //所在党支部
      let org = this.props.form.getFieldValue('orgCode');
      let d01Code: any = undefined;
      if (typeof org == 'string') {
        d01Code = basicInfo['d01Code'];
      } else {
        d01Code = _get(org, '[0].d01Code', undefined);
      }
      let isLianhe = (d01Code == '632' || d01Code == '932' || d01Code == '634') && d01Code;
      if (isLianhe) {
        //  1.2 是下拉框选择的单位
        let unitcode =
          this.props.form.getFieldValue('statisticalUnit') || basicInfo['statisticalUnit'];
        let find = this.state?.unitList?.find?.((it) => it.unitCode == unitcode);
        unitInfo = find;

        // 如果改变下拉框值，隐藏
        if (unitcode != basicInfo['statisticalUnit']) {
          hasChangedStatisticalUnit = true;
        }
      } else {
        //  1.1 只有一个单位
        unitInfo = {
          unitInformation: this.state.unitInformation,
          unitInformationCode: this.state.unitInformationCode,
          d04Code: this.state.unitInformationD04Code,
          isLegal: this.state.isLegal,
          d194Code: this.state.unitInformationd194Code,
        };
      }
    }

    // 2 中间交换区
    if (val1 == 0 && val == 1) {
      let code = this.props.form.getFieldValue('middleUnitCode');
      if (typeof code == 'string') {
        unitInfo = {
          middleUnitCode: this.props.form.getFieldValue('middleUnitCode'),
          middleUnitName: this.props.form.getFieldValue('middleUnitName'),
          d04Code: this.props.form.getFieldValue('d04Code'),
        };
      } else {
        unitInfo = code;
      }
    }

    //在加上额外判断条件
    // 1、当党员的党组织（单位属性为行政村、是法人单位、国民经济行业为村民自治组织或社区居民自治组织）三个条件同时满足
    // 2、当党员的党组织（单位属性为教育大类中331、是法人单位、国国民经济行业是普通高等教育）三个条件同时满足
    // 3、当党员的党组织（是教育大类中332、333、334、335；是法人单位、国国民经济行业是除普通高等教育以外的 中等教育，
    // 普通高中教育，初等教育，学前教育，）三个条件同时满足

    const d04 = unitInfo?.d04Code || '';
    const isLegal = unitInfo?.isLegal || 0;
    const d194Code = unitInfo?.d194Code || '';

    const find1 = (d04 == '923' || d04 == '922') && (d194Code == 'S9620' || d194Code == 'S9610');
    const find2 = d04?.startsWith('331') && d194Code.startsWith('P8341');
    const find3 =
      (d04?.startsWith('332') ||
        d04?.startsWith('333') ||
        d04?.startsWith('334') ||
        d04?.startsWith('335')) &&
      (d194Code.startsWith('P833') ||
        d194Code.startsWith('P8334') ||
        d194Code.startsWith('P832') ||
        d194Code.startsWith('P831'));

    let flag2 = find1 || find2 || find3;

    let finalFlag = flag2 || flag;

    // 如果有改变2个是否，且最后不是2个否
    if (hasChangedStatisticalUnit || hasChanged) {
      if (!flag) {
        finalFlag = false;
      }
    }

    // 增加判断工作岗位d09, 13-务工经商人员  国民经济一直显示可选
    const d09Code = this.props.form.getFieldValue('d09Code');
    let _keyd09 = typeof d09Code === 'string' ? d09Code : d09Code?.key;
    if (_keyd09 == '13') {
      finalFlag = true;
    }

    return finalFlag;
  };
  fileChange = ({ fileList, file, event }: any, item: any) => {
    const { onChange, children } = this.props;
    const { fileObj } = this.state;

    if (file.status === 'done') {
      const { response: { code = 500, message = '' } = {} } = file || {};
      if (code !== 0) {
        Tip.error('操作提示', message);
        fileList.pop();
      } else {
        Tip.success('操作提示', '上传成功');
      }
    } else if (file.status === 'error') {
      Tip.error('操作提示', '上传失败');
    }
    let find = fileObj.value.map(i => {
      if (i.key == item.key) {
        i.fileList = fileList
      }
      return i
    })
    this.setState({
      fileObj: find,
    });
  }
  delFile = () => {
    console.log('delFile')
  }
  getFormItem = (type: string) => {
    let node: any = null
    switch (type) {
      case 'radio':
        node = <Switch checkedChildren="是" unCheckedChildren="否" defaultChecked />
        break;
      case 'memselect':
        node = <MemSelect placeholder="请选择党员" />
        break;
      case 'time':
        node = <Date />
        break;
    }
    return node
  }
  render(): React.ReactNode {
    const {
      form,
      memDevelop: { basicInfo = {} } = {},
      loading: { effects = {} } = {},
      tipMsg = {},

    } = this.props;
    const { getFieldDecorator } = form;
    const {
      d08Code,
      hasAppointment,
      hasLost,
      area,
      modalVisible,
      isOutSystem_state,
      canEdit,
      d154CodeNoDraw = [],
      fileList = [],
      step = 1,
      fileObj
    } = this.state;
    const props: any = {
      action: '/api/base/upload',
      headers: {
        Authorization: sessionStorage.getItem('token') || '',
        dataApi: sessionStorage.getItem('dataApi') || '',
      },
    };

    return (
      <Fragment>
        <Modal
          title={
            !basicInfo?.id
              ? `新增${this.state.org.name}组织发展党员`
              : canEdit
                ? '编辑发展党员'
                : '发展党员详情'
          }
          destroyOnClose
          visible={modalVisible}
          onCancel={this.cancel}
          // onOk={this.submit}
          width={'1200px'}
          footer={
            canEdit
              ? [
                <Button onClick={this.cancel}>取消</Button>,
                <Button type="primary" onClick={this.submit}>
                  {step == 1 ? '下一步' : '保存'}
                </Button>,
              ]
              : null
          }
        // confirmLoading={effects['memAbroad/save']}
        >
          <div style={{ height: '600px', overflow: 'auto' }}>
            {
              step == 1 && <Row style={{ pointerEvents: canEdit ? 'auto' : 'none' }}>
                <Col span={12}>
                  <FormItem label={formLabel('人员姓名', tipMsg['name'])} {...formItemLayout}>
                    {getFieldDecorator('name', {
                      rules: [
                        { required: true, message: '请输人员姓名' },
                        { validator: this.nameValidator },
                      ],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['name'],
                    })(<Input placeholder={'请输人员姓名'} />)}
                  </FormItem>
                </Col>
                {getFieldDecorator('code', { initialValue: basicInfo['code'] })(
                  <div style={{ display: 'none' }}>123</div>,
                )}
                <Col span={12}>
                  <FormItem label={formLabel('性别', tipMsg['sexCode'])} {...formItemLayout}>
                    {getFieldDecorator('sexCode', {
                      rules: [{ required: true, message: '请选择性别' }],
                      initialValue: _isEmpty(basicInfo) ? '1' : basicInfo['sexCode'],
                    })(
                      <RadioGroup>
                        <Radio value={'1'}>男</Radio>
                        <Radio value={'0'}>女</Radio>
                      </RadioGroup>,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label={formLabel('身份证号', tipMsg['idcard'])} {...formItemLayout}>
                    {getFieldDecorator('idcard', {
                      rules: [
                        { required: true, message: '请输入身份证号' },
                        { validator: this.validatorIdcard },
                      ],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['idcard'],
                    })(<Input placeholder={'请输入身份证'} max={18} onBlur={this.getIDinfo} />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label={formLabel('出生日期', tipMsg['birthday'])} {...formItemLayout}>
                    {getFieldDecorator('birthday', {
                      rules: [
                        { required: true, message: '请选择出生日期' },
                        { validator: this.TemporalOrder5 },
                      ],
                      initialValue: _isEmpty(basicInfo) ? undefined : moment(basicInfo['birthday']),
                      // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
                    })(
                      <Date
                        disabledDate={this.disabledTomorrow}
                        disabled={!canEdit}
                        onChange={() => {
                          this.props.form.setFieldsValue({
                            politicsCode: undefined,
                          });
                          this['politicsCode'].clearAll();
                        }}
                      />,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label={formLabel('籍贯', tipMsg['d48Code'])} {...formItemLayout}>
                    {getFieldDecorator('d48Code', {
                      rules: [{ required: true, message: '请选择籍贯' }],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d48Code'],
                    })(
                      <DictArea
                        placeholder={'所在地区'}
                        onChange={(val, obj) => this.props.form.setFieldsValue({ d48Name: obj.name })}
                      />,
                    )}
                    {getFieldDecorator('d48Name')(<Input style={{ display: 'none' }} />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label={formLabel('民族', tipMsg['d06Code'])} {...formItemLayout}>
                    {getFieldDecorator('d06Code', {
                      rules: [{ required: true, message: '请选择民族' }],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d06Code'],
                    })(
                      <DictTreeSelect
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d06Code']}
                        codeType={'dict_d06'}
                        placeholder={'党员民族'}
                        parentDisable={true}
                        backType={'object'}
                      />,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label={formLabel('当前工作岗位', tipMsg['d09Code'])} {...formItemLayout}>
                    {getFieldDecorator('d09Code', {
                      rules: [{ required: true, message: '请选择当前工作岗位' }],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d09Code'],
                    })(
                      <DictTreeSelect
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d09Code']}
                        codeType={'dict_d09'}
                        placeholder={'当前工作岗位'}
                        // itemsDisabled={["11"]}
                        parentDisable={true}
                        onChange={(e) => {
                          const { key = '' } = e || {};
                          if (key.startsWith('3')) {
                            form.setFieldsValue({
                              d07Code: undefined,
                            });
                            this['d07Code'].clearAll();
                          }
                        }}
                        backType={'object'}
                      />,
                    )}
                  </FormItem>
                </Col>
                {
                  // 当工作岗位是3学生开头的时候 增加一个时间字段填写：入学时间
                  (function (_this) {
                    const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                    if (_key && _key.startsWith('3')) {
                      return (
                        <Fragment>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('入学时间', tipMsg['enterSchoolDate'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('enterSchoolDate', {
                                rules: [{ required: true, message: '请输入 入学时间' }],
                                initialValue:
                                  basicInfo['enterSchoolDate'] != undefined
                                    ? moment(basicInfo['enterSchoolDate'])
                                    : undefined,
                              })(
                                <Date
                                  startTime={'1910.01.01'}
                                  disabledDate={_this.disabledTomorrow}
                                />,
                              )}
                            </FormItem>
                          </Col>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('是否需要自动计算年级', tipMsg['hasCalculationGrade'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('hasCalculationGrade', {
                                rules: [{ required: true, message: '是否需要自动计算年级' }],
                                initialValue: basicInfo['hasCalculationGrade'] === 0 ? 0 : 1,
                              })(
                                <Select style={{ width: '100%' }}>
                                  <Select.Option value={1}>是</Select.Option>
                                  <Select.Option value={0}>否</Select.Option>
                                </Select>,
                              )}
                            </FormItem>
                          </Col>
                        </Fragment>
                      );
                    }
                  })(this)
                }
                <Col span={12}>
                  {
                    // 岗位和学历的校验判断
                    (function (_this) {
                      const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                      let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;

                      let itemsDisabled: Array<string> = [];
                      if (_key) {
                        if (_key.startsWith('31')) {
                          // 研究生
                          // itemsDisabled = ['11','12','13','14'];
                          itemsDisabled = [];
                        }
                        if (_key.startsWith('32')) {
                          // 本科
                          itemsDisabled = ['11', '12', '13', '14', '21', '22', '23'];
                        }
                        if (_key.startsWith('33')) {
                          // 专科
                          itemsDisabled = [
                            '11',
                            '12',
                            '13',
                            '14',
                            '21',
                            '22',
                            '23',
                            '31',
                            '32',
                            '33',
                            '34',
                          ];
                        }
                        if (_key == '34') {
                          // 中专
                          itemsDisabled = [
                            '11',
                            '12',
                            '13',
                            '14',
                            '21',
                            '22',
                            '23',
                            '31',
                            '32',
                            '33',
                            '34',
                            '4',
                            '5',
                            '6',
                          ];
                        }
                        if (_key == '35') {
                          // 高中
                          itemsDisabled = [
                            '11',
                            '12',
                            '13',
                            '14',
                            '21',
                            '22',
                            '23',
                            '31',
                            '32',
                            '33',
                            '34',
                            '4',
                            '5',
                            '6',
                          ];
                        }
                        if (_key == '36') {
                          // 技工
                          itemsDisabled = [
                            '11',
                            '12',
                            '13',
                            '14',
                            '21',
                            '22',
                            '23',
                            '31',
                            '32',
                            '33',
                            '34',
                            '4',
                            '5',
                            '6',
                          ];
                        }
                      }
                      return (
                        <FormItem
                          label={formLabel('当前学历情况', tipMsg['d07Code'])}
                          {...formItemLayout}
                        >
                          {getFieldDecorator('d07Code', {
                            rules: [{ required: true, message: '请选择当前学历情况' }],
                            initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d07Code'],
                          })(
                            <DictTreeSelect
                              initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d07Code']}
                              codeType={'dict_d07'}
                              placeholder={'当前学历情况'}
                              ref={(e) => (_this['d07Code'] = e)}
                              parentDisable={true}
                              itemsDisabled={itemsDisabled}
                              backType={'object'}
                            />,
                          )}
                        </FormItem>
                      );
                    })(this)
                  }
                </Col>
                {/* 选择无和其他知识分子只能选一个，选其他的可以选多个 */}
                {
                  // 工作岗位 满足以下代码时，不显示知识分子情况（1开头，3开头，514开头，515开头，516开头）。
                  (function (_this) {
                    const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                    if (
                      !(
                        `${_key}`.startsWith('1') ||
                        `${_key}`.startsWith('3') ||
                        `${_key}`.startsWith('514') ||
                        `${_key}`.startsWith('515') ||
                        `${_key}`.startsWith('516')
                      )
                    ) {
                      return (
                        <Col span={12}>
                          <FormItem
                            label={formLabel('知识分子情况', tipMsg['d154Code'])}
                            {...formItemLayout}
                          >
                            {getFieldDecorator('d154Code', {
                              rules: [{ required: true, message: '请选择知识分子情况' }],
                              initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d154Code'],
                            })(
                              <DictSelect
                                initValue={
                                  _isEmpty(basicInfo)
                                    ? undefined
                                    : basicInfo['d154Code']
                                      ? basicInfo['d154Code'].split(',')
                                      : undefined
                                }
                                codeType={'dict_d154'}
                                placeholder={'知识分子情况'}
                                backType={'object'}
                                mode="multiple"
                                noDraw={d154CodeNoDraw}
                                onChange={(e) => {
                                  if (!_isEmpty(e)) {
                                    if (e[e.length - 1]?.key == '0') {
                                      _this.setState({
                                        d154CodeNoDraw: [
                                          '1',
                                          '2',
                                          '3',
                                          '4',
                                          '5',
                                          '6',
                                          '7',
                                          '8',
                                          '9',
                                          'A',
                                          'B',
                                        ],
                                      });
                                    } else if (e[e.length - 1]?.key == 'B') {
                                      _this.setState({
                                        d154CodeNoDraw: [
                                          '0',
                                          '1',
                                          '2',
                                          '3',
                                          '4',
                                          '5',
                                          '6',
                                          '7',
                                          '8',
                                          '9',
                                          'A',
                                        ],
                                      });
                                    } else if (
                                      ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A'].includes(
                                        e[e.length - 1]?.key,
                                      )
                                    ) {
                                      _this.setState({
                                        d154CodeNoDraw: ['0', 'B'],
                                      });
                                    } else {
                                      _this.setState({
                                        d154CodeNoDraw: [],
                                      });
                                    }
                                  } else {
                                    _this.setState({
                                      d154CodeNoDraw: [],
                                    });
                                  }
                                }}
                              />,
                            )}
                          </FormItem>
                        </Col>
                      );
                    }
                  })(this)
                }
                {
                  // 在读大学生
                  (function (_this) {
                    const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                    if (
                      `${_key}`.startsWith('31') ||
                      `${_key}`.startsWith('32') ||
                      `${_key}`.startsWith('33')
                    ) {
                      return (
                        <Fragment>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('在读院校', tipMsg['readingCollege'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('readingCollege', {
                                rules: [{ required: true, message: '在读院校' }],
                                initialValue: _isEmpty(basicInfo)
                                  ? undefined
                                  : basicInfo['readingCollege'],
                              })(<Input placeholder={'在读院校'} />)}
                            </FormItem>
                          </Col>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('在读专业', tipMsg['readingProfessionalCode'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('readingProfessionalCode', {
                                rules: [{ required: true, message: '在读专业' }],
                                initialValue: _isEmpty(basicInfo)
                                  ? undefined
                                  : basicInfo['readingProfessionalCode'],
                              })(
                                <DictTreeSelect
                                  initValue={
                                    _isEmpty(basicInfo)
                                      ? undefined
                                      : basicInfo['readingProfessionalCode']
                                  }
                                  codeType={'dict_d88'}
                                  placeholder={'在读专业'}
                                  parentDisable={true}
                                  backType={'object'}
                                />,
                              )}
                            </FormItem>
                          </Col>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('学制', tipMsg['educationalSystem'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('educationalSystem', {
                                rules: [{ required: true, message: '学制' }],
                                initialValue: _isEmpty(basicInfo)
                                  ? undefined
                                  : basicInfo['educationalSystem'],
                              })(<InputNumber placeholder={'学制'} style={{ width: '100%' }} />)}
                            </FormItem>
                          </Col>
                        </Fragment>
                      );
                    } else {
                      // 全程保持在读院校信息，好让form回显
                      return (
                        <div style={{ display: 'none' }}>
                          {getFieldDecorator('readingCollege', {
                            rules: [{ required: false, message: '' }],
                            initialValue: basicInfo['readingCollege'],
                          })(<Input style={{ display: 'none' }} disabled />)}
                        </div>
                      );
                    }
                  })(this)
                }
                {
                  // 前后端增加毕业院校、毕业专业（高学历党员填写）
                  (function (_this: any) {
                    const { d07Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    const { birthday = undefined, applyDate = undefined } =
                      _this?.props?.form?.getFieldsValue() || {};
                    let bigThan28 = true;
                    if (
                      birthday &&
                      applyDate &&
                      moment(applyDate) <= moment(birthday).add(28, 'years')
                    ) {
                      bigThan28 = false;
                    }
                    if (birthday && !applyDate && moment() <= moment(birthday).add(28, 'years')) {
                      bigThan28 = false;
                    }
                    let _key = typeof d07Code === 'string' ? d07Code : d07Code?.key;
                    if (!['4', '5', '6', '7', '8', '9'].includes(_key)) {
                      return (
                        <Fragment>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('毕业院校', tipMsg['byyx'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('byyx', {
                                rules: [{ required: false, message: '毕业院校' }],
                                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['byyx'],
                              })(<Input placeholder={'毕业院校'} />)}
                            </FormItem>
                          </Col>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('毕业专业', tipMsg['d88Code'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('d88Code', {
                                rules: [{ required: false, message: '毕业专业' }],
                                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d88Code'],
                              })(
                                <DictTreeSelect
                                  initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d88Code']}
                                  codeType={'dict_d88'}
                                  placeholder={'毕业专业'}
                                  parentDisable={true}
                                  backType={'object'}
                                />,
                              )}
                            </FormItem>
                          </Col>
                          {/*<Col span={12}>*/}
                          {/*  <FormItem*/}
                          {/*    label="政治面貌"*/}
                          {/*    {...formItemLayout}*/}
                          {/*  >*/}
                          {/*    {getFieldDecorator('politicsCode', {*/}
                          {/*      rules: [{ required: false, message: '政治面貌' }],*/}
                          {/*      initialValue:_isEmpty(basicInfo)?undefined:basicInfo['politicsCode'],*/}
                          {/*    })(*/}
                          {/*      <DictSelect backType={'object'}*/}
                          {/*                  ref={e=>_this['politicsCode'] = e}*/}
                          {/*                  initValue={_isEmpty(basicInfo)?undefined:basicInfo['politicsCode'] ? basicInfo['politicsCode'].split(',') :undefined}*/}
                          {/*                  codeType={'dict_d89'}*/}
                          {/*                  placeholder="请选择"*/}
                          {/*                  mode={'multiple'}*/}
                          {/*                  filter={(data)=>{*/}
                          {/*                    if(bigThan28){*/}
                          {/*                      return data.filter(it=>it.key !== '12');*/}
                          {/*                    }*/}
                          {/*                    return data;*/}
                          {/*                  }} />*/}
                          {/*    )}*/}
                          {/*  </FormItem>*/}
                          {/*</Col>*/}
                        </Fragment>
                      );
                    }
                  })(this)
                }
                {(function (_this: any) {
                  const { birthday = undefined, applyDate = undefined } =
                    _this?.props?.form?.getFieldsValue() || {};
                  let bigThan28 = true;
                  if (
                    birthday &&
                    applyDate &&
                    moment(applyDate) <= moment(birthday).add(28, 'years')
                  ) {
                    bigThan28 = false;
                  }
                  if (birthday && !applyDate && moment() <= moment(birthday).add(28, 'years')) {
                    bigThan28 = false;
                  }
                  return (
                    <Fragment>
                      <Col span={12}>
                        <FormItem
                          label={formLabel('政治面貌', tipMsg['politicsCode'])}
                          {...formItemLayout}
                        >
                          {getFieldDecorator('politicsCode', {
                            rules: [{ required: true, message: '政治面貌' }],
                            initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['politicsCode'],
                          })(
                            <DictSelect
                              backType={'object'}
                              ref={(e) => (_this['politicsCode'] = e)}
                              initValue={
                                _isEmpty(basicInfo)
                                  ? undefined
                                  : basicInfo['politicsCode']
                                    ? basicInfo['politicsCode'].split(',')
                                    : undefined
                              }
                              codeType={'dict_d89'}
                              placeholder="请选择"
                              mode={'multiple'}
                              filter={(data) => {
                                if (bigThan28) {
                                  return data.filter((it) => it.key !== '03');
                                }
                                return data;
                              }}
                            />,
                          )}
                        </FormItem>
                      </Col>
                    </Fragment>
                  );
                })(this)}
                {
                  // 当工作岗位是工勤岗位（例如:岗位名称中的工勤岗位和工勤技能人员这类岗位），才弹出是否农民工和是否劳务派遣工的信息选择项
                  // 民办非企业工勤技能人员、社会团体工勤技能人员，选择以后都需要增加是否劳务派遣工和是否农民工
                  (function (_this) {
                    const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                    if (
                      `${_key}`.startsWith('016') ||
                      `${_key}`.startsWith('025') ||
                      `${_key}` == '0313' ||
                      `${_key}` == '0323' ||
                      `${_key}` == '0333'
                    ) {
                      return (
                        <Fragment>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('是否劳务派遣工', tipMsg['isDispatch'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('isDispatch', {
                                rules: [{ required: true, message: '请选择是否劳务派遣工' }],
                                initialValue: basicInfo['isDispatch'],
                                // initialValue: _isEmpty(basicInfo)? undefined : _isNumber(basicInfo['isDispatch']) ? basicInfo['isDispatch'].toString() : undefined,
                              })(<YN init={basicInfo['isDispatch']} />)}
                            </FormItem>
                          </Col>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('是否农民工', tipMsg['isFarmer'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('isFarmer', {
                                rules: [{ required: true, message: '是否农民工' }],
                                initialValue: basicInfo['isFarmer'],
                                // initialValue: _isEmpty(basicInfo)? undefined : _isNumber(basicInfo['isFarmer']) ? basicInfo['isFarmer'].toString() : undefined,
                              })(<YN init={basicInfo['isFarmer']} />)}
                            </FormItem>
                          </Col>
                        </Fragment>
                      );
                    }
                  })(this)
                }

                <Col span={12}>
                  <FormItem label={formLabel('手机号码', tipMsg['phone'])} {...formItemLayout}>
                    {getFieldDecorator('phone', {
                      getValueFromEvent: (e) => _trim(e.target.value),
                      rules: [
                        { required: true, message: '请输入联系电话' },
                        { pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: '请输入正确的手机号' },
                      ],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['phone'],
                    })(<Input placeholder={'请输入联系电话'} />)}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label={formLabel('人员类别', tipMsg['d08Code'])} {...formItemLayout}>
                    {getFieldDecorator('d08Code', {
                      rules: [{ required: true, message: '请输入人员类别' }],
                      // initialValue: _isEmpty(basicInfo)?undefined:basicInfo['d08Code'],
                      initialValue: d08Code,
                    })(
                      <DictSelect
                        codeType={'dict_d08'}
                        // initValue={_isEmpty(basicInfo)?undefined:basicInfo['d08Code']}
                        initValue={d08Code}
                        noDraw={['1', '2', '3', '4', '6']}
                        backType={'object'}
                        onChange={this.d08CodeOnChange}
                        disabled={true}
                      />,
                    )}
                  </FormItem>
                </Col>
                {/* 工作岗位 是学生，离退休人员，无业人员，学生毕业未就业人员、无固定职业人员都不出现工作性质 */}
                {(function (_this) {
                  const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                  let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                  if (
                    !(
                      `${_key}`.startsWith('3') ||
                      `${_key}`.startsWith('4') ||
                      `${_key}`.startsWith('514') ||
                      `${_key}`.startsWith('515') ||
                      `${_key}`.startsWith('516')
                    )
                  ) {
                    return (
                      <Col span={12}>
                        <FormItem
                          label={formLabel('工作性质', tipMsg['jobNatureCode'])}
                          {...formItemLayout}
                        >
                          {getFieldDecorator('jobNatureCode', {
                            rules: [{ required: true, message: '请选择工作性质' }],
                            initialValue: _isEmpty(basicInfo)
                              ? undefined
                              : basicInfo['jobNatureCode'],
                          })(
                            <DictSelect
                              codeType={'dict_d107'}
                              initValue={_isEmpty(basicInfo) ? undefined : basicInfo['jobNatureCode']}
                              backType={'object'}
                            />,
                          )}
                        </FormItem>
                      </Col>
                    );
                  }
                })(this)}
                {/* <Col span={23}>
                <Alert message="提示：管理党组织必须选择到支部。选择后，该发展人员会进入该组织。" type="info" showIcon />
              </Col>
              <Col span={24}>
                <div style={{marginBottom:'10px'}}/>
              </Col> */}
                <Col span={12}>
                  <FormItem label={formLabel('管理党组织', tipMsg['orgCode'])} {...formItemLayout}>
                    {getFieldDecorator('orgCode', {
                      rules: [{ required: true, message: '请选择管理党组织' }],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['orgCode'],
                    })(
                      <OrgSelect
                        orgTypeList={['3', '4']}
                        initValue={basicInfo['orgName']}
                        onChange={(e: any) => {
                          if (!_isEmpty(e)) {
                            const { code, d01Code } = e[0] || {};
                            this.getUnitList(code);
                          }
                        }}
                        disabled={!!basicInfo['id']}
                        placeholder={'请选择所在党支部'}
                      />,
                    )}
                  </FormItem>
                </Col>
                {/* <Col span={23}>
                <Alert message="提示：根据党统数据要求，一线情况、专业技术职务、新社会阶层为必填项，没有请选择无。" type="info" showIcon />
              </Col>
              <Col span={24}>
                <div style={{marginBottom:'10px'}}/>
              </Col> */}
                {!_isEmpty(d08Code) && parseInt(d08Code) <= 5 && (
                  <Col span={12}>
                    <FormItem
                      label={formLabel('申请入党时间', tipMsg['applyDate'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('applyDate', {
                        rules: [
                          { required: true, message: '请输入申请入党时间' },
                          { validator: this.TemporalOrder1 },
                        ],
                        initialValue: !_isNumber(basicInfo['applyDate'])
                          ? undefined
                          : moment(basicInfo['applyDate']),
                        // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
                      })(<Date disabledDate={this.disabledTomorrow} disabled={!canEdit} />)}
                    </FormItem>
                  </Col>
                )}
                {!_isEmpty(d08Code) && parseInt(d08Code) <= 4 && (
                  <Col span={12}>
                    <FormItem
                      label={formLabel('确定积极分子时间', tipMsg['activeDate'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('activeDate', {
                        rules: [
                          { required: true, message: '请输入确定积极分子时间' },
                          { validator: this.TemporalOrder2 },
                        ],
                        initialValue: !_isNumber(basicInfo['activeDate'])
                          ? undefined
                          : moment(basicInfo['activeDate']),
                        // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
                      })(<Date disabledDate={this.disabledTomorrow} disabled={!canEdit} />)}
                    </FormItem>
                  </Col>
                )}
                {d08Code === '3' && (
                  <Col span={12}>
                    <FormItem
                      label={formLabel('确定发展对象时间', tipMsg['objectDate'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('objectDate', {
                        rules: [
                          { required: true, message: '请输入确定发展对象时间' },
                          { validator: this.TemporalOrder3 },
                        ],
                        initialValue: !_isNumber(basicInfo['objectDate'])
                          ? undefined
                          : moment(basicInfo['objectDate']),
                        // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
                      })(<Date disabledDate={this.disabledTomorrow} disabled={!canEdit} />)}
                    </FormItem>
                  </Col>
                )}
                {(function (_this) {
                  // d09Code 党员、本年度发展党员、入党、积极分子、发展党员，工作岗位是3（学生）跟4（离退休）开头的。 一线情况 隐藏。 -- 王察
                  const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                  let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                  if (!(`${_key}`.startsWith('3') || `${_key}`.startsWith('4'))) {
                    return (
                      <Col span={12}>
                        <FormItem
                          label={formLabel('一线情况', tipMsg['d21Code'])}
                          {...formItemLayout}
                        >
                          {getFieldDecorator('d21Code', {
                            rules: [{ required: true, message: '请选择一线情况' }],
                            initialValue: _isEmpty(basicInfo) ? '0' : basicInfo['d21Code'],
                          })(
                            <DictSelect
                              codeType={'dict_d21'}
                              initValue={_isEmpty(basicInfo) ? '0' : basicInfo['d21Code']}
                              backType={'object'}
                            />,
                          )}
                        </FormItem>
                      </Col>
                    );
                  }
                })(this)}
                {/* <Col span={12}>
                <FormItem label={formLabel('一线情况', tipMsg['d21Code'])} {...formItemLayout}>
                  {getFieldDecorator('d21Code', {
                    rules: [{ required: true, message: '请选择一线情况' }],
                    initialValue: _isEmpty(basicInfo) ? '0' : basicInfo['d21Code'],
                  })(
                    <DictSelect
                      codeType={'dict_d21'}
                      initValue={_isEmpty(basicInfo) ? '0' : basicInfo['d21Code']}
                      backType={'object'}
                    />,
                  )}
                </FormItem>
              </Col> */}

                {(function (_this) {
                  // d09Code 社会组织 工作岗位是自由职业人员505和个体工商户中从业人员504， 才能选择新社会阶层
                  const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                  let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                  if (
                    `${_key}`.startsWith('03') ||
                    `${_key}`.startsWith('02') ||
                    `${_key}`.startsWith('505') ||
                    `${_key}` == '504'
                  ) {
                    return (
                      <Col span={12}>
                        <FormItem
                          label={formLabel('新社会阶层', tipMsg['d20Code'])}
                          {...formItemLayout}
                        >
                          {getFieldDecorator('d20Code', {
                            rules: [{ required: true, message: '请选择新社会阶层' }],
                            initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d20Code'],
                          })(
                            <DictTreeSelect
                              codeType={'dict_d20'}
                              initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d20Code']}
                              backType={'object'}
                              parentDisable={true}
                            />,
                          )}
                        </FormItem>
                      </Col>
                    );
                  }
                })(this)}
                {(function (_this) {
                  // 012	事业单位管理岗位（含参照管理）
                  // 013	事业单位专业技术岗位
                  // 014	公有经济控制企业管理岗位
                  // 015	公有经济控制企业专业技术岗位
                  // 0162	事业单位工勤技能人员
                  // 0163	公有制经济控制企业工勤技能人员
                  // 02	非公有制单位
                  // 03	社会组织
                  const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                  let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                  let arr = ['012', '013', '014', '015', '0162', '0163', '02', '03'];
                  if (arr.find((it) => `${_key}`.startsWith(it))) {
                    return (
                      <Col span={12}>
                        <FormItem
                          label={formLabel('专业技术职务', tipMsg['d19Code'])}
                          {...formItemLayout}
                        >
                          {getFieldDecorator('d19Code', {
                            rules: [{ required: true, message: '请选择专业技术职务' }],
                            initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d19Code'],
                          })(
                            <DictTreeSelect
                              initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d19Code']}
                              codeType={'dict_d19'}
                              placeholder={'专业技术职务'}
                              parentDisable={true}
                              backType={'object'}
                              onChange={() => {
                                // this['d126Code'].clearAll();
                                // this.props.form.setFieldsValue({d126Code:undefined});
                              }}
                            />,
                          )}
                        </FormItem>
                      </Col>
                    );
                  }
                })(this)}
                {/* {(function(_this){
                const {d19Code = undefined} = _this?.props?.form?.getFieldsValue() || {};
                let _key = typeof d19Code === 'string' ? d19Code : d19Code?.key || '';
                let parentKey = `${_key}`.slice(0, _key.length-1);
                return (
                  <Col span={12}>
                    <FormItem label={formLabel('专业技术职称', tipMsg['d126Code'])} {...formItemLayout}>
                      {getFieldDecorator('d126Code', {
                        rules: [{ required: true, message: '专业技术职称' }],
                        initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d126Code'],
                      })(
                        <DictTreeSelect
                          initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d126Code']}
                          codeType={'dict_d126'}
                          ref={e=>_this['d126Code']=e}
                          placeholder={'专业技术职称'}
                          parentDisable={true}
                          backType={'object'}
                          showConstant={false}
                          filter={(data)=>{
                            if(parentKey){
                              let find:any= treeToList(data).find(it=>it.key == parentKey) || {};
                              if(find?.children){
                                find.children = find.children.filter(it => it.key >= _key)
                              }
                              return [find];
                            }
                            return data;
                          }}
                        />
                      )}
                    </FormItem>
                  </Col>
                )
              })(this)} */}

                {/* <Col span={12}>
                <FormItem
                  label={formLabel('专业技术职务', tipMsg['d19Code'])}
                  {...formItemLayout}
                >
                  {getFieldDecorator('d19Code', {
                    rules: [{ required: true, message: '请选择专业技术职务' }],
                    initialValue: _isEmpty(basicInfo)?'0':basicInfo['d19Code'],
                  })(
                    <DictSelect codeType={'dict_d19'} initValue={_isEmpty(basicInfo)?'0':basicInfo['d19Code']} backType={'object'} onChange={this.d19CodeOnChange}/>
                  )}
                </FormItem>
              </Col> */}
                {/*<Col span={24}/>*/}
                {/*<Col span={8}>*/}
                {/*  <FormItem*/}
                {/*    label="专业技术职务"*/}
                {/*    {...formItemLayout}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('d19Code', {*/}
                {/*      rules: [{ required: true, message: '请选择专业技术职务' }],*/}
                {/*      initialValue: _isEmpty(basicInfo)?'0':basicInfo['d19Code'],*/}
                {/*    })(*/}
                {/*      <DictSelect codeType={'dict_d19'} initValue={_isEmpty(basicInfo)?'0':basicInfo['d19Code']} backType={'object'} onChange={this.d19CodeOnChange}/>*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                {/*<Col span={8}>*/}
                {/*  <FormItem*/}
                {/*    label="聘任日期"*/}
                {/*    {...formItemLayout}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('appointmentDate', {*/}
                {/*      rules: [{ required: hasAppointment, message: '请选择专业技术职务' },{ validator: this.TemporalOrder4 }],*/}
                {/*      initialValue:!_isNumber(basicInfo['appointmentDate'])?undefined:moment(basicInfo['appointmentDate']),*/}
                {/*    })(*/}
                {/*      <DatePicker style={{width:'100%'}} disabled={!hasAppointment}/>*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                {/*<Col span={8}>*/}
                {/*  <FormItem*/}
                {/*    label="终止日期"*/}
                {/*    {...formItemLayout}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('appointmentEndDate', {*/}
                {/*      rules: [{ required: hasAppointment, message: '请选择专业技术职务' },{ validator: this.TemporalOrder4 }],*/}
                {/*      initialValue:!_isNumber(basicInfo['appointmentEndDate'])?undefined:moment(basicInfo['appointmentEndDate']),*/}
                {/*    })(*/}
                {/*      <DatePicker style={{width:'100%'}} disabled={!hasAppointment}/>*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                {/*<Col span={24}/>*/}
                {/* {
                (function(_this) {
                  const {d19Code = undefined} = _this?.props?.form?.getFieldsValue() || {};
                  let _key = typeof d19Code === 'string' ? d19Code : d19Code?.key;
                  if(`${_key}`.startsWith('1') || _key == '2'){
                    return (
                      <Col span={12}>
                        <FormItem
                          label={formLabel('是否高知识群体', tipMsg['isHighKnowledge'])}
                          {...formItemLayout}
                        >
                          {getFieldDecorator('isHighKnowledge', {
                            rules: [{ required: true, message: '是否高知识群体' }],
                            initialValue: basicInfo['isHighKnowledge'],
                            // initialValue: _isEmpty(basicInfo)? undefined : _isNumber(basicInfo['isHighKnowledge']) ? basicInfo['isHighKnowledge'].toString() : undefined,
                          })(
                            <YN init={basicInfo['isHighKnowledge']}/>
                          )}
                        </FormItem>
                      </Col>
                    )
                  }
                })(this)
              } */}
                {
                  // d09Code 当前工作岗位
                  // 改成015，0163，022，025开头以下的，0312，0313，0322，0323，0332，0333才可以选产业工人
                  (function (_this) {
                    const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                    let arr = ['015', '0163', '022', '0312', '0313', '0322', '0323', '0332', '0333'];
                    if (`${_key}`.startsWith('025') || arr.includes(`${_key}`)) {
                      return (
                        <Col span={12}>
                          <FormItem
                            label={formLabel('是否产业工人', tipMsg['hasWorker'])}
                            {...formItemLayout}
                          >
                            {getFieldDecorator('hasWorker', {
                              rules: [{ required: true, message: '是否产业工人' }],
                              initialValue: basicInfo['hasWorker'],
                              // initialValue: _isEmpty(basicInfo)? undefined : _isNumber(basicInfo['hasWorker']) ? basicInfo['hasWorker'].toString() : undefined,
                            })(<YN init={basicInfo['hasWorker']} />)}
                          </FormItem>
                        </Col>
                      );
                    }
                  })(this)
                }
                {/*<Col span={12}>*/}
                {/*<FormItem*/}
                {/*  label="是否青年农民"*/}
                {/*  {...formItemLayout}*/}
                {/*>*/}
                {/*  {getFieldDecorator('hasYoungFarmers', {*/}
                {/*    rules: [{ required: true, message: '是否高知识群体' }],*/}
                {/*    initialValue: _isEmpty(basicInfo)?false:basicInfo['hasYoungFarmers'] === 1,*/}
                {/*  })(*/}
                {/*    <Switch checkedChildren="是" unCheckedChildren="否" defaultChecked={_isEmpty(basicInfo)?false:basicInfo['hasYoungFarmers'] === 1}/>*/}
                {/*  )}*/}
                {/*</FormItem>*/}
                {/*<FormItem*/}
                {/*  label="是否青年农民"*/}
                {/*  {...formItemLayout}*/}
                {/*>*/}
                {/*  {getFieldDecorator('hasYoungFarmers', {*/}
                {/*    rules: [{ required: true, message: '是否高知识群体' }],*/}
                {/*    initialValue: _isEmpty(basicInfo)?false:basicInfo['hasYoungFarmers'],*/}
                {/*  })(*/}
                {/*    <Select style={{width:'100%'}}>*/}
                {/*      <Select.Option value="1">是</Select.Option>*/}
                {/*      <Select.Option value="0">否</Select.Option>*/}
                {/*    </Select>*/}
                {/*  )}*/}
                {/*</FormItem>*/}
                {/*</Col>*/}
                <Col span={12}>
                  <FormItem
                    label={formLabel('先进模范人物', tipMsg['advancedModelCode'])}
                    {...formItemLayout}
                  >
                    {getFieldDecorator('advancedModelCode', {
                      rules: [{ required: false, message: '先进模范人物' }],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['advancedModelCode'],
                    })(
                      <DictSelect
                        codeType={'dict_d104'}
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['advancedModelCode']}
                        backType={'object'}
                      />,
                    )}
                  </FormItem>
                </Col>
                {/* <Col span={12}>
                <FormItem
                  label="参加工作日期"
                  {...formItemLayout}
                >
                  {getFieldDecorator('joinWorkDate', {
                    rules: [{ required: false, message: '参加工作日期' }],
                    initialValue:!_isNumber(basicInfo['joinWorkDate'])?undefined:moment(basicInfo['joinWorkDate']),
                    // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
                  })(

                    <Date disabledDate={this.disabledTomorrow} />
                  )}
                </FormItem>
              </Col> */}
                {/* <Col span={12}>
                <FormItem
                  label={formLabel('申请人档案管理单位', tipMsg['archiveUnit'])}
                  {...formItemLayout}
                >
                  {getFieldDecorator('archiveUnit', {
                    rules: [{ required: false, message: '申请人档案管理单位' }],
                    initialValue:_isEmpty(basicInfo)?undefined:basicInfo['archiveUnit'],
                  })(
                    <Input placeholder={'申请人档案管理单位'}/>
                  )}
                </FormItem>
              </Col> */}

                <Col span={12}>
                  <LongLabelFormItem
                    label={'人事关系是否在党组织关联单位内'}
                    required={true}
                    code={'hasUnitStatistics'}
                    tipMsg={tipMsg}
                    formItemLayout={formItemLayout}
                    formItem={(formItemLayout, code) => {
                      return (
                        <FormItem {...formItemLayout}>
                          {getFieldDecorator(code, {
                            rules: [{ required: true, message: '人事关系是否在党组织关联单位内' }],
                            initialValue: basicInfo[code],
                          })(
                            <YN
                              init={basicInfo[code]}
                              onChange={(e) => {
                                // //所在党支部
                                // let code = undefined;
                                // let org = this.props.form.getFieldValue('orgCode');
                                // if(typeof org == 'string'){
                                //   code = basicInfo['orgCode'];
                                // }else {
                                //   code = _get(org,'[0].code',undefined);
                                // }
                                // this.getUnitList(code);
                              }}
                            />,
                          )}
                        </FormItem>
                      );
                    }}
                  />
                </Col>
                {(function (_this) {
                  let val = _this.props.form.getFieldValue('hasUnitStatistics');
                  if (val == 0) {
                    return (
                      <Col span={12}>
                        <LongLabelFormItem
                          label={'人事关系所在单位是否省内单位'}
                          required={true}
                          code={'hasUnitProvince'}
                          tipMsg={tipMsg}
                          formItemLayout={formItemLayout}
                          formItem={(formItemLayout, code) => {
                            return (
                              <FormItem {...formItemLayout}>
                                {getFieldDecorator(code, {
                                  rules: [
                                    { required: true, message: '人事关系所在单位是否省内单位' },
                                  ],
                                  initialValue: basicInfo[code],
                                })(
                                  <Select style={{ width: '100%' }}>
                                    <Select.Option value={1}>是</Select.Option>
                                    <Select.Option value={0}>否</Select.Option>
                                  </Select>,
                                )}
                              </FormItem>
                            );
                          }}
                        />
                      </Col>
                    );
                  }
                })(this)}
                {(function (_this) {
                  //人事关系是否在党组织关联单位内
                  let val1 = _this.props.form.getFieldValue('hasUnitStatistics');
                  //人事关系所在单位是否省内单位
                  let hasUnitProvince = _isNumber(_this.props.form.getFieldValue('hasUnitProvince'))
                    ? _this.props.form.getFieldValue('hasUnitProvince')
                    : basicInfo['hasUnitProvince'];
                  //所在党支部
                  let org = _this.props.form.getFieldValue('orgCode');
                  let d01Code: any = undefined;
                  if (typeof org == 'string') {
                    d01Code = basicInfo['d01Code'];
                  } else {
                    d01Code = _get(org, '[0].d01Code', undefined);
                  }
                  let isLianhe =
                    (d01Code == '632' || d01Code == '932' || d01Code == '634') && d01Code;

                  // 当人事关系是否在党组织关联单位内选择 是
                  if (val1) {
                    // 并且党员所在党组织非联合党支部的时候，人事关系所在单位名称展示为党员所在党组织关联单位
                    if (!isLianhe) {
                      return (
                        <Col span={12}>
                          <LongLabelFormItem
                            label={'人事关系所在单位名称'}
                            required={true}
                            code={'unitInformation'}
                            tipMsg={tipMsg}
                            formItemLayout={formItemLayout}
                            formItem={(formItemLayout, code) => {
                              return (
                                <FormItem {...formItemLayout}>
                                  {getFieldDecorator(code, {
                                    rules: [{ required: false, message: '人事关系所在单位名称' }],
                                    initialValue: _this.state.unitInformation,
                                  })(
                                    <Input
                                      placeholder={'请填写人事关系所在单位名称'}
                                      style={{ width: '100%' }}
                                      disabled
                                    />,
                                  )}
                                </FormItem>
                              );
                            }}
                          />
                          <div style={{ display: 'none' }}>
                            {getFieldDecorator('statisticalUnit', {
                              rules: [{ required: false, message: '' }],
                              initialValue: _this.state.unitInformationCode,
                            })(<Input style={{ display: 'none' }} disabled />)}
                            {getFieldDecorator('_d04Code', {
                              rules: [{ required: false, message: '' }],
                              initialValue: _this.state.unitInformationD04Code,
                            })(<Input style={{ display: 'none' }} disabled />)}
                            {getFieldDecorator('d01Code', {
                              rules: [{ required: false, message: '' }],
                              initialValue: d01Code,
                            })(<Input style={{ display: 'none' }} disabled />)}
                          </div>
                        </Col>
                      );
                    } else {
                      // 党员所在党组织是联合党支部的时候，人事关系所在单位名称变成下拉框
                      return (
                        <Col span={12}>
                          <LongLabelFormItem
                            label={'人事关系所在单位名称'}
                            required={true}
                            code={'statisticalUnit'}
                            tipMsg={tipMsg}
                            formItemLayout={formItemLayout}
                            formItem={(formItemLayout, code) => {
                              return (
                                <Fragment>
                                  <FormItem {...formItemLayout}>
                                    {getFieldDecorator(code, {
                                      rules: [{ required: true, message: '人事关系所在单位名称' }],
                                      initialValue: basicInfo[code],
                                    })(
                                      <Select
                                        style={{ width: '100%' }}
                                        onChange={(e) => {
                                          let find = _this.state.unitList.find(
                                            (it) => it?.unitCode == e,
                                          );
                                          if (find) {
                                            _this.props.form.setFieldsValue({
                                              __d04Code: find.d04Code,
                                            });
                                          }
                                        }}
                                      >
                                        {_this.state.unitList &&
                                          _this.state.unitList.map((it, index) => (
                                            <Select.Option key={index} value={it.unitCode}>
                                              {it.unitName}
                                            </Select.Option>
                                          ))}
                                      </Select>,
                                    )}
                                  </FormItem>
                                  {getFieldDecorator('__d04Code', {
                                    rules: [{ required: false, message: '' }],
                                    initialValue: basicInfo['d04Code'],
                                  })(<Input style={{ display: 'none' }} disabled />)}
                                  {getFieldDecorator('d01Code', {
                                    rules: [{ required: false, message: '' }],
                                    initialValue: d01Code,
                                  })(<Input style={{ display: 'none' }} disabled />)}
                                </Fragment>
                              );
                            }}
                          />
                        </Col>
                      );
                    }
                  } else {
                    //人事关系所在单位是否省内单位选择是的时候，人事关系所在单位名称需要走中间交换区进行搜索
                    if (hasUnitProvince) {
                      return (
                        <Col span={12}>
                          <LongLabelFormItem
                            label={'人事关系所在单位名称'}
                            required={true}
                            code={'middleUnitCode'}
                            tipMsg={tipMsg}
                            formItemLayout={formItemLayout}
                            formItem={(formItemLayout, code) => {
                              return (
                                <FormItem {...formItemLayout}>
                                  {getFieldDecorator(code, {
                                    rules: [{ required: true, message: '人事关系所在单位名称' }],
                                    initialValue: basicInfo[code],
                                  })(
                                    <SearchUnit
                                      initName={basicInfo['middleUnitName']}
                                      initCode={basicInfo['middleUnitCode']}
                                      backType={'object'}
                                      style={{ width: '100%' }}
                                      onChange={(e) => {
                                        _this.props.form.setFieldsValue({
                                          d04Code: e.d04Code,
                                          middleUnitName: e.name,
                                          _d194Code: e.d194Code,
                                          _d194Name: e.d194Name,
                                          _d195Code: e.d195Code,
                                          _d195Name: e.d195Name,
                                        });
                                      }}
                                      params={{ orgTypeList: ['3', '4'] }}
                                    />,
                                  )}
                                </FormItem>
                              );
                            }}
                          />
                          <div style={{ display: 'none' }}>
                            {getFieldDecorator('middleUnitName', {
                              rules: [{ required: false, message: '' }],
                              initialValue: basicInfo['middleUnitName'],
                            })(<Input style={{ display: 'none' }} disabled />)}
                            {getFieldDecorator('d04Code', {
                              rules: [{ required: false, message: '' }],
                              initialValue: basicInfo['d04Code'],
                            })(<Input style={{ display: 'none' }} disabled />)}
                            {getFieldDecorator('_d194Code', {
                              rules: [{ required: false, message: '' }],
                              initialValue: basicInfo['_d194Code'],
                            })(<Input style={{ display: 'none' }} disabled />)}
                            {getFieldDecorator('_d194Name', {
                              rules: [{ required: false, message: '' }],
                              initialValue: basicInfo['_d194Name'],
                            })(<Input style={{ display: 'none' }} disabled />)}
                            {getFieldDecorator('_d195Code', {
                              rules: [{ required: false, message: '' }],
                              initialValue: basicInfo['_d195Code'],
                            })(<Input style={{ display: 'none' }} disabled />)}
                            {getFieldDecorator('_d195Name', {
                              rules: [{ required: false, message: '' }],
                              initialValue: basicInfo['_d195Name'],
                            })(<Input style={{ display: 'none' }} disabled />)}
                          </div>
                        </Col>
                      );
                    } else if (hasUnitProvince == 0) {
                      return (
                        <Col span={12}>
                          <LongLabelFormItem
                            label={'人事关系所在单位名称'}
                            required={true}
                            code={'selfUnitName'}
                            tipMsg={tipMsg}
                            formItemLayout={formItemLayout}
                            formItem={(formItemLayout, code) => {
                              return (
                                <FormItem {...formItemLayout}>
                                  {getFieldDecorator(code, {
                                    rules: [{ required: true, message: '人事关系所在单位名称' }],
                                    initialValue: basicInfo[code],
                                  })(
                                    <Input
                                      placeholder={'请填写人事关系所在单位名称'}
                                      style={{ width: '100%' }}
                                    />,
                                  )}
                                </FormItem>
                              );
                            }}
                          />
                        </Col>
                      );
                    }
                  }
                })(this)}

                {(function (_this) {
                  let val1 = _this.props.form.getFieldValue('hasUnitStatistics');
                  if (val1 == undefined) {
                    val1 = basicInfo['hasUnitStatistics'];
                  }
                  let val = _this.props.form.getFieldValue('hasUnitProvince');
                  if (val == undefined) {
                    val = basicInfo['hasUnitProvince'];
                  }
                  if (val1 == 0 && val == 0) {
                    return (
                      <Fragment>
                        <Col span={12}>
                          <LongLabelFormItem
                            label={'人事关系所在单位类别'}
                            required={true}
                            code={'d04Code'}
                            tipMsg={tipMsg}
                            formItemLayout={formItemLayout}
                            formItem={(formItemLayout, code) => {
                              return (
                                <FormItem {...formItemLayout}>
                                  {getFieldDecorator(code, {
                                    rules: [{ required: true, message: '人事关系所在单位类别' }],
                                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo[code],
                                  })(
                                    <DictTreeSelect
                                      initValue={_isEmpty(basicInfo) ? undefined : basicInfo[code]}
                                      codeType={'dict_d04'}
                                      placeholder={'请选择'}
                                      parentDisable={true}
                                      backType={'object'}
                                      onChange={async (e) => {
                                        // 更新d194Code
                                        const res = await normalList({
                                          data: {
                                            tableCode: 'ccp_unit',
                                            colCode: 'd04Code',
                                            colValue: e.key,
                                            compareColCode: 'd194Code',
                                          },
                                        });
                                        if (res.code == 0 && !_isEmpty(res.data)) {
                                          let key = Object.keys(res.data)?.[0];
                                          let name = res.data[key];
                                          _this.setState({
                                            d194CodeSatate: key,
                                            d194CodeKey: moment().valueOf(),
                                          });
                                          _this.props.form.setFieldsValue({
                                            d194Code: key,
                                            d194Name: name,
                                          });
                                          // 更新d195Code
                                          _this.d194Change(key);
                                        }
                                      }}
                                    />,
                                  )}
                                </FormItem>
                              );
                            }}
                          />
                        </Col>
                      </Fragment>
                    );
                  }
                })(this)}

                {(function (_this) {
                  let canEdit = _this.showGUOMINGJINGJI();
                  if (true) {
                    return (
                      <Fragment>
                        <Col span={12}>
                          <LongLabelFormItem
                            label={'国民经济行业'}
                            required={canEdit}
                            code={'d194Code'}
                            tipMsg={tipMsg}
                            formItemLayout={formItemLayout}
                            formItem={(formItemLayout, code) => {
                              return (
                                <FormItem {...formItemLayout}>
                                  {getFieldDecorator(code, {
                                    rules: [{ required: canEdit, message: '国民经济行业' }],
                                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo[code],
                                  })(
                                    <DictTreeSelect
                                      key={_this.state.d194CodeKey}
                                      backType={'object'}
                                      initValue={
                                        _this.state.d194CodeSatate ||
                                        (_isEmpty(basicInfo) ? undefined : basicInfo[code])
                                      }
                                      codeType={'dict_d194'}
                                      placeholder={'国民经济行业'}
                                      parentDisable={true}
                                      showModalIcon={canEdit}
                                      disabled={!canEdit}
                                      onChange={_this.d194Change}
                                    />,
                                  )}
                                </FormItem>
                              );
                            }}
                          />
                        </Col>
                        <Col span={12}>
                          <FormItem
                            label={formLabel('生产性服务行业', tipMsg['d195Code'])}
                            {...formItemLayout}
                          >
                            {getFieldDecorator('d195Code', {
                              initialValue: basicInfo['d195Code'] || 'V0000',
                              rules: [{ required: canEdit, message: '请选择生产性服务行业' }],
                            })(
                              <DictTreeSelect
                                backType={'object'}
                                key={_this.state.d195CodeKey}
                                initValue={
                                  _this.state.d195CodeSatate || basicInfo['d195Code'] || 'V0000'
                                }
                                codeType={'dict_d195'}
                                placeholder={'生产性服务行业'}
                                showModalIcon={canEdit}
                                disabled={!canEdit}
                                parentDisable={true}
                              />,
                            )}
                          </FormItem>
                        </Col>
                        {getFieldDecorator('d194Name', {
                          rules: [{ required: false, message: '' }],
                          initialValue: basicInfo['d194Name'],
                        })(<Input style={{ display: 'none' }} disabled />)}
                        {getFieldDecorator('d195Name', {
                          rules: [{ required: false, message: '' }],
                          initialValue: basicInfo['d195Name'],
                        })(<Input style={{ display: 'none' }} disabled />)}
                      </Fragment>
                    );
                  }
                })(this)}

                {/* <Col span={12}>
                <FormItem
                  label="入党时递交党组织是否为系统外"
                  {...formItemLayout3}
                >
                  {getFieldDecorator('isOutSystem', {
                    rules: [{ required: false, message: '必填' }],
                    initialValue:isOutSystem_state,
                  })(
                    <Select style={{width:'100%'}} onChange={this.isOutSystemOnChange}>
                      <Select.Option value="1">是</Select.Option>
                      <Select.Option value="0">否</Select.Option>
                    </Select>
                  )}
                </FormItem>
              </Col> */}
                {/* {
                !isOutSystem_state ?
                  <Col span={12}>
                    <FormItem
                      label="入党时递交党组织"
                      {...formItemLayout}
                    >
                      {getFieldDecorator('appliedOrgCode', {
                        rules: [{ required: false, message: '请选择申请时递交党组织' }],
                        initialValue: _isEmpty(basicInfo)?undefined:basicInfo['appliedOrgCode'],
                      })(
                        <OrgSelect orgTypeList={['3','4']} initValue={ basicInfo['appliedOrgName'] }  placeholder={'请选择入党时递交党组织'}/>
                      )}
                    </FormItem>
                  </Col>
                  :
                  <Col span={12}>
                    <FormItem
                      label="入党时递交党组织"
                      {...formItemLayout}
                    >
                      {getFieldDecorator('outBranchOrgName', {
                        rules: [{ required: false, message: '请选择入党时递交党组织' }],
                        initialValue: _isEmpty(basicInfo)?undefined:basicInfo['outBranchOrgName'],
                      })(
                        <Input placeholder={'请选择入党时递交党组织'}/>
                      )}
                    </FormItem>
                  </Col>
              } */}

                {/*<Col span={12}>*/}
                {/*  <FormItem*/}
                {/*    label="婚姻情况"*/}
                {/*    {...formItemLayout}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('d60Code', {*/}
                {/*      rules: [{ required: false, message: '请选择入党时递交党组织' }],*/}
                {/*      initialValue: _isEmpty(basicInfo)?undefined:basicInfo['d60Code'],*/}
                {/*    })(*/}
                {/*      <DictSelect codeType={'dict_d60'} initValue={_isEmpty(basicInfo)?undefined:basicInfo['d60Code']} backType={'object'}/>*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                {!_isEmpty(basicInfo) && !_isEmpty(d08Code) && parseInt(d08Code) < 5 && (
                  <Fragment>
                    {/* <Col span={12}>
                    <FormItem
                      label={formLabel('失联情况', tipMsg['d18Code'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('d18Code', {
                        rules: [{ required: false, message: '请选择失去联系类型' }],
                        initialValue: _isEmpty(basicInfo)?'0':basicInfo['d18Code'],
                      })(
                        <DictSelect codeType={'dict_d18'} initValue={_isEmpty(basicInfo)?'0':basicInfo['d18Code']} onChange={this.d18OnChange} backType={'object'}/>
                      )}
                    </FormItem>
                  </Col> */}
                    {/* <Col span={12}> */}
                    {/*<FormItem*/}
                    {/*  label="入党时递交党组织是否为系统外"*/}
                    {/*  {...formItemLayout3}*/}
                    {/*>*/}
                    {/*  {getFieldDecorator('isOutSystem', {*/}
                    {/*    rules: [{ required: false, message: '必填' }],*/}
                    {/*    initialValue:isOutSystem_state,*/}
                    {/*  })(*/}
                    {/*    <Switch checkedChildren="是" unCheckedChildren="否" defaultChecked={isOutSystem_state} onChange={this.isOutSystemOnChange}/>*/}
                    {/*  )}*/}
                    {/*</FormItem>*/}
                    {/* <FormItem
                      label={formLabel('失去联系时间', tipMsg['lossDate'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('lossDate', {
                        rules: [{ required: hasLost, message: '请选择失去联系时间' }],
                        initialValue: !_isNumber(basicInfo['lossDate'])?undefined:moment(basicInfo['lossDate']),
                        // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}} disabled={!hasLost}/>
                      })(

                        <Date disabledDate={this.disabledTomorrow} disabled={!canEdit}/>
                      )}
                    </FormItem> */}
                    {/* </Col> */}
                  </Fragment>
                )}
                {/* 当this.props.canEdit == mems,展示接收预备党员信息 */}
                {_get(this, 'props.canEdit[0]', undefined) == 'mems' && this.showMemsInfo()}
                <Col span={23}>
                  <FormItem label={formLabel('现居住地', tipMsg['homeAddress'])} {...formItemLayout1}>
                    {getFieldDecorator('homeAddress', {
                      rules: [
                        { required: true, message: '请填写家庭住址（8个字及以上）', min: 8 },
                        { validator: (...e) => validateLength(e, 100, 300) },
                      ],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['homeAddress'],
                    })(<Input placeholder={'请填写家庭住址'} maxLength={300} minLength={8} />)}
                  </FormItem>
                </Col>
              </Row>
            }
            {
              step == 2 && <div className={style.flex}>

                {
                  fileObj.formList.length > 0 && <Row> {
                    fileObj.formList.map((item, index) => {
                      return (
                        <Col span={6} key={index}>
                          <FormItem label={item.label} {...formItemLayout}>
                            {getFieldDecorator(`${item.key}`, {
                              rules: [
                                { required: item.required, message: `请输入${item.label}}` },
                              ],
                              // initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['name'],
                            })(this.getFormItem(item.type))}
                          </FormItem>
                        </Col>

                      )
                    })}
                  </Row>
                }

                <div className={style.flexbox}>
                  {
                    fileObj.value.map((item, index) => {
                      return (
                        <div className={style.flexbox_l} key={index}>
                          {
                            item.fileList.length > 0 && <Fragment>
                              <img className={style.previewImage} src={require('@/assets/login.png')} />
                              <div className={style.delfile} onClick={this.delFile}><LegacyIcon style={{ color: '#ccc', cursor: 'pointer' }} type="delete" /></div>
                            </Fragment>
                          }
                          {
                            item.fileList.length < 1 &&
                            <Upload
                              style={{ marginBottom: 10 }}
                              {...props}
                              onChange={(e) => this.fileChange(e, item)}
                              fileList={item.fileList}
                              showUploadList={false}>
                              <Button>上传</Button>
                            </Upload>
                          }

                        </div>
                      )
                    })
                  }
                </div>

              </div>
            }
          </div>
        </Modal>
      </Fragment>
    );
  }
}
export default Form.create()(index);
