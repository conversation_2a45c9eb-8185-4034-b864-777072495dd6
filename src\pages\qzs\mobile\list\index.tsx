import React, { useEffect, useState } from 'react';
import st from './index.less';
import { Col, Pagination, Row, Spin } from 'antd';
import { pullFile } from '@/services';
import _isEmpty from 'lodash/isEmpty';
import { _history as router } from '@/utils/method';
import moment from 'moment';
import { screenlist } from '../../screen/services';

const memSearch = (props: any) => {
  const {} = props;

  const { name, idcard, zzm } = router.location.query;

  const [mainList, setMainList] = useState([]);
  const [mainPagination, setMainPagination] = useState({
    pageNum: 1,
    pageSize: 999,
    total: 0,
    totalPage: 0,
  });
  const [loading, setLoading] = useState<any>(false);

  const getList = async (p = {}) => {
    setLoading(true);
    const {
      code: mainResCode = 500,
      data: { list: mainsList = [], ...others } = {},
    } = await screenlist({
      data: {
        pageNum: mainPagination.pageNum,
        pageSize: mainPagination.pageSize,
        idCard: idcard,
        name: name,
        joinOrgDate: zzm?.split?.('-')?.[1] || undefined,
        lastIdcard: zzm?.split?.('-')?.[2] || undefined,
        ...p,
      },
    });
    setTimeout(() => {
      setLoading(false);
    }, 1000 * 0.5);
    if (mainResCode == 0) {
      for (let i = 0; i < mainsList.length; i++) {
        const item: any = mainsList[i];
        if (_isEmpty(item.photo)) {
          item['photo_base64'] = require('../../../../assets/head.jpg');
          continue;
        }
        const base64 = await pullFile({ path: item.photo });
        item['photo_base64'] = base64;
      }
      setMainList(mainsList);
      setMainPagination({ ...others, pageNum: others.pageNumber });
    }
  };
  const onPageChange = async (pageNum, pageSize) => {
    getList({ pageNum, pageSize });
  };

  const onClick = (record) => {
    sessionStorage.setItem('sddj_mb', JSON.stringify(record));
    router.push('/qzs/mobile/sddj');
  };

  useEffect(() => {
    if ((name && idcard) || zzm) {
      getList();
    }
  }, [name, idcard, zzm]);
  return (
    <div className={st.searchMem}>
      <div className={st.center}>
        <Spin wrapperClassName={st.tit1} spinning={loading}>
          <div className={st.box}>
            {mainList.map((it: any, index) => {
              return (
                <div key={index} className={st.listItem} onClick={() => onClick(it)}>
                  <div className={st.photo}>
                    <img src={it.photo_base64} alt="" />
                  </div>
                  <div className={st.info}>
                    <div className={st.name}>
                      <div>{it.name}</div>
                      <span>{it.d06Name}</span>
                    </div>
                    <div className={st.desc}>
                      出生年月:{moment(it.birthday).format('YYYY年M月')}
                    </div>
                    <div className={st.desc}>
                      政治生日:{moment(it.joinOrgDate).format('YYYY年M月')}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default memSearch;
