import React from 'react';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _ceil from 'lodash/ceil';

export const cardConfig = [
  {
    key:'5001',
    value:{
      icon:'team',
      coverImg:require('@/components/CardsGroup/assets/transfer/zongshu.jpg'),
      iconColor:'#17C1C5',
      title:'转接总数',
      suffix:'人',
      action:'/api/chart/transfer/getTransferTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['transferType'] === '转接总人数'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['transferType'] === '整建制转接'){zNum = item['count']}
            if(item['transferType'] === '系统内转接'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>整建制转接:{zNum}人</div>
            <div>系统内转接:{yNum}人</div>
          </div>
      )
      }
    },
  },
  {
    key:'5002',
    value:{
      icon:'usergroup-delete',
      coverImg:require('@/components/CardsGroup/assets/transfer/zhuanchu.jpg'),
      iconColor:'#FFBABA',
      title:'转出总数',
      suffix:'人',
      action:'/api/chart/transfer/getTransferOutTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['transferType'] === '转出总人数'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['transferType'] === '系统外转接'){zNum = item['count']}
            if(item['transferType'] === '系统内转接'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>系统外转接:{zNum}人</div>
            <div>系统内转接:{yNum}人</div>
          </div>
        )
      }
    },
  },
  {
    key:'5003',
    value:{
      icon:'usergroup-add',
      coverImg:require('@/components/CardsGroup/assets/transfer/zhuanru.jpg'),
      iconColor:'#FF403A',
      title:'转入总数',
      suffix:'人',
      action:'/api/chart/transfer/getTransferInTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['transferType'] === '转入总人数'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['transferType'] === '系统外转接'){zNum = item['count']}
            if(item['transferType'] === '系统内转接'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>系统外转接:{zNum}人</div>
            <div>系统内转接:{yNum}人</div>
          </div>
        )
      }
    },
  },
  {
    key:'5004',
    value:{
      icon:'apartment',
      coverImg:require('@/components/CardsGroup/assets/transfer/cishu.jpg'),
      iconColor:'#35C0A0',
      title:'整建制转接次数',
      suffix:'次',
      action:'/api/chart/transfer/getTransferOrgTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['transferType'] === '整建制转接次数'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['transferType'] === '系统外整建制次数'){zNum = item['count']}
            if(item['transferType'] === '系统内整建制次数'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>系统外次数:{zNum}次</div>
            <div>系统内次数:{yNum}次</div>
          </div>
        )
      }
    },
  },
  {
    key:'5005',
    value:{
      icon:'flag',
      coverImg:require('@/components/CardsGroup/assets/transfer/zhengjianzhi.jpg'),
      iconColor:'#FFC549',
      title:'整建制转接人数',
      suffix:'人',
      action:'/api/chart/transfer/getTransferOrgMemTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['transferType'] === '整建制转接次数'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['transferType'] === '系统外整建制人数'){zNum = item['count']}
            if(item['transferType'] === '系统内整建制人数'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>系统外转接:{zNum}人</div>
            <div>系统内转接:{yNum}人</div>
          </div>
        )
      }
    },
  },
];
export const chartConfig = [];
