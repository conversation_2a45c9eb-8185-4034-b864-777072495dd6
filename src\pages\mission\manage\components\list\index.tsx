import React,{Fragment} from 'react';
import style from './index.less';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Divider, Popconfirm, Menu, Button, Input, Dropdown, Switch } from 'antd';
import RuiFilter from 'src/components/RuiFilter';
import ListTable from 'src/components/ListTable';
import NowOrg from 'src/components/NowOrg';
import WhiteSpace from '@/components/WhiteSpace';
import Details from '../../../components/details';
import Tip from '@/components/Tip';
import Add from '../modelStyle';
import _isNumber from 'lodash/isNumber';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import {withContext} from 'src/utils/global.jsx';
import {connect} from "dva";
import {_history as router} from "@/utils/method";
import moment from 'moment';
import {getSession} from "@/utils/session";
import qs from 'qs';
import {setListHeight} from "@/utils/method";
const {Search} = Input;
@withContext
@connect(({mission,commonDict,loading})=>({mission,commonDict,loading}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state={
      filterHeight:100,
      filterChecked:{}
    }
  }
  componentDidMount(): void {
    setListHeight(this);
  }
  // 筛选
  filterChange=(val)=>{
    this.setState({filterChecked:val});
    this.props.dispatch({
      type:'mission/updateState',
      payload:{
        filter:val
      }
    });
    this.action()
  };
  // 分页
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  action=(val?:object)=>{
    const {pagination={}}=this.props.mission;
    const {current = 1,pageSize = 10} = pagination;
    const roles = getSession('roles') || {};
    this.props.dispatch({
      type:'mission/getList',
      payload:{
        data:{
          orgCode:roles['managerOrgId'],
          orgOrgCode:roles['managerOrgCode'],
          pageNum:current,
          pageSize,
          ...val
        }
      }
    })
  };
  search=(value)=>{
    this.props.dispatch({
      type:'mission/updateState',
      payload:{
        taskName:value
      }
    });
    this.action();
  };
  addNew=()=>{
    this['addModal'].open();
  };
  edit= async (record)=>{
    const {code} = record;
    const res = await this.props.dispatch({
      type:'mission/getDtail',
      payload:{
        code
      }
    });
    const {code:resCode = 500, data} = res;
    if(resCode === 0){
      this['addModal'].open(data);
    }

  };
  showDetails = async (record)=>{
    const {code,taskStatus} = record;
    const res = await this.props.dispatch({
      type:'mission/getDtail',
      payload:{
        code
      }
    });
    const {code:resCode = 500, data} = res;
    if(resCode === 0){
      this['Details'].open(record);
    }
  };
  del= async (val)=>{
    const {code = ''} = val || {};
    const res = await this.props.dispatch({
      type:'mission/del',
      payload:{
        code
      }
    });
    const {code:resCode = 500 } = res;
    if(resCode === 0){
      Tip.success('操作提示','操作成功');
      this.action();
    }
  };
  onclose=()=>{
    this.action()
  };
  render(): React.ReactNode {
    const {mission ={},loading:{effects = {}} ={},commonDict} = this.props;
    const {list, pagination,taskName,filter} = mission;
    const {current = 1,pageSize = 10} = pagination;
    const {filterHeight}=this.state;

    const filterData = [
      {
        key:'status',name:'任务状态',value:[{key:'1',name:'未开始',},{key:'2',name:'进行中'},{key:'3',name:'已结束'}],
      }
    ];
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:60,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'任务名称',
        dataIndex:'taskName',
        width:120,
      },
      {
        title:'任务分值',
        dataIndex:'taskFraction',
        width:90,
      },
      {
        title:'任务周期',
        dataIndex:'taskCycle',
        width:90,
        render:(text)=>{
          let name = '';
          switch (text) {
            case 1:
              name = '一次';
              break;
            case 2:
              name = '每月';
              break;
            case 3:
              name = '每季度';
              break;
            case 4:
              name = '每年';
              break;
          }
          return (
            <div>{name}</div>
          )
        }
      },
      {
        title:'任务对象',
        dataIndex:'taskObjectType',
        width:90,
        render:(text)=>{
          let name = '';
          switch (text) {
            case 1:
              name = '发送给党员';
              break;
            case 2:
              name = '发送给组织';
              break;
          }
          return (
            <div>{name}</div>
          )
        }
      },
      {
        title:'开始时间',
        dataIndex:'startDate',
        width:90,
        render:(text)=>{
          return (
            <div>{ !!text ? moment(text).format('YYYY-MM-DD') : '暂无'}</div>
          )
        }
      },
      {
        title:'结束时间',
        dataIndex:'endDate',
        width:90,
        render:(text)=>{
          return (
            <div>{ !!text ? moment(text).format('YYYY-MM-DD') : '暂无'}</div>
          )
        }
      },
      {
        title:'任务状态',
        dataIndex:'taskStatus',
        width:90,
        render:(text)=>{
          let name = '';
          // 1.未开始，2进行中，3已结束
          switch (text) {
            case 1:
              name = '未开始';
              break;
            case 2:
              name = '进行中';
              break;
            case 3:
              name = '已结束';
              break;
          }
          return (
            <div>{name}</div>
          )
        }
      },
      {
        title:'操作',
        width:150,
        dataIndex:'action',
        render:(text,record)=>{
          return(
            <Fragment>
              <a onClick={()=>this.showDetails(record)}>查看</a>
              <Divider type="vertical"/>
              <a onClick={()=>this.edit(record)}>编辑</a>
              <Divider type="vertical"/>
              <Popconfirm title={'是否删除？'} onConfirm={()=>this.del(record)}>
                <a className={'del'} >删除</a>
              </Popconfirm>
            </Fragment>
          )
        },
      },
    ];

    return (
      <Fragment>
        <NowOrg
          extra={
            <React.Fragment>
              <Button htmlType={'button'} type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.addNew}>新增</Button>
              <Search style={{width:200,marginLeft:16}} onSearch={this.search} placeholder={'请输入检索关键词'}/>
            </React.Fragment>
          }
        />
        <RuiFilter
          data={filterData}
          onChange={this.filterChange}
        />
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:filterHeight}} columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
        <Add ref={e=>this['addModal']=e} onClose={this.onclose}/>
        <Details ref={e=>this['Details'] = e}/>
      </Fragment>
    );
  }
}
