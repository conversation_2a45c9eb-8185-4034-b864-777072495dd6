import React, { Fragment } from 'react';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Card, Progress } from 'antd';
import Charts from 'ant-design-pro/lib/Charts';
import style from './index.less';
import CountUp from 'react-countup';
import { isEmpty } from '@/utils/method';
import Echarts from '@/components/Echarts';

const { MiniArea } = Charts;
interface Interface {
  text: string;
  name: string;
  value: string | number;
  suf: string;
  chart?: any;
  extraVal: string | number;
  extraVal2: string | number;
  icon?: any;
  url?: any;
  prefix?: any;
  suffix?: any;
  decimals?: any;
  subText?: any;
}
export default class index extends React.Component<Interface, any> {
  constructor(props) {
    super(props);
    this.state = {};
  }
  render(): React.ReactNode {
    const { activeData } = this.state;
    const {
      text,
      name,
      value,
      suf,
      chart,
      extraVal,
      extraVal2,
      url,
      icon,
      prefix,
      suffix,
      subText,
      decimalPlaces,
    }: any = this.props;
    let option = {
      // backgroundColor: 'green',
      title: [
        {
          text: text,
          left:'47%',
          top: '80%',
          textAlign: 'center',
          textStyle: {
            fontWeight: 'normal',
            fontSize: '16',
            color: 'white',
            textAlign: 'center',
          },
        },
      ],
      series: [
        {
          // name: text,
          type: 'pie',
          clockWise: false,
          radius: [46, 50],
          itemStyle: {
            normal: {
              color: 'white',
              // shadowColor: 'pink',
              shadowBlur: 0,
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
            },
          },
          hoverAnimation: false,
          center: ['50%', '38%'],
          data: [
            {
              value: value,
              label: {
                normal: {
                  formatter: function (params) {
                    return params.value + '%';
                  },
                  position: 'center',
                  show: true,
                  textStyle: {
                    fontSize: '30',
                    fontWeight: '400',
                    color: 'white',
                  },
                },
              },
            },
            {
              value: 100-value,
              name: 'invisible',
              itemStyle: {
                normal: {
                  color: 'rgb(170,50,33)',
                },
                emphasis: {
                  // color: 'rgb(170,50,33)',
                  // color: 'red',
                },
              },
            },
          ],
        },
      ],
    };
    return (
      <div className={style.box}>
        {/* <div className={style.avatorInfo}> */}
          {/* <div className={style.number}>
            <div className={style.num}>
              <CountUp
                className="custom-count"
                start={0}
                end={value}
                prefix={prefix}
                suffix={suffix}
                decimals={decimalPlaces}
              />
              <span className={style.suf}>{suf}</span>
            </div>
            <div className={style.text}>{text}</div>
          </div> */}

          {/* <div className={style.avator}>
            { !isEmpty(url) &&  <img src={url} className={style.img}/> }
            { !isEmpty(icon) &&  <LegacyIcon type={icon} className={style.icon}/> }
          </div> */}
        {/* </div> */}
        <div className={style.echart}>
          <Echarts style={{height:150}} option={option} />
        </div>
        {/*<div className={name !== 'online' ?  style.percent : style.percent2}>*/}
        {/*  {*/}
        {/*    name === 'online' ?*/}
        {/*      //@ts-ignore*/}
        {/*      <MiniArea*/}
        {/*        animate={true}*/}
        {/*        line*/}
        {/*        borderWidth={1}*/}
        {/*        height={30}*/}
        {/*        scale={{ y: { tickCount: 3 } }}*/}
        {/*        yAxis={{ tickLine: false, label: false, title: false, line: false, }}*/}
        {/*        data={chart}*/}
        {/*      />*/}
        {/*      :*/}
        {/*      <Progress percent={chart} size="small" showInfo={false} strokeWidth={11}/>*/}
        {/*  }*/}
        {/*</div>*/}
        <div className={style.line} />
        <div className={style.extraInfo}>
          {subText ? (
            subText()
          ) : (
            <Fragment>
              {extraVal} {extraVal2}
            </Fragment>
          )}
        </div>
      </div>
    );
  }
}
