import React, { useEffect, useState, useImperativeHandle } from 'react';
import request from '@/utils/request';
import { Space,Select, Modal } from 'antd';
import tip from '@/components/Tip';
import Structure from './Structure';
import {connect} from 'dva';
import qs from 'qs';
function cond(props,ref) {
  const [visible,setVisible]=useState(false);
  const [tree,setTree]=useState([]);
  const [list,setList]:any=useState([]);
  const [edit,setEdit]:any=useState([]);
  const [msg,setMsg]:any=useState([]);
  const [codeTableCol,setCodeTableCol]=useState([]);
  const [dictName,setDictName]=useState();
  const {callback}=props;
  useEffect(()=>{
    request('/api/table/tableSelect').then(res=>{
      if(res['code']=='0'){
        setTree(res['data']);
      }
    });
    request('/api/table/tableAll').then(res=>{
      if(res['code']=='0'){
        let data:any=[];
        for(let k in res['data']){
          const item=res['data'][k];
          if(Array.isArray(item)){
            for(let obj of item){
              data.push({
                ...obj,
                tableName:k,
              })
            }
          }
        }
        setCodeTableCol(data)
      }
    })
  },[]);
  useImperativeHandle(ref, () => ({
    open: (val: any) => {
      setVisible(true);
      setEdit(val);
      if(val['colId']){
        request(`/api/table/tableColSecect?${qs.stringify({
          id:val['colId'],
          val:val['key']
        })}`).then(res=>{
          if(res['code']=='0'){
            let data:any=[];
            for(let obj of res['data']){
              data.push(JSON.parse(obj['data']))
            }
            setList(data);
          }
        });
      }
    },
  }));
  const listChange=(data,msg)=>{
    console.log(data,'ddddddddddddddd')
    setList([...data]);
    setMsg([...msg]);
  };
  const onCancel=()=>{
    setVisible(false);
    setList([]);
  };
  const onOk=()=>{
    let data:any=[];
    for(let obj of list){
      data.push({
        colId:edit['colId'],
        colName:edit['name'],
        colValue:edit['key'],
        data:JSON.stringify(obj),
        compareType:obj['compareType'],
        compareCol:obj['compareCol'],
        compareValue:obj['compareValue'],
        compareId:obj['compareId'],
      })
    }
    request(`/api/table/tableCompare`,{
      method:'POST',
      body:{
        data:data
      }
    }).then(res=>{
      if(res['code']=='0'){
        tip.success('提示信息','条件设置成功');
        onCancel();
        callback && callback();
      }
    });
  };
  return(
    <Modal
      title={'条件配置'}
      visible={visible}
      onCancel={onCancel}
      onOk={onOk}
      width={1360}
      destroyOnClose={true}
      bodyStyle={{maxHeight:'80vh',overflow:'auto'}}
    >
      {
        visible && <Structure codeTable={tree} codeTableCol={codeTableCol} list={list} desc={msg} onChange={listChange} commonDict={props.commonDict}/>
      }
    </Modal>
  )
}
export default connect(({commonDict}:any)=>({commonDict}),undefined,undefined,{forwardRef:true})(React.forwardRef(cond))
