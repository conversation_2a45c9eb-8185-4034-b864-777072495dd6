import React, { Fragment } from 'react';
import style from './index.less';
import moment from 'moment';
import { Modal,List,Avatar,Divider,Spin,Button,Input,Tabs } from 'antd';
import ListView from '@/components/List';
import { getSession } from 'src/utils/session';
import { throttle } from 'src/utils/method';
import _isNumber from 'lodash/isNumber';
const { TabPane } = Tabs;
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible:false,
      record:{},


    }
  }
  getTalkList = async ({code,pageNum,pageSize = 10})=>{
    const {talkList} = this.state;
    const res = await this.props.dispatch({
      type:'notice/getTalkList',
      payload:{
        data:{
          messageCode:code,
          pageNum,
          pageSize,
        }
      }
    });
    const {list = [],totalRow = 0, pageSize:resPageSize,pageNumber} = res;
    this.setState({
      talkList:talkList.concat(list),
      hasMore:list.length === 10,
      talkPagination:{
        current:pageNumber,
        pageSize:resPageSize,
        total:totalRow,
      }
    })
  };

  open=(record)=>{
    const {messageCode} = record;
    this.setState({
      visible:true,record
    });
    // this.getTalkList({code:messageCode,pageNum:1});
  };
  handleOk=()=>{

  };
  destory=()=>{
    this.setState({
      record:{},
      talkList:[],
      talkPagination:{},
      hasMore:true
    })
  };
  handleCancel=()=>{
    this.destory();
    this.setState({
      visible:false
    })
  };
  onLoadMore = () => {
    const {hasMore,talkPagination = {}} = this.state;
    const {current = 1} = talkPagination;
    if(hasMore){
      let org=getSession('org')|| {};
      this.getTalkList({code:org['orgCode'],pageNum:current+1})
    }
  };
  renderItem=(item)=>{
    return (
      <List.Item>
        <List.Item.Meta
          avatar={<Avatar>{item['receiveName']}</Avatar>}
          title={<span >{item['name']}</span>}
          description={<div className={style.info}>
            {item['replyContext']}
            <span>{_isNumber(item['time'])?moment(item['time']).format('YYYY-MM-DD') : '暂无'}</span>
          </div>}
        />
      </List.Item>
    )
  };
  renderItem2=(item)=>{
    return (
      <List.Item>
        <a onClick={()=>{console.log(item,'sss')}}>{item['name']}</a>
      </List.Item>

    )
  };
  render() {
    const {loading:{effects = {}} = {},detailsType} = this.props;
    const {visible,record,talkList,hasMore} = this.state;
    const {messageNme = '', name = '',messageContext = '', context = '',time = '',isReply = 0} = record;
    return (
      <Modal
        title={'详情'}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        width={1200}
        bodyStyle={{height:600,overflow:'auto',padding:'0 200px',background:'#fff'}}
      >
        <div style={{background:'#fff',padding:'0 20px'}}>
          <div>
            <div className={style.tit}>
              <h2>
                {detailsType === 'mySend' ? name : messageNme}
              </h2>
            </div>
            <div className={style.main}>
              {detailsType === 'mySend' ? context : messageContext}
            </div>
            <div className={style.footer}>
              <div>
                发布人：哎呀呀
              </div>
              <div>
                发布时间：{moment(time).format('YYYY年MM月DD日')}
              </div>
            </div>
          </div>

          {
            detailsType === 'mySend' &&
              <Fragment>
                {
                  isReply === 2 &&
                    <Fragment>
                      <Divider orientation={'left'}>评论</Divider>
                      <ListView action={'/api/notice/getNoticeReply'} payload={{reply:true,code:record['code']}} renderItem={this.renderItem} method={'Get'}/>
                    </Fragment>
                }
                {
                  isReply === 3 &&
                  <Tabs defaultActiveKey="1" >
                    <TabPane tab="已提交" key="1">
                      <ListView action={'/api/notice/getNoticeReply'} payload={{reply:true,code:record['code']}} renderItem={this.renderItem2} method={'Get'} listExtraConfig={{grid:{ gutter: 16, column: 4 }}}/>
                    </TabPane>
                    <TabPane tab="未提交" key="2">
                      <ListView action={'/api/notice/getNoticeReply'} payload={{reply:false,code:record['code']}} renderItem={this.renderItem2} listExtraConfig={{grid:{ gutter: 16, column: 4 }}}/>
                    </TabPane>
                  </Tabs>
                }
              </Fragment>
          }
        </div>

      </Modal>
    );
  }
}
