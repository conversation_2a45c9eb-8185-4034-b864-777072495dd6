/**
 * 领导班子
 **/
import React, { Fragment } from 'react'
import { connect } from "dva";
import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, Collapse, Modal, Popconfirm, Tooltip, Divider } from 'antd';
import AddJcInfo from './addJcInfo';
import AddMember from './addMember';
import Tip from '@/components/Tip';
import head from '@/assets/head.jpg';
import styles from './leader.less';
import moment from 'moment';
import { itteeList, itteeUP, committeeBackOut, orgElectList, itteeDel } from '../../../services/org.js';
import tip from '@/components/Tip';
import DateTime from '@/components/Date';
import { isEmpty } from '@/utils/method';
import EndOfTerm from '@/pages/org/list/subpage/addoredit/EndOfTerm';

import CTbale, { PanelTable, PanelListTable } from '@/components/CollapseTable';

const Panel = Collapse.Panel;
// @ts-ignore
@connect(({ org, loading }) => ({ org, loading }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      type: 'add',
      type1: 'addMem',
      visible: false,
      _key: undefined,
      editMemTypeWay: 'default',
      currentClickJc:{} // 点击“届内历史任职”时，保存当前点击的届次
    }
  }
  del = async (e, item) => {
    e.stopPropagation();
    const obj = await this.props.dispatch({
      type: 'org/orgElectDel',
      payload: {
        data: {
          code: item['code'],
        }
      }
    });
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', '删除成功');
      this['CTbale'].getList()
    }
  };
  getHistoryList = async (item, key) => {
    const payload = {
      electCode: item['code'],
      keys: key,
      leave: 0,
      pageNum: 1,
      pageSize: 100,
    }
    itteeList({ ...payload, leave: 1 }).then(res => {
      let data: any = [];
      if (res['code'] == '0') {
        data = res['data']['list'];
      }
      this.setState({
        hisData: data,
        visible: true,
        _key: key,
        currentClickJc: item,
      })
    })
  };
  reSetHistoryList = async (item, key) => {
    const payload = {
      electCode: item['code'],
      keys: key,
      leave: 0,
      pageNum: 1,
      pageSize: 100,
    }
    itteeList({ ...payload, leave: 1 }).then(res => {
      let data: any = [];
      if (res['code'] == '0') {
        data = res['data']['list'];
      }
      this.setState({
        hisData: data,
      })
    })
  };
  header = (item, key) => {
    return (
      <span className={styles.header}>
        <span>换届日期：{item['tenureStartDate'] && moment(item['tenureStartDate']).format('YYYY-MM-DD')}~{item['tenureEndDate'] && moment(item['tenureEndDate']).format('YYYY-MM-DD')}</span>
        {/*<span>选举方式：{item['electName']}</span>*/}
        <div>
          <a onClick={(e) => {
            e.stopPropagation();
            this['CTbale'].setActiveKeys(key);
            this.getHistoryList(item, key);
          }}>届内历史任职</a>
          {/* 判断是不是历史届次  0就不是 1就是  是的不展示编辑按钮 */}
          {item?.isHistory == 0 && <a onClick={(e) => this.editJc(e, item)}>编辑</a>}
          {/* 党组织班子和单位的行政班子， 届次的删除按钮 ， 屏蔽了。 --王察 */}
          {/* <div style={{display: 'inline-block'}} onClick={(e:any)=>e.stopPropagation()}>
            <Popconfirm title="是否删除该信息?" onConfirm={(e)=>this.del(e,item)} okText="是" cancelText="否" onCancel={(e:any)=>e.stopPropagation()}>
              <a onClick={(e)=>e.stopPropagation()} className={'del'}>删除</a>
            </Popconfirm>
          </div> */}
        </div>
      </span>
    )
  };
  editJc = (e, item) => {
    e.stopPropagation();
    this.setState({ type: 'edit', dataInfo: item }, () => this['AddJcInfo'].showModal());
  };
  editMem = (item, edit = false) => {
    this.setState({ type: 'edit', dataInfo: item }, () => {
      this['AddMember'].showModal(edit);
    });
  };
  end = (item, key) => {
    this['EndOfTermRef'].open({ ...item, key });
  };
  clos = () => {
    this.setState({
      visible: false,
      hisData: undefined,
      currentClickJc: {}
    })
  }
  render() {
    const { type, hisData = [], visible } = this.state;
    const { org } = this.props;
    const { electList = [], iteList = [], basicInfo: { linkedDTOList = [], linkedTopDTOList = [], d02Code = '' } = {} } = org;

    let isVillageCommunity = false, isHasMiddleManagement = false;
    let val = d02Code == '2' ? linkedTopDTOList : linkedDTOList;
    if (!isEmpty(val)) {
      // 关联党组织得单位信息是村和社区的时候
      let find = val?.find(it => (it?.unit?.d04Code || 'zzzzz').startsWith('92'));
      // 组织只有关联的单位是非公企业的时候才会出的
      let find2 = val?.find(it => !(it?.unit?.d04Code || 'zzzzz').startsWith('41') && (it?.unit?.d04Code || 'zzzzz').startsWith('4'));
      if (find2) {
        isHasMiddleManagement = true;
      }
      if (find) {
        isVillageCommunity = true;
      }
    }
    // 其他通用党组织.xlsx
    // 姓名	是否本党组织党员	党内职务	任职日期
    let cols: any = [
      {
        title: '姓名',
        dataIndex: 'memName',
        width: 100,
      },
      {
        title: '是否本党组织党员',
        dataIndex: 'memTypeCode',
        width: 100,
        render: (text) => {
          return text == 1 ? '是' : '否'
        }
      },
      {
        title: '党内职务',
        dataIndex: 'd022Name',
        width: 100,
      },
      {
        title: '任职时间',
        dataIndex: 'startDate',
        width: 100,
        align: 'center',
        render: (text, record) => {
          if (text) {
            return text ? moment(text).format('YYYY-MM-DD') : ''
          }
        },
      },
      {
        title: '任职到期时间',
        dataIndex: 'empEndDate',
        width: 100,
        align: 'center',
        render: (text, record) => {
          if (text) {
            return text ? moment(text).format('YYYY-MM-DD') : ''
          }
        },
      },
    ]
    // 乡镇（街道)党委班子.xlsx
    // 姓名	是否本党组织党员	党内职务	任职日期	班子成员来源
    if (!isEmpty(val)) {
      let find = val?.find(it => (it?.unit?.d04Code || 'zzzzz').startsWith('91'));
      if (find) {
        cols = [
          {
            title: '姓名',
            dataIndex: 'memName',
            width: 100,
          },
          {
            title: '是否本党组织党员',
            dataIndex: 'memTypeCode',
            width: 100,
            align: 'center',
            render: (text) => {
              return text == 1 ? '是' : '否'
            }
          },
          {
            title: '党内职务',
            dataIndex: 'd022Name',
            width: 100,
            align: 'center',
          },
          {
            title: '任职日期',
            dataIndex: 'startDate',
            width: 100,
            align: 'center',
            render: (text, record) => {
              if (text) {
                return text ? moment(text).format('YYYY-MM-DD') : ''
              }
            },
          },
          {
            title: '任职到期时间',
            dataIndex: 'empEndDate',
            width: 100,
            align: 'center',
            render: (text, record) => {
              if (text) {
                return text ? moment(text).format('YYYY-MM-DD') : ''
              }
            },
          },
          {
            title: '班子成员来源',
            dataIndex: 'd138Name',
            width: 100,
            align: 'center',
          },
        ]
      }
    }

    // 村、社区党组织.xlsx
    // 姓名	党内职务	任职日期	是否参加养老保险	人员来源	是否参加县级集中轮训	是否任职选调生	报酬（万元/年）
    if (!isEmpty(val)) {
      let find = val?.find(it => (it?.unit?.d04Code || 'zzzzz').startsWith('922') || (it?.unit?.d04Code || 'zzzzz').startsWith('923'));
      if (find) {
        cols = [
          {
            title: '姓名',
            dataIndex: 'memName',
            width: 100,
          },
          {
            title: '党内职务',
            dataIndex: 'd022Name',
            align: 'center',
            width: 100,
          },
          {
            title: '任职日期',
            dataIndex: 'startDate',
            width: 100,
            align: 'center',
            render: (text, record) => {
              if (text) {
                return text ? moment(text).format('YYYY-MM-DD') : ''
              }
            },
          },
          {
            title: '任职到期时间',
            dataIndex: 'empEndDate',
            width: 100,
            align: 'center',
            render: (text, record) => {
              if (text) {
                return text ? moment(text).format('YYYY-MM-DD') : ''
              }
            },
          },
          {
            title: '是否参加养老保险',
            dataIndex: 'endowmentInsuranceForUrbanEmployees',
            width: 100,
            align: 'center',
            render: (text) => {
              return text == 1 ? '是' : '否'
            }
          },
          {
            title: '人员来源',
            dataIndex: 'd121Name',
            width: 100,
            align: 'center',
          },
          {
            title: '是否参加县级集中轮训',
            dataIndex: 'hasPartTraining',
            width: 100,
            align: 'center',
            render: (text) => {
              return text == 1 ? '是' : '否'
            }
          },
          {
            title: '是否任职选调生',
            dataIndex: 'hasVillageTransferStudent',
            width: 100,
            align: 'center',
            render: (text) => {
              return text == 1 ? '是' : '否'
            }
          },
          {
            title: '报酬(万元/年）',
            dataIndex: 'reward',
            width: 100,
            align: 'center',
          }
        ]
      }
    }

    return (
      <div style={{ padding: '0 20px' }}>
        <Modal
          title={'届内历史任职'}
          visible={visible}
          onOk={this.clos}
          onCancel={this.clos}
          width={630}
          bodyStyle={{ maxHeight: '60vh', overflow: 'auto' }}
        >
          {
            hisData.map((item, ind) => {
              return (
                <div key={ind} className={styles.panel_body}>
                  <Tooltip title={item['d022Name']}>
                    <div><img src={head} style={{ width: 128, height: 158 }} /></div>
                  </Tooltip>
                  <div>
                    <h4>{item.memName}</h4>
                    <h4>
                      {/* 2025/3/21 内历史任职中的编辑直接不要，只能撤销  */}
                      {/* <a onClick={() => {
                        this.setState({ editMemTypeWay: 'history' })
                        this.editMem(item);
                      }} style={{ marginRight: 4 }}>编辑</a> */}
                      {/* 单位和组织的历史届次的届内历史任职不能撤销 */}
                      { this.state?.currentClickJc?.isHistory != 1 ? <a onClick={async () => {
                        const { code = 500 } = await committeeBackOut({ data: item });
                        if (code === 0) {
                          this[`PanelTable_${this.state._key}`].getList();
                          this.setState({
                            hisData: this.state.hisData.filter(it => it.code != item.code)
                          })
                        }
                      }}>撤销</a> : ''}
                    </h4>
                  </div>
                </div>
              )
            })
          }
        </Modal>
        <AddJcInfo
          title={type === 'edit' ? '编辑届次信息' : '新增届次信息'}
          wrappedComponentRef={(e) => this['AddJcInfo'] = e}
          onClose={() => this.setState({ dataInfo: undefined, type: undefined })}
          queryList={() => {
            this['CTbale'].getList()
          }}
          {...this.props}
          {...this.state}
        >
          <Button type="primary" icon={<PlusOutlined />} style={{ marginBottom: '10px' }}>添加届次信息</Button>
        </AddJcInfo>

        <CTbale
          tableActionOtherQueries={{ electOrgCode: this.props.org.basicInfo.orgCode }}
          tableListAction={orgElectList}
          ref={e => this['CTbale'] = e}
          mains={(item, index) => {
            const isHistory = item?.isHistory == 0
            return (
              <React.Fragment>
                <AddMember
                  title={type === 'edit' ? (isHistory ? '编辑班子成员' : '查看班子成员') : '新增班子成员'}
                  wrappedComponentRef={(e) => this['AddMember'] = e}
                  onClose={() => this.setState({ dataInfo: undefined, type: undefined })}
                  queryList={() => {
                    this[`PanelTable_${index}`].getList()
                    this.reSetHistoryList(item, index)
                  }}
                  elect={item} /*届次信息*/
                  iteListData={[]} /*当前届次的列表数据*/
                  {...{ isVillageCommunity, isHasMiddleManagement }}
                  {...this.props}
                  {...this.state}
                >
                  {isHistory && <Button type="primary" style={{ marginRight: 10 }}>添加人员</Button>}
                </AddMember>
                <div style={{ height: 10 }}></div>
                <PanelListTable
                  rowKey='id'
                  ref={e => this[`PanelTable_${index}`] = e}
                  data={item}
                  mainsListAction={itteeList}
                  mainsActionOtherQueries={{ electCode: item['code'], leave: 0 }}
                  columns={[
                    ...cols,
                    {
                      title: '操作',
                      dataIndex: 'action',
                      width: 200,
                      render: (text, record) => {
                        return (
                          <div>
                            {!(isHistory) && <Fragment>
                              <a onClick={() => {
                                this.setState({ editMemTypeWay: 'default' })
                                this.editMem(record, true)
                              }}>查看</a>
                            </Fragment>}
                            {isHistory && <Fragment>
                              <a onClick={() => {
                                this.setState({ editMemTypeWay: 'default' })
                                this.editMem(record)
                              }}>编辑</a>
                              <Divider type="vertical" />
                              <a onClick={() => this.end(record, index)}>转为历史任职</a>
                              <Divider type="vertical" />
                              <Popconfirm title="删除仅针对于错误录入的情况，离任请选择转为历史任职" onConfirm={async () => {
                                const { code = 500 } = await itteeDel({
                                  data: {
                                    code: record['code'],
                                    electCode: item['code'],
                                  }
                                });
                                if (code === 0) {
                                  Tip.success('操作提示', '操作成功');
                                  this[`PanelTable_${index}`].getList();
                                }
                              }} okText="是" cancelText="否">
                                <a onClick={e => e.stopPropagation()}>删除</a>
                              </Popconfirm>
                            </Fragment>}
                          </div>
                        )
                      },
                    },
                  ]}
                />
              </React.Fragment>
            )
            return (
              <PanelTable
                ref={e => this[`PanelTable_${index}`] = e}
                data={item}
                mainsListAction={itteeList}
                mainsActionOtherQueries={{ electCode: item['code'], leave: 0 }}
                add={() => {
                  return (
                    <AddMember
                      title={type === 'edit' ? '编辑班子成员' : '新增班子成员'}
                      wrappedComponentRef={(e) => this['AddMember'] = e}
                      onClose={() => this.setState({ dataInfo: undefined, type: undefined })}
                      queryList={() => {
                        this[`PanelTable_${index}`].getList()
                        this.reSetHistoryList(item, index)
                      }}
                      elect={item} /*届次信息*/
                      iteListData={[]} /*当前届次的列表数据*/
                      {...{ isVillageCommunity, isHasMiddleManagement }}
                      {...this.props}
                      {...this.state}
                    >
                      <div className={styles.add}><PlusOutlined style={{ fontSize: '50px', transform: 'translateY(100%)' }} /></div>
                    </AddMember>
                  )
                }}
                linkEdit={(items) => {
                  return (
                    <React.Fragment>
                      <a onClick={() => {
                        this.setState({ editMemTypeWay: 'default' })
                        this.editMem(items)
                      }}>编辑</a>
                      <a onClick={() => this.end(items, index)}>转为历史任职</a>
                      <Popconfirm title="删除仅针对于错误录入的情况，离任请选择转为历史任职" onConfirm={async () => {
                        const { code = 500 } = await itteeDel({
                          data: {
                            code: items['code'],
                            electCode: item['code'],
                          }
                        });
                        if (code === 0) {
                          Tip.success('操作提示', '操作成功');
                          this[`PanelTable_${index}`].getList();
                        }
                      }} okText="是" cancelText="否">
                        <a onClick={e => e.stopPropagation()}>删除</a>
                      </Popconfirm>
                    </React.Fragment>
                  )
                }} />
            )
          }}
          panelHeader={(obj, listLndex) => {
            return this.header(obj, listLndex)
          }} />

        <EndOfTerm ref={e => this['EndOfTermRef'] = e}
          {...{ isVillageCommunity }}
          onOK={(e) => {
            this[`PanelTable_${e.key}`].getList();
          }} />
      </div>
    );
  }
}
