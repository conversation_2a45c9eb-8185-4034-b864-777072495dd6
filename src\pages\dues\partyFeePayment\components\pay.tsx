/**
 * 新增/编辑  党费交纳
 * */

import React, { useState, useRef, useEffect, useImperativeHandle } from 'react';
import {
  Input,
  Select,
  Form,
  Modal,
  Tabs,
  Button,
  Divider,
  Popconfirm,
  Space,
  Radio,
  InputNumber,
  Row,
  Col,
} from 'antd';
import _isEmpty from 'lodash/isEmpty';
import moment from 'moment';
import Tip from '@/components/Tip';
import { getSession } from '@/utils/session';
import DateTime from '@/components/Date';
import { payDues } from '../../services';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

const index = (props: any, ref: any) => {
  const { code = undefined, orgCode = undefined, shortName = '' } = getSession('org') || {
    code: undefined,
    orgCode: undefined,
    shortName: '',
  };
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('添加党费交纳');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [basinInfo, setBasinInfo] = useState<any>({});
  const [currentMonth, setCurrentMonth]:any = useState({});
  useImperativeHandle(ref, () => ({
    open: (record: any, month: number, year: string) => {
      const { data = [] } = record;
      let thisMonth: any = {};
      if (!_isEmpty(data)) {
        data.map((item) => {
          if (item?.month === month) {
            thisMonth = item;
          }
        });
      }
      console.log('thisMonth===', thisMonth);
      if (thisMonth?.payMoney) {
        setTitle('修改党费交纳');
        form.setFieldsValue({
          payMoney: thisMonth?.payMoney,
          payDate: thisMonth?.payDate ? moment(thisMonth?.payDate) : undefined,
          isPayYearly: thisMonth?.isPayYearly,
        });
      } else {
        setTitle('添加党费交纳');
        form.setFieldsValue({
          isPayYearly: 0,
        });
      }
      setBasinInfo({ ...record, year, month });
      setCurrentMonth(thisMonth);
      setVisible(true);
    },
  }));
  const hadndleFinish = async (vals: any) => {
    setConfirmLoading(true);
    if (vals?.payDate) {
      vals.payDate = moment(vals.payDate).valueOf();
    }
    const { code: resCode = 500 } = await payDues({
      data: {
        memCode: basinInfo?.memCode,
        code: currentMonth?.code,
        ...vals,
      },
    });
    setConfirmLoading(false);
    if (resCode == 0) {
      const { onOk } = props;
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOk && onOk();
    }
  };
  const handleCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
    form.resetFields();
    setBasinInfo({});
  };
  return (
    <Modal
      title={title}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={'700px'}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
        <Row>
          <Col span={12}>
            <Form.Item
              {...formItemLayout1}
              label="党员姓名"
              name={'memName'}
              rules={[{ required: false, message: '党员姓名' }]}
            >
              <div>{basinInfo?.memName}</div>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              {...formItemLayout1}
              label="所在组织"
              name={'orgName'}
              rules={[{ required: false, message: '所在组织' }]}
            >
              <div>{basinInfo?.orgName}</div>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              {...formItemLayout1}
              label="党费缴费类型"
              name={'d49code'}
              rules={[{ required: false, message: '党费缴费类型' }]}
            >
              <div>{currentMonth?.d49Name}</div>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              {...formItemLayout1}
              label="党费交纳标准"
              name={'standard'}
              rules={[{ required: false, message: '党费交纳标准' }]}
            >
              <div>{currentMonth?.standard || ''}元</div>
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item
              {...formItemLayout2}
              label="交费金额(元)"
              name={'payMoney'}
              rules={[{ required: true, message: '交费金额(元)' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                precision={2}
                minLength={0}
                maxLength={8}
              ></InputNumber>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              {...formItemLayout2}
              label="交费时间"
              name={'payDate'}
              rules={[{ required: true, message: '交费时间' }]}
            >
              <DateTime />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="当前交费是否同步到全年"
              name={'isPayYearly'}
              rules={[{ required: true, message: '当前交费是否同步到全年' }]}
            >
              <Radio.Group>
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default React.forwardRef(index);
