import { PDFDocument } from 'pdf-lib';

export async function extractImagesFromPDF(file) {
  const arrayBuffer = await file.arrayBuffer();
  const pdfDoc = await PDFDocument.load(arrayBuffer);
  const images = [];

  for (let i = 0; i < pdfDoc.getPageCount(); i++) {
    const page = pdfDoc.getPage(i);
    const imagesOnPage = page.node.normalizedEntries().filter(([key, value]) => {
      return value.constructor.name === 'PDFImage';
    });

    for (const [key, image] of imagesOnPage) {
      const imageBytes = await image.decode();
      images.push({
        page: i + 1,
        key,
        imageBytes,
      });
    }
  }

  return images;
}