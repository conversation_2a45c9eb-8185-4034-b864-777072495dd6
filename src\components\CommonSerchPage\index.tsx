import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import style from './index.less';
import NowOrg from '@/components/NowOrg';
import { Button, Form, Alert } from 'antd';
import ListTable from '@/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import SpinProgress from '@/components/SpinProgress';

interface pType {
  pagination?: object,
}
export function AlertMsg(props: pType & any) {
  const { pagination = {} } = props;
  return (
    <Alert
      message={<span>根据当前条件查询出 <span style={{ color: 'red', fontWeight: 800 }}>{pagination['total'] || 0}</span> 条数据</span>}
      style={{ float: 'right', display: "inline-block", padding: '4px 15px' }}
    />
  )
}

interface Interface {
  login: any,
  dispatch?: any,
  tableAction: Function,
  FormComp: any,
  tableColumns: Function,
  otherRenderTableColumnsFunc?: Function,
  renderTableNumber?: Function,
  otherBtnsFunc: Function,
  getSearchCallBack: Function,
  progressCallback?: Function,
  isDefaultForm?: Boolean,
  rowKey?: string
}

const index: React.FC<Interface> = (props, ref) => {
  const {
    tableAction,
    FormComp,
    tableColumns,
    otherBtnsFunc,
    getSearchCallBack,
    progressCallback,
    otherRenderTableColumnsFunc,
    renderTableNumber,
    isDefaultForm = true,
    rowKey = 'id'
  } = props;

  useImperativeHandle(ref, () => ({
    getProgress: (val) => {
      progressRef.pageNum.getProgress(val);
    },
    search: (val, other = {}) => {
      onSearch(val, other);
    },
    restForm: () => {
      restForm();
    }
  }));
  const progressRef: any = useRef();
  const [form] = Form.useForm();
  const { pathname, query: urlQuery = {} } = window['g_history']?.location || {};
  const { login: { listTree = [] } = {} } = props;
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 20, total: 0, current: 1 });
  const [keyword, setKeyword] = useState({});
  const restForm = () => {
    form.resetFields();
    setList([]);
    setPagination({ pageNum: 1, pageSize: 20, total: 0, current: 1 })
  };
  const onSearch = async (vals, other = {}) => {
    let { code = 500, value = {} } = getSearchCallBack(vals, other);
    if (code === 0) {
      setKeyword(value);
      getList(value);
    }
  };
  const getList = async (p?) => {
    console.log(keyword,p,'keywordkeywordkeywordkeyword')
    setLoading(true);

    const {
      code = 500,
      data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await tableAction({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.pageNum,
        ...urlQuery?.box === 'true' ?
          {
            orgCodes: props.login.orgCodes
          } : {
            // @ts-ignore
            orgCode: getSession('org')?.code || '',
          },
        ...keyword,
        ...p,
      },
    });
    setLoading(false);
    if (code === 0) {
      if (otherRenderTableColumnsFunc) {
        const { _list, _pagination } = otherRenderTableColumnsFunc({ list, pageNum, total, pageSize });
        setList(_list);
        setPagination({ ..._pagination });
      } else {
        setList(list);
        setPagination({ pageNum, total, pageSize, current: pageNum });
      }
    }
  };

  const renderContent = (value, row, index) => {
    const { show = false, otherInfoList = [] } = row;
    return {
      children: value,
      props: {
        rowSpan: show ? otherInfoList.length : 0,
      },
    };
  };

  useEffect(() => {
    if (urlQuery?.box === 'true') {
      let code = _get(listTree, '[0][0].code', '');
      if (!_isEmpty(code)) {
        props.dispatch({
          type: 'login/updateState',
          payload: {
            orgCodes: [code],
          },
        });
      }
    }
  }, [_get(listTree, '[0][0].code', ''), urlQuery.box]);

  // useEffect(() => {
  //   if(!_isEmpty(props.login.orgCodes)){
  //     getList();
  //   }
  // }, [keyword, JSON.stringify(props.login.orgCodes)]);

  return (
    <div className={style.query}>
      <NowOrg />
      {isDefaultForm && FormComp({form,onSearch})}
      <div className={style.operation}>
        <div className={style.left}>
          <div style={{ float: 'left' }}><AlertMsg pagination={pagination} /></div>
          {isDefaultForm &&
            <Button htmlType={'button'} type='primary' onClick={() => {
              form.submit();
            }} loading={loading}>查询</Button>}
          {otherBtnsFunc({ list, keyword })}
        </div>
        <div className={style.right}>
          {isDefaultForm && <Button htmlType={'button'} type='primary' onClick={restForm}>清除条件</Button>}
        </div>
      </div>
      <div style={{ marginTop: 10 }} />
      <div>
        <SpinProgress
          ref={progressRef}
          callback={({ code, url, currDataName, data }) => {
            progressCallback && progressCallback({ code, url, currDataName, data })
          }}>
          <div className={'memList'}>
            <ListTable
              rowKey={rowKey}
              pagination={pagination}
              scroll={{
                x: tableColumns().reduce((total: any, it: any) => {
                  return total + it.width;
                }, 80),
              }}
              data={list}
              onPageChange={(page, pageSize) => {
                getList({ pageNum: page, current: page, pageSize });
              }}
              columns={[
                renderTableNumber ? renderTableNumber() : {
                  title: '序号',
                  dataIndex: 'num',
                  align: 'center',
                  width: 50,
                  render: (text, record, index) => {
                    return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
                  },
                },
                ...tableColumns(),
              ]} />
          </div>
        </SpinProgress>
      </div>
    </div>
  );
};
// @ts-ignore
export default React.forwardRef(index);
