import React from 'react';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';

const arr = [
  {
    key:'2005',
    value:{
      tit:'组织类别',
      type:'G2',
      coverImg:require('@/components/CardsGroup/assets/org/chart_orgtype.jpg'),
      chartId:'orgDetailsCharts',
      chartType:'interval',
      xAxisCode:'d01Name',
      yAxisCode:'count',
      extraG2ChartConfig:{padding:[50,20,70,0]},
      action:'/api/chart/org/getOrgTypeTotal',
    }
  },
  // {
  //   key:'2006',
  //   value:{
  //     tit:'单位类别',
  //     // coverImg:require('@/components/CardsGroup/assets/org/chart_sex.jpg'),
  //     chartId:'orgUnitCharts',
  //     chartType:'ringR',
  //     xAxisCode:'d04Name',
  //     yAxisCode:'count',
  //     extraG2ChartConfig:{padding:[50,100,25,0]},
  //     action:'/api/chart/unit/getUnitTypeTotal',
  //   }
  // },
];

export const cardConfig = [
  {
    key:'2001',
    value:{
      icon:'home',
      coverImg:require('@/components/CardsGroup/assets/org/dangzuzhi.jpg'),
      iconColor:'#00A0FF',
      title:'党组织',
      suffix:'个',
      action:'/api/chart/org/getOrgTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['orgTypeName'] === '党组织总数'){
              num = item['count']
            }
          })
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['orgTypeName'] === '党支部'){zNum = item['count']}
            if(item['orgTypeName'] === '党委'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>支部数:{zNum}个</div>
            <div>党委数:{yNum}个</div>
          </div>
        )
      }
    },
  },
  // {
  //   key:'2002',
  //   value:{
  //     icon:'bell',
  //     coverImg:require('@/components/CardsGroup/assets/org/jieman.jpg'),
  //     iconColor:'#6F79C1',
  //     title:'即将届满',
  //     suffix:'个',
  //     action:'/api/chart/org/getOrgTotal',
  //     end:(val)=>{
  //       let num = 0;
  //       if(!_isEmpty(val)){
  //         const {orgElectTotal = 0} = val || {};
  //         num = orgElectTotal;
  //       }
  //       return num;
  //     },
  //     content:(data)=>{
  //       let num = 0;
  //       if(!_isEmpty(data)){
  //         const {dzTotal = 0} = data || {};
  //         num = dzTotal;
  //       }
  //       return(
  //         <div>
  //           <span>党组织7天内届满</span>
  //         </div>
  //       )
  //     },
  //   },
  // },
  // {
  //   key:'2003',
  //   value:{
  //     icon:'home',
  //     coverImg:require('@/components/CardsGroup/assets/org/danwei.jpg'),
  //     iconColor:'#00A0FF',
  //     title:'单位',
  //     suffix:'个',
  //     action:'/api/chart/unit/getUnitTotal',
  //     end:(val)=>{
  //       let num = 0;
  //       if(!_isEmpty(val)){
  //         const {unitTotal = 0} = val || {};
  //         num = unitTotal;
  //       }
  //       return num;
  //     },
  //   },
  // },

  // {
  //   key:'2004',
  //   value:{
  //     icon:'hourglass',
  //     coverImg:require('@/components/CardsGroup/assets/org/zhengshi.jpg'),
  //     iconColor:'#6F79C1',
  //     title:'正式党员',
  //     suffix:'人',
  //     action:'/api/chart/mem/getFullMemTotal',
  //     end:(val)=>{
  //       let num = 0;
  //       if(!_isEmpty(val)){
  //         val.forEach(item=>{
  //           if(item['d08Code'] === '1'){
  //             num =  item['count']
  //           }
  //         });
  //       }
  //       return num;
  //     },
  //     content:(data)=>{
  //       let num = 0;
  //       if(!_isEmpty(data)) {
  //         data.forEach(item => {
  //           if (item['d08Code'] === '2') {
  //             num = item['count']
  //           }
  //         });
  //       }
  //       return(
  //         <div>
  //           <span>预备党员{num}人</span>
  //         </div>
  //       )},
  //   },
  // },
  {
    key:'2006',
    value:{
      icon:'inbox',
      coverImg:require('@/components/CardsGroup/assets/org/dangwei.jpg'),
      iconColor:'#6F79C1',
      title:'党委总数',
      suffix:'个',
      action:'/api/chart/org/getDwRatioTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['orgTypeName'] === '党委数'){
              num =  item['count']
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['orgTypeName'] === '基层党委数'){zNum = item['count']}
            if(item['orgTypeName'] === '其他党委数'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>基层党委数:{zNum}个</div>
            <div>其他党委数:{yNum}个</div>
          </div>
        )
      }
    },
  },
  {
    key:'2007',
    value:{
      icon:'cluster',
      coverImg:require('@/components/CardsGroup/assets/org/dangzhibu.jpg'),
      iconColor:'#f3715c',
      title:'党支部总数',
      suffix:'个',
      action:'/api/chart/org/getDzbRatioTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['orgTypeName'] === '党支部数'){
              num =  item['count']
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['orgTypeName'] === '基层党支部数'){zNum = item['count']}
            if(item['orgTypeName'] === '其他党支部数'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>基层党支部数:{zNum}个</div>
            <div>其他党支部数:{yNum}个</div>
          </div>
        )
      }
    },
  },
  {
    key:'2008',
    value:{
      icon:'paper-clip',
      coverImg:require('@/components/CardsGroup/assets/org/teshedangzhibu.jpg'),
      iconColor:'#9FF048',
      title:'特设党支部',
      suffix:'个',
      action:'/api/chart/org/getTsdzbRatioTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          val.forEach(item=>{
            if(item['d01Name'] === '特设党支部数'){
              num =  item['count']
            }
          });
        }
        return num;
      }
    },
  },
  {
    key:'2009',
    value:{
      icon:'credit-card',
      coverImg:require('@/components/CardsGroup/assets/org/dangxiaozu.jpg'),
      iconColor:'#2E68AA',
      title:'党小组数',
      suffix:'个',
      action:'/api/chart/org/getDxzRatioTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          val.forEach(item=>{
            if(item['d01Name'] === '党小组数'){
              num =  item['count']
            }
          });
        }
        return num;
      }
    },
  },
  {
    key:'2010',
    value:{
      icon:'flag',
      coverImg:require('@/components/CardsGroup/assets/org/lianhe.jpg'),
      iconColor:'#FFBABA',
      title:'联合党支部数',
      suffix:'个',
      action:'/api/chart/org/getLhdzbRatioTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          val.forEach(item=>{
            if(item['d01Name'] === '联合党支部数'){
              num =  item['count']
            }
          });
        }
        return num;
      }
    },
  },
  // {
  //   key:'2011',
  //   value:{
  //     icon:'hourglass',
  //     coverImg:require('@/components/CardsGroup/assets/org/jiguan.jpg'),
  //     iconColor:'#D6D85D',
  //     title:'机关党组',
  //     suffix:'个',
  //     action:'/api/chart/org/getDzTypeRatioTotal',
  //     end:(val)=>{
  //       let num = 0;
  //       if(!_isEmpty(val)){
  //         val.forEach(item=>{
  //           if(item['d01Name'] === '机关党组'){
  //             num =  item['count']
  //           }
  //         });
  //       }
  //       return num;
  //     },
  //     content:(val)=>{
  //       let zNum = 0;
  //       if(!_isEmpty(val) && _isArray(val)) {
  //         val.forEach(item=>{
  //           if(item['d01Name'] === '其他党组'){zNum = item['count']}
  //         })
  //       }
  //       return(
  //         <div>
  //           <div>其他党组:{zNum}个</div>
  //         </div>
  //       )
  //     }
  //   },
  // },
  {
    key:'2012',
    value:{
      icon:'tag',
      coverImg:require('@/components/CardsGroup/assets/org/teshedangwei.jpg'),
      iconColor:'#35C0A0',
      title:'特设党委',
      suffix:'个',
      action:'/api/chart/org/getTszzTypeRatioTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          val.forEach(item=>{
            if(item['d01Name'] === '特设党委'){
              num =  item['count']
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['d01Name'] === '特设党支部'){zNum = item['count']}
            if(item['d01Name'] === '特设党总支部'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>特设党总支部:{yNum}个</div>
            <div>特设党支部:{zNum}个</div>
          </div>
        )
      }
    },
  },
  {
    key:'2013',
    value:{
      icon:'car',
      coverImg:require('@/components/CardsGroup/assets/org/chengshi.jpg'),
      iconColor:'#FFC549',
      title:'城市街道',
      suffix:'个',
      action:'/api/chart/org/getXzAffiliationRatioTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val)){
          val.forEach(item=>{
            if(item['d03Name'] === '城市街道'){
              num =  item['count']
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['d03Name'] === '乡镇'){zNum = item['count']}
          })
        }
        return(
          <div>
            <div>乡镇:{zNum}个</div>
          </div>
        )
      }
    },
  },
];
export const chartConfig = [
  {
    key:'2005', // 组织类别
    value:{
      coverImg:require('@/components/CardsGroup/assets/org/chart_zuzhi.jpg'),
      action:'/api/chart/org/getOrgTypeTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d01Name']);
            arr.push({
              name:item['d01Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '组织类别',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '组织类别',
              type: 'pie',
              radius : '55%',
              center: ['30%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
    }
  },
  {
    key:'2014', // 建立单位情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/org/chart_jianlidanwei.jpg'),
      action:'/api/chart/org/getD02RatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d02Name']);
            arr.push({
              name:item['d02Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '建立单位情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            // orient: 'vertical',
            left: 'center',
            top:'bottom',
            data: arrName
          },
          series: [
            {
              name:'建立单位情况',
              type:'pie',
              center: ['50%', '50%'],
              radius: ['40%', '60%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '24',
                    fontWeight: 'bold'
                  }
                }
              },
              labelLine: {
                normal: {
                  show: true
                }
              },
              data:arr
            }
          ]
        }
      },
    }
  },
  {
    key:'2016', // 中共各级委员会
    value:{
      coverImg:require('@/components/CardsGroup/assets/org/chart_weiyuanhui.jpg'),
      action:'/api/chart/org/getZGTypeOneTotal',
      option:(val)=>{
        let arrName:Array<string> = [];
        let arrValue:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d01Name']);
            arrValue.push(item['count']);
          });
        }
        return {
          title : {
            text: '中共各级委员会',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arrValue,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'2017', // 中共各级工作委员会
    value:{
      coverImg:require('@/components/CardsGroup/assets/org/chart_gejigognzuo.jpg'),
      action:'/api/chart/org/getZGTypeTwoTotal',
      option:(val)=>{
        let arrName:Array<string> = [];
        let arrValue:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d01Name']);
            arrValue.push(item['count']);
          });
        }
        return {
          title : {
            text: '中共各级工作委员会',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arrValue,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'2018', // 国家工作部门党委情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/org/chart_buwendangwei.jpg'),
      action:'/api/chart/org/getGjgzbwTypeRatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d01Name']);
            arr.push({
              name:item['d01Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '国家工作部门党委情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            left: 'center',
            top:'bottom',
          },
          series: [{
            name: '国家工作部门党委情况',
            type: 'pie',
            clockwise: false, //饼图的扇区是否是顺时针排布
            minAngle: 20, //最小的扇区角度（0 ~ 360）
            radius: ["30%", "58%"],
            center: ["50%", "50%"],
            avoidLabelOverlap: false,
            itemStyle: { //图形样式
              normal: {
                borderColor: '#ffffff',
                borderWidth: 10,
              },
            },
            label: {
              normal: {
                show: false,
                position: 'center',
                formatter: '{text|{b}}\n{value|{d}%}',
                rich: {
                  text: {
                    color: "#666",
                    fontSize: 14,
                    align: 'center',
                    verticalAlign: 'middle',
                    padding: 5
                  },
                  value: {
                    color: "#8693F3",
                    fontSize: 24,
                    align: 'center',
                    verticalAlign: 'middle',
                  },
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: 46,
                }
              }
            },
            data: arr
          }]
        }
      },
    }
  },
  {
    key:'2019', // 党的基层党组织情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/org/chart_jichengdangwei.jpg'),
      action:'/api/chart/org/getDjcTypeRatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d01Name']);
            arr.push({
              name:item['d01Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '党的基层党组织情况',
            // subtext: '纯属虚构',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '党的基层党组织情况',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },

    }
  },
  {
    key:'2020', // 临时基层党组织
    value:{
      coverImg:require('@/components/CardsGroup/assets/org/chart_linshi.jpg'),
      action:'/api/chart/org/getLsjcdzzTypeRatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d01Name']);
            arr.push({
              name:item['d01Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '临时基层党组织',
            // subtext: '纯属虚构',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '临时基层党组织',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
    }
  },
  {
    key:'2021', // 隶属情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/org/chart_lishu.jpg'),
      action:'/api/chart/org/getAffiliationRatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d03Name']);
            arr.push({
              name:item['d03Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '隶属情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          // legend: {
          //   // orient: 'horizontal',
          //   // left: 'middle',
          //   top:'bottom',
          //   data: arrName
          // },
          series : [
            {
              name: '隶属情况',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr.sort(function(a, b) {
                return a['value'] - b['value']
              }),
              roseType : 'area',
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ],
        }
      },
    }
  },
  {
    key:'2022', // 社区建制村隶属情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/org/chart_shequ.jpg'),
      action:'/api/chart/org/getSqjwhAffiliationRatioTotal',
      option:(val)=>{
        let arrName:Array<string> = [];
        let arrValue:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d03Name']);
            arrValue.push(item['count']);
          });
        }
        return {
          title : {
            text: '社区,建制村隶属情况',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arrValue,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
];
