/**
 * 党费标准设置
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Input, InputNumber, Modal, Radio, Row, Switch } from 'antd';
import DictSelect from '@/components/DictSelect';
import DictArea from '@/components/DictArea';
import OrgSelect from '@/components/OrgSelect';
import {connect} from "dva";
import Tip from "@/components/Tip";
import Notice from '@/components/Notice';
import { getSession } from '@/utils/session';
import { isEmpty } from '@/utils/method';

const {MonthPicker}=DatePicker;
const RadioGroup=Radio.Group;
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
@connect(({transferIn})=>({transferIn}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      key:new Date().valueOf(),
      stand:'1',
    };
  }
  showModal=()=>{
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
      org
    },()=>{
      // this.selectMem()
    });
  };
  changeStd=(v)=>{
    this.setState({
      stand:v.target.value,
      proportion:null,
      standard:null
    });
    this.props.form.resetFields(['memType','phone','standard','reason'])
  };
  onBlur =(v) =>{
    let val=v.target.value;
    let proportion =0;
    if(val!==''){
      if (val < 3001) {
        proportion = 0.005
      } else if (val < 5001) {
        proportion = 0.01
      } else if (val < 10001) {
        proportion = 0.015
      } else {
        proportion = 0.02
      }
      let standards = val*proportion;
      // let standard = Math.ceil(standards*10)/10;
      this.setState({proportion,standard:`${standards.toFixed(2)}`})
      // this.props.form.setFieldsValue({
      //   standard:standard
      // })
    }
  };
  validFunction = (rule, value, callback) => {
    let han= /^(0\.\d{0,1}[1-9]|\+?[1-9][0-9]{0,9})(\.\d{1,2})?$/;
    // /^[0-9]+.?[0-9]*$/
    if (value){
      switch (rule.field) {
        case 'standard':
         if (!han.test(value)) {
            return callback('党费标准必须大于0,小数点后最多两位')
          }
          break;
        case 'base':
          if (value <= 0) {
            return callback('党费基数必须大于0,小数点后最多两位')
          }
      }
    }
    callback()
  };
  handleOk=()=>{
    const { data={},onChange} = this.props;
    const { org={} } =this.state;
    this.props.form.validateFieldsAndScroll(async (err,val)=>{
     if (err) {
       return
     }
     const { d49Code ,isYearly,memType,standard,...v } = val;
     let value={
       memCode:data['code'],
       memOrgCode:data['orgCode'],
       memOrgOrgCode:data['memOrgCode'],
       year:data['year'],
       month:data['month'].toString(),
       settingOrgOrgCode:data['memOrgCode'],
       settingOrgCode:data['orgCode'],
       standard:d49Code=='4'?0:parseFloat(standard),
       ...v,
       isYearly:isEmpty(isYearly)?'':isYearly?1:0,
       d49Code:d49Code,
       d49Name:d49Code=='1'?'标准交纳':d49Code=='2'?'按工资比例交纳':d49Code=='3'?'少交':'免交'
     };
     this.props.dispatch({
       type:'dues/add',
       payload:{
         data:{
           ...value
         }
       }
     }).then(res=>{
       if (res['code']===0){
         Notice("操作提示",res['message'],"check-circle","green");
         this.handleCancel();
         onChange&&onChange(true)
       } else {
         Notice("操作提示",res['message'],"exclamation-circle-o","orange");
       }
     })
    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      standard:null,
      proportion:null,
      stand:'1'
    });
    this.props.form.resetFields()
  };
  render(){
    const {visible}=this.state;
    const { data={} }=this.props;
    const {getFieldDecorator}=this.props.form;
    return(
      <div>
        <Modal
          destroyOnClose
          title="标准设置"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
          bodyStyle={{height:570,overflow:'auto'}}
        >
          <Form {...formItemLayout}>
            <Row>
              <Col span={24}>
                <FormItem
                  label="党员姓名"
                >
                 <span>{data['name']}</span>
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label="交费类型"
                >
                  {getFieldDecorator('d49Code', {
                    initialValue:this.state['stand'],
                    rules: [{ required: true, message: '交费类型' }],
                  })(
                    <RadioGroup onChange={this.changeStd}>
                      <Radio value={'1'}>标准交纳</Radio>
                      <Radio value={'2'}>按工资比例交纳</Radio>
                      <Radio value={'3'}>少交</Radio>
                      <Radio value={'4'}>免交</Radio>
                    </RadioGroup>
                  )}
                </FormItem>
              </Col>
              {
                this.state['stand']=='2'&&
                  <React.Fragment>
                   <Col span={24}>
                      <FormItem
                        label="党费基数"
                      >
                        {getFieldDecorator('base', {
                          rules: [{ required: true, message: '请填写党费基数'},{ validator: this.validFunction }],
                        })(
                          <Input placeholder={'请填写党费基数'} onBlur={(value)=>this.onBlur(value)}/>
                        )}
                      </FormItem>
                    </Col>
                    <Col span={24}>
                    <FormItem
                      label="计算比例"
                    >
                      {getFieldDecorator('memType', {
                        initialValue:this.state['proportion']||undefined,
                        rules: [{ required: true, message: '系统自动计算比例' }],
                      })(
                        <Input placeholder={'系统自动计算比例'} disabled/>
                      )}
                    </FormItem>
                   </Col>
                  </React.Fragment>
              }
              {
                this.state['stand']=='3'&&
                <Col span={24}>
                  <FormItem
                    label="少交原因"
                  >
                    {getFieldDecorator('reason', {
                      rules: [{ required: true, message: '请填写少交原因' }],
                    })(
                      <Input placeholder={'请填写少交原因'}/>
                    )}
                  </FormItem>
                </Col>
              }
              {
                this.state['stand']!=='4'&&
                <Col span={24}>
                  <FormItem
                    label="党费标准"
                  >
                    {getFieldDecorator('standard', {
                      initialValue:this.state['standard']||undefined,
                      rules: [{ required: true, message: '请填写党费标准' ,whitespace:true},
                        { validator: this.validFunction }
                        ],
                    })(
                      <Input
                        placeholder={'请填写党费标准'}
                        disabled={this.state['stand']=='2'}
                        // value={isEmpty(this.state['standard'])?null:this.state['standard']}
                      />
                    )}
                  </FormItem>
                </Col>
              }
              {
                this.state['stand']=='4'&&
                <Col span={24}>
                  <FormItem
                    label="免交原因"
                  >
                    {getFieldDecorator('reason', {
                      rules: [{ required: true, message: '请填写免交原因' }],
                    })(
                      <Input placeholder={'请填写免交原因'}/>
                    )}
                  </FormItem>
                </Col>
              }
              <Col span={24}>
                <FormItem
                  label="是否应用到全年"
                >
                  {getFieldDecorator('isYearly', {
                    initialValue:false,
                    rules: [{ required: true, message: '是否应用到全年' }],
                  })(
                    <Switch  checkedChildren="是" unCheckedChildren="否"  />
                  )}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </Modal>
      </div>
    )
  }
}
export default Form.create()(index)
