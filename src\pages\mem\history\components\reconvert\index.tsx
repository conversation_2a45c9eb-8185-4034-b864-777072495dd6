import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, InputNumber, Col, Input, DatePicker } from 'antd';
import DictSelect from '@/components/DictSelect';
import { unixMoment, findDictCodeName } from '@/utils/method.js';
import Tip from '@/components/Tip';
import Date from '@/components/Date';
import UploadComp, { getInitFileList, fitFileUrlForForm } from '@/components/UploadComp';
const FormItem = Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      info: {},
    };
  }
  open = (record) => {
    this.setState({ visible: true, info: record });
  };
  handleOk = () => {
    const { onClose } = this.props;
    const { info } = this.state;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if (val['recoveryBasis'] && typeof val['recoveryBasis'] == 'object') {
          val['recoveryBasis'] = fitFileUrlForForm(val['recoveryBasis'])
        }
        val = findDictCodeName(['d11'], val, {});
        val = unixMoment(['recoverPartyDate'], val);
        val['code'] = info['code'];
        // console.log(val,'val');
        const res = await this.props.dispatch({
          type: 'memLeaveOrg/reconvert',
          payload: { data: { ...val } },
        });
        const { code = 500 } = res || {};
        if (code === 0) {
          Tip.success('操作提示', '操作成功');
          this.handleCancel();
          onClose && onClose();
        }
      }
    });
  };
  destroy = () => {
    this.setState({
      info: {},
    });
  };
  handleCancel = () => {
    this.destroy();
    this.setState({ visible: false });
  };
  render(): React.ReactNode {
    const { form } = this.props;
    const { getFieldDecorator } = form;
    const { visible, info } = this.state;
    return (
      <Modal
        title="恢复党籍"
        destroyOnClose
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        width={'800px'}
      >
        <FormItem {...formItemLayout} label="姓名">
          {getFieldDecorator('name', {
            rules: [{ required: true, message: '姓名' }],
            initialValue: info['name'],
          })(<Input disabled />)}
        </FormItem>
        <FormItem {...formItemLayout} label="恢复党籍类型">
          {getFieldDecorator('d11Code', {
            rules: [{ required: true, message: '恢复党籍类型' }],
          })(
            <DictSelect
              codeType={'dict_d11'}
              backType={'object'}
              noDraw={[
                '0',
                '1',
                '3',
                '31',
                '32',
                '33',
                '34',
                '4',
                '5',
                '38',
                '3811',
                '3812',
                '3813',
              ]}
              placeholder={'恢复党籍类型'}
            />,
          )}
        </FormItem>
        <FormItem {...formItemLayout} label="工作单位及职务">
          {getFieldDecorator('workPost', {
            rules: [{ required: true, message: '工作单位及职务' }],
          })(<Input placeholder={'工作单位及职务'} />)}
        </FormItem>
        <FormItem {...formItemLayout} label="恢复党籍原因">
          {getFieldDecorator('recoverPartyReason', {
            rules: [{ required: true, message: '恢复党籍原因' }],
          })(<TextArea rows={3} placeholder={'恢复党籍原因'} />)}
        </FormItem>
        <FormItem {...formItemLayout} label="恢复党籍时间">
          {getFieldDecorator('recoverPartyDate', {
            rules: [{ required: true, message: '恢复党籍时间' }],
            // <DatePicker style={{width:'100%'}} placeholder={'恢复党籍时间'}/>
          })(<Date />)}
        </FormItem>
        {/* <FormItem {...formItemLayout} label="恢复党籍依据">
          {getFieldDecorator('recoveryBasis', {
            rules: [{ required: false, message: '恢复党籍依据' }],
          })(<UploadComp maxLen={1}/>)}
        </FormItem> */}
      </Modal>
    );
  }
}
export default Form.create()(index);
