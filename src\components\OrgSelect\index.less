.content{
  display: flex;
  flex-direction: row;
  // display: table;
  &>div{
    // display: table-cell;
  }
}
.tree{
  border-right: 1px solid #e9e9e9;
  vertical-align:top;
  &>div{
    height: 70vh;
    overflow: auto;
    width: 230px;
    &>div{
      overflow: unset !important;
    }
  }
  :global{
    .ant-anchor-wrapper{
      margin: 0;
      padding: 0;
    }
  }
}
.list{
  padding: 0 20px;
  padding-bottom: 0;
  // max-height: 600px;
  overflow: auto;
  width: 100%;
}
.search{
  line-height: 40px;
  overflow: hidden;
  &>div{
    display: inline-block;
    float: right;
  }
}
.tit {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  padding: 0 12px 0 24px;
  color: #2e2e2e;
  background: #e7eaef;
  height: 40px !important;
  line-height: 40px;
}
.check {
  font-size: 15px;
  & > span {
    margin-left: 2px;
    position: relative;
    top: 1px;
    color: #1890ff;
  }
}
:global {
  .ant-checkbox + span {
    padding-right: 0;
  }
  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-left: 0;
  }
  .ant-anchor-wrapper {
    overflow: unset;
  }
}