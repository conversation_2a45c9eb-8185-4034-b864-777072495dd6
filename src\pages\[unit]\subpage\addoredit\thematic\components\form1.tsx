// 农村党建有关情况
import React, { Fragment, useEffect, useState } from 'react';
import { Button, Form, Input, Row, Col, Switch,InputNumber } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import { formItemLayout3 } from './config';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import Tip from '@/components/Tip';
import { saveForm1, findForm } from '@/pages/[unit]/services/thematic';

const index = (props: any) => {
  const [form] = Form.useForm();
  const { selectTab, basicInfo } = props;
  const { code: unitCode = '' } = basicInfo;
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);
  const onFinish = async (e) => {
    let val = _cloneDeep(e);
    [
      'hasFiling',
      'hasAppointment',
      'hasCurrent',
      'hasLowOneWan',
      'hasWorkable',
      'hasPromotePromotion',
      'hasRecallAdjust',
      'hasRemunerationOrganization',
      'hasPracticableSubsidies',
      'hasPracticableMaintain',
      'hasPracticableDelaysSubsidies',
      'hasActivities',
      'hasTowBaiAbove',
      'hasCompleteFiveMin',
    ].map((it) => {
      val[it] = val[it] ? 1 : 0;
    });
    setLoading(true);
    const { code = 500 } = await saveForm1({
      data: {
        unitCode,
        ...val,
      },
    });
    setLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '修改成功');
    }
  };
  const getInfo = async () => {
    const { code = 500, data = {} } = await findForm({
      unitCode,
      type: '1',
    });
    if (code === 0) {
      setQuery(data);
      form.setFieldsValue(data);
    }
  };
  useEffect(() => {
    getInfo();
  }, []);
  return (
    <Fragment>
      <Form form={form} {...formItemLayout3} onFinish={onFinish}>
        <Row>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasFiling"
              label="是否建档立卡贫困村"
              initialValue={query['hasFiling'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          {/* <Col span={24}>
            <div style={{ padding: '0 4%' }}>
              <Divider plain>第一书记</Divider>
            </div>
          </Col> */}
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasAppointment"
              label="累计选派第一书记"
              initialValue={query['hasAppointment'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasCurrent"
              label="现任第一书记"
              initialValue={query['hasCurrent'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="trainFirstSecretary" label="本年各级培训第一书记（人次）">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasLowOneWan"
              label="为第一书记安排不低于1万元工作经费的村"
              initialValue={query['hasLowOneWan'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasWorkable"
              label="派出单位落实责任、项目、资金捆绑的村"
              initialValue={query['hasWorkable'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasPromotePromotion"
              label="提拔使用或晋级的第一书记"
              initialValue={query['hasPromotePromotion'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasRecallAdjust"
              label="因工作不胜任召回调整的第一书记"
              initialValue={query['hasRecallAdjust'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          {/* <Col span={24}>
            <div style={{ padding: '0 4%' }}>
              <Divider plain>基层基础保障</Divider>
            </div>
          </Col> */}
          <Col span={12}>
            <Form.Item name="operatingFunds" label="平均每村运转经费（万元∕年）">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="officeExpenses" label="平均每村办公经费（万元∕年）">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="villageReward" label="村党组织书记平均报酬（万元∕年）">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasRemunerationOrganization"
              label="村干部基本报酬和村级组织办公经费合计低于11万元的县（市、区、旗）"
              initialValue={query['hasRemunerationOrganization'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasPracticableSubsidies"
              label="落实正常离任村干部生活补贴的县（市、区、旗）"
              initialValue={query['hasPracticableSubsidies'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasPracticableMaintain"
              label="落实农村公共服务运行维护支出或服务群众专项经费的县（市、区、旗）"
              initialValue={query['hasPracticableMaintain'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasPracticableDelaysSubsidies"
              label="落实村民小组长误工补贴的县（市、区、旗）"
              initialValue={query['hasPracticableDelaysSubsidies'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasActivities"
              label="暂无活动场所的行政村"
              initialValue={query['hasActivities'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasTowBaiAbove"
              label="活动场所面积200㎡以上的行政村"
              initialValue={query['hasTowBaiAbove'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="buildSites" label="本年新建或改扩建活动场所数量">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasCompleteFiveMin"
              label="未完成“五小”建设的乡镇"
              initialValue={query['hasCompleteFiveMin'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
        </Row>
        <div style={{ textAlign: 'center' }}>
          <WhiteSpace />
          <Button
            type={'primary'}
            htmlType={'submit'}
            icon={<LegacyIcon type={'check'} />}
            // onClick={() => {}}
            style={{ marginRight: 16 }}
            loading={loading}
          >
            保存
          </Button>
          {/* <Button
            type={'primary'}
            danger
            htmlType={'button'}
            icon={<LegacyIcon type={'delete'} />}
            onClick={() => {}}
          >
            取消
          </Button> */}
        </div>
      </Form>
    </Fragment>
  );
};
export default index;
