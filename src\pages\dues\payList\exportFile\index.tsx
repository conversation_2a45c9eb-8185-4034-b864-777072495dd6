/**
 * 党费明细导出
 */
import React from 'react';
import { CheckCircleOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Input, Modal, Progress, Radio, Row, Switch } from 'antd';
import {connect} from "dva";
import { getSession } from '@/utils/session';
import { isEmpty } from '@/utils/method';
import moment from 'moment'
import request from "@/utils/request";
const {MonthPicker}=DatePicker;
const RadioGroup=Radio.Group;
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const options = [
  { label: '年度明细（组织）', value: '1' },
  { label: '月度明细（组织）', value: '2' },
  { label: '当月明细（组织）', value: '3' },
  { label: '年度明细（个人）', value: '4' },
  { label: '月度明细（个人）', value: '5' },
  { label: '当日明细（个人）', value: '6' },
];
@connect(({dues})=>({dues}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      isOpen: false,
      value: moment().format('YYYY'),
      mode:'year',
      modeFormat:'YYYY',
      check:'1'
    };
  }
  showModal=()=>{
    let user=getSession('user')|| {};
    this.setState({
      visible:true,
      user
    });
  };
  onChange=(e)=>{
    let mode='';
    let modeFormat='';
    switch (e.target.value) {
      case '1':
      case '4':
         mode='year';
        modeFormat='YYYY';
         break;
      case '2':
      case '3':
      case '5':
        mode='month';
        modeFormat='YYYY-MM';
        break;
      case '6':
        mode='day';
        modeFormat='YYYY-MM-DD';
        break;
      default:
    }
    this.setState({mode,modeFormat,check:e.target.value})
  };
  changeTime=(value)=>{
    this.setState({
      value,
      isOpen: false
    })
  };
  setTimeOut=()=>{
    let secondsToGo = 0;
    const modal = Modal.info({
      title: '正在导出中，请稍等...',
      okButtonProps: {
        disabled: true,
      },
      okText:'知道了',
      content: (
        <div>
          <Progress percent={secondsToGo}/>
        </div>
      ),
    });

    this['timer'] = setInterval(() => {
      secondsToGo += 1;
      if(secondsToGo >= 99){
        secondsToGo = 99;
        clearInterval(this['timer']);
        modal.update({
          content:(
            <div>
              <Progress percent={secondsToGo}/>
            </div>
          ),
        });
      }
      modal.update({
        content:(
          <div>
            <Progress percent={secondsToGo}/>
          </div>
        ),
      });
    }, 200);

    return modal;
  };
  handleOk=()=>{
    const { data={},onChange} = this.props;
    const { user={} } =this.state;
    this.props.form.validateFieldsAndScroll(async (err,val)=>{
      if (err) {
        return
      }
      const { exportCode,exportDate,isContainLower }=val;
      let value={
        memOrgOrgCode:user['orgCode'],
        exportCode,
        exportDate:moment(exportDate).valueOf(),
        isContainLower:isContainLower==false?'0':'1'
      };
      let modal = this.setTimeOut();
      request('/api/data/fee/exportData',{
        method:'POST',
        body:{
          data:{
            ...value
          }
        },
      },'file').then(res=>{
        if(res['status']===200){
          clearInterval(this['timer']);
          modal.update({
            icon:<CheckCircleOutlined style={{color:'#52C41A'}} />,
            title: '下载完成',
            okButtonProps: {
              disabled: false,
            },
            onOk:()=>{
              this.handleCancel();
            },
            content:(
              <div>
                <Progress percent={100}/>
              </div>
            ),
          });
        }
      });
    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      isOpen:false
    });
    this.props.form.resetFields()
  };
  render(){
    const {visible,value,mode,modeFormat}=this.state;
    const { data={} }=this.props;
    const {getFieldDecorator}=this.props.form;

    return(
      <div>
        <Modal
          destroyOnClose
          title="党费明细导出"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={880}
          bodyStyle={{height:400,overflow:'auto'}}
        >
          <Form {...formItemLayout}>
            <Row>
                <Col span={24}>
                  <FormItem
                    label="导出方式"
                  >
                    {getFieldDecorator('exportCode', {
                      initialValue:'1',
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <RadioGroup options={options} onChange={this.onChange} />
                    )}
                  </FormItem>
                </Col>
              <Col span={24}>
                <FormItem
                  label="选择时间"
                >
                  {getFieldDecorator('exportDate', {
                    initialValue:moment(value),
                    rules: [{ required: true, message: '请填写党费标准' }],
                  })(
                    // @ts-ignore
                    <DatePicker
                      open={this.state.isOpen}
                      format={modeFormat}
                      mode={mode}
                      onFocus={() => {this.setState({isOpen: true})}}
                      // onBlur={() => {this.setState({isOpen: false})}}
                      onPanelChange={this.changeTime}
                    />
                  )}
                </FormItem>
              </Col>
              {
                (this.state['check']=='1'||this.state['check']=='2'||this.state['check']=='3')&&
                <Col span={24}>
                  <FormItem
                    label="是否包含下级"
                  >
                    {getFieldDecorator('isContainLower', {
                      initialValue:'0',
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <Switch  checkedChildren="是" unCheckedChildren="否"/>
                    )}
                  </FormItem>
                </Col>
              }
            </Row>
          </Form>
        </Modal>
      </div>
    )
  }
}
export default Form.create()(index)
