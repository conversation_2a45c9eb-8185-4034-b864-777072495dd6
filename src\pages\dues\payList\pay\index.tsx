/**
 * 党费交纳时间设置
 */
import React from 'react';
import { DeleteOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  <PERSON><PERSON>,
  Button,
  Col,
  DatePicker,
  Divider,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Switch,
  Tag,
  Tooltip,
} from 'antd';
import {connect} from "dva";
import moment from 'moment'
import Notice from '@/components/Notice';
import { getSession } from '@/utils/session';
import { isEmpty } from '@/utils/method';
import MemSelect from '@/components/MemSelect'
import ListTable from '@/components/ListTable';
import qslogo from '@/assets/dues/logo.png';
import shuoming from '@/assets/dues/shuoming.png';
import styles from './index.less'
const QRCode = require('qrcode.react');
const {MonthPicker}=DatePicker;
const RadioGroup=Radio.Group;
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};
@connect(({dues,login})=>({dues,login}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      key:new Date().valueOf(),
      stand:'1',
      months:1,
      url:'',
      qsvsb:false
    };
  }
  showModal=()=>{
    let org=getSession('org')|| {};
    let user=getSession('user')|| {};
    this.setState({
      visible:true,
      account:user['account'],
      org
    });
  };
  disabledTomorrow=(current)=>{
    return current && current > moment().endOf('day')||current<moment('2019');
  };
  outMem=(e,memInfo=[])=>{
    let val:any=[];
   e.forEach((item,index)=>{
     if (!isEmpty(item['lastPayDate'])) {
       if (isEmpty(memInfo)) {
         let obj={
           memCode:item['code'],
           lastPayDate:item['lastPayDate'],
           deadline:moment(moment(item['lastPayDate']).add(1,'month')).valueOf()
         };
         val.push(obj)
       }else {
         // if (memInfo.map(it => it['code']).includes(item['code'])) {
           let find=memInfo.filter(it=>it['code']==item['code']);
           let obj={
             memCode:item['code'],
             lastPayDate:item['lastPayDate'],
             deadline:moment(moment(item['lastPayDate']).add(isEmpty(find[index])?1:find[index]['payMonth'],'month')).valueOf()
           };
           val.push(obj)
         // }
       }
     }
    });
    this.props.dispatch({
      type:'dues/getOne',
      payload:{
        data:[
          ...val
        ]
      }
    }).then(res=>{
      const { data=[] }=res;
      let money=0;
      e.forEach(i=>{
        data.map((item,index)=>{
          if (i['code']==item['memCode']){
              i['isError']=item['isError'];
              i['abort']=item['total']||0;
              i['payMonth']=1;
              i['errorMessage']=item['errorMessage'];
              i['payDate']=moment(item['deadline']).format('YYYY-MM-DD');
              if (!isEmpty(item['total'])) {
                money+=item['total']
              }
          }
        });
        // if (data.map(it=>it.code).includes(i['memCode'])){
        //   i['isError']=it['isError'];
        //   i['total']=it['total']
        // }
      });
      this.setState({memInfo:e,money})
    });

  };
  selMoney=(value,record)=>{
    // let value=this.props.form.getFieldValue('account');
    const { memInfo=[] }=this.state;
    let val={
      memCode:record['code'],
      lastPayDate:record['lastPayDate'],
      deadline:moment(moment(record['lastPayDate']).add(value,'month')).valueOf()
    };
    this.props.dispatch({
      type:'dues/getOne',
      payload:{
        data:[
          val
        ]
      }
    }).then(res=>{
      let money=0;
      if (res['code'] === 0) {
        memInfo.forEach(i=>{
          if (i.code == record['code']) {
            i['abort']=res['data'][0]['total'];
            i['payDate']=moment(res['data'][0]['deadline']).format('YYYY-MM-DD');
            i['payMonth']=value;
            i['errorMessage']=res['data'][0]['errorMessage'];
          }
          if (!isEmpty(i['abort'])) {
            money+=i['abort']
          }
        });
        this.setState({memInfo,money})
      }
    })
  };
  minus=(val,key)=>{
    const { memInfo=[],money=0 }=this.state;
    let newMoney=0;
    let find=memInfo.filter((it,index)=>index!==key);
    find.forEach(i=>{
      if (!isEmpty(i['abort'])) {
        newMoney+= i['abort']
      }
    });
    this.setState({memInfo:find,money:newMoney});
    let names:Array<string>=[];
    for(let obj of find){
      names.push(obj['name'])
    }
    this['mem'].setState({
      value:names.join('，'),
    });
  };
  maxNum=(record)=>{
      let m1:any = moment(record['lastPayDate']).format('YYYY-MM');
      let time:any=moment().diff(m1, 'months');
    return parseInt(time);
  };
  handleOk=()=>{
    const { data={},onChange} = this.props;
    const { money=0,memInfo=[] }=this.state;
    let val:any=[];
    let find=memInfo.filter(it=>it['isError']!=='0');
    if (!isEmpty(find)) {
      Notice("操作提示",'部分党员无党费标准,请先设置标准或移除该党员',"exclamation-circle-o","orange");
       val=[]
    }else {
      memInfo.map((item,index)=>{
        let obj={
          memCode:item['code'],
          memOrgCode:item['orgCode'],
          memOrgOrgCode:item['memOrgCode'],
          money:item['abort'],
          payType:'2',
          lastPayDate:item['lastPayDate'],
          endPayDate:moment(item['payDate']).valueOf()
        };
        val.push(obj);
      });
    }
  if (!isEmpty(val)) {
    this.props.dispatch({
      type:'dues/getWxpayQRCode',
      payload:{
        data:{
          money,
          chineseMoney:this.numberChinese(money),
          feeOrderDTOList:[...val]
        }
      }
    }).then(res=>{
      if (res['code']==0){
        if (res['data']['QRCode'] == 0) {
          Notice("操作提示",res['message'],"check-circle","green");
          this.handleCancel();
          onChange&&onChange()
        }else {
          Notice("操作提示",res['message'],"check-circle","green");
          this.setState({qsvsb:true,qsData:res['data']});
          this.isPay(res['data']['outTradeNo'])
        }
      }
    })
  }
  };
  isPay = (fee_order) => {
    const { onChange }=this.props;
    let feeOrder=fee_order;
    let timer;
    this.props.dispatch({
      type:'dues/getPayOrderQuery',
      payload:{
        outTradeNo:feeOrder
      }
    }).then(res=>{
      if (res['code'] == 0) {
        const { data={} }=res;
        if (data['success'] == '1') {
          clearTimeout(timer);
          this.setState({visible:false,qsvsb:false});
          this.handleCancel();
          onChange&&onChange(true)
        }else {
          timer=setTimeout(() => {
            this.isPay(feeOrder)
          }, 3000)
        }
      }else {
        Notice("操作提示",res['message'],"exclamation-circle-o","orange");
      }
    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      memInfo:[],
    })
  };
   numberChinese=(str) => {
    let num:any = parseFloat(str);
     let strOutput = "",
      strUnit = '仟佰拾亿仟佰拾万仟佰拾元角分';
    num += "00";
    let intPos = num.indexOf('.');
    if (intPos >= 0){
      num = num.substring(0, intPos) + num.substr(intPos + 1, 2);
    }
    strUnit = strUnit.substr(strUnit.length - num.length);
    for (let i=0; i < num.length; i++){
      strOutput += '零壹贰叁肆伍陆柒捌玖'.substr(num.substr(i,1),1) + strUnit.substr(i,1);
    }
    return strOutput.replace(/零角零分$/, '整').replace(/零[仟佰拾]/g, '零').replace(/零{2,}/g, '零').replace(/零([亿|万])/g, '$1').replace(/零+元/, '元').replace(/亿零{0,3}万/, '亿').replace(/^元/, "零元");

  };
  // componentWillUnmount() {
  //   clearTimeout(this.timer)
  // }
  onClose=()=>{
    this.setState({qsvsb:false})
  };
  render(){
    const {visible,filterHeight,memInfo=[],account='',money=0,qsvsb,qsData={}}=this.state;
    const { data={},dues:{ list1=[],pagination1:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={}}={},loading:{effects = {}} = {} }=this.props;
    const {getFieldDecorator}=this.props.form;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return ((page-1)*pageSize)+index+1
        }
      },
      {
        title:'姓名',
        dataIndex:'name',
        width:50,
      },
      {
        title:'所在组织',
        dataIndex:'orgName',
        width:200,
        render:text => {
          return (
                <Tooltip title={text}>
                  <span>{text.length>8?text.substring(0,8)+'...':text}</span>
                </Tooltip>
          )
        }
      },
      {
        title:'身份证号',
        dataIndex:'idcard',
        width:150,
      },
      {
        title:'已交到',
        dataIndex:'lastPayDate',
        width:100,
        render:text => {
          return <span>{isEmpty(text)?'--':moment(text).format('YYYY-MM-DD ')}</span>
        }
      },
      {
        title:'交费月数',
        dataIndex:'month',
        width:150,
        render:(text,record,index) => {
          return (
            <InputNumber key={record['code']} disabled={isEmpty(record['lastPayDate'])||record['isError']!=='0'} style={{width:50}} max={this.maxNum(record)} min={1} defaultValue={1} onChange={(e)=>this.selMoney(e,record)}/>
            )
        }
      },
      {
        title:'交纳到',
        dataIndex:'payDate',
        width:100,
        render:text => {
          return <span>{isEmpty(text)?'--':text}</span>
        }
      },
      {
        title:'交费金额',
        dataIndex:'abort',
        width:100,
        render:text => {
          return <span>{isEmpty(text)?'--':text+'元'}</span>
        }
      },
      {
        title:'提示',
        dataIndex:'errorMessage',
        width:100,
        render:(text,record) => {
          return <span>{isEmpty(record['lastPayDate'])?'该党员暂无上次缴费时间,请管理员前去设置':text}</span>
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        width:50,
        render:(text,record,index)=>{
          return <Button onClick={()=>this.minus(record,index)} size={'small'} icon={<DeleteOutlined />} style={{color:'red'}}>删除</Button>;
        },
      },
    ];
    return(
      <div>
        <Modal
          destroyOnClose
          title="党费交纳"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1200}
          bodyStyle={{height:570,overflow:'auto'}}
        >
          <Form {...formItemLayout}>
            <Alert
              message="温馨提示"
              description={
                <div >
                  <p>1、党员交纳党费必须得有党费交纳标准，没有标准则先设置标准再交党费。</p>
                  <p>2、连续6个月未交纳党费的党员在接受组织相应处理后方可进行补交党费。</p>
                  <p>3、党员交纳党费只能预交本年度内的党费，不能跨年。</p>
                </div>
              }
              type="info"
              showIcon
            />
            <Row>
              <Col span={12}>
                <FormItem
                  style={{marginTop:'50px'}}
                  label="当前组织"
                >
                  <span>{data['name']}</span>
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  style={{marginTop:'50px'}}
                  label="时间"
                >
                  <span>{moment().format('YYYY年MM月DD日')}</span>
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label={'选择人员'}
                  {...formItemLayout1}
                >
                  {getFieldDecorator('mem', {
                    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                    rules: [
                      { required: true, message: '请选择党员!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <MemSelect onChange={(e)=>this.outMem(e,memInfo)} ref={e=>this['mem']=e} isFee={'1'} isPermissionCheck={'0'} searchType={'0'} checkType={'checkbox'}/>
                  )}
                </FormItem>
              </Col>
            </Row>
            <ListTable
              columns={columns}
              data={memInfo}
              scroll={{y:filterHeight}}
              pagination={false}
              />
            <Row>
              <Col span={8}>
                <FormItem
                  style={{marginTop:'50px'}}
                  label="经办人："
                >
                  <span>{account}</span>
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem
                  style={{marginTop:'50px'}}
                  label="大写："
                >
                  <span>{this.numberChinese(money)}</span>
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem
                  style={{marginTop:'50px'}}
                  label="小写："
                >
                  <span>{money+'元'}</span>
                </FormItem>
              </Col>
            </Row>
          </Form>
        </Modal>
        <Modal
          visible={qsvsb}
          width={600}
          closable={false}
          footer={<Button onClick={this.onClose}>返回</Button>}
        >
          <div className={styles.pay1}>
            <div>
              订单编号：{qsData['outTradeNo']}
            </div>
            <div>
              应付金额：￥{money}元
            </div>
          </div>
          <div className={styles.pay}>
            <div>
              <img src={qslogo} style={{width:'5%'}}/>
              <span>微信支付</span>
            </div>
          <div>
            支付￥{money}元
          </div>
          </div>
          <div style={{textAlign:'center',marginTop:'20px'}}>
            <QRCode size={200} value={qsData['codeUrl']}/>
            <p><img src={shuoming} style={{width:'200px'}}/></p>
          </div>
        </Modal>
      </div>
    )
  }
}
export default Form.create()(index)
