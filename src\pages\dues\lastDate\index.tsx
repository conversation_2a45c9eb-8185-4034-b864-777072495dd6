/**
 * 党费交纳时间设置
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Alert, Col, DatePicker, Input, InputNumber, Modal, Radio, Row, Switch } from 'antd';
import DictSelect from '@/components/DictSelect';
import DictArea from '@/components/DictArea';
import OrgSelect from '@/components/OrgSelect';
import {connect} from "dva";
import moment from 'moment'
import Notice from '@/components/Notice';
import { getSession } from '@/utils/session';
import { isEmpty } from '@/utils/method';
const {MonthPicker}=DatePicker;
const RadioGroup=Radio.Group;
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
@connect(({transferIn})=>({transferIn}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      key:new Date().valueOf(),
      stand:'1',
    };
  }
  showModal=()=>{
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
      org
    },()=>{
      // this.selectMem()
    });
  };
  disabledTomorrow=(current)=>{
    return current && current > moment().endOf('day')||current<moment('2018-12');
  };

  handleOk=()=>{
    const { data={},onChange} = this.props;
    this.props.form.validateFieldsAndScroll(async (err,val)=>{
      if (err) {
        return
      }
      this.props.dispatch({
        type:'dues/upPayDate',
        payload:{
         data:{
           lastPayDate:moment(val['lastPayDate']).valueOf(),
           code:data['code'],
         }
        }
      }).then(res=>{
        if (res['code']===0){
          Notice("操作提示",res['message'],"check-circle","green");
          this.handleCancel();
          onChange&&onChange(true)
        } else {
          Notice("操作提示",res['message'],"exclamation-circle-o","orange");
        }
      })
    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
    })
  };
  render(){
    const {visible}=this.state;
    const { data={} }=this.props;
    const {getFieldDecorator}=this.props.form;
    return(
      <div>
        <Modal
          destroyOnClose
          title="起始时间设置"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
          bodyStyle={{height:570,overflow:'auto'}}
        >
          <Form {...formItemLayout}>
            <Alert
              message="温馨提示"
              description={
                <div >
                  <p>该党员暂无上次交纳时间，请选择一个时间来作为该党员的党费交纳起始时间</p>
                  <p>该时间为本系统最后交纳日期，党费交纳从此时间的下一个月生效</p>
                </div>
              }
              type="info"
              showIcon
            />
            <FormItem
              style={{marginTop:'50px'}}
              label="党员姓名"
            >
              <span>{data['name']}</span>
            </FormItem>
            <FormItem
              label="党费交纳起始时间"
            >
              {getFieldDecorator('lastPayDate', {
                rules: [{ required: true, message: '党费交纳起始时间'}],
              })(
                <DatePicker style={{width:'100%'}} disabledDate={this.disabledTomorrow}/>
              )}
            </FormItem>
          </Form>
        </Modal>
      </div>
    )
  }
}
export default Form.create()(index)
