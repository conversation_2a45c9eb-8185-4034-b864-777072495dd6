/**
 * 新增/编辑 党支部联系人
 */

import React, { useState, useImperativeHandle } from 'react';
import { Input, Form, Modal } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import Date from '@/components/Date';
import {
  findDictCodeName,
  unixMoment,
  timeSort,
  getIdCardInfo,
  correctIdcard,
} from '@/utils/method.js';
import DictTreeSelect from '@/components/DictTreeSelect';
import { addContactMem, updateContactMem } from '../../../../services';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 13 },
  },
};
const index = (props: any, ref: any) => {
  const { code = '', orgCode = '' } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('新增党支部联系人');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [basinInfo, setBasinInfo] = useState<any>({});

  useImperativeHandle(ref, () => ({
    open: (query?: any) => {
      if (query?.code) {
        setTitle('编辑党支部联系人');
      } else {
        setTitle('新增党支部联系人');
      }
      setBasinInfo(query);
      form.setFieldsValue(query);
      setVisible(true);
    },
  }));
  const hadndleFinish = async (vals: any) => {
    let timeArr = ['startTime'];
    vals = unixMoment(timeArr, vals);
    vals = findDictCodeName(['d189', 'd188'], vals, basinInfo);

    setConfirmLoading(true);
    let url = addContactMem;
    if (basinInfo?.code) {
      url = updateContactMem;
    }
    const { code: resCode = 500 } = await url({
      data: {
        orgCode: code,
        orgLevelCode: orgCode,
        ...basinInfo,
        ...vals,
      },
    });
    setConfirmLoading(false);
    if (resCode == 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
    }
  };
  const handleCancel = () => {
    const { onOk } = props;
    setVisible(false);
    form.resetFields();
    setBasinInfo({});
    onOk && onOk();
  };

  return (
    <Modal
      title={title}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={'400px'}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
        <Form.Item
          label="党支部联系人姓名"
          name={'name'}
          rules={[{ required: true, message: '党支部联系人姓名' }]}
        >
          <Input maxLength={20} />
        </Form.Item>
        <Form.Item
          label="身份证号码"
          name={'idcard'}
          rules={[{ required: false, message: '身份证号码' }]}
        >
          <Input maxLength={18} />
        </Form.Item>
        <Form.Item
          label="联系人类型"
          name={'d188Code'}
          rules={[{ required: true, message: '联系人类型' }]}
        >
          <DictTreeSelect
            codeType={'dict_d188'}
            backType={'object'}
            placeholder={'联系人类型'}
            initValue={basinInfo?.['d188Code']}
            parentDisable={true}
          />
        </Form.Item>
        <Form.Item
          label="联系人层级"
          name={'d189Code'}
          rules={[{ required: true, message: '联系人层级' }]}
        >
          <DictTreeSelect
            codeType={'dict_d189'}
            backType={'object'}
            initValue={basinInfo?.['d189Code']}
            placeholder={'联系人类型'}
            parentDisable={true}
          />
        </Form.Item>
        <Form.Item
          label="联系起始时间"
          name={'startTime'}
          rules={[{ required: true, message: '联系起始时间' }]}
        >
          <Date />
        </Form.Item>
      </Form>
    </Modal>
  );
};
// @ts-ignore
export default React.forwardRef(index);
