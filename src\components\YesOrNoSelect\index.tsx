import React, { Fragment, useEffect, useState } from 'react';
import { Select } from 'antd';
import _isNumber from 'lodash/isNumber';

const index = (props: any) => {
  const { onChange, init, disabled = false } = props;
  const [value, setValue] = useState<any>(undefined);
  useEffect(() => {
    if (_isNumber(init)) {
      setValue(init.toString());
    } else {
      setValue(init);
    }
  }, [init])
  return (
    <Fragment>
      <Select style={{ width: '100%' }} value={value} disabled={disabled} onChange={(e: string) => {
        setValue(e);
        onChange && onChange(+e);
      }}>
        <Select.Option value="1">是</Select.Option>
        <Select.Option value="0">否</Select.Option>
      </Select>
    </Fragment>
  );
};
export default index;
