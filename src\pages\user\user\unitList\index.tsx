import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input, Button, Checkbox } from 'antd';
import {connect} from 'dva';
import UnitTree from '../unitTree'
import {isEmpty} from '@/utils/method.js';
import ListTable from '@/components/ListTable';

@connect(({user})=>({
  user
}))
class index extends React.Component<any,{visible:boolean,data,dataArr:Array<any>}>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      data:[],
      dataArr:[],
    }
  }

  showModal=()=>{
    this.setState({
      visible:true,
    })
  };
  handleOk=(account)=>{
    // const {handleOk}=this.props;
    // if(handleOk){
    //   handleOk();
    // }
    this.props.form.validateFieldsAndScroll(async(errors, values) => {
      if (errors) {
        return;
      }
      this.props.dispatch({
        type:'user/userChange',
        payload:{
          data:{
            ...values,
            isResetPassword:true,
            account:account
          }
        }
      });
      this.handleCancel();
    });

  };
  handleCancel=()=>{
    this.setState({
      visible:false,
    })
  };
  openTree = () => {
    this.setState({visible:true})
  };
  handleNodeCancel = () => {
    this.setState({visible:false})
  };
  checkNodeOk = () => {
    const { onSelect } = this.props;
    const { data} =this.state;
    onSelect(data);
    this.setState({visible:false})
  };
  checkTree = (v) => {
    const { dataInfo } = this.props;
    let data=[];
    if(dataInfo){
      for(let obj of v) {
        let find=dataInfo.find(ob=>ob['orgId']===obj['id']);
        data.push({...obj,isReadOnly:find ? find['isReadOnly']===1 : 3});
      }
    }else{
      data=v;
    }
    this.setState({data});
  };
  //勾选只读权限
  isOk = (e,record) => {
    let {data}=this.state;
    const { dataInfo } = this.props;
    if(!data.length){
      data=[...dataInfo]
    }
    let index=data.findIndex(obj=>obj['id']===record['id']);
    if(index>-1){
      data[index]['isReadOnly']=e.target.checked;
    }
    this.setState({data})
  };
  render(): React.ReactNode {
    let {account,children,dataInfo}=this.props;
    const { data=[],dataArr=[] } = this.state;
    const nodeColumns = [
      {
        key: 'name',
        title:'单位名称',
        dataIndex:'name',
      },
      {
        title:'是否只读',
        dataIndex:'action',
        width:180,
        render:(text,record)=>{
          return(
            <Checkbox
              value={record['isReadOnly']===1}
              defaultChecked={record['isReadOnly']===1}
              onChange={(e)=>this.isOk(e,record)}
           />
          )
        }
      },
    ];
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          title='节点选择'
          visible={this.state.visible}
          onCancel={this.handleNodeCancel}
          onOk={this.checkNodeOk}
          style={{width:400,paddingTop:60}}
          maskClosable={false}
          className='node_modal'
        >
          <React.Fragment>
            <UnitTree onSelect={this.checkTree} dataInfo={dataInfo}><Button onClick={this.openTree}>单位选择</Button></UnitTree>
            <ListTable columns={nodeColumns} data={data.length ? data : dataInfo}/>
          </React.Fragment>
        </Modal>
      </React.Fragment>
    );
  }
}
export default index;
