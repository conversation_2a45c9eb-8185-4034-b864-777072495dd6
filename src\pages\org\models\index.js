import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {getList} from '../services';
// import {getSession} from 'src/utils/session';

const org = modelExtend(listPageModel,{
  namespace: "orgSurvey",
  state:{
    basicInfo:{},
    basicInfoData:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname } = location;
        if(pathname==='/org'){
          // const org=getSession('org') || {};

        }
      });
    }
  },
  effects: {
    // 列表
    *getList({payload}, {call, put,select}) {
      const {filter,orgName}=yield select(state=>state['org']);
      payload['data']={...payload['data'],...filter,orgName};
      const {data = {}} = yield call(getList, payload);
      yield put({
        type: 'querySuccess',
        payload: {
          list: data['list'],
          pagination: {
            current: data['pageNumber'],
            pageSize: data['pageSize'],
            total: data['totalRow'],
          }
        }
      })
    },
  }
});
export default org;
