---
description: 
globs: 
alwaysApply: false
---
# 编码标准

## 命名规范

1. **文件命名**：
   - 组件文件使用 PascalCase 命名（如 `UserProfile.tsx`）
   - 非组件 JS/TS 文件使用 camelCase 命名（如 `userService.ts`）
   - 样式文件与对应的组件同名（如 `UserProfile.less`）

2. **变量命名**：
   - 常量使用全大写加下划线（如 `MAX_COUNT`）
   - 普通变量、函数使用 camelCase（如 `getUserData`）
   - React 组件使用 PascalCase（如 `UserProfile`）

## 代码风格

- 缩进：使用 2 个空格
- 引号：使用单引号 `'`
- 分号：语句末尾使用分号 `;`
- 空格：运算符前后添加空格，如 `a + b`

