/**
 * 基本信息
 */
import React, { Fragment } from 'react';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Col, DatePicker, Input, Radio, Row, Switch, Tooltip, Select, InputNumber, Modal } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import moment from 'moment';
import { NumberReg } from "@/utils/validator";
import { connect } from "dva";
import { QuestionCircleOutlined } from '@ant-design/icons';
import { formLabel, formTip, isEmpty, jsonToTree, treeToList, findDictCodeName } from '@/utils/method';
import Date from '@/components/Date';
import YN from '@/components/YesOrNoSelect';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import _isEqual from 'lodash/isEqual';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import _trim from 'lodash/trim'
import _uniqBy from 'lodash/uniqBy'
import _differenceBy from 'lodash/differenceBy'
import DictSelect from '@/components/DictSelect';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import LinkedSpecialOrg from '@/pages/org/special/components/linkedOrg';
import { getSession, getLocalSession } from '@/utils/session';
import { tipsForChangingOrgType, superUnitOrgLinked, approveOrg } from '@/pages/org/services';
import { getMainUnitByOrg } from '@/pages/org/services/org';
import { LockMsg } from '@/pages/user/lock';
import { normalList } from '@/services';
import { validateLength, validateMobilePhoneNumber } from '@/utils/formValidator';
import ListTable from 'src/components/ListTable';
import { inflowOrganizationDInfo, auditfind } from '@/pages/flowMem/service'

const FormItem = Form.Item;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
    },
};
const formItemLayout2 = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 },
    },
};
const formItemLayout3 = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 12 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 8 },
    },
};
// @ts-ignore
@connect(({ loading, commonDict }) => ({ orgAdd: loading.effects['org/add'], orgUpdate: loading.effects['org/update'], commonDict }))
class index extends React.Component<any, any> {
    constructor(props) {
        super(props);
        this.state = {
            timeKey: moment().valueOf(),
            basicInfoCollectiveEconomy: [],
            linkedDTOListLh: [],
            timeKey2: moment().valueOf(),
        };
    }
    componentDidMount() {
        this.setState({ timeKey: moment().valueOf() });

        if (this.props?.record?.id) {
            this.findBaseInfo()
        }

    }



    static getDerivedStateFromProps = (nextProps, prevState) => {
        const state = {};
        const { org: { basicInfo = {} } = {} } = nextProps;
        const { _basicInfo } = prevState;
        if (!_isEqual(_basicInfo, basicInfo)) {
            state['linkedDTOListLh'] = basicInfo['linkedDTOList']
            state['linkedDTOList_old'] = _cloneDeep(basicInfo['linkedDTOList'])
            state['_basicInfo'] = basicInfo;
            // 这点需要对form表单重新赋值，因为194和195传入的值和返回的值会不一样
            if (basicInfo?.d01Code && basicInfo.d01Code != '25') {  //梁才--组织类别为2开头时国民经济赋默认值S
                if (basicInfo.d01Code.startsWith('1') || basicInfo.d01Code.startsWith('2')) {
                    state['d194CodeSatate'] = 'S'
                    state['d195CodeSatate'] = 'V0000';
                } else {
                    state['d194CodeSatate'] = undefined
                    state['d195CodeSatate'] = undefined;
                }
            } else {
                state['d194CodeSatate'] = undefined
                state['d195CodeSatate'] = undefined;
            }



            state['d194CodeKey'] = moment().valueOf();
            state['d195CodeKey'] = moment().valueOf();
            nextProps.form.setFieldsValue({
                ...basicInfo
            })
            state['basicInfoCollectiveEconomy'] = (basicInfo?.collectiveEconomy || []).map((it, index) => {
                return { ...it, id: moment().valueOf() + index }
            })
        }
        return state;
    };

    findBaseInfo = async () => {
        const { code = 500, data = {} } = await auditfind({
            data: { id: this.props.record.id }
        })
        // console.log(res,'rrrrrrrrrrrrrrrrr')
        // this.props.form.setFieldsValue({
        //   ...data
        // })
        // 判断农民工
        let flowMemTypeCode: any = undefined
        if (data['flowMemTypeCode']) {
            flowMemTypeCode = data['flowMemTypeCode'].length > 0 ? data['flowMemTypeCode']?.split(',') : []
            flowMemTypeCode = flowMemTypeCode.filter(item => item != '')
        }
        if (data['lrdIsFarmer'] == 1) {
            if (Array.isArray(flowMemTypeCode)) {
                flowMemTypeCode.push('5')
            } else {
                flowMemTypeCode = ['5']
            }
        }
        this.setState({
            memInfo: {
                ...data,
                flowMemTypeCode: flowMemTypeCode
            }
        })
    }


    showModal = () => {
        this.setState({
            visible: true,
            loading: true
        })
    }
    render() {
        const { orgAdd, orgUpdate, tipMsg = {}, commonDict, record = {} } = this.props;
        const { getFieldDecorator, getFieldValue } = this.props.form;
        const { basicInfo = {} } = this.props.org;
        const { lockFields = [] } = basicInfo || {};
        const { selectedRowKeys, loading, visible, list, pagination, selectedItems, memInfo = {} } = this.state;
        return (
            <>
                <Form {...formItemLayout} key={this.state.timeKey} style={{ pointerEvents: record.isEdit ? 'auto' : 'none' }}>
                    {/* <FormItem
                        label={formLabel('流动党组织名称', tipMsg['orgFlowName'])}
                    >
                        {getFieldDecorator('orgFlowName', {
                            initialValue: memInfo['orgFlowName'],
                            rules: [{ required: true, message: '' }],
                        })(
                            <Input placeholder={''} disabled />
                        )}
                    </FormItem>

                    <Row>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('申请党组织名称', tipMsg['applyOrgFlowName'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('applyOrgFlowName', {
                                    initialValue: memInfo['applyOrgFlowName'] || undefined,
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('流入地联系人', tipMsg['flowConnectionName'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('flowConnectionName', {
                                    initialValue: memInfo['flowConnectionName'],
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('流入地联系方式', tipMsg['flowConnection'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('flowConnection', {
                                    //   getValueFromEvent: e => _trim(e.target.value),
                                    initialValue: memInfo['flowConnection'],
                                    // rules: [{ required: true, message: '联系电话' }, { pattern: new RegExp('((\\d{11})|^((\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1})|(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1}))$)'), message: '请输入正确的联系电话' }],
                                    rules: [{ required: true, message: '联系电话' }],
                                })(
                                    <Input placeholder={'联系电话'} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('流动原因', tipMsg['d146Name'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('d146Name', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['d146Name'] || undefined,
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={'联系电话'} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('联系电话', tipMsg['connection'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('connection', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['connection'] || undefined,
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={'联系电话'} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('提醒常用手机号码', tipMsg['frequentlyPhone'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('frequentlyPhone', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['frequentlyPhone'] || undefined,
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={'提醒常用手机号码'} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>

                            <FormItem
                                label={formLabel('流动原因详情', tipMsg['flowReasonDetail'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('flowReasonDetail', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['flowReasonDetail'],
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('外出日期', tipMsg['outDate'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('outDate', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['outDate'] || undefined,
                                    rules: [{ required: true, message: '批准成立的党组织' }],
                                })(
                                    <Date disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('外出日期补充说明', tipMsg['outInstructions'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('outInstructions', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['outInstructions'],
                                    rules: [{ required: true, message: '批准成立的党组织' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('党费缴至日期流出地', tipMsg['ghanaDate'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('ghanaDate', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['ghanaDate'],
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Date disabled />
                                )}
                            </FormItem>
                        </Col>
                    </Row> */}
                    {
                        memInfo['dataType'] != '2' ?
                            <FormItem
                                label={formLabel('党员组织关系所在党组织', tipMsg['orgFlowName'])}
                            >
                                {getFieldDecorator('orgFlowName', {
                                    initialValue: memInfo['orgFlowName'],
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem> :
                            <FormItem
                                label={formLabel('党员组织关系所在党组织', tipMsg['applyOrgFlowName'])}
                            >
                                {getFieldDecorator('applyOrgFlowName', {
                                    initialValue: memInfo['applyOrgFlowName'],
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem>
                    }

                    <Row>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('流动党员姓名', tipMsg['name'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('name', {
                                    initialValue: memInfo['name'] || undefined,
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('流动党员身份证号码', tipMsg['idcard'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('idcard', {
                                    initialValue: memInfo['idcard'],
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('党员在流入地常用联系方式', tipMsg['frequentlyPhone'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('frequentlyPhone', {
                                    //   getValueFromEvent: e => _trim(e.target.value),
                                    initialValue: memInfo['frequentlyPhone'],
                                    // rules: [{ required: true, message: '联系电话' }, { pattern: new RegExp('((\\d{11})|^((\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1})|(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1}))$)'), message: '请输入正确的联系电话' }],
                                    rules: [{ required: true, message: '党员在流入地常用联系方式' }],
                                })(
                                    <Input placeholder={'党员在流入地常用联系方式'} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('流入地联系人', tipMsg['flowConnectionName'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('flowConnectionName', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['flowConnectionName'] || undefined,
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={'流入地联系人'} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('流入地联系方式', tipMsg['flowConnection'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('flowConnection', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['flowConnection'] || undefined,
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={'流入地联系方式'} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('流动原因', tipMsg['d146Name'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('d146Name', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['d146Name'] || undefined,
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Input placeholder={'流动原因'} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={24}>

                            {
                                memInfo['dataType'] != '2' ?
                                    <FormItem
                                        label={formLabel('流入地党支部', tipMsg['applyOrgFlowName'])}
                                    // {...formItemLayout2}
                                    >
                                        {getFieldDecorator('applyOrgFlowName', {
                                            initialValue: _isEmpty(memInfo) ? undefined : memInfo['applyOrgFlowName'],
                                            rules: [{ required: true, message: '' }],
                                        })(
                                            <Input placeholder={''} disabled />
                                        )}
                                    </FormItem> :
                                    <FormItem
                                        label={formLabel('流入地党支部', tipMsg['orgFlowName'])}
                                    // {...formItemLayout2}
                                    >
                                        {getFieldDecorator('orgFlowName', {
                                            initialValue: _isEmpty(memInfo) ? undefined : memInfo['orgFlowName'],
                                            rules: [{ required: true, message: '' }],
                                        })(
                                            <Input placeholder={''} disabled />
                                        )}
                                    </FormItem>
                            }

                        </Col>
                        <Col span={24}>
                            <FormItem
                                label={formLabel('外出地点补充说明', tipMsg['outInstructions'])}
                            // {...formItemLayout2}
                            >
                                {getFieldDecorator('outInstructions', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['outInstructions'],
                                    rules: [{ required: true, message: '外出地点补充说明' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('党费交纳：交到流出地至-', tipMsg['ghanaDate'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('ghanaDate', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['ghanaDate'],
                                    rules: [{ required: true, message: '' }],
                                })(
                                    <Date disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label={formLabel('外出日期', tipMsg['outDate'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('outDate', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['outDate'] || undefined,
                                    rules: [{ required: true, message: '外出日期' }],
                                })(
                                    <Date disabled />
                                )}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            {(() => {
                                let initialValue = _isEmpty(memInfo) ? undefined : memInfo['flowMemTypeCode']
                                return (
                                    <FormItem label={formLabel('人员类型', tipMsg['flowMemTypeCode'])}
                                        {...formItemLayout2}
                                    >
                                        {getFieldDecorator('flowMemTypeCode', {
                                            initialValue: initialValue,
                                            rules: [{ required: false, message: '人员类型' }],
                                        })(
                                            <DictTreeSelect
                                                treeCheckable={true}
                                                backType={'object'}
                                                initValue={initialValue}
                                                codeType={'dict_flow_mem_type'}
                                                placeholder={'请选择人员类别'}
                                                parentDisable={true}
                                                disabled={true}
                                            />)}
                                    </FormItem>
                                )
                            })()}
                        </Col>
                        {memInfo['flowMemTypeCode'] && memInfo['flowMemTypeCode'].includes('4') && <Col span={24}>
                            <FormItem
                                label={formLabel('人员类型备注', tipMsg['flowMemTypeRemark'])}
                            // {...formItemLayout2}
                            >
                                {getFieldDecorator('flowMemTypeRemark', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['flowMemTypeRemark'],
                                    rules: [{ required: true, message: '人员类型备注' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem>
                        </Col>}
                        {memInfo['flowMemTypeCode'] && memInfo['flowMemTypeCode'].includes('36') && <Col span={24}>
                            <FormItem
                                label={formLabel('人员类型新就业备注', tipMsg['flowMemTypeNewRemark'])}
                            // {...formItemLayout2}
                            >
                                {getFieldDecorator('flowMemTypeNewRemark', {
                                    initialValue: _isEmpty(memInfo) ? undefined : memInfo['flowMemTypeNewRemark'],
                                    rules: [{ required: true, message: '人员类型新就业备注' }],
                                })(
                                    <Input placeholder={''} disabled />
                                )}
                            </FormItem>
                        </Col>}
                        {/* <Col span={12}>
                            <FormItem
                                label={formLabel('登记日期', tipMsg['registerTime'])}
                                {...formItemLayout2}
                            >
                                {getFieldDecorator('registerTime', {
                                    initialValue: _isEmpty(memInfo?.registerTime) ? undefined : memInfo['registerTime'] || undefined,
                                    rules: [{ required: true, message: '登记日期' }],
                                })(
                                    <Date disabled />
                                )}
                            </FormItem>
                        </Col> */}


                    </Row>
                </Form>

            </>

        );
    }
}
export default Form.create<any>()(index);
