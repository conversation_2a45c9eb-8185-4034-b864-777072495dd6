import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Select, Alert, Modal, Spin, Input, Button, DatePicker, Switch } from 'antd';
import { throttle } from '@/utils/method';
import style from './index.less';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import MemSelect from '@/components/MemSelect'
import DictSelect from '@/components/DictSelect';
import OrgSelect from '@/components/OrgSelect';
import {changeToCodes} from '../../../config'
import { connect } from 'dva';
import Tip from '@/components/Tip';
const { TextArea } = Input;
const FormItem = Form.Item;
const Option = Select.Option;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const sign = [{key: '微信通知',label:'微信通知',value: '1',},{key: '短信通知',label:'短信通知',value: '2',},{key: '平台通知',label:'平台通知',value: '3',}];
const ways = [{key: '通知人员',label:'通知人员',value: '1'},{key: '通知组织',label:'通知组织',value: '2'}];
const sureWay = [{key:1,value:1,label:'查看确认'},{key:2,value:2,label:'回复确认'},{key:3,value:3,label:'提交文件确认'}];
@connect(({notice,commonDict})=>({notice,commonDict}))
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state={
      noticetype:''
    }
  }
  componentDidMount(): void {
    this.getDict('dict_d67');
  }
  getDict=(obj)=>{
    this.props.dispatch({
      type:'commonDict/getDictTree',
      payload:{
        data:{
          dicName:obj
        }
      }
    })
  };
  validContext=(rule, value, callback)=>{
    if(_get(value,'length',0) >= 200){
      callback('内容不能超过200字')
    }
    callback();
  };
  validateName=(rule, value, callback)=>{
    if(!_isEmpty(value) && value.length >= 20){
      callback('标题应在20字以内');
    }
    callback();
  };
  onSubmit=()=>{
    const {form, onClose} = this.props;
    form.validateFieldsAndScroll(async (err, values) =>{
      if(!err){
        const {memList = [],orgList = []} = values;
        switch (_get(values,'noticetype','')) {
          case '1':
            values['toCodes'] = changeToCodes(memList,'code');
            delete values['memList'];
            break;
          case '2':
            values['toCodes'] = changeToCodes(orgList,'code');
            delete values['orgList'];
            break;
        }
        values['messgaePlan'] = _isEmpty(_get(values,'messgaePlan','')) ? undefined :  parseInt(values['messgaePlan']);
        values['messageType'] = _isEmpty(_get(values,'messageType','')) ? undefined :  parseInt(values['messageType']);
        values['isReply'] = parseInt(_get(values,'isReply[0]','0'));
        delete values['noticetype'];
        // console.log(values,'values');
        const res = await this.props.dispatch({
          type:'notice/add',
          payload:{
            data:{
              ...values,
            }
          }
        });
        const {code = 500} = res;
        if(code === 0){
          Tip.success('操作提示','操作成功');
          onClose && onClose();
        }
      }
    })
  };
  render() {
    const {form, record, commonDict:{dict_d67 = [] }={}} = this.props;
    const { getFieldDecorator,setFieldsValue } = form;
    const {noticetype} = this.state;

    return (
     <div className={style.box}>
       <div style={{flex:1}}>
         <Form>
           <FormItem
             label="通知内容"
             {...formItemLayout1}
           >
             {getFieldDecorator('context', {
               rules: [
                 { required: true, message: '请填写通知内容!' },
                 { validator: this.validContext }
               ],
               initialValue:_isEmpty(record) ? undefined : record['messageContext']
             })(
               <TextArea placeholder={'内容不能超过200字'} rows={10} maxLength={200}/>
             )}
           </FormItem>
           <FormItem
             label="通知标题"
             {...formItemLayout1}
           >
             {getFieldDecorator('name', {
               rules: [{ required: true, message: '请填写通知标题' },{ validator: this.validateName }],
               // initialValue:_isEmpty(details) ? undefined : details['name']
             })(
               <Input placeholder={'20字以内'}/>
             )}
           </FormItem>
           <FormItem
             label="通知方式"
             {...formItemLayout1}
           >
             {getFieldDecorator('messgaePlan', {
               rules: [{ required: true, message: '请选择通知方式' }],
               // initialValue:!_isEmpty(_get(this.state,'groupCode',[])) ?_get(this.state,'groupCode',[]).split(',') : []
             })(
               <Select
                 placeholder="请选择通知方式"
                 style={{width:'100%'}}
               >
                 {
                   sign.map(item=><Option key={item['value']}>{item['label']}</Option>)
                 }
               </Select>
             )}
           </FormItem>
           <FormItem
             label="确认回复方式"
             {...formItemLayout1}
           >
             {getFieldDecorator('isReply', {
               rules: [{ required: true, message: '请选择确认回复方式' }],
               // initialValue:!_isEmpty(_get(this.state,'groupCode',[])) ?_get(this.state,'groupCode',[]).split(',') : []
             })(
               <Select
                 placeholder="确认回复方式"
                 style={{width:'100%'}}
               >
                 {
                   sureWay.map(item=><Option key={item['value']}>{item['label']}</Option>)
                 }
               </Select>
             )}
           </FormItem>
           <FormItem
             label="通知对象"
             {...formItemLayout1}
           >
             {getFieldDecorator('noticetype', {
               rules: [{ required: true, message: '请选择通知对象' }],
               // initialValue:!_isEmpty(_get(this.state,'groupCode',[])) ?_get(this.state,'groupCode',[]).split(',') : []
             })(
               <Select
                 placeholder="请选择通知对象"
                 style={{width:'100%'}}
                 onChange={val=>{this.setState({noticetype:val});setFieldsValue({messageType:undefined})}}
               >
                 {
                   ways.map(item=><Option key={item['value']}>{item['label']}</Option>)
                 }
               </Select>
             )}
           </FormItem>
           <FormItem
             label="消息类型"
             {...formItemLayout1}
           >
             {getFieldDecorator('messageType', {
               rules: [{ required: true, message: '请选择消息类型' }],
               initialValue:_isEmpty(record) ? undefined : _get(record,'messageType','').toString()
             })(
               <Select
                 placeholder="请选择消息类型"
                 style={{width:'100%'}}
               >
                 {
                   noticetype === '1' ? !_isEmpty(dict_d67) && dict_d67.filter(item=>item['type'] != '3').map(item=><Option key={item['key']}>{item['name']}</Option>)
                     :
                     dict_d67.map(item=><Option key={item['key']}>{item['name']}</Option>)
                 }

               </Select>
             )}
           </FormItem>
           {
             noticetype === '1' &&
             <FormItem
               label="通知人员"
               {...formItemLayout1}
             >
               {getFieldDecorator('memList', {
                 rules: [{ required: true, message: '请选择通知人员' }],
                 // initialValue:!_isEmpty(_get(this.state,'groupCode',[])) ?_get(this.state,'groupCode',[]).split(',') : []
               })(
                 <MemSelect checkType={'checkbox'}
                   // initValue={getArrCodes(_get(details,'hostorList',[]).filter(it=>it['isCustomize'] === '0'),'name').toString()}
                 />
               )}
             </FormItem>
           }
           {
             noticetype === '2' &&
             <FormItem
               label="通知组织"
               {...formItemLayout1}
             >
               {getFieldDecorator('orgList', {
                 rules: [{ required: true, message: '请选择通知组织' }],
                 // initialValue:!_isEmpty(_get(this.state,'groupCode',[])) ?_get(this.state,'groupCode',[]).split(',') : []
               })(
                 <OrgSelect placeholder={'请选择通知组织'} />
               )}
             </FormItem>
           }
         </Form>
       </div>
       <div className={style.submit}>
         <Button type="primary" onClick={this.onSubmit} >提交</Button>
       </div>
     </div>
    );
  }
}
export default Form.create()(index);
