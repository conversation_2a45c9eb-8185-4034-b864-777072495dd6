import React from 'react';
import {Button, Modal} from "antd";
import styles from './index.less';
import moment from "moment";

function createMarkup(text) {
  return {__html: text};
}
export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
    }
  }
  handOk=()=>{
    this.handCancel();
  };
  handCancel=()=>{
    this.setState({
      visible:false
    })
  };
  checkTrend=async (code)=>{
    const {prevCode}=this.props;
    const obj=await this.props.checkTrend({code:prevCode,status:code});
    if(obj){
      this.handCancel();
    }
  };
  render(){
    const {visible}=this.state;
    const {editObj={},isAudit=false}=this.props;
    return(
      <Modal
        title={'预览动态'}
        visible={visible}
        onOk={this.handOk}
        onCancel={this.handCancel}
        width={1200}
        bodyStyle={{height:'calc(100vh - 180px)',overflow:'auto',padding:'0 60px'}}
        footer={isAudit ? <React.Fragment>
          <Button type={'danger'} onClick={()=>this.checkTrend('2')}>审核退回</Button>
          <Button type={'primary'} onClick={()=>this.checkTrend('1')}>审核通过</Button>
        </React.Fragment> : null}
      >
        <h2 className={styles.title}>{editObj['tittle']}</h2>
        <div className={styles.second}>
          {/*<span>文章来源：</span>*/}
          <span>发布时间：{editObj['createTime'] ? moment(editObj['createTime']).format('YYYY-MM-DD hh:mm:ss') : null}</span>
          {/*<span>【字体：小 大】</span>*/}
        </div>
        <div className={styles.content} dangerouslySetInnerHTML={createMarkup(editObj['context'])} />
      </Modal>
    )
  }
}
