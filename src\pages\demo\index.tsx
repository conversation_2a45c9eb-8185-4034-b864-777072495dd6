import UploadComp from '@/components/UploadComp';
import { <PERSON><PERSON>, Modal, Steps } from 'antd';
import fetch from 'dva/fetch';
import React, { useEffect, useRef, useState } from 'react';
import st from './index.less';
import { createWorker } from 'tesseract.js';
import { getImgUrl, uploadFile } from '@/services';
import Cropper from 'react-cropper';
import 'cropperjs/dist/cropper.css';
import autofit from 'autofit.js';
import _debounce from 'lodash/debounce';
import CropperPro from 'react-cropper-pro';
import ReactSeamlessScroll from 'rc-seamless-scroll';
import SignatureCanvas from 'react-signature-canvas';

const { Step } = Steps;

// Base64 转 File
const base64ToFile = (base64, fileName) => {
  let arr = base64.split(','),
    type = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], fileName, { type });
};

const index = () => {
  const [step, setStep] = useState<any>(1);
  const [selectText, setSelectText] = useState<any>('');
  const [file, setFile] = useState<any>();
  const [text, setText] = useState<any>();
  const [JTbase64, setJTbase64] = useState<any>();
  // const [url, setUrl] = useState<any>();
  const [url, setUrl] = useState<any>(
    'data:application/octet-stream;base64,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',
  );

  const [vis, setVis] = useState<any>();

  const [dataInfo, setDataInfo] = useState<any>([...new Array(10)]);
  const ref: any = useRef();

  const cropperRef = useRef<any>(null);

  const getImgUrls = async (name) => {
    const base64 = await getImgUrl({ name });
    console.log('🚀 ~ getImgUrls ~ base64:', base64);
    setUrl(base64);
  };

  const onUploadCompChange = async (list, file) => {
    const { response: { code = 500, data = [] } = {} } = file;
    if (code == 0) {
      const f = data?.[0] || {};
      setFile(f);
      getImgUrls(f.name);
      setStep((e) => e + 1);
    }
  };

  const aa = async () => {
    const worker = await createWorker('chi_sim');
    const ret = await worker.recognize(url);
    console.log(ret);
    setText(ret.data.text);
    await worker.terminate();
  };

  // 截图回调
  const onCrop = _debounce(
    () => {
      const cropper: any = cropperRef.current?.cropper;
      const base64rul = cropper.getCroppedCanvas().toDataURL();
      setJTbase64(base64rul);
    },
    1000 * 1,
    {
      trailing: true,
    },
  );

  // 截图上传
  const onClick = async () => {
    let file = base64ToFile(JTbase64, `截图${+new Date()}.png`);
    const formData = new FormData();
    formData.append('file', file);
    // const res = await uploadFile({ formData });
    // if (res.code == 0) {
    //   console.log('🚀 ~ onClick ~ res:', res);
    // }

    // headers: {
    //   Authorization: sessionStorage.getItem('token') || '',
    //   dataApi: sessionStorage.getItem('dataApi') || '',
    //   ...ohterHeaders,
    // }

    const res = await fetch('/api/base/upload', {
      method: 'POST',
      headers: new Headers({
        Authorization: sessionStorage.getItem('token') || '',
        dataApi: sessionStorage.getItem('dataApi') || '',
      }),
      body: formData,
    }).then((response) => {
      if (response.ok) {
        return response.json();
      }
      throw new Error('Network response was not ok.');
    });
    console.log('🚀 ~ onClick ~ res:', res);
  };

  const open = async () => {
    setVis(true);
  };

  useEffect(() => {
    url && aa();
  }, [url]);

  function testSelection() {
    //获取Selection对象
    let selection: any = window.getSelection();
    //调用selection对象的toString()方法就可以获取鼠标拖动选中的文本。
    // console.log('选中的文本为：');
    // console.log(selection.toString());
    //选中的值
    let str = selection.toString();
    return str;
  }
  useEffect(() => {
    const el = document.getElementById('imgText');
    if (el) {
      el.onmouseup = function () {
        setSelectText(testSelection());
      };
    }
  }, []);

  // useEffect(() => {
  //   const autofitInstance:any = autofit;
  //   autofitInstance.init(); // 初始化 Autofit.js
  //   return () => {
  //     autofitInstance.off(); // 在组件卸载时销毁 Autofit.js 实例
  //   };
  // }, []);


  const [signImg, setSignImg] = useState('');
  const [signTip, setSignTip] = useState('请签名');
  
  let sigCanvas: any;
  const clearSign = () => {
    sigCanvas.clear();
  };
  const handleSign = () => {
    setSignImg(sigCanvas.toDataURL('image/png'));
  };

  return (
    <div>
      <Steps current={step}>
        <Step title="上传" description={'第一步'} />
        <Step title="第二步" description={'对比'} subTitle="Left 00:00:08" />
        <Step title="第三步" description={'asd'} />
      </Steps>
      <div>
        {step == 0 && (
          <React.Fragment>
            <UploadComp
              buttonText="选择图片"
              accept=".jpg,.png,.jpeg"
              maxLen={1}
              onChange={onUploadCompChange}
            />
          </React.Fragment>
        )}
        {step == 1 && (
          <div className={st.contrast}>
            <div style={{ overflow: 'auto' }}>
              <img id={'targetImage'} src={url} alt="" />
            </div>
            <div>
              <div id={'imgText'}>{text}</div>
              <div style={{ borderTop: '1px solid red', marginTop: 10 }}>
                <div>选中的文字：</div>
                {selectText}
              </div>
              <div style={{ borderTop: '1px solid red', marginTop: 10 }}>
                <div>
                  选中文字的截图：<Button onClick={open}>截图</Button>
                </div>
              </div>
            </div>
          </div>
        )}

        <Button onClick={onClick}> 确定</Button>

        <Modal
          destroyOnClose
          title="截图"
          visible={vis}
          width={1200}
          onCancel={() => {
            setVis(false);
          }}
          // bodyStyle={{ height: 560, overflow: 'auto' }}
        >
          <Cropper
            src={url}
            style={{ height: 400, width: '100%' }}
            // Cropper.js options
            crop={onCrop}
            ref={cropperRef}
            dragMode={'move'}
            zoomTo={2}
            guides={true}
            viewMode={1}
            minCropBoxHeight={10}
            minCropBoxWidth={10}
            movable={true}
            preview=".img-preview"
          />

          <div
            className="img-preview"
            style={{ width: '100%', height: '300px', overflow: 'hidden' }}
          ></div>
        </Modal>

        <div className={st.scrollBox}>
          <ReactSeamlessScroll
            list={dataInfo}
            ref={ref}
            hover={true}
            direction={'left'}
            wrapperHeight={20}
            step={0.2}
            // singleWidth={40}
          >
            {dataInfo.map?.((it, index) => {
              return (
                <React.Fragment>
                  <div className={st.scorllItem} key={index}>
                    <div>{index}</div>
                  </div>
                </React.Fragment>
              );
            })}
          </ReactSeamlessScroll>
        </div>
      </div>

      <div>
        <div className={st.signContainer}>
          <div className={st.signContent}>
            <SignatureCanvas
              penColor="#000"
              canvasProps={{
                width: 343,
                height: 294,
                className: st.canvasContainer,
              }}
              ref={(ref) => {
                sigCanvas = ref;
              }}
              onBegin={() => setSignTip('')}
            />
            {signTip && <div className={st.signTip}>{signTip}</div>}
          </div>
          <div className={st.buttonContainer}>
            <Button onClick={clearSign} className={st.clearBtn}>
              清除
            </Button>
            <Button onClick={handleSign} className={st.signBtn} type="primary">
              签字确认
            </Button>
          </div>
          {signImg && <img alt="" width={56} height={48} src={signImg} />}
        </div>
      </div>
    </div>
  );
};

export default index;
