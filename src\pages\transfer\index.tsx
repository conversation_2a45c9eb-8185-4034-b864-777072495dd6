/**
 * 关系转接概况
 */
import React, {Fragment} from 'react';
import {connect} from "dva";
import ListTable from 'src/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';
import {
  Button,
  DatePicker,
} from 'antd';
import NowOrg from 'src/components/NowOrg';
import moment from 'moment';
import { isEmpty, setListHeight } from '@/utils/method';
import CardsGroup from '@/components/CardsGroup';
import styles from './index.less';

import { cardConfig, chartConfig } from './transferOverviewConfig';

const {RangePicker}=DatePicker;
// @ts-ignore
@connect(({transferIn,loading})=>({transferIn,loading:loading.effects['transferIn/findInByPage']}))
export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      dataInfo:undefined,
      filter:{},//筛选器
      search:{},//搜索框
      view:false
    };
  }
  cardsEdit=()=>{
    this['transferCardsGroup'].open();
  };
  onPageChange=(page,pageSize)=>{
  };
  RangePickerChange=(val)=>{
    const [start,end] = val;
    if(!isEmpty(start) && !isEmpty(end)){
      this.setState({
        startDate:moment(start,'YYYY-MM-DD').valueOf(),
        endDate:moment(end,'YYYY-MM-DD').valueOf()
      })
    }
  };
  render(){
    const {loading}=this.props;
    const {startDate,endDate} = this.state;
    const columns=[
      {
        title:'姓名',
        dataIndex:'memName',
        width:200,
      },
      {
        title:'申请日期',
        dataIndex:'memOrgName',
        width:300,
      },
      {
        title:'源组织',
        dataIndex:'outflowOrgName',
        width:300,
      },
      {
        title:'目的组织',
        dataIndex:'isProvOutName',
        width:200,
      },
      {
        title:'转接类型',
        dataIndex:'outflowDate',
        width:200,
      },
      {
        title:'审核状态',
        dataIndex:'outflowDate1',
        width:200,
      },
      {
        title:'操作',
        dataIndex:'action',
        width:180,
      },
    ];

    return(
      <div className={styles.container}>
        <NowOrg
          extra={
            <Fragment>
              {/* <RangePicker
                onChange={this.RangePickerChange}
                ranges={{ '当天': [moment(), moment()], '当月': [moment().startOf('month'), moment().endOf('month')] }}
              /> */}
              <Button htmlType={'button'} style={{marginLeft:'5px'}} onClick={this.cardsEdit} type={'primary'}>编辑</Button>
            </Fragment>
          }
        />
        <CardsGroup timeRange={{startDate,endDate}} cardParent={'transferOverview'} ref={e=>this['transferCardsGroup'] = e} cardConfig={cardConfig} chartConfig={chartConfig}/>
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable 
        
         columns={columns} data={[]} pagination={{}} onPageChange={this.onPageChange}/>
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable 
        
         columns={columns} data={[]} pagination={{}} onPageChange={this.onPageChange}/>
      </div>
    )
  }
}
