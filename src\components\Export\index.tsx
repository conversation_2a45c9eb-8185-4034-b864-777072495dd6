import React, { Fragment } from 'react';
import { Modal, Checkbox, Row, Col, Divider, Progress, Button, Popconfirm, Spin, Switch, Radio, Tabs } from 'antd';
import Tip from '@/components/Tip';
import { FormComponentProps } from 'antd/lib/form';
import _isEmpty from 'lodash/isEmpty';
import _cloneDeep from 'lodash/cloneDeep';
import request from "@/utils/request";
import { getExportList, exportMemData } from './services';
import { getSession } from '@/utils/session';
import DictSelect from '@/pages/flowMem/inflows/outWithdraw';
import { Form } from '@ant-design/compatible';
import { CheckCircleOutlined } from "@ant-design/icons";
import { fileDownloadHeader } from '@/utils/method';
import _last from 'lodash/last';
const FormItem = Form.Item;
const { Group } = Checkbox;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
interface Interface extends FormComponentProps {
  tableName: string,
  tableListQuery: object,
  action: string,
  children?: any,
  form?: any,
  noModal?: boolean,
  extraFormData?: any,
  expType?: string,
  expinfo?: Array
}
class index extends React.Component<Interface, any> {
  constructor(props) {
    super(props);
    this.state = {
      modalVisible: false,
      checkBoxData: [],
      checkBoxLoading: false,
      checked: [],
      loading: false,
      btntype: '1'
    }
  }
  getData = async () => {
    const { tableName } = this.props;
    this.setState({ checkBoxLoading: true });
    const res = await getExportList({ tableName });
    const { code = 0, data } = res;
    this.setState({ checkBoxLoading: false });
    if (code === 0) {
      let checked: Array<string> = [];
      data.map(item => {
        if (item['default'] === '1') {
          checked.push(item['field']);
        }
      });
      this.setState({
        checkBoxData: data,
        checked
      })
    }
  };
  open = () => {
    this.getData();
    this.setState({ modalVisible: true })
  };
  submitNoModal = async () => {
    const { action, tableListQuery } = this.props;
    let res = await request(action, {
      method: 'POST',
      body: {
        data: {
          ...tableListQuery,
        }
      },
    });
    if (res['code'] == 0) {
      Tip.success('操作提示', '下载成功，正在下载...')
      let dataApi = sessionStorage.getItem('dataApi') || "";
      fileDownloadHeader(`/api${res.data.url}`, _last(res.data.url.split('/')), { dataApi });
      return 0;
    }
    return 500;
  };
  submit = async () => {
    const { tableName, action, tableListQuery, extraFormData, expType } = this.props;
    const { checked, checkBoxData, btntype } = this.state;
    if (_isEmpty(checked)) {
      Tip.warning('操作提示', '请选择至少一项进行导出。')
    } else {
      let flag = true;
      let extraFormVal = {};
      if (!_isEmpty(extraFormData)) {
        this.props.form.validateFieldsAndScroll(async (err, val) => {
          if (!err) {
            extraFormVal = val;
            extraFormVal['isExportAll'] = val['isExportAll'] ? 1 : 0;
          } else {
            flag = false
          }
        });
      }
      if (flag) {
        let final: Array<object> = [];
        checkBoxData.map(item => {
          checked.map(it => {
            if (it === item['field']) {
              final.push({ field: item['field'], annotation: item['annotation'] })
            }
          })
        });
        Tip.info('正在导出中', '可能会耗时一段时间，请等待...');
        this.setState({ loading: true });

        let modal = this.setTimeOut();
        let otherparams = expType ? {
          [expType]: btntype
        } : {}
        let aaa = {
          ...tableListQuery,
          ...extraFormVal,
          tableName,
          exportList: final,
          ...otherparams
        }
        console.log(aaa,'aaaaaaaaaaaaaaa')
        request(action, {
          method: 'POST',
          body: {
            data: {
              ...tableListQuery,
              ...extraFormVal,
              tableName,
              exportList: final,
              ...otherparams
            }
          },
        }, 'file').then(res => {
          if (res['status'] === 200) {
            clearInterval(this['timer']);
            modal.update({
              icon: <CheckCircleOutlined style={{ color: '#52C41A' }} />,
              title: '下载完成',
              okButtonProps: {
                disabled: false,
              },
              onOk: () => {
                this.canncel();
              },
              content: (
                <div>
                  <Progress percent={100} />
                </div>
              ),
            });
          }
        });

      }

    }
  };
  canncel = () => {
    this.destory();
    this.setState({ modalVisible: false ,btntype: '1'})
  };
  destory = () => {
    this.setState({
      checkBoxData: [],
      checkBoxLoading: false,
      checked: [],
      loading: false,
    })
  };

  groupOnChange = (val) => {
    this.setState({ checked: val })
  };
  setTimeOut = () => {

    let secondsToGo = 0;
    const modal = Modal.info({
      title: '正在导出中，请稍等...',
      okButtonProps: {
        disabled: true,
      },
      okText: '知道了',
      content: (
        <div>
          <Progress percent={secondsToGo} />
        </div>
      ),
    });

    this['timer'] = setInterval(() => {
      secondsToGo += 1;
      if (secondsToGo >= 99) {
        secondsToGo = 99;
        clearInterval(this['timer']);
        modal.update({
          content: (
            <div>
              <Progress percent={secondsToGo} />
            </div>
          ),
        });
      }
      modal.update({
        content: (
          <div>
            <Progress percent={secondsToGo} />
          </div>
        ),
      });
    }, 200);

    return modal;
  };
  formItems = (item) => {
    const { component, value } = item;
    let child = undefined;
    switch (component) {
      case 'switch':
        //@ts-ignore
        child = <Switch checkedChildren="是" unCheckedChildren="否" defaultChecked={value['initialValue']} />;
        break;
      case 'checkbox':
        //@ts-ignore
        child = <Checkbox value={value['field']}>{value['annotation']}</Checkbox>;
        break;
      default:
        break;
    }
    if (child && !_isEmpty(value)) {
      const { getFieldDecorator } = this.props.form;
      return (
        <Col span={12} key={value['field']}>
          <FormItem
            {...formItemLayout}
            label={value['label']}
            key={value['field']}
          >
            {getFieldDecorator(`${value['field']}`, {
              rules: [{ required: value['required'], message: '!' },],
              initialValue: value['initialValue'],
            })(
              child
            )}
          </FormItem>
        </Col>
      )
    }
  };
  extraFormRender = (data) => {
    if (data instanceof Array) {
      let result: Array<any> = [];
      for (let obj of data) {
        if (obj) {
          result.push(this.formItems(obj))
        }
      }
      return result;
    } else {
      return null
    }
  };
  render() {
    const { tableName, form, extraFormData, noModal = false, expinfo, expType } = this.props;
    const { getFieldDecorator } = form;
    const { modalVisible, checkBoxData, checked, loading, checkBoxLoading, btntype } = this.state;
    // if(noModal){
    //   this.submit();
    //   return;
    // }
    return (
      <Modal
        title={'导出'}
        destroyOnClose
        visible={modalVisible}
        onOk={this.submit}
        onCancel={this.canncel}
        width={1100}
        confirmLoading={loading}
        maskClosable={false}
        footer={[
          <Button htmlType={'button'} onClick={this.canncel} key={1}>取消</Button>,
          <Popconfirm title=" 该名册含有大量敏感信息，请妥善保管，严禁外泄!是否确认导出?" onConfirm={this.submit} okText="是" cancelText="否" key={2} placement="topRight">
            <Button htmlType={'button'} type={'primary'}>确定</Button>
          </Popconfirm>
          ,
        ]}
      >
        {
          extraFormData &&
          <Form>
            {this.extraFormRender(extraFormData)}
          </Form>
        }
        {
          expType &&
          <React.Fragment>
            <Divider>导出类型</Divider>
            {/* <Radio.Group value={btntype} onChange={e => {
              this.setState({
                btntype: e.target.value
              })
            }}>
              {
                expinfo.map((i,index)=>{
                  return  <Radio.Button value={i.value} key={index}>{i.label}</Radio.Button>
                })
              }
            </Radio.Group> */}
            <Tabs activeKey={btntype} onChange={e => {
              this.setState({
                btntype: e
              })
            }}>
              {
                expinfo.map((i, index) => {
                  return <Tabs.TabPane tab={i.label} key={i.value} />
                })
              }
            </Tabs>
          </React.Fragment>
        }
        <Divider>导出内容</Divider>
        <Spin spinning={checkBoxLoading}>
          <Group value={checked} onChange={this.groupOnChange}>
            <Row>
              {
                !_isEmpty(checkBoxData) && checkBoxData.map((item, index) => {
                  return (
                    <Col span={6} key={index}><Checkbox value={item['field']}>{item['annotation']}</Checkbox></Col>
                  )
                })
              }
            </Row>
          </Group>
        </Spin>
      </Modal>
    );
  }
}
export default Form.create<any>()(index);
