// 已纳入流入地-操作-流回
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input } from 'antd';
import moment from 'moment';
import Date from '@/components/Date';
import Tip from '@/components/Tip';
import { getSession } from '@/utils/session';
import { outManageFlowBack, inManageFlowBack } from '../../service/index';
const TextArea = Input.TextArea;

const FormItem = Form.Item;
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      memInfo: {},
      visible: false,
      timeKey: moment().valueOf(),
      confirmLoading: false,
    };
  }
  handleOk = () => {
    const { onOk } = this.props;
    const { code } = this.state.memInfo;
    const { modalType = '' } = this.state;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        val.flowOutDate = moment(val.flowOutDate).valueOf();
        let url: any = undefined;
        let p: object = {};
        if (modalType === 'inFlow') {
          url = inManageFlowBack;
          p = { flowBackTime: val.flowOutDate };
        }
        if (modalType === 'outFlow') {
          url = outManageFlowBack;
          p = { flowOutDate: val.flowOutDate };
        }
        if (url) {
          this.setState(
            {
              confirmLoading: true,
            },
            async () => {
              const res = await url({ data: { code, ...p } });
              this.setState({
                confirmLoading: false,
              });
              if (res.code === 0) {
                this.handleCancel();
                if (res.data) {
                  Tip.error('操作提示', res.data || '操作失败');
                } else {
                  Tip.success('操作提示', '操作成功');
                }
                onOk && onOk();
              }
            },
          );
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  open = (type: string, record) => {
    this.setState({ visible: true, memInfo: record, modalType: type, timeKey: moment().valueOf() });
  };
  destroy = () => {
    this.setState({
      memInfo: {},
    });
  };
  render() {
    const { form } = this.props;
    const { getFieldDecorator } = form;
    const { visible, confirmLoading } = this.state;
    return (
      <Modal
        destroyOnClose
        title="流回提示"
        visible={visible}
        onOk={this.handleOk}
        maskClosable={false}
        onCancel={this.handleCancel}
        width={400}
        confirmLoading={confirmLoading}
      >
        {visible && (
          <Fragment key={this.state.timeKey}>
            <div>是否确定该党员已经结束流动？根据情况输入返回日期。</div>
            <Form>
              <FormItem label="" colon={false}>
                {getFieldDecorator('flowOutDate', {
                  rules: [{ required: true, message: '请输入返回日期' }],
                })(<Date />)}
              </FormItem>
            </Form>
          </Fragment>
        )}
      </Modal>
    );
  }
}
export default Form.create()(index);
