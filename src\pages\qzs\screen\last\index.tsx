import React, { useEffect } from 'react';
import Search from '../commonSearch';
import { connect } from 'dva';
const CryptoJS = require('crypto-js');

const encryptedData = CryptoJS.AES.encrypt(
  'qaz@123456',
  CryptoJS.enc.Utf8.parse('AESNBHB3ZA==HKXt'),
  { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 },
);
const password = encryptedData.toString();

const index = (props:any) => {
  useEffect(() => {
    sessionStorage.removeItem('dataApi');
    if(true){
      let account = 'qztest003';
      props.dispatch({
        type: 'login/qzLogin',
        payload: {
          data:{
            password,
            account,
            captchaCode:'U32xS4DACRRP0XtJPhOjlQ=='
          }
        }
      }).then(({code}:any)=>{})
    }

  }, []);
  return (
    <React.Fragment>
      <Search
        style={{ height: 640 }}
        infoKey="last_info"
        goToPath2="/qzs/screen/last/memShow"
        goToPath={'/qzs/screen/last/memSearch'}
      ></Search>
    </React.Fragment>
  );
};

export default connect(({ login }) => ({ login }))(index);