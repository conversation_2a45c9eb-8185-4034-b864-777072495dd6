import React from 'react';
export const MyContext = React.createContext('');
export const Provider=MyContext.Provider;
// 在函数中引入组件
export function withContext(Component) {
  // 然后返回另一个组件
  return function ThemedComponent(props) {
    // 最后使用context theme渲染这个被封装组件
    // 注意我们照常引用了被添加的属性
    return (
      <MyContext.Consumer>
        {data => <Component {...props} context={data} />}
      </MyContext.Consumer>
    );
  };
}

