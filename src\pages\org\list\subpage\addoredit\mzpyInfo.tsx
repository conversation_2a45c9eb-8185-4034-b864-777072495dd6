/**
 * 扩展信息
 */
/**
 * 模块名
 */
import React, { Fragment, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import DictSelect from '@/components/DictSelect';
import Tip from '@/components/Tip';
import { Col, Input, Button, Switch, Row, InputNumber, Tabs, Form } from "antd";
import { values } from 'lodash';
const FormItem = Form.Item;
const { TabPane } = Tabs;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
};
function index(props, ref) {
    const [form] = Form.useForm();
    const { data = {}} = props
    const { basicInfo } = props.org
  const [loading, setLoading] = useState(false);
    useEffect(() => {
        form.setFieldsValue({
            ...data,
        })
    }, []);
    const onFinish = (value) => {
      setLoading(true);
        // value['appraisalSituation'] = value['appraisalSituation']?.key
        props.dispatch({
            type: 'org/addOrUpdate',
            payload: {
                data: {
                    ...value,
                    zbCode: basicInfo['zbCode'],
                    orgCode: basicInfo['orgCode'],
                    code: data?.code || undefined
                }
            },
        }).then(res => {
          setLoading(false);
            if (res.code === 0) {
                // Tip.success('操作提示', res['code'] ? '修改成功' : '新增成功');
              Tip.success('操作提示', '修改成功');
              props.onOK && props.onOK();
            }
        })
    }
    return (

        <Form {...formItemLayout} form={form} onFinish={onFinish} name='mzpyInfo'>
            <Row>
                <Col span={24}>
                    <FormItem
                        label="本年开展评议情况"
                        name="appraisalSituation"
                        rules={[{ required: true, message: '请填写' }]}
                        labelCol={{ span: 8 }}
                        wrapperCol={{ span: 12 }}
                    >
                        <DictSelect placeholder={'本年开展评议情况'} codeType={'dict_d73'} initValue={data['appraisalSituation']!=undefined ? data['appraisalSituation'].toString() : ''} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="结束评议党员数"
                        name="endReviewMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="表彰党员数"
                        name="recognitionMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="评定为不合格党员数"
                        name="evaluateUnqualifiedMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="限期改正党员数"
                        name="deadlineCorrectMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        label="劝退党员数"
                        name="stopMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="劝而不退除名党员数"
                        name="persuadeDismissMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="自行脱党除名党员数"
                        name="removeYourselfMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="党员违纪被开除党籍党员数"
                        name="violationDisciplineMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="党员违纪给予除出党以外纪律处分党员数"
                        name="disciplinaryOutsideMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="限期改正期满仍无转变予以劝退以及劝而不退除名党员数"
                        name="persuadePersuadeMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>
            </Row>
            <div style={{ textAlign: 'center' }}>
                <Button type={'primary'} htmlType={'submit'} style={{ marginRight: 16 }} loading={loading}>保存</Button>
                <Button type={'primary'} danger htmlType={'button'} onClick={() => props.close()}>取消</Button>
            </div>
        </Form>
    );
}
export default forwardRef(index);
