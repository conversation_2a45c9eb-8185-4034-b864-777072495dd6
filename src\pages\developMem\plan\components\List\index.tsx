import React from 'react';
import { Col, Card, Row } from 'antd';
import style from './index.less';
import ListTable from '@/components/ListTable';
import { connect } from 'dva';
import { getSession } from '@/utils/session';
import qs from 'qs';
import {_history as router} from "@/utils/method";
import TopCard from '../TopCard';
import Distribution from '../DistributionModal';
import _isNumber from 'lodash/isNumber';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import { withContext } from '@/utils/global';
@withContext
@connect(({loading,memDevelopPlan})=>({loading,memDevelopPlan}))
export default class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      _context:{},
      getPercent:({code = '', orgCode = '' } = {})=>{ // 百分比等信息
        this.props.dispatch({
          type:'memDevelopPlan/getPercent',
          payload:{code,orgCode}
        })
      }
    }
  }
  static getDerivedStateFromProps = (nextProps:any, prevState:any) => {
    const state = {};
    const {context= {}} = nextProps;
    const {_context = {}, getPercent} = prevState;
    if(!_isEqual(context, _context)){
      state['_context'] = context;
      getPercent(context)
    }
    return state;
  };

  // 筛选
  filterChange=(val)=>{
    this.setState({
      filter:val
    },()=>this.action())
  };
  // 分页
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  action=(val?:object)=>{
    const {pagination={}}=this.props.memDevelopPlan;
    const {current,pageSize}=pagination;
    const {search,filter}=this.state;
    const org=getSession('org') || {};
    this.props.dispatch({
      type:'memDevelopPlan/getList',
      payload:{
        data:{
          memOrgCode:org['orgCode'],
          pageNum:current,
          pageSize,
          ...search,
          ...filter,
          ...val
        }
      }
    })
  };
  distribution=(record)=>{
    const {memDevelopPlan:{planInfo={}}={}} = this.props;
    this['Distribution'].open(record,planInfo)
  };
  distributionOnClose=()=>{
    const {context} = this.props;
    const {getPercent} = this.state;
    this.action();
    getPercent(context);
  };
  render() {
    const {memDevelopPlan = {},loading:{effects = {}} ={}, context = {}} = this.props;
    const {list, pagination,planInfo} = memDevelopPlan;
    const {current,pageSize} = pagination;
    const roles = getSession('roles') || {} ;
    const columns = [
      {
        title:'序号',
        dataIndex:'id',
        align:'center',
        width:58,
        render:(text,record,index)=>{
          return index+1;
        }
      },
      {
        title:'党组织名称',
        dataIndex:'orgName',
        width:250
      },
      {
        title:'指标数',
        dataIndex:'totalNumber',
        width:58,
        render:(text)=>{
          return (
            <span>{ _isNumber(text) ? text : '-' }</span>
          )
        }
      },
      {
        title:'今年已使用指标',
        dataIndex:'usedNumber',
        width:58,
        render:(text)=>{
          return (
            <span>{ _isNumber(text) ? text : '-' }</span>
          )
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        align:'center',
        width:58,
        render:(text,record,index)=>{
          return(
            <span>
                {!_isEmpty(roles) && record['orgCode'] !== roles['managerOrgCode'] && <a onClick={()=>this.distribution(record)}>分配指标</a>}
            </span>
          )
        }
      },
    ];
    const {percent,usedNumber,unfinishedNumber = 0, totalNumber = 0} = planInfo;

    return (
      <div>
        <Card style={{margin:'20px 0'}}>
          {
            context['parentCode'] === '21d2ca26fd5d11e8a5f36c92bf562df8' ?
              <div className={style.topLevel}>
                <div className={style.topLevelBox}>
                  <TopCard title={usedNumber} value="实际发展党员数" icon="user-add" iconcolor='#F9BF00'/>
                </div>
              </div>
              :
              <Row>
                <Col span={6} >
                  <TopCard title={totalNumber} value="发展党员指标数" bordered icon="team" iconcolor="#17C1C5"/>
                </Col>
                <Col  span={6} >
                  <TopCard title={unfinishedNumber} value="未使用指标数" bordered  icon="save" iconcolor='#7DC856'/>
                </Col>
                <Col span={6} >
                  <TopCard title={percent} value="指标执行比例" bordered icon="line-chart" iconcolor='#F3857B' />
                </Col>
                <Col span={6} >
                  <TopCard title={usedNumber} value="实际发展党员数" icon="user-add" iconcolor='#F9BF00'/>
                </Col>
              </Row>
          }
        </Card>
        <ListTable columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
        <Distribution wrappedComponentRef={e => this['Distribution'] = e} onClose={this.distributionOnClose} {...this.props}/>
      </div>
    );
  }
}
