import React from 'react';
import ListTable from '@/components/ListTable';
import { Tabs, Input, Select, Tree, Modal, Switch, Button, Checkbox, Popconfirm } from 'antd';
import moment from 'moment'
import { connect } from 'dva';
import _get from 'lodash/get'
import _isEqual from 'lodash/isEqual';
import { isEmpty, changeListPayQuery } from '@/utils/method';
// import { success } from '@/components/Notice';
import Tip from '@/components/Tip';
import Pegging from '../text/components/Pegging';
const { TabPane } = Tabs;
const { Option } = Select;
const TreeNode = Tree.TreeNode;
const { TextArea } = Input;
const { Search } = Input;
//@ts-ignore

@connect(({tmwTable})=>({tmwTable}),null,null,{forwardRef:true})
export default class index extends React.Component<any, any> {
    constructor(props) {
        super(props);
        // 手动初始化key
        const {initKeys} = props;
        this.state = {
            expandedKeys: isEmpty(initKeys) ? ['html_1'] : initKeys,
            selectedKeys:isEmpty(initKeys) ? ['html_1'] : initKeys,
            visible: false,
            loading: false,
            timeKey:+new Date(),
            key: isEmpty(initKeys) ? 'html_1' : initKeys[0],
            selectHtml: this.selectHtml,
        }
    }

    static getDerivedStateFromProps = (nextProps, prevState) => {
      const state = {};
      const {} = nextProps;
      const org = JSON.parse(sessionStorage.getItem('org') || '{}')
      const {_org,key, selectHtml} = prevState;
      if(!_isEqual(org, _org)){
        state['_org'] = org;
        selectHtml(key);
      }
      return state;
    };

    componentDidMount = () => {
        const {key} = this.state;
        this.select(key);
        // this.selectHtml(key);
        let script = document.createElement("script"), _this = this;
        script.type = "text/javascript";
        script.src = '/js/check.js';
        document.body.appendChild(script);
        window.addEventListener('message', async function (e) {
            const { data } = e;
            if (data && typeof data == 'string') {
                const { tableCellIndex, tableName } = JSON.parse(data);
                let txt = tableCellIndex.split('_');
                let name = tableName.split('_');
                let type = name.includes('replenish') ? '2' : '1'
                txt = txt.slice(1, txt.length);
                _this.setState({
                    tableRow: txt[0],
                    tableColumn: txt[1],
                    type
                }, () => {
                    _this.findVerData(1, 10, name.join('-')).then(res => {
                        _this['peg'] && _this['peg'].setState({
                            visible: true
                        })
                    });
                });
            }
        })
    };

    selectHtml = (val) => {
        const org = JSON.parse(sessionStorage.getItem('org') || '{}')
        this.props.dispatch({
            type: 'tmwTable/queryExcelConfigReturnHtml',
            payload: {
                reportCode: val,
                orgCode: org?.code,
                orgLevelCode: org?.orgCode
            }
        }).then(res => {
            this.setState({
                Html: res,
                timeKey:+new Date(),
                // datas: res.data
            })
        })
    };


    onExpand = (expandedKeys, e) => {//展开树节点
        const { node: { props: { dataRef = {} } = {} } = {}, expanded = false } = e || {};
        const { id = '' } = dataRef;
        const { listTree, memType = '1' } = this.props.tmwTable;
        if (!isEmpty(listTree)) {
            let find = listTree.find(it => it['id'] === id) || {};
            if (isEmpty(_get(find, 'children', [])) && expanded) {
                this.props.dispatch({
                    type: 'tmwTable/getAnnualstatsTree',
                    payload: {
                        id: id
                    }
                });
            }
            this.setState({
                expandedKeys,
            });
        }
    };
    onSelect = (levelCode, e) => {
        const { node: { props: { dataRef = {} } = {} } = {} } = e || {};
        const val = { ...dataRef };
        const { tab } = this.state;
        delete val['children'];

        this.props.dispatch({
            type: 'tmwTable/updateState',
            payload: {
                treeOrg: val,
            }
        });
        if (!isEmpty(levelCode)) {
            this.select(levelCode[0]);
        }

        this.selectHtml(levelCode[0]);
        this.setState({
            selectedKeys: levelCode,
            key: levelCode[0]
        })
    };
    select = (code) => {
        this.props.dispatch({
            type: 'tmwTable/select',
            payload: {
                reportCode: code
            }
        }).then(res => {
            const { code = 500, data = {} } = res;
            const { rowCell = [], colCell = [], tableIndexConfigList = [], replenishConfig: { lineConfigList = [], hasReplenish = undefined, queryType = undefined } = {} } = data;
            if (code === 0) {
                this.setState({
                    rowCell,
                    colCell,
                    tableIndexConfigList,
                    lineConfigList,
                    hasReplenish,
                    queryType
                })
            }
        })
    };


    renderTreeNodes = (data, rootCode) => {//渲染树节点
        if (!isEmpty(data)) {
            return data.map(item => {
                if (item['hasSub'] === '1') {
                    return (
                        <TreeNode title={item['reportName'] || item['shortName']} key={item['reportCode'] || item['levelCode']} isLeaf={item['hasSub'] !== '1'} dataRef={item}>
                            {
                                this.renderTreeNodes(item['children'], item['reportCode'] || item['levelCode'])
                            }
                        </TreeNode>
                    );
                } else {
                    return <TreeNode title={item['reportName'] || item['shortName']} key={item['reportCode'] || item['levelCode']} dataRef={item} />
                }
            })
        }
    };


    findVerData = async (pageNum?, pageSize?, levelCode?) => {
        const { tableRow, tableColumn, key, type } = this.state;
        // const { type='', treeLevel, treeOrg, memType } = this.props.annualstats;
        // const { treeOrg= } =this.props.positionNum;
        let lastCode = levelCode || this.state['levelCode'];
        const org = JSON.parse(sessionStorage.getItem('org') || '{}')
        let params = {
            reportCode: key,
            rowIndex: tableRow,
            colIndex: tableColumn,
            orgCode: org?.code,
            orgLevelCode: org?.orgCode,
            pageNum: pageNum || 1,
            pageSize: pageSize || 10,
            type
        };
        // if (type == 'a') {
        //   if (memType === '3') {
        //     params['orgCode'] = treeOrg['orgCode'] + `,${treeOrg['unitId']}`;
        //   } else {
        //     params['orgCode'] = treeOrg['levelCode'];
        //   }
        // }
        // if (type == 'b') {
        //   params['countLevel'] = treeLevel['code'];
        // }
        const obj = await this.props.dispatch({
            type: 'tmwTable/findVerData',
            payload: { data: { ...params } }
        });
        const { data = {} } = obj;
        let changeList = changeListPayQuery(data || { list: [] });
        this.setState({
            params,
            ...changeList,
            levelCode: lastCode,
            tableType: data ? data['type'] : undefined,
        });
    };
    render(): React.ReactNode {
        const { rowCell, colCell, tableIndexConfigList, key = '', loading, lineConfigList, hasReplenish, queryType, datas = {}, list = [], pagination, tableType, tbCheckLoading, params } = this.state;
        // 增加otherExportPageInfo，便于往年报表导入
        const { tmwTable: { TreeList = [] } = {},otherExportPageInfo, pegging = true } = this.props;
        return (
            <React.Fragment>
                <div style={{ height: '100%' }}>
                    <div id={'leftDiv'} style={{ display: 'inline-block', width: '15%', verticalAlign: 'top', overflow: 'hidden scroll', height: '100%' }}>
                        <Tree
                            onExpand={this.onExpand}
                            onSelect={this.onSelect}
                            expandedKeys={this.state['expandedKeys']}
                            selectedKeys={this.state['selectedKeys'] || [TreeList[0]?.reportCode]}
                        // defaultSelectedKeys={[TreeList[0]['reportCode']]}
                        >
                            {this.renderTreeNodes(TreeList, '-1')}
                        </Tree>
                    </div>
                    <div id={'rightDiv'} style={{ display: 'inline-block', width: '84%', height: '100%', marginLeft: '1%' }}>
                        {otherExportPageInfo && otherExportPageInfo({state:this.state, props:this.props})}
                        <div key={this.state.timeKey} style={{ marginTop: 50, textAlign: 'center' }} dangerouslySetInnerHTML={{ __html: this.state['Html'] }} />
                    </div>
                    {
                      pegging &&
                      <Pegging tableType={tableType} list={list} pagination={pagination} pageChange={this.findVerData} params={params} ref={e => this['peg'] = e} />
                    }
                </div>
            </React.Fragment>
        );
    }
}
