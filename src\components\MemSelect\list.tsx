/**
 * 列表
 */
import React from 'react';
import ListTable from '../ListTable';
import { connect } from 'dva';
import RuiFilter from '../RuiFilter';
import WhiteSpace from '../WhiteSpace';
import { Input, Modal, Button } from 'antd';
import { selfRowSelection } from '@/utils/method.js';
import _isEmpty from 'lodash/isEmpty';
const Search = Input.Search;

@connect(({ memSelect, commonDict, loading }) => ({
  memSelect,
  commonDict,
  loading: loading.effects['memSelect/getList'],
}))
export default class index extends React.Component<any, any> {
  static action(params?: object) {}
  constructor(props) {
    super(props);
    const {selectedRows=[]} = props
    let keys = selectedRows.map((item,index)=>{
      return item.code;
    })
    this.state = {
      code: '',
      search: {},
      selectedRowKeys: keys || [],
      selectedItems: selectedRows || [],
    };
    index.action = this.action;
  }
  componentDidMount(): void {
    const dictData = ['dict_d09', 'dict_d07'];
    for (let obj of dictData) {
      this.props.dispatch({
        type: 'commonDict/getDict',
        payload: {
          data: {
            dicName: obj,
          },
        },
      });
    }
    this.action();
  }
  action = (params?: object) => {
    const { orgCode, searchType = 1, isFee, subordinate ,d08CodeList} = this.props;
    const { search, memName } = this.state;
    const path = window.location.pathname;
    let val =
      path === '/developMem/out' || path === '/developMem/into'
        ? {
            ...params,
            d08CodeList: !params?.d08CodeList ? ['3', '4'] : params?.d08CodeList,
          }
        : {
            ...params,
          };
          if(d08CodeList.length>0) {
            search.d08CodeList = d08CodeList;
          }
    this.props.dispatch({
      type: 'memSelect/getList',
      payload: {
        data: {
          subordinate,
          pageNum: 1,
          pageSize: 10,
          searchType,
          memOrgCode: orgCode,
          memName,
          ...search,
          ...val,
          isFee,
          
        },
      },
    });
  };
  filterChange = (val) => {
    console.log(val,'vvvvvvvvvvvvvvv')
    const { pagination = {} } = this.props.memSelect;
    const { current, pageSize } = pagination;
    this.action({ pageNum: 1, pageSize, ...val });
    this.setState({
      search: val,
    });
  };
  onPageChange = (page, pageSize) => {
    this.action({ pageNum: page, pageSize, subordinate: this.props.subordinate });
  };
  onSelectChange = (selectedRowKeys, record) => {
    const { onChange } = this.props;
    this.setState({
      selectedRowKeys,
      selectedItems: record,
    });
    onChange && onChange(record);
  };
  render() {
    const { list, pagination = {} } = this.props.memSelect;
    // const {current,pageSize}=pagination;
    const { loading, commonDict, checkType = 'radio', subordinate ,d08CodeList} = this.props;
    const { selectedRowKeys } = this.state;
    const columns = [
      // {
      //   title:'序号',
      //   dataIndex:'num',
      //   render:(text,record,index)=>{
      //     return (current-1)*pageSize+index+1
      //   }
      // },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 100,
      },
      {
        title: '性别',
        dataIndex: 'sexCode',
        width: 80,
        render: (text) => {
          return <span> {text === '1' ? '男' : '女'} </span>;
        },
      },
      {
        title: '公民身份证',
        dataIndex: 'idcard',
        width: 160,
      },
      {
        title: '电话',
        dataIndex: 'phone',
        width: 100,
      },
      {
        title: '党员类型',
        dataIndex: 'd08Name',
        width: 80,
      },
      {
        title: '所在组织',
        dataIndex: 'orgName',
        width: 160,
      },
    ];
    let filterData = [
      {
        key: 'd09CodeList',
        name: '工作岗位',
        value: commonDict['dict_d09_tree'],
      },
      {
        key: 'sexCodeList',
        name: '人员性别',
        value: [
          { key: '1', name: '男' },
          { key: '0', name: '女' },
        ],
      },
      {
        key: 'd08CodeList',
        name: '人员类型',
        value: window.location.pathname.startsWith('/developMem/out')
          ? [
              { key: '4', name: '积极分子' },
              { key: '3', name: '发展对象' },
            ]
          : [
              { key: '1', name: '正式党员' },
              { key: '2', name: '预备党员' },
            ],
      },
      {
        key: 'd07CodeList',
        name: '学历教育',
        value: commonDict['dict_d07_tree'],
      },
    ];
    const rowSelection = {
      selectedRowKeys,
      // onChange: this.onSelectChange,
      type: checkType ? checkType : 'radio',
      // onSelect: this.onSelect,
      // hideDefaultSelections: true,

      onSelect: (record, selected) => {
        const { onChange } = this.props;
        let records: any = [];
        if (selected) {
          this.setState({
            selectedRowKeys:
              checkType === 'radio'
                ? [record['code']]
                : [...new Set([...this.state.selectedRowKeys, record['code']])],
            selectedItems:
              checkType === 'radio'
                ? [record]
                : [...new Set([...this.state.selectedItems, record])],
          });
          records =
            checkType === 'radio' ? [record] : [...new Set([...this.state.selectedItems, record])];
        } else {
          this.setState({
            selectedRowKeys: this.state.selectedRowKeys.filter((it) => it !== record['code']),
            selectedItems: this.state.selectedItems.filter((it) => it['code'] !== record['code']),
          });
          records = this.state.selectedItems.filter((it) => it['code'] !== record['code']);
        }
        onChange && onChange(records);
      },
      onSelectAll: (selected, selectedRows, changeRows) => {
        const { onChange } = this.props;
        let records: any = [];
        if (selected) {
          this.setState({
            selectedRowKeys: [
              ...new Set([...this.state.selectedRowKeys, ...changeRows.map((it) => it['code'])]),
            ],
            selectedItems: [...new Set([...this.state.selectedItems, ...changeRows])],
          });
          records = [...new Set([...this.state.selectedItems, ...changeRows])];
        } else {
          let arr = [...this.state.selectedItems];
          changeRows.map((it) => {
            arr = arr.filter((ot) => ot !== it['code']);
          });
          let arr2 = [...this.state.selectedItems];
          changeRows.map((it) => {
            arr2 = arr2.filter((ot) => ot['code'] !== it['code']);
          });
          this.setState({
            selectedRowKeys: arr,
            selectedItems: arr2,
          });
          records = arr2;
        }
        onChange && onChange(records);
      },
    };
    if(d08CodeList.length > 0) {
      //遵义分支档案审核选择人员不需要显示党员类型，默认正式党员，由引用处传入d08CodeList过滤数据-20250716
      filterData = filterData.filter((it) => it.key != 'd08CodeList');
    }
    return (
      <React.Fragment>
        <div>
          <Search
            placeholder="请输入名字等信息"
            enterButton
            allowClear
            onSearch={(val) => {
              const { pagination = {} } = this.props.memSelect;
              const { current, pageSize } = pagination;
              this.action({ pageNum: 1, pageSize, memName: val });
              this.setState({
                memName: val,
              });
            }}
            style={{ width: 400, marginBottom: 20 }}
          />
        </div>
        <RuiFilter data={filterData} onChange={this.filterChange} />
        <WhiteSpace />
        <WhiteSpace />
        <ListTable
          rowSelection={rowSelection}
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={this.onPageChange}
          scroll={{ y: '34vh' }}
          rowKey={(record) => record['code']}
        />
      </React.Fragment>
    );
  }
}
