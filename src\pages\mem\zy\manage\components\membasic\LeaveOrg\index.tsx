import React, { useImperative<PERSON>andle, useState, useEffect } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Modal, Select } from 'antd';
import { getSession } from '@/utils/session';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import { findDictCodeName, unixMoment } from '@/utils/method.js';
import moment from 'moment';
import Tip from '@/components/Tip';
import { connect } from 'dva';
import _isEmpty from 'lodash/isEmpty';
import _startsWith from 'lodash/startsWith';
import _isNumber from 'lodash/isNumber';
import _isEqual from 'lodash/isEqual';
import _isArray from 'lodash/isArray';
import Date from '@/components/Date';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

//@ts-ignore
@connect(
  ({ memBasic, loading, memLeaveOrg, commonDict }) => ({
    memBasic,
    loading,
    memLeaveOrg,
    commonDict,
  }),
  null,
  null,
  { forwardRef: true },
)
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      _leaveInfo: {},
      memInfo: {},
      d12Type: '',
      d50Code: undefined,
    };
  }

  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    const state = {};
    const { memLeaveOrg: { leaveInfo = {} } = {} } = nextProps;
    const { _leaveInfo = {} } = prevState;
    if (!_isEqual(leaveInfo, _leaveInfo)) {
      const { d12Code = '', d50Code = '' } = leaveInfo;
      state['_leaveInfo'] = leaveInfo;
      state['d12Type'] = d12Code;
      state['d50Code'] = d50Code;
    }
    return state;
  };
  // 时间限制
  disabledTomorrow = (current) => {
    return current && current > moment().endOf('day');
  };
  submit = () => {
    const { memLeaveOrg, onClose } = this.props;
    const { leaveInfo = {} } = memLeaveOrg;
    const { memInfo } = this.state;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        val = unixMoment(['leaveOrgDate'], val);
        val = findDictCodeName(['d12', 'd51', 'd50', 'd18'], val, leaveInfo);
        val['code'] = memInfo['code'];
        val['memOrgCode'] = memInfo['memOrgCode'];
        val['name'] = memInfo['name'];
        val['deleteTime'] = _isEmpty(leaveInfo) ? undefined : moment().valueOf();
        // console.log(val,'val');

        const res = await this.props.dispatch({
          type: 'memLeaveOrg/save',
          payload: { data: { ...val } },
        });
        const { code = 500 } = res || {};
        if (code === 0) {
          Tip.success('操作提示', '操作成功');
          this.close();
          onClose && onClose();
        }
      }
    });
  };
  destroy = () => {
    this.setState({
      memInfo: {},
    });
    this.props.dispatch({
      type: 'memLeaveOrg/clear',
      payload: {},
    });
  };
  open = (val, way = 'history') => {
    // openWay 历史党员点为 history, 党员管理点 为 manage
    this.setState({ visible: true, memInfo: val, openWay: way });
  };
  close = () => {
    this.destroy();
    this.setState({ visible: false });
  };
  d12CodeOnChange = ({ key = '' } = {}) => {
    const { form, commonDict: { dict_d50 = [] } = {} } = this.props;
    this.setState({ d12Type: key });
    // if(key === '3'){ //停止党籍
    //
    // }
    if (key === '31') {
      //因出国停止党籍
      let find = dict_d50.find((it) => it.key === '2Z4');
      this.setState({ d50Code: '2Z4' });
      form.setFieldsValue({
        d50Code: find,
      });
    }
    // if(key === '32'){ //因失联停止党籍
    //
    // }
  };

  getDictValue = (formKey) => {
    const { getFieldValue } = this.props.form;
    let obj = getFieldValue(formKey);
    let val = obj;
    if (typeof obj == 'object') {
      val = obj['key'];
    }
    return val;
  };

  render(): React.ReactNode {
    const { loading: { effects = {} } = {}, form, memLeaveOrg } = this.props;
    const { leaveInfo = {} } = memLeaveOrg;
    const { getFieldDecorator } = form;
    const { visible, d12Type, d50Code } = this.state;
    return (
      <Modal
        title={!_isEmpty(leaveInfo) ? '编辑' : '离开党组织'}
        destroyOnClose
        visible={visible}
        onOk={this.submit}
        onCancel={this.close}
        width={'800px'}
        confirmLoading={effects['memLeaveOrg/save']}
      >
        <Form>
          {leaveInfo['d12Code'] == '32' ? (
            <React.Fragment>
              <Form.Item label="失去联系类型">
                {getFieldDecorator('d18Code', {
                  rules: [{ required: true, message: '请选择失去联系类型' }],
                  initialValue: leaveInfo['d18Code'] || undefined,
                })(
                  <DictSelect
                    codeType={'dict_d18'}
                    initValue={leaveInfo['d18Code']}
                    backType={'object'}
                  />,
                )}
              </Form.Item>

              <Form.Item label="失去联系时间">
                {getFieldDecorator('lostContactDate', {
                  rules: [{ required: true, message: '请选择失去联系时间' }],
                  initialValue: leaveInfo['lostContactDate'] || undefined,
                })(<Date />)}
              </Form.Item>
            </React.Fragment>
          ) : (
            <React.Fragment>
              <FormItem label="离开类型" {...formItemLayout}>
                {getFieldDecorator('d12Code', {
                  rules: [{ required: true, message: '离开类型' }],
                  initialValue: leaveInfo['d12Code'] || undefined,
                })(
                  <DictTreeSelect
                    codeType={'dict_d12'}
                    placeholder={'请选择离开类型'}
                    parentDisable={true}
                    backType={'object'}
                    initValue={leaveInfo['d12Code'] || undefined}
                    onChange={(event) => {
                      this['d50Code'] && this['d50Code'].clearAll();
                      this.props.form.setFieldsValue({ d50Code: undefined });
                    }}
                    filter={(data) => {
                      let tree = data;
                      // permission权限列表没有93时，离开类型不显示错误录入
                      const pidArr: any = getSession('pid') || [];
                      if (!pidArr.includes(93)) {
                        tree = tree.filter((it) => !it.key.startsWith('4'));
                      }
                      const { hasRatification } = this.state.memInfo;
                      if (hasRatification === 1) {
                        tree = tree.filter((it) => it.key.startsWith('1'));
                      }
                      if (this.state.openWay == 'manage') {
                        tree = tree.filter((it) => !(it.key.startsWith('3') || it.key.startsWith('2')));
                        // let find = tree.find(it =>it.key === '3');
                        // if(find?.children){
                        //   find.children = find.children.filter(it => it.key != '31');
                        // }
                      }
                      return tree;
                    }}
                    itemsDisabled={['6', '7']}
                  />,
                )}
              </FormItem>

              {(function (_this) {
                let d12Code = _this.getDictValue('d12Code');
                // 选择出党的时候，需要填写离开党组织时间，需要选择出党原因
                if (`${d12Code}`.startsWith('2')) {
                  return (
                    <FormItem label={'出党原因'} {...formItemLayout}>
                      {getFieldDecorator('d50Code', {
                        rules: [{ required: true, message: '请填写原因' }],
                        initialValue: leaveInfo['d50Code'] || undefined,
                      })(
                        <DictTreeSelect
                          codeType={'dict_d30'}
                          ref={(e) => (_this['d50Code'] = e)}
                          placeholder={`请选择${_startsWith(d12Type, '3') ? '停止党籍原因' : '出党原因'
                            }`}
                          parentDisable={true}
                          backType={'object'}
                          initValue={
                            _isEmpty(leaveInfo)
                              ? undefined
                              : leaveInfo['d50Code']
                                ? leaveInfo['d50Code'].split(',')
                                : undefined
                          }
                          onChange={(e) => {
                            form.setFieldsValue({ d50Code: _isEmpty(e) ? undefined : e });
                          }}
                          treeCheckable={true}
                          filter={(data) => {
                            // 选择开除党籍的时候，出党原因只展示党纪处分部分及其他
                            if (d12Code === '21') {
                              data = data.filter((it) => !it.key.startsWith('2'));
                            }
                            // 选择退党除名的时候,选择取不予承认资格的时候，出党原因只展示其他
                            if (d12Code === '22' || d12Code === '27') {
                              data = data.filter((it) => it.key.startsWith('3'));
                            }
                            // 选择劝退的时候，出党原因只展示组织处置以及其他原因
                            // 选择劝退而不出名的时候，出党原因只展示组织处置以及其他原因
                            if (d12Code === '23' || d12Code === '24') {
                              data = data.filter((it) => !it.key.startsWith('1'));
                            }
                            // 选择自行脱党除名的时候，出党原因只展示组织处置（只能选择组织纪律散漫）以及其他原因
                            if (d12Code === '25') {
                              data = data.filter((it) => it.key == '2' || it.key == '3');
                              let find = data.find((it) => it.key == '2');
                              if (find) {
                                let children = find.children.filter((it) => it.key === '25');
                                find.children = children;
                              }
                            }
                            // 选择取消预备党员资格的时候，出党原因展示党纪处分以及组织处置和其他原因
                            if (d12Code === '26') {
                            }
                            return data;
                          }}
                        />,
                      )}
                    </FormItem>
                  );
                }
              })(this)}

              <FormItem label={'离开党组织时间'} {...formItemLayout}>
                {getFieldDecorator('leaveOrgDate', {
                  rules: [{ required: true, message: `请选择离开党组织时间` }],
                  initialValue: !_isNumber(leaveInfo['leaveOrgDate'])
                    ? undefined
                    : moment(leaveInfo['leaveOrgDate']),
                  // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}} />
                })(<Date />)}
              </FormItem>

              {(function (_this) {
                let d12Code = _this.getDictValue('d12Code');
                // 错误录入，增加错误录入原因
                if (`${d12Code}` == '4') {
                  return (
                    <FormItem label={'错误录入原因'} {...formItemLayout}>
                      {getFieldDecorator('errorReason', {
                        rules: [{ required: true, message: '请填写原因' }, {
                          validator: (rule, value, callback) => {
                            if (value && value.length < 20) {
                              callback('原因不能少于20字')
                            }
                            callback();
                          }
                        }],
                        initialValue: leaveInfo['errorReason'] || undefined,
                      })(
                        <Input.TextArea showCount maxLength={300} placeholder={'仅党员重复录入，数据错误的人员经核实后从此处退出，其他原因请通过相应模块退出（例如：出党，停止党籍）。'} rows={4} />
                      )}
                    </FormItem>
                  );
                }
              })(this)}
            </React.Fragment>
          )}
        </Form>

        {/* {
          _startsWith(d12Type, '3') &&
          <FormItem
            label="工作单位及职务"
            {...formItemLayout}
          >
            {getFieldDecorator('workPost', {
              rules: [{ required: false, message: '工作单位及职务' }],
              initialValue: leaveInfo['workPost'] || undefined,
            })(
              <Input placeholder={'工作单位及职务'} />,
            )}
          </FormItem>
        }
        {
          d12Type === '31' &&
          <FormItem
            label="定居的国家（地区）"
            {...formItemLayout}
          >
            {getFieldDecorator('settleArea', {
              rules: [{ required: true, message: '定居的国家（地区）' }],
              initialValue: leaveInfo['settleArea'] || undefined,
            })(
              <Input placeholder={'定居的国家（地区）'} />,
            )}
          </FormItem>
        }
        {
          _startsWith(d12Type, '6') ? <React.Fragment>
            <FormItem
              label="出党原因"
              {...formItemLayout}
            >
              {getFieldDecorator('d50CodeText', {
                rules: [{ required: false, message: '出党原因' }],
                initialValue: leaveInfo['d50CodeText'] || undefined,
              })(
                <Input.TextArea
                  placeholder={`请填写出党原因`}
                  rows={3}
                />,
              )}
            </FormItem>
          </React.Fragment> : <React.Fragment>
            <FormItem
              label={_startsWith(d12Type, '3') ? '停止党籍原因' : '出党原因'}
              {...formItemLayout}
            >
              {getFieldDecorator('d50Code', {
                rules: [{ required: false, message: '请填写原因' }],
                initialValue: leaveInfo['d50Code'] || undefined,
              })(
                <DictTreeSelect
                  codeType={'dict_d50'}
                  placeholder={`请选择${_startsWith(d12Type, '3') ? '停止党籍原因' : '出党原因'}`}
                  parentDisable={true}
                  backType={'object'}
                  initValue={_isEmpty(leaveInfo) ? undefined : leaveInfo['d50Code'] ? leaveInfo['d50Code'].split(',') : undefined}
                  onChange={(e) => {
                    form.setFieldsValue({ d50Code: _isEmpty(e) ? undefined : e });
                  }}
                  treeCheckable={true}
                />,
              )}
            </FormItem>
            {
              (function (_this) {
                const { d50Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                let _keys = typeof d50Code === 'string' ? d50Code.split(',') : d50Code;
                if (_isArray(_keys) && !_isEmpty(_keys)) {
                  let find = _keys.find(it => {
                    let _key = typeof it === 'string' ? it : it['key'];
                    return !_startsWith(_key, '6') && _startsWith(_key, '2Z');
                  });
                  if (find) {
                    return (
                      <FormItem
                        label="备注"
                        {...formItemLayout}
                      >
                        {getFieldDecorator('reasonsNote', {
                          rules: [{ required: true, message: '出党原因备注' }],
                          initialValue: leaveInfo['reasonsNote'] || undefined,
                        })(
                          <Input.TextArea
                            placeholder={`请填写出党原因备注`}
                            rows={3}
                          />,
                        )}
                      </FormItem>
                    );
                  }
                }
              })(this)
            } */}
        {/*<FormItem*/}
        {/*  label="职务级别"*/}
        {/*  {...formItemLayout}*/}
        {/*>*/}
        {/*  {getFieldDecorator('d51Code', {*/}
        {/*    initialValue: leaveInfo['d51Code'] || undefined,*/}
        {/*    rules: [{ required: false, message: '请选择职务级别' }],*/}
        {/*  })(*/}
        {/*    <DictTreeSelect backType={'object'} codeType={'dict_d51'} placeholder="请选择职务级别" parentDisable={true} initValue={leaveInfo['d51Code'] || undefined}/>*/}
        {/*  )}*/}
        {/*</FormItem>*/}
        {/* <FormItem
              label={_startsWith(d12Type, '3') ? '停止党籍时间' : '离开党组织时间'}
              {...formItemLayout}
            >
              {getFieldDecorator('leaveOrgDate', {
                rules: [{ required: true, message: `请选择${_startsWith(d12Type, '3') ? '停止党籍时间' : '离开党组织时间'}` }],
                initialValue: !_isNumber(leaveInfo['leaveOrgDate']) ? undefined : moment(leaveInfo['leaveOrgDate']),
                // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}} />
              })(
                <Date disabledDate={this.disabledTomorrow} />,
              )}
            </FormItem>
          </React.Fragment>
        } */}
      </Modal>
    );
  }
}

export default Form.create<any>()(index);

import { findMemCorrelation } from '@/pages/mem/services';
import ListTable from '@/components/ListTable';

const LeaveTable = React.forwardRef((props: any, ref) => {
  const { title = '关联业务确认', width = 800, onOK } = props;
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState([]);
  const [query, setQurey] = useState<any>({});
  const [pagination, setPagination] = useState({ pageSize: 10, total: 0, pageNum: 1 });
  useImperativeHandle(ref, () => ({
    open: (query) => {
      setVisible(true);
      setQurey(query);
    },
    clear: () => {
      // clear();
    },
  }));
  useEffect(() => {
    if (!_isEmpty(query)) {
      getLists({ pageNum: 1 }).then();
    }
  }, [JSON.stringify(query)]);
  const getLists = async (p = {}) => {
    const {
      code = 500,
      data = [],
      // data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await findMemCorrelation({
      code: query.code,
      ...p,
      // data: {
      //   pageSize: pagination.pageSize,
      //   pageNum: pagination.pageNum,
      // }
    });
    if (code === 0) {
      setList(data);
      // setPagination({ pageNum, total, pageSize });
    }
  };
  const handleOk = () => {
    onOK && onOK(query);
    handleCancel();
  };
  const handleCancel = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setQurey({});
    setList([]);
    setPagination({ pageSize: 10, total: 0, pageNum: 1 });
  };
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 70,
      render: (text, record, index) => {
        return index + 1;
        // return (pagination.pageNum - 1) * pagination.pageSize + index + 1
      },
    },
    {
      title: '关联项',
      dataIndex: 'value',
      width: 100,
    },
    {
      title: '描述',
      dataIndex: 'desc',
    },
  ];
  return (
    <Modal
      title={title}
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={width}
      destroyOnClose={true}
    >
      <ListTable
        columns={columns}
        data={list}
        pagination={false}
        onPageChange={(page: any, pageSize: any) => {
          getLists({ pageNum: page, pageSize });
        }}
      />
    </Modal>
  );
});
export { LeaveTable };
