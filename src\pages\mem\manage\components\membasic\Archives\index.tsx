import React, { Fragment, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Col, Form, Input, Modal, Alert, Button, Popconfirm, Steps, Upload } from 'antd';
import Tip from '@/components/Tip';
import UploadComp, { fitFileUrlForForm, getInitFileList } from '@/components/UploadComp';
import _isEmpty from 'lodash/isEmpty';
import st from './index.less';
import { createWorker } from 'tesseract.js';
import Cropper from 'react-cropper';
import 'cropperjs/dist/cropper.css';
import _debounce from 'lodash/debounce';
import _last from 'lodash/last';
import fetch from 'dva/fetch';
import { archivesSave, archivesFind, pullFile, getImgUrl } from '@/services';
import { PlusOutlined } from '@ant-design/icons';
import _tr from 'lodash/trim';
import _replace from 'lodash/replace';
import { compressAccurately } from 'image-conversion'; // 压缩图片插件

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const upProps = {
  // multiple: true,
  // showUploadList: false,
  name: 'file',
  action: `/api/base/putFile`,
  accept: '.jpg,.png,.jpeg',
  headers: {
    Authorization: sessionStorage.getItem('token') || '',
    dataApi: sessionStorage.getItem('dataApi') || '',
  },
};

// Base64 转 File
const base64ToFile = (base64, fileName) => {
  let arr = base64.split(','),
    type = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], fileName, { type });
};

function testSelection() {
  //获取Selection对象
  let selection: any = window.getSelection();
  //调用selection对象的toString()方法就可以获取鼠标拖动选中的文本。
  // console.log('选中的文本为：');
  // console.log(selection.toString());
  //选中的值
  let str = selection.toString();
  return str;
}

const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

const index = (props: any, ref) => {
  const { title = '标题', onOK } = props;

  const cropperRef = useRef<any>(null);

  const [openType, setOpenType] = useState<any>();

  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const [step, setStep] = useState<any>(0);

  const [info, setInfo] = useState<any>({});

  const [file, setFile] = useState<any>();
  const [selectText, setSelectText] = useState<any>('');
  const [url, setUrl] = useState<any>([]);
  const [text, setText] = useState<any>();
  const [transTextLoading, setTransTextLoading] = useState(false);

  const [vis, setVis] = useState<any>();
  const [JTbase64, setJTbase64] = useState<any>();
  const [tempBase64, setTempBase64] = useState<any>();

  const [fileList, setFileList] = useState<any>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState(false);

  const handleCancel = () => {
    setRecord({});
    setVisible(false);
    setConfirmLoading(false);
    setFile({});
    setSelectText('');
    setUrl('');
    setText('');
    setTransTextLoading(false);
    setVis(false);
    setJTbase64('');
    setPreviewOpen(false);
    setPreviewImage('');
    setPreviewTitle(false);
    setStep(0);
    setInfo({});
    setFileList([]);
    setTempBase64({});
    form.resetFields();
  };
  const onOk = async (e) => {
    if (openType == 'step0') {
      if (fileList.length < 1) {
        Tip.error('操作提示', '至少上传一张');
        return;
      }
      let obj = {
        id: info?.id,
        memCode: record?.code,
        archivesPath: fitFileUrlForForm(fileList)?.split?.(',') || [],
      };

      const res = await archivesSave({
        data: obj,
      });

      if (res.code === 0) {
        Tip.success('操作提示', '操作成功');
        props?.onOK?.();
        handleCancel();
      }
    } else {
      if (fileList.length < 5 || fileList.length > 15) {
        Tip.error('操作提示', '照片在5-15张内');
        return;
      }

      let obj = {
        id: info?.id,
        memCode: record?.code,
        // 政治生活照
        politicalLifes: fitFileUrlForForm(fileList)?.split?.(',') || [],
      };

      const res = await archivesSave({
        data: obj,
      });

      if (res.code === 0) {
        Tip.success('操作提示', '操作成功');
        props?.onOK?.();
        handleCancel();
      }
    }
  };

  const getInfo = async (record, openType) => {
    const res = await archivesFind({ memCode: record.code });
    if (res.code == 0) {
      const { data: { archivesPath = [], politicalLifes = [] } = {} } = res;

      setInfo(res.data);

      if (_isEmpty(res.data)) return;

      // 政治生活照
      // 档案原件
      try {
        let imgList = openType == 'step0' ? [archivesPath] : politicalLifes;
        let photos = getInitFileList(imgList.join(','));
        let arr = photos.map((item, i) => {
          return () => pullFile({ path: item.url });
        });
        const res = await Promise.all(arr.map((it) => it()));
        photos = photos.map((it, index) => {
          return {
            ...it,
            thumbUrl: res[index],
            id: +new Date() + index,
            url: it.url,
          };
        });
        setFileList(photos);
      } catch (e) {
        console.log('🚀 ~ getInfo ~ e:', e, '政治生活照 档案原件');
      }
    }
  };

  useImperativeHandle(ref, () => ({
    open: (query, openType) => {
      setOpenType(openType);
      setVisible(true);
      setRecord(query);
      if (query) {
        getInfo(query, openType);
      }
    },
    clear: () => {
      // clear();
    },
  }));

  return (
    <Modal
      title={openType == 'step0' ? '档案录入' : '数据补充'}
      visible={visible}
      onCancel={handleCancel}
      width={1200}
      bodyStyle={{
        minHeight: 400,
      }}
      destroyOnClose={true}
      footer={[
        <Button onClick={handleCancel}>取消</Button>,
        <Button type="primary" onClick={onOk} loading={confirmLoading}>
          确定
        </Button>,
      ]}
    >
      {visible && (
        <Fragment>
          <div className={st.contrast2}>
            {openType == 'step12' ? (
              <React.Fragment>
                <Alert
                  message="照片上传格式"
                  description={
                    <div>
                      <div>把上传照片的名字修改为此照片含义（例如：集体团队活动XX）。</div>
                      <div>照片最少5张,最多15张</div>
                    </div>
                  }
                  type="info"
                  showIcon
                />
                <div style={{ height: 20 }}></div>
              </React.Fragment>
            ) : (
              <React.Fragment>
                <Alert
                  message="入党申请书上传"
                  description={
                    <div>
                      <div>第一张图片作为大屏展示内容</div>
                    </div>
                  }
                  type="info"
                  showIcon
                />
                <div style={{ height: 20 }}></div>
              </React.Fragment>
            )}

            <Upload
              {...upProps}
              listType="picture-card"
              data={{ model: openType == 'step12' ? 'politicalLifes' : 'archives' }}
              fileList={fileList}
              multiple
              onPreview={async (file: any) => {
                if (!file.url && !file.preview) {
                  file.preview = await getBase64(file.originFileObj);
                }
                setPreviewImage(file?.thumbUrl ? file.thumbUrl : (file.preview as string));
                setPreviewOpen(true);
                setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
              }}
              onChange={({ fileList: newFileList }) => {
                setFileList(newFileList);
              }}
              beforeUpload={(file, fileList) => {
                const { name = '', size = 0 } = file;
                let hasChinese = /[\u4e00-\u9fa5]/g.test(name);
                let fileSize: number = file['size'] / 1024 / 1024;
                return new Promise(async (resolve, reject) => {
                  if (fileSize > 1) {
                    compressAccurately(file, 1024 * 1).then((res) => {
                      resolve(res);
                    });
                  } else {
                    resolve(file);
                  }
                });
              }}
            >
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>点击上传图片</div>
              </div>
            </Upload>

            <Modal
              visible={previewOpen}
              title={previewTitle}
              footer={null}
              onCancel={() => {
                setPreviewOpen(false);
              }}
            >
              <img alt="image" style={{ width: '100%' }} src={previewImage} />
            </Modal>
          </div>
        </Fragment>
      )}
    </Modal>
  );
};
export default React.forwardRef(index);
