// #sddj1,
// #sddj2 {
//   :global {
//     .w-e-toolbar {
//       display: none !important;
//     }
//     .w-e-text-container {
//       height: 350px !important;
//       // height: 50% !important;
//       border: none !important;
//       z-index: 1 !important;
//     }
//     .w-e-text::-webkit-scrollbar {
//       width: 8px !important;
//       height: 8px !important;
//     }
//     .w-e-text::-webkit-scrollbar-thumb {
//       border-radius: 5px !important;
//       box-shadow: 0 0 2px #fff !important;
//       background-color: #ddd !important;
//     }
//     .w-e-text::-webkit-scrollbar-track {
//       border-radius: 0;
//       box-shadow: inset 0 0 2px #fff;
//     }
//   }
// }

.box {
  :global {
    .w-e-toolbar {
      display: none !important;
    }
    .w-e-text {
      border-top: 1px solid #ccc;
    }
  }
}
