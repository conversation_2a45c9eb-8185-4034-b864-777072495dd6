import React from 'react';
import {Table} from 'antd';
import styles from './index.less';

interface propsType{
  columns:Array<object>,
  data:Array<object>,
  rowKey?:any,
  className?:string,
  scroll?:object,
  loading?:boolean,
  onRow?:(record, index)=>object,
  rowSelection?:object,
  rowClassName?:(record:any, index:number)=>string,
  components?:any,
  pagination?:object | boolean,
  onPageChange?:(page:string | number,pageSize:string | number)=>void,
  defaultExpandAllRows?:boolean
  showQuickJumper?:boolean
}

export default class index extends React.Component<propsType,{pageSize?:string}>{
  constructor(props){
    super(props);
    this.state={
      pageSize:props['pageSize'],
    }
  }
  onChange=(page,pageSize)=>{
    const {onPageChange}=this.props;
    this.setState({
      pageSize
    });
    let lastPage=page;
    if(!pageSize){
      lastPage=1
    }else{
      lastPage= pageSize!==pageSize ? 1 : page
    }
    onPageChange && onPageChange(lastPage,pageSize);
  };
  onShowSizeChange=(page, size)=>{
    const {pageSize}=this.state;
    const {onPageChange}=this.props;
    let lastPage=page;
    if(!pageSize){
      lastPage=1
    }else{
      lastPage= pageSize!==size ? 1 : page
    }
    onPageChange && onPageChange(lastPage, size);
  };
  render(): React.ReactNode {
    const defaultPagination={
      current:1,
      pageSize:10,
      total:0,
      size:'small',
      showSizeChanger:true,
      showQuickJumper:!!this.props.showQuickJumper,
      showTotal:(total)=>`共 ${total} 条`,
      onChange:this.onChange,
      // onShowSizeChange:this.onShowSizeChange,
    };
    const {columns,data,rowKey,className,scroll=undefined,loading,onRow ,rowSelection,rowClassName,components,defaultExpandAllRows}=this.props;
    let {pagination}=this.props;
    if(columns.length>0) {
      for (let obj of columns) {
        if (obj['key'] === undefined) {
          obj['key'] = obj['dataIndex']
        }
      }
    }
    if(typeof pagination==='object'){
      pagination={...defaultPagination,...pagination};
    }
    return(
      <Table
        bordered={true}
        onRow={onRow}
        rowSelection={rowSelection}
        className={className ? `${className} ${styles.mytable}` : styles.mytable}
        columns={columns}
        dataSource={data}
        rowKey={rowKey ? rowKey : 'index'}
        //@ts-ignore
        pagination={pagination}
        scroll={scroll}
        loading={loading}
        rowClassName={rowClassName}
        components={components}
        size={'middle'}
        defaultExpandAllRows={defaultExpandAllRows}
      />
    );
  }
}
