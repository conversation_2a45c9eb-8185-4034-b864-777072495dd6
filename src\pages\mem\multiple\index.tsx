import React from 'react';
import {Tabs} from 'antd';
import Own from './components/ownMultule';
import Their from './components/theirMultle';
const TabPane = Tabs.TabPane;
import _isEmpty from 'lodash/isEmpty';
import { connect } from 'dva';
import qs from 'qs';
import { getSession } from '@/utils/session';
import {_history as router} from "@/utils/method";
@connect(({memMultiple})=>({memMultiple}))
export default class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      check:'0',
    }
  }
  onChange = async (val)=>{
    console.log(val,'val');
    await this.props.dispatch({
      type:'memMultiple/clear'
    });
    this.setState({
      check:val
    })
    const {query ={}}=this.props.location || {};
    router.push(`?${qs.stringify({...query,pageNum:1,grapPung:val})}`)
  };

  render() {
    const {check} = this.state;
    const tabs = [
      {key:'0',title:'本组织多重党员',component: <Own {...this.props}/>},
      {key:'1',title:'本组织党员被关联情况',component: <Their {...this.props}/>}
    ];
    return (
      <div style={{height:'100%',overflow:'hidden'}}>
        <Tabs defaultActiveKey="0" onChange={this.onChange}>
          {
            !_isEmpty(tabs) && tabs.map(item=> <TabPane tab={item['title']} key={item['key']}/>)
          }
        </Tabs>
        {
          check == '0' && <Own {...this.props}/>
        }
        {
          check == '1' && <Their {...this.props}/>
        }
      </div>
    );
  }
}
