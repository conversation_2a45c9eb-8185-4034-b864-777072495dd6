/**
 * 届次信息新增和编辑
 */
import React from 'react'
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, Modal, Row, Input, Radio, DatePicker, Switch, Alert } from 'antd';
import qs from 'qs';
import {connect} from "dva";
import { isEmpty } from '@/utils/method';
import { getSession } from '@/utils/session';
import WhiteSpace from '@/components/WhiteSpace';
import UnitSelect from '@/components/UnitSelect'
import styles from './index.less'
import Notice from '@/components/Notice';
import DictSelect from '@/components/DictSelect';
import moment from 'moment'
import Date from '@/components/Date';

const FormItem=Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const menuData=[
  {
    code:'1',
    name:'基本信息',
    icon:'star',
  },
  {
    code:'2',
    name:'',
    icon:'qrcode',
  },
];
@connect(({unit,commonDict,loading})=>({unit,commonDict,loading:loading.effects['unit/getList']}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    let obj=menuData[0];
    this.state={
      visible:false,
      vsb:false,
      org:{}
    };
  }

  showModal=()=>{
    const { dataInfo }=this.props;
    const org=getSession('org') || {};
    this.setState({
      visible:true,
      disabledTime:moment(dataInfo['startDate']).format('YYYY-MM-DD'),
      org
    });
  };

  handleOk=()=>{
    this.props.form.validateFieldsAndScroll((err,val)=> {
      if (err) {
        return
      }
      const { unit, startDate, endDate,isCountryTest,isNowElect, ...value } = val;
      const { dataInfo={} }=this.props;

      this.props.dispatch({
        type:'behalf/jcCheckElectDate',
        payload:{
          data: {
            unitCode:unit[0]['code']||dataInfo['unitCode'],
            startDate:moment(startDate).valueOf(),
            endDate:moment(endDate).valueOf(),
            code:dataInfo['code']||''
          }
        }
      }).then(res=>{
        if (!isEmpty(res['data'])) {
          this.setState({
            vsb:true,
            content:res['data'],
            addInfo:val
          })
        }else {
          this.setState({
            addInfo:val
          },()=>{
            this.add()
          })
        }
      })
    });
    // this.handleCancel();
  };
  isOk=()=>{
    this.isCancel();
   this.add();
  };
  isCancel=()=>{
    this.setState({vsb:false})
  };
  add = ()=> {
    const { addInfo,data }=this.state;
    const { unit, startDate, endDate,isCountryTest,isNowElect, ...value }=addInfo;
    const {onChange,dataInfo={},title=''} =this.props;
    const org = getSession('org') || {} ;
    if (title==='新增届次'){
      this.props.dispatch({
        type:'behalf/jcAddElect',
        payload:{
          data:{
            unitCode:unit[0]['code']||'',
            unitName:unit[0]['name']||'',
            startDate:moment(startDate).valueOf(),
            endDate:moment(endDate).valueOf(),
            isCountryTest:isCountryTest===undefined?0:isCountryTest?1:0,
            isNowElect:isNowElect===undefined?0:isNowElect?1:0,
            d61Name:data['d61Name'],
            levelCode:org['orgCode'],
            ...value
          }
        }
      }).then(res=>{
        if (res['code'] === 0) {
          Notice("操作提示",'保存成功!',"check-circle","green");
          this.handleCancel();
          onChange&&onChange();
        }else {
          Notice("操作提示",res['message'],"exclamation-circle-o","orange");
        }
      })
    }else {
      this.props.dispatch({
        type:'behalf/jcUp',
        payload:{
          data:{
            unitCode:unit[0]['code']||dataInfo['unitCode'],
            unitName:unit[0]['name']||dataInfo['unitName'],
            startDate:isEmpty(startDate)?dataInfo['startDate']:moment(startDate).valueOf(),
            endDate:isEmpty(endDate)?dataInfo['endDate']:moment(endDate).valueOf(),
            isCountryTest:isCountryTest===undefined?dataInfo['isCountryTest']:isCountryTest?1:0,
            isNowElect:isNowElect===undefined?dataInfo['isNowElect']:isNowElect?1:0,
            d61Name:isEmpty(data)?dataInfo['d61Name']:data['d61Name'],
            code:dataInfo['code'],
            levelCode:org['orgCode'],
            ...value
          }
        }
      }).then(res=>{
        if (res['code'] === 0) {
          Notice("操作提示",'保存成功!',"check-circle","green");
          this.handleCancel();
          onChange&&onChange();
        }else {
          Notice("操作提示",res['message'],"exclamation-circle-o","orange");
        }
      })
    }

  };
  handleCancel=()=>{
    this.setState({
      visible:false
    });
    this.props.form.resetFields();
  };
  getUnit=(v)=>{
    this.findByUnitCode(v[0]['code']);
    this.props.form.resetFields(['unit','d61Code','electNum','electName','startDate'])
  };
  findByUnitCode =(code) =>{
    this.props.dispatch({
      type:'behalf/jcFindByUnitCode',
      payload:{
        unitCode:code,
      }
    }).then(res=>{
      if (res['code'] === 0) {
        this.setState({data:res['data'],disabledTime:moment(res['data']['lastEndDate']).format('YYYY-MM-DD')})
      }else {
        Notice("操作提示",res['message'],"exclamation-circle-o","orange");
      }
    })
  };
  changeTime=(v)=>{
    this.setState({
      disabledTime:moment(v).format('YYYY-MM-DD')
    })
  };
  disabledTomorrow=(current)=>{
    return current && current < moment(this.state['disabledTime']).endOf('day');
  };
  validFunction = (rule, value, callback) => {
    if (value){
      switch (rule.field) {
        case 'endDate':
          if (isEmpty(this.state['disabledTime'])){
            return callback('请先选择起始日期')
          }
          break;
      }
    }
    callback()
  };
  render() {
    const {visible,data={},content='',vsb}=this.state;
    const { children ,title,dataInfo={}}=this.props;
    const { getFieldDecorator  } = this.props.form;
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          title={title||''}
          className='out_Modal'
          destroyOnClose
          maskClosable={false}
          closable={false}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1000}
        >
          <div className='container'>
            <Alert
              message="温馨提示"
              description={
                <div>
                  <p>1.党组织类型4种 ：“111 | 中共中央委员会”、“123 | 中共中央直辖市委员会”、“134 | 中共中央直辖市属区委员会”、“145 | 中共中央直辖市属县委员会”。</p>
                  <p>2.党组织关联单位类型2种：“9121 | 乡”、“9122 | 镇”。</p>
                  <p>3.所选单位必须符合以上 <span style={{color:'red'}}>六种类型</span>（党组织类型+党组织关联单位类型）中的其中一种，方可创建党代表届次</p>
                </div>
              }
              type="info"
              showIcon
            />
            <WhiteSpace/>
            <WhiteSpace/>
            <Form {...formItemLayout}>
              <Row>
                <Col span={12}>
                  <FormItem
                    label={'所属单位'}
                  >
                    {getFieldDecorator('unit', {
                      initialValue:isEmpty(dataInfo)?null:dataInfo.unitName,
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <UnitSelect org={this.state['org']} isCreateOrg={1} onChange={this.getUnit} initValue={dataInfo['unitName'] ? dataInfo['unitName']: undefined}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'届次类别'}
                  >
                    {getFieldDecorator('d61Code', {
                      initialValue:isEmpty(data)?dataInfo['d61Code']:data['d61Code'],
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <DictSelect disabled={true} placeholder={'根据所选单位自动生成届次类别'} codeType={'dict_d61'} backType={'object'} initValue={data['d61Code'] || dataInfo['d61Code']}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'届次数'}
                  >
                    {getFieldDecorator('electNum', {
                      initialValue:isEmpty(data)?dataInfo['electNum']:data.electNum,
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <Input placeholder={'请输入'}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'届次名称'}
                  >
                    {getFieldDecorator('electName', {
                      initialValue:isEmpty(data)?dataInfo['electName']:data.electName,
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <Input placeholder={'请输入'}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'起始日期'}
                  >
                    {getFieldDecorator('startDate', {
                      initialValue:isEmpty(dataInfo)?isEmpty(data['lastEndDate'])?undefined:moment(data['lastEndDate']):moment(dataInfo['startDate']),
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                      // <DatePicker onChange={this.changeTime}/>
                    })(

                      <Date />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'届满日期'}
                  >
                    {getFieldDecorator('endDate', {
                      initialValue:isEmpty(dataInfo)?undefined:moment(dataInfo['endDate']),
                      rules: [
                        { required: true, message: '必填!' },
                        { validator: this.validFunction }
                      ],
                      // <DatePicker disabledDate={this.disabledTomorrow}/>
                    })(

                      <Date disabledDate={this.disabledTomorrow} />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'是否当前届次'}
                  >
                    {getFieldDecorator('isNowElect', {
                      initialValue:isEmpty(dataInfo)?true:dataInfo['isNowElect']===1,
                      valuePropName:'checked',
                      // rules: [
                      //   { required: true, message: '必填!' },
                      //   { validator: this.validFunction }
                      // ],
                    })(
                      <Switch checkedChildren="是" unCheckedChildren="否" />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'是否试行年会'}
                  >
                    {getFieldDecorator('isCountryTest', {
                      initialValue:isEmpty(dataInfo)?null:dataInfo['isCountryTest']===1,
                      valuePropName:'checked',
                      // rules: [
                      //   { required: true, message: '必填!' },
                      //   { validator: this.validFunction }
                      // ],
                    })(
                      <Switch checkedChildren="是" unCheckedChildren="否"/>
                    )}
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </div>
        </Modal>
        <Modal
        title={'温馨提示'}
        destroyOnClose
        maskClosable={false}
        closable={false}
        visible={vsb}
        onOk={this.isOk}
        onCancel={this.isCancel}
        >
          <span>{content}</span>
        </Modal>
      </React.Fragment>

    )
  }
}
export default Form.create()(index);
