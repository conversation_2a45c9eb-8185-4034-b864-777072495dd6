import React from 'react';
import { Colgroup, Head as SelfHead, fakeLine } from '@/components/DynamicTableHead';
import {
  workSupervisionNew,
  peggingWorkSupervision,
  reportNewExcel,
  memFlowInspectionForm,
  superviseTable,
  grassRootsOrgLifeExcel,
  peggingWorkSupervisionNew,
  peggingExcelNew,
  FlowExcelNew,
} from '../services';
import moment from 'moment';
import { TableColDepMem } from '../briefing/config';
import { render } from 'react-dom';
// 党组织设置情况督查表
export const TableCol: any = [
  {
    key: '01',
    k0505: '党组织名称',
    parent: '-1',
    style: { width: 300 },
  },
  {
    key: '02',
    k0505: '直属党组织多于300个的党组织数',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '党员数小于50或大于100的党总支数',
    parent: '-1',
  },
  {
    key: '04',
    k0505: '党员数少于3的党支部数',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '0401',
        k0505: '无党员党支部',
        parent: '04',
        style: { borderTop: 'none', visibility: 'hidden' },
      },
      {
        key: '0402',
        k0505: '无党员党支部',
        parent: '04',
      },
    ],
  },
  {
    key: '05',
    k0505: '党员数51-1000的党支部数',
    parent: '-1',
    children: [
      {
        key: '0501',
        k0505: '党员数51-100',
        parent: '05',
      },
      {
        key: '0502',
        k0505: '党员数101-500',
        parent: '05',
      },
      {
        key: '0503',
        k0505: '党员数501-1000',
        parent: '05',
      },
    ],
  },
  {
    key: '06',
    k0505: '党员数多于1000的党支部数',
    parent: '-1',
  },
  {
    key: '07',
    k0505: '基层党组织总数',
    parent: '-1',
  },
  {
    key: '08',
    k0505: '配备书记的基层党组织数',
    parent: '-1',
  },
  {
    key: '09',
    k0505: '配备副书记的基层党组织数',
    parent: '-1',
  },
  {
    key: '10',
    k0505: '配备组织委员的基层党组织数',
    parent: '-1',
  },
  {
    key: '11',
    k0505: '配备纪检委员的基层党组织数',
    parent: '-1',
  },
  {
    key: '12',
    k0505: '配备宣传委员的基层党组织数',
    parent: '-1',
  },
  {
    key: '13',
    k0505: '配备统战委员的基层党组织数',
    parent: '-1',
  },
  {
    key: '14',
    k0505: '配备其他委员的基层党组织数',
    parent: '-1',
  },
];
//反查表头
export const getCheckTableCols = (type: any) => {
  let cols: any = [];
  switch (type) {
    case '1':
      cols = [
        {
          title: '组织名称',
          dataIndex: 'name',
          width: 200,
        },
        {
          title: '组织类别',
          dataIndex: 'd01Name',
          width: 80,
        },
        {
          title: '联系人',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '联系方式',
          dataIndex: 'contactPhone',
          width: 80,
        },
        {
          title: '党组织书记',
          dataIndex: 'secretary',
          width: 50,
        },
        {
          title: '党员人数',
          dataIndex: 'v6',
          width: 50,
        },
        {
          title: '正式党员人数',
          dataIndex: 'v7',
          width: 50,
        },
        {
          title: '支委会人数',
          dataIndex: 'v8',
          width: 50,
        },
        {
          title: '下一次换届时间',
          dataIndex: 'v9',
          width: 50,
        },
        {
          title: '是否设立党小组',
          dataIndex: 'v10',
          width: 50,
        },
        {
          title: '年内召开党员大会次数',
          dataIndex: 'v11',
          width: 50,
        },
        {
          title: '年内召开支委会次数',
          dataIndex: 'v12',
          width: 50,
        },
        {
          title: '年内召开党小组会次数',
          dataIndex: 'v13',
          width: 50,
        },
        {
          title: '年内上党课次数',
          dataIndex: 'v14',
          width: 50,
        },
        {
          title: '发展党员数',
          dataIndex: 'v15',
          width: 50,
        },
        {
          title: '近两年来发展党员数',
          dataIndex: 'v16',
          width: 50,
        },
      ];
      break;
    case '2':
      cols = [
        {
          title: '组织名称',
          dataIndex: 'name',
          width: 200,
        },
        {
          title: '组织类别',
          dataIndex: 'd01Name',
          width: 80,
        },
        {
          title: '联系人',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '联系方式',
          dataIndex: 'contactPhone',
          width: 80,
        },
        {
          title: '党组织书记',
          dataIndex: 'secretary',
          width: 50,
        },
      ];
      break;
    case 'mem':
      cols = [
        {
          title: '所属党组织',
          dataIndex: 'name',
          width: 200,
        },
        {
          title: '姓名',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '性别',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '民族',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '出生年月',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '身份证号',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '所属党支部名称',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '提交入党申请书时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '成为积极分子时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '成为发展对象时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '短期集中培训时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '政治审查结论性意见落款时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '成为预备党员时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '转为正式党员时间',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '是否按期转正',
          dataIndex: 'contacter',
          width: 50,
        },
      ];
      break;
    case 'flow1':
      cols = [
        {
          title: '姓名',
          dataIndex: 'memName',
          width: 200,
        },
        {
          title: '联系电话',
          dataIndex: 'memPhone',
          width: 80,
        },
        {
          title: '流出类型',
          dataIndex: 'outPlaceName',
          width: 80,
        },
        {
          title: '流动类型',
          dataIndex: 'flowTypeName',
          width: 80,
        },
        {
          title: '登记时间',
          dataIndex: 'registerTime',
          width: 110,
          render: (text) => {
            return text && text != '' ? moment(text).format('YYYY-MM-DD') : undefined;
          },
        },
        {
          title: '数据创建时间',
          dataIndex: 'createTime',
          width: 110,
          render: (text) => {
            return text && text != '' ? moment(text).format('YYYY-MM-DD') : undefined;
          },
        },
        {
          title: '外出日期',
          dataIndex: 'outTime',
          width: 100,
          render: (text) => {
            return text && text != '' ? moment(text).format('YYYY-MM-DD') : undefined;
          },
        },
        {
          title: '外出时长',
          dataIndex: 'outDuraion',
          width: 80,
        },
        {
          title: '接收时间',
          dataIndex: 'inReceivingTime',
          width: 100,
          render: (text) => {
            return text && text != '' ? moment(text).format('YYYY-MM-DD') : undefined;
          },
        },
        {
          title: '退回原因',
          dataIndex: 'rejectReasonName',
          width: 80,
        },
        // {
        //   title: '流出日期',
        //   dataIndex: 'outTime',
        //   width: 100,
        // },
        {
          title: '流回日期',
          dataIndex: 'flowBackTime',
          width: 100,
          render: (text) => {
            return text && text != '' ? moment(text).format('YYYY-MM-DD') : undefined;
          },
        },
        {
          title: '流动状态',
          dataIndex: 'flowStepName',
          width: 100,
        },
      ];
      break;
    case 'flow2':
      cols = [
        {
          title: '组织名称',
          dataIndex: 'name',
          width: 200,
        },
        {
          title: '党组织类型名称',
          dataIndex: 'd01Name',
          width: 120,
        },
        {
          title: '联系人',
          dataIndex: 'contacter',
          width: 80,
        },
        {
          title: '联系方式',
          dataIndex: 'contactPhone',
          width: 110,
        },
        {
          title: '批准成立的党组织（县级以上党委）',
          dataIndex: 'approveName',
          width: 250,
        },
        {
          title: '行政区划',
          dataIndex: 'administrativeDivisionName',
          width: 80,
        },
        // {
        //   title: '审批状态',
        //   dataIndex: 'status',
        //   width: 50,
        // },
        {
          title: '成立日期',
          dataIndex: 'createDate',
          width: 100,
          render: (text) => {
            return text && text != '' ? moment(text).format('YYYY-MM-DD') : undefined;
          },
        },
      ];
      break;
    case '6':
      cols = [
        {
          title: '所属党组织',
          dataIndex: 'orgName',
          width: 80,
        },
        {
          title: '姓名',
          dataIndex: 'name',
          width: 80,
        },
        {
          title: '性别',
          dataIndex: 'sexName',
          width: 80,
        },
        {
          title: '民族',
          dataIndex: 'd06Name',
          width: 80,
        },
        {
          title: '出生年月',
          dataIndex: 'birthday',
          width: 80,
        },
        {
          title: '身份证号',
          dataIndex: 'idcard',
          width: 80,
        },
        {
          title: '所属党支部名称',
          dataIndex: 'outBranchOrgName',
          width: 80,
        },
        {
          title: '提交入党申请书时间',
          dataIndex: 'applyDate',
          width: 80,
        },
        {
          title: '成为积极分子时间',
          dataIndex: 'activeDate',
          width: 80,
        },
        {
          title: '成为发展对象时间',
          dataIndex: 'objectDate',
          width: 80,
        },
        {
          title: '短期集中培训时间',
          dataIndex: 'shortTrainingBeginEndTime',
          width: 80,
        },
        {
          title: '政治审查结论性意见落款时间',
          dataIndex: 'reviewConclusionTime',
          width: 80,
        },
        {
          title: '成为预备党员时间',
          dataIndex: 'topreJoinOrgDate',
          width: 80,
        },
        {
          title: '转为正式党员时间',
          dataIndex: 'topartTurnPartyDate',
          width: 80,
        },
        {
          title: '是否按期转正',
          dataIndex: 'd28Name',
          width: 80,
        },
      ];
      break;

      cols = [
        {
          title: '组织名称',
          dataIndex: 'name',
          width: 200,
        },
        {
          title: '组织类别',
          dataIndex: 'd01Name',
          width: 80,
        },
        {
          title: '联系人',
          dataIndex: 'contacter',
          width: 50,
        },
        {
          title: '联系方式',
          dataIndex: 'contactPhone',
          width: 80,
        },
        {
          title: '党组织书记',
          dataIndex: 'secretary',
          width: 50,
        },
      ];
      break;
  }
  // switch (type) {
  //   case '1':
  //     cols = [
  //       {
  //         title: '组织名称',
  //         dataIndex: 'name',
  //         width: 200,
  //       },
  //       {
  //         title: '组织类别',
  //         dataIndex: 'd01Name',
  //         width: 80,
  //       },
  //       {
  //         title: '联系人',
  //         dataIndex: 'contacter',
  //         width: 50,
  //       },
  //       {
  //         title: '联系方式',
  //         dataIndex: 'contactPhone',
  //         width: 80,
  //       },
  //       {
  //         title: '党组织书记',
  //         dataIndex: 'secretary',
  //         width: 50,
  //       },
  //     ];
  //     break;
  //   default:
  //     cols = [
  //       {
  //         title: '组织名称',
  //         dataIndex: 'name',
  //         width: 200,
  //       },
  //       {
  //         title: '组织类别',
  //         dataIndex: 'd01Name',
  //         width: 80,
  //       },
  //       {
  //         title: '联系人',
  //         dataIndex: 'contacter',
  //         width: 50,
  //       },
  //       {
  //         title: '联系方式',
  //         dataIndex: 'contactPhone',
  //         width: 80,
  //       },
  //       {
  //         title: '党组织书记',
  //         dataIndex: 'secretary',
  //         width: 50,
  //       },
  //     ];
  //     break;
  // }
  return cols;
};

// 党组织设置情况督查表
export const TableColNew: any = [
  {
    key: '01',
    k0505: '党组织名称',
    parent: '-1',
    style: { width: 300 },
  },
  {
    key: '02',
    k0505: '党支部总数',
    parent: '-1',
  },
  {
    key: '04',
    k0505: '正式党员数少于3的党支部数',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '0401',
        k0505: '',
        style: { borderTop: 'none' },
        parent: '04',
      },
      {
        key: '0402',
        k0505: '无党员党支部',
        parent: '04',
      },
    ],
  },
  {
    key: '05',
    k0505: '党员数51以上的党支部数',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '0501',
        k0505: '',
        style: { borderTop: 'none' },
        parent: '05',
      },
      {
        key: '0502',
        k0505: '党员数51-100的党支部数',
        parent: '05',
      },
      {
        key: '0503',
        k0505: '党员数101-500的党支部数',
        parent: '05',
      },
      {
        key: '0504',
        k0505: '党员数501-1000的党支部数',
        parent: '05',
      },
      {
        key: '0506',
        k0505: '党员数多于1000的党支部数',
        parent: '05',
      },
    ],
  },
  {
    key: '06',
    k0505: '党总支总数',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '0601',
        k0505: '',
        style: { borderTop: 'none' },
        parent: '06',
      },
      {
        key: '0602',
        k0505: '党员数小于50或大于100的党总支数',
        parent: '06',
      },
    ],
  },
  {
    key: '07',
    k0505: '基层党委总数',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '0701',
        k0505: '',
        style: { borderTop: 'none' },
        parent: '07',
      },
      {
        key: '0702',
        k0505: '党员数小于100的基层党委数',
        parent: '07',
      },
    ],
  },
  {
    key: '08',
    k0505: '基层党组织总数',
    parent: '-1',
  },
  {
    key: '09',
    k0505: '未配备书记的基层党组织数(已配备党组织书记但未按期换届的未纳入统计)',
    parent: '-1',
  },
  {
    key: '10',
    k0505: '正式党员人数>7的支部数',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '1001',
        k0505: '',
        style: { borderTop: 'none' },
        parent: '10',
      },
      {
        key: '1002',
        k0505: '支委会人数少于3的',
        parent: '10',
      },
      {
        key: '1003',
        k0505: '支委会人数大于7的',
        parent: '10',
      },
    ],
  },
  {
    key: '10',
    k0505: '应换届党组织数',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '1101',
        k0505: '',
        style: { borderTop: 'none' },
        parent: '11',
      },
      {
        key: '1102',
        k0505: '未换届党组织数',
        parent: '11',
      },
    ],
  },
];

// 基层党组织落实党的组织生活制度情况督查表（仅统计支部）
export const TableColNew2: any = [
  {
    key: '01',
    k0505: '党组织名称',
    parent: '-1',
    style: { width: 300 },
  },
  {
    key: '02',
    k0505: '未设立党小组的党支部',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '0201',
        k0505: '',
        style: { borderTop: 'none' },
        parent: '02',
      },
      {
        key: '0202',
        k0505: '未坚持每月组织党员活动的支部数',
        parent: '02',
      },
      {
        key: '0203',
        k0505: '未坚持每季度至少召开一次党员大会的支部数',
        parent: '02',
      },
      {
        key: '0204',
        k0505: '未按月召开支委会的支部数',
        parent: '02',
      },
      {
        key: '0205',
        k0505: '未坚持每季度至少组织党员上一次党课的支部数',
        parent: '02',
      },
    ],
  },
  {
    key: '03',
    k0505: '设立党小组的党支部',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '0301',
        k0505: '',
        style: { borderTop: 'none' },
        parent: '03',
      },
      {
        key: '0302',
        k0505: '未坚持每季度至少召开一次党员大会的支部数',
        parent: '03',
      },
      {
        key: '0303',
        k0505: '未按月召开支委会的支部数',
        parent: '03',
      },
      {
        key: '0304',
        k0505: '未按月召开党小组会的支部数',
        parent: '03 ',
      },
      {
        key: '0305',
        k0505: '未坚持每季度至少组织党员上一次党课的支部数',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '统计时间内未召开组织生活会的支部数',
    parent: '-1',
  },
  {
    key: '05',
    k0505: '统计时间内未开展党员民主评议的支部数',
    parent: '-1',
  },
];

// 基层党组织发展党员工作情况督查表
const [f, ...ohter] = TableColDepMem;
export const TableColNew3: any = [
  {
    key: '01',
    k0505: '党组织名称',
    parent: '-1',
    style: { width: 300 },
  },
  ...ohter,
  {
    key: '02',
    k0505: '发展党员数',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '预备期已满的党员数',
    parent: '-1',
    style: { borderBottom: 'none' },
    children: [
      {
        key: '0301',
        k0505: '',
        style: { borderTop: 'none' },
        parent: '03',
      },
      {
        key: '0302',
        k0505: '未转正党员数',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '连续两年未发展党员的村党组织数',
    parent: '-1',
  },
];

// 党组织有关情况反查表
export const TableColNew8: any = [
  {
    key: '01',
    k0505: '组织名称',
    parent: '-1',
    style: { width: 300 },
  },
  {
    key: '02',
    k0505: '组织类别',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '联系人',
    parent: '-1',
  },
  {
    key: '04',
    k0505: '联系方式',
    parent: '-1',
  },
  {
    key: '05',
    k0505: '党组织书记',
    parent: '-1',
  },
  {
    key: '06',
    k0505: '党员人数',
    parent: '-1',
  },
  {
    key: '07',
    k0505: '正式党员人数',
    parent: '-1',
  },
  {
    key: '08',
    k0505: '支委会人数',
    parent: '-1',
  },
  {
    key: '09',
    k0505: '下一次换届时间（最新一个届次的届满时间）',
    parent: '-1',
  },
  {
    key: '10',
    k0505: '是否设立党小组',
    parent: '-1',
  },
  {
    key: '11',
    k0505: '年内召开党员大会次数（统计时间内）',
    parent: '-1',
  },
  {
    key: '12',
    k0505: '年内召开支委会次数（统计时间内）',
    parent: '-1',
  },
  {
    key: '13',
    k0505: '年内召开党小组会次数（统计时间内）',
    parent: '-1',
  },
  {
    key: '14',
    k0505: '年内上党课次数（统计时间内）',
    parent: '-1',
  },
  {
    key: '15',
    k0505: '发展党员数（统计时间内）',
    parent: '-1',
  },
  {
    key: '16',
    k0505: '近两年来发展党员数（统计年度及上一年度）',
    parent: '-1',
  },
];

// 流出预警
export const TableColWarn1: any = [
  {
    key: '01',
    k0505: '党组织名称',
    parent: '-1',
    style: { width: 300 },
  },
  {
    key: '02',
    k0505: '正在流出总人数',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '未纳入流入地管理',
    parent: '-1',
    children: [
      {
        key: '0301',
        k0505: '总计',
        parent: '03',
      },
      {
        key: '0302',
        k0505: '省内未纳入流入地管理',
        parent: '03',
      },
      {
        key: '0303',
        k0505: '跨省未纳入流入地管理',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '省内流动超20天未纳管',
    parent: '-1',
    children: [
      {
        key: '0401',
        k0505: '人数',
        parent: '04',
      },
      {
        key: '0402',
        k0505: '比例（%）',
        parent: '04',
      },
    ],
  },
  {
    key: '05',
    k0505: '跨省流动超30天未纳管',
    parent: '-1',
    children: [
      {
        key: '0501',
        k0505: '人数',
        parent: '05',
      },
      {
        key: '0502',
        k0505: '比例（%）',
        parent: '05',
      },
    ],
  },
  {
    key: '06',
    k0505: '无固定地点超90天流出地党组织未联系',
    parent: '-1',
    children: [
      {
        key: '0601',
        k0505: '人数',
        parent: '06',
      },
      {
        key: '0602',
        k0505: '比例（%）',
        parent: '06',
      },
    ],
  },
  {
    key: '07',
    k0505: '不掌握流向超过1年',
    parent: '-1',
    children: [
      {
        key: '0701',
        k0505: '人数',
        parent: '07',
      },
      {
        key: '0702',
        k0505: '比例（%）',
        parent: '07',
      },
      {
        key: '0703',
        k0505: '其中：超过2年',
        parent: '07',
        children: [
          {
            key: '070301',
            k0505: '人数',
            parent: '0703',
          },
          {
            key: '070302',
            k0505: '比例（%）',
            parent: '0703',
          },
        ],
      },
    ],
  },
  {
    key: '08',
    k0505: '未查看在流入地主动报道提醒',
    parent: '-1',
  },
  {
    key: '09',
    k0505: '对方未备案流动党员党组织',
    parent: '-1',
  },
];
// 流入预警
export const TableColWarn2: any = [
  {
    key: '01',
    k0505: '党组织名称',
    parent: '-1',
    style: { width: 300 },
  },
  {
    key: '02',
    k0505: '正在流出总人数',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '未纳入支部管理',
    parent: '-1',
    children: [
      {
        key: '0301',
        k0505: '总计',
        parent: '03',
      },
      {
        key: '0302',
        k0505: '省内未纳入支部管理',
        parent: '03',
      },
      {
        key: '0303',
        k0505: '跨省未纳入支部管理',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '省内超20天未纳入支部管理',
    parent: '-1',
    children: [
      {
        key: '0401',
        k0505: '人数',
        parent: '04',
      },
      {
        key: '0402',
        k0505: '比例（%）',
        parent: '04',
      },
    ],
  },
  {
    key: '05',
    k0505: '跨省流动超30天未纳管',
    parent: '-1',
    children: [
      {
        key: '0501',
        k0505: '人数',
        parent: '05',
      },
      {
        key: '0502',
        k0505: '比例（%）',
        parent: '05',
      },
    ],
  },
  {
    key: '06',
    k0505: '未备案流动党员党组织',
    parent: '-1',
  },
];
// 当前流出
export const TableColCurFlowOut: any = [
  {
    key: '01',
    k0505: '项目 对象',
    parent: '-1',
    style: { width: 300 },
    halveHeadTd: {
      left: '项目',
      right: '对象',
      rotate: 65,
      lineHeight: 340,
    },
  },
  {
    key: '02',
    k0505: '流出人数',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '按流动类型分',
    parent: '-1',
    children: [
      {
        key: '0301',
        k0505: '流出省外',
        parent: '03',
        children: [
          {
            key: '030101',
            k0505: '已纳管',
            parent: '0301',
          },
          {
            key: '030102',
            k0505: '未纳管',
            parent: '0301',
          },
        ],
      },
      {
        key: '0302',
        k0505: '省内跨市',
        parent: '03',
        children: [
          {
            key: '030201',
            k0505: '已纳管',
            parent: '0302',
          },
          {
            key: '030202',
            k0505: '未纳管',
            parent: '0302',
          },
        ],
      },
      {
        key: '0303',
        k0505: '市内流动',
        parent: '03',
        children: [
          {
            key: '030301',
            k0505: '已纳管',
            parent: '0303',
          },
          {
            key: '030302',
            k0505: '未纳管',
            parent: '0303',
          },
        ],
      },
      {
        key: '0304',
        k0505: '无固定地点',
        parent: '03',
      },
      {
        key: '0305',
        k0505: '不掌握流向',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '按外出地点分',
    parent: '-1',
    children: [
      {
        key: '0401',
        k0505: '流向基层党工委',
        parent: '04',
        children: [
          {
            key: '040101',
            k0505: '已纳管',
            parent: '0401',
          },
          {
            key: '040102',
            k0505: '未纳管',
            parent: '0401',
          },
        ],
      },
      {
        key: '0402',
        k0505: '流向流动党员党组织',
        parent: '04',
        children: [
          {
            key: '040201',
            k0505: '已纳管',
            parent: '0402',
          },
          {
            key: '040202',
            k0505: '未纳管',
            parent: '0402',
          },
        ],
      },
      {
        key: '0403',
        k0505: '流向县级流入库',
        parent: '04',
        children: [
          {
            key: '040301',
            k0505: '已纳管',
            parent: '0403',
          },
          {
            key: '040302',
            k0505: '未纳管',
            parent: '0403',
          },
        ],
      },
      {
        key: '0404',
        k0505: '无固定地点',
        parent: '04',
      },
      {
        key: '0405',
        k0505: '不掌握流向',
        parent: '04',
      },
    ],
  },
  {
    key: '05',
    k0505: '未纳入支部管理',
    parent: '-1',
    children: [
      {
        key: '0501',
        k0505: '30天及以下',
        parent: '05',
        style: { borderBottom: 'none' },
        children: [
          {
            key: '050101',
            k0505: '',
            parent: '0501',
            style: { borderTop: 'none', visibility: 'hidden' },
          },
          {
            key: '050102',
            k0505: '在县级流入库中',
            parent: '0501',
          },
        ],
      },
      {
        key: '0502',
        k0505: '30天以上',
        parent: '05',
        style: { borderBottom: 'none' },
        children: [
          {
            key: '050201',
            k0505: '',
            parent: '0502',
            style: { borderTop: 'none', visibility: 'hidden' },
          },
          {
            key: '050202',
            k0505: '在县级流入库中',
            parent: '0502',
          },
        ],
      },
    ],
  },
  {
    key: '06',
    k0505: '当前退回人次',
    parent: '-1',
  },
  {
    key: '07',
    k0505: '',
    parent: '-1',
    children: [
      {
        key: '0701',
        k0505: '省内退回',
        parent: '05',
        // style: { borderBottom: 'none' },
        children: [
          {
            key: '070101',
            k0505: '主动退回',
            parent: '0701',
          },
          {
            key: '070102',
            k0505: '超时退回',
            parent: '0701',
          },
        ],
      },
      {
        key: '0702',
        k0505: '跨省退回',
        parent: '05',
        // style: { borderBottom: 'none' },
        children: [
          {
            key: '070201',
            k0505: '主动退回',
            parent: '0702',
          },
          {
            key: '070202',
            k0505: '超时退回',
            parent: '0702',
          },
        ],
      },
    ],
  },
  {
    key: '08',
    k0505: '当前流回人数',
    parent: '-1',
  },
  {
    key: '09',
    k0505: '',
    parent: '-1',
    children: [
      {
        key: '0901',
        k0505: '省内流回',
        parent: '-1',
      },
      {
        key: '0902',
        k0505: '跨省流回',
        parent: '-1',
      },
    ],
  },
  {
    key: '10',
    k0505: '农民工人数',
    parent: '-1',
  },
];
// 时段流出
export const TableColTimeFlowOut: any = [
  {
    key: '01',
    k0505: '对象 项目',
    parent: '-1',
    style: { width: 300 },
    halveHeadTd: {
      left: '对象',
      right: '项目',
      rotate: 64,
      lineHeight: 340,
    },
  },
  {
    key: '02',
    k0505: '流动情况',
    parent: '-1',
    children: [
      {
        key: '0201',
        k0505: '流出人次',
        parent: '02',
        children: [
          {
            key: '020101',
            k0505: '小计',
            parent: '0201',
          },
          {
            key: '020102',
            k0505: '流出省外',
            parent: '0201',
          },
          {
            key: '020103',
            k0505: '省内流动',
            parent: '0201',
          },
          {
            key: '020104',
            k0505: '无固定地点',
            parent: '0201',
          },
          {
            key: '020105',
            k0505: '不掌握流向',
            parent: '0201',
          },
        ],
      },
      {
        key: '0202',
        k0505: '流出纳管人次',
        parent: '02',
        children: [
          {
            key: '020201',
            k0505: '小计',
            parent: '0202',
          },
          {
            key: '020202',
            k0505: '流出省外',
            parent: '0202',
          },
          {
            key: '020203',
            k0505: '省内流动',
            parent: '0202',
          },
        ],
      },
      {
        key: '0203',
        k0505: '流回人次',
        parent: '02',
        children: [
          {
            key: '020301',
            k0505: '小计',
            parent: '0203',
          },
          {
            key: '020302',
            k0505: '省外流回',
            parent: '0203',
          },
          {
            key: '020303',
            k0505: '省内流回',
            parent: '0203',
          },
          {
            key: '020304',
            k0505: '无固定地点',
            parent: '0203',
          },
          {
            key: '020305',
            k0505: '不掌握流向',
            parent: '0203',
          },
        ],
      },
      {
        key: '0204',
        k0505: '在流入地主动报道人次',
        parent: '02',
        children: [
          {
            key: '020401',
            k0505: '小计',
            parent: '0204',
          },
          {
            key: '020402',
            k0505: '流入地为省外',
            parent: '0204',
          },
          {
            key: '020403',
            k0505: '流入地为省内',
            parent: '0204',
          },
        ],
      },
      {
        key: '0205',
        k0505: '对方退回人次',
        parent: '02',
        children: [
          {
            key: '020501',
            k0505: '小计',
            parent: '0205',
          },
          {
            key: '020502',
            k0505: '省外退回',
            parent: '0205',
          },
          {
            key: '020503',
            k0505: '省内退回',
            parent: '0205',
          },
        ],
      },
      {
        key: '0206',
        k0505: '超时退回人次',
        parent: '02',
        children: [
          {
            key: '020601',
            k0505: '小计',
            parent: '0206',
          },
          {
            key: '020602',
            k0505: '省外退回',
            parent: '0206',
          },
          {
            key: '020603',
            k0505: '省内退回',
            parent: '0206',
          },
        ],
      },
      {
        key: '0207',
        k0505: '流向县级流入库情况',
        parent: '02',
        children: [
          {
            key: '020701',
            k0505: '小计',
            parent: '0207',
          },
          {
            key: '020702',
            k0505: '纳管人次',
            parent: '0207',
          },
          {
            key: '020703',
            k0505: '对方退回人次',
            parent: '0207',
          },
          {
            key: '020704',
            k0505: '超时退回人次',
            parent: '0207',
          },
          {
            key: '020705',
            k0505: '主动撤销人次',
            parent: '0207',
          },
          {
            key: '020706',
            k0505: '调整流动去向人次',
            parent: '0207',
          },
        ],
      },
      {
        key: '0208',
        k0505: '主动撤销人次',
        parent: '02',
        children: [
          {
            key: '020801',
            k0505: '小计',
            parent: '0208',
          },
          {
            key: '020802',
            k0505: '流向省外撤销',
            parent: '0208',
          },
          {
            key: '020803',
            k0505: '流向省内撤销',
            parent: '0208',
          },
        ],
      },
      {
        key: '0209',
        k0505: '流出后被调整流动去向人次',
        parent: '02',
        children: [
          {
            key: '020901',
            k0505: '小计',
            parent: '0209',
          },
          {
            key: '020902',
            k0505: '流向省外',
            parent: '0209',
          },
          {
            key: '020903',
            k0505: '流向省内',
            parent: '0209',
          },
        ],
      },
    ],
  },
];
// 当前流入
export const TableColCurFlowIn: any = [
  {
    key: '01',
    k0505: '项目 对象',
    parent: '-1',
    style: { width: 300 },
    halveHeadTd: {
      left: '项目',
      right: '对象',
      rotate: 65,
      lineHeight: 340,
    },
  },
  {
    key: '02',
    k0505: '流入人数',
    parent: '-1',
  },
  {
    key: '03',
    k0505: '按流动类型分',
    parent: '-1',
    children: [
      {
        key: '0301',
        k0505: '省外流入',
        parent: '03',
        children: [
          {
            key: '030101',
            k0505: '已纳管',
            parent: '0301',
          },
          {
            key: '030102',
            k0505: '未纳管',
            parent: '0301',
          },
        ],
      },
      {
        key: '0302',
        k0505: '省内跨市',
        parent: '03',
        children: [
          {
            key: '030201',
            k0505: '已纳管',
            parent: '0302',
          },
          {
            key: '030202',
            k0505: '未纳管',
            parent: '0302',
          },
        ],
      },
      {
        key: '0303',
        k0505: '市内流动',
        parent: '03',
        children: [
          {
            key: '030301',
            k0505: '已纳管',
            parent: '0303',
          },
          {
            key: '030302',
            k0505: '未纳管',
            parent: '0303',
          },
        ],
      },
    ],
  },
  {
    key: '04',
    k0505: '按外出地点分',
    parent: '-1',
    children: [
      {
        key: '0401',
        k0505: '流向基层党（工）委',
        parent: '04',
        children: [
          {
            key: '040101',
            k0505: '已纳管',
            parent: '0401',
          },
          {
            key: '040102',
            k0505: '未纳管',
            parent: '0401',
          },
        ],
      },
      {
        key: '0402',
        k0505: '流向流动党员党组织',
        parent: '04',
        children: [
          {
            key: '040201',
            k0505: '已纳管',
            parent: '0402',
          },
          {
            key: '040202',
            k0505: '未纳管',
            parent: '0402',
          },
        ],
      },
      {
        key: '0403',
        k0505: '流向县级流入库',
        parent: '04',
        children: [
          {
            key: '040301',
            k0505: '已纳管',
            parent: '0403',
          },
          {
            key: '040302',
            k0505: '未纳管',
            parent: '0403',
          },
        ],
      },
    ],
  },
  {
    key: '05',
    k0505: '未纳入支部管理',
    parent: '-1',
    children: [
      {
        key: '0501',
        k0505: '30天及以下',
        parent: '05',
        style: { borderBottom: 'none' },
        children: [
          {
            key: '050101',
            k0505: '',
            parent: '0501',
            style: { borderTop: 'none', visibility: 'hidden' },
          },
          {
            key: '050102',
            k0505: '在县级流入库中',
            parent: '0501',
          },
        ],
      },
      {
        key: '0502',
        k0505: '30天以上',
        parent: '05',
        children: [
          {
            key: '050201',
            k0505: '',
            parent: '0502',
          },
          {
            key: '050202',
            k0505: '在县级流入库中',
            parent: '0502',
          },
        ],
      },
    ],
  },
  {
    key: '06',
    k0505: '人员类别',
    parent: '-1',
    children: [
      {
        key: '0601',
        k0505: '未填写人员类别人数',
        parent: '06'
      },
      {
        key: '0602',
        k0505: '已填写人员类别人数',
        parent: '06'
      },
      {
        key: '0603',
        k0505: '农民工人数',
        parent: '06'
      },
      {
        key: '0604',
        k0505: '离退休人员',
        parent: '06'
      },
      {
        key: '0605',
        k0505: '新就业群体人数',
        parent: '06',
        children: [
          { key: '060501', k0505: '快递员', parent: '0605' },
          { key: '060502', k0505: '外卖员', parent: '0605' },
          { key: '060503', k0505: '网约车司机', parent: '0605' },
          { key: '060504', k0505: '货车司机', parent: '0605' },
          { key: '060505', k0505: '网络主播', parent: '0605' },
          { key: '060506', k0505: '其他新就业群体', parent: '0605' }
        ]
      },
      {
        key: '0606',
        k0505: '未就业高校毕业生人数',
        parent: '06'
      },
      {
        key: '0607',
        k0505: '其他人员类别人数',
        parent: '06'
      }
    ]
  }
];
// 时段流入
export const TableColTimeFlowIn: any = [
  {
    key: '01',
    k0505: '对象 项目',
    parent: '-1',
    style: { width: 300 },
    halveHeadTd: {
      left: '对象',
      right: '项目',
      rotate: 61,
      lineHeight: 350,
    },
  },
  {
    key: '02',
    k0505: '流动情况',
    parent: '-1',
    children: [
      {
        key: '0201',
        k0505: '流入人次',
        parent: '02',
        children: [
          {
            key: '020101',
            k0505: '小计',
            parent: '0201',
          },
          {
            key: '020102',
            k0505: '省内流动',
            parent: '0201',
          },
          {
            key: '020103',
            k0505: '省外流入',
            parent: '0201',
          },
        ],
      },
      {
        key: '0202',
        k0505: '流入纳管人次',
        parent: '02',
        children: [
          {
            key: '020201',
            k0505: '小计',
            parent: '0202',
          },
          {
            key: '020202',
            k0505: '省内流动',
            parent: '0202',
          },
          {
            key: '020203',
            k0505: '省外流入',
            parent: '0202',
          },
        ],
      },
      {
        key: '0203',
        k0505: '流回人次',
        parent: '02',
        children: [
          {
            key: '020301',
            k0505: '小计',
            parent: '0203',
          },
          {
            key: '020302',
            k0505: '流回省内',
            parent: '0203',
          },
          {
            key: '020303',
            k0505: '流回省外',
            parent: '0203',
          },
        ],
      },
      {
        key: '0204',
        k0505: '主动退回人次',
        parent: '02',
        children: [
          {
            key: '020401',
            k0505: '小计',
            parent: '0204',
          },
          {
            key: '020402',
            k0505: '退回省内',
            parent: '0204',
          },
          {
            key: '020403',
            k0505: '退回省外',
            parent: '0204',
          },
        ],
      },
      {
        key: '0205',
        k0505: '超时退回人次',
        parent: '02',
        children: [
          {
            key: '020501',
            k0505: '小计',
            parent: '0205',
          },
          {
            key: '020502',
            k0505: '退回省内',
            parent: '0205',
          },
          {
            key: '020503',
            k0505: '退回省外',
            parent: '0205',
          },
        ],
      },
      {
        key: '0206',
        k0505: '县级流入库流入情况',
        parent: '02',
        children: [
          {
            key: '020601',
            k0505: '小计',
            parent: '0206',
          },
          {
            key: '020602',
            k0505: '纳管人次',
            parent: '0206',
          },
          {
            key: '020603',
            k0505: '主动退回人次',
            parent: '0206',
          },
          {
            key: '020604',
            k0505: '超时退回人次',
            parent: '0206',
          },
          {
            key: '020605',
            k0505: '对方撤销人次',
            parent: '0206',
          },
          {
            key: '020606',
            k0505: '调整流动去向人次',
            parent: '0206',
          },
        ],
      },
      {
        key: '0207',
        k0505: '对方主动撤销人次',
        parent: '02',
        children: [
          {
            key: '020701',
            k0505: '小计',
            parent: '0207',
          },
          {
            key: '020702',
            k0505: '省内撤销',
            parent: '0207',
          },
          {
            key: '020703',
            k0505: '省外撤销',
            parent: '0207',
          },
        ],
      },
      {
        key: '0208',
        k0505: '省内调整去向人次',
        parent: '02',
        children: [
          {
            key: '020801',
            k0505: '小计',
            parent: '0208',
          },
          {
            key: '020802',
            k0505: '省内流动',
            parent: '0208',
          },
          {
            key: '020803',
            k0505: '省外流入',
            parent: '0208',
          },
        ],
      },
      {
        key: '0209',
        k0505: '流入地建在当地的流动党员党组织人次',
        parent: '02',
      },
      {
        key: '0210',
        k0505: '流入外地建在本地的流动党员党组织人次',
        parent: '02',
      },
      {
        key: '0211',
        k0505: '流入本地建在外地的流动党员党组织人次',
        parent: '02',
      },
    ],
  },
];
// 流出去向1
export const TableColGoOut1: any = [
  {
    key: '01',
    k0505: '组织 外出地点',
    parent: '-1',
    style: { width: 300 },
    halveHeadTd: {
      left: '组织',
      right: '外出地点',
      rotate: 76,
      lineHeight: 300,
    },
  },
  {
    key: '02',
    k0505: '北京市',
    parent: '-1',
    children: [
      {
        key: '0201',
        k0505: '人数',
        parent: '02',
      },
      {
        key: '0202',
        k0505: '纳管率',
        parent: '02',
      },
    ],
  },
  {
    key: '03',
    k0505: '天津市',
    parent: '-1',
    children: [
      {
        key: '0301',
        k0505: '人数',
        parent: '03',
      },
      {
        key: '0302',
        k0505: '纳管率',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '河北省',
    parent: '-1',
    children: [
      {
        key: '0401',
        k0505: '人数',
        parent: '04',
      },
      {
        key: '0402',
        k0505: '纳管率',
        parent: '04',
      },
    ],
  },
  {
    key: '05',
    k0505: '山西省',
    parent: '-1',
    children: [
      {
        key: '0501',
        k0505: '人数',
        parent: '05',
      },
      {
        key: '0502',
        k0505: '纳管率',
        parent: '05',
      },
    ],
  },
  {
    key: '06',
    k0505: '内蒙古自治区',
    parent: '-1',
    children: [
      {
        key: '0601',
        k0505: '人数',
        parent: '06',
      },
      {
        key: '0602',
        k0505: '纳管率',
        parent: '06',
      },
    ],
  },
  {
    key: '07',
    k0505: '辽宁省',
    parent: '-1',
    children: [
      {
        key: '0701',
        k0505: '人数',
        parent: '07',
      },
      {
        key: '0702',
        k0505: '纳管率',
        parent: '07',
      },
    ],
  },
  {
    key: '08',
    k0505: '吉林省',
    parent: '-1',
    children: [
      {
        key: '0801',
        k0505: '人数',
        parent: '08',
      },
      {
        key: '0802',
        k0505: '纳管率',
        parent: '08',
      },
    ],
  },
  {
    key: '09',
    k0505: '黑龙江省',
    parent: '-1',
    children: [
      {
        key: '0901',
        k0505: '人数',
        parent: '09',
      },
      {
        key: '0902',
        k0505: '纳管率',
        parent: '09',
      },
    ],
  },
  {
    key: '10',
    k0505: '上海市',
    parent: '-1',
    children: [
      {
        key: '1001',
        k0505: '人数',
        parent: '10',
      },
      {
        key: '1002',
        k0505: '纳管率',
        parent: '10',
      },
    ],
  },
  {
    key: '11',
    k0505: '江苏省',
    parent: '-1',
    children: [
      {
        key: '1101',
        k0505: '人数',
        parent: '11',
      },
      {
        key: '1102',
        k0505: '纳管率',
        parent: '11',
      },
    ],
  },
  {
    key: '12',
    k0505: '浙江省',
    parent: '-1',
    children: [
      {
        key: '1201',
        k0505: '人数',
        parent: '12',
      },
      {
        key: '1202',
        k0505: '纳管率',
        parent: '12',
      },
    ],
  },
  {
    key: '13',
    k0505: '安徽省',
    parent: '-1',
    children: [
      {
        key: '1301',
        k0505: '人数',
        parent: '13',
      },
      {
        key: '1302',
        k0505: '纳管率',
        parent: '13',
      },
    ],
  },
  {
    key: '14',
    k0505: '福建省',
    parent: '-1',
    children: [
      {
        key: '1401',
        k0505: '人数',
        parent: '14',
      },
      {
        key: '1402',
        k0505: '纳管率',
        parent: '14',
      },
    ],
  },
  {
    key: '15',
    k0505: '江西省',
    parent: '-1',
    children: [
      {
        key: '1501',
        k0505: '人数',
        parent: '15',
      },
      {
        key: '1502',
        k0505: '纳管率',
        parent: '15',
      },
    ],
  },
  {
    key: '16',
    k0505: '山东省',
    parent: '-1',
    children: [
      {
        key: '1601',
        k0505: '人数',
        parent: '16',
      },
      {
        key: '1602',
        k0505: '纳管率',
        parent: '16',
      },
    ],
  },
  {
    key: '17',
    k0505: '河南省',
    parent: '-1',
    children: [
      {
        key: '1701',
        k0505: '人数',
        parent: '17',
      },
      {
        key: '1702',
        k0505: '纳管率',
        parent: '17',
      },
    ],
  },
  {
    key: '18',
    k0505: '湖北省',
    parent: '-1',
    children: [
      {
        key: '1801',
        k0505: '人数',
        parent: '18',
      },
      {
        key: '1802',
        k0505: '纳管率',
        parent: '18',
      },
    ],
  },
  {
    key: '19',
    k0505: '湖南省',
    parent: '-1',
    children: [
      {
        key: '1901',
        k0505: '人数',
        parent: '19',
      },
      {
        key: '1902',
        k0505: '纳管率',
        parent: '19',
      },
    ],
  },
  {
    key: '20',
    k0505: '广东省',
    parent: '-1',
    children: [
      {
        key: '2001',
        k0505: '人数',
        parent: '20',
      },
      {
        key: '2002',
        k0505: '纳管率',
        parent: '20',
      },
    ],
  },
  {
    key: '21',
    k0505: '广西壮族自治区',
    parent: '-1',
    children: [
      {
        key: '2101',
        k0505: '人数',
        parent: '21',
      },
      {
        key: '2102',
        k0505: '纳管率',
        parent: '21',
      },
    ],
  },
  {
    key: '22',
    k0505: '海南省',
    parent: '-1',
    children: [
      {
        key: '2201',
        k0505: '人数',
        parent: '22',
      },
      {
        key: '2202',
        k0505: '纳管率',
        parent: '22',
      },
    ],
  },
  {
    key: '23',
    k0505: '重庆市',
    parent: '-1',
    children: [
      {
        key: '2301',
        k0505: '人数',
        parent: '23',
      },
      {
        key: '2302',
        k0505: '纳管率',
        parent: '23',
      },
    ],
  },
  {
    key: '24',
    k0505: '四川省',
    parent: '-1',
    children: [
      {
        key: '2401',
        k0505: '人数',
        parent: '24',
      },
      {
        key: '2402',
        k0505: '纳管率',
        parent: '24',
      },
    ],
  },
  {
    key: '25',
    k0505: '贵州省',
    parent: '-1',
    children: [
      {
        key: '2501',
        k0505: '人数',
        parent: '25',
      },
      {
        key: '2502',
        k0505: '纳管率',
        parent: '25',
      },
    ],
  },
  {
    key: '26',
    k0505: '云南省',
    parent: '-1',
    children: [
      {
        key: '2601',
        k0505: '人数',
        parent: '26',
      },
      {
        key: '2602',
        k0505: '纳管率',
        parent: '26',
      },
    ],
  },
  {
    key: '27',
    k0505: '西藏自治区',
    parent: '-1',
    children: [
      {
        key: '2701',
        k0505: '人数',
        parent: '27',
      },
      {
        key: '2702',
        k0505: '纳管率',
        parent: '27',
      },
    ],
  },
  {
    key: '28',
    k0505: '陕西省',
    parent: '-1',
    children: [
      {
        key: '2801',
        k0505: '人数',
        parent: '28',
      },
      {
        key: '2802',
        k0505: '纳管率',
        parent: '28',
      },
    ],
  },
  {
    key: '29',
    k0505: '甘肃省',
    parent: '-1',
    children: [
      {
        key: '2901',
        k0505: '人数',
        parent: '29',
      },
      {
        key: '2902',
        k0505: '纳管率',
        parent: '29',
      },
    ],
  },
  {
    key: '30',
    k0505: '青海省',
    parent: '-1',
    children: [
      {
        key: '3001',
        k0505: '人数',
        parent: '30',
      },
      {
        key: '3002',
        k0505: '纳管率',
        parent: '30',
      },
    ],
  },
  {
    key: '31',
    k0505: '宁夏回族自治区',
    parent: '-1',
    children: [
      {
        key: '3101',
        k0505: '人数',
        parent: '31',
      },
      {
        key: '3102',
        k0505: '纳管率',
        parent: '31',
      },
    ],
  },
  {
    key: '32',
    k0505: '新疆维吾尔自治区',
    parent: '-1',
    children: [
      {
        key: '3201',
        k0505: '人数',
        parent: '32',
      },
      {
        key: '3202',
        k0505: '纳管率',
        parent: '32',
      },
    ],
  },
  {
    key: '33',
    k0505: '台湾省',
    parent: '-1',
    children: [
      {
        key: '3301',
        k0505: '人数',
        parent: '33',
      },
      {
        key: '3302',
        k0505: '纳管率',
        parent: '33',
      },
    ],
  },
  {
    key: '34',
    k0505: '香港特别行政区',
    parent: '-1',
    children: [
      {
        key: '3401',
        k0505: '人数',
        parent: '34',
      },
      {
        key: '3402',
        k0505: '纳管率',
        parent: '34',
      },
    ],
  },
  {
    key: '35',
    k0505: '澳门特别行政区',
    parent: '-1',
    children: [
      {
        key: '3501',
        k0505: '人数',
        parent: '35',
      },
      {
        key: '3502',
        k0505: '纳管率',
        parent: '35',
      },
    ],
  },
  {
    key: '36',
    k0505: '其他',
    parent: '-1',
    children: [
      {
        key: '3601',
        k0505: '人数',
        parent: '36',
      },
      {
        key: '3602',
        k0505: '纳管率',
        parent: '36',
      },
    ],
  },
  {
    key: '07',
    k0505: '无固定地点',
    parent: '-1',
  },
  {
    key: '08',
    k0505: '不掌握流向',
    parent: '-1',
  },
];
// 流出去向2
export const TableColGoOut2: any = [
  {
    key: '01',
    k0505: '外出地点',
    parent: '-1',
    style: { width: 300 },
    halveHeadTd: {
      left: '',
      right: '外出地点',
      rotate: 68,
      lineHeight: 330,
    },
  },
  {
    key: '02',
    k0505: '北京市',
    parent: '-1',
    children: [
      {
        key: '0201',
        k0505: '纳管人数',
        parent: '02',
      },
    ],
  },
  {
    key: '03',
    k0505: '天津市',
    parent: '-1',
    children: [
      {
        key: '0301',
        k0505: '纳管人数',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '河北省',
    parent: '-1',
    children: [
      {
        key: '0401',
        k0505: '纳管人数',
        parent: '04',
      },
    ],
  },
  {
    key: '05',
    k0505: '山西省',
    parent: '-1',
    children: [
      {
        key: '0501',
        k0505: '纳管人数',
        parent: '05',
      },
    ],
  },
  {
    key: '06',
    k0505: '内蒙古自治区',
    parent: '-1',
    children: [
      {
        key: '0601',
        k0505: '纳管人数',
        parent: '06',
      },
    ],
  },
  {
    key: '07',
    k0505: '辽宁省',
    parent: '-1',
    children: [
      {
        key: '0701',
        k0505: '纳管人数',
        parent: '07',
      },
    ],
  },
  {
    key: '08',
    k0505: '吉林省',
    parent: '-1',
    children: [
      {
        key: '0801',
        k0505: '纳管人数',
        parent: '08',
      },
    ],
  },
  {
    key: '09',
    k0505: '黑龙江省',
    parent: '-1',
    children: [
      {
        key: '0901',
        k0505: '纳管人数',
        parent: '09',
      },
    ],
  },
  {
    key: '10',
    k0505: '上海市',
    parent: '-1',
    children: [
      {
        key: '1001',
        k0505: '纳管人数',
        parent: '10',
      },
    ],
  },
  {
    key: '11',
    k0505: '江苏省',
    parent: '-1',
    children: [
      {
        key: '1101',
        k0505: '纳管人数',
        parent: '11',
      },
    ],
  },
  {
    key: '12',
    k0505: '浙江省',
    parent: '-1',
    children: [
      {
        key: '1201',
        k0505: '纳管人数',
        parent: '12',
      },
    ],
  },
  {
    key: '13',
    k0505: '安徽省',
    parent: '-1',
    children: [
      {
        key: '1301',
        k0505: '纳管人数',
        parent: '13',
      },
    ],
  },
  {
    key: '14',
    k0505: '福建省',
    parent: '-1',
    children: [
      {
        key: '1401',
        k0505: '纳管人数',
        parent: '14',
      },
    ],
  },
  {
    key: '15',
    k0505: '江西省',
    parent: '-1',
    children: [
      {
        key: '1501',
        k0505: '纳管人数',
        parent: '15',
      },
    ],
  },
  {
    key: '16',
    k0505: '山东省',
    parent: '-1',
    children: [
      {
        key: '1601',
        k0505: '纳管人数',
        parent: '16',
      },
    ],
  },
  {
    key: '17',
    k0505: '河南省',
    parent: '-1',
    children: [
      {
        key: '1701',
        k0505: '纳管人数',
        parent: '17',
      },
    ],
  },
  {
    key: '18',
    k0505: '湖北省',
    parent: '-1',
    children: [
      {
        key: '1801',
        k0505: '纳管人数',
        parent: '18',
      },
    ],
  },
  {
    key: '19',
    k0505: '湖南省',
    parent: '-1',
    children: [
      {
        key: '1901',
        k0505: '纳管人数',
        parent: '19',
      },
    ],
  },
  {
    key: '20',
    k0505: '广东省',
    parent: '-1',
    children: [
      {
        key: '2001',
        k0505: '纳管人数',
        parent: '20',
      },
    ],
  },
  {
    key: '21',
    k0505: '广西壮族自治区',
    parent: '-1',
    children: [
      {
        key: '2101',
        k0505: '纳管人数',
        parent: '21',
      },
    ],
  },
  {
    key: '22',
    k0505: '海南省',
    parent: '-1',
    children: [
      {
        key: '2201',
        k0505: '纳管人数',
        parent: '22',
      },
    ],
  },
  {
    key: '23',
    k0505: '重庆市',
    parent: '-1',
    children: [
      {
        key: '2301',
        k0505: '纳管人数',
        parent: '23',
      },
    ],
  },
  {
    key: '24',
    k0505: '四川省',
    parent: '-1',
    children: [
      {
        key: '2401',
        k0505: '纳管人数',
        parent: '24',
      },
    ],
  },
  {
    key: '25',
    k0505: '贵州省',
    parent: '-1',
    children: [
      {
        key: '2501',
        k0505: '纳管人数',
        parent: '25',
      },
    ],
  },
  {
    key: '26',
    k0505: '云南省',
    parent: '-1',
    children: [
      {
        key: '2601',
        k0505: '纳管人数',
        parent: '26',
      },
    ],
  },
  {
    key: '27',
    k0505: '西藏自治区',
    parent: '-1',
    children: [
      {
        key: '2701',
        k0505: '纳管人数',
        parent: '27',
      },
    ],
  },
  {
    key: '28',
    k0505: '陕西省',
    parent: '-1',
    children: [
      {
        key: '2801',
        k0505: '纳管人数',
        parent: '28',
      },
    ],
  },
  {
    key: '29',
    k0505: '甘肃省',
    parent: '-1',
    children: [
      {
        key: '2901',
        k0505: '纳管人数',
        parent: '29',
      },
    ],
  },
  {
    key: '30',
    k0505: '青海省',
    parent: '-1',
    children: [
      {
        key: '3001',
        k0505: '纳管人数',
        parent: '30',
      },
    ],
  },
  {
    key: '31',
    k0505: '宁夏回族自治区',
    parent: '-1',
    children: [
      {
        key: '3101',
        k0505: '纳管人数',
        parent: '31',
      },
    ],
  },
  {
    key: '32',
    k0505: '新疆维吾尔自治区',
    parent: '-1',
    children: [
      {
        key: '3201',
        k0505: '纳管人数',
        parent: '32',
      },
    ],
  },
  {
    key: '33',
    k0505: '台湾省',
    parent: '-1',
    children: [
      {
        key: '3301',
        k0505: '纳管人数',
        parent: '33',
      },
    ],
  },
  {
    key: '34',
    k0505: '香港特别行政区',
    parent: '-1',
    children: [
      {
        key: '3401',
        k0505: '纳管人数',
        parent: '34',
      },
    ],
  },
  {
    key: '35',
    k0505: '澳门特别行政区',
    parent: '-1',
    children: [
      {
        key: '3501',
        k0505: '纳管人数',
        parent: '35',
      },
    ],
  },
  {
    key: '06',
    k0505: '无固定地点',
    parent: '-1',
  },
  {
    key: '07',
    k0505: '不掌握流向',
    parent: '-1',
  },
  {
    key: '36',
    k0505: '其他',
    parent: '-1',
    children: [
      {
        key: '3601',
        k0505: '纳管人数',
        parent: '36',
      },
    ],
  },
];
// 流入来源1
export const TableColGoIn1: any = [
  {
    key: '01',
    k0505: '组织 流出地',
    parent: '-1',
    style: { width: 300 },
    halveHeadTd: {
      left: '组织',
      right: '流出地',
      rotate: 76,
      lineHeight: 300,
    },
  },
  {
    key: '02',
    k0505: '北京市',
    parent: '-1',
    children: [
      {
        key: '0201',
        k0505: '人数',
        parent: '02',
      },
      {
        key: '0202',
        k0505: '纳管率',
        parent: '02',
      },
    ],
  },
  {
    key: '03',
    k0505: '天津市',
    parent: '-1',
    children: [
      {
        key: '0301',
        k0505: '人数',
        parent: '03',
      },
      {
        key: '0302',
        k0505: '纳管率',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '河北省',
    parent: '-1',
    children: [
      {
        key: '0401',
        k0505: '人数',
        parent: '04',
      },
      {
        key: '0402',
        k0505: '纳管率',
        parent: '04',
      },
    ],
  },
  {
    key: '05',
    k0505: '山西省',
    parent: '-1',
    children: [
      {
        key: '0501',
        k0505: '人数',
        parent: '05',
      },
      {
        key: '0502',
        k0505: '纳管率',
        parent: '05',
      },
    ],
  },
  {
    key: '06',
    k0505: '内蒙古自治区',
    parent: '-1',
    children: [
      {
        key: '0601',
        k0505: '人数',
        parent: '06',
      },
      {
        key: '0602',
        k0505: '纳管率',
        parent: '06',
      },
    ],
  },
  {
    key: '07',
    k0505: '辽宁省',
    parent: '-1',
    children: [
      {
        key: '0701',
        k0505: '人数',
        parent: '07',
      },
      {
        key: '0702',
        k0505: '纳管率',
        parent: '07',
      },
    ],
  },
  {
    key: '08',
    k0505: '吉林省',
    parent: '-1',
    children: [
      {
        key: '0801',
        k0505: '人数',
        parent: '08',
      },
      {
        key: '0802',
        k0505: '纳管率',
        parent: '08',
      },
    ],
  },
  {
    key: '09',
    k0505: '黑龙江省',
    parent: '-1',
    children: [
      {
        key: '0901',
        k0505: '人数',
        parent: '09',
      },
      {
        key: '0902',
        k0505: '纳管率',
        parent: '09',
      },
    ],
  },
  {
    key: '10',
    k0505: '上海市',
    parent: '-1',
    children: [
      {
        key: '1001',
        k0505: '人数',
        parent: '10',
      },
      {
        key: '1002',
        k0505: '纳管率',
        parent: '10',
      },
    ],
  },
  {
    key: '11',
    k0505: '江苏省',
    parent: '-1',
    children: [
      {
        key: '1101',
        k0505: '人数',
        parent: '11',
      },
      {
        key: '1102',
        k0505: '纳管率',
        parent: '11',
      },
    ],
  },
  {
    key: '12',
    k0505: '浙江省',
    parent: '-1',
    children: [
      {
        key: '1201',
        k0505: '人数',
        parent: '12',
      },
      {
        key: '1202',
        k0505: '纳管率',
        parent: '12',
      },
    ],
  },
  {
    key: '13',
    k0505: '安徽省',
    parent: '-1',
    children: [
      {
        key: '1301',
        k0505: '人数',
        parent: '13',
      },
      {
        key: '1302',
        k0505: '纳管率',
        parent: '13',
      },
    ],
  },
  {
    key: '14',
    k0505: '福建省',
    parent: '-1',
    children: [
      {
        key: '1401',
        k0505: '人数',
        parent: '14',
      },
      {
        key: '1402',
        k0505: '纳管率',
        parent: '14',
      },
    ],
  },
  {
    key: '15',
    k0505: '江西省',
    parent: '-1',
    children: [
      {
        key: '1501',
        k0505: '人数',
        parent: '15',
      },
      {
        key: '1502',
        k0505: '纳管率',
        parent: '15',
      },
    ],
  },
  {
    key: '16',
    k0505: '山东省',
    parent: '-1',
    children: [
      {
        key: '1601',
        k0505: '人数',
        parent: '16',
      },
      {
        key: '1602',
        k0505: '纳管率',
        parent: '16',
      },
    ],
  },
  {
    key: '17',
    k0505: '河南省',
    parent: '-1',
    children: [
      {
        key: '1701',
        k0505: '人数',
        parent: '17',
      },
      {
        key: '1702',
        k0505: '纳管率',
        parent: '17',
      },
    ],
  },
  {
    key: '18',
    k0505: '湖北省',
    parent: '-1',
    children: [
      {
        key: '1801',
        k0505: '人数',
        parent: '18',
      },
      {
        key: '1802',
        k0505: '纳管率',
        parent: '18',
      },
    ],
  },
  {
    key: '19',
    k0505: '湖南省',
    parent: '-1',
    children: [
      {
        key: '1901',
        k0505: '人数',
        parent: '19',
      },
      {
        key: '1902',
        k0505: '纳管率',
        parent: '19',
      },
    ],
  },
  {
    key: '20',
    k0505: '广东省',
    parent: '-1',
    children: [
      {
        key: '2001',
        k0505: '人数',
        parent: '20',
      },
      {
        key: '2002',
        k0505: '纳管率',
        parent: '20',
      },
    ],
  },
  {
    key: '21',
    k0505: '广西壮族自治区',
    parent: '-1',
    children: [
      {
        key: '2101',
        k0505: '人数',
        parent: '21',
      },
      {
        key: '2102',
        k0505: '纳管率',
        parent: '21',
      },
    ],
  },
  {
    key: '22',
    k0505: '海南省',
    parent: '-1',
    children: [
      {
        key: '2201',
        k0505: '人数',
        parent: '22',
      },
      {
        key: '2202',
        k0505: '纳管率',
        parent: '22',
      },
    ],
  },
  {
    key: '23',
    k0505: '重庆市',
    parent: '-1',
    children: [
      {
        key: '2301',
        k0505: '人数',
        parent: '23',
      },
      {
        key: '2302',
        k0505: '纳管率',
        parent: '23',
      },
    ],
  },
  {
    key: '24',
    k0505: '四川省',
    parent: '-1',
    children: [
      {
        key: '2401',
        k0505: '人数',
        parent: '24',
      },
      {
        key: '2402',
        k0505: '纳管率',
        parent: '24',
      },
    ],
  },
  {
    key: '25',
    k0505: '贵州省',
    parent: '-1',
    children: [
      {
        key: '2501',
        k0505: '人数',
        parent: '25',
      },
      {
        key: '2502',
        k0505: '纳管率',
        parent: '25',
      },
    ],
  },
  {
    key: '26',
    k0505: '云南省',
    parent: '-1',
    children: [
      {
        key: '2601',
        k0505: '人数',
        parent: '26',
      },
      {
        key: '2602',
        k0505: '纳管率',
        parent: '26',
      },
    ],
  },
  {
    key: '27',
    k0505: '西藏自治区',
    parent: '-1',
    children: [
      {
        key: '2701',
        k0505: '人数',
        parent: '27',
      },
      {
        key: '2702',
        k0505: '纳管率',
        parent: '27',
      },
    ],
  },
  {
    key: '28',
    k0505: '陕西省',
    parent: '-1',
    children: [
      {
        key: '2801',
        k0505: '人数',
        parent: '28',
      },
      {
        key: '2802',
        k0505: '纳管率',
        parent: '28',
      },
    ],
  },
  {
    key: '29',
    k0505: '甘肃省',
    parent: '-1',
    children: [
      {
        key: '2901',
        k0505: '人数',
        parent: '29',
      },
      {
        key: '2902',
        k0505: '纳管率',
        parent: '29',
      },
    ],
  },
  {
    key: '30',
    k0505: '青海省',
    parent: '-1',
    children: [
      {
        key: '3001',
        k0505: '人数',
        parent: '30',
      },
      {
        key: '3002',
        k0505: '纳管率',
        parent: '30',
      },
    ],
  },
  {
    key: '31',
    k0505: '宁夏回族自治区',
    parent: '-1',
    children: [
      {
        key: '3101',
        k0505: '人数',
        parent: '31',
      },
      {
        key: '3102',
        k0505: '纳管率',
        parent: '31',
      },
    ],
  },
  {
    key: '32',
    k0505: '新疆维吾尔自治区',
    parent: '-1',
    children: [
      {
        key: '3201',
        k0505: '人数',
        parent: '32',
      },
      {
        key: '3202',
        k0505: '纳管率',
        parent: '32',
      },
    ],
  },
  {
    key: '33',
    k0505: '台湾省',
    parent: '-1',
    children: [
      {
        key: '3301',
        k0505: '人数',
        parent: '33',
      },
      {
        key: '3302',
        k0505: '纳管率',
        parent: '33',
      },
    ],
  },
  {
    key: '34',
    k0505: '香港特别行政区',
    parent: '-1',
    children: [
      {
        key: '3401',
        k0505: '人数',
        parent: '34',
      },
      {
        key: '3402',
        k0505: '纳管率',
        parent: '34',
      },
    ],
  },
  {
    key: '35',
    k0505: '澳门特别行政区',
    parent: '-1',
    children: [
      {
        key: '3501',
        k0505: '人数',
        parent: '35',
      },
      {
        key: '3502',
        k0505: '纳管率',
        parent: '35',
      },
    ],
  },
  {
    key: '36',
    k0505: '其他',
    parent: '-1',
    children: [
      {
        key: '3601',
        k0505: '人数',
        parent: '36',
      },
      {
        key: '3602',
        k0505: '纳管率',
        parent: '36',
      },
    ],
  },
  // {
  //   key: '07',
  //   k0505: '无固定地点',
  //   parent: '-1',
  // },
  // {
  //   key: '08',
  //   k0505: '不掌握流向',
  //   parent: '-1',
  // },
];
// 流入来源2
export const TableColGoIn2: any = [
  {
    key: '01',
    k0505: '流出地',
    parent: '-1',
    style: { width: 300 },
    halveHeadTd: {
      left: '',
      right: '流出地',
      rotate: 68,
      lineHeight: 330,
    },
  },
  {
    key: '02',
    k0505: '北京市',
    parent: '-1',
    children: [
      {
        key: '0201',
        k0505: '纳管人数',
        parent: '02',
      },
    ],
  },
  {
    key: '03',
    k0505: '天津市',
    parent: '-1',
    children: [
      {
        key: '0301',
        k0505: '纳管人数',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '河北省',
    parent: '-1',
    children: [
      {
        key: '0401',
        k0505: '纳管人数',
        parent: '04',
      },
    ],
  },
  {
    key: '05',
    k0505: '山西省',
    parent: '-1',
    children: [
      {
        key: '0501',
        k0505: '纳管人数',
        parent: '05',
      },
    ],
  },
  {
    key: '06',
    k0505: '内蒙古自治区',
    parent: '-1',
    children: [
      {
        key: '0601',
        k0505: '纳管人数',
        parent: '06',
      },
    ],
  },
  {
    key: '07',
    k0505: '辽宁省',
    parent: '-1',
    children: [
      {
        key: '0701',
        k0505: '纳管人数',
        parent: '07',
      },
    ],
  },
  {
    key: '08',
    k0505: '吉林省',
    parent: '-1',
    children: [
      {
        key: '0801',
        k0505: '纳管人数',
        parent: '08',
      },
    ],
  },
  {
    key: '09',
    k0505: '黑龙江省',
    parent: '-1',
    children: [
      {
        key: '0901',
        k0505: '纳管人数',
        parent: '09',
      },
    ],
  },
  {
    key: '10',
    k0505: '上海市',
    parent: '-1',
    children: [
      {
        key: '1001',
        k0505: '纳管人数',
        parent: '10',
      },
    ],
  },
  {
    key: '11',
    k0505: '江苏省',
    parent: '-1',
    children: [
      {
        key: '1101',
        k0505: '纳管人数',
        parent: '11',
      },
    ],
  },
  {
    key: '12',
    k0505: '浙江省',
    parent: '-1',
    children: [
      {
        key: '1201',
        k0505: '纳管人数',
        parent: '12',
      },
    ],
  },
  {
    key: '13',
    k0505: '安徽省',
    parent: '-1',
    children: [
      {
        key: '1301',
        k0505: '纳管人数',
        parent: '13',
      },
    ],
  },
  {
    key: '14',
    k0505: '福建省',
    parent: '-1',
    children: [
      {
        key: '1401',
        k0505: '纳管人数',
        parent: '14',
      },
    ],
  },
  {
    key: '15',
    k0505: '江西省',
    parent: '-1',
    children: [
      {
        key: '1501',
        k0505: '纳管人数',
        parent: '15',
      },
    ],
  },
  {
    key: '16',
    k0505: '山东省',
    parent: '-1',
    children: [
      {
        key: '1601',
        k0505: '纳管人数',
        parent: '16',
      },
    ],
  },
  {
    key: '17',
    k0505: '河南省',
    parent: '-1',
    children: [
      {
        key: '1701',
        k0505: '纳管人数',
        parent: '17',
      },
    ],
  },
  {
    key: '18',
    k0505: '湖北省',
    parent: '-1',
    children: [
      {
        key: '1801',
        k0505: '纳管人数',
        parent: '18',
      },
    ],
  },
  {
    key: '19',
    k0505: '湖南省',
    parent: '-1',
    children: [
      {
        key: '1901',
        k0505: '纳管人数',
        parent: '19',
      },
    ],
  },
  {
    key: '20',
    k0505: '广东省',
    parent: '-1',
    children: [
      {
        key: '2001',
        k0505: '纳管人数',
        parent: '20',
      },
    ],
  },
  {
    key: '21',
    k0505: '广西壮族自治区',
    parent: '-1',
    children: [
      {
        key: '2101',
        k0505: '纳管人数',
        parent: '21',
      },
    ],
  },
  {
    key: '22',
    k0505: '海南省',
    parent: '-1',
    children: [
      {
        key: '2201',
        k0505: '纳管人数',
        parent: '22',
      },
    ],
  },
  {
    key: '23',
    k0505: '重庆市',
    parent: '-1',
    children: [
      {
        key: '2301',
        k0505: '纳管人数',
        parent: '23',
      },
    ],
  },
  {
    key: '24',
    k0505: '四川省',
    parent: '-1',
    children: [
      {
        key: '2401',
        k0505: '纳管人数',
        parent: '24',
      },
    ],
  },
  {
    key: '25',
    k0505: '贵州省',
    parent: '-1',
    children: [
      {
        key: '2501',
        k0505: '纳管人数',
        parent: '25',
      },
    ],
  },
  {
    key: '26',
    k0505: '云南省',
    parent: '-1',
    children: [
      {
        key: '2601',
        k0505: '纳管人数',
        parent: '26',
      },
    ],
  },
  {
    key: '27',
    k0505: '西藏自治区',
    parent: '-1',
    children: [
      {
        key: '2701',
        k0505: '纳管人数',
        parent: '27',
      },
    ],
  },
  {
    key: '28',
    k0505: '陕西省',
    parent: '-1',
    children: [
      {
        key: '2801',
        k0505: '纳管人数',
        parent: '28',
      },
    ],
  },
  {
    key: '29',
    k0505: '甘肃省',
    parent: '-1',
    children: [
      {
        key: '2901',
        k0505: '纳管人数',
        parent: '29',
      },
    ],
  },
  {
    key: '30',
    k0505: '青海省',
    parent: '-1',
    children: [
      {
        key: '3001',
        k0505: '纳管人数',
        parent: '30',
      },
    ],
  },
  {
    key: '31',
    k0505: '宁夏回族自治区',
    parent: '-1',
    children: [
      {
        key: '3101',
        k0505: '纳管人数',
        parent: '31',
      },
    ],
  },
  {
    key: '32',
    k0505: '新疆维吾尔自治区',
    parent: '-1',
    children: [
      {
        key: '3201',
        k0505: '纳管人数',
        parent: '32',
      },
    ],
  },
  {
    key: '33',
    k0505: '台湾省',
    parent: '-1',
    children: [
      {
        key: '3301',
        k0505: '纳管人数',
        parent: '33',
      },
    ],
  },
  {
    key: '34',
    k0505: '香港特别行政区',
    parent: '-1',
    children: [
      {
        key: '3401',
        k0505: '纳管人数',
        parent: '34',
      },
    ],
  },
  {
    key: '35',
    k0505: '澳门特别行政区',
    parent: '-1',
    children: [
      {
        key: '3501',
        k0505: '纳管人数',
        parent: '35',
      },
    ],
  },
  // {
  //   key: '06',
  //   k0505: '无固定地点',
  //   parent: '-1',
  // },
  // {
  //   key: '07',
  //   k0505: '不掌握流向',
  //   parent: '-1',
  // },
  {
    key: '36',
    k0505: '其他',
    parent: '-1',
    children: [
      {
        key: '3601',
        k0505: '纳管人数',
        parent: '36',
      },
    ],
  },
];

// 接收进展情况
export const TableColProgressReception: any = [
  {
    key: '01',
    k0505: '省份',
    parent: '-1',
    style: { width: 300 },
  },
  {
    key: '02',
    k0505: '省内流动',
    parent: '-1',
    children: [
      {
        key: '0201',
        k0505: '已作登记的省内流动党员数',
        parent: '02',
        style: { borderBottom: 'none' },
        children: [
          {
            key: '020101',
            k0505: '已纳管的',
            parent: '0201',
            style: { borderTop: 'none', visibility: 'hidden' },
          },
          {
            key: '020102',
            k0505: '已纳管的',
            parent: '0201',
          },
        ],
      },
      {
        key: '0202',
        k0505: '集中登记后超时退回的',
        parent: '02',
      },
      {
        key: '0203',
        k0505: '集中登记后对方主动退回的',
        parent: '02',
      },
    ],
  },
  {
    key: '03',
    k0505: '跨省流动',
    parent: '-1',
    children: [
      {
        key: '0301',
        k0505: '已作登记的跨省流动党员数',
        parent: '03',
        style: { borderBottom: 'none' },
        children: [
          {
            key: '030101',
            k0505: '流入地（单位）基层党组织纳管的',
            parent: '0301',
            style: { borderTop: 'none', visibility: 'hidden' },
          },
          {
            key: '030102',
            k0505: '流入地（单位）基层党组织纳管的',
            parent: '0301',
          },
          {
            key: '030103',
            k0505: '流动党员党组织纳管的',
            parent: '0301',
          },
        ],
      },
      {
        key: '0302',
        k0505: '集中登记后超时退回的',
        parent: '03',
      },
      {
        key: '0303',
        k0505: '集中登记后对方主动退回的',
        parent: '03',
      },
    ],
  },
  {
    key: '04',
    k0505: '本省县级流入库中未纳管流动党员数',
    parent: '-1',
  },
];

export const config = [
  {
    reportCode: '1',
    name: '党组织设置情况督查表1',
    head: TableCol,
    width: fakeLine(TableCol) * 54 + 300,
    nodeWith: 54,
    tableAction: superviseTable,
    // callBacktableType: 'org',
    callBackAction: peggingWorkSupervision,
  },
  {
    reportCode: '2',
    name: '党组织设置情况督查表2',
    head: TableColNew,
    width: fakeLine(TableColNew) * 54 + 300,
    nodeWith: 54,
    tableAction: workSupervisionNew,
    // callBacktableType: 'org',
    callBackAction: peggingWorkSupervisionNew,
  },
  {
    reportCode: '3',
    name: '基层党组织落实党的组织生活制度情况督查表',
    head: TableColNew2,
    width: fakeLine(TableColNew2) * 80 + 300,
    nodeWith: 80,
    tableAction: grassRootsOrgLifeExcel,
    // callBacktableType: 'org',
    callBackAction: peggingWorkSupervisionNew,
  },
  {
    reportCode: '4',
    name: '基层党组织发展党员工作情况督查表',
    head: TableColNew3,
    width: fakeLine(TableColNew3) * 54 + 300,
    nodeWith: 54,
    tableAction: reportNewExcel,
    // callBacktableType: 'dev',
    callBackAction: peggingExcelNew,
  },
  // 2025.02.25新增表 ↓
  {
    reportCode: 'N',
    name: '流动党员-流出预警',
    head: TableColWarn1,
    width: fakeLine(TableColWarn1) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
  {
    reportCode: 'O',
    name: '流动党员-流入预警',
    head: TableColWarn2,
    width: fakeLine(TableColWarn2) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
  {
    reportCode: 'A',
    name: '流动党员-当前流出',
    head: TableColCurFlowOut,
    width: fakeLine(TableColCurFlowOut) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
  {
    reportCode: 'B',
    name: '流动党员-时段流出',
    head: TableColTimeFlowOut,
    width: fakeLine(TableColTimeFlowOut) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
  {
    reportCode: 'C',
    name: '流动党员-当前流入',
    head: TableColCurFlowIn,
    width: fakeLine(TableColCurFlowIn) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
  {
    reportCode: 'D',
    name: '流动党员-时段流入',
    head: TableColTimeFlowIn,
    width: fakeLine(TableColTimeFlowIn) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
  {
    reportCode: 'E',
    name: '流动党员-流出去向',
    head: TableColGoOut1,
    width: fakeLine(TableColGoOut1) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
  {
    reportCode: 'G',
    name: '流动党员-流出去向-纳管人数',
    head: TableColGoOut2,
    width: fakeLine(TableColGoOut2) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
  {
    reportCode: 'F',
    name: '流动党员-流入来源',
    head: TableColGoIn1,
    width: fakeLine(TableColGoIn1) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
  {
    reportCode: 'J',
    name: '流动党员-流入来源-纳管人数',
    head: TableColGoIn2,
    width: fakeLine(TableColGoIn2) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
  // 2025.03.05新增表 ↑
  {
    reportCode: 'Z', //后端暂未定表码
    name: '流动党员-流动党员登记-接收进展情況',
    head: TableColProgressReception,
    width: fakeLine(TableColProgressReception) * 54 + 300,
    nodeWith: 54,
    tableAction: memFlowInspectionForm,
    // callBacktableType: 'dev',
    callBackAction: FlowExcelNew,
  },
];
