import React, { Fragment, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Col, Form, Input, Modal, Alert, Button, Popconfirm, Steps, Upload } from 'antd';
import Tip from '@/components/Tip';
import UploadComp, { fitFileUrlForForm, getInitFileList } from '@/components/UploadComp';
import _isEmpty from 'lodash/isEmpty';
import st from './index.less';
import { createWorker } from 'tesseract.js';
import Cropper from 'react-cropper';
import 'cropperjs/dist/cropper.css';
import _debounce from 'lodash/debounce';
import _last from 'lodash/last';
import fetch from 'dva/fetch';
import { archivesSave, archivesFind, pullFile, getImgUrl } from '@/services';
import { PlusOutlined } from '@ant-design/icons';
import _tr from 'lodash/trim';
import _replace from 'lodash/replace';
import { compressAccurately } from 'image-conversion'; // 压缩图片插件

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const upProps = {
  // multiple: true,
  // showUploadList: false,
  name: 'file',
  action: `/api/base/putFile`,
  accept: '.jpg,.png,.jpeg',
  headers: {
    Authorization: sessionStorage.getItem('token') || '',
    dataApi: sessionStorage.getItem('dataApi') || '',
  },
};

// Base64 转 File
const base64ToFile = (base64, fileName) => {
  let arr = base64.split(','),
    type = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], fileName, { type });
};

function testSelection() {
  //获取Selection对象
  let selection: any = window.getSelection();
  //调用selection对象的toString()方法就可以获取鼠标拖动选中的文本。
  // console.log('选中的文本为：');
  // console.log(selection.toString());
  //选中的值
  let str = selection.toString();
  return str;
}

const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

const index = (props: any, ref) => {
  const { title = '标题', onOK } = props;

  const cropperRef = useRef<any>(null);

  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const [step, setStep] = useState<any>(0);

  const [info, setInfo] = useState<any>({});

  const [file, setFile] = useState<any>();
  const [selectText, setSelectText] = useState<any>('');
  const [url, setUrl] = useState<any>();
  const [text, setText] = useState<any>();
  const [transTextLoading, setTransTextLoading] = useState(false);

  const [vis, setVis] = useState<any>();
  const [JTbase64, setJTbase64] = useState<any>();
  const [tempBase64, setTempBase64] = useState<any>();

  const [fileList, setFileList] = useState<any>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState(false);

  const handleCancel = () => {
    setRecord({});
    setVisible(false);
    setConfirmLoading(false);
    setFile({});
    setSelectText('');
    setUrl('');
    setText('');
    setTransTextLoading(false);
    setVis(false);
    setJTbase64('');
    setPreviewOpen(false);
    setPreviewImage('');
    setPreviewTitle(false);
    setStep(0);
    setInfo({});
    setFileList([]);
    setTempBase64({});
    form.resetFields();
  };
  const onOk = async (e) => {
    if (_isEmpty(JTbase64)) {
      Tip.error('操作提示', '经典一句话截图必填');
      return;
    }
    // 截图上传获取url
    const { code = 500, data = [] } = await JTupload();
    if (code != 0) return;

    let obj = {
      id: info?.id,
      memCode: record?.code,
      // 档案原件
      archivesPath: file.url,
      // 经典名言
      sutra: selectText,
      // 经典名言截图
      sutraPath: data?.[0]?.url,
      // 档案(扫描内容)
      archives: text,
      // 政治生活照
      politicalLifes: fitFileUrlForForm(fileList)?.split?.(',') || [],
    };
    if (_isEmpty(obj.archivesPath)) {
      Tip.error('操作提示', '请上传档案原件');
      return;
    }
    if (_isEmpty(obj.sutra)) {
      Tip.error('操作提示', '经典一句话必填');
      return;
    }
    setConfirmLoading(true);
    const res = await archivesSave({
      data: obj,
    });
    setConfirmLoading(false);
    if (res.code === 0) {
      Tip.success('操作提示', '操作成功');
      props?.onOK?.();
      handleCancel();
    }
  };

  const getImgUrls = async (originFileObj) => {
    const base64 = await getBase64(originFileObj);
    setUrl(base64);
  };

  const onUploadCompChange = async (list, file) => {
    const { response: { code = 500, data = [] } = {}, originFileObj = {} } = file;
    if (code == 0) {
      const f = data?.[0] || {};
      setFile(f);
      getImgUrls(originFileObj);
      // setStep((e) => e + 1);
    }
  };

  const getText = async () => {
    setTransTextLoading(true);
    const worker = await createWorker('chi_sim');
    const ret = await worker.recognize(url);
    setTransTextLoading(false);
    setText(_tr(_replace(ret.data.text, /\s/g, '')));
    await worker.terminate();
  };

  // 截图回调
  const onCrop = _debounce(
    () => {
      const cropper: any = cropperRef.current?.cropper;
      if (!cropper) {
        return;
      }
      const base64rul = cropper.getCroppedCanvas().toDataURL();
      setTempBase64(base64rul);
      // setJTbase64(base64rul);
    },
    1000 * 0.1,
    {
      trailing: true,
    },
  );

  // 截图上传
  const JTupload = async () => {
    let file = base64ToFile(JTbase64, `截图${+new Date()}.png`);
    const formData = new FormData();
    formData.append('file', file);
    const res = await fetch('/api/base/putFile', {
      method: 'POST',
      headers: new Headers({
        Authorization: sessionStorage.getItem('token') || '',
        dataApi: sessionStorage.getItem('dataApi') || '',
      }),
      body: formData,
    }).then((response) => {
      if (response.ok) {
        return response.json();
      }
      throw new Error('Network response was not ok.');
    });
    return res;
  };

  const jietuOpen = async () => {
    setVis(true);
  };

  const getInfo = async (record) => {
    const res = await archivesFind({ memCode: record.code });
    if (res.code == 0) {
      const {
        data: {
          archivesPath = '',
          archives = '',
          sutra = '',
          sutraPath = '',
          politicalLifes = [],
        } = {},
      } = res;

      setInfo(res.data);

      if (_isEmpty(res.data)) return;

      // 档案原件
      try {
        const file_base64 = await pullFile({ path: archivesPath });
        setUrl(file_base64);
        setFile({ url: archivesPath });
      } catch (e) {
        console.log('🚀 ~ getInfo ~ e:', e, '档案原件');
      }

      // 档案(扫描内容)
      setText(archives);

      // 经典一句话
      setSelectText(sutra);

      // 经典一句话截图
      try {
        const sutra_base64 = await pullFile({ path: sutraPath });
        setJTbase64(sutra_base64);
      } catch (e) {
        console.log('🚀 ~ getInfo ~ e:', e, '经典一句话截图');
      }

      // 政治生活照
      try {
        // let photos = getInitFileList(politicalLifes.join(','));
        // for (let i = 0; i < politicalLifes.length; i++) {
        //   const item = photos[i];
        //   const base64 = await pullFile({ path: item.url });
        //   photos[i].thumbUrl = base64;
        //   photos[i].id = +new Date() + i;
        //   photos[i].url = item.url;
        // }
        // setFileList(photos);

        let photos = getInitFileList(politicalLifes.join(',')).slice(0, 5);
        let arr = photos.map((item, i) => {
          return () => pullFile({ path: item.url });
        });
        const res = await Promise.all(arr.map((it) => it()));
        photos = photos.map((it, index) => {
          return {
            ...it,
            thumbUrl: res[index],
            id: +new Date() + index,
            url: it.url,
          };
        });
        setFileList(photos);
      } catch (e) {
        console.log('🚀 ~ getInfo ~ e:', e, '政治生活照');
      }
    }
  };

  useImperativeHandle(ref, () => ({
    open: (query, openType) => {
      console.log("🚀 ~ useImperativeHandle ~ openType:", openType)
      setVisible(true);
      setRecord(query);
      if (query) {
        getInfo(query);
      }
    },
    clear: () => {
      // clear();
    },
  }));

  useEffect(() => {
    if (step == 1) {
      const el = document.getElementById('imgText');
      if (el) {
        el.onmouseup = function () {
          setSelectText(testSelection());
        };
      }
    }
  }, [step]);

  return (
    <Modal
      title={'档案录入'}
      visible={visible}
      onCancel={handleCancel}
      width={1200}
      destroyOnClose={true}
      bodyStyle={{ height: 730 }}
      footer={[
        <Button onClick={handleCancel}>取消</Button>,
        step == 0 && (
          <Fragment>
            <Button type="primary" disabled={!text} onClick={() => setStep((e) => e + 1)}>
              下一步
            </Button>
          </Fragment>
        ),
        step == 1 && (
          <Fragment>
            <Button onClick={() => setStep((e) => e - 1)}>上一步</Button>
            <Button type="primary" onClick={() => setStep((e) => e + 1)}>
              下一步
            </Button>
          </Fragment>
        ),
        step == 2 && (
          <Fragment>
            <Button onClick={() => setStep((e) => e - 1)}>上一步</Button>
            <Button type="primary" onClick={onOk} loading={confirmLoading}>
              确定
            </Button>
          </Fragment>
        ),
      ]}
    >
      {visible && (
        <Fragment>
          <Steps current={step}>
            <Steps.Step title="上传档案" description={'第一步'} />
            <Steps.Step title="档案导入识别" description={'第二步'} />
            <Steps.Step title="党内生活照片采集" description={'第三步'} />
          </Steps>
          <div>
            {step == 0 && (
              <React.Fragment>
                <div>
                  {/* <UploadComp
                    action={'/api/base/putFile'}
                    showUploadList={false}
                    buttonText="选择档案"
                    accept=".jpg,.png,.jpeg"
                    maxLen={1}
                    onChange={onUploadCompChange}
                  />
                  <div style={{ height: 10 }}></div>
                  <Alert
                    message="文字识别说明"
                    description="扫描之后的文字可以进行修改"
                    type="info"
                    showIcon
                  /> */}

                  <div className={st.contrast}>
                    <div className={st.photoBox}>
                      <img id={'targetImage'} src={url} alt="" />
                    </div>
                    {/* <div className={st.trans}>
                      <Button
                        loading={transTextLoading}
                        disabled={!url}
                        onClick={() => {
                          url && getText();
                        }}
                      >
                        文字识别
                      </Button>
                    </div> */}
                    {/* <div className={st.photoBox}>
                      <div
                        id={'editText'}
                        onBlur={(e) => {
                          const el = document.getElementById('editText');
                          if (!el) return;
                          const innerText = el.innerText;
                          setText(innerText);
                        }}
                        style={{ height: '100%' }}
                        suppressContentEditableWarning
                        contentEditable={true}
                      >
                        {text}
                      </div>
                    </div> */}
                  </div>
                </div>
              </React.Fragment>
            )}

            {step == 1 && (
              <Fragment>
                <Alert
                  message="经典一句话说明"
                  description="在原文框中点击滑动文字，文字将自动复制粘贴在经典一句话框中"
                  type="info"
                  showIcon
                />
                <div className={st.step_1}>
                  <div className={st.leftBox}>
                    <div className={st.tit}>原文</div>
                    <div className={st.photoBox}>
                      <div id={'imgText'}>{text}</div>
                    </div>
                  </div>

                  <div className={st.rightBox}>
                    <div>
                      <div className={st.tit}>经典一句话</div>
                      <div className={st.one}>{selectText}</div>
                    </div>
                    <div className={st.ph}>
                      <div className={st.phLabel}>
                        <div className={st.tit}>出处截图</div>
                        <Button onClick={jietuOpen}>截图</Button>
                      </div>
                      <div className={st.phBox}>
                        <img src={JTbase64} />
                      </div>
                    </div>
                  </div>
                </div>
              </Fragment>
            )}

            {step == 2 && (
              <div className={st.contrast2}>
                <Alert
                  message="照片上传格式"
                  description="把上传照片的名字修改为此照片含义（例如：集体团队活动XX）"
                  type="info"
                  showIcon
                />
                <div style={{ height: 20 }}></div>
                {/* <Upload
                  {...upProps}
                  listType="picture-card"
                  fileList={fileList}
                  multiple
                  onPreview={async (file: any) => {
                    if (!file.url && !file.preview) {
                      file.preview = await getBase64(file.originFileObj);
                    }
                    setPreviewImage(file?.thumbUrl ? file.thumbUrl : (file.preview as string));
                    setPreviewOpen(true);
                    setPreviewTitle(
                      file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1),
                    );
                  }}
                  onChange={({ fileList: newFileList }) => {
                    setFileList(newFileList);
                  }}
                  beforeUpload={(file, fileList) => {
                    const { name = '', size = 0 } = file;
                    let hasChinese = /[\u4e00-\u9fa5]/g.test(name);
                    let fileSize: number = file['size'] / 1024 / 1024;
                    return new Promise(async (resolve, reject) => {
                      if (fileSize > 3) {
                        compressAccurately(file, 1024 * 3).then((res) => {
                          resolve(res);
                        });
                      } else {
                        resolve(file);
                      }
                    });
                  }}
                >
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>点击上传图片</div>
                  </div>
                </Upload> */}

                <Modal
                  visible={previewOpen}
                  title={previewTitle}
                  footer={null}
                  onCancel={() => {
                    setPreviewOpen(false);
                  }}
                >
                  <img alt="image" style={{ width: '100%' }} src={previewImage} />
                </Modal>
              </div>
            )}

            <Modal
              destroyOnClose
              title="截图"
              visible={vis}
              width={1200}
              onCancel={() => {
                setVis(false);
              }}
              onOk={() => {
                setJTbase64(tempBase64);
                setVis(false);
              }}
            // bodyStyle={{ height: 560, overflow: 'auto' }}
            >
              <div className={st.jtBody}>
                <Cropper
                  src={url}
                  style={{ height: 400, width: '100%' }}
                  // Cropper.js options
                  crop={onCrop}
                  ref={cropperRef}
                  dragMode={'move'}
                  zoomTo={1}
                  guides={true}
                  viewMode={1}
                  minCropBoxHeight={10}
                  minCropBoxWidth={10}
                  movable={true}
                // preview=".img-preview"
                />

                {/* <div
                  className="img-preview"
                  style={{ width: '100%', height: '300px', overflow: 'hidden' }}
                ></div> */}
              </div>
            </Modal>
          </div>

          {/* <Form form={form} {...formItemLayout}>
            <Form.Item name="applyDate" label="上传照片">
              <UploadComp
                action={'/api/base/putFile'}
                accept=".jpg,.png,.jpeg"
                files={[]}
                maxLen={1}
                showUploadList={false}
                btnStyle={'text'}
                onChange={onUploadCompChange}
                buttonText={
                  <div className={st.photoBox}>
                    {url ? <img style={{ width: 150, height: 201 }} src={url} /> : '无照片'}
                  </div>
                }
              />
            </Form.Item>
          </Form> */}
        </Fragment>
      )}
    </Modal>
  );
};
export default React.forwardRef(index);
