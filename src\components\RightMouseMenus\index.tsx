import React, { useState, useEffect, Fragment, useImperativeHandle } from 'react';
import styles from './index.less';
import { Menu } from 'antd';
import { MailOutlined, AppstoreOutlined, SettingOutlined } from '@ant-design/icons';

const index = (props: any, ref) => {
  const { getXY, fatherDomId, menu, disabled } = props;
  const [visible, setVisible] = useState<any>();
  const [x, setX] = useState<any>();
  const [y, setY] = useState<any>();

  const boxId = `RightMenu_${fatherDomId}`;

  const openMenu = () => {
    setVisible(true);
  };
  const handleContextMenu = (event: MouseEvent) => {
    event.preventDefault();
    const { target: dom } = event || {};
    let { x = 0, y = 0 } = getXY?.({ dom, event });

    if(x == 0 && y == 0) return;

    // 当右键在右下方时，更改xy轴坐标
    const offsetWidth = 210;
    const windowWidth = window.innerWidth;
    if (event?.clientX + offsetWidth > windowWidth) {
      x = x - offsetWidth * 0.96;
    }
    const offsetHeight = 40 * menu.length + 10;
    const windowHeight = window.innerHeight;
    if (event?.clientY + offsetHeight > windowHeight - 30) {
      y = y - offsetHeight * 1.1;
    }

    if (x && y) {
      setX(x);
      setY(y);
      openMenu();
    }
  };
  const handleMousedown = (event) => {
    const { target: dom } = event || {};
    const father: any = document.getElementById(boxId);
    let isInDom = false;
    if (father) {
      isInDom = father.contains(dom);
    }
    if (!isInDom) {
      setVisible(false);
    }
  };
  //   useImperativeHandle(ref, () => ({
  //     open: ({ x, y }) => {
  //       openMenu();
  //     },
  //   }));
  useEffect(() => {
    const father = document.getElementById(fatherDomId);
    if (father) {
      father.addEventListener('contextmenu', handleContextMenu);
      father.addEventListener('mousedown', handleMousedown);
    }
  }, []);
  if (!visible) {
    return <div></div>;
  }
  return (
    <div id={boxId} style={{ top: y, left: x }} className={styles.box}>
      <Menu>
        {menu.map((it) => {
          return (
            <Menu.Item
              key={it.key}
              className={styles.lines}
              icon={it.icon || <MailOutlined></MailOutlined>}
              disabled={disabled.includes(it.key)}
              onClick={() => {
                it.click({ ...it });
                setVisible(false);
              }}
            >
              {it.name}
            </Menu.Item>
          );
        })}
      </Menu>
    </div>
  );
};

export default React.forwardRef(index);
