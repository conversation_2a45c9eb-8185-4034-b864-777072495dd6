import request from "@/utils/request";
import qs from 'qs';
export function getList(params) {
  return request(`/api/activity/listAC`,{
    method:'POST',
    body:params,
  });
}
export function add(params) {
  return request(`/api/activity/addAc`,{
    method:'POST',
    body:params,
  });
}
export function findAcByCode(params) {
  return request(`/api/activity/findAcByCode?${qs.stringify(params)}`,{
    method:'Get',
  });
}
export function updateAc(params) {
  return request(`/api/activity/updateAc`,{
    method:'POST',
    body:params,
  });
}
export function cancelAc(params) {
  return request(`/api/activity/cancelAc`,{
    method:'POST',
    body:params,
  });
}

export function groupList(params) {
  return request(`/api/org/group/getList?${qs.stringify(params)}`,{
    method:'Get',
  });
}
export function groupMem(params) {
  return request(`/api/mem/findeMemByGroup`,{
    method:'POST',
    body:params,
  });
}

export function committeeList(params) {
  return request(`/api/org/orgElect/findNewElect?${qs.stringify(params)}`,{
    method:'Get',
  });
}



