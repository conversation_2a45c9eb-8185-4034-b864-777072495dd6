import React, {Component, Fragment} from 'react';
import {Col, Row, Tooltip} from 'antd';
import MiddleItems from './middleItems';
import PT from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import styles from './middle.less';
export default class index extends Component<any, any>{
  static propTypes = {
    Items:PT.array.isRequired
  };

  static defaultProps = {
    // 详情数组
    Items:[]
  };
  constructor(props){
    super(props);
    this.state = {

    };
  }
  RowStyle = ({leftTitle = '', rightTitle = '', leftInfo = '', rightInfo = '', showAll = false} = {}) => {
    const infoStyle = (val) => {
      return (
        <Fragment>
          {
            showAll ?
            <Fragment>
              <div>{val}</div>
            </Fragment>
              :
              <Fragment>
                {
                  (!_isEmpty(val) &&　val.length >= 14) ?
                    <Tooltip placement="topLeft" title={val} arrowPointAtCenter style={{width:'100%'}}>
                      <div style={{textOverflow:'ellipsis',whiteSpace:'nowrap',overflow:'hidden'}}>{val}</div>
                    </Tooltip> :
                    <span style={{width:'100%'}}>{val}</span>
                }
              </Fragment>
          }
        </Fragment>
      );
    };
    return (
      <Row>
        <Col span={4} style={{width: '80px'}}>{leftTitle}  {!_isEmpty(leftTitle) && <span> : </span>}</Col>
        <Col span={!_isEmpty(rightTitle) ? 8 : 20}>
          {infoStyle(leftInfo)}
        </Col>
        {
          !_isEmpty(rightTitle) &&
            <Fragment>
              <Col span={4} style={{width: '110px'}}>&nbsp; &nbsp; &nbsp; &nbsp;{rightTitle} {!_isEmpty(rightTitle) && <span> : </span>}</Col>
              <Col span={8}>
                {infoStyle(rightInfo)}
              </Col>
            </Fragment>
        }
      </Row>
    );
  };
  middleTop = (Items = []) => {
    return (
      <Row style={{marginTop: '20px', marginBottom: '20px'}}>
        <Col span={24}>
          <dd className="detail-briefly">
            {
              !_isEmpty(Items) && Items.map((item, index) => {
                return (
                  <div key={index}>
                    {this.RowStyle(item)}
                  </div>
                );
              })
            }
          </dd>
        </Col>
      </Row>
    );
  };

  render() {
    const {Items} = this.props;
    return (
      <Fragment>
        {this.middleTop(Items)}
        <MiddleItems/>
      </Fragment>
    );
  }
}
