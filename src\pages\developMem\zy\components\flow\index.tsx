import React, { useState, Fragment, useEffect } from 'react';
import _isEmpty from 'lodash/isEmpty';
import style from './index.less';
import { _history } from "@/utils/method";
import qs from 'qs';

export default function FlowIndex(props: any) {
  const [checked, setChecked]: any = useState([]);
  const [checkedType, setCheckedType]: any = useState([]);
  const [swidth, setSwidth] = useState(0);
  const [total, setTotal] = useState(0);
  const {
    onChange,
    data = {}
  } = props;
  const { location: { pathname = '' } = {} } = _history
  const selectkey = (obj: any) => {
    if (obj.key != '0') {
      let _checked = checked
      let _checkedType = checkedType
      if (checked.includes(obj.key)) {
        _checked = checked.filter(i => i !== obj.key)
        _checkedType = checkedType.filter(i => i !== obj.type)
      } else {
        _checked = [..._checked, obj.key]
        _checkedType = [..._checkedType, obj.type]
      }
      setChecked(_checked)
      setCheckedType(_checkedType)
      onChange({ type: _checkedType })
    } else {
      setChecked([])
      setCheckedType([])
      onChange({ type: [] })
    }

  }
  useEffect(() => {
    let w = document.getElementById('flowcard')
    setSwidth((1654 - w?.clientWidth) / 1654)
  }, [window.innerWidth])

  useEffect(() => {
    let num = 0
    for (let o in data) {
      num += data[o]
    }
    setTotal(num)
  }, [data])
  return (
    <React.Fragment>
      <div style={{ width: '100%' }} id='flowcard'>
        {
          pathname == '/developMem/zy/apply' &&
          <div className={style.flow} style={{ height: 150, transform: `scale(${1 - swidth},1)`, transformOrigin: 'left top' }}>
            <div className={checked.length < 1 ? `${style.checkbtn} ${style.label}` : style.label} onClick={() => selectkey({ key: '0' })}>全部</div>
            <div className={style.bot}>
              <div style={{ width: 130, padding: '10px 0' }} className={checked.includes('1') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() => selectkey({ id: 1, type: 'RD_1', key: '1' })}>入党申请{data?.RD_1 ? `(${data?.RD_1})` : ''}</div>
              <div className={style.line}><img src={require('@/assets/mem/line1.png')} /></div>
              <div className={style.btnbox}>
                <div className={style.bordertext}>党组织派人谈话</div>
                <div style={{ width: 200, padding: '10px 0' }} className={checked.includes('2') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                  selectkey({ id: 2, type: 'RD_2_1', key: '2' })
                }>一个月内需要谈话{data?.RD_2_1 ? `(${data?.RD_2_1})` : ''}</div>
                <div className={style.line}><img src={require('@/assets/mem/line2.png')} /></div>
                <div style={{ width: 200, padding: '10px 0' }} className={checked.includes('3') ? `${style.checkbtn1} ${style.btn1}` : style.btn1} onClick={() =>
                  selectkey({ id: 2, type: 'RD_2_2', key: '3' })
                }>谈话时间少于10天{data?.RD_2_2 ? `(${data?.RD_2_2})` : ''}</div>
                <div className={style.line}><img src={require('@/assets/mem/line2.png')} /></div>
                <div style={{ width: 200, padding: '10px 0' }} className={checked.includes('4') ? `${style.checkbtn2} ${style.btn2}` : style.btn2} onClick={() =>
                  selectkey({ id: 2, type: 'RD_2_3', key: '4' })
                }>一个月内未进行谈话{data?.RD_2_3 ? `(${data?.RD_2_3})` : ''}</div>
              </div>
              <div className={style.line}><img src={require('@/assets/mem/lin6.png')} /></div>
              <div style={{ width: 120, padding: '10px 0' }} className={checked.includes('5') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 33, type: 'RD_3', key: '5' })}>已谈话{data?.RD_3 ? `(${data?.RD_3})` : ''}</div>
              <div className={style.line}><img src={require('@/assets/mem/lin6.png')} /></div>
              <div style={{ width: 170, padding: '10px 0' }} className={checked.includes('6') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 3, type: 'RD_4', key: '6' })
              }>满足积极分子{data?.RD_4 ? `(${data?.RD_4})` : ''}</div>
              <div className={style.bordertext1}>提交入党申请书至少三个月</div>
            </div>
          </div>
        }
        {
          pathname == '/developMem/zy/active' &&
          <div className={style.flow} style={{ height: 160, transform: `scale(${1 - swidth},1)`, transformOrigin: 'left top' }}>
            <div className={checked.length < 1 ? `${style.checkbtn} ${style.label}` : style.label} onClick={() => selectkey({ key: '0' })}>全部</div>
            <div className={style.btnbox}>
              <div className={style.bordertext3}>入党积极分子考察</div>
              <div className={style.linec2}><img src={require('@/assets/mem/line12.png')} /></div>
              <div className={style.linec3}><img src={require('@/assets/mem/line17.png')} /></div>
              <div style={{ width: 140, padding: '10px 0' }} className={checked.includes('1') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 111, type: 'JJ_1', key: '1' })
              }>待考察人员{data?.JJ_1 ? `(${data?.JJ_1})` : ''}</div>
              <div className={style.line}>
                <div className={style.bordertext2}>半年</div>
                <div> <img src={require('@/assets/mem/line9.png')} /></div>
                <div><img style={{ transform: 'rotateX(180deg)' }} src={require('@/assets/mem/line9.png')} /></div>
              </div>
              <div className={style.btnbox1}>
                <div style={{ display: 'block', position: 'relative', zIndex: 2, width: 160, padding: '10px 0' }} className={checked.includes('2') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                  selectkey({ id: 4, type: 'JJ_2', key: '2' })
                }>待第一次考察{data?.JJ_2 ? `(${data?.JJ_2})` : ''}</div>
                <div style={{ height: '10px' }}></div>
                <div style={{ display: 'block', position: 'relative', zIndex: 2, width: 160, padding: '10px 0' }} className={checked.includes('3') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                  selectkey({ id: 5, type: 'JJ_3', key: '3' })
                }>待第二次考察{data?.JJ_3 ? `(${data?.JJ_3})` : ''}</div>
              </div>
              <div className={style.line}>
                <div className={style.linec}> <img src={require('@/assets/mem/line13.png')} /></div>
                <div className={style.linec1}><img style={{ transform: 'rotateX(180deg)' }} src={require('@/assets/mem/line13.png')} /></div>
              </div>
              <div style={{ width: 210, padding: '10px 0' }} className={checked.includes('4') ? `${style.checkbtn2} ${style.btn2}` : style.btn2} onClick={() =>
                selectkey({ id: 6, type: 'JJ_4', key: '4' })
              }>超半个月未按时考察{data?.JJ_4 ? `(${data?.JJ_4})` : ''}</div>
              <div className={style.line}><img src={require('@/assets/mem/line2.png')} /></div>
              <div style={{ width: 160, padding: '10px 0' }} className={checked.includes('5') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 6, type: 'JJ_5', key: '5' })
              }>持续考察人员{data?.JJ_5 ? `(${data?.JJ_5})` : ''}</div>
              <div className={style.linec4}><img src={require('@/assets/mem/line15.png')} /></div>
              <div className={style.linec5}><img src={require('@/assets/mem/line18.png')} /></div>
            </div>

            <div style={{ width: 100 }} className={style.line}><img src={require('@/assets/mem/lin6.png')} /></div>
            <div style={{ width: 160, padding: '10px 0' }} className={checked.includes('6') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
              selectkey({ id: 7, type: 'JJ_6', key: '6' })
            }>上级党委备案{data?.JJ_6 ? `(${data?.JJ_6})` : ''}</div>
            <div style={{ width: 100 }} className={style.line}><img src={require('@/assets/mem/lin6.png')} /></div>
            <div style={{ width: 160, padding: '10px 0' }} className={checked.includes('7') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
              selectkey({ id: 8, type: 'JJ_7', key: '7' })
            }>发展对象阶段{data?.JJ_7 ? `(${data?.JJ_7})` : ''}</div>
          </div>
        }
        {
          pathname == '/developMem/zy/object' &&
          <div className={style.flow} style={{ transform: `scale(${1 - swidth},1)`, transformOrigin: 'left top' }}>
            <div className={checked.length < 1 ? `${style.checkbtn} ${style.label}` : style.label} onClick={() => selectkey({ key: '0' })}>全部</div>
            <div style={{ width: 140, padding: '10px 0' }} className={checked.includes('1') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
              selectkey({ id: 9, type: 'FZ_1', key: '1' })
            }>支委会审查{data?.FZ_1 ? `(${data?.FZ_1})` : ''}</div>
            <div style={{ width: 95 }} className={style.line}><img src={require('@/assets/mem/lin6.png')} /></div>
            <div style={{ width: 140, padding: '10px 0' }} className={checked.includes('2') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
              selectkey({ id: 9, type: 'FZ_2', key: '2' })
            }>党总支审查{data?.FZ_2 ? `(${data?.FZ_2})` : ''}</div>
            <div style={{ width: 95 }} className={style.line}><img src={require('@/assets/mem/lin6.png')} /></div>
            <div className={style.btnbox3}>
              <div className={style.bordertext4}>基层党委预审</div>
              <div style={{ width: 190, padding: '10px 0' }} className={checked.includes('3') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 10, type: 'FZ_3_1', key: '3' })}>一个月内进行预审{data?.FZ_3_1 ? `(${data?.FZ_3_1})` : ''}</div>
              <div className={style.line}><img src={require('@/assets/mem/line50.png')} /></div>
              <div style={{ width: 190, padding: '10px 0' }} className={checked.includes('4') ? `${style.checkbtn1} ${style.btn1}` : style.btn1} onClick={() =>
                selectkey({ id: 10, type: 'FZ_3_2', key: '4' })
              }>预审时间少于十天
                {data?.FZ_3_2 ? `(${data?.FZ_3_2})` : ''}</div>
              <div className={style.line}><img src={require('@/assets/mem/line50.png')} /></div>
              <div style={{ width: 190, padding: '10px 0' }} className={checked.includes('5') ? `${style.checkbtn2} ${style.btn2}` : style.btn2} onClick={() =>
                selectkey({ id: 10, type: 'FZ_3_3', key: '5' })
              }>一月内未进行预审
                {data?.FZ_3_3 ? `(${data?.FZ_3_3})` : ''}</div>
            </div>
            <div style={{ width: 95 }} className={style.line}><img src={require('@/assets/mem/lin6.png')} /></div>
            <div style={{ width: 160, padding: '10px 0' }} className={checked.includes('6') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
              selectkey({ id: 9, type: 'FZ_4', key: '6' })
            }>县级党委预审{data?.FZ_4 ? `(${data?.FZ_4})` : ''}</div>
            <div className={style.lineCorner}><img src={require('@/assets/mem/line53.png')} /></div>

            <div className={style.bottombox}>
              <div style={{ width: 160, padding: '10px 0' }} className={checked.includes('14') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 9, type: 'FZ_7', key: '14' })
              }>接收预备党员
                {data?.FZ_7 ? `(${data?.FZ_7})` : ''}</div>
              <div style={{ width: 25 }} className={style.line}><img src={require('@/assets/mem/line61.png')} /></div>
              <div style={{ width: 120, padding: '10px 0' }} className={checked.includes('15') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 10, type: 'FZ_8', key: '15' })
              }>县级组织<br />部门审核
                {data?.FZ_8 ? `(${data?.FZ_8})` : ''}</div>
              <div style={{ width: 25 }} className={style.line}><img src={require('@/assets/mem/line61.png')} /></div>
              <div className={style.btnbox2}>
                <div className={style.bordertext4}>基层党委审批</div>
                <div style={{ width: 170, padding: '10px 0' }} className={checked.includes('13') ? `${style.checkbtn1} ${style.btn1}` : style.btn1} onClick={() =>
                  selectkey({ id: 12, type: 'FZ_6_4', key: '13' })
                }>特殊原因延长审批{data?.FZ_6_4 ? `(${data?.FZ_6_4})` : ''}</div>
                <div style={{ width: 15 }} className={style.line}><img src={require('@/assets/mem/line55.png')} /></div>
                <div className={style.linec6}><img src={require('@/assets/mem/line40.png')} /></div>
                <div style={{ width: 100, padding: '10px 0' }} className={checked.includes('12') ? `${style.checkbtn2} ${style.btn2}` : style.btn2} onClick={() =>
                  selectkey({ id: 12, type: 'FZ_6_3', key: '12' })
                }>审批超时{data?.FZ_6_3 ? `(${data?.FZ_6_3})` : ''}</div>
                <div style={{ width: 15 }} className={style.line}><img src={require('@/assets/mem/line55.png')} /></div>
                <div style={{ width: 170, padding: '10px 0' }} className={checked.includes('11') ? `${style.checkbtn1} ${style.btn1}` : style.btn1} onClick={() =>
                  selectkey({ id: 12, type: 'FZ_6_2', key: '11' })
                }>审批时间少于十天
                  {data?.FZ_6_2 ? `(${data?.FZ_6_2})` : ''}</div>


                <div style={{ width: 15 }} className={style.line}><img src={require('@/assets/mem/line55.png')} /></div>
                <div style={{ width: 170, padding: '10px 0' }} className={checked.includes('10') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                  selectkey({ id: 12, type: 'FZ_6_1', key: '10' })
                }>三个月内审批人员
                  {data?.FZ_6_1 ? `(${data?.FZ_6_1})` : ''}</div>
                <div className={style.bordertext5}>特殊情况可适当延长审批时间，但不得超过六个月</div>
              </div>
              <div style={{ width: 25 }} className={style.line}><img src={require('@/assets/mem/line61.png')} /></div>
              <div className={style.btnbox2}>
                <div className={style.bordertext4}>支部大会讨论</div>
                <div style={{ width: 170, padding: '10px 0' }} className={checked.includes('9') ? `${style.checkbtn2} ${style.btn2}` : style.btn2} onClick={() =>
                  selectkey({ id: 11, type: 'FZ_5_3', key: '9' })
                }>一月内未进行讨论
                  {data?.FZ_5_3 ? `(${data?.FZ_5_3})` : ''}</div>
                <div style={{ width: 15 }} className={style.line}><img src={require('@/assets/mem/line55.png')} /></div>
                <div style={{ width: 170, padding: '10px 0' }} className={checked.includes('8') ? `${style.checkbtn1} ${style.btn1}` : style.btn1} onClick={() =>
                  selectkey({ id: 11, type: 'FZ_5_2', key: '8' })
                }>讨论时间少于五天
                  {data?.FZ_5_2 ? `(${data?.FZ_5_2})` : ''}</div>
                <div style={{ width: 15 }} className={style.line}><img src={require('@/assets/mem/line55.png')} /></div>

                <div style={{ width: 200, padding: '10px 0' }} className={checked.includes('7') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                  selectkey({ id: 11, type: 'FZ_5_1', key: '7' })
                }>一个月内进行讨论人员
                  {data?.FZ_5_1 ? `(${data?.FZ_5_1})` : ''}</div>
              </div>
            </div>
          </div>
        }

        {
          pathname == '/mem/zy/manage' &&
          <div className={style.flow} style={{ transform: `scale(${1 - swidth},1)`, transformOrigin: 'left top' }}>
            <div className={checked.length < 1 ? `${style.checkbtn} ${style.label}` : style.label} onClick={() => selectkey({ key: '0' })}>全部</div>
            <div style={{ width: 140, padding: '10px 0' }} className={checked.includes('1') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
              selectkey({ id: 13, type: 'RDXS', key: '1' })
            }>待入党宣誓
              {data?.RDXS ? `(${data?.RDXS})` : ''}</div>
            <div style={{ width: 60 }} className={style.line}><img src={require('@/assets/mem/line68.png')} /></div>
            <div className={style.btnbox4}>
              <div className={style.bordertext8}>预备期</div>
              <div className={style.linec8}>
                <div className={style.bordertext6}>完成第一次考察</div>
                <img src={require('@/assets/mem/line12.png')} />
              </div>

              <div style={{ width: 140, padding: '10px 0' }} className={checked.includes('2') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 14, type: 'YBQ_1_1', key: '2' })
              }>待考察人员
                {data?.YBQ_1_1 ? `(${data?.YBQ_1_1})` : ''}
              </div>
              <div className={style.line}>
                <div className={style.linec7} >
                  <div className={style.bordertext7}>半年</div>
                  <img src={require('@/assets/mem/line9.png')} /></div>
                <div className={style.linec7} >
                  <div className={style.bordertext7}>一年</div>
                  <img style={{ transform: 'rotateX(180deg)' }} src={require('@/assets/mem/line9.png')} /></div>
              </div>
              <div style={{ display: 'inline-block', verticalAlign: 'middle' }}>
                <div style={{ width: 130, display: 'block', padding: '4px 0', height: '40px', lineHeight: '27px' }} className={checked.includes('3') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                  selectkey({ id: 14, type: 'YBQ_1_2', key: '3' })
                }>满足考察{data?.YBQ_1_2 ? `(${data?.YBQ_1_2})` : ''}</div>
                <div style={{ height: '2px' }}></div>
                <div style={{ width: 130, display: 'block', padding: '4px 0', height: '40px', lineHeight: '27px' }} className={checked.includes('4') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                  selectkey({ id: 18, type: 'YBQ_1_3', key: '4' })
                }>满足转正{data?.YBQ_1_3 ? `(${data?.YBQ_1_3})` : ''}</div>
              </div>
              <div className={style.textcss}>支部大会通过之日起算，<br />预备期一年</div>
            </div>
            <div style={{ width: 60 }} className={style.line}><img src={require('@/assets/mem/line68.png')} /></div>
            <div className={style.btnbox2}>
              <div className={style.bordertext4}>支部大会讨论转正</div>
              <div style={{ width: 220, padding: '10px 0' }} className={checked.includes('5') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 15, type: 'YBQ_2_1', key: '5' })
              }>一个月内进行讨论转正{data?.YBQ_2_1 ? `(${data?.YBQ_2_1})` : ''}</div>
              <div className={style.line}><img src={require('@/assets/mem/line50.png')} /></div>
              <div style={{ width: 190, padding: '10px 0' }} className={checked.includes('6') ? `${style.checkbtn1} ${style.btn1}` : style.btn1} onClick={() =>
                selectkey({ id: 15, type: 'YBQ_2_2', key: '6' })
              }>讨论时间少于十天
                {data?.YBQ_2_2 ? `(${data?.YBQ_2_2})` : ''}</div>
              <div className={style.line}><img src={require('@/assets/mem/line50.png')} /></div>
              <div style={{ width: 190, padding: '10px 0' }} className={checked.includes('7') ? `${style.checkbtn2} ${style.btn2}` : style.btn2} onClick={() =>
                selectkey({ id: 15, type: 'YBQ_2_3', key: '7' })
              }>一月内未进行讨论
                {data?.YBQ_2_3 ? `(${data?.YBQ_2_3})` : ''}</div>
            </div>

            <div className={style.lineCorner1}><img src={require('@/assets/mem/line70.png')} /></div>

            <div className={style.bottombox1}>
              <div style={{ width: 110, padding: '10px 0' }} className={checked.includes('12') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 16, type: 'YBQ_5', key: '12' })
              }>已归档
                {data?.YBQ_5 ? `(${data?.YBQ_5})` : ''}</div>
              <div className={style.line}><img src={require('@/assets/mem/line71.png')} /></div>
              <div style={{ width: 190, padding: '10px 0', margin: '0 4px' }} className={checked.includes('13') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 17, type: 'YBQ_6', key: '13' })
              }>县级组织部门审核
                {data?.YBQ_6 ? `(${data?.YBQ_6})` : ''}</div>
              <div className={style.line}><img src={require('@/assets/mem/line71.png')} /></div>

              <div className={style.btnbox2} style={{ padding: '25px 20px 5px 20px' }}>
                <div className={style.bordertext4}>基层党委审批</div>
                <div style={{ width: 210, padding: '10px 0' }} className={checked.includes('11') ? `${style.checkbtn2} ${style.btn2}` : style.btn2} onClick={() =>
                  selectkey({ id: 16, type: 'YBQ_4_3', key: '11' })
                }>三个月内未进行审批
                  {data?.YBQ_4_3 ? `(${data?.YBQ_4_3})` : ''}</div>
                <div className={style.line}><img src={require('@/assets/mem/line74.png')} /></div>
                <div style={{ width: 210, padding: '10px 0' }} className={checked.includes('10') ? `${style.checkbtn1} ${style.btn1}` : style.btn1} onClick={() =>
                  selectkey({ id: 16, type: 'YBQ_4_2', key: '10' })
                }>审批时间少于三十天
                  {data?.YBQ_4_2 ? `(${data?.YBQ_4_2})` : ''}</div>
                <div className={style.line}><img src={require('@/assets/mem/line74.png')} /></div>
                <div style={{ width: 170, padding: '10px 0' }} className={checked.includes('9') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                  selectkey({ id: 16, type: 'YBQ_4_1', key: '9' })
                }>三个月内审批{data?.YBQ_4_1 ? `(${data?.YBQ_4_1})` : ''}</div>
              </div>
              <div className={style.line}><img src={require('@/assets/mem/line71.png')} /></div>
              <div style={{ width: 140, padding: '10px 0' }} className={checked.includes('8') ? `${style.checkbtn} ${style.btn}` : style.btn} onClick={() =>
                selectkey({ id: 17, type: 'YBQ_3', key: '8' })
              }>转正前公示
                {data?.YBQ_3 ? `(${data?.YBQ_3})` : ''}</div>
            </div>


          </div>
        }
      </div>


    </React.Fragment>
  );
}
