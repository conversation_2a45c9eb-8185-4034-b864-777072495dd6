import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input, Button, Checkbox } from 'antd';
import {connect} from 'dva';
// import OrgTree from '../components';
import OrgTree from '@/components/OrgTree';
import {withContext} from '@/utils/global.jsx';
import {isEmpty} from '@/utils/method.js';
import ListTable from '@/pages/user/user/addEdit';
import { getSession } from '@/utils/session';
import _isEmpty from 'lodash/isEmpty'
import Tip from '@/components/Tip';

@connect(({login,common,user})=>({login,common,user}))
class index extends React.Component<any,{visible:boolean,checkArr,orgCode:string,expandedKeys,selectedKeys,treeInfo}>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      checkArr:[],
      orgCode:'',
      expandedKeys:[],
      selectedKeys:[],
      treeInfo:{}
    }
  }

  showModal=()=>{
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
      orgCode:org['orgCode']
    })
  };

  handleOk=(account)=>{
    this.handleCancel();
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
    },()=>{
      this.props.dispatch({
        type:'user/clear',
        payload:{}
      })
    })
  };
  treeOk = () => {
    const { onSelect } = this.props;
    const { treeInfo } =this.state;
    if(_isEmpty(treeInfo)){
      Tip.error('操作提示','请选择组织');
    }else{
      onSelect(treeInfo);
      this.handleCancel()
    }
  };
  checkNode = (v) => {
    this.setState({checkArr:v})
  };
  loadData=(val)=>{
    this.props.dispatch({
      type:'common/getTree',
      payload:{
        data:{
          orgCodeList:val,
          excludeOrgCodeList:[]
        }
      }
    });
  };
  onSelected=(v,k)=>{
    this.setState({
      treeInfo:k.node.props.dataRef
    });
    // console.log(this.props,'xxxxxxxxx')
    this.props.onChange(k.node.props.dataRef)
  };

  render(): React.ReactNode {
    let {children,user={},dataInfo,common,login,rootOrg}=this.props;
    const org=getSession('roles') || {};
    return(
      <div>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          title='单位选择'
          visible={this.state.visible}
          onCancel={this.handleCancel}
          onOk={this.treeOk}
          style={{width:500,paddingTop:60}}
          maskClosable={false}
          className='unit_modal'
          bodyStyle={{height:500,overflow:'auto'}}
        >
          {/*defaultSelectedKeys orgCode*/}
          <OrgTree
            listData={common['listTree']}
            mapData={common['mapTree']}
            loadData={this.loadData}
            onChange={this.onSelected}
            rootCode={org['managerOrgCode']}
            showSearch={false}
            type={'userTree'}
          />
        </Modal>
      </div>
    );
  }
}

export default withContext(index);
