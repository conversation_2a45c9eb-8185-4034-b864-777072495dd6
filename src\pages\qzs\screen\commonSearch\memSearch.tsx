import React, { useEffect, useState } from 'react';
import st from './index.less';
import { Col, Pagination, Row, Spin } from 'antd';
import { screenlist } from '../services';
import { pullFile } from '@/services';
import _isEmpty from 'lodash/isEmpty';
import { _history as router } from '@/utils/method';
import moment from 'moment';

const memSearch = (props: any) => {
  const {
    // 人员搜索列表标题图片
    titImg = require('../../../../assets/qzs/dyxx.png'),
    // 储存key
    infoKey = 'bwcx_info',
    // 跳转路由
    goToPath = '/qzs/screen/bwcx/memShow',
  } = props;

  const { name, idcard, zzm } = router.location.query;

  const [mainList, setMainList] = useState([]);
  const [mainPagination, setMainPagination] = useState({
    pageNum: 1,
    pageSize: 9,
    total: 0,
    totalPage: 0,
  });
  const [loading, setLoading] = useState<any>(false);

  const getList = async (p = {}) => {
    setLoading(true);
    const {
      code: mainResCode = 500,
      data: { list: mainsList = [], ...others } = {},
    } = await screenlist({
      data: {
        pageNum: mainPagination.pageNum,
        pageSize: mainPagination.pageSize,
        idCard: idcard,
        name: name,
        joinOrgDate: zzm?.split?.('-')?.[1] || undefined,
        lastIdcard: zzm?.split?.('-')?.[2] || undefined,
        ...p,
      },
    });
    setTimeout(() => {
      setLoading(false);
    }, 1000 * 0.5);
    if (mainResCode == 0) {
      for (let i = 0; i < mainsList.length; i++) {
        const item: any = mainsList[i];
        if (_isEmpty(item.photo)) {
          item['photo_base64'] = require('../../../../assets/head.jpg');
          continue;
        }
        const base64 = await pullFile({ path: item.photo });
        item['photo_base64'] = base64;
      }
      setMainList(mainsList);
      setMainPagination({ ...others, pageNum: others.pageNumber });
    }
  };
  const onPageChange = async (pageNum, pageSize) => {
    getList({ pageNum, pageSize });
  };

  const onClick = (record) => {
    sessionStorage.setItem(infoKey, JSON.stringify(record));
    router.push(goToPath);
  };

  useEffect(() => {
    if (name || idcard || zzm) {
      getList();
    }
  }, [name, idcard, zzm]);
  return (
    <div className={st.searchMem} style={props?.style || {}}>
      <div className={st.center}>
        <img className={st.tit} src={titImg} alt="" />
        <Spin wrapperClassName={st.tit1} spinning={loading}>
          <Row style={{ width: '100%' }} gutter={[16, 16]}>
            {mainList.map((it: any, index) => {
              return (
                <Col span={8} key={index}>
                  <div className={st.listItem} onClick={() => onClick(it)}>
                    <div className={st.photo}>
                      <img src={it.photo_base64} alt="" />
                    </div>
                    <div className={st.info}>
                      <div className={st.name}>
                        <div>{it.name}</div>
                        <span>{it.d06Name}</span>
                      </div>
                      <div className={st.desc}>
                        出生年月:{moment(it.birthday).format('YYYY年M月')}
                      </div>
                      <div className={st.desc}>
                        政治生日:{moment(it.joinOrgDate).format('YYYY年M月')}
                      </div>
                    </div>
                  </div>
                </Col>
              );
            })}
          </Row>
          <div className={st.pagination}>
            <Pagination
              size="small"
              {...mainPagination}
              current={mainPagination.pageNum}
              onChange={onPageChange}
              showSizeChanger={false}
            />
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default memSearch;
