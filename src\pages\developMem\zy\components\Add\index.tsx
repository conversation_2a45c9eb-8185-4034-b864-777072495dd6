import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Row,
  Col,
  Input,
  Radio,
  Switch,
  Button,
  Select,
  Modal,
  InputNumber,
  Upload,
  message,
  Space,
  Spin,
  Progress,
  Skeleton,
  Popconfirm
} from 'antd';
import OrgSelect from '@/components/OrgSelect';
import DictTreeSelect from 'src/components/DictTreeSelect';
import DictSelect from 'src/components/DictSelect';
import Tip from '@/components/Tip';
import MemSelect from '@/components/MemSelect';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import {
  findDictCodeName,
  unixMoment,
  timeSort,
  getIdCardInfo,
  formLabel,
  correctIdcard,
  convertToWebpBeforeUpload,
  _history
} from '@/utils/method';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _iisArray from 'lodash/isArray';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import _isNumber from 'lodash/isNumber';
import _trim from 'lodash/trim';
import { getSession } from '@/utils/session';
import DictArea from '@/components/DictArea';
import Date from '@/components/Date';
import YN from '@/components/YesOrNoSelect';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import { getUnitByOrg } from '@/services';
import SearchUnit from '@/components/SearchUnit';
import { getUnitName, normalList } from '@/services';
import { validateLength } from '@/utils/formValidator';
import style from './index.less'
import Info from './info'
import FormList from './formList'
import { listMemDigital, preview, dauploadFileDigital, checkOath, shareLink } from '@/pages/developMem/services'
import MembersWorkProcedures from '@/pages/archivesAdministration/components/membersWorkProcedures'
import { LoadingOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import LazyImage from '../Edit/lazyImage';
import XX from '@/assets/mem/xx.png'
import ImageCropper from '@/components/ImageCropper'

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const formItemLayout3 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 13 },
  },
};
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
let time: null = null;
class index extends React.Component<any, any> {
  imgContainerRefs: { [key: string]: HTMLDivElement | null };

  constructor(props) {
    super(props);
    this.state = {
      nowAge: undefined,
      hasLost: false,
      _basicInfo: {},
      modalVisible: false,
      d08Code: '',
      hasAppointment: false,
      isOutSystem_state: false,
      getUnitList: this.getUnitList,
      canEdit: false,
      fileObj: {
        formList: [],
        value: [],
        checkList: [],
      },
      checkList: [],
      step: 0,
      fileIndex: 0,
      isSub: false,
      uploadloading: false,
      isDragging: false,
      filepercent: 0,
      prevent: false,
      cropperVisible: false,
      fileListIndex: 0
    };
    this.imgContainerRefs = {};
  }
  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    const org = getSession('org') || {};
    const state = {};
    const { memDevelop: { basicInfo = {} } = {} } = nextProps;
    const {
      _basicInfo = {},
      getUnitList,
      editType,
      modalVisible,
      unitInformation,
      unitOrgLinkedList,
    } = prevState;

    if (!_isEqual(basicInfo?.orgCode, _basicInfo?.orgCode) && !editType && modalVisible) {
      state['_basicInfo'] = basicInfo;
      if (basicInfo?.orgCode) {
        getUnitList(basicInfo?.orgCode);
      }
    }
    if (!_isEqual(basicInfo?.id, _basicInfo?.id) && modalVisible) {
      state['_basicInfo'] = basicInfo;
      const {
        d08Code = '',
        d18Code = '',
        d19Code = '',
        isOutSystem,
        d48Code = '',
        d48Name = '',
      } = basicInfo;
      state['d08Code'] = d08Code;
      if (!_isEmpty(d18Code) && d18Code !== '0') {
        state['hasLost'] = true;
      } else {
        state['hasLost'] = false;
      }
      if (isOutSystem === 1) {
        state['isOutSystem_state'] = true;
      } else {
        state['isOutSystem_state'] = false;
      }
      if (!_isEmpty(d19Code) && d19Code !== '0') {
        state['hasAppointment'] = true;
      } else {
        state['hasAppointment'] = false;
      }
      if (d48Code) {
        state['area'] = d48Name;
      }
      getUnitList(basicInfo?.orgCode, basicInfo);
    }

    if (!_isEqual(basicInfo?.id, _basicInfo?.id) && modalVisible) {
      // 初始化 知识分子情况 不渲染的选项
      if (!_isEmpty(basicInfo['d154Code'])) {
        let arr = basicInfo['d154Code'].split(',');
        if (arr.includes('0')) {
          state['d154CodeNoDraw'] = ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B'];
        } else if (arr.includes('B')) {
          state['d154CodeNoDraw'] = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A'];
        } else if (
          ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A'].includes(arr[arr.length - 1])
        ) {
          state['d154CodeNoDraw'] = ['0', 'B'];
        } else {
          state['d154CodeNoDraw'] = [];
        }
      } else {
        state['d154CodeNoDraw'] = [];
      }
    }

    return { ...state, org };
  };
  getUnitList = async (orgCode, basicInfo = {}) => {
    const {
      code: resCode = 500,
      data: {
        d194Code = '',
        isLegal = '',
        unitOrgLinkedList = [],
        unitInformation = undefined,
        unitInformationCode = undefined,
        d04Code = undefined,
      } = {},
    } = await getUnitByOrg({ orgCode });
    if (resCode === 0) {
      this.props.form.setFieldsValue({
        readingCollege: basicInfo['readingCollege']
          ? basicInfo['readingCollege']
          : unitInformation
            ? unitInformation
            : _get(unitOrgLinkedList, '[0].unitName', undefined),
      });

      this.setState({
        unitInformation,
        unitList: unitOrgLinkedList,
        unitInformationCode,
        unitInformationD04Code: d04Code,
        isLegal: isLegal,
        unitInformationd194Code: d194Code,
      });
    }
  };
  d08CodeOnChange = (e) => {
    const { key = '' } = e;
    this.setState({ d08Code: key });
  };

  d18OnChange = (code) => {
    if (code && code['key'] !== '0') {
      this.setState({ hasLost: true });
    } else {
      this.setState({ hasLost: false });
      this.props.form.setFieldsValue({ lossDate: undefined });
    }
  };
  d19CodeOnChange = (code) => {
    if (code && code['key'] !== '0') {
      this.setState({ hasAppointment: true });
    } else {
      this.setState({ hasAppointment: false });
      this.props.form.setFieldsValue({ appointmentDate: undefined, appointmentEndDate: undefined });
    }
  };
  // 身份证
  getIDinfo = (e) => {
    const { target: { value = '' } = {} } = e || {};

    let info = getIdCardInfo(value);

    if (!_isEmpty(value) && info !== 'Error') {
      // 大陆身份证自动赋值，港澳台身份证不自动赋值
      if (`${value}`.length === 18 || `${value}`.length === 15) {
        this.setState({
          area: info[0],
        });
        this.props.form.setFieldsValue({
          sexCode: info[2] === '女' ? '0' : '1',
          birthday: info[1] ? moment(info[1], 'YYYY-MM-DD') : undefined,
          d48Code: info[3],
          politicsCode: undefined,
        });
        this['politicsCode'].clearAll();
      }
    }
  };
  validatorIdcard = async (rule, value, callback) => {
    if (_isEmpty(value)) {
      callback('身份证必填');
    }
    callback();
    // if (value && value.length !== 18 && process.env.idCheck != 'false') {
    //   callback('身份证应该为18位');
    // }
    // if (getIdCardInfo(value) === 'Error') {
    //   callback('身份证格式错误,请核对身份证图片');
    // } else {
    //   // let fieldValue = this.props.form.getFieldValue('name');
    //   // let res =await geitCard({idCard:value,name:fieldValue});
    //   callback();
    // }
  };

  // 姓名校验：不能有空格和·以外的字符
  nameValidator = (rule, value, callback) => {
    // let reg = /(^[\u4e00-\u9fa5]{1}[\u4e00-\u9fa5\.·。]{0,18}[\u4e00-\u9fa5]{1}$)|(^[a-zA-Z]{1}[a-zA-Z\s]{0,18}[a-zA-Z]{1}$)/
    let reg = /(^[\u4e00-\u9fa5]{1}[\u4e00-\u9fa5·]{0,18}[\u4e00-\u9fa5]{1}$)/;

    if (reg.test(value)) {
      // console.log('通过');
      validateLength([rule, value, callback], 16, 50);
      // callback();
    } else {
      callback('只能是汉字，不能有空格或特殊字符');
    }
  };

  // 时间限制
  disabledTomorrow = (current) => {
    return current && current > moment().endOf('day');
  };
  // 时间先后顺序判断
  TemporalOrder = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { applyDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr1 = [
        { text: '出生日期', value: value },
        { text: '申请入党时间', value: applyDate },
      ];
      if (timeSort(timeArr1, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder1 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { birthday = undefined, activeDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr1 = [
        { text: '出生日期', value: birthday },
        { text: '申请入党时间', value: value },
        { text: '确定积极分子时间', value: activeDate },
      ];
      if (timeSort(timeArr1, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder2 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { applyDate = undefined, objectDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr2 = [
        { text: '申请入党时间', value: applyDate },
        { text: '确定积极分子时间', value: value },
        { text: '确定发展对象时间', value: objectDate },
      ];
      if (timeSort(timeArr2, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder3 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { activeDate = undefined } = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr2 = [
        { text: '确定积极分子时间', value: activeDate },
        { text: '确定发展对象时间', value: value },
      ];
      if (timeSort(timeArr2, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder4 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      const { appointmentDate = undefined, appointmentEndDate = undefined } =
        form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '';
      const backFunc = (val) => {
        text = val;
      };
      let timeArr2 = [
        { text: '聘任开始时间', value: appointmentDate },
        { text: '结束时间', value: appointmentEndDate },
      ];
      if (timeSort(timeArr2, backFunc)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  TemporalOrder5 = (rule, value, callback) => {
    if (value) {
      const { form } = this.props;
      // const {applyDate = undefined} = form.getFieldsValue() || {};
      const errs = form.getFieldsError();
      let text = '年龄须大于等于18岁';
      // const backFunc = (val) =>{
      //   text = val;
      // };
      // let timeArr1 = [
      //   {text: '出生日期',value:value},
      //   // {text: '申请入党时间',value:applyDate},
      // ];
      const yearsAgo = moment().subtract(18, 'years');
      if (value.isAfter(yearsAgo)) {
        callback(text);
      } else {
        callback();
        for (let obj of Object.entries(errs)) {
          !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
        }
      }
    }
    callback();
  };
  // 保存
  submit = async () => {
    const { memDevelop: { basicInfo = {} } = {}, onsubmit, commonDict: { area = [] } = {} } = this.props;
    const { step, fileObj, rowCode, hasMemValue, rowData, baseformdata = {}, isSub, processNode } = this.state
    let _step = step
    if (_step == 1) {
      this.props.form.validateFieldsAndScroll(async (err, val) => {
        if (!err) {
          if (val['name'] != basicInfo['name'] || val['idcard'] != basicInfo['idcard']) {
            let result = await correctIdcard(val['name'], val['idcard']);
            if (result['code'] != '200') {
              this.props.form.setFields({
                idcard: {
                  value: val['idcard'],
                  errors: [
                    new Error(
                      '经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。',
                    ),
                  ],
                },
              });
              Tip.error(
                '操作提示',
                '经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。',
              );
              return;
            } else {
              val['idCardReason'] = result['reason'];
              val['idCardReasonName'] = result['reasonName'];
            }
          }
          val = unixMoment(
            [
              'birthday',
              'applyDate',
              'activeDate',
              'objectDate',
              'appointmentDate',
              'appointmentEndDate',
              'lossDate',
              'joinWorkDate',
              'enterSchoolDate',
            ],
            val,
          );
          //bool改为int
          [
            'isHighKnowledge',
            'hasWorker',
            'hasYoungFarmers',
            'isAdvancedModel',
            'isDispatch',
            'isFarmer',
            'isOutSystem',
            'hasStaffOrganization'
          ].map((item) => {
            val[`${item}`] = val[`${item}`] == 1 ? 1 : val[`${item}`] == 0 ? 0 : undefined;
          });
          // 增加字典表的name
          val = findDictCodeName(
            [
              'd49',
              'd18',
              'd19',
              'd20',
              'd21',
              'd07',
              'd09',
              'd11',
              'd27',
              'd28',
              'd126',
              'd04',
              'd194',
              'd195',
              'd06',
              'd08',
              'd60',
              'd88',
              'readingProfessional',
              'politics',
              'advancedModel',
              'jobNature',
              'joinOrg',
            ],
            val,
            baseformdata
          );
          val['d08Name'] = baseformdata['d08Name'] ? baseformdata['d08Name'] : '入党申请人';

          val['sexName'] = val['sexCode'] === '1' ? '男' : '女';
          // 增加组织zbcode
          val['orgZbCode'] =
            typeof val['orgCode'] === 'object' ? val['orgCode'][0]['zbCode'] : basicInfo['orgZbCode'];
          val['developOrgCode'] =
            typeof val['orgCode'] === 'object'
              ? val['orgCode'][0]['orgCode']
              : basicInfo['developOrgCode'];
          val['orgName'] =
            typeof val['orgCode'] === 'object' ? val['orgCode'][0]['name'] : basicInfo['orgName'];
          val['orgCode'] =
            typeof val['orgCode'] === 'object' ? val['orgCode'][0]['code'] : basicInfo['orgCode'];

          if (val['isOutSystem'] === 0) {
            val['developAppliedOrgCode'] =
              typeof val['appliedOrgCode'] === 'object'
                ? val['appliedOrgCode'][0]['orgCode']
                : basicInfo['developAppliedOrgCode'];
            val['appliedOrgZbCode'] =
              typeof val['appliedOrgCode'] === 'object'
                ? val['appliedOrgCode'][0]['zbCode']
                : basicInfo['appliedOrgZbCode'];
            val['appliedOrgName'] =
              typeof val['appliedOrgCode'] === 'object'
                ? val['appliedOrgCode'][0]['name']
                : basicInfo['appliedOrgName'];
            val['appliedOrgCode'] =
              typeof val['appliedOrgCode'] === 'object'
                ? val['appliedOrgCode'][0]['code']
                : basicInfo['appliedOrgCode'];
          }

          //  知识分子情况 数组改为字符串
          if (!_isEmpty(val['d154Code'])) {
            if (typeof val['d154Code'] === 'object') {
              let nameArr: any = [];
              let codeArr: any = [];
              val['d154Code'].map((item: any, index) => {
                const { key = '', name = '' } = item;
                nameArr.push(name);
                codeArr.push(key);
              });
              val['d154Name'] = nameArr.toString();
              val['d154Code'] = codeArr.toString();
            } else {
              val['d154Name'] = baseformdata['d154Name'];
            }
          }

          // 中间交换区
          if (val['hasUnitStatistics'] == 1 && val['_d04Code']) {
            val['d04Code'] = this.state.unitInformationD04Code;
          }
          if (val['hasUnitStatistics'] == 1 && val['__d04Code']) {
            val['d04Code'] = val['__d04Code'];
          }
          val['middleUnitCode'] =
            typeof val['middleUnitCode'] == 'object'
              ? val['middleUnitCode']?.code
              : val['middleUnitCode'];
          if (val['middleUnitCode'] && val.d09Code != '13') {
            val['d194Code'] = val['_d194Code'];
            val['d194Name'] = val['_d194Name'];
            val['d195Code'] = val['_d195Code'];
            val['d195Name'] = val['_d195Name'];
          }
          if (!_isEmpty(val.d09Code)) {
            let _key = val.d09Code;
            //是否劳务派遣工默认为否
            if (
              !(
                `${_key}`.startsWith('016') ||
                `${_key}`.startsWith('025') ||
                `${_key}` == '0313' ||
                `${_key}` == '0323' ||
                `${_key}` == '0333'
              )
            ) {
              val['isDispatch'] = '0';
              val['isFarmer'] = '0';
            }
          }
          if (_isEmpty(val.d194Code)) {
            val.d194Code = '';
            val.d194Name = '';
          }
          if (_isEmpty(val.d195Code)) {
            val.d195Code = '';
            val.d195Name = '';
          }

          if (val['d195Code'] == 'V0000') {
            val['d195Name'] = '无';
          }
          val['toactiveContextPerson'] = _isEmpty(val['toactiveContextPerson'])
            ? ''
            : hasMemValue
              ? val['toactiveContextPerson'].map((item) => item['code']).toString()
              : val['toactiveContextPerson'];
          const canEdit = this.showGUOMINGJINGJI();
          if (!canEdit) {
            val.d194Code = '';
            val.d194Name = '';
            val.d195Code = '';
            val.d195Name = '';
          }
          if (!val['d48Name']) {
            val['d48Name'] = area.find(i => i.key == val['d48Code'])?.name
          }
          let url = this.state.editType == 'benNian' ? 'memDevelop/saveYear' : 'memDevelop/zysave';
          let subobj = {
            type: url,
            payload: {
              data: { ...val },
              type: !basicInfo?.id ? 'add' : 'edit',
            },
          }
          console.log(val, 'valvalval数据提交')
          let res = await this.props.dispatch({
            type: 'memDevelop/zycheckDevelop',
            payload: {
              data: { ...val }
            }
          })
          if (res.code == 0) {
            this.setState({
              step: 2,
              subobj,
              baseformdata: { ...val },
              isSub: true, // 判断页面是否进入提交，未进入保存参数使用baseinfo
            })
          }
          
        }
      });

    }

    if (_step == 2) {
      const { fileObj: { value, id } = {} } = this.state;
      {
        let subobj = {
          type: 'memDevelop/zysave',
          payload: {
            data: {},
          },
        }
        value.forEach((i, k) => {
          this.setState({
            [`fileList${k}`]: [],
          })
        })
        this.setState({
          subobj,
        }, () => {
          let flag = true
          if (id == '8') {
            let filter = value.filter(i => i.d222Code != '304' && i.d222Code != '305')
            console.log(filter, 'filterfilterfilter')
            filter.map(i => {
              if (i.fileList.length < 1) {
                message.error(`${i.d222Name}未上传`);
                flag = false
              }
            })
          } else {
            value.map(i => {
              if (i.fileList.length < 1) {
                message.error(`${i.d222Name}未上传`);
                flag = false
              }
            })
          }

          if (fileObj?.formList && fileObj?.formList.length > 0) {
            if (flag) {
              this['FormList'].submit();
            }
          } else {
            if (flag) this['Info'].showModal({ data: isSub ? { ...baseformdata } : { ...basicInfo }, file: fileObj, rowCode, processNode })
          }
        })
      }
    }
  };
  confirmAgain = async (obj: any) => {
    const { node = {}, formDatas = {}, memInfo = {} } = obj;
    const { subobj = {}, fileObj: { value = [] } = {}, rowCode, developOrgCode, rowData, processNode } = this.state;
    let _value = value
    const { memDevelop: { basicInfo = {} } = {}, onsubmit } = this.props;
    // let nodetype = rowData['processNode']
    let res: any = {}
    let arr = ['RD_1', 'RD_4', 'JJ_7']
    let ob = {}
    node['processNode'] = rowData['processNode']
    if (rowData['processNode'] == 'JJ_4') {
      node['lastCode'] = rowData['lastCode']
    }
    if (rowData['processNode'] == 'JJ_5') {
      node['nextCode'] = rowData['processNode']
    }
    if (rowData['processNode'] == 'RD_4') {
      ob['memCode'] = rowCode
      ob['logOrgCode'] = developOrgCode
    }

    let fileList: any = [];
    _value.map(i => {
      i.fileList.map(j => {
        let obj = {
          ...i,
          ...j
        }
        delete obj.fileList
        delete obj.files
        fileList.push(obj)
      })
    })
    console.log(fileList, 'fileList')
    if (arr.includes(rowData['processNode'])) {
      res = await this.props.dispatch({
        type: subobj.type,
        payload: {
          data: {
            ...subobj?.payload?.data, ...formDatas, ...node, ...memInfo, ...ob, filesList: fileList.map((i, k) => {
              return {
                ...i,
                sort: k + 1
              }
            })
          },
          type: !basicInfo?.id ? 'add' : 'edit',

        },
      });
    } else {
      res = await this.props.dispatch({
        type: 'memDevelop/uploadFileDigital',
        payload: {
          data: {
            code: rowCode,
            // ...obj,
            filesList: fileList,
            digitalLotNo: memInfo['digitalLotNo'] || rowData['digitalLotNo'],
            ...formDatas,
            ...node
          }
        }
      })
    }

    const { code = 500, data = {} } = res;
    if (code === 0) {
      Tip.success('操作提示', basicInfo['code'] ? '修改成功' : '新增成功');
      this.cancel();
      onsubmit && onsubmit({ editType: this.state.editType });
    }
    this['Info'].closeModal();
  }
  backform = (vals) => {
    const { step, fileObj, rowCode, hasMemValue, processNode } = this.state
    const { memDevelop: { basicInfo = {} } = {} } = this.props;
    const { location: { pathname = '' } = {} } = _history
    if (processNode == 'RD_4') {
      let subobj = {
        type: 'memDevelop/tozyActive',
        payload: {
          data: { ...vals },
        },
      }
      this.setState({
        subobj
      }, () => {
        this['Info'].showModal({ data: basicInfo, formData: vals, file: fileObj, rowCode, processNode })
      })
    } else if (processNode == 'JJ_7') {

      let subobj = {
        type: 'memDevelop/zytoObject',
        payload: {
          data: { ...vals },
        },
      }
      this.setState({
        subobj
      }, () => {
        this['Info'].showModal({ data: basicInfo, formData: vals, file: fileObj, rowCode, processNode })
      })
    } else {
      this['Info'].showModal({ data: basicInfo, formData: vals, file: fileObj, rowCode, processNode })
    }
  }
  cancel = () => {
    const { fileObj: { value = [] } = {} } = this.state;
    let list = value
    list.map((i, k) => {
      this.setState({
        [`fileList${k}`]: []
      })
    })
    this.setState({
      d154CodeNoDraw: [],
      step: 1,
      fileObj: {
        formList: [],
        value: [],
        checkList: [],
      },
      fileList: [],
      modalVisible: false,
      baseformdata: {},
      isSub: false,
    });
    this.destroy();
    this.props.onsubmit && this.props.onsubmit();
  };
  open = (e) => {
    let uparr = [
      {
        id: '1',
        name: '入党申请人资料',
        type: ['RD_1'],
        formList: [],
        value: [
          {
            key: 'file',
            d222Name: '入党申请书',
            fileList: [],
            d222Code: '101',
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否手写、内容是否合规、年龄是否满足、落款是否规范',
              ]
            }
          },
          {
            key: 'file1',
            d222Name: '身份证或户口本复印件',
            d222Code: '103',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 复印件是否加盖复印单位鲜章，是否签署“复印件与原件内容一致”的意见',
              ]
            }
          },
        ]
      },
      {
        id: '2',
        name: '谈话记录',
        type: ['RD_2_1', 'RD_2_2', 'RD_2_3'],
        formList: [],
        value: [
          {
            key: 'file',
            d222Name: '党组织派人与入党申请人谈话的记录',
            d222Code: '102',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 谈话时间是否在提出入党申请的1个月内。',
                '2. 谈话内容（是否涵盖动机、目的、基本知识、意愿等）。',
                '3. 谈话意见、签名是否盖章'
              ]
            }
          },
        ]
      },
      {
        id: '3',
        name: '确定积极分子',
        type: ['RD_4'],
        formList: [
          {
            label: '联系人是否为本组织人员',
            required: true,
            type: 'radio',
            key: 'hasStaffOrganization'
          },
          {
            label: '入党积极分子培养联系人',
            required: true,
            type: 'memselect',
            key: 'toactiveContextPerson'
          },
          {
            label: '确定积极分子时间',
            required: true,
            type: 'time',
            key: 'activeDate'
          },
        ],
        value: [
          {
            key: 'file',
            d222Name: '党员（群团组织）推荐入党积极分子登记表',
            d222Code: '201',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 推优方是否合规（如：28岁以下是团员的需由团区委推优，企事业单位职工的需由公会推优，是女性的需由妇联推优）',

              ]
            }
          },
          {
            key: 'file1',
            d222Name: '确定为入党积极分子的支委会会议记录、备案',
            d222Code: '202',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '一、支委会会议记录',
                '1. 会议时间是否距递交申请书时间三个月以上',
                '2. 会议主持、参会人员情况是否合规',
                '3. 参会人员与发言人员一致',
                '4. 会议记录是否规范',
                '二、备案',
                '1. 基础信息是否一致',
                '2. 报告时间应是会议当天',
                '3. 意见是否明确'
              ]
            }
          },
          {
            key: 'file2',
            d222Name: '《入党积极分子、发展对象培养教育考察登记表》',
            d222Code: '203',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 封面是否盖章',
                '2. 内页个人基本情况从确定为积极分子起算 ',
                '3. 个人简历从小学起算，不得断档，需月对月',
                '4. 确定积极分子时间、入党申请时间与前期资料是否一致',
                '5. 是否符合半年一次填写',
                '6. 内容（包含时事等）是否符合实际'
              ]
            }
          },
        ]
      },
      {
        id: '4',
        name: '第一次考察',
        type: ['JJ_2'],
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '入党积极分子、发展对象培养教育考察登记表-阶段考察纪实（一）',
            d222Code: '203',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否符合半年一次填写',
                '2. 内容（包含时事等）是否符合实际 ',
              ]
            }
          },
          {
            key: 'file1',
            d222Name: '阶段考察纪实（一）-入党积极分子思想汇报',
            d222Code: '204',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否手写、内容是否合规',
                '2. 是否每三个月一次、培养联系人是否签字 ',
              ]
            }
          },
        ]
      },
      {
        id: '5',
        type: ['JJ_3'],
        name: '第二次考察',
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '入党积极分子、发展对象培养教育考察登记表-阶段考察纪实（二）',
            d222Code: '203',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否符合半年一次填写',
                '2. 内容（包含时事等）是否符合实际 ',
              ]
            }
          },
          {
            key: 'file1',
            d222Name: '阶段考察纪实（二）-入党积极分子思想汇报',
            d222Code: '204',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否手写、内容是否合规',
                '2. 是否每三个月一次、培养联系人是否签字 ',
              ]
            }
          },
        ]
      },
      {
        id: '6',
        type: ['JJ_4', 'JJ_5'],
        name: '持续考察',
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '入党积极分子、发展对象培养教育考察登记表-阶段考察纪实',
            d222Code: '203',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否符合半年一次填写',
                '2. 内容（包含时事等）是否符合实际 ',
              ]
            }
          },
          {
            key: 'file1',
            d222Name: '阶段考察纪实-入党积极分子思想汇报',
            d222Code: '204',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否手写、内容是否合规',
                '2. 是否每三个月一次、培养联系人是否签字 ',
              ]
            }
          },
        ]
      },
      {
        id: '7',
        type: ['JJ_6'],
        name: '上级党委备案',
        formList: [
        ],
        value: [
          // {
          //   key: 'file',
          //   d222Name: '入党积极分子思想汇报',
          //   d222Code: '204',
          //   fileList: []
          // },
          {
            key: 'file1',
            d222Name: '讨论确定发展对象前听取意见记录（复印件）',
            d222Code: '301',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 征求意见人数不少于5人（两名培养联系人、党员、群众）',
              ]
            }
          },
          {
            key: 'file2',
            d222Name: '确定为发展对象的支委会会议记录（复印件）',
            d222Code: '302',
            fileList: []
          },
          // {
          //   key: 'file2',
          //   d222Name: '入党积极分子、发展对象培养教育考察登记表',
          //   d222Code: '203',
          //   fileList: []
          // },
        ]
      },
      {
        id: '8',
        type: ['JJ_7'],
        name: '上传材料',
        formList: [
          {
            label: '联系人是否为本组织人员',
            required: true,
            type: 'radio',
            key: 'hasStaffOrganization',
          },
          {
            label: '培养联系人',
            required: true,
            type: 'memselect',
            key: 'toobjContextMem'
          },
          {
            label: '确定发展对象时间',
            required: true,
            type: 'time',
            key: 'objectDate'
          },
        ],
        value: [
          {
            key: 'file1',
            d222Name: '发展党员工作有关部门征求意见情况（复印件）',
            d222Code: '403',
            fileList: []
          },
          // {
          //   key: 'file2',
          //   d222Name: '《入党积极分子、发展对象培养教育考察登记表》',
          //   d222Code: '203',
          //   fileList: []
          // },
          {
            key: 'file2',
            d222Name: '确定为发展对象相关公示材料及备案报告、批复',
            d222Code: '303',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '一、公示材料',
                '1. 公示时间是否满5个工作日',
                '2.涉及内容和时间信息是否符合要求',
                '二、备案请示',
                '1. 报告时间是否在公示期结束后',
                '2. 党支部、党总支（党委）意见明确',
              ]
            }
          },
          {
            key: 'file3',
            d222Name: '综合性政审报告和相关材料',
            d222Code: '304',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 本人、直系亲属与本人关系密切的主要社会关系是否进行政审',
                '2. 政治审查是否符合贵州省委组织部相关清单',
                '3. 是否由支部出具综合性政审报告，内容是否符合规定',
              ]
            },
            tip1: {
              title: '注意',
              type: 'error',
              show: true,
              info: '综合性政审报告和相关材料可以根据实际工作情况在此处上传；也可在支部审查之前完成政治审查上传'
            }
          },
          {
            key: 'file4',
            d222Name: '参加短期集中培训的结业证书',
            d222Code: '305',
            fileList: [],
            tip1: {
              title: '注意',
              type: 'error',
              show: true,
              info: '参加短期集中培训的结业证书可以根据实际工作情况在此处上传；也可在支部审查之前完成政治审查上传'
            },
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 培训时间是否为发展对象之后，接受预备党员之前',
                '2. 是否需重新培训',
              ]
            },
          },

        ]
      },
      {
        id: '9',
        type: ['FZ_1', 'FZ_2', 'FZ_4'],
        name: '支委会审查',
        formList: [
        ],
        value: [
          // {
          //   key: 'file',
          //   d222Name: '确定为发展对象的支委会会议记录（复印件）',
          //   d222Code: '302',
          //   fileList: []
          // },
          // {
          //   key: 'file2',
          //   d222Name: '确定为发展对象相关公示材料及备案报告、批复',
          //   d222Code: '303',
          //   fileList: []
          // },
          {
            key: 'file2',
            d222Name: '支委会拟接收预备党员审查会议记录和上报的材料',
            d222Code: '401',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 参会实到人数是否达到应到人数的五分之四以上',
                '2.会议主持、参会人员情况是否合规',
                '3. 参会人员与发言人员一致',
                '4. 会议记录是否规范',
              ]
            }
          },
        ]
      },
      {
        id: '10',
        type: ['FZ_3_1', 'FZ_3_2', 'FZ_3_3'],
        name: '基层党委审查',
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '入党积极分子、发展对象培养教育考察登记表',
            d222Code: '203',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否符合半年一次填写',
                '2. 内容（包含时事等）是否符合实际',
              ]
            }
          },
          {
            key: 'file1',
            d222Name: '基层党委预审会议记录及预审批复（复印件）',
            d222Code: '402',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '一、会议记录',
                '1. 实到人数是应到人数的三分之二',
                '2. 会议主持、参会人员情况是否合规',
                '3. 参会人员与发言人员一致',
                '4. 会议记录是否规范',
                '二、预审批复',
                '1. 预审批复时间（2019年5月后需经县级组织部门预审后，方可批复）',
              ]
            }
          },

        ]
      },
      {
        id: '11',
        type: ['FZ_5_1', 'FZ_5_2', 'FZ_5_3'],
        name: '支部大会讨论',
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '支部大会接收为中共预备党员的会议记录（复印件）',
            d222Code: '405',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 应到人数、实到人数（具有表决权的一半以上）是否符合规定',
                '2. 会议主持、参会人员情况是否合规',
                '3. 参会人员与发言人员一致',
                '4. 会议记录是否规范'
              ]
            }
          },
          {
            key: 'file1',
            d222Name: '接收为中共预备党员审批请示（复印件）',
            d222Code: '406',
            fileList: [],
            tip: {
              title: '档案注意事项',
              show: true,
              type: 'warning',
              info: [
                '1. 请示内容涉及信息是否与其他资料信息佐证',
              ]
            }
          },

        ]
      },
      {
        id: '12',
        type: ['FZ_6_1', 'FZ_6_2', 'FZ_6_3', 'FZ_6_4'],
        name: '基层党委审批',
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '中国共产党入党志愿书',
            d222Code: '404',
            fileList: [],
            tip: {
              title: '档案注意事项',
              show: true,
              type: 'warning',
              info: [
                '1. 封面盖章（党支部）',
                '2. 个人基本情况，从确定为预备党员时间起算',
                '3. 确定入党申请时间和积极分子时间，应与前期资料一致',
                '4. 涉及时事等内容是否符合实际'
              ]
            }
          },
          {
            key: 'file1',
            d222Name: '基层党委审批会议记录、会议纪要和批复（复印件）',
            d222Code: '407',
            fileList: [],
            tip: {
              title: '档案注意事项',
              show: true,
              type: 'warning',
              info: [
                '1. 应到人数、实到人数（具有表决权的一半以上）是否符合规定',
                '2. 会议主持、参会人员情况是否合规',
                '3. 参会人员与发言人员一致',
                '4. 会议记录是否规范',
              ]
            }
          },
        ]
      },

    ]
    const { canEdit, editType = '', id, code, developOrgCode, processNode, lastCode = '', isAdd = false } = e;
    const { fileObj } = this.state
    const { memDevelop: { basicInfo = {} } = {} } = this.props
    console.log(e, lastCode || processNode, 'iiiiiiiiiiiiiiiiii')
    let arr: any = uparr.find(i => i.type.includes(lastCode || processNode))
    let _step = isAdd ? 1 : 2
    if (basicInfo['notExistD222Code']?.length > 0) {
      let a = [
        {
          key: 'file3',
          d222Name: '综合性政审报告和相关材料',
          d222Code: '304',
          fileList: [],
          tip: {
            title: '档案注意事项',
            type: 'warning',
            show: true,
            info: [
              '1. 本人、直系亲属与本人关系密切的主要社会关系是否进行政审',
              '2. 政治审查是否符合贵州省委组织部相关清单',
              '3. 是否由支部出具综合性政审报告，内容是否符合规定',
            ]
          },
          // tip1: {
          //   title: '注意',
          //   type: 'error',
          //   show: true,
          //   info: '应在支部审查之前完成政治审查上传，此处暂时可不上传'
          // }
        },
        {
          key: 'file4',
          d222Name: '参加短期集中培训的结业证书',
          d222Code: '305',
          fileList: [],
          // tip1: {
          //   title: '注意',
          //   type: 'error',
          //   show: true,
          //   info: '应在支部审查之前完成政治审查上传，此处暂时可不上传'
          // },
          tip: {
            title: '档案注意事项',
            type: 'warning',
            show: true,
            info: [
              '1. 培训时间是否为发展对象之后，接受预备党员之前',
              '2. 是否需重新培训',
            ]
          },
        },
      ]
      let other: any = []
      basicInfo['notExistD222Code'].map(i => {
        let find = a.find(j => j.d222Code == i)
        if (find) {
          other.push(find)
        }
      });
      arr['value'] = [...other, ...arr['value']];
      _step = 2
    }
    console.log(arr, '打开弹窗显示arr')
    this.setState({
      modalVisible: true,
      canEdit,
      editType,
      fileObj: arr,
      step: _step,
      rowCode: code,
      developOrgCode,
      rowData: e,
      processNode: lastCode || processNode,
    });
  };

  destroy = () => {
    this.setState({
      hasLost: false,
      _basicInfo: {},
      d08Code: '5',
      hasAppointment: false,
      isOutSystem_state: false,
    });
    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        basicInfo: {},
      },
    });
  };
  isOutSystemOnChange = (value) => {
    // this.props.form.setFieldsValue({
    //   appliedOrgCode:undefined,
    //   appliedOrgName:undefined
    // });
    this.setState({ isOutSystem_state: value == '1' });
  };
  validatorHomeAddress = (rule, value, callback) => {
    if (value && value.length < 8) {
      callback('家庭住址长度不能少于8字符');
    }
    callback();
  };
  showMemsInfo = () => {
    const { tipMsg = {}, memDevelop: { basicInfo = {} } = {} } = this.props;
    const { getFieldDecorator, getFieldValue } = this.props.form;
    return (
      <Fragment>
        <Row>
          <Col span={12}>
            <FormItem
              label={formLabel('入党介绍人', tipMsg['topreIntroductionMem'])}
              {...formItemLayout}
            >
              <Input value={basicInfo['topreIntroductionMem']} disabled={true} />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label={formLabel('支部党员大会讨论通过时间', tipMsg['topreJoinOrgDate'])}
              {...formItemLayout}
            >
              {/* <Input value={basicInfo['topreJoinOrgDate'] ? moment(basicInfo['topreJoinOrgDate']).format('YYYY.MM.DD') : undefined} disabled={true}/> */}
              {getFieldDecorator('topreJoinOrgDate', {
                rules: [{ required: true, message: '支部党员大会讨论通过时间' }],
                initialValue: basicInfo['topreJoinOrgDate'],
              })(<Date />)}
            </FormItem>
          </Col>

          <Col span={12}>
            {/* <FormItem
              label={formLabel('加入党组织方式', tipMsg['joinOrgCode'])}
              {...formItemLayout}
            >
              <Input value={basicInfo['joinOrgCodeName']} disabled={true}/>
            </FormItem> */}
            {/* 编辑发展党员，本年发展党员这里，加入中共组织的类别（名字改成加入党组织方式），改成必填，非灰色 */}
            <FormItem label="加入党组织方式" {...formItemLayout}>
              {getFieldDecorator('joinOrgCode', {
                rules: [{ required: true, message: '加入党组织方式' }],
                initialValue: basicInfo['joinOrgCode'],
              })(
                <DictTreeSelect
                  backType={'object'}
                  codeType={'dict_d27'}
                  placeholder="请选择"
                  // noDraw={noDrawArr}
                  parentDisable={true}
                  initValue={basicInfo['joinOrgCode']}
                // onChange={this.joinOrgChange}
                // filter={(data={})=>{
                //    if(specialChecked) {
                //     let item=[]
                //   item=item.concat(data.filter(obj=>['11'].includes(obj['key'])))
                //     return item
                //    }
                //     return data
                // }}
                />,
              )}
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem label={formLabel('进入支部类型', tipMsg['d11Code'])} {...formItemLayout}>
              <Input value={basicInfo['d11Name']} disabled={true} />
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('入党志愿书编号', tipMsg['topreJoinBookNum'])}
              {...formItemLayout}
            >
              <Input value={basicInfo['topreJoinBookNum']} disabled={true} />
            </FormItem>
          </Col>
        </Row>
      </Fragment>
    );
  };
  d194Change = async (p) => {
    let newCode = p;
    if (typeof p === 'object') {
      newCode = p?.key || undefined;
    }
    const res = await normalList({
      data: {
        tableCode: 'ccp_unit',
        colCode: 'd194Code',
        compareColCode: 'd195Code',
        colValue: newCode,
      },
    });
    if (res.code == 0 && !_isEmpty(res.data)) {
      let key = Object.keys(res.data)?.[0];
      let name = res.data[key];
      this.setState({
        d195CodeSatate: key,
        d195CodeKey: moment().valueOf(),
      });
      this.props.form.setFieldsValue({
        d195Code: key,
        d195Name: name,
      });
    }
  };
  showGUOMINGJINGJI = () => {
    const { memDevelop: { basicInfo = {} } = {} } = this.props;
    // 人事关系是否在党组织关联单位内
    let val1 = this.props.form.getFieldValue('hasUnitStatistics');
    if (val1 == undefined) {
      val1 = basicInfo['hasUnitStatistics'];
    }
    // 人事关系所在单位是否省内单位
    let val = this.props.form.getFieldValue('hasUnitProvince');
    if (val == undefined) {
      val = basicInfo['hasUnitProvince'];
    }
    const hasChangedHasUnitStatistics =
      basicInfo['hasUnitStatistics'] != this.props.form.getFieldValue('hasUnitStatistics');
    const hasChangedHasUnitProvince =
      basicInfo['hasUnitProvince'] != this.props.form.getFieldValue('hasUnitProvince');
    let hasChangedStatisticalUnit = false;
    // 当第一个值（人事关系是否在党组织关联单位内）改变，直接隐藏
    let hasChanged = hasChangedHasUnitStatistics;
    // 当第一个值未变且为否 判断第二个值（人事关系所在单位是否省内单位）改变
    if (!hasChangedHasUnitStatistics && val1 == 0 && hasChangedHasUnitProvince) {
      hasChanged = true;
    }

    // 同"人事关系所在单位类别"显示逻辑一样
    let flag = val1 == 0 && val == 0;

    let unitInfo: any = {};
    // 有几种获取单位的情况
    // 1 人事关系是在党组织关联单位内
    if (val1 == 1) {
      //所在党支部
      let org = this.props.form.getFieldValue('orgCode');
      let d01Code: any = undefined;
      if (typeof org == 'string') {
        d01Code = basicInfo['d01Code'];
      } else {
        d01Code = _get(org, '[0].d01Code', undefined);
      }
      let isLianhe = (d01Code == '632' || d01Code == '932' || d01Code == '634') && d01Code;
      if (isLianhe) {
        //  1.2 是下拉框选择的单位
        let unitcode =
          this.props.form.getFieldValue('statisticalUnit') || basicInfo['statisticalUnit'];
        let find = this.state?.unitList?.find?.((it) => it.unitCode == unitcode);
        unitInfo = find;

        // 如果改变下拉框值，隐藏
        if (unitcode != basicInfo['statisticalUnit']) {
          hasChangedStatisticalUnit = true;
        }
      } else {
        //  1.1 只有一个单位
        unitInfo = {
          unitInformation: this.state.unitInformation,
          unitInformationCode: this.state.unitInformationCode,
          d04Code: this.state.unitInformationD04Code,
          isLegal: this.state.isLegal,
          d194Code: this.state.unitInformationd194Code,
        };
      }
    }

    // 2 中间交换区
    if (val1 == 0 && val == 1) {
      let code = this.props.form.getFieldValue('middleUnitCode');
      if (typeof code == 'string') {
        unitInfo = {
          middleUnitCode: this.props.form.getFieldValue('middleUnitCode'),
          middleUnitName: this.props.form.getFieldValue('middleUnitName'),
          d04Code: this.props.form.getFieldValue('d04Code'),
        };
      } else {
        unitInfo = code;
      }
    }

    //在加上额外判断条件
    // 1、当党员的党组织（单位属性为行政村、是法人单位、国民经济行业为村民自治组织或社区居民自治组织）三个条件同时满足
    // 2、当党员的党组织（单位属性为教育大类中331、是法人单位、国国民经济行业是普通高等教育）三个条件同时满足
    // 3、当党员的党组织（是教育大类中332、333、334、335；是法人单位、国国民经济行业是除普通高等教育以外的 中等教育，
    // 普通高中教育，初等教育，学前教育，）三个条件同时满足

    const d04 = unitInfo?.d04Code || '';
    const isLegal = unitInfo?.isLegal || 0;
    const d194Code = unitInfo?.d194Code || '';

    const find1 = (d04 == '923' || d04 == '922') && (d194Code == 'S9620' || d194Code == 'S9610');
    const find2 = d04?.startsWith('331') && d194Code.startsWith('P8341');
    const find3 =
      (d04?.startsWith('332') ||
        d04?.startsWith('333') ||
        d04?.startsWith('334') ||
        d04?.startsWith('335')) &&
      (d194Code.startsWith('P833') ||
        d194Code.startsWith('P8334') ||
        d194Code.startsWith('P832') ||
        d194Code.startsWith('P831'));

    let flag2 = find1 || find2 || find3;

    let finalFlag = flag2 || flag;

    // 如果有改变2个是否，且最后不是2个否
    if (hasChangedStatisticalUnit || hasChanged) {
      if (!flag) {
        finalFlag = false;
      }
    }

    // 增加判断工作岗位d09, 13-务工经商人员  国民经济一直显示可选
    const d09Code = this.props.form.getFieldValue('d09Code');
    let _keyd09 = typeof d09Code === 'string' ? d09Code : d09Code?.key;
    if (_keyd09 == '13') {
      finalFlag = true;
    }

    return finalFlag;
  };
  fileChange = ({ fileList, file, event, }: any, item: any, index: number) => {
    console.log(fileList, 'fileListfileListfileList')
    const { prevent, fileObj } = this.state;
    if (prevent) {
      this.setState({
        [`fileList${index}`]: []
      })
      return false
    };

    if (file.status === 'done') {
      const { response: { code = 500, message = '' } = {} } = file || {};
      if (code !== 0) {
        Tip.error('操作提示', message);
        fileList.pop();
      } else {

        this.getfu(fileList, item, index)

      }
    } else if (file.status === 'error') {
      Tip.error('操作提示', '上传失败');
      this.setState({
        fileloading: false
      })
    }
    let _value: any = [];
    fileObj.value.forEach((item, is) => {
      if (is == index) {
        // if (item['tip1']) {
        //   item['tip1']['show'] = false
        // }
        if (item['tip']) {
          item['tip']['show'] = false
        }
      }
      _value.push(item)
    })
    this.setState({
      [`fileList${index}`]: fileList.map(i => {
        i['percent'] = (i['percent'] * 1).toFixed(0)
        return i
      }),
      fileObj: {
        ...fileObj,
        value: _value
      }
    })
  }
  getfu = (fileList, item, index) => {
    const { fileObj: { value = [] } = {}, step } = this.state;
    let _value = value
    let done = false;
    let anum = 0
    if (time) clearInterval(time)
    time = setInterval(() => {
      fileList.forEach((file) => {
        console.log(file, 'filefile')
        const { response: { data = [], code = 500 } = {} } = file
        if (code !== 0) {
          done = false
          anum++
          return
        }
      })
      if (anum == 0) {
        let arr: any = []
        fileList.map((i, index) => {
          const { response: { data = [] } = {} } = i
          data.map(j => {
            let obj = {
              path: j.url,
              ...j
            }
            arr.push(obj)
          })
        })
        let find = _value.map(i => {
          if (i.key == item.key) {
            i.fileList = [...i.fileList, ...arr].map((i, k) => {
              return {
                ...i,
                sort: k + 1
              }
            })
          }
          return i
        })

        this.setState({
          fileObj: {
            ...this.state.fileObj,
            value: find
          },
          renderfile: false,
          [`fileList${index}`]: [],
          fileloading: false
        });

        clearInterval(time)
        Tip.success('操作提示', '上传成功');
      }
    }, 3000)

  }

  getbase64 = (item) => {
    let arr: any = []
    let _list: any = []
    let _list1: any = []

    const { fileObj: { value = [] } = {}, step } = this.state;
    let _value = value
    _value.map((f) => {
      if (f.key == item.key) {
        // i.fileList = [...i.fileList, ...fileData]
        _list = [...f.fileList].filter(i => !i.previewPath)
        _list1 = [...f.fileList].filter(i => i.previewPath)
      }
    })
    const promise = _list.map(async (i) => {
      arr.push(i)
      const result = await shareLink({ path: i?.url })
      return result
    })
    Promise.all(promise).then(res => {
      arr.map((j, k) => {
        j['previewPath'] = res[k]?.data
        j['path'] = j['url']
        return j
      })

      let find = _value.map(i => {
        if (i.key == item.key) {
          i.fileList = [..._list1, ...arr]
        }
        return i
      })

      this.setState({
        fileObj: {
          ...this.state.fileObj,
          value: find
        },
      });


    })
  }
  beforeUpload = (file, fileList, index) => {
    const { prevent } = this.state;
    message.destroy();


    let imgs: any = []
    let pdfs: any = []
    fileList.forEach(i => {
      if (i.type.startsWith('image')) {
        imgs.push(i)
      }
      if (i.type.startsWith('application/pdf')) {
        pdfs.push(i)
      }
    })
    if (imgs.length > 0 && pdfs.length > 0) {
      message.error('请勿同时上传pdf和图片')
      this.setState({
        prevent: true
      })
      return false
    }
    if (imgs.length > 10) {
      message.error('一次性最多上传10个文件')
      this.setState({
        prevent: true,
      })
      return false
    }
    if (pdfs.length > 1) {
      message.error('一次性最多上传1个pdf文件')
      this.setState({
        prevent: true,
      })
      return false
    }
    let fileSize: number = file['size'] / 1024 / 1024;
    if (fileSize >= 50) {
      message.error('请上传小于50M的文件!');
      this.setState({
        prevent: true
      })
      return false
    }
    this.setState({
      uploadloading: true,
      fileloading: true,
      prevent: false
    })

    return convertToWebpBeforeUpload(file)

  }
  delFile = () => {
    console.log('delFile')
  }
  hasMemOnChange = (val) => {
    this.props.form.setFieldsValue({ toactiveContextPerson: undefined });
    this.setState({ hasMemValue: val });
  };
  getFormItem = (item: any) => {
    const { hasMemValue } = this.state
    const {
      form,
    } = this.props;
    const { getFieldDecorator } = form;
    // let node: any = null
    // switch (type) {
    //   case 'radio':
    //     node = <Switch checkedChildren="是" unCheckedChildren="否" defaultChecked />
    //     break;
    //   case 'memselect':
    //     node = <MemSelect checkType={'checkbox'} placeholder="请选择党员" />
    //     break;
    //   case 'time':
    //     node = <Date />
    //     break;
    // }
    // return node
    switch (item.type) {
      case 'radio':
        return (
          <FormItem label={item.label} {...formItemLayout3}>
            {getFieldDecorator(`${item.key}`, {
              rules: [
                { required: item.required, message: `请输入${item.label}}` },
              ],
              // initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['name'],
            })(<Switch checkedChildren="是" unCheckedChildren="否" onChange={this.hasMemOnChange}
              checked={hasMemValue} />)}
          </FormItem>

        )
      case 'memselect':
        return (
          <FormItem label={item.label} {...formItemLayout3}>
            {getFieldDecorator(`${item.key}`, {
              rules: [
                { required: item.required, message: `请输入${item.label}}` },
              ],
              // initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['name'],
            })(<MemSelect checkType={'checkbox'} placeholder="请选择党员" />)}
          </FormItem>

        )
      case 'time':
        return (
          <FormItem label={item.label} {...formItemLayout3}>
            {getFieldDecorator(`${item.key}`, {
              rules: [
                { required: item.required, message: `请输入${item.label}}` },
              ],
              // initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['name'],
            })(<Date />)}
          </FormItem>

        )
    }
  }
  check = (obj, index) => {
    console.log(obj)
    const { checkList = [] } = this.state;
    let arr: any = []
    if (obj?.id) {
      if (checkList.includes(obj?.id)) {
        arr = checkList.filter(i => i !== obj?.id)
      } else {
        arr = [...checkList, obj?.id]
      }
      this.setState({
        checkList: [...new Set([...arr])],
        fileIndex: index
      })
    }
  }
  del = (index) => {
    const { fileObj: { value = [] } = {}, checkList, fileIndex, step } = this.state;
    let _fileList = this.state[`fileList${index}`]

    let _value = value
    let o = _value[index].fileList.filter(j => !checkList.includes(j.id))
    let f = _fileList.filter(i => !checkList.includes(i.response.data[0].id))
    _value[index].fileList = o

    this.setState({
      fileObj: {
        ...this.state.fileObj,
        value: _value
      },
      checkList: [],
      [`fileList${index}`]: f
    })

  }
  isdisabled = (item) => {
    const { fileObj, checkList, fileIndex } = this.state;
    let f = item.fileList.filter(i => checkList.includes(i.id))
    return f.length < 1
  }
  toswitch = (key) => {
    const { fileObj: { value = [] } = {}, previewImage = '' } = this.state
    // let arr = allList.filter(i=>i['isDelete']!=1)
    let arr: any = []
    let _value = value
    _value.map(i => {
      let f = i.fileList.find(j => j.previewPath == previewImage)
      if (f) {
        arr = i.fileList
      }
    })
    let n = arr.filter(i => i['isDelete'] != 1)
    let findIndex = n.findIndex(i => i.previewPath == previewImage)
    if (key == 'up') {
      if (findIndex == 0) {
        message.error('已经是第一个了')

      } else {
        this.setState({
          previewImage: n[findIndex - 1]?.previewPath
        })
      }
    } else {
      if (findIndex == n.length - 1) {
        message.error('已经是最后一个了')
      } else {
        this.setState({
          previewImage: n[findIndex + 1]?.previewPath
        })
      }
    }
  }
  dragstarts = (e, item) => {
    // 设置拖动的数据
    e.dataTransfer.setData('text/plain', JSON.stringify(item));
    this.setState({
      sorto: item,
      isDragging: true
    })
  }
  drops = (e, item) => {
    e.preventDefault();
    const { sorto, fileObj: { value = [] } = {}, step } = this.state;
    let arr = []

    let _value = value
    _value.map((i, index) => {
      if (index == sorto['old']) {
        let newrr = i.fileList.filter(i => i.id != sorto.id)
        newrr.splice(item.sort - 1, 0, sorto)
        if (newrr) {
          newrr.map((j, index) => {
            j['sort'] = index + 1
            return j
          })
        }
        i['fileList'] = newrr
      }
      arr.push(i)
      // return i
    })

    this.setState({
      fileObj: {
        ...this.state.fileObj,
        value: arr
      },
      isDragging: false
    })


  }
  setText = () => {
    const { step, fileObj } = this.state;
    let str = ''
    if (step == 1) {
      str = '下一步'
    } else {
      str = '保存'
    }
    return str
  }
  setBtn = () => {
    const { step, rowData, fileObj, baseformdata } = this.state;
    const {
      memDevelop: { basicInfo = {} } = {},
    } = this.props;
    this.setState({
      step: 1,
      baseformdata: { ...basicInfo, ...baseformdata }
    })
  }
  setTip = (index, k) => {
    const { fileObj } = this.state;
    let _value: any = [];
    fileObj.value.forEach((item, is) => {
      if (is == index) {
        item['tip']['show'] = !item['tip']['show']
      }
      _value.push(item)
    })
    this.setState({
      fileObj: {
        ...fileObj,
        value: _value
      }
    })
  }
  closeTip = (index, k) => {
    const { fileObj } = this.state;
    let _value: any = [];
    fileObj.value.forEach((item, is) => {
      if (is == index) {
        item['tip']['show'] = false
      }
      _value.push(item)
    })
    this.setState({
      fileObj: {
        ...fileObj,
        value: _value
      }
    })
  }
  handOk = async () => {
    const { prevent, fileObj, fileListIndex, previewImage } = this.state;
    const { location: { pathname = '' } = {} } = _history
    let file = this['imageCropperRef'].geturl()
    this['imageCropperRef'].clears()
    this.setState({
      cropperVisible: false
    })
    const formData = new FormData();
    formData.append('file', file);
    let str = ''
    switch (pathname) {
      case '/developMem/zy/apply':
        str = 'rdsqr';
        break;
      case '/developMem/zy/active':
        str = 'jjfz';
        break;
      case '/developMem/zy/object':
        str = 'fzdx';
        break;

    }
    try {
      const response = await fetch(`/api/minio/upload?model=${str}`, {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: sessionStorage.getItem('token') || '',
          dataApi: sessionStorage.getItem('dataApi') || '',
        }
      });

      if (response.ok) {
        const { code = 500, data = [] } = await response.json();

        let _fileList = fileObj.value[fileListIndex].fileList.map(i => {
          if (i.previewPath == previewImage) {
            i.path = data[0].url
            i.previewPath = data[0].previewPath
          }
          return i
        })
        fileObj.value[fileListIndex].fileList = _fileList
        console.log(fileObj.value, _fileList, '1212121212121')
        this.setState({
          fileObj: {
            ...fileObj,
          }
        })

        console.log('Success:', data);
      } else {
        console.error('Error:', response.statusText);
      }
    } catch (error) {
      console.error('Error during file upload:', error);
    }
  }
  // 添加新的处理滚动的方法
  handleDragOver = (e, containerRef) => {
    e.preventDefault();

    if (!this.state.isDragging || !containerRef) return;

    const container = containerRef;
    const containerRect = container.getBoundingClientRect();
    const mouseY = e.clientY;

    // 计算滚动速度 - 离边缘越近滚动越快
    const calculateSpeed = (distance, maxSpeed = 15) => {
      // 只在靠近边缘50px内触发滚动
      const threshold = 50;
      if (distance > threshold) return 0;

      // 距离边缘越近，速度越快
      return Math.ceil((threshold - distance) / 5);
    };

    // 鼠标在容器顶部区域，向上滚动
    if (mouseY < containerRect.top + 50) {
      const distance = mouseY - containerRect.top;
      const speed = calculateSpeed(distance);
      if (speed > 0) {
        container.scrollTop -= speed;
      }
    }

    // 鼠标在容器底部区域，向下滚动
    if (mouseY > containerRect.bottom - 50) {
      const distance = containerRect.bottom - mouseY;
      const speed = calculateSpeed(distance);
      if (speed > 0) {
        container.scrollTop += speed;
      }
    }
  }

  componentDidMount() {
    document.addEventListener('dragover', this.handleGlobalDragOver);
  }

  componentWillUnmount() {
    document.removeEventListener('dragover', this.handleGlobalDragOver);
  }

  handleGlobalDragOver = (e) => {
    if (!this.state.isDragging) return;

    // 为所有图片容器添加滚动逻辑
    Object.keys(this.imgContainerRefs).forEach(key => {
      const containerRef = this.imgContainerRefs[key];
      if (containerRef) {
        this.handleDragOver(e, containerRef);
      }
    });
  }


  render(): React.ReactNode {
    const {
      form,
      memDevelop: { basicInfo = {} } = {},
      loading: { effects = {} } = {},
      tipMsg = {},

    } = this.props;
    const { getFieldDecorator } = form;
    const {
      d08Code,
      hasAppointment,
      hasLost,
      area,
      modalVisible,
      isOutSystem_state,
      canEdit,
      d154CodeNoDraw = [],
      fileList = [],
      step,
      fileObj,
      previewVisible = false,
      previewImage = '',
      rowData = {},
      checkList,
      isup = true,
      subobj,
      baseformdata = {},
      uploadloading,
      filepercent = 0,
      renderfile = false,
      fileloading = false,
      cropperVisible = false
    } = this.state;
    const { location: { pathname = '' } = {} } = _history
    let str = ''
    switch (pathname) {
      case '/developMem/zy/apply':
        str = 'rdsqr';
        break;
      case '/developMem/zy/active':
        str = 'jjfz';
        break;
      case '/developMem/zy/object':
        str = 'fzdx';
        break;

    }
    const props: any = {
      action: `/api/minio/upload?model=${str}`,
      // accept: '.jpg,.png,.jpeg,.webp,.pdf,.tiff',
      accept: '.jpg,.png,.jpeg,.webp,.pdf',
      headers: {
        Authorization: sessionStorage.getItem('token') || '',
        dataApi: sessionStorage.getItem('dataApi') || '',
      },
    };
    console.log(step, fileObj, '怎么可能')
    return (
      <Fragment>
        <Modal
          zIndex={1}
          title={
            step == 1
              ? `新增${this.state.org.name}组织发展党员`
              :
              <Space>
                <div>{fileObj['name']}</div>
                <div className={style.fzdy} onClick={() => {
                  // this['MembersWorkProcedures'].open('发展党员规程')
                  window.open('/archivesAdministration/membersWorkProcedures')
                }}>发展党员规程</div>
                <div className={style.flexboxTip} >温馨提示：拖动图片可以进行排序</div>
              </Space>
          }
          destroyOnClose
          visible={modalVisible}
          onCancel={this.cancel}
          // onOk={this.submit}
          width={rowData['processNode'] == 'JJ_7' ? '1600px' : '1400px'}
          footer={
            canEdit
              ? [
                <React.Fragment>
                  {

                    (step == 3 || (rowData['processNode'] == 'RD_1'&& step == 2)) &&
                    <Button onClick={() => this.setBtn()}>上一步</Button>
                  }
                </React.Fragment>,
                <Button onClick={this.cancel}>取消</Button>,
                
                <React.Fragment>
                  {

                    step == 1 ? <Popconfirm placement="topRight" title="姓名和身份证一旦录入，只允许一次修改，修改后不可再进行修改。请注意核实正确！" onConfirm={this.submit} okText="确定" cancelText="取消">
                      <Button type="primary">
                        {this.setText()}
                      </Button>
                    </Popconfirm> : <Button type="primary" onClick={this.submit} loading={fileloading}>
                    {/* {step == 1 ? '下一步' : '保存'} */}
                    {this.setText()}
                  </Button>
                  }
              </React.Fragment>,
              ]
              : null
          }
        // confirmLoading={effects['memAbroad/save']}
        >
          <div style={{ height: '600px', overflow: 'auto' }}>

            <div className={style.flex}>
              {
                step == 1 && <Row style={{ pointerEvents: canEdit ? 'auto' : 'none' }}>
                  <Col span={12}>
                    <FormItem label={formLabel('人员姓名', tipMsg['name'])} {...formItemLayout}>
                      {getFieldDecorator('name', {
                        rules: [
                          { required: true, message: '请输人员姓名' },
                          { validator: this.nameValidator },
                        ],
                        initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['name'],
                      })(<Input placeholder={'请输人员姓名'} />)}
                    </FormItem>
                  </Col>
                  {getFieldDecorator('code', { initialValue: baseformdata['code'] })(
                    <div style={{ display: 'none' }}>123</div>,
                  )}
                  <Col span={12}>
                    <FormItem label={formLabel('性别', tipMsg['sexCode'])} {...formItemLayout}>
                      {getFieldDecorator('sexCode', {
                        rules: [{ required: true, message: '请选择性别' }],
                        initialValue: _isEmpty(baseformdata) ? '1' : baseformdata['sexCode'],
                      })(
                        <RadioGroup>
                          <Radio value={'1'}>男</Radio>
                          <Radio value={'0'}>女</Radio>
                        </RadioGroup>,
                      )}
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem label={formLabel('身份证号', tipMsg['idcard'])} {...formItemLayout}>
                      {getFieldDecorator('idcard', {
                        rules: [
                          { required: true, message: '请输入身份证号' },
                          { validator: this.validatorIdcard },
                        ],
                        initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['idcard'],
                      })(<Input placeholder={'请输入身份证'} max={18} onBlur={this.getIDinfo} />)}
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem label={formLabel('出生日期', tipMsg['birthday'])} {...formItemLayout}>
                      {getFieldDecorator('birthday', {
                        rules: [
                          { required: true, message: '请选择出生日期' },
                          { validator: this.TemporalOrder5 },
                        ],
                        initialValue: !_isNumber(baseformdata['birthday']) ? undefined : moment(baseformdata['birthday']),
                        // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
                      })(
                        <Date
                          disabledDate={this.disabledTomorrow}
                          disabled={!canEdit}
                          onChange={() => {
                            this.props.form.setFieldsValue({
                              politicsCode: undefined,
                            });
                            this['politicsCode'].clearAll();
                          }}
                        />,
                      )}
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem label={formLabel('籍贯', tipMsg['d48Code'])} {...formItemLayout}>
                      {getFieldDecorator('d48Code', {
                        rules: [{ required: true, message: '请选择籍贯' }],
                        initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['d48Code'],
                      })(
                        <DictArea
                          placeholder={'所在地区'}
                          onChange={(val, obj) => this.props.form.setFieldsValue({ d48Name: obj.name })}
                        />,
                      )}
                      {getFieldDecorator('d48Name')(<Input style={{ display: 'none' }} />)}
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem label={formLabel('民族', tipMsg['d06Code'])} {...formItemLayout}>
                      {getFieldDecorator('d06Code', {
                        rules: [{ required: true, message: '请选择民族' }],
                        initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['d06Code'],
                      })(
                        <DictTreeSelect
                          initValue={_isEmpty(baseformdata) ? undefined : baseformdata['d06Code']}
                          codeType={'dict_d06'}
                          placeholder={'党员民族'}
                          parentDisable={true}
                          backType={'object'}
                        />,
                      )}
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem label={formLabel('当前工作岗位', tipMsg['d09Code'])} {...formItemLayout}>
                      {getFieldDecorator('d09Code', {
                        rules: [{ required: true, message: '请选择当前工作岗位' }],
                        initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['d09Code'],
                      })(
                        <DictTreeSelect
                          initValue={_isEmpty(baseformdata) ? undefined : baseformdata['d09Code']}
                          codeType={'dict_d09'}
                          placeholder={'当前工作岗位'}
                          // itemsDisabled={["11"]}
                          parentDisable={true}
                          onChange={(e) => {
                            const { key = '' } = e || {};
                            if (key.startsWith('3')) {
                              form.setFieldsValue({
                                d07Code: undefined,
                              });
                              this['d07Code'].clearAll();
                            }
                          }}
                          backType={'object'}
                        />,
                      )}
                    </FormItem>
                  </Col>
                  {
                    // 当工作岗位是3学生开头的时候 增加一个时间字段填写：入学时间
                    (function (_this) {
                      const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                      let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                      if (_key && _key.startsWith('3')) {
                        return (
                          <Fragment>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('入学时间', tipMsg['enterSchoolDate'])}
                                {...formItemLayout}
                              >
                                {getFieldDecorator('enterSchoolDate', {
                                  rules: [{ required: true, message: '请输入 入学时间' }],
                                  initialValue:
                                    baseformdata['enterSchoolDate'] != undefined
                                      ? moment(baseformdata['enterSchoolDate'])
                                      : undefined,
                                })(
                                  <Date
                                    startTime={'1910.01.01'}
                                    disabledDate={_this.disabledTomorrow}
                                  />,
                                )}
                              </FormItem>
                            </Col>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('是否需要自动计算年级', tipMsg['hasCalculationGrade'])}
                                {...formItemLayout}
                              >
                                {getFieldDecorator('hasCalculationGrade', {
                                  rules: [{ required: true, message: '是否需要自动计算年级' }],
                                  initialValue: baseformdata['hasCalculationGrade'] === 0 ? 0 : 1,
                                })(
                                  <Select style={{ width: '100%' }}>
                                    <Select.Option value={1}>是</Select.Option>
                                    <Select.Option value={0}>否</Select.Option>
                                  </Select>,
                                )}
                              </FormItem>
                            </Col>
                          </Fragment>
                        );
                      }
                    })(this)
                  }
                  <Col span={12}>
                    {
                      // 岗位和学历的校验判断
                      (function (_this) {
                        const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                        let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;

                        let itemsDisabled: Array<string> = [];
                        if (_key) {
                          if (_key.startsWith('31')) {
                            // 研究生
                            // itemsDisabled = ['11','12','13','14'];
                            itemsDisabled = [];
                          }
                          if (_key.startsWith('32')) {
                            // 本科
                            itemsDisabled = ['11', '12', '13', '14', '21', '22', '23'];
                          }
                          if (_key.startsWith('33')) {
                            // 专科
                            itemsDisabled = [
                              '11',
                              '12',
                              '13',
                              '14',
                              '21',
                              '22',
                              '23',
                              '31',
                              '32',
                              '33',
                              '34',
                            ];
                          }
                          if (_key == '34') {
                            // 中专
                            itemsDisabled = [
                              '11',
                              '12',
                              '13',
                              '14',
                              '21',
                              '22',
                              '23',
                              '31',
                              '32',
                              '33',
                              '34',
                              '4',
                              '5',
                              '6',
                            ];
                          }
                          if (_key == '35') {
                            // 高中
                            itemsDisabled = [
                              '11',
                              '12',
                              '13',
                              '14',
                              '21',
                              '22',
                              '23',
                              '31',
                              '32',
                              '33',
                              '34',
                              '4',
                              '5',
                              '6',
                            ];
                          }
                          if (_key == '36') {
                            // 技工
                            itemsDisabled = [
                              '11',
                              '12',
                              '13',
                              '14',
                              '21',
                              '22',
                              '23',
                              '31',
                              '32',
                              '33',
                              '34',
                              '4',
                              '5',
                              '6',
                            ];
                          }
                        }
                        return (
                          <FormItem
                            label={formLabel('当前学历情况', tipMsg['d07Code'])}
                            {...formItemLayout}
                          >
                            {getFieldDecorator('d07Code', {
                              rules: [{ required: true, message: '请选择当前学历情况' }],
                              initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['d07Code'],
                            })(
                              <DictTreeSelect
                                initValue={_isEmpty(baseformdata) ? undefined : baseformdata['d07Code']}
                                codeType={'dict_d07'}
                                placeholder={'当前学历情况'}
                                ref={(e) => (_this['d07Code'] = e)}
                                parentDisable={true}
                                itemsDisabled={itemsDisabled}
                                backType={'object'}
                              />,
                            )}
                          </FormItem>
                        );
                      })(this)
                    }
                  </Col>
                  {/* 选择无和其他知识分子只能选一个，选其他的可以选多个 */}
                  {
                    // 工作岗位 满足以下代码时，不显示知识分子情况（1开头，3开头，514开头，515开头，516开头）。
                    (function (_this) {
                      const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                      let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                      if (
                        !(
                          `${_key}`.startsWith('1') ||
                          `${_key}`.startsWith('3') ||
                          `${_key}`.startsWith('514') ||
                          `${_key}`.startsWith('515') ||
                          `${_key}`.startsWith('516')
                        )
                      ) {
                        return (
                          <Col span={12}>
                            <FormItem
                              label={formLabel('知识分子情况', tipMsg['d154Code'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('d154Code', {
                                rules: [{ required: true, message: '请选择知识分子情况' }],
                                initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['d154Code'],
                              })(
                                <DictSelect
                                  initValue={
                                    _isEmpty(baseformdata)
                                      ? undefined
                                      : baseformdata['d154Code']
                                        ? baseformdata['d154Code'].split(',')
                                        : undefined
                                  }
                                  codeType={'dict_d154'}
                                  placeholder={'知识分子情况'}
                                  backType={'object'}
                                  mode="multiple"
                                  noDraw={d154CodeNoDraw}
                                  onChange={(e) => {
                                    if (!_isEmpty(e)) {
                                      if (e[e.length - 1]?.key == '0') {
                                        _this.setState({
                                          d154CodeNoDraw: [
                                            '1',
                                            '2',
                                            '3',
                                            '4',
                                            '5',
                                            '6',
                                            '7',
                                            '8',
                                            '9',
                                            'A',
                                            'B',
                                          ],
                                        });
                                      } else if (e[e.length - 1]?.key == 'B') {
                                        _this.setState({
                                          d154CodeNoDraw: [
                                            '0',
                                            '1',
                                            '2',
                                            '3',
                                            '4',
                                            '5',
                                            '6',
                                            '7',
                                            '8',
                                            '9',
                                            'A',
                                          ],
                                        });
                                      } else if (
                                        ['1', '2', '3', '4', '5', '6', '7', '8', '9', 'A'].includes(
                                          e[e.length - 1]?.key,
                                        )
                                      ) {
                                        _this.setState({
                                          d154CodeNoDraw: ['0', 'B'],
                                        });
                                      } else {
                                        _this.setState({
                                          d154CodeNoDraw: [],
                                        });
                                      }
                                    } else {
                                      _this.setState({
                                        d154CodeNoDraw: [],
                                      });
                                    }
                                  }}
                                />,
                              )}
                            </FormItem>
                          </Col>
                        );
                      }
                    })(this)
                  }
                  {
                    // 在读大学生
                    (function (_this) {
                      const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                      let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                      if (
                        `${_key}`.startsWith('31') ||
                        `${_key}`.startsWith('32') ||
                        `${_key}`.startsWith('33')
                      ) {
                        return (
                          <Fragment>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('在读院校', tipMsg['readingCollege'])}
                                {...formItemLayout}
                              >
                                {getFieldDecorator('readingCollege', {
                                  rules: [{ required: true, message: '在读院校' }],
                                  initialValue: _isEmpty(baseformdata)
                                    ? undefined
                                    : baseformdata['readingCollege'],
                                })(<Input placeholder={'在读院校'} />)}
                              </FormItem>
                            </Col>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('在读专业', tipMsg['readingProfessionalCode'])}
                                {...formItemLayout}
                              >
                                {getFieldDecorator('readingProfessionalCode', {
                                  rules: [{ required: true, message: '在读专业' }],
                                  initialValue: _isEmpty(baseformdata)
                                    ? undefined
                                    : baseformdata['readingProfessionalCode'],
                                })(
                                  <DictTreeSelect
                                    initValue={
                                      _isEmpty(baseformdata)
                                        ? undefined
                                        : baseformdata['readingProfessionalCode']
                                    }
                                    codeType={'dict_d88'}
                                    placeholder={'在读专业'}
                                    parentDisable={true}
                                    backType={'object'}
                                  />,
                                )}
                              </FormItem>
                            </Col>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('学制', tipMsg['educationalSystem'])}
                                {...formItemLayout}
                              >
                                {getFieldDecorator('educationalSystem', {
                                  rules: [{ required: true, message: '学制' }],
                                  initialValue: _isEmpty(baseformdata)
                                    ? undefined
                                    : baseformdata['educationalSystem'],
                                })(<InputNumber placeholder={'学制'} style={{ width: '100%' }} />)}
                              </FormItem>
                            </Col>
                          </Fragment>
                        );
                      } else {
                        // 全程保持在读院校信息，好让form回显
                        return (
                          <div style={{ display: 'none' }}>
                            {getFieldDecorator('readingCollege', {
                              rules: [{ required: false, message: '' }],
                              initialValue: baseformdata['readingCollege'],
                            })(<Input style={{ display: 'none' }} disabled />)}
                          </div>
                        );
                      }
                    })(this)
                  }
                  {
                    // 前后端增加毕业院校、毕业专业（高学历党员填写）
                    (function (_this: any) {
                      const { d07Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                      const { birthday = undefined, applyDate = undefined } =
                        _this?.props?.form?.getFieldsValue() || {};
                      let bigThan28 = true;
                      if (
                        birthday &&
                        applyDate &&
                        moment(applyDate) <= moment(birthday).add(28, 'years')
                      ) {
                        bigThan28 = false;
                      }
                      if (birthday && !applyDate && moment() <= moment(birthday).add(28, 'years')) {
                        bigThan28 = false;
                      }
                      let _key = typeof d07Code === 'string' ? d07Code : d07Code?.key;
                      if (!['4', '5', '6', '7', '8', '9'].includes(_key)) {
                        return (
                          <Fragment>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('毕业院校', tipMsg['byyx'])}
                                {...formItemLayout}
                              >
                                {getFieldDecorator('byyx', {
                                  rules: [{ required: false, message: '毕业院校' }],
                                  initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['byyx'],
                                })(<Input placeholder={'毕业院校'} />)}
                              </FormItem>
                            </Col>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('毕业专业', tipMsg['d88Code'])}
                                {...formItemLayout}
                              >
                                {getFieldDecorator('d88Code', {
                                  rules: [{ required: false, message: '毕业专业' }],
                                  initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['d88Code'],
                                })(
                                  <DictTreeSelect
                                    initValue={_isEmpty(baseformdata) ? undefined : baseformdata['d88Code']}
                                    codeType={'dict_d88'}
                                    placeholder={'毕业专业'}
                                    parentDisable={true}
                                    backType={'object'}
                                  />,
                                )}
                              </FormItem>
                            </Col>
                          </Fragment>
                        );
                      }
                    })(this)
                  }
                  {(function (_this: any) {
                    const { birthday = undefined, applyDate = undefined } =
                      _this?.props?.form?.getFieldsValue() || {};
                    let bigThan28 = true;
                    if (
                      birthday &&
                      applyDate &&
                      moment(applyDate) <= moment(birthday).add(28, 'years')
                    ) {
                      bigThan28 = false;
                    }
                    if (birthday && !applyDate && moment() <= moment(birthday).add(28, 'years')) {
                      bigThan28 = false;
                    }
                    return (
                      <Fragment>
                        <Col span={12}>
                          <FormItem
                            label={formLabel('政治面貌', tipMsg['politicsCode'])}
                            {...formItemLayout}
                          >
                            {getFieldDecorator('politicsCode', {
                              rules: [{ required: true, message: '政治面貌' }],
                              initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['politicsCode'],
                            })(
                              <DictSelect
                                backType={'object'}
                                ref={(e) => (_this['politicsCode'] = e)}
                                initValue={
                                  _isEmpty(baseformdata)
                                    ? undefined
                                    : baseformdata['politicsCode']
                                      ? baseformdata['politicsCode'].split(',')
                                      : undefined
                                }
                                codeType={'dict_d89'}
                                placeholder="请选择"
                                mode={'multiple'}
                                filter={(data) => {
                                  let arr = data.filter((it) => it.key !== '14');
                                  if (bigThan28) {
                                    return arr.filter((it) => it.key !== '03');
                                  }
                                  return arr;
                                }}
                              />,
                            )}
                          </FormItem>
                        </Col>
                      </Fragment>
                    );
                  })(this)}
                  {
                    // 当工作岗位是工勤岗位（例如:岗位名称中的工勤岗位和工勤技能人员这类岗位），才弹出是否农民工和是否劳务派遣工的信息选择项
                    // 民办非企业工勤技能人员、社会团体工勤技能人员，选择以后都需要增加是否劳务派遣工和是否农民工
                    (function (_this) {
                      const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                      let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                      if (
                        `${_key}`.startsWith('016') ||
                        `${_key}`.startsWith('025') ||
                        `${_key}` == '0313' ||
                        `${_key}` == '0323' ||
                        `${_key}` == '0333'
                      ) {
                        return (
                          <Fragment>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('是否劳务派遣工', tipMsg['isDispatch'])}
                                {...formItemLayout}
                              >
                                {getFieldDecorator('isDispatch', {
                                  rules: [{ required: true, message: '请选择是否劳务派遣工' }],
                                  initialValue: baseformdata['isDispatch'],
                                  // initialValue: _isEmpty(basicInfo)? undefined : _isNumber(basicInfo['isDispatch']) ? basicInfo['isDispatch'].toString() : undefined,
                                })(<YN init={baseformdata['isDispatch']} />)}
                              </FormItem>
                            </Col>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('是否农民工', tipMsg['isFarmer'])}
                                {...formItemLayout}
                              >
                                {getFieldDecorator('isFarmer', {
                                  rules: [{ required: true, message: '是否农民工' }],
                                  initialValue: baseformdata['isFarmer'],
                                  // initialValue: _isEmpty(basicInfo)? undefined : _isNumber(basicInfo['isFarmer']) ? basicInfo['isFarmer'].toString() : undefined,
                                })(<YN init={baseformdata['isFarmer']} />)}
                              </FormItem>
                            </Col>
                          </Fragment>
                        );
                      }
                    })(this)
                  }

                  <Col span={12}>
                    <FormItem label={formLabel('手机号码', tipMsg['phone'])} {...formItemLayout}>
                      {getFieldDecorator('phone', {
                        getValueFromEvent: (e) => _trim(e.target.value),
                        rules: [
                          { required: true, message: '请输入联系电话' },
                          { pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: '请输入正确的手机号' },
                        ],
                        initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['phone'],
                      })(<Input placeholder={'请输入联系电话'} />)}
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem label={formLabel('人员类别', tipMsg['d08Code'])} {...formItemLayout}>
                      {getFieldDecorator('d08Code', {
                        rules: [{ required: true, message: '请输入人员类别' }],
                        // initialValue: _isEmpty(basicInfo)?undefined:basicInfo['d08Code'],
                        initialValue: d08Code,
                      })(
                        <DictSelect
                          codeType={'dict_d08'}
                          // initValue={_isEmpty(basicInfo)?undefined:basicInfo['d08Code']}
                          initValue={d08Code}
                          noDraw={['1', '2', '3', '4', '6']}
                          backType={'object'}
                          onChange={this.d08CodeOnChange}
                          disabled={true}
                        />,
                      )}
                    </FormItem>
                  </Col>
                  {/* 工作岗位 是学生，离退休人员，无业人员，学生毕业未就业人员、无固定职业人员都不出现工作性质 */}
                  {(function (_this) {
                    const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                    if (
                      !(
                        `${_key}`.startsWith('3') ||
                        `${_key}`.startsWith('4') ||
                        `${_key}`.startsWith('514') ||
                        `${_key}`.startsWith('515') ||
                        `${_key}`.startsWith('516')
                      )
                    ) {
                      return (
                        <Col span={12}>
                          <FormItem
                            label={formLabel('工作性质', tipMsg['jobNatureCode'])}
                            {...formItemLayout}
                          >
                            {getFieldDecorator('jobNatureCode', {
                              rules: [{ required: true, message: '请选择工作性质' }],
                              initialValue: _isEmpty(baseformdata)
                                ? undefined
                                : baseformdata['jobNatureCode'],
                            })(
                              <DictSelect
                                codeType={'dict_d107'}
                                initValue={_isEmpty(baseformdata) ? undefined : baseformdata['jobNatureCode']}
                                backType={'object'}
                              />,
                            )}
                          </FormItem>
                        </Col>
                      );
                    }
                  })(this)}
                  {/* <Col span={23}>
                <Alert message="提示：管理党组织必须选择到支部。选择后，该发展人员会进入该组织。" type="info" showIcon />
              </Col>
              <Col span={24}>
                <div style={{marginBottom:'10px'}}/>
              </Col> */}
                  <Col span={12}>
                    <FormItem label={formLabel('管理党组织', tipMsg['orgCode'])} {...formItemLayout}>
                      {getFieldDecorator('orgCode', {
                        rules: [{ required: true, message: '请选择管理党组织' }],
                        initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['orgCode'],
                      })(
                        <OrgSelect
                          orgTypeList={['3', '4']}
                          initValue={basicInfo['orgName']}
                          onChange={(e: any) => {
                            if (!_isEmpty(e)) {
                              const { code, d01Code } = e[0] || {};
                              this.getUnitList(code);
                            }
                          }}
                          disabled={!!baseformdata['id']}
                          placeholder={'请选择所在党支部'}
                        />,
                      )}
                    </FormItem>
                  </Col>
                  {/* <Col span={23}>
                <Alert message="提示：根据党统数据要求，一线情况、专业技术职务、新社会阶层为必填项，没有请选择无。" type="info" showIcon />
              </Col>
              <Col span={24}>
                <div style={{marginBottom:'10px'}}/>
              </Col> */}
                  {!_isEmpty(d08Code) && parseInt(d08Code) <= 5 && (
                    <Col span={12}>
                      <FormItem
                        label={formLabel('申请入党时间', tipMsg['applyDate'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('applyDate', {
                          rules: [
                            { required: true, message: '请输入申请入党时间' },
                            { validator: this.TemporalOrder1 },
                          ],
                          initialValue: !_isNumber(baseformdata['applyDate'])
                            ? undefined
                            : moment(baseformdata['applyDate']),
                          // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
                        })(<Date disabledDate={this.disabledTomorrow} disabled={!canEdit} />)}
                      </FormItem>
                    </Col>
                  )}
                  {!_isEmpty(d08Code) && parseInt(d08Code) <= 4 && (
                    <Col span={12}>
                      <FormItem
                        label={formLabel('确定积极分子时间', tipMsg['activeDate'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('activeDate', {
                          rules: [
                            { required: true, message: '请输入确定积极分子时间' },
                            { validator: this.TemporalOrder2 },
                          ],
                          initialValue: !_isNumber(baseformdata['activeDate'])
                            ? undefined
                            : moment(baseformdata['activeDate']),
                          // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
                        })(<Date disabledDate={this.disabledTomorrow} disabled={!canEdit} />)}
                      </FormItem>
                    </Col>
                  )}
                  {d08Code === '3' && (
                    <Col span={12}>
                      <FormItem
                        label={formLabel('确定发展对象时间', tipMsg['objectDate'])}
                        {...formItemLayout}
                      >
                        {getFieldDecorator('objectDate', {
                          rules: [
                            { required: true, message: '请输入确定发展对象时间' },
                            { validator: this.TemporalOrder3 },
                          ],
                          initialValue: !_isNumber(baseformdata['objectDate'])
                            ? undefined
                            : moment(baseformdata['objectDate']),
                          // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
                        })(<Date disabledDate={this.disabledTomorrow} disabled={!canEdit} />)}
                      </FormItem>
                    </Col>
                  )}
                  {(function (_this) {
                    // d09Code 党员、本年度发展党员、入党、积极分子、发展党员，工作岗位是3（学生）跟4（离退休）开头的。 一线情况 隐藏。 -- 王察
                    const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                    if (!(`${_key}`.startsWith('3') || `${_key}`.startsWith('4'))) {
                      return (
                        <Col span={12}>
                          <FormItem
                            label={formLabel('一线情况', tipMsg['d21Code'])}
                            {...formItemLayout}
                          >
                            {getFieldDecorator('d21Code', {
                              rules: [{ required: true, message: '请选择一线情况' }],
                              initialValue: _isEmpty(baseformdata) ? '0' : baseformdata['d21Code'],
                            })(
                              <DictSelect
                                codeType={'dict_d21'}
                                initValue={_isEmpty(baseformdata) ? '0' : baseformdata['d21Code']}
                                backType={'object'}
                              />,
                            )}
                          </FormItem>
                        </Col>
                      );
                    }
                  })(this)}
                  {/* <Col span={12}>
                <FormItem label={formLabel('一线情况', tipMsg['d21Code'])} {...formItemLayout}>
                  {getFieldDecorator('d21Code', {
                    rules: [{ required: true, message: '请选择一线情况' }],
                    initialValue: _isEmpty(basicInfo) ? '0' : basicInfo['d21Code'],
                  })(
                    <DictSelect
                      codeType={'dict_d21'}
                      initValue={_isEmpty(basicInfo) ? '0' : basicInfo['d21Code']}
                      backType={'object'}
                    />,
                  )}
                </FormItem>
              </Col> */}

                  {(function (_this) {
                    // d09Code 社会组织 工作岗位是自由职业人员505和个体工商户中从业人员504， 才能选择新社会阶层
                    const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                    console.log(_key, '_key_key_key')
                    if (
                      `${_key}`.startsWith('03') ||
                      `${_key}`.startsWith('02') ||
                      `${_key}`.startsWith('505') ||
                      `${_key}` == '504'
                    ) {
                      return (
                        <Col span={12}>
                          <FormItem
                            label={formLabel('新社会阶层', tipMsg['d20Code'])}
                            {...formItemLayout}
                          >
                            {getFieldDecorator('d20Code', {
                              rules: [{ required: true, message: '请选择新社会阶层' }],
                              initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['d20Code'],
                            })(
                              <DictTreeSelect
                                codeType={'dict_d20'}
                                initValue={_isEmpty(baseformdata) ? undefined : baseformdata['d20Code']}
                                backType={'object'}
                                parentDisable={true}
                              />,
                            )}
                          </FormItem>
                        </Col>
                      );
                    }
                  })(this)}
                  {(function (_this) {
                    // 012	事业单位管理岗位（含参照管理）
                    // 013	事业单位专业技术岗位
                    // 014	公有经济控制企业管理岗位
                    // 015	公有经济控制企业专业技术岗位
                    // 0162	事业单位工勤技能人员
                    // 0163	公有制经济控制企业工勤技能人员
                    // 02	非公有制单位
                    // 03	社会组织
                    const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                    let arr = ['012', '013', '014', '015', '0162', '0163', '02', '03'];
                    if (arr.find((it) => `${_key}`.startsWith(it))) {
                      return (
                        <Col span={12}>
                          <FormItem
                            label={formLabel('专业技术职务', tipMsg['d19Code'])}
                            {...formItemLayout}
                          >
                            {getFieldDecorator('d19Code', {
                              rules: [{ required: true, message: '请选择专业技术职务' }],
                              initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['d19Code'],
                            })(
                              <DictTreeSelect
                                initValue={_isEmpty(baseformdata) ? undefined : baseformdata['d19Code']}
                                codeType={'dict_d19'}
                                placeholder={'专业技术职务'}
                                parentDisable={true}
                                backType={'object'}
                                onChange={() => {
                                  // this['d126Code'].clearAll();
                                  // this.props.form.setFieldsValue({d126Code:undefined});
                                }}
                              />,
                            )}
                          </FormItem>
                        </Col>
                      );
                    }
                  })(this)}

                  {
                    // d09Code 当前工作岗位
                    // 改成015，0163，022，025开头以下的，0312，0313，0322，0323，0332，0333才可以选产业工人
                    (function (_this) {
                      const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
                      let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
                      let arr = ['015', '0163', '022', '0312', '0313', '0322', '0323', '0332', '0333'];
                      if (`${_key}`.startsWith('025') || arr.includes(`${_key}`)) {
                        return (
                          <Col span={12}>
                            <FormItem
                              label={formLabel('是否产业工人', tipMsg['hasWorker'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('hasWorker', {
                                rules: [{ required: true, message: '是否产业工人' }],
                                initialValue: baseformdata['hasWorker'],
                                // initialValue: _isEmpty(basicInfo)? undefined : _isNumber(basicInfo['hasWorker']) ? basicInfo['hasWorker'].toString() : undefined,
                              })(<YN init={baseformdata['hasWorker']} />)}
                            </FormItem>
                          </Col>
                        );
                      }
                    })(this)
                  }
                  <Col span={12}>
                    <FormItem
                      label={formLabel('先进模范人物', tipMsg['advancedModelCode'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('advancedModelCode', {
                        rules: [{ required: false, message: '先进模范人物' }],
                        initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['advancedModelCode'],
                      })(
                        <DictSelect
                          codeType={'dict_d104'}
                          initValue={_isEmpty(baseformdata) ? undefined : baseformdata['advancedModelCode']}
                          backType={'object'}
                        />,
                      )}
                    </FormItem>
                  </Col>


                  <Col span={12}>
                    <LongLabelFormItem
                      label={'人事关系是否在党组织关联单位内'}
                      required={true}
                      code={'hasUnitStatistics'}
                      tipMsg={tipMsg}
                      formItemLayout={formItemLayout3}
                      formItem={(formItemLayout, code) => {
                        return (
                          <FormItem {...formItemLayout}>
                            {getFieldDecorator(code, {
                              rules: [{ required: true, message: '人事关系是否在党组织关联单位内' }],
                              initialValue: baseformdata[code],
                            })(
                              <YN
                                init={baseformdata[code]}
                                onChange={(e) => {
                                  // //所在党支部
                                  // let code = undefined;
                                  // let org = this.props.form.getFieldValue('orgCode');
                                  // if(typeof org == 'string'){
                                  //   code = basicInfo['orgCode'];
                                  // }else {
                                  //   code = _get(org,'[0].code',undefined);
                                  // }
                                  // this.getUnitList(code);
                                }}
                              />,
                            )}
                          </FormItem>
                        );
                      }}
                    />
                  </Col>
                  {(function (_this) {
                    let val = _this.props.form.getFieldValue('hasUnitStatistics');
                    if (val == 0) {
                      return (
                        <Col span={12}>
                          <LongLabelFormItem
                            label={'人事关系所在单位是否省内单位'}
                            required={true}
                            code={'hasUnitProvince'}
                            tipMsg={tipMsg}
                            formItemLayout={formItemLayout3}
                            formItem={(formItemLayout, code) => {
                              return (
                                <FormItem {...formItemLayout}>
                                  {getFieldDecorator(code, {
                                    rules: [
                                      { required: true, message: '人事关系所在单位是否省内单位' },
                                    ],
                                    initialValue: baseformdata[code],
                                  })(
                                    <Select style={{ width: '100%' }}>
                                      <Select.Option value={1}>是</Select.Option>
                                      <Select.Option value={0}>否</Select.Option>
                                    </Select>,
                                  )}
                                </FormItem>
                              );
                            }}
                          />
                        </Col>
                      );
                    }
                  })(this)}
                  {(function (_this) {
                    //人事关系是否在党组织关联单位内
                    let val1 = _this.props.form.getFieldValue('hasUnitStatistics');
                    //人事关系所在单位是否省内单位
                    let hasUnitProvince = _isNumber(_this.props.form.getFieldValue('hasUnitProvince'))
                      ? _this.props.form.getFieldValue('hasUnitProvince')
                      : basicInfo['hasUnitProvince'];
                    //所在党支部
                    let org = _this.props.form.getFieldValue('orgCode');
                    let d01Code: any = undefined;
                    if (typeof org == 'string') {
                      d01Code = basicInfo['d01Code'];
                    } else {
                      d01Code = _get(org, '[0].d01Code', undefined);
                    }
                    console.log(d01Code, 'd01Coded01Coded01Coded01Code')
                    let isLianhe =
                      (d01Code == '632' || d01Code == '932' || d01Code == '634') && d01Code;

                    // 当人事关系是否在党组织关联单位内选择 是
                    if (val1) {
                      // 并且党员所在党组织非联合党支部的时候，人事关系所在单位名称展示为党员所在党组织关联单位
                      if (!isLianhe) {
                        return (
                          <Col span={11}>
                            <LongLabelFormItem
                              label={'人事关系所在单位名称'}
                              required={true}
                              code={'unitInformation'}
                              tipMsg={tipMsg}
                              formItemLayout={formItemLayout3}
                              formItem={(formItemLayout, code) => {
                                return (
                                  <FormItem {...formItemLayout}>
                                    {getFieldDecorator(code, {
                                      rules: [{ required: false, message: '人事关系所在单位名称' }],
                                      initialValue: _this.state.unitInformation,
                                    })(
                                      <Input
                                        placeholder={'请填写人事关系所在单位名称'}
                                        style={{ width: '100%' }}
                                        disabled
                                      />,
                                    )}
                                  </FormItem>
                                );
                              }}
                            />
                            <div style={{ display: 'none' }}>
                              {getFieldDecorator('statisticalUnit', {
                                rules: [{ required: false, message: '' }],
                                initialValue: _this.state.unitInformationCode,
                              })(<Input style={{ display: 'none' }} disabled />)}
                              {getFieldDecorator('_d04Code', {
                                rules: [{ required: false, message: '' }],
                                initialValue: _this.state.unitInformationD04Code,
                              })(<Input style={{ display: 'none' }} disabled />)}
                              {getFieldDecorator('d01Code', {
                                rules: [{ required: false, message: '' }],
                                initialValue: d01Code,
                              })(<Input style={{ display: 'none' }} disabled />)}
                            </div>
                          </Col>
                        );
                      } else {
                        // 党员所在党组织是联合党支部的时候，人事关系所在单位名称变成下拉框
                        return (
                          <Col span={11}>
                            <LongLabelFormItem
                              label={'人事关系所在单位名称'}
                              required={true}
                              code={'statisticalUnit'}
                              tipMsg={tipMsg}
                              formItemLayout={formItemLayout3}
                              formItem={(formItemLayout, code) => {
                                return (
                                  <Fragment>
                                    <FormItem {...formItemLayout}>
                                      {getFieldDecorator(code, {
                                        rules: [{ required: true, message: '人事关系所在单位名称' }],
                                        initialValue: baseformdata[code],
                                      })(
                                        <Select
                                          style={{ width: '100%' }}
                                          onChange={(e) => {
                                            let find = _this.state.unitList.find(
                                              (it) => it?.unitCode == e,
                                            );
                                            if (find) {
                                              _this.props.form.setFieldsValue({
                                                __d04Code: find.d04Code,
                                              });
                                            }
                                          }}
                                        >
                                          {_this.state.unitList &&
                                            _this.state.unitList.map((it, index) => (
                                              <Select.Option key={index} value={it.unitCode}>
                                                {it.unitName}
                                              </Select.Option>
                                            ))}
                                        </Select>,
                                      )}
                                    </FormItem>
                                    {getFieldDecorator('__d04Code', {
                                      rules: [{ required: false, message: '' }],
                                      initialValue: baseformdata['d04Code'],
                                    })(<Input style={{ display: 'none' }} disabled />)}
                                    {getFieldDecorator('d01Code', {
                                      rules: [{ required: false, message: '' }],
                                      initialValue: d01Code,
                                    })(<Input style={{ display: 'none' }} disabled />)}
                                  </Fragment>
                                );
                              }}
                            />
                          </Col>
                        );
                      }
                    } else {
                      //人事关系所在单位是否省内单位选择是的时候，人事关系所在单位名称需要走中间交换区进行搜索
                      if (hasUnitProvince) {
                        return (
                          <Col span={11}>
                            <LongLabelFormItem
                              label={'人事关系所在单位名称'}
                              required={true}
                              code={'middleUnitCode'}
                              tipMsg={tipMsg}
                              formItemLayout={formItemLayout3}
                              formItem={(formItemLayout, code) => {
                                return (
                                  <FormItem {...formItemLayout}>
                                    {getFieldDecorator(code, {
                                      rules: [{ required: true, message: '人事关系所在单位名称' }],
                                      initialValue: baseformdata[code],
                                    })(
                                      <SearchUnit
                                        initName={baseformdata['middleUnitName']}
                                        initCode={baseformdata['middleUnitCode']}
                                        backType={'object'}
                                        style={{ width: '100%' }}
                                        onChange={(e) => {
                                          _this.props.form.setFieldsValue({
                                            d04Code: e.d04Code,
                                            middleUnitName: e.name,
                                            _d194Code: e.d194Code,
                                            _d194Name: e.d194Name,
                                            _d195Code: e.d195Code,
                                            _d195Name: e.d195Name,
                                          });
                                        }}
                                        params={{ orgTypeList: ['3', '4'] }}
                                      />,
                                    )}
                                  </FormItem>
                                );
                              }}
                            />
                            <div style={{ display: 'none' }}>
                              {getFieldDecorator('middleUnitName', {
                                rules: [{ required: false, message: '' }],
                                initialValue: basicInfo['middleUnitName'],
                              })(<Input style={{ display: 'none' }} disabled />)}
                              {getFieldDecorator('d04Code', {
                                rules: [{ required: false, message: '' }],
                                initialValue: basicInfo['d04Code'],
                              })(<Input style={{ display: 'none' }} disabled />)}
                              {getFieldDecorator('_d194Code', {
                                rules: [{ required: false, message: '' }],
                                initialValue: basicInfo['_d194Code'],
                              })(<Input style={{ display: 'none' }} disabled />)}
                              {getFieldDecorator('_d194Name', {
                                rules: [{ required: false, message: '' }],
                                initialValue: basicInfo['_d194Name'],
                              })(<Input style={{ display: 'none' }} disabled />)}
                              {getFieldDecorator('_d195Code', {
                                rules: [{ required: false, message: '' }],
                                initialValue: basicInfo['_d195Code'],
                              })(<Input style={{ display: 'none' }} disabled />)}
                              {getFieldDecorator('_d195Name', {
                                rules: [{ required: false, message: '' }],
                                initialValue: basicInfo['_d195Name'],
                              })(<Input style={{ display: 'none' }} disabled />)}
                            </div>
                          </Col>
                        );
                      } else if (hasUnitProvince == 0) {
                        return (
                          <Col span={11}>
                            <LongLabelFormItem
                              label={'人事关系所在单位名称'}
                              required={true}
                              code={'selfUnitName'}
                              tipMsg={tipMsg}
                              formItemLayout={formItemLayout3}
                              formItem={(formItemLayout, code) => {
                                return (
                                  <FormItem {...formItemLayout}>
                                    {getFieldDecorator(code, {
                                      rules: [{ required: true, message: '人事关系所在单位名称' }],
                                      initialValue: baseformdata[code],
                                    })(
                                      <Input
                                        placeholder={'请填写人事关系所在单位名称'}
                                        style={{ width: '100%' }}
                                      />,
                                    )}
                                  </FormItem>
                                );
                              }}
                            />
                          </Col>
                        );
                      }
                    }
                  })(this)}

                  {(function (_this) {
                    let val1 = _this.props.form.getFieldValue('hasUnitStatistics');
                    if (val1 == undefined) {
                      val1 = basicInfo['hasUnitStatistics'];
                    }
                    let val = _this.props.form.getFieldValue('hasUnitProvince');
                    if (val == undefined) {
                      val = basicInfo['hasUnitProvince'];
                    }
                    if (val1 == 0 && val == 0) {
                      return (
                        <Fragment>
                          <Col span={12}>
                            <LongLabelFormItem
                              label={'人事关系所在单位类别'}
                              required={true}
                              code={'d04Code'}
                              tipMsg={tipMsg}
                              formItemLayout={formItemLayout3}
                              formItem={(formItemLayout3, code) => {
                                return (
                                  <FormItem {...formItemLayout3}>
                                    {getFieldDecorator(code, {
                                      rules: [{ required: true, message: '人事关系所在单位类别' }],
                                      initialValue: _isEmpty(baseformdata) ? undefined : baseformdata[code],
                                    })(
                                      <DictTreeSelect
                                        initValue={_isEmpty(baseformdata) ? undefined : baseformdata[code]}
                                        codeType={'dict_d04'}
                                        placeholder={'请选择'}
                                        parentDisable={true}
                                        backType={'object'}
                                        onChange={async (e) => {
                                          // 更新d194Code
                                          const res = await normalList({
                                            data: {
                                              tableCode: 'ccp_unit',
                                              colCode: 'd04Code',
                                              colValue: e.key,
                                              compareColCode: 'd194Code',
                                            },
                                          });
                                          if (res.code == 0 && !_isEmpty(res.data)) {
                                            let key = Object.keys(res.data)?.[0];
                                            let name = res.data[key];
                                            _this.setState({
                                              d194CodeSatate: key,
                                              d194CodeKey: moment().valueOf(),
                                            });
                                            _this.props.form.setFieldsValue({
                                              d194Code: key,
                                              d194Name: name,
                                            });
                                            // 更新d195Code
                                            _this.d194Change(key);
                                          }
                                        }}
                                      />,
                                    )}
                                  </FormItem>
                                );
                              }}
                            />
                          </Col>
                        </Fragment>
                      );
                    }
                  })(this)}

                  {(function (_this) {
                    let canEdit = _this.showGUOMINGJINGJI();
                    if (true) {
                      return (
                        <Fragment>
                          <Col span={12}>
                            <LongLabelFormItem
                              label={'国民经济行业'}
                              required={canEdit}
                              code={'d194Code'}
                              tipMsg={tipMsg}
                              formItemLayout={formItemLayout3}
                              formItem={(formItemLayout3, code) => {
                                return (
                                  <FormItem {...formItemLayout3}>
                                    {getFieldDecorator(code, {
                                      rules: [{ required: canEdit, message: '国民经济行业' }],
                                      initialValue: _isEmpty(baseformdata) ? undefined : baseformdata[code],
                                    })(
                                      <DictTreeSelect
                                        key={_this.state.d194CodeKey}
                                        backType={'object'}
                                        initValue={
                                          _this.state.d194CodeSatate ||
                                          (_isEmpty(baseformdata) ? undefined : baseformdata[code])
                                        }
                                        codeType={'dict_d194'}
                                        placeholder={'国民经济行业'}
                                        parentDisable={true}
                                        showModalIcon={canEdit}
                                        disabled={!canEdit}
                                        onChange={_this.d194Change}
                                      />,
                                    )}
                                  </FormItem>
                                );
                              }}
                            />
                          </Col>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('生产性服务行业', tipMsg['d195Code'])}
                              {...formItemLayout}
                            >
                              {getFieldDecorator('d195Code', {
                                initialValue: baseformdata['d195Code'] || 'V0000',
                                rules: [{ required: canEdit, message: '请选择生产性服务行业' }],
                              })(
                                <DictTreeSelect
                                  backType={'object'}
                                  key={_this.state.d195CodeKey}
                                  initValue={
                                    _this.state.d195CodeSatate || baseformdata['d195Code'] || 'V0000'
                                  }
                                  codeType={'dict_d195'}
                                  placeholder={'生产性服务行业'}
                                  showModalIcon={canEdit}
                                  disabled={!canEdit}
                                  parentDisable={true}
                                />,
                              )}
                            </FormItem>
                          </Col>
                          {getFieldDecorator('d194Name', {
                            rules: [{ required: false, message: '' }],
                            initialValue: basicInfo['d194Name'],
                          })(<Input style={{ display: 'none' }} disabled />)}
                          {getFieldDecorator('d195Name', {
                            rules: [{ required: false, message: '' }],
                            initialValue: basicInfo['d195Name'],
                          })(<Input style={{ display: 'none' }} disabled />)}
                        </Fragment>
                      );
                    }
                  })(this)}

                  {/* <Col span={12}>
                <FormItem
                  label="入党时递交党组织是否为系统外"
                  {...formItemLayout3}
                >
                  {getFieldDecorator('isOutSystem', {
                    rules: [{ required: false, message: '必填' }],
                    initialValue:isOutSystem_state,
                  })(
                    <Select style={{width:'100%'}} onChange={this.isOutSystemOnChange}>
                      <Select.Option value="1">是</Select.Option>
                      <Select.Option value="0">否</Select.Option>
                    </Select>
                  )}
                </FormItem>
              </Col> */}
                  {/* {
                !isOutSystem_state ?
                  <Col span={12}>
                    <FormItem
                      label="入党时递交党组织"
                      {...formItemLayout}
                    >
                      {getFieldDecorator('appliedOrgCode', {
                        rules: [{ required: false, message: '请选择申请时递交党组织' }],
                        initialValue: _isEmpty(basicInfo)?undefined:basicInfo['appliedOrgCode'],
                      })(
                        <OrgSelect orgTypeList={['3','4']} initValue={ basicInfo['appliedOrgName'] }  placeholder={'请选择入党时递交党组织'}/>
                      )}
                    </FormItem>
                  </Col>
                  :
                  <Col span={12}>
                    <FormItem
                      label="入党时递交党组织"
                      {...formItemLayout}
                    >
                      {getFieldDecorator('outBranchOrgName', {
                        rules: [{ required: false, message: '请选择入党时递交党组织' }],
                        initialValue: _isEmpty(basicInfo)?undefined:basicInfo['outBranchOrgName'],
                      })(
                        <Input placeholder={'请选择入党时递交党组织'}/>
                      )}
                    </FormItem>
                  </Col>
              } */}

                  {/*<Col span={12}>*/}
                  {/*  <FormItem*/}
                  {/*    label="婚姻情况"*/}
                  {/*    {...formItemLayout}*/}
                  {/*  >*/}
                  {/*    {getFieldDecorator('d60Code', {*/}
                  {/*      rules: [{ required: false, message: '请选择入党时递交党组织' }],*/}
                  {/*      initialValue: _isEmpty(basicInfo)?undefined:basicInfo['d60Code'],*/}
                  {/*    })(*/}
                  {/*      <DictSelect codeType={'dict_d60'} initValue={_isEmpty(basicInfo)?undefined:basicInfo['d60Code']} backType={'object'}/>*/}
                  {/*    )}*/}
                  {/*  </FormItem>*/}
                  {/*</Col>*/}
                  {!_isEmpty(basicInfo) && !_isEmpty(d08Code) && parseInt(d08Code) < 5 && (
                    <Fragment>
                      {/* <Col span={12}>
                    <FormItem
                      label={formLabel('失联情况', tipMsg['d18Code'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('d18Code', {
                        rules: [{ required: false, message: '请选择失去联系类型' }],
                        initialValue: _isEmpty(basicInfo)?'0':basicInfo['d18Code'],
                      })(
                        <DictSelect codeType={'dict_d18'} initValue={_isEmpty(basicInfo)?'0':basicInfo['d18Code']} onChange={this.d18OnChange} backType={'object'}/>
                      )}
                    </FormItem>
                  </Col> */}
                      {/* <Col span={12}> */}
                      {/*<FormItem*/}
                      {/*  label="入党时递交党组织是否为系统外"*/}
                      {/*  {...formItemLayout3}*/}
                      {/*>*/}
                      {/*  {getFieldDecorator('isOutSystem', {*/}
                      {/*    rules: [{ required: false, message: '必填' }],*/}
                      {/*    initialValue:isOutSystem_state,*/}
                      {/*  })(*/}
                      {/*    <Switch checkedChildren="是" unCheckedChildren="否" defaultChecked={isOutSystem_state} onChange={this.isOutSystemOnChange}/>*/}
                      {/*  )}*/}
                      {/*</FormItem>*/}
                      {/* <FormItem
                      label={formLabel('失去联系时间', tipMsg['lossDate'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('lossDate', {
                        rules: [{ required: hasLost, message: '请选择失去联系时间' }],
                        initialValue: !_isNumber(basicInfo['lossDate'])?undefined:moment(basicInfo['lossDate']),
                        // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}} disabled={!hasLost}/>
                      })(

                        <Date disabledDate={this.disabledTomorrow} disabled={!canEdit}/>
                      )}
                    </FormItem> */}
                      {/* </Col> */}
                    </Fragment>
                  )}
                  {/* 当this.props.canEdit == mems,展示接收预备党员信息 */}
                  {_get(this, 'props.canEdit[0]', undefined) == 'mems' && this.showMemsInfo()}
                  <Col span={23}>
                    <FormItem label={formLabel('现居住地', tipMsg['homeAddress'])} {...formItemLayout1}>
                      {getFieldDecorator('homeAddress', {
                        rules: [
                          { required: true, message: '请填写家庭住址（8个字及以上）', min: 8 },
                          { validator: (...e) => validateLength(e, 100, 300) },
                        ],
                        initialValue: _isEmpty(baseformdata) ? undefined : baseformdata['homeAddress'],
                      })(<Input placeholder={'请填写家庭住址'} maxLength={300} minLength={8} />)}
                    </FormItem>
                  </Col>
                </Row>
              }
              {
                step != 1 &&
                <React.Fragment>
                  {
                    (fileObj?.formList && fileObj?.formList.length > 0) && <FormList ref={e => this['FormList'] = e} dataInfo={rowData} onOk={this.backform} />
                  }
                </React.Fragment>
              }
              {/* {
               step == 2 && 
                <div className={style.flexbox}>
                  {
                    fileObj?.value1 && fileObj?.value1.map((item, index) => {
                      return (
                        <div className={style.flexbox_l} key={index}>
                          <div className={style.filename}>{item?.d222Name}</div>
                          <div
                            className={style.imgs}
                            ref={(el) => this.imgContainerRefs[`imgs_${index}`] = el}
                            onDragOver={(e) => this.handleDragOver(e, this.imgContainerRefs[`imgs_${index}`])}
                          >
                            <React.Fragment>
                              <React.Fragment>
                                {
                                  item?.fileList && item?.fileList.map((file, i) => {
                                    return (
                                      <div
                                        draggable
                                        onDragStart={(e) => this.dragstarts(e, { ...file, old: index })}
                                        onDrop={(e) => this.drops(e, file)}
                                        onDragOver={(e) => e.preventDefault()}
                                        key={i}
                                        className={checkList.includes(file.id) ? `${style.previewImage} ${style.cImage}` : style.previewImage}
                                        onClick={() => this.check(file, index)}
                                      >
                                        {
                                          checkList.includes(file?.id) &&
                                          <div className={style.cicon}>
                                            <LegacyIcon type="check" />
                                          </div>
                                        }
                                        <LazyImage
                                          dataSrc={`${window.location.origin}/${file.previewPath}`}
                                          alt="file-preview"
                                          className={style.simg}
                                          onLoad={() => { }}
                                        />
                                        <div className={style.preview} onClick={(e) => {
                                          e.stopPropagation()
                                          this.setState({
                                            previewVisible: true,
                                            previewImage: file.previewPath
                                          })
                                        }}>
                                          <Button size="small" type='primary'>预览</Button>
                                        </div>

                                      </div>
                                    )
                                  })

                                }
                              </React.Fragment>
                              <React.Fragment>

                                {
                                  this.state[`fileList${index}`] && this.state[`fileList${index}`].map((item: any) => {
                                    return (
                                      <div className={style.previewImage}>
                                        <Skeleton.Image style={{ width: '100%', height: '100%' }} />
                                        <Progress percent={item.percent} size="small" />
                                      </div>
                                    )
                                  })
                                }
                              </React.Fragment>
                            </React.Fragment>

                          </div>

                          {
                            isup && <div className={style.upbtn}>
                              <Space>
                                <Spin spinning={fileloading} indicator={<LoadingOutlined spin />} size="small">
                                  <Upload
                                    style={{ marginBottom: 10 }}
                                    {...props}
                                    multiple
                                    onChange={(file) => this.fileChange(file, item, index)}
                                    beforeUpload={(file, fileList) => this.beforeUpload(file, fileList, index)}
                                    fileList={this.state[`fileList${index}`]}
                                    showUploadList={false}
                                  >
                                    <Button>上传</Button>
                                  </Upload>
                                </Spin>
                                {
                                  item?.fileList && item?.fileList.length > 0 && <Button disabled={this.isdisabled(item)} type={'danger'} onClick={() => this.del(index)}>删除</Button>
                                }
                              </Space>
                            </div>
                          }

                        </div>
                      )
                    })
                  }
                </div>

            } */}
              {
                step == 2 &&

                <div className={style.flexbox}>
                  {
                    fileObj?.value && fileObj?.value.map((item, index) => {
                      return (
                        <div className={style.flexbox_l} key={index}>
                          <div className={style.filename}>{item?.d222Name}</div>
                          {
                            item?.tip &&
                            <div className={style.tips}>
                              {/* {
                                  item?.tip1 && <div className={style.tips1} onClick={() => this.setTip(index, 1)}>
                                    <Space>
                                      <ExclamationCircleOutlined />
                                      注意
                                    </Space>
                                  </div>
                                } */}
                              <div className={style.tips2} onClick={() => this.setTip(index, 2)}>
                                <Space>
                                  <ExclamationCircleOutlined />
                                  档案注意事项
                                </Space>
                              </div>

                            </div>
                          }
                          {
                            item?.tip?.show &&
                            <div className={style.tipsbox}>
                              {
                                item?.tip?.show &&
                                <div className={style.tipsbox2}>
                                  {/* <div className={style.tipclose}><img src={XX} onClick={() => this.closeTip(index, 2)} /></div> */}
                                  <div>{item?.tip?.title}:</div>
                                  {
                                    item?.tip?.info.map((it, ik) => {
                                      return (
                                        <div key={ik}>{it}</div>
                                      )
                                    })
                                  }
                                  {
                                    item?.tip1 && <div><span style={{ fontWeight: 'bold' }}>注</span>：{item?.tip1?.info}</div>
                                  }
                                </div>
                              }

                            </div>
                          }
                          <div
                            className={style.imgs}
                            ref={(el) => this.imgContainerRefs[`imgs_${index}`] = el}
                            onDragOver={(e) => this.handleDragOver(e, this.imgContainerRefs[`imgs_${index}`])}
                          >
                            <React.Fragment>
                              <React.Fragment>
                                {
                                  item?.fileList && item?.fileList.map((file, i) => {
                                    return (
                                      <div
                                        draggable
                                        onDragStart={(e) => this.dragstarts(e, { ...file, old: index })}
                                        onDrop={(e) => this.drops(e, file)}
                                        onDragOver={(e) => e.preventDefault()}
                                        key={i}
                                        className={checkList.includes(file.id) ? `${style.previewImage} ${style.cImage}` : style.previewImage}
                                        onClick={() => this.check(file, index)}
                                      >
                                        {
                                          checkList.includes(file?.id) &&
                                          <div className={style.cicon}>
                                            <LegacyIcon type="check" />
                                          </div>
                                        }
                                        <LazyImage
                                          dataSrc={`${window.location.origin}/${file.previewPath}`}
                                          alt="file-preview"
                                          className={style.simg}
                                          onLoad={() => { }}
                                        />
                                        {/* <img src={`${window.location.origin}/${file.previewPath}`} /> */}
                                        <div className={style.cropper} onClick={(e) => {
                                          e.stopPropagation()
                                          this.setState({
                                            cropperVisible: true,
                                            previewImage: file.previewPath,
                                            fileListIndex: index
                                          })
                                        }}>
                                          <Button size="small" type='primary'>编辑</Button>
                                        </div>
                                        <div className={style.preview} onClick={(e) => {
                                          e.stopPropagation()
                                          this.setState({
                                            previewVisible: true,
                                            previewImage: file.previewPath
                                          })
                                        }}>
                                          <Button size="small" type='primary'>预览</Button>
                                          {/* <LegacyIcon type="eye" /> */}
                                          {/* <img src={require('@/assets/mem/yj.png')} /> */}
                                        </div>

                                      </div>
                                    )
                                  })

                                }
                              </React.Fragment>
                              <React.Fragment>

                                {
                                  this.state[`fileList${index}`] && this.state[`fileList${index}`].map((item: any) => {
                                    return (
                                      <div className={style.previewImage}>
                                        <Skeleton.Image style={{ width: '100%', height: '100%' }} />
                                        <Progress percent={item.percent} size="small" />
                                      </div>
                                    )
                                  })
                                }
                              </React.Fragment>
                            </React.Fragment>

                          </div>

                          {
                            isup && <div className={style.upbtn}>
                              <Space>
                                <Spin spinning={fileloading} indicator={<LoadingOutlined spin />} size="small">
                                  <Upload
                                    style={{ marginBottom: 10 }}
                                    {...props}
                                    multiple
                                    onChange={(file) => this.fileChange(file, item, index)}
                                    beforeUpload={(file, fileList) => this.beforeUpload(file, fileList, index)}
                                    fileList={this.state[`fileList${index}`]}
                                    showUploadList={false}
                                  >
                                    <Button>上传</Button>
                                  </Upload>
                                </Spin>
                                {
                                  item?.fileList && item?.fileList.length > 0 && <Button disabled={this.isdisabled(item)} type={'danger'} onClick={() => this.del(index)}>删除</Button>
                                }
                              </Space>
                            </div>
                          }

                        </div>
                      )
                    })
                  }
                </div>
              }
            </div>
          </div>
          <Info ref={e => this['Info'] = e} change={this.confirmAgain} />
        </Modal>
        <Modal
          width={'1000px'}
          visible={previewVisible}
          footer={null}
          zIndex={3}
          onCancel={() => {
            this.setState({
              previewImage: '',
              previewVisible: false
            })
          }}>
          <div style={{ width: '780px', height: '800px', position: 'relative', margin: 'auto' }}>
            <LegacyIcon className={style.changeicon} type="left" onClick={() => this.toswitch('up')} />
            <img style={{ width: '100%', height: '100%', userSelect: 'none' }} alt="example" src={`${window.location.origin}/${previewImage}`} />
            <LegacyIcon className={style.changeicon1} onClick={() => this.toswitch('below')} type="right" />
          </div>

        </Modal>
        {
          cropperVisible &&
          <Modal
            width={'1360px'}
            visible={cropperVisible}
            zIndex={3}
            onOk={this.handOk}
            onCancel={() => {
              this['imageCropperRef'].clears()
              this.setState({
                previewImage: '',
                cropperVisible: false
              })
            }}>
            <div style={{  height: '690px', position: 'relative', margin: 'auto' }}>
              <ImageCropper keys={Math.random()} src={previewImage} ref={(e) => this['imageCropperRef'] = e} />
            </div>

          </Modal>
        }

        <MembersWorkProcedures wrappedComponentRef={e => this['MembersWorkProcedures'] = e} />
      </Fragment>
    );
  }
}
export default Form.create()(index);
