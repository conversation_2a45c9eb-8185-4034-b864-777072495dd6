.searchBg {
  background: url('../../../../assets/qzs/bg1.webp') no-repeat;
  background-size: 100% 100%;
}

.searchPage {
  .searchBg;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .center {
    width: 625px;
    height: 500px;
    display: flex;
    // justify-content: center;
    align-items: center;
    flex-direction: column;
    .tit {
      // width: 250px;
      height: 65px;
      margin-bottom: 30px;
    }
    .form {
      position: relative;
      width: 80%;
      :global {
        .ant-form-item {
          margin-bottom: 30px;
          font-size: 30px;
        }
        .ant-form-item-control-input-content {
          > input {
            font-size: 30px;
            background: transparent !important;
            border: 2px solid #c1a984;
            border-radius: 78px;
          }
        }
      }
      .zzsrm {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .zzsrmItem {
          width: 34%;
        }
        .zzsrmItemLine {
          margin-bottom: 30px;
          font-size: 25px;
        }
      }
      .tooltip {
        position: absolute;
        bottom: 41px;
        font-size: 25px;
        right: -35px;
        cursor: pointer;
      }
    }
    .btn {
      width: 80%;
      background-color: #be0c10;
      border-radius: 78px;

      cursor: pointer;

      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 32px;
      color: #fff9e7;
      text-align: center;
    }

    :global {
      .ant-tabs-tab {
        color: #be0c10 !important;
        font-weight: bolder !important;
      }
      .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #be0c10 !important;
        font-weight: bolder !important;
      }
      .ant-tabs-ink-bar {
        background: #be0c10 !important;
      }
    }
  }
}

.searchMem {
  .searchBg;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 50px;
  .center {
    background: url('../../../../assets/qzs/box1.png') no-repeat;
    background-size: 100% 100%;
    width: 1100px;
    height: 700px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30px;
    .tit {
      width: 80%;
      margin-bottom: 20px;
    }
    .tit1 {
      width: 100%;
      min-height: 400px;
    }
    .listItem {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #ffffff;
      border-radius: 8px;
      padding: 10px;
      box-shadow: 3px 3px 10px #ccc;
      cursor: pointer;
      .photo {
        > img {
          width: 110px;
          height: 144px;
        }
        margin-right: 16px;
      }
      .info {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 18px;
        color: #62471e;

        .name {
          font-family: Source Han Serif SC;
          font-weight: 800;
          font-size: 30px;
          color: #be0c10;
          display: flex;
          align-items: center;
          > div {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            /* autoprefixer: on */
          }
          > span {
            text-align: center;
            width: 50px;
            margin-left: 4px;
            padding: 2px 4px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            display: inline-block;
            background: url('../../../../assets/qzs/icon1.png') no-repeat;
            background-size: 100% 100%;
          }
        }
        .desc {
          margin-top: 10px;
        }
      }
    }
    .pagination {
      margin-top: 10px;
      text-align: right;
      width: 100%;
    }
  }
}
