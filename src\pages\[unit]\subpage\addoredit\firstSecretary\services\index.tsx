import request from "@/utils/request";
import qs from 'qs';

export function electUpdate(params) {
  return request(`/api/unit/resident/elect/update`,{
    method:'POST',
    body:params,
  });
}
export function electSave(params) {
  return request(`/api/unit/resident/elect/save`,{
    method:'POST',
    body:params,
  });
}
export function electDel(params) {
  return request(`/api/unit/resident/elect/del`,{
    method:'POST',
    body:params,
  });
}

export function electList(params) {
  return request(`/api/unit/resident/elect/list?${qs.stringify(params)}`,{
    method:'GET',
  });
}


export function residentDel(params) {
  return request(`/api/unit/resident/del`,{
    method:'POST',
    body:params,
  });
}
export function residentSave(params) {
  return request(`/api/unit/resident/save`,{
    method:'POST',
    body:params,
  });
}
export function residentUpdate(params) {
  return request(`/api/unit/resident/update`,{
    method:'POST',
    body:params,
  });
}


export function residentList(params) {
  return request(`/api/unit/resident/list?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function residentBackOut(params) {
  return request(`/api/unit/resident/backOut`,{
    method:'POST',
    body:params,
  });
}


