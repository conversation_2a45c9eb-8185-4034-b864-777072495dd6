import request from 'src/utils/request';
import qs from 'qs';

export function add(params) {
  return request(`/api/role/addRole`,{
    method:'POST',
    body:params
  });
}
export function update(params) {
  return request(`/api/role/updateRole`,{
    method:'POST',
    body:params
  });
}
export function del(params) {
  return request(`/api/role/delRole?roleId=${params['id']}`,{
    method:'Get',
  });
}
// 角色管理 列表
export function getList(params) {
  return request(`/api/role/getList?${qs.stringify(params)}`,{
    method:'Get',
  });
}

// 获取角色
export function getById(params) {
  return request(`/api/role/getById?roleId=${params['roleId']}`,{
    method:'Get',
  });
}
// 编辑权限
export function upUserRole(params) {
  return request(`/api/permission/permissionList`,{
    method:'Get',
  });
}

// 获取角色父id树
export function getlv(params) {
  return request(`/api/role/getRoleTree`,{
    method:'Get',
  });
}

export function findPermissionByRoleId(params) {
  return request('/api/permission/findPermissionByRoleId',{
    method:'POST',
    body:params,
  });
}

export function updateRolePermisson(params) {
  return request('/api/role/updateRolePermisson',{
    method:'POST',
    body:params,
  });
}

export function getUserList(params) {
  return request(`/api/role/getUserRole?${qs.stringify(params)}`,{
    method:'Get',
  });
}
