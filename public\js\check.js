
function ChangeRowspanHiddenData() {
  var node;
  var params=["r2","r3","r4","r8","r11","r14"];
  for (var i = 0;i < params.length; i++) {
    node = document.getElementById(params[i]);
    if (node != null) {
      node.style.display = "";
    }
  }
}
ChangeRowspanHiddenData();

function sendMessage(cell){
  var obj=new Object();
  var tableName=cell.getAttribute("table-name");
  var type=cell.getAttribute("btn-type");
  var tableCellIndex=cell.getAttribute("table-cell");
  obj['tableName']=tableName;
  obj['tableCellIndex']=tableCellIndex;
  obj['btnType']=type;
  console.log(obj,'1');
  window.parent.postMessage(JSON.stringify(obj),'*')
}
