// 流动人员详情-仅作展示信息使用，不可编辑基本信息
import React, { Fragment } from 'react';
import { connect } from 'dva';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import _isObject from 'lodash/isObject';
import { Form } from '@ant-design/compatible';
import { Input, Modal, InputNumber, Button, Select, message, Radio } from 'antd';
import moment from 'moment';
import { getSession } from '@/utils/session';
import { formLabel, findDictCodeName } from '@/utils/method';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import DictArea from '@/components/DictArea';
import Tip from '@/components/Tip';
import {
  inflowOrganizationSave, inflowOrganizationDCancel, inflowOrganizationCancel
} from '../../service/index';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const RadioGroup = Radio.Group;

// @ts-ignore
@connect(
  ({ unit, commonDict, loading, flowMem }) => ({
    flowMem,
    unit,
    commonDict,
    loading: loading.effects['unit/getList'],
  }),
  undefined,
  undefined,
  { forwardRef: true },
)
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  open = (record, text: string, type: number) => {
    console.log("🚀 ~ index ~ record:", record)
    let title = '流动党员党组织审批';
    this.setState(
      {
        readOnly: true,
        modalTitle: title,
        visible: true,
        flowOrgCode: record.code,
        isShow: text == 'cancel',
        showType: type
      },
      () => { },
    );
  };
  handleOk = async () => {
    const { onOk } = this.props;
    const { basicInfo, flowOrgCode, isShow, showType } = this.state;
    const org: any = getSession('org');
    // console.log(this.props, this.state);
    this.props.form.validateFieldsAndScroll(async (error, values) => {
      // console.log("🚀 ~ index ~ this.props.form.validateFieldsAndScroll ~ values:", values)
      if (error) {
        return;
      }

      let url: any = undefined;
      const data = { ...values }
      if (showType == 0) {
        url = inflowOrganizationDCancel
        data.code = flowOrgCode
      } else {
        if (!isShow) {
          url = inflowOrganizationSave
        } else {
          data.status = "3"
          url = inflowOrganizationCancel
        }
        data.flowOrgCode = flowOrgCode
      }
      if (url) {
        this.setState(
          {
            confirmLoading: true,
          },
          async () => {
            const { code = 500 } = await url({
              data: data,
            });
            this.setState({
              confirmLoading: false,
            });
            if (code == 0) {
              Tip.success('操作提示', '操作成功');
              this.handleCancel();
              onOk && onOk();
            }
          },
        );
      }
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      modalTitle: '',
      memData: [],
      basicInfo: {},
      outPlaceCode: '',
      flowTypeCode: undefined,
      orgSearchType: '', // 组织查询选择器返回的type
      readOnly: false,
      confirmLoading: false,
      showInMemD20Code: false,
    });
  };
  statusChange = (e) => {
    // const { target: { value = '' } = {} } = e;
    // const { form } = this.props;
    // let arr: Array<string> = [];
    // if (value) {
    //   switch (value) {
    //     case '1':
    //       arr = ['14', '16'];
    //       this.setState({ beFull: true });
    //       break;
    //     case '2':
    //       arr = ['11', '12', '13'];
    //       form.setFieldsValue({ fullMemberDate: undefined });
    //       this.setState({ beFull: false });
    //       break;
    //     default:
    //       break;
    //   }
    //   form.setFieldsValue({ d28Code: undefined });
    // }
    // this.setState({
    //   d28Disabled: arr,
    // });
  };

  render() {
    const { children, tipMsg = {}, commonDict } = this.props;
    const {
      visible,
      modalTitle = '',
      basicInfo = {},
      flowOrgCode = '',
      readOnly = false,
      confirmLoading = false,
      itemsDisabled = [],
      isShow = false,
      showType = 0
    } = this.state;
    // console.log(modalType, 'modalTypemodalTypemodalType')
    const { getFieldDecorator, setFieldsValue, getFieldValue } = this.props.form;
    const org: object = getSession('org') || {};

    return (
      <div>
        {children
          ? React.cloneElement(children as any, {
            onClick: this.open,
            key: 'container1',
          })
          : null}
        <Modal
          footer={(
            <Fragment>
              <Button
                onClick={() => {
                  this.handleCancel();
                }}
              >
                取消
              </Button>
              <Button
                loading={confirmLoading}
                onClick={() => {
                  this.handleOk();
                }}
                type="primary"
              >
                保存
              </Button>
            </Fragment>
          )
          }
          bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
          destroyOnClose
          // maskClosable={false}
          width={800}
          title={modalTitle}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
        >
          <Form {...formItemLayout}>
            {!isShow && <FormItem label={formLabel('审批意见', tipMsg['status'])} {...formItemLayout}>
              {getFieldDecorator('status', {
                rules: [{ required: true, message: '请输入审批意见' }],
                initialValue: _isEmpty(basicInfo) ? '1' : basicInfo['status'],
              })(
                <RadioGroup onChange={this.statusChange} disabled={false}>
                  {/* <Radio value={'0'}>待审批</Radio> */}
                  <Radio value={'1'}>审批通过</Radio>
                  <Radio value={'2'}>审批不通过</Radio>
                </RadioGroup>,
              )}
            </FormItem>}
            {showType == 0 && <FormItem label={formLabel('撤销原因', tipMsg['cancel'])}>
              {getFieldDecorator('cancel', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.cancel,
                rules: [{ required: true, message: '请填写' }],
              })(
                <Input.TextArea
                  disabled={false}
                  placeholder="请输入"
                  showCount
                  maxLength={100}
                  rows={4}
                />,
              )}
            </FormItem>}
            {
              getFieldValue("status") == 2 &&
              <FormItem label={formLabel('不通过原因', tipMsg['reason'])}>
                {getFieldDecorator('reason', {
                  initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.reason,
                  rules: [{ required: true, message: '请填写' }],
                })(
                  <Input.TextArea
                    disabled={false}
                    placeholder="请输入"
                    showCount
                    maxLength={100}
                    rows={4}
                  />,
                )}
              </FormItem>
            }
            {
              showType != 0 && isShow &&
              <FormItem label={formLabel('不通过原因', tipMsg['reason'])}>
                {getFieldDecorator('reason', {
                  initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.reason,
                  rules: [{ required: true, message: '请填写' }],
                })(
                  <Input.TextArea
                    disabled={false}
                    placeholder="请输入"
                    showCount
                    maxLength={100}
                    rows={4}
                  />,
                )}
              </FormItem>
            }
          </Form>
        </Modal>
      </div>
    );
  }
}

export default Form.create()(index);
