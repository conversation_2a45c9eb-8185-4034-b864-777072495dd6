import React, {PureComponent, Fragment} from 'react';
import {Provider} from './context';
import PT from 'prop-types';
import styles from './index.less';
import _isEmpty from 'lodash/isEmpty';
import Top from './top';
import Middle from './middle';
interface Interface {
  title?:any,
  goBack?:(any)=>void,
  subTitle?:React.ReactNode,
  itemsArr?:Array<{
    leftTitle:string | React.ReactNode,
    leftInfo:string | React.ReactNode,
    rightTitle?:string | React.ReactNode,
    rightInfo?:string | React.ReactNode,
  }>,
  middleRows:Array<{
    title:string | React.ReactNode,
    content:string | React.ReactNode,
  }>,
  extraInfo?:any
}
export default class index extends PureComponent<Interface, any>{
  static propTypes = {
    itemsArr:PT.array.isRequired
  };

  static defaultProps = {
    // 详情数组
    itemsArr:[]
  };
  constructor(props){
    super(props);
    this.state = {

    };
  }

  render() {
   const {itemsArr, extraInfo} = this.props;
    return (
      <Provider value={this.props}>
        <div className={styles.pageAll}>
          <Top/>
          <div className={styles.page}>
            <div className={styles.main}>
              <Middle Items={itemsArr}/>
            </div>
            {
              !_isEmpty(extraInfo) &&
              <div className={styles.listInfo}>{extraInfo}</div>
            }
          </div>
        </div>
      </Provider>
    );
  }
}
