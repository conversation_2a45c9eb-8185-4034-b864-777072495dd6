import { <PERSON><PERSON>, Col, Modal, Pa<PERSON><PERSON>, <PERSON>, Spin } from 'antd';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import st from './index.less';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import { pullFile } from '@/services';
import { politicalBirthday } from '../services';
import { _history as router } from '@/utils/method';

const index = (props) => {
  const { joinOrgDate } = router.location.query;

  const [mainList, setMainList] = useState([]);
  const [mainPagination, setMainPagination] = useState({
    pageNum: 1,
    pageSize: 9,
    total: 0,
  });
  const [spinning, setSpinning] = useState<any>(false);

  const getBirth = async (p = {}) => {
    setSpinning(true);
    const res = await politicalBirthday({
      data: {
        pageNum: mainPagination.pageNum,
        pageSize: mainPagination.pageSize,
        joinOrgDate: joinOrgDate,
        ...p,
      },
    });
    if (res.code == 0) {
      const { data: { list: mainsList = [], ...others } = {} } = res;

      for (let i = 0; i < mainsList.length; i++) {
        const item: any = mainsList[i];
        if (_isEmpty(item.photo)) {
          item['photo_base64'] = require('../../../../assets/head.jpg');
          continue;
        }
        const base64 = await pullFile({ path: item.photo });
        item['photo_base64'] = base64;
      }
      setSpinning(false);
      setMainList(mainsList);
      setMainPagination({ ...others, total: others.totalRow, pageNum: others.pageNumber });
    }
  };

  const onPageChange = async (page, size) => {
    getBirth({ pageNum: page, pageSize: size });
  };

  useEffect(() => {
    joinOrgDate && getBirth();
  }, [joinOrgDate]);

  return (
    <Fragment>
      <div className={st.searchMem}>
        <div className={st.center}>
          <img className={st.tit} src={require('../../../../assets/qzs/dyxx.png')} alt="" />
          <Spin wrapperClassName={st.tit1} spinning={spinning}>
            <Row style={{ width: '100%' }} gutter={[16, 16]}>
              {mainList.map((it: any, index) => {
                return (
                  <Col span={8} key={index}>
                    <div className={st.listItem}>
                      <div className={st.photo}>
                        <img src={it.photo_base64} alt="" />
                      </div>
                      <div className={st.info}>
                        <div className={st.name}>
                          <div>{it.name}</div>
                          <span>{it.d06Name}</span>
                        </div>
                        <div className={st.desc}>
                          出生年月:{moment(it.birthday).format('YYYY年M月')}
                        </div>
                        <div className={st.desc}>
                          政治生日:{moment(it.joinOrgDate).format('YYYY年M月')}
                        </div>
                      </div>
                    </div>
                  </Col>
                );
              })}
            </Row>
            <div className={st.pagination}>
              <Pagination
                size="small"
                {...mainPagination}
                current={mainPagination.pageNum}
                onChange={onPageChange}
                showSizeChanger={false}
              />
            </div>
          </Spin>
        </div>
      </div>
    </Fragment>
  );
};

export default index;
