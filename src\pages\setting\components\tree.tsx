import React, { useEffect, useState } from 'react';
import { Tree } from 'antd';
export default function setTree(props) {
  const {data=[],onChange,selectedKeys=[]}=props;
  const [treeData,setTreeData]=useState([]);
  const [treeKey,setTreeKey]=useState([]);
  useEffect(()=>{
    let temp:any=[];
    for(let obj of data){
      temp.push({
        key:obj['id'],
        title:obj['tableName'],
        ...obj
      })
    }
    setTreeData(temp);
  },[JSON.stringify(data)]);
  const onSelect=(keys,e)=>{
    setTreeKey(keys);
    onChange && onChange(e['node']);
  };
  return <Tree selectedKeys={treeKey.length> 0 ? treeKey : selectedKeys} treeData={treeData} onSelect={onSelect}/>
}
