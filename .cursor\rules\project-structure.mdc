---
description: 
globs: 
alwaysApply: false
---
# 项目结构

本项目是一个基于 React + UmiJS + Dva + Ant Design 的党建平台前端项目。

## 主要目录结构
```
src/
  ├── assets/        # 静态资源文件夹
  ├── components/    # 公共组件
  ├── layouts/       # 布局组件
  ├── models/        # Dva 模型
  ├── pages/         # 页面组件
  ├── services/      # API 服务
  ├── utils/         # 工具函数
  ├── common/        # 公共资源
  ├── app.js         # 应用入口
  └── global.less    # 全局样式

