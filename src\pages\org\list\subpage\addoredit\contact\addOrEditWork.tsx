/**
 * 新增/编辑 工作开展情况
 */

import React, { useState, useImperativeHandle } from 'react';
import { Input, Select, Form, Modal, Upload } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import moment from 'moment';
import { compressAccurately } from 'image-conversion'; // 压缩图片插件
import DateTime from '@/components/Date';
import DictSelect from '@/components/DictSelect';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _isObject from 'lodash/isObject';
import Tip from '@/components/Tip';
import { addContactWork, updateContactWork, findContactWork } from '../../../../services';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};

const index = (props: any, ref: any) => {
  const { code = '', orgCode = '' } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('新增工作开展情况');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [basinInfo, setBasinInfo] = useState<any>({});
  const [options, setOptions] = useState([]);
  const [fileList, setFileList] = useState<any>([]);
  const [previewImage, setPreviewImage] = useState('');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewTitle, setPreviewTitle] = useState('');
  useImperativeHandle(ref, () => ({
    open: ({ memList, record }) => {
      //   setTimekey(moment().valueOf());
      let arr: any = [];
      if (memList.length > 0) {
        memList.map((item, index) => {
          arr.push({ label: item.name, value: item.code });
        });
      }
      setOptions(arr);

      if (record?.code) {
        setTitle('编辑工作开展情况');
        getBasicInfo(record?.code);
      } else {
        setBasinInfo({});
        setTitle('新增工作开展情况');
      }

      setVisible(true);
    },
  }));

  const uploadButton = (
    <div>
      {/*{loading ? <LoadingOutlined /> : <PlusOutlined />}*/}
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>点击上传图片</div>
    </div>
  );
  const upProps = {
    // multiple: true,
    // showUploadList: false,
    name: 'file',
    action: `/api/base/upload`,
    accept: '.jpg,.png,.jpeg',
    headers: {
      Authorization: sessionStorage.getItem('token') || '',
      dataApi: sessionStorage.getItem('dataApi') || '',
    },
  };
  const imgChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  const getBase64 = (file): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file?.thumbUrl ? file.thumbUrl : (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };
  const blobToBase64 = (blob) => {
    return new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.onload = (e?: any) => {
        resolve(e.target.result);
      };
      fileReader.readAsDataURL(blob);
    });
  };
  const getBasicInfo = async (code?: any) => {
    const { code: resCode = 500, data = {} } = await findContactWork({ code });
    if (resCode == 0) {
      setBasinInfo(data);
      let newData = { ...data };
      if (newData?.contactCode) {
        newData.contactCode = newData?.contactCode.split(',');
      }
      if (newData?.d156Code) {
        newData.d156Code = newData?.d156Code.split(',');
      }
      if (newData?.startDate) {
        newData.startDate = moment(newData?.startDate);
      }
      // 图片
      if (!_isEmpty(newData['filePath'])) {
        let imgObj = newData['filePath'];
        let temp: any = [];
        let xhr = new XMLHttpRequest();
        //GET请求,请求路径url,async(是否异步)
        xhr.open('GET', `/api${imgObj['url']}`, true);
        xhr.setRequestHeader('Authorization', sessionStorage.getItem('token') || '');
        xhr.setRequestHeader('dataApi', sessionStorage.getItem('dataApi') || '');
        //设置响应类型为 blob
        xhr.responseType = 'blob';
        //发送请求
        xhr.send();
        //关键部分
        xhr.onload = function (e) {
          //如果请求执行成功
          if (this.status === 200) {
            let blob = this.response;
            blobToBase64(blob).then((res) => {
              temp.push({ uid: new Date().valueOf(), thumbUrl: res, ...imgObj });
              setFileList(temp);
            });
          }
        };
      }
      form.setFieldsValue(newData);
    }
  };

  const hadndleFinish = async (vals: any) => {
    // console.log('vals==', vals);
    // return
    if (vals?.contactCode) {
      vals.contactCode = vals?.contactCode.toString();
    }
    if (vals?.startDate) {
      vals.startDate = moment(vals?.startDate).valueOf();
    }
    if (vals?.d156Code) {
      if (_isArray(vals?.d156Code)) {
        let codeArr: any = [];
        let nameArr: any = [];
        vals?.d156Code.map((item: any, index) => {
          if (item?.key) {
            codeArr.push(item['key']);
            nameArr.push(item['name']);
          } else {
            codeArr.push(item);
            nameArr = basinInfo?.d156Name || [];
          }
        });
        vals.d156Name = nameArr.toString();
        vals.d156Code = codeArr.toString();
      }
    }
    if (_isObject(vals?.filePath)) {
      if (!(vals?.filePath?.id && vals?.filePath?.name && vals?.filePath?.url)) {
        const { fileList = [] } = vals?.filePath;
        let fileObj = {};
        if (fileList.length > 0) {
          fileObj = fileList[0];
        } else {
          vals['filePath'] = undefined;
        }
        if (fileObj['response'] && fileObj['response']['code'] == '0') {
          vals['filePath'] = fileObj['response']['data'][0];
        } else {
          vals['filePath'] = undefined;
        }
      }
    }

    // return;
    setConfirmLoading(true);
    let url = addContactWork;
    if (basinInfo?.code) {
      url = updateContactWork;
    }
    const { code: resCode = 500 } = await url({
      data: {
        orgCode: code,
        orgLevelCode: orgCode,
        ...basinInfo,
        ...vals,
      },
    });
    setConfirmLoading(false);
    if (resCode == 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
    }
  };
  const handleCancel = () => {
    const { onOk } = props;
    setVisible(false);
    setBasinInfo({});
    form.resetFields();
    setFileList([]);
    onOk && onOk();
  };

  return (
    <Modal
      title={title}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={'800px'}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
        <Form.Item
          label="支部联系人"
          name={'contactCode'}
          rules={[{ required: true, message: '党支部联系人姓名' }]}
        //   initialValue={basinInfo['contactCode'] ? basinInfo['contactCode'].split(',') : undefined}
        >
          <Select
            mode="multiple"
            allowClear
            style={{ width: '100%' }}
            placeholder="请选择"
            //   defaultValue={['a10', 'c12']}
            //   onChange={handleChange}
            options={options}
          />
        </Form.Item>
        <Form.Item
          label="工作类型"
          name={'d156Code'}
          rules={[{ required: true, message: '工作类型' }]}
        //   initialValue={basinInfo['d156Code'] ? basinInfo['d156Code'].split(',') : undefined}
        >
          <DictSelect
            codeType={'dict_d156'}
            backType={'object'}
            mode="multiple"
            initValue={basinInfo['d156Code'] ? basinInfo['d156Code'].split(',') : undefined}
            placeholder={'请选择工作类型'}
          />
        </Form.Item>
        <Form.Item
          label="工作开展时间"
          name={'startDate'}
          rules={[{ required: true, message: '工作开展时间' }]}
        //   initialValue={basinInfo['startDate'] ? moment(basinInfo['startDate']) : undefined}
        >
          <DateTime />
        </Form.Item>
        <Form.Item
          label="工作记录"
          name={'workRecord'}
          rules={[{ required: true, message: '工作记录' }]}
        //   initialValue={basinInfo['workRecord']}
        >
          <Input.TextArea rows={4} placeholder="请简要描述活动内容，禁止上传涉密信息及敏感信息" maxLength={200} />
        </Form.Item>
        {/* <Form.Item
          label="上传图片"
          name={'filePath'}
          //   initialValue={getInitFileList(basinInfo['filePath'])}
          rules={[{ required: false, message: '上传图片' }]}
        >
          <Upload
            {...upProps}
            listType="picture-card"
            fileList={fileList}
            onPreview={handlePreview}
            onChange={imgChange}
            beforeUpload={(file, fileList) => {
              const { name = '', size = 0 } = file;
              let hasChinese = /[\u4e00-\u9fa5]/g.test(name);
              let fileSize: number = file['size'] / 1024 / 1024;
              return new Promise(async (resolve, reject) => {
                if (hasChinese) {
                  form.setFields([
                    {
                      name: 'filePath',
                      value: fileList,
                      errors: ['文件名不能包含中文，否则会上传失败。请修改后重新上传'],
                    },
                  ]);
                  // reject(Upload.LIST_IGNORE);
                  return Upload.LIST_IGNORE;
                }
                // 图片大于1M压缩为1M
                if (fileSize > 1) {
                  compressAccurately(file, 1024).then((res) => {
                    resolve(res);
                  });
                } else {
                  resolve(file);
                }
              });
            }}
          >
            {fileList.length >= 1 ? null : uploadButton}
          </Upload>
        </Form.Item> */}
        <Form.Item
          label="备注说明"
          name={'remark'}
          rules={[{ required: false, message: '备注说明' }]}
        //   initialValue={basinInfo['remark']}
        >
          <Input maxLength={100} />
        </Form.Item>
      </Form>
      <Modal
        visible={previewOpen}
        title={previewTitle}
        footer={null}
        onCancel={() => {
          setPreviewOpen(false);
        }}
      >
        <img alt="image" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </Modal>
  );
};
// @ts-ignore
export default React.forwardRef(index);
