/**
 * 添加届次信息
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, Input, Modal, Row, Select } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import moment from 'moment';
import Tip from '@/components/Tip';
import Date from '@/components/Date';
import { addElect, updateElect } from '@/pages/[unit]/services';
import { isEmpty } from '@/utils/method';

const FormItem = Form.Item;
const Option = Select.Option;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};

class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      confirmLoading: false
    };
  }

  showModal = () => {
    this.setState({
      visible: true,
      startValue: undefined,
      endValue: undefined,
    });
  };

  handleOk = () => {
    let { unit: { basicInfo: { code: unitCode = '' } = {} } = {}, onClose, queryList, dataInfo = {}, type = '' } = this.props;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if (val['tenureEndDate']) {
          val['tenureEndDate'] = val['tenureEndDate'].valueOf();
        }
        if (val['tenureStartDate']) {
          val['tenureStartDate'] = val['tenureStartDate'].valueOf();
        }

        if (moment(val['tenureEndDate']).subtract(5, 'years') <= val['tenureStartDate']) {
          this.props.form.setFields({
            tenureEndDate: { errors: [new Error('结束时间要间隔5年以上')] }
          });
          return;
        }
        this.setState({ confirmLoading: true })
        let url = addElect;
        if (type === 'edit') {
          url = updateElect;
        }
        const { code = 500 } = await url({
          data: {
            ...val,
            unitCode,
            code: dataInfo['code'],
          },
        });
        this.setState({ confirmLoading: true })
        if (code === 0) {
          Tip.success('操作提示', '操作成功');
          this.handleCancel();
          onClose && onClose();
          queryList && queryList();
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false,
    });
    this.props.onClose();
  };
  onStartChange = (value) => {
    this.setState({
      startValue: value,
    });
  };
  onEndChange = (value) => {
    this.setState({
      endValue: value,
    });
  };
  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.endOf('day') <= startValue.endOf('day');
  };
  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.endOf('day') <= startValue.endOf('day');
  };

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    let { children, title, dataInfo = {}, unit: { basicInfo = {} } = {} } = this.props;
    const { confirmLoading } = this.state;
    if (title === '新增届次信息') { dataInfo = {} }
    return (
      <React.Fragment>
        {
          children ? React.cloneElement(children as any, {
            onClick: this.showModal,
            key: 'container',
          }) : null
        }
        {
          this.state.visible &&
          <Modal
            title={title || '请输入标题'}
            visible={this.state.visible}
            onOk={() => this.handleOk()}
            onCancel={this.handleCancel}
            width={900}
            className='add_modal'
            maskClosable={false}
            confirmLoading={confirmLoading}
          >
            <WhiteSpace />
            <Form {...formItemLayout}>
              <Row>
                <Col span={24}>
                  <FormItem label="单位名称" {...formItemLayout1}>
                    {basicInfo['name']}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="开始时间"
                  >
                    {getFieldDecorator('tenureStartDate', {
                      initialValue: dataInfo['tenureStartDate'] ? moment(dataInfo['tenureStartDate']) : undefined,
                      rules: [{ required: true, message: '请选择开始时间' }],
                      // <DatePicker placeholder="请选择" style={{width:'100%'}} onChange={this.onStartChange} disabledDate={this.disabledStartDate}/>
                    })(
                      <Date />,
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="结束日期"
                  >
                    {getFieldDecorator('tenureEndDate', {
                      initialValue: dataInfo['tenureEndDate'] ? moment(dataInfo['tenureEndDate']) : undefined,
                      rules: [{ required: true, message: '请选择结束日期 ' }],
                      // <DatePicker placeholder="请选择" style={{width:'100%'}} onChange={this.onEndChange} disabledDate={this.disabledEndDate}/>
                    })(
                      <Date startTime={getFieldValue('tenureStartDate')} isDefaultEnd={false} />,
                    )}
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </Modal>
        }

      </React.Fragment>
    );
  }
}

export default Form.create<any>()(index);
