/**
 * 党代表新增和编辑
 */
import React from 'react'
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, Modal, Row, Input, Radio, DatePicker, Switch, Select } from 'antd';
import {connect} from "dva";
import { isEmpty } from '@/utils/method';
import { getSession } from '@/utils/session';
import DictSelect from '@/components/DictSelect';
import MemSelect from '@/components/MemSelect'
import styles from './index.less'
import moment from 'moment'
import DictTreeSelect from '@/components/DictTreeSelect';
import Notice from '@/components/Notice';
import Date from '@/components/Date';
import { validateLength } from '@/utils/formValidator';

const FormItem=Form.Item;
const RadioGroup = Radio.Group;
const Option = Select.Option;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
const menuData=[
  {
    code:'1',
    name:'基本信息',
    icon:'star',
  },
  {
    code:'2',
    name:'',
    icon:'qrcode',
  },
];
@connect(({unit,commonDict,loading})=>({unit,commonDict,loading:loading.effects['unit/getList']}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    let obj=menuData[0];
    this.state={
      visible:false,
      key:obj['code'],
      keyPath:[obj['code']],
      selected:obj,
      vsbTime:true
    };
  }

  showModal=()=>{
    const { dataInfo={},keyword='',title=''} = this.props;
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
      org:org
    },()=>{
      this.bfSubElectList();
    });
    if (title === '编辑') {
      this.props.dispatch({
        type:'behalf/bfFindByCode',
        payload:{
          code:dataInfo['code']
        }
      }).then(res=>{
        this.setState({data:res['data'],qualification:res['data']['d45Code'],leadInfo:res['data']['d46Code'],disabledTime:moment(res['data']['zbStopDate'])})
      })
    }
  };

  bfSubElectList=()=>{
    this.props.dispatch({
      type:'behalf/bfSubElectList',
      payload:{
        orgCodeLevel:this.state['org']['orgCode']
      }
    }).then(res=>{
      if (res['code'] === 0) {
        this.setState({jcInfo:res['data']})
      }else {
        Notice("操作提示",res['message'],"exclamation-circle-o","orange");
      }
    })
  };

  handleOk=()=>{
    this.props.form.validateFieldsAndScroll((err,val)=> {
      if (err) {
        return
      }
      const { onChange,title='' }=this.props;
      const { unitInfo,jcInfo,data={} }=this.state;
      if (title==='新增'){
        const { d24,d44,d45,d46,d61,isAdvanced,isMember,isTeach,memName,startDate,zbStopDate,...value } =val;
        let jc = jcInfo.filter(i=>i.code===d61);
        let v={
          memName:memName[0]['name']||'',
          memCode:memName[0]['code']||'',
          orgCode:memName[0]['orgCode']||'',
          representativeOrgCode:memName[0]['memOrgCode']||'',
          orgName:memName[0]['orgName']||'',
          unitCode:unitInfo['mainUnitCode']||'',
          unitName:unitInfo['mainUnitName']||'',
          electCode:jc[0]['code']||'',
          electName:jc[0]['electName']||'',
          d61Code:jc[0]['d61Code']||'',
          d61Name:jc[0]['d61Name']||'',
          d44Code:d44['key']||'',
          d44Name:d44['name']||'',
          d46Code:d46['key']||'',
          d46Name:d46['name']||'',
          isMember:isMember===undefined?0:isMember?1:0,
          isAdvanced:isAdvanced===undefined?0:isAdvanced?1:0,
          isTeach:isTeach===undefined?0:isTeach?1:0,
          d45Code:d45['key']||'',
          d45Name:d45['name']||'',
          d24Code:isEmpty(d24)?'':d24['key'],
          d24Name:isEmpty(d24)?'':d24['name'],
          zbStopDate:moment(moment(zbStopDate).format('YYYY-MM-DD')).valueOf()||'',
          startDate:moment(moment(startDate).format('YYYY-MM-DD')).valueOf()||'',
          ...value
        };
        this.props.dispatch({
          type:'behalf/bfAdd',
          payload:{
            data:{
              ...v
            }
          }
        }).then(res=>{
          if (res['code'] === 0) {
            Notice("操作提示",'保存成功!',"check-circle","green");
            this.handleCancel();
            onChange&&onChange();
          }
        })
      } else {
        const {  d24,d44,d45,d46,d61,isAdvanced,isMember,isTeach,memName,startDate,zbStopDate,...value  }=val;
        let jc = jcInfo.filter(i=>i.code===d61);
        let v={
          memName:isEmpty(memName[0]['name'])?data['memName']:memName[0]['name'],
          memCode:isEmpty(memName[0]['code'])?data['memCode']:memName[0]['code'],
          orgCode:isEmpty(memName[0]['orgCode'])?data['orgCode']:memName[0]['orgCode'],
          representativeOrgCode:isEmpty(memName[0]['memOrgCode'])?data['representativeOrgCode']:memName[0]['memOrgCode'],
          orgName:isEmpty(memName[0]['orgName'])?data['orgName']:memName[0]['orgName'],
          unitCode:isEmpty(unitInfo)?data['unitCode']:unitInfo['mainUnitCode'],
          unitName:isEmpty(unitInfo)?data['unitName']:unitInfo['mainUnitName'],
          electCode:jc[0]['code']||'',
          electName:jc[0]['electName']||'',
          d61Code:jc[0]['d61Code']||'',
          d61Name:jc[0]['d61Name']||'',
          d44Code:d44['key']||d44,
          d44Name:d44['name']||data['d44Name'],
          d46Code:d46['key']||d46,
          d46Name:d46['name']||data['d46Name'],
          isMember:isMember===undefined?0:isMember?1:0,
          isAdvanced:isAdvanced===undefined?0:isAdvanced?1:0,
          isTeach:isTeach===undefined?0:isTeach?1:0,
          d45Code:d45['key']||d45,
          d45Name:d45['name']||data['d45Name'],
          d24Code:isEmpty(d24)?d24:d24['key'],
          d24Name:isEmpty(d24)?data['d24Name']:d24['name'],
          zbStopDate:moment(moment(zbStopDate).format('YYYY-MM-DD')).valueOf()||'',
          startDate:moment(moment(startDate).format('YYYY-MM-DD')).valueOf()||'',
          code:data['code'],
          ...value
        };
        this.props.dispatch({
          type:'behalf/bfUp',
          payload:{
            data:{
              ...v
            }
          }
        }).then(res=>{
          if (res['code'] === 0) {
            Notice("操作提示",'保存成功!',"check-circle","green");
            this.handleCancel();
            onChange&&onChange();
          }else {
            Notice("操作提示",res['message'],"exclamation-circle-o","orange");
          }
        })
      }
    });
    // this.handleCancel();
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      data:{}
    });
    this.props.form.resetFields();
  };
  open=()=>{
    this.setState({
      visible:true,
    })
  };

  onSelect=(item)=>{
    const {key,keyPath}=item;
    const selected=menuData.find(obj=>obj['code']===key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  isMem=(v)=>{
    this.setState({memInfo:v[0]||{}});
    this.props.form.resetFields(['org','unit']);
    this.props.dispatch({
      type:'behalf/bfUnitByOrgCode',
      payload:{
        orgCode:v[0]['orgCode'],
      }
    }).then(res=>{
      if (res['code'] === 0) {
        this.setState({unitInfo:res['data']})
      }else {
        Notice("操作提示",res['message'],"exclamation-circle-o","orange");
      }
    })
 };
  getD46=(v)=>{
    this.setState({leadInfo:isEmpty(v)?'000':v['key']})
  };
  getD45=(v)=>{
    this.setState({qualification:v['key']})
  };
  changeJc=(v)=>{
    const { jcInfo=[] }=this.state;
    let a = jcInfo.filter(i=>i['code']===v);
    this.setState({
      disabledTime:a[0]['startDate']
    })
  };
  changeD44=(v)=>{
    let d44d=true;
    if (v['key'] === '1') {
      d44d=true
    }else {
      d44d=false
    }
    this.setState(
      {
        vsbTime:d44d
      }
    )
  };
  disabledTomorrow=(current)=>{
    return current && current < moment(this.state['disabledTime']).endOf('day');
  };

  render() {
    const {visible,org,memInfo={},unitInfo={},jcInfo=[],leadInfo={},qualification='',data={},disabledTime=null}=this.state;
    const { children ,title,dataInfo={}}=this.props;
    const { getFieldDecorator  } = this.props.form;
    let isLead=false;
    if (leadInfo==='0111'||leadInfo==='0112'||leadInfo==='0113'||leadInfo==='0114') {
      isLead=true
    }else {
      isLead=false
    }

    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:()=>this.showModal(),
            key: 'container'
          }) : null
        }
        <Modal
          title={title||''}
          className='behalf_Modal'
          destroyOnClose
          closable={false}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1000}
        >
          <div className='container'>
            <Form {...formItemLayout}>
              <Row>
                <Col span={12}>
                  <FormItem
                    label={'姓名'}
                  >
                    {getFieldDecorator('memName', {
                      initialValue:isEmpty(data)?null:data['memCode'],
                      rules: [
                        { required: true, message: '必填!' },
                        {validator: (...e)=>validateLength(e, 16, 50)}
                        // { validator: this.validFunction }
                      ],
                    })(
                      <MemSelect org={org} onChange={this.isMem} initValue={data['memName']}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'所在党组织'}
                  >
                    {getFieldDecorator('org', {
                      initialValue:isEmpty(memInfo)?data['orgName']:memInfo['orgName'],
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <Input placeholder={'根据党代表生成党组织'} disabled={true}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'所在选举单位'}
                  >
                    {getFieldDecorator('unit', {
                      initialValue:isEmpty(unitInfo)?data['unitName']:unitInfo['mainUnitName'],
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <Input placeholder={'根据党代表生成选举单位'} disabled={true}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'所属届次'}
                  >
                    {getFieldDecorator('d61', {
                      initialValue:isEmpty(data)?undefined:data['electCode'],
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <Select placeholder='请选择' onChange={this.changeJc} disabled={title==='编辑'}>
                        {
                          jcInfo&&jcInfo.map((item,index)=>{
                            return <Option key={index} value={item['code']}>{item['electName']}</Option>
                          })
                        }
                      </Select>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'当选类型'}
                  >
                    {getFieldDecorator('d44', {
                      initialValue:isEmpty(data)?null:data['d44Code'],
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <DictSelect placeholder={'根据所选单位自动生成届次类别'} codeType={'dict_d44'} backType={'object'} initValue={ data['d44Code']} onChange={this.changeD44}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'当选日期'}
                  >
                    {getFieldDecorator('startDate', {
                      initialValue:isEmpty(data)?isEmpty(disabledTime)?undefined:moment(disabledTime):moment(data['startDate']),
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                        // <DatePicker placeholder={'根据届次生成时间'} disabled={this.state['vsbTime']}/>
                      ],
                    })(
                      
                      <Date  disabled={this.state['vsbTime']} />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'当选时身份'}
                  >
                    {getFieldDecorator('d46', {
                      initialValue:isEmpty(data)?null:data['d46Code'],
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <DictTreeSelect codeType={'dict_d46'} parentDisable={true} backType={'object'} onChange={this.getD46} initValue={data['d46Code']||undefined}/>
                    )}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label={'当选时职务'}
                  >
                    {getFieldDecorator('positionName', {
                      initialValue:isEmpty(data)?null:data['positionName'],
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <Input placeholder={'请填写当选时职务'}/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'是否委员'}
                  >
                    {getFieldDecorator('isMember', {
                      initialValue:isEmpty(data)?undefined:data['isMember']===1,
                      valuePropName:'checked',
                      // rules: [
                      //   { required: true, message: '必填!' },
                      //   { validator: this.validFunction }
                      // ],
                    })(
                      <Switch checkedChildren="是" unCheckedChildren="否"/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'是否先进模范人物'}
                  >
                    {getFieldDecorator('isAdvanced', {
                      initialValue:isEmpty(data)?null:data['isAdvanced']===1,
                      valuePropName:'checked',
                      // rules: [
                      //   { required: true, message: '必填!' },
                      //   { validator: this.validFunction }
                      // ],
                    })(
                      <Switch checkedChildren="是" unCheckedChildren="否" />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'参与各级党组织培训'}
                  >
                    {getFieldDecorator('isTeach', {
                      initialValue:isEmpty(data)?null:data['isTeach']===1,
                      valuePropName:'checked',
                      // rules: [
                      //   { required: true, message: '必填!' },
                      //   { validator: this.validFunction }
                      // ],
                    })(
                      <Switch checkedChildren="是" unCheckedChildren="否"/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={'代表资格状态'}
                  >
                    {getFieldDecorator('d45', {
                      initialValue:isEmpty(data)?null:data['d45Code'],
                      rules: [
                        { required: true, message: '必填!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <DictSelect codeType={'dict_d45'} backType={'object'} onChange={this.getD45} initValue={data['d45Code']||undefined}/>
                    )}
                  </FormItem>
                </Col>
                {
                  isLead&&
                  <Col span={12}>
                    <FormItem
                      label={'管理单位'}
                    >
                      {getFieldDecorator('gbManageUnit', {
                        initialValue:isEmpty(data)?null:data['gbManageUnit'],
                        rules: [
                          { required: true, message: '必填!' },
                          // { validator: this.validFunction }
                        ],
                      })(
                        <Input placeholder={'管理单位'}/>
                      )}
                    </FormItem>
                  </Col>
                }
                {
                  (qualification==='2'||qualification==='3')&&
                  <React.Fragment>
                      <Col span={12}>
                        <FormItem
                          label={'资格终止类型'}
                        >
                          {getFieldDecorator('d24', {
                            initialValue:isEmpty(data)?null:data['d24Code'],
                            rules: [
                              { required: true, message: '必填!' },
                              // { validator: this.validFunction }
                            ],
                          })(
                            <DictSelect codeType={'dict_d24'} backType={'object'} initValue={data['d24Code']||undefined} />
                          )}
                        </FormItem>
                      </Col>
                      <Col span={12}>
                        <FormItem
                          label={'资格终止备注'}
                        >
                          {getFieldDecorator('zbStopRemark', {
                            initialValue:isEmpty(data)?null:data['zbStopRemark'],
                            // rules: [
                            //   { required: true, message: '必填!' },
                            //   { validator: this.validFunction }
                            // ],
                          })(
                            <Input placeholder={'请填写资格终止备注'}/>
                          )}
                        </FormItem>
                      </Col>
                      <Col span={12}>
                        <FormItem
                          label={'资格终止日期'}
                        >
                          {getFieldDecorator('zbStopDate', {
                            initialValue:isEmpty(data)?null:moment(data['zbStopDate']),
                            rules: [
                              { required: true, message: '必填!' },
                              // { validator: this.validFunction }
                              // <DatePicker disabledDate={this.disabledTomorrow}/>
                            ],
                          })(
                            
                            <Date disabledDate={this.disabledTomorrow} />
                          )}
                        </FormItem>
                      </Col>
                    </React.Fragment>
                }

              </Row>
            </Form>
          </div>
        </Modal>
      </React.Fragment>

    )
  }
}
export default Form.create()(index);
