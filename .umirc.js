const path = require('path');
const pxtorem = require('postcss-pxtorem');

// ref: https://umijs.org/config/
function getUTCDate(utc) {
  let d = new Date();
  let timeOffsetInHours = new Date().getTimezoneOffset() / 60 - utc;
  d.setHours(d.getHours() + timeOffsetInHours);
  return d;
}
let webpackConfig = {
  nodeModulesTransform: {
    type: 'none',
    exclude: [],
  },
  dva: {
    hmr: true,
  },
  antd: {
    // compact: true,
  },
  hash: true,
  // publicPath: "./",
  // history: {
  //   type: 'hash',
  // },
  dynamicImport: {
    loading: '@/components/Loading',
  },
  title: '贵州省综合党务管理系统',
  targets: {
    ie: 9,
    firefox: 45,
  },
  theme: {
    '@font-size-base': '15px',
  },
  alias: {
    src: path.resolve(__dirname, 'src/'),
  },
  ignoreMomentLocale: true,
  metas: [{ 'http-equiv': 'renderer', content: `webkit` }],
  define: {
    'process.env.hideConsole': false,
    'process.env.idCheck': process.env.idCheck != 'false' ? 'true' : 'false', //是否启用身份证校验
    'process.env.NODE_ENV': process.env.NODE_ENV === 'production' ? 'production' : 'development',
    'process.env.buildTime': `${getUTCDate(-8).toLocaleString()} (Beijing time)`,
  },
  chunks: ['vendors', 'antd', 'umi', 'file', 'echarts', 'pages'],
  chainWebpack: function (config) {
    config.merge({
      optimization: {
        splitChunks: {
          chunks: 'all',
          minSize: 30000, // 降低到30KB，创建更多小块以提高缓存效率
          minChunks: 2, // 至少被2个模块引用才分离
          maxAsyncRequests: 20, // 增加异步请求数量，支持更细粒度的分割
          maxInitialRequests: 8, // 控制初始加载的chunk数量，避免过多并发请求
          cacheGroups: {
            // React 核心库
            // react: {
            //   name: 'react',
            //   test: /[\\/]node_modules[\\/](react|react-dom|react-router|react-router-dom)[\\/]/,
            //   priority: 30,
            //   chunks: 'all',
            //   enforce: true,
            // },
            // ECharts 图表库
            echarts: {
              name: 'echarts',
              test: /[\\/]node_modules[\\/](echarts|echarts-for-react)[\\/]/,
              priority: 25,
              chunks: 'all',
              enforce: true,
            },
            // Ant Design 组件库
            antd: {
              name: 'antd',
              test: /[\\/]node_modules[\\/](@ant-design|antd|ant-design-pro)[\\/]/,
              priority: 24,
              chunks: 'all',
              enforce: true,
            },
            // 工具库 (lodash, moment等)
            // utils: {
            //   name: 'utils',
            //   test: /[\\/]node_modules[\\/](lodash|moment|crypto-js|md5|base-64)[\\/]/,
            //   priority: 23,
            //   chunks: 'all',
            //   enforce: true,
            // },
            // 文件处理库
            file: {
              name: 'file',
              test: /[\\/]node_modules[\\/](exceljs|file-saver|luckyexcel)[\\/]/,
              priority: 21,
              chunks: 'all',
              enforce: true,
            },
            // 其他第三方库
            vendors: {
              name: 'vendors',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
              chunks: 'all',
            },
            // // 项目组件库
            // components: {
            //   name: 'components',
            //   test: /[\\/]src[\\/]components[\\/]/,
            //   priority: 20,
            //   chunks: 'all',
            //   enforce: true,
            // },
            // 通用页面模块
            pages: {
              name: 'pages',
              test: /[\\/]src[\\/]pages[\\/]/,
              priority: 15,
              chunks: 'all',
              minChunks: 6,
              enforce: true,
            },
          },
        },
      },
    });
  },
  proxy: {
    '/api': {
      target: 'http://192.168.0.151',
      // target: 'http://192.168.0.76:8001/',
      // target: 'http://172.18.40.61:8089/',
      // target: 'http://172.18.40.105:8080',
      // "target": "http://djgb.zt.com.cn:28088/",
      changeOrigin: true,
      pathRewrite: { '^/api': '/api' },
      // pathRewrite: { '^/api': '' },
    },
  },
  extraPostCSSPlugins: [
    pxtorem({
      rootValue: 37.5, //这里根据设计稿大小配置,一般是375
      propList: ['*', '!border-top', '!border-right', '!border-bottom', '!border-left', '!border-width'],
      selectorBalckList: ['.am-'], // 匹配不被转换为rem的选择器，例如UI框架antd-mobile
      exclude: (e) => {
        if (/src(\\|\/)pages(\\|\/)qzs(\\|\/)mobile/.test(e)) {
          return false;
        } else {
          return true;
        }
      },
    }),
  ],
};
if (process.env.NODE_ENV === 'production') {
  webpackConfig = {
    ...webpackConfig,
    outputPath: process.env.idCheck == 'false' ? 'dist-noCheck' : 'dist',
    define: {
      ...webpackConfig['define'],
      'process.env.hideConsole': true,
    },
  };
}
export default webpackConfig;
