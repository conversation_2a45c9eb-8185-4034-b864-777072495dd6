/**
 * 党员流出登记
 */

import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Avatar, Button, Col, Dropdown, Menu, Modal, Row, Input, Radio, DatePicker } from 'antd';
import qs from 'qs';
import { connect } from 'dva';
import { _history as router } from '@/utils/method';
import { isEmpty } from '@/utils/method';
import { getSession } from '@/utils/session';
import DictArea from '@/components/DictArea';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import OrgSelect from '@/components/OrgSelect';
import MemSelect from '@/components/MemSelect';
import moment from 'moment';
import { root } from '@/common/config';
import styles from './index.less';
import Date from '@/components/Date';
import { findOrgByCode, countIsProvOut } from '../../service/index';
import SearchOrg from '@/components/SearchOrg';
import { formLabel } from '@/utils/method';
import Tip from '@/components/Tip';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const menuData = [
  {
    code: '1',
    name: '基本信息',
    icon: 'star',
  },
  {
    code: '2',
    name: '班子成员',
    icon: 'qrcode',
  },
];
@connect(
  ({ unit, commonDict, loading, flowMem }) => ({
    flowMem,
    unit,
    commonDict,
    loading: loading.effects['unit/getList'],
  }),
  undefined,
  undefined,
  { forwardRef: true },
)
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    let obj = menuData[0];
    this.state = {
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    };
  }

  showModal = () => {
    const { data: { id = '' } = {}, keyword = '' } = this.props;
    let org = getSession('org') || {};
    this.setState({
      visible: true,
    });
  };
  handleOk = () => {
    this.props.form.validateFieldsAndScroll(async (errors, values) => {
      if (errors) {
        return;
      }
      const { memInfo, addr } = this.state;
      const { onChange } = this.props;
      const {
        account111,
        account2,
        outflowAreaId,
        outflowAreaName,
        outflowOrgCode,
        outflowDate,
        outflowReasonCode,
        isExplicitInflowOrg,
        outflowUnitTypeCode,
        outflowOrgLinkman,
        outflowOrgPhone,
        isHold,
        outflowReason,
        inflowOrgName1,
        ...val
      } = values;
      let vall = {
        memCode: isEmpty(account111) ? '' : account111[0]['code'],
        // memOrgName:isEmpty(account111)?'':account111[0]['orgName'],
        memOrgCode: isEmpty(account111) ? '' : account111[0]['orgCode'],
        memOrgOrgCode: isEmpty(account111) ? '' : account111[0]['memOrgCode'],
        isProvOut: isEmpty(account2) ? '' : account2['id'],
        isProvOutName: isEmpty(account2) ? '' : account2['name'],
        outflowTypeCode: isEmpty(account2) ? '' : account2['id'],
        outflowTypeName: isEmpty(account2) ? '' : account2['name'],
        outflowAreaId: isEmpty(outflowAreaId) ? '' : outflowAreaId,
        outflowAreaName: isEmpty(outflowAreaName) ? '' : outflowAreaName,
        outflowOrgCode: isEmpty(outflowOrgCode) ? '' : outflowOrgCode['code'],
        outflowOrgName: isEmpty(outflowOrgCode) ? inflowOrgName1 : outflowOrgCode['name'],
        outflowOrgOrgCode: isEmpty(outflowOrgCode) ? '' : outflowOrgCode['orgCode'],
        outflowReasonCode: isEmpty(outflowReasonCode) ? '' : outflowReasonCode['id'],
        outflowReasonName: isEmpty(outflowReasonCode) ? '' : outflowReasonCode['name'],
        outflowDate: isEmpty(outflowDate) ? '' : moment(outflowDate).valueOf(),
        isExplicitInflowOrg: isEmpty(isExplicitInflowOrg) ? null : isExplicitInflowOrg,
        outflowUnitTypeCode: isEmpty(outflowUnitTypeCode) ? '' : outflowUnitTypeCode['id'],
        outflowUnitTypeName: isEmpty(outflowUnitTypeCode) ? '' : outflowUnitTypeCode['name'],
        outflowOrgLinkman: isEmpty(outflowOrgLinkman) ? '' : outflowOrgLinkman,
        outflowOrgPhone: isEmpty(outflowOrgPhone) ? '' : outflowOrgPhone,
        isHold: isEmpty(isHold) ? null : isHold,
        outflowReason: isEmpty(outflowReason) ? '' : outflowReason,
      };

      for (let o in vall) {
        if (isEmpty(vall[o])) {
          delete vall[o];
        }
      }
      this.props
        .dispatch({
          type: 'flowMem/add',
          payload: {
            data: {
              ...vall,
            },
          },
        })
        .then((res) => {
          if (res.code === 0) {
            Tip.success('操作提示','操作成功')
            onChange(true);
            this.handleCancel();
          }
        });
    });
    //
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      showOrgName: '',
    });
    this.props.form.resetFields();
  };
  open = () => {
    this.setState({
      visible: true,
    });
  };
  destroy = () => {
    let obj = menuData[0];
    this.setState({
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    });
    this.props.dispatch({
      //重置model
      type: 'unit/updateState',
      payload: {
        basicInfo: {},
      },
    });
  };
  onSelect = (item) => {
    const { key, keyPath } = item;
    const selected = menuData.find((obj) => obj['code'] === key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  onPageChange = (page, pageSize) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`);
  };
  outMem = (e) => {
    this.setState({ memInfo: e });
    this.getContactInfo(e[0].code || '');
  };
  // 获取联系人 组织联系方式
  getContactInfo = async (memCode) => {
    const { code = 500, data = {} } = await findOrgByCode({ code: memCode });
    if (code === 0) {
      this.props.form.setFieldsValue({
        outflowOrgLinkman: data.outflowOrgLinkman || '',
        outflowOrgPhone: data.outflowOrgPhone || '',
      });
    }
  };
  outAddr = async (val, obj) => {
    this.setState({ addr: obj });
    const { code = 500, data = {} } = await countIsProvOut({ name: obj.name, key: obj.key });
    if (code === 0) {
      this.setState({
        account2: data,
      });
      this.props.form.setFieldsValue({
        account2: data,
        outflowAreaName: obj.name,
      });
    }
  };
  determine = (e) => {
    if (e.target.value === 0) {
      this.setState({ determine: e.target.value, showOrgName: '未明确' });
    } else {
      this.setState({ determine: e.target.value, showOrgName: '' });
    }
  };
  showOrg = (e) => {
    this.setState({ showOrgName: e['name'] });
  };
  showOrg1 = () => {
    let value = this.props.form.getFieldValue('inflowOrgName1');
    this.setState({ showOrgName: value });
  };
  disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };
  validFunction = (rule, value, callback) => {
    let han = /^[\u4e00-\u9fa5]+$/;
    if (value) {
      switch (rule.field) {
        case 'outflowOrgLinkman':
          if (value.length > 20) {
            return callback('组织联系人不能超过20个字符');
          } else if (!han.test(value)) {
            return callback('联系人名称不合法');
          }
          break;
        case 'outflowOrgPhone':
          if (!/^1[345789]\d{9}$/.test(value)) {
            return callback('手机号码不合法');
          }
          break;
      }
    }
    callback();
  };

  render() {
    const {
      visible,
      selected,
      keyPath,
      key,
      pagination = {},
      determine = undefined,
      addr = {},
      memInfo = {},
      showOrgName = '',
    } = this.state;
    const { filterHeight, loading, children, tipMsg = {} } = this.props;
    const { getFieldDecorator } = this.props.form;
    return (
      <React.Fragment>
        {children
          ? React.cloneElement(children as any, {
              onClick: this.showModal,
              key: 'container',
            })
          : null}
        <Modal
          title="党员流出登记"
          className="out_Modal"
          destroyOnClose
          closable={false}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={700}
        >
          <div className="container">
            {!isEmpty(memInfo) && (
              <div className={styles.head}>
                <div>
                  <p>党员:{memInfo[0]['name']}</p>
                  <p>党员类型:{memInfo[0]['d08Name']}</p>
                </div>
                <div>
                  <p>
                    <span>从:</span>
                    {memInfo[0]['orgName']}
                  </p>
                  <p>
                    <span>到:</span>
                    {showOrgName}
                  </p>
                </div>
              </div>
            )}
            <Form {...formItemLayout}>
              <FormItem label={formLabel('选择党员', tipMsg['account111'])}>
                {getFieldDecorator('account111', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择党员!' },
                    // { validator: this.validFunction }
                  ],
                })(<MemSelect onChange={this.outMem} />)}
              </FormItem>
              <FormItem label={formLabel('流动去向', tipMsg['outflowAreaId'])}>
                {getFieldDecorator('outflowAreaId', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择流动去向!' },
                    // { validator: this.validFunction }
                  ],
                })(<DictArea onChange={this.outAddr} />)}
                {getFieldDecorator('outflowAreaName')(<Input style={{ display: 'none' }} />)}
              </FormItem>
              <FormItem label={formLabel('流动类型', tipMsg['account2'])}>
                {getFieldDecorator('account2', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: false, message: '请选择流动类型!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <DictSelect
                    codeType={'dict_d34'}
                    backType={'object'}
                    disabled={true}
                    initValue={this.state.account2?.id}
                  />,
                )}
              </FormItem>
              <FormItem label={formLabel('是否明确流入党组织', tipMsg['isExplicitInflowOrg'])}>
                {getFieldDecorator('isExplicitInflowOrg', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择是否明确!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <RadioGroup onChange={this.determine}>
                    <Radio value={1}>已明确</Radio>
                    <Radio value={0}>未明确</Radio>
                  </RadioGroup>,
                )}
              </FormItem>
              {
                determine === 1 ?
                <React.Fragment>
                  {
                    `${addr['id']}`.startsWith(root['areaCode']) ?
                    <FormItem label={formLabel('流入党支部', tipMsg['outflowOrgCode'])}>
                        {getFieldDecorator('outflowOrgCode', {
                          // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                          rules: [
                            { required: true, message: '请选择流入党支部!' },
                            // { validator: this.validFunction }
                          ],
                        })(
                          <SearchOrg
                            onChange={this.showOrg}
                            backType={'object'}
                            params={{ orgTypeList: ['3', '4'] }}
                          />,
                        )}
                      </FormItem>:
                      <FormItem label={formLabel('流入党支部', tipMsg['inflowOrgName1'])}>
                        {getFieldDecorator('inflowOrgName1', {
                          rules: [
                            { required: true, message: '请选择流入党支部!' },
                            { validator: this.validFunction },
                          ],
                        })(<Input placeholder={'请输入'} onBlur={this.showOrg1} />)}
                      </FormItem>
                  }
                </React.Fragment>:null
              }
              {!isEmpty(addr['id']) && addr['id'] !== root['areaCode'] && determine === 1 && (
                <React.Fragment>
                  <FormItem label={formLabel('流入单位类型', tipMsg['outflowUnitTypeCode'])}>
                    {getFieldDecorator('outflowUnitTypeCode')(
                      <DictTreeSelect
                        backType={'object'}
                        // initValue={basicInfo['d04Code']}
                        codeType={'dict_d04'}
                        placeholder={'单位类别'}
                        parentDisable={true}
                      />,
                      // <DictSelect codeType={'dict_d04'} backType={'object'}/>
                    )}
                  </FormItem>
                </React.Fragment>
              )}
              <FormItem label={formLabel('外出原因类型', tipMsg['outflowReasonCode'])}>
                {getFieldDecorator('outflowReasonCode', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择外出原因类型!' },
                    // { validator: this.validFunction }
                  ],
                })(<DictSelect codeType={'dict_d41'} backType={'object'} />)}
              </FormItem>
              <FormItem label={formLabel('流出党组织联系人', tipMsg['outflowOrgLinkman'])}>
                {getFieldDecorator('outflowOrgLinkman', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: false, message: '请填写联系人姓名!' },
                    // { validator: this.validFunction }
                  ],
                })(<Input disabled placeholder="请输入" />)}
              </FormItem>
              <FormItem label={formLabel('流出党组织联系方式', tipMsg['outflowOrgPhone'])}>
                {getFieldDecorator('outflowOrgPhone', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: false, message: '请填写手机号!' },
                    // { validator: this.validFunction }
                  ],
                })(<Input disabled placeholder="请输入" />)}
              </FormItem>
              {/* <FormItem
                label={'流动原因'}
              >
                {getFieldDecorator('outflowReason', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [{pattern:/(^[^\s]*$)/g, message:'不能输入空格'}],
                })(
                  <Input placeholder="请输入" />
                )}
              </FormItem> */}
              <FormItem label={formLabel('流动党员活动证', tipMsg['isHold'])}>
                {getFieldDecorator('isHold', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择流动党员活动证!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <RadioGroup>
                    <Radio value={1}>已发放</Radio>
                    <Radio value={0}>未发放</Radio>
                  </RadioGroup>,
                )}
              </FormItem>
              <FormItem label={formLabel('流出日期', tipMsg['outflowDate'])}>
                {getFieldDecorator('outflowDate', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择流出日期!' },
                    // { validator: this.validFunction }
                    // <DatePicker style={{width:'100%'}} disabledDate={this.disabledDate}/>
                  ],
                })(<Date disabledDate={this.disabledDate} />)}
              </FormItem>
            </Form>
          </div>
        </Modal>
      </React.Fragment>
    );
  }
}
export default Form.create()(index);
