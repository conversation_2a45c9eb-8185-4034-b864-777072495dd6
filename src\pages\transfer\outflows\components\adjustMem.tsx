/**
 * 人员调整
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Input, Modal, Radio, Row } from 'antd';
import {connect} from "dva";
import OrgSelect from '@/components/OrgSelect';
import MemSelect from '@/components/MemSelect';
import Tip from "@/components/Tip";
import Date_ from '@/components/Date';
import moment from 'moment'

const TextArea=Input.TextArea;
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
@connect((({transferOut,loading})=>({transferOut,loading:loading.effects['transferOut/adjustMem']})),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  static open(){}
  static close(){}
  constructor(props){
    super(props);
    this.state={
      visible:false,
      key:new Date().valueOf(),
    };
    index.open=this.open;
  }
  handleOk=()=>{
    this.props.form.validateFieldsAndScroll(async (err,val)=>{
      if(!err){
        let obj=undefined;
        let data:Array<object>=[];
        for(let obj of val['memId']){
          let pushObj={
            memId:obj['code'],
            srcOrgId:val['srcOrgId'][0]['code'],
            srcOrgName:val['srcOrgId'][0]['name'],
            targetOrgId:val['targetOrgId'][0]['code'],
            targetOrgName:val['targetOrgId'][0]['name'],
            type:'29',
            reason:val['reason'],
            transferOutTime: moment(val['transferOutTime']).valueOf()
          };
          data.push(pushObj);
        }
        obj=await this.props.dispatch({
          type:'transferOut/adjustMem',
          payload:{
            data:data
          }
        });
        if(obj && obj['code']===0){
          Tip.success('操作提示','关系转接申请已提交');
          this.props.refresh();
          this.handleCancel();
        }
      }
    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
    })
  };
  open=()=>{
    this.setState({
      visible:true,
    })
  };
  outOrgChange=()=>{
    this.props.form.setFieldsValue({outMem:undefined});
    this.setState({
      key:new Date().valueOf()
    },()=>{
      this['mem'].clearAll();
    })
  };
  render(){
    const {visible,key}=this.state;
    const {children,loading}=this.props;
    const { getFieldDecorator } = this.props.form;
    const outOrg=this.props.form.getFieldValue('srcOrgId');
    // console.log(outOrg,'rrrrrrrr')
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClik:this.open
          }) : null
        }
        <Modal
          destroyOnClose
          title="支部间人员调整"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          confirmLoading={loading}
          width={800}
        >
          <Form {...formItemLayout}>
            <Row>
              <Col span={12}>
                <FormItem
                  label="转接日期"
                  {...formItemLayout2}
                >
                  {getFieldDecorator('transferOutTime', {
                    rules: [{ required: true, message: '请输入转接日期' }],
                     // <DatePicker placeholder={'转接日期'}/>
                  })(

                    <Date_ />
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  label="转接类型"
                  {...formItemLayout2}
                >
                  {getFieldDecorator('time2', {
                    initialValue:1,
                    rules: [{ required: true, message: '请选择转接类型' }],
                  })(
                    <Radio checked={true}>支部间人员调整</Radio>
                  )}
                </FormItem>
              </Col>
            </Row>

            <FormItem
              label="转出党支部"
            >
              {getFieldDecorator('srcOrgId', {
                rules: [{ required: true, message: '请选择转出党支部' }],
              })(
                <OrgSelect onChange={this.outOrgChange} orgTypeList={['3','4']}/>
              )}
            </FormItem>

            <FormItem
              label="转接人员"
            >
              {getFieldDecorator('memId', {
                rules: [{ required: true, message: '请选择转接人员' }],
              })(
                <MemSelect key={key} org={outOrg ? outOrg[0] : undefined  } ref={e=>this['mem']=e} checkType={'checkbox'} disabled={!outOrg}/>
              )}
            </FormItem>

            <FormItem
              label="转入党支部"
            >
              {getFieldDecorator('targetOrgId', {
                rules: [{ required: true, message: '请选择转入党支部' }],
              })(
                <OrgSelect disabled={!outOrg} orgTypeList={['3','4']}/>
              )}
            </FormItem>

            <FormItem
              label="转接原因"
            >
              {getFieldDecorator('reason', {
                initialValue:'支部间人员调整',
                rules: [{ required: true, message: '请输入转接原因' }],
              })(
                <TextArea rows={4} placeholder={'转接原因'}/>
              )}
            </FormItem>
          </Form>
        </Modal>
      </React.Fragment>
    )
  }
}
export default Form.create()(index)
