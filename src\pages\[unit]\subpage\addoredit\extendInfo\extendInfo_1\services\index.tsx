import request from "@/utils/request";
import qs from 'qs';

// 街道扩展信息列表
export function getList(params) {
  return request(`/api/unit/streetsCadres/getList`,{
    method:'POST',
    body:params,
  });
}

// 新增街道扩展信息
export function add(params) {
  return request(`/api/unit/streetsCadres/add`,{
    method:'POST',
    body:params,
  });
}

// // 编辑街道扩展信息
// export function updateSecondary(params) {
//   return request(`/api/unit/secondary/updateSecondary`,{
//     method:'POST',
//     body:params,
//   });
// }

// 删除街道扩展信息
export function delByCode(params) {
  return request(`/api/unit/streetsCadres/delByCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}

// 街道扩展信息详情
export function findByCode(params) {
  return request(`/api/unit/streetsCadres/findByCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}