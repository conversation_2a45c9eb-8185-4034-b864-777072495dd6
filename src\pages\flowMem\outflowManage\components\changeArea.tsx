// 修改流入行政区
import React, { Fragment } from 'react';
import { connect } from 'dva';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import { Form } from '@ant-design/compatible';
import { Modal } from 'antd';
import moment from 'moment';
import _cloneDeep from 'lodash/cloneDeep';
import _isArray from 'lodash/isArray';
import { getSession } from '@/utils/session';
import { formLabel, findDictCodeName } from '@/utils/method';
import DictTreeSelect from '@/components/DictTreeSelect';
import Tip from '@/components/Tip';
import { updateAdministrativeDivision } from '../../service/index';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

// @ts-ignore
@connect(
  ({ unit, commonDict, loading, flowMem }) => ({
    flowMem,
    unit,
    commonDict,
    loading: loading.effects['unit/getList'],
  }),
  undefined,
  undefined,
  { forwardRef: true },
)
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  open = (record?: any) => {
    let title = '修改流入行政区';
    this.setState({
      modalTitle: title,
      visible: true,
      basicInfo: record,
    });
  };

  handleOk = async () => {
    this.setState({
      confirmLoading: true,
    });

    const { onOk } = this.props;
    const { basicInfo } = this.state;
    this.props.form.validateFieldsAndScroll(async (error, values) => {
      if (error) {
        this.setState({
          confirmLoading: false,
        });
        return;
      }
      const { code = 500 } = await updateAdministrativeDivision({
        data: {
          code: basicInfo?.code,
          ...values,
        },
      });
      this.setState({
        confirmLoading: false,
      });
      if (code == 0) {
        Tip.success('操作提示', '操作成功');
        this.handleCancel();
        onOk && onOk();
      }
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      modalTitle: '',
      confirmLoading: false,
      basicInfo: {},
    });
  };

  render() {
    const { onOk, children, tipMsg = {} } = this.props;
    const { visible, modalTitle = '', basicInfo = {}, confirmLoading = false } = this.state;
    const { getFieldDecorator, setFieldsValue, getFieldValue } = this.props.form;

    return (
      <div>
        {children
          ? React.cloneElement(children as any, {
            onClick: this.open,
            key: 'container',
          })
          : null}
        <Modal
          bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
          destroyOnClose
          maskClosable={false}
          width={500}
          title={modalTitle}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          confirmLoading={confirmLoading}
        >
          <Form {...formItemLayout}>
            <FormItem label={formLabel('流入行政区', tipMsg['outAdministrativeDivisionCode'])}>
              {getFieldDecorator('outAdministrativeDivisionCode', {
                initialValue: _isEmpty(basicInfo)
                  ? undefined
                  : basicInfo['outAdministrativeDivisionCode'],
                rules: [{ required: true, message: '请选择所在行政区域' }],
              })(
                <DictTreeSelect
                  parentDisable={true}
                  placeholder={'请选择行政区域'}
                  initValue={
                    _isEmpty(basicInfo) ? undefined : basicInfo.outAdministrativeDivisionCode
                  }
                  codeType={'dict_d151_flow'}
                />,
              )}
            </FormItem>
          </Form>
        </Modal>
      </div>
    );
  }
}

export default Form.create()(index);
