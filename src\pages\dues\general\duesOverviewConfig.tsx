import React from 'react';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _ceil from 'lodash/ceil';
const pathSymbols = {
  women: 'path://M692.288 506.048 636.544 303.552C628.928 280.64 602.048 240.32 556.928 238.4L467.392 238.4c-46.656 1.92-73.344 41.856-79.808 65.216l-55.68 202.304C319.936 550.144 371.072 567.36 383.616 525.888l49.792-186.624 14.016 0-85.632 327.808 80 0 0 246.336c0 44.608 60.608 44.608 60.608 0l0-246.336 18.944 0 0 246.336c0 44.608 58.688 44.608 58.688 0l0-246.336 82.24 0L574.784 339.264l15.872 0 49.856 186.624C652.8 568.32 703.616 550.144 692.288 506.048zM511.872 221.888c38.336 0 69.376-32.384 69.376-72.384 0-39.872-31.04-72.32-69.376-72.32s-69.312 32.448-69.312 72.32C442.56 189.504 473.536 221.888 511.872 221.888z',
  men: 'path://M500.840045 0.014c53.579783-0.691997 82.164667 25.284898 92.126627 60.441755 8.743965 30.841875-3.299987 66.980729-18.776924 80.392674-16.770932 14.533941-32.817867 25.226898-64.545738 25.231898h-15.257939c-12.41995-4.506982-23.101906-11.566953-32.859866-18.186926-7.082971-4.806981-28.079886-26.481893-24.647901-33.449865-4.95198-1.609993-10.336958-33.530864-7.043971-45.773814 6.229975-23.139906 21.899911-49.765798 41.079834-59.26776L500.840045 0.014z m106.799567 311.595737v642.548396c0 19.297922 1.635993 40.805835-5.869976 52.812786-6.147975 9.84496-17.26293 11.625953-28.754883 16.432933-4.291983 1.791993-15.262938-1.029996-17.596929-1.759992-6.394974-1.989992-9.737961-1.007996-14.67394-3.519986-23.508905-11.957952-17.606929-55.279776-17.606929-90.371634l-0.584998-342.691611c-6.437974-0.159999-16.041935-0.938996-20.536917 1.169995 0.133999 95.753612-1.179995 196.313204-1.179995 293.993809v84.497657c0.005 13.653945 2.632989 26.249894-2.33999 36.96485-7.949968 17.113931-33.299865 27.349889-56.92277 19.366922-10.659957-3.593985-19.329922-13.293946-22.300909-22.881907-2.659989-8.599965 0.203999-15.749936-1.759993-26.995891l-0.589998-125.577491-0.579997-533.987836c-5.293979 0.101-7.552969 1.221995-11.737953 2.342991v288.124832c-8.905964 12.714948-16.603933 37.151849-45.769814 28.749883-35.224857-10.139959-25.234898-71.802709-25.234898-116.189529V330.978659c0-66.922729-5.134979-110.255553 41.663831-132.031465 22.371909-10.407958 58.745762-7.629969 90.956632-7.629969h99.756595c25.703896 0 53.965781-0.389998 72.764705 4.109983 16.415933 3.932984 33.031866 16.121935 41.073834 28.756883 17.537929 27.536888 10.563957 111.428548 10.563957 157.262363v175.449289c0 33.643864 2.982988 61.412751-19.952919 72.174708-6.147975 2.885988-19.865919 2.35999-25.815895 0-25.269898-10.021959-22.892907-33.766863-22.886908-68.654722l-0.589997-248.804992H607.639612z',
};

export const cardConfig = [
  {
    key:'2001',
    value:{
      icon:'team',
      coverImg:require('@/components/CardsGroup/assets/dues/biaozhun.png'),
      iconColor:'#17C1C5',
      title:'标准设置',
      suffix:'人',
      action:'/api/chart/fee/getFeeStandardTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['name'] === '标准设置'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '已设置人数'){zNum = item['count']}
            if(item['name'] === '未设置人数'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>已设置人数:{zNum}人</div>
            <div>未设置人数:{yNum}人</div>
          </div>
        )
      }
    },
  },
  {
    key:'2006',
    value:{
      icon:'hourglass',
      coverImg:require('@/components/CardsGroup/assets/dues/jiaonao.png'),
      iconColor:'#6F79C1',
      title:'党费交纳',
      // decimals:2,
      suffix:'人',
      action:'/api/chart/fee/getFeePayTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['name'] === '党费交纳'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '已交纳人数'){zNum = item['count']}
            if(item['name'] === '未交纳人数'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>已交纳人数:{zNum}人</div>
            <div>未交纳人数:{yNum}人</div>
          </div>
        )
      }
    },
  },
  {
    key:'2007',
    value:{
      icon:'minus-square-o',
      coverImg:require('@/components/CardsGroup/assets/dues/jine.png'),
      iconColor:'#00A0FF',
      title:'党费金额',
      suffix:'元',
      action:'/api/chart/fee/getFeePayMoneyTotal',
      decimals:2,
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['name'] === '党费金额'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '已收取金额'){zNum = item['count']}
            if(item['name'] === '未收取金额'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>已收取金额{zNum}元</div>
            <div>未收取金额{yNum}元</div>
          </div>
        )
      },
    },
  },
  {
    key:'2008',
    value:{
      icon:'account-book',
      coverImg:require('@/components/CardsGroup/assets/dues/zhichujine.png'),
      iconColor:'#f3715c',
      title:'党费支出',
      suffix:'元',
      action:'/api/chart/fee/getFeeDisburseAndSurplus',
      decimals:2,
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['name'] === '党费支出'){
              num =  item['count']
            }
          });
        }
        return num;
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '总支出金额'){zNum = item['count']}
            if(item['name'] === '总结余金额'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>总支出金额:{zNum}元</div>
            <div>总结余金额:{yNum}元</div>
          </div>
        )}
    },
  },
  {
    key:'2009',
    value:{
      icon:'usergroup-add',
      coverImg:require('@/components/CardsGroup/assets/dues/xiabojine.png'),
      iconColor:'#00ae9d',
      title:'党费下拨',
      suffix:'元',
      action:'/api/chart/fee/getFeeAllocateTotal',
      end:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '总下拨金额'){zNum = item['count']}
            if(item['name'] === '已下拨金额'){yNum = item['count']}
          })
        }
        return Number(zNum)+Number(yNum);
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '总下拨金额'){zNum = item['count']}
            if(item['name'] === '已下拨金额'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>总下拨金额:{zNum}元</div>
            <div>已下拨金额:{yNum}元</div>
          </div>
        )}
    },
  },
  {
    key:'2010',
    value:{
      icon:'solution',
      coverImg:require('@/components/CardsGroup/assets/dues/duizhangjine.png'),
      iconColor:'#fdb933',
      title:'党费对账',
      suffix:'人',
      action:'/api/chart/fee/getFeeAndCheckTotal',
      end:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '总收取金额'){zNum = item['count']}
            if(item['name'] === '已对账金额'){yNum = item['count']}
          })
        }
        return Number(zNum)+Number(yNum);
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '总收取金额'){zNum = item['count']}
            if(item['name'] === '已对账金额'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>总收取金额:{zNum}元</div>
            <div>已对账金额:{yNum}元</div>
          </div>
        )}
    },
  },
  {
    key:'2012',
    value:{
      icon:'money-collect',
      coverImg:require('@/components/CardsGroup/assets/dues/zhichu.png'),
      iconColor:'#de46f3',
      title:'党费支出',
      suffix:'元',
      action:'/api/chart/fee/getFeeDisburseTotal',
      decimals:2,
      end:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '手动支出金额'){zNum = item['count']}
            if(item['name'] === '账单支出金额'){yNum = item['count']}
          })
        }
        return Number(zNum)+Number(yNum);
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '手动支出金额'){zNum = item['count']}
            if(item['name'] === '账单支出金额'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>手动支出金额:{zNum}元</div>
            <div>账单支出金额:{yNum}元</div>
          </div>
        )}
    },
  },
  {
    key:'2013',
    value:{
      icon:'appstore',
      coverImg:require('@/components/CardsGroup/assets/dues/xiabozuzhi.png'),
      iconColor:'#a01bfd',
      title:'下拨组织',
      suffix:'个',
      action:'/api/chart/fee/getFeeAllocateOrgCount',
      end:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['name'] === '党委'){zNum = item['count']}
            if(item['name'] === '党支部'){yNum = item['count']}
          });
        }
        return Number(zNum)+Number(yNum);
      },
      content:(val)=>{
        let zNum = 0;
        let yNum = 0;
        if(!_isEmpty(val) && _isArray(val)) {
          val.forEach(item=>{
            if(item['name'] === '党委'){zNum = item['count']}
            if(item['name'] === '党支部'){yNum = item['count']}
          })
        }
        return(
          <div>
            <div>下拨党委:{zNum}个</div>
            <div>下拨党支部:{yNum}个</div>
          </div>
        )}
    },
  },
];
export const chartConfig = [
  {
    key:'2005', // 金额月份统计
    value:{
      coverImg:require('@/components/CardsGroup/assets/dues/jineyuefen.png'),
      action:'/api/chart/fee/getFeePayByMonth',
      option:(val)=>{
        let arr:Array<object> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arr.push({
              name:item['month'],
              value:item['count']
            })
          });
          arr=arr.sort((a,b)=>a['name']-b['name']);
        }
        let arrName=['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'];
        return {
          title : {
            text: '金额月份统计',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arr,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'2016', // 下拨金额月份统计
    value:{
      coverImg:require('@/components/CardsGroup/assets/dues/xiabo.png'),
      action:'/api/chart/mem/getMemTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d08Name']);
            arr.push({
              name:item['d08Name'],
              value:item['count']
            })
          });
          arr = arr.filter(item=>item['name'] !== '党员总数');

        }
        return {
          title : {
            text: '下拨金额月份统计',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arr,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'2005', // 党费对账金额月份统计
    value:{
      coverImg:require('@/components/CardsGroup/assets/dues/duizhang.png'),
      action:'/api/chart/mem/getMemTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d08Name']);
            arr.push({
              name:item['d08Name'],
              value:item['count']
            })
          });
          arr = arr.filter(item=>item['name'] !== '党员总数');

        }
        return {
          title : {
            text: '党费对账金额月份统计',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arr,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'2005',
    value:{
      coverImg:require('@/components/CardsGroup/assets/dues/zhichuyuefen.png'),
      action:'/api/chart/fee/getFeeDisburseByMonth',
      option:(val)=>{
        let arr:Array<object> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arr.push({
              name:item['month'],
              value:item['count']
            })
          });
          arr=arr.sort((a,b)=>a['name']-b['name']);
        }
        let arrName=['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'];
        // let arr=[100,200,300,400,500,600,700,800,700,600,500,400];
        return {
          title : {
            text: '支出金额月份统计',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arr,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'2005',
    value:{
      coverImg:require('@/components/CardsGroup/assets/dues/jineleibie.png'),
      action:'/api/chart/fee/getFeeDisburseByType',
      option:(val)=>{
        let arr:Array<object> = [];
          let arrName:Array<string> = [];
          if(!_isEmpty(val) && _isArray(val)){
            val.forEach(item=>{
              arrName.push(item['name']);
              arr.push(item['count']);
            });
        }
        return {
          title : {
            text: '支出金额类别',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arr,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'2016',
    value:{
      coverImg:require('@/components/CardsGroup/assets/dues/geleibie.png'),
      action:'/api/chart/fee/getFeeDisburseByTypeRatio',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['name']);
            arr.push({
              name:item['name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '支出各类别占比',
            // subtext: '纯属虚构',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '支出类别',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
    }
  },
  {
    key:'2016',
    value:{
      coverImg:require('@/components/CardsGroup/assets/dues/luruleixing.png'),
      action:'/api/chart/fee/getFeeDisburseByInputRatio',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['name']);
            arr.push({
              name:item['name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '支出录入类型占比',
            // subtext: '纯属虚构',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '支出类别',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
    }
  },

];
