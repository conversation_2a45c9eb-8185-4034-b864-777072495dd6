import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import style from './index.less';
import NowOrg from '@/components/NowOrg';
import { Button, Form, Alert, Input, Row, Col, Select, Radio, InputNumber, Switch, Divider, Popconfirm } from 'antd';
import ListTable from '@/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import SpinProgress from '@/components/SpinProgress';
import { validateLength } from '@/utils/formValidator';
import {
    findDictCodeName,
    unixMoment,
    timeSort,
    getIdCardInfo,
    formLabel,
    correctIdcard,
} from '@/utils/method';
import moment from 'moment'
import Date from '@/components/Date';
import DictSelect from 'src/components/DictSelect';
import DictTreeSelect from 'src/components/DictTreeSelect';
import { selectlist, selectleave, selectdel } from '@/pages/[unit]/services/index';
import { PlusOutlined } from '@ant-design/icons';
import SelectAdd from './selectAdd';
import Tip from '@/components/Tip';
import SelectLeave from './selectLeave';

const Option = Select.Option;
const RadioGroup = Radio.Group;
const { TextArea } = Input;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 },
    },
};
const formItemLayout1 = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 19 },
    },
};
const formItemLayout2 = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
};
const index: any = (props, ref) => {
    const {
        tableAction,
        FormComp,
        tableColumns,
        otherBtnsFunc,
        getSearchCallBack,
        progressCallback,
        otherRenderTableColumnsFunc,
        renderTableNumber,
        isDefaultForm = true,
        rowKey = 'id'
    } = props;

    const progressRef: any = useRef();
    const leaveRef: any = useRef();
    const [loading, setLoading] = useState(false);
    const [list, setList] = useState([]);
    const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 20, total: 0, current: 1 });

    const getList = async (params = {}) => {

        const { code = 500, data: { list = [], totalRow = 0, pageSize, pageNumber } = {} } = await selectlist({
            data: {
                unitCode: props.unit.basicInfo.code,
                pageNum: pagination.pageNum,
                pageSize: pagination.pageSize,
                ...params
            }
        })
        if (code == 0) {
            setList(list);
            setPagination({
                pageNum: pageNumber,
                pageSize: pageSize,
                total: totalRow,
                current: pageNumber
            })
        }
    }
    const AddorEdit = (type, record?: any) => {
        if (progressRef.current) {
            progressRef.current.open(record, type);
        }
    }
    const del = async (record) => {
        const { code = 500 } = await selectdel({ code: record.code });
        if (code == 0) {
            Tip.success('操作提示', '删除成功');
            getList({ pageNum: 1 });
        }
    }
    useEffect(() => {
        getList()
    }, []);

    const columns = [
        {
            title: '姓名',
            dataIndex: 'memName',
            align: 'center',
        },
        {
            title: '政治面貌',
            dataIndex: 'd89Name',
            align: 'center',
        },
        {
            title: '学历',
            dataIndex: 'd07Name',
            align: 'center',
           
        },
        {
            title: '到村任职开始时间',
            dataIndex: 'startTime',
            align: 'center',
            render: (text, record) => {
                if(text){
                    return moment(text).format('YYYY-MM-DD')
                }
            }
        },
        {
            title: '到村任职结束时间（预计）',
            dataIndex: 'endTime',
            align: 'center',
            render: (text, record) => {
                if(text){
                    return moment(text).format('YYYY-MM-DD')
                }
            }
        },
        {
            title: '联系电话',
            dataIndex: 'phone',
            align: 'center',
        },
        {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            render: (text, record) => {
                return (
                    <div>
                        <a onClick={() => AddorEdit('edit', record)}>编辑</a>
                        <Divider type="vertical" />
                        <Popconfirm title="确定删除？" onConfirm={() => {
                             del(record)
                        }}>
                            <a className={'del'}>删除</a>
                        </Popconfirm>
                        <Divider type="vertical" />
                        <Popconfirm title="确定要离开？" onConfirm={() => {
                            leaveRef.current.open(record);
                        }}>
                            <a >离开</a>
                        </Popconfirm>
                    </div>
                )
            }
        }
    ]
    return (
        <div className={style.selectBox}>
            <Button type="primary" icon={<PlusOutlined />} style={{ marginBottom: '10px' }} onClick={() => AddorEdit('add',{})}>添加选调生</Button>
            <ListTable data={list} pagination={pagination} columns={columns} onPageChange={(pageNum, pageSize) => getList({ pageNum, pageSize })} />
            <SelectAdd ref={progressRef} upList={getList} {...props} />
            <SelectLeave ref={leaveRef} {...props} onOK={() => {
                getList({ pageNum: 1 });
            }} />
        </div>
    );
};
// @ts-ignore
export default index;
