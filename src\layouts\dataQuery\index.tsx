// 流入管理-未纳入-退回
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import { Modal, Input, Select, Tabs } from 'antd';
import RelationshipTransfer from './components/RelationshipTransfer'
import FlowMembers from './components/flowMembers'


const TabPane = Tabs.TabPane;



class index extends React.Component<any, any> {
    constructor(props) {
        super(props);
        this.state = {
            activeTab: '1',
        };
    }
    handleCancel = () => {
        this.setState({ visible: false });
    };
    open = () => {
        this.setState({ visible: true });
    };
    render() {
        const { form } = this.props;
        const { visible, confirmLoading, activeTab } = this.state;
        return (
            <Modal
                destroyOnClose
                title="数据查询"
                visible={visible}
                maskClosable={false}
                onCancel={this.handleCancel}
                width={1300}
                footer={null}
                confirmLoading={confirmLoading}
            >
                {visible && (
                    <Fragment>
                        <Tabs
                            activeKey={activeTab}
                            onChange={(e) => {
                                this.setState({
                                    activeTab: e,
                                });
                            }}
                        >
                            <TabPane tab="关系转接" key="1" />
                            <TabPane tab="流动党员" key="2" />
                        </Tabs>
                        {activeTab === '1' && <RelationshipTransfer />}
                        {activeTab === '2' && <FlowMembers />}
                    </Fragment>
                )}
            </Modal>
        );
    }
}
export default Form.create()(index);
