/**
 * 添加班子成员
 */
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Input, Modal, Radio, Row, Switch, InputNumber, Select, Upload, Button } from "antd";
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import MemSelect from '@/components/MemSelect';
import Tip from '@/components/Tip';
import UploadComp, { getInitFileList, fitFileUrlForForm } from '@/components/UploadComp';
import moment from 'moment';
import YN from '@/components/YesOrNoSelect';
import { formLabel, getIdCardInfo, correctIdcard, fileDownloadHeader } from '@/utils/method.js';
import Date from '@/components/Date';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import { getUnitCommitteeSrcOrg } from '@/pages/[unit]/services';
import _isNumber from 'lodash/isNumber';
import ListTable from '@/components/ListTable';
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};

class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      visible1: false,
      idCard: '',
      list: [],
      params: {},
    }
  }
  showModal = (edit = false) => {
    const { memInfo = {} } = this.props;
    if (memInfo) {
      this.setState({
        visible: true,
        idCard: memInfo?.memIdcard,
        readonly: edit

      });
    } else {
      this.setState({
        visible: true,
        readonly: edit
      });
    }
  };

  // 根据姓名查找人员信息，用于页面回显
  getSelectMemInfo = async (mem) => {
    const { memInfo = {} } = this.props;
    const { basicInfo = {} } = this.props.unit;
    console.log('this.props====', this.props);
    this.setState({
      idCard: mem[0]?.idcard || '',
    })
    const { code = 500, data = {} } = await getUnitCommitteeSrcOrg({
      unitCode: basicInfo?.code,
      memCode: mem[0]?.code || undefined,
    })
    if (code == 0) {
      if (!_isEmpty(data)) {
        let arr = Object.keys(data)
        arr.map((item) => {
          memInfo[item] = data[item];
        })
        this.setState({})
      }
    }
  }

  handleOk = () => {
    const { basicInfo = {} } = this.props.unit;
    const { children, title, memInfo = {}, dataInfo = {} } = this.props;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if (val['memName'] != memInfo['memName'] || val['memIdcard'] != memInfo['memIdcard']) {
          let result = await correctIdcard(val['memName'], val['memIdcard']);
          if (result['code'] != '200') {
            this.props.form.setFields({
              memIdcard: {
                value: val['memIdcard'],
                errors: [new Error('经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')]
              }
            })
            Tip.error('操作提示', '经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')
            return
          } else {
            val['idCardReason'] = result['reason']
            val['idCardReasonName'] = result['reasonName']
          }
        }
        if (val['reward'] > 200) {
          Tip.error('操作提示', '报酬（万元/年）不能大于200')
          return
        }
        // 图片
        if (!_isEmpty(val['photoPath'])) {
          console.log('photoPath===', val['photoPath']);
          if (_isArray(val['photoPath'])) {
            val['fileName'] = val['photoPath'][0].name || ''
            val['photoPath'] = fitFileUrlForForm(val['photoPath'])
          }
        } else {
          val['fileName'] = undefined;
          val['photoPath'] = undefined;
        }

        let obj = undefined;
        ['d25Code', 'd26Code', 'd07Code', 'd144Code', 'd0121Code', 'd89Code'].map(obj => {
          if (val[obj] && typeof val[obj] === 'object') {
            let key = obj.split('C')[0];
            val[`${key}Name`] = val[obj]['name'];
            val[obj] = val[obj]['key']
          }
        });
        // 是否村任职选调生选否的时候清空选调单位层级的值
        if (val['hasVillageTransferStudent'] === 0) {
          val['d144Code'] = undefined;
          val['d144Name'] = undefined;
          val['isDoubleFirst'] = undefined;
        }
        ['startDate', 'endDate', 'birthday'].map(obj => {
          if (val[obj]) {
            val[obj] = val[obj].valueOf();
          }
        });
        if (val['memCode'] && typeof val['memCode'] === 'object') {
          val['memName'] = val['memCode'][0]['name'];
          val['memCode'] = val['memCode'][0]['code']
        }
        ['isIncumbent', 'memTypeCode'].map(obj => {
          if (val[obj]) {
            val[obj] = 1;
          } else {
            val[obj] = 0
          }
        });
        val['unitCode'] = basicInfo['code'];
        if (!val['d26Code']) {
          val['d26Name'] = undefined;
        }
        let type = 'unit/addite';
        if (memInfo['code']) {
          type = 'unit/upite';
        }
        let data = {
          ...memInfo,
          electCode: dataInfo['code'],
          ...val
        }
        this.setState({
          params: data
        })
        this.confirmAgain()
      }
    });
  };
  //后端验证接口查询是否有任职情况
  confirmAgain = (pageNum = 1, pageSize = 100) => {
    this.props.dispatch({
      type: 'unit/getListByIdcard',
      payload: {
        idcard: this.state.idCard,
        pageNum,
        pageSize,
      }
    }).then(res => {
      this.setState({
        visible1: true,
        list: res.data.list
      })
    })
  }
  handleCancel = () => {
    this.props.onClose();
    this.setState({
      visible: false,
      list: [],
      idCard: '',
      params: {}
    });
  };
  handleOk1 = async () => {
    const { basicInfo = {} } = this.props.unit;
    const { children, title, memInfo = {}, dataInfo = {} } = this.props;
    const { params } = this.state
    let type = 'unit/addite';
    if (memInfo['code']) {
      type = 'unit/upite';
    }
    let obj = undefined
    obj = await this.props.dispatch({
      type,
      payload: {
        data: {
          ...params
        }
      }
    });
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', memInfo['code'] ? '修改成功' : '新增成功');
      this.props.queryList();
      this.handleCancel1();
      this.handleCancel()
    }
  }
  handleCancel1 = () => {
    this.setState({
      visible1: false,

    });
  };
  onStartChange = (value) => {
    this.setState({
      startValue: value
    })
  };
  onEndChange = (value) => {
    this.setState({
      endValue: value
    })
  };
  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.endOf('day') <= startValue.endOf('day');
  };
  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.endOf('day') <= startValue.endOf('day');
  };
  validatorIdcard = async (rule, value, callback) => {
    if (!value) {
      callback('身份证必填');
    }
    if (value && value.length !== 18 && process.env.idCheck != 'false') {
      callback('身份证应该为18位');
    }
    if (getIdCardInfo(value) === 'Error') {
      callback('身份证格式错误,请核对身份证图片');
    } else {
      // let fieldValue = this.props.form.getFieldValue('memName');
      // let res =await geitCard({idCard:fieldValue,name:value});
      callback()
    }
  };
  getIDinfo = (e) => {
    const { target: { value = '' } = {} } = e || {};
    let info = getIdCardInfo(value);
    if (value && info !== 'Error') {
      this.props.form.setFieldsValue({
        sexCode: info[2] === '女' ? '0' : '1',
        birthday: info[1] ? moment(info[1], 'YYYY-MM-DD') : undefined,
      });
    }
  };
  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { children, title, dataInfo = {}, tipMsg = {}, memInfo = {}, loading: { effects = {} } = {} } = this.props;
    const { basicInfo = {} } = this.props.unit;
    console.log(memInfo, 'basicInfobasicInfo')
    const { d04Code = '' } = basicInfo;
    const { pagination, list = [], readonly = false } = this.state;
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 58,
        render: (text, record, index) => {
          return index + 1
        }
      },
      {
        title: '职务名称',
        dataIndex: 'd022Name',
      },
      {
        title: '任职开始时间',
        dataIndex: 'startDate',
        render: (text, record) => {
          return (<div>{!_isNumber(text) ? '' : moment(text).format('YYYY-MM-DD')}</div>)
        }
      },
      {
        title: '任职结束时间',
        dataIndex: 'endDate',
        render: (text, record) => {
          return (<div>{!_isNumber(text) ? '' : moment(text).format('YYYY-MM-DD')}</div>)
        }
      },
      // {
      //   title: '职务级别',
      //   dataIndex: 'd51Name',
      // },
      {
        title: '任职所在党组织/单位名称',
        dataIndex: 'positionOrgName',
      },
    ];
    return (
      <React.Fragment>
        {
          children ? React.cloneElement(children as any, {
            onClick: () => {
              this.showModal(false)
            },
            key: 'container'
          }) : null
        }
        {
          this.state.visible &&
          <Modal
            title={title || "请输入标题"}
            visible={this.state.visible}
            onOk={() => this.handleOk()}
            onCancel={this.handleCancel}
            width={1000}
            className='add_member_modal'
            maskClosable={false}
            footer={
              <Fragment>
                <Button key="back" onClick={this.handleCancel}>取消</Button>
                {!readonly && <Button key="submit" type="primary" onClick={() => this.handleOk()}>确定</Button>}
              </Fragment>
            }
          >
            <Form {...formItemLayout}>
              <Row>
                <Col span={24}>
                  <FormItem
                    label={formLabel('任职单位名称', tipMsg['unitName'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('unitName', {
                      initialValue: basicInfo['name'],
                      rules: [{ required: true, message: '请输入任职单位名称' }],
                    })(
                      <Input placeholder={'任职单位名称'} disabled />
                    )}
                  </FormItem>
                </Col>
                {/* <Col span={12}>
                  <FormItem
                    label={ formLabel('是否在任',tipMsg['isIncumbent']) }
                  >
                    {getFieldDecorator('isIncumbent', {
                      valuePropName:'checked',
                      initialValue:memInfo['isIncumbent']!==undefined ? memInfo['isIncumbent']==1 : false,
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <Switch/>
                    )}
                  </FormItem>
                </Col> */}
                <Col span={12}>
                  <FormItem
                    label={formLabel('是否本单位党员', tipMsg['memTypeCode'])}
                  >
                    {getFieldDecorator('memTypeCode', {
                      valuePropName: 'checked',
                      initialValue: memInfo['memTypeCode'] !== undefined ? memInfo['memTypeCode'] == 1 : true,
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <Switch disabled={readonly} />
                    )}
                  </FormItem>
                </Col>
                {
                  (function (_this) {
                    const { props } = _this;
                    const memTypeCode = props.form.getFieldValue('memTypeCode');
                    return (
                      <React.Fragment>
                        {
                          memTypeCode == 0 ? <React.Fragment>
                            <Col span={12}>
                              <FormItem
                                label={formLabel('人员姓名', tipMsg['memName'])}
                              >
                                {getFieldDecorator('memName', {
                                  initialValue: memInfo['memName'],
                                  rules: [{ required: true, message: '请输入人员姓名' }],
                                })(
                                  <Input placeholder="请输入人员姓名" disabled={readonly} />
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('性别', tipMsg['sexCode'])}
                              >
                                {getFieldDecorator('sexCode', {
                                  initialValue: memInfo['sexCode'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <RadioGroup disabled={readonly}>
                                    <Radio value={'1'}>男</Radio>
                                    <Radio value={'0'}>女</Radio>
                                  </RadioGroup>
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('身份证', tipMsg['memIdcard'])}
                              >
                                {getFieldDecorator('memIdcard', {
                                  initialValue: memInfo['memIdcard'],
                                  rules: [
                                    { required: true, message: '请输入身份证' },
                                    { validator: _this.validatorIdcard },
                                  ],
                                })(
                                  <Input placeholder="请输入身份证" onBlur={_this.getIDinfo} disabled={readonly} />
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('出生日期', tipMsg['birthday'])}
                              >
                                {getFieldDecorator('birthday', {
                                  initialValue: memInfo['birthday'] !== undefined ? moment(memInfo['birthday'] * 1) : undefined,
                                  rules: [{ required: true, message: '请输入' }],
                                })(
                                  <Date disabled={readonly} />
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('政治面貌', tipMsg['d89Code'])}
                              >
                                {getFieldDecorator('d89Code', {
                                  rules: [{ required: true, message: '政治面貌' }],
                                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['d89Code'],
                                })(
                                  <DictSelect backType={'object'}
                                    // ref={e=>_this['politicsCode'] = e}
                                    initValue={_isEmpty(memInfo) ? undefined : memInfo['d89Code'] ? memInfo['d89Code'] : undefined}
                                    codeType={'dict_d89'}
                                    placeholder="请选择"
                                    disabled={readonly}
                                  // mode={'multiple'}
                                  // filter={(data)=>{
                                  //   if(bigThan28){
                                  //     return data.filter(it=>it.key !== '12');
                                  //   }
                                  //   return data;
                                  // }}
                                  // onChange={(e)=>{ handlePoliticsCodeChange && handlePoliticsCodeChange(e)
                                  // }}
                                  />
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={formLabel('学历情况', tipMsg['d07Code'])}
                              >
                                {getFieldDecorator('d07Code', {
                                  initialValue: memInfo['d07Code'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <DictTreeSelect backType={'object'} codeType={'dict_d07'} initValue={memInfo['d07Name']} placeholder="请选择" parentDisable={true} disabled={readonly} />
                                )}
                              </FormItem>
                            </Col>
                          </React.Fragment> : <Col span={12}>
                            <FormItem
                              label={formLabel('党员姓名', tipMsg['memCode'])}
                            >
                              {getFieldDecorator('memCode', {
                                initialValue: memInfo['memCode'],
                                rules: [{ required: true, message: '请选择' }],
                              })(
                                <MemSelect initValue={memInfo['memName']} disabled={readonly} placeholder="请选择党员" onChange={(e) => {
                                  // 单位班子新增选择党员后调用接口回显信息：
                                  console.log('e===', e);
                                  _this.getSelectMemInfo(e)

                                }} />
                              )}
                            </FormItem>
                          </Col>
                        }
                      </React.Fragment>
                    )
                  })(this)
                }
                {/* <Col span={12}>
                  <FormItem
                    label={ formLabel('职务',tipMsg['currentPositionJob']) }
                  >
                    {getFieldDecorator('currentPositionJob', {
                      initialValue:memInfo['currentPositionJob'],
                      rules: [{ required: true, message: '职务' }],
                    })(
                      <Input placeholder="职务" maxLength={100}/>
                    )}
                  </FormItem>
                </Col> */}
                <Col span={12}>
                  <FormItem
                    label={formLabel('行政职务', tipMsg['d25Code'])}
                  >
                    {getFieldDecorator('d25Code', {
                      initialValue: memInfo['d25Code'],
                      rules: [{ required: true, message: '请选择行政职务' }],
                    })(
                      <DictTreeSelect initValue={memInfo['d25Code']}
                        backType={'object'}
                        codeType={'dict_d25'}
                        placeholder="请选择"
                        disabled={readonly}
                        filter={(data) => {
                          // 921城市社区 922乡镇社区 923村 4企业 9121乡 9122镇
                          if (d04Code === '9121') {
                            return data.filter(it => it.key === '2');
                          }
                          // if(d04Code === '9122'){
                          //   return data.filter(it=>it.key==='3');
                          // }
                          if (d04Code === '923') {
                            return data.filter(it => it.key === '4');
                          }
                          if (d04Code === '921' || d04Code === '922') {
                            return data.filter(it => it.key === '5');
                          }

                          // 单位类型911 ，政府班子行政职务只显示 1开头的
                          // 单位类型9121 ，政府班子行政职务只显示 2开头的
                          // 单位类型9122 ，政府班子行政职务只显示 3开头的
                          if (d04Code === '911') {
                            return data.filter(it => it.key.startsWith('1'));
                          }
                          if (d04Code === '9121') {
                            return data.filter(it => it.key.startsWith('2'));
                          }
                          if (d04Code === '9122') {
                            return data.filter(it => it.key.startsWith('3'));
                          }
                          return data;
                        }}
                        parentDisable={true} />
                    )}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label={formLabel('决定或批准任职的文号', tipMsg['fileNumber'])}
                  >
                    {getFieldDecorator('fileNumber', {
                      initialValue: memInfo['fileNumber'],
                      rules: [{ required: false, message: '请输入决定或批准任职的文号' }],
                    })(
                      <Input placeholder="决定或批准任职的文号" disabled={readonly} />
                    )}
                  </FormItem>
                </Col>
                {
                  (function (_this) {
                    const { memTypeCode = undefined } = _this?.props?.form?.getFieldsValue() || {};
                    if (!memTypeCode) {
                      return (
                        <Col span={12}>
                          <FormItem
                            label={formLabel('兼职情况', tipMsg['d26Code'])}
                          >
                            {getFieldDecorator('d26Code', {
                              initialValue: memInfo['d26Code'],
                              rules: [{ required: false, message: '请选择' }],
                            })(
                              <DictTreeSelect initValue={memInfo['d26Code']} disabled={readonly} backType={'object'} parentDisable={true} codeType={'dict_d26'} placeholder="请选择" />
                            )}
                          </FormItem>
                        </Col>
                      )
                    }
                  })(this)
                }

                <Col span={12}>
                  <FormItem
                    label={formLabel('行政任职起始日期', tipMsg['startDate'])}
                  >
                    {getFieldDecorator('startDate', {
                      initialValue: memInfo['startDate'] ? moment(memInfo['startDate']) : dataInfo['tenureStartDate'] ? moment(dataInfo['tenureStartDate']) : undefined,
                      rules: [{ required: true, message: '请选择' }],
                      // <DatePicker placeholder="请选择" style={{width:'100%'}} onChange={this.onStartChange} disabledDate={this.disabledStartDate}/>
                    })(
                      <Date onChange={this.onStartChange} disabled={readonly} />
                    )}
                  </FormItem>
                </Col>
                {
                  // 15.当单位类别为村（社区）以及党组织关联的单位类别为村（社区）的时候，编辑和录入班子成员的时候，增加信息项目：是否村任职选调生（选择框、必填）
                  // 8.当单位类别是村（社区）的时候，添加班子成员和编辑班子成员得时候，增加信息项目：报酬（万元/年）（数字[保留两位小数]、必填）
                  `${d04Code}`.startsWith('92') &&
                  <React.Fragment>
                    {/* <Col span={12}>
                      <FormItem
                        label={formLabel('是否村任职选调生', tipMsg['hasVillageTransferStudent'])}
                      >
                        {getFieldDecorator('hasVillageTransferStudent', {
                          initialValue: memInfo['hasVillageTransferStudent'],
                          rules: [{ required: true, message: '请选择' }],
                        })(
                          <YN init={memInfo['hasVillageTransferStudent']} disabled={readonly} />
                        )}
                      </FormItem>
                    </Col> */}
                    { //6、是否村任职选调生 选择是的时候，要弹出来一个填写框：选调单位层级
                      getFieldValue('hasVillageTransferStudent') == 1 &&
                      (
                        <React.Fragment>
                          <Col span={12}>
                            <FormItem
                              label={formLabel('选调单位层级', tipMsg['d144Code'])}
                            >
                              {getFieldDecorator('d144Code', {
                                initialValue: memInfo['d144Code'],
                                rules: [{ required: true, message: '请选择' }],
                              })(
                                <DictTreeSelect backType={'object'} codeType={'dict_d144'} initValue={memInfo['d144Code']} placeholder="请选择" parentDisable={true} disabled={readonly} />
                              )}
                            </FormItem>
                          </Col>
                          {/* 是否村任职选调生 选择是的时候，要弹出来一个填写框：双一流大学 */}
                          <Col span={12}>
                            <FormItem
                              label={formLabel('是否双一流大学生', tipMsg['isDoubleFirst'])}
                            >
                              {getFieldDecorator('isDoubleFirst', {
                                initialValue: memInfo['isDoubleFirst'],
                                rules: [{ required: true, message: '请选择' }],
                              })(
                                <YN init={memInfo['isDoubleFirst']} disabled={readonly} />
                              )}
                            </FormItem>
                          </Col>
                        </React.Fragment>
                      )
                    }
                    <Col span={12}>
                      <FormItem
                        label={formLabel('报酬（万元/年）', tipMsg['reward'])}
                      >
                        {getFieldDecorator('reward', {
                          initialValue: memInfo['reward'],
                          rules: [{ required: true, message: '报酬（万元/年）' }],
                        })(
                          <InputNumber style={{ width: '100%' }} min={0} precision={2} disabled={readonly} />
                        )}
                      </FormItem>
                    </Col>
                  </React.Fragment>
                }
                {/*<Col span={12}>*/}
                {/*  <FormItem*/}
                {/*    label={ formLabel('离职日期',tipMsg['endDate']) }*/}
                {/*  >*/}
                {/*    {getFieldDecorator('endDate', {*/}
                {/*      initialValue:memInfo['endDate'] ? moment(memInfo['endDate']) : undefined,*/}
                {/*      rules: [{ required: false, message: '请选择' }],*/}
                {/*      // <DatePicker placeholder="请选择" style={{width:'100%'}} onChange={this.onEndChange} disabledDate={this.disabledEndDate}/>*/}
                {/*    })(*/}
                {/*      <Date onChange={this.onEndChange}/>*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                <Col span={12}>
                  <FormItem labelCol={{ style: { width: 158, whiteSpace: 'normal', textAlign: 'right', lineHeight: '20px' } }} label={formLabel('是否参加城镇职工养老保险', tipMsg['endowmentInsuranceForUrbanEmployees'])}>
                    {getFieldDecorator('endowmentInsuranceForUrbanEmployees', {
                      initialValue: _isEmpty(memInfo) ? undefined : memInfo['endowmentInsuranceForUrbanEmployees'],
                      rules: [{ required: true, message: '是否参加城镇职工养老保险' }],
                    })(
                      <Select style={{ width: '100%' }} disabled={readonly}>
                        <Select.Option value={1}>是</Select.Option>
                        <Select.Option value={0}>否</Select.Option>
                      </Select>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={formLabel('人员来源', tipMsg['d0121Code'])}
                  >
                    {getFieldDecorator('d0121Code', {
                      initialValue: memInfo['d0121Code'],
                      rules: [{ required: true, message: '人员来源' }],
                    })(
                      <DictTreeSelect initValue={_isEmpty(memInfo) ? undefined : memInfo['d0121Code']}
                        backType={'object'}
                        codeType={'dict_d121'}
                        placeholder="请选择" parentDisable={true} disabled={readonly}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={24}>
                  <FormItem
                    label={formLabel('行政职务说明', tipMsg['remark'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('remark', {
                      initialValue: memInfo['remark'],
                      rules: [{ required: false, message: '' }],
                    })(
                      <TextArea rows={4} placeholder={'党内职务说明'} disabled={readonly} />
                    )}
                  </FormItem>
                </Col>
                {/* <Col span={24}>
                  <FormItem
                    label={formLabel('上传头像', tipMsg['photoPath'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('photoPath', {
                      initialValue: memInfo['photoPath'],
                      rules: [{ required: false, message: '' }],
                    })(
                      <UploadComp action='/api/base/putFile' buttonText='选择图片' accept='.jpg,.png,.jpeg' files={getInitFileList(memInfo['photoPath'], memInfo['fileName'])} maxLen={1} disabled={readonly} />
                    )}
                  </FormItem>
                </Col> */}
              </Row>
            </Form>
            <Modal
              title={"任职情况"}
              visible={this.state.visible1}
              onOk={() => this.handleOk1()}
              onCancel={this.handleCancel1}
              width={1000}
              className='add_member_modal'
              maskClosable={false}
            >
              <ListTable columns={columns} data={list} pagination={false} scroll={{ y: 500 }} />
            </Modal>
          </Modal>
        }

      </React.Fragment>
    )
  }
}
export default Form.create()(index);
