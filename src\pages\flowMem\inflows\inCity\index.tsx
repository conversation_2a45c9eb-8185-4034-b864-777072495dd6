/**
 * 省内流入登记
 */

import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Avatar, Button, Col, Dropdown, Menu, Modal, Row, Input, Radio, DatePicker } from 'antd';
import qs from 'qs';
import { connect } from 'dva';
import _isEmpty from 'lodash/isEmpty';
import ListTable from 'src/components/ListTable';
import { _history as router, getIdCardInfo } from '@/utils/method';
import { isEmpty } from '@/utils/method';
import { getSession } from '@/utils/session';
import DictArea from '@/components/DictArea';
import DictSelect from '@/components/DictSelect';
import MemSelect from '@/components/MemSelect';
import OrgSelect from '@/components/OrgSelect';
import styles from './index.less';
import moment from 'moment';
import { root } from '@/common/config';
import Notice from '@/components/Notice';
import Date from '@/components/Date';
import SearchOrg from '@/components/SearchOrg';

import { countIsProvOut } from '@/pages/flowMem/service';
import { formLabel, correctIdcard } from '@/utils/method';
import Tip from '@/components/Tip';
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 17 },
  },
};
const menuData = [
  {
    code: '1',
    name: '基本信息',
    icon: 'star',
  },
  {
    code: '2',
    name: '',
    icon: 'qrcode',
  },
];
@connect(
  ({ unit, commonDict, loading }) => ({
    unit,
    commonDict,
    loading: loading.effects['unit/getList'],
  }),
  undefined,
  undefined,
  { forwardRef: true },
)
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    let obj = menuData[0];
    this.state = {
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    };
  }

  showModal = () => {
    const { data: { id = '' } = {}, keyword = '' } = this.props;
    let org = getSession('org') || {};
    this.setState({
      visible: true,
    });
  };

  handleOk = () => {
    this.props.form.validateFieldsAndScroll(async (errors, values) => {
      if (errors) {
        return;
      }
      let result = await correctIdcard(values['memName'], values['idcard']);
      if (result['code'] != '200') {
        this.props.form.setFields({
          idcard: {
            value: values['idcard'],
            errors: [new Error('经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')],
          },
        });
        Tip.error('操作提示', '经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。');
        return;
      } else {
        values['idCardReason'] = result['reason'];
        values['idCardReasonName'] = result['reasonName'];
      }
      const { onChange } = this.props;
      const {
        memName,
        idcard,
        mem,
        outflowArea,
        outflowAreaName,
        isProvOut,
        outflowOrg,
        outflowOrgLinkman,
        outflowOrgPhone,
        outflowReasonCode,
        outflowReason,
        isHold,
        outflowDate,
      } = values;
      let val = {
        idcard,
        memName,
        memCode: isEmpty(mem) ? '' : mem[0]['code'],
        memOrgOrgCode: isEmpty(mem) ? '' : mem[0]['memOrgCode'],
        memOrgCode: isEmpty(mem) ? '' : mem[0]['orgCode'],
        isHold: isHold || null,
        isProvOut: isEmpty(isProvOut) ? '' : isProvOut['id'],
        isProvOutName: isEmpty(isProvOut) ? '' : isProvOut['name'],
        outflowDate: isEmpty(outflowDate) ? '' : moment(outflowDate).valueOf(),
        outflowReason: outflowReason || '',
        outflowTypeCode: isEmpty(isProvOut) ? '' : isProvOut['id'],
        outflowTypeName: isEmpty(isProvOut) ? '' : isProvOut['name'],
        outflowOrgCode: isEmpty(outflowOrg) ? '' : outflowOrg[0]['code'],
        outflowOrgOrgCode: isEmpty(outflowOrg) ? '' : outflowOrg[0]['orgCode'],
        outflowReasonCode: isEmpty(outflowReasonCode) ? '' : outflowReasonCode['id'],
        outflowReasonName: isEmpty(outflowReasonCode) ? '' : outflowReasonCode['name'],
        outflowOrgLinkman: outflowOrgLinkman || '',
        outflowOrgPhone: outflowOrgPhone || '',
        outflowAreaName: isEmpty(outflowAreaName) ? '' : outflowAreaName,
        outflowAreaId: isEmpty(outflowArea) ? '' : outflowArea,
        outflowOrgName:this.state.showOrgName
      };
      this.props
        .dispatch({
          type: 'flowMem/addInMem',
          payload: {
            data: {
              ...val,
            },
          },
        })
        .then((res) => {
          if (res['code'] === 0) {
            Notice('操作提示', res['message'], 'check-circle', 'green');
            this.handleCancel();
            onChange && onChange(true);
          } else {
            Notice('操作提示', res['message'], 'exclamation-circle-o', 'orange');
          }
        });
    });
    // this.handleCancel();
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      memInfo: [],
      showOrgName: '',
    });
    this.props.form.resetFields();
  };
  open = () => {
    this.setState({
      visible: true,
    });
  };
  destroy = () => {
    let obj = menuData[0];
    this.setState({
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    });
    this.props.dispatch({
      //重置model
      type: 'unit/updateState',
      payload: {
        basicInfo: {},
      },
    });
  };
  onSelect = (item) => {
    const { key, keyPath } = item;
    const selected = menuData.find((obj) => obj['code'] === key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  outMem = (e) => {
    this.setState({ memInfo: e });
  };
  outAddr = async (val, obj) => {
    this.setState({ addr: obj });
    const { code = 500, data = {} } = await countIsProvOut({ name: obj.name, key: obj.key });
    if (code === 0) {
      this.setState({
        isProvOut: data,
      });
      this.props.form.setFieldsValue({
        isProvOut: data,
        outflowAreaName: obj.name,
      });
    }
  };
  showOrg = (e) => {
    this.setState({ showOrgName: e[0]['name'] });
  };
  disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };
  validFunction = (rule, value, callback) => {
    let han = /^[\u4e00-\u9fa5]+$/;
    if (value) {
      switch (rule.field) {
        case 'outflowOrgLinkman':
          if (value.length > 20) {
            return callback('组织联系人不能超过20个字符');
          } else if (!han.test(value)) {
            return callback('联系人名称不合法');
          }
          break;
        case 'outflowOrgPhone':
          if (!/^1[345789]\d{9}$/.test(value)) {
            return callback('手机号码不合法');
          }
          break;
      }
    }
    callback();
  };
  onPageChange = (page, pageSize) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`);
  };
  validatorIdcard = async (rule, value, callback) => {
    if (!value) {
      callback('身份证必填');
    }
    if (value && value.length !== 18 && process.env.idCheck != 'false') {
      callback('身份证应该为18位');
    }
    if (getIdCardInfo(value) === 'Error') {
      callback('身份证格式错误,请核对身份证图片');
    } else {
      // let fieldValue = this.props.form.getFieldValue('memName');
      // let res=await geitCard({idCard:value,name:fieldValue});
      callback();
    }
  };
  render() {
    const {
      visible,
      selected,
      keyPath,
      key,
      pagination = {},
      memInfo = [],
      addr = {},
      showOrgName = '',
    } = this.state;
    const { basicInfo = {} } = this.props.unit;
    const { filterHeight, loading, children, tipMsg = {} } = this.props;
    const { getFieldDecorator } = this.props.form;
    return (
      <React.Fragment>
        {children
          ? React.cloneElement(children as any, {
              onClick: this.showModal,
              key: 'container',
            })
          : null}
        <Modal
          title="省内流入登记"
          className="out_Modal"
          destroyOnClose
          closable={false}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
        >
          <div className="container">
            {!isEmpty(memInfo) && (
              <div className={styles.head}>
                <div>
                  <p>党员:{memInfo[0]['name']}</p>
                  <p>党员类型:{memInfo[0]['d08Name']}</p>
                </div>
                <div>
                  <p>
                    <span>从:</span>
                    {memInfo[0]['orgName']}
                  </p>
                  <p>
                    <span>到:</span>
                    {showOrgName}
                  </p>
                </div>
              </div>
            )}
            <Form {...formItemLayout}>
              {/*<FormItem*/}
              {/*  label={'党员姓名'}*/}
              {/*>*/}
              {/*  {getFieldDecorator('mem', {*/}
              {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
              {/*    rules: [*/}
              {/*      { required: true, message: '请选择党员!' },*/}
              {/*      // { validator: this.validFunction }*/}
              {/*    ],*/}
              {/*  })(*/}
              {/*    <MemSelect onChange={this.outMem} org={root} isPermissionCheck={'0'} searchType={'0'}/>*/}
              {/*  )}*/}
              {/*</FormItem>*/}

              <FormItem label={formLabel('党员姓名', tipMsg['memName'])}>
                {getFieldDecorator('memName', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '党员姓名!' },
                    // { validator: this.validFunction }
                  ],
                })(<Input />)}
              </FormItem>

              <FormItem label={formLabel('身份证号', tipMsg['idcard'])}>
                {getFieldDecorator('idcard', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '身份证号!' },
                    { validator: this.validatorIdcard },
                  ],
                })(<Input />)}
              </FormItem>

              <FormItem label={formLabel('流入来源地', tipMsg['outflowArea'])}>
                {getFieldDecorator('outflowArea', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择流入来源地!' },
                    // { validator: this.validFunction }
                  ],
                })(<DictArea onChange={this.outAddr} areaCode={root['areaCode']} />)}
                {getFieldDecorator('outflowAreaName')(<Input style={{ display: 'none' }} />)}
              </FormItem>
              <FormItem label={formLabel('流动类型', tipMsg['isProvOut'])}>
                {getFieldDecorator('isProvOut', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择流动类型!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <DictSelect
                    codeType={'dict_d34'}
                    disabled={true}
                    backType={'object'}
                    initValue={this.state.isProvOut?.id}
                  />,
                )}
              </FormItem>
              <FormItem label={formLabel('流入党支部', tipMsg['outflowOrg'])}>
                {getFieldDecorator('outflowOrg', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择流入党支部!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  // <SearchOrg backType={'object'} params={{d01CodeList:['63','631','632']}}/>
                  <OrgSelect
                    orgTypeList={['3', '4']}
                    onChange={this.showOrg}
                    org={{ orgCode: getSession('user').orgCode }}
                    isPermissionCheck={'0'}
                  />,
                )}
              </FormItem>
              {/*<FormItem*/}
              {/*  label={'外出原因类型'}*/}
              {/*>*/}
              {/*  {getFieldDecorator('outflowReasonCode', {*/}
              {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
              {/*    rules: [*/}
              {/*      { required: false, message: '请选择外出原因类型!' },*/}
              {/*      // { validator: this.validFunction }*/}
              {/*    ],*/}
              {/*  })(*/}
              {/*    <DictSelect codeType={'dict_d41'} backType={'object'}/>*/}
              {/*  )}*/}
              {/*</FormItem>*/}
              <FormItem label={formLabel('流出党组织联系人', tipMsg['outflowOrgLinkman'])}>
                {getFieldDecorator('outflowOrgLinkman', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请填写流出党组织联系人!' },
                    { validator: this.validFunction },
                  ],
                })(<Input placeholder="请输入" />)}
              </FormItem>
              <FormItem label={formLabel('流出党组织联系方式', tipMsg['outflowOrgPhone'])}>
                {getFieldDecorator('outflowOrgPhone', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请填写流出党组织联系方式!' },
                    { validator: this.validFunction },
                  ],
                })(<Input placeholder="请输入" />)}
              </FormItem>
              {/*<FormItem*/}
              {/*  label={'流动原因'}*/}
              {/*>*/}
              {/*  {getFieldDecorator('outflowReason', {*/}
              {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
              {/*    rules: [*/}
              {/*      {pattern:/(^[^\s]*$)/g, message:'不能输入空格'}*/}
              {/*      // { required: true, message: '必填!' },*/}
              {/*      // { validator: this.validFunction }*/}
              {/*    ],*/}
              {/*  })(*/}
              {/*    <Input placeholder="请输入" />*/}
              {/*  )}*/}
              {/*</FormItem>*/}
              <FormItem label={formLabel('流动党员活动证', tipMsg['isHold'])}>
                {getFieldDecorator('isHold', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择流动党员活动证是否发放!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <RadioGroup>
                    <Radio value={1}>已发放</Radio>
                    <Radio value={0}>未发放</Radio>
                  </RadioGroup>,
                )}
              </FormItem>
              <FormItem label={formLabel('流入日期', tipMsg['outflowDate'])}>
                {getFieldDecorator('outflowDate', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择流入日期!' },
                    // { validator: this.validFunction }
                    // <DatePicker style={{width:'100%'}} disabledDate={this.disabledDate}/>
                  ],
                })(<Date disabledDate={this.disabledDate} />)}
              </FormItem>
            </Form>
          </div>
        </Modal>
      </React.Fragment>
    );
  }
}
export default Form.create()(index);
