.tagTree{
  &>div{
    display: table-cell;
  }
  h6{
    margin-right: 20px;
    font-size: 15px;
    white-space: nowrap;
  }
  :global{
    .ant-tag{
      margin-right: 15px;
      font-size: 15px;
    }
  }
}
.tagMore{
  opacity: .1;
  max-height: 0;
  overflow: hidden;
  visibility: hidden;
  transition: all .5s;
}
.tagMoreActive{
  opacity: 1;
  max-height: 500px;
  visibility: visible;
  transition: all .5s;
}
.bootBtn{
  text-align: center;
  color: #1890FF;
  cursor: pointer;
  width: 100%;
  display: table;
  white-space: nowrap;
  &>span{
    padding: 0 16px;
  }
  &::before, &::after{
    border-top: 1px solid #e8e8e8;
    width: 50%;
    content: "";
    display: table-cell;
    position: relative;
    transform: translateY(50%);
  }
}
.tags{
  margin: 4px auto;
}
.allBtn{
  white-space: nowrap;
}
.other{
  background-color: #ffc53d;
  color: white !important;
  :global{
    .ant-tag-checkable:not(.ant-tag-checkable-checked):hover{
      color: white !important;
    }
  }
}

.card{
  display: flex;
  justify-content: space-between;
  align-items: start;
  flex-direction: row;
  .more {
    color: #1890FF;
    cursor: pointer;
    min-width: 60px;
    margin-top: 4px;
  }
}
