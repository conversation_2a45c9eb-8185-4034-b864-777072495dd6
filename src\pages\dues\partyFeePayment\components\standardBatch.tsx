/**
 * 批量设置 党费标准
 * */

import React, { useState, useRef, useEffect, useImperativeHandle } from 'react';
import {
  Input,
  Select,
  Modal,
  Tabs,
  Button,
  Divider,
  Popconfirm,
  Space,
  Radio,
  InputNumber,
} from 'antd';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _cloneDeep from 'lodash/cloneDeep';
import _isNumber from 'lodash/isNumber';
import MemSelect from '@/components/MemSelect';
import Tip from '@/components/Tip';
import Date from '@/components/Date';
import { getSession } from '@/utils/session';
import ListTable from 'src/components/ListTable';
import DictSelect from '@/components/DictSelect';
import { listStandard, batchStandard } from '../../services';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const index = (props: any, ref: any) => {
  const memselectRef: any = useRef();

  const org: any = getSession('org') || {};
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('批量设置党费标准');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [basinInfo, setBasinInfo] = useState<any>({});
  const [listLoading, setListLoading] = useState(false);
  const [listData, setListData]: any = useState([]);
  const [selectMem, setSelectMem]: any = useState([]);
  const [selectMemName, setSelectMemName] = useState([]);
  const [selectMemCode, setSelectMemCode] = useState([]);

  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 40,
      render: (text, record, index) => {
        return index + 1;
      },
    },
    {
      title: '姓名',
      dataIndex: 'name',
      align: 'center',
      width: 60,
    },
    {
      title: '所属组织',
      dataIndex: 'orgName',
      //   align: 'center',
      width: 100,
    },
    {
      title: '身份证号码',
      dataIndex: 'idcard',
      align: 'center',
      width: 80,
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
          if (text.indexOf('*') > 0) {
            return text;
          }
          return <span>{newVal}</span>;
        } else {
          return '';
        }
      },
    },
    {
      title: '起交时间',
      dataIndex: 'payDate',
      width: 90,
      align: 'center',
      render: (text, record, index) => {
        if (text) {
          return <div>{moment(text).format('YYYY-MM')}</div>;
        } else {
          return <div>未设置起交时间</div>;
        }
      },
    },
    {
      title: '党费标准类型',
      width: 150,
      align: 'center',
      dataIndex: 'd49Code',
      render: (text, record, index) => {
        if (record?.payDate) {
          return (
            <div style={{ width: 150, margin: '0 auto' }}>
              <Select
                style={{ width: 150 }}
                onChange={(e) => {
                  let newData = listData.map((item: any) => {
                    if (item?.memCode === record?.memCode) {
                      switch (e) {
                        case '1':
                          return {
                            ...item,
                            d49Code: e,
                            cardinalNumber: undefined,
                            calculationRatio: undefined,
                            standard: undefined,
                            cause: undefined,
                          };
                        case '2':
                          return {
                            ...item,
                            d49Code: e,
                            cause: undefined,
                            standard: undefined,
                          };
                        case '3':
                          return {
                            ...item,
                            d49Code: e,
                            cardinalNumber: undefined,
                            calculationRatio: undefined,
                          };
                        case '4':
                          return {
                            ...item,
                            d49Code: e,
                            cardinalNumber: undefined,
                            calculationRatio: undefined,
                            standard: undefined,
                          };
                        default:
                          return {
                            ...item,
                            d49Code: undefined,
                            cardinalNumber: undefined,
                            calculationRatio: undefined,
                            standard: undefined,
                          };
                      }
                      // if (e == '2') {
                      //   return { ...item, d49Code: e };
                      // } else {
                      //   return { ...item, d49Code: e, calculationRatio: undefined };
                      // }
                    } else {
                      return item;
                    }
                  });
                  setListData(newData);
                }}
              >
                <Select.Option value={'1'}>按标准交纳</Select.Option>
                <Select.Option value={'2'}>按工资比例交纳</Select.Option>
                <Select.Option value={'3'}>少交</Select.Option>
                <Select.Option value={'4'}>免交</Select.Option>
              </Select>
              {/* <DictSelect
                showConstant={false}
                // backType="object"
                codeType={'dict_d49'}
                initValue={text || record['d49Code'] || ''}
                onChange={(e) => {}}
              /> */}
            </div>
          );
        } else {
          return;
        }
      },
    },
    {
      title: '党费基数',
      width: 80,
      align: 'center',
      dataIndex: 'cardinalNumber',
      render: (text, record, index) => {
        if (record?.d49Code === '2' && record?.payDate) {
          return (
            <InputNumber
              min={0}
              max={999999}
              style={{ width: '100%' }}
              onChange={(e) => {
                console.log('党费基数===', e);
                // 计算比例
                // ①每月工资收入（税后）在3000元以下（含3000元）者，交纳月工资收入的0.5%；
                // ②3000元以上至5000元（含5000元）者，交纳1%；
                // ③5000元以上至10000元（含10000元）者，交纳1.5%；
                // ④10000元以上者，交纳2%。
                let calculationRatio = 0.005;
                let standard = 5;
                if (e <= 3000) {
                  calculationRatio = 0.005;
                  standard = e * 0.005;
                }
                if (e > 3000 && e <= 5000) {
                  calculationRatio = 0.01;
                  standard = e * 0.01;
                }
                if (e > 5000 && e <= 10000) {
                  calculationRatio = 0.015;
                  standard = e * 0.015;
                }
                if (e > 10000) {
                  calculationRatio = 0.02;
                  standard = e * 0.02;
                }
                let newData = listData.map((item: any) => {
                  if (item?.memCode === record?.memCode) {
                    console.log('calculationRatio===', calculationRatio, 'standard===', standard);

                    return { ...item, calculationRatio, standard, cardinalNumber: e };
                  } else {
                    return item;
                  }
                });
                setListData(newData);
              }}
            />
          );
        }
      },
    },
    {
      title: '计算比例',
      width: 60,
      align: 'center',
      dataIndex: 'calculationRatio',
      render: (text, record, index) => {
        if (record?.d49Code === '2' && record?.payDate) {
          return <div>{text}</div>;
        } else {
          return;
        }
      },
    },
    {
      title: '党费标准',
      width: 60,
      align: 'center',
      dataIndex: 'standard',
      render: (text, record, index) => {
        console.log('党费标准====', text);
        if (record?.payDate) {
          if (record?.d49Code === '2') {
            return (
              <InputNumber
                value={text}
                disabled
                min={0}
                max={999999}
                precision={2}
                style={{ width: '100%' }}
              />
            );
          }
          if (record?.d49Code === '4') {
            return;
          } else {
            return (
              <InputNumber
                defaultValue={text}
                min={0}
                max={999999}
                precision={2}
                style={{ width: '100%' }}
                onChange={(e) => {
                  console.log('党费标准===', e);
                  let newData = listData.map((item: any) => {
                    if (item?.memCode === record?.memCode) {
                      return { ...item, standard: e };
                    } else {
                      return item;
                    }
                  });
                  setListData(newData);
                }}
              />
            );
          }
        } else {
          return;
        }
      },
    },
    {
      title: '标准适用到(月份)',
      width: 100,
      align: 'center',
      dataIndex: 'standardContinueToDate',
      render: (text, record, index) => {
        if (text) {
          return (
            <Date
              isDefaultEnd={false}
              disabled
              mode="YYYY.MM"
              value={moment(text)}
              style={{ width: '100%' }}
              // onChange={(e) => {
              //   console.log('标准适用到===', e);
              //   let newData = listData.map((item: any) => {
              //     if (item?.memCode === record?.memCode) {
              //       return { ...item, standardContinueToDate: e ? moment(e).valueOf() : lastM };
              //     } else {
              //       return item;
              //     }
              //   });
              //   setListData(newData);
              // }}
            />
          );
        } else {
          return;
        }
      },
    },
    {
      title: '免交/少交原因',
      width: 100,
      // align: 'center',
      dataIndex: 'cause',
      render: (text, record, index) => {
        if ((record?.d49Code === '3' || record?.d49Code === '4') && record?.payDate) {
          return (
            <Input
              placeholder="输入原因"
              style={{ width: '100%' }}
              onChange={(e) => {
                console.log('输入原因===', e);
                let newData = listData.map((item: any) => {
                  if (item?.memCode === record?.memCode) {
                    return { ...item, cause: e.target.value || undefined };
                  } else {
                    return item;
                  }
                });
                setListData(newData);
              }}
            />
          );
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 60,
      render: (text, record) => {
        return (
          <div>
            <Popconfirm
              title="确定要删除吗？"
              onConfirm={() => {
                let newArr = listData.filter((item) => item?.code !== record?.code);
                let _selectMem = selectMem.filter((it) => it.code !== record.code);
                setListData(newArr);
                setSelectMem(_selectMem);
                memselectRef?.current?.outChangeselectedRows(_selectMem);
                // let newNames=newArr.map((item)=>item?.name)
                // setSelectMemName(newNames)
                // let newCodes = newArr.map((item)=>item?.memCode)
                // setSelectMemCode(newCodes)
              }}
            >
              <a className={'del'}>删除</a>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  useImperativeHandle(ref, () => ({
    open: (query?: any) => {
      setBasinInfo(query);
      setVisible(true);
    },
  }));
  const getList = async (memArr: any) => {
    setListLoading(true);
    if (!_isEmpty(memArr)) {
      let idArr: any = memArr.map((item: any) => {
        return { memCode: item?.code };
      });
      const { code: resCode = 500, data = [] } = await listStandard({
        data: idArr,
      });
      setListLoading(false);
      if (resCode === 0) {
        // 统一设置 标准适用到月份为 当年12月
        let listArr = _cloneDeep(data);
        listArr = listArr.map((item: any, index: any) => {
          const { payDate = undefined } = item;
          let standardContinueToDate: any = undefined;
          if (payDate) {
            standardContinueToDate = moment(payDate).month(11).valueOf();
          }
          let find = memArr.find((it) => it.code == item.code);
          return { ...item, standardContinueToDate, ...find };
        });
        setListData(listArr);
      }
    }
  };
  const hadndleFinish = async () => {
    console.log('listData===', listData);
    if (!_isEmpty(listData)) {
      // let noDateMem = listData.find((item) => !item?.payDate);
      let hasNoDateMem = false;
      let hasNoFill = false;
      let lastList = listData.map((item) => {
        const {
          code: memCode = undefined,
          payDate = undefined,
          d49Code = undefined,
          cardinalNumber = undefined,
          calculationRatio = undefined,
          standard = undefined,
          standardContinueToDate = undefined,
          cause = undefined,
        } = item;
        if (!payDate) {
          hasNoDateMem = true;
        }
        if (!d49Code) {
          hasNoFill = true;
        }
        if (d49Code === '1') {
          if (!_isNumber(standard) || !_isNumber(standardContinueToDate)) {
            hasNoFill = true;
          } else {
            return { memCode, d49Code, standard, standardContinueToDate };
          }
        }
        if (d49Code === '2') {
          if (
            !_isNumber(cardinalNumber) ||
            !_isNumber(calculationRatio) ||
            !_isNumber(standard) ||
            !_isNumber(standardContinueToDate)
          ) {
            hasNoFill = true;
          } else {
            return {
              memCode,
              d49Code,
              cardinalNumber,
              calculationRatio,
              standard,
              standardContinueToDate,
            };
          }
        }
        if (d49Code === '3') {
          if (!_isNumber(standard) || !_isNumber(standardContinueToDate) || _isEmpty(cause)) {
            hasNoFill = true;
          } else {
            return { memCode, d49Code, standard, standardContinueToDate, cause };
          }
        }
        if (d49Code === '4') {
          if (!_isNumber(standardContinueToDate) || _isEmpty(cause)) {
            hasNoFill = true;
          } else {
            return { memCode, d49Code, standardContinueToDate, cause };
          }
        }
      });
      if (hasNoDateMem) {
        Tip.error('操作提示', '存在未设置起交时间的党员');
        return;
      } else if (hasNoFill) {
        Tip.error('操作提示', '请完善党费标准填写');
        return;
      } else {
        setConfirmLoading(true);
        const { code: resCode = 500 } = await batchStandard({
          data: lastList,
        });
        setConfirmLoading(false);
        if (resCode == 0) {
          const { onOk } = props;
          Tip.success('操作提示', '操作成功');
          handleCancel();
          onOk && onOk();
        }
      }
    }
  };
  const handleCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
    setListLoading(false);
    setBasinInfo({});
    setListData([]);
    setSelectMem([]);
  };
  return (
    <Modal
      maskClosable={false}
      title={title}
      visible={visible}
      onOk={() => {
        hadndleFinish();
      }}
      onCancel={handleCancel}
      width={'1400px'}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
      bodyStyle={{ padding: '20px', overflow: 'auto' }}
    >
      <div style={{ color: '#faad14', paddingBottom: '20px' }}>
        注意：使用【批量添加党费标准】时，需要满足党员党费标准从起交时间起全年是统一的一个标准，若存在不同月份不同标准的情况下不适用批量操作。
      </div>
      <div style={{ display: 'flex', justifyContent: 'space-between', paddingBottom: '20px' }}>
        <div>当前组织：{org?.name}</div>
        <div>时间：{moment().format('YYYY年MM月DD日')}</div>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', paddingBottom: '20px' }}>
        <div style={{ whiteSpace: 'nowrap', marginRight: '10px' }}>党员姓名</div>
        <MemSelect
          org={org}
          checkType="checkbox"
          ref={memselectRef}
          selectedRows={selectMem}
          placeholder="请选择"
          // selectedRows={selectMemCode}
          onChange={(e) => {
            if (!_isEmpty(e)) {
              setSelectMem(e);
              getList(e);
            } else {
              setSelectMem([]);
              setListData([]);
            }
          }}
        />
      </div>
      <ListTable
        rowKey={'memCode'}
        
        columns={columns}
        data={listData}
        // pagination={pagination}
        // onPageChange={(page, pageSize) => {
        //   getList({ pageNum: page, pageSize });
        // }}
      />
    </Modal>
  );
};

export default React.forwardRef(index);
