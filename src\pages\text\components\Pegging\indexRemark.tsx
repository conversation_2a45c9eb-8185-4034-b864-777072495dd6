import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Checkbox, Modal } from 'antd';
import ListTable from "@/components/ListTable";
import WhiteSpace from "@/components/WhiteSpace";
import styles from './index.less';
import moment from 'moment';
import SpinProgress from '@/components/SpinProgress';
import {isEmpty, fileDownloadHeader} from '@/utils/method';
// import {exportVerification} from '@/pages/services/annualstats/positionNum';
function formatTime(time,format='YYYY.MM'){
  if(time){
    return moment(time).format(format)
  }
  return undefined;
}
function contactNameAndPhone(record){
  if ( !isEmpty(record['contact']) ){
    if (isEmpty(record['phone'])) {
      return record['contact']
    }else {
      return  `${record['contact']}/${record['phone']}`
    }
  } else {
    if (isEmpty(record['phone'])) {
      return record['contact']
    }else {
      return `${record['contact']}/${record['phone']}`
    }
  }

};

const disMem=['1','2','3','4','5','6','7','8','9','10','11','12','13','74','75','76'],disOrg=['1','2','3','4'],
  disInstitution=['1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22'],
  disTeam=['1','2','3','4','5','6','9','10','11','12','13','14','15','16','17'],
  disUnit=['1','2','3','4','5','6','9','10','11','12','13','14','15'],
  disTransfer=['1','2','3','4','5','6','7'];

const TableMem=[
  {key:'ryxx',label: '人员基本信息'},
  {key:'1',label:'姓名',value:{title:'姓名',dataIndex:'name',width:70,fixed:true}},
  {key:'2',label:'身份证号码',value:{title:'身份证号码',dataIndex:'idNum',width:150,fixed:true}},
  {key:'3',label:'性别',value:{title:'性别',dataIndex:'sex',width:50,render:(text)=>text=='1' ? '男' : '女'}},
  {key:'4',label:'年龄',value:{title:'年龄',dataIndex:'age',width:50}},
  {key:'5',label:'民族',value:{title:'民族',dataIndex:'ethnic',width:80,render:(text)=>text=='01' ? '汉族' : '少数民族'}},
  {key:'6',label:'出生年月',value:{title:'出生年月',dataIndex:'birthdate',width:80,render:(text)=>formatTime(text)}},
  {key:'7',label:'年份',value:{title:'年份',dataIndex:'year',width: 50}},
  {key:'8',label:'统计层次',value:{title:'统计层次',dataIndex:'countLevel',width: 100}},
  {key:'74',label:'人员分布情况',value:{title:'人员分布情况',dataIndex:'distributionName',width: 100}},
  {key:'75',label:'人员类型',value:{title:'人员类型',dataIndex:'typeName',width: 80}},
  {key:'9',label:'参加工作时间',value:{title:'参加工作时间',dataIndex:'joinWorkDate',width: 70,render:(text)=>formatTime(text)}},
  {key:'10',label:'政治面貌',value:{title:'政治面貌',dataIndex:'politicCountenance',width: 90,render:(text)=> (text=='01'|| text==='02') ? '中共党员' : '非中共党员'}},
  {key:'11',label:'全日制学历',value:{title:'全日制学历',dataIndex:'educationFullTimeName',width: 100}},
  {key:'12',label:'最高学位',value:{title:'最高学位',dataIndex:'educationDegreeName',width: 80}},
  {key:'13',label:'最高学历',value:{title:'最高学历',dataIndex:'educationName',width: 80}},
  {key:'14',label:'上年度是否为领导成员',value:{title:'上年度是否为领导成员',dataIndex:'isPreLeaderMem',width: 90}},
  {key:'15',label:'是否兼任领导职务',value:{title:'是否兼任领导职务',dataIndex:'isLeaderJob',width: 80}},
  // {key:'16',label:'是否属于乡人大机关',value:{title:'是否属于乡人大机关',dataIndex:'isVillageNpc',width: 90}},
  {key:'76',label:'现工作单位及职务',value:{title:'现工作单位及职务',dataIndex:'curWorkUnitAndJob',width: 200}},
  // {key:'84',label:'是否纳入年度统计',value:{title:'是否纳入年度统计',dataIndex:'isYearCount',width: 80}},
  {key:'85',label:'是否具有2年以上基层工作经历',value:{title:'是否具有2年以上基层工作经历',dataIndex:'addIsTwoYearWorkExpNew',width: 120}},
  {key:'90',label:'任现职务层次年限',value:{title:'任现职务层次年限',dataIndex:'jobCurLevelDateCount',width: 80}},
  {key:'rybhqk',label: '人员变化情况'},
  {key:'17',label:'变化情况',value:{title:'变化情况',dataIndex:'changeStatus',width: 80,render:(text)=>text=='1' ? '新增人员' : text=='2' ? '减少人员' : '无变化'}},
  {key:'18',label:'是否有2年基层工作经验',value:{title:'是否有2年基层工作经验',width: 100,dataIndex:'addIsTwoYearWorkExp'}},
  {key:'19',label:'是否留学回归人员',value:{title:'是否留学回归人员',width: 80,dataIndex:'addIsBackMem'}},
  {key:'20',label:'录用时间',value:{title:'录用时间',dataIndex:'addTakeTime',width: 80,render:(text)=>formatTime(text)}},
  {key:'21',label:'减少原因',value:{title:'减少原因',dataIndex:'reduceReasonName',width: 80}},
  {key:'82',label:'新增类型',value:{title:'新增类型',dataIndex:'addＭemTypeName',width: 80}},
  // {key:'82',label:'减少原因',value:{title:'减少原因',dataIndex:'reduceReasonName',width: 80}},
  {key:'22',label:'减少时职务层次',value:{title:'减少时职务层次',dataIndex:'reduceJobLevelName',width: 80}},
  {key:'23',label:'是否交流',value:{title:'是否交流',dataIndex:'reduceIsSwap',width: 80}},
  {key:'zwjs',label: '职务（职级）晋升'},
  // {key:'77',label:'晋升类别',value:{title:'晋升类别',dataIndex:'promotionTypeName',width: 80}},
  {key:'24',label:'晋升时全日制学历',value:{title:'晋升时全日制学历',dataIndex:'promotionFullTimeEducationName',width: 80}},
  {key:'25',label:'晋升时最高学历',value:{title:'晋升时最高学历',dataIndex:'promotionEducationName',width: 80}},
  {key:'26',label:'晋升时间',value:{title:'晋升时间',dataIndex:'promotionTime',width: 80,render:(text)=>formatTime(text)}},
  {key:'27',label:'晋升时是否是中共党员',value:{title:'晋升时是否是中共党员',dataIndex:'promotionIsCpcMember',width: 100}},
  {key:'28',label:'晋升后职务层次',value:{title:'晋升后职务层次',dataIndex:'promotionJobLevelName',width: 140}},
  {key:'29',label:'晋升前职务层次',value:{title:'晋升前职务层次',dataIndex:'promotionPreJobLevelName',width: 140}},
  {key:'30',label:'前一职级任职时间',value:{title:'前一职级任职时间',dataIndex:'promotionPreJobTime',width: 80,render:(text)=>formatTime(text)}},
  // {key:'31',label:'晋升类别(废除)',value:{title:'晋升类别(废除)',dataIndex:'promotionType',width: 80}},
  {key:'32',label:'是否破格提拔',value:{title:'是否破格提拔',dataIndex:'promotionIsPromoted',width: 80}},
  {key:'33',label:'是否越级提拔',value:{title:'是否越级提拔',dataIndex:'promotionIsLeapfrogPromoted',width: 80}},
  {key:'34',label:'是否公开选拔',value:{title:'是否公开选拔',dataIndex:'promotionIsPublicPromoted',width: 80}},
  {key:'35',label:'是否竞争上岗',value:{title:'是否竞争上岗',dataIndex:'promotionIsCompetition',width: 80}},
  {key:'zjjs',label: '职级晋升'},
  // {key:'36',label:'本年底享受职级待遇',value:{title:'本年底享受职级待遇',dataIndex:'jobCurTreatment',width: 100}},
  {key:'78',label:'本年底享受职级待遇',value:{title:'本年底享受职级待遇',dataIndex:'jobCurTreatmentName',width: 100}},
  // {key:'37',label:'现任职务类型',value:{title:'现任职务类型',dataIndex:'jobCurTypeName',width: 80}},
  {key:'38',label:'现任职务层次',value:{title:'现任职务层次',dataIndex:'jobCurLevelName',width: 140}},
  // {key:'39',label:'现任职务层次时间',value:{title:'现任职务层次时间',dataIndex:'jobCurLevelDate',width: 80,render:(text)=>formatTime(text)}},
  // {key:'40',label:'享受当前职级待遇时间(废除)',value:{title:'享受当前职级待遇时间(废除)',dataIndex:'jobCurTreatmentTime',width: 120,render:(text)=>formatTime(text)}},
  {key:'87',label:'本年度晋升职级待遇情况',value:{title:'本年度晋升职级待遇情况',dataIndex:'jobCurYearTreatmentName',width: 110}},
  {key:'jlqk',label: '交流情况'},
  {key:'79',label:'交流前职务',value:{title:'交流前职务',dataIndex:'swapPreJobName',width: 80}},
  // {key:'80',label:'交流前职务层次',value:{title:'交流前职务层次',dataIndex:'swapPreLevelName',width: 80}},
  // {key:'41',label:'交流前职务',value:{title:'交流前职务',dataIndex:'swapPreJob',width: 120}},
  {key:'42',label:'交流时是否是中共党员',value:{title:'交流时是否是中共党员',dataIndex:'swapIsCpcMember',width: 100}},
  {key:'43',label:'交流时职务层次',value:{title:'交流时职务层次',dataIndex:'swapJobLevelName',width: 120}},
  {key:'44',label:'交流时间',value:{title:'交流时间',dataIndex:'swapTime',width: 80,render:(text)=>formatTime(text)}},
  {key:'45',label:'交流形式',value:{title:'交流形式',dataIndex:'swapFormalName',width: 80}},
  {key:'46',label:'是否任职满10年交流',value:{title:'是否任职满10年交流',dataIndex:'swapIsFullTenYear',width: 90}},
  // {key:'47',label:'交流去向',value:{title:'交流去向',dataIndex:'swapDirection',width: 80}},
  {key:'83',label:'交流去向',value:{title:'交流去向',dataIndex:'swapDirectionName',width: 80}},
  {key:'48',label:'是否回避交流',value:{title:'是否回避交流',dataIndex:'swapIsAvoid',width: 80}},
  {key:'88',label:'交流时全日制学历',value:{title:'交流时全日制学历',dataIndex:'swapEducationFullTimeName',width: 80}},
  {key:'89',label:'交流时最高学历',value:{title:'交流时最高学历',dataIndex:'swapEducationName',width: 80}},
  {key:'khqk',label: '考核情况'},
  {key:'49',label:'考核时职务层次',value:{title:'考核时职务层次',dataIndex:'assessJobLevelName',width: 80}},
  {key:'50',label:'考核结果',value:{title:'考核结果',dataIndex:'assessResultName',width: 80}},
  {key:'khqk',label: '奖惩情况'},
  {key:'51',label:'奖励项目',value:{title:'奖励项目',dataIndex:'rpProjectName',width: 80}},
  {key:'52',label:'奖励时职务层次',value:{title:'奖励时职务层次',dataIndex:'rpRewardJobLevelName',width: 80}},
  {key:'53',label:'是否有惩罚信息',value:{title:'是否有惩罚信息',dataIndex:'rpIsPenalty',width: 80}},
  {key:'54',label:'本年度是否有被免职',value:{title:'本年度是否有被免职',dataIndex:'rpIsDismissal',width: 90}},
  {key:'55',label:'免职时职务层次',value:{title:'免职时职务层次',dataIndex:'rpDismissalJobLevelName',width: 80}},
  {key:'56',label:'本年度是否有被降职',value:{title:'本年度是否有被降职',dataIndex:'rpIsDemotion',width: 90}},
  {key:'57',label:'降职时职务层次',value:{title:'降职时职务层次',dataIndex:'rpDemotionJobLevelName',width: 80}},
  {key:'58',label:'辞去领导职务',value:{title:'辞去领导职务',dataIndex:'rpResignationLeaderJobName',width: 80}},

  {key:'59',label:'辞去领导职务职务层次',value:{title:'辞去领导职务职务层次',dataIndex:'rpResignationLeaderJobLevelName',width: 90}},
  {key:'60',label:'政纪处分',value:{title:'政纪处分',dataIndex:'rpPunishName',width: 80}},
  {key:'61',label:'政纪处分时职务层次',value:{title:'政纪处分时职务层次',dataIndex:'rpPunishJobLevelName',width: 90}},
  {key:'62',label:'是否提出申诉',value:{title:'是否提出申诉',dataIndex:'rpIsAppeal',width: 80}},
  {key:'63',label:'是否接受申诉',value:{title:'是否接受申诉',dataIndex:'rpIsAcceptAppeal',width: 80}},
  {key:'64',label:'申诉处理决定',value:{title:'申诉处理决定',dataIndex:'rpIsAppealResultName',width: 80}},
  {key:'65',label:'申诉时职务层次',value:{title:'申诉时职务层次',dataIndex:'rpIsAppealJobLevelName',width: 80}},
  {key:'66',label:'是否提出再申诉',value:{title:'是否提出再申诉',dataIndex:'rpIsReappeal',width: 80}},
  {key:'67',label:'是否接受再申诉',value:{title:'是否接受再申诉',dataIndex:'rpIsReappealAccept',width: 80}},
  {key:'68',label:'再申诉处理决定',value:{title:'再申诉处理决定',dataIndex:'rpIsReappealResultName',width: 80}},
  {key:'69',label:'再申诉时职务层次',value:{title:'再申诉时职务层次',dataIndex:'rpIsReappealJobLevelName',width: 80}},
  {key:'rypx',label: '人员培训'},
  {key:'70',label:'是否培训',value:{title:'是否培训',dataIndex:'pxIsTrain',width: 80}},
  {key:'71',label:'培训总学时',value:{title:'培训总学时',dataIndex:'pxTrainTotalHour',width: 80}},
  {key:'72',label:'出国培训总次数',value:{title:'出国培训总次数',dataIndex:'pxTrainAbroad',width: 80}},
  {key:'73',label:'西部12省区市参加对口支援培训总次数',value:{title:'西部12省区市参加对口支援培训总次数',dataIndex:'pxTrainWest',width: 150}},
  // {key:'86',label:'是否维护',value:{title:'是否维护',dataIndex:'isUpdate',width: 80}},
  // {key:'91',label:'逐级晋升在下一级岗位任职年限',value:{title:'逐级晋升在下一级岗位任职年限',dataIndex:'promotionPreJobTimeCount',width: 120}},
];
const TableOrg=[
  {key:'1',label:'单位名称',value:{title:'单位名称',dataIndex:'name'}},
  {key:'2',label:'单位简称',value:{title:'单位简称',dataIndex:'shortName'}},
  {key:'3',label:'单位类型',value:{title:'单位类型',dataIndex:'typeName'}},
  {key:'4',label:'隶属类型',value:{title:'隶属类型',dataIndex:'affiliationName'}},
  {key:'5',label:'统计层次',value:{title:'统计层次',dataIndex:'countLevelName'}},
  {key:'6',label:'联系人名称',value:{title:'联系人名称',dataIndex:'contactName'}},
  {key:'7',label:'联系人电话',value:{title:'联系人电话',dataIndex:'contactPhone'}},
  // {key:'8',label:'是否纳入年度统计',value:{title:'是否纳入年度统计',dataIndex:'isYearCount'}},
  {key:'9',label:'是否统计公务员表',value:{title:'是否统计公务员表',dataIndex:'activeGwy'}},
  {key:'10',label:'公务员分布情况',value:{title:'公务员分布情况',dataIndex:'gwyDistributionName'}},
  {key:'11',label:'公务员上年末实有数',value:{title:'公务员上年末实有数',dataIndex:'gwyPreYearCount'}},
  {key:'12',label:'公务员本年度应有数',value:{title:'公务员本年度应有数',dataIndex:'gwyCurYearCount'}},
  {key:'13',label:'是否统计参公群团表',value:{title:'是否统计参公群团表',dataIndex:'activeCgqt'}},
  {key:'14',label:'参公群团分布情况',value:{title:'参公群团分布情况',dataIndex:'cgqtDistributionName'}},
  {key:'15',label:'参公群团上年末实有数',value:{title:'参公群团上年末实有数',dataIndex:'cgqtPreYearCount'}},
  {key:'16',label:'参公群团本年末实有数',value:{title:'参公群团本年末实有数',dataIndex:'cgqtCurYearCount'}},
  {key:'17',label:'是否统计参公事业表',value:{title:'是否统计参公事业表',dataIndex:'activeCgsy'}},
  {key:'18',label:'参公事业分布情况',value:{title:'参公事业分布情况',dataIndex:'cgsyDistributionName'}},
  {key:'19',label:'参公事业上年末实有数',value:{title:'参公事业上年末实有数',dataIndex:'cgsyPreYearCount'}},
  {key:'20',label:'参公事业本年末实有数',value:{title:'参公事业本年末实有数',dataIndex:'cgsyCurYearCount'}},
  {key:'21',label:'是否统计班子表',value:{title:'是否统计班子表',dataIndex:'activeBz'}},
  {key:'22',label:'班子表统计类型',value:{title:'班子表统计类型',dataIndex:'bzCountTypeName'}},
  {key:'23',label:'行政规划类型',value:{title:'行政规划类型',dataIndex:'bzAreaName'}},
  {key:'24',label:'单位性质',value:{title:'单位性质',dataIndex:'natureName'}},
];
const TableInstitution=[
  {key:'1',label:'姓名',value:{title:'姓名',dataIndex:'name',width:80,fixed:true}},
  {key:'2',label:'身份证号码',value:{title:'身份证号码',dataIndex:'idNum',width:150,fixed:true}},
  {key:'3',label:'性别',value:{title:'性别',dataIndex:'sex',width:50,render:(text)=>text=='1' ? '男' : '女'}},
  {key:'4',label:'民族',value:{title:'民族',dataIndex:'ethnic',width:80,render:(text)=>text=='01' ? '汉族' : '少数民族'}},
  {key:'5',label:'出生年月',value:{title:'出生年月',dataIndex:'birthdate',width:80,render:(text)=>formatTime(text)}},
  {key:'6',label:'参加工作时间',value:{title:'参加工作时间',dataIndex:'joinWorkDate',width: 80,render:(text)=>formatTime(text)}},
  {key:'7',label:'政治面貌',value:{title:'政治面貌',dataIndex:'politicCountenance',width: 90,render:(text)=> (text=='01'|| text==='02') ? '中共党员' : '非中共党员'}},
  {key:'8',label:'全日制学历',value:{title:'全日制学历',dataIndex:'educationFullTimeName',width: 100}},
  {key:'9',label:'最高学位',value:{title:'最高学位',dataIndex:'educationDegreeName',width: 80}},
  {key:'10',label:'最高学历',value:{title:'最高学历',dataIndex:'educationName',width: 80}},
  {key:'11',label:'现工作单位及职务',value:{title:'现工作单位及职务',dataIndex:'jobUnitAndCurJob',width: 200,render:(text,record)=>record['curJob']}},
  {key:'12',label:'职务类别',value:{title:'职务类别',dataIndex:'jobTypeName',width: 50}},
  {key:'13',label:'管理岗位等级',value:{title:'管理岗位等级',dataIndex:'managerJobLevelName',width: 80}},
  {key:'14',label:<span>兼任专业技<br/>术职务等级</span>,value:{title:<span>兼任专业技<br/>术职务等级</span>,dataIndex:'specialtyJobLevelName',width: 100}},
  {key:'15',label:'任职来源情况',value:{title:'任职来源情况',width: 100,dataIndex:'sourceJobName'}},
  {key:'16',label:'选拔方式',value:{title:'选拔方式',width: 80,dataIndex:'selectTypeName'}},
  {key:'17',label:'任用方式',value:{title:'任用方式',dataIndex:'appointTypeName',width: 80}},
  {key:'18',label:'是否破格提拔',value:{title:'是否破格提拔',dataIndex:'isPromoted',width: 80,render:(text)=>text==1?'是':'否'}},
  {key:'19',label:'是否越级提拔',value:{title:'是否越级提拔',dataIndex:'isLeapfrogPromoted',width: 80,render:(text)=>text==1?'是':'否'}},
  {key:'20',label:<span>实行任期<br/>目标责任<br/>制情况</span>,value:{title:<span>实行任期<br/>目标责任<br/>制情况</span>,dataIndex:'termTargetInstitutionName',width: 80}},
  {key:'21',label:<span>上年度考<br/>核情况</span>,value:{title:<span>上年度考<br/>核情况</span>,dataIndex:'preYearAssessName',width: 80}},
  {key:'22',label:<span>年内任期<br/>考核情况</span>,value:{title:<span>年内任期<br/>考核情况</span>,dataIndex:'inYearAssessYear',width: 80}},
];
const TableUnit=[
  {key:'1',label:'单位名称',value:{title:'单位名称',dataIndex:'name',width:80}},
  {key:'2',label:'联系人/电话',value:{title:'联系人/电话',dataIndex:'contactNameAndPhone',width:150,render:(text,record)=>contactNameAndPhone(record)}},
  {key:'3',label:'统计层次',value:{title:'统计层次',dataIndex:'unitCountLevelName',width:50}},
  {key:'4',label:'单位属性',value:{title:'单位属性',dataIndex:'unitAttributesName',width:80}},
  {key:'5',label:'单位所属类别',value:{title:'单位所属类别',dataIndex:'unitTypeName',width:80}},
  {key:'6',label:'实行任期制情况',value:{title:'实行任期制情况',dataIndex:'termInstitutionName',width: 80}},
  {key:'7',label:<span>实行任期<br/>目标责任<br/>制情况</span>,value:{title:<span>实行任期<br/>目标责任<br/>制情况</span>,dataIndex:'termTargetInstitutionName',width: 90}},
  {key:'8',label:'上年度考核情况',value:{title:'上年度考核情况',dataIndex:'preYearAssessName',width: 100}},
  {key:'9',label:<span>年内任期<br/>考核情况</span>,value:{title:'最高学位',dataIndex:'inYearAssessName',width: 80}},
  {key:'10',label:<span>上年末正职<br/>领导人数</span>,value:{title:<span>上年末正职<br/>领导人数</span>,dataIndex:'preMainLeader',width: 80}},
  {key:'11',label:<span>上年末副职<br/>领导人数</span>,value:{title:<span>上年末副职<br/>领导人数</span>,dataIndex:'preMinorLeader',width: 80}},
  {key:'12',label:<span>上年末其他<br/>领导人数</span>,value:{title:<span>上年末其他<br/>领导人数</span>,dataIndex:'preOtherLeader',width: 80}},
  {key:'13',label:'正职领导职数',value:{title:'正职领导职数',dataIndex:'mainLeader',width: 80}},
  {key:'14',label:'副职领导职数',value:{title:'副职领导职数',dataIndex:'minorLeader',width: 80}},
  {key:'15',label:'其他领导职数',value:{title:'其他领导职数',width: 80,dataIndex:'otherLeader'}},
];
const TableTeam=[
  {key:'1',label:'姓名',value:{title:'姓名',dataIndex:'name',width:70,fixed:true}},
  {key:'2',label:'身份证号码',value:{title:'身份证号码',dataIndex:'idNum',width:150,fixed:true}},
  {key:'3',label:'性别',value:{title:'性别',dataIndex:'sex',width:50,render:(text)=>text=='1' ? '男' : '女'}},
  {key:'4',label:'年龄',value:{title:'年龄',dataIndex:'age',width:50}},
  {key:'5',label:'民族',value:{title:'民族',dataIndex:'ethnic',width:80,render:(text)=>text=='01' ? '汉族' : '少数民族'}},
  {key:'6',label:'出生年月',value:{title:'出生年月',dataIndex:'birthdate',width:80,render:(text)=>formatTime(text)}},
  {key:'9',label:'参加工作时间',value:{title:'参加工作时间',dataIndex:'joinWorkDate',width: 110,render:(text)=>formatTime(text)}},
  {key:'10',label:'政治面貌',value:{title:'政治面貌',dataIndex:'politicCountenance',width: 90,render:(text)=> (text=='01'|| text==='02') ? '中共党员' : '非中共党员'}},
  {key:'11',label:'全日制学历',value:{title:'全日制学历',dataIndex:'educationFullTimeName',width: 100}},
  {key:'12',label:'最高学位',value:{title:'最高学位',dataIndex:'educationDegreeName',width: 80}},
  {key:'13',label:'最高学历',value:{title:'最高学历',dataIndex:'educationName',width: 80}},
  {key:'14',label:'职务',value:{title:'职务',dataIndex:'jobName',width: 200}},
  {key:'15',label:'职务类别',value:{title:'职务类别',dataIndex:'jobTypeName',width: 80}},
  {key:'16',label:'任现职务时间',value:{title:'任现职务时间',dataIndex:'curJobTime',width: 110,render:(text)=>formatTime(text)}},
  {key:'17',label:'任现职级时间',value:{title:'任现职级时间',dataIndex:'curJobLevelTime',width: 110,render:(text)=>formatTime(text)}},
  {key:'18',label:'是否具有3年以上的乡镇（街道）企事业单位领导工作经历',value:{title:'是否具有3年以上的乡镇（街道）企事业单位领导工作经历',dataIndex:'isThreeYearExp',width: 180}},
  {key:'19',label:'备注',value:{title:'备注',dataIndex:'remark',width: 80}},
];
const TableTransfer=[
  {key:'1',label:'姓名',value:{title:'姓名',dataIndex:'name',width:70}},
  {key:'2',label:'身份证号码',value:{title:'身份证号码',dataIndex:'idNum',width:80}},
  {key:'3',label:'所在单位',value:{title:'所在单位',dataIndex:'orgName',width:150}},
  {key:'4',label:'是否乡镇领导成员',value:{title:'是否乡镇领导成员',dataIndex:'isTownsLeader',width:50,render:(text)=>text=='1' ? '是' : '否'}},
  {key:'5',label:'调离（调整）前党委职务',value:{title:'调离（调整）前党委职务',dataIndex:'adjustmentPreDwJobName',width:110}},
  {key:'6',label:'调离（调整）前政府职务',value:{title:'调离（调整）前政府职务',dataIndex:'adjustmentPreZfJobName',width:110}},
  {key:'7',label:'调离（调整）前担任同一职务年限',value:{title:'调离（调整）前担任同一职务年限',dataIndex:'adjustmentPreJobLimitName',width: 110}},
];
const TextColumn=[
  {key:'1',label:'姓名',value:{title:'姓名',dataIndex:'name',width:70}},
  {key:'2',label:'身份证号码',value:{title:'身份证号码',dataIndex:'idcard',width:80}},
  {key:'3',label:'工作岗位',value:{title:'工作岗位',dataIndex:'d09_name',width:80}},
  {key:'4',label:'学历',value:{title:'学历',dataIndex:'d07_name',width:80}},
]
export default class index extends React.Component<any,any>{
  constructor(props) {
    super(props);
    this.state={
      visible:false,
      visible2:false,
      columns:[],
      checkData:[],
      pagination:{page:1,pageSize:10,total:0},
      progressType:'', //进度条类型
      checkExportLoading:false // 反查列表的导出
    }
  }
  handleCancel=()=>{
    this.setState({
      visible:false,
      visible2:false,
    })
  };
  handleOk=()=>{
    let columns=[];
    const {checkData}=this.state;
    const {tableType}=this.props;
    for(let obj of checkData){
      if(tableType=='ccp_mem'){
        let find = TextColumn.find(ob=>ob['key']==obj);
        if(find){
          columns.push(find['value'])
        }
      }
      if(tableType=='mem_develop'){
        let find = TableOrg.find(ob=>ob['key']==obj);
        if(find){
          columns.push(find['value'])
        }
      }
      if(tableType=='team'){
        let find = TableTeam.find(ob=>ob['key']==obj);
        if(find){
          columns.push(find['value'])
        }
      }
      if(tableType=='transfer'){
        let find = TableTransfer.find(ob=>ob['key']==obj);
        if(find){
          columns.push(find['value'])
        }
      }
    }
    this.setState({
      columns,
      visible2:false,
    })
  };
  handleCancel2=()=>{
    this.setState({
      visible2:false,
    })
  };
  showSel=()=>{
    this.setState({
      visible2:true
    })
  };
  exportRes=async()=>{
    const {params} = this.props;
    this.setState({checkExportLoading:true,progressType:'checkExport'});
    const {code = 500, data={}} = await exportVerification({...params,pageNum:1,pageSize:10});
    this.setState({checkExportLoading:false});
    if(code === 0){
      fileDownloadHeader(`/api${data}`,data.split('/')[2]);
      Modal.success({
        content: '导出成功',
      });
      // this['SpinProgress'].getProgress(key,'niandu');
    }
  };
  progressCallback=(res)=>{
    const {progressType} = this.state;
    switch (progressType) {
      case '1':
        this.setState({tbDownLoadLoading:false});
        fileDownloadHeader(`/api${res.url}`,res['url'].split('/')[2]);
        Modal.success({
          content: '导出成功',
        });
        break;
    }

  };
  static getDerivedStateFromProps(props,state){
    const {tableType}=props;
    const {_tableType}=state;
    let columns=[];
    if(tableType!=_tableType){
      if(tableType=='ccp_mem'){
        for(let obj of disMem){
          let find = TextColumn.find(ob=>ob['key']==obj);
          if(find){
            columns.push(find['value'])
          }
        }
        // for(let obj of TableMem){
        //   if(obj['value']){
        //     columns.push(obj['value'])
        //   }
        // }
        return {columns,checkData:disMem,_tableType:tableType}
      }
      if(tableType=='org'){
        for(let obj of disOrg){
          let find = TableOrg.find(ob=>ob['key']==obj);
          if(find){
            columns.push(find['value'])
          }
        }
        return {columns,checkData:disOrg,_tableType:tableType}
      }
      if (tableType == 'institution') {
        for(let obj of disInstitution){
          let find = TableInstitution.find(ob=>ob['key']==obj);
          if(find){
            columns.push(find['value'])
          }
        }
        return {columns,_tableType:tableType}
      }
      if (tableType == 'unit') {
        for(let obj of disUnit){
          let find = TableUnit.find(ob=>ob['key']==obj);
          if(find){
            columns.push(find['value'])
          }
        }
        return {columns,_tableType:tableType}
      }
      if(tableType=='team'){
        for(let obj of disTeam){
          let find = TableTeam.find(ob=>ob['key']==obj);
          if(find){
            columns.push(find['value'])
          }
        }
        return {columns,checkData:disTeam,_tableType:tableType}
      }
      if(tableType=='transfer'){
        for(let obj of disTransfer){
          let find = TableTransfer.find(ob=>ob['key']==obj);
          if(find){
            columns.push(find['value'])
          }
        }
        return {columns,checkData:disTransfer,_tableType:tableType}
      }
    }
    return null;
  }
  boxChange=(val)=>{
    this.setState({
      checkData:val,
    })
  };

  render(){
    const {visible,visible2,columns,checkData=[],checkExportLoading}=this.state;
    const {list=[],pagination,tableType}=this.props;
    return(
      <React.Fragment key={0}>
        <Modal
          maskClosable={false}
          title={<span>显示列设置 <span style={{color:'red',marginLeft:16,fontSize:14,lineHeight:'16px'}}>勾选列名后反查信息将会根据勾选项进行动态变化展示，便于统计涉及基本信息核实</span></span>}
          visible={visible2}
          onOk={this.handleOk}
          onCancel={this.handleCancel2}
          width={900}
        >
          <div className={styles.clu}>
            <Checkbox.Group value={checkData} onChange={this.boxChange}>
              {
                tableType=='mem' ? TableMem.map((obj,index)=>{
                  if(!obj['value']){
                    return(
                      <React.Fragment key={1}>
                        {index!=0 ? <WhiteSpace/> : null}
                        <Alert message={obj['label']} type="info" />
                        <WhiteSpace/>
                      </React.Fragment>
                    )
                  }
                  if(disMem.includes(obj['key'])){
                    return(
                      <Checkbox key={obj['key']} value={obj['key']} disabled checked>
                        {obj['label']}
                      </Checkbox>
                    )
                  }
                  return(
                    <Checkbox key={obj['key']} value={obj['key']}>{obj['label']}</Checkbox>
                  )
                }) : null
              }

              {
                tableType=='org' ? TableOrg.map((obj,index)=>{
                  if(disOrg.includes(obj['key'])){
                    return(
                      <Checkbox
                        key={obj['key']}
                        value={obj['key']}
                        style={index==0 ? {marginLeft:8} : undefined}
                        disabled
                        checked={true}
                      >
                        {obj['label']}
                      </Checkbox>
                    )
                  }
                  return(
                    <Checkbox key={obj['key']} value={obj['key']}>{obj['label']}</Checkbox>
                  )
                }) : null
              }

              {
                tableType=='team' ? TableTeam.map((obj,index)=>{
                  if(disTeam.includes(obj['key'])){
                    return(
                      <Checkbox
                        key={obj['key']}
                        value={obj['key']}
                        style={index==0 ? {marginLeft:8} : undefined}
                        disabled
                        checked={true}
                      >
                        {obj['label']}
                      </Checkbox>
                    )
                  }
                  return(
                    <Checkbox key={obj['key']} value={obj['key']}>{obj['label']}</Checkbox>
                  )
                }) : null
              }
            </Checkbox.Group>
          </div>
        </Modal>
        <Modal
          maskClosable={false}
          title={
            ' 反查结果'
            // <div>
             
            //   {tableType!=='institution'&&<Button type={'primary'} onClick={this.showSel} style={{marginRight:5}}>显示列设置</Button>}
            //   <Button type={'primary'} onClick={this.exportRes} loading={checkExportLoading}>导出</Button>
            // </div>
          }
          visible={visible}
          onOk={this.handleCancel}
          onCancel={this.handleCancel}
          width={1360}
          footer={null}
          bodyStyle={{minHeight:0}}
        >
          {/*<SpinProgress ref={e => this['SpinProgresses'] = e }*/}
                        {/*callback={(res) => this.progressCallback(res)}>*/}
            <div className={styles.list}>
              <ListTable scroll={{x:300}} columns={columns} data={list} pagination={pagination} onPageChange={this.props.pageChange}/>
            </div>
          {/*</SpinProgress>*/}
        </Modal>
      </React.Fragment>
    )
  }
}
// export default function Pegging(props,ref) {
//   console.log(props,ref,'sssssssssssssss')
//   const [visible,setVisible]=useState(false);
//   const [pagination,setPagination]=useState({page:1,pageSize:10,total:0});
//   const [columns,setColumns]=useState([]);
//   function handleOk() {
//     setVisible(false);
//   }
//   function handleCancel() {
//     setVisible(false);
//   }
//   return(
//     <React.Fragment>
//       <Modal
//         title="反查结果"
//         visible={visible}
//         onOk={handleCancel}
//         onCancel={handleCancel}
//         width={1300}
//       >
//         <ListTable columns={columns} data={[]}/>
//         <ListPagination pagination={pagination}/>
//       </Modal>
//     </React.Fragment>
//   )
// }
