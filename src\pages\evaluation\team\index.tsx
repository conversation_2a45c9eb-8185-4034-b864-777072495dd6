/**
 * 考核评价-班子建设
 */
import React from 'react';
import {connect} from "dva";
import ListTable from 'src/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import {
  Button,
  Divider,
  Input,
  Popconfirm,
  Tabs,
  Dropdown,
  Menu,
  Modal,
  Row,
  Tooltip,
} from 'antd';
import NowOrg from 'src/components/NowOrg';
import moment from 'moment';
import { isEmpty, setListHeight } from '@/utils/method';
import {getSession} from "@/utils/session";
import styles from './index.less';
const Search = Input.Search;
const TabPane = Tabs.TabPane;

@connect(({evaluation,loading})=>({evaluation,loading:loading.effects['unit/getList']}))
export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      tab:'1',
    };
  }
  addOrEdit=(record?:object)=>{

  };


  componentDidMount() {
    const org=getSession('org') || {};
    this.setState({orgCode:org['orgCode']});
    setListHeight(this);
    this.selectList(1 ,10,org['orgCode']);
  }

  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org=getSession('org') || {};
    if (!isEmpty(this.state['orgCode'])&&this.state['orgCode']!==org['orgCode']) {
      this.setState({
        orgCode:org['orgCode']
      },()=>{
        this.selectList(1,10,org['orgCode'])
      })
    }
  }

  selectList=( pageNum=1,size=10,code='')=>{
    this.props.dispatch({
      type:'evaluation/teamList',
      payload:{
        data:{
          pageNum:pageNum,
          pageSize:size,
          orgCode:code,
          endTime:this.state['years'],
        },
        tab:this.state['tab']
      }
    })
  };

  onPageChange=(page,pageSize)=>{
    this.selectList(page,pageSize,this.state.orgCode)
    // let {query}=this.props.location;
    // router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };

  confirm=(record)=>{
    this.props.dispatch({
      type:'evaluation/del',
      payload:{
        data:{
          code:record['code']
        }
      }
    }).then(res=>{
      if (res['code'] === 0) {
        this.selectList(1,10,this.state['orgCode'])
      }
    });
  };

  handleOk=()=>{
    this.setState({view:false})
  };
  handleCancel=()=>{
    this.setState({view:false})
  };
  changeTab=(e)=>{
    this.props.dispatch({
      type:'evaluation/remove',
      payload:{}
    }).then(res=>{
      this.setState({tab:e},()=>{
        this.selectList(1,10,this.state['orgCode'])
      })
    });
  };

  render() {
    const {loading,evaluation:{list=[],pagination:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={}}={}}=this.props;
    console.log(list,'list')
    console.log(totalRow,'totalRow')
    const {dataInfo,filterHeight,detailInfo={},tab}=this.state;
    let filterWidth=0;
    let data=[
      {
        memName:1,

      }
    ];
    let columns:any=[];
    let pagination={};
    const columns0=[
      {
        title:'序号',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'党统单位',
        dataIndex:'memOrgName',
        width:200,
      },
      {
        title:'党组织',
        dataIndex:'outflowOrgName',
        width:200,
      },
      {
        title:'学习时间及时长',
        dataIndex:'isProvOutName',
        width:100,
      },
      {
        title:'地点',
        dataIndex:'outflowDate',
        width:100,
      },
      {
        title:'学习内容',
        dataIndex:'outflowDate',
        width:200,
      },
      {
        title:'领学人',
        dataIndex:'outflowDate',
        width:100,
      },
      {
        title:'参会人数',
        dataIndex:'outflowDate',
        width:100
      },
      {
        title:'附学习照片',
        dataIndex:'outflowDate',
        width:100,
      },
      {
        title:'备注',
        dataIndex:'outflowDate',
        width:200,
      },
    ];
    const columns1=[
      {
        title:'序号',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'党统单位',
        dataIndex:'memOrgName',
        width:200,
      },
      {
        title:'党组织名称',
        dataIndex:'outflowOrgName',
        width:200,
      },
      {
        title:'时间',
        dataIndex:'isProvOutName',
        width:100,
      },
      {
        title:'地点',
        dataIndex:'outflowDate',
        width:200,
      },
      {
        title:'会议议题',
        dataIndex:'outflowDate',
        width:200,
      },
      {
        title:'会议类别',
        dataIndex:'outflowDate',
        width:100,
      },
      {
        title:'主持人',
        dataIndex:'outflowDate',
        width:100
      },
      {
        title:'记录人',
        dataIndex:'outflowDate',
        width:100,
      },
      {
        title:'参会人数',
        dataIndex:'outflowDate',
        width:200,
      },
      {
        title:'备注',
        dataIndex:'outflowDate',
        width:200,
      },
    ];
    const columns2=[
      {
        title:'序号',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'党统单位',
        dataIndex:'memOrgName',
        width:200,
      },
      {
        title:'单位名称',
        dataIndex:'outflowOrgName',
        width:200,
      },
      {
        title:'支部名称',
        dataIndex:'isProvOutName',
        width:200,
      },
      {
        title:'主题',
        width:1080,
        children: [
          {
            title: '一月',
            dataIndex: '1',
            width: 150,
          },
          {
            title: '二月',
            dataIndex: '2',
            width: 150,
          },
          {
            title: '三月',
            dataIndex: '3',
            width: 150,
          },
          {
            title: '四月',
            dataIndex: '4',
            width: 150,
          },
          {
            title: '五月',
            dataIndex: '5',
            width: 150,
          },
          {
            title: '六月',
            dataIndex: '6',
            width: 150,
          },
          {
            title: '七月',
            dataIndex: '7',
            width: 150,
          },
          {
            title: '八月',
            dataIndex: '8',
            width: 150,
          },
          {
            title: '九月',
            dataIndex: '9',
            width: 150,
          },
          {
            title: '十月',
            dataIndex: '10',
            width: 150,
          },
          {
            title: '十一月',
            dataIndex: '11',
            width: 150,
          },
          {
            title: '十二月',
            dataIndex: '12',
            width: 150,
          },

        ]
      },

    ];
    const columns3=[
      {
        title:'序号',
        width:50,
        fixed: 'left',
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'乡镇街道',
        dataIndex:'parentOrgName',
        width:300,
        render:text => {
          return <span>{isEmpty(text)?'--':text}</span>
        }
      },
      {
        title:'村（社区）',
        dataIndex:'orgName',
        width:300,
        render:text => {
          return <span>{isEmpty(text)?'--':text}</span>
        }
      },
      {
        title:'姓名',
        dataIndex:'memName',
        width:100,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'性别',
        dataIndex:'sexName',
        width:100,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'年龄',
        dataIndex:'age',
        width:100,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'政治面貌',
        dataIndex:'politicsCode',
        width:100,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'入党时间',
        dataIndex:'joinOrgDate',
        width:150,
              render:text => {
                return <span>{isEmpty(text)?'--':moment(text).format('YYYY-MM-DD')}</span>
              }
      },
      {
        title:'转正时间',
        dataIndex:'fullMemberDate',
        width:150,
              render:text => {
                return <span>{isEmpty(text)?'--':moment(text).format('YYYY-MM-DD')}</span>
              }
      },
      {
        title:'个人身份',
        dataIndex:'isFarmer',
        width:150,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'学历',
        dataIndex:'d07Name',
        width:100,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'职业水平',
        dataIndex:'d19Name',
        width:100,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'党内职务',
        dataIndex:'d22Name',
        width:100,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'行政职务',
        dataIndex:'d25Name',
        width:100,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'兼任职务',
        dataIndex:'d26Name',
        width:100,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'任职情况',
        dataIndex:'isIncumbent',
        width:100,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },
      {
        title:'身份证号',
        dataIndex:'idcard',
        width:200,
        render:text => {
          return(
            <span>
              {isEmpty(text)?'--':
                <Tooltip title={text}>
                  <span>{text.substring(0,8)+'...'}</span>
                </Tooltip>
              }
            </span>
          )
        }
      },
      {
        title:'手机号码',
        dataIndex:'phone',
        width:100,
        render:text => {
          return(
            <span>
              {isEmpty(text)?'--':
                text
              }
            </span>
          )
        }
      },
      {
        title:'备注',
        dataIndex:'remark',
        width:200,
              render:text => {
                return <span>{isEmpty(text)?'--':text}</span>
              }
      },

    ];
    const columns4=[
      {
        title:'序号',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'单位名称',
        dataIndex:'memOrgName',
        width:200,
      },
      {
        title:'党组织名称',
        dataIndex:'outflowOrgName',
        width:200,
      },
      {
        title:'姓名',
        dataIndex:'isProvOutName',
        width:200,
      },
      {
        title: '问题清单',
        dataIndex: '1',
        width: 150,
      },
      {
        title: '整改措施',
        dataIndex: '13',
        width: 150,
      },
      {
        title: '整改时限',
        dataIndex: '11',
        width: 150,
      },
      {
        title: '备注',
        dataIndex: '12',
        width: 150,
      }
    ];
    switch (tab) {
      case '1':
        columns=columns0;
        break;
      case '2':
        columns=columns1;
        break;
      case '3':
        columns=columns2;
        break;
      case '4':
        columns=columns3;
        if (isEmpty(list)) {
          filterWidth=0;
        }else {
          filterWidth=3000;
        }
        break;
      case '5':
        columns=columns4;
        break;
    }
    return (
      <div className={styles.showHead}>
        <Tabs defaultActiveKey={tab} onChange={(e)=>this.changeTab(e)} type="card">
          <TabPane tab="基层党组织" key="1"/>
          <TabPane tab="村（社区）书记、副书记" key="2"/>
          <TabPane tab="村（农村社区）党组织书记有关情况统计表" key="3"/>
          <TabPane tab="村（社区）“两委”委员基本情况统计表" key="4"/>
          <TabPane tab="村（社区）后备人才队伍台账" key="5"/>
          <TabPane tab="村（社区）后备干部台账" key="6"/>
          <TabPane tab="选聘本土人才台账" key="7"/>
          <TabPane tab="村社区干部联审台账" key="8"/>
          <TabPane tab="后进基层党组织台账" key="9"/>
          <TabPane tab="基层党组织换届情况统计表" key="10"/>
        </Tabs>
        {/*<OutWithdraw onChange={this.changeList} data={this.state['record']} filterHeight={filterHeight} wrappedComponentRef={(e)=>this['OutWithdraw']=e}/>*/}
        {/*<OutRegistrationDetail onChange={this.changeList} wrappedComponentRef={(e)=>this['RegistrationDetail']=e}/>*/}
        <NowOrg extra={
          <React.Fragment>
            <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={()=>this.addOrEdit()}>导出</Button>
            {/*<Search style={{width:200,marginLeft:16}} onSearch={this.search}/>*/}
          </React.Fragment>
        }/>
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable
          key={tab}
          scroll={{x:filterWidth,y:filterHeight}}
          
          columns={columns}
          data={list}
          pagination={{pageSize,total:totalRow,page,current:pageNumber}}
          onPageChange={this.onPageChange}/>
      </div>
    );
  }
}
