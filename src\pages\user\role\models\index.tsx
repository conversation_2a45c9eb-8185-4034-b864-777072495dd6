import modelExtend from "dva-model-extend";
import {listPageModel} from "@/utils/common-model.tsx";
import {add, del, update, getList, getById,upUserRole, getlv,findPermissionByRoleId,updateR<PERSON><PERSON>ermisson,getUserList} from "../service/index.js";
import {changeListPayQuery} from '@/utils/method.js'
import Notice from '@/components/Notice';
const user=modelExtend(listPageModel,{
  namespace:'role',
  state:{

  },
  subscriptions:{
    setup({ dispatch, history }) {
      history.listen(location => {
        // const { pathname, search } = location;
        // dispatch({
        //   type:'upUserRole',
        // })
      });
    }
  },
  effects:{
    *queryList({payload},{call,put}){
      const res = yield call(getList,payload);
      const {code = 500, message = '请求失败啊'} = res;
      if(code !== 0){
        // Notice("操作提示",message,"exclamation-circle-o","orange");
      }else {
        let finallRes = changeListPayQuery(res.data);
        yield put ({
          type:'querySuccess',
          payload:{
            ...finallRes
          }
        })
      }
    },
    // 获取角色权限
    *getLV({payload},{call,put}){
      const res = yield call(getlv,payload);
      const {data = []} = res;
      // const arr = [
      //   {key:'8284b8a5942c4366a9f52ae127878c4b',name:'权限1'},
      //   {key:'8284b8a5942c4366a9f52ae127878c4c',name:'权限2'},
      // ];
      yield put({
        type:'updateState',
        payload:{
          lvArr:data
        }
      })
    },
    // 新增角色
    *add({payload},{call,put}){
      return yield call(add,payload);
    },
    // 获取角色信息
    *getEditInfo({payload},{call,put}){
      const res = yield call(getById,{roleId:payload.id});
      const {code = 500, data = {}, message = '失败'} = res;
      let final = data;
      if(code !== 0){
        final = {};
      }
      yield put ({
        type:'updateState',
        payload:{
          editInfo:final
        }
      })
    },
    *edit({payload},{call,put}){
      return yield call(update,payload);
    },
    *del({payload},{call,put}){
      return yield call(del,payload);
    },
    *getUserRole({payload},{call,put}) {
      const res = yield call(findPermissionByRoleId, payload);
      let userRole:Array<string>=[],userRoleAll:Array<string>=[];
      if(res['data']){
        const {data}=res;
        for(let obj of data){
          const find=data.find(item=>item['parentId']==obj['id']);
          userRoleAll.push(`${obj['id']}`);
          if(!find &&obj['id']){
            userRole.push(`${obj['id']}`);
          }
        }
      }
      yield put({
        type:'updateState',
        payload:{
          userRole,//菜单选中节点 排除父级
          userRoleAll,//菜单选中节点 包含父级
        }
      })
    },
    *upUserRole({payload},{call,put}) {
      return yield call(updateRolePermisson, payload);
    },
    // 使用人列表
    *userList({payload},{call,put}){
      const res = yield call(getUserList,payload);
      return res;
    }
  }
});
export default user
