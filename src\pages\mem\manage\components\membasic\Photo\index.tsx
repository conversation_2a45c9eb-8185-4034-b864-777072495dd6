import React, { Fragment, useImperativeHandle, useState } from 'react';
import Date from '@/components/Date';
import { Col, Form, Input, Modal, Alert, Button, Popconfirm } from 'antd';
import { unixMoment } from '@/utils/method.js';
import DictTreeSelect from '@/components/DictTreeSelect';
import { joinPartyRevise } from '@/pages/mem/services';
import Tip from '@/components/Tip';
import moment from 'moment';
import { compressAccurately } from 'image-conversion'; // 压缩图片插件
import UploadComp from '@/components/UploadComp';
import { getImgUrl, editPhoto, pullFile } from '@/services';
import _isEmpty from 'lodash/isEmpty';
import st from './index.less';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const getBase64 = (file): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

const index = (props: any, ref) => {
  const { title = '标题', onOK } = props;
  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [file, setFile] = useState<any>();
  const [url, setUrl] = useState<any>();

  const handleCancel = () => {
    setRecord({});
    setVisible(false);
    form.resetFields();
  };
  const onOk = async (e) => {
    console.log(url, file, '111111111111111');
    if (_isEmpty(url) && _isEmpty(file)) {
      Tip.error('操作提示', '请上传头像');
      return;
    }

    if (!_isEmpty(file)) {
      setConfirmLoading(true);
      const res = await editPhoto({
        data: { memCode: record.code, photo: file?.url },
      });
      setConfirmLoading(false);
      if (res.code === 0) {
        Tip.success('操作提示', '操作成功');
        props?.onOK?.();
        handleCancel();
      }
    } else {
      handleCancel();
    }
  };

  const onUploadCompChange = async (list, file) => {
    const { response: { code = 500, data = [] } = {}, originFileObj = {} } = file;
    if (code == 0) {
      const f = data?.[0] || {};
      setFile(f);
      const base64 = await getBase64(originFileObj);
      setUrl(base64);
    }
  };

  const putFiles = async (path) => {
    if (path) {
      const res = await pullFile({ path });
      setUrl(res);
    } else {
      setUrl('');
    }
  };

  useImperativeHandle(ref, () => ({
    open: (query) => {
      setVisible(true);
      setFile({});
      putFiles(query.photo);
      setRecord(query);
      //   if (query) {
      //     const { lockFields = [] } = query;
      //     const arr = ['applyDate', 'activeDate', 'objectDate', 'joinOrgDate', 'fullMemberDate'];
      //     let newCanEdit = { ...canEdit };
      //     lockFields.map((item: any) => {
      //       if (arr.includes(item)) {
      //         newCanEdit[item] = true;
      //         setCanEdit({
      //           ...newCanEdit,
      //         });
      //       }
      //     });
      //     form.setFieldsValue({ ...query });
      //   }
    },
    clear: () => {
      // clear();
    },
  }));

  const beforeUpload = async (file) => {
    const { name = '', size = 0 } = file;
    // let hasChinese = /[\u4e00-\u9fa5]/g.test(name);
    let fileSize: number = file['size'] / 1024 / 1024;
    return new Promise(async (resolve, reject) => {
      // 图片超过1M都压缩到1M
      if (fileSize > 1) {
        compressAccurately(file, 1024 * 1).then((res) => {
          resolve(res);
        });
      } else {
        resolve(file);
      }
    });
  }

  return (
    <Modal
      title={'照片采集'}
      visible={visible}
      onOk={onOk}
      onCancel={handleCancel}
      width={600}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      {visible && (
        <Fragment>
          <Alert message="替换图片请点击图片直接上传。" type="info" showIcon />
          <div style={{ marginBottom: '10px' }} />

          <Form form={form} {...formItemLayout}>
            <Form.Item name="applyDate" label="上传照片">
              <UploadComp
                action={'/api/base/putFile'}
                accept=".jpg,.png,.jpeg"
                files={[]}
                maxLen={1}
                showUploadList={false}
                btnStyle={'text'}
                onChange={onUploadCompChange}
                buttonText={
                  <div className={st.photoBox}>
                    {url ? <img style={{ width: 150, height: 201 }} src={url} /> : '无照片'}
                  </div>
                }
                beforeUpload={beforeUpload}
              />
            </Form.Item>
          </Form>
        </Fragment>
      )}
    </Modal>
  );
};
export default React.forwardRef(index);
