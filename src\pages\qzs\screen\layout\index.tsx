import React, { useEffect, useState } from 'react';
import { MyContext } from '@/utils/global';
import { getSession } from '@/utils/session';
import autofit from 'autofit.js';
import { FullscreenOutlined, FullscreenExitOutlined, RedoOutlined } from '@ant-design/icons';
function refreshScale() {
  console.log('触发');
  let baseWidth = document.documentElement.clientWidth;
  let baseHeight = document.documentElement.clientHeight;
  // let appStyle = document.body.style;
  // let bodyStyle = document.body.style;
  let appStyle = document.getElementById('root').style;
  let realRatio = baseWidth / baseHeight;
  let designRatio = 16 / 9;
  let scaleRate = baseWidth / 1920;
  let hscale = baseHeight / 1080;
  // if (baseWidth < 1900 && realRatio > designRatio) {
  //   scaleRate = baseHeight / 1080;
  // }
  // console.log(realRatio, designRatio, realRatio > designRatio, baseWidth, baseHeight, baseWidth < 1920, "dddddddddddddd");
  appStyle.transformOrigin = 'left top';
  let scaleStr = `scaleX(${scaleRate}) scaleY(${hscale})`;
  if (process.env.NODE_ENV == 'development') {
    appStyle.transform = `scale(${scaleRate})`;
  }
  appStyle.transform = scaleStr;
  // bodyStyle.width = "100%";
  // bodyStyle.height = "100%";
  // appStyle.width = `${baseWidth / scaleRate}px`;

  const htmlStyle = document.getElementById('htmlStyle');
  htmlStyle && htmlStyle.parentNode.removeChild(htmlStyle);
  var style = document.createElement('style');
  style.id = 'htmlStyle';
  style.appendChild(
    document.createTextNode(
      `.ant-select-dropdown {transform: ${scaleStr}} .ant-picker-dropdown{transform: ${scaleStr}} .ant-modal-wrap{transform: ${scaleStr};transform-origin:left top;}`,
    ),
  );
  var head = document.getElementsByTagName('head')[0];
  head.appendChild(style);
}

const index = (props: any) => {
  const [isFull, setIsFull] = useState<any>(false);

  const goFull = () => {

    const isFullScreen = document.fullscreenElement
    const element = document.documentElement; // 获取整个文档的元素
    if (!isFullScreen) {
      if (element.requestFullscreen) {
        // 标准写法
        element.requestFullscreen();
        setIsFull(true);
      } else if (element.mozRequestFullScreen) {
        // Firefox 浏览器
        element.mozRequestFullScreen();
        setIsFull(true);
      } else if (element.webkitRequestFullscreen) {
        // Chrome 和 Safari
        element.webkitRequestFullscreen();
        setIsFull(true);
      } else if (element.msRequestFullscreen) {
        // IE11
        element.msRequestFullscreen();
        setIsFull(true);
      }
    } else {
      if (document.exitFullscreen) {
        // 标准写法
        document.exitFullscreen();
        setIsFull(false);
      } else if (document.mozCancelFullScreen) {
        // Firefox 浏览器
        document.mozCancelFullScreen();
        setIsFull(false);
      } else if (document.webkitExitFullscreen) {
        // Chrome 和 Safari
        document.webkitExitFullscreen();
        setIsFull(false);
      } else if (document.msExitFullscreen) {
        // IE11
        document.msExitFullscreen();
        setIsFull(false);
      }
    }
  };

  const refff = () => {
    location.reload();
  };

  // useEffect(() => {
  //   const autofitInstance: any = autofit;
  //   autofitInstance.init({
  //     designHeight: 1080,
  //     designWidth: 1920,
  //     renderDom: '#asd',
  //     resize: true,
  //   }); // 初始化 Autofit.js
  //   return () => {
  //     autofitInstance.off(); // 在组件卸载时销毁 Autofit.js 实例
  //   };
  // }, []);

  // window.onload = () => {
  //   refreshScale();
  // };
  // window.addEventListener(
  //   'pageshow',
  //   function (e) {
  //     if (e.persisted) {
  //       // 浏览器后退的时候重新计算
  //       refreshScale();
  //     }
  //   },
  //   false,
  // );
  return (
    <div
      id={'asd'}
      style={{
        minHeight: 280,
        background: '#fff',
        height: '100%',
        overflow: 'auto',
        fontSize: 20,
      }}
    >
      <div style={{ position: 'absolute', right: '10px', top: '10px', zIndex:999}}>
        <RedoOutlined style={{ marginRight: '10px' }} onClick={refff} />
        {isFull ? (
          <FullscreenExitOutlined onClick={goFull} />
        ) : (
          <FullscreenOutlined onClick={goFull} />
        )}
      </div>
      <MyContext.Provider value={getSession('org')}>{props.children}</MyContext.Provider>
    </div>
  );
};

export default index;
