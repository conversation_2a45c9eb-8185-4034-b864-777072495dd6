import React from 'react';
import ListTable from '@/components/ListTable'
import { Icon as LegacyIcon } from '@ant-design/compatible';
import {<PERSON><PERSON>, Divider, <PERSON>con<PERSON>rm, Tabs} from "antd";
import NowOrg from "@/components/NowOrg";
import Search from '@/components/Search';
import AddEdit from '../components/addEdit';
import Preview from '../components/preview';
import {connect} from "dva";
import moment from 'moment';
import Tip from '@/components/Tip';
import {_history as router} from "@/utils/method";
import qs from 'qs';
import RuiFilter from "@/components/RuiFilter";
import WhiteSpace from '@/components/WhiteSpace';
import {getSession} from "@/utils/session";
const TabPane = Tabs.TabPane;

// @ts-ignore
@connect(({workTrend,loading})=>({workTrend,loading:loading.effects['workTrend/getList']}))
export default class workTrend extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      title:'',
      editObj:undefined,
    }
  }
  addOrEdit=(item?:object)=>{
    if(item){
      this.setState({
        title:'编辑动态',
        editObj:item,
      })
    }else{
      this.setState({
        title:'新增动态',
        editObj:undefined,
      })
    }
    this['addEdit'].setState({
      visible:true
    })
  };
  confirm=async (item)=>{//删除
    const obj=await this.props.dispatch({
      type:'workTrend/delTrend',
      payload:{
        data:{
          code:item['code']
        }
      }
    });
    if(obj && obj['code']===0){
      Tip.success('操作提示','删除成功');
      this.refresh();
    }
  };
  cancel=async (item)=>{//撤销
    const obj=await this.props.dispatch({
      type:'workTrend/cancelTrend',
      payload:{
        data:{
          code:item['code']
        }
      }
    });
    if(obj && obj['code']===0){
      Tip.success('操作提示','撤销成功');
      this.refresh();
    }
  };
  push=async (item)=>{//发布
    const obj=await this.props.dispatch({
      type:'workTrend/pushTrend',
      payload:{
        data:{
          code:item['code']
        }
      }
    });
    if(obj && obj['code']===0){
      Tip.success('操作提示','发布成功');
      this.refresh();
    }
  };
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  refresh=()=>{
    const {query}=this.props.location;
    const {current,pageSize}=this.props.workTrend.pagination;
    router.push(`?${qs.stringify({...query,pageNum:current,pageSize})}`)
  };
  previews=(item)=>{
    this.setState({
      editObj:item,
    });
    this['preview'].setState({
      visible:true,
    })
  };
  portal=async (item)=>{
    const obj=await this.props.dispatch({
      type:'workTrend/portalTrend',
      payload:{
        data:{
          code:item['code']
        }
      }
    });
    if(obj && obj['code']===0){
      Tip.success('操作提示','推送成功');
      this.refresh();
    }
  };
  afreshTrend=async (item)=>{
    const obj=await this.props.dispatch({
      type:'workTrend/afreshTrend',
      payload:{
        data:{
          code:item['code']
        }
      }
    });
    if(obj && obj['code']===0){
      Tip.success('操作提示','重新发布成功');
      this.refresh();
    }
  };
  filterChange=(val)=>{
    this.props.dispatch({
      type:'workTrend/updateState',
      payload:{
        filter:val
      }
    });
    this.action();
  };
  search=(val)=>{
    this.props.dispatch({
      type:'workTrend/updateState',
      payload:{
        trendName:val
      }
    });
    this.action();
  };
  action=(pageNum=1,pageSize=10)=>{
    const org=getSession('org') || {};
    this.props.dispatch({
       type:'workTrend/getList',
       payload:{
         data:{
           pageNum,
           pageSize,
           orgOrgCode:org['orgCode'],
         }
       }
    })
  };
  componentWillUnmount(): void {
    this.props.dispatch({
      type:'workTrend/reSet',
    })
  }

  render(){
    const {title,editObj}=this.state;
    const {list,pagination}=this.props.workTrend;
    const {current,pageSize}=pagination;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:58,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'标题',
        dataIndex:'tittle',
        width:200,
      },
      {
        title:'发布人',
        dataIndex:'createAccount',
        width:100,
      },
      {
        title:'发布状态',
        dataIndex:'pushStatus',
        width:100,
        render:(text)=>{
          switch (text) {
            case 1:
              return '预发布';
            case 2:
              return '已发布';
            case 3:
              return '已撤销';
          }
        }
      },
      {
        title:'推送状态',
        dataIndex:'isPortal',
        width:100,
        render:(text)=>{
          switch (text) {
            case 0:
              return '未推送';
            case 1:
              return '审核中';
            case 2:
              return '已通过';
          }
        }
      },
      {
        title:'创建时间',
        dataIndex:'createTime',
        width:100,
        render:(text)=>{
          return moment(text).format('YYYY-MM-DD')
        }
      },
      {
        title:'审核人',
        dataIndex:'checkPerson',
        width:100,
      },
      {
        title:'操作',
        dataIndex:'action',
        width:130,
        render:(text,record)=>{
          switch (record['pushStatus']) {
            case 1:
              return(
                <span>
                  <a onClick={()=>this.addOrEdit(record)}>编辑</a>
                  <Divider type="vertical"/>
                  <a onClick={()=>this.push(record)}>发布</a>
                  <Divider type="vertical"/>
                  <Popconfirm title="确定要删除吗？" onConfirm={()=>this.confirm(record)}>
                   <a className={'del'}>删除</a>
                  </Popconfirm>
                </span>
              );
            case 2:
              return(
                <span>
                  <Popconfirm title="确定要撤销吗？" onConfirm={()=>this.cancel(record)}>
                    <a className={'del'}>撤销</a>
                  </Popconfirm>
                  <Divider type="vertical"/>
                  {
                    record['isPortal']===0 ? <a onClick={()=>this.portal(record)}>推送</a> : <a className={'eventNone'}>已推送</a>
                  }
                  <Divider type="vertical"/>
                  <a onClick={()=>this.previews(record)}>预览</a>
                </span>
              );
            case 3:
              return(
                <span>
                  <a onClick={()=>this.afreshTrend(record)}>重新发布</a>
                  <Divider type="vertical"/>
                  <a>预览</a>
                  <Divider type="vertical"/>
                  <Popconfirm title="确定要删除吗？" onConfirm={()=>this.confirm(record)}>
                   <a className={'del'}>删除</a>
                  </Popconfirm>
                </span>
              );
          }
        },
      },
    ];
    const filterData=[
      {
        key:'trendStatus',name:'发布状态',value:[{key:1,name:'预发布'},{key:2,name:'已发布'},{key:3,name:'已撤销'}],
      },
      {
        key:'checkTypeList',name:'推送状态',value:[{key:0,name:'未推送'},{key:2,name:'已推送'}],
      },
      {
        key:'typeList',name:'动态类型',value:[{key:1,name:'工作动态'}],
      },
    ];
    return (
      <div style={{height:'100%', overflow:'hidden'}}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        <NowOrg extra={
          <React.Fragment>
            <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={()=>this.addOrEdit()} style={{marginLeft:16}}>添加动态</Button>
            <Search onChange={this.search}/>
          </React.Fragment>
        }/>
        <RuiFilter data={filterData}
                   openCloseChange={()=>setListHeight(this,20)}
                   onChange={this.filterChange}/>
        <WhiteSpace/>
        <WhiteSpace/>
        <AddEdit title={title} editObj={editObj} wrappedComponentRef={e=>this['addEdit']=e} refresh={this.refresh}/>
        <Preview editObj={editObj} ref={e=>this['preview']=e}/>
        <ListTable columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
      </div>
    );
  }
}
