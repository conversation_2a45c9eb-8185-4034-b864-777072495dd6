import './index.less';
import React from 'react';
import { ConfigProvider, Input, Layout, Modal, Form, Spin } from 'antd';
import MenuData from 'src/common/menu';
import HMenu from './header';
import SMenu from './silder';
import Avater from './avater';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';
import { MyContext } from '@/utils/global';
import OrgTree from 'src/components/OrgTree';
import { connect } from 'dva';
import styles from './index.less';
import { history as router } from 'umi';
import Notice from 'src/components/Tip';
import ErrorBoundary from './ErrorBoundary';
import { getSession } from '@/utils/session';
import QueueAnim from 'rc-queue-anim';
import SplitterLayout from 'react-splitter-layout';
import { upPassword } from '../services';
import { EditOutlined, QuestionCircleOutlined, LoginOutlined } from '@ant-design/icons';
import style from '@/pages/login/index.less';
import SetLayouts from './setLayouts';
import 'intro.js/introjs.css';
import { Steps, Hints } from 'intro.js-react';
import { needToChangePassword } from '@/services/index';
import DataQuery from './dataQuery/index';
import NotFound from '@/pages/404.js';
import QZSlayout from 'src/pages/qzs/screen/layout/index.tsx';
import QZSlayout2 from 'src/pages/qzs/mobile/layout/index.tsx';
import { useRoutePreloader } from '@/hooks/useRoutePreloader';
moment.locale('zh-cn');

const { Header, Sider } = Layout;
const full = [
  '/login',
  '/desktop',
  '/404',
  '/500',
  '/forum',
  '/archivesAdministration/membersWorkProcedures',
];
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};
@connect(({ login, common, user }) => ({ login, common, user }))
class BasicLayout extends React.Component {
  formRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      header: undefined,
      visible: false,
      org: undefined,
    };
  }
  static getDerivedStateFromProps(props, state) {
    let _state = {};
    const { timeKey } = props.login;
    const { _timeKey, header } = state;
    if (_timeKey != timeKey) {
      _state['_timeKey'] = timeKey;
      _state['header'] = undefined;
    }
    const { authorization, assess } = sessionStorage;
    if (authorization && assess == undefined) {
      needToChangePassword({}).then((result) => {
        // 是否需要修改默认密码
        sessionStorage.setItem('assess', result.data);
      });
    }
    return _state;
  }

  componentDidMount() {
    const { query } = this.props.location;
    if (query['appcode'] && query['token']) {
      this.props.dispatch({
        type: 'login/login',
        payload: {
          data: {
            ...query,
          },
        },
      });
    }
    // for (let i in document.images) {
    //   document.images[i].ondragstart = function () {
    //     return false;
    //   };
    // }
  }
  headerChange = (val) => {
    this.setState({ header: val });
  };
  loadData = async (val, func, type) => {
    if (type == 'initTree') {
      return await this.props.dispatch({
        type: 'common/updateInitTree',
        payload: {
          data: {
            orgCodeList: val,
          },
        },
      });
    } else {
      return await this.props.dispatch({
        type: 'common/getTree',
        payload: {
          data: {
            orgCodeList: val,
            excludeOrgCodeList: [],
          },
        },
      });
    }
  };
  treeSearch = (val) => {
    this.props.dispatch({
      type: 'common/queryTree',
      payload: {
        name: val,
      },
    });
  };
  treeChange = (selectedKeys, e) => {
    console.log(selectedKeys, e);
    // const {dataRef}=e.node;
    // this.setState({
    //   org:dataRef
    // })
  };
  onChangePW = () => {
    this.setState({ visible: true });
  };
  validFunction = (rule, value, callback) => {
    console.log(rule, value, 'rulerulerule')
    if (value) {
      switch (rule.field) {
        case 'password':
          // if (value.length < 6 || value.length > 12) {
          //   return callback('密码长度应为6-12个字符');
          // } else if (/[\u4e00-\u9fa5]+/.test(value)) {
          //   return callback('密码不能包含汉字');
          // } else {
          //   this.setState({ newPassword: value });
          // }
          if (value.length < 6 || value.length > 12) {
            return callback('密码长度应为6-12个字符');
          } else if (!(/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/).test(value)) {
            return callback('格式有误')
          } else if ((/\s+/g).test(value)) {
            return callback('密码不能包含空格')
          } else if (/[\u4e00-\u9fa5]+/.test(value)) {
            return callback('密码不能包含汉字');
          } else {
              this.setState({ newPassword: value });
            }
          break;
        case 'isPassword':
          // if (value.length < 6 || value.length > 12) {
          //   return callback('密码长度应为6-12个字符');
          // } else if (/[\u4e00-\u9fa5]+/.test(value)) {
          //   return callback('密码不能包含汉字');
          // } else if (this.state.newPassword !== value) {
          //   return callback('两次密码不匹配');
          // }
          if (value.length < 6 || value.length > 12) {
            return callback('密码长度应为6-12个字符');
          } else if (!(/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/).test(value)) {
            return callback('格式有误')
          } else if ((/\s+/g).test(value)) {
            return callback('密码不能包含空格')
          } else if (/[\u4e00-\u9fa5]+/.test(value)) {
            return callback('密码不能包含汉字');
          } else if (this.state.newPassword !== value) {
            return callback('两次密码不匹配');
          }
          break;
        default:
          break;
      }
    }
    callback();
  };
  handleOk = async (values) => {
    const id = JSON.parse(sessionStorage.user)['id'];
    const { isPassword, ...val } = values;
    const res = await upPassword({
      data: {
        ...val,
        id,
      },
    });
    if (res.code === 0) {
      Notice.info('操作提示', '密码修改成功');
      sessionStorage.clear();
      this.props.dispatch({
        type: 'common/clear',
        payload: {},
      });
      this.props.dispatch({
        type: 'login/clear',
        payload: {},
      });
      this.handleCancel();
      window.location.replace('/login');
    } else {
      Notice.info('操作提示', '密码修改失败');
    }
  };
  handleCancel = () => {
    this.setState({
      visible: false,
    });
    this.formRef.current.resetFields();
  };
  userMenuOnclick = ({ key = '' } = {}) => {
    switch (key) {
      case '1': // 修改密码
        // Notice.info("操作提示",'功能正在升级中');
        this.onChangePW();
        break;
      case '2': // 操作说明
        Notice.info('操作提示', '功能正在升级中');
        break;
      case '3': // 安全退出
        sessionStorage.clear();
        window.location.replace('/login');
        Notice.success('操作提示', '安全退出成功');
        break;
      case '4': // 修改密码
        this['backToRef'].open();
        break;
      default:
        break;
    }
  };
  toDesktop = () => {
    this.setState({
      header: undefined,
    });
    router.push('/desktop');
  };
  onDragEnd = () => {
    const e = document.createEvent('Event');
    e.initEvent('resize', true, true);
    window.dispatchEvent(e);
  };
  render() {
    const { common, login } = this.props;
    const { pathname, query } = this.props.location;
    const { menuKey, menuType, token, assess, account } = sessionStorage;
    let { header, org } = this.state,
      lastData = login['menuData'] || [];
    // if(process.env.NODE_ENV !== 'production'){
    //   lastData=MenuData;
    // }else{
    lastData = lastData.filter((obj) => Number(menuType) === obj['type']);
    // }
    if (!token) {
      // 清镇市大屏
      if (pathname !== '/login' && !pathname.startsWith('/qzs')) {
        window.location.replace('/login');
        // router.push('/login')
      }
    } else {
      if (assess == 'true') {
        lastData = [];
        setTimeout(() => {
          this.onChangePW();
        }, 500);
      }
    }
    // 筛选条件增加把桌面的系统设置筛掉
    let menuItems = lastData.filter((obj) => obj['isRoot'] && obj.code != '33');

    const userMenuItems = [
      { key: '4', name: '数据查询', icon: <LoginOutlined /> },
      { key: '1', name: '修改密码', icon: <EditOutlined /> },
      // {key:'2',name:'操作说明',icon:<QuestionCircleOutlined />},
      { key: '3', name: '安全退出', icon: <LoginOutlined /> },
    ];
    let childMenu = [];
    if (!pathname.startsWith('/text')) {
      if (header) {
        childMenu = lastData.filter((obj) => obj['parent'] === header['key']);
      } else {
        childMenu = lastData.filter((obj) => obj['parent'] === menuKey);
      }
    }

    // if(!org){
    //   org=common['org']
    // }
    let rootOrg = {}; //组织树根节点
    const roles = getSession('roles');
    if (roles) {
      rootOrg = roles['managerOrgCode'];
    }
    if (query['appcode'] && query['token']) {
      return (
        <div className={styles.fake}>
          <Spin spinning={true} tip={'登录中，请稍后...'} />
        </div>
      );
    }

    if (full.includes(pathname)) {
      return <ConfigProvider locale={zh_CN}>{this.props.children}</ConfigProvider>;
    }
    // else if(pathname.toString().startsWith('/setting')){
    //   return <SetLayouts>{this.props.children}</SetLayouts>
    // }

    const steps = [
      {
        element: '.newFishInfo1',
        title: '指引提示',
        intro: <div>指引提示1</div>,
      },
      {
        element: '.newFishInfo2',
        title: '指引提示',
        intro: <div>指引提示2</div>,
      },
      {
        element: '.newFishInfo3',
        title: '指引提示',
        intro: <div>指引提示3</div>,
      },
      {
        element: '.newFishInfo4',
        title: '指引提示',
        intro: <div>指引提示4</div>,
      },
    ];

    if (
      (account != 'admin' && account != 'gys001')
      && pathname !== '/'
      && pathname !== '/login'
      && pathname !== '/desktop'
      && !pathname.startsWith('/text')
      && !pathname.startsWith('/qzs') // 清镇市大屏不校验
      && !pathname.startsWith('/qzs/mobile') // 清镇市手机端
      && !pathname.startsWith('/annualStatistics/checkResultPage')
    ) {
      let find = lastData.find(item => item['url'].startsWith(pathname))
      if (!find) {
        return <NotFound />;
      }
      // console.log(find,'fffffffffffffffff')
    }

    const NoSiderUrl = ['/annualStatistics/checkResultPage'];
    const NoTreeUrl = ['/systemSet'];
    // 清镇市大屏
    if (pathname.startsWith('/qzs/screen')) {
      return <QZSlayout {...this.props}></QZSlayout>
    }
    if (pathname.startsWith('/qzs/mobile')) {
      return <QZSlayout2 {...this.props}></QZSlayout2>
    }
    return (
      <ConfigProvider locale={zh_CN}>
        <ErrorBoundary>
          <Steps
            enabled={false}
            steps={steps}
            initialStep={0}
            onExit={(...e) => { }}
            options={{
              nextLabel: '下一步',
              prevLabel: '上一步',
              doneLabel: '结束',
              hidePrev: true,
              disableInteraction: true,
              exitOnEsc: false,
              scrollToElement: true,
              exitOnOverlayClick: false,
              overlayOpacity: 0.8,
              positionPrecedence: ['bottom', 'top', 'right', 'left'],
            }}
          />
          <QueueAnim
            duration={600}
            animConfig={[
              { opacity: [1, 0], translateX: [0, 800] },
              { opacity: [1, 0], translateX: [0, -800] },
            ]}
          >
            <div>
              <Layout>
                <Header className={`${styles.header} newFishInfo1`}>
                  {/*@ts-ignore*/}
                  <div className={styles.logoDiv} onClick={this.toDesktop}>
                    {/*<img src={require('@/assets/dh.png')}/>智慧云党建平台*/}
                    <img src={require('@/assets/smallLogo.png')} width={173}/>
                  </div>
                  <HMenu data={menuItems} allData={lastData} headerChange={this.headerChange} />
                  <Avater userMenuItems={userMenuItems} userMenuOnclick={this.userMenuOnclick} />
                </Header>
                <Layout className={styles.Layout}>
                  <SplitterLayout
                    secondaryInitialSize={230}
                    primaryIndex={1}
                    onDragEnd={this.onDragEnd}
                  >
                    {/*style={{ background: '#fff',width:'100%',height:'100%',borderRight:'1px solid #e8e8e8' }}*/}
                    {childMenu.length > 0 && !pathname.startsWith(NoSiderUrl) && (
                      <Sider id={'Sider'} width={'100%'}>
                        <SMenu data={childMenu} {...this.state} />
                        {!pathname.startsWith(NoTreeUrl) && (
                          <OrgTree
                            isShowFlowOrg={true}
                            listData={common['listTree']}
                            mapData={common['mapTree']}
                            filterData={common['filterData']}
                            loadData={this.loadData}
                            onSearch={this.treeSearch}
                            onChange={this.treeChange}
                            orgCodes={login['orgCodes'] || []}
                            deductHeight={childMenu.length * 48}
                            rootCode={rootOrg}
                          />
                        )}
                      </Sider>
                    )}
                    {/* {
                      pathname==='/annualStatistics'&&
                      <OrgTree
                      listData={common['listTree']}
                      mapData={common['mapTree']}
                      filterData={common['filterData']}
                      loadData={this.loadData}
                      onSearch={this.treeSearch}
                      onChange={this.treeChange}
                      deductHeight={childMenu.length*48}
                      rootCode={rootOrg}
                    />
                    } */}
                    <div
                      style={{
                        padding: '0 16px',
                        minHeight: 280,
                        background: '#fff',
                        height: '100%',
                        overflow: 'auto',
                      }}
                      className={'newFishInfo4'}
                    >
                      <MyContext.Provider value={getSession('org') || common['org']}>
                        {this.props.children}
                      </MyContext.Provider>
                    </div>
                  </SplitterLayout>
                </Layout>
                <Modal
                  title="修改密码"
                  visible={this.state.visible}
                  onOk={() => this.formRef.current.submit()}
                  onCancel={this.handleCancel}
                  width={400}
                  maskClosable={false}
                >
                  {this.state.visible && (
                    <Form {...formItemLayout} ref={this.formRef} onFinish={this.handleOk}>
                      <FormItem
                        label={'旧密码'}
                        name={'oldPassword'}
                        rules={[{ required: true, message: '请输入！' }]}
                      >
                        <Input.Password placeholder="请输入当前密码" />
                      </FormItem>
                      <FormItem
                        label={'新密码'}
                        name={'password'}
                        rules={[
                          { required: true, message: '请输入!' },
                          { validator: this.validFunction },
                        ]}
                      >
                        <Input.Password placeholder="请输入新密码" />
                      </FormItem>
                      <FormItem
                        label={'确认密码'}
                        name={'isPassword'}
                        rules={[
                          { required: true, message: '请输入!' },
                          { validator: this.validFunction },
                        ]}
                      >
                        <Input.Password placeholder="请确认新密码" />
                      </FormItem>
                    </Form>
                  )}
                </Modal>
                <DataQuery wrappedComponentRef={(e) => (this['backToRef'] = e)} />
              </Layout>
            </div>
          </QueueAnim>
        </ErrorBoundary>
      </ConfigProvider>
    );
  }
}

// export default withRouter(Form.create()(BasicLayout));
export default BasicLayout;
