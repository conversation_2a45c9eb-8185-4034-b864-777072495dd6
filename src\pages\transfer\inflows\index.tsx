/**
 * 关系转入
 */
import React from 'react';
import RuiFilter from '@/components/RuiFilter';
import ListTable from '@/components/ListTable';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Radio, Tabs, Modal } from "antd";
import NowOrg from "@/components/NowOrg";
import WhiteSpace from '@/components/WhiteSpace';
import TransferIn from './components/transferIn';
import Transfer from './components/transfer';
import moment from 'moment';
import { connect } from "dva";
import Details from "@/pages/transfer/outflows/components/details";
import { getSession } from "@/utils/session";
import Tip from "@/components/Tip";
import qs from 'qs';
import { _history as router } from "@/utils/method";
import { setListHeight, isFlowingParty } from "@/utils/method";
import ExportInfo from '@/components/Export';
import { ButtonDisabled } from '@/common/config.js'
import { Letter } from '@/pages/transfer/outflows'
import FindLetter from './components/findLetter';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import TransVideo from '@/pages/transfer/outflows/components/transVideo';
import { findInByPage, cancelHint } from "../services/index"

const TabPane = Tabs.TabPane;
const Search = Input.Search;

@connect(({ transferIn, loading, commonDict }) => ({ transferIn, loading: loading.effects['transferIn/findInByPage'], commonDict: commonDict['dict_d59_tree'] }))
export default class extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      transferId: undefined,
      showWeekDataVisible: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination4: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      weekType: 3,
      weekData: [],
      weekData4: [],
      modalListLoading: false,
      countDown: 10,
      isOpen: true,
      selectedRowKeys:[]
    }
  }
  confirm = async (item) => {
    // console.log(item,'iririri');
    const obj = await this.props.dispatch({
      type: 'transferOut/undo',
      payload: {
        data: {
          id: item['id'],
          reason: '撤销'
        }
      }
    });
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', '撤销成功');
      this.refresh();
    }
  };
  filterChange = (val) => {
    this.props.dispatch({
      type: 'transferIn/updateState',
      payload: {
        filter: val
      }
    });
    this.refresh();
  };
  search = (val) => {
    this.props.dispatch({
      type: 'transferIn/updateState',
      payload: {
        keyWord: val
      }
    });
    this.refresh({ pageNum: 1 });
  };
  searchClear = (e) => {
    if (!e.target.value) {
      this.props.dispatch({
        type: 'transferIn/updateState',
        payload: { keyWord: undefined }
      });
      this.refresh();
    }
  };
  addOrEdit = () => {//关系转接

    Modal.confirm({
      title: '提示',
      icon: <ExclamationCircleOutlined style={{ color: '#1890ff' }} />,
      content: '本入口仅用于转出党组织尚未接入全国交换区的党员以及党政机关县处级及以上的党员领导干部组织关系转入。如某党员转出党组织已接入全国交换区，请联系转出党组织通过对方党员系统转出。转出党组织是否接入全国交换区，请与转出党组织联系确认。',
      okText: '确定',
      cancelText: '取消',
      // onCancel:()=>{ Modal.destroyAll()},
      onOk: () => {
        let org = getSession('org') || {}
        this.props.dispatch({
          type: 'memBasic/updateState',
          payload: {
            basicInfo: {
              d01Code: org['d01Code'],
              orgName: org['name'],
              orgCode: org['code'],
              orgZbCode: org['zbCode'],
              memOrgCode: org['orgCode'],
            }
          }
        })

        this['TransferIn'].open();
      },
    });


  };
  add = () => {//关系转接整建制
    this['Transfer'].open();
  };
  refresh = (params?: any) => {//刷新列表
    const { inPagination = {} } = this.props.transferIn;
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'transferIn/findInByPage',
      payload: {
        isHistory: false,
        orgId: org['code'],
        pageNum: inPagination['current'] || 1,
        pageSize: inPagination['pageSize'] || 10,
        ...params,
      }
    });
  };
  onPageChange = (page, pageSize) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`)
  };
  onPageChange1 = (page, pageSize) => {
    console.log('weekType====', this.state.weekType);
    if(this.state.weekType == 3){
      this.setState({
        pagination: {
          current: page,
          pageSize: pageSize,
          total: this.state.pagination.total
        }
      },()=>{
        this.getWeekData()
      })
    }
    if(this.state.weekType == 1){
      this.setState({
        pagination1: {
          current: page,
          pageSize: pageSize,
          total: this.state.pagination1.total
        }
      },()=>{
        this.getMessageList(1)
      })
    }
    if(this.state.weekType == 2){
      this.setState({
        pagination2: {
          current: page,
          pageSize: pageSize,
          total: this.state.pagination2.total
        }
      },()=>{
        this.getMessageList(2)
      })
    }
    if(this.state.weekType == 4){
      this.setState({
        pagination4: {
          current: page,
          pageSize: pageSize,
          total: this.state.pagination4.total
        }
      },()=>{
        this.getMessageList(4)
      })
    }
  };
  checkData = () => {
    //组织关系转入剩余7天, ，4-超期自动退回。请求接口有数据的就弹出提示框，倒计时结束点确定之后下次就不再弹出
    Promise.all([this.getWeekData(), this.getMessageList(4)]).then(()=>{
      const { weekData, weekData4 } = this.state
      // 有数据才弹出提示
      if(weekData.length > 0 || weekData4.length > 0){
        // 初始选择项
        if(weekData.length > 0){
          this.setState({
            weekType: 3
          })
        }else if(weekData4.length > 0){
          this.setState({
            weekType: 4
          })
        }
        this.setState({
          showWeekDataVisible: true,
        })
      }else{
        this.setState({
          showWeekDataVisible: false,
        })
      }
    })
  }
  componentDidMount(): void {
    setListHeight(this)
    this.checkData()
  }

  componentWillUnmount(): void {
    this.props.dispatch({
      type: 'transferIn/destroy',
    });
  }
  exportInfo = async () => {
    this.setState({
      flowBackDownload: true,
    })
    await this['flowBack'].submitNoModal();
    this.setState({
      flowBackDownload: false,
    })
  };


  //组织关系转入剩余7天列表 请求方法
  getWeekData = async () => {
    const { pagination } = this.state
    const org = getSession('org') || {};
    await this.setState({ modalListLoading: true })
    const { data, code } = await findInByPage({
      data: {
        isHistory: false,
        orgId: org['code'],
        pageNum: pagination['current'] || 1,
        // pageNum: 1,
        pageSize: pagination['pageSize'] || 10,
        // pageSize: 9999,
        // isFlowstatus: "1",
        subordinate: 1,
        isReminder: '1',
        reminderDayInt: 7,
      }
    })
    if (code == 0) {
      this.setState({
        weekData: data?.list,
        pagination: {
          current: data['pageNumber'],
          // pageSize: data['pageSize'],
          pageSize: 10,
          total: data['totalRow']
        }
      })
    }
    this.setState({ modalListLoading: false }, () => {
      if (this.state.isOpen) {
        this.openTimer()
        this.setState({
          isOpen: false
        })
      }
    })
  }
    // 提示弹窗： 省外上传交换区失败的、主动撤销的、超期自动退回
    getMessageList = async(type) => {
      const { pagination } = this.state
      const currentPagination = this.state[`pagination${type}`];
      const org = getSession('org') || {};
      await this.setState({ modalListLoading: true })
      const { data, code } = await findInByPage({
        data: {
          isHistory: true,
          orgId: org['code'],
          pageNum: currentPagination['current'] || 1,
          // pageNum: 1,
          pageSize: currentPagination['pageSize'] || 10,
          // pageSize: 9999,
          subordinate: 1,
          hintTypes: [type], // 提醒记录类型：1-上传交换区失败的， 2-主动撤销的，4-超期自动退回
          isFlowStatus: "1"
        }
      })
      this.setState({
        modalListLoading: false,
      })
      if (code == 0) {
        if(type == 4){
          this.setState({
            weekData4: data?.list,
            pagination4: {
              current: data['pageNumber'],
              // pageSize: data['pageSize'],
              pageSize: 10,
              total: data['totalRow']
            }
          })
        }
      }
    }
    // 取消提示
    cancelMessage = async () => {
      const type = this.state.weekType
      let ids = []
      // if(type==3){
      //   ids= this.state.selectedRowKeys
      // }
      // if(type==4){
      //   ids= this.state.weekData4.map(item => item.id)
      // }
      ids= this.state.selectedRowKeys
      console.log('cancelMessage====',ids);
      const { code } = await cancelHint({ data:{
        transferIdList: ids,
        hintType: type, // 提醒记录类型：1-上传交换区失败的， 2-主动撤销的，4-超期自动退回
      }})
      if (code == 0) {
       this.setState({
        selectedRowKeys: []
       })
      }
      this.checkData()
    }

  // 开启确定按钮倒计时
  openTimer = () => {
    const timer = setInterval(() => {
      const { countDown } = this.state
      if (countDown > 0) {
        this.setState({
          countDown: countDown - 1
        })
      } else {
        clearInterval(timer)
      }
    }, 1000)
  }

  handleOk = () => {
    // this.cancelMessage()
    if(this.state.weekType == 3){
      this.handleCancel();
    }else{
      this.cancelMessage()
    }
  };
  handleCancel = () => {
    this.setState({ showWeekDataVisible: false })
  };
  onSelectChange = (selectedRowKeys, infos) => {
    this.setState({ selectedRowKeys })
  }
  render() {
    const { loading } = this.props;
    const { inList = [], inPagination = false } = this.props.transferIn;
    const { weekType, pagination, weekData, pagination4, weekData4, modalListLoading, countDown,selectedRowKeys } = this.state
    const { current, pageSize } = inPagination;
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 50,
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1
        }
      },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 120,
      },
      {
        title: '申请日期',
        dataIndex: 'createTime',
        width: 100,
        render: (text) => {
          return moment(text).format('YYYY-MM-DD')
        }
      },
      {
        title: '源组织',
        dataIndex: 'srcOrgName',
        width: 200,
      },
      {
        title: '目的组织',
        dataIndex: 'targetOrgName',
        width: 200,
      },
      {
        title: '转接类型',
        dataIndex: 'typeName',
        width: 100,
      },
      {
        title: '转接状态',
        dataIndex: 'status',
        width: 80,
        render: (text) => {
          switch (text) {
            case 0:
              return '转接中';
            case 1:
              return '已完成';
            case 2:
              return '已撤销';
            case 4:
              return '超期自动退回';
            default:
          }
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 140,
        render: (text, record, index) => {
          return (
            <span>
              <a
                onClick={() => {
                  this.props.dispatch({
                    type: 'transferOut/inDetail',
                    payload: {
                      transferId: record['id']
                    }
                  }).then(res => {
                    this.setState({
                      transferId: record['id'],
                      memCode: record['memId'],
                    }, () => {
                      this['Details'].open(record)
                    })
                  });
                }}
              >
                {`${record['isCheck']}` == '1' ? <span style={{ color: 'red' }}>操作</span> : '查看'}
              </a>
              {/* <Divider type="vertical"/>
              <Popconfirm title="确定要删除吗？" onConfirm={()=>this.confirm(record)}>
               <a className={'del'}>撤销</a>
              </Popconfirm> */}
              <Divider type="vertical" />
              <Letter {...this.props} record={record} isOut={'0'} />
            </span>
          )
        }
      },
    ];


    const weekColumns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 50,
        render: (text, record, index) => {
          return (pagination.current - 1) * pagination.pageSize + index + 1
        }
      },
      ...columns.slice(1, columns.length - 1)
      // ...columns.slice(1)
    ]
    const filterData = [
      {
        key: 'types', name: '转接类型', value: this.props.commonDict,
      },
    ];
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
    };
    const org = getSession('org') || { d01Code: '' };
    const { d01Code = '' } = org || { d01Code: '' };
    // 权限列表有95	才显示 省外（含系统外，如军队、银行等单位）转入 按钮
    const pidArr: any = getSession('pid') || [];

    const extraNodes = (
      <React.Fragment>
        <TransVideo></TransVideo>
        {
          isFlowingParty() && <Button type={'primary'} style={{ marginLeft: 16 }} onClick={() => {
            this['findLetter'].open()
          }}>介绍信唯一码获取关系转接</Button>
        }

        <Button onClick={this.exportInfo} loading={this.state.flowBackDownload} style={{ marginLeft: 16 }}>导出</Button>
        {/*
          新增 与发展对象》接受预备党员逻辑一致----关系转接省外转入这个按钮，跟预备党员权限挂钩
          */}
        {/* 关系转接--关系转入，省外（含系统外...） */}
        {
          (d01Code === '631' || d01Code === '632' || d01Code === '634' || d01Code === '931' || d01Code === '932') && !ButtonDisabled.statistics2021 && pidArr.includes(94) &&
          <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.addOrEdit} style={{ marginLeft: 16 }}>省外（含系统外，如军队、银行等单位）转入</Button>
        }
        {/* <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.add} style={{marginLeft:16}}>省外关系转入（整建制）</Button> */}
        <Search style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'} />
      </React.Fragment>
    )

    return (
      <div>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1" />
        </Tabs>
        {/*转接详情*/}
        <Details refresh={this.refresh} transferId={this.state.transferId} memCode={this.state.memCode} type={'in'} wrappedComponentRef={e => this['Details'] = e} />
        {/*省外转入*/}
        <TransferIn wrappedComponentRef={e => this['TransferIn'] = e} refresh={this.refresh} />
        {/*省外转入（整建制）*/}
        <Transfer wrappedComponentRef={e => this['Transfer'] = e} refresh={this.refresh} />
        {/* 介绍信唯一码获取关系转接 */}
        <FindLetter ref={e => this['findLetter'] = e} refresh={this.refresh} />
        <NowOrg />
        <div style={{ marginBottom: 10, textAlign: 'right' }}>
          {extraNodes}
        </div>
        <RuiFilter data={filterData} onChange={this.filterChange} />
        <WhiteSpace />
        <WhiteSpace />
        <ListTable scroll={{ y: this.state.filterHeight }}
         
          columns={columns} data={inList} pagination={inPagination} onPageChange={this.onPageChange} />
        <ExportInfo wrappedComponentRef={e => this['flowBack'] = e}
          tableName={''}
          noModal={true}
          tableListQuery={{ isHistory: false, orgId: org['code'], ...this.props.transferIn.filter, keyWord: this.props.transferIn.keyWord }}
          action={'/api/transfer/exportInt'}
        />
        {/* 展示组织关系转入剩余7天的数据 */}
        <Modal
          closable={countDown > 0 ? false : true}
          keyboard={countDown > 0 ? false : true}
          destroyOnClose
          maskClosable={false}
          title={<React.Fragment>
              <Radio.Group onChange={(e)=>{
                const type = e.target.value
                this.setState({
                  weekType:type,
                  pagination: {
                    current: 1,
                    pageSize: 10,
                    total: 0,
                  },
                  pagination1: {
                    current: 1,
                    pageSize: 10,
                    total: 0,
                  },
                  pagination2: {
                    current: 1,
                    pageSize: 10,
                    total: 0,
                  },
                  pagination4: {
                    current: 1,
                    pageSize: 10,
                    total: 0,
                  },
                  selectedRowKeys: [],
                },()=>{
                  // this.refresh({ pageNum: 1 })
                })
                if(type === 3){
                  this.getWeekData()
                }else{ 
                  this.getMessageList(type)
                }

              }} value={weekType}>
              { weekData.length > 0 && <Radio.Button value={3}>组织关系转入剩余7天</Radio.Button>}
              { weekData4.length > 0 &&<Radio.Button value={4}>超期自动退回</Radio.Button>}
            </Radio.Group>
          </React.Fragment> }
          visible={this.state.showWeekDataVisible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1400}
          footer={
            <>
              {/* <Button size={'middle'} style={{ width: '70px' }} type="primary" disabled={countDown > 0} onClick={this.handleCancel}>取消{countDown > 0 ? countDown : ''}</Button> */}
              <Button size={'middle'} style={{ width: '70px' }} type="primary" disabled={countDown > 0} onClick={this.handleOk}>确定{countDown > 0 ? countDown : ''}</Button>
            </>
          }
        >
          <div>
            {weekType == 3 && <ListTable scroll={{ y: this.state.filterHeight }} columns={weekColumns} data={weekData} pagination={pagination} onPageChange={this.onPageChange1} />}
            {weekType == 4 && <ListTable rowKey={record => record.id} rowSelection={rowSelection} scroll={{ y: this.state.filterHeight }} columns={weekColumns} data={weekData4} pagination={pagination4} onPageChange={this.onPageChange1} />}
          </div>
        </Modal>
      </div>
    );
  }
}
