import React, { Fragment, useState, useImperativeHandle } from 'react';
import { Form, Button, Modal, message, InputNumber, Table, Popconfirm, Select, Space } from 'antd';
import moment from 'moment';
import {
  findByCode,
  saveSlackRectificationList,
  slackGetList,
} from '../../services/lax.js';
import DictSelect from '@/components/DictSelect';

const typeData = [
  {
    code: '1',
    name: '调整培训党组织书记',
    children: [
      {
        code: '11',
        name: '年初缺配的软弱涣散基层党组织书记数（人）',
      },
      {
        code: '12',
        name: '年初需调整的软弱涣散基层党组织书记数（人）',
      },
      {
        code: '13',
        name: '培训软弱涣散基层党组织书记（人）',
      },
    ]
  },
  {
    code: '2',
    name: '整顿措施',
    children: [
      {
        code: '21',
        name: '联村的县级领导班子成员（人）',
      },
      {
        code: '22',
        name: '包村的县级领导班子成员（人）',
      },
      {
        code: '23',
        name: '选派第一书记（人）',
      },
      {
        code: '24',
        name: '结对帮扶的县级及以上机关单位（人）',
      },
      {
        code: '25',
        name: '省市两级挂牌督办的村（个）',
      },
    ]
  },
  {
    code: '3',
    name: '解决问题',
    children: [
      {
        code: '11',
        name: '开展专项整治（项）',
      },
      {
        code: '12',
        name: '解决各类问题（个）',
      },
      {
        code: '13',
        name: '查处违纪违法行为（例）',
      },
    ]
  },
]
const NewAdd = (props, ref) => {
  const { onOk } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);
  const [list, setList]: any = useState([]);
  useImperativeHandle(ref, () => ({
    open: (val = {}) => {
      form.resetFields();
      setVisible(true);
      val['code'] && searchInfo(val['code']);
    },
  }));
  const handleCancel = () => {
    setVisible(false);
    setQuery([]);
    setList([])
  };
  // 根据code查信息
  const searchInfo = async (codeId) => {
    findByCode({ code: codeId || '' }).then(res => {
      const { data = {}, code = 500 } = res;
      if (code === 0) {
        let _val = {
          ...data,
          neatenTime: data.neatenTime ? moment(data.neatenTime) : undefined,
          d74Code: data.d74Code ? [...data.d74Code.split(',')] : [],
        };
        setQuery(_val);
      }
    });
    slackGetList({ code: codeId || '' }).then(res => {
      if (res['code'] == 0) {
        let forms: any = {};
        res['data'].map((it, index) => {
          for (let key in it) {
            forms[index + '_' + key] = it[key]
          }
        })
        // 为了使用form的error提示，table的colmus使用formItem, 但取值存值方式还是以前的没变
        form.setFieldsValue({ ...forms });
        setList(res['data']);
      }
    });
  };
  const onFinish = async (e) => {
    let url = saveSlackRectificationList;
    setLoading(true);
    let data: any = [];
    list.forEach(obj => {
      data.push({
        id: query['id'],
        zbCode: query['zbCode'],
        orgCode: query['orgCode'],
        slackOrgCode: query['slackOrgCode'],
        code: query['code'],
        ...obj
      })
    })
    const { code = 500 } = await url({
      data: {
        code: query['code'],
        list: data
      },
    });
    if (code === 0) {
      message.success('操作成功');
      handleCancel();
      onOk && onOk();
    }
  };
  const valChange = (val, key, item) => {
    item[key] = val;
    let data: any = [];
    list.forEach(obj => {
      if (obj['id'] == item['id']) {
        if (key == 'd90Code') {
          data.push({ id: item['id'], 'd90Code': item['d90Code'] });
        } else {
          data.push(item);
        }
      } else {
        data.push(obj);
      }
    })
    setList([...data])
  }
  const add = () => {
    list.push({
      id: new Date().valueOf(),
    })
    setList([...list])
  };
  const del = (item) => {
    let data = list.filter(obj => obj['id'] != item['id']);
    setList([...data])
  };
  const columns = [
    {
      title: '整顿形式',
      dataIndex: 'd90Code',
      width: 195,
      render: (text, record, index) => {
        return (
          <Form.Item
            name={`${index}_d90Code`}
            rules={[{ required: true, message: '整顿形式' }]}
          >
            <DictSelect codeType={'dict_d90'}
              initValue={text || undefined}
              onChange={value => valChange(value, 'd90Code', record)}
              showConstant={false} />
          </Form.Item>
        )
      }
    },
    {
      title: '形式详情',
      dataIndex: 'd91Code',
      width: 375,
      render: (text, record, index) => {
        return (
          <Form.Item
            name={`${index}_d91Code`}
            rules={[{ required: true, message: '形式详情' }]}
          >
            <DictSelect
              codeType={'dict_d91'}
              initValue={text || undefined}
              onChange={value => {
                valChange(value, 'd91Code', record);
                form.setFieldsValue({ [`${index}_currentYearAdjusted`]: undefined });
                form.setFieldsValue({ [`${index}_currentYearSelected`]: undefined });
              }}
              filter={(data) => {
                return data.filter(obj => obj['key'].toString().startsWith(record['d90Code']));
              }}
              showConstant={false}
            />
          </Form.Item>

        )
      }
    },
    {
      title: '数量',
      dataIndex: 'type3',
      render: (text, record, index) => {
        // 年初缺配的软弱涣散基层党组织书记数（人）
        if (record['d91Code'] == '11') {
          let label = '缺配';
          let label2 = '选配';
          return (
            <Fragment>
              <div style={{ display: 'flex' }}>
                <Form.Item
                  name={`${index}_currentYearSelected`}
                  label={label}
                  rules={[{ required: true, message: '形式详情' }]}
                >
                  <InputNumber
                    max={1}
                    // defaultValue={record['currentYearSelected']}
                    onChange={e => valChange(e, 'currentYearSelected', record)}
                  />
                </Form.Item>
                <Form.Item
                  name={`${index}_currentYearAdjusted`}
                  label={label2}
                  rules={[{ required: true, message: '形式详情' }]}
                >
                  <InputNumber
                    // defaultValue={record['currentYearAdjusted']}
                    onChange={e => valChange(e, 'currentYearAdjusted', record)}
                  />
                </Form.Item>
              </div>
            </Fragment>
          )
        } else if (record['d91Code'] == '12') {
          let label = '应调整';
          let label2 = '已调整';
          return (
            <Fragment>
              <div style={{ display: 'flex' }}>
                <Form.Item
                  name={`${index}_currentYearSelected`}
                  label={label}
                  rules={[{ required: true, message: '必填' }]}
                >
                  <Select style={{ width: '100px' }} onChange={(e) => {
                    form.setFieldsValue({ [`${index}_currentYearAdjusted`]: undefined })
                    valChange(e, 'currentYearSelected', record)
                  }}>
                    <Select.Option value={1}>1</Select.Option>
                    <Select.Option value={0}>0</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) => prevValues[`${index}_currentYearSelected`] !== currentValues[`${index}_currentYearSelected`]}
                >
                  {({ getFieldValue }) => {
                    return (
                      <Form.Item
                        name={`${index}_currentYearAdjusted`}
                        label={label2}
                        rules={[{ required: true, message: '必填' }]}
                      >
                        <Select style={{ width: '100px' }} onChange={(e) => { valChange(e, 'currentYearAdjusted', record) }}>
                          {
                            getFieldValue(`${index}_currentYearSelected`) == 1 && <Select.Option value={1}>1</Select.Option>
                          }
                          <Select.Option value={0}>0</Select.Option>
                        </Select>
                      </Form.Item>
                    )
                  }}
                </Form.Item>
              </div>
            </Fragment>
          )
        } else if (record['d91Code'] == '25') {
          return (
            <Form.Item
              name={`${index}_currentYearSelected`}
              rules={[{ required: true, message: '必填' }]}
            >
              <Select style={{ width: '100%' }} onChange={(e) => { valChange(e, 'currentYearSelected', record) }}>
                <Select.Option value={1}>1</Select.Option>
                <Select.Option value={0}>0</Select.Option>
              </Select>
            </Form.Item>
          )
        } else {
          return (
            <Form.Item
              name={`${index}_currentYearSelected`}
              rules={[{ required: true, message: '必填' }]}
            >
              <InputNumber
                // defaultValue={record['currentYearSelected']}
                style={{ width: '100%' }}
                onChange={e => valChange(e, 'currentYearSelected', record)}
              />
            </Form.Item>
          )
        }
        if (record['d91Code'] == '11' || record['d91Code'] == '12') {
          let label = '缺配';
          let label2 = '选配';
          if (record['d91Code'] == '12') {
            label = '应调整';
            label2 = '已调整';
          }
          return (
            <Space>
              <Space>
                {label}
                <InputNumber max={label == '缺配' ? 1 : 9999999} defaultValue={record['currentYearSelected']} onChange={e => valChange(e, 'currentYearSelected', record)} />
              </Space>
              <Space>
                {label2}
                <InputNumber defaultValue={record['currentYearAdjusted']} onChange={e => valChange(e, 'currentYearAdjusted', record)} />
              </Space>
            </Space>
          )
        }
        return (
          <InputNumber defaultValue={record['currentYearSelected']} style={{ width: '100%' }} onChange={e => valChange(e, 'currentYearSelected', record)} />
        )
      }
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return (
          <Popconfirm title={'是否确认删除'} onConfirm={() => del(record)}>
            <a className={'del'}>删除</a>
          </Popconfirm>
        )
      }
    },
  ]
  return (
    <Fragment>
      <Modal
        closable={true}
        width={1100}
        title={'编辑'}
        visible={visible}
        footer={null}
        onCancel={handleCancel}
        destroyOnClose={true}
      >
        <Form form={form} onFinish={onFinish}>
          {visible && (
            <Table
              key={new Date().valueOf()}
              bordered={true}
              rowKey={'id'}
              columns={columns as any}
              dataSource={list}
              pagination={false}
              footer={() => {
                return (
                  <div style={{ textAlign: 'center' }}>
                    <Button type="primary" onClick={add} style={{ width: '20%', marginRight: '5%' }} size={'small'}
                    // disabled={!!query['neatenEndTime']}
                    >
                      添加
                    </Button>
                    <Button type="primary"
                    // disabled={list.length == 0 || !!query['neatenEndTime']}
                    disabled={list.length == 0}
                     onClick={() => form.submit()} style={{ width: '20%' }} size={'small'}>
                      保存
                    </Button>
                  </div>
                );
              }}
              size={'middle'}
            />
          )}
        </Form>
      </Modal>
    </Fragment>
  );
};

export default React.forwardRef(NewAdd);
