/**
 * 列表
 */
import React from 'react';
import ListTable from '../ListTable';
import {connect} from "dva";
import RuiFilter from "../RuiFilter";
import WhiteSpace from '../WhiteSpace'
import {Input} from "antd";
import styles from './index.less';
import { root, rootParent } from '@/common/config';

const Search=Input.Search;
// @ts-ignore
@connect(({orgSpecialSelect,commonDict,loading})=>({orgSpecialSelect,commonDict,loading:loading.effects['orgSpecialSelect/getList']}))
export default class index extends React.Component<any,any>{
  static action(params?:object){}
  constructor(props){
    super(props);
    this.state={
      code:'',
      search:{},
      selectedRowKeys:[],
      selectedItems:[],
    };
    index.action=this.action;
  }
  componentDidMount(): void {
    // const dictData=['dict_d01','dict_d02','dict_d03',];
    // for(let obj of dictData){
    //   this.props.dispatch({
    //     type:'commonDict/getDict',
    //     payload:{
    //       data:{
    //         dicName:obj
    //       }
    //     }
    //   });
    // }
    this.action();
  }
  static getDerivedStateFromProps(props,state){
    const {value=[]}=props;
    if(value.length>0){
      let selectedRowKeys:any=[]
      value.forEach(obj=>{
        selectedRowKeys.push(obj['code'])
      })
      return {selectedRowKeys}
    }
    return null;
  };
  action=(params?:object)=>{
    const {orgCode,orgTypeList=undefined,exclude,isPermissionCheck}=this.props;
    const {search,filter}=this.state;
    const {pagination={}}=this.props.orgSpecialSelect;
    const {current=1,pageSize=10}=pagination;
    if(orgTypeList && filter){//如果限定只能选择组织类别 删除 d01CodeList
      delete filter['d01CodeList']
    }
    this.props.dispatch({
      type:'orgSpecialSelect/getList',
      payload:{
        data:{
          orgCode,
          isPermissionCheck,//是否权限检验,0--不进行校验,1--进行权限校验,为空默认进行权限检验
          excludeOrgCodeList:exclude || [],//排除的组织层级码集合
          pageNum:current,
          pageSize:pageSize,
          ...search,
          ...filter,
          ...params,
          orgTypeList
        }
      }
    })
  };
  filterChange=(val)=>{
    this.setState({
      filter:val
    },()=>this.action());
  };
  onPageChange=(page,pageSize)=>{
    this.action({pageNum:page,pageSize});
  };
  onSelectChange=(selectedRowKeys,record)=>{
    const {onChange}=this.props;
    this.setState({
      selectedRowKeys,selectedItems:record,
    });
    onChange && onChange(record);
  };
  search=(val)=>{
    this.setState({
      search:{orgName:val}
    },()=>this.action());
  };
  searchClear=(e)=>{
    if(!e.target.value){
      this.setState({
        search:{orgName:undefined}
      },()=>this.action());
    }
  };
  render(){
    const {list,pagination={}}=this.props.orgSpecialSelect;
    const {current,pageSize}=pagination;
    const {loading,org,multiple=false}=this.props;
    const {selectedRowKeys}=this.state;
    const columns=[
      // {
      //   title:'序号',
      //   dataIndex:'num',
      //   render:(text,record,index)=>{
      //     return (current-1)*pageSize+index+1
      //   }
      // },
      {
        title:'产业名称',
        dataIndex:'industryName',
        width:200,
      },
      {
        title:'发展经济类型',
        dataIndex:'d128Name',
        width:200,
      },
      {
        title:'采取组织形式',
        dataIndex:'d129Name',
        width:200,
      },
    ];
    const filterData=[
      {
        key:'d01CodeList',name:'组织类别',value:this.props.commonDict[`dict_d01_tree`],
      },
      {
        key:'d03CodeList',name:'隶属关系',value:this.props.commonDict[`dict_d03_tree`],
      },
      {
        key:'d02CodeList',name:'单位情况',value:this.props.commonDict[`dict_d02_tree`],
      },
    ];
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
      type: multiple ? 'checkbox' : 'radio',
    };
    return(
      <React.Fragment>
        <div className={styles.search}>
          <div>
            <Search style={{width:200,marginLeft:16}} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'}/>
          </div>
          <div style={{textAlign:'left',float:'left',fontSize:20}}>{org['name'] || org['managerOrgName']}</div>
        </div>
        <WhiteSpace/>
        <WhiteSpace/>
        {/*<RuiFilter data={filterData} onChange={this.filterChange}/>*/}
        {/*<WhiteSpace/>*/}
        {/*<WhiteSpace/>*/}
        <ListTable
          rowSelection={rowSelection}
          
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={this.onPageChange}
          scroll={{y:254}}
          rowKey={'code'}
        />
      </React.Fragment>
    )
  }
}
