/**
 * 列表
 */
import React from 'react';
import ListTable from '../ListTable';
import {connect} from "dva";
import RuiFilter from "../RuiFilter";
import WhiteSpace from '../WhiteSpace'
import styles from './index.less';
import {root, rootParent} from "@/common/config";
// @ts-ignore
@connect(({unitSelect,commonDict,loading})=>({unitSelect,commonDict,loading:loading.effects['unitSelect/getOtherList']}),undefined,undefined,{forwardRef:true})
export default class index extends React.Component<any,any>{
  static action(params?:object){}
  constructor(props){
    super(props);
    this.state={
      code:'',
      search:{},
      selectedRowKeys:[],
      selectedItems:[],
    };
    index.action=this.action;
  }
  componentDidMount(): void {
    const dictData=[
      // 'dict_d04',
      'dict_d35','dict_d36'
    ];
    for(let obj of dictData){
      this.props.dispatch({
        type:'commonDict/getDict',
        payload:{
          data:{
            dicName:obj
          }
        }
      });
    }
    this.action();
  }
  action=(params?:object)=>{
    const {orgCode,isCreateOrg,unitType,subordinate}=this.props;
    const {search}=this.state;
    this.props.dispatch({
      type:'unitSelect/getOtherList',
      payload:{
        data:{
          subordinate,
          isCreateOrg:isCreateOrg,
          mainUnitOrgCode:orgCode,
          manageUnitOrgCode:orgCode,
          pageNum:1,
          pageSize:10,
          d04CodeList:unitType,
          ...search,
          ...params,
        }
      }
    })
  };
  filterChange=(val)=>{
    const {pagination={}}=this.props.unitSelect;
    const {current,pageSize}=pagination;
    this.action({pageNum:current, pageSize,...val});
    this.setState({
      search:val
    });
  };
  onPageChange=(page,pageSize)=>{
    this.action({pageNum:page,pageSize});
  };
  onSelectChange=(selectedRowKeys,record)=>{
    const {onChange}=this.props;
    this.setState({
      selectedRowKeys,selectedItems:record,
    });
    onChange && onChange(record);
  };
  render(){
    const {list,pagination={}}=this.props.unitSelect;
    const {current,pageSize}=pagination;
    const {loading, disabledColFunc}=this.props;
    const {selectedRowKeys}=this.state;
    const columns=[
      // {
      //   title:'序号',
      //   dataIndex:'num',
      //   render:(text,record,index)=>{
      //     return (current-1)*pageSize+index+1
      //   }
      // },
      {
        title:'单位名称',
        dataIndex:'name',
        width:200,
      },
      {
        title:'单位类别',
        dataIndex:'d04Name',
        width:200,
      },
      {
        title:'隶属关系',
        dataIndex:'d35Name',
        width:200,
      },
      {
        title:'关联组织',
        dataIndex:'mainOrgName',
        width:200,
      },
    ];
    const filterData=[
      // {
      //   key:'d04CodeList',name:'单位类别',value:this.props.commonDict[`dict_d04_tree`],
      // },
      {
        key:'d36CodeList',name:'组织情况',value:this.props.commonDict[`dict_d36_tree`],
      },
      {
        key:'d35CodeList',name:'隶属关系',value:this.props.commonDict[`dict_d35_tree`],
      },
    ];
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
      type:'radio',
      getCheckboxProps: (record) => ({
        disabled: disabledColFunc ? disabledColFunc(record) : false,
      }),
      // onSelect: this.onSelect,
      // hideDefaultSelections: true,
    };
    return(
      <React.Fragment>
        <RuiFilter data={filterData} onChange={this.filterChange}/>
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable
          rowSelection={rowSelection}
          
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={this.onPageChange}
          scroll={{y:254}}
          rowKey={'code'}
        />
      </React.Fragment>
    )
  }
}
