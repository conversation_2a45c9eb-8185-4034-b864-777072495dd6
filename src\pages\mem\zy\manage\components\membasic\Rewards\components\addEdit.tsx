import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Avatar, Col, Input, Modal, Row, Radio, DatePicker, Button } from 'antd';
import styles from './addEdit.less';
import DictTreeSelect from '@/components/DictTreeSelect';
import { getSession } from '@/utils/session';
import { findDictCodeName, treeToList } from '@/utils/method.js';
import Tip from '@/components/Tip';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _startsWith from 'lodash/startsWith';
import _isNumber from 'lodash/isNumber';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import _isArray from 'lodash/isArray';
import { formLabel } from '@/utils/method';
import Date from '@/components/Date';
import { validateLength } from '@/utils/formValidator';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      type: '1',// 信息类型 1奖励 0惩罚
      // rewardCode_reward:[],   // 授奖受惩名称字典表类型分类
      // rewardCode_punish:[],
      // rewardReason_reward:[],  // 授奖受惩原因字典表类型分类
      // rewardReason_punish:[],
      _rewardsInfo: {},
    };
  }
  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    const state = {};
    const { memRewards: { rewardsInfo = {} } = {}, type = '' } = nextProps;
    const { _rewardsInfo, _type = '' } = prevState;
    if (!_isEqual(rewardsInfo, _rewardsInfo)) {
      state['_rewardsInfo'] = rewardsInfo;
      const { type } = rewardsInfo;
      state['type'] = _isNumber(type) ? type.toString() : '';
    }
    if (!_isEqual(type, _type)) {
      state['_type'] = type;
      state['type'] = type;
    }
    return state;
  };


  open = () => {
    const { basicInfo } = this.props?.memBasic || {}
    // 党员类别 1正式党员 2预备党员
    // 正式党员增加奖惩的时候，需要把预备期满取消预备党员资格和预备期未满取消预备党员资格这两项去掉。预备党员增加受惩时，要根据预备期是否满，来判断显示预备期满取消预备党员资格和预备期未满被取消预备党员资格
    const { d08Code = undefined, extendPreparDate = undefined } = basicInfo;
    // 判断预备期满     预备期满C22,预备期未满C23
    let isFinish = extendPreparDate && moment(extendPreparDate).isSameOrBefore(moment())
    if (d08Code === '1') {
      this.setState({
        d029CodeNoDraw: ['C22', 'C23']
      })
    }
    if (d08Code === '2') {
      if (isFinish) {
        this.setState({
          d029CodeNoDraw: ['C23']
        })
      }
      if (!isFinish) {
        this.setState({
          d029CodeNoDraw: ['C22']
        })
      }
    }
    this.setState({
      visible: true,
    })
  };
  onCancel = () => {
    const { onclose } = this.props;
    onclose && onclose();
    this.destroy()
  };
  destroy = () => {
    this.setState({
      visible: false,
      type: '1',// 信息类型 1奖励 0惩罚
      rewardCode_reward: [],   // 授奖受惩名称字典表类型分类
      rewardCode_punish: [],
      rewardReason_reward: [],  // 授奖受惩原因字典表类型分类
      rewardReason_punish: [],
      _rewardsInfo: {},
      _dict_d29_tree: [],
      _dict_d47_tree: []
    });
    this.props.dispatch({ //重置model
      type: 'memRewards/updateState',
      payload: {
        rewardsInfo: {},
      }
    })
  };
  getList = () => {
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memBasic/getList',
      payload: {
        data: {
          pageNum: 1,
          pageSize: 10,
          searchType: 1,
          memOrgCode: org['orgCode']
        }
      }
    })
  };

  typeOnChange = (e) => {
    const { target: { value = '' } = {} } = e;
    if (!_isEmpty(value)) {
      this.setState({ type: value });
      this.props.form.resetFields();
    }
  };
  submit = () => {
    const { form, loading: { effects = {} } = {}, memBasic: { basicInfo = {} } = {}, memRewards: { rewardsInfo = {} } = {} } = this.props;
    form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {

        val = findDictCodeName(['d029', 'd030', 'd52', 'd51', 'd47'], val, rewardsInfo,);

        val['memCode'] = _get(basicInfo, 'code', '');
        val['rewardOrgCode'] = _get(basicInfo, 'memOrgCode', '');
        val['orgCode'] = _get(basicInfo, 'orgCode', '');
        val['orgEntryCode'] = _get(basicInfo, 'orgCode', '');
        ['startDate'].forEach(item => {
          val[`${item}`] = _isEmpty(val[`${item}`]) ? undefined : moment(val[`${item}`], 'YYYY-MM-DD').valueOf();
        });
        if (val['d029Code'].startsWith('C2')) {
          val['d030Code'] = ''
          val['d030Name'] = ''
        }
        const res = await this.props.dispatch({
          type: 'memRewards/save',
          payload: {
            data: val,
            type: _isEmpty(val['code']) ? 'add' : 'edit'
          }
        });
        const { code = 500 } = res || {};
        if (code === 0) {
          Tip.success('操作提示', !_isEmpty(val['code']) ? '修改成功' : '新增成功');
          this.destroy();
          this.props.dispatch({
            type: 'memRewards/getList',
            payload: { pageNum: 1, pageSize: 10, memCode: basicInfo['code'] }
          });
          if (val['d029Code'] == '181') {
            // 党员奖惩新增追授优秀共产党员后，党员列表需要刷新列表
            this.props.getList();
          }
        }
      }
    })
  };
  render() {
    const { form, memRewards, loading: { effects = {} } = {}, memBasic: { basicInfo = {} } = {}, tipMsg = {} } = this.props;
    const { rewardsInfo = {} } = memRewards;
    const { getFieldDecorator, getFieldValue } = form;
    const { visible, type, d029CodeNoDraw = [] } = this.state;
    return (
      <Modal
        title={rewardsInfo['code'] ? '编辑' : '新增'}
        destroyOnClose
        visible={visible}
        onOk={this.submit}
        onCancel={this.onCancel}
        width={'800px'}
        confirmLoading={effects['memRewards/save']}
      >
        <Form>
          {getFieldDecorator('type', { initialValue: rewardsInfo['type'] || type })(
            <div style={{ display: 'none' }}>123</div>
          )}
          {getFieldDecorator('code', { initialValue: rewardsInfo['code'] })(
            <div style={{ display: 'none' }}>123</div>
          )}
          <FormItem
            label={formLabel(`党员${type === '1' ? '授奖' : '受惩'}名称`, tipMsg['d029Code'])}
            {...formItemLayout}
          >
            {getFieldDecorator('d029Code', {
              rules: [{ required: true, message: `党员${type === '1' ? '授奖' : '受惩'}名称` }],
              initialValue: _isEmpty(rewardsInfo) ? undefined : rewardsInfo['d029Code'],
            })(
              <DictTreeSelect
                showConstant={false}
                key={type}
                initValue={_isEmpty(rewardsInfo) ? undefined : rewardsInfo['d029Code']}
                codeType={'dict_d29'}
                placeholder={`党员${type === '1' ? '授奖' : '受惩'}名称`}
                parentDisable={true}
                backType={'object'}
                noDraw={d029CodeNoDraw}
                filter={(data: any) => {
                  if (type === '1') {
                    return data.filter(it => !`${it?.key || ''}`.startsWith('C'))
                  } else {
                    return data.filter(it => `${it?.key || ''}`.startsWith('C1') || `${it?.key || ''}`.startsWith('C2'))
                  }
                }}
              />
            )}
          </FormItem>
          {
            type === '1' &&
            <FormItem
              label={formLabel('表彰类型', tipMsg['commendation'])}
              {...formItemLayout}
            >
              {getFieldDecorator('commendation', {
                rules: [{ required: true, message: '请填写表彰类型' }],
                initialValue: _isEmpty(rewardsInfo) ? 0 : rewardsInfo['commendation'],
              })(
                <RadioGroup>
                  <Radio value={0}>及时性表彰</Radio>
                  <Radio value={1}>定期集中性表彰</Radio>
                </RadioGroup>
              )}
            </FormItem>
          }
          {/* <FormItem
            label={ formLabel(`${type === '1' ? '授奖' : '受惩'}原因说明`,tipMsg['remark']) }
            {...formItemLayout}
          >
            {getFieldDecorator('remark', {
              rules: [{ required: false, message: `请填写${type === '1' ? '授奖' : '受惩'}原因说明` }],
              initialValue: _isEmpty(rewardsInfo)?undefined:rewardsInfo['remark'],
            })(
              <Input placeholder={`请填写${type === '1' ? '授奖' : '受惩'}原因说明`}/>
            )}
          </FormItem> */}
          <FormItem
            label={formLabel('原始文件号', tipMsg['fileNumber'])}
            {...formItemLayout}
          >
            {getFieldDecorator('fileNumber', {
              rules: [{ required: false, message: `请填写原始文件号` }],
              initialValue: _isEmpty(rewardsInfo) ? undefined : rewardsInfo['fileNumber'],
            })(
              <Input placeholder={`请填写原始文件号`} />
            )}
          </FormItem>

          {
            type === '1' ?
              // <FormItem
              //   key={1}
              //   label={ formLabel('党员授奖原因',tipMsg['d47Code']) }
              //   {...formItemLayout}
              // >
              //   {getFieldDecorator('d47Code', {
              //     rules: [{ required: true, message: `请填写党员授奖原因` }],
              //     initialValue: _isEmpty(rewardsInfo)?undefined:rewardsInfo['d47Code'],
              //   })(
              //     <DictTreeSelect
              //       initValue={_isEmpty(rewardsInfo)?undefined:rewardsInfo['d47Code']}
              //       codeType={'dict_d47'}
              //       placeholder={`请填写党员授奖原因`}
              //       parentDisable={true}
              //       backType={'object'}
              //       noDraw={['92']}
              //       filter={(data:any)=>data.filter(it=>!it.key.startsWith('2'))}
              //     />
              //   )}
              // </FormItem>
              null
              :
              <Fragment>
                {(function (_this) {
                  let obj = _this.props.form.getFieldValue('d029Code');
                  let val = obj;
                  if (typeof obj == 'object') {
                    val = obj['key'];
                  }
                  if (`${val}`.startsWith('C2')) {
                    return <React.Fragment />
                  }
                  return (
                    <FormItem
                      key={2}
                      label={formLabel('党员受惩原因', tipMsg['d030Code'])}
                      {...formItemLayout}
                    >
                      {getFieldDecorator('d030Code', {
                        rules: [{ required: true, message: `请填写党员受惩原因` }],
                        initialValue: _isEmpty(rewardsInfo) ? undefined : rewardsInfo['d030Code'],
                      })(
                        <DictTreeSelect
                          initValue={_isEmpty(rewardsInfo) ? undefined : rewardsInfo['d030Code'] ? rewardsInfo['d030Code'].split(',') : undefined}
                          codeType={'dict_d301'}
                          placeholder={`请填写党员受惩原因`}
                          parentDisable={true}
                          backType={'object'}
                          treeCheckable={true}
                          filter={(data) => {
                            let newData = data;
                            const onlyOneClass = (arr) => {
                              // 党员受惩原因，大类互斥，只能在一个大类下多选：选择2的子类后不能选择3的子类和9，但是2的内部子类是可以多选的
                              let item = getFieldValue('d030Code')
                              let itemLastKey = '';
                              if (_isArray(item)) {
                                itemLastKey = item[item.length - 1]?.key || '';
                              }
                              if (typeof item == 'string') {
                                let itemArr = item.split(',');
                                itemLastKey = itemArr[itemArr.length - 1]
                              }
                              if (itemLastKey.startsWith('2')) {
                                arr = arr.filter(it => it.key.startsWith('2'))
                              } else if (itemLastKey.startsWith('3')) {
                                arr = arr.filter(it => it.key.startsWith('3'))
                              } else if (itemLastKey.startsWith('9')) {
                                arr = arr.filter(it => it.key.startsWith('9'))
                              }
                              return arr;
                            }
                            // 原因只能是纪律处分和其他的原因，即上表的2开头和9其他原因
                            if (`${val}`.startsWith('C1')) {
                              newData = data.filter(it => it.key.startsWith('2') || it.key.startsWith('9'))
                            }
                            // 原因只能是组织处置和其他的原因，即上表的3开头和9其他原因
                            if (`${val}`.startsWith('C2')) {
                              // 退党除名	原因只能是其他原因，即上表9其他原因
                              // 不予承认	原因只能是其他原因，即上表9其他原因
                              if (`${val}` == 'C26' || `${val}` == 'C29') {
                                newData = data.filter(it => it.key.startsWith('9'))
                              } else if (`${val}` == 'C27') { // 自行脱党除名	原因只能是组织纪律散漫和其他原因，即上表的35和9其他原因
                                let datas = _cloneDeep(data).filter(it => it.key == '9' || it.key == '3');
                                let find = datas.find(it => it.key == '3');
                                if (find) {
                                  let children = find.children.filter(it => it.key === '35');
                                  find.children = children;
                                }
                                newData = datas;
                              } else if (`${val}` == 'C22' || `${val}` == 'C23') { //党员授奖（受惩）名称 C22 或 C23开头, 原因2开头的选项也要
                                newData = data.filter(it => it.key.startsWith('2') || it.key.startsWith('3') || it.key.startsWith('9'))
                              } else {
                                newData = data.filter(it => it.key.startsWith('3') || it.key.startsWith('9'))
                              }
                            }
                            newData = onlyOneClass(newData)
                            return newData;
                          }}
                          // itemsDisabled={['9']} 前端取消限制改为后端配置
                        />
                      )}
                    </FormItem>
                  )
                })(this)}
              </Fragment>
          }

          <FormItem
            label={formLabel(`${type === '1' ? '授奖' : '受惩'}批准机构名称`, tipMsg['orgName'])}
            {...formItemLayout}
          >
            {getFieldDecorator('orgName', {
              rules: [{ required: true, message: `${type === '1' ? '授奖' : '受惩'}批准机构名称` }, { validator: (...e) => validateLength(e, 100, 300) }],
              initialValue: _isEmpty(rewardsInfo) ? undefined : rewardsInfo['orgName'],
            })(
              <Input placeholder={`${type === '1' ? '授奖' : '受惩'}批准机构名称`} />
            )}
          </FormItem>
          <FormItem
            label={formLabel(`${type === '1' ? '授奖' : '受惩'}批准机构级别`, tipMsg['d52Code'])}
            {...formItemLayout}
          >
            {getFieldDecorator('d52Code', {
              rules: [{ required: true, message: `${type === '1' ? '授奖' : '受惩'}批准机构级别` }],
              initialValue: _isEmpty(rewardsInfo) ? undefined : rewardsInfo['d52Code'],
            })(
              <DictTreeSelect
                key={type}
                initValue={_isEmpty(rewardsInfo) ? undefined : rewardsInfo['d52Code']}
                codeType={'dict_d52'}
                placeholder={`${type === '1' ? '授奖' : '受惩'}批准机构级别`}
                parentDisable={true}
                backType={'object'}
              />
            )}
          </FormItem>
          {/* <FormItem
            label={ formLabel(`${type === '1' ? '授奖' : '受惩'}时行政职务级别`,tipMsg['d51Code']) }
            {...formItemLayout}
          >
            {getFieldDecorator('d51Code', {
              rules: [{ required: false, message: `${type === '1' ? '授奖' : '受惩'}时行政职务级别` }],
              initialValue: _isEmpty(rewardsInfo)?undefined:rewardsInfo['d51Code'],
            })(
              <DictTreeSelect
                key={type}
                initValue={_isEmpty(rewardsInfo)?undefined:rewardsInfo['d51Code']}
                codeType={'dict_d51'}
                placeholder={`${type === '1' ? '授奖' : '受惩'}时行政职务级别`}
                parentDisable={true}
                backType={'object'}
              />
            )}
          </FormItem> */}
          <FormItem
            label={formLabel(`${type === '1' ? '授奖时间' : '惩罚时间'}`, tipMsg['startDate'])}
            {...formItemLayout}
          >
            {getFieldDecorator('startDate', {
              rules: [{ required: true, message: `${type === '1' ? '授奖' : '惩罚'}生效时间` }],
              initialValue: !_isNumber(rewardsInfo['startDate']) ? undefined : moment(rewardsInfo['startDate']),
              // <DatePicker placeholder="请选择" style={{width:'100%'}}/>
            })(
              <Date />
            )}
          </FormItem>
        </Form>
      </Modal>
    )
  }
}
export default Form.create()(index);
