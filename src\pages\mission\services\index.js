import request from '@/utils/request';
import qs from 'qs';
export function insertTask(params) {
  return request(`/api/task/insertTask`,{
    method:'POST',
    body:params
  });
}
export function updateTask(params) {
  return request(`/api/task/updateTask`,{
    method:'POST',
    body:params
  });
}
export function taskList(params) {
  return request(`/api/task/taskList`,{
    method:'POST',
    body:params
  });
}
export function delTask(params) {
  return request(`/api/task/delTask`,{
    method:'POST',
    body:params
  });
}
export function findTaskByCode(params) {
  return request(`/api/task/findTaskByCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function findCheckList(params) {
  return request(`/api/task/findCheckList?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function remindTask(params) {
  return request(`/api/task/remindTask`,{
    method:'POST',
    body:params
  });
}

export function removeTask(params) {
  return request(`/api/task/removeTask`,{
    method:'POST',
    body:params
  });
}

export function checkTask(params) {
  return request(`/api/task/checkTask`,{
    method:'POST',
    body:params
  });
}
