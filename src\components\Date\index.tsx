import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';
import moment from 'moment';
import { uuid } from '@/utils/method.js';
import tip from '@/components/Tip';
import { Tooltip, Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
moment.suppressDeprecationWarnings = true;
interface Interface {
  value?: any;
  size?: 'large' | 'small' | 'default';
  pickerOnChange?: (val: moment.Moment) => void;
  mode?: string; // 'YYYY.MM.DD'  'YYYY.MM'
  format?: string;
  onChange?: (any) => void;
  onBlur?: Function;
  onError?: Function;
  startTime?: moment.Moment | string;
  endTime?: moment.Moment | string;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  style?: any;
  isDefaultEnd?: boolean; // endTime 有值时 isDefaultEnd 失效
  tipMessage?: string;
}

function handleKeyPress(event) {
  const invalidChars = ['-', '+', 'e', '.', 'E'];
  if (invalidChars.indexOf(event.key) !== -1) {
    event.preventDefault();
  }
}
export default function DateTime(props: Interface & { defaultEndTime?}) {
  const { disabled = false, onChange, value, mode = 'YYYY.MM.DD', defaultEndTime = moment(), isDefaultEnd = true, endTime, startTime, placeholder = undefined, allowClear = false, tipMessage = undefined } = props;
  const [id, setId]: any = useState();
  const [visible, setVisible]: any = useState(false);
  const date: any = useRef({ year: undefined, month: undefined, day: undefined });
  const dou: any = useRef();
  const tips: any = useRef();
  useEffect(() => {
    let y: any, hDay: any;
    if (value != undefined) {
      y = moment(value).format(mode);
      hDay = moment(value).format('d');
      date.current['year'] = y;
      holiday(hDay);
    } else {
      date.current['year'] = undefined;
    }
    setId(uuid());
  }, [JSON.stringify(value)]);
  const holiday = (val, isClick = false) => {
    // 当mode='YYYY.MM'，不显示具体日期时就不提示是否节假日
    if (!visible && ['0', '6'].includes(val) && mode === 'YYYY.MM.DD') {
      setVisible(true);
      setTimeout(() => {
        if (tips.current && isClick) {
          tips.current.onClick();
          tips.current = undefined;
        }
      }, 100);
      console.log('节假日');
    }
  };
  const getFocus = () => {
    const { year } = date.current;
    let elementById: any = document.getElementById(`time1_${id}`);
    let val = elementById.value;
    elementById.type = 'number';
    if (year) {
      elementById.value = moment(year).format(mode.replace(/\./g, ''));
    } else {
      elementById.value = val;
    }
    // console.log(elementById, 'eeeeeeeeeeeeeeeee');
  };
  const formatTime = () => {
    const { year } = date.current;
    // console.log('formatTime', `${year}`);
    const onError = () => {
      console.error('不是有效的时间');
      tip.error('提示', '不是有效的时间');
      onChange && onChange(undefined);
    };
    if (year) {
      let time = moment(year, mode);
      if (time.format(mode) == 'Invalid date') {
        onError();
      } else {
        holiday(time.format('d'), true);
        if (endTime != undefined) {
          if (moment(time).endOf('day') > moment(endTime).endOf('day')) {
            if (tipMessage) {
              return errMsg(tipMessage);
            }
            return errMsg(`输入日期不能晚于${moment(endTime).format(mode)}`);
          }
        } else if (isDefaultEnd) {
          if (moment(time).endOf('day') > defaultEndTime.endOf('day')) {
            if (tipMessage) {
              return errMsg(tipMessage);
            }
            return errMsg(`输入日期不能晚于${defaultEndTime.format(mode)}`);
          }
        }
        if (startTime != undefined) {
          if (moment(time).endOf('day') < moment(startTime).endOf('day')) {
            if (tipMessage) {
              return errMsg(tipMessage);
            }
            return errMsg(`输入日期不能早于${moment(startTime).format(mode)}`);
          }
        }
        //夏令时时间 转 时间戳会少一个小时，需加一个小时
        // if(time.isDST()){
        //   time.add(2,'hours')
        // }
        time.add(3, 'hours');
        onChange && onChange(time);
      }
      // console.log(time.format(mode), 'xxxxxxxxxxxxxxxxxxx');
    } else {
      onChange && onChange(undefined);
    }
  };
  const errMsg = (msg) => {
    let elementById: any = document.getElementById(`time1_${id}`);
    elementById.value = '';
    const { onChange } = props;
    onChange && onChange(undefined);
    date.current = { year: undefined };
    return Modal.error({
      title: '错误信息提示',
      content: (
        <div>
          <p>{msg}</p>
        </div>
      ),
      onOk() { },
    });
  };
  const timeBlur = () => {
    if (dou.current) {
      clearTimeout(dou.current);
    }
    dou.current = setTimeout(() => {
      const { year } = date.current;
      let elementById: any = document.getElementById(`time1_${id}`);
      elementById.type = 'text';
      if (year) {
        let val = moment(year).format(mode);
        if (val == 'Invalid date' || val == 'Invalid Date') {
          elementById.value = '';
        } else {
          elementById.value = val;
          props.onBlur && props.onBlur(val);
        }
      } else {
        props.onBlur && props.onBlur(undefined);
      }
      formatTime();
    }, 50);
  };
  return (
    <div className={`ant-input over ${styles.date} ${disabled ? styles.disabledBg : ''}`} onBlur={timeBlur} onFocus={getFocus} style={{ pointerEvents: disabled ? 'none' : 'auto', ...props.style }}>
      <input
        id={`time1_${id}`}
        type="text"
        placeholder={placeholder || '请输入日期 如:20080808'}
        defaultValue={date.current['year']}
        style={{ width: '80%' }}
        onClick={(e) => e.stopPropagation()}
        onFocus={() => dou.current && clearTimeout(dou.current)}
        onChange={(e) => {
          const val = e.target['value'];
          // 删不掉  加了判断大于0
          if (val.length < 4 && val.length > 0) {
            date.current['year'] = `${2000 + Number(val)}`;
          } else {
            date.current['year'] = val;
          }
          console.log(val, 'e.target');
          if (visible) {
            setVisible(false);
          }
        }}
        maxLength={8}
        onKeyPress={(e) => handleKeyPress(e)}
      />
      {visible && (
        <span className={styles.tip}>
          <Tooltip ref={tips} placement="top" title="该日期为节假日" trigger={['hover', 'click']}>
            <ExclamationCircleOutlined />
          </Tooltip>
        </span>
      )}
    </div>
  );
  // return (
  //   <div
  //     className={`ant-input over ${styles.date} ${disabled ? styles.disabledBg : ''}`}
  //     onClick={getFocus}
  //     onBlur={timeBlur}
  //     style={{ pointerEvents: disabled ? 'none' : 'auto' }}
  //   >
  //     <input
  //       id={`time1_${id}`}
  //       type="number"
  //       defaultValue={date.current['year']}
  //       style={{ width: 38 }}
  //       onClick={(e) => e.stopPropagation()}
  //       onFocus={() => dou.current && clearTimeout(dou.current)}
  //       onChange={(e) => {
  //         date.current['year'] = e.target['value'];
  //         if (visible) {
  //           setVisible(false);
  //         }
  //       }}
  //       maxLength={4}
  //       onKeyPress={(e) => handleKeyPress(e)}
  //       onKeyUp={(e) => {
  //         e.target['value'].length >3 && keyChange('time2');
  //         backSpace(e, 'year');
  //       }}
  //     />
  //     年
  //     <input
  //       id={`time2_${id}`}
  //       type="number"
  //       defaultValue={date.current['month']}
  //       style={{ width: 20 }}
  //       onClick={(e) => e.stopPropagation()}
  //       onFocus={() => {
  //         dou.current && clearTimeout(dou.current);
  //       }}
  //       onChange={(e) => {
  //         date.current['month'] = e.target['value'];
  //         if (visible) {
  //           setVisible(false);
  //         }
  //       }}
  //       maxLength={2}
  //       onKeyPress={(e) => handleKeyPress(e)}
  //       onKeyUp={(e) => {
  //         let val = e.target['value'];
  //         console.log(val,'vvvvvvvvvvvvvv')
  //         if (val.length>1) {
  //           keyChange('time3');
  //         }
  //         backSpace(e, 'month');
  //       }}
  //     />
  //     月
  //     <input
  //       id={`time3_${id}`}
  //       type="number"
  //       defaultValue={date.current['day']}
  //       style={{ width: 20 }}
  //       onClick={(e) => e.stopPropagation()}
  //       onFocus={() => {
  //         dou.current && clearTimeout(dou.current);
  //       }}
  //       onChange={(e) => {
  //         date.current['day'] = e.target['value'];
  //         if (visible) {
  //           setVisible(false);
  //         }
  //       }}
  //       maxLength={2}
  //       onKeyPress={(e) => handleKeyPress(e)}
  //       onKeyUp={(e) => {
  //         backSpace(e, 'day');
  //       }}
  //     />
  //     日
  //     {visible && (
  //       <span className={styles.tip}>
  //         <Tooltip ref={tips} placement="top" title="该日期为节假日" trigger={['hover', 'click']}>
  //           <ExclamationCircleOutlined />
  //         </Tooltip>
  //       </span>
  //     )}
  //   </div>
  // );
}
