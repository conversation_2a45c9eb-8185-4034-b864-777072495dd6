/**
 * 新增编辑收支
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Alert,
  Button,
  Col,
  DatePicker,
  Divider,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Switch,
  Tag,
  Tooltip,
} from 'antd';
import {connect} from "dva";
import moment from 'moment'
import Notice from '@/components/Notice';
import { getSession } from '@/utils/session';
import { isEmpty } from '@/utils/method';
import MemSelect from '@/components/MemSelect'
import DictSelect from '@/components/DictSelect';
import ListFilter from '@/pages/dues/spending/index';
const QRCode = require('qrcode.react');
const {MonthPicker}=DatePicker;
const RadioGroup=Radio.Group;
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};
@connect(({dues,login})=>({dues,login}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      key:new Date().valueOf(),
      stand:'1',
      months:1,
      url:'',
      qsvsb:false
    };
  }
  showModal=()=>{
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
      org
    });
  };
  disabledDate=(current)=>{
    return current && current > moment().endOf('day')||current<moment('2019');
  };

  handleOk=()=>{
    const { org={} }=this.state;
    const { onChange,type ,data={}} = this.props;
    this.props.form.validateFieldsAndScroll(async(errors, values) => {
      if (errors){
        return
      }
      const { d68,recordTime,...val } = values;
      let obj={
        code:data['code'],
        orgCode:org['code'],
        disburseOrgCode:org['orgCode'],
        d68Code:d68['key']||data['d68Code'],
        d68Name:d68['name']||data['d68Name'],
        recordTime:moment(recordTime).valueOf(),
        recordType:0,
        ...val
      };
      for (let o in obj) {
        if (isEmpty(obj[o])) {
          delete obj[o]
        }
      }
      if (type == 'add') {
        this.props.dispatch({
          type:'dues/saveFeeDisburse',
          payload:{
            data:{
              ...obj
            }
          }
        }).then(res=>{
          if (res['code'] == 0) {
            Notice("操作提示",'保存成功!',"check-circle","green");
            this.handleCancel();
            onChange()
          }else {
            Notice("操作提示",res['message'],"exclamation-circle-o","orange");
          }
        })
      }else {
        this.props.dispatch({
          type:'dues/updateFeeDisburse',
          payload:{
            data:{
              ...obj
            }
          }
        }).then(res=>{
          if (res['code'] == 0) {
            Notice("操作提示",'保存成功!',"check-circle","green");
            this.handleCancel();
            onChange()
          }else {
            Notice("操作提示",res['message'],"exclamation-circle-o","orange");
          }
        })
      }

    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      data:{},
    });
    this.props.form.resetFields()
  };


  changeDate=(v)=>{
    this.setState({
      year:moment(v).format('YYYY'),
      month:moment(v).format('M'),
      date:moment(v).valueOf()
    },()=>{

    })
  };

  render(){
    const {visible,filterHeight,memInfo=[],account='',money=0,qsvsb,org={}}=this.state;
    const {title='', data={},dues={},loading:{effects = {}} = {},children }=this.props;
    const {getFieldDecorator}=this.props.form;
    return (
      <div>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          destroyOnClose
          title={title||''}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={600}
          bodyStyle={{height:'auto',overflow:'auto'}}
        >
          <Form {...formItemLayout}>
            <Row>
              <Col span={24}>
                <FormItem
                  label="组织名称"
                >
                  <span>{isEmpty(data)?org['name']:data['orgName']}</span>
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label="收支项目"
                >
                  {getFieldDecorator('d68', {
                    initialValue:isEmpty(data)?undefined:data['d68Code'],
                    rules: [
                      { required: true, message: '请选择' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <DictSelect codeType={'dict_d68'} backType={'object'} initValue={data['d68Code'] || undefined}/>
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label={'金额'}
                >
                  {getFieldDecorator('money', {
                    initialValue:isEmpty(data)?null:data['money'],
                    rules: [
                      { required: true, message: '请填写金额' },
                      // { validator: this.validFunction }
                    ],
                    normalize: (a, prev) => {
                      if (a && !/^(([1-9]\d*)|0)(\.\d{0,2}?)?$/.test(a)) {
                        if (a === '.') {
                          return '0.';
                        }
                        return prev;
                      }
                      return a;
                    }
                  })(
                    <Input placeholder={'请填写金额'}/>
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label={'记录人'}
                >
                  {getFieldDecorator('recorder', {
                    initialValue:isEmpty(data)?null:data['recorder'],
                    rules: [
                      { required: true, message: '请填写记录人' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <Input placeholder={'请填写记录人'}/>
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label={'月份'}
                >
                  {getFieldDecorator('recordTime', {
                    initialValue:isEmpty(data)?moment():moment(data['recordTime']),
                    rules: [
                      { required: true, message: '请选择月份' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <MonthPicker
                      disabledDate={this.disabledDate}
                      placeholder="请选择月份"
                      style={{width:'100%'}}
                      // defaultValue={moment()}
                      format={'YYYY/MM'}
                      onChange={this.changeDate}/>
                  )}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </Modal>
      </div>
    );
  }
}
export default Form.create()(index)
