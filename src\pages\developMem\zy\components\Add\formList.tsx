import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import style from './index.less';
import NowOrg from '@/components/NowOrg';
import { Button, Form, Alert, Modal, Space, Radio, Input, Row, Col, Switch } from 'antd';
import ListTable from '@/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import SpinProgress from '@/components/SpinProgress';
import { processNodeNext, developExtendApproval, findDevelopProcess } from '@/pages/developMem/services'
import Tip from '@/components/Tip'
import Date from '@/components/Date';
import moment from 'moment'
import { connect } from "dva";
import { _history, unixMoment } from '@/utils/method';
import MemSelect from '@/components/MemSelect';
import _isNumber from 'lodash/isNumber';
import _isArray from 'lodash/isArray';
import _isString from 'lodash/isString';
import { compare, compareDate, getContextPerson } from '@/pages/developMem/services/index'
const { TextArea } = Input;
const RadioGroup = Radio.Group;

const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};
const formItemLayout1 = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 9 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
    },
};
const formItemLayout2 = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 12 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 },
    },
};
const index: any = (props, ref) => {
    const [form] = Form.useForm();
    const {
        dataInfo = {},
        commonDict: { dict_d28 = [] } = {},
        onOk
    } = props;
    console.log(dataInfo, 'dataInfodataInfodataInfo')
    useImperativeHandle(ref, () => ({
        showModal: (obj) => {
            open(obj);
        },
        submit: () => {
            form.submit()
        }
    }));

    const { location: { pathname = '' } = {} } = _history

    const [loading, setLoading] = useState(false);

    const [modalVisible, setModalVisible] = useState(false);
    const [row, setRow] = useState<any>({})
    const [info, setInfo] = useState<any>({})
    const [hasMemValue, setHasMemValue] = useState(true)
    const [showIsFullYear, setShowIsFullYear] = useState(false)
    const [isFullYear, setIsFullYear] = useState(true)
    const [toactiveContextPerson, setToactiveContextPerson] = useState<any>('')
    const [toobjContextMem, setToobjContextMem] = useState<any>('')

    const open = (obj) => {
        setRow(obj)
        // getData(obj)
        setModalVisible(true)
    }
    const getReferences = async () => {
        const { code = 500, data = {} } = await getContextPerson({
            memCode: dataInfo.code,
            d08Code: "5",
        });
        if (code === 0) {

            setToactiveContextPerson(data.toactiveContextPerson)
            setToobjContextMem(data.name)
            form.setFieldsValue({toobjContextMem: data.name,toactiveContextPerson:data.toactiveContextPerson })

        }
    };
    // const getData = async () => {
    //     const { code = 500, data = {} } = await zymemInfo({ memCode: dataInfo.code }) //预备党员转正-人员信息
    //     if (code === 0) {
    //         form.setFieldsValue({ topartTurnPartyDate: moment(data.topartTurnPartyDate), })
    //         setInfo({ name: data.name, extendPreparDate: moment(data.extendPreparDate).format('YYYY-MM-DD') })
    //     }
    // }
    const cancel = () => {
        setRow({})
        setModalVisible(false)
    }
    const hadndleFinish = async (e) => {
        const { orgCode, orgName, memOrgCode, code: memCode, d08Code, d08Name, name, orgZbCode } = dataInfo;
        console.log('🚀 ~ e:', e);
        // e['extendEndTime'] = moment(e['extendEndTime']).valueOf()
        e = unixMoment(['topartCommitteeDate', 'topartTurnPartyDate', 'topartOathDate', 'activeDate', 'objectDate'], e);
        [
            'hasStaffOrganization',
        ].map((item) => {
            e[`${item}`] = e[`${item}`] == '1' ? 1 : 0;
        });
        e['toactiveContextPerson'] = _isEmpty(e['toactiveContextPerson'])
            ? ''
            : hasMemValue
                ? e['toactiveContextPerson'].map((item) => item['code']).toString()
                : e['toactiveContextPerson'];
        if (_isString(e['toobjContextMem'])) {
            e['toobjContextMem'] = toactiveContextPerson;
        } else {
            e['toobjContextMem'] = _isEmpty(e['toobjContextMem']) ? "" : hasMemValue ? e['toobjContextMem'].map(item => item['code']).toString() : e['toobjContextMem'];
        }

        if (!_isEmpty(dict_d28)) {
            dict_d28.forEach(item => {
                if (item['key'] === e['d28Code']) {
                    e['d28Name'] = item['name']
                }
            })
        }
        e['logOrgCode'] = memOrgCode;
        e['orgName'] = orgName;
        e['orgCode'] = orgCode;
        e['memCode'] = memCode;
        e['d08Code'] = d08Code;
        e['d08Name'] = d08Name;
        e['name'] = name;
        e['orgZbCode'] = orgZbCode;
        console.log(e, '表单值')
        onOk && onOk(e)
    };
    const onChange1 = (e) => {
        console.log(e, 'eee')
        // setIsMark(e.target.value)
    }
    const RadioGroupOnChange = ({ target: { value = '' } = {} } = {}) => {
        // this.setState({d28Value:value})
    };
    const hasMemOnChange = (val,key) => {
        if(key=='active') {
            let person=form.getFieldValue("toobjContextMem") 
            if(!val){
              if(_isArray(person)){
                let personArr=person.map((item,index)=>item.name);
                form.setFieldsValue({toobjContextMem:personArr.toString()});
              }
            }else{
             form.setFieldsValue({toobjContextMem:person});
            }
        }
        form.setFieldsValue({toactiveContextPerson:undefined});
        setHasMemValue(val)

    }
    const disabledTomorrow = (current) => {
        const { memDevelop:{basicInfo={}}={} } = props;
        console.log(basicInfo,'basicInfo')
        const cu = moment(current);
        const start = moment(basicInfo['applyDate']).endOf('day');
        const end = moment();
        if (_isNumber(basicInfo['applyDate'])) {
            return (
                current && (cu.isBefore(start) || cu.isSame(start) || cu.isAfter(end) || cu.isSame(end))
            );
        } else {
            return false;
        }
    };
    const disabledTomorrow1 = (current) => {
        const { memDevelop:{basicInfo={}}={} } = props;
        const cu = moment(current);
        const start = moment(basicInfo['activeDate']).endOf('day');
        const end = moment();
        if (_isNumber(basicInfo['activeDate'])) {
            return (
                current && (cu.isBefore(start) || cu.isSame(start) || cu.isAfter(end) || cu.isSame(end))
            );
        } else {
            return false;
        }
    };
    const timeValidator = async (rule, value, callback) => {
        const { code = 500, data = true } = await compareDate({
            data: {
                code: dataInfo.code,
                type: 3,
                time: moment(value).valueOf()
            }
        });
        if (code === 0) {
            if (!data) {
                setShowIsFullYear(false)
                // callback(new Error('确定发展对象时间早于确定积极分子时间'));
                return Promise.reject('确定发展对象时间早于确定积极分子时间')

            } else {
                setShowIsFullYear(true)
                // callback();
                return Promise.resolve()
            }
        }
        // callback();
        return Promise.resolve()
    }
    
    const timeValidator3 = async (rule, value, callback) => {
        const { memDevelop:{basicInfo={}}={} } = props;
        console.log(basicInfo,'basicInfo')
        const cu = moment(value);
        const applyDate = moment(basicInfo.applyDate);
        const now = applyDate.add(3,'months')
        console.log(now.format('YYYY-MM-DD'),cu.format('YYYY-MM-DD'),'now')
        if(!now.isBefore(cu)) {
            return Promise.reject('该时间距离入党申请不足三个月')
        } else {
            return Promise.resolve()
        }
        // if (!data) {
        //     setShowIsFullYear(false)
        //     // callback(new Error('确定发展对象时间早于确定积极分子时间'));
        //     return Promise.reject('确定发展对象时间早于确定积极分子时间')

        // } else {
        //     setShowIsFullYear(true)
        //     // callback();
        //     return Promise.resolve()
        // }
        // callback();
      
    }
    const timeCompare = async (e) => {
        const { code = 500, data = true } = await compare({
            data: {
                code: dataInfo.code,
                type: 3,
                time: moment(e).valueOf()
            }
        });
        if (code === 0) {
            setIsFullYear(data)
        }
    }
    const renderNode = () => {
        switch (pathname) {
            case '/developMem/zy/apply':
                return (
                    <Row>
                        <Col span={8}>
                            <Form.Item
                                name="hasStaffOrganization"
                                label="联系人是否为本组织人员"
                                labelCol={
                                    { span: 14 }
                                }
                                wrapperCol={
                                    { span: 4 }
                                }
                                rules={[{ required: true, message: '请输入联系人是否为本组织人员' }]}
                            >
                                <Switch
                                    checkedChildren="是"
                                    unCheckedChildren="否"
                                    onChange={(e)=>hasMemOnChange(e,'apply')}
                                    checked={hasMemValue}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            {
                                hasMemValue ?
                                    <Form.Item
                                        name="toactiveContextPerson"
                                        label="入党积极分子培养联系人"
                                        {...formItemLayout2}
                                        rules={[{ required: true, message: '请输入入党积极分子培养联系人' }]}
                                    >
                                        <MemSelect checkType={'checkbox'} placeholder="请选择" />
                                    </Form.Item>
                                    :
                                    <Form.Item
                                        name="toactiveContextPerson"
                                        label="入党积极分子培养联系人"
                                        {...formItemLayout2}
                                        rules={[{ required: true, message: '请输入入党积极分子培养联系人' }]}
                                    >
                                        <Input placeholder="请选择" />
                                    </Form.Item>
                            }

                        </Col>
                        <Col span={8}>
                            <Form.Item
                                name="activeDate"
                                label="确定积极分子时间"
                                {...formItemLayout2}
                                rules={[{ required: true, message: '请输入确定积极分子时间' }, { validator: timeValidator3 }]}
                            >
                                <Date disabledDate={disabledTomorrow} />
                            </Form.Item>
                        </Col>

                    </Row>
                );
            case '/developMem/zy/active':
                return (
                    <Row>
                        <Col span={5}>
                            <Form.Item
                                name="hasStaffOrganization"
                                label="联系人是否为本组织人员"
                                labelCol={
                                    { span: 14 }
                                }
                                wrapperCol={
                                    { span: 4 }
                                }
                                rules={[{ required: true, message: '请输入联系人是否为本组织人员' }]}
                            >
                                <Switch
                                    checkedChildren="是"
                                    unCheckedChildren="否"
                                    onChange={(e)=>hasMemOnChange(e,'active')}
                                    checked={hasMemValue}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            {
                                hasMemValue ?
                                    <Form.Item
                                        name="toobjContextMem"
                                        label="培养联系人"
                                        {...formItemLayout2}
                                        rules={[{ required: true, message: '请输入培养联系人' }]}
                                    >
                                        <MemSelect initValue={toobjContextMem} checkType={'checkbox'} placeholder="请选择" />
                                    </Form.Item>
                                    :
                                    <Form.Item
                                        name="toobjContextMem"
                                        label="培养联系人"
                                        {...formItemLayout2}
                                        rules={[{ required: true, message: '请输入培养联系人' }]}
                                    >
                                        <Input  placeholder="请选择" />
                                    </Form.Item>
                            }

                        </Col>
                        <Col span={6}>
                            <Form.Item
                                name="objectDate"
                                label="确定发展对象时间"
                                {...formItemLayout2}
                                rules={[{ required: true, message: '请输入确定发展对象时间' }, { validator: timeValidator }]}
                            >
                                <Date disabledDate={disabledTomorrow1} onChange={e => {
                                    timeCompare(e)
                                }} />
                            </Form.Item>
                        </Col>
                        {(!isFullYear) && showIsFullYear && <Col span={6}><Form.Item
                            name="instructions"
                            label="确定积极分子不满一年说明"
                            {...formItemLayout2}
                            rules={[{ required: true, message: '确定积极分子不满一年说明' }]}
                        >
                            <TextArea rows={2} />
                        </Form.Item></Col>}
                    </Row>
                );
        }
    }
    useEffect(() => {
        form.setFieldsValue({ hasStaffOrganization: true })
        if (dataInfo.processNode.startsWith('JJ')) {
            getReferences(dataInfo)
        }
        // if (dataInfo.processNode.startsWith('YBQ')) {
        //     getData()
        // }
    }, [dataInfo]);


    return (
        <div style={{ paddingTop: '2px' }}>
            <Form form={form} onFinish={hadndleFinish}  {...formItemLayout}>
                {
                    renderNode()
                }
            </Form>
        </div>


    );
};
// @ts-ignore
// export default React.forwardRef(index);
export default connect(({ commonDict,memDevelop }: any) => ({ commonDict,memDevelop }), undefined, undefined, { forwardRef: true })(React.forwardRef(index))
