/**
 * 列表
 */
import React, { Fragment } from 'react';
import ListTable from '../ListTable';
import {connect} from "dva";
import RuiFilter from "../RuiFilter";
import WhiteSpace from '../WhiteSpace';
import _isNumber from 'lodash/isNumber';
import moment from 'moment';
import { Divider, Popconfirm } from 'antd';

@connect(({memSelect,commonDict,loading})=>({memSelect,commonDict,loading:loading.effects['memSelect/getListHistory']}))
export default class index extends React.Component<any,any>{
  static action(params?:object){}
  constructor(props){
    super(props);
    const {selectedRows=[]} = props;
    let keys = selectedRows.map((item,index)=>{
      return item.code;
    })
    this.state={
      code:'',
      search:{},
      selectedRowKeys: keys || [],
      selectedItems: selectedRows || [],
    };
    index.action=this.action;
  }
  componentDidMount(): void {
    const dictData=['dict_d12','dict_d50','dict_d51'];
    for(let obj of dictData){
      this.props.dispatch({
        type:'commonDict/getDict',
        payload:{
          data:{
            dicName:obj
          }
        }
      });
    }
    this.action();
  }
  action=(params?:object)=>{
    const {orgCode,searchType=1,isFee,subordinate}=this.props;
    const {search}=this.state;
    this.props.dispatch({
      type:'memSelect/getListHistory',
      payload:{
        data:{
          subordinate,
          pageNum:1,
          pageSize:10,
          searchType,
          memOrgCode:orgCode,
          ...search,
          ...params,
          isFee
        }
      }
    })
  };
  filterChange=(val)=>{
    const {pagination={}}=this.props.memSelect;
    const {current,pageSize}=pagination;
    this.action({pageNum:current, pageSize,...val});
    this.setState({
      search:val
    });
  };
  onPageChange=(page,pageSize)=>{
    this.action({pageNum:page,pageSize});
  };
  onSelectChange=(selectedRowKeys,record)=>{
    const {onChange}=this.props;
    this.setState({
      selectedRowKeys,selectedItems:record,
    });
    onChange && onChange(record);
  };
  render(){
    const {list,pagination={}}=this.props.memSelect;
    // const {current,pageSize}=pagination;
    const {loading,commonDict,checkType}=this.props;
    const {selectedRowKeys}=this.state;
    const columns = [
      {
        title: '姓名',
        dataIndex: 'name',
        width: 100,
      },
      // {
      //   title: '性别',
      //   dataIndex: 'sexCode',
      //   width: 100,
      //   render: (text) => {
      //     return <span> {text === '1' ? '男' : '女'} </span>;
      //   },
      // },
      {
        title: '离开时所在组织',
        dataIndex: 'orgName',
        width: 260,
      },
      {
        title: '离开类型',
        width: 100,
        dataIndex: 'd12Name',
      },
      {
        title: '出党原因',
        width: 120,
        dataIndex: 'd50Name',
      },
      // {
      //   title: '职务级别',
      //   width: 160,
      //   dataIndex: 'd51Name',
      // },
      // {
      //   title: '离开时间',
      //   width: 160,
      //   dataIndex: 'leaveOrgDate',
      //   render: (text, record) => {
      //     return (
      //       <div>
      //         {/* {!_isEmpty(text) ? moment(text).format('YYYY-MM-DD') : ''} */}
      //         {_isNumber(text) ? moment(text).format('YYYY-MM-DD') : ''}
      //       </div>
      //     );
      //   },
      // },
    ];

    const filterData = [
      {
        key: 'd12CodeList',
        name: '离开类型',
        value: commonDict['dict_d12_tree'],
      },
      {
        key: 'd50CodeList',
        name: '出党原因',
        value: commonDict['dict_d50_tree'],
      },
      {
        key: 'd51CodeList',
        name: '职务级别',
        value: commonDict['dict_d51_tree'],
      },
    ];
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
      type:checkType ? checkType : 'radio',
      // onSelect: this.onSelect,
      // hideDefaultSelections: true,
    };
    return(
      <React.Fragment>
        <RuiFilter data={filterData} onChange={this.filterChange}/>
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable
          rowSelection={rowSelection}
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={this.onPageChange}
          scroll={{y:254}}
          rowKey={record=>record['code']}
        />
      </React.Fragment>
    )
  }
}
