import React, { useState } from "react";
import { Form, Input, Button, Descriptions } from "antd"
import Tip from '@/components/Tip';


import { getFlowUniqueCode } from "../../../services/index"

const index = () => {
    const [form] = Form.useForm();
    const [formData, setFormData] = useState<any>({});


    const onFinish = async (values) => {
        console.log(values);
        const { code, data } = await getFlowUniqueCode({ ...values })
        if (code == 0) {
            console.log(data);
            setFormData(data)
        }
    }
    return (
        <div>
            <Form layout={'inline'} onFinish={onFinish} form={form}>
                <Form.Item label="流动党员省内唯一标识" name="flowCode"
                    rules={[
                        { required: true, message: '请输入' }
                    ]}
                >
                    <Input style={{ width: '300px' }} placeholder={'请输入'} />
                </Form.Item>
                <Form.Item label="流动党员全国唯一标识" name="flowUqCode"
                    rules={[
                        { required: false, message: '请输入' }
                    ]}
                >
                    <Input style={{ width: '300px' }} placeholder={'请输入'} />
                </Form.Item>
                <Form.Item>
                    <Button type={'primary'} onClick={form.submit}>
                        查询
                    </Button>
                </Form.Item>
            </Form>
            <div style={{ width: '100%', marginTop: '20px' }}>
                <Descriptions column={1} title="基本信息">
                    <Descriptions.Item span={1} label="姓名">{formData?.name}</Descriptions.Item>
                    <Descriptions.Item span={1} label="省交换区状态">{formData?.sheng}</Descriptions.Item>
                    <Descriptions.Item span={1} label="全国交换区状态">{formData?.qg}</Descriptions.Item>
                    <Descriptions.Item span={1} label="是否已经下载到接收记录">{formData?.downloadReceive}</Descriptions.Item>
                </Descriptions>
            </div>
        </div>
    )
}

export default index;