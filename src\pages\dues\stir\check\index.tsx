/**
 * 新增编辑收支
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Progress, InputNumber, message, Modal, Button } from 'antd';
import {connect} from "dva";
import moment from 'moment'
import Notice from '@/components/Notice';
import { getSession } from '@/utils/session';
// import 'react-sliding-pane/dist/react-sliding-pane.css';
import { isEmpty } from '@/utils/method';
import styles from './index.less'

@connect(({dues,login})=>({dues,login}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      queryDate:moment().format('YYYY-MM-DD'),
      stand:'1',
      months:1,
      url:'',
      editVisible:false,
      selectedRowKeys:[],
      isFirst:'1',
      selections:[],
      progress:0
    };
  }
  showModal=()=>{
    let org=getSession('org')|| {};
    let { progress }=this.state;
    this.setState({
      visible:true,
      org,
      progress
    });
  };

  handleOk=()=>{
    const { selectedRowKeys } = this.state;
    if (isEmpty(selectedRowKeys)) {
      message.info('请选择流水');
    }else {
      this.setState({
        editVisible:true,
      })
    }
  };
  handleCancel=()=>{
    const { onChange } = this.props;
    onChange();
    this.setState({
      visible:false,
      queryDate:moment().format('YYYY-MM-DD'),
    });
  };




  render(){
    const {visible,filterHeight,editVisible,selectedRowKeys,selections,progress}=this.state;
    const {title='', data={},dues:{ list1=[],pagination1:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={} }={},loading:{effects = {}} = {},children }=this.props;
    const {getFieldDecorator}=this.props.form;
    let option={
      sort:1,
      name:'aaa'
    };
    return(
      <div>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          destroyOnClose={false}
          title={false}
          visible={visible}
          closable={false}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
          footer={false}
          bodyStyle={{height:'auto',overflow:'auto'}}
        >
          <React.Fragment>
            <div style={{overflow:'auto'}}>
              <div style={{width: "100%"}}>
                <div className="module-body">
                  <div>
                    <div className="PE" >
                      <div className={styles.PEtit}>
                        {/*校验效果*/}
                        <div className={styles.radar}>
                          <div className={styles.container}>
                            <div className={styles.content}>
                              {
                                !this.state.isStart ?
                                  <div><div className="stop ball"/><div className="ball1 stop"/></div>
                                  :
                                  Math.ceil(this.state.progress) == 100 ?
                                    <div><div className="stop ball"/><div className="ball1 stop"/></div>
                                    :
                                    <div><div className="ball"/><div className="ball1"/></div>
                              }
                            </div>
                          </div>
                        </div>
                        {/*校验提示*/}
                        <div className={styles.PEtitRight}>
                          {
                            !this.state.isStart ?
                              <div className={styles.PEing}/>
                              :
                              Math.ceil(this.state.progress) == 100 ?
                                <div className={styles.PEing}/>
                                :
                                <div className={styles.PEing}>{isEmpty(option) ? '准备中...' : `正在校核 第${option.sort}项：${option.name}`}</div>
                          }
                          <div className={styles.PEprogress}>
                            <Progress style={{width:'95%'}} status="active" percent={Math.ceil(this.state.progress)} />
                          </div>
                        </div>
                        {/*校验按钮*/}
                        {/*<div className={styles.sure}>*/}
                          {/*{*/}
                            {/*!this.state.isStart ?*/}
                              {/*<Button type="primary" style={{marginTop:'50px'}} onClick={this.startCheck}>开始校核</Button>*/}
                              {/*:*/}
                              {/*Math.ceil(this.state.progress) == 100 ?*/}
                                {/*<div>*/}
                                  {/*<Button type="primary" style={{marginTop:'50px'}} onClick={this.reStartCheck}>重新校核</Button>*/}
                                {/*</div>*/}
                                {/*:*/}
                                {/*<Button type="primary" style={{marginTop:'50px'}} disabled>正在校核</Button>*/}
                          {/*}*/}
                        {/*</div>*/}
                      </div>
                    </div>
                    <div>
                      {/*校验项显示*/}

                    </div>
                    <div style={{marginTop:'2%'}}>.</div>
                  </div>
                </div>
              </div>
            </div>
          </React.Fragment>
        </Modal>

      </div>
    )
  }
}
export default Form.create()(index)
