import React from 'react';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Card, Avatar } from 'antd';
import style from './index.less';
interface Interface {
  info:object
}
export default class index extends React.Component<Interface, any> {
  constructor(props) {
    super(props);

  }
  render() {
    const {info} = this.props;
    return (
      <Card className={style.daibanCard}>
        <div className={style.avator}>
          <Avatar src={info['url']} size="large" icon={<LegacyIcon type={info['icon']} />} style={{ color: info['color'], background:info['background']}}/>
        </div>
        <div className={style.infos}>
          {info['text']}
          <div className={style.desc}>
            {info['time']}
          </div>
        </div>
      </Card>
    );
  }
}
