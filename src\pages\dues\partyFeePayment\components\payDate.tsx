/**
 * 批量设置起交时间
 * */

import React, { useState, useRef, useEffect, useImperativeHandle } from 'react';
import {
  Input,
  Select,
  Form,
  Modal,
  Tabs,
  Button,
  Divider,
  Popconfirm,
  Space,
  Radio,
  InputNumber,
  message,
} from 'antd';
import moment from 'moment';
import MemSelect from '@/components/MemSelect';
import DateTime from '@/components/Date';
import Tip from '@/components/Tip';
import { getSession } from '@/utils/session';
import ListTable from 'src/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _uniqBy from 'lodash/uniqBy';
import _isNumber from 'lodash/isNumber';
import _cloneDeep from 'lodash/cloneDeep';
import { listPayment, batchPayment } from '../../services';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const index = (props: any, ref: any) => {
  const memSelectRef = useRef<any>();
  const org: any = getSession('org') || {};
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [title, setTitle] = useState('批量设置起交时间');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [basinInfo, setBasinInfo] = useState<any>({});
  const [listLoading, setListLoading] = useState(false);
  const [timeKey, setTimeKey] = useState(+new Date());
  const dateRef1: any = useRef();
  const [listData, setListData]: any = useState([]);
  const [pagination, setPagination] = useState({ pageSize: 20, current: 1, total: 0 });
  const [selectMem, setSelectMem]: any = useState([]);

  useImperativeHandle(ref, () => ({
    open: (query?: any) => {
      setBasinInfo(query);
      setVisible(true);
    },
  }));
  const hadndleFinish = async (e) => {
    console.log('🚀 ~ e:', e);
  };
  const handleCancel = () => {
    setVisible(false);
    setBasinInfo({});
    setListData([]);
  };

  return (
    <Modal
      maskClosable={false}
      title={title}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={'800px'}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
      bodyStyle={{ padding: '20px', overflow: 'auto' }}
    >
      {/* <div style={{ color: '#faad14', paddingBottom: '20px' }}>
        注意：适用【批量添加党费交纳】时，需要满足党员党费标准从起交时间起全年是统一的一个标准，若存在不同月份不同标准的情况下不适用批量操作。
      </div>
      <div style={{ display: 'flex', justifyContent: 'space-between', paddingBottom: '20px' }}>
        <div>当前组织：{org?.name}</div>
        <div>时间：{moment().format('YYYY年MM月DD日')}</div>
      </div> */}
      <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
        <Form.Item label="党员姓名" name={'name'} rules={[{ required: true, message: '党员姓名' }]}>
          <MemSelect
            ref={memSelectRef}
            org={org}
            checkType="checkbox"
            // selectedRows={selectMem}
            placeholder="请选择"
            // onChange={(e) => {
            //   if (!_isEmpty(e)) {
            //     setSelectMem(e);
            //     getList(e);
            //   } else {
            //     setSelectMem([]);
            //     setListData([]);
            //   }
            // }}
          />
        </Form.Item>
        <Form.Item
          label="交费时间"
          name={'lastPayDate'}
          rules={[{ required: true, message: '交费时间' }]}
        >
          <DateTime></DateTime>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default React.forwardRef(index);
