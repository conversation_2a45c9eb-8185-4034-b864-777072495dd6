.page {
  width: 100%;
  height: 100vh;
  //display: flex;
  //justify-content: center;
  //align-items: center;
  background: url('../../assets/login.jpg') no-repeat center;
  background-size: 100% 100%;
  overflow: hidden;

  .lin {
    background: red;
    width: 4px;
    border-radius: 4px;
    height: 26px;
    vertical-align: middle;
    display: inline-block;
    margin: 0px 6px 0px 2px;
    position: relative;
    top: -2px;
  }

  .center {
    margin: 0 auto;
    width: 770px;
    height: 566px;
    background: url('../../assets/loginC.png') no-repeat center;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    // position: relative;
    // right: 8%;
    // top: -2%;
    :global {
      .ant-input-affix-wrapper {
        background-color: rgba(0, 0, 0, 0.00001) !important;
      }

      .ant-form-item {
        margin-bottom: 50px;
        border-bottom: 1px solid #d9d9d9;
      }

      .ant-form-item-explain {
        position: absolute;
        top: 32px;
        left: 0;
      }

      .ant-input-affix-wrapper {
        border: none;
        //border-bottom: 1px solid #d9d9d9;
      }

      .ant-select-selector {
        border: none !important;
      }

      .ant-form-item-required {
        font-weight: 500;
        font-size: 16px;
        white-space: pre;

        &::before {
          display: none !important;
        }

        img {
          margin-right: 8px;
          position: relative;
          top: -2px;
        }
      }

      .ant-input-affix-wrapper-focused {
        box-shadow: unset;
      }

      .ant-input {
        border: unset;
        outline: unset;
        box-shadow: unset;
      }

      .ant-input:hover {
        border: unset;
        outline: unset;
        box-shadow: unset;
      }

      .ant-btn-dangerous.ant-btn-primary {
        background: #ED2C25;
      }
    }
  }

  .numberCode {
    position: absolute;
    top: -4px;
    right: 0;
    z-index: 99;
  }

  .login {
    width: 380px;
    margin-top: -50px;
    // margin: 0 auto;
    // position: absolute;
    // top: 46%;
    // right: -10%;
    // transform: translate(-50%,-60%);
    white-space: pre;

    //position: absolute;
    //top: 0;
    //left: 0;
    //right: 0;
    //bottom: 0;
    .headers {
      width: 100%;
      height: 36px;
      font-size: 24px;
      font-weight: 600;
      color: rgba(34, 34, 34, 1);
      line-height: 36px;
      text-align: center;
      margin-bottom: 50px;
    }

    .sub {
      :global {
        .ant-btn {
          border-color: #ffb23a !important;
        }

        .ant-btn:hover,
        .ant-btn:focus {
          border-color: #ffb23a !important;
        }
      }
    }

    .loginBtn {
      width: 306px;
      height: 46px;
      background: linear-gradient(90deg, rgba(255, 178, 58, 1) 0%, rgba(255, 0, 0, 1) 100%);
      box-shadow: 0px 2px 7px 3px rgba(255, 129, 129, 0.5);
      border-radius: 25px;
      font-size: 21px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      line-height: 29px;
    }

    .yzm {
      height: 32px;
      position: relative;
      top: 4px;

      &:hover {
        cursor: pointer;
      }
    }
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 60px auto;
    .icon {
      width: 62px;
      height: 62px;
      background: url('../../assets/icon.png') no-repeat center;
      background-size: 100% 100%;
      margin-right: 20px;
    }
    .top {
      width: 753px;
      height: 62px;
      background: url('../../assets/login/title.png') no-repeat center;
      background-size: 100% 100%;
    }
  }
}
