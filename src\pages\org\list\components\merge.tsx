import React, { useImperative<PERSON>andle, useState } from 'react';
import { But<PERSON>, Divider, Form, Input, Modal, Popconfirm } from 'antd';
import OrgSelect from '@/components/OrgSelect';
import ListTable from '@/components/ListTable';
import Tip from '@/components/Tip';
import {combined} from '../../services/org';
function merge(props,ref) {
  // @ts-ignore
  let {form}=Form.useForm();
  const [visible,setVisible]=useState(false);
  const [edit,setEdit]=useState({});
  const [list,setList]:any=useState([]);

  useImperativeHandle(ref, () => ({
    open: (val: any) => {
      setEdit(val);
      setVisible(true);
    },
  }));
  const onCancel=()=>{
    setVisible(false);
    setList([]);
  }
  const onOk=async ()=>{
    const {callBack}=props;
    let codes:any=[];
    list.forEach(obj=>{
      codes.push(obj['code'])
    })
    if(codes.length>0){
      await combined({
        data:{
          code:edit['code'],
          codes,
        }
      }).then(res=>{

        if(res['code']==0){
          Tip.success('操作提示','合并成功');
          onCancel();
          callBack && callBack();
        }
      })
    }
  }
  const columns=[
    {
      title:'序号',
      dataIndex:'num',
      width:58,
      render:(text,record,index)=>{
        return index+1
      }
    },
    {
      title:'组织名称',
      dataIndex:'name',
      width:270,
    },
    {
      title:'组织类别',
      width:100,
      dataIndex:'d01Name',
    },
    {
      title:'隶属关系',
      width:160,
      dataIndex:'d03Name',
    },
    {
      title:'联系人',
      width:80,
      dataIndex:'contacter',
    },
    {
      title:'联系方式',
      width:110,
      dataIndex:'contactPhone',
    },
    {
      title:'党组织书记',
      width:100,
      dataIndex:'secretary',
    },
    {
      title:'操作',
      dataIndex:'action',
      width:60,
      render:(text,record)=>{
        return(
          <Popconfirm title="确定要删除吗？" onConfirm={()=>{
            let filter = list.filter(obj=>obj['code']!=record['code']);
            setList([...filter])
          }}>
            <a className={'del'}>删除</a>
          </Popconfirm>
        )
      },
    },
  ];
  return(
    <Modal
      destroyOnClose
      title={'组织合并'}
      visible={visible}
      onOk={onOk}
      onCancel={onCancel}
      width={1000}
      bodyStyle={{maxHeight:'80vh',overflow:'auto'}}
    >
      <div style={{textAlign:'right',marginBottom:10}}>
        <OrgSelect multiple={true} onChange={(data)=>{
          setList(data)
        }}
        onSelectChange={(data)=>{
          setList(data)
        }}
        initValue={list}
        >
          <Button type={'primary'}>新增合并组织</Button>
        </OrgSelect>
      </div>
      <ListTable columns={columns} data={list} pagination={false}/>
    </Modal>
  )
}
export default React.forwardRef(merge)
