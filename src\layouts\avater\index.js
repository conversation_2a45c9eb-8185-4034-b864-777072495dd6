import React from 'react'
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Badge, Dropdown, Menu } from 'antd';
import {isEmpty} from 'src/utils/method';
import styles from './index.less';
export default class index extends  React.Component {
  // 用户菜单
  getUserMenu = () => {
    const {userMenuItems, userMenuOnclick} = this.props;
    return (
      <Menu onClick={userMenuOnclick}>
        {
          !isEmpty(userMenuItems) && userMenuItems.map((item)=><Menu.Item key={item.key}>{item.icon}{item.name}</Menu.Item>)
        }
      </Menu>
    );
  };
  render() {
    return (
      <div style={{display:'inline-block',float:'right'}}>
        <Dropdown
          // visible={this.state.dropdown}
          overlay={this.getUserMenu()}
          // onVisibleChange={this.onDropdownChange}
          placement={'bottomLeft'}
          overlayClassName={styles.down}
        >
          <span>
            {/* <Badge count={1}> */}
              <Avatar icon={<UserOutlined />} size={'large'}/>
              {/* </Badge> */}
          </span>
        </Dropdown>
      </div>
    );
  }
}
