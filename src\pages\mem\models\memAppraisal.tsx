import modelExtend from 'dva-model-extend';
import { listPageModel } from 'src/utils/common-model';
import {
   
    personSave,
    personUpdate,
    personRemove,
    personList,
    year
} from '../services/memAppraisal';
import { getSession } from 'src/utils/session';
import { changeListPayQuery } from '@/utils/method.js';

const org = modelExtend(listPageModel, {
    namespace: "memAppraisal",
    state: {
    },
    subscriptions: {
        setup({ dispatch, history }) {
            history.listen(location => {
                const { pathname, query } = location;
                // if (pathname === '/mem/manage') {
                //     const org = getSession('org') || {};
                //     const defaultParas = {
                //         pageNum: 1,
                //         pageSize: 10,
                //     };
                //     const dictData = ['dict_d01', 'dict_d02', 'dict_d03'];
                //     for (let obj of dictData) {
                //         dispatch({
                //             type: 'commonDict/getDictTree',
                //             payload: {
                //                 data: {
                //                     dicName: obj
                //                 }
                //             }
                //         });
                //     }
                // }
            });
        }
    },
    effects: {

        //民主评议
        *personSave({ payload }, { call, put }) {
            const info = yield call(personSave, payload);
            return Promise.resolve(info);
        },
        *personUpdate({ payload }, { call, put }) {
            const info = yield call(personUpdate, payload);
            return Promise.resolve(info);
        },
        *personRemove({ payload }, { call, put }) {
            const info = yield call(personRemove, payload);
            return Promise.resolve(info);
        },
        
        *year({ payload }, { call, put }) {
            const info = yield call(year, payload);
            return Promise.resolve(info);
        },
        *personList({ payload }, { call, put }) {
            const { data: { list = [], ...pagination } = {} } = yield call(personList, payload);
            yield put({
                type: 'updateState',
                payload: {
                    list: list || [],
                    pagination: pagination
                }
            });
        },
    }
});
export default org;
