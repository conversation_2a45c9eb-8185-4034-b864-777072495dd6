/**
 * 模块名
 */

function getSession(name='') {
  const storage=sessionStorage.getItem(name);
  let obj=undefined;
  if(storage){
    try {
      obj=JSON.parse(storage);
    } catch (e) {
      console.log(`${name} JSON转换异常`)
    }
  }
  return obj;
}
function getLocalSession(name='') {
  const storage=localStorage.getItem(name);
  let obj=undefined;
  if(storage){
    try {
      obj=JSON.parse(storage);
    } catch (e) {
      console.log(`${name} JSON转换异常`)
    }
  }
  return obj;
}
export {getSession,getLocalSession}
