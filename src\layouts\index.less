.header {
  //padding-left: 10px !important;
  :global {
    .ant-menu {
      width: calc(~'100% - 280px');
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
@pageHight:calc(100vh - 65px);
.Layout {
  height: @pageHight !important;
  :global {
    .splitter-layout {
      position: absolute;
      display: flex;
      flex-direction: row;
      width: 100%;
      height: @pageHight;
      overflow: hidden;
    }

    .splitter-layout .layout-pane {
      position: relative;
      flex: 0 0 auto;
      overflow: hidden;
    }

    .splitter-layout .layout-pane.layout-pane-primary {
      flex: 1 1 auto;
    }

    .splitter-layout > .layout-splitter {
      flex: 0 0 auto;
      min-height: @pageHight;
      width: 4px;
      height: 100%;
      cursor: col-resize;
      background-color: #f5f5f5;
    }

    .splitter-layout .layout-splitter:hover {
      background-color: #bbb;
    }

    .splitter-layout.layout-changing {
      cursor: col-resize;
    }

    .splitter-layout.layout-changing > .layout-splitter {
      background-color: #aaa;
    }

    .splitter-layout.splitter-layout-vertical {
      flex-direction: column;
    }

    .splitter-layout.splitter-layout-vertical.layout-changing {
      cursor: row-resize;
    }

    .splitter-layout.splitter-layout-vertical > .layout-splitter {
      width: 100%;
      height: 4px;
      cursor: row-resize;
    }
  }
}

.logoDiv {
  width: 230px;
  display: inline-block;
  text-align: center;
  color: #a6adb4;
  font-size: 16px;
  position: relative;
  top: -25px;
  > img {
    //width: 44px;
    //margin-right: 4px;
    position: relative;
    left: -20px;
  }
}
.logoDiv:hover {
  cursor: pointer;
}
.fake {
  height: 200px;
  width: 200px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -100px;
  margin-top: -100px;
}
