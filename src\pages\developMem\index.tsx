import React,{Fragment} from 'react';
import { <PERSON>, <PERSON>, <PERSON>, Divider, DatePicker, Button } from 'antd';
import style from './index.less';
import moment from 'moment';
import NowOrg from '@/components/NowOrg';
import CardsGroup from '@/components/CardsGroup';
import {cardConfig,chartConfig} from './developOverviewConfig';
import _isEmpty from 'lodash/isEmpty';
const {RangePicker} = DatePicker;
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state ={
      startDate:undefined,
      endDate:undefined,
    }
  }

  RangePickerChange=(val)=>{
    const [start,end] = val;
    if(!_isEmpty(start) && !_isEmpty(end)){
      this.setState({
        startDate:moment(start,'YYYY-MM-DD').valueOf(),
        endDate:moment(end,'YYYY-MM-DD').valueOf()
      })
    }else {
      this.setState({
        startDate:undefined,
        endDate:undefined,
      })
    }
  };
  cardsEdit=()=>{
    this['developCardsGroup'].open();
  };
  render() {
    const {startDate,endDate} = this.state;

    return (
      <div className={style.main}>
        <Row>
          <NowOrg
            extra={
              <Fragment>
                {/* <RangePicker
                  onChange={this.RangePickerChange}
                  ranges={{
                    '当天': [moment(), moment()],
                    '当月': [moment().startOf('month'), moment().endOf('month')],
                    '本季度':[moment(moment().format('YYYY')).quarter(moment().quarter()).startOf('day'),moment(moment().format('YYYY')).quarter(moment().quarter()).endOf('quarter')]
                  }}
                /> */}
                <Button htmlType={'button'} style={{marginLeft:'5px'}} onClick={this.cardsEdit} type={'primary'}>编辑</Button>
              </Fragment>
            }
          />
        </Row>

        <CardsGroup timeRange={{startDate,endDate}} cardParent={'developOverview'} ref={e=>this['developCardsGroup'] = e} cardConfig={cardConfig} chartConfig={chartConfig}/>

      </div>
    );
  }
}
