/**
 * 列表
 */
import React from 'react';
import ListTable from '../ListTable';
import {connect} from "dva";
import RuiFilter from "../RuiFilter";
import WhiteSpace from '../WhiteSpace'
import {Input} from "antd";
import styles from './index.less';
import { selfRowSelectionSate } from '@/utils/method.js';
import { root, rootParent } from '@/common/config';

const Search=Input.Search;
// @ts-ignore
@connect(({orgSelect,commonDict,loading})=>({orgSelect,commonDict,loading:loading.effects['orgSelect/getList']}))
export default class index extends React.Component<any,any>{
  static action(params?:object){}
  constructor(props){
    super(props);
    const { selectedRows=[] } = props;
    let keys = selectedRows.map((item,index)=>{
      return item?.code;
    })
    this.state={
      code:'',
      search:{},
      selectedRowKeys: keys || [],
      selectedItems: selectedRows || [],
    };
    index.action=this.action;
  }
  componentDidMount(): void {
    const dictData=['dict_d01','dict_d02','dict_d03',];
    for(let obj of dictData){
      this.props.dispatch({
        type:'commonDict/getDict',
        payload:{
          data:{
            dicName:obj
          }
        }
      });
    }
    this.action({pageNum:1});
  }
  static getDerivedStateFromProps(props,state){
    const {value=[]}=props;
    if(value.length>0){
      let selectedRowKeys:any=[]
      value.forEach(obj=>{
        selectedRowKeys.push(obj['code'])
      })
      return {selectedRowKeys}
    }
    return null;
  };
  action=(params?:object)=>{
    const {orgCode,orgTypeList=undefined,exclude,isPermissionCheck,subordinate, otherListQuery={}}=this.props;
    const {search,filter}=this.state;
    const {pagination={}}=this.props.orgSelect;
    const {current=1,pageSize=10}=pagination;
    // if(orgTypeList && filter){//如果限定只能选择组织类别 删除 d01CodeList
    //   delete filter['d01CodeList']
    // }

    this.props.dispatch({
      type:'orgSelect/getList',
      payload:{
        data:{
          orgCode,
          subordinate,
          isPermissionCheck,//是否权限检验,0--不进行校验,1--进行权限校验,为空默认进行权限检验
          excludeOrgCodeList:exclude || [],//排除的组织层级码集合
          pageNum:current,
          pageSize:pageSize,
          ...search,
          ...filter,
          ...params,
          orgTypeList,
          ...otherListQuery
        }
      }
    })
  };
  filterChange=(val)=>{
    this.setState({
      filter:val
    },()=>this.action({pageNum:1}));
  };
  onPageChange=(page,pageSize)=>{
    this.action({pageNum:page,pageSize});
  };
  onSelectChange=(selectedRowKeys,record)=>{
    const {onChange}=this.props;
    this.setState({
      selectedRowKeys,selectedItems:record,
    });
    onChange && onChange(record);
  };
  search=(val)=>{
    this.setState({
      search:{orgName:val}
    },()=>this.action({pageNum:1}));
  };
  searchClear=(e)=>{
    if(!e.target.value){
      this.setState({
        search:{orgName:undefined}
      },()=>this.action({pageNum:1}));
    }
  };
  render(){
    const {list,pagination={}}=this.props.orgSelect;
    const {current,pageSize}=pagination;
    const {loading,org,multiple=false, showFilterData=true}=this.props;
    const {selectedRowKeys}=this.state;
    const columns=[
      // {
      //   title:'序号',
      //   dataIndex:'num',
      //   render:(text,record,index)=>{
      //     return (current-1)*pageSize+index+1
      //   }
      // },
      {
        title:'组织名称',
        dataIndex:'name',
      },
      {
        title:'组织类别',
        dataIndex:'d01Name',
        width:150,
      },
      {
        title:'隶属关系',
        dataIndex:'d03Name',
        width:120,
      },
      {
        title:'联系人',
        width:70,
        dataIndex:'contacter',
      },
      {
        title:'联系方式',
        width:110,
        dataIndex:'contactPhone',
      },
      {
        title:'党组织书记',
        width:100,
        dataIndex:'secretary',
      },
    ];
    // orgTypeList传过来的参数，就是比如6 63 631 632 这种，然后就可以把搜索的选择筛出来
    // let d01CodeListValue =this.props.orgTypeList ? jsonToTree(this.props.commonDict[`dict_d01`].reduce((arr,it)=> {
    //   if( this.props.orgTypeList.includes(it.key)){
    //     const {children,...others} = it;
    //     return [...arr, others]
    //   }else{
    //     return arr
    //   }
    // },[]),'parent','key') : this.props.commonDict[`dict_d01_tree`];

    const filterData=[
      {
        key:'d01CodeList',name:'组织类别',value:this.props.commonDict[`dict_d01_tree`],
      },
      {
        key:'d03CodeList',name:'隶属关系',value:this.props.commonDict[`dict_d03_tree`],
      },
      {
        key:'d02CodeList',name:'单位情况',value:this.props.commonDict[`dict_d02_tree`],
      },
    ];
    const rowSelection = {
      selectedRowKeys,
      // onChange: this.onSelectChange,
      type: multiple ? 'checkbox' : 'radio',
      onSelect: (record, selected) => {
        const {onChange}=this.props;
        let records:any = [];
        if (selected) {
          this.setState({
            selectedRowKeys:!multiple ? [record['code']] : [...new Set([...this.state.selectedRowKeys, record['code']])],
            selectedItems:!multiple ? [record] : [...new Set([...this.state.selectedItems, record])]
          });
          records = !multiple ? [record] :  [...new Set([...this.state.selectedItems, record])]
        }else {
          this.setState({
            selectedRowKeys: this.state.selectedRowKeys.filter((it) => it !== record['code']),
            selectedItems: this.state.selectedItems.filter((it) => it['code'] !== record['code'])
          })
          records = this.state.selectedItems.filter((it) => it['code'] !== record['code'])
        }
        onChange && onChange(records);
      },
      onSelectAll: (selected, selectedRows, changeRows) => {
        const {onChange}=this.props;
        let records:any = [];
        if (selected) {
          this.setState({
            selectedRowKeys:[ ...new Set([...this.state.selectedRowKeys, ...changeRows.map((it) => it['code'])]),],
            selectedItems: [...new Set([...this.state.selectedItems, ...changeRows])],
          });
          records = [...new Set([...this.state.selectedItems, ...changeRows])];
        }else{
          let arr = [...this.state.selectedItems];
          changeRows.map((it) => {
            arr = arr.filter((ot) => ot !== it['code']);
          });
          let arr2 = [...this.state.selectedItems];
          changeRows.map((it) => {
            arr2 = arr2.filter((ot) => ot['code'] !== it['code']);
          });
          this.setState({
            selectedRowKeys:arr,
            selectedItems:arr2,
          });
          records = arr2;
        }
        onChange && onChange(records);
      },
    };
    return(
      <React.Fragment>
        <div className={styles.search}>
          <div>
            <Search style={{width:200,marginLeft:16}} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'}/>
          </div>
          <div style={{textAlign:'left',float:'left',fontSize:20}}>{org['name'] || org['managerOrgName']}</div>
        </div>
        <WhiteSpace/>
        <WhiteSpace/>
        {showFilterData && <RuiFilter data={filterData} onChange={this.filterChange}/>}
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable
          rowSelection={rowSelection}
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={this.onPageChange}
          scroll={{y:'42vh'}}
          rowKey={'code'}
        />
      </React.Fragment>
    )
  }
}
