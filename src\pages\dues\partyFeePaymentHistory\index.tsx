/**
 * 历史党员党费交纳-基本信息
 * */
import React, { useState, useRef, useEffect } from 'react';
import { Input, Select, Form, Modal, Tabs, Button, Divider, Popconfirm, Space } from 'antd';
import moment from 'moment';
import ListTable from 'src/components/ListTable';
import NowOrg from '@/components/NowOrg';
import Standard from '../partyFeePayment/components/standard';
//  import StandardBatch from './components/standardBatch';
import Pay from '../partyFeePayment/components/pay';
//  import PayBatch from './components/payBatch';
import SetTime from '../partyFeePayment/components/setTime';
import { historyPayment } from '@/pages/dues/services';
import { getSession } from '@/utils/session';

const TabPane = Tabs.TabPane;
const Search = Input.Search;
const index = (props: any) => {
  const org: any = getSession('org');
  const standardRef = useRef<any>();
  const standardBatchRef = useRef<any>();
  const payRef = useRef<any>();
  const payBatchRef = useRef<any>();
  const setTimeRef = useRef<any>();
  const [activeTab, setActiveTab] = useState('1');
  const [listLoading, setListLoading] = useState(false);
  const [listData, setListData] = useState([
    { code: '123', d158Name: 'derfr', activityName: 'free' },
  ]);
  const [pagination, setPagination] = useState({ pageSize: 20, current: 1, total: 0 });
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return (pagination.current - 1) * pagination.pageSize + index + 1;
      },
    },
    {
      title: '姓名',
      dataIndex: 'activityName',
      align: 'center',
      width: 60,
    },
    {
      title: '已转入组织',
      dataIndex: 'd158Name',
      align: 'left',
      width: 150,
    },
    {
      title: '转出时间',
      dataIndex: 'fv',
      align: 'left',
      width: 80,
      render: (text, record, index) => {
        if (text) {
          return moment(text).format('YYYY.MM');
        }
      },
    },
    {
      title: '一月',
      dataIndex: 'activityName',
      align: 'center',
      width: 100,
      render: (text, record, index) => {
        return (
          <div>
            <div>
              <a
                style={{ color: '#1890ff', whiteSpace: 'nowrap' }}
                onClick={() => {
                  standardRef.current.open();
                }}
              >
                未设置标准
              </a>
            </div>
            <div>
              <a
                style={{ color: '#1890ff', whiteSpace: 'nowrap' }}
                onClick={() => {
                  payRef.current.open();
                }}
              >
                添加交费记录
              </a>
            </div>
          </div>
        );
      },
    },
    {
      title: '二月',
      dataIndex: 'activityName',
      align: 'center',
      width: 100,
      render: (text, record, index) => {
        return (
          <div>
            <div>
              <a
                style={{ color: '#52c41a', whiteSpace: 'nowrap' }}
                onClick={() => {
                  standardRef.current.open({ code: '123' });
                }}
              >
                已设置标准
              </a>
            </div>
            <div>
              <a
                style={{ color: '#52c41a', whiteSpace: 'nowrap' }}
                onClick={() => {
                  payRef.current.open({ code: '123' });
                }}
              >
                已添加交费记录
              </a>
            </div>
          </div>
        );
      },
    },
    {
      title: '三月',
      dataIndex: 'activityName',
      align: 'center',
    },
    {
      title: '四月',
      dataIndex: 'activityName',
      align: 'center',
    },
    {
      title: '五月',
      dataIndex: 'activityName',
      align: 'center',
    },
    {
      title: '六月',
      dataIndex: 'activityName',
      align: 'center',
    },
    {
      title: '七月',
      dataIndex: 'activityName',
      align: 'center',
    },
    {
      title: '八月',
      dataIndex: 'activityName',
      align: 'center',
    },
    {
      title: '九月',
      dataIndex: 'activityName',
      align: 'center',
    },
    {
      title: '十月',
      dataIndex: 'activityName',
      align: 'center',
    },
    {
      title: '十一月',
      dataIndex: 'activityName',
      align: 'center',
    },
    {
      title: '十二月',
      dataIndex: 'activityName',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 100,
      render: (text, record) => {
        return (
          <div>
            <a
              style={{ color: '#52c41a', whiteSpace: 'nowrap' }}
              onClick={() => {
                setTimeRef.current.open(record);
              }}
            >
              请设置起交时间
            </a>
            <a
              style={{ whiteSpace: 'nowrap' }}
              onClick={() => {
                setTimeRef.current.open(record);
              }}
            >
              已设置起交时间
            </a>
          </div>
        );
      },
    },
  ];
  const getList = async (p?: any) => {
    const {
      code: resCode = 500,
      data: { list = [], pageNumber = 1, pageSize = 10, totalPage = 0 } = {},
    } = await historyPayment({
      data: {
        orgCode: org.orgCode,
      },
    });
    if (resCode === 0) {
      setListData(list);
      setPagination({ current: pageNumber, total: totalPage, pageSize });
    }
  };
  useEffect(() => {
    // getList();
  }, [org.code]);
  return (
    <div>
      <Tabs
        defaultActiveKey={activeTab}
        onChange={(e) => {
          setActiveTab(e);
        }}
      >
        <TabPane tab="基本信息" key="1" />
        {/* <TabPane tab="统计信息" key="2"/> */}
      </Tabs>
      <NowOrg
        extra={
          <Space>
            <Button onClick={() => {}}>导出</Button>
            <Search placeholder="请输入检索关键词" onSearch={(e) => {}} />
            <Select style={{ width: '90px' }} defaultValue="2022">
              <Select.Option value={'2018'}>2018&nbsp;&nbsp;</Select.Option>
              <Select.Option value={'2019'}>2019&nbsp;&nbsp;</Select.Option>
              <Select.Option value={'2020'}>2020&nbsp;&nbsp;</Select.Option>
              <Select.Option value={'2021'}>2021&nbsp;&nbsp;</Select.Option>
              <Select.Option value={'2022'}>2022&nbsp;&nbsp;</Select.Option>
              <Select.Option value={'all'}>全部&nbsp;&nbsp;</Select.Option>
            </Select>
          </Space>
        }
      />
      <ListTable
        rowKey={'code'}
        columns={columns}
        data={listData}
        pagination={pagination}
        onPageChange={(page, pageSize) => {
          getList({ pageNum: page, pageSize });
        }}
      />
      <Standard ref={standardRef} onOk={() => {}} />
      {/* <StandardBatch ref={standardBatchRef} onOk={() => {}} /> */}
      <Pay ref={payRef} onOk={() => {}} />
      {/* <PayBatch ref={payBatchRef} onOk={() => {}} /> */}
      <SetTime ref={setTimeRef} onOk={() => {}} />
    </div>
  );
};

export default index;
