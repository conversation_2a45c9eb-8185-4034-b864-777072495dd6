.box {
  width: 100%;
  display: flex;
  .icon {
    flex: 1;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
    cursor: pointer;
  }
}
// @pagehight: 500px;
.Layout {
  // height: @pagehight!important;
  height: 100%;
  :global {
    .splitter-layout {
      // position: absolute;
      display: flex;
      flex-direction: row;
      width: 100%;
      // height: @pagehight;
      height: 100%;
      overflow: hidden;
    }

    .splitter-layout .layout-pane {
      position: relative;
      flex: 0 0 auto;
      overflow: hidden;
    }

    .splitter-layout .layout-pane.layout-pane-primary {
      flex: 1;
      // flex: 1 1 auto;
    }

    .splitter-layout > .layout-splitter {
      flex: 0 0 auto;
      width: 4px;
      height: 100%;
      cursor: col-resize;
      background-color: #f5f5f5;
    }

    .splitter-layout .layout-splitter:hover {
      background-color: #bbb;
    }

    .splitter-layout.layout-changing {
      cursor: col-resize;
    }

    .splitter-layout.layout-changing > .layout-splitter {
      background-color: #aaa;
    }

    .splitter-layout.splitter-layout-vertical {
      flex-direction: column;
    }

    .splitter-layout.splitter-layout-vertical.layout-changing {
      cursor: row-resize;
    }

    .splitter-layout.splitter-layout-vertical > .layout-splitter {
      width: 100%;
      height: 4px;
      cursor: row-resize;
    }
  }
  .tree {
    height: 100%;
    overflow: auto;
  }
  .table {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding-left: 10px;
    overflow: auto;
    .infos {
      padding: 10px 0;
      max-height: 140px;
      overflow: auto;
      min-height: 44px;
    }
    .ListTable {
      :global {
        .ant-table-thead > .ant-checkbox-wrapper {
          display: none !important;
        }
      }
    }
  }
}
