/**
 * 添加党小组
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { DatePicker, Input, Modal } from "antd";
import moment from 'moment';
import Tip from '@/components/Tip';
import { formLabel } from '@/utils/method';
import Date from '@/components/Date';
const FormItem=Form.Item;

const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
class index extends React.Component<any,any> {
  static showModal(){};
  constructor(props){
    super(props);
    this.state={
      visible:false,
    };
    index.showModal=this.showModal;
  }
  showModal=()=>{
    this.setState({
      visible:true,
    });
  };

  handleOk=()=>{
    const {basicInfo={}}=this.props.org;
    const {children,title,dataInfo={}}=this.props;
    this.props.form.validateFieldsAndScroll(async(err, val) => {
      if (!err) {
        let obj=undefined;
        if(val['createDate']){
          val['createDate']=val['createDate'].valueOf();
        }
        if(dataInfo['code']){
          obj=await this.props.dispatch({
            type:'org/updateGroup',
            payload:{
              data:{
                ...dataInfo,
                ...val,
              }
            }
          });
        }else{
          obj=await this.props.dispatch({
            type:'org/addGroup',
            payload:{
              data:{
                ...val,
                groupOrgCode:basicInfo['orgCode'],
                zbCode:basicInfo['zbCode'],
                orgCode:basicInfo['code']
              }
            }
          });
        }

        if(obj && obj['code']===0){
          Tip.success('操作提示',dataInfo['code'] ? '修改成功' : '新增成功');
          this.props.queryList();
          this.handleCancel();
        }
      }
    });

  };
  handleCancel=()=>{
    this.setState({
      visible:false,
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const {children,title,dataInfo={},tipMsg={}}=this.props;
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        {
          this.state.visible &&
          <Modal
            title={ title || "请输入标题" }
            visible={this.state.visible}
            onOk={()=>this.handleOk()}
            onCancel={this.handleCancel}
            width={500}
            className='add_randp_modal'
            maskClosable={false}
            destroyOnClose
          >
            <Form {...formItemLayout1}>
              <FormItem
                label={ formLabel('党小组全称',tipMsg['name']) }
              >
                {getFieldDecorator('name', {
                  initialValue:dataInfo['name'],
                  rules: [{ required: true, message: '党小组全称' }],
                })(
                  <Input placeholder="党小组全称" />
                )}
              </FormItem>

              <FormItem
                label={ formLabel('批准日期',tipMsg['createDate']) }
              >
                {getFieldDecorator('createDate', {
                  initialValue:dataInfo['createDate'] ? moment(dataInfo['createDate']) : undefined,
                  rules: [{ required: false, message: '请选择批准日期' }],
                  // <DatePicker placeholder="请选择" style={{width:'100%'}}/>
                })(

                  <Date />
                )}
              </FormItem>

            </Form>
          </Modal>
        }

      </React.Fragment>
    )
  }
}
export default Form.create<any>({})(index);
