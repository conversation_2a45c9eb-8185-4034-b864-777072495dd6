import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { Input, Modal, Form, Row, Col } from 'antd';
import { getSession } from '@/utils/session';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import Date from '@/components/Date';
import Tip from '@/components/Tip';
import UnitSelect from '@/components/UnitSelect';
import { findDictCodeName } from '@/utils/method';
import { addParty, updateParty } from '../../services/org.js';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
function AddParty(props, ref) {
  const [form] = Form.useForm();
  const org: any = getSession('org') || {};
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [title, setTitle] = useState('新增党组(党组性质党委)');
  const [formInfo, setFormInfo] = useState({});
  useImperativeHandle(ref, () => ({
    open: (e) => {
      open(e);
    },
    close: () => {
      handleCancel();
    },
  }));
  const open = (formInfo) => {
    if (formInfo) {
      setTitle('编辑党组(党组性质党委)');
      setFormInfo(formInfo);
      form.setFieldsValue({
        ...formInfo,
      });
    }
    setVisible(true);
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setFormInfo({});
    setConfirmLoading(false);
    form.resetFields();
  };
  const handleOk = async (e) => {
    if (!_isEmpty(e['buildeTime'])) {
      e['buildeTime'] = e['buildeTime'].valueOf();
    }
    let val = findDictCodeName(['d108'], e, formInfo);
    if (typeof val['unitCode'] === 'object') {
      val['d35Code'] = val['unitCode'][0]['d35Code'];
      val['unitName'] = val['unitCode'][0]['name'];
      val['unitCode'] = val['unitCode'][0]['code'];
    } else {
      val['unitName'] = formInfo['unitName'];
    }
    const { onOk } = props;
    setConfirmLoading(true);
    let url = addParty;
    let dataInfo: any = {
      ...val,
      orgCode: org.code,
      zbCode: org.zbCode,
      partyOrgCode: org.orgCode,
    };
    if (!_isEmpty(formInfo)) {
      url = updateParty;
      dataInfo = { ...dataInfo, id: formInfo['id'], code: formInfo['code'] };
    }
    const res = ({} = await url({
      data: { ...dataInfo },
    }));
    setConfirmLoading(false);
    if (res.code === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOk && onOk();
    }
  };
  const handleCancel = () => {
    close();
  };
  return (
    <Modal
      title={title}
      destroyOnClose
      visible={visible}
      confirmLoading={confirmLoading}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={'1000px'}
    >
      <Form {...formItemLayout} form={form} onFinish={handleOk}>
        <Row>
          <Col span={12}>
            <Form.Item
              name="partyName"
              label="名称"
              rules={[
                { required: true, message: '请输入名称' },
                { max: 100, type: 'string' },
              ]}
            >
              <Input placeholder="请输入党组名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="d108Code"
              label="类别"
              rules={[{ required: true, message: '请选择类别' }]}
            >
              <DictTreeSelect
                codeType={'dict_d108'}
                backType={'object'}
                parentDisable={true}
                initValue={formInfo['d108Code'] || undefined}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="buildeTime"
              label="创建时间"
              rules={[{ required: false, message: '请输入创建时间' }]}
            >
              <Date />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="unitCode"
              label="关联单位"
              rules={[{ required: true, message: '请选择关联单位' }]}
              // initialValue={
              //   formInfo['unitCode']
              //     ? formInfo['unitCode'].startsWith('2')
              //       ? '2'
              //       : formInfo['unitCode']
              //     : '1'
              // }
            >
              <UnitSelect initValue={formInfo['unitName']} org={getSession('org') || {}}/>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
}

// export default connect(({ org, commonDict, loading }:any) => ({ org, commonDict, loading: loading.effects['org/getList'] }))(OrgParty)
export default React.forwardRef(AddParty);
