import React, { useEffect, useState } from 'react';
import styles from './index.less';
import ListTable from '@/components/ListTable';
import { Button, Divider, Input, Modal, Select, TreeSelect } from 'antd';
import { ArrowDownOutlined, ArrowUpOutlined, DeleteOutlined, PlusSquareOutlined } from '@ant-design/icons';
import WhiteSpace from '@/components/WhiteSpace';
import DictTreeSelect from '@/components/DictTreeSelect';
import moment from 'moment';
const Option=Select.Option;
const operatorList = [
  {
    label: '不等于',
    rule: '!=',
    value: 'notEqual',
    type: ['NUMBER', 'VARCHAR2', 'DATE', 'BOOLEAN'],
  },
  {
    label: '等于',
    rule: '=',
    value: 'equal',
    type: ['NUMBER', 'VARCHAR2', 'DATE', 'BOOLEAN'],
  },
  {
    label: '小于',
    rule: '<',
    value: 'less',
    type: ['NUMBER'],
  },
  {
    label: '晚于',
    rule: '<',
    value: 'less',
    type: ['DATE'],
  },
  {
    label: '大于',
    rule: '>',
    value: 'greater',
    type: ['NUMBER'],
  },
  {
    label: '早于',
    rule: '>',
    value: 'greater',
    type: ['DATE'],
  },
  {
    label: '小于等于',
    rule: '<=',
    value: 'lessEqual',
    type: ['NUMBER'],
  },
  {
    label: '晚于等于',
    rule: '<=',
    value: 'lessEqual',
    type: ['DATE'],
  },
  {
    label: '大于等于',
    rule: '>=',
    value: 'greaterEqual',
    type: ['NUMBER'],
  },
  {
    label: '早于等于',
    rule: '>=',
    value: 'greaterEqual',
    type: ['DATE'],
  },
  {
    label: '左包含(值)',
    rule: '左包含',
    value: 'leftLike',
    type: ['VARCHAR2'],
  },
  {
    label: '右包含(值)',
    rule: '右包含',
    value: 'rightLike',
    type: ['VARCHAR2'],
  },
  {
    label: '包含(值)',
    rule: '包含',
    value: 'like',
    type: ['VARCHAR2'],
  },
  {
    label: '包含(值)',
    rule: '包含',
    value: 'in',
    type: ['CODE'],
  },
  {
    label: '不包含(值)',
    rule: '不包含',
    value: 'notIn',
    type: ['CODE'],
  },
  {
    label: '为空',
    rule: 'is null',
    value: 'null',
    type: ['CODE', 'NUMBER', 'VARCHAR2', 'DATE', 'BOOLEAN'],
  },
];
export default function(props:{data:Array<any>,onChange?:any,desc?:Array<any>,showDesc?:boolean}){
  let [list,setList]:Array<any>=useState([{}]);
  const {desc=[],onChange,showDesc=true}=props;
  useEffect(()=>{
    // setList(props.list && props.list.length>0 ? props.list : [{}]);
    setList(props.data)
  },[JSON.stringify(props.data)])
  const changeRelation=async (data)=>{
    //数据变更清除所有关系条件
    data.forEach(obj=>{
      obj['leftBrackets']="";
      obj['rightBrackets']="";
    })
    data.forEach((obj,index)=>{
      //上一个
      let last=data[index-1];
      //下一个
      let next=data[index+1];
      console.log(index,last,next,'asdasd')
      //并且条件
      if(obj['relation']=='and'){
        obj['leftBrackets']="leftBrackets";
        if(last && last['relation']=='and'){
          obj['leftBrackets']="";
        }
        //没有下一个条件或者下一条件未设置 反括号
        if(!next || next['relation']==''){
          obj['rightBrackets']="rightBrackets";
        }
      }
      //或者条件 默认左、右括号
      if(obj['relation']=='or'){
        obj['leftBrackets']="leftBrackets";
        //上一个条件是并且时 左括号去除
        if(last && last['relation']=='and'){
          obj['leftBrackets']="";
        }
        obj['rightBrackets']="rightBrackets";
      }
    })
    setList([...data]);
  }
  const valChange=async (val,key,index)=>{
    let data=[...list];
    data[index][key]=val;
    //a==1 && a==2 || a==3
    //a==1 || a==2 && a==3
    //a==1 && a==2 && a==3 || a==4
    if(key=='relation'){
      await changeRelation(data);
    }else{
      setList(data);
    }
    onChange && onChange(data);
  }
  const columns=[
    {
      title:'序号',
      dataIndex:'num',
      width: 30,
      align: 'center',
      render: (text, record, index) => {
        return index + 1;
      },
    },
    {
      title:'信息项',
      dataIndex:'colName',
      width: 160,
      render:(text,record)=>{
        return record['colName'] || record['name']
      }
    },
    {
      title:'备注',
      dataIndex:'remark',
      render: (text, record, index) => {
        return (
          <Input allowClear defaultValue={text} style={{ width: '100%' }} size={'small'} onChange={e=>record['remark']=e.target.value} onBlur={()=>onChange && onChange(record)}/>
        );
      },
    },
  ];
  // console.log(desc);
  return(
    <React.Fragment>
      {
        showDesc && <React.Fragment>
          <div style={{minHeight:180,border: '1px solid #d9d9d9',padding:'12px'}}>
            {
              desc.map((msg,index)=>{
                return(
                  <p key={index} style={{margin:'unset'}}>
                    {index+1}.{msg}
                  </p>
                )
              })
            }
          </div>
          <WhiteSpace/>
          <WhiteSpace/>
        </React.Fragment>
      }
      {/*<div className={styles.table}>*/}
      {/*  <ListTable columns={columns} data={list} />*/}
      {/*</div>*/}
      <ListTable columns={columns} data={list} pagination={false}/>
    </React.Fragment>
  )
}
