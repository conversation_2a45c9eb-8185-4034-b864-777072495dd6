import React, { useEffect, useState } from 'react';
import ListTable from '@/components/ListTable';
import { Button, Divider, Input, Modal, Select, TreeSelect } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
export default function (props: {
  data: Array<any>;
  onChange?: any;
  desc?: Array<any>;
  showDesc?: boolean;
}) {
  let [list, setList]: Array<any> = useState([{}]);
  const { desc = [], onChange, showDesc = true } = props;
  useEffect(() => {
    // setList(props.list && props.list.length>0 ? props.list : [{}]);
    setList(props.data);
  }, [JSON.stringify(props.data)]);
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 30,
      align: 'center',
      render: (text, record, index) => {
        return index + 1;
      },
    },
    {
      title: '信息项',
      dataIndex: 'colName',
      width: 160,
      render: (text, record) => {
        // console.log("🚀 ~ record:colNamecolNamecolNamecolName", record)
        return record['colName'] || record['name'];
      },
    },
    {
      title: '是否可选',
      dataIndex: 'disabled',
      width: 160,
      render: (text, record, index) => {
        return (
          <Select
            style={{ width: 120 }}
            defaultValue={record.disabled ? 'true' : 'false'}
            onSelect={(e) => (record['disabled'] = e)}
            onBlur={(e) =>
              e != text && onChange && onChange({ enabled: false, remark: '', ...record })
            }
            options={[
              {
                value: 'true',
                label: '否',
              },
              {
                value: 'false',
                label: '是',
              },
            ]}
          />
        );
      },
    },
    {
      title: '是否显示',
      dataIndex: 'enabled',
      width: 160,
      render: (text, record, index) => {
        console.log('🚀 ~ record:enabled', record);
        return (
          <Select
            style={{ width: 120 }}
            defaultValue={record.enabled ? 'true' : 'false'}
            onSelect={(e) => (record['enabled'] = e)}
            onBlur={(e) =>
             {
              e != text && onChange && onChange({ disabled: false, remark: '', ...record })
             }
            }
            options={[
              {
                value: 'true',
                label: '否',
              },
              {
                value: 'false',
                label: '是',
              },
            ]}
          />
        );
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      render: (text = '', record, index) => {
        // console.log("🚀 ~ record:11111111111", record)
        return (
          <Input
            key={`${index}_${record['colName'] || record['name']}`}
            allowClear
            defaultValue={text}
            style={{ width: '100%' }}
            size={'small'}
            onChange={(e) => (record['remark'] = e.target.value)}
            onBlur={(e) => {
              console.log('🚀 ~ record:', record);
              e.target.value != text &&
                onChange &&
                onChange({ disabled: false, enabled: false, ...record });
            }}
          />
        );
      },
    },
  ];
  // console.log(desc);
  return (
    <React.Fragment>
      {showDesc && (
        <React.Fragment>
          <div style={{ minHeight: 180, border: '1px solid #d9d9d9', padding: '12px' }}>
            {desc.map((msg, index) => {
              return (
                <p key={index} style={{ margin: 'unset' }}>
                  {index + 1}.{msg}
                </p>
              );
            })}
          </div>
          <WhiteSpace />
          <WhiteSpace />
        </React.Fragment>
      )}
      {/*<div className={styles.table}>*/}
      {/*  <ListTable columns={columns} data={list} />*/}
      {/*</div>*/}
      <ListTable columns={columns} data={list} pagination={false} />
    </React.Fragment>
  );
}
