/**
 * 关系转接编辑人员
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Input, InputNumber, Modal, Row } from 'antd';
import {connect} from "dva";
import moment from 'moment';
import Tip from '@/components/Tip';
import DictTreeSelect from '@/components/DictTreeSelect';
import { findDictCodeName} from '@/utils/method.js';

const {MonthPicker}=DatePicker;
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};
const formItemLayout3 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12,pull:4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10,pull:4 },
  },
};
@connect(({transferOut})=>({transferOut}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  static open(){}
  static close(){}
  constructor(props){
    super(props);
    this.state={
      visible:false,
      key:new Date().valueOf(),
    };
    index.open=this.open;
  }
  open=()=>{
    this.setState({
      visible:true,
    })
  };
  handleOk=()=>{
    const {transferMemInfo}=this.props.transferOut;
    this.props.form.validateFieldsAndScroll(async (err,val)=>{
      if(!err){
        if(val['memFeeEndTime']){
          val['memFeeEndTime']=val['memFeeEndTime'].valueOf();
        }
        val = findDictCodeName(['d146'],val,transferMemInfo);
        const obj=await this.props.dispatch({
          type:'transferOut/editTransferForMem',
          payload:{
            data:{
              id:transferMemInfo['recordId'],
              ...val
            }
          }
        });
        if(obj && obj['code']===0){
          Tip.success('操作提示','修改成功');
          this.props.dispatch({
            type:'transferOut/updateState',
            payload:{
              transferMemInfo:undefined
            }
          });
          this.handleCancel();
        }
      }
    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
    })
  };
  render(){
    const {visible}=this.state;
    const {getFieldDecorator}=this.props.form;
    const {transferMemInfo={}}=this.props.transferOut;
    return(
      <div>
        <Modal
          destroyOnClose
          title="编辑"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
        >
          <Form {...formItemLayout}>
            <Row>
              <Col span={12}>
                <FormItem
                  label="姓名"
                  {...formItemLayout2}
                >
                  <span>{transferMemInfo['memName']}</span>
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  label="身份证"
                  {...formItemLayout3}
                >
                  <span>{transferMemInfo['idCard']}</span>
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  label="党员性质"
                  {...formItemLayout2}
                >
                  <span>{transferMemInfo['memType']}</span>
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  label="联系方式"
                  {...formItemLayout3}
                >
                  <span>{transferMemInfo['phone']}</span>
                </FormItem>
              </Col>
            </Row>
            {
              transferMemInfo['d146Code'] ? (
                <FormItem
                  label={'转接原因'}
                >
                  {getFieldDecorator('d146Code', {
                    initialValue:transferMemInfo['d146Code'],
                    rules: [{ required: true, message: '请选择转接原因' }],
                  })(
                    <DictTreeSelect
                      initValue={transferMemInfo['d146Code']}
                      codeType={'dict_d146'}
                      placeholder={'请选择转接原因'}
                      // ref={e=>this['d146_code'] = e}
                      parentDisable={true}
                      backType={'object'}/>
                  )}
                </FormItem>
              ) : (
                <FormItem
                  label="转接原因"
                >
                  {getFieldDecorator('reason', {
                    initialValue:transferMemInfo['reason'],
                    rules: [{ required: true, message: '请输入转接原因' }],
                  })(
                    <Input placeholder={'转接原因'}/>
                  )}
                </FormItem>
              )
            }


            <FormItem
              label="党费交纳标准(元/月)"
            >
              {getFieldDecorator('memFeeStandard', {
                initialValue:transferMemInfo['memFeeStandard'],
                rules: [{ required: true, message: '请输入党费交纳标准(元/月)' }],
              })(
                <InputNumber min={0} placeholder={'党费交纳标准(元/月)'} style={{width:'100%'}}/>
              )}
            </FormItem>

            <FormItem
              label="党费最后交纳到月份"
            >
              {getFieldDecorator('memFeeEndTime', {
                initialValue:transferMemInfo['memFeeEndTime'] ? moment(transferMemInfo['memFeeEndTime']) : undefined,
                rules: [{ required: true, message: '请选择党费最后交纳到月份' }],
              })(
                <MonthPicker placeholder={'党费最后交纳到月份'} style={{width:'100%'}}/>
              )}
            </FormItem>

          </Form>
        </Modal>
      </div>
    )
  }
}
export default Form.create()(index)
