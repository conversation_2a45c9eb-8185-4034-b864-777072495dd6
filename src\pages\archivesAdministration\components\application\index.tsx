import React, { useState, useImperativeHandle, useEffect } from 'react';
import { Modal, Form, Row, Col, Input, Button } from 'antd';
import TableSelect from '@/components/TableSelect';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import { getSession } from "@/utils/session";
import { digitalauditsave } from '@/pages/archivesAdministration/service/index';
import MemSelect from '@/components/MemSelect';
import Date from '@/components/Date'
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import moment from 'moment';
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
    },
};
const index = React.forwardRef((props: any, ref) => {
    const {
        title = '人员调整',
        width = 800,
        onsubmit,
    } = props;
    const org = getSession('org') || {};
    const [visible, setVisible] = useState(false);
    const [query, setQurey] = useState<any>({});
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();

    const [initValueTB, setInitValueTB] = useState<any>([]);
    useImperativeHandle(ref, () => ({
        open: query => {
            setVisible(true);
            setQurey(query);
             form.setFieldsValue({
                            applicantDate: moment(),
                        })
        },
        clear: () => {
            // clear();
        },
    }));
    useEffect(() => {
        if (!_isEmpty(query)) {
        }
    }, [JSON.stringify(query)])

    const handleCancel = () => {
        setVisible(false);
        clear();
    };
    const clear = () => {
        setQurey({});
        form.resetFields();
    };
    const onFinish = async (e) => {
        if (e['applicantMem']) {
            e['applicantMemCode'] = e['applicantMem'].map(i=>i.code).join(',');
            e['applicantMemName'] = e['applicantMem'].map(i=>i.name).join(',');
        }
             e['applicantDate'] = e['applicantDate']? moment(e['applicantDate']).valueOf():''
        e['memCode'] = query['code']
        e['memName'] = query['name']
        e['memOrgLevelCode'] = query['memOrgCode']
        e['memOrgName'] = query['orgName']
        e['digitalLotNo'] = query['digitalLotNo']
        console.log(e, 'eeeeeeeee')
        delete e['applicantMem']
        setLoading(true);
        const { code = 500 } = await digitalauditsave({
            data: {
                ...e,
                type: '1'
            }
        });
        setLoading(false);
        if (code === 0) {
            Tip.success('操作提示', '操作成功');
            handleCancel();
            onsubmit && onsubmit();
        }
    }
 const disabledTomorrow = (current) => {
        return current && current > moment().endOf('day');
    };
    return (
        <Modal
            title={'档案导出申请'}
            visible={visible}
            onOk={() => {
                form.submit();
            }}
            onCancel={handleCancel}
            width={width}
            confirmLoading={loading}
            destroyOnClose={true}
        >
            <Form form={form} {...formItemLayout} onFinish={onFinish}>
                <Form.Item
                    name="applicantMem"
                    label="申请人"
                    rules={[{ required: true, message: '人员选择' }]}
                >
                    <MemSelect checkType={'radio'} placeholder="请选择" d08CodeList={['1']}/>
                </Form.Item>
                <Form.Item
                    name="applicantDate"
                    label="申请时间"
                    rules={[{ required: true, message: '人员选择' }]}
                >
                    <Date disabled/>
                </Form.Item>
                <Form.Item
                    name="applicantReason"
                    label="导出理由"
                    rules={[{ required: true, message: '人员选择' }]}
                >
                    <Input.TextArea rows={4} placeholder="请输入申请理由" />
                </Form.Item>
            </Form>
        </Modal>
    )
});
export default index;

