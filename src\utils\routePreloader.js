/**
 * 路由预加载工具
 * 用于预加载特定页面，减少路由切换时的loading时间
 */

class RoutePreloader {
  constructor() {
    this.preloadedRoutes = new Set();
    this.preloadPromises = new Map();
    this.routeMap = {};
  }

  /**
   * 设置路由映射
   * @param {Object} routeMap - 路由映射对象
   */
  setRouteMap(routeMap) {
    this.routeMap = routeMap;
  }

  /**
   * 预加载单个路由
   * @param {string} routePath - 路由路径
   * @returns {Promise}
   */
  async preloadRoute(routePath) {
    if (this.preloadedRoutes.has(routePath)) {
      return Promise.resolve();
    }

    if (this.preloadPromises.has(routePath)) {
      return this.preloadPromises.get(routePath);
    }

    const promise = this._loadRouteComponent(routePath);
    this.preloadPromises.set(routePath, promise);

    try {
      await promise;
      this.preloadedRoutes.add(routePath);
      console.log(`✅ 预加载完成: ${routePath}`);
    } catch (error) {
      console.warn(`❌ 预加载失败: ${routePath}`, error);
    } finally {
      this.preloadPromises.delete(routePath);
    }

    return promise;
  }

  /**
   * 批量预加载路由
   * @param {string[]} routePaths - 路由路径数组
   */
  async preloadRoutes(routePaths) {
    console.log(`🚀 开始预加载 ${routePaths.length} 个路由`);

    try {
      await Promise.allSettled(routePaths.map((route) => this.preloadRoute(route)));
      console.log('✅ 预加载完成');
    } catch (error) {
      console.warn('预加载失败:', error);
    }
  }

  /**
   * 预加载常用页面
   * @param {string[]} routes - 要预加载的路由数组
   */
  async preloadCommonRoutes(routes = []) {
    await this.preloadRoutes(routes);
  }

  /**
   * 加载路由组件
   * @private
   */
  async _loadRouteComponent(routePath) {
    // 查找匹配的路由
    const matchedRoute = Object.keys(this.routeMap).find((route) => routePath.startsWith(route));

    if (matchedRoute && this.routeMap[matchedRoute]) {
      return await this.routeMap[matchedRoute]();
    } else {
      throw new Error(`未找到路由: ${routePath}`);
    }
  }

  /**
   * 检查路由是否已预加载
   */
  isRoutePreloaded(routePath) {
    return this.preloadedRoutes.has(routePath);
  }

  /**
   * 清理预加载缓存
   */
  clearCache() {
    this.preloadedRoutes.clear();
    this.preloadPromises.clear();
    console.log('🧹 预加载缓存已清理');
  }

  /**
   * 获取预加载统计信息
   */
  getStats() {
    return {
      preloadedCount: this.preloadedRoutes.size,
      preloadingCount: this.preloadPromises.size,
      isPreloading: this.isPreloading,
      preloadedRoutes: Array.from(this.preloadedRoutes),
    };
  }
}

// 创建单例实例
const routePreloader = new RoutePreloader();

export default routePreloader;
