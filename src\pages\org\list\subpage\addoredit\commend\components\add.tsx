// import React, { Fragment, useEffect, useState } from 'react';
// import { Button, Form, Input, Row, Col } from 'antd';
// import { Icon as LegacyIcon } from '@ant-design/compatible';
// import style from './add.less';
// import {recognitionAdd, findRecognitionByCode} from '@/pages/org/services/org'
// import Tip from '@/components/Tip';
// import DictSelect from '@/components/DictSelect';
// const formItemLayout = {
//   labelCol: { span: 4 },
//   wrapperCol: { span: 16 },
// };
// const formItemLayout2 = {
//   wrapperCol: { span: 24 },
// };
// const index = (props: any) => {
//   const [form] = Form.useForm();
//   const [infoData, setInfoData] = useState<any>({});
//   const [loading, setLoading ] = useState(false);
//
//   const onFinish = async (e) => {
//     const { org:{ basicInfo:{code:orgCode = '', orgCode:recognitionOrgCode = ''} = {} }={} } = props;
//     let val = {
//       ...e,
//       orgCode,
//       recognitionOrgCode,
//       code:infoData?.code
//     }
//     setLoading(true);
//     const {code = 500} = await recognitionAdd({
//       data:val
//     });
//     setLoading(false);
//     if(code === 0){
//       Tip.success('操作提示','保存成功');
//       getData()
//     }
//   };
//
//   const getData = async ()=>{
//     const { org:{ basicInfo:{code:orgCode = ''} = {} }={} } = props;
//     const { code = 500, data={} } = await findRecognitionByCode({orgCode});
//     if(code === 0){
//       setInfoData(data);
//       form.setFieldsValue(data);
//     }
//   };
//   useEffect(()=>{
//     getData()
//   },[]);
//   return (
//     <Fragment>
//
//       <Form form={form} {...formItemLayout} onFinish={onFinish}>
//         <Form.Item name='fileNo'
//                    label="表彰文件号"
//                    rules={[{ required: true, message: '请输入表彰文件号' }]}
//         >
//           <Input/>
//         </Form.Item>
//         <Form.Item name='recognitionObject'
//                    label="表彰对象"
//                    rules={[{ required: true, message: '请选择表彰对象' }]}
//         >
//           <DictSelect codeType={'dict_d93'} initValue={infoData['recognitionObject'] || ''} />
//         </Form.Item>
//         <Form.Item name='recognitionLevel'
//                    label="表彰级别"
//                    rules={[{ required: true, message: '请选择表彰对象' }]}
//         >
//           <DictSelect codeType={'dict_d94'} initValue={infoData['recognitionLevel'] || ''} />
//         </Form.Item>
//
//
//         <Row>
//           <Col span={4} style={{textAlign:'right'}}>
//             <div style={{marginTop:4}} className={style.redPoint}>
//               情况说明：
//             </div>
//           </Col>
//           <Col span={16}>
//             <Form.Item {...formItemLayout2}>
//               <div>
//                 其中党委(总支部、支部)书记优秀共产党员
//                 <Form.Item name='committeeParty'
//                            noStyle
//                            rules={[{ required: true, message: '请输入' }]}
//                 >
//                   <Input style={{ width: 100 }}/>
//                 </Form.Item>
//                 名；
//               </div>
//             </Form.Item>
//             <Form.Item {...formItemLayout2}>
//               <div>
//                 党委(总支部、支部)书记优秀党务工作者
//                 <Form.Item name='committeeWorker'
//                            noStyle
//                            rules={[{ required: true, message: '请输入' }]}
//                 >
//                   <Input style={{ width: 100 }}/>
//                 </Form.Item>
//                 名；
//               </div>
//             </Form.Item>
//             <Form.Item {...formItemLayout2}>
//               <div>
//                 生活困难优秀共产党员
//                 <Form.Item name='difficultParty'
//                            noStyle
//                            rules={[{ required: true, message: '请输入' }]}
//                 >
//                   <Input style={{ width: 100 }}/>
//                 </Form.Item>
//                 名；
//               </div>
//             </Form.Item>
//             <Form.Item {...formItemLayout2}>
//               <div>
//                 生活困难优秀党务工作者
//                 <Form.Item name='difficultWorker'
//                            noStyle
//                            rules={[{ required: true, message: '请输入' }]}
//                 >
//                   <Input style={{ width: 100 }}/>
//                 </Form.Item>
//                 名
//               </div>
//             </Form.Item>
//           </Col>
//         </Row>
//
//
//       </Form>
//       <div style={{ textAlign: 'center' }}>
//         <Button
//           type={'primary'}
//           icon={<LegacyIcon type={'check'} />}
//           style={{ marginRight: 16 }}
//           loading={loading}
//           onClick={()=>{
//             form.submit();
//           }}
//         >
//           保存
//         </Button>
//       </div>
//     </Fragment>
//   );
// };
// export default index;


import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Button, Col, Form, Input, InputNumber, Modal, Popconfirm, Row, Select, Table } from 'antd';
import moment from 'moment';
import DictSelect from '@/components/DictSelect';
import style from './add.less';
import { recognitionAdd, findRecognitionByCode } from '@/pages/org/services/org';
import Tip from '@/components/Tip';
import TimeDate from '@/components/Date';
import _isEmpty from 'lodash/isEmpty';
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

export const FormComp = (props: any) => {
  const { form, onFinish, list, add, del, isDetail = false, dataInfo = {} } = props;
  const columns = [
    {
      title: '表彰对象',
      dataIndex: 'recognitionObject',
      width: 375,
      render: (text, record) => {
        return (
          <Form.Item name={`recognitionObject_${record._key}`}
            initialValue={record.recognitionObject}
            rules={[{ required: true, message: '请选择表彰对象' }]}
          >
            <DictSelect codeType={'dict_d93'} initValue={record['recognitionObject'] || ''} />
          </Form.Item>
        );
      },
    },
    {
      title: '表彰级别',
      dataIndex: 'recognitionLevel',
      width: 195,
      render: (text, record) => {
        return (
          <Form.Item name={`recognitionLevel_${record._key}`}
            initialValue={record.recognitionLevel}
            rules={[{ required: true, message: '请选择表彰级别' }]}
          >
            <DictSelect codeType={'dict_d94'} initValue={record['recognitionLevel'] || ''} />
          </Form.Item>
        );
      },
    },
    {
      title: '人数',
      dataIndex: 'number',
      render: (text, record) => {
        return (
          <Form.Item name={`number_${record._key}`}
            initialValue={record.number}
            rules={[{ required: true, message: '请输入人数' }]}
          >
            <InputNumber defaultValue={record['number']} style={{ width: '100%' }} />
          </Form.Item>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return (
          <Popconfirm title={'是否确认删除'} onConfirm={() => del(record)}>
            <a className={'del'}>删除</a>
          </Popconfirm>
        );
      },
    },
  ];
  const validFunction = (rule, value, callback) => {
    if (!(/^[0-9]+$/).test(value)) {
      callback('只能是整数');
    }
    if ((/\s+/g).test(value)) {
      callback('不能包含空格');
    }
    callback();
  }
  return (
    <Form form={form} {...formItemLayout} onFinish={onFinish} style={{ pointerEvents: isDetail ? 'none' : 'auto' }}>
      <Form.Item name='annual'
        label='表彰时间'
        rules={[{ required: true, message: '请输入' }]}
      >
        <TimeDate />
      </Form.Item>

      <Form.Item name='fileNo'
        label="表彰文件号"
        rules={[{ required: true, message: '请输入表彰文件号' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item name='recognitionType'
        label="表彰类型"
        rules={[{ required: true, message: '请选择表彰类型' }]}
      >
        <DictSelect codeType={'dict_d125'} initValue={dataInfo['recognitionType'] || ''} />
      </Form.Item>

      {/* <Form.Item name='anniversarySituation'
                 label="光荣在党50周年情况"
                 rules={[{ required: true, message: '光荣在党50周年情况' }]}
      >
        <InputNumber style={{ width: '100%' }}/>
      </Form.Item> */}



      <Form.Item label={<div className={style.redPoint}>表彰情况</div>} name={'information'}>
        <Table
          bordered={true}
          rowKey={'_key'}
          // @ts-ignore
          columns={isDetail ? columns.filter(it => it.dataIndex != 'action') : columns}
          dataSource={list}
          pagination={false}
          footer={() => {
            if (isDetail) {
              return null
            }
            return (
              <div style={{ textAlign: 'center' }}>
                <Button type="primary" onClick={add} style={{ width: '20%', marginRight: '5%' }} size={'small'}>
                  添加
                </Button>
              </div>
            );
          }}
          size={'middle'}
        />
      </Form.Item>

      <Row style={{ marginTop: 20 }}>
        <Col span={4} style={{ textAlign: 'right' }}>
          <div style={{ marginTop: 4 }} className={style.redPoint}>
            情况说明：
          </div>
        </Col>
        <Col span={20}>
          其中党委(总支部、支部)书记优秀共产党员
          <div style={{ display: 'inline-block' }}>
            <Form.Item name='committeeParty'
              rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
            >
              <InputNumber style={{ width: 100 }} min={0} />
            </Form.Item>
          </div>
          名；

          党委(总支部、支部)书记优秀党务工作者
          <div style={{ display: 'inline-block' }}>
            <Form.Item name='committeeWorker'
              rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
            >
              <InputNumber style={{ width: 100 }} min={0} />
            </Form.Item>
          </div>
          名；

          生活困难优秀共产党员
          <div style={{ display: 'inline-block' }}>
            <Form.Item name='difficultParty'
              rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
            >
              <InputNumber style={{ width: 100 }} min={0} />
            </Form.Item>
          </div>
          名；

          生活困难优秀党务工作者
          <div style={{ display: 'inline-block' }}>
            <Form.Item name='difficultWorker'
              rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
            >
              <InputNumber style={{ width: 100 }} min={0} />
            </Form.Item>
          </div>
          名

        </Col>
      </Row>
    </Form>
  );
};
const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [isOnly, setIsOnly] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [list, setList] = useState([{ _key: +new Date() }]);
  const {
    width = 1000,
  } = props;

  useImperativeHandle(ref, () => ({
    open: async (query, isOnly = false) => {
      const { code } = query || { code: undefined };
      setDataInfo({});
      setIsOnly(isOnly);
      setList([{ _key: +new Date() }]);
      form.resetFields();
      if (code) {
        const { code: resCode = 500, data = {} } = await findRecognitionByCode({ code });
        if (resCode === 0) {
          let _data = {
            ...data,
            information: data.information.map((it, index) => {
              return { ...it, _key: index + 1 }
            })
          }
          setDataInfo(_data);
          setList(_data.information);
          form.setFieldsValue(_data);
        }
      }
      open();
    },
  }));
  const open = () => {
    setVisible(true);
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    setConfirmLoading(false);
    form.resetFields();
  };
  const handleCancel = () => {
    close();
  };
  const changeTableValue = (formValue) => {
    let final: any = {};
    let finalArr: any = [];
    Object.keys(formValue).map(it => {
      let [name, spilt] = it.split('_');
      if (spilt) {
        if (final[spilt]) {
          final[spilt] = { ...final[spilt], [name]: formValue[it] };
        } else {
          final[spilt] = { [name]: formValue[it] };
        }
      }
    });
    if (final) {
      for (let i in final) {
        finalArr.push(final[i]);
      }
    }
    return finalArr;
  };
  const onFinish = async (e) => {
    if (isOnly) {
      handleCancel();
      props.onOK && props.onOK();
      return
    }
    let { annual, fileNo, committeeParty, committeeWorker, difficultParty, difficultWorker, recognitionType, anniversarySituation, ...other } = e;
    let information = changeTableValue(other);

    if (_isEmpty(information)) {
      form.setFields([{ value: [], errors: ['表彰情况不能为空'], name: 'information' }]);
      return
    }
    if (annual) {
      annual = moment(annual).valueOf();
    }
    let final = {
      annual, fileNo, committeeParty, committeeWorker, difficultParty, difficultWorker,
      information, recognitionType, anniversarySituation
    };

    const { basicInfo } = props.org;
    const { code = 500 } = await recognitionAdd({
      data: {
        ...final,
        orgCode: basicInfo.code,
        recognitionOrgCode: basicInfo.orgCode,
        code: dataInfo?.code
      },
    });
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      props.onOK && props.onOK();
    }
  };
  const del = (record) => {
    setList(e => e.filter(a => a._key !== record._key));
  };
  const add = () => {
    setList(e => [...e, { _key: +new Date() }]);
  };

  return (
    <Fragment>
      <Modal
        title={dataInfo.code ? '编辑' : '新增'}
        visible={visible}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
      >
        {
          visible && <Fragment>
            <FormComp form={form} dataInfo={dataInfo} onFinish={onFinish} del={del} list={list} add={add} isDetail={isOnly} />
          </Fragment>
        }
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);

