import { listPageModel, ListPageStore } from "@/utils/common-model";
import modelExtend from "dva-model-extend";
import {
  add,
  outMemList,
  findOutMemByCode,
  backOutMem,
  addInMem,
  systemInMme,
  delMemFlow,
  inMemList,
  continueFlow,
  historyFlowList,
  outManageList,
  outManageRegister,
  inManageList,
  inflowOrganizationList,
  inflowOrganizationDList,
  ReminderMobilePartyMembersList
} from "../service"
import Notice from '../../../components/Notice';
import { getSession } from '@/utils/session';
const flowMem = modelExtend(listPageModel, {
  namespace: 'flowMem',
  state: {
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if (pathname === '/flowMem/flowBack' || pathname === '/flowMem/inflows' || pathname === '/flowMem/flowHistory' || pathname === '/flowMem/outflowManage' || pathname === '/flowMem/inflowManage' || pathname === '/flowMem/inflowOrganization') {
          const org = getSession('org') || {};
          const defaultParas = {
            pageNum: 1,
            pageSize: 10,
          };
          const dictData = ['dict_d09', 'dict_d07', 'dict_d41', 'dict_d34', 'dict_d151_flow', 'dict_d148', 'dict_d202', 'dict_d203'];
          for (let obj of dictData) {
            dispatch({
              type: 'commonDict/getDictTree',
              payload: {
                data: {
                  dicName: obj
                }
              }
            });
          }
          dispatch({
            type: 'getList',
            payload: {
              data: {
                orgCode: org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
      });
    }
  },
  effects: {
    *del({ payload }, { call, put }) {
      const info = yield call(delMemFlow, payload);
      return Promise.resolve(info);
    },
    *add({ payload }, { call, put }) {
      const info = yield call(add, payload);
      return Promise.resolve(info);
    },
    *list({ payload }, { call, put }) {
      const info = yield call(outMemList, { data: payload });
      const { data: { list = [], ...pagination } = {}, message = '操作失败', code = null } = info;
      if (code === 0) {
        yield put({
          type: 'querySuccess',
          payload: {
            list: list, pagination: {
              ...pagination,
              current: pagination.pageNumber || 1,
              pageSize: pagination.pageSize,
              total: pagination.totalRow,
            }
          }
        });
      } else {
        Notice("操作提示", message, "exclamation-circle-o", "orange");
      }
    },
    *detail({ payload }, { call, put }) {
      const info = yield call(findOutMemByCode, payload);
      return Promise.resolve(info);
    },
    *getBackOutMem({ payload }, { call, put }) {
      const info = yield call(backOutMem, payload);
      return Promise.resolve(info);
    },
    //党员流入-------------------------------------------------
    *inList({ payload }, { call, put }) {
      const info = yield call(inMemList, { data: payload });
      const { data: { list = [], ...pagination } = {}, message = '操作失败', code = null } = info;
      if (code === 0) {
        yield put({
          type: 'querySuccess',
          payload: {
            list: list, pagination: {
              ...pagination,
              current: pagination.pageNumber || 1,
              pageSize: pagination.pageSize,
              total: pagination.totalRow,
            }
          }
        });
      } else {
        Notice("操作提示", message, "exclamation-circle-o", "orange");
      }
    },
    *addInMem({ payload }, { call, put }) {
      const info = yield call(addInMem, payload);
      return Promise.resolve(info);
    },
    *systemInMme({ payload }, { call, put }) {
      const info = yield call(systemInMme, payload);
      return Promise.resolve(info);
    },
    *continueFlow({ payload }, { call, put }) {
      const info = yield call(continueFlow, payload);
      return Promise.resolve(info);
    },
    //历史-----------------------------------------------
    *getHistory({ payload }, { call, put }) {
      const info = yield call(historyFlowList, payload);
      const { data: { list = [], ...pagination } = {}, message = '操作失败', code = null } = info;
      if (code === 0) {
        yield put({
          type: 'querySuccess',
          payload: {
            list: list, pagination: {
              ...pagination,
              current: pagination.pageNumber || 1,
              pageSize: pagination.pageSize,
              total: pagination.totalRow,
            }
          }
        });
      } else {
        Notice("操作提示", message, "exclamation-circle-o", "orange");
      }
    },
    // 流出管理--------------------------
    *outManageList({ payload }, { call, put }) {
      yield put({
        type: 'querySuccess',
        payload: {
          list: [],
          pagination: {},
        }
      })
      const info = yield call(outManageList, payload);
      const { data: { list = [], ...pagination } = {}, message = '操作失败', code = null } = info;
      if (code === 0) {
        yield put({
          type: 'querySuccess',
          payload: {
            list: list,
            pagination: {
              ...pagination,
              current: pagination.pageNumber || 1,
              pageSize: pagination.pageSize,
              total: pagination.totalRow,
            },
          }
        })
      } else {
        Notice('操作提示', message, "exclamation-circle-o", "orange")
      }
    },
    *outManageAdd({ payload }, { call, put }) {
      const info = yield call(outManageRegister, payload);
      return Promise.resolve(info);
    },
    // 流入管理--------------------------
    *inManageList({ payload }, { call, put }) {
      yield put({
        type: 'querySuccess',
        payload: {
          list: [],
          pagination: {},
        }
      })
      const info = yield call(inManageList, payload);
      const { data: { list = [], ...pagination } = {}, message = '操作失败', code = null } = info;
      if (code === 0) {
        yield put({
          type: 'querySuccess',
          payload: {
            list: list,
            pagination: {
              ...pagination,
              current: pagination.pageNumber || 1,
              pageSize: pagination.pageSize,
              total: pagination.totalRow,
            },
          }
        })
      } else {
        Notice('操作提示', message, "exclamation-circle-o", "orange")
      }
    },
    //流动党员党组织
    *inflowOrganizationDList({ payload }, { call, put }) {
      yield put({
        type: 'querySuccess',
        payload: {
          list: [],
          pagination: {},
        }
      })
      const info = yield call(inflowOrganizationDList, payload);
      // console.log("🚀 ~ *inflowOrganizationList ~ payload:", payload)
      const { data: { list = [], ...pagination } = {}, message = '操作失败', code = null } = info;
      if (code === 0) {
        yield put({
          type: 'querySuccess',
          payload: {
            list: list,
            pagination: {
              ...pagination,
              current: pagination.pageNumber || 1,
              pageSize: pagination.pageSize,
              total: pagination.totalRow,
            },
          }
        })
      } else {
        Notice('操作提示', message, "exclamation-circle-o", "orange")
      }
    },
    //流动党组织审核
    *inflowOrganizationList({ payload }, { call, put }) {
      // yield put({
      //   type: 'querySuccess',
      //   payload: {
      //     list: [],
      //     pagination: {},
      //   }
      // })
      const info = yield call(inflowOrganizationList, payload);
      // console.log("🚀 ~ *inflowOrganizationList ~ payload:", payload)
      const { data: { list = [], ...pagination } = {}, message = '操作失败', code = null } = info;
      if (code === 0) {
        yield put({
          type: 'querySuccess',
          payload: {
            list: list,
            pagination: {
              ...pagination,
              current: pagination.pageNumber || 1,
              pageSize: pagination.pageSize,
              total: pagination.totalRow,
            },
          }
        })
      } else {
        Notice('操作提示', message, "exclamation-circle-o", "orange")
      }
    },
    //流动党员提醒
    *ReminderMobilePartyMembersList({ payload }, { call, put }) {
      yield put({
        type: 'querySuccess',
        payload: {
          list: [],
          pagination: {},
        }
      })
      const info = yield call(ReminderMobilePartyMembersList, payload);
      console.log("🚀 ~ *inflowOrganizationList ~ payload:", payload)
      const { data: { list = [], ...pagination } = {}, message = '操作失败', code = null } = info;
      if (code === 0) {
        yield put({
          type: 'querySuccess',
          payload: {
            list: list,
            pagination: {
              ...pagination,
              current: pagination.pageNumber || 1,
              pageSize: pagination.pageSize,
              total: pagination.totalRow,
            },
          }
        })
      } else {
        Notice('操作提示', message, "exclamation-circle-o", "orange")
      }
    },
  },
  reducers: {
    success(state, { payload }) {
      return { ...state, ...payload };
    },
  }
});
export default flowMem
