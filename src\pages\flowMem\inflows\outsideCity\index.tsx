/**
 * 省内流入登记
 */
import React from 'react'
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Avatar, Button, Col, Dropdown, Menu, Modal, Row, Input, Radio, DatePicker } from 'antd';
import qs from 'qs';
import { connect } from "dva";
import ListTable from 'src/components/ListTable';
import { _history as router } from "@/utils/method";
import { isEmpty } from '@/utils/method';
import { getSession } from '@/utils/session';
import DictArea from '@/components/DictArea';
import DictSelect from '@/components/DictSelect';
import OrgSelect from '@/components/OrgSelect'
import styles from './index.less'
import { getIdCardInfo } from '@/utils/method.js';
import moment from 'moment'
import { root } from '@/common/config'
import DictTreeSelect from '@/components/DictTreeSelect';
import Notice from '@/components/Notice';
import Date from '@/components/Date';
import DateTime from '@/components/Date';
import _isEmpty from 'lodash/isEmpty';
import { formLabel,correctIdcard } from '@/utils/method';
import SearchOrg from '@/components/SearchOrg';
import Tip from '@/components/Tip';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};
const menuData = [
  {
    code: '1',
    name: '基本信息',
    icon: 'star',
  },
  {
    code: '2',
    name: '',
    icon: 'qrcode',
  },
];
@connect(({ unit, commonDict, loading }) => ({ unit, commonDict, loading: loading.effects['unit/getList'] }), undefined, undefined, { forwardRef: true })
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    let obj = menuData[0];
    this.state = {
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    };
  }

  showModal = () => {
    const { data: { id = '' } = {}, keyword = '' } = this.props;
    let org = getSession('org') || {};
    this.setState({
      visible: true,
    });
  };

  handleOk = () => {
    this.props.form.validateFieldsAndScroll(async (errors, values) => {
      if (errors) {
        return
      }
      let result =await correctIdcard(values['memName'],values['idcard']);
      if(result['code']!='200'){
        this.props.form.setFields({
          idcard:{
            value:values['idcard'],
            errors:[new Error('经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')]
          }
        })
        Tip.error('操作提示','经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')
        return
      }else{
        values['idCardReason']=result['reason']
        values['idCardReasonName']=result['reasonName']
      }
      const { onChange } = this.props;
      const { joinOrgDate, birthday, gender, memType, outflowEdu, nation, outflowJob, stratumType, outflowUnitType, outflowOrg, outflowArea, outflowReason, outflowDate, ...value } = values;
      let val = {
        birthday: isEmpty(birthday) ? '' : moment(birthday).format('YYYY-MM-DD'),
        joinOrgDate: isEmpty(joinOrgDate) ? '' : moment(joinOrgDate).format('YYYY-MM-DD'),
        gender: isEmpty(gender) ? '' : gender,
        genderName: isEmpty(gender) ? '' : gender === 1 ? '男' : '女',
        memTypeName: isEmpty(memType) ? '' : memType === 1 ? '正式党员' : '预备党员',
        memTypeCode: isEmpty(memType) ? '' : memType,
        outflowEduCode: isEmpty(outflowEdu) ? '' : outflowEdu['id'],
        outflowEduName: isEmpty(outflowEdu) ? '' : outflowEdu['name'],
        nationName: isEmpty(nation) ? '' : nation['name'],
        nationCode: isEmpty(nation) ? '' : nation['id'],
        outflowJobCode: isEmpty(outflowJob) ? '' : outflowJob['id'],
        outflowJobName: isEmpty(outflowJob) ? '' : outflowJob['name'],
        stratumTypeName: isEmpty(stratumType) ? '' : stratumType['name'],
        stratumTypeCode: isEmpty(stratumType) ? '' : stratumType['id'],
        outflowUnitTypeCode: isEmpty(outflowUnitType) ? '' : outflowUnitType['id'],
        outflowUnitTypeName: isEmpty(outflowUnitType) ? '' : outflowUnitType['name'],
        outflowOrgName: isEmpty(outflowOrg) ? '' : outflowOrg['name'],
        outflowOrgCode: isEmpty(outflowOrg) ? '' : outflowOrg['code'],
        outflowOrgOrgCode: isEmpty(outflowOrg) ? '' : outflowOrg['orgCode'],
        outflowReasonCode: isEmpty(outflowReason) ? '' : outflowReason['id'],
        outflowReasonName: isEmpty(outflowReason) ? '' : outflowReason['name'],
        outflowAreaName: isEmpty(outflowArea) ? '' : outflowArea['name'],
        outflowAreaId: isEmpty(outflowArea) ? '' : outflowArea['id'],
        outflowTypeCode: isEmpty(outflowReason) ? '' : outflowReason['id'],
        outflowTypeName: isEmpty(outflowReason) ? '' : outflowReason['name'],
        outflowDate: isEmpty(outflowDate) ? '' : moment(outflowDate).format('YYYY-MM-DD'),
        ...value
      };
      this.props.dispatch({
        type: 'flowMem/systemInMme',
        payload: {
          data: {
            ...val
          }
        }
      }).then(res => {
        if (res['code'] === 0) {
          Notice("操作提示", res['message'], "check-circle", "green");
          this.handleCancel();
          onChange && onChange(true)
        } else {
          Notice("操作提示", res['message'], "exclamation-circle-o", "orange");
        }
      })
    });
    // this.handleCancel();
  };
  handleCancel = () => {
    this.setState({
      visible: false
    });
    this.props.form.resetFields();
  };
  open = () => {
    this.setState({
      visible: true,
    })
  };
  destroy = () => {
    let obj = menuData[0];
    this.setState({
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    });
    this.props.dispatch({//重置model
      type: 'unit/updateState',
      payload: {
        basicInfo: {},
      }
    })
  };
  onSelect = (item) => {
    const { key, keyPath } = item;
    const selected = menuData.find(obj => obj['code'] === key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  validFunction = async (rule, value, callback) => {
    let han = /^[\u4e00-\u9fa5]+$/;
    if (value) {
      switch (rule.field) {
        case 'memName':
          if (value.length > 8) {
            return callback('姓名长度不能超过8个字符')
          } else if (!han.test(value)) {
            return callback('姓名不合法')
          }
          break;
        case 'idcard':
          if (getIdCardInfo(value) === 'Error') {
            return callback('身份证格式错误,请核对身份证图片')
          } else {
            // let fieldValue = this.props.form.getFieldValue('memName');
            // let res = await geitCard({ idCard: value, name: fieldValue });
            return callback()
          }
          break;
        case 'birthday':
          if (!moment(value).isBefore(moment().format('YYYY-MM-DD'))) {
            return callback('生日不能大于当前日期')
          }
          break;
        case 'memOrgName':
          if (value.length > 20) {
            return callback('组织名称不能超过20个字符')
          } else if (!han.test(value)) {
            return callback('组织名称不合法')
          }
          break;
        case 'outflowOrgLinkman':
          if (value.length > 20) {
            return callback('组织联系人不能超过20个字符')
          } else if (!han.test(value)) {
            return callback('联系人名称不合法')
          }
          break;
        case 'outflowOrgPhone':
          if (!(/^1[345789]\d{9}$/).test(value)) {
            return callback('手机号码不合法')
          }
          break;
      }
    }
    callback()
  };
  onPageChange = (page, pageSize) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`)
  };
  render() {
    const { visible, selected, keyPath, key, pagination = {} } = this.state;
    const { basicInfo = {} } = this.props.unit;
    const { filterHeight, loading, children, tipMsg = {} } = this.props;
    const { getFieldDecorator } = this.props.form;

    return (
      <React.Fragment>
        {
          children ? React.cloneElement(children as any, {
            onClick: this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          title="省外流入登记"
          className='out_Modal'
          destroyOnClose
          closable={false}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1000}
        >
          <div className='container'>
            <Form {...formItemLayout}>
              <Row>
                <Col span={12}>
                  <FormItem
                    label={formLabel('姓名', tipMsg['memName'])}
                  >
                    {getFieldDecorator('memName', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请输入姓名!' },
                        { validator: this.validFunction }
                      ],
                    })(
                      <Input placeholder={'请填写'}/>
                    )}
                  </FormItem>
                </Col>
                {/*<Col span={12}>*/}
                {/*  <FormItem*/}
                {/*    label={'性别'}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('gender', {*/}
                {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
                {/*      rules: [*/}
                {/*        { required: true, message: '请选择性别!' },*/}
                {/*        // { validator: this.validFunction }*/}
                {/*      ],*/}
                {/*    })(*/}
                {/*      <RadioGroup>*/}
                {/*        <Radio value={1}>男</Radio>*/}
                {/*        <Radio value={0}>女</Radio>*/}
                {/*      </RadioGroup>*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                <Col span={12}>
                  <FormItem
                    label={formLabel('身份证号', tipMsg['idcard'])}
                  >
                    {getFieldDecorator('idcard', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请输入身份证号!' },
                        { validator: this.validFunction }
                      ],
                    })(
                      <Input placeholder={'请填写'} />
                    )}
                  </FormItem>
                </Col>
                {/*<Col span={12}>*/}
                {/*  <FormItem*/}
                {/*    label={'出生日期'}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('birthday', {*/}
                {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
                {/*      rules: [*/}
                {/*        { required: true, message: '请选择出生日期!' },*/}
                {/*        { validator: this.validFunction }*/}
                {/*         // <DatePicker style={{width:'100%'}} placeholder={'请选择出生日期'}/>*/}
                {/*      ],*/}
                {/*    })(*/}

                {/*      <Date />*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                <Col span={12}>
                  <FormItem
                    label={formLabel('党员类别', tipMsg['memType'])}
                  >
                    {getFieldDecorator('memType', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请选择党员类别!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <RadioGroup>
                        <Radio value={1}>正式党员</Radio>
                        <Radio value={2}>预备党员</Radio>
                      </RadioGroup>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label={formLabel('入党时间', tipMsg['joinOrgDate'])}>
                    {getFieldDecorator('joinOrgDate', {
                      rules: [
                        { required: true, message: '入党时间!' },
                      ],
                    })(
                      <DateTime />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={formLabel('学历', tipMsg['outflowEdu'])}
                  >
                    {getFieldDecorator('outflowEdu', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请选择学历!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <DictTreeSelect codeType={'dict_d07'} parentDisable={true} backType={'object'} placeholder={'请选择学历'} />
                    )}
                  </FormItem>
                </Col>
                {/*<Col span={12}>*/}
                {/*  <FormItem*/}
                {/*    label={'民族'}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('nation', {*/}
                {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
                {/*      rules: [*/}
                {/*        { required: true, message: '请选择民族!' },*/}
                {/*        // { validator: this.validFunction }*/}
                {/*      ],*/}
                {/*    })(*/}
                {/*      <DictTreeSelect codeType={'dict_d06'} parentDisable={true} backType={'object'} placeholder={'请选择民族'}/>*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                <Col span={24}>
                  <FormItem
                    label={formLabel('原职业(工作岗位)', tipMsg['outflowJob'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('outflowJob', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请选择工作岗位!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <DictTreeSelect codeType={'dict_d09'} placeholder={'请选择工作岗位'} parentDisable={true} backType={'object'} />
                    )}
                  </FormItem>
                </Col>
                {/*<Col span={24}>*/}
                {/*  <FormItem*/}
                {/*    label={'原职业(新社会阶层)'}*/}
                {/*    {...formItemLayout1}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('stratumType', {*/}
                {/*      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
                {/*      rules: [*/}
                {/*        { required: true, message: '请选择新社会阶层!' },*/}
                {/*        // { validator: this.validFunction }*/}
                {/*      ],*/}
                {/*    })(*/}
                {/*      <DictTreeSelect codeType={'dict_d20'} backType={'object'} parentDisable={true}/>*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                <Col span={24}>
                  <FormItem
                    label={formLabel('流出党支部', tipMsg['memOrgName'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('memOrgName', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请填写流出党支部!' },
                        { validator: this.validFunction }
                      ],
                    })(
                      <Input placeholder={'请填写'} />
                    )}
                  </FormItem>
                </Col>
                <Col span={24}>
                  <FormItem
                    label={formLabel('流出单位类型', tipMsg['outflowUnitType'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('outflowUnitType', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请选择流出单位类型!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      // <DictSelect codeType={'dict_d04'} backType={'object'} placeholder={'请选择单位类型'} />
                      <DictTreeSelect codeType={'dict_d04'} placeholder={'请选择单位类型'} parentDisable={true} backType={'object'} />
                    )}
                  </FormItem>
                </Col>
                <Col span={24}>
                  <FormItem
                    label={formLabel('流入党支部', tipMsg['outflowOrg'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('outflowOrg', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请选择流入党支部!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <SearchOrg backType={'object'} params={{orgTypeList:['3','4']}}/>
                      // <OrgSelect orgTypeList={['3','4']} placeholder={'请选择流入党支部'} org={{ orgCode: getSession('user').orgCode }} isPermissionCheck={'0'} />
                    )}
                  </FormItem>
                </Col>
                {/*<Col span={24}>*/}
                {/*<FormItem*/}
                {/*  label={'流动去向'}*/}
                {/*  {...formItemLayout1}*/}
                {/*>*/}
                {/*  {getFieldDecorator('outflowArea', {*/}
                {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
                {/*    rules: [*/}
                {/*      { required: true, message: '请选择流动去向!' },*/}
                {/*      { validator: this.validFunction }*/}
                {/*    ],*/}
                {/*  })(*/}
                {/*    <DictArea backType={'object'} areaCode={root['areaCode']} placeholder={'请选择流动去向'}/>*/}
                {/*  )}*/}
                {/*</FormItem>*/}
                {/*</Col>*/}
                <Col span={24}>
                  <FormItem
                    label={formLabel('流入原因类型', tipMsg['outflowReason'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('outflowReason', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请选择流入原因类型!' },
                        // { validator: this.validFunction }
                      ],
                    })(
                      <DictSelect codeType={'dict_d41'} backType={'object'} placeholder={'请选择流入原因'} />
                    )}
                  </FormItem>
                </Col>
                <Col span={24}>
                  <FormItem
                    label={formLabel('流动党员活动证', tipMsg['isHold'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('isHold', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请选择流动党员活动证!' },
                        { validator: this.validFunction }
                      ],
                    })(
                      <RadioGroup>
                        <Radio value={1}>已发放</Radio>
                        <Radio value={0}>未发放</Radio>
                      </RadioGroup>
                    )}
                  </FormItem>
                </Col>
                <Col span={24}>
                  <FormItem
                    label={formLabel('流入日期', tipMsg['outflowDate'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('outflowDate', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        { required: true, message: '请选择流入日期!' },
                        // { validator: this.validFunction }
                        // <DatePicker style={{width:'100%'}} placeholder={'请选择日期'}/>
                      ],
                    })(

                      <Date />
                    )}
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </div>
        </Modal>
      </React.Fragment>

    )
  }
}
export default Form.create()(index);
