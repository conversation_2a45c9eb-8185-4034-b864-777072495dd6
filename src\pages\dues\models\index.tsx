import {listPageModel, ListPageStore} from "@/utils/common-model";
import modelExtend from "dva-model-extend";
import {
  add,
  getList,
  getPayList,
  updateLastPayDate,
  getOnePayList,
  getWxpayQRCode,
  wxPayOrderQuery,
  getCountList,
  getPayTotalListt,
  getListzc,
  saveFeeDisburse,
  updateFeeDisburse,
  delFeeDisburse,
  getWxBillList,
  saveByWxBill,
  getListxb,
  saveFeeAllocate
} from  "../services"
import Notice from '../../../components/Notice';
import { getSession } from '@/utils/session';
const flowMem=modelExtend(listPageModel,{
  namespace:'dues',
  state:{
  },
  subscriptions:{
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if(pathname==='/dues/spending'){
          const org=getSession('org') || {};
          const defaultParas={
            pageNum:1,
            pageSize:10,
          };
          const dictData=['dict_d68'];
          for(let obj of dictData){
            dispatch({
              type:'commonDict/getDictTree',
              payload:{
                data:{
                  dicName:obj
                }
              }
            });
          }
        }
      });
    }
  },
  effects:{
    *add({payload},{call,put}){
      const info = yield call(add, payload);
      return Promise.resolve(info);
    },
    *list({payload},{call,put}){
      const info = yield call(getList, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'querySuccess',
          payload: {list:list,pagination:pagination}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *upPayDate({payload},{call,put}){
      const info = yield call(updateLastPayDate, payload);
      return Promise.resolve(info);
    },
    *getCountList({payload},{call,put}){
      const info = yield call(getCountList, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'updateState',
          payload: {list1:list,pagination1:{
              pageSize:pagination['pageSize'],
              total:pagination['totalRow'],
              page:pagination['page'],
              current:pagination['pageNumber']
            }}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    //------------------------------------------------------------------------------------
    *payList({payload},{call,put}){
      const info = yield call(getPayList, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'updateState',
          payload: {list:list,pagination:pagination}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *getPayTotalListt({payload},{call,put}){
      const info = yield call(getPayTotalListt, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'updateState',
          payload: {
            list1:list,
            pagination1:{
              pageSize:pagination['pageSize'],
              total:pagination['totalRow'],
              page:pagination['page'],
              current:pagination['pageNumber']
            }}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },

    *getOne({payload},{call,put}){
      const info = yield call(getOnePayList, payload);
      return Promise.resolve(info);
    },

    *getWxpayQRCode({payload},{call,put}){
      const info = yield call(getWxpayQRCode, payload);
      return Promise.resolve(info);
    },
    *getPayOrderQuery({payload},{call,put}){
      const info = yield call(wxPayOrderQuery, payload);
      return Promise.resolve(info);
    },
    //--------------------------------------------------------
    *getListzc({payload},{call,put}){
      const info = yield call(getListzc, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'updateState',
          payload: {list:list,pagination:pagination}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *saveFeeDisburse({payload},{call,put}){
      const info = yield call(saveFeeDisburse, payload);
      return Promise.resolve(info);
    },
    *updateFeeDisburse({payload},{call,put}){
      const info = yield call(updateFeeDisburse, payload);
      return Promise.resolve(info);
    },
    *delFeeDisburse({payload},{call,put}){
      const info = yield call(delFeeDisburse, payload);
      return Promise.resolve(info);
    },
    *getWxBillList({payload},{call,put}){
      const info = yield call(getWxBillList, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code == 0){
        yield put({
          type: 'updateState',
          payload: {list1:list,pagination1:pagination}
        });
      }else {
        yield put({
          type: 'updateState',
          payload: {list1:[],pagination1:{}}
        });
      }
    },
    *saveByWxBill({payload},{call,put}){
      const info = yield call(saveByWxBill, payload);
      return Promise.resolve(info);
    },
//-----------------------------------------------------------
    *getListxb({payload},{call,put}){
      const info = yield call(getListxb, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'updateState',
          payload: {list:list,pagination:pagination}
        });
      }
    },
    *saveFeeAllocate({payload},{call,put}){
      const info = yield call(saveFeeAllocate, payload);
      return Promise.resolve(info);
    },
    *isLeaf({payload},{call,put}){
        yield put({
          type: 'updateState',
          payload: {list:[],pagination:{}}
        });
    },
  },
  reducers: {
    success(state, {payload}) {
      return {...state, ...payload};
    },
  }
});
export default flowMem
