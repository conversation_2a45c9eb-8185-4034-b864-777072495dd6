import React, { useState, useRef, useEffect } from 'react';
import st from './index.less';
import { Button } from 'antd';
import { _history as router } from '@/utils/method';
import moment from 'moment';
import { screenexport } from '../../../services';
import Tip from '@/components/Tip';
import Birthday from '../birthday';
import useDomToimage from '@/utils/hooks/useDomToimage';
import { changeOrgName2 } from '../../../utils/tool';
import ReactSeamlessScroll from 'rc-seamless-scroll';
import _isEmpty from 'lodash/isEmpty';
import _cloneDeep from 'lodash/cloneDeep';

const index = (props: any) => {
  const ref = useRef();
  const {
    session: {
      birthday = '',
      joinOrgDate = '',
      fileList = [],
      name = '',
      photo_base64 = '',
      selectText = '',
      info: { answer = '', d06Name = '', orgName = '' } = {},
      code = '',
      sign = '',
      asd = '',
    } = {},
  } = props;

  const [lineRef, func] = useDomToimage({
    callBack: () => {
      setTimeout(() => {
        Tip.success('操作提示', '下载成功');
        setBtnHide(false);
      }, 1000 * 2);
    },
  });
  const [btnHide, setBtnHide] = useState<any>(false);

  const back = async () => {
    router.push('/qzs/screen/last');
  };

  const exports = async () => {
    const res = await screenexport({ memCode: code });
    if (res.status == 200) {
      Tip.success('操作提示', '操作成功');
    }
  };
  function adjustArray(a, m) {
    if (_isEmpty(a)) return a;
    const n = a.length;
    if (n < m) {
      // 补全数组长度到 m
      for (let i = n; i < m; i++) {
        a[i] = a[i % n];
      }
    } else if (n > m) {
      // 截取到 m 位长度
      a.splice(m, n - m);
    }
    return a;
  }
  const finList = adjustArray(_cloneDeep(fileList), 10);
  useEffect(() => {
    console.log('fileList===', fileList);
  }, []);
  const arr = new Array(10).fill('');
  return (
    <div ref={lineRef} style={{ height: 633 }}>
      <div className={st.box}>
        <div className={st.head} style={{ visibility: btnHide ? 'hidden' : undefined }}>
          <Button className={st.btn} onClick={back}>
            返 回
          </Button>
          <div>
            <Button
              className={st.btn}
              style={{ marginRight: 10 }}
              onClick={async () => {
                await setBtnHide(true);
                setTimeout(() => {
                  func(`${name}_${moment().format('YYYY_M_D')}`);
                }, 1000 * 1);
              }}
            >
              保存电子贺卡
            </Button>
            {/* <Button className={st.btn} onClick={exports}>
              数据导出
            </Button> */}
          </div>
        </div>
        <div className={st.body}>
          <div className={st.left}>
            <div className={st.box1}>
              <div className={st.info}>
                <div>
                  <div className={st.name}>
                    {name}
                    <span>{d06Name}</span>
                  </div>
                  <div className={st.desc}>
                    <div>出生年月</div>:<div>{moment(birthday).format('YYYY年M月D日')}</div>
                  </div>
                  <div style={{ height: 10 }}></div>
                  <div className={st.desc}>
                    <div>政治生日</div>:<div>{moment(joinOrgDate).format('YYYY年M月D日')}</div>
                  </div>
                  <div style={{ height: 10 }}></div>
                  <div className={st.desc}>
                    <div>所在党支部</div>:<div>{changeOrgName2(orgName)}</div>
                  </div>
                </div>
                <div className={st.avaBox}>
                  <img
                    className={st.avaBoxImg}
                    src={require('../../../../../../assets/qzs/youngavator.png')}
                    alt=""
                  />
                  <div className={st.cyc}>
                    <img className={st.avt} src={asd} alt="" />
                  </div>
                </div>
              </div>
              <div className={st.say}>
                <div className={st.text}>{selectText}</div>
              </div>
            </div>
          </div>
          <div className={st.mid}>
            <div className={st.midbg}>
              <div className={st.trans}>
                <img src={require('../../../../../../assets/qzs/youngflag.png')} alt="" />
                <img
                  className={st.titimg}
                  src={require('../../../../../../assets/qzs/zzsh1.png')}
                  alt=""
                  style={{ marginTop: 75 }}
                />
                {/* <div className={st.photo}>
                  {fileList.map((it, index) => {
                    if (index < 3) {
                      return (
                        <div className={st.photoItem} key={index}>
                          <img src={it.thumbUrl} alt="" />
                          <div>{it?.name?.split?.('.')?.[0] || ''}</div>
                        </div>
                      );
                    }
                  })}
                </div> */}
              </div>
              <div  className={st.imgs}>
                <ReactSeamlessScroll
                  list={arr}
                  ref={ref}
                  hover={true}
                  isWatch={true}
                  direction={'left'}
                  wrapperHeight={300}
                  step={0.2}
                  // singleWidth={145}
                >
                  {arr.map?.((its, index) => {
                    // {fileList.map?.((its, index) => {
                    const it = finList?.[index];
                    if (_isEmpty(it)) {
                      return (
                        <div style={{ visibility: 'hidden' }} className={st.scorllItem}>
                          {index}
                        </div>
                      );
                    }
                    return (
                      <React.Fragment key={index}>
                        <div className={st.scorllItem}>
                          <img className={st.scorllItemph} src={it?.thumbUrl} alt="" />
                          <img
                            className={st.scorllItembg}
                            src={require('@/assets/qzs/midboticon.png')}
                            // src={require('../../../../../../assets/qzs/midboticon.png')}
                          />
                        </div>
                      </React.Fragment>
                    );
                  })}
                </ReactSeamlessScroll>
              </div>
            </div>
            <div className={st.birthday} style={{ visibility: btnHide ? 'hidden' : undefined }}>
              <Birthday joinOrgDate={joinOrgDate} memCode={code}></Birthday>
            </div>
          </div>
          <div className={st.right}>
            <div className={st.rightbg}>
              <img className={st.titimg} src={require('../../../../../../assets/qzs/sddj1.png')} alt="" />
              <div style={{ height: 14 }}></div>
              <div className={st.text} dangerouslySetInnerHTML={{ __html: answer }}></div>
              <div className={st.sign}>{sign && <img src={sign} alt="" />}</div>
              <div className={st.logos}>
                <div>清镇政治生日仪式中心</div>
                <div style={{ marginRight: '21px' }}>{moment().format('YYYY年M月D日')}</div>
              </div>
            </div>
          </div>
        </div>
        <div className={st.bot}></div>
      </div>
    </div>
  );
};

export default index;
