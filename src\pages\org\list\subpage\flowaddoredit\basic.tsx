/**
 * 党组织管理-基本信息
 */
import React, { Fragment } from 'react';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Col, DatePicker, Input, Radio, Row, Switch, Tooltip, Select, InputNumber, Modal } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import moment from 'moment';
import LinkedUnit from './linkedUnit';
import { NumberReg } from "@/utils/validator";
import { connect } from "dva";
import { QuestionCircleOutlined } from '@ant-design/icons';
import { formLabel, formTip, isEmpty, jsonToTree, treeToList, findDictCodeName } from '@/utils/method';
import Date from '@/components/Date';
import YN from '@/components/YesOrNoSelect';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import _isEqual from 'lodash/isEqual';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import _trim from 'lodash/trim'
import _uniqBy from 'lodash/uniqBy'
import _differenceBy from 'lodash/differenceBy'
import DictSelect from '@/components/DictSelect';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import LinkedSpecialOrg from '@/pages/org/special/components/linkedOrg';
import { getSession, getLocalSession } from '@/utils/session';
import { tipsForChangingOrgType, superUnitOrgLinked, approveOrg } from '@/pages/org/services';
import { getMainUnitByOrg } from '@/pages/org/services/org';
import { LockMsg } from '@/pages/user/lock';
import { normalList } from '@/services';
import { validateLength, validateMobilePhoneNumber } from '@/utils/formValidator';
import ListTable from 'src/components/ListTable';
import { inflowOrganizationDInfo, auditfind } from '@/pages/flowMem/service'

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const formItemLayout3 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
};
// @ts-ignore
@connect(({ loading, commonDict }) => ({ orgAdd: loading.effects['org/add'], orgUpdate: loading.effects['org/update'], commonDict }))
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      timeKey: moment().valueOf(),
      basicInfoCollectiveEconomy: [],
      linkedDTOListLh: [],
      timeKey2: moment().valueOf(),
      d201CodeState: '',
      d201key: Math.random(),
      submitLoading: false
    };
  }
  componentDidMount() {
    this.setState({ timeKey: moment().valueOf() });
    const { basicInfo = {} } = this.props.org;
    // 关联单位 d02Code 选择 与上级党组织所在单位建立联合党支部 时，获取上级党组织列表
    if (basicInfo?.d02Code === '4') {
      this.handleClick()
    }
    if (this.props?.record?.code) {
      this.findBaseInfo()
    }

  }



  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const { org: { basicInfo = {} } = {} } = nextProps;
    const { _basicInfo } = prevState;
    if (!_isEqual(_basicInfo, basicInfo)) {
      state['linkedDTOListLh'] = basicInfo['linkedDTOList']
      state['linkedDTOList_old'] = _cloneDeep(basicInfo['linkedDTOList'])
      state['_basicInfo'] = basicInfo;
      // 这点需要对form表单重新赋值，因为194和195传入的值和返回的值会不一样
      if (basicInfo?.d01Code && basicInfo.d01Code != '25') {  //梁才--组织类别为2开头时国民经济赋默认值S
        if (basicInfo.d01Code.startsWith('1') || basicInfo.d01Code.startsWith('2')) {
          state['d194CodeSatate'] = 'S'
          state['d195CodeSatate'] = 'V0000';
        } else {
          state['d194CodeSatate'] = undefined
          state['d195CodeSatate'] = undefined;
        }
      } else {
        state['d194CodeSatate'] = undefined
        state['d195CodeSatate'] = undefined;
      }



      state['d194CodeKey'] = moment().valueOf();
      state['d195CodeKey'] = moment().valueOf();
      nextProps.form.setFieldsValue({
        ...basicInfo
      })
      state['basicInfoCollectiveEconomy'] = (basicInfo?.collectiveEconomy || []).map((it, index) => {
        return { ...it, id: moment().valueOf() + index }
      })
    }
    return state;
  };

  findBaseInfo = async () => {
    const { code = 500, data: { orgFlow = {} } = {} } = await inflowOrganizationDInfo({
      data: { code: this.props.record.code }
    })
    // console.log(res,'rrrrrrrrrrrrrrrrr')
    this.setState({
      orgFlow: orgFlow,
      d201CodeState: orgFlow?.d201Code,
    })
  }

  changeLinkInfoToForm = (e: any, key: any) => {
    if (e[key]) {
      let data: Array<object> = [];
      for (let obj of e[key]) {
        const { org = {} } = obj;
        if (org['code']) {
          //新增的关联组织
          data.push({
            code: org['code'],
            orgName: org['industryName'] || org['name'],
            orgType: org['d01Code'] || '1',
            orgTypeName: org['d01Name'] || '1',
            linkedOrgCode: org['code'],
            orgTypeCode: org['orgType'] || '1',
            isOrgMain: obj['isOrgMain'],
          });
        } else {
          //已关联的组织
          data.push(obj);
        }
      }
      e[key] = data;
    }
    return e;
  };
  handleSubmit = () => {
    const { basicInfo = {} } = this.props.org;
    const { orgFlow = {} } = this.state
    const org = getSession('org') || {};
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        let obj = undefined
        let parentOrgCode = undefined;
        if (val['parentCode'] instanceof Array) {
          parentOrgCode = val['parentCode'][0]['orgCode'];
          val['parentCode'] = val['parentCode'][0]['code'];
        }

        ['d01Code'].map(obj => {
          if (typeof val[obj] === 'object') {
            val[obj] = val[obj]['key']
          }
        });
        if (val['createDate']) {
          val['createDate'] = moment(val['createDate']).valueOf();
        }
        if (org['d01Code'].startsWith('8')) {
          val['parentFlowType'] = 2
        } else {
          val['parentFlowType'] = getSession('org')?.['isFlowStatus']
        }

        if (orgFlow['code']) {
          // 屏蔽了props上面的删除，使用深克隆的方式删除linkedDTOList和d02Code
          // delete basicInfo['linkedDTOList'];
          // delete basicInfo['d02Code'];
          let _basicInfo = _cloneDeep(orgFlow);
          delete _basicInfo['linkedDTOList'];
          delete _basicInfo['d02Code'];
          console.log({
            ..._basicInfo,
            ...val
          }, '1111111111111111111')
          obj = await this.props.dispatch({
            type: 'org/updateOrgFlow',
            payload: {
              data: {
                ...orgFlow,
                ...val
              }
            }
          })
        } else {
          console.log(val, 'vvvvvvvvvvvvvvvvvvvv')
          this.setState({ submitLoading: true });
          obj = await this.props.dispatch({
            type: 'org/addOrgFlow',
            payload: {
              data: {
                ...val
              }
            }
          })
        }
        if (obj && obj['code'] === 0) {
          Tip.success('操作提示', orgFlow['code'] ? '修改成功' : '新增成功');
          if (!orgFlow['code']) {//新增完成后关闭窗口
            this.props.close({ colseType: 'add' });
            this.props.dispatch({
              type: 'common/getTree',
              payload: {
                data: {
                  orgCodeList: [parentOrgCode],
                  excludeOrgCodeList: []
                }
              }
            })
          } else {
            // 编辑点保存后也要请求一遍树
            this.props.dispatch({
              type: 'common/getTree',
              payload: {
                data: {
                  orgCodeList: [orgFlow.orgCode],
                  excludeOrgCodeList: []
                }
              }
            })

            // this.props.dispatch({
            //   type: 'org/findOrg',
            //   payload: {
            //     code: basicInfo['code'],
            //   },
            // })
          }
        }
        this.setState({ submitLoading: false });

        if (val?.d02Code == '2') {
          this.getUpOrgLinkMainUnit();
        }
      }
    })
  };
  orgName = (rule, value, callback) => {
    if (value) {
      if (!((value.startsWith('中共') || value.startsWith('中国共产党')) && value.endsWith('委员会'))) {
        callback('组织名称需以中共开头或中国共产党开头及委员会结尾')
      }

      validateLength([rule, value, callback], 100, 300)
      // if(value.length > 100) {
      //   callback('长度不能超过100个字符')
      // }
      callback();
      // let reg = /^(中共|中国共产党)[\u4e00-\u9fa5_a-zA-Z0-9]*委员会$/;
      // if (!reg.test(value)) {
      // }
    }
  };

  // 获取党组织的关联单位
  getLinkedDTOList = () => {
    const { basicInfo = {} } = this.props.org;
    let val = this.props.form.getFieldValue('linkedDTOList') || [];
    if (this.props.form.getFieldValue('d02Code') == '2') {
      val = basicInfo['linkedDTOListUpOrg'] || [];
    }
    return val;
  }


  changeD01Msg = async (val) => {
    let d01Code = val
    if (typeof val === 'object') {
      d01Code = val?.key || undefined
    }
    const { basicInfo = {} } = this.props.org;
    const { code = 500, data = [] } = await tipsForChangingOrgType({ data: { code: basicInfo['code'], d01Code } });
    if (code === 0 && !_isEmpty(data)) {
      Modal.info({
        title: '信息提示',
        content: (
          <div>
            <div>该操作会导致以下信息被删除，且无法找回，请务必谨慎操作！！！</div>
            {data && data.map(it => {
              return (
                <div>{it.value}: {it.desc}</div>
              )
            })}
          </div>
        ),
        onOk() { },
      })
      // Tip.info('信息提示', <div>
      //   <div>以下信息会进入历史信息</div>
      //   {data && data.map(it=>{
      //     return (
      //       <div>{it.value}: {it.desc}</div>
      //     )
      //   })}
      // </div>,5);
    }
  }

  renderD01Code = (_this) => {
    const { tipMsg = {} } = _this.props;
    const { getFieldDecorator } = _this.props.form;
    const { basicInfo = {} } = _this.props.org;
    const { lockFields = [] } = basicInfo;
    const { orgFlow = {} } = this.state;
    const org: any = getSession('org') || {}
    let fieldValue = _this.props.form.getFieldValue('parentCode');
    console.log(fieldValue, 'fieldValuefieldValue')
    let itemsDisabled: any = [];
    if (org['d01Code'] === '813') {
      itemsDisabled = ['811', '812']
    }
    let pD01Code;
    if (typeof fieldValue === 'object' && fieldValue.length > 0) {
      pD01Code = fieldValue[0]['d01Code']
      if (pD01Code) {
        pD01Code = pD01Code.split('');
      }
    }
    return (
      <FormItem
        label={formLabel('组织类别', tipMsg['d01Code'])}
      >
        {getFieldDecorator('d01Code', {
          initialValue: orgFlow['d01Code'],
          rules: [{ required: true, message: '请选择组织类别' }],
        })(
          <DictTreeSelect
            ref={e => _this['d01Ref'] = e}
            backType={'object'}
            initValue={orgFlow['d01Code']}
            codeType={'dict_d01'}
            disabled={orgFlow['code']}
            placeholder={'组织类别'}
            parentDisable={true}
            // itemsDisabled={itemsDisabled}
            filter={data => {
              // if(org['d01Code'] === '813') {
              //   return data.filter(obj => obj['key']=='813');
              // }
              return data.filter(obj => obj['key'].startsWith('8'));
            }}
          />
        )}
      </FormItem>
    )
  };


  // 获取上级党组织列表
  handleClick = async (p?: any) => {
    let orgType = this.props.form.getFieldValue('d01Code');
    if (typeof (orgType) === 'object') {
      orgType = orgType.key
    }
    if (orgType == '632' || orgType == '634') {
      const { basicInfo: { code: orgCode = '' } = {} } = this.props.org;
      const { linkedDTOListLh = [] } = this.state;
      const { code }: any = getSession('org') || {};
      const { code: resCode = 500, data = [] } = await superUnitOrgLinked({
        code: orgCode || code,
        added: _isEmpty(orgCode) ? true : undefined,
      })
      if (resCode === 0) {
        this.setState({
          superUnitOrgLinkedList: data, // 上级党组织
        })
        if (p === 'add') {
          let newArr = _uniqBy([...linkedDTOListLh, ...data], (obj) => obj.unitCode) || [];
          // 需要回显原来的住单位,在切换关联单位 3和4
          let oldmainunit = this.state?.linkedDTOList_old?.find(it => it.isUnitMain == 1)?.unit
          if (oldmainunit) {
            newArr = newArr.map(it => {
              if (oldmainunit?.code == it?.unit?.code) {
                return {
                  ...it,
                  isUnitMain: 1
                }
              }
              return it
            })
          }
          this.setState({ linkedDTOListLh: newArr, timeKey2: moment().valueOf() });
          this.props.form.setFieldsValue({
            linkedDTOList: newArr
          })
        }
        if (p === 'reduce') {
          let resArr = _differenceBy(linkedDTOListLh, data, (obj: any) => obj.unitCode) || [];
          this.setState({ linkedDTOListLh: resArr, timeKey2: moment().valueOf() });
          this.props.form.setFieldsValue({
            linkedDTOList: resArr
          })
        }
      }
    }
  }
  // 关联单位点击与上级党组织相同时，获取上级党组织关联单位的主单位，d04Code判断单位类别
  getUpOrgLinkMainUnit = async () => {
    const { basicInfo: { code: orgCode = '' } = {} } = this.props.org;
    const { code }: any = getSession('org') || {};
    const { code: resCode = 500, data = {} } = await getMainUnitByOrg({
      orgCode: orgCode || code
    })
    if (resCode === 0) {
      this.setState({
        upOrgLinkMainUnit: data
      })
      // // 与上级党组织相同时，需改变国民经济赋值
      // let arr = [{unit:data}]
      // let flag = this.showGUOMINGJINGJI(arr)
      // if(flag){
      //   let d194Code = _get(arr,'[0].unit.d194Code')
      //   let d194Name = _get(arr,'[0].unit.d194Name')
      //   let d195Code = _get(arr,'[0].unit.d195Code')
      //   let d195Name = _get(arr,'[0].unit.d195Name')
      //   this.setState({
      //     d194CodeSatate:d194Code,
      //     d195CodeSatate:d195Code,
      //     d195CodeKey:moment().valueOf(),
      //     d194CodeKey:moment().valueOf()
      //   },()=>{
      //     this.props.form.setFieldsValue({
      //       d194Code:d194Code,
      //       d195Code:d195Code,
      //       d194Name:d194Name,
      //       d195Name:d195Name
      //     })
      //   })
      // }
    } else {
      this.setState({
        upOrgLinkMainUnit: {}
      })
    }
  }
  showModal = () => {
    this.getapproveOrg({ pageNum: 1 })
    this.setState({
      visible: true,
      loading: true
    })
  }
  getapproveOrg = async ({ pageNum = 1, pageSize = 10 }) => {
    const { code = 500, data: { list = [], pageNumber = 1, pageSize: size = 10, totalRow = 0 } = {}, msg } = await approveOrg({ pageNum, pageSize })
    if (code == 0) {
      this.setState({
        list: list,
        loading: false,
        pagination: {
          pageNum: pageNumber,
          pageSize: size,
          total: totalRow,
        }
      })
    }
  }
  onSelectChange = (selectedRowKeys, record) => {
    console.log(selectedRowKeys, record);
    this.setState({
      selectedRowKeys,
      selectedItems: record[0],
    });
    this.props.form.setFieldsValue({
      approveCode: record[0].code,
      approveName: record[0].name
    })
  }
  onPageChange = (page, pageSize) => {
    console.log(page, pageSize);
    this.getapproveOrg({ pageNum: page, pageSize })
  };
  render() {
    const { orgAdd, orgUpdate, tipMsg = {}, commonDict, record = {} } = this.props;
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { basicInfo = {} } = this.props.org;
    const { lockFields = [] } = basicInfo || {};
    const { selectedRowKeys, loading, visible, list, pagination, selectedItems, orgFlow = {}, d201CodeState, d201key, submitLoading } = this.state;
    console.log(orgFlow, 'orgFlow')
    console.log(orgUpdate, orgAdd, 'orgFlow')
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
      type: 'radio',
    };
    const columns = [{
      title: '组织名称',
      dataIndex: 'name',
      width: 270,
    },
    {
      title: '组织类别',
      width: 200,
      dataIndex: 'd01Name',
    },
    {
      title: '联系人',
      width: 100,
      dataIndex: 'contacter',
    },
    {
      title: '联系方式',
      width: 110,
      dataIndex: 'contactPhone',
      render: (text, record) => {
        return (
          <div>{text ? '********' : ''}</div>
        )
      }
    },
    {
      title: '党组织书记',
      width: 100,
      dataIndex: 'secretary',
    },
    ]
    return (
      <>
        <Form {...formItemLayout} key={this.state.timeKey}>
          <span style={{ pointerEvents: record.isEdit ? 'auto' : 'none' }}>
            <FormItem
              label={formLabel('上级组织', tipMsg['parentCode'])}
            >
              {getFieldDecorator('parentCode', {
                initialValue: orgFlow['parentCode'] || (getSession('org') ? [getSession('org')] : undefined), // 当新增时 自动带入树的信息
                rules: [{ required: true, message: '请选择上级组织' }],
              })(
                <OrgSelect
                  orgTypeList={['1', '2', '5']} org={orgFlow['code'] ? orgFlow : undefined}
                  initValue={orgFlow['parentOrgName'] || (getSession('org')?.['name'] || undefined)}
                  disabled={!!orgFlow['parentCode']}
                  placeholder={'上级组织'}
                  onChange={() => {
                    this.props.form.setFieldsValue({ d01Code: undefined })
                    this['d01Ref'] && this['d01Ref'].clearAll()
                  }}
                />
              )}
            </FormItem>

            <FormItem
              label={formLabel('组织全称', tipMsg['name'])}
              shouldUpdate
            >
              {getFieldDecorator('name', {
                initialValue: orgFlow['name'],
                rules: [{ required: true, message: '请输入组织全称' }, { validator: this.orgName }],
              })(
                <Input placeholder={'组织全称'} />
              )}
            </FormItem>

            {/* 组织类别 */}
            {this.renderD01Code(this)}

            <Row>
              <Col span={12}>
                <FormItem
                  label={formLabel('成立日期', tipMsg['createDate'])}
                  {...formItemLayout2}
                >
                  {getFieldDecorator('createDate', {
                    initialValue: orgFlow['createDate'] || undefined,
                    rules: [{ required: true, message: '成立日期' }],
                  })(
                    <Date placeholder={'成立日期'} mode={'YYYY-MM-DD'} disabled={orgFlow['code']} />
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  label={formLabel('党组织联系人', tipMsg['contacter'])}
                  {...formItemLayout2}
                >
                  {getFieldDecorator('contacter', {
                    initialValue: orgFlow['contacter'],
                    rules: [{ required: true, message: '党组织联系人' }, { validator: (...e) => validateLength(e, 16, 50) }],
                  })(
                    <Input placeholder={'党组织联系人'} />
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  label={formLabel('联系电话', tipMsg['contactPhone'])}
                  {...formItemLayout2}
                >
                  {getFieldDecorator('contactPhone', {
                    getValueFromEvent: e => _trim(e.target.value),
                    initialValue: orgFlow['contactPhone'],
                    // rules: [{ required: true, message: '联系电话' }, { pattern: new RegExp('((\\d{11})|^((\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1})|(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1}))$)'), message: '请输入正确的联系电话' }],
                    rules: [{ required: true, message: '联系电话' }, { validator: validateMobilePhoneNumber }],
                  })(
                    <Input placeholder={'联系电话'} />
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  label={formLabel('党组织成立类型', tipMsg['d200Code'])}
                  {...formItemLayout2}
                >
                  {getFieldDecorator('d200Code', {
                    initialValue: _isEmpty(orgFlow) ? undefined : orgFlow['d200Code'] || undefined,
                    rules: [{ required: true, message: '党组织成立类型' }],
                  })(
                    <DictSelect
                      initValue={orgFlow['d200Code']}
                      codeType={'dict_d200'}
                      placeholder={'党组织成立类型'}
                      disabled={orgFlow['code']}
                      onChange={(val) => {
                        console.log(val, 'd200Code')
                        if (val) {
                          this.setState({
                            d201CodeState: undefined,
                            d201key: Math.random()
                          })
                          this.props.form.setFieldsValue({
                            d201Code: undefined
                          })
                        }
                      }}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  label={formLabel('批准成立的党组织', tipMsg['approveName'])}
                  {...formItemLayout2}
                >
                  {getFieldDecorator('approveName', {
                    initialValue: _isEmpty(orgFlow) ? undefined : orgFlow['approveName'] || undefined,
                    rules: [{ required: true, message: '批准成立的党组织' }],
                  })(
                    <Input readOnly placeholder={'批准成立的党组织'} onClick={() => this.showModal()} disabled={orgFlow['code']} />
                  )}
                </FormItem>
                <FormItem
                  label={formLabel('', tipMsg['approveCode'])}
                  {...formItemLayout2}
                  style={{ display: 'none' }}
                >
                  {getFieldDecorator('approveCode', {
                    initialValue: _isEmpty(orgFlow) ? undefined : orgFlow['approveCode'] || undefined,
                    rules: [{ required: true, message: '批准成立的党组织' }],
                  })(
                    <Input placeholder={'批准成立的党组织'} onClick={() => this.showModal()} />
                  )}
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  label={formLabel('行政区划', tipMsg['administrativeDivision'])}
                  {...formItemLayout2}
                >
                  {getFieldDecorator('administrativeDivision', {
                    initialValue: _isEmpty(orgFlow) ? undefined : orgFlow['administrativeDivision'] || undefined,
                    rules: [{ required: true, message: '行政区划' }],
                  })(
                    <DictTreeSelect
                      parentDisable={true}
                      placeholder={'请选择行政区域'}
                      initValue={orgFlow['administrativeDivision'] || undefined}
                      disabled={orgFlow['code']}
                      codeType={'dict_d151'}
                      filter={(data) => {
                        return data.filter(it => it.key.startsWith('520') || it.key.startsWith('522'));
                      }}
                    />,
                  )}
                </FormItem>
              </Col>
              {
                (function (_this) {
                  let d200Code = _this.props.form.getFieldValue('d200Code'); // 流出类型
                  let itemsDisabled: any = []
                  if (d200Code == 1) {
                    itemsDisabled = commonDict['dict_d151'].filter(i => i.key.startsWith('520') || i.key.startsWith('522')).map(i => i.key)
                  }
                  if (d200Code == 2) {
                    itemsDisabled = commonDict['dict_d151'].filter(i => !i.key.startsWith('520') && !i.key.startsWith('522')).map(i => i.key)
                  }
                  if (d200Code) {
                    return (
                      <Col span={12}>
                        <FormItem
                          label={formLabel(d200Code == '1' ? `驻地所属行政区划` : '流出地所属行政区划', tipMsg['flowAdministrativeDivision'])}
                          {...formItemLayout2}
                        >
                          {getFieldDecorator('flowAdministrativeDivision', {
                            initialValue: _isEmpty(orgFlow) ? undefined : orgFlow['flowAdministrativeDivision'] || undefined,
                            rules: [{ required: true, message: '流出地所属行政区划' }],
                          })(
                            <DictTreeSelect
                              parentDisable={true}
                              placeholder={'请选择行政区域'}
                              initValue={orgFlow['flowAdministrativeDivision'] || undefined}
                              // itemsDisabled={itemsDisabled}
                              disabled={orgFlow['code']}
                              codeType={'dict_d151'}
                              filter={(data) => {
                                return data.filter(i => itemsDisabled.includes(i.key))
                              }}
                            />,
                          )}
                        </FormItem>
                      </Col>
                    )
                  }

                })(this)
              }
              {
                (function (_this) {
                  let d200Code = _this.props.form.getFieldValue('d200Code'); // 流出类型
                  return (
                    <Col span={12}>
                      <FormItem
                        label={formLabel('依托单位', tipMsg['d201Code'])}
                        {...formItemLayout2}
                      >
                        {getFieldDecorator('d201Code', {
                          initialValue: d201CodeState || orgFlow['d201Code'],
                          rules: [{ required: true, message: '行政区划' }],
                        })(
                          <DictTreeSelect
                            parentDisable={true}
                            key={d201key}
                            placeholder={'请选择依托单位'}
                            initValue={d201CodeState || orgFlow['d201Code']}
                            disabled={orgFlow['code']}
                            codeType={'dict_d201'}
                            filter={(data) => {
                              return data.filter(it => !it.key.startsWith(d200Code));
                            }}
                          />,
                        )}
                      </FormItem>
                    </Col>
                  )
                })(this)}

            </Row>
            <FormItem
              label={formLabel('其他依托单位单位详情', tipMsg['supportUnitDetails'])}
            >
              {getFieldDecorator('supportUnitDetails', {
                initialValue: orgFlow['supportUnitDetails'] || undefined, // 当新增时 自动带入树的信息
                rules: [{ required: true, message: '请选择上级组织' }],
              })(
                <Input.TextArea
                  disabled={orgFlow['code']}
                  placeholder="请输入"
                  showCount
                  maxLength={100}
                  rows={4}
                />
              )}
            </FormItem>
          </span>
          {
            !this.props.hideSave &&
            <div style={{ textAlign: 'center' }}>
              {
                record.isEdit &&
                // <Button type={'primary'} htmlType={'submit'} icon={<LegacyIcon type={'check'} />} onClick={this.handleSubmit} style={{ marginRight: 16 }} loading={orgFlow['code'] ? orgUpdate : orgAdd}>保存</Button>
                <Button type={'primary'} htmlType={'submit'} icon={<LegacyIcon type={'check'} />} onClick={this.handleSubmit} style={{ marginRight: 16 }} loading={submitLoading}>保存</Button>
              }

              <Button type={'primary'} danger htmlType={'button'} icon={<LegacyIcon type={'delete'} />} onClick={() => this.props.close({})}>取消</Button>
            </div>
          }

        </Form>
        <Modal
          title={'选择党组织'}
          visible={visible}
          onOk={() => {
            this.setState({
              visible: false
            })
          }}
          onCancel={() => {
            this.setState({
              visible: false
            })
          }}
          width={1000}
        >
          <ListTable
            
            columns={columns}
            data={list}
            pagination={pagination}
            rowSelection={rowSelection}
            rowKey={'code'}
            onPageChange={this.onPageChange}
          />
        </Modal>
      </>

    );
  }
}
export default Form.create<any>()(index);
