/**
 * Description: 通用Ukey的js接口
 * Author: duanlang
 * Version: V3.3.0
 * Update: 20221017
 */
var jsversion = "3.3.0_S"
var initurl = "http://127.0.0.1:8090/do";
var keyType = 4;
var ppid = 0;//sm3 hash时默认id
// jQuery.support.cors=true;
if (isIE() && isSSL()) {
	initurl = "https://127.0.0.1:433/do";
	//alert(initurl);
}
function isSSL() {
	var url = window.location.href;
	if (url.indexOf("https") != -1) {
		return true;
	}
	return false;
}
function isIE() {
	var userAgent = navigator.userAgent;
	//alert(userAgent);
	var isIE = userAgent.indexOf("NET") > -1 && userAgent.indexOf("rv") > -1;
	isIE = isIE || (userAgent.indexOf("MSIE") > -1);
	return isIE;
}

var xmlhttp;
if (window.XMLHttpRequest) {
	xmlhttp = new XMLHttpRequest();
}
else {
	xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
}
function batchHeader() {
	xmlhttp.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
	if (isIE() && isSSL()) {
		xmlhttp.setRequestHeader("Access-Control-Allow-Origin", "https://127.0.0.1:433");
	} else {
		xmlhttp.setRequestHeader("Access-Control-Allow-Origin", "http://127.0.0.1:8090");
	}

}

function encodeMessage(Message) {
	var data = "";
	for (var key in Message) {
		// alert(key + " " +Message[key]);
		data = data + key + "=" + Message[key] + "&";
	}
	data = data.substring(0, data.length - 1);
	return data;
}
function decodeMessage(Message, key) {
	var data = JSON.parse(Message);
	return data[key];
}

export var SKFKEY = {
	GetVersion: function () {
		return jsversion;
	},

	SKF_EnumDev: function () {
		var result = null;
		var rawdata = {
			"order": '02000004'
		};
		rawdata["flag"] = arguments[0];
		if (arguments.length > 1) {
			if (arguments[1] != null && arguments[1] != "")
				rawdata["provider"] = arguments[1];
		}

		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "NameList");
			} else {
				throw new Error("枚举失败,rev = " + data.rev);
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ConnectDev: function () {
		var result = null;
		var rawdata = {
			"order": '02000005'
		};
		rawdata["devName"] = arguments[0];
		if (arguments.length > 1) {
			if (arguments[1] != null && arguments[1] != "")
				rawdata["provider"] = arguments[1];
		}

		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "hDev");
			} else {
				throw new Error("连接失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_DisConnect: function () {
		var result = null;
		var rawdata = {
			"order": '02000006'
		};
		rawdata["hDev"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("枚举失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_GetDevState: function () {
		var result = null;
		var rawdata = {
			"order": '02000007'
		};
		rawdata["devName"] = arguments[0];
		if (arguments.length > 1) {
			if (arguments[1] != null && arguments[1] != "")
				rawdata["provider"] = arguments[1];
		}
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "state");
			} else {
				throw new Error("枚举失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_SetLabel: function () {
		var result = null;
		var rawdata = {
			"order": '02000008'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["Label"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("设置失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_GetDevInfo: function () {
		var result = null;
		var rawdata = {
			"order": '02000009'
		};
		rawdata["hDev"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "devInfo");
			} else {
				throw new Error("设置失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_LockDev: function () {
		var result = null;
		var rawdata = {
			"order": '02000010'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["timeOut"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("锁定失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_UnlockDev: function () {
		var result = null;
		var rawdata = {
			"order": '02000011'
		};
		rawdata["hDev"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev"); ``
			} else {
				throw new Error("解锁失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ChangeDevAuthKey: function () {
		var result = null;
		var rawdata = {
			"order": '02000012'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["authKey"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("设置失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_DevAuth: function () {
		var result = null;
		var rawdata = {
			"order": '02000013'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["authData"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("验证失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ChangePIN: function () {
		var result = null;
		var rawdata = {
			"order": '02000014'
		};
		rawdata["happ"] = arguments[0];
		rawdata["pintype"] = arguments[1];
		rawdata["oldPin"] = arguments[2];
		rawdata["newPin"] = arguments[3];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("更改失败,count=" + decodeMessage(xmlhttp.responseText, "count") + ",rev=" + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_GetPINInfo: function () {
		var result = null;
		var rawdata = {
			"order": '02000015'
		};
		rawdata["happ"] = arguments[0];
		rawdata["pintype"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "pininfo");
			} else {
				throw new Error("获取失败,rev=" + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_VerifyPIN: function () {
		var result = null;
		var rawdata = {
			"order": '02000016'
		};
		rawdata["happ"] = arguments[0];
		rawdata["pintype"] = arguments[1];
		rawdata["pin"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("校验失败,count=" + decodeMessage(xmlhttp.responseText, "count") + ",rev=" + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_UnblockPIN: function () {
		var result = null;
		var rawdata = {
			"order": '02000017'
		};
		rawdata["happ"] = arguments[0];
		rawdata["adminpin"] = arguments[1];
		rawdata["usrpin"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("解锁失败,count=" + decodeMessage(xmlhttp.responseText, "count") + ",rev=" + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ClearSecureState: function () {
		var result = null;
		var rawdata = {
			"order": '02000018'
		};
		rawdata["happ"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("清除安全状态失败,rev=" + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},


	SKF_CreateApplication: function () {
		var result = null;
		var rawdata = {
			"order": '02000019'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["appname"] = arguments[1];
		rawdata["adminPin"] = arguments[2];
		rawdata["adminretry"] = arguments[3];
		rawdata["usrPin"] = arguments[4];
		rawdata["usrretry"] = arguments[5];
		rawdata["fileright"] = arguments[6];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "hApp");
			} else {
				throw new Error("创建失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_EnumApplication: function () {
		var result = null;
		var rawdata = {
			"order": '02000020'
		};
		rawdata["hDev"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "appList");
			} else {
				throw new Error("枚举失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_DeleteApplication: function () {
		var result = null;
		var rawdata = {
			"order": '02000021'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["appname"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("删除失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_OpenApplication: function () {
		var result = null;
		var rawdata = {
			"order": '02000022'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["appname"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "hApp");
			} else {
				throw new Error("打开失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_CloseApplication: function () {
		var result = null;
		var rawdata = {
			"order": '02000023'
		};
		rawdata["happ"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("关闭失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_CreateFile: function () {
		var result = null;
		var rawdata = {
			"order": '02000024'
		};
		rawdata["happ"] = arguments[0];
		rawdata["filename"] = arguments[1];
		rawdata["filesize"] = arguments[2];
		rawdata["readright"] = arguments[3];
		rawdata["writeright"] = arguments[4];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("创建文件失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_DeleteFile: function () {
		var result = null;
		var rawdata = {
			"order": '02000025'
		};
		rawdata["happ"] = arguments[0];
		rawdata["filename"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("删除文件失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_EnumFiles: function () {
		var result = null;
		var rawdata = {
			"order": '02000026'
		};
		rawdata["happ"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "fileList");
			} else {
				throw new Error("文件枚举失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_GetFileInfo: function () {
		var result = null;
		var rawdata = {
			"order": '02000027'
		};
		rawdata["happ"] = arguments[0];
		rawdata["filename"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "fileInfo");
			} else {
				throw new Error("获取文件信息失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ReadFile: function () {
		var result = null;
		var rawdata = {
			"order": '02000028'
		};
		rawdata["happ"] = arguments[0];
		rawdata["filename"] = arguments[1];
		rawdata["offset"] = arguments[2];
		rawdata["size"] = arguments[3];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "outData");
			} else {
				throw new Error("读取文件数据失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_WriteFile: function () {
		var result = null;
		var rawdata = {
			"order": '02000029'
		};
		rawdata["happ"] = arguments[0];
		rawdata["filename"] = arguments[1];
		rawdata["offset"] = arguments[2];
		rawdata["size"] = arguments[3];
		rawdata["data"] = arguments[4];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("写入文件数据失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}
		return result;
	},

	SKF_CreateContainer: function () {
		var result = null;
		var rawdata = {
			"order": '02000030'
		};
		rawdata["happ"] = arguments[0];
		rawdata["conName"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "hCon");
			} else {
				throw new Error("创建容器失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}
		return result;
	},

	SKF_DeleteContainer: function () {
		var result = null;
		var rawdata = {
			"order": '02000031'
		};
		rawdata["happ"] = arguments[0];
		rawdata["conName"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("删除容器失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}
		return result;
	},

	SKF_OpenContainer: function () {
		var result = null;
		var rawdata = {
			"order": '02000032'
		};
		rawdata["happ"] = arguments[0];
		rawdata["conName"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "hCon");
			} else {
				throw new Error("打开容器失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}
		return result;
	},

	SKF_CloseContainer: function () {
		var result = null;
		var rawdata = {
			"order": '02000033'
		};
		rawdata["hCon"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("关闭容器失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}
		return result;
	},

	SKF_EnumContainer: function () {
		var result = null;
		var rawdata = {
			"order": '02000034'
		};
		rawdata["happ"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "conList");
			} else {
				throw new Error("枚举容器失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}
		return result;
	},

	SKF_GetContainerType: function () {
		var result = null;
		var rawdata = {
			"order": '02000035'
		};
		rawdata["hCon"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "conType");
			} else {
				throw new Error("获取容器类型失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}
		return result;
	},

	SKF_ImportCertificate: function () {
		var result = null;
		var rawdata = {
			"order": '02000036'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["flag"] = arguments[1];
		rawdata["cert"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("导入证书失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}
		return result;
	},

	SKF_ExportCertificate: function () {
		var result = null;
		var rawdata = {
			"order": '02000037'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["flag"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "outData");
			} else {
				throw new Error("导出证书失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}
		return result;
	},

	SKF_GenRandom: function (hDev, len) {
		var result = null;
		var rawdata = {
			"order": '02000038'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["randomLen"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "ranData");
			} else {
				throw new Error("生成失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_GenExtRSAKey: function () {
		var result = null;
		var rawdata = {
			"order": '02000039'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["bitsLen"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "priKey");
			} else {
				throw new Error("生成失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_GenRSAKeyPair: function () {
		var result = null;
		var rawdata = {
			"order": '02000040'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["bitsLen"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "pubKey");
			} else {
				throw new Error("生成失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ImportRSAKeyPair: function () {
		var result = null;
		var rawdata = {
			"order": '02000041'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["SymAlg"] = arguments[1];
		rawdata["warppedKey"] = arguments[2];
		rawdata["encryptedData"] = arguments[3];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "pubKey");
			} else {
				throw new Error("导入失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_RSASignData: function () {
		var result = null;
		var rawdata = {
			"order": '02000042'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["inData"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "signData");
			} else {
				throw new Error("签名失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_RSAVerify: function () {
		var result = null;
		var rawdata = {
			"order": '02000043'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["pubKey"] = arguments[1];
		rawdata["inData"] = arguments[2];
		rawdata["signData"] = arguments[3];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("验签失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_RSAExportSessionKey: function () {
		var result = null;
		var rawdata = {
			"order": '02000044'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["alg"] = arguments[1];
		rawdata["pubkey"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "data");
			} else {
				throw new Error("生成会话密钥失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ExtRSAPubKeyOperation: function () {
		var result = null;
		var rawdata = {
			"order": '02000045'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["pubKey"] = arguments[1];
		rawdata["inData"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "outData");
			} else {
				throw new Error("公钥运算失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ExtRSAPriKeyOperation: function () {
		var result = null;
		var rawdata = {
			"order": '02000046'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["priKey"] = arguments[1];
		rawdata["inData"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "outData");
			} else {
				throw new Error("私钥运算失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_GenECCKeyPair: function () {
		var result = null;
		var rawdata = {
			"order": '02000048'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["alg"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "pubKey");
			} else {
				throw new Error("生成失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_GenExtECCKey: function () {//暂不支持
		var result = null;
		var rawdata = {
			"order": '02000049'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["alg"] = arguments[1];
		var data = encodeMessage(rawdata);

		// xmlhttp.open("post",initurl,false);
		// xmlhttp.setRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=UTF-8");
		// xmlhttp.send(data);

		// if(xmlhttp.status==200){
		// 	if(decodeMessage(xmlhttp.responseText,"rev")==0){
		// 		result = decodeMessage(xmlhttp.responseText,"pubKey");
		// 	}else{
		// 		throw new Error("生成失败,rev = " + decodeMessage(xmlhttp.responseText,"rev"));
		// 	}
		// }
		// else{
		// 	throw new Error("http 失败,http status = " + xmlhttp.status);
		// }

		return result;
	},

	SKF_ImportECCKeyPair: function () {
		var result = null;
		var rawdata = {
			"order": '02000050'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["envelopedKey"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("导入失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ECCSignData: function () {
		var result = null;
		var rawdata = {
			"order": '02000051'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["inData"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "signData");
			} else {
				throw new Error("签名失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ECCVerify: function () {
		var result = null;
		var rawdata = {
			"order": '02000052'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["pubKey"] = arguments[1];
		rawdata["inData"] = arguments[2];
		rawdata["signData"] = arguments[3];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("验签失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ECCExportSessionKey: function () {
		var result = null;
		var rawdata = {
			"order": '02000053'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["alg"] = arguments[1];
		rawdata["pubKey"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "data");
			} else {
				throw new Error("导出会话密钥失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ExtECCEncrypt: function () {
		var result = null;
		var rawdata = {
			"order": '02000054'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["pubKey"] = arguments[1];
		rawdata["inData"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "cipher");
			} else {
				throw new Error("外部加密失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ExtECCDecrypt: function () {
		var result = null;
		var rawdata = {
			"order": '02000055'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["priKey"] = arguments[1];
		rawdata["cipher"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "outData");
			} else {
				throw new Error("外部解密失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ExtECCSign: function () {
		var result = null;
		var rawdata = {
			"order": '02000057'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["priKey"] = arguments[1];
		rawdata["inData"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "signData");
			} else {
				throw new Error("外部签名失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ExtECCVerify: function () {
		var result = null;
		var rawdata = {
			"order": '02000058'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["pubKey"] = arguments[1];
		rawdata["inData"] = arguments[2];
		rawdata["signData"] = arguments[3];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("外部签名失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ExportPublicKey: function () {
		var result = null;
		var rawdata = {
			"order": '02000062'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["signFlag"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "keyData");
			} else {
				throw new Error("导出公钥失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_ImportSessionKey: function () {
		var result = null;
		var rawdata = {
			"order": '02000063'
		};
		rawdata["hCon"] = arguments[0];
		rawdata["alg"] = arguments[1];
		rawdata["wrapedData"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "phKey");
			} else {
				throw new Error("导入会话密钥失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_SetSymmKey: function () {
		var result = null;
		var rawdata = {
			"order": '02000064'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["symkey"] = arguments[1];
		rawdata["symalg"] = arguments[2];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "phKey");
			} else {
				throw new Error("设置会话密钥失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_EncryptInit: function () {
		var result = null;
		var rawdata = {
			"order": '02000065'
		};
		rawdata["hkey"] = arguments[0];
		rawdata["iv"] = arguments[1];
		rawdata["padding"] = arguments[2];
		rawdata["feedbitlen"] = arguments[3];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("初始化加密失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_Encrypt: function () {
		var result = null;
		var rawdata = {
			"order": '02000066'
		};
		rawdata["hkey"] = arguments[0];
		rawdata["data"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "encData");
			} else {
				throw new Error("加密失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_EncryptUpdate: function () {
		var result = null;
		var rawdata = {
			"order": '02000067'
		};
		rawdata["hkey"] = arguments[0];
		rawdata["data"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "encData");
			} else {
				throw new Error("多包加密失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_EncryptFinal: function () {
		var result = null;
		var rawdata = {
			"order": '02000068'
		};
		rawdata["hkey"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "encData");
			} else {
				throw new Error("结束多包加密失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_DecryptInit: function () {
		var result = null;
		var rawdata = {
			"order": '02000069'
		};
		rawdata["hkey"] = arguments[0];
		rawdata["iv"] = arguments[1];
		rawdata["padding"] = arguments[2];
		rawdata["feedbitlen"] = arguments[3];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("生成失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_Decrypt: function () {
		var result = null;
		var rawdata = {
			"order": '02000070'
		};
		rawdata["hkey"] = arguments[0];
		rawdata["data"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "decData");
			} else {
				throw new Error("加密失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_DecryptUpdate: function () {
		var result = null;
		var rawdata = {
			"order": '02000071'
		};
		rawdata["hkey"] = arguments[0];
		rawdata["data"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "decData");
			} else {
				throw new Error("多包加密失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_DecryptFinal: function () {
		var result = null;
		var rawdata = {
			"order": '02000072'
		};
		rawdata["hkey"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "decData");
			} else {
				throw new Error("结束多包加密失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_DigestInit: function () {
		var result = null;
		var rawdata = {
			"order": '02000073'
		};
		rawdata["hDev"] = arguments[0];
		rawdata["alg"] = arguments[1];
		rawdata["pubkey"] = arguments[2];
		rawdata["id"] = arguments[3];

		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "hHash");
			} else {
				throw new Error("初始化杂凑失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_Digest: function () {
		var result = null;
		var rawdata = {
			"order": '02000074'
		};
		rawdata["hHash"] = arguments[0];
		rawdata["inData"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "hashData");
			} else {
				throw new Error("单包杂凑失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_DigestUpdate: function () {
		var result = null;
		var rawdata = {
			"order": '02000075'
		};
		rawdata["hHash"] = arguments[0];
		rawdata["inData"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("多包杂凑失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_DigestFinal: function () {
		var result = null;
		var rawdata = {
			"order": '02000076'
		};
		rawdata["hHash"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "hashData");
			} else {
				throw new Error("多包杂凑结束失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_MacInit: function () {
		var result = null;
		var rawdata = {
			"order": '02000077'
		};
		rawdata["hkey"] = arguments[0];
		rawdata["iv"] = arguments[1];
		rawdata["padding"] = arguments[2];
		rawdata["feedbitlen"] = arguments[3];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "hMac");
			} else {
				throw new Error("初始化MAC失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_Mac: function () {
		var result = null;
		var rawdata = {
			"order": '02000078'
		};
		rawdata["hMac"] = arguments[0];
		rawdata["iData"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "macData");
			} else {
				throw new Error("单包MAC失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_MacUpdate: function () {
		var result = null;
		var rawdata = {
			"order": '02000079'
		};
		rawdata["hMac"] = arguments[0];
		rawdata["iData"] = arguments[1];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("多包MAC失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_MacFinal: function () {
		var result = null;
		var rawdata = {
			"order": '02000080'
		};
		rawdata["hMac"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "macData");
			} else {
				throw new Error("多包MAC结束失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	},

	SKF_CloseHandle: function () {
		var result = null;
		var rawdata = {
			"order": '02000081'
		};
		rawdata["hKey"] = arguments[0];
		var data = encodeMessage(rawdata);

		xmlhttp.open("post", initurl, false);
		batchHeader();
		xmlhttp.send(data);

		if (xmlhttp.status == 200) {
			if (decodeMessage(xmlhttp.responseText, "rev") == 0) {
				result = decodeMessage(xmlhttp.responseText, "rev");
			} else {
				throw new Error("关闭句柄失败,rev = " + decodeMessage(xmlhttp.responseText, "rev"));
			}
		}
		else {
			throw new Error("http 失败,http status = " + xmlhttp.status);
		}

		return result;
	}
}