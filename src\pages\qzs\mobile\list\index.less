.searchBg {
  background: url('../../../../assets/qzs/bg1.webp') no-repeat;
  background-size: 100% 100%;
}


.searchMem {
  .searchBg;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 50px;
  .center {
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: auto;
    width: 100%;
    .box {
      
    }
    .listItem {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #ffffff;
      border-radius: 8px;
      padding: 10px;
      box-shadow: 3px 3px 10px #ccc;
      margin-bottom: 20px;
      cursor: pointer;
      .photo {
        > img {
          width: 80px;
          height: calc(80px * 1.33);
        }
        margin-right: 16px;
      }
      .info {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #62471e;

        .name {
          font-family: Source Han Serif SC;
          font-weight: 800;
          font-size: 28px;
          color: #be0c10;
          display: flex;
          align-items: center;
          > div {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            /* autoprefixer: on */
          }
          > span {
            text-align: center;
            width: 50px;
            margin-left: 4px;
            padding: 2px 4px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            display: inline-block;
            background: url('../../../../assets/qzs/icon1.png') no-repeat;
            background-size: 100% 100%;
          }
        }
        .desc {
          margin-top: 10px;
        }
      }
    }
    .pagination {
      margin-top: 10px;
      text-align: right;
      width: 100%;
    }
  }
}
