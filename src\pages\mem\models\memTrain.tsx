import modelExtend from 'dva-model-extend';
import { listPageModel } from 'src/utils/common-model';
import {
  trainAdd,
  listTrainByCode,
  findTrainByCode,
  deleteTrainByCode
} from '../services/train';
import { getSession } from "@/utils/session"; //模拟数据
const memTrain = modelExtend(listPageModel, {
  namespace: "memTrain",
  state: {
    basicInfo: {} // 人员基本信息
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if (pathname === '/mem/manage') {
          const memCode = JSON.parse(sessionStorage.getItem('user') || "")['memCode'];
          const defaultParas = {
            pageNum: 1,
            pageSize: 10,
          };
          const dictData = ['dict_d09', 'dict_d07', 'dict_d01', 'dict_d02', 'dict_d03','dict_d194','dict_d195'];
          for (let obj of dictData) {
            dispatch({
              type: 'commonDict/getDictTree',
              payload: {
                data: {
                  dicName: obj
                }
              }
            });
          }
          // dispatch({
          //   type: 'getList',
          //   payload: {
          //     data: {
          //       memCode: memCode,
          //       ...defaultParas,
          //       ...query,
          //     }
          //   }
          // })
        }
      });
    }
  },
  effects: {
    // 列表
    *trainAdd({payload}, {call, put}) {
      const info = yield call(trainAdd, payload);
        return Promise.resolve(info);
    },
    *getList({payload}, {call, put}) {
      const { code, data } = yield call(listTrainByCode, payload);
      if (code === 0) {
        return Promise.resolve(data);
      }
      return Promise.reject();
    },
    *findTrainByCode({payload}, {call, put}) {
      const { code, data } = yield call(findTrainByCode, payload);
      if (code === 0) {
        return Promise.resolve(data);
      }
      return Promise.reject();
    },
    *deleteTrainByCode({payload}, {call, put}) {
      const info = yield call(deleteTrainByCode, payload);
      return Promise.resolve(info)
    },
  }
});
export default memTrain;
