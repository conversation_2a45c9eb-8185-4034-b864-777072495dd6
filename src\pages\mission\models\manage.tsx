import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {insertTask,taskList,delTask,findTaskByCode,updateTask,findCheckList} from '../services'
import {getSession} from "@/utils/session";
import { changeListPayQuery } from '@/utils/method.js';
const mission = modelExtend(listPageModel,{
  namespace: "mission",
  state:{
    basicInfo:{},
    basicInfoData:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if(pathname==='/mission/manage'){
          let roles=getSession('roles') || {};
          let defaultParas={
            pageNum:1,
            pageSize:10,
          };
        dispatch({
          type:'getList',
          payload:{
            data:{
              orgCode:roles['managerOrgId'],
              orgOrgCode:roles['managerOrgCode'],
              ...defaultParas,
              ...query,
            }
          }
        })
      }
      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put,select }) {
      const {filter,taskName}=yield select(state=>state['mission']);
      const {data={}} = yield call(taskList, {data:{...payload['data'],...filter,taskName}});
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      });
    },
    *add({ payload }, { call, put,select }) {
      return yield call(insertTask, { data: { ...payload } });
    },
    *edit({ payload }, { call, put,select }) {
      return yield call(updateTask, { data: { ...payload } });
    },
    *del({ payload }, { call, put,select }) {
      return yield call(delTask, { data: { ...payload } });
    },
    *getDtail({ payload }, { call, put,select }) {
      return yield call(findTaskByCode, payload);
    },
    *findCheckList({ payload }, { call, put,select }) {
      return yield call(findTaskByCode, payload);
    },
  }
});
export default mission;
