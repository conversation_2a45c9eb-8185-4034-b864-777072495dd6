import React from 'react';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Checkbox,
  Input,
  message,
  Modal,
  Radio,
  Select,
  Tabs,
  TreeSelect,
  Button,
  Row,
  Col,
} from 'antd';
import { FormComponentProps } from '@ant-design/compatible/lib/form';
import { connect } from 'dva';
import ListTable from '@/components/ListTable';
import { isEmpty } from '@/utils/method.js';
import { throttle, jsonToTree } from '@/utils/method'
import { withContext } from '@/utils/global.jsx';
import UnitList from '../unitList';
import MemSelect from '@/components/MemSelect';
import UnitTree from '../unitTree'
import styles from './index.less';
import Notice from 'src/components/Tip';
import { inspect } from 'util';
import { array } from 'prop-types';
import { getSession } from '@/utils/session';
import _isEqual from 'lodash/isEqual';
import _get from 'lodash/get';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const RadioGroup = Radio.Group;
const TabPane = Tabs.TabPane;
const Option = Select.Option;
const Search = Input.Search;
const TreeNode = TreeSelect.TreeNode;

interface propsType extends FormComponentProps {
  title?: string,
  handleOk?: () => void,
  up?: boolean
}
@connect(({ user, login, common }) => ({
  user,
  login,
  common
}))
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      isTab: false,
      orgCode: '',
      id: [],
      value: undefined,
      unitvsb: false,
      unitTit: '',
      unitCode: '',
      unitValue: {},
      slKey: [],
      editCheck: false,
      nodevsb: false,
      checkArr: [],
      unitData: [],
      msg: null,
      dataInfo: {},
      orgName: '',
      isUp: true,
      check: false,
      keys: [{ id: 1, checked: false }],
      destroy: true,
      disabledSubmit: false,
      loading: false,
    }
  }

  static getDerivedStateFromProps = (nextProps:any, prevState:any) => {
    let org = getSession('org') || {};
    const state = {};
    const {user: { getList = []} = {}, data: { id = '' } = {}} = nextProps;
    const {_getList} = prevState;
    // 新增时，附上默认组织和默认角色
    if(!_isEqual(_getList,getList) && !id){
      let findDefault = getList.find(it=>it.name == '默认角色') || {};
      state['unitData'] = [{
        code: org['code'],
        name: org['name'],
        orgCode: org['orgCode'],
        roleId: _get(findDefault,'id',undefined),
        shortName: org['name'],
      }];
      state['keys'] = [{
        checked: true,
        id: 1,
        orgName: org['name'],
        roleId:_get(findDefault,'id',undefined),
        roleName:_get(findDefault,'name',undefined),
      }]
    }
    return state;
  };

  showModal = () => {
    const { data: { id = '' } = {}, keyword = '' } = this.props;
    let org = getSession('org') || {};
    this.setState({
      dataInfo:{},
      visible: true,
      destroy: false,
      orgCode: org['orgCode'],
      code: org['code'],
      keyword
    });
    this.props.dispatch({
      type: 'user/getListing',
      payload: {
      }
    });
    // this.selectList();
    this.updateRoleId();
    id && this.editUser(id);
  };

  componentDidMount = () => {
    let { dataInfo } = this.props;
    if (isEmpty(dataInfo)) {
      this.setState({ isTab: true })
    } else {
      this.setState({ isTab: false })
    }
  };
  //编辑查询单个角色信息------
  editUser = (id) => {
    this.props.dispatch({
      type: 'user/editUsers',
      payload: {
        id: id
      }
    }).then(res => {
      let { unitData, keys, editCheck } = this.state;
      let orgName = '';
      if (res.code === 0) {
        const { data = {} } = res;
        const { manages = [] } = data;
        let { user: { getList = [] } = {} } = this.props;
        unitData = manages.map((item, index) => {
          let obj = {
            code: item['managerOrgId'],
            name: item['managerOrgName'],
            orgCode: item['managerOrgCode'],
            shortName: item['managerOrgName'],
            roleId: item['roleId']
          };
          return obj
        });
        keys = manages.map((item, index) => {
          let obj = {
            id: index + 1,
            checked: item['isDefault'] === 1,
            orgName: item['managerOrgName'],
            roleId: item['roleId'],
            roleName: item['roleName']
          };
          return obj
        });
        // keys.forEach(j => {
        //   if (!getList.map(i => i['id']).includes(j['roleId'])) {
        //     j['roleId']=-1;
        //   }
        // });
        unitData.forEach(j => {
          if (!getList.map(i => i['id']).includes(j['roleId'])) {
            j['roleId'] = -1;
          }
        });
        // let keyss=keys.filter(it=>it['roleId']!==-1);
        let unitDates = unitData.filter(it => it['roleId'] !== -1);
        let kkey = [{ id: 1, checked: false }];
        this.setState({ dataInfo: res['data'], unitData: isEmpty(unitDates) ? [] : unitDates, keys: keys, editCheck: data['readOnly'] })
      }
    })
  };
  updateRoleId = () => {
    if (!isEmpty(this.props.dataInfo)) {
      let { dataInfo: { currentUserRoleId = '', id = '' } = {} } = this.props;
      this.props.dispatch({
        type: 'user/updateRoleIding',
        payload: {
          roleID: id,
          userID: currentUserRoleId
        }
      });
    }
  };
  //验证用户名是否存在-----
  findUserByAccount = async (value) => {
    const { title } = this.props;
    if (title === '新增用户' && !isEmpty(value)) {
      this.props.dispatch({
        type: 'user/getExist',
        payload: {
          data: {
            account: value
          }
        }
      }).then(res => {
        if (res.code === 0) {
          Notice.info("操作提示", '用户名可以使用');
          this.setState({disabledSubmit:false});
        }else {
          this.setState({disabledSubmit:true});
        }
        this.setState({ msg: res.code })
      });
    }
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      unitData: [],
      destroy: true,
      keys: [{ id: 1, checked: false }],
    });
    this.handleUnitCancel();
    // this.props.dispatch({
    //   type:'common/clear',
    //   payload:{}
    // })
  };
  handleUnitCancel = () => {
    this.setState({ unitvsb: false });
    this.props.form.resetFields()
  };
  isOk = (e, v, selKey) => {
    let { slKey = [] } = this.state;
    if (e.target.checked) {
      if (slKey.length > 0) {
        slKey = [...slKey, v.id];
      } else {
        slKey = [...selKey, v.id];
      }
    } else {
      if (slKey.length > 0) {
        slKey = slKey.filter(o => o !== v.id)
      } else {
        slKey = selKey.filter(o => o !== v.id)
      }
    }
    this.setState({ slKey });
  };
  //表单验证----------------------------------
  validFunction = (rule, value, callback) => {
    if (!isEmpty(value)) {
      switch (rule.field) {
        case 'account':
          if (!(/^[0-9a-zA-Z]+$/).test(value)) {
            return callback('登录名只能填写数字和字母组合')
          } else if ((/\s+/g).test(value)) {
            return callback('登录名不能包含空格')
          }
          break;
        case 'name':
          if (value.length > 5) {
            return callback('姓名不能超过5个字')
          } else if ((/\s+/g).test(value)) {
            return callback('姓名不能包含空格')
          }
          break;
        case 'password':
          if (value.length < 8) {
            return callback('密码长度不足')
          } else if (!(/^(?=.*\d)(?=.*[a-zA-Z])[\da-zA-Z~!@#$%^&*]{8,16}$/).test(value)) {
            return callback('格式有误')
          } else if ((/\s+/g).test(value)) {
            return callback('密码不能包含空格')
          }
          break;
        case 'memCode':
          if (isEmpty(value)) {
            return callback('请选择')
          }
          break;
      }
    }
    callback()
  };
  findAccount = () => {
    let value = this.props.form.getFieldValue('account');
    this.findUserByAccount(value);
  };
  isReadOnly = (v) => {
    this.setState({ check: v.target.checked })
  };
  editCheck = (e) => {
    let check = e.target.checked;
    this.setState({ editCheck: check })
  };
  //树返回数据
  checkTree = (v, index) => {
    let { unitData, keys } = this.state;
    v.index = index;
    if (isEmpty(unitData[index])) {
      unitData.push(v)
    } else {
      unitData.splice(index, 1, v)
    }
    if (!keys.map(i => i.orgName).includes(v.name)) {
      keys[index].orgName = v.name;
      if (keys.map(i => i.checked).includes(true)) {
        keys[index].checked = false;
      } else {
        keys[0].checked = true
      }
    } else {
      Notice.info("操作提示", '已存在管理组织');
      this.props.form.resetFields([`managerOrgCode${v.index + 1}`])
    }
    this.setState({ unitData, keys })
  };
  //刷新列表
  upList = () => {
    const { orgCode = '' } = this.state;
    this.props.dispatch({
      type: 'user/list',
      payload: {
        data: {
          pageNumber: 1,
          pageSize: 10,
          code: orgCode
        }
      }
    })
  };
  getSearch = (pageNum = 1, size = 10) => {
    this.props.dispatch({
      type: 'user/getSearchUserByKeyword',
      payload: {
        pageNum: pageNum,
        pageSize: size,
        orgCode: this.state.orgCode,
        keyword: this.state['keyword']
      }
    })
  };
  handleOk = (isEdit) => {
    this.props.form.validateFieldsAndScroll((errors, values) => {
      if (errors || isEmpty(values)) {
        return;
      }
      let { parentCode, code, orgCode } = JSON.parse(sessionStorage['org']);
      let { editCheck, unitData, keys, dataInfo, memInfo = [] } = this.state;
      const { data } = this.props;
      let orgID = '';
      let orgCODE = '';
      let manages = unitData.map((item, index) => {
        let obj = {
          managerOrgCode: item.orgCode,
          managerOrgId: item.code,
          roleId: values[`roleId${index + 1}`],
          isDefault: keys[index]['checked'] ? 1 : 0,
        };
        if (keys[index]['checked']) {
          orgID = item.code;
          orgCODE = item.orgCode;
        }
        return obj
      });
      if (isEmpty(manages)) {
        return message.warning('管理单位不能为空')
      }
      const { isReadOnly, memCode, ...value } = values;

      for (let o in value) {
        if (o.substring(0, 14) === 'managerOrgCode' || o.substring(0, 6) === 'roleId') {
          delete value[o]
        }
      }
      let val = {
        ...value,
        manages,
        readOnly: editCheck ? 1 : 0,
        orgCode: orgCODE,
        orgId: orgID,
        memCode: isEmpty(memInfo) ? memInfo['memCode'] : memInfo[0]['code'],//关联人员id
        phone: isEmpty(memInfo) ? memInfo['phone'] : memInfo[0]['phone'],//关联人员phone
        memName: isEmpty(memInfo) ? memInfo['name'] : memInfo[0]['name'],//关联人员name
      };
      for (let obj in val) {
        if (val[obj] === '' || val[obj] === undefined || obj.substring(0, obj.length - 1) === 'roleId') {
          delete val[obj];
        }
      }
      this.setState({loading:true});
      if (isEdit) {
        this.props.dispatch({
          type: 'user/edit',
          payload: {
            data: {
              ...val,
              id: dataInfo['id']
            }
          }
        }).then(res => {
          if (res.code === 0) {
            if (isEmpty(this.state.keyword)) {
              this.upList();
            } else {
              this.getSearch()
            }
            Notice.info("操作提示", '保存成功');
            this.handleCancel();
          }
        })
      } else {
        this.props.dispatch({
          type: 'user/add',
          payload: {
            data: {
              ...val
            }
          }
        }).then(res => {
          if (res.code === 0) {
            if (isEmpty(this.state.keyword)) {
              this.upList();
            } else {
              this.getSearch()
            }
            Notice.info("操作提示", '保存成功');
            this.handleCancel();
          }
        })
      }
      this.setState({loading:false});
    });
  };
  addForm = () => {
    let { keys } = this.state;
    let a = { id: keys.length + 1 };
    keys.push({ ...a, checked: false });
    this.setState({ keys })
  };
  minus = (item, indexs) => {
    let { keys, unitData } = this.state;
    if (keys.length < 2) {
      message.warning('已经是最后一个了')
    } else {
      let key = keys.filter((it, index) => index !== indexs);
      let unitDatas = unitData.filter((it, index) => index !== indexs);
      if (!key.map(i => i.checked).includes(true)) {
        key[0].checked = true
      }
      this.setState({ keys: key, unitData: unitDatas });
      this.props.form.resetFields([`managerOrgCode${indexs + 1}`, `roleId${indexs + 1}`])
    }
  };
  ismr = (e, v, k) => {
    v.checked = e.target.checked;
    let { keys } = this.state;
    keys.map((item, index) => {
      if (item.id === v.id) {
        item.checked = e.target.checked
      } else {
        item.checked = false
      }
    });
    this.setState({ keys })
  };
  outMem = (e) => {
    this.setState({ memInfo: e })
  };
  roleOnchange = (v, index) => {
    // let value=this.props.form.getFieldValue('account');
    let { keys, unitData } = this.state;
    unitData[index]['roleId'] = v
    keys[index]['roleId'] = v
    this.setState({ roleVal: v, unitData, keys })
  };
  renderTreeNodes = data => data.map((item) => {
    if (item.children) {
      return (
        <TreeNode title={item['name']} key={item['id']} value={item['id']}>
          {this.renderTreeNodes(item.children)}
        </TreeNode>
      );
    }
    return <TreeNode title={item['name']} key={item['id']} value={item['id']} />;
  });
  render(): React.ReactNode {
    let { title, children, keyword, user: { getList = [], getRole = [], data = [], treeData = [], RoleIdInfo = [], repeat = null } = {}, common = {}, orgCode = '' } = this.props;
    const { unitData, dataInfo, orgName, keys } = this.state;
    let a = jsonToTree(getList, 'parent_id', 'id', isEmpty(getList) ? '' : getList[0]['parent_id']);
    let isEdit = false;
    let selKey: Array<string> = [];
    if (RoleIdInfo && RoleIdInfo.length > 0) {
      for (let obj of RoleIdInfo) {
        selKey.push(obj['id'])
      }
    }
    if (title === '新增用户') {
      isEdit = false
    } else {
      isEdit = true
    }
    const { getFieldDecorator } = this.props.form;

    let formItems = keys.length && keys.map((item, index) => {
      return (
        <React.Fragment key={index}>
          <Row gutter={16}>
            <Col span={2}>{index + 1}</Col>
            <Col span={8}>
              <FormItem>
                {getFieldDecorator(`managerOrgCode${index + 1}`, {
                  initialValue: isEmpty(item['orgName']) ? null : item['orgName'],
                  rules: [{ required: true, message: '请选择管理组织!' }],
                })(
                  <UnitTree onSelect={(v) => this.checkTree(v, index)} dataInfo={{}}>
                    <Input
                      // onClick={()=>this.isFocus()}
                      value={isEmpty(item['orgName']) ? null : item['orgName']}
                      placeholder="请选择管理组织"
                      readOnly
                    />
                  </UnitTree>
                )}
              </FormItem>
            </Col>
            <Col span={8}>
              <FormItem>
                {getFieldDecorator(`roleId${index + 1}`, {
                  //isEmpty(unitData[index]['roleId'])?null:unitData[index]['roleId']:item['roleId']
                  initialValue: isEmpty(item['roleId']) ? null : item['roleId'],
                  rules: [{ required: true, message: '请选择角色!' }],
                })(
                  <TreeSelect
                    // value={this.state['roleVal']}
                    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    placeholder="请选择角色"
                    onChange={(e) => this.roleOnchange(e, index)}
                  >
                    {
                      a && this.renderTreeNodes(a)
                    }
                  </TreeSelect>
                )}
              </FormItem>
            </Col>

            <Col span={2} style={{ textAlign: 'center' }}><Radio onChange={e => this.ismr(e, item, index)} checked={item.checked} /></Col>
            <Col span={2} style={{ marginLeft: '20px' }}>
              <Button onClick={() => this.minus(item, index)} size={'small'} icon={<DeleteOutlined />} className='del'>删除</Button>
              {/*<Icon onClick={()=>this.minus(index)} type="delete" style={{color:'red'}}/>*/}
            </Col>
          </Row>
        </React.Fragment>
      );
    });
    return (
      <React.Fragment>
        {
          children ? React.cloneElement(children as any, {
            onClick: this.showModal,
            key: 'container'
          }) : null
        }
        {
          !this.state.destroy &&
          <Modal
            title={title || "请输入标题"}
            visible={this.state.visible}
            // onOk={() => this.handleOk(isEdit)}
            onCancel={this.handleCancel}
            width={800}
            maskClosable={false}
            footer={[
              <Button key={1} htmlType={'button'} onClick={this.handleCancel} style={{marginRight:'5px'}}>取消</Button>,
              <Button key={2} htmlType={'button'} onClick={()=>this.handleOk(isEdit)} loading={this.state.loading} disabled={this.state.disabledSubmit} type={'primary'}>确定</Button>,
            ]}
          >
            <React.Fragment>
              <Form {...formItemLayout}>
                <FormItem
                  label={'登录名称'}
                >
                  {getFieldDecorator('account', {
                    initialValue: isEmpty(dataInfo) ? null : dataInfo.account,
                    rules: [
                      { required: true, message: '请填写登录名称!' },
                      { validator: this.validFunction }
                    ],
                  })(
                    <Input placeholder="请输入登录名" onBlur={() => this.findAccount()} disabled={!isEmpty(dataInfo)} />
                  )}
                </FormItem>
                {/* <FormItem
                    label={'真实名称'}
                  >
                    {getFieldDecorator('name', {
                      initialValue:isEmpty(dataInfo)?null:dataInfo.name,
                      rules: [
                        { required: true, message: '请填写真实名称!' },
                        { validator: this.validFunction }
                      ],
                    })(
                      <Input placeholder="请输入用户姓名" />
                    )}
                  </FormItem> */}
                {
                  isEmpty(dataInfo) &&
                  <FormItem
                    label={'登录密码'}
                  >
                    {getFieldDecorator('password', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.password,
                      rules: [
                        { required: true, message: '请填写登录密码!' },
                        { validator: this.validFunction }
                      ],
                    })(
                      <Input placeholder="请输入密码" />
                    )}
                  </FormItem>
                }
                <FormItem
                  label={'关联党员'}
                >
                  {getFieldDecorator('memCode', {
                    initialValue: isEmpty(dataInfo) ? null : dataInfo.memName,
                    rules: [
                      { required: true, message: '请选择关联党员!' }],
                  })(
                    <MemSelect onChange={this.outMem} initValue={dataInfo['memName']} />
                  )}
                </FormItem>
                {
                  isEdit &&
                  <FormItem
                    label={'状态'}
                  >
                    {getFieldDecorator('isLock', {
                      initialValue: isEmpty(dataInfo) ? 0 : dataInfo.isLock,
                      // rules: [{ required: true, message: '必填!' }],
                    })(
                      <Select>
                        <Option value={0}>正常</Option>
                        <Option value={1}>锁定</Option>
                      </Select>
                    )}
                  </FormItem>
                }
                <FormItem
                  label={'只 读'}
                >
                  {getFieldDecorator('readOnly', {
                    // initialValue:isEmpty(dataInfo)?null:dataInfo['readOnly']===1,
                    // rules: [{ required: true, message: '请输入!' }],
                  })(
                    <div>
                      <Checkbox onChange={this.editCheck} checked={this.state.editCheck} />
                      <span>（打√表示只能查看内容，无编辑权限）</span>
                    </div>
                  )}
                </FormItem>
                {/* <div style={{display: 'none'}}> */}
                  <FormItem
                    label={'管理权限'}
                    className={styles.permissionsBody}
                  >
                    <React.Fragment>
                      <Row>
                        <Col span={2}>
                          序号
                        </Col>
                        <Col span={8}>管理组织</Col>
                        <Col span={8}>系统角色</Col>
                        <Col span={5}>是否默认</Col>
                      </Row>
                      {formItems}
                    </React.Fragment>
                  </FormItem>
                {/* </div> */}
              </Form>
              <div style={{ textAlign: 'center',display: 'none' }}>
                <Button type="dashed" onClick={this.addForm} style={{ width: '40%' }}>
                  <PlusOutlined /> 添加权限
                </Button>
              </div>
            </React.Fragment>
          </Modal>
        }

      </React.Fragment>
    );
  }
}
export default withContext(Form.create()(index));
