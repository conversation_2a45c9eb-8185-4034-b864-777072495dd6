import React, { Component, useEffect } from 'react';
import ReactDOM, { createPortal } from 'react-dom';
import { Drawer } from 'antd';

class Dialog extends Component{
  state = {
    visible:false,
    msg:undefined,
  };
  //打开弹窗
  open =(options)=>{
    // document.body.appendChild(div);
    this.setState({
      visible:true,
      msg:options
    })
  };
  //关闭弹窗
  onClose=()=>{
    this.setState({
      visible:false
    })
  }
  render(){
    const {visible,msg}=this.state;
    return (
      <Drawer
        title="提示信息"
        placement={'right'}
        onClose={this.onClose}
        visible={visible}
        style={{zIndex:99999}}
      >
        {
          msg && Array.isArray(msg) ? msg.map((val,index)=>{
            return <p key={index}>{val}</p>
          }) : <p>{msg}</p>
        }

      </Drawer>
    );
  }
}
//
const div = document.createElement('div');
document.body.appendChild(div);
// export default Box;
export default ReactDOM.render(<Dialog/>,div);
