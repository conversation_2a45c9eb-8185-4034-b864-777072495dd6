/**
 * 流动党员
 */
import React, { Fragment } from 'react';
import { connect } from "dva";
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs, Dropdown, Menu, Modal, Row, Col } from 'antd';
import NowOrg from 'src/components/NowOrg';
import InCity from './inCity';
import OutsideCity from './outsideCity';
import Continue from './continue';
import moment from 'moment'
import DictArea from 'src/components/DictArea';
import { getSession } from "@/utils/session";
import OutWithdraw from './outWithdraw';
import { isEmpty, setListHeight } from '@/utils/method';
import styles from '@/pages/flowMem/flowBack/index.less';
import { tableColConfig } from '@/services';
import { changeMsgTip } from '@/utils/method';
import ExportInfo from '@/components/Export';
const Search = Input.Search;
const TabPane = Tabs.TabPane;

@connect(({ unit, commonDict, loading, flowMem }) => ({ flowMem, unit, commonDict, loading: loading.effects['unit/getList'] }))
export default class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {},//筛选器
      search: {},//搜索框
      view: false,
      subordinate:getSession('subordinate')
    };
  }
  addOrEdit = (record?: object) => {
    // OutRegistration.show();
  };
  export = () => {

  };

  componentDidMount() {
    const org = getSession('org') || {};
    this.setState({ orgCode: org['orgCode'] });
    setListHeight(this);
    this.selectList(1, 10, org['orgCode']);
    this.getMsg();
  }
  // 获取tipMsg
  getMsg=()=>{
    tableColConfig({id:'ccp_mem_flow'}).then(res=>{
      if(res['code']=='0'){
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg:msg,
        });
      }
    });
  }
  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org = getSession('org') || {};
    const subordinate = getSession('subordinate') || '0';
    if ((!isEmpty(this.state['orgCode']) && this.state['orgCode'] !== org['orgCode']) || (subordinate !== this.state.subordinate)) {
      this.setState({
        orgCode: org['orgCode'],
        subordinate,
      }, () => {
        this.selectList(1, 10, org['orgCode'])
      })
    }
  }
  selectList = (pageNum = 1, size = 10, code = '') => {
    this.props.dispatch({
      type: 'flowMem/inList',
      payload: {
        pageNum: pageNum,
        pageSize: size,
        orgCode: code,
        ...this.state.filter,
        ...this.state.search,
      }
    })
  };
  onPageChange = (page, pageSize) => {
    this.selectList(page, pageSize, this.state.orgCode)
  };

  filterChange = (val) => {
    // this.setState({
    //   filter:val,
    // },()=>this.action());
    const org = getSession('org') || {};
    this.setState({
      filter: val,
    }, () => this.selectList(1, 10, org['orgCode']));
  };
  action = (val?: any) => {
    // const {search,filter}=this.state;
    // const {pagination={}}=this.props.unit;
    // const {current,pageSize}=pagination;
    // const {query}=this.props.location;
    // const org=getSession('org')||{};
    // this.props.dispatch({
    //   type:'unit/getList',
    //   payload:{
    //     data:{
    //       mainUnitOrgCode:org['orgCode'],
    //       manageUnitOrgCode:org['orgCode'],
    //       isCreateOrg:0,
    //       pageNum:current,
    //       pageSize,
    //       ...query,
    //       ...search,
    //       ...filter,
    //       ...val,
    //     }
    //   }
    // })
  };
  confirm = (record) => {
    this.props.dispatch({
      type: 'flowMem/del',
      payload: {
        data: {
          code: record['code']
        }
      }
    }).then(res => {
      if (res['code'] === 0) {
        this.selectList(1, 10, this.state['orgCode'])
      }
    });
  };
  goContinue = (record) => {
    this.setState({ continueInfo: record });
    this['Continue'].showModal();
  };
  changeList = (v) => {
    if (v) {
      this.selectList(1, 10, this.state['orgCode'])
    }
  };
  handleOk = () => {
    this.setState({ view: false })
  };
  handleCancel = () => {
    this.setState({ view: false })
  };
  view = (record) => {
    this.setState({ view: true });
    this.props.dispatch({
      type: 'flowMem/detail',
      payload: {
        code: record['code']
      }
    }).then(res => {
      if (res.code === 0) {
        this.setState({
          detailInfo: res['data']
        })
      }
    })
  };
  goBack = (record) => {
    this.setState({ record });
    this['outWithdraw'].showModal();
  };
  exportInfo= async ()=>{
    this.setState({
      inflowsDownload:true,
    })
    await this['inflows'].submitNoModal();
    this.setState({
      inflowsDownload:false,
    })
  };

  render() {
    const { loading, commonDict, flowMem: { list = [], pagination = {} } } = this.props;
    const { dataInfo, filterHeight, continueInfo, detailInfo = {} } = this.state;
    const org = getSession('org') || {};
    const columns = [
      {
        title: '姓名',
        dataIndex: 'memName',
        width: 200
      },
      {
        title: '流出组织',
        dataIndex: 'memOrgName',
        width: 200
      },
      {
        title: '流入组织',
        dataIndex: 'outflowOrgName',
        width: 200
      },
      {
        title: '流动类型',
        dataIndex: 'isProvOutName',
        width: 200
      },
      {
        title:'匹配情况',
        dataIndex:'matchSituation',
        width:200,
      },
      {
        title: '流出时间',
        dataIndex: 'outflowDate',
        width: 200,
        render: text => {
          return <span>{moment(text).format('YYYY-MM-DD')}</span>
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 250,
        render: (text, record) => {
          const { flowStatus = ''}= record;
          return (
            <React.Fragment>
              <a onClick={() => this.view(record)}>查看</a>
              <Divider type="vertical" />
              {
                flowStatus !== 2 &&
                <Fragment>
                  <a onClick={() => this.goBack(record)}>停止流动</a>
                  <Divider type="vertical" />
                </Fragment>
              }
              {/*<a onClick={() => this.goContinue(record)}>继续流动</a>*/}
              {/*<Divider type="vertical" />*/}
              <Popconfirm title="确定要删除吗？" onConfirm={() => this.confirm(record)}>
                <a className='del'>删除</a>
              </Popconfirm>
            </React.Fragment>
          )
        },
      },
    ];
    const filterData = [
      {
        key: 'sexCodeList', name: '人员性别', value: [{ key: '1', name: '男' }, { key: '0', name: '女' }],
      },
      {
        key: 'd09CodeList', name: '工作岗位', value: commonDict[`dict_d09_tree`],
      },
      {
        key: 'd07CodeList', name: '学历教育', value: commonDict[`dict_d07_tree`],
      },
      {
        key: 'd41CodeList', name: '原因类型', value: commonDict[`dict_d41_tree`],
      },
    ];
    return (
      <div className={styles.showHead}  style={{height:'100%', overflow:'hidden'}}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1" />
        </Tabs>
        {/*<OutRegistration filterHeight={filterHeight}/>*/}
        <NowOrg extra={
          <React.Fragment>
            <Button onClick={this.exportInfo} loading={this.state.inflowsDownload}>导出</Button>
            &nbsp;
            <InCity onChange={this.changeList} tipMsg={this.state.tipMsg}>
              <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={() => this.addOrEdit()}>省内流入</Button>
              </InCity>
            &nbsp;
            <OutsideCity onChange={this.changeList} tipMsg={this.state.tipMsg}>
              <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={() => this.addOrEdit()}>省外流入</Button>
              </OutsideCity>
            {/*<Search style={{width:200,marginLeft:16}} onSearch={this.search}/>*/}
          </React.Fragment>
        } />
        <OutWithdraw onChange={this.changeList} data={this.state['record']} filterHeight={filterHeight} wrappedComponentRef={(e) => this['outWithdraw'] = e} />
        <RuiFilter data={filterData}
                   openCloseChange={()=>setListHeight(this,20)}
                   onChange={this.filterChange} />
        <Continue wrappedComponentRef={(e) => this['Continue'] = e} data={continueInfo} onChange={this.changeList} />
        <WhiteSpace />
        <WhiteSpace />
        <ListTable scroll={{ y: filterHeight }} 
        
         columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange} />
        <Modal
          title={'流入记录查看'}
          destroyOnClose
          closable={false}
          className='view_Modal'
          visible={this.state['view']}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          footer={false}
          width={700}
        >
          <div>
            <div className={styles.head}>
              <div>
                <p>党员:{detailInfo['memName']}</p>
                <p>党员类型:{detailInfo['memTypeName']}</p>
              </div>
              <div>
                <p><span>从:</span>{detailInfo['memOrgName']}</p>
                <p><span>到:</span>{detailInfo['outflowOrgName']}</p>
              </div>
            </div>
            <div>
              <Row>
                <Col span={6} className={styles.col_text}>是否明确流入党组织:</Col><Col span={18} className={styles.col_val}>{detailInfo['isExplicitInflowOrg'] === 1 ? '已明确' : '未明确'}</Col>
                <Col span={6} className={styles.col_text}>流动去向:</Col><Col span={18} className={styles.col_val}>{detailInfo['outflowAreaName'] || '--'}</Col>
                <Col span={6} className={styles.col_text}>流动类型:</Col><Col span={18} className={styles.col_val}>{detailInfo['isProvOutName'] || '--'}</Col>
                <Col span={6} className={styles.col_text}>外出原因类型:</Col><Col span={18} className={styles.col_val}>{detailInfo['outflowReasonName'] || '--'}</Col>
                <Col span={6} className={styles.col_text}>流出党组织联系人:</Col><Col span={18} className={styles.col_val}>{detailInfo['outflowOrgLinkman'] || '--'}</Col>
                <Col span={6} className={styles.col_text}>流出党组织联系方式:</Col><Col span={18} className={styles.col_val}>{detailInfo['outflowOrgPhone'] || '--'}</Col>
                {/* <Col span={6} className={styles.col_text}>流动原因:</Col><Col span={18} className={styles.col_val}>{detailInfo['outflowReason'] || '--'}</Col> */}
                <Col span={6} className={styles.col_text}>流动党员活动证:</Col><Col span={18} className={styles.col_val}>{detailInfo['isHold'] === 1 ? '已发放' : '未发放'}</Col>
              </Row>
            </div>
          </div>
        </Modal>
        <ExportInfo wrappedComponentRef={e=>this['inflows'] = e}
                    tableName={''}
                    noModal={true}
                    tableListQuery={{...this.state.filter,...this.state.search,orgCode:org['orgCode']}}
                    action={'/api/flowmem/exportInMemList'}
        />
      </div>
    );
  }
}
