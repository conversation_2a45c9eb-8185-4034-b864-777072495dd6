import React, { useEffect, useState,useRef } from 'react';
import { connect } from 'dva';
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import { _history as router, hookSetListHeight, setListHeight } from '@/utils/method';
import qs from 'qs';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs } from 'antd';
import NowOrg from 'src/components/NowOrg';
import ExportInfo from '@/components/Export';
import { getSession } from '@/utils/session';
import AddParty from './components/addParty'
import _isNumber from 'lodash/isNumber';
import moment from 'moment';
import Tip from '@/components/Tip';
import { delParty } from "../services/org.js";

const Search = Input.Search;
const TabPane = Tabs.TabPane;

function OrgParty(props) {
  const [d108CodeList, setD108CodeList] = useState({})
  const addPartyRef:any = useRef(null);
  const [filterHeight,setFilterHeight]=useState(`calc(100vh - ${400}px)`);
  const listData=props.org.list || [];
  const { pagination = {}, orgName, filter } = props.org;
  const { current, pageSize } = pagination;
  const subordinate = getSession('subordinate') || '0';

  const downloadRef:any = useRef();
  const [searches, setSearch] = useState<any>(undefined);
  const [loading, setLoading] = useState<any>(false);

  const org=getSession('org') || {};
  useEffect(()=>{
    const dictData=['dict_d108'];
    for(let obj of dictData){
      props.dispatch({
        type:'commonDict/getDictTree',
        payload:{
          data:{
            dicName:obj
          }
        }
      });
    }
  },[])
  useEffect(()=>{
    action();
  },[org['orgCode'], subordinate])
  const addOrEdit = (record?: object) => {
    addPartyRef.current.open(record)
  };

  const onPageChange = (page, pageSize) => {
    let { query } = props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`);
    action({
      pageNum: page || 1,
      pageSize:pageSize || 10,
    });
  };
  const filterChange = async (val) => {
    setD108CodeList(val);
    let { query } = props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: 1})}`);
    action({...val, pageNum: 1});
  };
  const action = (val?: object) => {
    const org = getSession('org') || {};
    props.dispatch({
      type: 'org/partyGetList',
      payload: {
        data: {
          orgCode: org['orgCode'],
          pageNum: current || 1,
          pageSize:pageSize || 10,
          ...d108CodeList,
          ...val,
        },
      },
    });
  };
   // 删除
  const confirm = async(record) => {
    const {code=500}=await delParty({code:record.code});
    if(code===0){
      Tip.success('操作提示', '操作成功');
      action({ pageNum: 1});
    }
  };

  const search = (value) => {
    props.dispatch({
      type: 'org/updateState',
      payload: {
        partyName: value,
      },
    });
    setSearch(value);
    action({pageNum:1});
  };
  const searchClear = (e) => {
    if (!e.target.value) {
      setSearch(undefined)
      props.dispatch({
        type: 'org/updateState',
        payload: {
          partyName: '',
        },
      });
      action();
    }
  };


  // 导出
  const exportInfo = async () => {
    // this['orgExportInfo'].open();
  };
  // const orgChange = () => {
  //   action();
  // };
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 58,
      align: 'center',
      render: (text, record, index) => {
        return (pagination['current'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    {
      title: '党组名称',
      dataIndex: 'partyName',
      width: 200,
      // render: (text, record) => <a onClick={() => this.addOrEdit(record)}>{text}</a>,
    },
    {
      title: '党组类别',
      width: 180,
      dataIndex: 'd108Name',
    },
    {
      title: '关联单位',
      width: 260,
      dataIndex: 'unitName',
    },
    {
      title: '创建时间',
      dataIndex: 'buildeTime',
      align: 'center',
      width: 100,
        render: (text, record) => {
          return (
            <div>
              {_isNumber(text) ? moment(text).format('YYYY-MM-DD') : ''}
            </div>
          );
        },
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 120,
      render: (text, record) => {
        return (
          <span>
              <a onClick={() => addOrEdit(record)}>编辑</a>
              <Divider type="vertical"/>
            <Popconfirm title="确定要删除吗？" onConfirm={()=>confirm(record)}>
            <a className={'del'}>删除</a>
            </Popconfirm>
            {/* <Divider type="vertical"/>*/}
            {/*<Dropdown*/}
            {/*  overlay={*/}
            {/*    <Menu>*/}
            {/*      <Menu.Item>*/}
            {/*        <a onClick={() => this.merge(record)}>合并</a>*/}
            {/*      </Menu.Item>*/}
            {/*      <Menu.Item>*/}
            {/*        <a onClick={() => this.breakUp(record)}>拆分</a>*/}
            {/*      </Menu.Item>*/}
            {/*      <Menu.Item>*/}
            {/*        <Popconfirm title="确定要撤销吗？" onConfirm={() => this.unDo(record)}>*/}
            {/*          <a>撤销</a>*/}
            {/*        </Popconfirm>*/}
            {/*      </Menu.Item>*/}
            {/*    </Menu>*/}
            {/*  }*/}
            {/*>*/}
            {/*   <a>更多 <DownOutlined/></a>*/}
            {/*</Dropdown>*/}
            </span>
        );
      },
    },
  ];
  const filterData = [
    // {
    //   key: 'd01CodeList', name: '组织类别', value: props.commonDict[`dict_d01_tree`],
    // },
    // {
    //   key: 'd03CodeList', name: '隶属关系', value: props.commonDict[`dict_d03_tree`],
    // },
    // {
    //   key: 'd02CodeList', name: '单位情况', value: props.commonDict[`dict_d02_tree`],
    // },
    {
      key: 'd108CodeList', name: '党组类别', value: props.commonDict[`dict_d108_tree`],
    },
  ];
  const addPartyOk=()=>{
    action({ pageNum: 1});
  }
  return (
    <div style={{height:'100%',overflow:'hidden'}}>
      <Tabs defaultActiveKey="1">
        <TabPane tab="基本信息" key="1"/>
      </Tabs>
      <NowOrg extra={
        <React.Fragment>
            <Button onClick={async ()=>{
            setLoading(true);
            await downloadRef.current.submitNoModal();
            setLoading(false);
          }} loading={loading}>导出</Button>
          <Button type={'primary'} icon={<LegacyIcon type={'plus'}/>} onClick={() => addOrEdit()}
                  style={{ marginLeft: 16 }}>添加党组(党组性质党委)</Button>
          <Search style={{ width: 200, marginLeft: 16 }} onSearch={search} onChange={searchClear}
                  placeholder={'请输入检索关键词'}/>
        </React.Fragment>
      }/>
      <RuiFilter data={filterData}
                 onChange={filterChange}
                 openCloseChange={() => {
                   hookSetListHeight(setFilterHeight,20)
                 }}/>
      <WhiteSpace/>
      <WhiteSpace/>
      <ListTable
        // rowClassName={(record) => {
        //   if (record['code'] && record['code'] === org['code']) {
        //     return 'toptable';
        //   }
        //   return '';
        // }}
        scroll={{ y: filterHeight }}
        columns={columns}
        data={listData}
        pagination={pagination}
        onPageChange={onPageChange}
      />
      <ExportInfo wrappedComponentRef={downloadRef}
                    tableName={''}
                    noModal={true}
                    tableListQuery={{...d108CodeList,partyName:searches,orgCode:org['orgCode']}}
                    action={'/api/org/party/export'}
        />
      <AddParty ref={addPartyRef} onOk={addPartyOk} />
    </div>
  );
}

export default connect(({ org, commonDict, loading }:any) => ({ org, commonDict, loading: loading.effects['org/partyGetList'] }))(OrgParty)
