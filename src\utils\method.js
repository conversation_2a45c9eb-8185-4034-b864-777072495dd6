import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _isNumber from 'lodash/isNumber';
import _cloneDeep from 'lodash/cloneDeep';
import moment from 'moment';
import { Modal, Tooltip,Select } from 'antd';
import React from 'react';
import { QuestionCircleOutlined,ExclamationCircleOutlined } from '@ant-design/icons';
import { history as _history } from 'umi';
export { history as _history } from 'umi';
import {geitCard} from '@/services'
import {getSession} from '@/utils/session';


export const preLoadDicts = (list = [], dispatch) =>{
  if(!dispatch) return;
  let _arr = _cloneDeep(list);
  const path = _history.location.pathname;

  if(path.startsWith('/mem') || path.startsWith('/developMem')){
    _arr = [
      'dict_d135',
      'dict_d89',
      'dict_d06',
      'dict_d09',
      'dict_d07',
      'dict_d145',
      'dict_d154',
      'dict_d88',
      'dict_d04',
      'dict_d194',
      'dict_d195',
      'dict_d21',
      'dict_d19',
      'dict_d20',
      'dict_d27',
      'dict_d11',
      'dict_d28',
      'dict_d49',
      'dict_d136',
      'dict_d08',
      'dict_d104',
      'dict_d107',
    ]
  }
  _arr.forEach(it=>{
    dispatch({
      type:'commonDict/getDict',
      payload:{
        data:{
          dicName:it,
        }
      }
    })
  })
}

export const changeTreeItem = (tree, func) => {
  const suit = (_tree) => {
    return _tree.map((it) => {
      if (it.children && it.children.length) {
        it.children = suit(it.children);
      }
      it = func(it);
      return it;
    });
  };
  return !_isEmpty(tree) ? suit(tree) : [];
};

export const selfRowSelection = ({
  selectedRowKeys,
  setSelectedRowKeys,
  setSelectedItem,
  selectType,
  rowKey,
}) => {
  return {
    selectedRowKeys,
    type: selectType,
    onSelect: (record, selected) => {
      setSelectedRowKeys((e) => {
        if (selected) {
          return selectType == 'radio' ? [record[rowKey]]:[...new Set([...e, record[rowKey]])];
        } else {
          return e.filter((it) => it !== record[rowKey]);
        }
      });
      setSelectedItem((e) => {
        if (selected) {
          return selectType == 'radio' ? [record]: [...new Set([...e, record])];
        } else {
          return e.filter((it) => it[rowKey] !== record[rowKey]);
        }
      });
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      setSelectedRowKeys((e) => {
        if (selected) {
          return [
            ...new Set([...e, ...changeRows.map((it) => it[rowKey])]),
          ];
        } else {
          let arr = [...e];
          changeRows.map((it) => {
            arr = arr.filter((ot) => ot !== it[rowKey]);
          });
          return arr;
        }
      });
      setSelectedItem((e) => {
        if (selected) {
          return [...new Set([...e, ...changeRows])];
        } else {
          let arr = [...e];
          changeRows.map((it) => {
            arr = arr.filter((ot) => ot[rowKey] !== it[rowKey]);
          });
          return arr;
        }
      });
    },
  };
};

export const selfRowSelectionSate = (_this)=>{
  return {
    onSelect: (record, selected) => {
      let records = [];
      if (selected) {
        _this.setState({
          selectedRowKeys: [...new Set([..._this.state.selectedRowKeys, record['code']])],
          selectedItems: [...new Set([..._this.state.selectedItems, record])]
        });
        records =  [...new Set([..._this.state.selectedItems, record])]
      }else {
        this.setState({
          selectedRowKeys: _this.state.selectedRowKeys.filter((it) => it !== record['code']),
          selectedItems: _this.state.selectedItems.filter((it) => it['code'] !== record['code'])
        })
      }
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      let records = [];
      if (selected) {
        _this.setState({
          selectedRowKeys:[ ...new Set([..._this.state.selectedRowKeys, ...changeRows.map((it) => it['code'])]),],
          selectedItems: [...new Set([..._this.state.selectedItems, ...changeRows])],
        });
        records = [...new Set([..._this.state.selectedItems, ...changeRows])];
      }else{
        let arr = [..._this.state.selectedItems];
        changeRows.map((it) => {
          arr = arr.filter((ot) => ot !== it['code']);
        });
        let arr2 = [..._this.state.selectedItems];
        changeRows.map((it) => {
          arr2 = arr2.filter((ot) => ot['code'] !== it['code']);
        });
        _this.setState({
          selectedRowKeys:arr,
          selectedItems:arr2,
        });
      }
    },
  }
}

export function useThrottle(fn, delay, dep = []) {
  const { current } = React.useRef({ fn, timer: null })
  React.useEffect(
    function () {
      current.fn = fn
    },
    [fn]
  )

  return React.useCallback(function f(...args) {
    if (!current.timer) {
      current.timer = setTimeout(() => {
        delete current.timer
      }, delay)
      current.fn.call(this, ...args)
    }
  }, dep)
}

export function uuid() {
  let s = [];
  let hexDigits = "0123456789abcdef";
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = "-";

  let uuid = s.join("");
  return uuid;
}

export function isEmpty(val) {
  if (val === null || val === undefined) {
    return true;
  }
  let type = typeof val;
  switch (type) {
    case 'string': // 判断字符串是否为空
      return val === '' || false;
    case 'object':
      if (val instanceof Array) { // 判断数组是否为空
        return val.length === 0 || false;
      }
      return Object.getOwnPropertyNames(val).length === 0 || false;// 判断对象是否为空
    default:
      break;
  }
}

export function diff(obj1, obj2) {
  /*
  * false 不相等  true 相等
  * */
  let o1 = obj1 instanceof Object;
  let o2 = obj2 instanceof Object;
  if (!o1 || !o2) { /*  判断不是对象  */
    return obj1 === obj2;
  }
  if (Object.getOwnPropertyNames(obj1).length !== Object.getOwnPropertyNames(obj2).length) {
    return false; //Object.parents() 返回一个由对象的自身可枚举属性(parent值)组成的数组,例如：数组返回下表：let arr = ["a", "b", "c"];console.log(Object.parents(arr))->0,1,2;
  }
  for (let attr in obj1) {
    let t1 = obj1[attr] instanceof Object;
    let t2 = obj2[attr] instanceof Object;
    if (t1 && t2) {
      return diff(obj1[attr], obj2[attr]);
    } else if (obj1[attr] !== obj2[attr]) {
      return false;
    }
  }
  return true;
}
export function fileDownloadbyUrl(url, name){
  const a = document.createElement('a');
  a.href = url;
  if (name) {
    a.download = name;
  }
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}
export function fileDownload(url, name) {//A标签文件下载
  const a = document.createElement('a');
  // 所有的/api/upload都换成 /api/obtain/attachment 2025.06.11 余斌（渗透测试报告问题解决）
  // a.href = `/api/upload?name=${name}`;
  a.href = `/api/obtain/attachment?name=${name}`;
  a.target = '_blank';
  if (name) {
    a.download = name;
  }
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}
export function changeListPayQuery({ list = [], pageNumber = 1, totalRow = 0, pageSize = 10 } = {}) {
  return {
    list,
    pagination: {
      current: pageNumber,
      page: pageNumber,
      total: totalRow,
      pageSize: pageSize,
    }
  }
}

export function treeToList(data = [], delChild = false) {
  let resData = [];
  for (let obj of data) {
    let child = obj.children;
    if (delChild) {
      delete obj.children;
    }
    resData.push({ ...obj });
    if (child && child.length > 0) {
      resData = [...resData, ...treeToList(child, delChild)]
    }
  }
  return resData;
}

export function jsonToTree(data = [], parent = 'parent', code = 'code', parentID = '-1') {
  const lastData = [...data];
  let result = [], temp;
  for (var i = 0; i < lastData.length; i++) {
    if (lastData[i][parent] === parentID) {
      let datas = [...lastData].filter(obj => obj[parent] !== lastData[i][parent]);//eslint-disable-line
      let obj = lastData[i];
      temp = jsonToTree(datas, parent, code, lastData[i][code]);
      if (temp.length > 0) {
        obj.children = temp;
      }
      result.push(obj);
    }
  }
  return result;
}

export function getCredit(val = ''){
  if(!(/^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})$/.test(val))){
    return 'Error'
  }
  return 'Success'
}
export function getIdCardInfo(sId = '') {
  let CityArray = { 11: "北京", 12: "天津", 13: "河北", 14: "山西", 15: "内蒙古", 21: "辽宁", 22: "吉林", 23: "黑龙江", 31: "上海", 32: "江苏", 33: "浙江", 34: "安徽", 35: "福建", 36: "江西", 37: "山东", 41: "河南", 42: "湖北", 43: "湖南", 44: "广东", 45: "广西", 46: "海南", 50: "重庆", 51: "四川", 52: "贵州", 53: "云南", 54: "西藏", 61: "陕西", 62: "甘肃", 63: "青海", 64: "宁夏", 65: "新疆", 71: "台湾", 81: "香港", 82: "澳门", 91: "国外" }
  let countyArray = {
    520381: '赤水市', 520382: '仁怀市', 522323: '普安县', 522728: '罗甸县', 522729: '长顺县', 522324: '晴隆县', 522325: '贞丰县', 520422: '普定县', 522722: '荔波县',
    522601: '凯里市', 522326: '望谟县', 520423: '镇宁布依族苗族自治县', 520302: '红花岗区', 522723: '贵定县', 522327: '册亨县', 520424: '关岭布依族苗族自治县',
    520303: '汇川区', 522328: '安龙县', 520425: '紫云苗族布依族自治县', 520304: '播州区', 522725: '瓮安县', 522726: '独山县', 522727: '平塘县', 522730: '龙里县',
    520113: '白云区', 520115: '观山湖区', 520121: '开阳县', 520122: '息烽县', 522301: '兴义市', 520123: '修文县', 522302: '兴仁市',
    520521: '大方县', 520522: '黔西县', 522701: '都匀市', 520523: '金沙县', 520402: '西秀区', 522702: '福泉市', 520524: '织金县', 520403: '平坝区', 520525: '纳雍县',
    520526: '威宁彝族回族苗族自治县', 520527: '赫章县', 522632: '榕江县',522631: '黎平县',
    522633: '从江县', 522634: '雷山县', 522635: '麻江县', 522636: '丹寨县', 520221: '水城县', 520181: '清镇市', 520627: '沿河土家族自治县',
    520628: '松桃苗族自治县', 520103: '云岩区', 520102: '南明区', 520621: '江口县', 520622: '玉屏侗族自治县', 520623: '石阡县', 520502: '七星关区', 520624: '思南县',
    520625: '印江土家族苗族自治县', 520626: '德江县', 520112: '乌当区', 520111: '花溪区',  522731: '惠水县',
    522732: '三都水族自治县', 520281: '盘州市', 522629: '剑河县', 520322: '桐梓县', 520201: '钟山区', 522622: '黄平县', 520323: '绥阳县', 522623: '施秉县',
    520324: '正安县', 520203: '六枝特区', 522624: '三穗县', 520325: '道真仡佬族苗族自治县', 522625: '镇远县', 520326: '务川仡佬族苗族自治县', 522626: '岑巩县',
    520602: '碧江区', 520327: '凤冈县', 522627: '天柱县', 520603: '万山区', 520328: '湄潭县', 522628: '锦屏县', 520329: '余庆县', 520330: '习水县', 522630: '台江县',
  }
  // 老身份证的地区映射
  let change = {
    522121:'520304',520321:'520304'
  }
  if (sId.length === 15) {
    sId = sId.replace(/([\d]{6})(\d{9})/, "$119$2x");
  }
  let iSum = 0;
  let sBirthday = sId.substr(6, 4) + "-" + Number(sId.substr(10, 2)) + "-" + Number(sId.substr(12, 2));
  let d = new Date(sBirthday.replace(/-/g, "/"));
  if(sId.length === 18){
    if (sBirthday !== (d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate())){
      if(process.env.idCheck=='false'){
        sBirthday=undefined;
      }else{
        return "Error"
      }
    };
  }
  if(process.env.idCheck!='false'){
    // 大陆身份证
    if(sId.length === 18){
      console.log('大陆身份证校验');
      if (!/^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/i.test(sId)) return "Error";
      sId = sId.replace(/x$/i, "a");
      if (CityArray[parseInt(sId.substr(0, 2))] == null) return "Error";
      for (let i = 17; i >= 0; i--) iSum += (Math.pow(2, i) % 11) * parseInt(sId.charAt(17 - i), 11);
      if (iSum % 11 !== 1) return "Error";
    }else{
      // 香港身份证
      // 开头一位或两位大写字母，然后接上6位数字，最后括号内一位数字或字母A校验
      let regHongkong = /^[A-Z]{1,2}[0-9]{6}\([0-9A]\)$/;
      let regHongkong1 = /^[A-Z]{1,2}[0-9]{6}\[[0-9A]\]$/;
      // 澳门身份证
      // 开头数字1或者5或者7，然后接上6位数字，再接上括号内一位数字或者大写字母校验
      let regMacau = /^[1|5|7][0-9]{6}\([0-9A-Z]\)$/;
      let regMacau1 = /^[1|5|7][0-9]{6}\[[0-9A-Z]\]$/;
      // 台湾身份证
      // 开头一位小写或者大写字母，接上9位数字
      let regTaiwan = /^[a-zA-Z][0-9]{9}$/;
      if(!(regHongkong.test(sId) || regHongkong1.test(sId) || regMacau.test(sId) || regMacau1.test(sId) || regTaiwan.test(sId))){
        console.log('港澳台身份证校验');
        return "Error";
      }
    }  
  }
  return [
    parseInt(sId.substr(0, 2)) == 52
    ? countyArray[parseInt(sId.substr(0, 6))]:
    CityArray[parseInt(sId.substr(0, 2))],
     sBirthday,
     (sId.substr(16, 1) % 2 ? "男" : "女"),
     change[sId.substr(0, 6)] ? change[sId.substr(0, 6)] : sId.substr(0, 6)
    ]
}
const reasonData=[
  {code:'1',name:'现役军人，刚退役不到2年的军人（一般为2年）、特殊部门人员'},
  {code:'2',name:'身份证号真实，大学生户口迁移'},
  {code:'3',name:'户口迁出，且没有在新的迁入地迁入'},
  {code:'4',name:'户口迁入新迁入地，当地公安系统未上报到公安部（上报时间有地域差异）'},
  {code:'5',name:'更改姓名，当地公安系统未上报到公安部（上报时间有地域差异）'},
  {code:'6',name:'身份证号真实，但是逾期未办理'},
  {code:'7',name:'身份证号真实，未更换二代身份证'},
  {code:'8',name:'移民和死亡'},
  {code:'9',name:'身份证号确实不存在'},
]
export async function correctIdcard(name,idcard){
  return {code:200}
  if(!name || !idcard){
    return {code:200}
  }
  //去除空格
  name=name.replace(/\s*/g,"");
  idcard=idcard.replace(/\s*/g,"");
  return await geitCard({idCard:idcard,name}).then(res=>{
    if(res.message=='库中无此号'){
      let reason,reasonName;
      return new Promise((resolve, reject)=>{
        Modal.confirm({
          width: 800,
          title: '提示：经验证，库中无此身份证号，请核实是否以下原因',
          icon: <ExclamationCircleOutlined />,
          content: <Select placeholder={"请选择原因"} style={{width:'100%'}} onChange={(val,options)=>{reason=val;reasonName=options.children}}>
            {
              reasonData.map(obj=><Select.Option value={obj.code} key={obj.code}>{obj.name}</Select.Option>)
            }
          </Select>,
          okText: '确定',
          cancelText: '取消',
          onCancel:()=>{ Modal.destroyAll()},
          onOk:async ()=>{
            return new Promise((solve, ject)=>{
              if(!reason){
                Tip.error('操作提示','请选择原因');
                ject()
                reject()
              }else{
                solve()
                resolve({...res,code:200,reason,reasonName})
              }
            })
          },
        });
      })
    }
    return {...res,reason:undefined,reasonName:undefined}
  });

}
// 函数节流基础版
export function throttleBasic(fn, context, delay, text) {
  clearTimeout(fn.timeoutId);
  fn.timeoutId = setTimeout(function () {
    fn.call(context, text);
  }, delay);
}
// 函数节流增强版
export function throttle(fn, context, delay, mustApplyTime) {
  clearTimeout(fn.timer);
  fn._cur = Date.now();  //记录当前时间

  if (!fn._start) {      //若该函数是第一次调用，则直接设置_start,即开始时间，为_cur，即此刻的时间
    fn._start = fn._cur;
  }
  if (fn._cur - fn._start > mustApplyTime) {
    //当前时间与上一次函数被执行的时间作差，与mustApplyTime比较，若大于，则必须执行一次函数，若小于，则重新设置计时器
    fn.call(this, context);
    fn._start = fn._cur;
  } else {
    fn.timer = setTimeout(function () {
      fn.call(this, context);
    }, delay);
  }
}
// 首字母转换小写
export function toLowerCase(obj) {
  let jsonObj = { ...obj };
  if (typeof (jsonObj) == 'object') {
    for (let key in jsonObj) {
      jsonObj[key.substring(0, 1).toLowerCase() + key.substring(1)] = jsonObj[key];
      delete (jsonObj[key]);
    }
    return jsonObj;
  }
  return jsonObj;
}
// 首字母转换大写
export function toUpperCase(obj) {
  let jsonObj = { ...obj };
  if (typeof (jsonObj) == 'object') {
    for (let key in jsonObj) {
      jsonObj[key.substring(0, 1).toUpperCase() + key.substring(1)] = jsonObj[key];
      if (key.substring(0, 1).toUpperCase() !== key.substring(0, 1)) {
        delete (jsonObj[key]);
      }
    }
    return jsonObj;
  }
  return jsonObj;
}
// 时间先后顺序判断
export function timeSort(arr, backFunc) {
  /*
  *  ture  时间顺序有错
  *  数组顺序代表时间顺序
  * let timeArr = [
          {
            name: 'A0107',
            text: '出生日期',
            value:val['A0107']
          },
          {
            name: 'A0144',
            text: '入党时间',
            value:val['A0107']
          },
        ];
  *
  * */
  let dateArr = [...arr];
  let dateArrResult = dateArr.filter(function (item) {
    return !isEmpty(item['value'])
  });
  dateArrResult.map(it => {
    it['value'] = moment(it['value']).format('YYYY-MM-DD')
  });
  if (dateArrResult.length > 1) {
    let lock = false;
    for (let i = 0; i < dateArrResult.length; i++) {
      if (lock) { return lock; }
      for (let j = 0; j < dateArrResult.length; j++) {
        if (i < j) {
          if (dateArrResult[i].value >= dateArrResult[j].value) {
            backFunc ? backFunc(dateArrResult[j].text + '必须晚于' + dateArrResult[i].text) : Tip.warning("操作提示", dateArrResult[i].text + '日期必须早于' + dateArrResult[j].text + '日期');
            lock = true;
            break;
          }
        }
      }
    }
    return lock
  } else {
    return false
  }
}


export function findParent(code, lastData = [], key = 'key', parentKey = 'parent', result = []) {
  let find = lastData.find(obj => obj[key] === code);
  if (find) {
    findParent(find[parentKey], lastData, key, parentKey, result);
    result.push(find);
  }
  return result;
}

export function unixMoment(timeArr = [], val = {}) {
  if (!_isEmpty(timeArr)) {
    timeArr.forEach(item => {
      val[`${item}`] = _isEmpty(val[`${item}`]) ? _isNumber(val[`${item}`]) ? val[`${item}`] : undefined : moment(val[`${item}`], 'YYYY-MM-DD').valueOf();
    });
  }
  return val;
}

export function findDictCodeName(codeArr = [], val = {}, initValue = {}) {
  if (!_isEmpty(codeArr)) {
    codeArr.map(item => {
      if (!_isEmpty(val[`${item}Code`])) {
        if (typeof val[`${item}Code`] === 'object') {
          if (_isArray(val[`${item}Code`])) { // 多选
            if(typeof val[`${item}Code`][0] == 'object'){
              val[`${item}Name`] = val[`${item}Code`].map(it => it?.name).toString();
              val[`${item}Code`] = val[`${item}Code`].map(it => it?.key || it).toString();
            }else {
              // 当又是数组，且不是对象数组的时候，默认为没有改变数据
              val[`${item}Name`] = initValue[`${item}Name`];
              val[`${item}Code`] = val[`${item}Code`].toString();
            }
          } else { // 单选
            val[`${item}Name`] =val[`${item}Code`]['name'];
            val[`${item}Code`] = val[`${item}Code`]['key'];
          }
          //  else {
          //   val[`${item}Name`] = initValue[`${item}Name`];
          //   val[`${item}Code`] = val[`${item}Code`]['key'];
          // }
        } else {
          if (_isEmpty(initValue)) {
            if (val[`${item}Code`] === '0') {
              val[`${item}Name`] = '无'
            } else {
              console.warn('这有问题',item)
            }
          } else {
            val[`${item}Name`] =val[`${item}Name`] || initValue[`${item}Name`];
          }
        }

      }
    });
  }
  return val
}
export function hookSetListHeight(setState, extraHeight = 0) {
  let element = document.getElementById('ruiFilter');
  if (element && setState) {
    let height = element.clientHeight || element.offsetHeight;
    let filterHeight = `calc(100vh - ${height + 308 + extraHeight}px)`;
    setState(filterHeight);
    window.onresize = function () {
      if (setState) {
        let height = element.clientHeight || element.offsetHeight;
        let filterHeight = `calc(100vh - ${height + 308 + extraHeight}px)`;
        setState(filterHeight);
      }
    };
  } else if (setState) {
    let filterHeight = `calc(100vh - ${308 + extraHeight}px)`;
    setState(filterHeight);
  }
}
export function setListHeight(_this, extraHeight = 0) {
  let element = document.getElementById('ruiFilter');
  if (element && _this) {
    let height = element.clientHeight || element.offsetHeight;
    let filterHeight = `calc(100vh - ${height + 308 + extraHeight}px)`;
    _this.setState({
      filterHeight,
    });
    window.onresize = function () {
      if (_this) {
        let height = element.clientHeight || element.offsetHeight;
        let filterHeight = `calc(100vh - ${height + 308 + extraHeight}px)`;
        _this.setState({
          filterHeight,
        });
      }
    };
  } else if (_this) {
    let filterHeight = `calc(100vh - ${308 + extraHeight}px)`;
    _this.setState({
      filterHeight,
    });
  }
}
export function arrSort(arr) {
  let newArr = [];
  if (!_isEmpty(arr)) {
    newArr = arr.sort(function (a, b) {
      if (a < b) {
        return -1;
      }
      if (a > b) {
        return 1;
      }
      return 0;
    })
  }
  return newArr;
}
export function getBrowserInfo() {
  let Sys = {};
  let ua = navigator.userAgent.toLowerCase();
  let re = /(msie|firefox|chrome|opera|edge|version).*?([\d.]+)/;
  if (ua.indexOf('edge') > -1) {//edge 浏览器
    re = /(edge).*?([\d.]+)/;
  }
  let m = ua.match(re);
  Sys.browser = m[1].replace(/version/, "'safari");
  Sys.ver = m[2];
  return Sys;
}
//表单提示信息转换
export function changeMsgTip(data = []) {
  let res = {};
  for (let obj of data) {
    if (obj['remark']) {
      res[obj['colCode']] = obj['remark']
    }
  }
  return res;
}
//antd 3.x form表单提示信息
export function formLabel(title, msg) {
  return (
    <span>
      {title}
      {
        msg && <React.Fragment>
          &nbsp;
          <Tooltip title={msg}>
            <QuestionCircleOutlined style={{ fontSize: 12, color: '#9B9898' }} />
          </Tooltip>
        </React.Fragment>
      }
    </span>
  )
}
export function changeArrItem(val, id, arr) {
  /*
  * val 当前值
  * id 选中的行
  * arr 原数组
  * */
  if (!isEmpty(arr)) {
    arr = arr.map(item => {
      if (item['id'] === id) {
        return { ...item, ...val }
      } else {
        return item
      }
    });
  }
  return arr;
}
export function unique(arr, key) {//数组去重 key非必传
  const res = new Map();
  if (key) {
    return arr.filter((a) => !res.has(a[key]) && res.set(a[key], 1))
  }
  return arr.filter((a) => !res.has(a) && res.set(a, 1))
}
export function fileDownloadHeader(url, name, header) {//A标签文件下载
  let xhr = new XMLHttpRequest();
  //GET请求,请求路径url,async(是否异步)
  xhr.open('GET', url, true);
  if (typeof header == 'object') {
    for (let [k, v] of Object.entries(header)) {
      //设置请求头参数的方式,如果没有可忽略此行代码
      xhr.setRequestHeader(k, v);
    }
  } else {
    //设置请求头参数的方式,如果没有可忽略此行代码
    xhr.setRequestHeader("dataApi", sessionStorage.getItem('dataApi') || "");
  }
  //设置响应类型为 blob
  xhr.responseType = 'blob';
  //关键部分
  xhr.onload = function (e) {
    //如果请求执行成功
    if (this.status === 200) {
      let blob = this.response;
      let url = URL.createObjectURL(blob);
      let a = document.createElement('a');
      document.body.appendChild(a);
      a.href = url;
      a.target = '_blank';
      //创键临时url对象
      if (name) {
        a.download = name;
      }
      a.click();
      document.body.removeChild(a);
      //释放之前创建的URL对象
      window.URL.revokeObjectURL(url);
    }
  };
  //发送请求
  xhr.send();
}

/**
 * 将图片转换为webp格式后再上传
 * @param {File} file - 原始文件对象
 * @param {Object} options - 配置选项
 * @param {number} options.quality - webp质量，范围0-1，默认0.8
 * @param {Function} options.beforeConvert - 转换前的回调函数
 * @param {Function} options.afterConvert - 转换后的回调函数
 * @returns {Promise<File>} - 返回转换后的webp格式文件
 */
export async function convertToWebpBeforeUpload(file, options = {}) {
  const { quality = 0.8, beforeConvert, afterConvert } = options;
  
  // 检查文件是否为图片
  if (!file.type.startsWith('image/')) {
    return Promise.resolve(file);
  }
  
  // 如果已经是webp格式，直接返回
  if (file.type === 'image/webp') {
    return Promise.resolve(file);
  }
  
  return new Promise((resolve, reject) => {
    // 转换前回调
    if (typeof beforeConvert === 'function') {
      beforeConvert(file);
    }
    
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        // 创建canvas元素
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        
        // 在canvas上绘制图片
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        
        // 将canvas内容转换为webp格式
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error('图片转换失败'));
            return;
          }
          
          // 创建新的File对象
          const webpFile = new File(
            [blob], 
            `${file.name.substring(0, file.name.lastIndexOf('.'))}.webp`, 
            { type: 'image/webp' }
          );
          
          // 转换后回调
          if (typeof afterConvert === 'function') {
            afterConvert(webpFile);
          }
          
          resolve(webpFile);
        }, 'image/webp', quality);
      };
      
      img.onerror = () => {
        reject(new Error('图片加载失败'));
      };
    };
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };
  });
}
// 获取是否是流动党组织  权限判断
export function isFlowingParty() {
  const org = getSession('org')||{}
  return !org?.['d01Code'].startsWith('8')
}
