import React from 'react';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Row } from 'antd';
import styles from './index.less';
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);

  }

  render() {
    const {iconcolor,icon,title,value,bordered} = this.props;
    return (
      <div>
        <div className={styles.iconBox} style={{backgroundColor:`${iconcolor}`}}>
          <LegacyIcon className={styles.iconMain} type={icon}/>
        </div>
        <div className={styles.boxInfo}>
          <div style={{fontSize:'30px'}}>{title}</div>
          <span>{value}</span>
        </div>
        <div style={{clear:'both'}}/>
        {bordered && <em />}
      </div>
    );
  }
}
