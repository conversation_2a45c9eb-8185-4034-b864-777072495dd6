/**
 * 添加届次信息
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Alert,
  Button,
  Col,
  DatePicker,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Select,
  Upload,
} from "antd";
import WhiteSpace from '@/components/WhiteSpace'
import Tip from '@/components/Tip';
import moment from 'moment';
import Date from '@/components/Date';
import { uuid } from '@/utils/method';
import UploadComp, { getInitFileList, fitFileUrlForForm } from '@/components/UploadComp';
import { formLabel } from '@/utils/method';
const FormItem = Form.Item;
const Option = Select.Option;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      loading: false
    }
  }
  showModal = () => {
    this.setState({
      visible: true,
      startValue: undefined,
      endValue: undefined,
    });
  };

  handleOk = () => {
    const { children, title, dataInfo = {}, type } = this.props;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        // const org=getSession('org') || {};
        const { basicInfo = {} } = this.props.org;
        let obj = undefined;
        if (val['tenureEndDate']) {
          val['tenureEndDate'] = val['tenureEndDate'].valueOf();
        }
        if (val['tenureStartDate']) {
          val['tenureStartDate'] = val['tenureStartDate'].valueOf();
        }

        if (!(moment(val['tenureEndDate']) >= moment(val['tenureStartDate']).add(3, 'years') && moment(val['tenureEndDate']) <= moment(val['tenureStartDate']).add(6, 'years'))) {
          this.props.form.setFields({
            tenureEndDate: { errors: [new Error('结束时间要间隔在3年到6年')] }
          });
          return;
        }

        if (val['material'] && typeof val['material'] == 'object') {
          // const {data}=val['material']['0']['response']
          // val['material']=data[0]['url'];
          val['material'] = fitFileUrlForForm(val['material'])
        }
        if (val['electCode']) {
          switch (val['electCode']) {
            case 1:
              val['electName'] = '等额';
              break;
            case 2:
              val['electName'] = '差额';
              break;
            case 4:
              val['electName'] = '公推直选';
              break;
            default:
              break;
          }
        }
        val['orgCode'] = basicInfo['code'];
        val['electOrgCode'] = basicInfo['orgCode'];
        val['zbCode'] = basicInfo['zbCode'];
        await this.setState({ loading: true })
        if (type === 'edit') {
          // orgElect
          obj = await this.props.dispatch({
            type: 'org/orgElectUp',
            payload: {
              data: {
                ...dataInfo,
                ...val
              }
            }
          });
        } else {
          // orgElect
          obj = await this.props.dispatch({
            type: 'org/orgElect',
            payload: {
              data: {
                ...val
              }
            }
          });
        }
        await this.setState({ loading: false })
        if (obj && obj['code'] === 0) {
          Tip.success('操作提示', type === 'edit' ? '编辑成功' : '新增成功');
          this.handleCancel();
          this.props.onClose();
          this.props.queryList();
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false,
    });
    this.props.onClose();
  };
  onStartChange = (value) => {
    this.setState({
      startValue: value
    })
  };
  onEndChange = (value) => {
    this.setState({
      endValue: value
    })
  };
  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.endOf('day') <= startValue.endOf('day');
  };
  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.endOf('day') <= startValue.endOf('day');
  };
  getInitFileList = (val) => {
    let _name = [];
    if (val) {
      _name = val.split('\\') || [];
      return [
        { name: [..._name].pop(), url: _name.join('/'), uid: uuid() }
      ]
    } else {
      return []
    }
  };
  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    // const org=getSession('org') || {};
    let { children, title, dataInfo = {}, tipMsg } = this.props;
    const { basicInfo = {} } = this.props.org;
    const props = {
      name: 'file',
      action: '/api/base/upload',
      headers: {
        authorization: sessionStorage.getItem('token') || "", dataApi: sessionStorage.getItem('dataApi') || ""
      },
      onChange(info) {
        if (info.file.status !== 'uploading') {
          console.log(info.file, info.fileList);
        }
        if (info.file.status === 'done') {
          message.success(`${info.file.name} 上传成功`);
        } else if (info.file.status === 'error') {
          message.error(`${info.file.name} 上传失败.`);
        }
      },
    };
    const { loading } = this.state
    return (
      <React.Fragment>
        {
          children ? React.cloneElement(children as any, {
            onClick: this.showModal,
            key: 'container'
          }) : null
        }
        {
          this.state.visible &&
          <Modal
            title={title || "请输入标题"}
            visible={this.state.visible}
            onOk={() => this.handleOk()}
            onCancel={this.handleCancel}
            width={1000}
            className='add_modal'
            maskClosable={false}
            confirmLoading={loading}
          >
            <Alert message="提示：班子届次（届）统计期内建立党组织请选择是。" type="info" showIcon />
            <WhiteSpace />
            <WhiteSpace />
            <Form {...formItemLayout}>
              <Row>
                <Col span={24}>
                  <FormItem
                    label={formLabel('组织名称', tipMsg['orgName'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('orgName', {
                      initialValue: basicInfo['name'],
                      rules: [{ required: true, message: '请输入组织名称' }],
                    })(
                      <Input placeholder={'组织全称'} disabled />
                    )}
                  </FormItem>
                </Col>
                {/*<Col span={12}>*/}
                {/*  <FormItem*/}
                {/*    label="是否首任届次"*/}
                {/*  >*/}
                {/*    {getFieldDecorator('isFristCode', {*/}
                {/*      initialValue:dataInfo['isFristCode'],*/}
                {/*      rules: [{ required: true, message: '请选择' }],*/}
                {/*    })(*/}
                {/*      <Select placeholder={'请选择'}>*/}
                {/*        <Option value={1}>是</Option>*/}
                {/*        <Option value={2}>否</Option>*/}
                {/*      </Select>*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                <Col span={12}>
                  <FormItem
                    label={formLabel('开始时间', tipMsg['tenureStartDate'])}
                  >
                    {getFieldDecorator('tenureStartDate', {
                      initialValue: dataInfo['tenureStartDate'] ? moment(dataInfo['tenureStartDate']) : undefined,
                      rules: [{ required: true, message: '请选择开始时间' }],
                      // <DatePicker placeholder="请选择" style={{width:'100%'}} onChange={this.onStartChange} disabledDate={this.disabledStartDate}/>
                    })(
                      <Date />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label={formLabel('结束日期', tipMsg['tenureEndDate'])}>
                    {getFieldDecorator('tenureEndDate', {
                      initialValue: dataInfo['tenureEndDate'] ? moment(dataInfo['tenureEndDate']) : undefined,
                      rules: [{ required: true, message: '请选择结束日期' }],
                      // <DatePicker placeholder="请选择" style={{width:'100%'}} onChange={this.onEndChange} disabledDate={this.disabledEndDate}/>
                    })(
                      <Date startTime={getFieldValue('tenureStartDate')} isDefaultEnd={false} />
                    )}
                  </FormItem>
                </Col>
                {/*<Col span={12}>*/}
                {/*<FormItem*/}
                {/*  label="选举方式"*/}
                {/*>*/}
                {/*  {getFieldDecorator('electCode', {*/}
                {/*    initialValue:dataInfo['electCode'],*/}
                {/*    rules: [{ required: true, message: '请选择' }],*/}
                {/*  })(*/}
                {/*    <Select placeholder={'请选择'}>*/}
                {/*      <Option value={1}>等额</Option>*/}
                {/*      <Option value={2}>差额</Option>*/}
                {/*      <Option value={4}>公推直选</Option>*/}
                {/*    </Select>*/}
                {/*  )}*/}
                {/*</FormItem>*/}
                {/*</Col>*/}
                <Col span={12}>
                  <FormItem
                    label={formLabel('应到人数', tipMsg['shoudNumber'])}
                  >
                    {getFieldDecorator('shoudNumber', {
                      initialValue: dataInfo['shoudNumber'],
                      rules: [{ required: false, message: '' }],
                    })(
                      <InputNumber style={{ width: '100%' }} min={0} placeholder={'应到人数'} />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={formLabel('实到人数', tipMsg['realNumber'])}
                  >
                    {getFieldDecorator('realNumber', {
                      initialValue: dataInfo['realNumber'],
                      rules: [{ required: false, message: '' }],
                    })(
                      <InputNumber style={{ width: '100%' }} min={0} placeholder={'实到人数'} />
                    )}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label={formLabel('复批职数', tipMsg['postNumber'])}
                  >
                    {getFieldDecorator('postNumber', {
                      initialValue: dataInfo['postNumber'],
                      rules: [{ required: false, message: '' }],
                    })(
                      <InputNumber style={{ width: '100%' }} min={0} placeholder={'复批职数'} />
                    )}
                  </FormItem>
                </Col>
                {/* 2025/3/31 钟镇西 换届相关材料 屏蔽 */}
                {/* <Col span={12}>
                  <FormItem
                    label={formLabel('换届相关材料', tipMsg['material'])}
                  >
                    {getFieldDecorator('material', {
                      initialValue: dataInfo['material'],
                      rules: [{ required: false, message: '' }],
                    })(
                      <UploadComp files={getInitFileList(dataInfo['material'])} maxLen={1} />
                    )}
                  </FormItem>
                </Col> */}
                <Col span={12}>
                  <FormItem
                    label={formLabel('实际选出职数', tipMsg['realPostNumber'])}
                  >
                    {getFieldDecorator('realPostNumber', {
                      initialValue: dataInfo['realPostNumber'],
                      rules: [{ required: false, message: '' }],
                    })(
                      <InputNumber style={{ width: '100%' }} min={0} placeholder={'实际选出职数'} />
                    )}
                  </FormItem>
                </Col>
                <Col span={24}>
                  <FormItem
                    label={formLabel('特殊情况说明', tipMsg['specialExplain'])}
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('specialExplain', {
                      initialValue: dataInfo['specialExplain'],
                      rules: [{ required: false, message: '' }],
                    })(
                      <TextArea rows={4} placeholder={'特殊情况说明'} />
                    )}
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </Modal>
        }

      </React.Fragment>
    )
  }
}
export default Form.create<any>()(index);
