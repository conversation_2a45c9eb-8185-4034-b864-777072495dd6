import React, { useImperativeHandle, useRef, useEffect, useState } from 'react';
import { Modal, Button } from 'antd';
import ExportInfo from '@/components/Export';

const index = (props, ref) => {
  const {
    htmlType
  } = props;
  const _ref: any = useRef();
  const org = JSON.parse(sessionStorage.getItem('org') || '{}')
  useImperativeHandle(ref, () => ({
    open: query => {
      setQuery(query);
      setIsModalVisible(true);
    },
    clear: () => {
      // clear();
    },
  }));
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState<any>();

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const download = async () => {
    setLoading(true);
    let res = await _ref.current.submitNoModal();
    setLoading(false);
  }

  return (
    <React.Fragment>
      <Modal
        title="校核结果"
        forceRender
        visible={isModalVisible}
        // onOk={handleOk}
        onCancel={handleCancel}
        maskClosable={false}
        footer={[
          <Button onClick={handleCancel}>取消</Button>,
          <Button type={'primary'} onClick={() => {
            window.open(`/annualStatistics/checkResultPage?type=${htmlType}&orgCode=${org.orgCode}`)
          }}>查看</Button>,
          <Button type={'primary'} onClick={download} loading={loading}>下载</Button>,
        ]}>
        <div style={{ marginLeft: 2 }}>
          {query}
        </div>
      </Modal>
      <ExportInfo wrappedComponentRef={_ref}
        tableName={'ccp_mem_develop'}
        tableListQuery={{ orgCode: org.orgCode}}
        action={'/api/annual/findCheckTableListExport'}
      />
    </React.Fragment>

  );
};
export default React.forwardRef(index);
