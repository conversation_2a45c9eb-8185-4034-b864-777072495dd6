import React, { useRef } from 'react';
import moment from 'moment';
import { detail } from '@/pages/dataCheck/services';

import _get from 'lodash/get';

export const checkCols = (data: any = {}, props, funcs) => {
  const { renderMemDetail, renderDevelopDetail, renderOrgDetail, renderUnitDetail } = funcs;
  const { table: tableName = '', id = '' } = data || {};

  // 如果返回了表名，根据表名判断
  if (tableName) {
    let columns: any = [
      {
        title: '姓名',
        dataIndex: 'name',
      },
      {
        title: '性别',
        dataIndex: 'sex_name',
      },
      {
        title: '身份证号',
        dataIndex: 'idcard',
      },
      {
        title: '民族',
        dataIndex: 'd06_name',
      },
      {
        title: '学历',
        dataIndex: 'd07_name',
      },
      {
        title: ' 工作岗位',
        dataIndex: 'd0_name',
      },
      {
        title: ' 单位类别',
        dataIndex: 'd04_name',
      },
    ];

    // ccp_mem 党员基础信息
    // ccp_org 组织基础信息
    // ccp_unit 单位基础信息
    // ccp_mem_reward 党员奖惩信息
    // ccp_mem_abroad 党员出国境信息
    // ccp_mem_train 党员培训信息
    // ccp_org_reward 组织奖惩信息
    // ccp_org_committee 组织领导班子信息
    // ccp_org_reviewers 组织民主评议信息
    // ccp_mem_develop 发展党员基础信息
    // ccp_mem_flow 流动党员基础信息

    switch (tableName) {
      case 'ccp_org':
      case 'ccp_org_all':
        // columns = [
        //   {
        //     title: '组织名称',
        //     dataIndex: 'org_name',
        //   },
        //   {
        //     title: '姓名',
        //     dataIndex: 'name',
        //     render: (text, record) => {
        //       return tableName === 'ccp_mem' ? renderMemDetail(text, record) : renderDevelopDetail(text, record)
        //     }
        //   },
        //   {
        //     title: '性别',
        //     dataIndex: 'sex_name',
        //   },
        //   {
        //     title: '民族',
        //     dataIndex: 'd06_name',
        //   },
        //   {
        //     title: '学历',
        //     dataIndex: 'd07_name',
        //   },
        //   {
        //     title: ' 工作岗位',
        //     dataIndex: 'd09_name',
        //   },
        //   {
        //     title: '新社会阶层',
        //     dataIndex: 'd20_name',
        //   },
        //   {
        //     title: '一线情况',
        //     dataIndex: 'd21_name',
        //   },
        //   {
        //     title: ' 单位类别',
        //     dataIndex: 'd04_name',
        //   },
        //   {
        //     title: '单位名称',
        //     dataIndex: 'unit_name',
        //   },
        // ];
        columns = [
          {
            title: '组织名称',
            dataIndex: 'orgName',
            width: 270,
            render: (text, record) => {
              return renderOrgDetail(text, record);
            },
          },
          {
            title: '组织类别',
            width: 200,
            dataIndex: 'd01Name',
          },
          {
            title: '隶属关系',
            width: 160,
            dataIndex: 'd03Name',
          },
          {
            title: '联系人',
            width: 100,
            dataIndex: 'contacter',
          },
          {
            title: '联系方式',
            width: 110,
            dataIndex: 'contactPhone',
          },
          {
            title: '党组织书记',
            width: 100,
            dataIndex: 'secretary',
          },
        ];
        break;
      case 'ccp_mem':
      case 'ccp_mem_all':
        // 党员 ccp_mem、ccp_mem_all
        columns = [
          {
            title: '姓名',
            dataIndex: 'name',
            width: 100,
            render: (text, record) => {
              return renderMemDetail(text, record);
            },
          },
          {
            title: '性别',
            dataIndex: 'sexName',
            width: 100,
          },
          {
            title: '出生日期',
            dataIndex: 'birthday',
            width: 100,
            render: (text, record) => {
              if(text){
                return moment(text).format('YYYY-MM-DD')
              }
            },
          },
          {
            title: '党员类型',
            width: 120,
            dataIndex: 'd08Name',
          },
          {
            title: '所在组织',
            width: 260,
            dataIndex: 'orgName',
          },
          {
            title: '转正时间',
            dataIndex: 'fullMemberDate',
            width: 100,
            render: (text, record) => {
              if(text){
                return moment(text).format('YYYY-MM-DD')
              }
            },
          },
          {
            title: '入党时间',
            dataIndex: 'joinOrgDate',
            width: 100,
            render: (text, record) => {
              if(text){
                return moment(text).format('YYYY-MM-DD')
              }
            },
          },
        ];
        break;
      case 'ccp_mem_develop':
      case 'ccp_mem_develop_all':
      case 'ccp_develop_step_log':
      case 'ccp_develop_step_log_all':
        // 发展党员ccp_mem_develop、ccp_mem_develop_all
        // 本年度发展党员ccp_develop_step_log、ccp_develop_step_log_all
        columns = [
          {
            title: '姓名',
            dataIndex: 'name',
            width: 100,
            render: (text, record) => {
              // if (tableName === 'ccp_mem' || tableName === 'ccp_mem_all') {
              //   return renderMemDetail(text, record);
              // }
              if (tableName === 'ccp_mem_develop' || tableName === 'ccp_mem_develop_all') {
                return renderDevelopDetail(text, record);
              }
              if (
                tableName === 'ccp_develop_step_log' ||
                tableName === 'ccp_develop_step_log_all'
              ) {
                return renderDevelopDetail(text, record);
              }
            },
          },
          {
            title: '性别',
            dataIndex: 'sexName',
            width: 100,
          },
          {
            title: '党员类型',
            width: 120,
            dataIndex: 'd08Name',
          },
          {
            title: '所在组织',
            width: 260,
            dataIndex: 'orgName',
          },
        ];
        break;
      case 'ccp_unit':
        columns = [
          {
            title: '单位名称',
            dataIndex: 'name',
            width: 200,
            render: (text, record) => {
              return renderUnitDetail(text, record);
            },
          },
          {
            title: '单位类别',
            dataIndex: 'd04Name',
            width: 200,
          },
          {
            title: '隶属关系',
            dataIndex: 'd35Name',
            width: 200,
          },
          {
            title: '关联组织',
            dataIndex: 'mainOrgName',
            width: 200,
          },
        ];
        break;
      case 'ccp_unit_all':
        columns = [
          {
            title: '单位名称',
            dataIndex: 'unitName',
            width: 200,
            render: (text, record) => {
              return renderUnitDetail(text, record);
            },
          },
          {
            title: '单位类别',
            dataIndex: 'd04Name',
            width: 200,
          },
          {
            title: '隶属关系',
            dataIndex: 'd35Name',
            width: 200,
          },
          {
            title: '关联组织',
            dataIndex: 'mainOrgName',
            width: 200,
          },
        ];
        break;
      case 'mem_flow':
        columns = [
          {
            title: '姓名',
            dataIndex: 'name',
            width: 50,
          },
          {
            title: '流出类型',
            dataIndex: 'outPlaceName',
            width: 100,
          },
          {
            title: '流入地行政区域',
            dataIndex: 'outAdministrativeDivisionName',
            width: 100,
          },
        ];
        break;
    }
    return columns;
  } else {
    let columns: any = [];
    return columns;
    switch (id) {
      // 1 单位基础信息
      case '1':
      case '5':
      case '13':
      case '56':
        columns = [
          {
            title: '单位名称',
            dataIndex: 'unitName',
            width: 200,
            render: (text, record) => {
              return renderUnitDetail(text, record);
            },
          },
          {
            title: '单位类别',
            dataIndex: 'd04Name',
            width: 200,
          },
          {
            title: '隶属关系',
            dataIndex: 'd35Name',
            width: 200,
          },
          {
            title: '关联组织',
            dataIndex: 'mainOrgName',
            width: 200,
          },
        ];
        break;
      // 2 党组织基础信息
      case '2':
      case '14':
      case '21':
        columns = [
          {
            title: '组织名称',
            dataIndex: 'name',
            width: 270,
            render: (text, record) => {
              return renderOrgDetail(text, record);
            },
          },
          {
            title: '组织类别',
            width: 200,
            dataIndex: 'd01Name',
          },
          {
            title: '隶属关系',
            width: 160,
            dataIndex: 'd03Name',
          },
          {
            title: '联系人',
            width: 100,
            dataIndex: 'contacter',
          },
          {
            title: '联系方式',
            width: 110,
            dataIndex: 'contactPhone',
          },
          {
            title: '党组织书记',
            width: 100,
            dataIndex: 'secretary',
          },
        ];
        break;
      // 3 党员基础信息
      case '3':
      case '6':
      case '8':
      case '10':
      case '11':
      case '12':
      case '15':
      case '18':
      case '19':
      case '20':
      case '22':
      case '23':
      case '24':
      case '25':
      case '27':
      case '28':
      case '29':
      case '30':
      case '35':
      case '36':
      case '37':
      case '38':
      case '39':
      case '40':
      case '41':
      case '42':
      case '43':
      case '44':
      case '45':
      case '34':
        columns = [
          {
            title: '姓名',
            dataIndex: 'name',
            width: 100,
            render: (text, record) => {
              return renderMemDetail(text, record);
            },
          },
          {
            title: '性别',
            dataIndex: 'sexName',
            width: 100,
          },
          {
            title: '党员类型',
            width: 120,
            dataIndex: 'd08Name',
          },
          {
            title: '所在组织',
            width: 260,
            dataIndex: 'orgName',
          },
        ];
        break;
      // 4 发展党员基础信息
      case '4':
      case '7':
      case '9':
      case '16':
      case '17':
      case '26':
      case '31':
      case '32':
      case '33':
      case '46':
      case '47':
      case '48':
      case '49':
      case '50':
      case '51':
      case '55':
        columns = [
          {
            title: '姓名',
            dataIndex: 'name',
            width: 100,
            render: (text, record) => {
              return renderDevelopDetail(text, record);
            },
          },
          {
            title: '性别',
            dataIndex: 'sexName',
            width: 100,
          },
          {
            title: '党员类型',
            width: 120,
            dataIndex: 'd08Name',
          },
          {
            title: '所在组织',
            width: 260,
            dataIndex: 'orgName',
          },
        ];
        break;
      case '52':
      case '53':
      case '54':
      case '57':
      case '58':
        columns = [
          {
            title: '组织名称',
            dataIndex: 'orgName',
            width: 200,
            render: (text, record) => {
              return renderOrgDetail(text, record);
            },
          },
          {
            title: '联系人',
            dataIndex: 'contacter',
            width: 50,
          },
          {
            title: '联系方式',
            width: 100,
            dataIndex: 'contactPhone',
          },
          {
            title: '党组织书记',
            width: 50,
            dataIndex: 'secretary',
          },
        ];
        break;
    }
    return columns;
  }
};

export const detailUrl = (data: any = {}) => {
  const { tableName = '' } = data || {};
  let url = detail;
  switch (tableName) {
    case 'aaa':
      break;
  }
  return url;
};
