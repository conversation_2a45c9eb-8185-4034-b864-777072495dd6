/*
* 人员管理 -- 基本信息
* */
import React, { Fragment } from 'react';
import { DownOutlined, WarningTwoTone } from '@ant-design/icons';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Divider, Popconfirm, Menu, Button, Input, Dropdown, Switch, Modal, Descriptions, Row, Col } from 'antd';
import RuiFilter from 'src/components/RuiFilter';
import ListTable from 'src/components/ListTable';
import NowOrg from 'src/components/NowOrg';
import AddEdit from './AddEdit';
import OrgLevel from './OrgLevel';
import ToPositive from './Preparation/ToPositive';
import DelayMem from './Preparation/Delay';
import CancelMem from './Preparation/Cancel';
import TimeCheck from './TimeCheck';
import Lost from './Preparation/Lost';
import ExportInfo from '@/components/Export';
import WhiteSpace from '@/components/WhiteSpace';
import _isEmpty from 'lodash/isEmpty';
import { withContext } from 'src/utils/global.jsx';
import { connect } from "dva";
import { _history as router } from "@/utils/method";
import { getSession } from "@/utils/session";
import LeaveOrg, { LeaveTable } from './LeaveOrg';
import LockModal from './Lock/index2';
import qs from 'qs';
import { setListHeight, preLoadDicts } from "@/utils/method";
import { backToDevelopMem, backToProbationary, findMemResume, flowcountdy, zybackToProbationary, findAuditProcess } from '@/pages/mem/services'
import Tip from '@/components/Tip';
import { ModalTL } from '@/components/TimeLine';
import { ButtonDisabled } from '@/common/config.js';
import { TableActionMenu } from '@/pages/user/lock';
import Flow from '@/pages/developMem/zy/components/flow'
import Add from './AddFlowFile'
import EditDevlop from '@/pages/developMem/zy/components/Edit'
import ElectronicArchives from '@/pages/developMem/zy/components/electronicArchives'
import Extend from './extend'
import _isEqual from 'lodash/isEqual';
import Info from './AddFlowFile/info'
import CancelLevel from './CancelLevel'
const { Search } = Input;
@withContext
@connect(({ becomeFullMem, commonDict, loading }) => ({ becomeFullMem, commonDict, loading }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      filterHeight: 150,
      filterChecked: {},
      processNode: [],
    }
  }
  componentDidMount(): void {
    setListHeight(this, 150);

    // 预加载编辑字典表
    preLoadDicts([], this.props.dispatch);

    // 转正字典表
    this.props.dispatch({
      type: 'commonDict/getDict',
      payload: {
        data: {
          dicName: 'dict_d28',
        }
      }
    })
    // this.props.dispatch({
    //   type:'commonDict/getArea',
    //   payload:{
    //     parent:'-1',
    //   }
    // })
    const org = getSession('org') || {};
  }

  getList = (pageNum = 1, pageSize = 10, orgCode = '') => {
    !_isEmpty(orgCode) && this.props.dispatch({
      type: 'memBasic/getzylist',
      payload: {
        data: {
          pageNum,
          pageSize,
          searchType: 1,
          memOrgCode: orgCode,
        }
      }
    })
  };
  // 筛选
  filterChange = (val) => {
    this.setState({ filterChecked: val });
    this.props.dispatch({
      type: 'memBasic/updateState',
      payload: {
        filter: val,
      }
    });
    const { pagination = {} } = this.props.memBasic;
    const { query } = this.props.location;

    router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`)
    // this.action()
    // this.setState({
    //   filter:val
    // },()=>this.action())
  };
  // 分页
  onPageChange = (page, pageSize) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`)
  };
  action = (val?: object) => {
    const { pagination = {} } = this.props.memBasic;
    const { current, pageSize } = pagination;
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memBasic/getzylist',
      payload: {
        data: {
          searchType: 1,
          memOrgCode: org['orgCode'],
          pageNum: current || 1,
          pageSize,
          ...val,
        }
      }
    })
    this.getFlowCount()
  };
  getFlowCount = async () => {
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memBasic/flowcount',
      payload: {
        data: {
          memOrgCode: org['orgCode'],
          searchType: 1,
        }
      }
    })
  }
  search = (value) => {
    this.props.dispatch({
      type: 'memBasic/updateState',
      payload: {
        memName: value,
      }
    });
    const { pagination = {} } = this.props.memBasic;
    const { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`)
    // this.action();
    // this.setState({
    //   search:{memName:value}
    // },()=>this.action());
  };
  searchClear = (e) => {
    this.setState({
      searchVal: e.target.value
    })
    if (!e.target.value) {
      this.props.dispatch({
        type: 'memBasic/updateState',
        payload: {
          memName: undefined,
        },
      });
      this.action();
    }
  };
  // 人员编辑
  addOrEdit = async (record) => {
    // AddEdit['WrappedComponent'].clear();
    // console.log(AddEdit);
    AddEdit['WrappedComponent'].show();
    if (record && record['code']) {
      await this.props.dispatch({
        type: 'memBasic/findMem',
        payload: {
          code: record['code']
        }
      })
    }
  };

  // 人员删除
  del = (record) => {
    // console.log(record,'人员删除');
    // this['LeaveOrg'].open(record);
    this['LeaveTable'].open(record);
  };
  // 取消关联
  cancel = (record, type) => {
    // console.log(record,'取消关联')
    switch (type) {
      case '1':
        this['ToPositive'].open(record);
        break;
      case '2':
        this['DelayMem'].open(record);
        break;
      case '3':
        this['CancelMem'].open(record);
        break;
      default:
        break;
    }
  };
  addNew = () => {
    let org = getSession('org') || {}
    AddEdit['WrappedComponent'].clear();
    this.props.dispatch({
      type: 'memBasic/updateState',
      payload: {
        basicInfo: {
          d01Code: org['d01Code'],
          orgName: org['name'],
          orgCode: org['code'],
          orgZbCode: org['zbCode'],
          memOrgCode: org['orgCode'],
        }
      }
    })
    AddEdit['WrappedComponent'].show();
  };
  // 查看当组织层级
  lookOrgs = (record) => {
    this['OrgLevel'].open(record);
  };
  // 导出
  exportInfo = async () => {
    this['memExportInfo'].open();
  };

  lock = () => {
    this['LockModal'].open();
  };

  lockOrUnlock = (record) => {
    const { pagination = {} } = this.props.memBasic;
    const { current, pageSize } = pagination;
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: current })}`)
  };
  clickFlow = (obj) => {
    console.log(obj, 'aaaaa')

    // // if (obj.type == 'RD_1') {
    // //   this.addNew(obj);
    // // }
    // this.setState({
    //   processNode: obj.type,
    //   flowData: obj
    // }, () => {
    //   this.action()
    // })

    const { pagination = {} } = this.props.memBasic;
    const { query } = this.props.location;

    const org = getSession('org') || { d01Code: '' };

    this.setState({
      processNode: obj.type,
    }, () => {
      // if (obj.type !== 'RD_1') {
      //   this.action()
      // }
    })
    this.props.dispatch({
      type: 'memBasic/updateState',
      payload: {
        processNode: obj.type
      }
    });
    router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`)
  }

  openAdd = async (record) => {
    const { id, ...other } = record
    this['addDevlop'].destroy();
    await this.props.dispatch({
      type: 'memBasic/zymemInfo',
      payload: {
        memCode: record['code'],
      }
    })
    // }
    this['addDevlop'].open({ canEdit: true, name: '', ...other });
  }
  openInfo = async (record, canEdit = false, editType = 'default') => {
    await this.props.dispatch({
      type: 'memBasic/zymemInfo',
      payload: {
        memCode: record['code'],
      }
    })
    const { memBasic: { basicInfo = {} } = {} } = this.props;
    this['Info'].showModal({ data: basicInfo, file: { value: basicInfo?.filesList, name: record['name'] }, rowCode: record?.code, row: record })
  }
  getReview = async (record) => {
    const { code, data } = await findAuditProcess({ data: { digitalLotNo: record.digitalLotNo, processNode: record.processNode } })
    if (code == 0) {
      this.setState({
        showAuditResult: true,
        review: data
      })
    }
  }
  getActionBtn = (record) => {
    const { processNode = '', isExtendPrepare } = record;
    const { processNode: processNodeState } = this.state;
    let node = null;
    switch (processNode) {
      case 'YBQ_1_1':
        node =
          <React.Fragment>
            {
              isExtendPrepare == 0 ?
                <React.Fragment>
                  <a onClick={() => this['Extend'].open(record)}>延长预备期</a>
                  <Divider type="vertical" />
                </React.Fragment>
                :
                null
            }
            {
              record['isOath'] == '0' ?
                <React.Fragment>
                  <a onClick={() => this.openAdd({ ...record, processNode: 'RDXS' }, true)}>上传入党宣誓材料</a>
                  <Divider type="vertical" />
                </React.Fragment>
                :
                null
            }
          </React.Fragment>
        break;
      case 'YBQ_1_2':
        node = (
          <React.Fragment>
            {
              record['isOath'] == '0' ?
                <React.Fragment>
                  <a onClick={() => this.openAdd({ ...record, processNode: 'RDXS' }, true)}>上传入党宣誓材料</a>
                  <Divider type="vertical" />
                </React.Fragment>
                :
                null
            }

            <a onClick={() => this.openAdd(record, true)}>上传材料</a>
            <Divider type="vertical" />
            {
              isExtendPrepare == 0 ?
                <a onClick={() => this['Extend'].open(record)}>延长预备期</a> :
                null
            }
          </React.Fragment>
        )
        break;
      case 'YBQ_1_3':
        node = (
          <React.Fragment>
            {
              record['isOath'] == '0' ?
                <React.Fragment>
                  <a onClick={() => this.openAdd({ ...record, processNode: 'RDXS' }, true)}>上传入党宣誓材料</a>
                  <Divider type="vertical" />
                </React.Fragment>
                :
                null
            }

            <a onClick={() => this.openAdd(record, true)}>上传材料</a>
            <Divider type="vertical" />
            {
              isExtendPrepare == 0 ?
                <a onClick={() => this['Extend'].open(record)}>延长预备期</a> :
                null
            }
          </React.Fragment>
        )
        break;
      case 'YBQ_2_1':
      case 'YBQ_2_2':
      case 'YBQ_2_3':
        node = (
          <React.Fragment>
            {
              record['isOath'] == '0' ?
              <React.Fragment>
                <a onClick={() => this.openAdd({ ...record, processNode: 'RDXS' }, true)}>上传入党宣誓材料</a>
                <Divider type="vertical" />
              </React.Fragment>
              :
              null
            }
            <a onClick={() => this.openAdd(record, true)}>上传材料</a>
          </React.Fragment>
        )
      case 'YBQ_3':
        // node = <a onClick={() => this.openAdd(record, true)}>上传材料</a>;
        node = (
          <React.Fragment>
            {
              record['isOath'] == '0' ?
              <React.Fragment>
                <a onClick={() => this.openAdd({ ...record, processNode: 'RDXS' }, true)}>上传入党宣誓材料</a>
                <Divider type="vertical" />
              </React.Fragment>
              :
              null
            }
            <a onClick={() => this.openAdd(record, true)}>上传材料</a>
          </React.Fragment>
        )
        break;
      case 'YBQ_4_1':
      case 'YBQ_4_2':
      case 'YBQ_4_3':
        node = record['approve'] != false ? <a onClick={() => this.openAdd(record, true)}>上传材料</a> : null;
        break;
      case 'YBQ_6':
        node = <React.Fragment>
          {
            record?.auditStatus == 0 &&
            <a onClick={() => this.openInfo({ ...record, name: '县委审核申请' })}>提交县委审核申请</a>
          }
          {
            record?.auditStatus == 3 &&
            <React.Fragment>
              <a onClick={() => this.openInfo({ ...record, name: '县委审核申请' })}>提交县委审核申请</a>
              {/* <Divider type="vertical" />
              <a onClick={() => this.getReview(record)}>查看审核结果</a> */}
            </React.Fragment>
          }
        </React.Fragment>
    }
    return node
  }
  digitalArchives = async (record) => {
    // this['editDevlop'].destroy();
    if (record && record['code']) {
      await this.props.dispatch({
        type: 'memBasic/findMem',
        payload: {
          code: record['code']
        }
      })
    }
    this['editDevlop'].open({ ...record });

  }
  tobook = (records) => {
    this['ElectronicArchives'].showModal(records['code'])
  }
  componentWillUnmount(): void {
    this.props.dispatch({
      type: 'memBasic/updateState',
      payload: {
        memName: undefined,
        filter: {},
        processNode: []
      }
    })
  }
  render(): React.ReactNode {
    const { memBasic = {}, loading: { effects = {} } = {}, commonDict, compType } = this.props;
    const { list, pagination, memName, filter, flowCount = {} } = memBasic;
    const { current, pageSize } = pagination;
    const { filterHeight, processNode, review = {} } = this.state;
    const filterData = [
      {
        key: 'd09CodeList', name: '工作岗位', value: commonDict['dict_d09_tree'],
      },
      {
        key: 'sexCodeList', name: '人员性别', value: [{ key: '1', name: '男' }, { key: '0', name: '女' }],
      },
      {
        key: 'd08CodeList', name: '人员类型', value: [{ key: '1', name: '正式党员' }, { key: '2', name: '预备党员' }],
      },
      {
        key: 'd07CodeList', name: '学历教育', value: commonDict['dict_d07_tree'],
      },
      {
        key: 'd194CodeList', name: '国民经济行业', value: this.props.commonDict[`dict_d194_tree`],
      },
      {
        key: 'd195CodeList', name: '生产性服务行业', value: this.props.commonDict[`dict_d195_tree`],
      },
    ];
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 60,
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1
        }
      },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 100,
        render: (text, record) => {
          if (compType == 'lock') {
            return text;
          }
          return (
            // 在党员查询列表里面加一个图标，鼠标悬停后显示，该预备党员预备期已满，请及时维护
            <div>{record['message'] ? <WarningTwoTone style={{ fontSize: '20px' }} twoToneColor={'#faad14'} title={record['message']} /> : ''} <a onClick={() => this.addOrEdit(record)} >{text}</a></div>
          )
        }
      },
      {
        title: '性别',
        dataIndex: 'sexCode',
        width: 100,
        render: (text) => {
          return (
            <span> {text === '1' ? '男' : '女'} </span>
          )
        }
      },
      {
        title: '公民身份证',
        dataIndex: 'idcard',
        width: 160,
        render: (text, record) => {
          if (typeof text === 'string' && !_isEmpty(text)) {
            // let newVal=text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
            let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z\(\)\[\]]{4})/, "$1***********$2"); //增加港澳台身份证马赛克
            if (text.indexOf("*") > 0) {
              return text
            }
            return (
              <span>{newVal}</span>
            );
          } else {
            return ''
          }
        }
      },
      {
        title: '电话',
        width: 100,
        dataIndex: 'phone',
        render: (text, record) => {
          if (typeof text === 'string' && !_isEmpty(text)) {
            let newVal = text.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
            if (text.indexOf("*") > 0) {
              return text
            }
            return (
              <span>{newVal}</span>
            );
          } else {
            return ''
          }
        }
      },
      {
        title: '党员类型',
        width: 120,
        dataIndex: 'd08Name',
      },
      {
        title: '所在组织',
        dataIndex: 'orgName',
        render: (text, record) => {
          return (
            <a onClick={() => this.lookOrgs(record)}>{text}</a>
          )
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 270,
        render: (text, record) => {
          const { d08Code, isExtend, isExtendPrepare } = record;
          let menuArr = [
            // { key: '1', value: '预备党员转正' },
            // { key: '2', value: '延长预备期' },
            // {key:'3',value:'撤销延长'},
          ];
          if (isExtendPrepare === 1) {
            menuArr = menuArr.filter(it => it.key != '2');
          }
          if (isExtend !== 1) {
            menuArr = menuArr.filter(item => item['key'] !== '3')
          }
          // 当此页面作为组件时，会有compType对应的功能
          if (compType == 'lock') {
            return (
              <TableActionMenu record={record} lockOrUnlock={this.lockOrUnlock} type={'1'} />
            )
          }
          return (
            <span>
              {/* <a onClick={() => this.tobook(record)}>电子档案</a>
              <Divider type="vertical" /> */}
              <a onClick={() => this.digitalArchives(record)}>档案管理</a>
              <Divider type="vertical" />
              <a onClick={() => this.addOrEdit(record)}>编辑</a>
              {/* <Divider type="vertical"/> */}
              {/*<Popconfirm title={d08Code === '2' ? '是否取消预备党员资格？' : '是否离开党组织？'} onConfirm={()=>this.del(record)}>*/}
              {/*    <a className={'del'} >{d08Code === '2' ? '取消预备党员资格' : '离开党组织'}</a>*/}
              {/*</Popconfirm>*/}
              <Divider type="vertical" />
              <TableActionMenu record={record} lockOrUnlock={this.lockOrUnlock} type={'1'} />
              <Divider type="vertical" />
              <Dropdown overlay={(
                <Menu>
                  {
                    d08Code === '2' ? menuArr.map((item, index) => {
                      return (
                        <Menu.Item key={index}>
                          <a onClick={() => this.cancel(record, item['key'])}>{item['value']}</a>
                        </Menu.Item>
                      )
                    }) : null
                  }
                  <Menu.Item>
                    <Popconfirm title={'离开党组织仅包含死亡、错误录入，离开本支部请使用关系转接进行处理！'} placement="topRight" onConfirm={() => this.del(record)}>
                      <a className={'del'} >{d08Code === '2' ? '离开党组织' : '离开党组织'}</a>
                    </Popconfirm>
                  </Menu.Item>
                  <Menu.Item>
                    <a onClick={() => { this['Lost'].open({ record }); }}>
                      失联党员
                    </a>
                  </Menu.Item>

                  {
                    d08Code === '1' &&

                    <Menu.Item>
                      <a onClick={() => this['CancelLevel'].open(record)}>党员退回到预备党员</a>
                      {/* <Popconfirm title={d08Code === '2' ? '是否预备党员退回到发展党员？' : '是否党员退回到预备党员？'}
                        onConfirm={async () => {
                          let url: any = zybackToProbationary;
                          if (d08Code == '2') {
                            url = backToDevelopMem;
                          }
                          const { code: resCode = 500 } = await url({ code: record.code });
                          if (resCode === 0) {
                            Tip.success('操作提示', '操作成功');
                            this.action();
                          }
                        }}>
                        <a>{d08Code == '2' ? '预备党员退回到发展党员' : '党员退回到预备党员'}</a>
                      </Popconfirm> */}
                    </Menu.Item>
                  }


                  <Menu.Item>
                    <a onClick={() => {
                      this['ModalTL'].open(record);
                    }}>人员时间轴</a>
                  </Menu.Item>

                  {/* <Menu.Item>
                    <a onClick={() => {
                      this['TimeCheck'].open(record);
                    }}>入党时间修正</a>
                  </Menu.Item> */}

                  {/*{*/}
                  {/*  d08Code == '2' ?*/}
                  {/*    :*/}
                  {/*    <Menu.Item>*/}
                  {/*      <a onClick={()=>{*/}

                  {/*      }}>党员退回到预备党员</a>*/}
                  {/*    </Menu.Item>*/}
                  {/*}*/}
                </Menu>
              )}>
                <a className="ant-dropdown-link">
                  业务操作 <DownOutlined />
                </a>
              </Dropdown>
              <Divider type="vertical" />
              {
                this.getActionBtn(record)
              }
            </span>
          );
        },
      },
    ];
    const org = getSession('org') || { d01Code: '' };
    const { d01Code = '' } = org || {};
    // 权限列表有92	才显示补录按钮
    const pidArr: any = getSession('pid') || [];

    return (
      <Fragment>
        <NowOrg
          extra={
            <React.Fragment>
              {
                (d01Code === '631' || d01Code === '632' || d01Code === '634' || d01Code === '931' || d01Code === '932') && !compType &&
                <Button htmlType={'button'} onClick={this.lock} style={{ marginRight: 16 }}>批量锁定</Button>
              }
              {!compType && <Button htmlType={'button'} onClick={this.exportInfo} disabled={_isEmpty(list)}>导出</Button>}
              {
                (d01Code === '631' || d01Code === '632' || d01Code === '634' || d01Code === '931' || d01Code === '932') && !ButtonDisabled.statistics2021 && !compType && pidArr.includes(92) &&
                <Button htmlType={'button'} type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.addNew} style={{ marginLeft: 16 }}>补录党员</Button>
              }
              <Search style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'} />
            </React.Fragment>
          }
        />
        <Flow onChange={this.clickFlow} data={flowCount} />
        <Info ref={e => this['Info'] = e} upList={this.action} />
        {
          !compType &&
          <Fragment>
            <RuiFilter
              data={filterData}
              onChange={this.filterChange}
              openCloseChange={() => {
                setListHeight(this, 150);
              }}
            />
          </Fragment>
        }
        <WhiteSpace />
        <WhiteSpace />
        <ListTable scroll={{ y: filterHeight }} columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange} />
        <AddEdit getList={this.action} />
        <Lost ref={e => this['Lost'] = e} onOK={this.action} />
        <LeaveOrg wrappedComponentRef={e => this['LeaveOrg'] = e} onClose={this.action} />
        <LeaveTable ref={e => this['LeaveTable'] = e} onOK={(record) => {
          this['LeaveOrg'].open(record, 'manage');
        }} />
        <OrgLevel {...this.props} ref={e => this['OrgLevel'] = e} />
        <ToPositive wrappedComponentRef={e => this['ToPositive'] = e} {...this.props} onClose={this.action} />
        <DelayMem wrappedComponentRef={e => this['DelayMem'] = e} {...this.props} onClose={this.action} />
        <Extend wrappedComponentRef={e => this['Extend'] = e} {...this.props} onClose={this.action} />

        <CancelMem wrappedComponentRef={e => this['CancelMem'] = e} {...this.props} onClose={this.action} />
        <ExportInfo wrappedComponentRef={e => this['memExportInfo'] = e}
          expinfo={[{
            label: '党员基本信息',
            value: '1'
          }, {
            label: '本年内参加民主评议的党员',
            value: '2'
          }, {
            label: '本年内受纪律处分党员',
            value: '3'
          },]}
          expType='exportType'
          tableName={'ccp_mem'}
          tableListQuery={{ memName, ...filter, searchType: 1, memOrgCode: org['orgCode'], processNode }}
          action={'/api/data/mem/exportData'}
        />
        <ModalTL
          ref={e => this['ModalTL'] = e}
          timeLineAction={findMemResume}
        />
        <TimeCheck ref={e => this['TimeCheck'] = e} onOK={this.action} />
        <LockModal ref={e => this['LockModal'] = e} onOK={this.action} />
        <Add wrappedComponentRef={e => this['addDevlop'] = e} onsubmit={this.action} {...this.props} tipMsg={this.state.tipMsg} />
        <EditDevlop wrappedComponentRef={e => this['editDevlop'] = e} onclose={this.action} {...this.props} tipMsg={this.state.tipMsg} />
        <Modal
          title='查看审核结果'
          visible={this.state.showAuditResult}
          onCancel={() => {
            this.setState({ showAuditResult: false })
          }
          }
          width={800}
          footer={[
            <Button onClick={() => {
              this.setState({ showAuditResult: false })
            }}>取消</Button>
          ]}
        >
          <Descriptions title="" column={1}>
            <Descriptions.Item label="审核结果">{review?.status == '0' ? '不通过' : '通过'}</Descriptions.Item>
            {
              review?.status == '0' &&
              <Descriptions.Item label="不通过原因">{review?.reason}</Descriptions.Item>
            }
          </Descriptions>
        </Modal>
        <ElectronicArchives ref={e => this['ElectronicArchives'] = e} onoK={this.action} />
        <CancelLevel {...this.props} wrappedComponentRef={e => this['CancelLevel'] = e} submit={this.action} />
      </Fragment>
    );
  }
}
