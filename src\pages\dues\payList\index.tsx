/**
 * 党费交纳列表
 * */
import React from 'react';
import ListTable from '@/components/ListTable';
import ListFilter from '@/components/ListFilter';
import NowOrg from '@/components/NowOrg';
import ExportFile from './exportFile'
import { Button, Input, Tag, Tabs, Select, DatePicker, Modal, Divider, Popconfirm } from 'antd';
import styles from "./index.less";
import { connect } from 'dva';
import moment from 'moment';
import Pay from './pay'
import { isEmpty, setListHeight } from '@/utils/method';
import {getSession} from "@/utils/session";
const TabPane = Tabs.TabPane;
const Search=Input.Search;
const Option = Select.Option;
const { MonthPicker, } = DatePicker;
@connect(({dues,login})=>({
  dues,
  login
}))
export default class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      memName:'',
      orgCode:'',
      type:'add',
      visible:false,
      menuTreeData:[],
      date:moment().valueOf(),
      tab:'1',
      years:moment().format('YYYY')
    }
  }
  //JSON.parse(sessionStorage.getItem('org') || "")['code']||
  componentDidMount = () => {
    let org=getSession('org')|| {};
    this.setState({
      orgCode:org['orgCode'],
      org:org,
    },()=>{
      this.onPage();
    });
    setListHeight(this);
  };
  componentWillReceiveProps =(nextProps: Readonly<any>, nextContext: any) => {
    if (!isEmpty(this.state.orgCode)&&this.state.orgCode!==JSON.parse(sessionStorage.org)['orgCode']) {
      this.setState({orgCode:JSON.parse(sessionStorage.org)['orgCode']},()=>{
        const { tab } = this.state;
        if (tab == '1') {
          this.onPage();
        }else {
          this.selectCount();
        }
      })
    }
  };
  onPage = ( pageNum=1,size=10) => {
    let val = {
      memOrgOrgCode:this.state['orgCode'],
      pageNum:pageNum,
      pageSize:size,
      findDate:this.state['date'],
      memName:this.state['memName']
    };
    for (let obj in val) {
      if (isEmpty(val[obj])) {
        delete val[obj]
      }
    }
    this.props.dispatch({
      type:'dues/payList',
      payload:{
        data:{
          ...val
        }
      }
    })
  };
  // onChange = (page) => {
  //   this.onPage(page);
  // };

  isSearch = (value) => {
    this.setState({memName:value},()=>{
      const { tab } = this.state;
      if (tab == '1') {
        this.onPage();
      }else {
        this.selectCount();
      }
    })
  };

  changePage=(v,k)=>{
    const { tab } = this.state;
    if (tab == '1') {
      this.onPage(v,k);
    }else {
      this.selectCount(v,k);
    }
    this.setState({page:v,pageNum:k})
  };
  changeList=()=>{
    this.onPage();
  };
  goPay=()=>{
    this['Pay'].showModal();
  };
  export=()=>{
    this['ExportFile'].showModal();
  };
  disabledTomorrow=(current) =>{
    // Can not select days before today and today
    return current && current < moment('2019')||current>moment().endOf('day');
  };

  changeDate=(v)=>{
    this.setState({
      year:moment(v).format('YYYY'),
      month:moment(v).format('M'),
      date:moment(v).valueOf()
    },()=>{
      const { tab } = this.state;
      if (tab == '1') {
        this.onPage();
      }else {
        this.selectCount();
      }
    })
  };
  handleOk=()=>{

  };
  handleCancel=()=>{
    this.setState({visible:false})
  };
  changeInfo=(e)=>{
    this.setState({tab:e},()=>{
      this.selectCount(1,10);
    })
  };
  selectCount=(pageNum=1,size=10)=>{
    let val = {
      memOrgOrgCode:this.state['orgCode'],
      pageNum:pageNum,
      pageSize:size,
      year:this.state['years'],
      orgName:this.state['memName']
    };
    for (let obj in val) {
      if (isEmpty(val[obj])) {
        delete val[obj]
      }
    }
    this.props.dispatch({
      type:'dues/getPayTotalListt',
      payload:{
        data:{
          ...val
        }
      }
    })
  };
  onChangeYear=(val)=>{
    const { tab } =this.state;
    this.setState({years:val},()=>{
      this.selectCount();
    });
  };
  showCount=(record,key)=>{
    const { feeVOList=[] }=record;
    let now = moment().format('M');
   if (!isEmpty(feeVOList)) {
     if (feeVOList[key]['month'] <= parseInt(now)) {
       return (
         <div>
           <div style={{color:'#44B549'}}>应交:<span>{feeVOList[key]['submittedMoney']}元</span></div>
           <div style={{color:'#FF9F08'}}>未交:<span>{feeVOList[key]['unpaidMoney']}元</span></div>
         </div>
       )
     }else {
       return (
         <div>
           <div style={{color:'#FF9F08'}}>未交:<span>{feeVOList[key]['unpaidMoney']}元</span></div>
         </div>
       )
     }
   }
  };
  render(): React.ReactNode {
    const { dues:{ list=[],pagination:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={},list1=[],pagination1={}}={},login:{userRole=[]}, loading:{effects = {}} = {}} =this.props;
    const { type,id,menuTreeData,dataInfo={} ,filterHeight,org={},tab}=this.state;
    const monthFormat = 'YYYY/MM';
    let years=parseInt(moment().format('YYYY'));
    let yearArr:any=[];
    for (let i:any=2019;i<=years;i++){
      yearArr.push(i)
    }
    const dda=[
      {
        time:'2019-02-02',
        bankMoney:200,
        sysMoney:190,
        poor:10,
        percentage:'20%'
      },
    ];
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'姓名',
        dataIndex:'memName',
        width:50,
      },
      {
        title:'交纳人',
        dataIndex:'creatorName',
        width:50,
        render:(text,record) => {
          return <span>{isEmpty(text)?record['creatorAccount']:text}</span>
        }
      },
      {
        title:'交纳金额',
        dataIndex:'money',
        width:50,
        render:text => {
          return <span>{text=='0'?'免交':text+'元'}</span>
        }
      },
      {
        title:'交费类型',
        dataIndex:'payType',
        width:100,
        render:text => {
          return (
            <span>
            {
              text=='1'?
                <Tag color="gold">手机支付</Tag>
                :
                <Tag color="green">PC支付</Tag>
            }
          </span>
          )
        }
      },
      {
        title:'组织名称',
        dataIndex:'orgName',
        width:150,
      },
      {
        title:'交纳类型',
        dataIndex:'orderType',
        width:100,
        render:text => {
          return (
            <span>
            {
              text=='1'?
                <Tag color="gold">个人交纳</Tag>
                :
                <Tag color="green">代缴</Tag>
            }
          </span>
          )
        }
      },
      {
        title:'交费时间',
        dataIndex:'payDate',
        width:50,
        render:text => {
          return <span>{moment(text).format('YYYY-MM-DD')}</span>
        }
      },
    ];
    const columns1=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'所属组织',
        dataIndex:'orgShortName',
        width:80,
      },
      {
        title:'一月',
        dataIndex:'feeVOList1',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'二月',
        dataIndex:'feeVOList2',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'三月',
        dataIndex:'feeVOList3',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'四月',
        dataIndex:'feeVOList4',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'五月',
        dataIndex:'feeVOList5',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'六月',
        dataIndex:'feeVOList6',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'七月',
        dataIndex:'feeVOList7',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'八月',
        dataIndex:'feeVOList8',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'九月',
        dataIndex:'feeVOList9',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'十月',
        dataIndex:'feeVOList10',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'十一月',
        dataIndex:'feeVOList11',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'十二月',
        dataIndex:'feeVOList12',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
    ];
    const columns2=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'对账时间',
        dataIndex:'time',
        width:100,
      },
      {
        title:'银行金额',
        dataIndex:'bankMoney',
        width:100,

      },
      {
        title:'系统金额',
        dataIndex:'sysMoney',
        width:100,

      },
      {
        title:'相差金额',
        dataIndex:'poor',
        width:100,

      },
      {
        title:'对账百分比',
        dataIndex:'percentage',
        width:100,

      },
      {
        title:'操作',
        dataIndex:'action',
        width:80,
        render:(text,record)=>{
          return(
            <React.Fragment>
              <a href={'#'}>核实</a>
              <Divider type="vertical"/>
              <a href={'#'}>纠正</a>
            </React.Fragment>
          )
        },
      }
    ];
    return(
      <div className={styles.container}>
        <Tabs defaultActiveKey={tab} onChange={this.changeInfo}>
          <TabPane tab="基本信息" key="1"/>
          <TabPane tab="统计信息" key="2"/>
          <TabPane tab="党费对账" key="3"/>
        </Tabs>
        <NowOrg  extra={
          <ListFilter>
            {
              (tab=='1'||tab=='2')&&
              <Search
                placeholder="请输入检索关键词"
                onSearch={value => this.isSearch(value)}
                style={{ width: 200 }}
                className={styles.filter}
              />
            }
            {
              tab=='2'&&
              <Select
                showSearch
                style={{ width: 200 ,marginLeft:20}}
                placeholder="Select a person"
                defaultValue="2019"
                onChange={ this.onChangeYear}
              >
                {
                  yearArr.map((item,index)=>{
                    return <Option key={index} value={`${item}`}>{item}</Option>
                  })
                }
              </Select>
            }
            {
              ( tab=='1'||tab=='3')&&
              <MonthPicker
                disabledDate={this.disabledTomorrow}
                placeholder="请选择日期"
                style={{marginLeft:20}}
                defaultValue={moment()}
                format={monthFormat}
                onChange={this.changeDate}/>
            }
            {
              tab=='1'&&
              <React.Fragment>
                <Button style={{marginLeft:20}} type="primary" onClick={this.export}>党费明细导出</Button>
                <Button style={{marginLeft:20}} type="primary" onClick={this.goPay}>党费批量交纳</Button>
              </React.Fragment>
            }
            {
              tab=='3'&&
              <Button
                style={{marginLeft:20}}
                type="primary"
                // onClick={this.goPay}
              >党费对账</Button>
            }
          </ListFilter>
        }/>
        <Pay wrappedComponentRef={(e)=>this['Pay']=e} data={org} onChange={this.changeList}/>
        <ExportFile wrappedComponentRef={(e)=>this['ExportFile']=e} onChange={this.changeList}/>
        {
          tab=='1'&&
            <ListTable
              columns={columns}
              data={list}
              scroll={{y:filterHeight}}
              pagination={{pageSize,total:totalRow,page,current:pageNumber}}
              onPageChange={this.changePage}/>
        }
        {
          tab=='2'&&
          <ListTable
            columns={columns1}
            data={list1}
            scroll={{y:filterHeight}}
            pagination={pagination1}
            onPageChange={this.changePage}/>
        }
        {
          tab=='3'&&
          <ListTable
            columns={columns2}
            data={dda}
            scroll={{y:filterHeight}}
            pagination={pagination1}
            onPageChange={this.changePage}/>
        }
      </div>
    );
  }
}
