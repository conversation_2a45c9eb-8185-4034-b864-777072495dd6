/**
 * 组织选中器
 */
import React from 'react';
import {Input, Modal} from 'antd';
import OrgTree from '../OrgTree';
import List from './list';
import {connect} from "dva";
import styles from './index.less';
import {root, rootParent} from 'src/common/config.js';
import {getSession} from "@/utils/session";

interface pType {
  dispatch?:any,
  onChange?:(data:Array<object>)=>void,
  placeholder?:string,
  disabled?:boolean,
  orgTypeList?:Array<string>,
  org?:object,
  common?:any,
  initValue?:any,
  exclude?:any,
  multiple?:boolean,
  isPermissionCheck?:string,//是否权限检验,0--不进行校验,1--进行权限校验,为空默认进行权限检验
}
const Search = Input.Search;
// @ts-ignore
@connect(({common})=>({common}))
export default class index extends React.Component<pType,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      value:undefined,
      org:getSession('org'),
      isExOrg:false,
      data:[],
    }
  }
  show=()=>{
    const {org}=this.props;
    if(org){
      this.loadData([org['orgCode'] || org['code']])
    }
    this.setState({
      visible:true,
    });
  };
  handleOk=()=>{
    const {onChange}=this.props;
    const {data}=this.state;
    let obj=data[0] || {};
    onChange && onChange(data);
    this.setState({
      value:obj.industryName
    });
    this.handleCancel();
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
    })
  };
  loadData=(val)=>{
    const {isPermissionCheck=undefined}=this.props;
    this.props.dispatch({
      type:'common/getTree',
      payload:{
        data:{
          orgCodeList:val,
          excludeOrgCodeList:[],
          isPermissionCheck,
        }
      }
    });
  };
  treeSearch=(val)=>{
    this.props.dispatch({
      type:'common/queryTree',
      payload:{
        name:val
      }
    });
  };
  onChange=(data=[])=>{
    this.setState({
      data,
      value:data[0]['industryName']
    })
  };
  treeChange=(selectedKeys,e)=>{
    const {dataRef}=e.node;
    this.setState({
      org:dataRef
    });
    List['WrappedComponent'].action({orgCode:dataRef['orgCode'],pageNum:1});
  };
  static getDerivedStateFromProps(props,state){
    const {initValue,common,org}=props;
    const {mapTreeCode}=common;
    let {value,isExOrg}=state;
    if(initValue && !value){
      let obj=mapTreeCode.get(initValue);
      if(obj){
        return {value:obj['name']}
      }else if(root['parentCode']===initValue){
        return {value:rootParent['name']}
      }else{
        return {value:initValue}
      }
    }
    if(!isExOrg && org){
      return {org,isExOrg:true}
    }
    return null;
  };
  render(){
    const {visible,value,org}=this.state;
    const {common,children,placeholder,disabled=false,orgTypeList,exclude=[],isPermissionCheck=undefined,multiple=false,initValue=[]}=this.props;
    let listOrg=org;
    // if(this.props.org){
    //   listOrg=this.props.org || {};
    // }
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.show,
          }) :
            <Search
              value={value}
              disabled={disabled}
              onClick={this.show}
              onSearch={this.show}
              placeholder={placeholder || '请点击选择'}
              enterButton
            />
        }
        <Modal
          title="组织选择器"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1200}
          bodyStyle={{padding:0}}
        >
          <div className={styles.content}>
            <div className={styles.tree}>
              <OrgTree
                listData={common['listTree']}
                mapData={common['mapTree']}
                filterData={common['filterData']}
                loadData={this.loadData}
                onSearch={this.treeSearch}
                onChange={this.treeChange}
                showSearch={false}
                rootCode={this.props.org ? this.props.org['code'] : undefined}
                type={'selector'}
                exclude={exclude}
              />
            </div>
            <div className={styles.list}>
              <List
                org={org}
                value={multiple ? initValue : []}
                multiple={multiple}
                exclude={exclude}
                orgTypeList={orgTypeList}
                isPermissionCheck={isPermissionCheck}
                orgCode={listOrg['orgCode'] || listOrg['managerOrgCode']}
                onChange={this.onChange}
              />
            </div>
          </div>
        </Modal>
      </React.Fragment>
    )
  }
}
