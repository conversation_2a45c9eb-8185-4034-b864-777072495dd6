.header{
  width: 100%;
  text-align: center;
  display: table;
  white-space: nowrap;
  margin: 10px auto;
  &>span{
    display: table-cell;
    padding: 0 12px;
    font-size: 17px;
    font-weight: 500;
    color: rgb(0,0,0);
  }
  &::before,&::after{
    content: "";
    width:50%;
    //height:2px;
    //background: #f03eff;
    display:table-cell;              /*1.首先使伪类显示方式为块级元素*/
    position:relative;
    border-top:1px solid #e9e9e9;
    transform: translateY(50%);
  }
  &::before{
    width: 3%;
  }
  &::after{
    width: 97%;
  }
}
