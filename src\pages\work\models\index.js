import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {add,getList,pushTrend,cancelTrend,delTrend,updateTrend,portalTrend,checkTrendList,checkTrend,afreshTrend,findTrendByCode} from '../services';
import {getSession} from "@/utils/session";

const workTrend = modelExtend(listPageModel,{
  namespace: "workTrend",
  state:{

  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname,query } = location;
        const defaultParas={
          pageNum:1,
          pageSize:10,
        };
        const org=getSession('org') || {};
        if(pathname==='/work/trend'){
          dispatch({
            type:'getList',
            payload:{
              data:{
                ...defaultParas,
                orgOrgCode:org['orgCode'],
                ...query,
              }
            }
          });
        }
        if(pathname==='/work/audit'){
          dispatch({
            type:'checkTrendList',
            payload:{
              data:{
                ...defaultParas,
                code:org['code'],
                ...query,
              }
            }
          });
        }
      });
    }
  },
  effects: {
    // 列表
    *getList({payload}, {call, put,select}) {
      const {filter,trendName}=yield select(state=>state['workTrend']);
      payload['data']={...payload['data'],checkTypeList:['1'],...filter,trendName};
      const {data={}}=yield call(getList, payload);
      yield put({
        type: 'querySuccess',
        payload: {
          list:data['list'] || [],
          pagination:{
            current:data['pageNumber'] || 0,
            pageSize:data['pageSize'] || 0,
            total:data['totalRow'] || 0,
          }
        }
      })
    },
    *add({payload}, {call, put,select}) {
      return yield call(add, payload);
    },
    *updated({payload}, {call, put,select}) {
      return yield call(updateTrend, payload);
    },
    *pushTrend({payload}, {call, put,select}) {
      return yield call(pushTrend, payload);
    },
    *cancelTrend({payload}, {call, put,select}) {
      return yield call(cancelTrend, payload);
    },
    *delTrend({payload}, {call, put,select}) {
      return yield call(delTrend, payload);
    },
    *portalTrend({payload}, {call, put,select}) {
      return yield call(portalTrend, payload);
    },
    *checkTrend({payload}, {call, put,select}) {
      return yield call(checkTrend, payload);
    },
    *afreshTrend({payload}, {call, put,select}) {
      return yield call(afreshTrend, payload);
    },
    *findTrendByCode({payload}, {call, put,select}) {
      return yield call(findTrendByCode, payload);
    },
    *checkTrendList({payload}, {call, put,select}) {
      const {filter,trendName}=yield select(state=>state['workTrend']);
      payload['data']={...payload['data'],checkTypeList:['1'],...filter,trendName};
      const {data={}}=yield call(checkTrendList, payload);
      yield put({
        type: 'updateState',
        payload: {
          list2:data['list'] || [],
          pagination2:{
            current:data['pageNumber'] || 0,
            pageSize:data['pageSize'] || 0,
            total:data['totalRow'] || 0,
          }
        }
      })
    },
    *reSet({payload}, {call, put,select}) {
      yield put({
        type:'updateState',
        payload:{
          filter:[],
          trendName:undefined,
        }
      })
    },
  }
});
export default workTrend;
