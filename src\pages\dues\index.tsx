/**
* 党费列表
* */
import React from 'react';
import ListTable from '@/components/ListTable';
import ListFilter from '@/components/ListFilter';
import NowOrg from '@/components/NowOrg';
import { Input, Popover,Tabs,Select  } from 'antd';
import styles from "./index.less";
import { connect } from 'dva';
import moment from 'moment';
import Standard from './standard';
import LastDate from './lastDate'
import { isEmpty, setListHeight } from '@/utils/method';
import {getSession} from "@/utils/session";
const TabPane = Tabs.TabPane;
const Search=Input.Search;
const Option = Select.Option;
@connect(({dues,login})=>({
  dues,
  login
}))
export default class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      memName:'',
      orgCode:'',
      type:'add',
      visible:false,
      menuTreeData:[],
      year:moment().format('YYYY'),
      tab:'1'
    }
  }
  //JSON.parse(sessionStorage.getItem('org') || "")['code']||
  componentDidMount = () => {
    let org=getSession('org')|| {};
    this.setState({
      orgCode:org['orgCode']
    },()=>{
      this.onPage();
    });
    setListHeight(this);
  };
  componentWillReceiveProps =(nextProps: Readonly<any>, nextContext: any) => {
    const { tab }=this.state;
    if (!isEmpty(this.state.orgCode)&&this.state.orgCode!==JSON.parse(sessionStorage.org)['orgCode']) {
      this.setState({orgCode:JSON.parse(sessionStorage.org)['orgCode']},()=>{
        if (tab == '1') {
          this.onPage();
        }else {
          this.selectCount();
        }
      })
    }
  };
  onPage = ( pageNum=1,size=10) => {
    this.props.dispatch({
      type:'dues/list',
      payload:{
        data:{
          memOrgOrgCode:this.state['orgCode'],
          pageNum:pageNum,
          pageSize:size,
          year:this.state['year'],
          memName:this.state['memName']
        }
      }
    })
  };
  onChange = (page) => {
    this.onPage(page);
  };
  onChangeYear=(val)=>{
    const { tab } =this.state;
    this.setState({year:val},()=>{
      if (tab == '1') {
        this.onPage();
      }else {
        this.selectCount();
      }
    });
  };
  isSearch = (value) => {
    const { tab } = this.state;
    this.setState({memName:value},()=>{
      if (tab == '1') {
        this.onPage();
      }else {
        this.selectCount();
      }
    })
  };
  changePage=(v,k)=>{
    const { tab }=this.state;
    if (tab == '1') {
      this.onPage(v,k);
    }else {
      this.selectCount(v,k)
    }
    this.setState({page:v,pageNum:k})
  };
  changeList=()=>{
    this.onPage();
  };
  goStandard=(record,month)=>{
    this.setState({
      dataInfo:{
        ...record,
        month:month,
        year:this.state['year']
      },
    },()=>{
      if (isEmpty(record['lastPayDate'])) {
        this['LastDate'].showModal();
      }else {
        this['Standard'].showModal();
      }
    });

  };
  show = (value,key) => {
    let valuea=value;
    let val=value['feeList'];
    if (!isEmpty(value['startPayDate'])&&moment(value['startPayDate']).format('M')>=key&&moment(value['startPayDate']).format('YYYY')>=this.state['year']) {
      return '--'
    }else {
      if (isEmpty(val)){
        return <a onClick={()=>this.goStandard(value,key)}>{'未设置'}</a>
      } else {
        let fand = val.filter(it=>it.month==key);
        if (!fand.map(it => it.month).includes(key.toString())) {
          return <a onClick={()=>this.goStandard(valuea,key)}>{'未设置'}</a>
        }else {
          let value= fand.map((item,index)=>{
            if (isEmpty(item['standard'])){
              return <a key={index} onClick={()=>this.goStandard(valuea,key)}>{'未设置'}</a>
            } else {
              if (isEmpty(item['payMoney'])||item['payMoney']=='0'){
                let content= (
                  <div key={index} className={styles.top}>
                    <span>应交：{item['standard']||''}</span>
                    <span>已交：{item['payMoney']||0}</span>
                  </div>
                );
                return (
                  <React.Fragment>
                    {
                      item['d49Code']=='4'?'免交':
                        <Popover content={content} trigger="hover" key={index}>
                          <a onClick={()=>this.goStandard(valuea,key)} style={{color:'#FF9F08'}}>{item['standard']&&item['standard']+'元'}</a>
                        </Popover>
                    }
                  </React.Fragment>
                )
              }else {
                return <a style={{color:'#44B549'}}>{item['payMoney']&&item['payMoney']+'元'}</a>
              }
            }
          });
          return value
        }
      }
    }
  };

  changeInfo=(e)=>{
    this.setState({tab:e},()=>{
       this.selectCount(1,10);
    })
  };
  selectCount=(pageNum=1,size=10)=>{
    this.props.dispatch({
      type:'dues/getCountList',
      payload:{
        data:{
          memOrgOrgCode:this.state['orgCode'],
          pageNum:pageNum,
          pageSize:size,
          year:this.state['year'],
          orgName:this.state['memName']
        }
      }
    })
  };
  showCount=(record,key)=>{
    const { feeVOList=[] }=record;
    let now = moment().format('M');
    if (!isEmpty(feeVOList)) {
      if (feeVOList[key]['month'] <= parseInt(now)) {
        return (
          <div>
            <div style={{color:'#44B549'}}>已交纳:<span>{feeVOList[key]['submittedCount']}人</span></div>
            <div style={{color:'#FF9F08'}}>未交纳:<span>{feeVOList[key]['unpaidCount']}人</span></div>
          </div>
        )
      }else {
        return (
          <div>
            <div style={{color:'#FF9F08'}}>未交纳:<span>{feeVOList[key]['unpaidCount']}人</span></div>
            <div style={{color:'#339DFF'}}>未设置:<span>{feeVOList[key]['notSetCount']}人</span></div>
          </div>
        )
      }
    }
  };

  render(): React.ReactNode {
    const { dues:{ list=[],pagination:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={},list1=[],pagination1={} }={},login:{userRole=[]}, loading:{effects = {}} = {}} =this.props;
    const { type,id,menuTreeData,dataInfo={} ,filterHeight,tab}=this.state;
    let years=parseInt(moment().format('YYYY'));
    let yearArr:any=[];
    for (let i:any=2019;i<=years;i++){
      yearArr.push(i)
    }
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'姓名',
        dataIndex:'name',
        width:50,
      },
      {
        title:'所属组织',
        dataIndex:'orgName',
        width:100,
      },
      {
        title:'一月',
        dataIndex:'feeList1',
        width:50,
         render:(text,record) => {
          return this.show(record,1)
         }
      },
      {
        title:'二月',
        dataIndex:'feeList2',
        width:50,
        render:(text,record) => {
          return this.show(record,2)
        }
      },
      {
        title:'三月',
        dataIndex:'feeList3',
        width:50,
        render:(text,record) => {
          return this.show(record,3)
        }
      },
      {
        title:'四月',
        dataIndex:'feeList4',
        width:50,
        render:(text,record) => {
          return this.show(record,4)
        }
      },
      {
        title:'五月',
        dataIndex:'feeList5',
        width:50,
        render:(text,record) => {
          return this.show(record,5)
        }
      },
      {
        title:'六月',
        dataIndex:'feeList6',
        width:50,
        render:(text,record) => {
          return this.show(record,6)
        }
      },
      {
        title:'七月',
        dataIndex:'feeList7',
        width:50,
        render:(text,record) => {
          return this.show(record,7)
        }
      },
      {
        title:'八月',
        dataIndex:'feeList8',
        width:50,
        render:(text,record) => {
          return this.show(record,8)
        }
      },
      {
        title:'九月',
        dataIndex:'feeList9',
        width:50,
        render:(text,record) => {
          return this.show(record,9)
        }
      },
      {
        title:'十月',
        dataIndex:'feeList10',
        width:50,
        render:(text,record) => {
          return this.show(record,10)
        }
      },
      {
        title:'十一月',
        dataIndex:'feeList11',
        width:50,
        render:(text,record) => {
          return this.show(record,11)
        }
      },
      {
        title:'十二月',
        dataIndex:'feeList12',
        width:50,
        render:(text,record) => {
          return this.show(record,12)
        }
      },
    ];
    const columns1=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'所属组织',
        dataIndex:'orgShortName',
        width:80,
      },
      {
        title:'一月',
        dataIndex:'feeVOList1',
        width:50,
         render:(text,record,index) => {
          return this.showCount(record,index)
         }
      },
      {
        title:'二月',
        dataIndex:'feeVOList2',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'三月',
        dataIndex:'feeVOList3',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'四月',
        dataIndex:'feeVOList4',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'五月',
        dataIndex:'feeVOList5',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'六月',
        dataIndex:'feeVOList6',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'七月',
        dataIndex:'feeVOList7',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'八月',
        dataIndex:'feeVOList8',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'九月',
        dataIndex:'feeVOList9',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'十月',
        dataIndex:'feeVOList10',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'十一月',
        dataIndex:'feeVOList11',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
      {
        title:'十二月',
        dataIndex:'feeVOList12',
        width:50,
        render:(text,record,index) => {
          return this.showCount(record,index)
        }
      },
    ];

    return(
      <div className={styles.container}>
        <Tabs defaultActiveKey={tab} onChange={this.changeInfo}>
          <TabPane tab="基本信息" key="1"/>
          <TabPane tab="统计信息" key="2"/>
        </Tabs>
        <NowOrg  extra={
          <ListFilter>
            <Search
              placeholder="请输入检索关键词"
              onSearch={value => this.isSearch(value)}
              style={{ width: 200 }}
              className={styles.filter}
            />
            <Select
              showSearch
              style={{ width: 200 ,marginLeft:20}}
              placeholder="Select a person"
              defaultValue="2019"
              onChange={ this.onChangeYear}
            >
              {
                yearArr.map((item,index)=>{
                  return <Option key={index} value={`${item}`}>{item}</Option>
                })
              }

            </Select>
          </ListFilter>
        }/>
        <Standard wrappedComponentRef={(e)=>this['Standard']=e} data={dataInfo} onChange={this.changeList}/>
        <LastDate wrappedComponentRef={(e)=>this['LastDate']=e} data={dataInfo} onChange={this.changeList}/>
        {
          tab=='1'?
            <ListTable
              rowKey={record=>record.code}
              columns={columns}
              data={list}
              scroll={{y:filterHeight}}
              pagination={{pageSize,total:totalRow,page,current:pageNumber}}
              onPageChange={this.changePage}/>
              :
            <ListTable
              rowKey={record=>record.code}
              columns={columns1}
              data={list1}
              scroll={{y:filterHeight}}
              pagination={pagination1}
              onPageChange={this.changePage}/>
        }


      </div>
    );
  }
}
