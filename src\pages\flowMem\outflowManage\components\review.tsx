// 业务操作-民主评议情况
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input, Select } from 'antd';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import { inManageOperate } from '../../service/index';

const FormItem = Form.Item;

class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      memInfo: {},
      visible: false,
      timeKey: moment().valueOf(),
      confirmLoading: false,
    };
  }
  handleOk = () => {
    const { onOk } = this.props;
    const { code } = this.state.memInfo;
    const { modalType = '' } = this.state;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        let url: any = undefined;
        if (modalType === 'inFlow') {
          url = inManageOperate;
        }
        if (url) {
          this.setState(
            {
              confirmLoading: true,
            },
            async () => {
              const res = await url({ data: { code, ...val } });
              this.setState({
                confirmLoading: false,
              });
              if (res.code === 0) {
                this.handleCancel();
                Tip.success('操作提示', '操作成功');
                onOk && onOk();
              }
            },
          );
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  open = (type: string, record) => {
    this.setState({ visible: true, memInfo: record, modalType: type, timeKey: moment().valueOf() });
  };
  destroy = () => {
    this.setState({
      memInfo: {},
      modalType: '',
    });
  };
  render() {
    const { form } = this.props;
    const { getFieldDecorator } = form;
    const { visible, confirmLoading, memInfo } = this.state;
    return (
      <Modal
        destroyOnClose
        title="参与民主评议情况"
        visible={visible}
        onOk={this.handleOk}
        maskClosable={false}
        onCancel={this.handleCancel}
        width={400}
        confirmLoading={confirmLoading}
      >
        {visible && (
          <Fragment key={this.state.timeKey}>
            <Form labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
              <FormItem label="请选择参与民主评议情况" colon={false}>
                {getFieldDecorator('mzAppraisal', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['mzAppraisal'],
                  rules: [{ required: true, message: '请选择参与民主评议情况' }],
                })(
                  <Select>
                    <Select.Option value={'0'}>未参加流入党支部民主评议</Select.Option>
                    <Select.Option value={'1'}>参加流入党支部民主评议</Select.Option>
                  </Select>,
                )}
              </FormItem>
            </Form>
          </Fragment>
        )}
      </Modal>
    );
  }
}
export default Form.create()(index);
