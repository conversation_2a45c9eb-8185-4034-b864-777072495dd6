import React, { useRef, useEffect, useState, Fragment } from 'react';
import {Tree,Tabs} from 'antd';
import { isEmpty } from '@/utils/method';
import { connect } from 'dva';
import _get from 'lodash/get';
import Page1 from './components/page1';
import Page2 from './components/page2';
const { TabPane } = Tabs;
const TreeNode=Tree.TreeNode;
function index(props,ref) {
  const { tmwTable:{ TreeList = [] , treeOrg = {} }={} } = props || {};
  const [ expandedKeys, setExpandedKeys ] = useState([]);
  const [ selectedKeys, setSelectedKeys ] = useState([]);
  const [ tabKey, setTabKey ] = useState('1');
  const pageRef:any = useRef(null);
  const pageRef2 = useRef(null);
  //树展开，选择
  const onTreeExpand =(expandedKeys, e)=>{
    const {node:{props:{ dataRef = {} }={}}={}, expanded = false } = e || {};
    const {id = ''} = dataRef;
    const {listTree, memType = '1'} = props.tmwTable;
    if(!isEmpty(listTree)){
      let find = listTree.find(it=>it['id'] === id) || {};
      if( isEmpty(_get(find,'children',[])) && expanded ){
        props.dispatch({
          type:'tmwTable/getAnnualstatsTree',
          payload:{
            id:id
          }
        });
      }
      setExpandedKeys(expandedKeys);
    }
  };
  const onTreeSelect=(levelCode,e)=>{
    const {node:{props:{ dataRef = {} }={}}={}} = e || {};
    const val = {...dataRef};
    delete val['children'];
    props.dispatch({
      type:'tmwTable/updateState',
      payload:{
        treeOrg:val,
      }
    });
    setSelectedKeys(levelCode);
    if(!isEmpty(_get(pageRef,'current',{}))){
      pageRef.current.reset();
    }
  };
  const renderTreeNodes = (data,rootCode) => {//渲染树节点
    if(!isEmpty(data)){
      return data.map(item=>{
        if(item['hasSub'] === '1'){
          return (
            // @ts-ignore
            <TreeNode title={item['shortName']} key={item['levelCode']} isLeaf={item['hasSub'] !== '1'}  dataRef={item}>
              {
                renderTreeNodes(item['children'],item['levelCode'])
              }
            </TreeNode>
          );
        }else {
          // @ts-ignore
          return <TreeNode title={item['shortName']} key={item['levelCode']} isLeaf={item['hasSub'] !== '1'} dataRef={item} />
        }
      })
    }
  };
  const onTabChange=(val)=>{
    setTabKey(val);
  };
  return (
    <div style={{height:'100%'}}>
      <div style={{display:'inline-block',width:'15%',verticalAlign:'top',overflow:'scroll',height:'100%'}}>
        <Tree
          onExpand={onTreeExpand}
          onSelect={onTreeSelect}
          expandedKeys={expandedKeys}
          selectedKeys={selectedKeys}
        >
          { renderTreeNodes(TreeList,'-1') }
        </Tree>
      </div>
      <div style={{display:'inline-block',width:'85%',height:'100%',padding:10}}>
        <Tabs onChange={onTabChange} activeKey={tabKey} animated={false}>
          <TabPane tab="行列配置" key="1">
            { tabKey === '1' && <Page1 {...props} ref={pageRef}/> }
          </TabPane>
          <TabPane tab="特殊配置（一个空）" key="2">
            { tabKey === '2' && <Page2 {...props} ref={pageRef2}/> }
          </TabPane>
        </Tabs>

      </div>
    </div>
  )
}
export default connect(({tmwTable}:any)=>({tmwTable}))(index)
