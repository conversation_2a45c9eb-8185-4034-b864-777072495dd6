/**
 * 模块名
 */
import React from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Table, Button, Switch } from "antd";
import UnitSelect from '@/components/UnitSelect';
import _cloneDeep from 'lodash/cloneDeep';
import _isEmpty from 'lodash/isEmpty';
import { getSession } from '@/utils/session';


export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      initial:1,
      timeKey:+new Date(),
      data:[
        {
          id:'1',
          isUnitMain:1,
        }
      ]
    }
  }
  propsChange=(data)=>{
    this.setState({
      data,
      initial:0,
    });
    let resData=[...data];
    // resData=resData.filter(obj=>obj['unit']);
    const {onChange}=this.props;
    onChange && onChange(resData)
  };
  add=()=>{
    let {data=[]}=this.state;
    let obj={id:`${data.length+1}`,unit:'',isUnitMain:data.length==0 ? 1 : 0,};
    data.push(obj);
    this.setState({
      timeKey:+new Date(),
      data,
      initial:0,
    });
  };
  del=(obj)=>{
    let {data}=this.state;
    data=data.filter(ob=>ob['id']!==obj['id']);
    this.propsChange(data);
    this.setState({
      timeKey:+new Date()
    })
  };
  onChange=(val,obj)=>{
    if(!_isEmpty(val)){
      let {data}=this.state;
      const index=data.findIndex(ob=>ob['id']===obj['id']);
      if(index>-1){
        data[index]={...data[index],unit:val[0]}
      }
      this.propsChange(data);
    }
  };
  switchChange=(e,obj)=>{
    if(obj.unit){
      let {data=[]}=this.state;
      const index=data.findIndex(ob=>ob['id']===obj['id']);
      if(index>-1){
        data[index]={...data[index],isUnitMain:e?1:0}
      }
      // 联合支部关联了多个单位时，调整主单位，原主单位的按钮需要自动弹回
      if(e){
        data.map((item)=>{
          if(item?.id !== obj?.id){
            item.isUnitMain = 0
          }
        })
      }
      this.propsChange(data);
    }
  };
  static getDerivedStateFromProps = (props:any, state:any) => {
    const {data}=props;
    const {initial}=state;
    if(data && initial){
      return {data}
    }
    return null;
  };
  render(){
    const {data}=this.state;
    const {disabled,isCreateOrg,disabledColFunc, org}=this.props;
    const columns=[
      {
        title:'序号',
        dataIndex:'id',
        align:'center',
        width:58,
        render:(text,record,index)=>{
          return index+1;
        }
      },
      {
        title:'选择单位',
        dataIndex:'num2',
        align:'center',
        width:258,
        render:(text,record,index)=>{
          return(
            <UnitSelect disabled={disabled}
                        disabledColFunc={disabledColFunc}
                        key={record['id']}
                        isCreateOrg={isCreateOrg}
                        org={ org || getSession('org') || {}}
                        initValue={record['unitName'] ? record['unitName'] : record['unit'] ? record['unit']['name'] : undefined}
                        onChange={(val)=>this.onChange(val,record)}/>
          )
        }
      },
      {
        title:'是否主单位',
        dataIndex:'isUnitMain',
        width:68,
        align:'center',
        render:(text,record,index)=>{
          return(
            <Switch disabled={disabled} defaultChecked={record['isUnitMain']===1} onChange={(e)=>this.switchChange(e,record)}/>
          )
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        align:'center',
        width:58,
        render:(text,record,index)=>{
          if(record.self){
            return(
              <a style={disabled ? {color: 'rgba(0, 0, 0, 0.25)', cursor: 'not-allowed', pointerEvents: 'none',} : undefined} className={'del'} onClick={()=>this.del(record)}>删除</a>
            )
          }
        }
      },
    ];
    return (
      <Table
        key={this.state.timeKey}
        bordered={true}
        rowKey={'id'}
        columns={columns as any}
        dataSource={data}
        pagination={false}
        footer={disabled ? undefined :()=>{
          return (
            <div style={{textAlign:'center'}}>
              <Button type="primary" onClick={this.add} style={{ width: '30%' }} size={'small'}>
                <PlusOutlined />点击添加
              </Button>
            </div>
          );
        }}
        size={'middle'}
      />
    );
  }
}
