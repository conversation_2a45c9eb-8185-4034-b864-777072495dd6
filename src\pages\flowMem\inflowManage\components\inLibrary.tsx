// 流动党员-流入管理-县级流入库
import React, { Fragment } from 'react';
import { connect } from 'dva';
import { Button, Divider, Input } from 'antd';
import moment from 'moment';
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import { isEmpty, setListHeight, changeMsgTip } from '@/utils/method';
import NowOrg from 'src/components/NowOrg';
import ExportInfo from '@/components/Export';
import { getSession } from '@/utils/session';
import FlowAddOrEdit from './flowAddOrEdit';
import BackTo from './backTo';
import FlowDetail from './flowDetail';
import Receive from './receive';
import { inManageList } from '@/pages/flowMem/service';

const Search = Input.Search;

@connect(({ unit, commonDict, loading, flowMem }) => ({
  flowMem,
  unit,
  commonDict,
  loading: loading.effects['unit/getList'],
}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {}, //筛选器
      memName: undefined, //搜索框
      view: false,
      subordinate: getSession('subordinate'),
      exportLoading: false, //导出loading
      searchLoading: false, //查询loading
      loading: false, // 数据加载状态
      flowJurisdiction: getSession('flowJurisdiction'), //这个是来判断退回按钮是否显示的  权限 为true就显示
      list: [], // 列表数据
      pagination: {
        // 分页数据
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
    };
  }

  exportInfo = async () => {
    this.setState({
      exportLoading: true,
    });
    await this['exportRef'].submitNoModal();
    this.setState({
      exportLoading: false,
    });
  };
  filterChange = (val) => {
    this.setState(
      {
        filter: val,
      },
      () => this.getList(),
    );
  };
  handleSearch = (e) => {
    this.setState(
      {
        memName: e,
        searchLoading: true,
      },
      () => {
        this.getList({ memName: e });
      },
    );
  };
  searchChange(e) {
    this.setState({
      memName: e.currentTarget.value || undefined,
    });
  }
  getList = async (params?: object) => {
    const { filter, memName } = this.state;
    const org = getSession('org') || {};
    this.setState({ loading: true }); // 开始加载
    try {
      const { data = {}, code } = await inManageList({
        data: {
          type: 3, // 根据当前选中tab菜单 1 未纳入管理，2 已纳入管理，3 县级流入库，4 流入历史
          pageNum: 1,
          pageSize: 10,
          orgCode: org['orgCode'],
          orgId: org['code'],
          memName,
          ...filter,
          ...params,
        },
      });
      if (code === 0) {
        const { list = [], ...pagination } = data;
        this.setState({
          list: list,
          pagination: {
            ...pagination,
            current: data.pageNumber,
            pageSize: data.pageSize,
            total: data.totalRow,
          },
        });
      }
    } catch (error) {
      console.error('获取列表失败:', error);
    } finally {
      this.setState({
        loading: false,
        searchLoading: false,
      });
    }
  };
  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org = getSession('org') || {};
    const subordinate = getSession('subordinate') || '0';
    if ((!isEmpty(this.state['orgCode']) && this.state['orgCode'] !== org['orgCode']) || subordinate !== this.state.subordinate) {
      this.setState(
        {
          orgCode: org['orgCode'],
          subordinate,
        },
        () => {
          this.getList({ orgCode: org['orgCode'] });
        },
      );
    }
  }
  componentDidMount() {
    const org = getSession('org') || {};
    setListHeight(this);
    this.setState({ orgCode: org['orgCode'] });
    this.getList();
  }

  render() {
    const { filterHeight, filter, memName, subordinate, searchLoading, loading, list, pagination } = this.state;
    const org = getSession('org') || {};
    const { loading: dvaLoading, commonDict } = this.props;

    const filterData = [
      {
        key: 'flowType',
        name: '流动类型',
        value: commonDict[`dict_d34`],
      },
      {
        // 时间选择器的时候没用到该key
        key: 'timeSelect',
        name: '外出时间',
        value: '',
        // type 1是树选择器 2是时间选择器
        type: 2,
        textArr: ["startOutTime", 'endOutTime']
      },
      {
        key: 'timeSelect',
        name: '登记日期',
        value: '',
        // type 1是树选择器 2是时间选择器
        type: 2,
        textArr: ["startRegisterTime", 'endRegisterTime']
      },
      {
        key: 'timeSelect',
        name: '创建日期',
        value: '',
        // type 1是树选择器 2是时间选择器
        type: 2,
        textArr: ["startCreateTime", 'endCreateTime']
      },
    ];
    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 40,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'memName',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return (
            <a
              onClick={() => {
                this['flowAddOrEditRef'].open('readOnly-inTab3', record, 'inFlow');
              }}
            >
              {text}
            </a>
          );
        },
      },
      // {
      //   title: '性别',
      //   dataIndex: 'memSexName',
      //   align: 'center',
      //   width: 50,
      // },
      {
        title: '联系电话',
        dataIndex: 'memPhone',
        align: 'center',
        width: 100,
      },
      {
        title: '流动类型',
        dataIndex: 'flowTypeName',
        align: 'left',
        width: 100,
      },
      {
        title: '流出地党支部',
        dataIndex: 'memOrgName',
        align: 'left',
        width: 150,
      },
      {
        title: '数据创建时间',
        dataIndex: 'createTime',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      // {
      //   title: '外出地点',
      //   dataIndex: 'outPlaceName',
      //   align: 'left',
      //   width: 100,
      // },
      {
        title: '外出日期',
        dataIndex: 'outTime',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '外出时长',
        dataIndex: 'outLength',
        align: 'center',
        width: 100,
        // render: (text, record, index) => {
        //   if (text) {
        //     return `${text}天`;
        //   }
        // },
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 100,
        render: (text, record) => {
          return (
            <Fragment>
              <a
                onClick={() => {
                  this['flowDetailRef'].open('readOnly-inTab3', record, 'inFlow');
                }}
              >
                查看
              </a>
              {/* <a
                onClick={() => {
                  this['flowAddOrEditRef'].open('readOnly-inTab3', record, 'inFlow');
                }}
              >
                查看
              </a> */}
              <Divider type="vertical" />
              <a
                onClick={() => {
                  this['receiveRef'].open(record);
                }}
              >
                接收
              </a>
              {this.state.flowJurisdiction && (
                <Fragment>
                  <Divider type="vertical" />
                  <a
                    onClick={() => {
                      this['backToRef'].open(record);
                    }}
                  >
                    退回
                  </a>
                </Fragment>
              )}
            </Fragment>
          );
        },
      },
    ];
    return (
      <Fragment>
        <NowOrg
          extra={
            <Fragment>
              <Button style={{ marginLeft: 16 }} onClick={this.exportInfo} loading={this.state.exportLoading}>
                导出
              </Button>
              <Search
                loading={searchLoading}
                allowClear
                placeholder="请输入姓名"
                enterButton={'查询'}
                style={{ width: 200, marginLeft: 16 }}
                onSearch={(e) => {
                  this.handleSearch(e);
                }}
                onChange={(e) => {
                  this.searchChange(e);
                }}
              />
            </Fragment>
          }
        />
        <RuiFilter showLine={3} data={filterData} openCloseChange={() => setListHeight(this, 20)} onChange={this.filterChange} />
        <ListTable
          rowKey={'id'}
          scroll={{ y: filterHeight, x: 100 }}
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={(page, pageSize) => {
            this.getList({ pageNum: page, pageSize });
          }}
        />
        <ExportInfo
          wrappedComponentRef={(e) => (this['exportRef'] = e)}
          tableName={''}
          noModal={true}
          tableListQuery={{
            ...filter,
            pageNum: pagination.pageNumber || 1,
            pageSize: pagination.pageSize || 10,
            subordinate,
            memName,
            orgCode: org['orgCode'],
            orgId: org['code'],
            type: '3',
          }}
          action={'/api/mem/flow/exportXsl'}
        />
        <FlowAddOrEdit wrappedComponentRef={(e) => (this['flowAddOrEditRef'] = e)} onOk={this.getList} />
        <FlowDetail wrappedComponentRef={(e) => (this['flowDetailRef'] = e)} onOk={this.getList} />
        <Receive wrappedComponentRef={(e) => (this['receiveRef'] = e)} onOk={this.getList} />
        <BackTo wrappedComponentRef={(e) => (this['backToRef'] = e)} onOk={this.getList} />
      </Fragment>
    );
  }
}
