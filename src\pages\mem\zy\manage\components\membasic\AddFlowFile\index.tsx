import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Row,
  Col,
  Input,
  Radio,
  Switch,
  Button,
  Select,
  Modal,
  InputNumber,
  Upload,
  message,
  Space,
  Spin,
  Skeleton,
  Progress
} from 'antd';

import Tip from '@/components/Tip';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import {
  convertToWebpBeforeUpload,
  _history
} from '@/utils/method';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _iisArray from 'lodash/isArray';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import _isNumber from 'lodash/isNumber';
import _trim from 'lodash/trim';
import style from './index.less'
import Info from './info'
import FormList from './formList'
import { listMemDigital, preview, dauploadFileDigital, checkOath, shareLink } from '@/pages/developMem/services'
import MembersWorkProcedures from '@/pages/archivesAdministration/components/membersWorkProcedures'
import { LoadingOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import LazyImage from '../../../../../../../pages/developMem/zy/components/Edit/lazyImage';
import XX from '@/assets/mem/xx.png'
import ImageCropper from '@/components/ImageCropper'

let time: null = null;
class index extends React.Component<any, any> {
  imgContainerRefs: { [key: string]: HTMLDivElement | null };
  constructor(props) {
    super(props);
    this.state = {
      nowAge: undefined,
      hasLost: false,
      _basicInfo: {},
      modalVisible: false,
      d08Code: '',
      hasAppointment: false,
      isOutSystem_state: false,
      canEdit: false,
      fileObj: {
        formList: [],
        value: [],
        checkList: [],
      },
      checkList: [],
      step: 0,
      fileIndex: 0,
      uploadloading: false,
      isDragging: false,
      prevent: false,
      cropperVisible: false,
      fileListIndex: 0
    };
    this.imgContainerRefs = {};
  }

  // 保存
  submit = () => {
    const { memBasic: { basicInfo = {} } = {}, onsubmit, commonDict: { area = [] } = {} } = this.props;
    const { step, fileObj, rowCode, type, hasMemValue, rowData, baseformdata = {} } = this.state
    let subobj = {
      type: 'memDevelop/zysave',
      payload: {
        data: {},
      },
    }
    this.setState({
      subobj,
    }, () => {
      let flag = true
      fileObj?.value.map(i => {
        if (i.fileList.length < 1) {
          message.error(`${i.d222Name}未上传`);
          flag = false
        }
      })
      if (fileObj?.formList && fileObj?.formList.length > 0) {
        if (flag) {
          this['FormList'].submit();
        }
      } else {
        if (flag) this['Info'].showModal({ data: { ...basicInfo }, file: fileObj, rowCode, type })
      }
    })
  };
  confirmAgain = async (obj: any) => {
    const { node = {}, developStepLogDTO, memInfo = {} } = obj
    if (obj['files']) {
      delete obj['files']
    }
    const { subobj = {}, fileObj = {}, type, rowCode, developOrgCode, rowData, processNode } = this.state
    const { memDevelop: { basicInfo = {} } = {}, onsubmit } = this.props;

    let res: any = {}
    let fileList: any = []
    fileObj.value.map(i => {
      i.fileList.map(j => {
        let obj = {
          ...i,
          ...j
        }
        delete obj.fileList
        delete obj.files
        fileList.push(obj)
      })
    })
    console.log({
      code: rowData['code'],
      ...node,
      developStepLogDTO,
      filesList: fileList,
      processNode: rowData['processNode'],
      digitalLotNo: rowData['digitalLotNo'] || memInfo['digitalLotNo'],
    }, '保存参数')
    res = await this.props.dispatch({
      type: 'memDevelop/zydyuploadFileDigital',
      payload: {
        data: {
          code: rowData['code'],
          ...node,
          developStepLogDTO,
          filesList: fileList.map((i, k) => {
            return {
              ...i,
              sort: k + 1
            }
          }),
          processNode: rowData['processNode'],
          digitalLotNo: rowData['digitalLotNo'] || memInfo['digitalLotNo'],
        },
      },
    });

    const { code = 500, data = {} } = res;
    if (code === 0) {
      Tip.success('操作提示', basicInfo['code'] ? '修改成功' : '新增成功');
      this.cancel();
      onsubmit && onsubmit({ editType: this.state.editType });
    }
    this['Info'].closeModal();
  }
  backform = (vals) => {
    const { step, fileObj, rowCode, type, hasMemValue } = this.state
    const { location: { pathname = '' } = {} } = _history
    console.log(vals, 'obj')
    const { memBasic: { basicInfo = {} } = {}, onsubmit, commonDict: { area = [] } = {} } = this.props;
    this['Info'].showModal({ data: basicInfo, formData: vals, file: fileObj, rowCode })
  }
  cancel = () => {
    const { fileObj: { value = [] } = {} } = this.state;
    let list = value
    list.map((i, k) => {
      this.setState({
        [`fileList${k}`]: []
      })
    })
    this.setState({
      d154CodeNoDraw: [],
      step: 1,
      fileObj: {
        formList: [],
        value: [],
        checkList: [],
      },
      fileList: [],
      modalVisible: false,
      baseformdata: {}
    });
    this.destroy();
    this.props.onsubmit && this.props.onsubmit();
  };
  open = (e) => {
    let uparr = [
      {
        id: '13',
        type: ['RDXS'],
        name: '待入党宣誓',
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '入党宣誓材料',
            d222Code: '9901',
            fileList: []
          },


        ]
      },
      {
        id: '14',
        type: ['YBQ_1_1', 'YBQ_1_2'],
        name: '满足考察',
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '预备期第一次思想汇报',
            d222Code: '504',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否手写、内容是否合规',
                '2. 从确定为预备党员之日起每3个月一次',
                '3. 入党介绍人签署意见及签字',
              ]
            }
          },
          {
            key: 'file1',
            d222Name: '预备期第二次思想汇报',
            d222Code: '504',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否手写、内容是否合规',
                '2. 从确定为预备党员之日起每3个月一次',
                '3. 入党介绍人签署意见及签字',
              ]
            }
          },


        ]
      },
      {
        id: '18',
        type: ['YBQ_1_3'],
        name: '满足转正',
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '中国共产党入党志愿书',
            d222Code: '404',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 封面盖章（党支部）',
                // '2. 个人基本情况，从确定为积极分子时间起算',
                '2. 确定入党申请时间和积极分子时间，应与前期资料一致',
                '3. 涉及时事等内容是否符合实际',
              ]
            }
          },
          {
            key: 'file1',
            d222Name: '转正申请书',
            d222Code: '503',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 是否手写、内容是否合规',
                '2. 是否每三个月一次、培养联系人是否签字',
                '3. 落款时间是否合规（一般在预备期满前一个星期内）',
              ]
            }
          },
          {
            key: 'file2',
            d222Name: '预备党员培养教育考察登记表',
            d222Code: '501',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1. 入党宣誓时间（在党委审批同意后3个月内）',
                '2. 封面盖章（党支部）',
                '3. 个人基本情况，从确定为预备党员时间起算',
                '4. 确定入党申请时间和积极分子时间，应与前期资料一致',
                '5. 内容中涉及时事是否符合实际',
              ]
            }
          },


        ]
      },
      {
        id: '15',
        type: ['YBQ_2_1', 'YBQ_2_2', 'YBQ_2_3'],
        name: '支部大会讨论转正',
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '预备党员转正征求党内外群众意见谈话记录',
            d222Code: '505',
            fileList: []
          },
          {
            key: 'file1',
            d222Name: '转为正式党员的支部大会会议记录（复印件）',
            d222Code: '506',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1、应到人数、实到人数（具有表决权的一半以上）是否符合规定',
                '2、会议主持、参会人员情况是否合规',
                '3、参会人员与发言人员一致。'
              ]
            }
          },


        ]
      },
      {
        id: '16',
        type: ['YBQ_4_1', 'YBQ_4_2', 'YBQ_4_3'],
        name: '基层党委审批',
        formList: [
          {
            label: '转正人员',
            required: true,
            type: 'text',
            key: 'hasStaffOrganization'
          },
          {
            label: '预备期满时间',
            required: true,
            type: 'text',
            key: 'hasStaffOrganization'
          },
          {
            label: '支部大会讨论通过时间',
            required: true,
            type: 'time',
            key: 'hasStaffOrganization'
          },
          {
            label: '转正类型',
            required: true,
            type: 'rd',
            key: 'hasStaffOrganization'
          },
        ],
        value: [
          {
            key: 'file',
            d222Name: '转为中共正式党员审批请示（复印件）',
            d222Code: '507',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                '1、请示内容涉及信息是否与其他资料信息佐证',
              ]
            }
          },
          {
            key: 'file1',
            d222Name: '基层党委审批会议记录、会议纪要和批复（复印件）',
            d222Code: '508',
            fileList: [],
            tip: {
              title: '档案注意事项',
              type: 'warning',
              show: true,
              info: [
                ' 1、应到人数、实到人数（是应到人数的三分之二以上）是否符合规定',
                '2、会议主持、参会人员情况是否合规',
                '3、参会人员与发言人员一致。'
              ]
            }
          },


        ]
      },
      {
        id: '17',
        type: ['YBQ_3'],
        name: '转正前公示',
        formList: [
        ],
        value: [
          {
            key: 'file',
            d222Name: '转正公示材料',
            d222Code: '502',
            fileList: []
          },
        ]
      },
    ]
    const { canEdit, editType = '', type, id, code, developOrgCode, processNode, lastCode = '', isAdd = false } = e;
    const { fileObj } = this.state
    console.log(id, fileObj, type, processNode, 'iiiiiiiiiiiiiiiiii')
    let arr: any = []
    if (id) {
      arr = uparr.find(i => i.id == id)
    } else {
      arr = uparr.find(i => i.type.includes(type || processNode))
    }

    console.log(arr, '打开弹窗显示arr')
    if (id == '17' && arr.name == '转正前公示') {
      this.getcheckOath(code)
    }

    this.setState({
      modalVisible: true,
      canEdit,
      editType,
      type,
      fileObj: arr,
      rowCode: code,
      developOrgCode,
      rowData: e,
    });
  };
  getcheckOath = async (memCode: any) => {
    const { code = 500, data = {} } = await checkOath({ memCode })
    if (code != 0) {
      this.setState({
        isup: false
      })
    }
  }
  destroy = () => {
    this.setState({
      hasLost: false,
      _basicInfo: {},
      d08Code: '5',
      hasAppointment: false,
      isOutSystem_state: false,
    });
    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        basicInfo: {},
      },
    });
  };


  fileChange = ({ fileList, file, event, }: any, item: any, index: number) => {
    const { prevent, fileObj } = this.state;
    if (prevent) {
      this.setState({
        [`fileList${index}`]: []
      })
      return false
    };
    if (file.status === 'done') {
      const { response: { code = 500, message = '' } = {} } = file || {};
      if (code !== 0) {
        Tip.error('操作提示', message);
        fileList.pop();
      } else {
        this.getfu(fileList, item, index)
      }
    } else if (file.status === 'error') {
      Tip.error('操作提示', '上传失败');
      this.setState({
        fileloading: false
      })
    }
    let _value: any = [];
    fileObj.value.forEach((item, is) => {
      if (is == index) {
        // if (item['tip1']) {
        //   item['tip1']['show'] = false
        // }
        if (item['tip']) {
          item['tip']['show'] = false
        }
      }
      _value.push(item)
    })
    this.setState({
      [`fileList${index}`]: fileList.map(i => {
        i['percent'] = (i['percent'] * 1).toFixed(0)
        return i
      }),
      fileObj: {
        ...fileObj,
        value: _value
      }
    })
  }
  getfu = (fileList, item, index) => {
    const { fileObj } = this.state
    let done = false;
    let anum = 0
    if (time) clearInterval(time)
    time = setInterval(() => {
      fileList.forEach((file) => {
        console.log(file, 'filefile')
        const { response: { data = [], code = 500 } = {} } = file
        if (code !== 0) {
          done = false
          anum++
          return
        }
      })
      if (anum == 0) {
        let arr: any = []
        fileList.map((i, index) => {
          const { response: { data = [] } = {} } = i
          data.map(j => {
            let obj = {
              path: j.url,
              sort: index + 1,
              ...j
            }
            arr.push(obj)
          })
        })
        let find = fileObj.value.map(i => {
          if (i.key == item.key) {
            i.fileList = [...i.fileList, ...arr].map((i, k) => {
              return {
                ...i,
                sort: k + 1
              }
            })
          }
          return i
        })
        this.setState({
          fileObj: {
            ...fileObj,
            value: find
          },
          renderfile: false,
          [`fileList${index}`]: [],
          uploadloading: false
        });
        clearInterval(time)
        Tip.success('操作提示', '上传成功');
      }
    }, 3000)

  }
  uploadFile = (file, item) => {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', file.originFileObj);

      const xhr = new XMLHttpRequest();
      xhr.open('POST', `/api/minio/upload?model=dygl`);
      xhr.setRequestHeader('Authorization', sessionStorage.getItem('token') || '');
      xhr.setRequestHeader('dataApi', sessionStorage.getItem('dataApi') || '');

      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          file.percent = Math.round((event.loaded / event.total) * 100);
          // console.log(file.percent, '进度')
          // setFilepercent(file.percent); // 更新总体进度
          this.setState({
            fileList: [...this.state.fileList]
          })
        }
      };

      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            file.response = response;

            // 如果上传成功且有数据，添加到显示列表
            if (response && response.code === 0 && response.data && response.data.length > 0) {
              // if(type== 'application/pdf') {
              //     setAllList(prev => [...prev, ...response.data]);
              // }
              const fileData = response.data.map(i => {
                return {
                  path: i.url,
                  url: i.url,
                  name: i.name,
                  // pathurl: fileData.url,
                  id: String(Math.floor(Math.random() * 1000)),
                }
              });

              // 更新allList，添加新上传的文件
              // setAllList(prev => [...prev, ...fileData]);
              const { fileObj } = this.state;
              let find = fileObj.value.map(i => {
                if (i.key == item.key) {
                  i.fileList = [...i.fileList, ...fileData]
                }
                return i
              })
              this.setState({
                fileObj: {
                  ...fileObj,
                  value: find
                },
              });
            }

            resolve(response);
          } catch (e) {
            reject(e);
          }
        } else {
          reject(new Error('上传失败'));
        }
      };

      xhr.onerror = () => reject(new Error('网络错误'));
      xhr.send(formData);
    });
  };
  getbase64 = (item) => {
    let arr: any = []
    let _list: any = []
    let _list1: any = []
    const { fileObj = {} } = this.state;
    fileObj.value.map((f) => {
      if (f.key == item.key) {
        // i.fileList = [...i.fileList, ...fileData]
        _list = [...f.fileList].filter(i => !i.previewPath)
        _list1 = [...f.fileList].filter(i => i.previewPath)
      }
    })
    const promise = _list.map(async (i) => {
      arr.push(i)
      const result = await shareLink({ path: i?.url })
      return result
    })
    Promise.all(promise).then(res => {
      arr.map((j, k) => {
        j['previewPath'] = res[k]?.data
        j['path'] = j['url']
        return j
      })

      let find = fileObj.value.map(i => {
        if (i.key == item.key) {
          i.fileList = [..._list1, ...arr]
        }
        return i
      })
      this.setState({
        fileObj: {
          ...fileObj,
          value: find
        },
      });
    })
  }
  beforeUpload = (file, fileList, index) => {
    message.destroy();
    let imgs: any = []
    let pdfs: any = []
    fileList.forEach(i => {
      if (i.type.startsWith('image')) {
        imgs.push(i)
      }
      if (i.type.startsWith('application/pdf')) {
        pdfs.push(i)
      }
    })
    if (imgs.length > 0 && pdfs.length > 0) {
      message.error('请勿同时上传pdf和图片')
      this.setState({
        prevent: true,
      })
      return false
    }
    if (imgs.length > 10) {
      message.error('一次性最多上传10个文件')
      this.setState({
        prevent: true,
      })
      return false
    }
    if (pdfs.length > 1) {
      message.error('一次性最多上传1个pdf文件')
      this.setState({
        prevent: true,
      })
      return false
    }
    let fileSize: number = file['size'] / 1024 / 1024;
    if (fileSize >= 50) {
      message.error('请上传小于50M的文件!');
      this.setState({
        prevent: true,
      })
      return false
    }
    this.setState({
      uploadloading: true,
      prevent: false
    })
    return convertToWebpBeforeUpload(file)

  }
  delFile = () => {
    console.log('delFile')
  }


  check = (obj, index) => {
    console.log(obj)
    const { checkList = [] } = this.state;
    let arr: any = []
    if (obj?.id) {
      if (checkList.includes(obj?.id)) {
        arr = checkList.filter(i => i !== obj?.id)
      } else {
        arr = [...checkList, obj?.id]
      }
      this.setState({
        checkList: [...new Set([...arr])],
        fileIndex: index
      })
    }
  }
  del = (index) => {
    const { fileObj, checkList, fileIndex } = this.state;
    let _fileList = this.state[`fileList${index}`]
    let o = fileObj.value[index].fileList.filter(j => !checkList.includes(j.id))
    let f = _fileList.filter(i => !checkList.includes(i.response.data[0].id))
    fileObj.value[index].fileList = o
    this.setState({
      fileObj,
      checkList: [],
      [`fileList${index}`]: f
    })
  }
  isdisabled = (item) => {
    const { fileObj, checkList, fileIndex } = this.state;
    let f = item.fileList.filter(i => checkList.includes(i.id))
    return f.length < 1
  }
  toswitch = (key) => {
    const { fileObj: { value = [] } = {}, previewImage = '' } = this.state
    let arr: any = []
    value.map(i => {
      let f = i.fileList.find(j => j.previewPath == previewImage)
      if (f) {
        arr = i.fileList
      }
    })
    let findIndex = arr.findIndex(i => i.previewPath == previewImage)
    if (key == 'up') {
      if (findIndex == 0) {
        message.error('已经是第一个了')

      } else {
        this.setState({
          previewImage: arr[findIndex - 1]?.previewPath
        })
      }
    } else {
      if (findIndex == arr.length - 1) {
        message.error('已经是最后一个了')
      } else {
        this.setState({
          previewImage: arr[findIndex + 1]?.previewPath
        })
      }
    }
  }
  // dragstarts = (e, item) => {
  //   this.setState({
  //     sorto: item
  //   })
  // }
  dragstarts = (e, item) => {
    // 设置拖动的数据
    e.dataTransfer.setData('text/plain', JSON.stringify(item));
    this.setState({
      sorto: item,
      isDragging: true
    })
  }
  drops = (e, item) => {
    const { sorto, fileObj: { value = [] } = {} } = this.state;
    let arr = []
    value.map((i, index) => {
      if (index == sorto['old']) {
        let newrr = i.fileList.filter(i => i.id != sorto.id)
        newrr.splice(item.sort - 1, 0, sorto)
        if (newrr) {
          newrr.map((j, index) => {
            j['sort'] = index + 1
            return j
          })
        }
        i['fileList'] = newrr
      }
      arr.push(i)
      // return i
    })
    this.setState({
      fileObj: {
        ...this.state.fileObj,
        value: arr,
        isDragging: false
      }
    })
    console.log(arr, '排序')
    // let newrr = arr.filter(i => i.id != sorto.id)
    // newrr.splice(item.sort - 1, 0, sorto)
    // // let _allList = arr.filter((i, index) => index !== sorto.old)
    // let newrr1 = newrr.map((item, index) => {
    //   item['sort'] = index + 1
    //   return item
    // })
  }

  setTip = (index, k) => {
    const { fileObj } = this.state;
    let _value: any = [];
    fileObj.value.forEach((item, is) => {
      if (is == index) {
        item['tip']['show'] = !item['tip']['show']
      }
      _value.push(item)
    })
    this.setState({
      fileObj: {
        ...fileObj,
        value: _value
      }
    })
  }
  closeTip = (index, k) => {
    const { fileObj } = this.state;
    let _value: any = [];
    fileObj.value.forEach((item, is) => {
      item['tip']['show'] = false
      _value.push(item)
    })
    this.setState({
      fileObj: {
        ...fileObj,
        value: _value
      }
    })
  }
  handOk = async () => {
    const { prevent, fileObj, fileListIndex, previewImage } = this.state;
    let file = this['ImageCropperref'].geturl()
    this['ImageCropperref'].clears()
    this.setState({
      cropperVisible: false
    })
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch(`/api/minio/upload?model=dygl`, {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: sessionStorage.getItem('token') || '',
          dataApi: sessionStorage.getItem('dataApi') || '',
        }
      });

      if (response.ok) {
        const { code = 500, data = [] } = await response.json();

        let _fileList = fileObj.value[fileListIndex].fileList.map(i => {
          if (i.previewPath == previewImage) {
            i.path = data[0].url
            i.previewPath = data[0].previewPath
          }
          return i
        })
        fileObj.value[fileListIndex].fileList = _fileList
        console.log(fileObj.value, _fileList, '1212121212121')
        this.setState({
          fileObj: {
            ...fileObj,
          }
        })

        console.log('Success:', data);
      } else {
        console.error('Error:', response.statusText);
      }
    } catch (error) {
      console.error('Error during file upload:', error);
    }
  }
  // 添加新的处理滚动的方法
  handleDragOver = (e, containerRef) => {
    e.preventDefault();

    if (!this.state.isDragging || !containerRef) return;

    const container = containerRef;
    const containerRect = container.getBoundingClientRect();
    const mouseY = e.clientY;

    // 计算滚动速度 - 离边缘越近滚动越快
    const calculateSpeed = (distance, maxSpeed = 15) => {
      // 只在靠近边缘50px内触发滚动
      const threshold = 50;
      if (distance > threshold) return 0;

      // 距离边缘越近，速度越快
      return Math.ceil((threshold - distance) / 5);
    };

    // 鼠标在容器顶部区域，向上滚动
    if (mouseY < containerRect.top + 50) {
      const distance = mouseY - containerRect.top;
      const speed = calculateSpeed(distance);
      if (speed > 0) {
        container.scrollTop -= speed;
      }
    }

    // 鼠标在容器底部区域，向下滚动
    if (mouseY > containerRect.bottom - 50) {
      const distance = containerRect.bottom - mouseY;
      const speed = calculateSpeed(distance);
      if (speed > 0) {
        container.scrollTop += speed;
      }
    }
  }

  componentDidMount() {
    document.addEventListener('dragover', this.handleGlobalDragOver);
  }

  componentWillUnmount() {
    document.removeEventListener('dragover', this.handleGlobalDragOver);
  }

  handleGlobalDragOver = (e) => {
    if (!this.state.isDragging) return;

    // 为所有图片容器添加滚动逻辑
    Object.keys(this.imgContainerRefs).forEach(key => {
      const containerRef = this.imgContainerRefs[key];
      if (containerRef) {
        this.handleDragOver(e, containerRef);
      }
    });
  }
  render(): React.ReactNode {
    const {
      form,
      memDevelop: { basicInfo = {} } = {},
      loading: { effects = {} } = {},
      tipMsg = {},

    } = this.props;
    const { getFieldDecorator } = form;
    const {
      d08Code,
      hasAppointment,
      hasLost,
      area,
      modalVisible,
      isOutSystem_state,
      canEdit,
      d154CodeNoDraw = [],
      fileList = [],
      step = 1,
      fileObj,
      previewVisible = false,
      previewImage = '',
      type,
      rowData,
      checkList,
      isup = true,
      subobj,
      baseformdata = {},
      uploadloading,
      renderfile = false,
      cropperVisible
    } = this.state;
    const { location: { pathname = '' } = {} } = _history
    const upprops: any = {
      action: `/api/minio/upload?model=dygl`,
      // accept: '.jpg,.png,.jpeg,.webp,.pdf,.tiff',
      accept: '.jpg,.png,.jpeg,.webp,.pdf',
      headers: {
        Authorization: sessionStorage.getItem('token') || '',
        dataApi: sessionStorage.getItem('dataApi') || '',
      },
    };
    return (
      <Fragment>
        <Modal
          zIndex={1}
          title={
            <Space>
              <div>{fileObj['name']}</div>
              <div className={style.fzdy} onClick={() => {
                // this['MembersWorkProcedures'].open('发展党员规程')
                window.open('/archivesAdministration/membersWorkProcedures')
              }}>发展党员规程</div>
              <div className={style.flexboxTip} >温馨提示：拖动图片可以进行排序</div>
            </Space>
          }
          destroyOnClose
          visible={modalVisible}
          onCancel={this.cancel}
          // onOk={this.submit}
          width={type == 'JJ_7' ? '1600px' : '1400px'}
          footer={
            canEdit
              ? [
                <React.Fragment>
                </React.Fragment>,
                <Button onClick={this.cancel}>取消</Button>,
                <Button type="primary" onClick={this.submit} loading={uploadloading}>
                  保存
                </Button>
              ]
              : null
          }
        // confirmLoading={effects['memAbroad/save']}
        >
          <div style={{ height: '600px', overflow: 'auto' }}>
            <div className={style.flex}>

              {
                (fileObj?.formList && fileObj?.formList.length > 0) && <FormList ref={e => this['FormList'] = e} dataInfo={rowData} onOk={this.backform} />
              }

              <div className={style.flexbox}>
                {
                  fileObj?.value && fileObj?.value.map((item, index) => {
                    return (
                      <div className={style.flexbox_l} key={index}>
                        <div className={style.filename}>{item?.d222Name}</div>
                        {
                          item?.tip &&
                          <div className={style.tips}>
                            {/* <Space>
                              {
                                item?.tip1 && <div className={style.tips1} onClick={() => this.setTip(index, 1)}>
                                  <Space>
                                    <ExclamationCircleOutlined />
                                    注意
                                  </Space>
                                </div>
                              } */}
                            <div className={style.tips2} onClick={() => this.setTip(index, 2)}>
                              <Space>
                                <ExclamationCircleOutlined />
                                档案注意事项
                              </Space>
                            </div>

                            {/* </Space> */}
                          </div>
                        }
                        {
                          item?.tip?.show &&
                          <div className={style.tipsbox}>
                            {
                              item?.tip?.show &&
                              <div className={style.tipsbox2}>
                                {/* <div className={style.tipclose}><img src={XX} onClick={() => this.closeTip(index, 2)} /></div> */}
                                <div>{item?.tip?.title}:</div>
                                {
                                  item?.tip?.info.map((it, ik) => {
                                    return (
                                      <div key={ik}>{it}</div>
                                    )
                                  })
                                }
                                {
                                  item?.tip1 && <div><span style={{ fontWeight: 'bold' }}>注</span>：{item?.tip1?.info}</div>
                                }

                              </div>
                            }

                          </div>
                        }
                        <div
                          className={style.imgs}
                          ref={(el) => this.imgContainerRefs[`imgs_${index}`] = el}
                          onDragOver={(e) => this.handleDragOver(e, this.imgContainerRefs[`imgs_${index}`])}
                        >
                          <React.Fragment>
                            <React.Fragment>
                              {
                                item?.fileList && item?.fileList.map((file, i) => {
                                  return (
                                    <div
                                      draggable
                                      onDragStart={(e) => this.dragstarts(e, { ...file, old: index })}
                                      onDrop={(e) => this.drops(e, file)}
                                      onDragOver={(e) => e.preventDefault()}
                                      key={i}
                                      className={checkList.includes(file.id) ? `${style.previewImage} ${style.cImage}` : style.previewImage}
                                      onClick={() => this.check(file, index)}
                                    >
                                      {
                                        checkList.includes(file?.id) &&
                                        <div className={style.cicon}>
                                          <LegacyIcon type="check" />
                                        </div>
                                      }
                                      <LazyImage
                                        dataSrc={`${window.location.origin}/${file.previewPath}`}
                                        alt="file-preview"
                                        className={style.simg}
                                        onLoad={() => { }}
                                      />
                                      {/* <img src={`${window.location.origin}/${file.previewPath}`} /> */}
                                      <div className={style.cropper} onClick={(e) => {
                                        e.stopPropagation()
                                        this.setState({
                                          cropperVisible: true,
                                          previewImage: file.previewPath,
                                          fileListIndex: index
                                        })
                                      }}>
                                        <Button size="small" type='primary'>编辑</Button>
                                      </div>
                                      <div className={style.preview} onClick={(e) => {
                                        e.stopPropagation()
                                        this.setState({
                                          previewVisible: true,
                                          previewImage: file.previewPath
                                        })
                                      }}>
                                        <Button size="small" type='primary'>预览</Button>
                                        {/* <LegacyIcon type="eye" /> */}
                                        {/* <img src={require('@/assets/mem/yj.png')} /> */}
                                      </div>

                                    </div>
                                  )
                                })

                              }
                            </React.Fragment>
                            <React.Fragment>

                              {
                                this.state[`fileList${index}`] && this.state[`fileList${index}`].map((item: any) => {
                                  return (
                                    <div className={style.previewImage}>
                                      <Skeleton.Image style={{ width: '100%', height: '100%' }} />
                                      <Progress percent={item.percent} size="small" />
                                    </div>
                                  )
                                })
                              }
                            </React.Fragment>
                          </React.Fragment>

                        </div>

                        {
                          isup && <div className={style.upbtn}>
                            <Space>
                              <Spin spinning={uploadloading} indicator={<LoadingOutlined spin />} size="small">
                                <Upload
                                  style={{ marginBottom: 10 }}
                                  {...upprops}
                                  multiple
                                  onChange={(file) => this.fileChange(file, item, index)}
                                  beforeUpload={(file, fileList) => this.beforeUpload(file, fileList, index)}
                                  fileList={this.state[`fileList${index}`]}
                                  showUploadList={false}
                                >
                                  <Button>上传</Button>
                                </Upload>
                              </Spin>
                              {
                                item?.fileList && item?.fileList.length > 0 && <Button disabled={this.isdisabled(item)} type={'danger'} onClick={() => this.del(index)}>删除</Button>
                              }
                            </Space>
                          </div>
                        }
                      </div>
                    )
                  })
                }
              </div>

            </div>

          </div>
          <Info ref={e => this['Info'] = e} change={this.confirmAgain} />
        </Modal>
        <Modal
          width={'1000px'}
          visible={previewVisible}
          footer={null}
          zIndex={3}
          onCancel={() => {
            this.setState({
              previewImage: '',
              previewVisible: false
            })
          }}>
          <div style={{ width: '780px', height: '800px', position: 'relative', margin: 'auto' }}>
            <LegacyIcon className={style.changeicon} type="left" onClick={() => this.toswitch('up')} />
            <img style={{ width: '100%', height: '100%', userSelect: 'none' }} alt="example" src={`${window.location.origin}/${previewImage}`} />
            <LegacyIcon className={style.changeicon1} onClick={() => this.toswitch('below')} type="right" />
          </div>

        </Modal>
        {
          cropperVisible &&
          <Modal
            width={'1360px'}
            visible={cropperVisible}
            zIndex={3}
            onOk={this.handOk}
            onCancel={() => {
              this.setState({
                previewImage: '',
                cropperVisible: false
              })
              this['ImageCropperref'].clears()
            }}>
            <div style={{  height: '690px', position: 'relative', margin: 'auto' }}>
              <ImageCropper keys={Math.random()} src={previewImage} ref={(e) => this['ImageCropperref'] = e} />
            </div>

          </Modal>
        }

        <MembersWorkProcedures wrappedComponentRef={e => this['MembersWorkProcedures'] = e} />
      </Fragment>
    );
  }
}
export default Form.create()(index);
