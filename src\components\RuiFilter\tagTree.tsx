/**
 * 模块名
 */
import React from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu, Tag, Tree } from 'antd';
import { treeToList } from "@/utils/method";
import styles from './index.less';
const { CheckableTag } = Tag;
const TreeNode = Tree.TreeNode;
export default class index extends React.Component<any, any>{
  static destroy() { };
  constructor(props) {
    super(props);
    this.state = {
      selectedTags: ['clear'],
      checkedKeys: [],
      visible: false,
    };
    index.destroy = this.destroy;
  }
  destroy = () => {
    this.setState({
      selectedTags: ['clear'],
      checkedKeys: [],
      visible: false,
    }, () => {
    })
  };
  onVisibleChange = (key, visible) => {//下拉展开 tag不允许选中
    this.setState({
      visible,
    });
  };
  handleChange = (key, checked, item) => {
    const { onChange } = this.props;
    // selectedTags选中tag标签 checkedKeys 树选中节点 visible 展开的下拉树
    let { selectedTags, checkedKeys, visible } = this.state;
    selectedTags = selectedTags.filter(it => it !== 'clear');
    if (visible) {//下拉树展开
      if (!checkedKeys.includes(key)) {//
        selectedTags = selectedTags.filter(it => it !== key);
      } else {//树选中节点中包含tag的key tag选中 反之
        selectedTags.push(key);
        checkedKeys.push(key);
      }
    } else {
      if (checked) {//选中
        selectedTags.push(key);
        if (item.children && item.children.length > 0) {
          const { data } = this.props;
          const listData = treeToList(data);
          for (let obj of listData) {
            if (obj['key'].startsWith(key)) {
              checkedKeys.push(obj['key']);
            }
          }
        }
      } else {
        selectedTags = selectedTags.filter(it => it !== key);
        checkedKeys = checkedKeys.filter((it) => it !== key)
        if (item.children && item.children.length > 0) {
          let list = treeToList(item.children);
          if (list.length > 0) {
            let keys: Array<string> = [];
            for (let obj of list) {
              keys.push(obj['key'])
            }
            checkedKeys = checkedKeys.filter((it) => !keys.includes(it))
          }
        }
      }
    }
    if (selectedTags.length === 0 && checkedKeys.length === 0) {//什么都没有选中 默认选择全部
      onChange && onChange([]);
      selectedTags.push('clear')
    } else {
      onChange && onChange(Array.from(new Set([...selectedTags, ...checkedKeys])))
    }
    this.setState({
      selectedTags,
      checkedKeys,
    });
  };
  findParent = (code, lastData = [], key = 'key', parentKey = 'parent', result = []) => {
    let find = lastData.find(obj => obj[key] === code);
    if (find) {
      this.findParent(find[parentKey], lastData, key, parentKey, result);
      result.push(find);
    }
    return result;
  };
  onCheck = (newKeys, e, data) => {//勾选树节点
    const { checked, node } = e;
    let { dataRef, children = [] } = node;
    let { checkedKeys } = this.state;
    // console.log(newKeys, e, data, 'newKeys, e, data')
    checkedKeys = Array.from(new Set([...checkedKeys, ...newKeys]));
    if (!checked) {
      let lastData = treeToList(data);
      let datas = this.findParent(dataRef['parent'], lastData);//查询所有上级
      if (datas.length > 0) {//删除上级选中
        let keys: Array<string> = [];
        for (let obj of datas) {
          keys.push(obj['key'])
        }
        checkedKeys = checkedKeys.filter((it) => !keys.includes(it))
      }
      //删除所有下级
      if (dataRef['key'] == data[0]['key']) {
        if (lastData.length > 0) {
          let keys: Array<string> = [];
          for (let obj of lastData) {
            keys.push(obj['key'])
          }
          checkedKeys = checkedKeys.filter((it) => !keys.includes(it))
        }
      } else {//当前选择节点所有下级
        checkedKeys = checkedKeys.filter((it) => !it.startsWith(dataRef['key']));
        let list = treeToList(children);
        if (list.length > 0) {
          let keys: Array<string> = [];
          for (let obj of list) {
            keys.push(obj['key'])
          }
          checkedKeys = checkedKeys.filter((it) => !keys.includes(it))
        }
      }
    }
    // @ts-ignore
    this.state.checkedKeys = checkedKeys;
    this.setState({
      checkedKeys,
    })
  };
  renderTreeNodes = data => {//渲染树节点
    return data.map((item) => {
      if (item.children) {
        return (
          <TreeNode title={item['name']} key={item['key']} value={item['name']} dataRef={item}>
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode title={item['name']} key={item['key']} value={item['name']} dataRef={item} />;
    });
  };
  renderMenu = (data) => {//渲染下拉菜单
    const { checkedKeys } = this.state;
    const treeNodes = this.renderTreeNodes(data);
    return (
      <Menu>
        <div style={{ maxHeight: 500, overflow: 'auto' }}>
          <Tree
            checkable
            defaultExpandAll
            checkedKeys={checkedKeys}
            onCheck={(keys, e) => this.onCheck(keys, e, data)}
          >
            {
              treeNodes
            }
          </Tree>
        </div>
      </Menu>
    )
  };
  renderTag = (data) => {//渲染tag
    const { selectedTags, checkedKeys } = this.state;
    return data.map((obj, index) => {
      const { children } = obj;
      let key = obj['key'] || index;
      let bool = false;
      // console.log(selectedTags, 'cccccccccc')
      if (!checkedKeys.includes(key) && children && children.length > 0) {
        let list = treeToList(children);
        for (let i = 0; i < list.length; i++) {
          let obj = list[i];
          if (checkedKeys.includes(obj['key'])) {
            bool = true
            break;
          }
        }
      }
      // if (checkedKeys.length > 0 && children && children.length > 0) {
      //   let len = 0;
      //   for (let obj of checkedKeys) {
      //     if (obj.startsWith(key)) {
      //       len++;
      //     }
      //   }
      //   if (len > 0 && children.length + 1 > len) {
      //     bool = true
      //   }
      // }
      return (
        <CheckableTag
          key={key}
          checked={selectedTags.includes(key)}
          onChange={checked => this.handleChange(key, checked, obj)}
          className={`${styles.tags} ${bool && styles.other}`}
        // className={`${styles.tags}` }
        >
          {obj['name']}
          {
            obj.children && obj.children.length > 0 && <React.Fragment>
              &nbsp;
              <Dropdown overlay={() => this.renderMenu([obj])} onVisibleChange={visible => this.onVisibleChange(key, visible)}>
                <a className="ant-dropdown-link" style={bool || selectedTags.includes(key) ? { color: 'white' } : undefined}>
                  {/* <a className="ant-dropdown-link" > */}
                  <DownOutlined />
                </a>
              </Dropdown>
            </React.Fragment>
          }
        </CheckableTag>
      );
    });
  };
  clearTag = () => {
    const { onChange } = this.props;
    this.setState({
      selectedTags: ['clear'],
      checkedKeys: [],
      visible: false,
    });
    onChange && onChange(undefined)
  };
  render() {
    const { data } = this.props;
    const { selectedTags } = this.state;
    return (
      <React.Fragment>
        <div style={{ display: 'table-cell' }}>
          <CheckableTag key={'clear'} checked={selectedTags.includes('clear')} className={styles.allBtn} onChange={this.clearTag}>全部</CheckableTag>
        </div>
        <div style={{ display: 'table-cell' }}>
          {
            data && this.renderTag(data)
          }
        </div>
      </React.Fragment>
    )
  }
}
