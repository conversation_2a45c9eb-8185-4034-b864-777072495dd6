import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Radio, Modal, Switch, Alert, DatePicker, Upload, message, Button, Input, } from 'antd';
import moment from 'moment';
import Tip from '@/components/Tip';
import {unixMoment} from '@/utils/method.js';
import _isEmpty from 'lodash/isEmpty';
import Date from '@/components/Date';

const RadioGroup = Radio.Group;
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      visible:false,
      memInfo:{},
      timeKey:moment().valueOf()
    }
  }
  // 时间限制
  disabledTomorrow=(current)=>{
    return current && current > moment().endOf('day');
  };
  handleOk=()=>{
    const {submit} = this.props;
    const {memInfo,hasMemValue} = this.state;
    const {name,code:memCode,orgCode,orgName,orgZbCode,developOrgCode:logOrgCode,d08Code,d08Name} = memInfo;
    this.props.form.validateFieldsAndScroll( async (err,val)=>{
      if(!err){
        // val = unixMoment(['canncelDate'],val);
        val['canncelName'] = val['canncelCode'] === '1' ? '退回入党申请人阶段': val['canncelCode'] === '2' ? '退回积极分子阶段' : '取消发展';
        val['name'] = name;
        val['memCode'] = memCode;
        val['orgCode'] = orgCode;
        val['orgName'] = orgName;
        val['orgZbCode'] = orgZbCode;
        val['logOrgCode'] = logOrgCode;
        val['d08Code'] = d08Code;
        val['d08Name'] = d08Name;
        // console.log(val,'val');

        const res = await this.props.dispatch({
          type:'memDevelop/backOutStatus',
          payload:{data:{...val}}
        });
        const {code = 500} = res;
        if(code === 0){
          this.handleCancel();
          Tip.success('操作提示','操作成功');
          submit && submit();
        }

      }
    })
  };
  handleCancel=()=>{
    this.setState({visible:false});
    this.destroy();
  };
  open=(record)=>{
    this.setState({visible:true,memInfo:record,timeKey:moment().valueOf()})
  };
  destroy=()=>{
   this.setState({
     memInfo:{},
   })
  };
  render() {
    const {form,loading:{effects = {}}={}} = this.props;
    const { getFieldDecorator } = form;
    const {visible,memInfo} = this.state;
    const { d08Code = '' } = memInfo;
    return (
      <Modal
        destroyOnClose
        title={`取消${d08Code === '3' ? '发展对象' : '积极分子'}`}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        confirmLoading={effects['memDevelop/toActive']}
        width={800}
        bodyStyle={{textAlign: 'center'}}
      >
        {
          visible &&
            <FormItem key={this.state.timeKey}>
              <Form>
                {/* <FormItem
                  label="取消资格日期"
                  {...formItemLayout}
                >
                  {getFieldDecorator('canncelDate', {
                    rules: [{ required: true, message: '召开支部委员会日期' }],
                    // initialValue:hasMemValue,
                    // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}} />
                  })(

                    <Date disabledDate={this.disabledTomorrow} />
                  )}
                </FormItem> */}
                <FormItem
                  label=""
                >
                  {getFieldDecorator('canncelCode', {
                    rules: [{ required: true, message: '必填' }],
                    // initialValue:hasMemValue,
                  })(
                    <RadioGroup>
                      <Radio value={'1'}>退回入党申请人阶段</Radio>
                      {
                        d08Code === '3' && <Radio value={'2'}>退回积极分子阶段</Radio>
                      }
                      <Radio value={'3'}>取消发展</Radio>
                    </RadioGroup>
                  )}
                </FormItem>

                {(function(_this){
                  let value = _this.props.form.getFieldValue('canncelCode');
                  if(value == '3'){
                    return (
                      <FormItem
                        label="取消发展原因"
                      >
                        {getFieldDecorator('cancelDevelopReason', {
                          rules: [{ required: true, message: '必填' }],
                          // initialValue:hasMemValue,
                        })(
                          <Input.TextArea></Input.TextArea>
                        )}
                      </FormItem>
                    )
                  }
                })(this)}
              </Form>
            </FormItem>
        }

      </Modal>
    );
  }
}
export default Form.create()(index);
