import { listTrainByCode, trainAddOrUpdate, deleteTrainByCode } from '@/pages/org/services/org'
export const getTrainingPageType = (d01Code = '') => {
  let pageType: any = undefined;
  if (`${d01Code}`.startsWith('63')) {
    pageType = '1';
  }
  if (`${d01Code}`.startsWith('1')) {
    pageType = '2';
  }
  if (`${d01Code}`.startsWith('61') || `${d01Code}`.startsWith('62') || `${d01Code}` === '911') {
    pageType = '3';
  }
  return pageType;
}
export const type63Config = () => {
  let cols = [
    {
      title: '年度',
      dataIndex: 'year',
      // width: 150,
    },
    {
      title: '总培训党员人次',
      dataIndex: 'trainTotal',
      // width: 150,
    },
    {
      title: '新党员培训人次',
      dataIndex: 'newTrain',
      // width: 150,
    },
    {
      title: '老年党员培训人次',
      dataIndex: 'elderlyTrain',
      // width: 150,
    },
    {
      title: '流动党员培训人次',
      dataIndex: 'flowTrain',
      // width: 150,
    },
  ];
  let tbaleUrl = listTrainByCode;
  let delUrl = deleteTrainByCode;
  let submitUrl = trainAddOrUpdate;
  return { cols, url: tbaleUrl, delUrl, submitUrl }
}

export const type1Colums = () => {
  let cols = [
    {
      title: '年度',
      dataIndex: 'year',
      // width: 150,
    },
    {
      title: '省级培训班期数',
      dataIndex: 'provincialTrainClass',
      // width: 150,
    },
    {
      title: '基层党委举办培训班期数',
      dataIndex: 'levelPartyClass',
      // width: 150,
    },
    {
      title: '远程教育终端站点数',
      dataIndex: 'remoteEducation',
      // width: 150,
    },
  ]
  let url = listTrainByCode;
  let delUrl = deleteTrainByCode;
  let submitUrl = trainAddOrUpdate;
  return { cols, url, delUrl, submitUrl }
}

export const getPageConfig = (pageType: any = '') => {
  switch (pageType) {
    case '1':
      return type63Config();
    case '2':
      return type1Colums();
    case '3':
      return type1Colums();
  }
};
