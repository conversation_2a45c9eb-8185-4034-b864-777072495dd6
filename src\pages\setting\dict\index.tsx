import React, { useEffect, useState } from 'react';
import Structure from '../components/Structure';
import request from '@/utils/request';
import { Space, Select } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import Tree from '../components/tree';
import { getDictList } from '@/services';
import tip from '@/components/Tip';
export default function (props) {
  const [tree, setTree] = useState([]);
  const [list, setList] = useState([]);
  const [select, setSelect] = useState([]);
  const [dictName, setDictName] = useState();
  const [selKey, setSelKey] = useState();
  const [treeKey, setTreeKey]: any = useState([]);
  useEffect(() => {
    request('/api/table/tableSelect').then(res => {
      if (res['code'] == '0') {
        setTree(res['data']);
        if (res['data'].length > 0) {
          const item = res['data'][0];
          treeChange(item);
          setTreeKey([item['id']]);
        }
      }
    })
  }, []);
  const treeChange = (item) => {
    setSelKey(undefined);
    setSelect([]);
    request(`/api/table/tableFind?id=${item['id']}`).then(res => {
      if (res['code'] == '0') {
        let data: any = [];
        for (let obj of res['data']) {
          if (obj['colType'] == 'lable') {
            data.push(obj)
          }
        }
        setSelect(data);
        setList([]);
        setDictName(undefined);
      }
    })
  }
  const getDict = (val) => {
    if (val) {
      let find = select.find(obj => obj['id'] == val);
      if (find) {
        console.log(find,'find')
        setSelKey(val);
        setDictName(find['colLectionCode']);
        getDictList({
          data: {
            dicName: find['colLectionCode']
          }
        }).then(res => {
          if (res['code'] == '0') {
            setList(res['data'])
          }
        })
      }
    } else {
      setSelKey(undefined);
      setList([]);
    }
  }
  const listChange = (data) => {
    console.log("🚀 ~ listChange ~ data:", data)
    request(`/api/table/tableUpDic`, {
      method: 'POST',
      body: {
        data: {
          dicName: dictName,
          ...data
        }
      }
    }).then(res => {
      if (res['code'] == '0') {
        tip.success('提示信息', '备注修改成功');
        getDict(dictName);
      }
    });
  };
  return (
    <div style={{ display: 'flex', height: '100%' }}>
      <div style={{ width: 200, borderRight: '1px solid #f2f2f2', marginRight: 12 }}>
        <WhiteSpace />
        <Tree selectedKeys={treeKey} data={tree} onChange={treeChange} />
      </div>
      <div style={{ width: '100%', padding: '12px 0' }}>
        <Space>
          <Space>
            字段：
            <Select value={selKey} style={{ width: 200 }} onChange={(val) => getDict(val)} allowClear placeholder={'请选择'}>
              {
                select.map(item => <Select.Option value={item['id']}>{item['colName']}</Select.Option>)
              }
            </Select>
          </Space>
        </Space>
        <WhiteSpace />
        <Structure data={list} onChange={listChange} />
      </div>
    </div>
  )
}
