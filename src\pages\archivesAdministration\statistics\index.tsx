import React, { useState, Fragment, useEffect, useRef } from 'react';
import _isEmpty from 'lodash/isEmpty';
import style from './index.less';
import { _history } from "@/utils/method";
import qs from 'qs';
import { Tabs, Input, Modal, Form, Radio, Button, Descriptions, Divider } from 'antd';
import ListTable from 'src/components/ListTable';
import { getMemDevelopAuditList, auditMemDevelop } from '../service'
import { getSession } from '@/utils/session';
import NowOrg from 'src/components/NowOrg';
import moment from 'moment'
import { connect } from "dva";
import tip from '@/components/Tip';
import ElectronicArchives from '@/pages/developMem/zy/components/electronicArchives'
import Icon from '@ant-design/icons';
import DA from './da';
import ZH from './zh';
const Search = Input.Search;

const TabPane = Tabs.TabPane;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};

function Index(props: any) {
    const [filterHeight, setFilterHeight] = useState();
    const [loading, setLoading] = useState(false);
    const [list, setList] = useState([]);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [memName, setMemName] = useState('')
    const [visible, setVisible] = useState(false)
    const [activeKey, setActiveKey] = useState('1'); // 默认选中第一个Tab
    const [confirmLoading, setConfirmLoading] = useState(false)
    const [statu, setStatu] = useState(null)
    const [digitalLotNo, setDigitalLotNo] = useState('')
    const [visible1, setVisible1] = useState(false)
    const [reason, setReason] = useState('')
    const electronicArchives = useRef()
    const [form] = Form.useForm();
    const { location: { pathname = '' } = {} } = _history
    const fxref = useRef();
    const org = getSession('org') || {};

    const searchClear = (e) => {
        setMemName(e.target.value)
    }
    const search = (value) => {
        getList({ pageNum: 1, memName: value })
    }
    const getList = async (p?: any) => {
        setLoading(true)
        const { code = 500, data: { list = [], pageNumber = '', pageSize = '', totalRow = '' } = {} } = await getMemDevelopAuditList({ data: { memOrgCode: org['orgCode'], memName, pageNum: 1, pageSize: 10, ...p } })
        setLoading(false)
        if (code == 0) {
            setList(list)
            setPagination({ current: pageNumber, pageSize: pageSize, total: totalRow })
        }

    }
    const handleOk = () => {
        form.submit()
    }
    const handleCancel = () => {
        form.resetFields()
        setStatu(null)
        setVisible(false)
    }
    const handleCancel1 = () => {
        setVisible1(false)
    }
    const hadndleFinish = async (value) => {
        setConfirmLoading(true)
        const { code = 500 } = await auditMemDevelop({ data: { digitalLotNo, ...value } })
        if (code == 0) {
            tip.success('操作提示', '操作成功')
            getList()
            handleCancel()
        }
        setConfirmLoading(false)
    }
    useEffect(() => {
        getList({ pageNum: 1, pageSize: 10, memName })
    }, [org['code']])
    return (
        <div>
            <NowOrg 
            // extra={
            //     <React.Fragment>
            //         <Search style={{ width: 200, marginLeft: 16 }} onSearch={search} onChange={searchClear} placeholder={'请输入检索关键词'} />
            //     </React.Fragment>
            // } 
            />
            <Tabs activeKey={activeKey} onChange={(e) => {
                setActiveKey(e)
            }}>
                <TabPane tab="档案统计" key="1" />
                <TabPane tab="综合统计" key="2" />
            </Tabs>
            {
                activeKey == '1' && <DA />
            }
            {
                activeKey == '2' && <ZH />
            }
        </div>
    );
}
export default Index