import React,{Fragment} from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Divider, <PERSON>con<PERSON>rm, <PERSON>u, Button, Input, Dropdown, Switch } from 'antd';
import RuiFilter from 'src/components/RuiFilter';
import ListTable from 'src/components/ListTable';
import NowOrg from 'src/components/NowOrg';
import WhiteSpace from '@/components/WhiteSpace';
import Tip from '@/components/Tip';
import Details from '../../../components/detail';
import _isNumber from 'lodash/isNumber';
import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';
import {withContext} from 'src/utils/global.jsx';
import {connect} from "dva";
import {_history as router} from "@/utils/method";
import Add from '../../../manage/components/Modal';
import {getSession} from "@/utils/session";
import moment from 'moment';
import qs from 'qs';
import {setListHeight} from "@/utils/method";
import style from '@/pages/notice/manage/components/list/index.less';
const {Search} = Input;
@withContext
@connect(({notice,commonDict,loading})=>({notice,commonDict,loading}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state={
      filterHeight:100,
      filterChecked:{}
    }
  }
  componentDidMount(): void {
    setListHeight(this);
  }

  // 筛选
  filterChange=(val)=>{
    this.setState({filterChecked:val});
    this.props.dispatch({
      type:'notice/updateState',
      payload:{
        filter2:val
      }
    });
    this.action()
  };
  // 分页
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  action=(val?:object)=>{
    const {pagination2={}}=this.props.notice;
    const {current = 1,pageSize = 10} = pagination2;
    const org=getSession('org') || {};
    this.props.dispatch({
      type:'notice/getList2',
      payload:{
        data:{
          code:org['code'],
          pageNum:current,
          pageSize,
          ...val
        }
      }
    })
  };
  search=(value)=>{
    this.props.dispatch({
      type:'notice/updateState',
      payload:{
        memName2:value
      }
    });
    this.action();
    // this.setState({
    //   search:{memName:value}
    // },()=>this.action());
  };
  // 人员编辑
  addOrEdit=async(record)=>{

  };

  del=async(record)=>{
    const {code} = record;
    if(!_isEmpty(code)){
      const res = await this.props.dispatch({
        type:'notice/del',
        payload:{
          data:{code}
        }
      });
      const {code:resCode} = res;
      if(resCode === 0){
        Tip.success('操作提示','删除成功');
        this.action()
      }
    }
  };

  addNew=()=>{
    this['addNotice'].open();
  };
  edit= async ({isSupplt,isEdit},code)=>{
    const res = await this.props.dispatch({
      type:'notice/getDtail',
      payload:{
        type:2,
        code
      }
    });
    if(res === 0){
      this['AcModal'] && this['AcModal'].open({isSupplt,isEdit});
    }
  };
  // 查看当组织层级
  lookOrgs=(record)=>{

  };
  // 导出
  exportInfo= async ()=>{

  };
  openDtail= async (code)=>{
    const res = await this.props.dispatch({
      type:'notice/getDtail',
      payload:{
        type:2,
        code
      }
    });
    if(res === 0){
      this['AcDetails'] && this['AcDetails'].open();
    }
  };
  componentWillUnmount(): void {

  }

  addNoticeOnclose=()=>{

  };
  showDetails=(record)=>{
    this['DetailNotice'].open(record);
  };
  render(): React.ReactNode {
    const {notice ={},loading:{effects = {}} ={},commonDict} = this.props;
    const {list2, pagination2 ,memName,filter} = notice;
    const {current = 1,pageSize = 10} = pagination2;
    const {filterHeight}=this.state;

    const filterData = [
      {
        key:'d68CodeList',name:'消息类型',value:commonDict[`dict_d67`],
      },
    ];
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:60,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'消息名称',
        dataIndex:'name',
        width:120,
      },
      {
        title:'消息内容',
        dataIndex:'context',
        render:(text)=>{
          return (
            <div className={style.messageContext}>{text}</div>
          )
        }
      },
      {
        title:'消息类型',
        dataIndex:'messageTypeName',
        width:90,
      },
      {
        title:'操作',
        width:120,
        dataIndex:'action',
        render:(text,record)=>{
          return(
            <span>
              <a onClick={()=>this.showDetails(record)}>详情</a>
              <Divider type="vertical"/>
              <Popconfirm title={'是否删除？'} onConfirm={()=>this.del(record)}>
               <a className={'del'} >删除</a>
              </Popconfirm>
            </span>
          )
        },
      },
    ];

    return (
      <Fragment>
        <NowOrg
          extra={
            <React.Fragment>
              <Search style={{width:200,marginLeft:16}} onSearch={this.search} placeholder={'请输入检索关键词'}/>
            </React.Fragment>
          }
        />
        <RuiFilter
          data={filterData}
          onChange={this.filterChange}
        />
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:filterHeight}} columns={columns} data={list2} pagination={pagination2} onPageChange={this.onPageChange}/>
        <Add ref={ e=>this['addNotice']= e} onClose={this.addNoticeOnclose}/>
        <Details ref={e=>this['DetailNotice'] = e} {...this.props} detailsType={'mySend'}/>
      </Fragment>
    )
  }
}
