import React, { Fragment, useEffect, useRef, useState } from 'react';
import st from './index.less';
import { Alert, Button, Col, Modal, Pagination, Row, Skeleton } from 'antd';
import { _history as router } from '@/utils/method';
import ReactSeamlessScroll from 'rc-seamless-scroll';
import moment from 'moment';
import { screenexport, politicalBirthday } from '../../../services';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _cloneDeep from 'lodash/cloneDeep';
import Birthday from '../../components/birthday';
import useDomToimage from '@/utils/hooks/useDomToimage';
import { changeOrgName2 } from '../../../utils/tool';

const index = (props: any) => {
  const {
    session: {
      birthday = '',
      joinOrgDate = '',
      fileList = [],
      name = '',
      photo_base64 = '',
      selectText = '',
      info: { answer = '', orgName = '' } = {},
      code = '',
      sign = '',
      moreThan50 = '',
      asd = ''
    } = {},
  } = props;

  const ref: any = useRef();

  const [lineRef, func] = useDomToimage({
    callBack: () => {
      setTimeout(() => {
        Tip.success('操作提示', '下载成功');
        setBtnHide(false);
      }, 1000 * 2);
    },
  });
  const [btnHide, setBtnHide] = useState<any>(false);

  const back = async () => {
    router.push('/qzs/screen/last');
  };

  const exports = async () => {
    const res = await screenexport({ memCode: code });
    if (res.status == 200) {
      Tip.success('操作提示', '操作成功');
    }
  };

  function adjustArray(a, m) {
    if (_isEmpty(a)) return a;
    const n = a.length;
    if (n < m) {
      // 补全数组长度到 m
      for (let i = n; i < m; i++) {
        a[i] = a[i % n];
      }
    } else if (n > m) {
      // 截取到 m 位长度
      a.splice(m, n - m);
    }
    return a;
  }

  const finList = adjustArray(_cloneDeep(fileList), 10);
  const arr = new Array(10).fill('');

  return (
    <div ref={lineRef} style={{ height: 633 }}>
      <div className={st.box}>
        <div className={st.head} style={{ visibility: btnHide ? 'hidden' : undefined }}>
          <Button className={st.btn} onClick={back}>
            返 回
          </Button>
          <div>
            <Button
              className={st.btn}
              style={{ marginRight: 10 }}
              onClick={async () => {
                await setBtnHide(true);
                setTimeout(() => {
                  func(`${name}_${moment().format('YYYY_MM_DD')}`);
                }, 1000 * 1);
              }}
            >
              保存电子贺卡
            </Button>
            {/* <Button className={st.btn} onClick={exports}>
            数据导出
          </Button> */}
          </div>
        </div>
        <div className={st.body}>
          <div className={st.left}>
            <div className={st.box1}>
              <div className={st.box1top}>
                <div className={st.box1topl}>
                  <img className={st.photo} src={asd} alt="" />
                  {/* <img
                  className={st.jiangzhang}
                  src={require('../../../../../../assets/qzs/icon11.png')}
                  alt=""
                /> */}
                </div>
                <div className={st.box1topr}>
                  {/* 一句话 */}
                  <div className={st.text}>{selectText}</div>
                  <img src={require('../../../../../../assets/qzs/icon2.png')} alt="" />
                </div>
              </div>
              <div className={st.name}>{name}</div>
              <div className={st.box1bot}>
                <div className={st.desc}>
                  <div>出生年月</div>:<div>{moment(birthday).format('YYYY年M月D日')}</div>
                </div>
                <div className={st.desc}>
                  <div>政治生日</div>:<div>{moment(joinOrgDate).format('YYYY年M月D日')}</div>
                </div>
                <div className={st.desc}>
                  <div>所在党支部</div>:<div>{changeOrgName2(orgName)}</div>
                </div>
              </div>
            </div>
          </div>
          <div className={st.mid}>
            <img
              src={require('../../../../../../assets/qzs/midflag.png')}
              alt=""
              style={{ position: 'relative', top: '-90px', left: '148px' }}
            />
            <img
              className={st.midicon}
              src={require('../../../../../../assets/qzs/midicon.png')}
              alt=""
            />
            <div className={st.scroll}>
              <div className={st.stk}>
                <Skeleton loading={_isEmpty(finList)} active></Skeleton>
              </div>
              <ReactSeamlessScroll
                list={arr}
                ref={ref}
                hover={true}
                direction={'left'}
                wrapperHeight={300}
                isWatch={true}
                step={0.2}
                // singleWidth={250}
              >
                {arr.map?.((its, index) => {
                  const it = finList?.[index];
                  if (_isEmpty(it)) {
                    return (
                      <div style={{ visibility: 'hidden' }} className={st.scorllItem}>
                        {index}
                      </div>
                    );
                  }
                  return (
                    <React.Fragment key={index}>
                      <div className={st.scorllItem}>
                        <img className={st.scorllItemph} src={it?.thumbUrl} alt="" />
                        <img
                          className={st.scorllItembg}
                          src={require('../../../../../../assets/qzs/midboticon.png')}
                        />
                      </div>
                    </React.Fragment>
                  );
                })}
              </ReactSeamlessScroll>
            </div>
            <div className={st.birthday} style={{ visibility: btnHide ? 'hidden' : undefined }}>
              <Birthday joinOrgDate={joinOrgDate} memCode={code}></Birthday>
            </div>
          </div>
          <div className={st.right}>
            <div className={st.rightbg}>
              <img
                src={require('../../../../../../assets/qzs/midright.png')}
                style={{ width: '100%' }}
                alt=""
              />
              {!!moreThan50 && (
                <img
                  src={require('../../../../../../assets/qzs/more50.png')}
                  className={st.more50}
                  alt=""
                />
              )}
              <div style={{ height: 14 }}></div>
              <div className={st.text} dangerouslySetInnerHTML={{ __html: answer }}></div>
              <div className={st.sign}>
                <img src={sign} alt="" />
              </div>
              <div className={st.logos}>
                <div>清镇政治生日仪式中心</div>
                <div style={{ marginRight: '21px' }}>{moment().format('YYYY年M月D日')}</div>
              </div>
            </div>
          </div>
        </div>
        <div className={st.bot}></div>
      </div>
    </div>
  );
};

export default index;
