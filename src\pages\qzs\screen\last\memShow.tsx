import React, { Fragment, useEffect, useState } from 'react';
import Young from './components/young';
import Middle from './components/middle';
import Old from './components/old';
import { getSession } from '@/utils/session';
import { archivesSave, archivesFind, pullFile, pullFileQz, getImgUrl } from '@/services';
import _isEmpty from 'lodash/isEmpty';
import UploadComp, { fitFileUrlForForm, getInitFileList } from '@/components/UploadComp';
import { findByCode } from '../services';
import { _history as router } from '@/utils/method';
import moment from 'moment';

const memShow = () => {
  const session: any = getSession('last_info') || {};

  const [selectText, setSelectText] = useState<any>('');
  const [fileList, setFileList] = useState<any>([]);
  const [info, setInfo] = useState<any>({});
  const [isYoung, setIsYoung] = useState<any>();
  const [moreThan50, setMoreThan50] = useState<any>();
  const [sign, setSign] = useState<any>();
  const [img, setImg] = useState<any>();

  const getInfo = async (record) => {
    const res = await findByCode({ memCode: record.code });
    if (res.code == 0) {
      const {
        data: {
          archivesPath = '',
          archives = '',
          sutra = '',
          tissueWord = '',
          sutraPath = '',
          signaturePath = '',
          politicalLifes = [],
        } = {},
      } = res;

      if (_isEmpty(res.data)) return;

      const join = moment(res.data.joinOrgDate);
      const now = moment();
      let _isYoung = now.diff(join, 'year') <= 20;
      setIsYoung(_isYoung);

      let _moreThan50 = now.diff(join, 'year') >= 50;
      // setMoreThan50(_moreThan50);

      setInfo(res.data);
      // // 档案原件
      // try {
      //   const file_base64 = await pullFile({ path: archivesPath });
      //   setUrl(file_base64);
      //   setFile({ url: archivesPath });
      // } catch (e) {
      //   console.log('🚀 ~ getInfo ~ e:', e, '档案原件');
      // }

      // // 档案(扫描内容)
      // setText(archives);

      console.log('🚀 ~ getInfo ~ res:', res);
      if (_isEmpty(res.data.photo)) {
        setImg(require('../../../../assets/head.jpg'));
      } else {
        try {
          pullFileQz({ path: res.data.photo }).then((base64) => {
            setImg(base64);
          });
        } catch (error) {
          console.log('🚀 ~ getList ~ error:', error);
        }
      }

      // 经典一句话
      setSelectText(tissueWord);

      // 签名截图
      try {
        if (signaturePath) {
          pullFileQz({ path: signaturePath }).then((sutra_base64) => {
            setSign(sutra_base64);
          });
        }
      } catch (e) {
        console.log('🚀 ~ getInfo ~ e:', e, '签名截图');
      }

      // // 经典一句话截图
      // try {
      //   const sutra_base64 = await pullFile({ path: sutraPath });
      //   setJTbase64(sutra_base64);
      // } catch (e) {
      //   console.log('🚀 ~ getInfo ~ e:', e, '经典一句话截图');
      // }

      // 政治生活照
      try {
        // let photos = getInitFileList(politicalLifes.join(','));
        // for (let i = 0; i < politicalLifes.length; i++) {
        //   const item = photos[i];
        //   const base64 = await pullFile({ path: item.url });
        //   photos[i].thumbUrl = base64;
        //   photos[i].id = +new Date() + i;
        //   photos[i].url = item.url;
        // }
        // setFileList(photos);

        let len = 3;
        if (!_isYoung) {
          len = 10;
        }

        let photos = getInitFileList(politicalLifes.join(',')).slice(0, len);
        let arr = photos.map((item, i) => {
          return () => pullFileQz({ path: item.url });
        });
        const res = await Promise.all(arr.map((it) => it()));
        photos = photos.map((it, index) => {
          return {
            ...it,
            thumbUrl: res[index],
            id: +new Date() + index,
            url: it.url,
          };
        });
        setFileList(photos);
      } catch (e) {
        console.log('🚀 ~ getInfo ~ e:', e, '政治生活照');
      }
    }
  };

  useEffect(() => {
    getInfo(session);
  }, [session.code]);
  return (
    <Fragment>
      {isYoung ? (
        <Young session={{ ...session, selectText, fileList, info, sign, asd: img }}></Young>
      ) : (
        <Middle
          session={{ ...session, selectText, fileList, info, sign, moreThan50, asd: img }}
        ></Middle>
      )}
    </Fragment>
  );
};

export default memShow;
