import request from '@/utils/request';
import qs from 'qs';

// 表1
export function saveForm1(params) {
  return request(`/api/specialSurvey/addOne`, {
    method: 'POST',
    body: params,
  });
}
export function findForm(params) {
  return request(`/api/specialSurvey/findDataByCode?${qs.stringify(params)}`);
}

// 表2
export function saveForm2(params) {
  return request(`/api/specialSurvey/addTwo`, {
    method: 'POST',
    body: params,
  });
}
// 表3
export function saveForm3(params) {
  return request(`/api/specialSurvey/addThree`, {
    method: 'POST',
    body: params,
  });
}
// 表4
export function saveForm4(params) {
  return request(`/api/specialSurvey/addFour`, {
    method: 'POST',
    body: params,
  });
}
// 表5
export function saveForm5(params) {
  return request(`/api/specialSurvey/addFive`, {
    method: 'POST',
    body: params,
  });
}
// 表8
export function saveForm8(params) {
  return request(`/api/ztdc/saveZt78Data`, {
    method: 'POST',
    body: params,
  });
}
export function saveZt13Data(params) {
  return request(`/api/ztdc/saveZt13Data`, {
    method: 'POST',
    body: params,
  });
}
export function saveZt12Data(params) {
  return request(`/api/ztdc/saveZt12Data`, {
    method: 'POST',
    body: params,
  });
}
export function saveZt11Data(params) {
  return request(`/api/ztdc/saveZt11Data`, {
    method: 'POST',
    body: params,
  });
}
export function saveZt10Data(params) {
  return request(`/api/ztdc/saveZt10Data`, {
    method: 'POST',
    body: params,
  });
}

export function findZtDataByCode(params) {
  return request(`/api/ztdc/findZtDataByCode?${qs.stringify(params)}`);
}
