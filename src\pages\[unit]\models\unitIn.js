import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import { del, getList, collectiveEconomicGetList, collectiveEconomicDelete, collectiveEconomicDetails } from '../services';
import {getSession} from "@/utils/session";
import { changeListPayQuery } from '@/utils/method.js';

const unitIn = modelExtend(listPageModel,{
  namespace: "unitIn",
  state:{
    basicInfo:{},
    basicInfoData:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if(pathname==='/unit/not'){
          let org=getSession('org') || {};
          let defaultParas={
            pageNum:1,
            pageSize:10,
            isCreateOrg:'0',
          };
          const dictData=['dict_d04','dict_d05','dict_d35'];
          for(let obj of dictData){
            dispatch({
              type:'commonDict/getDictTree',
              payload:{
                data:{
                  dicName:obj
                }
              }
            });
          }
          dispatch({
            type:'getList',
            payload:{
              data:{
                mainUnitOrgCode:org['orgCode'],
                manageUnitOrgCode:org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
        if(pathname==='/unit/economic'){
          let org=getSession('org') || {};
          let defaultParas={
            pageNum:1,
            pageSize:10,
          };
          dispatch({
            type:'getList2',
            payload:{
              data:{
                orgCode:org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put,select }) {
      const {filter,unitName}=yield select(state=>state['unitIn']);
      const {data={}} = yield call(getList, {data:{...payload['data'],...filter,unitName}});
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      });
      yield put({
        type:'updateState',
        payload:{
          isCreateOrg:payload['data']['isCreateOrg'],
        }
      })
    },
    *del({ payload }, { call, put }) {
      return yield call(del, payload);
    },


    //集体经济
    *getList2({ payload }, { call, put,select }) {
      const {filter,unitName}=yield select(state=>state['unitIn']);
      const {data={}} = yield call(collectiveEconomicGetList, {data:{...payload['data'],...filter,unitName}});
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      });
      yield put({
        type:'updateState',
        payload:{
          isCreateOrg:payload['data']['isCreateOrg'],
        }
      })
    },
    *del2({ payload }, { call, put }) {
      return yield call(collectiveEconomicDelete, payload);
    },
    *findOrg({ payload }, { call, put }) {
      const obj= yield call(collectiveEconomicDetails, payload);
      yield put({
        type:'updateState',
        payload:{
          basicInfo:obj['data'] || {},
        }
      });
    },

  }
});
export default unitIn;
