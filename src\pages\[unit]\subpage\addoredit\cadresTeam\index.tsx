import React, { Fragment, useEffect, useRef, useState } from 'react';
import Add from './add';
import ListTable from '@/components/ListTable';
import { But<PERSON>, Divider, Popconfirm } from 'antd';
import moment from 'moment';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import Tip from '@/components/Tip';
import { getList,del } from './services';

const index = (props: any) => {
  let filterHeight = `calc(100vh - ${288}px)`;
  const addOrEditRef: any = useRef();
  const [listData, setListData] = useState([]);
  const [pagination, setPagination] = useState<any>({ pageSize: 20, current: 1, total: 0 });
  const [listLoading, setListLoading] = useState(false);
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 58,
      align: 'center',
      render: (text, record, index) => {
        return index + 1;
      },
    },
    {
      title: '统计时间',
      dataIndex: 'countDate',
      align: 'center',
      render: (text, record, index) => {
        if(text){
          return moment(text).format('YYYY-MM-DD')
        }
      },
    },
    {
      title: '行政编制数',
      dataIndex: 'xzbzNum',
      align: 'center',
    },
    {
      title: '事业编制数',
      dataIndex: 'sybzNum',
      align: 'center',
    },
    {
      title: '被借调工作人员总数',
      dataIndex: 'secondedStaffNum',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      align: 'center',
      render: (text, record) => {
        return (
          <span>
            <a onClick={() =>addOrEditRef.current.open(record)}>编辑</a>
            <Divider type="vertical" />
            <Popconfirm title="确定要删除吗？" onConfirm={async () => {
              const {code = 500 } = await del({code:record.code});
              if(code === 0){
                Tip.success('操作提示', '操作成功');
                getLists({ pageNum: 1 });
              }
            }}>
              <a href={'#'} className={'del'}>删除</a>
            </Popconfirm>
          </span>
        );
      },
    },
  ];

  const getLists = async (p = {}) => {
    const { unit: { basicInfo = {} } = {} } = props;
    setListLoading(true);
    const {
      code = 500,
      data: { pageNumber: current = 1, pageSize = 20, totalRow: total = 0, list = [] } = {},
    } = await getList({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
        unitCode: basicInfo.code,
        ...p,
      },
    });
    setListLoading(false);
    if (code === 0) {
      setListData(list);
      setPagination({ current, total, pageSize });
    }
  };

  useEffect(() => {
    getLists({ pageNum: 1 });
  }, []);
  return (
    <Fragment>
      <Button
        type={'primary'}
        icon={<LegacyIcon type={'plus'} />}
        onClick={() => addOrEditRef.current.open()}
        style={{ marginLeft: 16 }}
      >
        新增
      </Button>
      <div style={{ margin: 16 }}>
        <ListTable
        
          scroll={{ y: filterHeight }}
          columns={columns}
          data={listData}
          pagination={pagination}
          onPageChange={(page: any, pageSize: any) => {
            getLists({ pageNum: page, pageSize });
          }}
        />
      </div>
      <Add
        {...props}
        ref={addOrEditRef}
        onOK={() => {
          getLists({ pageNum: 1 });
        }}
      />
    </Fragment>
  );
};

export default index;
