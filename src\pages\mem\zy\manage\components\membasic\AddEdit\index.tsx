import React, { Fragment } from 'react';
import {
  CloseOutlined,
  ApartmentOutlined,
  BlockOutlined,
  BranchesOutlined,
  CalendarOutlined,
  ContainerOutlined,
  FieldTimeOutlined,
  FlagOutlined,
  AccountBookOutlined,
} from '@ant-design/icons';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Avatar, Col, Menu, Modal, Row } from 'antd';
import Base from '../BasicInfo';
import Rewards from '../Rewards';
import Abroad from '../Abroad';
import Difficulty from '../Difficulty';
import Administrative from '../AdministrativeDuties';
import PartyPosition from '../PartyPosition';
import Train from '../train';
import PartyFeePayment from '../partyFeePayment';
import Appraisal from '../Appraisal'
import { connect } from "dva";
import { getSession } from '@/utils/session';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { tableColConfig } from '@/services';
import { changeMsgTip } from '@/utils/method';
import {LockMsg} from '@/pages/user/lock';
let menuData = [
  {
    code: '1',
    name: '基本信息',
    icon: <ApartmentOutlined />,
  },
  {
    code: '2',
    name: '任职情况',
    icon: <BlockOutlined />,
  },
  // {
  //   code: '3',
  //   name: '行政职务',
  //   icon: <BranchesOutlined />,
  // },
  {
    code: '4',
    name: '奖惩及出党',
    icon: <CalendarOutlined />,
  },
  {
    code: '5',
    name: '出国出境',
    icon: <ContainerOutlined />,
  },
  {
    code: '6',
    name: '困难党员',
    icon: <FieldTimeOutlined />,
  },
  {
    code: '7',
    name: '培训情况',
    icon: <FlagOutlined />,
  },
  // {
  //   code: '8',
  //   name: '民主评议',
  //   icon: <FlagOutlined />,
  // },
  // {
  //   code: '9',
  //   name: '党费交纳情况',
  //   icon: <AccountBookOutlined />,
  // },
];
// @ts-ignore
@connect(({ memBasic, memRewards, memAbroad, memDifficulty, memTrain, memAppraisal, commonDict, loading }) => ({ memBasic, memRewards, memAbroad, memDifficulty, memTrain, memAppraisal, commonDict, loading }))
export default class index extends React.Component<any, any>{
  static show() { };
  static close() { };
  static clear() { };
  constructor(props) {
    super(props);
    let obj = menuData[0];
    this.state = {
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    };
    index.show = this.open;
    index.close = this.handleCancel;
    index.clear = this.destroy;
  }
  componentDidMount(): void {
    tableColConfig({ id: 'ccp_mem' }).then(res => {
      if (res['code'] == '0') {
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg: msg,
        });
      }
    });

    tableColConfig({ id: 'ccp_mem_reward' }).then(res => {
      if (res['code'] == '0') {
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg_reward: msg,
        });
      }
    });

    tableColConfig({ id: 'ccp_mem_abroad' }).then(res => {
      if (res['code'] == '0') {
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg_abroad: msg,
        });
      }
    });

    tableColConfig({ id: 'ccp_mem_train' }).then(res => {
      if (res['code'] == '0') {
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg_train: msg,
        });
      }
    });
  }

  handleOk = () => {
    this.handleCancel();
  };
  handleCancel = () => {
    this.destroy();
    this.props.onClose && this.props.onClose()
    // this.getList();
    // this.setState({
    //   visible:false
    // })
  };
  open = () => {
    // this.destroy();
    this.setState({
      visible: true,
    })
  };
  destroy = () => {
    let obj = menuData[0];
    this.setState({
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    });
    this.props.dispatch({ //重置model
      type: 'memBasic/updateState',
      payload: {
        basicInfo: {},
      }
    })
    this.props.destroy && this.props.destroy();
  };
  onSelect = (item) => {
    const { key, keyPath } = item;
    const selected = menuData.find(obj => obj['code'] === key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  getList = () => {
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memBasic/getList',
      payload: {
        data: {
          pageNum: 1,
          pageSize: 10,
          searchType: 1,
          memOrgCode: org['orgCode']
        }
      }
    })
  };
  render() {
    const { visible, selected, keyPath, key, tipMsg = {}, tipMsg_reward = {}, tipMsg_abroad = {}, tipMsg_train = {} } = this.state;
    const { basicInfo = {} } = this.props.memBasic;
    const {menuDataKey} = this.props;
    let _memu = menuData;
    //外部控制只展示某些menu item项
    if(!_isEmpty(menuDataKey)){
      _memu = menuData.filter(it => menuDataKey.includes(it.code))
    }
    if(basicInfo['findHistory']) {
      _memu = menuData.filter(it =>it.code=='1')
    }

    return (
      <Modal
        title=""
        wrapClassName='editModal'
        destroyOnClose
        closable={false}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        footer={false}
        width={'calc(90vw)'}
      >
        <div className='container'>
          <div className='header'>
            <Row>
              <Col span={4} style={{ textAlign: 'center' }}>
                <Avatar style={{ backgroundColor: '#7265e6', verticalAlign: 'middle' }} size="large">
                  {_get(basicInfo, 'name', 'admin')}
                </Avatar>
              </Col>
              <Col span={15}><h2>{basicInfo['code'] ? <div>
                编辑
                 {/* {key == '1' && <LockMsg basicInfo={{...basicInfo,unlockObject:'1'}}/>} */}
              </div> : '新增'}</h2></Col>
              <Col span={5} className={'close'}><CloseOutlined onClick={this.destroy} /></Col>
            </Row>
          </div>
          <div>
            <Row>
              <Col span={4} style={{ borderRight: '1px solid rgb(233, 233, 233)' }}>
                <div className='slider'>
                  <LegacyIcon type={selected['icon'] || undefined} style={{ marginRight: 8 }} />{selected['name']}
                </div>
                {
                  _isEmpty(basicInfo?.code) ?
                    <Menu selectedKeys={keyPath} style={{ border: 'none' }} onSelect={this.onSelect} key={1}>
                      {
                        _memu && [_memu[0]].map((obj) => {
                          return (
                            <Menu.Item key={obj['code']} icon={obj['icon']}>
                              {obj['name']}
                            </Menu.Item>
                          );
                        })
                      }
                    </Menu> :
                    <Menu selectedKeys={keyPath} style={{ border: 'none' }} onSelect={this.onSelect} key={2}>
                      {
                        _memu && _memu.map((obj) => {
                          return (
                            <Menu.Item key={obj['code']} icon={obj['icon']}>
                              {obj['name']}
                            </Menu.Item>
                          );
                        })
                      }
                    </Menu>
                }

              </Col>
              <Col span={20} style={{ padding: 20 }} className='content'>
                {
                  key === '1' && <Base tipMsg={tipMsg} {...this.props} />
                }
                {
                  key === '2' && <PartyPosition tipMsg={tipMsg} {...this.props} />
                }
                {
                  key === '3' && <Administrative tipMsg={tipMsg} {...this.props} />
                }
                {
                  key === '4' && <Rewards tipMsg={tipMsg_reward} {...this.props} />
                }
                {
                  key === '5' && <Abroad tipMsg={tipMsg_abroad} {...this.props} callBack={()=>{
                    this.handleCancel()
                    this.props.getList &&  this.props.getList()
                  }} />
                }
                {
                  key === '6' && <Difficulty tipMsg={tipMsg} {...this.props} />
                }
                {
                  key === '7' && <Train tipMsg={tipMsg_train} {...this.props} />
                }
                {/*{*/}
                {/*  key === '8' && <Appraisal tipMsg={tipMsg} {...this.props} />*/}
                {/*}*/}
                {
                  key === '9' && <PartyFeePayment tipMsg={tipMsg} {...this.props} /> // 党费交纳情况
                }
              </Col>
            </Row>
          </div>
        </div>
      </Modal>
    );
  }
}
