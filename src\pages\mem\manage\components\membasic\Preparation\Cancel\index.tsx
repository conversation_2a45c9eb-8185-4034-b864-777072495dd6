import React, {Fragment} from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, Input, Modal } from 'antd';
import { findDictCodeName, unixMoment } from '@/utils/method';
import Tip from '@/components/Tip';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
const {TextArea} = Input;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state={
      visible:false,
      d28Value:'1'
    }
  }
  // componentDidMount(): void {
  //   this.props.dispatch({
  //     type:'commonDict/getDict',
  //     payload:{
  //       data:{
  //         dicName:'dict_d28',
  //       }
  //     }
  //   })
  // }
  submit=()=>{
    const {onClose} = this.props;
    const {memInfo} = this.state;
    const {memOrgCode,code} = memInfo;
    this.props.form.validateFieldsAndScroll(async(err, val) => {
      if (!err) {
        val['memOrgCode'] = memOrgCode;
        val['code'] = code;
        // console.log(val,'sssss');
        const res = await this.props.dispatch({
          type:'memToPositive/repealProlongationMem',
          payload:{
            data:{...val}
          }
        });
        const {code:resCode = 500} = res || {};
        if(resCode === 0){
          onClose && onClose();
          this.close();
          Tip.success('操作提示','操作成功');
        }
      }
    });
  };
  destroy=()=>{
    this.setState({
      memInfo:{}
    });
    // this.props.dispatch({
    //   type:'memLeaveOrg/clear',
    //   payload:{}
    // })
  };
  open=(val)=>{
    this.setState({
      visible:true,
      memInfo:val
    })
  };
  close=()=>{
    this.destroy();
    this.setState({visible:false})
  };
  render() {
    const {form,loading:{effects={}}={}} = this.props;
    const {getFieldDecorator} = form;
    const {visible,memInfo ={}} = this.state;
    return (
      <Fragment>
        <Modal
          title={'撤销延长'}
          destroyOnClose
          visible={visible}
          onOk={this.submit}
          onCancel={this.close}
          width={'600px'}
          confirmLoading={effects['memToPositive/repealProlongationMem']}
        >
          <Form>
            <FormItem
              label="人员姓名"
              {...formItemLayout}
            >
              {memInfo['name'] || ''}
            </FormItem>
            <FormItem
              label="延长到的时间"
              {...formItemLayout}
            >
              {_isNumber(memInfo['extendPreparDate']) ? moment(memInfo['extendPreparDate']).format('YYYY-MM-DD') : ''}
            </FormItem>
            <FormItem
              label="撤销原因"
              {...formItemLayout}
            >
              {getFieldDecorator('cancelExtendDateReason', {
                rules: [{ required: true, message: '撤销原因' }],
                // initialValue:d28Value,
              })(
                <TextArea rows={4} placeholder={'撤销原因'}/>
              )}
            </FormItem>
          </Form>
        </Modal>

      </Fragment>
    );
  }
}
export default Form.create<any>()(index);
