.box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .card {
    border: 1px solid #E0E2E2;
    border-radius:10px;
    height:160px;
    width: 19%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .main {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .bigText {
        color: #63C9F0;
        font-size:24px;
      }
    }
    .more {
      width: 100%;
      height:30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-top: 1px solid #E0E2E2;
      color: white;
      border-radius: 0px 0px 10px 10px;
      cursor: pointer;
    }
  }
}
