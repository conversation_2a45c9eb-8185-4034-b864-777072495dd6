import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {getList,leaveOrg,findByCode,delMemHistory,reconvertMemHistory} from '../services/memLeave';
import { getSession } from '@/utils/session';
import { changeListPayQuery } from '@/utils/method.js';  
const memAbroad = modelExtend(listPageModel,{
  namespace: "memLeaveOrg",
  state:{
    leaveInfo:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if( pathname === '/mem/history'){
          const org=getSession('org') || {};
          const defaultParas={
            pageNum:1,
            pageSize:10,
          };
          const dictData=['dict_d12','dict_d50','dict_d51'];
          for(let obj of dictData){
            dispatch({
              type:'commonDict/getDictTree',
              payload:{
                data:{
                  dicName:obj
                }
              }
            });
          }
          dispatch({
            type:'getList',
            payload:{
              data:{
                memOrgCode:org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put, select }) {
      const {filter,memName}=yield select(state=>state['memLeaveOrg']);
      payload['data']={...payload['data'],...filter,memName:memName};
      const {data={}} = yield call(getList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    // 新增编辑保存
    *save({ payload }, { call, put }) {
      let res = yield call(leaveOrg,payload);
      return res;
    },
    // 恢复党籍
    *reconvert({ payload }, { call, put }) {
      const res = yield call(reconvertMemHistory, payload);
      return res;
    },
    // 删除
    *del({ payload }, { call, put }) {
      const res = yield call(delMemHistory, payload);
      return res;
    },
    //清除
    *clear({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          leaveInfo:{},
        }
      })
    },
  }
});
export default memAbroad;
