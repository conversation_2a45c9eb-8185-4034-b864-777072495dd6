/**
 * 党组织管理-基本信息
 */
import React, { Fragment, useState, useImperativeHandle, useEffect, useRef } from 'react';
import '@ant-design/compatible/assets/index.css';
import { Button, Col, DatePicker, Input, Radio, Row, Switch, Tooltip, Select, InputNumber, Modal, Form, Checkbox, message, Space } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import OrgSelect from '@/components/OrgSelect';
import MemSelect from '@/components/MemSelect';
import SearchOrg from '@/components/SearchOrg';
import YN from '@/components/YesOrNoSelect';
import Tip from '@/components/Tip';
import moment from 'moment';
import { connect } from 'dva';
import { formLabel, formTip, isEmpty, jsonToTree, treeToList, findDictCodeName, getIdCardInfo } from '@/utils/method';
import Date from '@/components/Date';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import _isEqual from 'lodash/isEqual';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import _trim from 'lodash/trim';
import _uniqBy from 'lodash/uniqBy';
import _differenceBy from 'lodash/differenceBy';
import DictSelect from '@/components/DictSelect';
import { getSession, getLocalSession } from '@/utils/session';
import ListTable from 'src/components/ListTable';
import { inflowadd, findApprovalOrg, findTransferOrg } from '@/pages/flowMem/service';
import { findOrgByName, findFlowOrgByName } from '@/services';
import Item from 'antd/lib/list/Item';

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const formItemLayout3 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
};



const index = (props, ref) => {
  const [form] = Form.useForm();
  const { query = {} } = props.location || {};
  const [visible, setVisible] = useState(false);
  const [memInfo, setMemInfo] = useState({});
  const [Setp, setSetp] = useState(1);
  const org = props.org
  const [name, setName] = useState('')
  const [flowMemTypeDisabled, setFlowMemTypeDisabled] = useState<any>([])
  const [flowMemTypeValue, setFlowMemTypeValue] = useState<any>([])
  const flowMemTypeRef = useRef<any>(null);
  useEffect(() => {
    setName(props.org?.name)
    // console.log(getSession('org'), 'vvvvvvvvvvvvvvvvvvvvvvvvvv1111111111111111111');
  })

  useImperativeHandle(ref, () => ({
    open: (val) => {
      setVisible(true);
      form.resetFields();
      // console.log(val, props?.org?.name, org?.name, 'vvvvvvvvvvvvvvvvvvvvvvvvvv');
    },
  }));
  const validatorIdcard = async (rule, value, callback) => {
    console.log(value, 'valuevaluevalue');
    if (!value) {
      // callback('身份证必填');
      return Promise.reject('身份证必填');
    }
    if (value && value.length !== 18 && process.env.idCheck != 'false') {
      // callback('身份证应该为18位');
      return Promise.reject('身份证应该为18位');
    }
    if (getIdCardInfo(value) === 'Error') {
      // callback('身份证格式错误,请核对身份证图片');
      return Promise.reject('身份证格式错误,请核对身份证图片');
    } else {
      // let fieldValue = this.props.form.getFieldValue('memName');
      // let res =await geitCard({idCard:fieldValue,name:value});
      // callback()
      return Promise.resolve();
    }
  };
  const getIDinfo = (e) => {
    const { target: { value = '' } = {} } = e || {};
    let info = getIdCardInfo(value);
    if (value && info !== 'Error') {
      form.setFieldsValue({
        sexCode: info[2] === '女' ? '0' : '1',
        birthday: info[1] ? moment(info[1], 'YYYY-MM-DD') : undefined,
      });
    }
  };
  const onFinish = async (value) => {
    // if (typeof value['receive'] === 'object') {
    //   value['receiveCode'] = value['receive']['code'];
    //   value['receiveName'] = value['receive']['name'];
    // }
    // if (typeof value['orgFlow'] === 'object') {
    //   value['orgFlowCode'] = value['orgFlow']['code'];
    //   value['orgFlowName'] = value['orgFlow']['name'];
    //   value['orgFlowLevelCode'] = value['orgFlow']['orgCode'];
    //   value['receiveNodeCode'] = value['orgFlow']['nodeCode'];
    // }
    // if (typeof value['d06'] === 'object') {
    //   value['d06Code'] = value['d06']['key'];
    //   value['d06Name'] = value['d06']['name'];
    // }
    // if (typeof value['d07'] === 'object') {
    //   value['d07Code'] = value['d07']['key'];
    //   value['d07Name'] = value['d07']['name'];
    // }
    // if (typeof value['d08'] === 'object') {
    //   value['d08Code'] = value['d08']['key'];
    //   value['d08Name'] = value['d08']['name'];
    // }
    // if (typeof value['d09'] === 'object') {
    //   value['d09Code'] = value['d09']['key'];
    //   value['d09Name'] = value['d09']['name'];
    // }
    // if (typeof value['d19'] === 'object') {
    //   value['workPostCode'] = value['d19']['key'];
    //   value['workPostName'] = value['d19']['name'];
    // }
    // if (typeof value['d20'] === 'object') {
    //   value['d20Code'] = value['d20']['key'];
    //   value['d20Name'] = value['d20']['name'];
    // }
    // if (typeof value['d145'] === 'object') {
    //   value['d145Code'] = value['d145']['key'];
    //   value['d145Name'] = value['d145']['name'];
    // }
    if (typeof value['d146'] === 'object') {
      value['d146Code'] = value['d146']['key'];
      value['d146Name'] = value['d146']['name'];
    }
    // if (typeof value['mem'] === 'object') {
    //     value['memCode'] = value['mem'][0]['code']
    //     value['name'] = value['mem'][0]['name']
    //     value['id'] = value['mem'][0]['id']
    // }
    // if (value['sexCode']) {
    //   value['sexName'] = value['sexCode'] === '1' ? '男' : '女';
    // }

    value['ghanaDate'] = value['ghanaDate'] && moment(value['ghanaDate']).valueOf();
    value['outDate'] = value['outDate'] && moment(value['outDate']).valueOf();
    // value['formalDate'] = value['formalDate'] && moment(value['formalDate']).valueOf();
    // value['joinDate'] = value['joinDate'] && moment(value['joinDate']).valueOf();
    // value['birthday'] = value['birthday'] && moment(value['birthday']).valueOf();

    console.log(value, 'value');
    // delete value['receive'];
    // delete value['orgFlow'];
    // delete value['d06'];
    // delete value['d07'];
    // delete value['d08'];
    // delete value['d09'];
    // delete value['d19'];
    // delete value['d20'];
    // delete value['d145'];
    delete value['d146'];

    // 以前通过选择款选择的 流入地党支部
    // if (value['applyOrgFlowCode']) {
    //   value['applyOrgFlowLevelCode'] = value['applyOrgFlowCode'][0]['orgCode'];
    //   value['applyOrgFlowName'] = value['applyOrgFlowCode'][0]['name'];
    //   value['applyOrgFlowCode'] = value['applyOrgFlowCode'][0]['code'];
    // }

    // 现在直接从机构数那边拿过来赋值
    if (org) {
      value['applyOrgFlowLevelCode'] = org['orgCode'];
      value['applyOrgFlowName'] = org['name'];
      value['applyOrgFlowCode'] = org['code'];
    }
    if (value['orgFlowCode']) {
      value['orgFlowLevelCode'] = value['orgFlowCode']['orgCode'];
      value['orgFlowName'] = value['orgFlowCode']['name'];
      value['orgFLowDivision'] = value['orgFlowCode']['divisionCode'];
      value['receiveNodeCode'] = value['orgFlowCode']['nodeCode'];
      value['orgFlowCode'] = value['orgFlowCode']['code'];
    }
    if (value['flowMemTypeName']) {
      if (value['flowMemTypeName'].includes('农民工')) {
        const nmgIndex = value['flowMemTypeName'].indexOf('农民工')
        value['flowMemTypeName'].splice(nmgIndex, 1)
        value['flowMemTypeCode'].splice(nmgIndex, 1)
        value['lrdIsFarmer'] = 1
      }
      value['flowMemTypeName'] = value['flowMemTypeName'].join(',');
      value['flowMemTypeCode'] = value['flowMemTypeCode'].join(',');
    }
    console.log(value, 'resresresresres');
    const res = await inflowadd({ data: value });
    // console.log(res, 'resresresresres');
    if (res?.code == 0) {
      message.success('登记成功');
      handleCancel();
    }
  };
  const handleCancel = () => {
    form.resetFields();
    setSetp(1);
    setName('')
    setVisible(false);
    setFlowMemTypeDisabled([])
    props?.uplist && props.uplist();
  };



  //计算人员类别 不可以点击的子节点
  const setFlowMemTypeItemsDisabled = async (value) => {
    const flowMemType = form.getFieldValue('flowMemType')
    console.log(flowMemType, value, flowMemTypeDisabled, 'flowMemType');
    // 人员类别为必填项，字段如下，其中离退休人员、新就业群体、农民工可多选，
    // 未就业高校毕业生、其他为单选。
    if (value && value.length > 0) {
      // 判断是否有2和4，有则不能选择1
      // 2和4 为未就业高校毕业生、其他
      const filterData = value.filter((item) => item.key == 2 || item.key == 4)
      if (filterData.length > 0) {
        await form.setFieldsValue({
          flowMemTypeCode: [filterData[0].key],
          flowMemTypeName: [filterData[0].name]
        });
        if (flowMemTypeRef.current) {
          flowMemTypeRef.current.changeValue([filterData[0].key])
        }
        if (filterData[0].key == 2) {
          setFlowMemTypeDisabled(['1', '4', '5', '31', '32', '33', '34', '35', '36'])
          return
        }
        if (filterData[0].key == 4) {
          setFlowMemTypeDisabled(['1', '2', '5', '31', '32', '33', '34', '35', '36'])
          return
        }
      } else {
        setFlowMemTypeDisabled([])
        await form.setFieldsValue({
          flowMemTypeCode: value.map((item) => item.key),
          flowMemTypeName: value.map((item) => item.name)
        });
      }
    } else {
      setFlowMemTypeDisabled([])
      await form.setFieldsValue({
        flowMemTypeCode: undefined,
        flowMemTypeName: undefined
      });
    }
  }



  //流入登记改版
  return (
    <>
      <Modal
        title="流入登记"
        destroyOnClose
        visible={visible}
        onOk={() => {
          setVisible(false);
        }}
        onCancel={handleCancel}
        footer={false}
        width={'1300px'}
      >
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Row>
            <Col span={24}>
              <FormItem labelCol={{ span: 4 }} wrapperCol={{ span: 19 }} label={'党员组织关系所在党组织'} name={'orgFlowCode'} rules={[{ required: true, message: '请选择' }]}>
                <SearchOrg ohterAction={findTransferOrg} params={{ pageNum: 1, pageSize: 999 }} backType="object" />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem label={'流动党员姓名'} name={'name'} rules={[{ required: true, message: '请填写姓名' }]}>
                <Input placeholder="请填写姓名" />
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label={'流动党员身份证号码'} name={'idcard'} rules={[
                { required: true, message: '' },
                //  { validator: validatorIdcard }
              ]}>
                <Input placeholder="请输入身份证" onBlur={getIDinfo} />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem name={'frequentlyPhone'} label={'党员在流入地常用联系方式'} rules={[{ required: true, message: '请填写党员在流入地常用联系方式' }]}>
                <Input placeholder="请输入党员在流入地常用联系方式" />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem name={'flowConnectionName'} label={'流入地联系人'} rules={[{ required: true, message: '请填写流入地联系人' }]}>
                <Input placeholder="请输入流入地联系人" />
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem name={'flowConnection'} label={'流入地联系方式'} rules={[{ required: true, message: '请填写流入地联系方式' }]}>
                <Input placeholder="请输入流入地联系方式" />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem name={'d146'} label={'流动原因'} rules={[{ required: true, message: '请选择流动原因' }]}>
                <DictSelect codeType="dict_d204" initValue={memInfo['d146Code']} backType={'object'} />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <FormItem
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 19 }}
                name={'applyOrgFlowCode'}
                label={'流入地党支部'}
                rules={[{ required: true, message: '请选择流入地党支部' }]}
                initialValue={getSession('org').name}
              >

                {/* <OrgSelect orgTypeList={['3', '4']} placeholder={'请选择流入地党支部'} otherListQuery={{}} /> */}
                {/* 直接将机构数上的组织选过来 */}
                <Input placeholder="请输入流入地联系人" disabled={true} />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <FormItem
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 19 }}
                name={'outInstructions'}
                label={'外出地点补充说明'}
                rules={[{ required: true, message: '请填写外出地点补充说明' }]}
              >
                <Input.TextArea placeholder={'请输入外出地点补充说明'} />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem name={'ghanaDate'} label={'党费交纳：交到流出地至 - '} rules={[{ required: false, message: '' }]}>
                <Date />
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem name={'outDate'} label={'外出日期'} rules={[{ required: true, message: '请填写' }]}>
                <Date />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem name={'flowMemTypeCode'} label={'人员类别'} rules={[{ required: true, message: '请选择人员类别' }]}>
                {(() => {
                  return (
                    <DictTreeSelect
                      ref={flowMemTypeRef}
                      treeCheckable={true}
                      backType={'object'}
                      initValue={form.getFieldValue('flowMemTypeCode')}
                      codeType={'dict_flow_mem_type'}
                      placeholder={'请选择人员类别'}
                      parentDisable={true}
                      itemsDisabled={flowMemTypeDisabled}
                      onChange={(value) => setFlowMemTypeItemsDisabled(value)}
                    />
                  )
                })()}
              </FormItem>
            </Col>
            <Col span={12}>
              {(() => {
                const flowMemTypeCode = form.getFieldValue('flowMemTypeCode')
                if (flowMemTypeCode && flowMemTypeCode.includes('4')) {
                  return <FormItem name={'flowMemTypeRemark'} label={'人员类型备注'} rules={[{ required: true, message: '请输入人员类型备注' }]}>
                    <Input placeholder="请输入人员类型备注" />
                  </FormItem>
                }
              })()}
              {(() => {
                const flowMemTypeCode = form.getFieldValue('flowMemTypeCode')
                if (flowMemTypeCode && flowMemTypeCode.includes('36')) {
                  return <FormItem name={'flowMemTypeNewRemark'} label={'人员类型新就业备注'} rules={[{ required: true, message: '请输入人员类型备注' }]}>
                    <Input placeholder="请输入人员类型新就业备注" />
                  </FormItem>
                }
              })()}
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem hidden name={'flowMemTypeName'} label={''}></FormItem>
            </Col>
          </Row>
          <div style={{ width: '100%', textAlign: 'center' }}>
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  handleCancel();
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  form.submit();
                  console.log(form.getFieldsValue(), 'data');
                }}
              >
                保存
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </>
  );


};
export default React.forwardRef(index);
