/*
 * 历史党员
 * */
import React, { Fragment } from 'react';
import { Divider, <PERSON>con<PERSON>rm, Ta<PERSON>, Button, Input, Modal } from 'antd';
import ListTable from 'src/components/ListTable';
import NowOrg from 'src/components/NowOrg';
import WhiteSpace from '@/components/WhiteSpace';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import { withContext } from 'src/utils/global.jsx';
import { connect } from 'dva';
import moment from 'moment';
import Tip from '@/components/Tip';
import Reconvert from '../reconvert';
import CancelReason from '../canncel';
import { _history as router } from '@/utils/method';
import { getSession } from '@/utils/session';
import LeaveOrg from '../../../manage/components/membasic/LeaveOrg';
import AddEdit from '../../../manage/components/membasic/AddEdit';
import qs from 'qs';
import RuiFilter from '@/components/RuiFilter';
import Lost from '../lost';
import { setListHeight } from '@/utils/method';
const { Search } = Input;
@withContext
@connect(({ commonDict, loading, memLeaveOrg,memBasic }) => ({ commonDict, loading, memLeaveOrg,memBasic }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      filterHeight: 100,
      modalVisible: false,
    };
  }
  componentDidMount(): void {
    setListHeight(this);
  }

  // 筛选
  filterChange = (val) => {
    this.props.dispatch({
      type: 'memLeaveOrg/updateState',
      payload: {
        filter: val,
      },
    });
    this.setState(
      {
        filter: val,
      },
      () => {
        const { pagination = {} } = this.props.memLeaveOrg;
        const { query } = this.props.location;
        router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`);
      },
    );
  };
  componentWillUnmount() {
    this.props.dispatch({
      type: 'memLeaveOrg/updateState',
      payload: {
        filter: {},
        memName: undefined,
      },
    });
  }
  // 分页
  onPageChange = (page, pageSize) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`);
  };
  action = (val?: object) => {
    const { pagination = {} } = this.props.memLeaveOrg;
    const { current, pageSize } = pagination;
    const { search, filter } = this.state;
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memLeaveOrg/getList',
      payload: {
        data: {
          memOrgCode: org['orgCode'],
          pageNum: current,
          pageSize,
          ...search,
          ...filter,
          ...val,
        },
      },
    });
  };
  search = (value) => {
    this.props.dispatch({
      type: 'memLeaveOrg/updateState',
      payload: {
        memName: value,
      },
    });
    this.setState(
      {
        search: { memName: value },
      },
      () => {
        const { pagination = {} } = this.props.memLeaveOrg;
        const { query } = this.props.location;
        router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`);
      },
    );
  };
  searchClear = (e) => {
    this.setState({
      searchVal: e.target.value
    })
    if (!e.target.value) {
      this.props.dispatch({
        type: 'memLeaveOrg/updateState',
        payload: {
          memName: undefined,
        },
      });
      this.action();
    }
  };
  // 人员编辑
  addOrEdit = async (record) => {
    // this['LeaveOrg'].destroy();
    // if (record) {
    //   await this.props.dispatch({
    //     type: 'memLeaveOrg/updateState',
    //     payload: {
    //       leaveInfo: record,
    //     },
    //   });
    // }
    // this['LeaveOrg'].open(record);
    AddEdit['WrappedComponent'].show();
    if(record && record['code']){
      await this.props.dispatch({
        type:'memBasic/findHistoryMem',
        payload:{
          code:record['code']
        }
      })
    }
  };

  // 人员删除
  del = async (record) => {
    this['CancelReason'].open(record);
  };
  open = () => {};
  canncel = () => {
    this.action();
  };
  destroy = () => {
    this.props.dispatch({
      type: 'memLeaveOrg/updateState', //重置model
      payload: { leaveInfo: {} },
    });
  };
  orgLevelClose = () => {
    this.action();
  };
  reconvert = (record) => {
    this['Reconvert'].open(record);
  };
  render(): React.ReactNode {
    const { memLeaveOrg = {}, loading: { effects = {} } = {}, commonDict } = this.props;
    const { list, pagination } = memLeaveOrg;
    const { current, pageSize } = pagination;
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 60,
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 100,
      },
      {
        title: '性别',
        dataIndex: 'sexCode',
        width: 100,
        render: (text) => {
          return <span> {text === '1' ? '男' : '女'} </span>;
        },
      },
      {
        title: '离开时所在组织',
        dataIndex: 'orgName',
        width: 260,
      },
      {
        title: '离开类型',
        width: 100,
        dataIndex: 'd12Name',
      },
      {
        title: '出党原因',
        width: 120,
        dataIndex: 'd50Name',
      },
      {
        // title: '职务级别',
        title: '转接类型',
        width: 160,
        dataIndex: 'd51Name',
      },
      {
        title: '离开时间',
        width: 160,
        dataIndex: 'leaveOrgDate',
        render: (text, record) => {
          return (
            <div>
              {/* {!_isEmpty(text) ? moment(text).format('YYYY-MM-DD') : ''} */}
              {_isNumber(text) ? moment(text).format('YYYY-MM-DD') : ''}
            </div>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 220,
        render: (text, record) => {
          return (
            <span>
              <a onClick={() => this.addOrEdit(record)}>查看</a>
              {record['isReconvert'] === 1 && (
                <Fragment>
                  <Divider type="vertical" />
                  <a onClick={() => this.reconvert(record)}>恢复党籍</a>
                </Fragment>
              )}
              {record['d12Code'] == 32 && (
                <Fragment>
                  <Divider type="vertical" />
                  <a onClick={() => this['Lost'].open(record)}>脱党处理</a>
                </Fragment>
              )}
              <Divider type="vertical" />
              <Popconfirm title="确定要撤销吗？" onConfirm={() => this.del(record)}>
                <a className={'del'}>撤销</a>
              </Popconfirm>
            </span>
          );
        },
      },
    ];
    const filterData = [
      {
        key: 'd12CodeList',
        name: '离开类型',
        value: commonDict['dict_d12_tree'],
      },
      {
        key: 'd50CodeList',
        name: '出党原因',
        value: commonDict['dict_d50_tree'],
      },
      {
        key: 'd51CodeList',
        name: '转接类型',
        // value: commonDict['dict_d51_tree'],
        value: [{key:'跨省（系统）组织关系转出', name:'跨省（系统）组织关系转出'}]
      },
    ];
    return (
      <Fragment>
        <NowOrg
          extra={
            <Fragment>
              <Search
                style={{ width: 200, marginLeft: 16 }}
                onSearch={this.search}
                onChange={this.searchClear}
                placeholder={'请输入检索关键词'}
              />
            </Fragment>
          }
        />
        <RuiFilter
          data={filterData}
          openCloseChange={() => setListHeight(this, 20)}
          onChange={this.filterChange}
        />
        <WhiteSpace />
        <ListTable
          scroll={{ y: this.state.filterHeight }}
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={this.onPageChange}
        />
        <LeaveOrg
          wrappedComponentRef={(e) => (this['LeaveOrg'] = e)}
          onClose={this.orgLevelClose}
        />
        <Lost ref={(e) => (this['Lost'] = e)} onOK={this.action} />
        <Reconvert
          wrappedComponentRef={(e) => (this['Reconvert'] = e)}
          {...this.props}
          onClose={this.action}
        />
        <CancelReason
          wrappedComponentRef={(e) => (this['CancelReason'] = e)}
          {...this.props}
          onClose={this.action}
        />
        <AddEdit getList={this.action}/>
      </Fragment>
    );
  }
}
