/**
 * 继续流动
 */

import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input, Radio, DatePicker } from 'antd';
import qs from 'qs';
import { connect } from 'dva';
import { _history as router } from '@/utils/method';
import { isEmpty } from '@/utils/method';
import { getSession } from '@/utils/session';
import DictArea from '@/components/DictArea';
import DictSelect from '@/components/DictSelect';
import { root } from '@/common/config';
import OrgSelect from '@/components/OrgSelect';
import styles from './index.less';
import moment from 'moment';
import DictTreeSelect from '@/components/DictTreeSelect';
import Date from '@/components/Date';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 0 },
    sm: { span: 0 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
};
const menuData = [
  {
    code: '1',
    name: '基本信息',
    icon: 'star',
  },
  {
    code: '2',
    name: '',
    icon: 'qrcode',
  },
];
@connect(
  ({ unit, commonDict, loading }) => ({
    unit,
    commonDict,
    loading: loading.effects['unit/getList'],
  }),
  undefined,
  undefined,
  { forwardRef: true },
)
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    let obj = menuData[0];
    this.state = {
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    };
  }

  showModal = () => {
    const { data: { id = '' } = {}, keyword = '' } = this.props;
    let org = getSession('org') || {};
    this.setState(
      {
        visible: true,
      },
      () => {
        this.selectMem();
      },
    );
  };
  selectMem = () => {
    const { data } = this.props;
    this.props
      .dispatch({
        type: 'flowMem/detail',
        payload: {
          code: data['code'],
        },
      })
      .then((res) => {
        if (res.code === 0) {
          this.setState({
            detailInfo: res['data'],
          });
        }
      });
  };
  handleOk = () => {
    this.props.form.validateFieldsAndScroll(async (errors, values) => {
      if (errors) {
        return;
      }
      const { memInfo, addr, detailInfo } = this.state;
      const { onChange, data } = this.props;

      const {
        account0,
        account2,
        outflowAreaId,
        outflowAreaName,
        outflowOrgCode,
        outflowDate,
        outflowReasonCode,
        isExplicitInflowOrg,
        outflowUnitTypeCode,
        outflowOrgLinkman,
        outflowOrgPhone,
        isHold,
        outflowReason,
        inflowOrgName1,
        ...val
      } = values;
      let vall = {
        code: data['code'],
        memCode: isEmpty(detailInfo) ? '' : detailInfo['memCode'],
        memOrgCode: isEmpty(detailInfo) ? '' : detailInfo['memOrgCode'],
        memOrgOrgCode: isEmpty(detailInfo) ? '' : detailInfo['memOrgOrgCode'],
        isProvOut: isEmpty(account2) ? '' : account2['id'],
        isProvOutName: isEmpty(account2) ? '' : account2['name'],
        outflowTypeCode: isEmpty(account2) ? '' : account2['id'],
        outflowTypeName: isEmpty(account2) ? '' : account2['name'],
        outflowAreaId: isEmpty(outflowAreaId) ? '' : outflowAreaId,
        outflowAreaName: isEmpty(outflowAreaName) ? '' : outflowAreaName,
        outflowOrgCode: isEmpty(outflowOrgCode) ? '' : outflowOrgCode[0]['code'],
        outflowOrgName: isEmpty(outflowOrgCode) ? inflowOrgName1 : outflowOrgCode[0]['name'],
        outflowOrgOrgCode: isEmpty(outflowOrgCode) ? '' : outflowOrgCode[0]['orgCode'],
        outflowReasonCode: isEmpty(outflowReasonCode) ? '' : outflowReasonCode['id'],
        outflowReasonName: isEmpty(outflowReasonCode) ? '' : outflowReasonCode['name'],
        outflowDate: isEmpty(outflowDate) ? '' : moment(outflowDate).valueOf(),
        isExplicitInflowOrg: isEmpty(isExplicitInflowOrg) ? null : isExplicitInflowOrg,
        outflowUnitTypeCode: isEmpty(outflowUnitTypeCode) ? '' : outflowUnitTypeCode['id'],
        outflowUnitTypeName: isEmpty(outflowUnitTypeCode) ? '' : outflowUnitTypeCode['name'],
        outflowOrgLinkman: isEmpty(outflowOrgLinkman) ? '' : outflowOrgLinkman,
        outflowOrgPhone: isEmpty(outflowOrgPhone) ? '' : outflowOrgPhone,
        isHold: isEmpty(isHold) ? null : isHold,
        outflowReason: isEmpty(outflowReason) ? '' : outflowReason,
      };

      for (let o in vall) {
        if (isEmpty(vall[o])) {
          delete vall[o];
        }
      }
      this.props
        .dispatch({
          type: 'flowMem/continueFlow',
          payload: {
            data: {
              ...vall,
            },
          },
        })
        .then((res) => {
          if (res.code === 0) {
            onChange && onChange(true);
            this.handleCancel();
          }
        });
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false,
    });
    this.props.form.resetFields();
  };
  open = () => {
    this.setState({
      visible: true,
    });
  };
  destroy = () => {
    let obj = menuData[0];
    this.setState({
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    });
    this.props.dispatch({
      //重置model
      type: 'unit/updateState',
      payload: {
        basicInfo: {},
      },
    });
  };
  onSelect = (item) => {
    const { key, keyPath } = item;
    const selected = menuData.find((obj) => obj['code'] === key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  outAddr = (val, obj) => {
    this.setState({ addr: obj });
    this.props.form.setFieldsValue({
      outflowAreaName: obj.name,
    });
  };
  determine = (e) => {
    if (e.target.value === 0) {
      this.setState({ determine: e.target.value, showOrgName: '未明确' });
    } else {
      this.setState({ determine: e.target.value, showOrgName: '' });
    }
  };
  disabledDate = (current) => {
    return current && current > moment().endOf('day');
  };
  showOrg = (e) => {
    this.setState({ showOrgName: e[0]['name'] });
  };
  showOrg1 = () => {
    let value = this.props.form.getFieldValue('inflowOrgName1');
    this.setState({ showOrgName: value });
  };
  onPageChange = (page, pageSize) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`);
  };
  render() {
    const { visible, addr = {}, determine, detailInfo = {}, showOrgName = '' } = this.state;
    const { children, data = {} } = this.props;
    const { getFieldDecorator } = this.props.form;
    let a = addr['id'] === root['areaCode'] ? ['1'] : ['2', '3'];
    return (
      <React.Fragment>
        {children
          ? React.cloneElement(children as any, {
              onClick: this.showModal,
              key: 'container',
            })
          : null}
        <Modal
          title="继续流动"
          className="out_Modal"
          destroyOnClose
          closable={false}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
        >
          <div className="container">
            <Form {...formItemLayout}>
              <FormItem {...formItemLayout1}>
                {getFieldDecorator('account0')(
                  <div className={styles.head}>
                    <div>
                      <p>党员:{detailInfo['memName']}</p>
                      <p>党员类型:{detailInfo['memTypeName']}</p>
                    </div>
                    <div>
                      <p>
                        <span>从:</span>
                        {detailInfo['memOrgName']}
                      </p>
                      <p>
                        <span>到:</span>
                        {showOrgName}
                      </p>
                    </div>
                  </div>,
                )}
              </FormItem>
              <FormItem label={'流动去向'}>
                {getFieldDecorator('outflowAreaId', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(<DictArea onChange={this.outAddr} />)}
                {getFieldDecorator('outflowAreaName')(<Input style={{ display: 'none' }} />)}
              </FormItem>

              <FormItem label={'流动类型'}>
                {getFieldDecorator('account2', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(<DictSelect codeType={'dict_d34'} backType={'object'} noDraw={a} />)}
              </FormItem>
              <FormItem label={'是否明确流入党组织'}>
                {getFieldDecorator('isExplicitInflowOrg', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <RadioGroup onChange={this.determine}>
                    <Radio value={1}>已明确</Radio>
                    <Radio value={0}>未明确</Radio>
                  </RadioGroup>,
                )}
              </FormItem>
              {addr['id'] === root['areaCode'] && determine === 1 && (
                <FormItem label={'流入党支部'}>
                  {getFieldDecorator('outflowOrgCode', {
                    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                    rules: [
                      { required: true, message: '必填!' },
                      // { validator: this.validFunction }
                    ],
                  })(<OrgSelect onChange={this.showOrg} />)}
                </FormItem>
              )}
              {!isEmpty(addr['id']) && addr['id'] !== root['areaCode'] && determine === 1 && (
                <React.Fragment>
                  <FormItem label={'流入党支部'}>
                    {getFieldDecorator('inflowOrgName1', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        // { validator: this.validFunction }
                      ],
                    })(<Input placeholder={'请输入'} onBlur={this.showOrg1} />)}
                  </FormItem>
                  <FormItem label={'流入单位类型'}>
                    {getFieldDecorator('outflowUnitTypeCode', {
                      // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                      rules: [
                        // { validator: this.validFunction }
                      ],
                    })(
                      <DictTreeSelect
                        backType={'object'}
                        // initValue={basicInfo['d04Code']}
                        codeType={'dict_d04'}
                        placeholder={'单位类别'}
                        parentDisable={true}
                      />,
                      // <DictSelect codeType={'dict_d04'} backType={'object'}/>
                    )}
                  </FormItem>
                </React.Fragment>
              )}
              <FormItem label={'外出原因类型'}>
                {getFieldDecorator('outflowReasonCode', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(<DictSelect codeType={'dict_d41'} backType={'object'} />)}
              </FormItem>
              <FormItem label={'流出党组织联系人'}>
                {getFieldDecorator('outflowOrgLinkman', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(<Input placeholder="请输入" />)}
              </FormItem>
              <FormItem label={'流出党组织联系方式'}>
                {getFieldDecorator('outflowOrgPhone', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(<Input placeholder="流出党组织联系方式" />)}
              </FormItem>
              <FormItem label={'流动原因'}>
                {getFieldDecorator('outflowReason', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  // rules: [
                  //   { required: true, message: '必填!' },
                  //   { validator: this.validFunction }
                  // ],
                })(<Input placeholder="流动原因" />)}
              </FormItem>
              <FormItem label={'流动党员活动证'}>
                {getFieldDecorator('isHold', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <RadioGroup>
                    <Radio value={1}>已发放</Radio>
                    <Radio value={0}>未发放</Radio>
                  </RadioGroup>,
                )}
              </FormItem>
              <FormItem label={'流入日期'}>
                {getFieldDecorator('outflowDate', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  // rules: [
                  //   { required: true, message: '必填!' },
                  //   { validator: this.validFunction }
                  // ],
                  // <DatePicker style={{width:'100%'}} disabledDate={this.disabledDate}/>
                })(<Date disabledDate={this.disabledDate} />)}
              </FormItem>
            </Form>
          </div>
        </Modal>
      </React.Fragment>
    );
  }
}
export default Form.create()(index);
