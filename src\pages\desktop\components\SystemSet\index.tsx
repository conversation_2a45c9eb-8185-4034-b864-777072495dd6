import React, { FC, Fragment, useEffect, useImperativeHandle, useState, useRef } from 'react';
import {
  Form,
  Input,
  Modal,
  Table,
  Button,
  Select,
  Switch,
  Row,
  Col,
  TreeSelect,
  Radio,
  Space,
} from 'antd';
import ListTable from '@/components/ListTable';
import { selfRowSelection, jsonToTree } from '@/utils/method.js';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import request from '@/utils/request';
import qs from 'qs';
import moment from 'moment';
import Tip from '@/components/Tip';
import isEmpty from 'lodash/isEmpty';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import UnitTree from '@/pages/user/user/unitTree';
import DictTreeSelect from '@/components/DictTreeSelect';
import { getSession } from '@/utils/session';
import {
  exist,
  findValidUser,
  unlock,
  lock,
  userAdd,
  edit,
  updateMS,
  getListAndValid,
  userAddMpKey
} from '@/pages/user/user/service';
import { connect } from 'dva';
import { SKFKEY } from "@/utils/fiseckey"

const Search = Input.Search;
const TreeNode = TreeSelect.TreeNode;
const systemsList = [
  { key: '1', name: '综合党务管理系统' },
  { key: '2', name: '村社区管理系统' },
  { key: '3', name: '清镇市大屏系统-不忘初心' },
  {
    key: '4',
    name: '清镇市大屏系统-时代答卷',
  },
  {
    key: '5',
    name: '清镇市大屏系统-阶梯教室',
  },
];

const ModalAdd = React.forwardRef((props: any, ref) => {
  const formItemLayout = {
    labelCol: {
      xs: { span: 28 },
      sm: { span: 4 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 20 },
    },
  };

  const { close } = props;

  const [form] = Form.useForm();
  const [query, setQurey] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [keys, setKeys] = useState<any>([]);
  const [unitData, setUnitData] = useState<any>([]);
  const [roleVal, setRoleVal] = useState<any>();
  const [roleList, setRoleList] = useState<any>([]);
  const [timeKey, setTimeKey] = useState<any>(+new Date());
  const [basicInfo, setBasicInfo] = useState({});
  const [timer, setTimer] = useState<any>(undefined);
  const [mpKeyList, setMpKeyList] = useState<any>([]);

  useImperativeHandle(ref, () => ({
    open: (query: any) => {
      clearInterval(timer)
      getRoleList();
      setVisible(true);
      setQurey(query);
      if (!_isEmpty(query)) {
        let obj = {
          ...query,
          managementSystem: query?.managementSystem ? query?.managementSystem.split(',') : ['1'],
        };
        form.setFieldsValue({
          ...obj,
        });
        setBasicInfo(obj);
      }
      getIntervalKey()
    },
  }));

  const getRoleList = async () => {
    const { code = 500, data: { list = [] } = {} } = await getListAndValid();
    if (code === 0) {
      setRoleList(jsonToTree(list, 'parent_id', 'id', isEmpty(list) ? '' : list[0]['parent_id']));
    }
  };

  const handleCancel = () => {
    setVisible(false);
    setQurey({});
    form.resetFields();
    clearInterval(timer)
    setTimer(undefined)
  };

  const validFunction = (rule, value, callback) => {
    if (!isEmpty(value)) {
      switch (rule.field) {
        case 'account':
          if (!/^[0-9a-zA-Z]+$/.test(value)) {
            return callback('登录名只能填写数字和字母组合');
          } else if (/\s+/g.test(value)) {
            return callback('登录名不能包含空格');
          }
          break;
        case 'name':
          if (value.length > 5) {
            return callback('姓名不能超过5个字');
          } else if (/\s+/g.test(value)) {
            return callback('姓名不能包含空格');
          }
          break;
        case 'password':
          if (value.length < 8) {
            return callback('密码长度不足');
          } else if (!/^(?=.*\d)(?=.*[a-zA-Z])[\da-zA-Z~!@#$%^&*]{8,16}$/.test(value)) {
            return callback('格式有误');
          } else if (/\s+/g.test(value)) {
            return callback('密码不能包含空格');
          }
          break;
        case 'memCode':
          if (isEmpty(value)) {
            return callback('请选择');
          }
          break;
      }
    }
    callback();
  };

  const findAccount = () => {
    let value = form.getFieldValue('account');
    findUserByAccount(value);
  };

  const findUserByAccount = async (value) => {
    if (!_isEmpty(value)) {
      const res = await exist({ data: { account: value } });
      if (res.code === 0) {
        Tip.info('操作提示', '用户名可以使用');
      } else {
        form.setFields([
          {
            name: 'account',
            value: value,
            errors: [`账号不能使用`],
          },
        ]);
      }
    }
  };

  const onFinish = async (e) => {
    console.log(e);
    let url = userAdd;
    if (!_isEmpty(query)) {
      url = updateMS;
    }
    let orgID = '';
    let orgCODE = '';
    let manages = unitData.map((item, index) => {
      let obj = {
        managerOrgCode: item.orgCode,
        managerOrgId: item.code,
        roleId: e[`roleId${index + 1}`],
        isDefault: keys[index]['checked'] ? 1 : 0,
      };
      if (keys[index]['checked']) {
        orgID = item.code;
        orgCODE = item.orgCode;
      }
      return obj;
    });
    if (isEmpty(manages)) {
      return Tip.warning('管理单位不能为空', '');
    }
    const { account, password, ukey } = e;
    let data = {
      account,
      password,
      manages,
      managementSystem: e.managementSystem.toString(),
      id: query?.id,
    };
    setConfirmLoading(true);
    const res = await url({
      data: data,
    });
    // 绑定密评的key到用户头上
    let mcode = 0
    if (ukey) {
      const { code } = await userAddMpKey({
        id: query?.id,
        ukey
      })
      mcode = code
    }
    setConfirmLoading(false);
    if (res.code == 0 && mcode == 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      close && close();
    }
  };

  // 绑定密评的key到用户头上
  // const setUserMpKey = async (param) => {
  //   const { id,ukey } = param

  // } 

  const checkTree = (v, index) => {
    // let { unitData, keys } = this.state;
    v.index = index;
    if (isEmpty(unitData[index])) {
      unitData.push(v);
    } else {
      unitData.splice(index, 1, v);
    }
    if (!keys.map((i) => i.orgName).includes(v.name)) {
      keys[index].orgName = v.name;
      // if (keys.map(i => i.checked).includes(true)) {
      //   keys[index].checked = false;
      // } else {
      //   keys[0].checked = true
      // }
    } else {
      Tip.info('操作提示', '已存在管理组织');
      form.resetFields([`managerOrgCode${v.index + 1}`]);
    }
    // this.setState({ unitData, keys })
    setKeys(keys);
    setTimeKey(+new Date());
    setUnitData(unitData);
  };

  const roleOnchange = (v, index) => {
    // let value=this.props.form.getFieldValue('account');
    // let { keys, unitData } = this.state;
    unitData[index]['roleId'] = v;
    keys[index]['roleId'] = v;
    // this.setState({ roleVal: v, unitData, keys })
    setKeys(keys);
    setUnitData(unitData);
    setRoleVal(roleVal);
  };

  const renderTreeNodes = (data) =>
    data.map((item) => {
      if (item.children) {
        return (
          <TreeNode title={item['name']} key={item['id']} value={item['id']}>
            {renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode title={item['name']} key={item['id']} value={item['id']} />;
    });

  const minus = (item, indexs) => {
    if (keys.length < 2) {
      Tip.warning('已经是最后一个了', '');
    } else {
      let key = keys.filter((it, index) => index !== indexs);
      let unitDatas = unitData.filter((it, index) => index !== indexs);
      if (!key.map((i) => i.checked).includes(true)) {
        key[0].checked = true;
      }
      setKeys(key);
      setUnitData(unitDatas);
      form.resetFields([`managerOrgCode${indexs + 1}`, `roleId${indexs + 1}`]);
    }
  };

  const ismr = (e, v, k) => {
    v.checked = e.target.checked;
    // let { keys } = this.state;
    keys.map((item, index) => {
      if (item.id === v.id) {
        item.checked = e.target.checked;
      } else {
        item.checked = false;
      }
    });

    // this.setState({ keys })
    setKeys(keys);
    setTimeKey(+new Date());
  };

  useEffect(() => {
    let org = getSession('org') || {};
    if (!_isEmpty(query)) {
      console.log(456);
      const { manages = [] } = query;

      let unitData = manages.map((item, index) => {
        let obj = {
          code: item['managerOrgId'],
          name: item['managerOrgName'],
          orgCode: item['managerOrgCode'],
          shortName: item['managerOrgName'],
          roleId: item['roleId'],
        };
        return obj;
      });
      let keys = manages.map((item, index) => {
        let obj = {
          id: index + 1,
          checked: item['isDefault'] === 1,
          orgName: item['managerOrgName'],
          roleId: item['roleId'],
          roleName: item['roleName'],
        };
        return obj;
      });

      if (!_isEmpty(roleList)) {
        console.log(46789);
        unitData.forEach((j) => {
          if (!roleList.map((i) => i['id']).includes(j['roleId'])) {
            j['roleId'] = -1;
          }
        });
      }

      setUnitData(unitData);
      setKeys(keys);
    } else {
      console.log(123);
      if (_isEmpty(roleList)) {
        return;
      }
      let findDefault = roleList.find((it) => it.name == '默认角色') || {};
      let obj = {
        code: org['code'],
        name: org['name'],
        orgCode: org['orgCode'],
        roleId: _get(findDefault, 'id', undefined),
        shortName: org['name'],
      };
      setUnitData([obj]);
      let keys = [
        {
          checked: true,
          id: 1,
          orgName: org['name'],
          roleId: _get(findDefault, 'id', undefined),
          roleName: _get(findDefault, 'name', undefined),
        },
      ];
      setKeys(keys);
    }
  }, [JSON.stringify(roleList), JSON.stringify(query)]);

  //新增定时任务获取密评的key  通过u盘插入获取
  const getIntervalKey = () => {
    let mpKey = SKFKEY.SKF_EnumDev()
    let mpKeyList: any = regexMatch(mpKey)
    const timer = setInterval(() => {
      mpKey = SKFKEY.SKF_EnumDev()
      mpKeyList = regexMatch(mpKey) // 正则匹配 序列号
      setMpKeyList(mpKeyList)
    }, 5000)
    setMpKeyList(mpKeyList)
    setTimer(timer)
  }

  //正则匹配函数
  const regexMatch = (str) => {
    const regex = /[A-Z]\d{10}[A-Z]\d{4}/g
    const data = [...str.matchAll(regex)]
    console.log("🚀 ~ index ~ data:", data)
    return data
  }

  let formItems =
    keys.length &&
    keys.map((item: any, index) => {
      if (!item.roleId) {
        return;
      }
      return (
        <React.Fragment key={index}>
          <Row gutter={18} key={timeKey}>
            <Col span={2}>{index + 1}</Col>
            <Col span={5}>
              <Form.Item
                name={`managerOrgCode${index + 1}`}
                initialValue={isEmpty(item['orgName']) ? null : item['orgName']}
              >
                <UnitTree onSelect={(v) => checkTree(v, index)} dataInfo={{}}>
                  <Input
                    value={isEmpty(item['orgName']) ? null : item['orgName']}
                    placeholder="请选择管理组织"
                    readOnly
                  />
                </UnitTree>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name={'ukey'} initialValue={item['ukey']}>
                <Select placeholder="请选择序列码" onChange={() => { }}>
                  {mpKeyList.map((item) => {
                    return <Select.Option key={item[0]} value={item[0]}>{item[0]}</Select.Option>
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name={`roleId${index + 1}`} initialValue={item['roleId']}>
                <TreeSelect
                  // value={this.state['roleVal']}
                  dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                  placeholder="请选择角色"
                  onChange={(e) => roleOnchange(e, index)}
                >
                  {roleList && renderTreeNodes(roleList)}
                </TreeSelect>
              </Form.Item>
            </Col>

            <Col span={2} style={{ textAlign: 'center' }}>
              <Radio onChange={(e) => ismr(e, item, index)} checked={item.checked} />
            </Col>
            <Col span={2} style={{ marginLeft: '20px' }}>
              <Button
                onClick={() => minus(item, index)}
                size={'small'}
                icon={<DeleteOutlined />}
                className="del"
              >
                删除
              </Button>
              {/*<Icon onClick={()=>this.minus(index)} type="delete" style={{color:'red'}}/>*/}
            </Col>
          </Row>
        </React.Fragment>
      );
    });

  return (
    <Modal
      title={!_isEmpty(query) ? '编辑' : '新增'}
      visible={visible}
      onOk={form.submit}
      onCancel={handleCancel}
      maskClosable={false}
      destroyOnClose={true}
      width={1000}
      confirmLoading={confirmLoading}
    >
      <Form form={form} {...formItemLayout} onFinish={onFinish}>
        <Form.Item
          label={'登录名称'}
          name={'account'}
          rules={[{ required: true, message: '请填写登录名称!' }, { validator: validFunction }]}
        >
          <Input onBlur={() => findAccount()} disabled={!_isEmpty(query)}></Input>
        </Form.Item>
        {_isEmpty(query) && (
          <Form.Item
            label={'登录密码'}
            name={'password'}
            rules={[{ required: true, message: '请填写登录密码!' }, { validator: validFunction }]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>
        )}
        <Form.Item
          label={'管理系统'}
          name={'managementSystem'}
          initialValue={['1']}
          rules={[{ required: true, message: '请填写管理系统!' }]}
        >
          <DictTreeSelect
            treeCheckable={true}
            initValue={basicInfo['managementSystem']}
            // backType={'object'}
            codeType={'dict_system'}
            placeholder={'管理系统'}
            parentDisable={true}
          />
          {/* <Select
            mode={'multiple'}
            onChange={(e: any = []) => {
              if (!e.includes('1')) {
                form.setFieldsValue({
                  managementSystem: [...e, '1'],
                });
              }
            }}
          >
            {systemsList.map((system) => (
              <Select.Option value={system.key} key={system.key}>
                {system.name}
              </Select.Option>
            ))}
          </Select> */}
        </Form.Item>
        <Form.Item
          label={'管理权限'}
        // className={styles.permissionsBody}
        >
          <React.Fragment>
            <Row>
              <Col span={2}>序号</Col>
              <Col span={5}>管理组织</Col>
              <Col span={6}>序列码</Col>
              <Col span={6}>系统角色</Col>
              <Col span={3}>是否默认</Col>
            </Row>
            {formItems}
          </React.Fragment>
        </Form.Item>
      </Form>
    </Modal>
  );
});

const index = (props: any, ref: any) => {
  const { closeCallBack, title, width = 1200 } = props;
  const addRef: any = useRef();
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [list, setList] = useState([]);
  const [pagination, setPagination] = useState({
    pageSize: 10,
    total: 0,
    current: 1,
    pageNum: 1,
  });

  const handleOk = async () => { };
  const handleCancel = () => {
    setVisible(false);
    closeCallBack && closeCallBack();
  };
  useImperativeHandle(ref, () => ({
    open: (query: object) => {
      setVisible(true);
      getLists();
    },
    clear: () => {
      clear();
    },
  }));
  const clear = () => { };
  const searchChange = (e) => {
    getLists();
    console.log(e.target.value);
  };
  const getLists = async (p = {}) => {
    const {
      code = 500,
      data: { list = [], pageNumber = 1, pageSize = 10, totalRow = 0 } = {},
    } = await findValidUser({
      data: {
        pageNumber: pagination.current,
        pageSize: pagination.pageSize,
        ...p,
      },
    });
    if (code === 0) {
      setList(list);
      setPagination({
        pageSize: pageSize,
        total: totalRow,
        current: pageNumber,
        pageNum: pageNumber,
      });
    }
  };

  const onLock = async (checked, record) => {
    let url = unlock;
    if (checked) {
      url = lock;
    }
    const res = await url({ data: { id: record.id } });
    if (res.code === 0) {
      getLists();
    }
    // if (checked){
    //   this.props.dispatch({
    //     type:'user/isUnLocks',
    //     payload:{
    //       data:{
    //         id:record.id
    //       }
    //     }
    //   }).then(res=>{
    //     if (res.code===0){
    //       if (isEmpty(this.state.keyowrd)) {
    //         this.onPage(this.state['page'],this.state['pageNum']);
    //       }else {
    //         this.getSearch(this.state['page'],10)
    //       }
    //     }
    //   })
    // }else {
    //   this.props.dispatch({
    //     type:'user/isLocks',
    //     payload:{
    //       data:{
    //         id:record.id
    //       }
    //     }
    //   }).then(res=>{
    //     if (res.code===0){
    //       if (isEmpty(this.state.keyowrd)) {
    //         this.onPage(this.state['page'],this.state['pageNum']);
    //       }else {
    //         this.getSearch(this.state['page'],10)
    //       }
    //     }
    //   })
    // }
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 50,
      render: (text, record, index) => {
        return (pagination['current'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    {
      title: '登录名',
      width: 150,
      dataIndex: 'account',
    },
    {
      title: '管理系统',
      dataIndex: 'managementSystem',
      render: (text) => {
        if (text) {
          let ar = text.split(',');
          let _text = ar
            .map((it) => {
              let find = systemsList.find((its) => its.key == it);
              if (find) {
                return find.name;
              }
              return '';
            })
            .toString();
          return _text;
        }
        return '';
      },
    },
    // {
    //   title: '登录次数',
    //   dataIndex: 'loginCount',
    //   width: 80,
    // },
    // {
    //   title: '登录ip',
    //   dataIndex: 'ip',
    //   width: 120,
    // },
    {
      title: '最近登录时间',
      dataIndex: 'lastLoginTime',
      width: 120,
      render: (text) => {
        return <span>{!text ? '' : moment(text).format('YYYY-MM-DD')}</span>;
      },
    },
    {
      title: '状态',
      dataIndex: 'isLock',
      width: 100,
      render: (text, record) => {
        return (
          <span>
            <Switch
              key={new Date().valueOf()}
              checkedChildren="已锁定"
              unCheckedChildren="未锁定"
              defaultChecked={record.isLock === 1}
              onChange={(e) => onLock(e, record)}
            />
          </span>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      render: (text, record, index) => {
        return (
          <React.Fragment>
            <a
              onClick={() => {
                addRef.current.open(record);
              }}
            >
              编辑
            </a>
          </React.Fragment>
        );
      },
    },
  ];
  return (
    <Fragment>
      <Modal
        title={'系统设置'}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={width}
        maskClosable={false}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
        footer={null}
        bodyStyle={{ height: '75vh' }}
      >
        <div style={{ textAlign: 'right', marginBottom: 10 }}>
          <Space>
            <Search
              width={'200px'}
              placeholder="请输入登录名搜索"
              onSearch={(e) => {
                getLists({ pageNumber: 1, account: e });
              }}
            />
            <Button
              onClick={() => {
                addRef.current.open();
              }}
            >
              新增
            </Button>
          </Space>
        </div>
        <ListTable
          rowKey={'id'}
          columns={columns}
          data={list}
          scroll={{ y: '55vh' }}
          pagination={pagination}
          onPageChange={(page, pageSize) => {
            getLists({ pageNumber: page, pageSize });
          }}
        />
        <ModalAdd
          ref={addRef}
          close={() => {
            getLists({ pageNumber: 1 });
            // 重新请求role接口，更新 managementSystem
            props.dispatch({
              type: 'login/getRoleToRefresh',
              payload: {},
            });
          }}
        ></ModalAdd>
      </Modal>
    </Fragment>
  );
};

// @ts-ignore
// export default React.forwardRef(index);
export default connect(({ login }: any) => ({ login }), undefined, undefined, { forwardRef: true })(
  React.forwardRef(index),
);
