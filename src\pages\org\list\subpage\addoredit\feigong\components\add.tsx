import React, { useState, useImperativeHandle, useEffect, Fragment } from 'react';
import { Modal, Form, Input, InputNumber, Row, Col, Select, Alert } from 'antd';
import Tip from '@/components/Tip';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import { addNonPublic, updateNonPublic } from '@/pages/org/services/org';
const formItemLayout = {
  labelCol: {
    xs: { span: 12 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 10 },
    sm: { span: 10 },
  },
};
const YearValidator = (rule, value, callback) => {
  let rep = /^\d{4}$/;
  if (!rep.test(value)) {
    return callback('请输入正确年份');
  } else {
    return callback();
  }
}
const NowYear = new Array(5).fill(1).map((it, index) => +moment().format('YYYY') - index);

const index = React.forwardRef((props: any, ref) => {
  const {
    title = '',
    width = 1000,
    onOK,
    pageType = '',
    tipMsg = {},
    org: { basicInfo = {} } = {}
  } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [query, setQurey] = useState<any>({});
  const [loading, setLoading] = useState(false);
  useImperativeHandle(ref, () => ({
    open: (query = {}) => {
      setVisible(true);
      setQurey(query);
      if (_isEmpty(query)) {
        form.resetFields();
      } else {
        form.setFieldsValue({ ...query, year: query['year'] });
      }
    },
    clear: () => {
      // clear();
    },
  }));
  useEffect(() => {
  }, [JSON.stringify(query)])
  const handleOk = async (e) => {
    let submitUrl = query?.code ? updateNonPublic : addNonPublic;
    // console.log('1111111111111111111111111111111111', e);
    if (e.fiscalFunds > 200) {
      form.setFields([{
        name: 'fiscalFunds',
        value: e.fiscalFunds,
        errors: [`财政专项列支非公企业党建工作经费（万元）输入值必须小于200`]
      }]);
      return;
    }
    // 社区纳入财政预算的工作经费总额（万元）输入值必须小于200
    if (e.partyExpenses > 200) {
      form.setFields([{
        name: 'partyExpenses',
        value: e.partyExpenses,
        errors: [`党费拨补非公企业党建工作经费（万元）输入值必须小于200`]
      }]);
      return;
    }

    if (e.activityServiceCenter < e.newActivityServiceCenter) {
      form.setFields([{
        name: 'newActivityServiceCenter',
        value: e.newActivityServiceCenter,
        errors: [`非公企业集聚区综合性党群活动服务中心” 必须大于 “新建立非公企业集聚区综合性党群活动服务中心”`]
      }]);
      return;
    }

    setLoading(true);
    const { code = 500 } = await submitUrl({
      data: {
        ...e,
        year: +e['year'],
        code: query?.code,
        orgLevelCode: basicInfo['orgCode'],
        orgCode: basicInfo['code'],
        zbCode: basicInfo['zbCode'],
      }
    });
    setLoading(false);
    if (code == 0) {
      Tip.success('操作提示', '操作成功');
      setVisible(false);
      clear();
      onOK && onOK();
    }
  };
  const handleCancel = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setQurey({});
    setLoading(false);
    form.resetFields();
  };
  return (
    <Fragment>
      <Modal
        title={`${_isEmpty(query) ? '新增' : '编辑'}非公党建情况`}
        visible={visible}
        onOk={() => form.submit()}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        confirmLoading={loading}
      >
        <Form form={form} onFinish={handleOk}>
          <Row>
            <Col span={24}>
              <Alert message="操作提示：只填写本级党组织的非公党建情况，不包含下级。" type="info" showIcon />
              <div style={{ marginBottom: 10 }} />
            </Col>
            <Col span={12}>
              <Form.Item name='year'
                label='年度'
                {...formItemLayout}
                rules={[{ required: true, message: '请填写' }]}
              >
                <Select style={{ width: '100%' }} >
                  {NowYear.map(it => <Select.Option value={it}>{it}</Select.Option>)}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <LongLabelFormItem label={'是否依托组织部门或社会工作部门成立非公党工委'}
                required={true}
                code={'hasWorkCommittee'}
                tipMsg={tipMsg}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item {...formItemLayout}
                      name={code}
                      rules={[{ required: true, message: '是否依托组织部门或社会工作部门成立非公党工委' }]}
                    >
                      <Select placeholder={'请选择'} style={{ width: '100%' }}>
                        <Select.Option value={1}>是</Select.Option>
                        <Select.Option value={0}>否</Select.Option>
                      </Select>
                    </Form.Item>
                  )
                }} />
            </Col>
            <Col span={12}>

              <LongLabelFormItem label={'非公党工委是否设立专门办事机构'}
                required={true}
                code={'hasWorkingBody'}
                tipMsg={tipMsg}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item {...formItemLayout}
                      name={code}
                      rules={[{ required: true, message: '非公党工委是否设立专门办事机构' }]}
                    >
                      <Select placeholder={'请选择'} style={{ width: '100%' }}>
                        <Select.Option value={1}>是</Select.Option>
                        <Select.Option value={0}>否</Select.Option>
                      </Select>
                    </Form.Item>
                  )
                }} />
            </Col>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) => prevValues.hasWorkingBody !== currentValues.hasWorkingBody}
            >
              {({ getFieldValue }) => {
                if (getFieldValue('hasWorkingBody') == 1) {
                  return (
                    <Col span={12}>
                      <LongLabelFormItem label={'非公党工委办事机构工作人员编制（个）'}
                        required={true}
                        code={'workingBodyNumber'}
                        tipMsg={tipMsg}
                        formItemLayout={formItemLayout}
                        formItem={(formItemLayout, code) => {
                          return (
                            <Form.Item {...formItemLayout}
                              name={code}
                              rules={[{ required: true, message: '非公党工委办事机构工作人员编制（个）' }]}
                            >
                              <InputNumber min={0} style={{ width: '100%' }} />
                            </Form.Item>
                          )
                        }} />
                    </Col>
                  )
                }
              }}
            </Form.Item>
            <Col span={12}>
              <LongLabelFormItem label={'组织部门或社会工作部门（非公党工委）直接管理的非公企业党组织数'}
                required={true}
                code={'manageOrganizationNumber'}
                tipMsg={tipMsg}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item {...formItemLayout}
                      name={code}
                      rules={[{ required: true, message: '组织部门或社会工作部门（非公党工委）直接管理的非公企业党组织数' }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  )
                }} />
            </Col>
            <Col span={12}>
              <LongLabelFormItem label={'组织部门或社会工作部门（非公党工委）直接联系的非公企业党组织数'}
                required={true}
                code={'connectOrganizationNumber'}
                tipMsg={tipMsg}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item {...formItemLayout}
                      name={code}
                      rules={[{ required: true, message: '组织部门或社会工作部门（非公党工委）直接联系的非公企业党组织数' }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  )
                }} />
            </Col>

            <Col span={12}>
              <LongLabelFormItem label={'财政专项列支非公企业党建工作经费（万元）'}
                required={true}
                code={'fiscalFunds'}
                tipMsg={tipMsg}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item {...formItemLayout}
                      name={code}
                      rules={[{ required: true, message: '财政专项列支非公企业党建工作经费（万元）' }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  )
                }} />
            </Col>
            <Col span={12}>
              <LongLabelFormItem label={'党费拨补非公企业党建工作经费（万元）'}
                required={true}
                code={'partyExpenses'}
                tipMsg={tipMsg}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item {...formItemLayout}
                      name={code}
                      rules={[{ required: true, message: '党费拨补非公企业党建工作经费（万元）' }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  )
                }} />
            </Col>
            <Col span={12}>
              <LongLabelFormItem label={'非公企业集聚区综合性党群活动服务中心（个）'}
                required={true}
                code={'activityServiceCenter'}
                tipMsg={tipMsg}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item {...formItemLayout}
                      name={code}
                      rules={[{ required: true, message: '非公企业集聚区综合性党群活动服务中心（个）' }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  )
                }} />
            </Col>
            <Col span={12}>
              <LongLabelFormItem label={'新建立非公企业集聚区综合性党群活动服务中心（个）'}
                required={true}
                code={'newActivityServiceCenter'}
                tipMsg={tipMsg}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item {...formItemLayout}
                      name={code}
                      rules={[{ required: true, message: '新建立非公企业集聚区综合性党群活动服务中心（个）' }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  )
                }} />
            </Col>
          </Row>
        </Form>
      </Modal>
    </Fragment>
  )
});
export default index
