import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {getList} from 'src/pages/mem/services';
import { changeListPayQuery } from '@/utils/method.js';
import {getList as getListHistory} from '@/pages/mem/services/memLeave'
const mem = modelExtend(listPageModel,{
  namespace: "memSelect",
  state:{
    basicInfo:{},
    basicInfoData:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        // const { pathname, query } = location;
      });
    }
  },
  effects: {
    // 查找人员列表
    *getList({ payload }, { call, put }) {
      const {data={}} = yield call(getList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    // 查找历史人员列表
    *getListHistory({ payload }, { call, put }) {
      const {data={}} = yield call(getListHistory, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
  }
});
export default mem;
