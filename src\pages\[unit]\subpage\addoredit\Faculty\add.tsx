import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Form, Input, Modal } from 'antd';
import Tip from '@/components/Tip';
import { addSecondary, updateSecondary } from './services';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get'
import DictSelect from '@/components/DictSelect';
import OrgSelect from '@/components/OrgSelect';
import { findDictCodeName } from '@/utils/method';


const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 14 },
};

const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  // const { width = 800 } = props;
  const [title, setTitle] = useState('新增');
  useImperativeHandle(ref, () => ({
    open: (dataInfo) => {
      open(dataInfo);
    },
  }));
  const open = (dataInfo) => {
    // console.log('dataInfo==', dataInfo);
    setVisible(true);
    if (!_isEmpty(dataInfo)) {
      setTitle('编辑');
      setDataInfo(dataInfo);
      form.setFieldsValue({
        ...dataInfo,
      });
    }
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    const { unit: { basicInfo = {} } = {}, onOK } = props;
    let val = findDictCodeName(['d110'], e, dataInfo);
    if (_isArray(val['orgCode'])) {
      val['orgName'] = _get(val, 'orgCode.[0].name');
      val['orgCode'] = _get(val,'orgCode.[0].code');
    } else {
      val['orgName'] = dataInfo['orgName'];
    }

    let url = addSecondary;
    if (!_isEmpty(dataInfo)) {
      url = updateSecondary;
    }
    val = {
      ...val,
      unitCode: basicInfo?.code,
      id: dataInfo?.id,
      code: dataInfo?.code,
    };
    // console.log(val,'val');

    setConfirmLoading(true);
    const { code: resCode = 500 } = await url({
      data: {
        ...val,
      },
    });
    setConfirmLoading(false);
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  return (
    <Fragment>
      <Modal
        title={title}
        destroyOnClose
        visible={visible}
        confirmLoading={confirmLoading}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={'550px'}
      >
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Form.Item
            name="facultyName"
            label="院系名称"
            rules={[{ required: true, message: '请输入院系名称' }]}
          >
            <Input/>
          </Form.Item>
          <Form.Item
            name="d110Code"
            label="配备专职组织员情况"
            rules={[{ required: true, message: '请选择配备专职组织员情况' }]}
          >
            <DictSelect
              codeType={'dict_d123'}
              backType={'object'}
              initValue={dataInfo['d110Code'] || undefined}
              placeholder={'请选择配备专职组织员情况'}
            />
          </Form.Item>
          <Form.Item
            name="orgCode"
            label="关联党组织"
            rules={[{ required: false, message: '请选择关联党组织' }]}
          >
            <OrgSelect
              initValue={dataInfo['orgName']}
              placeholder={'关联党组织'}
            />
          </Form.Item>
          <Form.Item
            name="remark"
            label="备注"
            rules={[{ required: false, message: '请填写备注' }]}
          >
            <Input.TextArea/>
          </Form.Item>
        </Form>
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
