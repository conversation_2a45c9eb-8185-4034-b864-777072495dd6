import React from 'react';
import style from './index.less';
import { Button, Tree, Modal, Collapse, Divider, Tooltip, Table, Space, Dropdown, Menu, Select } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import _isEqual from 'lodash/isEqual';
import _get from 'lodash/get';
import { isEmpty, fileDownloadHeader } from '@/utils/method';
import { connect } from 'dva';
import { getSession } from '@/utils/session';
import SortTable from '@/components/SortTable';
import { checkTable } from '@/pages/annualStatistics/services/instruction';
import Tip from '@/components/Tip';
import SpinProgress from '@/components/SpinProgress';
import ListTable from '@/components/ListTable';
// import SelectMem from '../components/selectMem'
// import InOffice from '../components/inOffice'
import DictRender from '../components/dictRender'
const TreeNode = Tree.TreeNode;
const { Panel } = Collapse;
const { Option } = Select;

// @ts-ignore
@connect(({ login, instruction, annualstats, tmwTable }) => ({ login, instruction, annualstats, tmwTable }), undefined, undefined, { forwardRef: true })
export default class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    const { initKeys } = props;
    this.state = {
      params: {}, // 反查参数
      key: isEmpty(initKeys) ? 'html_1' : initKeys[0],
      isFirst: true,
      progressType: undefined, //进度条类型
      checkPass: false, // 上报之前必须点校核
      reportStatus: undefined,
      listData: [{}],
      isModalVisible: false,
      isUnitVisible: false,
      isAll: false,
      rowCheckId: '',
      isTreeVisible: false,
      activeKey: '',
      getlistData:this.getlistData,
      // levelCode:'J1801-1',
    };
    // this.props.dispatch({
    //   type: 'instruction/updateTree',
    //   payload: {}
    // })
  }
  // 获取大列表数据
  getlistData = (obj) => {
    this.props.dispatch({
      type: 'instruction/findTreeByTbsm',
      payload: {
        ...obj,
      }
    })
  }
  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    let state = {};
    const org = JSON.parse(sessionStorage.getItem('org') || '{}')
    const { instruction: { TreeList = [], expandedKeys = [], listData = [], columnsList = [], tableList = [], originally = [] } = {},
      annualstats: { treeLevel = {}, treeOrg = {}, type = '', memType = '' } = {} } = nextProps;
    const { getlistData ,key, _TreeList = [], _org, _expandedKeys, _html = '', levelCode = '', columnsList: _columnsList = [], originally: _originally = [], _treeLevel = {}, _treeOrg = {}, getReportStatus, _memType = undefined } = prevState;
    // 点第一颗树时请求
    if (!_isEqual(org, _org)) {
      state['_org'] = org;
      let obj = {
        reportLevelCode: key,
        orgLevelCode: org['orgCode']
      };
      if (!isEmpty(obj['orgLevelCode']) && !isEmpty(obj['reportLevelCode'])) {
        getlistData(obj);
      }
    }
    if (listData) {
      state['checkPass'] = false; //html更新重置校核状态
      state['listData'] = listData;
      state['_treeOrg'] = treeOrg;
      state['tableList'] = tableList || [];
      state['org'] = org
      // memType === '1' && getReportStatus(); // 根据html的不同，重新请求该表状态
    }
    if (!_isEqual(originally, _originally)) {
      state['originally'] = originally
    } else {
      state['originally'] = _originally
    }
    if (columnsList) {
      state['columnsList'] = columnsList
    }
    return state;
  };

  onExpand = (expandedKeys, e) => {//展开树节点
    const { node: { props: { dataRef = {} } = {} } = {}, expanded = false } = e || {};
    const { id = '' } = dataRef;
    const { listTree, memType = '1' } = this.props.instruction;
    if (!isEmpty(listTree)) {
      let find = listTree.find(it => it['id'] === id) || {};
      if (isEmpty(_get(find, 'children', [])) && expanded) {
        this.props.dispatch({
          type: 'instruction/getAnnualstatsTree',
          payload: {
            id: id
          }
        });
      }
      this.setState({
        expandedKeys,
      });
    }
  };
  onSelect = (key) => {
    const org = JSON.parse(sessionStorage.getItem('org') || '{}')
    this.setState({
      key,
    });
    let obj = {
      reportLevelCode: key,
      orgLevelCode: org['orgCode']
    };
    this.getlistData(obj)
    // const { node: { props: { dataRef = {} } = {} } = {} } = e || {};
    // const val = { ...dataRef };
    // delete val['children'];

    // this.props.dispatch({
    //   type: 'instruction/updateState',
    //   payload: {
    //     treeOrg: val,
    //     selectedKeys: id,
    //   }
    // });
    // this.setState({
    //   selectedKeys: id,
    //   org: e
    // }, () => {
    //   this.treeSelect(e.node.props.dataRef.levelCode)
    // })
  };

  // treeSelect = (val) => {
  //   const { type = '', memType = '' } = this.props.annualstats;
  //   const { levelCode = '', orgCode = '', unitId = '' } = getSession('annualstatsTreeOrg') || {};
  //   const { code: lcode = '' } = getSession('annualstatsLevel') || {};
  //   let text = type === 'a' ? 'orgCode' : 'countLevelCode';
  //   let obj = {
  //     reportLevelCode: val,
  //     orgLevelCode: levelCode,
  //   };
  //   this.getList(obj)
  // };

  renderTreeNode1 = (data) => {//渲染树节点
    if (!isEmpty(data)) {
      return data.map(item => {
        if (item['hasSub'] === '1' || item['children']) {
          return (
            <TreeNode title={item['name']} key={item['levelCode']} isLeaf={item['hasSub'] !== '1'} dataRef={item} >
              {
                this.renderTreeNode1(item['children'])
              }
            </TreeNode>
          );
        } else {
          return <TreeNode title={item['name']} key={item['levelCode']} isLeaf={item['hasSub'] !== '1'} dataRef={item} />
        }
      })
    }
  }
  componentDidMount(): void {
    let script = document.createElement("script"), _this = this;
    script.type = "text/javascript";
    script.src = '/js/check.js';
    document.body.appendChild(script);
    window.addEventListener('message', async function (e) {
      const { data } = e;
      if (data && typeof data == 'string') {
        const { tableCellIndex, tableName } = JSON.parse(data);
        let txt = tableCellIndex.split('_');
        let name = tableName.split('_');
        txt = txt.slice(1, txt.length);
      }
    })
  }

  check = async () => {
    const {
      instruction: { treeOrg: { parentCode = '', levelCode: levelCodes = '' } = {} } = {},
      annualstats: { memType = '', type: treeType = '', treeOrg: { levelCode = '', unitId = undefined, id = undefined } = {}, treeLevel: { code: countLevelCode = '' } = {} } = {}
    } = this.props;
    let orgCode = levelCode;
    if (memType === '3') {
      orgCode = `${id},${unitId}`;
    }
    const { code = 500, data: { key = '' } = {} } = await checkTable({ orgCode, type: parentCode === '-1' ? levelCodes : parentCode });
    if (code === 0) {
      this.setState({ tbCheckLoading: true, progressType: 'check' });
      this['SpinProgress'].getProgress(key, 'niandu');
    }
  };
  callback = (e) => {
    if (e) {
      const arr = e.split('.')
      let k = arr[0]
      let k1 = arr[1]
      let tableid = arr[2]
      let prtid = arr[3]
      if (!isEmpty(k)) {
        this.props.dispatch({
          type: 'instruction/findFieldList',
          payload: {
            tti005: k,
            tti000: k1,
            orgLevelCode: this.state._treeOrg.levelCode
          }
        })
        this.setState({ tableid: tableid, prtid: prtid, tti000: k1, tti005: k })
      }
    }
    this.setState({ activeKey: e })
  }
  getList = (o) => {
    this.props.dispatch({
      type: 'instruction/findTreeByTbsm',
      payload: {
        ...o
      }
    })
  }
  changeDictValue = (e, val, record, forcalculate) => {
    const { list = [], listData = [], columnsList = [], tableList = [] } = this.state;
    if (typeof e == 'object') {
      record[`${val}name`] = e.name
      record[val] = e.code
    } else {
      record[val] = e
    }
    let arr = tableList.map(i => {
      if (i.index == record.index) {
        let num = (eval(forcalculate) * 100).toFixed(2)
        if (num !== 'Infinity' && num !== 'NaN') {
          i['ttit005'] = num + '%'
        }
        i = record
      }
      return i
    })
    this.setState({ tableList: [...arr] })
  }
  saveList = (record) => {
    let type = record.ttit000 ? 'instruction/updateTbsmItemList' : 'instruction/addTbsmItemList'
    this.props.dispatch({
      type: type,
      payload: {
        data: {
          tableid: this.state.tableid,
          prtid: this.state.prtid,
          unid: this.state._treeOrg.levelCode,
          tti000: this.state.tti000,
          ...record
        }
      }
    })
  }
  delList = (record) => {
    this.props.dispatch({
      type: 'instruction/deleteTbsmItemList',
      payload: {
        data: [{ ttit000: record.ttit000 }]
      }
    }).then(res => {
      Tip.success('操作说明', '删除成功')
      let obj = {
        reportLevelCode: this.state.org.levelCode,
        orgLevelCode: this.state._treeOrg.levelCode,
      };
      this.getTable()
      this.getList(obj)
    })
  }
  addList = () => {
    const { tableList = [] } = this.state;
    let arr = tableList
    arr.push({ index: tableList.length + 1 })
    this.setState({ tableList: [...arr] })
  }
  checkMem = (e) => {
    this.props.dispatch({
      type: 'instruction/addByBaseSelect',
      payload: {
        data: {
          tableid: this.state.tableid,
          prtid: this.state.prtid,
          unid: this.state._treeOrg.levelCode,
          tti000: this.state.tti000,
          tti005: this.state.tti005,
          a0000s: e,
        }
      }
    }).then(res => {
      Tip.success('操作提示', '操作成功')
      this.getTable()
    })
  }
  checkMem1 = (e) => {
    this.props.dispatch({
      type: 'instruction/addByReportSelect',
      payload: {
        data: {
          tableid: this.state.tableid,
          prtid: this.state.prtid,
          unid: this.state._treeOrg.levelCode,
          tti000: this.state.tti000,
          tti005: this.state.tti005,
          ids: e,
        }
      }
    }).then(res => {
      Tip.success('操作提示', '操作成功')
      this.getTable()

    })
  }
  allSave = () => {
    let arr: any = []
    this.state.tableList.map(i => {
      i['tableid'] = this.state.tableid
      i['prtid'] = this.state.prtid
      i['unid'] = this.state._treeOrg.levelCode
      i['tti000'] = this.state.tti000
      let count = 0;
      for (var obj in i) {
        if (i.hasOwnProperty(obj)) {
          count++;
        }
      }
      if (count > 5) {
        arr.push(i)
      }
    })
    this.props.dispatch({
      type: 'instruction/addBatchTbsmItemList',
      payload: {
        data: arr
      }
    }).then(res => {
      if (res.code === 0) {
        Tip.success('操作提示', '操作成功')
        let obj = {
          reportLevelCode: this.state.org.levelCode,
          orgLevelCode: this.state._treeOrg.levelCode,
        };
        this.getList(obj)
      }
    })
  }
  getTable = () => {
    let arr = this.state.columnsList.map(i => {
      return i.dataIndex
    })
    this.props.dispatch({
      type: 'instruction/findTbsmItemList',
      payload: {
        tti005: this.state.tti005,
        tti000: this.state.tti000,
        orgLevelCode: this.state._treeOrg.levelCode,
        columnsArr: arr
      }
    })
  }

  sort = (arr) => {
    const para = arr.map((i, key) => {
      return {
        ttit000: i.ttit000
      }
    });
    this.setState({
      originally: [...arr],
    });
    this.props.dispatch({
      type: 'instruction/sortTbsmItemList',
      payload: {
        data: para
      }
    }).then(res => {
      const { code = 500 } = res
      if (code === 0) {
        Tip.success('操作提示', '操作成功')
        this.getTable()
      }
    })
  };
  openSort = () => {
    SortTable.open();
  };
  fetchNum = () => {
    this.handleCancel()
    if (this.state.isAll) {
      this.setState({ isUnitVisible: true, activeKey: '' })
      return
    }
    this.props.dispatch({
      type: 'instruction/qsData',
      payload: {
        data: {
          tableid: this.state.tableid,
          prtid: this.state.prtid,
          unid: this.state._treeOrg.levelCode,
          tti000: this.state.tti000,
          tti005: this.state.tti005,
        }
      }
    }).then(res => {
      const { code = 500 } = res
      if (code === 0) {
        Tip.success('操作提示', '操作成功')
        let obj = {
          reportLevelCode: this.state.org.levelCode,
          orgLevelCode: this.state._treeOrg.levelCode,
        };
        this.getList(obj)
      }
    })
  }
  handleCancel = () => {
    this.setState({
      isModalVisible: false
    })
  }
  fetchNum1 = () => {
    if (this.state.rowCheckLevel) {
      this.props.dispatch({
        type: 'instruction/batchQsData',
        payload: {
          reportLevelCode: this.state.rowCheckLevel,
          orgLevelCode: this.state._treeOrg.levelCode,
        }
      }).then(res => {
        const { code = 500, data = {} } = res
        if (code === 0) {
          this.handleCancel1()
          this['progressRef'].getProgress(data.key, 'niandu');
        }
      })
    } else {
      Tip.error('操作提示', '请选择报表')
    }
  }
  handleCancel1 = () => {
    this.setState({
      isUnitVisible: false,
      isAll: false
    })
  }
  fetchNum2 = () => {
    if (this.state.selectedKeys2.length > 0 && this.state.checkedKeys.length > 0) {
      this.props.dispatch({
        type: 'instruction/dataAggregate',
        payload: {
          data: {
            leftList: this.state.checkedKeys,
            rightList: this.state.selectedKeys2[0]
          }
        }
      }).then(res => {
        const { code = 500, data = {} } = res
        if (code === 0) {
          this.handleCancel2()
          this['progressRef'].getProgress(data.key, 'niandu');
        }
      })
    } else {
      Tip.error('操作提示', '请选择单位')
    }
  }
  handleCancel2 = () => {
    this.setState({
      isTreeVisible: false
    })
  }
  clearList = () => {
    // this.setState({ originally: [] })
    // this.props.dispatch({
    //   type: 'instruction/clear',
    //   payload: {
    //     reportLevelCode: this.state.rowCheckLevel,
    //     orgLevelCode: this.state._treeOrg.levelCode
    //   }
    // })
  }
  allNum = () => {
    this.setState({ isModalVisible: true, isAll: true })
  }
  summary = () => {
    this.setState({
      isTreeVisible: true
    })
  }
  expTbsm = (key) => {
    let data = {}
    if (key == 0) {
      data = {
        orgLevelCode: this.state._treeOrg.levelCode,
        reportLevelCode: this.state.org.levelCode
      }
    } else {
      data = {
        orgLevelCode: this.state._treeOrg.levelCode
      }
    }
    this.props.dispatch({
      type: 'instruction/expTbsm',
      payload: data
    }).then(res => {
      const { code = 500, data = {} } = res
      if (code === 0) {
        this['progressRef'].getProgress(data.key, 'niandu');
      }
    })
  }
  rowClick = (e) => {
    this.setState({ rowCheckId: e.id, rowCheckLevel: e.levelCode })
  }
  setRowClassName = (record) => {
    return record.id === this.state.rowCheckId ? 'clickRowStyl' : '';
  }
  progressRefCallBack = (res) => {
    const { code = 0 } = res;
    if (code === '2') {
      if (res.url) {
        fileDownloadHeader(`/api${res.url}`, res.url.split('/')[2])
      }
      Tip.success('操作提示', res.currDataName)
      this.getTable()
    } else {
      Tip.error('操作提示', res.currDataName)
      this.getTable()
    }
  };

  onCheck = (checkedKeys) => {
    this.setState({
      checkedKeys: checkedKeys.checked,
      defaultCheckedKeys: checkedKeys.checked
    })
  }
  onSelect2 = (selectedKeys, e) => {
    this.setState({
      selectedKeys2: selectedKeys
    })
  }
  onExpand1 = (expandedKeys, e) => {
    const { node: { props: { dataRef = {} } = {} } = {}, expanded = false } = e || {};
    const { id = '' } = dataRef;
    const { listTree, memType = '1' } = this.props.annualstats;
    if (!isEmpty(listTree)) {
      let find = listTree.find(it => it['id'] === id);
      if (isEmpty(_get(find, 'children', [])) && expanded) {
        this.props.dispatch({
          type: 'annualstats/getAnnualstatsTree',
          payload: {
            orgId: memType !== '3' ? parseInt(id) : id
          }
        });
        this.props.dispatch({
          type: 'annualstats/updateState',
          payload: {
            expandedKeys
          }
        });
      }
    }
    this.setState({
      expandedKeys1: expandedKeys,
    });
  }
  onExpand2 = (expandedKeys, e) => {
    this.setState({
      expandedKeys2: expandedKeys
    })
  }
  menu = () => {
    return (
      <Menu>
        <Menu.Item>
          <a onClick={() => this.expTbsm(0)}>导出</a>
        </Menu.Item>
        <Menu.Item>
          <a onClick={() => this.expTbsm(1)}>导出全部</a>
        </Menu.Item>
      </Menu>
    )
  }
  render() {
    const { tmwTable: { TreeList = [] } = {}, instruction: { forcalculate = '' } = {}, login: { listTree = [] } = {}, commonDict = {}, annualstats: { TreeList: ATreeList = [] } = {} } = this.props;
    const { list = [], listData = [], columnsList = [], tableList = [], originally = [], activeKey = '', key } = this.state;
    let he = window.innerHeight - 65
    let column = columnsList.map((i, k) => {
      let obj = {
        ...i,
        width: k == 0 ? 60 : i.gridwidth,
        align: 'center',
        render: (text, record, index) => {
          return (
            (k == 0 || i.forcalculate) ?
              <span>{text}</span> :
              <DictRender key={record.ttit000}
                formType={i.formtype}
                codeType={i.aboutcode}
                value={text || ''}
                valueName={record[`${i.dataIndex}name`] || ''}
                onSelect={(e) => this.changeDictValue(e, i.dataIndex, record, forcalculate)}
              />
          )
        }
      }
      return obj
    })
    column.push({
      title: '操作',
      dataIndex: 'option',
      width: 100,
      fixed: 'right',
      align: 'center',
      render: (text, record, index) => {
        return (
          <React.Fragment>
            {/* <a onClick={() => this.saveList(record)}>保存</a> */}
            {/* <Divider type="vertical" /> */}
            {
              index == tableList.length - 1 &&
              <React.Fragment>
                <a onClick={this.addList}>新增</a>
                <Divider type="vertical" />
              </React.Fragment>
            }
            <a onClick={() => this.delList(record)}>删除</a>
          </React.Fragment>
        )
      }
    })
    let columns = [
      {
        title: '序号',
        dataIndex: 'index',
        render: (text, record, index) => {
          return <span>{index + 1}</span>
        }
      },
      {
        title: '套表名称',
        dataIndex: 'name',
      },
    ]
    const onRow = (record) => {
      return {
        onClick: () => this.rowClick(record),       // 点击行
      }
    };
    let arr: any = JSON.parse(JSON.stringify(TreeList))
    arr.map(i => {
      if (i.children) {
        delete i['children']
      }
      return i
    })
    return (
      <SpinProgress
        // @ts-ignore
        ref={e => this['progressRef'] = e}
        callback={this.progressRefCallBack}>
        <div className={style.box}>
          {/* <Pegging tableType={tableType} list={list} pagination={pagination} pageChange={this.findVerData} params={params} ref={e=>this['peg']=e}/> */}
          <div className={style.content}>
            <div className={style.title}>
              <div>
                填报说明目录
                <Select style={{ width: 300, marginLeft: 10 }} onChange={this.onSelect} value={key}>
                  {TreeList && TreeList.map(tree => <Option key={tree.levelCode} value={tree.levelCode}>{tree.shortName}</Option>)}
                </Select>
              </div>

              <div style={{ marginRight: '20px' }}>
                <Space>
                  <Button onClick={this.allNum} type="primary">批量取数</Button>
                  <Button onClick={this.summary} type="primary">汇总</Button>
                  <Dropdown overlay={this.menu}>
                    <Button type="primary">导出 <DownOutlined /></Button>
                  </Dropdown>
                </Space>
              </div>
            </div>
            <div className={style.right}>
              <Collapse accordion activeKey={activeKey} onChange={this.callback} expandIconPosition="right" className="site-collapse-custom-collapse">
                {
                  listData.length ?
                    listData.map((i, k) => {
                      return (
                        <React.Fragment>
                          {
                            i.children ?
                              <div className={style.caption}>{i.name}</div> :
                              <Panel header={
                                <div>
                                  {
                                    i.innerNum ?
                                      <span style={{ color: '#F39700', fontWeight: 600 }}>({i.innerNum + '条'})</span> : null
                                  }
                                  {i.name}
                                  {
                                    i.remark ?
                                      <Tooltip title={i.remark}>
                                        <span style={{ color: '#D15FEE' }}>注</span>
                                      </Tooltip>
                                      : null
                                  }
                                </div>

                              } key={`${i.tti005}.${i.tti000}.${i.tableid}.${i.prtid}.${k}`}>
                                <div style={{ textAlign: 'right' }}>
                                  <Button onClick={this.allSave}>保存</Button>
                                  <Button onClick={() => this.setState({ isModalVisible: true })}>取数</Button>
                                  {/* {
                                    i.tti011 == '1' &&
                                    <SelectMem {...this.props} onCheck={this.checkMem} />
                                  }
                                  {
                                    i.tti012 == '1' &&
                                    <InOffice orgCode={this.state._treeOrg.levelCode} onCheck={this.checkMem1} />
                                  } */}
                                  <Button onClick={this.openSort}>排序</Button></div>
                                <ListTable scroll={{ y: 500, x: 1300 }} columns={column} data={[...tableList]} />
                              </Panel>
                          }
                        </React.Fragment>
                      )
                    })
                    :
                    null
                }
              </Collapse>
            </div>
          </div>
          <SortTable
            title={'机构排序'}
            sortData={originally}
            onSortEnd={this.sort}
            onClose={this.clearList}
            render={(value, index) => {
              return (
                <div className={style.sortItem}>
                  <span>{index + 1}、</span><span>{value.title}</span><br />
                </div>
              )
            }} />
          <Modal title="提示信息" centered visible={this.state.isModalVisible} onOk={this.fetchNum} onCancel={this.handleCancel} maskClosable={false} closable={false}>
            取数会当前选择单位删除历史数据，重新取数，是否确认取数！！！
          </Modal>
          <Modal title="选择报表信息" centered visible={this.state.isUnitVisible} onOk={this.fetchNum1} onCancel={this.handleCancel1} maskClosable={false} closable={false}>
            <Table columns={columns} dataSource={arr} rowKey={record => record.id} onRow={onRow} rowClassName={this.setRowClassName} />
          </Modal>
          <Modal title="填报说明汇总" width={800} centered visible={this.state.isTreeVisible} onOk={this.fetchNum2} onCancel={this.handleCancel2} maskClosable={false} closable={false}>
            <div className={style.tree1}>
              <div>
                <span>待汇总的单位</span>
                {
                  !isEmpty(ATreeList) &&
                  <Tree
                    checkable
                    checkStrictly
                    key={new Date().valueOf() * 1.2}
                    onExpand={this.onExpand1}
                    onCheck={this.onCheck}
                    expandedKeys={this.state['expandedKeys1']}
                    defaultCheckedKeys={this.state.defaultCheckedKeys}
                  >
                    {this.renderTreeNode1(ATreeList)}
                  </Tree>
                }
              </div>
              <div>
                <span>汇总到的单位</span>
                <Tree
                  onExpand={this.onExpand2}
                  key={new Date().valueOf() * 1.3}
                  onSelect={this.onSelect2}
                  expandedKeys={this.state['expandedKeys2']}
                  selectedKeys={this.state['selectedKeys2']}
                >
                  {this.renderTreeNode1(ATreeList)}
                </Tree>
              </div>
            </div>
          </Modal>
        </div>
      </SpinProgress>
    );
  }
}
