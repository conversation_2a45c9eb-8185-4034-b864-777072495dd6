/**
 * 关系转入
 */
import React, { Fragment } from 'react';
import RuiFilter from '@/components/RuiFilter';
import ListTable from '@/components/ListTable';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import {Button, Divider, Input, Popconfirm, Tabs} from "antd";
import NowOrg from "@/components/NowOrg";
import WhiteSpace from '@/components/WhiteSpace';
import moment from 'moment';
import {connect} from "dva";
import Details from "../outflows/components/details";
import {getSession} from "@/utils/session";
import Tip from "@/components/Tip";
import {setListHeight} from "@/utils/method";
import {_history as router} from "@/utils/method";
import qs from 'qs';
import TransferIn from "@/pages/transfer/inflows/components/transferIn";
import Archives from '../components/archives';
import ExportInfo from '@/components/Export';
import {Letter} from '@/pages/transfer/outflows'

const TabPane=Tabs.TabPane;
const Search=Input.Search;

// @ts-ignore
@connect(({transferIn,loading,commonDict})=>({transferIn,loading:loading.effects['transferIn/findInByPage'],commonDict:commonDict['dict_d59_tree']}))
export default class extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      transferId:undefined,
    }
  }
  confirm=async (item)=>{
    // console.log(item,'iririri');
    const obj=await this.props.dispatch({
      type:'transferOut/undo',
      payload:{
        data:{
          id:item['id'],
          reason:'撤销'
        }
      }
    });
    if(obj && obj['code']===0){
      Tip.success('操作提示','撤销成功');
      this.refresh();
    }
  };
  filterChange=(val)=>{
    // 地址栏显示的页码重置为1
    let {query}=this.props.location;
    const { pageNum,pageSize } = this.state;
    router.push(`?${qs.stringify({...query,pageNum: 1, pageSize})}`)

    this.props.dispatch({
      type:'transferIn/updateState',
      payload:{
        filter:val
      }
    });
    // this.refresh();
  };
  search=(val)=>{
    // 地址栏显示的页码重置为1
    let {query}=this.props.location;
    const { pageNum,pageSize } = this.state;
    router.push(`?${qs.stringify({...query,pageNum: 1, pageSize})}`)

    this.props.dispatch({
      type:'transferIn/updateState',
      payload:{
        keyWord:val
      }
    });
    // this.refresh({pageNum:1});
  };
  searchClear=(e)=>{
    if(!e.target.value){
      this.props.dispatch({
        type:'transferIn/updateState',
        payload:{ keyWord:undefined }
      });
      this.refresh();
    }
  };
  componentWillUnmount(): void {
    this.props.dispatch({
      type:'transferIn/destroy',
    })
  }
  addOrEdit=()=>{//关系转接
    this['TransferIn'].open();
  };
  refresh=(params?:any)=>{//刷新列表
    const {inPagination={}}=this.props.transferIn;
    const org=getSession('org') || {};
    this.props.dispatch({
      type:'transferIn/findInByPage',
      payload:{
        isHistory:true,
        orgId:org['code'],
        pageNum:inPagination['current'] || 1,
        pageSize:inPagination['pageSize'] || 10,
        ...params,
      }
    });
  };
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
    this.setState({
      pageNum:page,
      pageSize,
    })
  };
  componentDidMount(): void {
    setListHeight(this)
  }
  exportInfo= async ()=>{
    this.setState({
      flowBackDownload:true,
    })
    await this['flowBack'].submitNoModal();
    this.setState({
      flowBackDownload:false,
    })
  };

  render(){
    const {loading}=this.props;
    const {inList=[],inPagination=false}=this.props.transferIn;
    const {current,pageSize}=inPagination;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'姓名',
        dataIndex:'name',
        width:120,
      },
      {
        title:'申请日期',
        dataIndex:'createTime',
        width:100,
        render:(text)=>{
          return moment(text).format('YYYY-MM-DD')
        }
      },
      {
        title:'源组织',
        dataIndex:'srcOrgName',
        width:200,
      },
      {
        title:'目的组织',
        dataIndex:'targetOrgName',
        width:200,
      },
      {
        title:'转接类型',
        dataIndex:'typeName',
        width:100,
      },
      {
        title:'转接状态',
        dataIndex:'status',
        width:80,
        render:(text)=>{
          switch (text) {
            case 0:
              return '转接中';
            case 1:
              return '已完成';
            case 2:
              return '已撤销';
            case 4:
              return '超期自动退回';
            default:
          }
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        width:120,
        render:(text,record,index)=>{
          return(
            <span>
              <a
                onClick={()=>{
                  this.props.dispatch({
                    type:'transferOut/inDetail',
                    payload:{
                      transferId:record['id']
                  }}).then(res=>{
                    this.setState({
                      transferId:record['id'],
                      memCode: record['memId'],
                    },()=>{
                      this['Details'].open()
                    })
                  });
                }}
              >
                详情
              </a>
              {/* {
                record['status'] == 1 &&
                <Fragment>
                  <Divider type="vertical"/>
                  <a onClick={()=>{
                    if(this['Archives']?.open){
                      this['Archives']?.open(record);
                    }
                  }}>档案管理</a>
                </Fragment>
              } */}
              <Divider type="vertical"/>
              <Letter {...this.props} record={record} isOut={'0'}/>
            </span>
          )
        }
      },
    ];
    const filterData=[
      {
        key:'types',name:'转接类型',value:this.props.commonDict,
      },
      {
        key:'status',name:'转接状态',value:[{key:1, name:'已完成'},{key:2, name:'已撤销'}, {key:4, name:'超期自动退回'}],
      },
    ];
    const org = getSession('org') || {};
    return (
      <div>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        {/*转接详情*/}
        <Details refresh={this.refresh} transferId={this.state.transferId} memCode={this.state.memCode} isHistory={true} type={'in'} wrappedComponentRef={e=>this['Details']=e}/>
        {/*省外转入*/}
        <TransferIn wrappedComponentRef={e=>this['TransferIn']=e} refresh={this.refresh}/>
        <NowOrg extra={
          <React.Fragment>
            <Button onClick={this.exportInfo} loading={this.state.flowBackDownload} style={{marginLeft:16}}>导出</Button>
            {/* <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.addOrEdit} style={{marginLeft:16}}>省外关系转入</Button> */}
            <Search style={{width:200,marginLeft:16}} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'}/>
          </React.Fragment>
        }/>
        <RuiFilter data={filterData} onChange={this.filterChange}/>
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:this.state.filterHeight}}
          columns={columns} data={inList} pagination={inPagination} onPageChange={this.onPageChange}/>
        <Archives ref={e=>this['Archives'] = e} onOK={()=>{
          this.refresh();
        }}/>
        <ExportInfo wrappedComponentRef={e=>this['flowBack'] = e}
                    tableName={''}
                    noModal={true}
                    tableListQuery={{isHistory:true,orgId:org['code'], ...this.props.transferIn.filter,keyWord:this.props.transferIn.keyWord}}
                    action={'/api/transfer/exportInt'}
        />
      </div>
    );
  }
}
