// @import url(/public/fonts/index.css);
html, body, #root {
  height: 100%;
  // user-select: none;
}

body {
  margin: 0;
  color:rgba(0,0,0,.9);
  font-size: 15px;
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-transition-delay: 99999s;
    -webkit-transition: color 99999s ease-out, background-color 99999s ease-out;
  }
  // .ant-modal{
  //   top:5% !important;
  // }
  .ant-tree .ant-tree-node-content-wrapper{
    white-space: nowrap;
  }
  //.ant-notification{
  //  width: 300px !important;
  //}
  h1, h2, h3, h4, h5, h6{
    color: rgba(0,0,0,.9);
  }
  .ant-form-item-label > label{
    color: rgba(0,0,0,.9);
  }
  .ant-form-item{
    margin-bottom: 16px;
  }
  .ant-form-item-with-help{
    margin-bottom: 0;
  }
  .ant-form-explain{
    margin-top: -2px;
    margin-bottom:-5px;
  }
  .ant-layout-header{
    padding-left: 0;
  }
  .ant-layout{
    font-size: 15px;
  }
  .ant-menu{
    color: rgba(0,0,0,.9);
  }
  .ant-menu-item{
    font-size: 15px !important;
  }
  //.ant-menu-item:not(:last-child){
  //  margin-bottom: 0 !important;
  //}
  //.ant-tree-switcher{
  //  .anticon svg{
  //    display: none !important;
  //  }
  //}
  //.ant-tree li span.ant-tree-switcher.ant-tree-switcher_open{
  //  background-image: url("./assets/minus4.png");
  //  background-size: 75%;
  //  background-repeat: no-repeat;
  //  background-position: 50%;
  //}
  //.ant-tree li span.ant-tree-switcher.ant-tree-switcher_close{
  //  background-image: url("./assets/plus.png");
  //  background-size: 75%;
  //  background-repeat: no-repeat;
  //  background-position: 50%;
  //}
  //.ant-modal{
  //  top:56px !important;
  //}
  .ant-select-switcher-icon{
    font-size: 16px !important;
  }
  .ant-tabs-bar{
    margin: 0 0 10px 0;
  }
  .ant-table-footer{
    padding: 8px;
    border-bottom: 1px solid #e8e8e8;
    background: white;
  }
  .ant-tree-title{
    white-space: nowrap;
  }
  //.ant-select-tree-indent{
  //  position: absolute;
  //  display: none;
  //}
  //.ant-select-tree-treenode-disabled{
  //  position: relative;
  //  .ant-select-tree .ant-select-tree-treenode{
  //    display: block;
  //  }
  //  .ant-select-tree-switcher{
  //    position: absolute;
  //    text-align: left;
  //    width: 80%;
  //    z-index: 99;
  //    padding-left: 6px;
  //  }
  //  .ant-select-tree-node-content-wrapper{
  //    margin-left: 24px;
  //  }
  //}

}
:global{
  .ant-notification{
    width: 300px !important;
  }
  h1, h2, h3, h4, h5, h6{
    color: rgba(0,0,0,.9);
  }
  .ant-form-item-label > label{
    color: rgba(0,0,0,.9);
  }
  .ant-form-item{
    margin-bottom: 16px;
  }
  .ant-form-item-with-help{
    margin-bottom: 0;
  }
  .ant-form-explain{
    margin-top: -2px;
    margin-bottom:-5px;
  }
  .ant-layout-header{
    padding-left: 0;
  }
  .ant-layout{
    font-size: 15px;
  }
  .ant-menu{
    color: rgba(0,0,0,.9);
  }
  .ant-menu-item{
    font-size: 15px !important;
  }
  .ant-tree-title{
    white-space: nowrap;
  }
  //.ant-menu-item:not(:last-child){
  //  margin-bottom: 0 !important;
  //}
  //.ant-tree-switcher{
  //  .anticon svg{
  //    display: none !important;
  //  }
  //}
  //.ant-tree li span.ant-tree-switcher.ant-tree-switcher_open{
  //  background-image: url("./assets/minus4.png");
  //  background-size: 75%;
  //  background-repeat: no-repeat;
  //  background-position: 50%;
  //}
  //.ant-tree li span.ant-tree-switcher.ant-tree-switcher_close{
  //  background-image: url("./assets/plus.png");
  //  background-size: 75%;
  //  background-repeat: no-repeat;
  //  background-position: 50%;
  //}
  //.ant-modal{
  //  top:56px !important;
  //}
  .ant-select-switcher-icon{
    font-size: 16px !important;
  }
  .ant-tabs-bar{
    margin: 0 0 10px 0;
  }
  .ant-table-footer{
    padding: 8px;
    border-bottom: 1px solid #e8e8e8;
    background: white;
  }
}
.editModal{
  .ant-modal-content{
    background-color: unset;
    box-shadow: unset;
  }
  //:global{
  //  .ant-modal-body{
  //    padding: 0 !important;
  //    overflow: auto;
  //  }
  //  .ant-modal-content{
  //    background-color: unset;
  //    box-shadow: unset;
  //  }
  //}
  .container{
    border-radius: 4px;
    margin: auto;
    border: 1px solid #e9e9e9;
    background: #fff;
    box-shadow:0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: calc(~'100vh - 120px');
    max-width: 80%;
    min-width: 1200px;
    margin: auto;
  }
  .content{
    //min-height: 570px;
    height: calc(~'100vh - 188px');
    overflow: auto;
  }
  .header{
    background: #e9e9e9;
    padding: 6px 0;
    line-height: 40px;
    h2{
      margin: 0;
    }
  }
  .close{
    text-align: right;
    font-size: 22px;
    padding-right: 22px;
    &>i:hover{
      cursor: pointer;
    }
  }
  .slider{
    position: relative;
    max-width: 160px;
    min-width: 120px;
    margin: 20px 0 20px -15px;
    padding-left: 30px;
    line-height: 40px;
    border-radius: 0 5px 5px 0;
    font-size: 15px;
    font-weight: bold;
    color: #fff;
    background-color: #40a9ff;
    overflow: hidden;
    white-space: nowrap;
    &:after{
      content: ' ';
      position: absolute;
      top: 40px;
      left: 0;
      width: 0;
      height: 0;
      border-width: 7px;
      border-style: solid;
      border-color: #2185D0 #2185D0 transparent transparent;
    }
  }
}
.eventNone{
  color: rgba(0, 0, 0, 0.25);
  cursor: pointer;
  pointer-events: none;
  &:hover{
    cursor: pointer;
  }
}
.del{
  color: red;
}
.del:hover{
  color: red;
}
.toptable{
  font-weight: bold;
  background: rgba(188,214,253,.6);
  &>td{
    font-size: 16px !important;
  }
}
///*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
//::-webkit-scrollbar {
//  width: 6px;
//  height: 6px;
//  background-color: #F5F5F5;
//  cursor: pointer;
//}
///*定义滚动条轨道 内阴影+圆角*/
//::-webkit-scrollbar-track {
//  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
//  border-radius: 10px;
//  background-color: #FFF;
//}
//
///*定义滑块 内阴影+圆角*/
//::-webkit-scrollbar-thumb {
//  border-radius: 10px;
//  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
//  //background-color: #0188DE;
//}
