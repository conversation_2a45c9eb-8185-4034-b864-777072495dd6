import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {Modal, Input} from 'antd';
import moment from 'moment';
import Tip from '@/components/Tip';
import {delDevelop} from '@/pages/developMem/services/index'
const TextArea=Input.TextArea;

const FormItem=Form.Item;
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      memInfo:{},
      visible:false,
      timeKey:moment().valueOf(),
    }
  }
  handleOk=()=>{
    const {submit,memDevelop:{basicInfo = {}}={}} = this.props;
    const { code } = this.state.memInfo;
    this.props.form.validateFieldsAndScroll( async (err,val)=>{
      if(!err){
        // 取消申请人
        const res = await delDevelop({code, cancelDevelopReason:val.cancelDevelopReason });
        if(res.code === 0){
          this.handleCancel();
          Tip.success('操作提示','操作成功');
          submit && submit();
        }
      }
    })
  };
  handleCancel=()=>{
    this.setState({visible:false});
    this.destroy();
  };
  open=(record)=>{
    this.setState({visible:true,memInfo:record,timeKey:moment().valueOf()})
  };
  destroy=()=>{
   this.setState({
     memInfo:{},
   })
  };
  render() {
    const {form,loading:{effects = {}}={}} = this.props;
    const { getFieldDecorator } = form;
    const {visible} = this.state;
    return (
      <Modal
        destroyOnClose
        title="取消入党申请人"
        visible={visible}
        onOk={this.handleOk}
        maskClosable={false}
        onCancel={this.handleCancel}
        // confirmLoading={effects['memDevelop/toObject']}
      >
        {
          visible &&
            <Fragment key={this.state.timeKey}>
              <Form>
                <FormItem
                  label="取消入党申请人原因"
                >
                  {getFieldDecorator('cancelDevelopReason', {
                    rules: [{ required: true, message: '取消入党申请人原因' }],
                  })(
                    <TextArea rows={4} placeholder={'请输入取消入党申请人原因'}/>
                  )}
                </FormItem>
              </Form>
            </Fragment>
        }
      </Modal>
    );
  }
}
export default Form.create()(index);
