import React, { Fragment, useState, useImperativeHandle } from 'react';
import { Form, Row, Col, Button, Modal, message} from 'antd';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import DictSelect from '@/components/DictSelect';
import moment from 'moment';
import styles from './newAdd.less';
import { addSlackOrg, updateSlackOrg, findByCode } from '../services/lax.js';
import OrgSelect from '@/components/OrgSelect';
import Date from '@/components/Date';
import { getSession } from '@/utils/session';

const formItemLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 12 },
};
const formItemLayout1 = {
  labelCol: { span: 10 },
  wrapperCol: { span: 12 },
};
const NewAdd = (props, ref) => {
  const { onOk } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [query, setQuery] = useState<any>({});
  const [loading, setLoading] = useState(false);
  useImperativeHandle(ref, () => ({
    open: (val = {}) => {
      let _val = {
        ...val,
        neatenTime: val['neatenTime'] ? moment(val['neatenTime']) : undefined,
        d74Code: val['d74Code'] ? [...val['d74Code'].split(',')] : [],
      };
      form.resetFields();
      setVisible(true);
      // setQuery(_val);
      // if (!_isEmpty(_val)) {
      //   form.setFieldsValue(_val);
      // }
      val['code'] && searchInfo(val['code']);
    },
    close: () => {
      handleCancel();
    },
  }));
  const handleCancel = () => {
    setVisible(false);
    setQuery([]);
    form.resetFields();
  };
  // 根据code查信息
  const searchInfo = async (codeId) => {
    const { data = {}, code = 500 } = await findByCode({ code: codeId || '' });
    if (code === 0) {
      let _val = {
        ...data,
        neatenTime: data.neatenTime ? moment(data.neatenTime) : undefined,
        d74Code: data.d74Code ? [...data.d74Code.split(',')] : [],
      };
      setQuery(_val);
      form.setFieldsValue(_val);
    }
  };
  const onFinish = async (value) => {
    const {
      name = '',
      d74Code = [],
      neatenTime = Number,
      neatenEndTime = Number,
      isFilingPovertyVillage = false,
      ...others
    } = value;
    let arr: any = [];
    if (!_isEmpty(d74Code)) {
      d74Code.map((it) => {
        if (it.search('_temp')) {
          let v = it.split('_')[0];
          arr.push(v);
        } else {
          arr.push(it);
        }
      });
    }
    if (!_isEmpty(value.neatenTime)) {
      value.neatenTime = value.neatenTime.valueOf();
    }
    if (!_isEmpty(value.neatenEndTime)) {
      value.neatenEndTime = value.neatenEndTime.valueOf();
    }
    let _val = {
      zbCode: name.zbCode || query['zbCode'],
      orgCode: name.code || query['orgCode'], //name.orgCode
      d74Code: [...new Set(arr)].toString(),
      neatenTime: moment(neatenTime).valueOf(),
      isFilingPovertyVillage: isFilingPovertyVillage ? 1 : 0,
      ...others,
      neatenEndTime: neatenEndTime ? moment(neatenEndTime).valueOf() : undefined,
    };
    let url = addSlackOrg;
    if (query['code']) {
      url = updateSlackOrg;
      setLoading(true);
    const { code = 500 } = await url({
      data: {
        ..._val,
        code: _get(query, 'code', ''),
      },
    });
    setLoading(false);
    if (code === 0) {
      message.success('操作成功');
      onOk && onOk(_val);
    }
    }else{
      setLoading(true);
    const { code = 500 } = await url({
      data: {
        ..._val,
        // code: _get(query, 'code', ''),
      },
    });
    setLoading(false);
    if (code === 0) {
      message.success('操作成功');
      onOk && onOk(_val);
    }
    }

  };

  const canEdit = !query?.neatenEndTime
  return (
    <Fragment>
      <Modal
        closable={true}
        width={800}
        title={!query['code'] ? '新增' : '编辑'}
        visible={visible}
        footer={null}
        onCancel={handleCancel}
        destroyOnClose={true}
      >
        {visible && (
          <Form style={{pointerEvents:canEdit ? 'auto' : 'auto'}} className={styles.form} form={form} {...formItemLayout} onFinish={onFinish}>
            <Row>
              <Col span={24}>
                <Form.Item
                  name="name"
                  {...formItemLayout1}
                  label="软弱涣散基层党组织"
                  rules={[{ required: true }]}
                >
                  <OrgSelect
                    disabled={!!query['name']}
                    // orgTypeList={['1', '2', '5']}
                    // org={query['code'] ? query : undefined}
                    org={query['code'] ? query : getSession('org')}
                    oorg={{orgCode: getSession('org').orgCode, subordinate:0}}
                    initValue={query['name']}
                    onChange={(data) => {
                      form.setFieldsValue({
                        name: data[0],
                      });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  name="d74Code"
                  {...formItemLayout1}
                  label="涣散基层党组织主要类型"
                  rules={[{ required: true }]}
                >
                  <DictSelect
                    codeType={'dict_d74'}
                    mode={'multiple'}
                    initValue={_get(query, 'd74Code', undefined)}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  name="neatenTime"
                  {...formItemLayout1}
                  label="列为软弱涣散基层党组织时间"
                  rules={[{ required: true }]}
                >
                  <Date disabledDate={(current) => {
                      return current && current > moment().endOf('day');
                    }}/>
                </Form.Item>
              </Col>
              {/*{*/}
              {/*  query['code'] && <Col span={24}>*/}
              {/*    <Form.Item*/}
              {/*      name="neatenEndTime"*/}
              {/*      {...formItemLayout1}*/}
              {/*      label="整顿结束时间"*/}
              {/*      rules={[{ required: true }]}*/}
              {/*    >*/}
              {/*      <Date/>*/}
              {/*    </Form.Item>*/}
              {/*  </Col>*/}
              {/*}*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    valuePropName="checked"*/}
              {/*    name="isFilingPovertyVillage"*/}
              {/*    label="是否建档立卡贫困村"*/}
              {/*    initialValue={query['isFilingPovertyVillage'] || 0}*/}
              {/*  >*/}
              {/*    <Switch checkedChildren="是" unCheckedChildren="否" />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="earlyLackSecretaryNum"*/}
              {/*    label="年初缺配的基层党组织书记数"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="currentYearSelected"*/}
              {/*    label="本年度已选配"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="earlyAdjustSecretaryNum"*/}
              {/*    label="年初需要调整的党组织书记数"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="currentYearAdjusted"*/}
              {/*    label="本年度已调整"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="trainSecretaryNum"*/}
              {/*    label="培训基层党组织书记"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="lianVillageLeaderNum"*/}
              {/*    label="联村的县级领导班子成员（人）"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="baoVillageLeaderNum"*/}
              {/*    label="包村的县级领导班子成员（人）"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="selectSecretaryNum"*/}
              {/*    label="选派第一书记（人）"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="twinningUnitNum"*/}
              {/*    label="结对帮扶的县级及以上机关单位（人）"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="twoLevelListingVillageNum"*/}
              {/*    label="省市两级挂牌督办的村（个）"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="wageSpecialNum"*/}
              {/*    label="开展专项整治（项）"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="solveProblemNum"*/}
              {/*    label="解决各类问题（个）"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {/*<Col span={12}>*/}
              {/*  <Form.Item*/}
              {/*    name="treatViolateNum"*/}
              {/*    label="查处违纪违法行为（例）"*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <InputNumber max={99999999} min={0} />*/}
              {/*  </Form.Item>*/}
              {/*</Col>*/}
              {
                // 当有整顿结束时间，不让操作
                // canEdit &&
                <Col span={24} style={{ textAlign: 'center' }}>
                  <Button loading={loading} type="primary" htmlType={'submit'} icon={<LegacyIcon type={'plus'} />}>
                    保存
                  </Button>
                </Col>
              }
            </Row>
          </Form>
        )}
      </Modal>
    </Fragment>
  );
};

export default React.forwardRef(NewAdd);
