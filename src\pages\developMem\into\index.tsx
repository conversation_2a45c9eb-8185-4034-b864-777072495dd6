/**
 * 关系转入
 */
import React from 'react';
import RuiFilter from '@/components/RuiFilter';
import ListTable from '@/components/ListTable';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs, Modal } from "antd";
import NowOrg from "@/components/NowOrg";
import WhiteSpace from '@/components/WhiteSpace';
import TransferIn from './components/transferIn';
import Transfer from './components/transfer';
import moment from 'moment';
import { connect } from "dva";
import Details from "@/pages/developMem/out/components/details";
import { getSession } from "@/utils/session";
import Tip from "@/components/Tip";
import qs from 'qs';
import { _history as router } from "@/utils/method";
import { setListHeight,isFlowingParty } from "@/utils/method";
import ExportInfo from '@/components/Export';
import { ButtonDisabled } from '@/common/config.js'
import { Letter } from '@/pages/transfer/outflows'
import { ExclamationCircleOutlined } from '@ant-design/icons';
const TabPane = Tabs.TabPane;
const Search = Input.Search;

@connect(({ memTransferIn, loading, commonDict, memDevelop }) => ({ memTransferIn, memDevelop, loading: loading.effects['memTransferIn/findInByPage'], commonDict: commonDict['dict_d59_tree'] }))
export default class extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      transferId: undefined,
    }
  }
  confirm = async (item) => {
    // console.log(item,'iririri');
    const obj = await this.props.dispatch({
      type: 'memDevelop/undo',
      payload: {
        data: {
          id: item['id'],
          reason: '撤销'
        }
      }
    });
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', '撤销成功');
      this.refresh();
    }
  };
  filterChange = (val) => {
    this.props.dispatch({
      type: 'memTransferIn/updateState',
      payload: {
        filter: val
      }
    });
    this.refresh();
  };
  search = (val) => {
    this.props.dispatch({
      type: 'memTransferIn/updateState',
      payload: {
        keyWord: val
      }
    });
    this.refresh({ pageNum: 1 });
  };
  searchClear = (e) => {
    if (!e.target.value) {
      this.props.dispatch({
        type: 'memTransferIn/updateState',
        payload: { keyWord: undefined }
      });
      this.refresh();
    }
  };
  addOrEdit = () => {//关系转接

    Modal.confirm({
      title: '提示',
      icon: <ExclamationCircleOutlined style={{ color: '#1890ff' }} />,
      content: '本入口仅用于转出党组织尚未接入全国交换区的党员以及党政机关县处级及以上的党员领导干部组织关系转入。如某党员转出党组织已接入全国交换区，请联系转出党组织通过对方党员系统转出。转出党组织是否接入全国交换区，请与转出党组织联系确认。',
      okText: '确定',
      cancelText: '取消',
      // onCancel:()=>{ Modal.destroyAll()},
      onOk: () => {
        let org = getSession('org') || {}
        this.props.dispatch({
          type: 'memBasic/updateState',
          payload: {
            basicInfo: {
              d01Code: org['d01Code'],
              orgName: org['name'],
              orgCode: org['code'],
              orgZbCode: org['zbCode'],
              memOrgCode: org['orgCode'],
            }
          }
        })

        this['TransferIn'].open();
      },
    });


  };
  add = () => {//关系转接整建制
    this['Transfer'].open();
  };
  expFile = () => {
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memDevelop/exportXsl',
      payload: {
        data: {
          isHistory: false,
          orgId: org['code'],
          isOut: 0
        }
      }
    })
  }
  refresh = (params?: any) => {//刷新列表
    const { inPagination = {} } = this.props.memTransferIn;
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'memTransferIn/findInByPage',
      payload: {
        isHistory: false,
        orgId: org['code'],
        pageNum: inPagination['current'] || 1,
        pageSize: inPagination['pageSize'] || 10,
        ...params,
      }
    });
  };
  onPageChange = (page, pageSize) => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`)
  };
  componentDidMount(): void {
    setListHeight(this);
  }

  componentWillUnmount(): void {
    this.props.dispatch({
      type: 'memTransferIn/destroy',
    });
  }
  exportInfo = async () => {
    this.setState({
      flowBackDownload: true,
    })
    await this['flowBack'].submitNoModal();
    this.setState({
      flowBackDownload: false,
    })
  };
  render() {
    const { loading } = this.props;
    const { inList = [], inPagination = false } = this.props.memTransferIn;
    const { current, pageSize } = inPagination;
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 50,
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1
        }
      },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 120,
      },
      {
        title: '申请日期',
        dataIndex: 'createTime',
        width: 100,
        render: (text) => {
          return moment(text).format('YYYY-MM-DD')
        }
      },
      {
        title: '源组织',
        dataIndex: 'srcOrgName',
        width: 200,
      },
      {
        title: '目的组织',
        dataIndex: 'targetOrgName',
        width: 200,
      },
      {
        title: '转接类型',
        dataIndex: 'typeName',
        width: 100,
      },
      {
        title: '转接状态',
        dataIndex: 'status',
        width: 80,
        render: (text) => {
          switch (text) {
            case 0:
              return '转接中';
            case 1:
              return '已完成';
            case 2:
              return '已撤销';
            default:
          }
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 140,
        render: (text, record, index) => {
          const { id = '' } = record
          return (
            <span>
              <a
                onClick={() => {
                  this.props.dispatch({
                    type: 'memDevelop/inDetail',
                    payload: {
                      transferId: id
                    }
                  }).then(res => {
                    this.setState({
                      transferId: id,
          
                    }, () => {
                      this['Details'].open()
                    })
                  });
                }}
              >
                {record?.status == 0 ? '操作' : '详情'}
              </a>
              {/* <Divider type="vertical"/>
              <Popconfirm title="确定要删除吗？" onConfirm={()=>this.confirm(record)}>
               <a className={'del'}>撤销</a>
              </Popconfirm> */}
              {/* <Divider type="vertical" />
              <Letter {...this.props} record={record} /> */}
            </span>
          )
        }
      },
    ];
    const filterData = [
      {
        key: 'status', name: '转接状态', value: [
          {
            id: 0,
            name: '转接中'
          },
          {
            id: 1,
            name: '已完成'
          },
          {
            id: 2,
            name: '已撤销'
          },
        ],
      },
    ];
    const org = getSession('org') || { d01Code: '' };
    const { d01Code = '' } = org || { d01Code: '' };
    return (
      <div>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1" />
        </Tabs>
        {/*转接详情*/}
        <Details refresh={this.refresh} transferId={this.state.transferId} type={'in'} wrappedComponentRef={e => this['Details'] = e} />
        {/*省外转入*/}
        <TransferIn wrappedComponentRef={e => this['TransferIn'] = e} refresh={this.refresh} />
        {/*省外转入（整建制）*/}
        <Transfer wrappedComponentRef={e => this['Transfer'] = e} refresh={this.refresh} />
        <NowOrg extra={
          <React.Fragment>
            {/* <Button onClick={this.exportInfo} loading={this.state.flowBackDownload}>导出</Button> */}
            {/* {
              (d01Code === '631' || d01Code === '632' || d01Code === '634' || d01Code === '931' || d01Code === '932') && !ButtonDisabled.statistics2021 &&
              <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.addOrEdit} style={{ marginLeft: 16 }}>省外（含系统外，如军队、银行等单位）转入</Button>
            } */}
            {/* <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.add} style={{marginLeft:16}}>省外关系转入（整建制）</Button> */}
            {
              isFlowingParty()&&   <Button icon={<LegacyIcon type={'vertical-align-bottom'} />} onClick={() => this.expFile()} style={{ marginLeft: 16 }}>导出</Button>
            }
         
            <Search style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'} />
          </React.Fragment>
        } />
        <RuiFilter data={filterData} onChange={this.filterChange} />
        <WhiteSpace />
        <WhiteSpace />
        <ListTable scroll={{ y: this.state.filterHeight }}
         
          columns={columns} data={inList} pagination={inPagination} onPageChange={this.onPageChange} />
        <ExportInfo wrappedComponentRef={e => this['flowBack'] = e}
          tableName={''}
          noModal={true}
          tableListQuery={{ isHistory: false, orgId: org['code'], ...this.props.memTransferIn.filter, keyWord: this.props.memTransferIn.keyWord }}
          action={'/api/transfer/exportInt'}
        />
      </div>
    );
  }
}
