export const HeaderMenu = [//顶部一级菜单
  // {
  //   name:'系统首页', code:'10', icon:'', color:'', parent:'-1', isRoot:true, url:'/home',serverId:'1',
  // },
  {
    name: '单位管理', code: '13', icon: require('@/assets/desktop/svg/unit.png'), color: '#E5FFF1', parent: '-1', isRoot: true, url: '/unit/overview', serverId: '9',
  },
  {
    name: '党组织管理', code: '11', icon: require('@/assets/desktop/svg/org.png'), color: '#FFF9E5', parent: '-1', isRoot: true, url: '/org', serverId: '2',
  },
  {
    name: '党员管理', code: '12', icon: require('@/assets/desktop/svg/mem.png'), color: '#E5F8FF', parent: '-1', isRoot: true, url: '/mem', serverId: '3',
  },
  {
    name: '入党申请人', code: '17', icon: require('@/assets/desktop/svg/develop.png'), color: '#FFF9E5', parent: '-1', isRoot: true, url: '/developMem', serverId: '26',
  },
  {
    name: '流动党员', code: '14', icon: require('@/assets/desktop/svg/flow.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/flowMem/flowBack', serverId: '13',
  },
  {
    name: '党代表', code: '16', icon: require('@/assets/desktop/svg/deputy.png'), color: '#E5F8FF', parent: '-1', isRoot: true, url: '/behalf', serverId: '29',
  },
  {
    name: '关系转接', code: '15', icon: require('@/assets/desktop/svg/trans.png'), color: '#E5FFF1', parent: '-1', isRoot: true, url: '', serverId: '20',
  },
  // {
  //   name: '组织生活', code: '34', icon: require('@/assets/desktop/svg/orglife.png'), color: '#E5FFF1', parent: '-1', isRoot: true, url: '/organizeLife', serverId: '9',
  // },
  {
    name: '档案管理', code: '35', icon: require('@/assets/desktop/svg/dagl.png'), color: '#E5FFF1', parent: '-1', isRoot: true, url: '/archivesAdministration', serverId: '97',
  },
  {
    name: '年度统计', code: '27', icon: require('@/assets/desktop/svg/ldtj.png'), color: '#E5FFF1', parent: '-1', isRoot: true, url: '/annualStatistics', serverId: '56',
  },
  {
    name: '数据统计', code: '28', icon: require('@/assets/desktop/svg/data.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/datStatistics', serverId: '65',
  },
  {
    name: '数据查询', code: '31', icon: require('@/assets/desktop/svg/sjcx.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/dataSearch', serverId: '74',
  },
  {
    name: '报表信息', code: '32', icon: require('@/assets/desktop/svg/bbxx.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/dataSheet', serverId: '78',
  },
  // {
  //   name: '党费管理', code: '18', icon: require('@/assets/desktop/svg/fee.png'), color: '#E5FFF1', parent: '-1', isRoot: true, url: '', serverId: '9',
  // },
  // {
  //   name: '活动管理', code: '25', icon: require('@/assets/desktop/svg/activity.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/activity/manage', serverId: '9',
  // },
  // {
  //   name: '工作动态', code: '19', icon: require('@/assets/desktop/svg/gzdt.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/work/trend', serverId: '44',
  // },
  // {
  //   name: '通知管理', code: '21', icon: require('@/assets/desktop/svg/tzgl.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/notice/manage', serverId: '48',
  // },
  // {
  //   name: '台账管理', code: '22', icon: require('@/assets/desktop/svg/deputy.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/evaluation/political', serverId: '',
  // },
  // {
  //   name: '任务管理', code: '20', icon: require('@/assets/desktop/svg/deputy.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/mission/manage', serverId: '',
  // },
  {
    name: '数据校验', code: '29', icon: require('@/assets/desktop/svg/sjjy.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/dataCheck', serverId: '67',
  },
  {
    name: '信息解锁', code: '30', icon: require('@/assets/desktop/svg/xxjs.png'), color: '#FFE8EB', parent: '-1', isRoot: true, url: '/unlock', serverId: '73',
  },
  {
    name: '系统管理', code: '26', icon: '', color: '', parent: '-1', isRoot: true, url: '/home', serverId: '4',
  },
  {
    name: '账户管理', code: '33', icon: require('@/assets/desktop/svg/zhgl.png'), color: '', parent: '-1', isRoot: true, url: '', serverId: '87',
  },

];
export const SliderMenu = [//子菜单
  // 报表信息
  {
    name: '报表生成', code: '3210', icon: 'gold', color: '#EE76FF', parent: '32', url: '/dataSheet/create', serverId: '79',
  },
  {
    name: '报表发布', code: '3211', icon: 'stock', color: '#9273FF', parent: '32', url: '/dataSheet/send', serverId: '80',
  },
  // 数据查询
  {
    name: '分析查询', code: '3111', icon: 'solution', color: '#ff6824', parent: '31', url: '/dataSearch/analysis?lockObject=1', serverId: '82',
  },
  {
    name: '组合查询', code: '3110', icon: 'search', color: '#9273FF', parent: '31', url: '/dataSearch?lockObject=1', serverId: '75',
  },
  // 信息项解锁
  {
    name: '已锁定信息', code: '3010', icon: 'lock', color: '#9273FF', parent: '30', url: '/unlock/locked?lockObject=1&locked=1', serverId: '76',
  },
  {
    name: '未锁定信息', code: '3011', icon: 'unlock', color: '#ff6824', parent: '30', url: '/unlock/unlock?lockObject=1&locked=0', serverId: '77',
  },
  {
    name: '申请记录', code: '3012', icon: 'copy', color: '#EE76FF', parent: '30', url: '/unlock/lockLog?lockObject=1', serverId: '81',
  },
  // 数据校验
  {
    name: '数据校验', code: '2911', icon: 'area-chart', color: '#f03eff', parent: '29', url: '/dataCheck', serverId: '68',
  },
  // 数据统计
  {
    name: '数据概况', code: '2811', icon: 'area-chart', color: '#f03eff', parent: '28', url: '/datStatistics', serverId: '66',
  },
  {
    name: '统计简报', code: '2812', icon: 'line-chart', color: '#0fb625', parent: '28', url: '/datStatistics/word', serverId: '71',
  },
  {
    name: '业务工作调度表', code: '2813', icon: 'dot-chart', color: '#6161FF', parent: '28', url: '/datStatistics/briefing', serverId: '83',
  },
  {
    name: '工作督查表', code: '2814', icon: 'solution', color: '#0000CD', parent: '28', url: '/datStatistics/supervise', serverId: '86',
  },
  // 年度统计
  {
    name: '当年年度统计', code: '2711', icon: 'area-chart', color: '#f03eff', parent: '27', url: '/annualStatistics', serverId: '63',
  },
  // {
  //   name: '报表说明', code: '2713', icon: 'area-chart', color: '#f03eff', parent: '27', url: '/annualStatistics/instruction', serverId: '63',
  // },
  // {
  //   name: ' 2022半年统说明', code: '2714', icon: 'area-chart', color: '#f03eff', parent: '27', url: '/annualStatistics/half2022', serverId: '64',
  // },
  {
    name: '往年年度统计', code: '2712', icon: 'area-chart', color: '#f03eff', parent: '27', url: '/annualStatistics/old', serverId: '64',
  },
  //二级菜单
  // {
  //   name: '党组织概况', code: '1110', icon: 'area-chart', color: '#f03eff', parent: '11', url: '/org', serverId: '5',
  // },
  {
    name: '党组织管理', code: '1111', icon: 'cluster', color: '#6161FF', parent: '11', url: '/org/list', serverId: '6',
  },
  {
    name: '软弱涣散基层党组织', code: '1112', icon: 'solution', color: '#2261FF', parent: '11', url: '/org/lax', serverId: '55',
  },
  {
    name: '行业党组织', code: '1113', icon: 'stock', color: '#EE76FF', parent: '11', url: '/org/special', serverId: '57',
  },
  {
    name: '党组(党组性质党委)管理', code: '1114', icon: 'gold', color: '#0fb625', parent: '11', url: '/org/party', serverId: '19',
  },
  {
    name: '审批预备党员权限管理', code: '1115', icon: 'stock', color: '#167f30', parent: '11', url: '/org/hasDevelopOrg', serverId: '70',
  },
  //单位管理 -- start --
  // {
  //   name: '单位概况', code: '1312', icon: 'area-chart', color: '#259CFF', parent: '13', url: '/unit/overview', serverId: '38',
  // },
  {
    // name: '已建立党组织', code: '1310', icon: 'line-chart', color: '#9273FF', parent: '13', url: '/unit', serverId: '11',
    name: '单位管理', code: '1310', icon: 'line-chart', color: '#9273FF', parent: '13', url: '/unit', serverId: '11',
  },
  // {
  //   name: '未建立党组织', code: '1311', icon: 'stock', color: '#EE76FF', parent: '13', url: '/unit/not', serverId: '12',
  // },
  // {
  //   name: '集体经济情况', code: '1313', icon: 'stock', color: '#167f30', parent: '13', url: '/unit/economic', serverId: '58',
  // },
  // {
  //   name: '村(社区)工作者和后备干部情况', code: '1314', icon: 'user', color: '#FF9106', parent: '13', url: '/unit/memManage', serverId: '62',
  // },
  //单位管理 -- end --
  {
    name: '用户管理', code: '2610', icon: 'user', color: '#259CFF', parent: '26', url: '/user/user', serverId: '7',
  },
  {
    name: '信息项锁定', code: '2613', icon: 'solution', color: '#8A7CFF', parent: '26', url: '/user/lock', serverId: '72',
  },
  // {
  //   name: '权限管理', code: '2611', icon: 'menu-unfold', color: '#2AFFDA', parent: '26', url: '/user/role', serverId: '8',
  // },
  // {
  //   name: '用户日志', code: '2612', icon: 'copy', color: '#FF9106', parent: '26', url: '/user/log', serverId: '35',
  // },
  // 人员
  // {
  //   name: '党员概况', code: '1214', icon: 'area-chart', color: '#f03eff', parent: '12', url: '/mem', serverId: '36',
  // },
  {
    name: '党员管理', code: '1210', icon: 'user', color: '#75FF31', parent: '12', url: '/mem/manage', serverId: '10',
  },
  // {
  //   name: '困难党员', code: '1211', icon: 'solution', color: '#1AC0FF', parent: '12', url: '/mem/difficulty', serverId: '18',
  // },
  // {
  //   name: '多重党员', code: '1213', icon: 'team', color: '#FF13B0', parent: '12', url: '/mem/multiple', serverId: '19',
  // },
  {
    name: '历史党员', code: '1212', icon: 'user-delete', color: '#FF9E53', parent: '12', url: '/mem/history', serverId: '17',
  },
  //流动党员
  {
    // name: '党员流出', code: '1410', icon: 'retweet', color: '#FFA35F', parent: '14', url: '/flowMem/flowBack', serverId: '14',
  },
  {
    // name: '党员流入', code: '1411', icon: 'rollback', color: '#62FF85', parent: '14', url: '/flowMem/inflows', serverId: '15',
  },
  {
    // name: '流动历史', code: '1412', icon: 'dot-chart', color: '#8A7CFF', parent: '14', url: '/flowMem/flowHistory', serverId: '16',
  },
  {
    name: '流出管理', code: '1413', icon: 'user-delete', color: '#F1A406', parent: '14', url: '/flowMem/outflowManage', serverId: '88',
  },
  {
    name: '流入管理', code: '1414', icon: 'user-add', color: '#03993F', parent: '14', url: '/flowMem/inflowManage', serverId: '89',
  },
  {
    name: '流动党组织', code: '1415', icon: 'cluster', color: '#03993F', parent: '14', url: '/flowMem/inflowOrganization', serverId: '90',
  },
  //关系转接
  // {
  //   name: '转接概况', code: '1510', icon: 'area-chart', color: '#FFBF30', parent: '15', url: '/transfer', serverId: '21',
  // },
  {
    name: '关系转出', code: '1511', icon: 'retweet', color: '#30FF5D', parent: '15', url: '/transfer/outflows', serverId: '22',
  },
  {
    name: '关系转入', code: '1512', icon: 'rollback', color: '#21FFFF', parent: '15', url: '/transfer/inflows', serverId: '23',
  },
  {
    name: '历史转出', code: '1513', icon: 'retweet', color: '#FFCE4E', parent: '15', url: '/transfer/historyout', serverId: '33',
  },
  {
    name: '历史转入', code: '1514', icon: 'rollback', color: '#A368FF', parent: '15', url: '/transfer/historyin', serverId: '34',
  },
  // 组织生活
  // {
  //   name: '活动管理', code: '3410', icon: 'file-text', color: '#A368FF', parent: '34', url: '/organizeLife/activityManage', serverId: '9',
  // },
  //党代表
  // {
  //   name: '党代表概况', code: '1610', icon: 'user', color: '#AE72FF', parent: '16', url: '/behalf', serverId: '30',
  // },
  {
    name: '党代表列表信息统计', code: '1611', icon: 'pie-chart', color: '#C16EFF', parent: '16', url: '/behalf/jcInfo', serverId: '31',
  },
  // {
  //   name: '联络机构', code: '1612', icon: 'cluster', color: '#FF67EB', parent: '16', url: '/behalf/contactAgency', serverId: '32',
  // },
  //档案管理

  {
    name: '档案管理', code: '3511', icon: 'retweet', color: '#C16EFF', parent: '35', url: '/archivesAdministration', serverId: '98',
  },
  {
    name: '档案审核', code: '3512', icon: 'pie-chart', color: '#C16EFF', parent: '35', url: '/archivesAdministration/fileReview', serverId: '99',
  },
  {
    name: '档案统计', code: '3513', icon: 'bar-chart', color: '#C16EFF', parent: '35', url: '/archivesAdministration/statistics', serverId: '100',
  },
  // 发展党员
  // {
  //   name: '发展概况', code: '1712', icon: 'bar-chart', color: '#0AFF61', parent: '17', url: '/developMem', serverId: '37',
  // },
  // {
  //   name: '发展党员', code: '1710', icon: 'user', color: '#31FFF7', parent: '17', url: '/developMem/develop', serverId: '27',
  // },
  {
    name: '入党申请人', code: '1713', icon: 'user', color: '#7083FF', parent: '17', url: '/developMem/apply', serverId: '59',
  },
  {
    name: '积极分子', code: '1714', icon: 'user-add', color: '#31FFF7', parent: '17', url: '/developMem/active', serverId: '60',
  },
  {
    name: '发展对象', code: '1715', icon: 'usergroup-add', color: '#FF3188', parent: '17', url: '/developMem/object', serverId: '61',
  },
  // ----遵义发展党员新二级
  {
    name: '入党申请人', code: '1713', icon: 'user', color: '#7083FF', parent: '17', url: '/developMem/zy/apply', serverId: '59', system: 'zy'
  },
  {
    name: '积极分子', code: '1714', icon: 'user-add', color: '#31FFF7', parent: '17', url: '/developMem/zy/active', serverId: '60', system: 'zy'
  },
  {
    name: '发展对象', code: '1715', icon: 'usergroup-add', color: '#FF3188', parent: '17', url: '/developMem/zy/object', serverId: '61', system: 'zy'
  },
// 遵义党员管理

{
  name: '党员管理', code: '1210', icon: 'user', color: '#75FF31', parent: '12', url: '/mem/zy/manage', serverId: '10', system: 'zy'
},
  {
    name: '入党对象转出', code: '1717', icon: 'retweet', color: '#FF3188', parent: '17', url: '/developMem/out', serverId: '84',
  },
  {
    name: '入党对象转入', code: '1718', icon: 'rollback', color: '#FF3188', parent: '17', url: '/developMem/into', serverId: '85',
  },
  {
    name: '本年度发展党员', code: '1716', icon: 'solution', color: '#FF8619', parent: '17', url: '/developMem/mems', serverId: '69',
  },
  // {
  //   name: '发展指标', code: '1711', icon: 'bar-chart', color: '#7083FF', parent: '17', url: '/developMem/plan', serverId: '28',
  // },
  // 党费管理
  // {
  //   name: '党费概况', code: '1810', icon: 'bar-chart', color: '#7083FF', parent: '18', url: '/dues/general', serverId: '46',
  // },
  // {
  //   name: '党费标准', code: '1811', icon: 'bar-chart', color: '#7083FF', parent: '18', url: '/dues', serverId: '40',
  // },
  // {
  //   name: '党费缴纳', code: '1812', icon: 'money-collect', color: '#06FF7A', parent: '18', url: '/dues/payList', serverId: '40',
  // },
  // {
  //   name: '党费支出', code: '1813', icon: 'pay-circle', color: '#FF8619', parent: '18', url: '/dues/spending', serverId: '42',
  // },
  // {
  //   name: '党费下拨', code: '1814', icon: 'red-envelope', color: '#FF3188', parent: '18', url: '/dues/stir', serverId: '43',
  // },
  // {
  //   name: '党费交纳', code: '1815', icon: 'money-collect', color: '#4ba2f3', parent: '18', url: '/dues/partyFeePayment', serverId: '9',
  // },
  // {
  //   name: '历史党员党费交纳', code: '1816', icon: 'history', color: '#fbc65a', parent: '18', url: '/dues/partyFeePaymentHistory', serverId: '9',
  // },
  // 活动管理
  // {
  //   name: '活动管理', code: '2510', icon: 'bar-chart', color: '#F822FF', parent: '25', url: '/activity/manage', serverId: '9',
  // },
  //工作动态
  {
    name: '动态内容', code: '1910', icon: 'bar-chart', color: '#ff6824', parent: '19', url: '/work/trend', serverId: '45',
  },
  {
    name: '动态审核', code: '1911', icon: 'api', color: '#3540ff', parent: '19', url: '/work/audit', serverId: '47',
  },
  //通知管理
  {
    name: '我收到的', code: '2110', icon: 'bar-chart', color: '#ff6824', parent: '21', url: '/notice/manage', serverId: '49',
  },
  {
    name: '我发布的', code: '2111', icon: 'api', color: '#3540ff', parent: '21', url: '/notice/mySend', serverId: '50',
  },
  //考核评价
  {
    name: '政治建设', code: '2210', icon: 'bar-chart', color: '#ff6824', parent: '22', url: '/evaluation/political', serverId: '',
  },
  {
    name: '班子建设', code: '2211', icon: 'api', color: '#3540ff', parent: '22', url: '/evaluation/team', serverId: '',
  },
  {
    name: '队伍建设', code: '2212', icon: 'red-envelope', color: '#F822FF', parent: '22', url: '/evaluation/political', serverId: '',
  },
  {
    name: '重点工作', code: '2213', icon: 'cluster', color: '#06FF7A', parent: '22', url: '/evaluation/political', serverId: '',
  },
  {
    name: '工作机制', code: '2214', icon: 'retweet', color: '#75FF31', parent: '22', url: '/evaluation/political', serverId: '',
  },
  {
    name: '宣传表彰', code: '2215', icon: 'rollback', color: '#FFCE4E', parent: '22', url: '/evaluation/political', serverId: '',
  },
  {
    name: '关怀帮扶', code: '2216', icon: 'solution', color: '#0AFF61', parent: '22', url: '/evaluation/political', serverId: '',
  },
  {
    name: '投入保障', code: '2217', icon: 'copy', color: '#7083FF', parent: '22', url: '/evaluation/political', serverId: '',
  },
  // 任务管理
  {
    name: '我的发布', code: '2010', icon: 'copy', color: '#7083FF', parent: '20', url: '/mission/manage', serverId: '',
  },
  {
    name: '我的任务', code: '2011', icon: 'solution', color: '#ff6824', parent: '20', url: '/mission/myTask', serverId: '',
  },
];
const MenuData = [
  ...HeaderMenu,
  ...SliderMenu,
];
export default MenuData;
