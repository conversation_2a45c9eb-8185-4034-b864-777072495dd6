import request from '@/utils/request';
import qs from 'qs';
// export function login(params) {
//   return request(`api/user/login?code=${params['data']['code']}`,{
//     method:'POST',
//     body:params
//   });
// }

export function getImgUrl(para) {
  return request(
    // 所有的/api/upload都换成 /api/obtain/attachment 2025.06.11 余斌（渗透测试报告问题解决）
    `/api/obtain/attachment?${qs.stringify(para)}`,
    {
      method: 'GET',
    },
    'showImg',
  );
}

export function pullFile(para) {
  return request(
    `/api/base/pullFile?${qs.stringify(para)}`,
    {
      method: 'GET',
    },
    'showImg',
  );
}
export function pullFileQz(para) {
  return request(`/api/mem/screen/pullFileQz?${qs.stringify(para)}`, {
    method: 'GET',
  }).then((res) => {
    if (res.code == 0) {
      return `/qzImage/${res.data.files}`;
    }
    return res;
  });
}
export function uploadFile(params) {
  return request(`/api/base/upload`, {
    method: 'POST',
    body: params,
  });
}
export function archivesSave(params) {
  return request(`/api/mem/archives/save`, {
    method: 'POST',
    body: params,
  });
}

export function archivesFind(para) {
  return request(`/api/mem/archives/find?${qs.stringify(para)}`, {
    method: 'GET',
  });
}

export function editPhoto(params) {
  return request(`/api/mem/editPhoto`, {
    method: 'POST',
    body: params,
  });
}

export function getProgressBar(para) {
  return request(`/api/progressBar/getProgressBar?${qs.stringify(para)}`, {
    method: 'GET',
  });
}
export function getProgress(para) {
  return request(`/api/progress/getProgress?${qs.stringify(para)}`, {
    method: 'GET',
  });
}
export function queryDictList(params) {
  return request(`/api/dictionary/queryDictionaryForList?${qs.stringify(params)}`);
}
export function createCode(params) {
  return request(`api/user/createCode`, {
    method: 'Get',
  });
}

export function getUnitByOrg(params) {
  return request(`/api/unit/getUnitByOrg?${qs.stringify(params)}`);
}

export function getDictList(params) {
  return request(`/api/dictionary/getDictionaryList`, {
    method: 'POST',
    body: params,
  });
}
export function normalList(params) {
  return request(`/api/dictionary/normalList`, {
    method: 'POST',
    body: params,
  });
}
export function getArea(params) {
  //获取地区
  return request(`/api/dictionary/getDictD48List?${qs.stringify(params)}`);
}
export function getAllArea(params) {
  //获取下级所有地区
  return request(`/api/dictionary/getDictD48JuniorList?${qs.stringify(params)}`);
}
export async function getOrgTree(params) {
  return request(`/api/org/getOrgTree`, {
    method: 'POST',
    body: params,
  });
}
export function queryOrgTree(params) {
  return request(`/api/org/findTreeByName?${qs.stringify(params)}`, {
    method: 'POST',
    body: params,
  });
}

export function getRole(params) {
  //获取用户权限信息
  return request(`/api/user`);
}
export function getMenu(params) {
  //获取菜单
  return request(`/api/permission/permissionList`, {
    method: 'Get',
  });
}
export function getPermission(params) {
  //获取权限码
  return request(`/api/permission/permissionCode`, {
    method: 'Get',
  });
}

//
export function code(params) {
  return request(`/api/login/code`, {
    method: 'Get',
  });
}
export function login(params) {
  return request('/api/login', {
    method: 'POST',
    body: params,
  });
}
export function needToChangePassword(params) {
  return request('/api/login/needToChangePassword', {
    method: 'POST',
    body: params,
  });
}
export function password(params) {
  return request('/api/user/password', {
    method: 'POST',
    body: params,
  });
}
//
export function logout(params) {
  return request('/api/logout', {
    method: 'POST',
    body: params,
  });
}

export function upPassword(params) {
  return request('/api/user/password', {
    method: 'POST',
    body: params,
  });
}
//获取表单提示信息 参数id：表名
export function tableColConfig(params) {
  return request(`/api/table/tableColConfig?${qs.stringify(params)}`);
}
export function findOrgByName(params) {
  return request(`/api/org/findOrgByName`, {
    method: 'POST',
    body: params,
  });
}
export function findFlowOrgByName(params) {
  return request(`/api/orgFlow/search`, {
    method: 'POST',
    body: params
  });
}

export function findOutsideOrgByName(params) {
  return request(`/api/org/findOutsideOrgByName`, {
    method: 'POST',
    body: params,
  });
}

export function getUnitName(params) {
  return request(`/api/unit/getUnitName`, {
    method: 'POST',
    body: params,
  });
}

export function geitCard({ idCard, name }) {
  if (process.env.idCheck == 'false') {
    return new Promise((resolve) => {
      resolve({ code: 200 });
    });
  }
  return request(`/api/user/checkIdCard?${qs.stringify({ idCard, name })}`).then((res) => {
    // return {code:200,message:'库中无此号'}
    return res['data'] || { code: 101 };
  });
}


export function getUniqueCodeProcess(para) {
  return request(`/api/transfer/findStatusOnUniqueCode?${qs.stringify(para)}`, {
    method: 'GET',
  });
}

export function getFlowUniqueCode(para) {
  return request(`/api/zyMem/findMemFlowStatus?${qs.stringify(para)}`, {
    method: 'GET',
  });
}
//获取序列码绑定的用户名
export function getMpkEYUserName(para) {
  return request(`/api/user/findByUkey?${qs.stringify(para)}`, {
    method: 'GET',
  });
}
//密评序列码 请求生成随机数
export function getMpkEYUserRandomNumbers(para) {
  return request(`/api/login/genRandomData?${qs.stringify(para)}`, {
    method: 'GET',
  });
}
//密评序列码 后端验证
// export function getMpkEYUserVerifySignedData(para) {
//   return request(`/api/login/verifySignedData?${qs.stringify(para)}`, {
//     method: 'GET',
//   });
// }
export function getMpkEYUserVerifySignedData(params) {
  return request(`/api/login/verifySignedData`, {
    method: 'POST',
    body: params,
  });
}
