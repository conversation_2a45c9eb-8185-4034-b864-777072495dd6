import React, { Fragment, useImperativeHandle, useState } from 'react';
import Date from '@/components/Date';
import { Col, Form, Input, Modal, Alert, Button, Popconfirm } from 'antd';
import { unixMoment } from '@/utils/method.js'
import DictTreeSelect from '@/components/DictTreeSelect';
import { joinPartyRevise } from '@/pages/mem/services';
import Tip from '@/components/Tip';
import moment from 'moment';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const index = (props: any, ref) => {
  const { title = '标题', onOK } = props;
  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [canEdit, setCanEdit] = useState({
    applyDate:false,
    activeDate:false,
    objectDate:false,
    joinOrgDate:false,
    fullMemberDate:false
  })

  const handleCancel = () => {
    setRecord({});
    setVisible(false);
    form.resetFields();
  };
  const onFinish = async (e) => {
    let time = ['applyDate', 'activeDate', 'objectDate', 'joinOrgDate', 'fullMemberDate'];
    e = unixMoment(time, e);
    setConfirmLoading(true);
    const { code = 500 } = await joinPartyRevise({
      data: {
        code: record?.code,
        memOrgCode:record?.memOrgCode,
        ...e,
      }
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOK && onOK();
    }
  };
  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      setRecord(query);
      if (query) {
        const { lockFields=[] } = query;
        const arr = ['applyDate', 'activeDate', 'objectDate', 'joinOrgDate', 'fullMemberDate']
        let newCanEdit = {...canEdit}
        lockFields.map((item:any)=>{
          if(arr.includes(item)){
            newCanEdit[item] = true;
            setCanEdit({
              ...newCanEdit
            })
          }
        })
        form.setFieldsValue({ ...query });
      }
    },
    clear: () => {
      // clear();
    },
  }));

  const TimeValidator = (rule, value, callback) => {
    if (value) {
      if (moment(value) > moment('2020.12.31')) {
        callback('不能填2021年及以后的时间');
      }
    }
    callback();
  }
  const TimeValidator1= (rule, value, callback) => {
    if (value) {
      if (moment(value).isBefore(moment().startOf('day'))) {
        callback();
      }else{
        callback('只能填写今天以前的日期');
      }
    }
  }

  return (
    <Modal
      title={'入党时间修正'}
      visible={visible}
      // onOk={() => {
      //   form.submit()
      // }}
      onCancel={handleCancel}
      width={800}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
      footer={[
        <Button key={1} htmlType={'button'} onClick={handleCancel} style={{ marginRight: '5px' }}>取消</Button>,
        <Popconfirm placement="topRight" title={<span>
          本功能仅用于原库中党员入党时间有误的修正，请谨慎操作。
        </span>} onConfirm={() => { form.submit() }}>
          <Button key={2} htmlType={'button'} type={'primary'} loading={confirmLoading}>确定</Button>
        </Popconfirm>,
      ]}
    >
      {
        visible &&
        <Fragment>
          <Alert message="本功能仅用于原库中党员入党时间有误的修正，请谨慎操作。" type="info" showIcon />
          <div style={{ marginBottom: '10px' }} />
          <Form form={form} {...formItemLayout} onFinish={onFinish}>
            <Form.Item name='applyDate'
              label="申请入党时间"
              rules={[{ required: false, message: '申请入党时间' },
              //  { validator: TimeValidator }
              ]}
            >
              <Date disabled={canEdit['applyDate']} />
            </Form.Item>

            <Form.Item name='activeDate'
              label="确定积极分子时间"
              rules={[{ required: false, message: '确定积极分子时间' },
              // { validator: TimeValidator }
            ]}
            >
              <Date disabled={canEdit.activeDate} />
            </Form.Item>

            <Form.Item name='objectDate'
              label="确定发展对象时间"
              rules={[{ required: false, message: '确定发展对象时间' },
              // { validator: TimeValidator }
            ]}
            >
              <Date disabled={canEdit.objectDate} />
            </Form.Item>

            <Form.Item name='joinOrgDate'
              label="确定为预备党员时间"
              rules={[{ required: !canEdit['joinOrgDate'], message: '确定为预备党员时间' },
              // { validator: TimeValidator1 }
            ]}
            >
              <Date disabled={canEdit.joinOrgDate} />
            </Form.Item>
            {
              record?.d08Code == '1' &&
              <Form.Item name='fullMemberDate'
                label="成为正式党员时间"
                rules={[{ required: !canEdit['fullMemberDate'], message: '成为正式党员时间' }]}
              >
                <Date disabled={canEdit.fullMemberDate} />
              </Form.Item>
            }
          </Form>
        </Fragment>
      }

    </Modal>
  )
};
export default React.forwardRef(index);
