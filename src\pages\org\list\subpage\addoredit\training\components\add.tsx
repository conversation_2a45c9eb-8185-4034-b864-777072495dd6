import React, { useState, useImperativeHandle, useEffect, Fragment } from 'react';
import { Modal, Form, Input, InputNumber, Row, Col, Select } from 'antd';
import { getPageConfig } from '../components/config';
import Tip from '@/components/Tip';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import { findCountYear } from '@/pages/[unit]/subpage/addoredit/extendInfo/extendInfoAll/services';
const formItemLayout = {
  labelCol: {
    xs: { span: 12 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 10 },
    sm: { span: 10 },
  },
};
const formItemLayout2 = {
  labelCol: { span: 14 },
  wrapperCol: { span: 8 },
};
const formItemLayout3 = {
  labelCol: { span: 0 },
  wrapperCol: { span: 22 },
};
const formItemLayout4 = {
  labelCol: { span: 7 },
  wrapperCol: { span: 16 },
};
const YearValidator = (rule, value, callback) => {
  let rep = /^\d{4}$/;
  if (!rep.test(value)) {
    return callback('请输入正确年份');
  } else {
    return callback();
  }
};
const NowYear = new Array(5).fill(1).map((it, index) => +moment().format('YYYY') - index);

const index = React.forwardRef((props: any, ref) => {
  const { title = '', width = 1200, onOK, pageType = '', org: { basicInfo = {} } = {} } = props;
  const { submitUrl } = getPageConfig(pageType) || {};

  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [query, setQurey] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [defaultYear, setDefaultYear] = useState<any>();
  useImperativeHandle(ref, () => ({
    open: (query = {}) => {
      setVisible(true);
      setQurey(query);
      if (_isEmpty(query)) {
        form.resetFields();
        getYears().then((yearTime) => {
          console.log('yearTime===', yearTime);
          form.setFieldsValue({ year: yearTime });
        });
      } else {
        form.setFieldsValue({ ...query, year: query['year'] });
        setDefaultYear(query['year']);
      }
    },
    clear: () => {
      // clear();
    },
  }));
  const getYears = async () => {
    const res = await findCountYear({});
    const year = res?.data?.year ? parseInt(res?.data?.year) : undefined;
    setDefaultYear(year);
    return year;
  };
  useEffect(() => {}, [JSON.stringify(query)]);
  const handleOk = async (e) => {
    console.log('e====', e);
    let flag = true;
    if (pageType === '1') {
      let arr = [
        'newTrain',
        'youthTrain',
        'a3',
        'elderlyTrain',
        'flowTrain',
        'laidOffTrain',
        'minorityAreasTrain',
        'organizationSecretary',
        'countyPartyCommittee',
        'levelOrganizationsSecretary',
        'communityParty',
      ];
      let errors: any = [];
      arr.forEach((it) => {
        if (e[it] > e['trainTotal']) {
          errors = ['数字必须小于总培训人次'];
          form.setFields([{ value: e[it], errors, name: it }]);
          flag = false;
        }
      });
    }
    if (pageType === '3') {
      let arr = ['xxlydypx', 'qtdypx', 'lddypx'];
      let errors: any = [];
      arr.forEach((it) => {
        if (e[it] > e['trainTotal']) {
          errors = ['数字必须小于总培训人次'];
          form.setFields([{ value: e[it], errors, name: it }]);
          flag = false;
        }
      });
    }

    if (flag) {
      setLoading(true);
      const { code = 500 } = await submitUrl({
        data: {
          ...e,
          year: +e['year'],
          code: query?.code,
          orgOrgCode: basicInfo['orgCode'],
          orgCode: basicInfo['code'],
        },
      });
      setLoading(false);
      if (code == 0) {
        Tip.success('操作提示', '操作成功');
        setVisible(false);
        clear();
        onOK && onOK();
      }
    }
  };
  const handleCancel = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setQurey({});
    setLoading(false);
    form.resetFields();
  };
  return (
    <Fragment>
      <Modal
        title={`${_isEmpty(query) ? '新增' : '编辑'}培训情况`}
        visible={visible}
        onOk={() => form.submit()}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        confirmLoading={loading}
      >
        {pageType === '1' && (
          <Form63
            form={form}
            onFinish={handleOk}
            {...props}
            // defaultYear={defaultYear}
            // changeDefaultYear={setDefaultYear}
          />
        )}
        {pageType === '2' && (
          <Form1
            form={form}
            onFinish={handleOk}
            {...props}
            defaultYear={defaultYear}
            changeDefaultYear={setDefaultYear}
          />
        )}
        {pageType === '3' && (
          <Form61
            form={form}
            onFinish={handleOk}
            {...props}
            defaultYear={defaultYear}
            changeDefaultYear={setDefaultYear}
          />
        )}
      </Modal>
    </Fragment>
  );
});

export default index;

const validFunction = (rule, value, callback) => {
  if (!/^[0-9]+$/.test(value)) {
    callback('只能是整数');
  }
  if (/\s+/g.test(value)) {
    callback('不能包含空格');
  }
  callback();
};
const Form63 = (props: any) => {
  const { form, onFinish, org: { basicInfo = {} } = {} } = props;
  const { d01Code = '', d02Code = '' } = basicInfo;
  let val = d02Code == '2' ? basicInfo['linkedDTOListUpOrg'] || [] : basicInfo['linkedDTOList'];
  let findSchool = val.find(
    (it) => it.unit.d04Code.startsWith('33') || it.unit.d04Code.startsWith('521'),
  );
  return (
    <Form form={form} {...formItemLayout} onFinish={onFinish}>
      <Row>
        <Col span={12}>
          <Form.Item name="year" label="年度" rules={[{ required: true, message: '请填写' }]}>
            <Select style={{ width: '100%' }}>
              {NowYear.map((it) => (
                <Select.Option value={it}>{it}</Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="trainTotal"
            label="总培训党员人次"
            rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>

        {findSchool && (
          <Col span={12}>
            <Form.Item
              name="schoolTrain"
              label="学校党员培训人次"
              rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        )}
        <Col span={12}>
          <Form.Item
            name="newTrain"
            label="新党员培训人次"
            rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="youthTrain"
            label="青年党员培训人次"
            rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="elderlyTrain"
            label="老年党员培训人次"
            rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="flowTrain"
            label="流动党员培训人次"
            rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="laidOffTrain"
            label="下岗失业人员中党员培训人次"
            rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="minorityAreasTrain"
            label="民族地区党员培训人次"
            rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="organizationSecretary"
            label="党组织书记培训人次"
            rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <LongLabelFormItem
            label={'党组织书记参加县级以上党委集中轮训人次'}
            required={true}
            code={'countyPartyCommittee'}
            tipMsg={{}}
            formItemLayout={formItemLayout}
            formItem={(formItemLayout, code) => {
              return (
                <Form.Item
                  name={code}
                  {...formItemLayout}
                  rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
                >
                  <InputNumber min={0} style={{ width: '100%' }} />
                </Form.Item>
              );
            }}
          />
        </Col>
        <Col span={12}>
          <Form.Item
            name="levelOrganizationsSecretary"
            label="新任基层党组织书记培训人次"
            rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="communityParty"
            label="社区党务工作者培训人次"
            rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="hasWillLesson"
            label="是否按照规定开展三会一课"
            rules={[{ required: true, message: '请选择' }]}
          >
            <Select style={{ width: '100%' }}>
              <Select.Option value={1}>是</Select.Option>
              <Select.Option value={0}>否</Select.Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="hasPartyDay"
            label="是否按照按照规定开展主题党日"
            rules={[{ required: true, message: '请选择' }]}
          >
            <Select style={{ width: '100%' }}>
              <Select.Option value={1}>是</Select.Option>
              <Select.Option value={0}>否</Select.Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};
const Form1 = (props: any) => {
  const { form, onFinish, org: { basicInfo = {} } = {}, defaultYear, changeDefaultYear } = props;
  const { d01Code = '' } = basicInfo;
  return (
    <Form form={form} {...formItemLayout2} onFinish={onFinish}>
      <Row>
        <Col span={24}>
          <Form.Item
            name="year"
            label="年度"
            {...formItemLayout4}
            rules={[{ required: true, message: '请填写' }]}
          >
            <Select
              style={{ width: '100%' }}
              onChange={(value) => {
                changeDefaultYear(value);
              }}
            >
              {NowYear.map((it) => (
                <Select.Option value={it}>{it}</Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        {/* 2024年字段↓ */}
        {/* 暂无 */}
        {/* 旧的字段↓ */}
        {defaultYear !== 2024 && (
          <Fragment>
            {
              // 当组织类别为13开头的时候，不显示省级举办培训班(期)县级举办培训班(期)以及培训(人次)
              // 当类别为14开头的时候，不现实省级举办培训班(期)市级举办培训班(期)以及培训人次；
              !d01Code.startsWith('13') && !d01Code.startsWith('14') && (
                <Row style={{ width: '100%' }}>
                  <Col span={12}>
                    <Form.Item
                      name="provincialTrainClass"
                      label="省级举办培训班(期)"
                      rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="provincialTrainMember"
                      label="培训(人次)"
                      rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              )
            }

            {
              // 当组织类别为12开头的时候，不显示市级举办培训班(期) 县级举办培训班(期
              // 当类别为14开头的时候，不现实省级举办培训班(期)市级举办培训班(期)以及培训人次；
              !d01Code.startsWith('12') && !d01Code.startsWith('14') && (
                <Row style={{ width: '100%' }}>
                  <Col span={12}>
                    <Form.Item
                      name="cityTrainClass"
                      label="市级举办培训班(期)"
                      rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="cityTrainMember"
                      label="培训(人次)"
                      rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              )
            }

            {
              // 当组织类别为12开头的时候，不显示市级举办培训班(期) 县级举办培训班(期
              // 当组织类别为13开头的时候，不显示省级举办培训班(期)县级举办培训班(期)以及培训(人次)
              !d01Code.startsWith('12') && !d01Code.startsWith('13') && (
                <Row style={{ width: '100%' }}>
                  <Col span={12}>
                    <Form.Item
                      name="countyTrainClass"
                      label="县级举办培训班(期)"
                      rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="countyTrainMember"
                      label="培训(人次)"
                      rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              )
            }
          </Fragment>
        )}
        {/* 1.当党组织类别为地方委员会的时候，不需要展示和填写信息项(基层党委举办培训班(期)) */}
        {/* <Row style={{ width: '100%' }}>
          <Col span={12}>
            <Form.Item name='levelPartyClass'
              label='基层党委举办培训班(期)'
              rules={[{ required: true, message: '请填写' }]}
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name='levelPartyMember'
              label='培训(人次)'
              rules={[{ required: true, message: '请填写' }]}
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row> */}

        {/* <Col span={12}>
          <Form.Item name='ruralPartyVillages'
            label='直接组织开展农村党员集中培训的乡镇(个)'
            rules={[{ required: true, message: '请填写' }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col> */}
        {/* <Col span={12}>
          <Form.Item name='remoteEducation'
            label='党员干部现代远程教育终端站点(个)'
            rules={[{ required: true, message: '请填写' }]}
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col> */}
      </Row>

      {
        //当党组织类别为省级（12）和市级地方委员会（13）的时候，不需要展示和填写
        !d01Code.startsWith('12') && !d01Code.startsWith('13') && (
          <Row style={{ marginTop: 20 }}>
            <Col span={1} />
            <Col span={22}>
              {/* 2024 且 组织类别d01Code 14开头 增加字段： */}
              {defaultYear === 2024 && d01Code.startsWith('14') && (
                <Fragment>
                  乡镇、街道等基层党校共
                  <div style={{ display: 'inline-block' }}>
                    <Form.Item
                      name="xzjddjcdxg"
                      {...formItemLayout3}
                      rules={[{ required: true, message: '请输入' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: 100 }} />
                    </Form.Item>
                  </div>
                  个，其中乡镇党校
                  <div style={{ display: 'inline-block' }}>
                    <Form.Item
                      name="xzdx"
                      {...formItemLayout3}
                      rules={[{ required: true, message: '请输入' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: 100 }} />
                    </Form.Item>
                  </div>
                  个，街道党校
                  <div style={{ display: 'inline-block' }}>
                    <Form.Item
                      name="jddx"
                      {...formItemLayout3}
                      rules={[{ required: true, message: '请输入' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: 100 }} />
                    </Form.Item>
                  </div>
                  个。现场教学点
                  <div style={{ display: 'inline-block' }}>
                    <Form.Item
                      name="xcjxd"
                      {...formItemLayout3}
                      rules={[{ required: true, message: '请输入' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: 100 }} />
                    </Form.Item>
                  </div>
                  个。
                </Fragment>
              )}
              党员干部现代远程教育终端站点共
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="remoteEducation"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              个，其中乡镇（街道）
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="remoteEducationVillages"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              个，行政村
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="remoteEducationAdministrativeVillage"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              个，区（居委会）
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="remoteEducationCommittee"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              个； 通过互联网传播
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="internet"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              个，有线
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="wired"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              个，卫星
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="satellite"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              个；站点管理员共
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="siteAdministrator"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              名，其中乡镇（街道）干部
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="villagesCadres"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              名，村、社区干部
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="villageCommunity"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              ，志愿者
              <div style={{ display: 'inline-block' }}>
                <Form.Item
                  name="volunteers"
                  {...formItemLayout3}
                  rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                >
                  <Input style={{ width: 100 }} />
                </Form.Item>
              </div>
              名。
              {defaultYear !== 2024 && (
                <Fragment>
                  农村党员远程教育培训党员
                  <div style={{ display: 'inline-block' }}>
                    <Form.Item
                      name="ruralRemoteEducationParty"
                      {...formItemLayout3}
                      rules={[{ required: true, message: '请输入' }, { validator: validFunction }]}
                    >
                      <Input style={{ width: 100 }} />
                    </Form.Item>
                  </div>
                  人次。
                </Fragment>
              )}
            </Col>
          </Row>
        )
      }
    </Form>
  );
};

const Form61 = (props: any) => {
  const { form, onFinish, org: { basicInfo = {} } = {}, defaultYear, changeDefaultYear } = props;
  const { d01Code = '', d02Code = '' } = basicInfo;
  let val = d02Code == '2' ? basicInfo['linkedDTOListUpOrg'] || [] : basicInfo['linkedDTOList'];
  // 事业单位：d04code 3开头
  let findSy = val.find((it) => it.unit.d04Code.startsWith('3'));
  // 企业：d04code 4开头
  let findQy = val.find((it) => it.unit.d04Code.startsWith('4'));
  return (
    <Form form={form} {...formItemLayout2} onFinish={onFinish}>
      <Row>
        <Col span={24}>
          <Form.Item
            name="year"
            label="年度"
            {...formItemLayout4}
            rules={[{ required: true, message: '请填写' }]}
          >
            <Select
              style={{ width: '100%' }}
              onChange={(value) => {
                changeDefaultYear(value);
              }}
            >
              {NowYear.map((it) => (
                <Select.Option value={it}>{it}</Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        {/* 2024的字段 */}
        {defaultYear === 2024 && (
          <Fragment>
            <Row style={{ width: '100%' }}>
              <Col span={12}>
                <Form.Item
                  name="trainTotal"
                  label="总培训党员人次"
                  rules={[{ required: true, message: '请填写' }]}
                >
                  <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="xxlydypx"
                  label="新兴领域党员培训"
                  rules={[{ required: true, message: '请填写' }]}
                >
                  <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="qtdypx"
                  label="其他党员培训"
                  rules={[{ required: true, message: '请填写' }]}
                >
                  <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="lddypx"
                  label="流动党员培训"
                  rules={[{ required: true, message: '请填写' }]}
                >
                  <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="levelOrganizationsSecretary"
                  label="基层党组织书记培训人次"
                  rules={[{ required: true, message: '请填写' }]}
                >
                  <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              {/* 事业单位增加字段 */}
              {findSy && (
                <Fragment>
                  <Col span={12}>
                    <Form.Item
                      name="xcsxwhxtdypx"
                      label="宣传思想文化系统党员培训"
                      rules={[{ required: true, message: '请填写' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="jyxtdypx"
                      label="教育系统党员培训"
                      rules={[{ required: true, message: '请填写' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="xsdypx"
                      label="学生党员培训"
                      rules={[{ required: true, message: '请填写' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="kyjgdypx"
                      label="科研机构党员培训"
                      rules={[{ required: true, message: '请填写' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="yywsxtdypx"
                      label="医药卫生系统党员培训"
                      rules={[{ required: true, message: '请填写' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Fragment>
              )}
              {/* 企业增加字段 */}
              {findQy && (
                <Fragment>
                  <Col span={12}>
                    <Form.Item
                      name="gyqydypx"
                      label="国有企业党员培训"
                      rules={[{ required: true, message: '请填写' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="fgyzqydypx"
                      label="非公有制企业党员培训"
                      rules={[{ required: true, message: '请填写' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="jrqydypx"
                      label="金融企业党员培训"
                      rules={[{ required: true, message: '请填写' }]}
                    >
                      <InputNumber min={0} precision={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Fragment>
              )}
            </Row>
            <Row style={{ width: '100%', marginTop: 20 }}>
              <Col span={22} offset={2}>
                乡镇党员（不包括乡镇社区党员）培训
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="xzdypx"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                人次。
              </Col>
            </Row>
            <Row style={{ width: '100%' }}>
              <Col span={22} offset={2}>
                举办脱产培训班共
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="jbtcpxbgq"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                期、
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="jbtcpxbgrc"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                人次；其中视频培训班
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="sppxbq"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                期、
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="sppxbrc"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                人次。
              </Col>
            </Row>
            <Row style={{ width: '100%' }}>
              <Col span={22} offset={2}>
                组织集体学习共
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="zzjtxxgc"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                次，其中讲党课
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="jdkc"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                次，举办专题讲座、报告会等
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="jbztjzbghc"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                场。
              </Col>
            </Row>
            <Row style={{ width: '100%' }}>
              <Col span={22} offset={2}>
                党员年度集中学习培训学时达标
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="dyndjzxxpxxsdb"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                人。
              </Col>
            </Row>
            <Row style={{ width: '100%' }}>
              <Col span={22} offset={2}>
                基层党组织书记年度集中学习培训学时达标
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="jcdzzsjndjzxxpxxsdb"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                人，参加县级以上党委举办的脱产培训学时达标
                <div style={{ display: 'inline-block' }}>
                  <Form.Item
                    name="cjxjysdwjbdtcpxxsdb"
                    {...formItemLayout3}
                    rules={[{ required: true, message: '请输入' }]}
                  >
                    <InputNumber min={0} precision={0} style={{ width: 100 }} />
                  </Form.Item>
                </div>
                人。
              </Col>
            </Row>
          </Fragment>
        )}
        {/* 旧的字段 */}
        {defaultYear !== 2024 && (
          <Row style={{ width: '100%' }}>
            <Col span={12}>
              <Form.Item
                name="levelPartyClass"
                label="基层党委举办培训班(期)"
                rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="levelPartyMember"
                label="培训(人次)"
                rules={[{ required: true, message: '请填写' }, { validator: validFunction }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        )}
      </Row>
    </Form>
  );
};
