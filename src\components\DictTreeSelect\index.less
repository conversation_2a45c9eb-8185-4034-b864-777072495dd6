.box {
  width: 100%;
  display: flex;
  .icon {
    flex: 1;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
    cursor: pointer;
  }
}
@pagehight: 600px;
.Layout {
  height: @pagehight!important;
  :global {
    .splitter-layout {
      // position: absolute;
      display: flex;
      flex-direction: row;
      width: 100%;
      height: @pagehight;
      overflow: hidden;
    }

    .splitter-layout .layout-pane {
      position: relative;
      flex: 0 0 auto;
      overflow: hidden;
    }

    .splitter-layout .layout-pane.layout-pane-primary {
      flex: 1;
      // flex: 1 1 auto;
    }

    .splitter-layout > .layout-splitter {
      flex: 0 0 auto;
      width: 4px;
      height: 100%;
      cursor: col-resize;
      background-color: #f5f5f5;
    }

    .splitter-layout .layout-splitter:hover {
      background-color: #bbb;
    }

    .splitter-layout.layout-changing {
      cursor: col-resize;
    }

    .splitter-layout.layout-changing > .layout-splitter {
      background-color: #aaa;
    }

    .splitter-layout.splitter-layout-vertical {
      flex-direction: column;
    }

    .splitter-layout.splitter-layout-vertical.layout-changing {
      cursor: row-resize;
    }

    .splitter-layout.splitter-layout-vertical > .layout-splitter {
      width: 100%;
      height: 4px;
      cursor: row-resize;
    }
  }
  .tree {
    height: 100%;
    overflow: auto;
  }
  .table {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    .infos {
      padding: 10px 0;
    }
    .ListTable {
      :global {
        .ant-table-thead > .ant-checkbox-wrapper {
          display: none !important;
        }
      }
    }
  }
}

.dropdownClass {
  // color: green;
  :global {
    // .ant-select-tree-node-content-wrapper-normal {
    //   margin-left: 24px;
    // }
    .ant-select-tree .ant-select-tree-treenode-disabled .ant-select-tree-node-content-wrapper {
      cursor: pointer;
    }
    .ant-select-tree-switcher-noop {
      width: 0;
    }
    .ant-select-tree-indent {
      width: 0;
    }
    // .rootNode {
    //   .ant-select-tree-switcher-noop {
    //     width: 24px;
    //   }
    // }

    .more-options-button {
      // position: absolute;
      // bottom: 0;
      // left: 0;
      // right: 0;
      text-align: center;
      padding: 4px 0;
      background: #fff;
      border-top: 1px solid #f0f0f0;
      color: #1890ff;
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;

      &:hover {
        background: #f5f5f5;
      }

      .arrow-icon {
        margin-left: 4px;
        font-size: 12px;
      }
    }
  }
}

// 添加自定义类名用于定位
:global(.dict-tree-select-dropdown-) {
  &:global(.dict-tree-select-dropdown-#{codeType}) {
    // 用于标识特定的下拉框
  }
}