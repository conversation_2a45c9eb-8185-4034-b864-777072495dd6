import React, { useEffect, useImperativeHandle, useState } from 'react';
import {
  Button,
  DatePicker,
  Divider,
  Form,
  Input,
  Modal,
  Popconfirm,
  Tag,
  Transfer,
  Alert,
} from 'antd';
import DictSelect from '@/components/DictSelect';
import { outManageRevoke } from '../../service/index';
import OrgSelect from '@/components/OrgSelect';
import ListTable from '@/components/ListTable';
import Tip from '@/components/Tip';
import Date from '@/components/Date';
import moment from 'moment';
function unDo({ callBack = () => { } }, ref) {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [record, setRecord] = useState<any>({});
  const [d207Code, setD207Code] = useState({});
  useImperativeHandle(ref, () => ({
    open: (val: any) => {
      setVisible(true);
      setRecord(val)
      form.resetFields();
    },
  }));
  const onCancel = () => {
    form.resetFields();
    setVisible(false);
  };
  const onOk = async (val) => {
    console.log("🚀 ~ onOk ~ val:", val)
    const { cancelTime, cancelReasonRecord } = val
    // val['date'] = val['date'].valueOf();
    const rData = { cancelTime: moment(cancelTime).valueOf(), d207Code, code: record?.code, cancelReasonRecord }
    console.log("🚀 ~ onOk ~ val:", rData, moment(cancelTime).valueOf())
    // return
    const { code = 500 } = await outManageRevoke({
      data: rData
    });
    if (code == 0) {
      Tip.success('操作提示', '操作成功');
      setVisible(false);
      callBack()
    }

  };
  return (
    <React.Fragment>
      <Modal
        destroyOnClose
        title={'撤销提示'}
        visible={visible}
        onOk={() => form.submit()}
        onCancel={onCancel}
        width={600}
        bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
      >
        {visible && (
          <Form
            form={form}
            onFinish={onOk}
            {...{ labelCol: { span: 6 }, wrapperCol: { span: 17 } }}
          >
            <Form.Item
              label={'撤销原因'}
              name={'d207Code'}
              rules={[{ required: true, message: '请输入撤销原因' }]}
              style={{ marginBottom: 15 }}
            >
              <DictSelect
                disabled={false}
                codeType={'dict_d207'}
                onChange={
                  (e) => {
                    setD207Code(e)
                    form.setFieldsValue({
                      d207Code: e
                    })
                  }
                }
              // backType={'object'}
              // initValue={this.state.account2?.id}
              />
            </Form.Item>
            <Form.Item
              label={'撤销原因详情'}
              name={'cancelReasonRecord'}
              rules={[{ required: true, message: '请输入撤销原因详情' }]}
            >
              <Input.TextArea rows={4} placeholder='请输入撤销原因详情' />
            </Form.Item>
            <Form.Item
              label={'撤销日期'}
              name={'cancelTime'}
              rules={[{ required: true, message: '请输入撤销日期' }]}
            >
              {/* <DatePicker style={{width:'100%'}}/> */}
              <Date />
            </Form.Item>
          </Form>
        )}
      </Modal>
    </React.Fragment>
  );
}
export default React.forwardRef(unDo);
