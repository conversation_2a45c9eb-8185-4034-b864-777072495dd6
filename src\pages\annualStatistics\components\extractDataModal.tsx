import React ,{ Fragment, useImperativeHandle, useState, useEffect } from 'react';
import {Modal,message} from 'antd';
import ListTable from '@/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import {getExtractPegging} from '../services'
const extractDataModal = React.forwardRef((props:any, ref) => {
  const {
    title = '数据提取',
    width = 1200,
    onOK,
  } = props;
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState([]);
  const [query, setQurey] = useState<any>({});
  const [loading, setLoading] = useState<any>(false);
  const [pagination, setPagination] = useState({ pageSize: 10, total: 0, pageNum: 1 });
  const [copeData, setCopeData] = useState<any>([]);
  useImperativeHandle(ref, () => ({
    open: query => {
      setQurey(query);
      getLists({ pageNum: 1,...query }).then(()=>{
        setVisible(true);
      });
    },
    clear: () => {
      // clear();
    },
  }));
  // useEffect(() => {
  //   if (!_isEmpty(query)) {
  //     getLists({ pageNum: 1 }).then();
  //   }
  // }, [JSON.stringify(query)])
  useEffect(() => {
    console.log(list);
    if(list.length > 0){
      let data = list.map((item:any) => item.total)
      setCopeData(data)
    }
  },[list])
  const getLists = async (p = {}) => {
    const {
      code = 500,
      data = [],
      // data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await getExtractPegging({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.pageNum,
        ...p,
      }
    });
    if (code === 0) {
      setList(data);
      // setPagination({ pageNum, total, pageSize });
    }
  };
  const handleOk = () => {
    onOK && onOK(query);
    handleCancel();
  };
  const handleCancel = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setQurey({});
    setList([]);
    setPagination({ pageSize: 10, total: 0, pageNum: 1 })
  };
  const columns = [
    {
      title:'组织名称',
      dataIndex:'orgName',
      width:200,
      render: (text,record) => {
        return <span style={{userSelect:'none'}}>{text}</span>
      }
    },
    {
      title:'数量',
      dataIndex:'total',
      width:200,
      render: (text,record) => {
        return <span style={{cursor:'pointer'}} onContextMenu={(e:any) => copeColData(e)}>{text}</span>
      }
    },
  ]
  // const copeColData = (e) => {
  //   // 点击数量的一个需要复制一列 -- 直接屏蔽 2025-1-2
  //   e.preventDefault()
  //   navigator.clipboard.writeText(copeData.join('\n')).then(() => {
  //     message.success('复制成功')
  //   })
  // }
  // const copeColData = (e) => {
  //   // 点击数量的一个需要复制一列 -- 直接屏蔽 2025-1-2
  //   console.log("navigator",navigator);
  //   e.preventDefault()
  //   const textArea = document.createElement('textarea');
  //   textArea.value = copeData.join('\n');
  //   console.log(textArea.value);
  //   document.body.appendChild(textArea);
  //   textArea.select();
  //   try {
  //     document.execCommand('copy');
  //     console.log('文本已复制到剪贴板！');
  //     message.success('复制成功')
  //   } catch (err) {
  //     console.error('复制失败：', err);
  //   } finally {
  //     document.body.removeChild(textArea);
  //   }
  // }
  const copeColData = async (e) => {
    e.preventDefault();

    const text = copeData.join('\n');
    console.log("navigator",navigator);
    console.log(text);
    if (navigator.clipboard) {
        try {
          await navigator.clipboard.writeText(text);
          message.success('文本已复制到剪贴板！');
        } catch (err) {
          console.error('复制失败：', err);
        }
      } else {
        // 没有就使用 document.execCommand('copy')
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
          document.execCommand('copy');
          message.success('文本已复制到剪贴板！');
        } catch (err) {
          console.error('复制失败：', err);
        } finally {
          document.body.removeChild(textArea);
        }
      }
  }
  return (
    <Fragment>
      <Modal
        title={title}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={width}
        footer={null}
        destroyOnClose={true}
      >
        <ListTable
          
          columns={columns}
          data={list}
          pagination={false}
          // pagination={pagination}
          // onPageChange={(page:any, pageSize:any) => {
          //   getLists({ pageNum:page, pageSize });
          // }}
          scroll={{y:500}}
          rowKey={record=>record['code']}
        />
      </Modal>
    </Fragment>
  )
});
export default extractDataModal;
