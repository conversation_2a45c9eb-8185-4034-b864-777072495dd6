import React, { useEffect, useImperativeHandle, useState } from 'react';
import {
  <PERSON><PERSON>,
  DatePicker,
  Divider,
  Form,
  Input,
  Modal,
  Popconfirm,
  Tag,
  Transfer,
  Alert,
} from 'antd';
import OrgSelect from '@/components/OrgSelect';
import ListTable from '@/components/ListTable';
import Tip from '@/components/Tip';
import { dissolve, dissolveMsg } from '../../services/org';
import Date from '@/components/Date';
import moment from 'moment';
function unDo(props, ref) {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [visible2, setVisible2] = useState(false);
  const [edit, setEdit] = useState({});
  const [list, setList] = useState([]);
  const [formVal, setFormVal] = useState({});
  useImperativeHandle(ref, () => ({
    open: (val: any) => {
      setEdit(val);
      setVisible(true);
      form.resetFields();
    },
  }));
  const onCancel = () => {
    form.resetFields();
    setList([]);
    setVisible(false);
    setVisible2(false);
  };
  const onOK2 = (params?: any) => {
    const { callBack } = props;
    console.log({
      code: edit['code'],
      ...formVal,
      ...params
    },'formValformVal')
    dissolve({
      data: {
        code: edit['code'],
        ...formVal,
        ...params
      },
    }).then((res) => {
      if (res['code'] == 0) {
        Tip.success('操作提示', '组织撤销成功');
        onCancel();
        callBack && callBack();
      }
    });
  };
  const onOk =  (val) => {
    val['date'] = val['date'].valueOf();
     setFormVal(val);
    dissolveMsg({
      data: {
        code: edit['code'],
      },
    }).then((res) => {
      const { data = [] } = res;
      if (data.length > 0) {
        setList(data);
        setVisible(false);
        setVisible2(true);
      } else {
        onOK2(val);
      }
    });
  };
  const columns = [
    {
      title: '数据名称',
      dataIndex: 'value',
      width: 200,
    },
    {
      title: '数据详情',
      dataIndex: 'desc',
      render: (text) => {
        return <div style={{ whiteSpace: 'pre-wrap' }}>{text}</div>;
      },
    },
    {
      title: '数据时间',
      dataIndex: 'time',
      width: 100,
      render: (text) => {
        return text ? moment(text).format('YYYY.MM.DD') : '';
      },
    },
  ];
  return (
    <React.Fragment>
      <Modal
        destroyOnClose
        title={'组织撤销'}
        visible={visible2}
        onOk={()=>onOK2({})}
        onCancel={onCancel}
        width={800}
        bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
      >
        <Alert message="该组织任有牵连数据，请确认是否继续撤销！" type="warning" showIcon />
        <div style={{ height: 12 }} />
        <ListTable scroll={{ y: '55vh' }} columns={columns} data={list} />
      </Modal>
      <Modal
        destroyOnClose
        title={'组织撤销'}
        visible={visible}
        onOk={() => form.submit()}
        onCancel={onCancel}
        width={600}
        bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
      >
        {visible && (
          <Form
            form={form}
            onFinish={onOk}
            {...{ labelCol: { span: 4 }, wrapperCol: { span: 19 } }}
          >
            <Form.Item
              label={'撤销文号'}
              name={'documentNum'}
              rules={[{ required: false, message: '请输入撤销文号' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              label={'撤销日期'}
              name={'date'}
              rules={[{ required: true, message: '请输入撤销日期' }]}
            >
              {/* <DatePicker style={{width:'100%'}}/> */}
              <Date />
            </Form.Item>
            <Form.Item
              label={'撤销原因'}
              name={'reason'}
              rules={[{ required: true, message: '请输入撤销原因' }]}
            >
              <Input />
            </Form.Item>
          </Form>
        )}
      </Modal>
    </React.Fragment>
  );
}
export default React.forwardRef(unDo);
