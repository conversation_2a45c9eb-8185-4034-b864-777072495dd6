/**
 * 扩展信息
 */
/**
 * 模块名
 */
import React from 'react';
import '@ant-design/compatible/assets/index.css';
import BasicExtension from './basicExtension';
import MzpyInfo from './mzpyInfo';
import DypxInfo from './dypxInfo';
import { Col, Input, Button, Switch, Row, InputNumber, Tabs } from "antd";
const { TabPane } = Tabs;

class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      data: {}
    }
  }
  componentDidMount() {
    this.findInfo()
  }
  findInfo = () => {
    const { basicInfo } = this.props.org
    if (basicInfo['code']) {
      this.props.dispatch({
        type: 'org/findExtendByCode',
        payload: {
          orgCode: basicInfo['orgCode'],
        },
      }).then(res => {
        this.setState({ data: res })
      });
    }
  }
  callback = () => {
    this.findInfo()
  }
  render() {
    return (
      <Tabs defaultActiveKey="1" onChange={this.callback} style={{padding:'0 16px'}}>
        <TabPane tab="基础扩展信息" key="1" >
          <BasicExtension {...this.props} data={this.state.data} onOK={()=>this.findInfo()}/>
        </TabPane>
        <TabPane tab="民主评议党员情况" key="2">
          <MzpyInfo {...this.props} data={this.state.data} onOK={()=>this.findInfo()}/>
        </TabPane>
        <TabPane tab="党员培训情况" key="3">
          <DypxInfo {...this.props} data={this.state.data} onOK={()=>this.findInfo()}/>
        </TabPane>
      </Tabs>
    );
  }
}
export default index;
