/**
 * 关联组织
 */
import React from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Table, Button, Switch } from "antd";
import OrgSelect from '@/components/OrgSelect';
import { getSession } from '@/utils/session';


export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      initial:1,
      data:[
        {
          id:'1',
          isOrgMain:0,
        }
      ]
    }
  }
  propsChange=(data)=>{
    this.setState({
      data,
      initial:0,
    });
    let resData=[...data];
    resData=resData.filter(obj=>obj['org'] || obj['code']);
    const {onChange}=this.props;
    onChange && onChange(resData)
  };
  add=()=>{
    let {data}=this.state;
    let obj={id:`${data.length+1}`,org:'',isOrgMain:0,};
    data.push(obj);
    this.setState({
      data,
    });
  };
  del=(obj)=>{
    let {data}=this.state;
    data=data.filter(ob=>ob['id']!==obj['id']);
    this.propsChange(data);
  };
  onChange=(val,obj)=>{
    let {data}=this.state;
    const index=data.findIndex(ob=>ob['id']===obj['id']);
    if(index>-1){
      data[index]={...data[index],org:val[0]}
    }
    this.propsChange(data);
  };
  switchChange=(e,obj)=>{
    let {data}=this.state;
    const index=data.findIndex(ob=>ob['id']===obj['id']);
    if(index>-1){
      data[index]={...data[index],isOrgMain:e?1:0}
    }
    this.propsChange(data);
  };
  static getDerivedStateFromProps = (props:any, state:any) => {
    const {data}=props;
    const {initial}=state;
    if(data && initial){
      return {data}
    }
    return null;
  };
  render(){
    let {data}=this.state;
    const {
      orgSelectDisabled = false,
      addDisabled = false,
      delDisabled = false,
      isMainDisabled = false,
      renderTableCol
    } = this.props;
    const columns=[
      {
        title:'序号',
        dataIndex:'id',
        align:'center',
        width:58,
        render:(text,record,index)=>{
          return index+1;
        }
      },
      {
        title:'关联党组织',
        dataIndex:'unitName',
        align:'center',
        width:258,
        render:(text,record)=>{
          return(
            <OrgSelect key={record['id']}
                        org={getSession('org')}
                        oorg={{orgCode: getSession('org').orgCode, subordinate:0}}
                       disabled={orgSelectDisabled}
                       initValue={record['orgName'] ? record['orgName'] : record['org'] ? record['org']['name'] : undefined}
                       onChange={(val)=>this.onChange(val,record)}/>
          )
        }
      },
      {
        title:'是否主要组织',
        dataIndex:'isOrgMain',
        width:78,
        align:'center',
        render:(text,record)=>{
          return(
            <Switch key={record['id']} defaultChecked={record['isOrgMain']===1} disabled={isMainDisabled} onChange={(e)=>this.switchChange(e,record)}/>
          )
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        align:'center',
        width:58,
        render:(text,record,index)=>{
          return(
            <span>
              {
                !delDisabled ?
                  <a className={'del'} onClick={()=>this.del(record)}>删除</a>:
                  null
              }
            </span>
          )
        }
      },
    ];
    return (
      <React.Fragment>
        <Table
          bordered={true}
          columns={ !renderTableCol ? columns as any : renderTableCol(columns)}
          dataSource={[...data]}
          pagination={false}
          rowKey={record=>record['id']}
          footer={()=>{
            return (
              <div style={{textAlign:'center'}}>
                <Button type="primary" onClick={this.add} disabled={addDisabled} style={{ width: '30%' }} size={'small'}>
                  <PlusOutlined />点击添加
                </Button>
              </div>
            );
          }}
          size={'middle'}
        />
      </React.Fragment>
    );
  }
}
