/**
 * 考核评价-政治建设
 */
import React from 'react';
import {connect} from "dva";
import ListTable from 'src/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';
import { CheckCircleOutlined } from '@ant-design/icons';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Input, Tabs, Modal, DatePicker, Progress, Select } from 'antd';
import NowOrg from 'src/components/NowOrg';
import moment from 'moment';
import { isEmpty, setListHeight } from '@/utils/method';
import Notice from '@/components/Tip';
import request from "@/utils/request";
import {getSession} from "@/utils/session";
import styles from './index.less';
import ListFilter from '@/pages/dues/payList';
const Search = Input.Search;
const TabPane = Tabs.TabPane;
const Option = Select.Option;
const { MonthPicker, } = DatePicker;

@connect(({loading,evaluation})=>({evaluation,loading:loading.effects['unit/getList']}))
export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
        tab:'1',
      years:moment().valueOf()
    };
  }

  componentDidMount() {
    const org=getSession('org') || {};
    this.setState({orgCode:org['orgCode']});
    setListHeight(this);
    this.selectList(1 ,10,org['orgCode']);
  }

  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org=getSession('org') || {};
    if (!isEmpty(this.state['orgCode'])&&this.state['orgCode']!==org['orgCode']) {
      this.setState({
        orgCode:org['orgCode']
      },()=>{
        this.selectList(1,10,org['orgCode'])
      })
    }
  }
  export=(list)=>{
    if (isEmpty(list)) {
      Notice.error('操作提示','暂无数据')
    }else {
      let action='';
      const { tab }=this.state;
      let modal = this.setTimeOut();
      switch (tab) {
        case '1':
          action='';
          break;
        case '2':
          action='/api/data/political/exportHreMeetingsAndOneClass';
          break;
        case '3':
          action='/api/data/political/exportThematicPartyDay';
          break;
        case '4':
          action='';
          break;
        case '5':
          action='';
          break;
      }
      request(action,{
        method:'POST',
        body:{
          data:{
            orgCode:this.state['orgCode']
          }
        },
      },'file').then(res=>{
        if(res['status']===200){
          clearInterval(this['timer']);
          modal.update({
            icon:<CheckCircleOutlined style={{color:'#52C41A'}} />,
            title: '下载完成',
            okButtonProps: {
              disabled: false,
            },
            onOk:()=>{
            },
            content:(
              <div>
                <Progress percent={100}/>
              </div>
            ),
          });
        }
      });
    }
  };
  setTimeOut=()=>{
    let secondsToGo = 0;
    const modal = Modal.info({
      title: '正在导出中，请稍等...',
      okButtonProps: {
        disabled: true,
      },
      okText:'知道了',
      content: (
        <div>
          <Progress percent={secondsToGo}/>
        </div>
      ),
    });

    this['timer'] = setInterval(() => {
      secondsToGo += 1;
      if(secondsToGo >= 99){
        secondsToGo = 99;
        clearInterval(this['timer']);
        modal.update({
          content:(
            <div>
              <Progress percent={secondsToGo}/>
            </div>
          ),
        });
      }
      modal.update({
        content:(
          <div>
            <Progress percent={secondsToGo}/>
          </div>
        ),
      });
    }, 200);

    return modal;
  };
  selectList=( pageNum=1,size=10,code='')=>{
    this.props.dispatch({
      type:'evaluation/list',
      payload:{
      data:{
        pageNum:pageNum,
        pageSize:size,
        orgCode:code,
        endTime:this.state['years'],
      },
        tab:this.state['tab']
      }
    })
  };

  onPageChange=(page,pageSize)=>{
    this.selectList(page,pageSize,this.state.orgCode)
    // let {query}=this.props.location;
    // router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };

  changeTab=(e)=>{
    this.setState({tab:e},()=>{
      this.selectList(1,10,this.state.orgCode);
    })
  };
  show=(record,key,value)=>{
    const { monthList }=record;
    let obj={};
    monthList&&monthList.map((item,index)=>{
      let find = monthList.filter(it=>it['month']==key);
      if (!isEmpty(find)) {
        obj=item
      }else {
        obj={}
      }
      return obj
    });
    if (isEmpty(obj)){
      return
    } else {
      return obj[value]
    }
  };
  disabledTomorrow=(current) =>{
    // Can not select days before today and today
    return current && current < moment('2019')||current>moment().endOf('day');
  };
  onChangeYear=(val)=>{
    this.setState({years:val},()=>{
      this.selectList(1,10,this.state['orgCode']);
    });
  };


  render() {
    const {loading,commonDict,evaluation:{list=[],pagination:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={}}={}}=this.props;
    const {dataInfo,filterHeight,detailInfo={},tab}=this.state;
    const monthFormat = 'YYYY';
    let years=parseInt(moment().format('YYYY'));
    let yearArr:any=[];
    for (let i:any=2019;i<=years;i++){
      yearArr.push(i)
    }
    let filterWidth=0;
    let columns:any=[];
    const columns0=[
      {
        title:'序号',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'党统单位',
        dataIndex:'memOrgName',
        width:200,
      },
      {
        title:'党组织',
        dataIndex:'outflowOrgName',
        width:200,
      },
      {
        title:'学习时间及时长',
        dataIndex:'isProvOutName',
        width:100,
      },
      {
        title:'地点',
        dataIndex:'outflowDate',
        width:100,
      },
      {
        title:'学习内容',
        dataIndex:'outflowDate',
        width:200,
      },
      {
        title:'领学人',
        dataIndex:'outflowDate',
        width:100,
      },
      {
        title:'参会人数',
        dataIndex:'outflowDate',
        width:100
      },
      {
        title:'附学习照片',
        dataIndex:'outflowDate',
        width:100,
      },
      {
        title:'备注',
        dataIndex:'outflowDate',
        width:200,
      },
    ];
    const columns1=[
      {
        title:'序号',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'党统单位',
        dataIndex:'countOrgName',
        width:200,
      },
      {
        title:'党组织名称',
        dataIndex:'orgName',
        width:200,
      },
      {
        title:'时间',
        dataIndex:'holdTime',
        width:100,
        render:text=>{
          return <span>{moment(text).format('YYYY-MM-DD')}</span>
        }
      },
      {
        title:'地点',
        dataIndex:'address',
        width:200,
      },
      {
        title:'会议议题',
        dataIndex:'name',
        width:200,
      },
      {
        title:'会议类别',
        dataIndex:'typeNames',
        width:100,
      },
      {
        title:'主持人',
        dataIndex:'compere',
        width:100
      },
      {
        title:'记录人',
        dataIndex:'creatorAccount',
        width:100,
      },
      {
        title:'参会人数',
        dataIndex:'meetingCount',
        width:200,
      },
      {
        title:'备注',
        dataIndex:'remark',
        width:200,
      },
    ];
    const columns2=[
      {
        title:'序号',
        width:60,
        fixed: 'left',
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'党统单位',
        dataIndex:'countOrgName',
        width:220,
      },
      {
        title:'单位名称',
        dataIndex:'countOrgName',
        width:200,
      },
      {
        title:'支部名称',
        dataIndex:'orgName',
        width:200,
      },
      {
        title: '一月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'1','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'1','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'1','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'1','meetingCount'))
            }
          },
        ]
      },
      {
        title: '二月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'2','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'2','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'2','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'2','meetingCount'))
            }
          },
        ]
      },
      {
        title: '三月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'3','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'3','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'3','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'3','meetingCount'))
            }
          },
        ]
      },
      {
        title: '四月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'4','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'4','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'4','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'4','meetingCount'))
            }
          },
        ]
      },
      {
        title: '五月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'5','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'5','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'5','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'5','meetingCount'))
            }
          },
        ]
      },
      {
        title: '六月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'6','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'6','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'6','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'6','meetingCount'))
            }
          },
        ]
      },
      {
        title: '七月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'7','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'7','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'7','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'7','meetingCount'))
            }
          },
        ]
      },
      {
        title: '八月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'8','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'8','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'8','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'8','meetingCount'))
            }
          },
        ]
      },
      {
        title: '九月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'9','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'9','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'9','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'9','meetingCount'))
            }
          },
        ]
      },
      {
        title: '十月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'10','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'10','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'10','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'10','meetingCount'))
            }
          },
        ]
      },
      {
        title: '十一月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'11','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'11','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'11','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'11','meetingCount'))
            }
          },
        ]
      },
      {
        title: '十二月',
        children: [
          {
            title: '日',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'12','day'))
            }
          },
          {
            title: '主题',
            width: 100,
            render:(text,record)=>{
              return (this.show(record,'12','name'))
            }
          },
          {
            title: '支部党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'12','orgMemCount'))
            }
          },
          {
            title: '参加党员数',
            width: 50,
            render:(text,record)=>{
              return (this.show(record,'12','meetingCount'))
            }
          },
        ]
      },
    ];
    const columns3=[
      {
        title:'序号',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'评价内容',
        dataIndex:'memOrgName',
        width:500,
      },
      {
        title:'评价等次',
        dataIndex:'outflowOrgName',
        width:200,
      },
    ];
    const columns4=[
      {
        title:'序号',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },
      {
        title:'单位名称',
        dataIndex:'memOrgName',
        width:200,
      },
      {
        title:'党组织名称',
        dataIndex:'outflowOrgName',
        width:200,
      },
      {
        title:'姓名',
        dataIndex:'isProvOutName',
        width:200,
      },
      {
        title: '问题清单',
        dataIndex: '1',
        width: 150,
      },
      {
        title: '整改措施',
        dataIndex: '13',
        width: 150,
      },
      {
        title: '整改时限',
        dataIndex: '11',
        width: 150,
      },
      {
        title: '备注',
        dataIndex: '12',
        width: 150,
      }
    ];
    switch (tab) {
      case '1':
        columns=columns0;
        break;
      case '2':
        columns=columns1;
        break;
      case '3':
        columns=columns2;
        if (isEmpty(list)) {
          filterWidth=0;
        }else {
          filterWidth=3000;
        }
        break;
      case '4':
        columns=columns3;
        break;
      case '5':
        columns=columns4;
        break;
    }
    return (
      <div className={styles.showHead}>
        <Tabs defaultActiveKey={tab} onChange={(e)=>this.changeTab(e)} type="card">
          <TabPane tab="集中学习" key="1"/>
          <TabPane tab="三会一课" key="2"/>
          <TabPane tab="主题党日" key="3"/>
          <TabPane tab="民主生活会" key="4"/>
          <TabPane tab="问题整改" key="5"/>
        </Tabs>
        {/*<OutWithdraw onChange={this.changeList} data={this.state['record']} filterHeight={filterHeight} wrappedComponentRef={(e)=>this['OutWithdraw']=e}/>*/}
        {/*<OutRegistrationDetail onChange={this.changeList} wrappedComponentRef={(e)=>this['RegistrationDetail']=e}/>*/}
        <NowOrg extra={
          <React.Fragment>
              <Select
                showSearch
                style={{ width: 200 ,marginRight:20}}
                placeholder="Select a person"
                defaultValue="2019"
                onChange={ this.onChangeYear}
              >
                {
                  yearArr.map((item,index)=>{
                    return <Option key={index} value={`${item}`}>{item}</Option>
                  })
                }
              </Select>

            <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={()=>this.export(list)}>导出</Button>
          </React.Fragment>
        }/>
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable
          key={tab}
          scroll={{x:filterWidth,y:filterHeight}}
          
          columns={columns}
          data={list}
          pagination={{pageSize,total:totalRow,page,current:pageNumber}}
          onPageChange={this.onPageChange}/>
      </div>
    );
  }
}
