// 流动党员-流出管理
import React, { Fragment } from 'react';
import { Tabs } from 'antd';
import { getSession } from '@/utils/session';
import MemberInformation from "./memberInformation"
import DevMemberInformation from "./devMemberInformation"

const TabPane = Tabs.TabPane;

export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: '1',
    };
  }
  render() {
    const { activeTab } = this.state;
    return (
      <Fragment>
        <Tabs
          activeKey={activeTab}
          onChange={(e) => {
            this.setState({
              activeTab: e,
            });
          }}
        >
          <TabPane tab="党员信息" key="1" />
          <TabPane tab="发展党员信息" key="2" />
        </Tabs>
        {activeTab === '1' && <MemberInformation activeTab={activeTab} org={getSession('org')} />}
        {activeTab === '2' && <DevMemberInformation activeTab={activeTab} org={getSession('org')} />}
      </Fragment>
    );
  }
}
