import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import style from './index.less';
import { Button, Form, Alert, Modal, Space, Upload, Input, Select } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import Tip from '@/components/Tip';
import moment from 'moment';
import { processNodeNext } from '@/pages/developMem/services'
import { _history } from "@/utils/method";
import MemSelect from '@/components/MemSelect';

const { Option } = Select;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};

const index: any = (props, ref) => {
    const {
        change,
        upList
    } = props;

    useImperativeHandle(ref, () => ({
        showModal: (obj) => {
            open(obj);

        },
        closeModal: () => { close() }
    }));
    const org: any = getSession('org') || {};
    const [form] = Form.useForm();
    const { login: { listTree = [] } = {} } = props;
    const [loading, setLoading] = useState(false);
    const [scloading, setScloading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [accountvisible, setAccountvisible] = useState(false)
    const [accountvisible1, setAccountvisible1] = useState(false)
    const [baseInfo, setBaseInfo]: any = useState({})
    const [fileArr, setFileArr]: any = useState([])
    const [rows, setRows]: any = useState({})//列表点进来不进上传页面
    const [formDatas, setFormDatas]: any = useState({})
    const [nodetype, setNodeType]: any = useState('')//当前节点
    const [title, setTitle]: any = useState('')//
    const { location: { pathname = '' } = {} } = _history
    const open = (obj) => {
        const { rowCode = '', file: { value = [], name = '' } = {}, data = {}, row, formData = {}, type = '' } = obj;
        console.log(data, row, type, rowCode, value, name, 'valuevaluevalue')
        setTitle(name)
        setBaseInfo(data)
        setFormDatas(formData)
        setModalVisible(true)
        setFileArr(value)
        setNodeType(type)
        if (rowCode) {
            getData(rowCode, row)
            setRows(row)
        }
    }
    const getData = async (codes, row) => {
        // let act = zyfindByCode
        // let obj = {
        //     code: codes
        // }
        // if (pathname == '/mem/zy/manage') {
        //     act = zymemInfo
        //     obj = {
        //         memCode: codes
        //     }
        // }
        // const { code = 500, data = {} } = await act(obj)
        // if (code == 0) {
        //     setBaseInfo({ ...baseInfo, ...data })
        //     if (row?.code) {
        //         let arr = (data?.filesList || []).map(i => {
        //             i.filesList.map(j => {
        //                 j['pathurl'] = j.path
        //                 return j
        //             })
        //             return i
        //         })
        //         setFileArr(arr)
        //     }

        // }
    }
    const cancel = () => {
        setModalVisible(false)
        setFileArr([])
        setRows({})
        setBaseInfo({})
    }
    const confirm = async () => {
        if (rows?.code) {
            if (rows?.processNode == 'YBQ_6') {
                setScloading(true)
                const { code = 500 } = await processNodeNext({
                    data: {
                        code: rows.code,
                        processNode: rows.processNode
                    }
                })
                setScloading(false)
                if (code == 0) {
                    Tip.success('操作提示', '操作成功')
                    setAccountvisible(false)
                    cancel()
                    upList && upList()
                }
            } else {
                setAccountvisible1(true)
            }
        } else {
            let flag = []
            fileArr.forEach((item) => {

                item.fileList.map(i => {
                    if (i.id) {
                        flag.push(i.id)
                    }
                })
            })
            if (flag.length > 0) {
                setAccountvisible(true)
            } else {
                change && change({ isClose: true })
            }

        }

    }
    const confirm1 = async () => {
        // setScloading(true)
        // const { code = 500 } = await processNodeNext({
        //     data: {
        //         code: rows.code,
        //         processNode: rows.processNode
        //     }
        // })
        // setScloading(false)
        // if (code == 0) {
        //     Tip.success('操作提示', '审查已通过')
        //     setAccountvisible1(false)
        //     cancel()
        //     upList && upList()
        // }
    }

    const onFinish = async (e) => {
        const { oprationUser, ...other } = e
        setLoading(true)
        change && change({node: {oprationUser:oprationUser[0]['name'],oprationCode:oprationUser[0]['code'],  ...other }, isClose: false, developStepLogDTO: formDatas, memInfo: baseInfo })
    }
    const close = () => {
        form.resetFields()
        setBaseInfo({})
        setModalVisible(false)
        setAccountvisible(false)
        setLoading(false)
        setTitle('')
    }
    useEffect(() => {
        console.log(baseInfo, 'baseInfoba111seInfobaseInfo')
    }, []);

    return (
        <React.Fragment>
            <Modal
                title={title}
                destroyOnClose
                visible={modalVisible}
                onCancel={cancel}
                onOk={confirm}
                width={'1200px'}
            >
                <div className={style.tables}>
                    <table>
                        <colgroup>
                            <col style={{ width: 160 }} />
                            <col style={{ width: 160 }} />
                            <col style={{ width: 160 }} />
                            <col style={{ width: 160 }} />
                            <col style={{ width: 160 }} />
                            <col style={{ width: 160 }} />
                        </colgroup>
                        <tbody>
                            <tr>
                                <td colSpan={6} className={style.label} style={{ textAlign: 'center', color: '#3D3D3D', fontWeight: 'bold' }}>{baseInfo.name}基本信息</td>
                            </tr>
                            <tr>
                                <td className={style.label}>人员姓名</td>
                                <td colSpan={2}>{baseInfo.name}</td>
                                <td className={style.label}>性别</td>
                                <td colSpan={2}>{baseInfo.sexName}</td>
                            </tr>
                            <tr>
                                <td className={style.label}>出生日期</td>
                                <td colSpan={2}>{baseInfo.birthday && moment(baseInfo.birthday).format('YYYY-MM-DD')}</td>
                                <td className={style.label}>籍贯</td>
                                <td colSpan={2}>{baseInfo.d48Name}</td>
                            </tr>
                            <tr>
                                <td className={style.label}>民族</td>
                                <td colSpan={2}>{baseInfo.d06Name}</td>
                                <td className={style.label}>当前工作岗位</td>
                                <td colSpan={2}>{baseInfo.d09Name}</td>
                            </tr>
                            <tr>
                                <td className={style.label}>当前学历情况</td>
                                <td colSpan={2}>{baseInfo.d07Name}</td>
                                <td className={style.label}>知识分子情况</td>
                                <td colSpan={2}>{baseInfo.d154Name}</td>
                            </tr>
                            <tr>
                                <td className={style.label}>政治面貌</td>
                                <td colSpan={2}>{baseInfo.politicsName || baseInfo.d89Name}</td>
                                <td className={style.label}>工作性质</td>
                                <td colSpan={2}>{baseInfo?.jobNatureCodeName || baseInfo?.jobNatureName}</td>
                            </tr>
                            <tr>
                                <td className={style.label}>一线情况</td>
                                <td colSpan={2}>{baseInfo.d21Name}</td>
                                <td className={style.label}>人事关系是否在<br />党组织关联单位</td>
                                <td colSpan={2}>{baseInfo.hasUnitStatistics == '1' ? '是' : '否'}</td>
                            </tr>
                            <tr>
                                <td className={style.label}>现居住地</td>
                                <td colSpan={5}><div>{baseInfo.homeAddress}</div></td>
                            </tr>
                            {
                                fileArr.length > 0 &&
                                <tr>
                                    <td colSpan={6} className={style.label} style={{ textAlign: 'center', color: '#3D3D3D', fontWeight: 'bold' }}>上传材料</td>
                                </tr>
                            }

                            {
                                fileArr.map((item, index) => {

                                    return (
                                        <tr key={index}>
                                            <td className={style.label}>{item.d222Name}</td>
                                            <td colSpan={5} style={{ display: 'table-cell' }}>
                                                <div style={{ width: 770, overflow: 'auto' }}>
                                                    <div className={style.imgBox} style={{ width: (item?.fileList || item?.filesList).length * 200 }}>
                                                        {
                                                            (item?.fileList || item?.filesList).map((i, k) => {
                                                                if (i['isDelete'] != 1) {
                                                                    return (
                                                                        <div key={k}><img src={`${window.location.origin}/${i?.previewPath || ''}`} /></div>
                                                                    )
                                                                }
                                                            })
                                                        }
                                                    </div>
                                                </div>


                                            </td>
                                        </tr>
                                    )
                                })
                            }
                        </tbody>


                    </table>
                </div>
            </Modal>
            <Modal
                title={`审查`}
                visible={accountvisible1}
                onCancel={() => {
                    setAccountvisible1(false)
                }}
                footer={null}
                zIndex={1001}
            >
                <div style={{ textAlign: 'center' }}>
                    <p>请确认是否审查通过</p>
                    <Space>
                        <Button onClick={() => { setAccountvisible1(false) }}>取消</Button>
                        <Button type='primary' loading={scloading} onClick={confirm1}>确认</Button>
                    </Space>
                </div>
            </Modal>
            <Modal
                title={`资料上传`}
                visible={accountvisible}
                confirmLoading={loading}
                onOk={() => {
                    form.submit()
                }
                }
                onCancel={() => {
                    setAccountvisible(false)
                }}
                zIndex={1001}
            >
                <Form form={form} {...formItemLayout} onFinish={onFinish} style={{ marginBottom: 10 }}>
                    <Form.Item name='oprationUser'
                        label="操作人"
                        rules={[{ required: true, message: '请填写操作人' }]}
                    >
                        {/* <Input allowClear style={{ width: 260 }} /> */}
                            <MemSelect checkType={'radio'} placeholder="请选择"  d08CodeList={['1']} />
                    </Form.Item> 
                    {
                        nodetype == 'JJ_3' && <Form.Item name='nextProcessNode'
                            label="流转节点"
                            rules={[{ required: true, message: '请选择流转节点' }]}
                        >
                            <Select style={{ width: 260 }} >
                                <Option value={'JJ_5'}>持续考察人员</Option>
                                <Option value={'JJ_6'}>上级党委备案</Option>
                            </Select>
                        </Form.Item>
                    }

                </Form>
            </Modal>
        </React.Fragment>

    );
};
// @ts-ignore
export default React.forwardRef(index);
