import _map from 'lodash/map';
import _startsWith from 'lodash/startsWith';
import _isEmpty from 'lodash/isEmpty';
import { getSession } from '@/utils/session';

// 对象数组变为字符串数组
export function getArrCodes(arr,str,filter?:any) {
  let newArr:Array<any> = [];
  if(!_isEmpty(arr)){
    arr.map(item=>{
      newArr.push(item[str])
    })
  }
  return newArr;
}
// 更新自定义数字的length
export function self(arr,value) {
  arr.map(item=>{
    if(!_isEmpty(value[item])){
      value[item] = value[item].map(item=>item)
    }
  });
  return value;
}
// 是否党委
export function isDW(isEdit,orgType?:any) {
  let isDW = true;
  if(isEdit){
    if(orgType === '4' || orgType === '3'){
      isDW = false
    }
  }else {
    const currentOrg = getSession('org') || {};
    if(currentOrg['orgType'] === '4' || currentOrg['orgType'] === '3'){
      isDW = false
    }
  }
  return isDW;
}
// 活动类型判断
export function chooseTypes(dict,isEdit,detail?:object) {
  let dictAcTypes:Array<any> = [];
  dictAcTypes =_isEmpty(dict) ? [] : dict.filter(item=> item['parent'] != '-1');
  //@ts-ignore
  const {orgType = ''} = detail || {};
  if( isDW(isEdit,orgType) ){
    dictAcTypes = dictAcTypes.filter(item=>item['parent'] != '2' );
  }else {
    dictAcTypes = dictAcTypes.filter(item=>item['parent'] != '1' );
  }
  return dictAcTypes
}
// reason组件，返回值的数据格式
export function changeAbsenceListb(arr) {
  let newArr:Array<any> = [];
  if(!_isEmpty(arr)){
    arr.map(item=>{
      newArr.push({
        isCustomize: 0,
        name:item['name'],
        memCode:item['code'],
        memOrgCode:item['orgCode'],
        memOrgOrgCode:item['memOrgCode'],
        // idcard:item['idcard'],
        reason:item['reason']
      })
    })
  }
  return newArr;
}
// 保存时 通用人员数据格式
export function changeMemStyle(arr,isCustomize) {
  let newArr:Array<any> = [];
  if(!_isEmpty(arr)){
    arr.map(item=>{
      if(isCustomize === '0'){
        newArr.push({
          isCustomize,
          name:item['name'],
          memCode:item['code'],
          memOrgCode:item['orgCode'],
          memOrgOrgCode:item['memOrgCode'],
          // idcard:item['idcard']
        })
      }else {
        newArr.push({
          isCustomize,
          name:item['name'],
          phone:item['phone'],
          // idcard:item['idcard'],
          remark:item['remark']
        })
      }

    })
  }
  return newArr;
}
// 字符串数组转换成对象数组
export function returnOringalObject(codeArr,allArr) {
  let newArr:Array<any> = [];
  if(!_isEmpty(codeArr) && !_isEmpty(allArr)){
    allArr.forEach(item=>{
      codeArr.forEach(it=>{
        if(item['code'] == it){
          newArr.push(item)
        }
      })
    })
  }
  return newArr;
}
// 编辑回显时 返回成自己需要的通用人员格式
export function reventChangeMemStyle(arr,isCustomize,isAbsence = false) {
  let newArr:Array<any> = [];
  if(!_isEmpty(arr)){
    arr.map(item=>{
      if(isCustomize === '0'){
        if(isAbsence){
          newArr.push({
            isCustomize,
            name:item['name'],
            code:item['memCode'],
            orgCode:item['memOrgCode'],
            memOrgCode:item['memOrgOrgCode'],
            // idcard:item['idcard'],
            reason:item['reason']
          })
        }else {
          newArr.push({
            isCustomize,
            name:item['name'],
            code:item['memCode'],
            orgCode:item['memOrgCode'],
            memOrgCode:item['memOrgOrgCode'],
            // idcard:item['idcard']
          })
        }
      }else {
        newArr.push({
          isCustomize,
          name:item['name'],
          phone:item['phone'],
          // idcard:item['idcard'],
          remark:item['remark']
        })
      }
    })
  }
  return newArr;
}
// 编辑回显时 缺席人员的数据格式
export function getAbsenceStyle(arr) {
  let newArr:Array<any> = [];
  if(!_isEmpty(arr)){
    arr.map(item=>{
      newArr.push({
        isCustomize:item['name'],
        name:item['name'],
        memCode:item['memCode'],
        memOrgCode:item['memOrgCode'],
        memOrgOrgCode:item['memOrgOrgCode'],
        // idcard:item['idcard'],
        reason:item['reason']
      })
    })
  }
  return newArr;
}
// 党小组人员总集合格式
export function changeDxzStyle(arr) {
  let newArr:Array<any>= [];
  if(!_isEmpty(arr)){
    arr.map(item=>{
      newArr.push({
        name:item['memName'],
        code:item['memCode'],
        groupCode:item['groupCode'],
        memOrgCode:item['memOrgCode'],
        memOrgOrgCode:item['memOrgOrgCode'],
      })
    })
  }
  return newArr;
}
// 保存时党小组 应到人员格式
export function changeDxzMemStyle(arr) {
  let newArr:Array<any> = [];
  if(!_isEmpty(arr)){
    arr.map(item=>{
      newArr.push({
        isCustomize:0,
        name:item['name'],
        memCode:item['code'],
        memOrgCode:item['orgCode'],
        memOrgOrgCode:item['memOrgCode'],
        // idcard:item['idcard'],
        groupCode:item['groupCode'],
      })
    })
  }
  return newArr;
}
// 支委会人员总集合格式
export function changeZwhStyle(arr) {
  let newArr:Array<any>= [];
  if(!_isEmpty(arr)){
    arr.map(item=>{
      newArr.push({
        name:item['memName'],
        code:item['memCode'],
        electCode:item['electCode'],
        orgCode:item['orgCode'],
        memOrgCode:item['positionOrgCode'],
      })
    })
  }
  return newArr;
}
// 保存时支委会 应到人员格式
export function returnZwhMemStyle(arr) {
  let newArr:Array<any> = [];
  if(!_isEmpty(arr)){
    arr.map(item=>{
      newArr.push({
        isCustomize:0,
        name:item['name'],
        memCode:item['code'],
        memOrgCode:item['orgCode'],
        memOrgOrgCode:item['memOrgCode'],
        electCode:item['electCode'],
      })
    })
  }
  return newArr;
}
