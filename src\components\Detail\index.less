.pageAll {
  //padding-bottom: 30px;
  .page {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    .main {
      flex: 1;
      padding:0 0 0 10px;
      background: #fff;
    }
    .listInfo {
      background: #fff;
      margin-left: 2px;
      width: 30%;
      height: auto;
    }
  }
}

:global( .detail-title .h1)
{
  margin: 0 10px 0 0;
  display: inline;
  font-size: 34px;
  line-height: 1.15;
  font-weight: 400;
  vertical-align: sub;

}

:global(.detail-title .h2)
{
  margin: 0 10px 0 -10px;
  display: inline;
  font-weight: 400;
  font-size: 20px;
  vertical-align: sub;
}
:global(.info-card-inner)
{
  text-align: center;
}

:global(.info-card-inner .img:first-child)
{
  width: 40px;
  height: 40px;
}

:global(.info-detail)
{
  padding: 20px;
}

:global(.segment)
{
  margin-left: -3px;
}

:global .detail-briefly .ant-row{
  margin-top: 10px;
  min-height:27px;
  border-bottom: 1px dashed #D9D9D9;
  padding:0 0 0 10px
}
:global .detail-briefly .ant-col-5{
  font-weight: 700;
  color: #999;
}
:global .detail-briefly .ant-col-7{
  color: #333;
}
