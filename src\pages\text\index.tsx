import React from 'react';
import ListTable from '@/components/ListTable';
import { Tabs, Input, Select, Tree, Modal, Switch, Button, Checkbox, Popconfirm } from 'antd';
import moment from 'moment'
import { connect } from 'dva';
import _get from 'lodash/get'
import { isEmpty, changeListPayQuery } from '@/utils/method';
// import { success } from '@/components/Notice';
import Pegging from './components/Pegging';
import Tip from '@/components/Tip';
import ReportExplainModal from '../annualStatistics/components/reportExplainModal';
const { TabPane } = Tabs;
const { Option } = Select;
const TreeNode = Tree.TreeNode;
const { TextArea } = Input;
const { Search } = Input;
@connect(({ tmwTable }) => ({
  tmwTable
}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      rowCell: [
        {
          id: new Date().valueOf()
        }
      ],
      colCell: [
        {
          id: new Date().valueOf() + 1
        }
      ],
      tableIndexConfigList: [
        {
          id: new Date().valueOf() + 2
        }
      ],
      lineConfigList: [
        {
          id: new Date().valueOf() + 3
        }
      ],
      visible: false,
      loading: false,
      key: 'html_1'
    }
  }
  componentDidMount = () => {
    this.select(this.state.key);
    let script = document.createElement("script"), _this = this;
    script.type = "text/javascript";
    script.src = '/js/check.js';
    document.body.appendChild(script);
    window.addEventListener('message', async function (e) {
      const { data } = e;
      if (data && typeof data == 'string') {
        const { tableCellIndex, tableName } = JSON.parse(data);
        let txt = tableCellIndex.split('_');
        let name = tableName.split('_');
        let type = name.includes('replenish') ? '2' : '1'
        txt = txt.slice(1, txt.length);
        _this.setState({
          tableRow: txt[0],
          tableColumn: txt[1],
          type
        }, () => {
          _this['ReportExplainModal'].open({
            rowIndex: txt[0],
            colIndex: txt[1],
            type: type,
            year: '',
            reportCode: _this.state.key,
          });
          // _this.findVerData(1, 10, name.join('-')).then(res => {
          //   _this['peg'] && _this['peg'].setState({
          //     visible: true
          //   })
          // });
        });
      }
    })
  };
  callback = (v) => {
    const { key = '' } = this.state;
    if (v === '5') {
      this.selectHtml(key);
      this.setState({
        tab: v
      })
    }
  };
  selectHtml = (val) => {
    this.props.dispatch({
      type: 'tmwTable/queryExcelConfigReturnHtmlExplain',
      payload: {
        reportCode: val,
        orgCode: 1,
        orgLevelCode: 500
      }
    }).then(res => {
      this.setState({
        Html: res,
        // datas: res.data
      })
    })
  };
  getValue = (e, key, record) => {
    record[key] = e.target.value;
    let { rowCell = [] } = this.state;
    rowCell.map(item => {
      if (item['id'] === record['id']) {
        item = record
      }
    });
    this.setState({ rowCell })
  };
  getValue1 = (e, key, record) => {
    record[key] = e.target.value;
    let { colCell = [] } = this.state;
    colCell.map(item => {
      if (item['id'] === record['id']) {
        item = record
      }
    });
    this.setState({ colCell })
  };
  getValue2 = (e, key, record) => {
    record[key] = e.target.value;
    let { tableIndexConfigList = [] } = this.state;
    tableIndexConfigList.map(item => {
      if (item['id'] === record['id']) {
        item = record
      }
    });
    this.setState({ tableIndexConfigList })
  };
  getValue3 = (e, key, record) => {
    record[key] = e.target.value;
    let find1: any = []
    let { lineConfigList = [] } = this.state;
    if (record?.parentId) {
      let find = lineConfigList.find(i => i.id === record?.parentId)
      // find1 = lineConfigList.filter(i => i.id !== record?.parentId)
      find.children = find.children.map(n => {
        if (n.id === record.id) {
          n = record
        }
        return n
      })
      find1 = lineConfigList.map(i => {
        if (i.id == find.id) {
          i = find
        }
        return i
      })
      // find1.push(find)
    } else {
      find1 = lineConfigList.map(i => {
        if (i.id === record?.id) {
          i = record
        }
        return i
      })
    }
    this.setState({ lineConfigList: find1 })
  };
  isHasExtend = (record, e) => {
    let find1: any = []
    let { lineConfigList = [] } = this.state;
    if (record?.parentId) {
      let find = lineConfigList.find(i => i.id === record?.parentId)
      find1 = lineConfigList.filter(i => i.id !== record?.parentId)
      find.children = find.children.map(n => {
        if (n.id === record.id) {
          n = { ...record, hasExtend: e }
        }
        return n
      })
      find1.push(find)
    } else {
      find1 = lineConfigList.map(i => {
        if (i.id === record?.id) {
          i = { ...record, hasExtend: e }
          return i
        }
      })
    }
    this.setState({
      lineConfigList: find1
    })
  }
  boxChange = (e, key, record) => {
    record[key] = e.target.checked;
    let { rowCell = [] } = this.state;
    rowCell.map(item => {
      if (item['id'] === record['id']) {
        item = record
      }
    });
    this.setState({ rowCell })
  };
  boxChange1 = (e, key, record) => {
    record[key] = e.target.checked;
    let { colCell = [] } = this.state;
    colCell.map(item => {
      if (item['id'] === record['id']) {
        item = record
      }
    });
    this.setState({ colCell })
  };
  getSelect = (e, key, record) => {
    record[key] = e;
    let { rowCell = [] } = this.state;
    rowCell.map(item => {
      if (item['id'] === record['id']) {
        item = record
      }
    });
    this.setState({ rowCell })
  };
  getSelect1 = (e, key, record) => {
    record[key] = e;
    let { colCell = [] } = this.state;
    colCell.map(item => {
      if (item['id'] === record['id']) {
        item = record
      }
    });
    this.setState({ colCell })
  };
  add = (key) => {
    this.setState({
      loading: true
    }, () => {
      let { rowCell, colCell, tableIndexConfigList, lineConfigList } = this.state;
      switch (key) {
        case '1':
          rowCell.push({ id: new Date().valueOf() });
          this.setState({ rowCell, loading: false });
          break;
        case '2':
          colCell.push({ id: new Date().valueOf() });
          this.setState({ colCell, loading: false });
          break;
        case '3':
          tableIndexConfigList.push({ id: new Date().valueOf() });
          this.setState({ tableIndexConfigList, loading: false });
          break;
        case '4':
          lineConfigList.push({ id: new Date().valueOf(), hasExtend: true });
          this.setState({ lineConfigList, loading: false });
          break;
      }
    })

  };
  save = () => {
    const { rowCell = [], colCell = [], tableIndexConfigList = [], key = '', lineConfigList = [], hasReplenish, queryType } = this.state;
    this.props.dispatch({
      type: 'tmwTable/add',
      payload: {
        data: {
          reportCode: key,
          rowCell,
          colCell,
          tableIndexConfigList,
          replenishConfig: {
            lineConfigList,
            hasReplenish: hasReplenish ? hasReplenish : undefined,
            queryType
          }
        }
      }
    }).then(res => {
      if (res['code'] === 0) {
        Tip.success('操作提示', '操作成功');
      } else {
        Tip.error('操作提示', '操作失败')
      }
    })
  };
  onExpand = (expandedKeys, e) => {//展开树节点
    const { node: { props: { dataRef = {} } = {} } = {}, expanded = false } = e || {};
    const { id = '' } = dataRef;
    const { listTree, memType = '1' } = this.props.tmwTable;
    if (!isEmpty(listTree)) {
      let find = listTree.find(it => it['id'] === id) || {};
      if (isEmpty(_get(find, 'children', [])) && expanded) {
        this.props.dispatch({
          type: 'tmwTable/getAnnualstatsTree',
          payload: {
            id: id
          }
        });
      }
      this.setState({
        expandedKeys,
      });
    }
  };
  onSelect = (levelCode, e) => {
    const { node: { props: { dataRef = {} } = {} } = {} } = e || {};
    const val = { ...dataRef };
    const { tab } = this.state;
    delete val['children'];

    // this.props.dispatch({
    //   type: 'tmwTable/updateState',
    //   payload: {
    //     treeOrg: val,
    //   }
    // });
    if (!isEmpty(levelCode)) {
      this.select(levelCode[0]);
    }

    if (tab === '5' && !isEmpty(levelCode[0])) {
      this.selectHtml(levelCode[0]);
    }
    this.setState({
      selectedKeys: levelCode,
      key: levelCode[0]
    })
  };
  select = (code) => {
    this.props.dispatch({
      type: 'tmwTable/select',
      payload: {
        reportCode: code
      }
    }).then(res => {
      const { code = 500, data = {} } = res;
      const { rowCell = [], colCell = [], tableIndexConfigList = [], replenishConfig: { lineConfigList = [], hasReplenish = undefined, queryType = undefined } = {} } = data;
      if (code === 0) {
        this.setState({
          rowCell,
          colCell,
          tableIndexConfigList,
          lineConfigList,
          hasReplenish,
          queryType
        })
      }
    })
  };

  del = (record, index) => {
    let { rowCell, loading } = this.state;
    this.setState({
      loading: true
    }, () => {
      let find = [];
      if (record['id']) {
        find = rowCell.filter(i => i['id'] !== record['id']);
        this.setState({ rowCell: find, loading: false })
      } else {
        rowCell.splice(index, 1);
        this.setState({ rowCell, loading: false })
      }
    })
  };
  del1 = (record, index) => {
    let { colCell, loading } = this.state;
    this.setState({
      loading: true
    }, () => {
      let find = [];
      if (record['id']) {
        find = colCell.filter(i => i['id'] !== record['id']);
        this.setState({ colCell: find, loading: false })
      } else {
        colCell.splice(index, 1);
        this.setState({ colCell, loading: false })
      }
    })
  };
  del2 = (record, index) => {
    let { tableIndexConfigList } = this.state;
    this.setState({
      loading: true
    }, () => {
      let find = [];
      if (record['id']) {
        find = tableIndexConfigList.filter(i => i['id'] !== record['id']);
        this.setState({ tableIndexConfigList: find, loading: false })
      } else {
        tableIndexConfigList.splice(index, 1);
        this.setState({ tableIndexConfigList, loading: false })
      }
    })
  };
  del3 = (record, index) => {
    let { lineConfigList } = this.state;
    let find1: any = []
    if (record?.parentId) {
      let find = lineConfigList.find(i => i.id === record?.parentId)
      find1 = lineConfigList.filter(i => i.id !== record?.parentId)
      find.children = find.children.filter(n => n.id !== record.id)
      find1.push(find)
    } else {
      find1 = lineConfigList.filter(i => i.id !== record?.id)
    }
    this.setState({
      lineConfigList: find1
    })
  }
  copyConfig = (e) => {
    const { key } = this.state;
    if (key) {
      this.props.dispatch({
        type: 'tmwTable/copyConfig',
        payload: {
          srcConfig: e,
          tarConfig: key
        }
      }).then(res => {
        const { code = 500, data = {} } = res;
        if (code === 0) {
          Tip.success('操作提示', '复制成功');
          this.select(key);
        }
      })
    }
  };
  renderTreeNodes = (data, rootCode) => {//渲染树节点
    if (!isEmpty(data)) {
      return data.map(item => {
        if (item['hasSub'] === '1') {
          return (
            <TreeNode title={item['reportName'] || item['shortName']} key={item['reportCode'] || item['levelCode']} isLeaf={item['hasSub'] !== '1'} dataRef={item}>
              {
                this.renderTreeNodes(item['children'], item['reportCode'] || item['levelCode'])
              }
            </TreeNode>
          );
        } else {
          return <TreeNode title={item['reportName'] || item['shortName']} key={item['reportCode'] || item['levelCode']} dataRef={item} />
        }
      })
    }
  };
  addChild = (record, index) => {
    this.setState({
      loading: true
    }, () => {
      const { lineConfigList } = this.state
      if (record?.children) {
        record?.children.push({ id: new Date().valueOf(), parentId: record?.id, hasExtend: true })
      } else {
        record.children = []
        record?.children.push({ id: new Date().valueOf(), parentId: record?.id, hasExtend: true })
      }
      lineConfigList.map(i => {
        if (i.id === record.id) {
          i = record
        }
      })
      this.setState({ lineConfigList, loading: false })
    })
  }
  ischeck = (e) => {
    this.setState({
      hasReplenish: e
    })
  }
  handleChange = (e) => {
    this.setState({
      queryType: e
    })
  }
  onMouseLeave = () => {
    let oPanel = document.getElementById('leftDiv');
    let oPanel1 = document.getElementById('rightDiv');
    let allW = document.body.clientWidth
    let disX = 0;
    let disW = 0;
    oPanel.onmousedown = function (ev) {
      var ev = ev || window.event;
      disX = ev.clientX; // 获取鼠标按下时光标x的值
      disW = oPanel.offsetWidth; // 获取拖拽前div的宽
      document.onmousemove = function (ev) {
        var ev = ev || window.event;
        //拖拽时为了对宽和高 限制一下范围，定义两个变量
        var W = ev.clientX - disX + disW;
        if (W < 100) {
          W = 100;
        }
        if (W > 800) {
          W = 800;
        }
        oPanel.style.width = W + 'px';// 拖拽后物体的宽
        oPanel1.style.width = (allW - W - 100) + 'px'
      }
      document.onmouseup = function () {
        document.onmousemove = null;
        document.onmouseup = null;
      }
    }
  }
  findVerData = async (pageNum?, pageSize?, levelCode?) => {
    const { tableRow, tableColumn, key, type } = this.state;
    // const { type='', treeLevel, treeOrg, memType } = this.props.annualstats;
    // const { treeOrg= } =this.props.positionNum;
    let lastCode = levelCode || this.state['levelCode'];
    let params = {
      reportCode: key,
      rowIndex: tableRow,
      colIndex: tableColumn,
      orgCode: "1",
      orgLevelCode: "500",
      pageNum: pageNum || 1,
      pageSize: pageSize || 10,
      type
    };
    // if (type == 'a') {
    //   if (memType === '3') {
    //     params['orgCode'] = treeOrg['orgCode'] + `,${treeOrg['unitId']}`;
    //   } else {
    //     params['orgCode'] = treeOrg['levelCode'];
    //   }
    // }
    // if (type == 'b') {
    //   params['countLevel'] = treeLevel['code'];
    // }
    const obj = await this.props.dispatch({
      type: 'tmwTable/findVerData',
      payload: { data: { ...params } }
    });
    const { data = {} } = obj;
    let changeList = changeListPayQuery(data || { list: [] });
    this.setState({
      params,
      ...changeList,
      levelCode: lastCode,
      tableType: data ? data['type'] : undefined,
    });
  };
  render(): React.ReactNode {
    const { rowCell, colCell, tableIndexConfigList, key = '', loading, lineConfigList, hasReplenish, queryType, datas = {}, list = [], pagination, tableType, tbCheckLoading, params } = this.state;
    console.log("🚀 ~ render ~ params:", params)
    const { tmwTable: { TreeList = [] } = {} } = this.props;
    const columns = [
      {
        title: '表序号',
        dataIndex: 'tableIndex',
        width: 80,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'tableIndex', record)} />
        }
      },
      // {
      //   title: '序号',
      //   dataIndex: 'tableInfoIndex',
      //   width: 80,
      //   render: (text, record) => {
      //     return <Input value={text} onChange={(e) => this.getValue(e, 'tableInfoIndex', record)} />
      //   }
      // },
      {
        title: '行号',
        dataIndex: 'index',
        width: 80,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'index', record)} />
        }
      },
      {
        title: '字段名',
        dataIndex: 'fieldName',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'fieldName', record)} />
        }
      },
      {
        title: '条件',
        dataIndex: 'className',
        width: 150,
        render: (text, record) => {
          return (
            <Select style={{ width: '100%' }} onChange={(e) => this.getSelect(e, 'className', record)} value={text}>
              <Option value={'AnyCondition'}>AnyCondition</Option>
              <Option value={'EmptyCondition'}>EmptyCondition</Option>
              <Option value={'EqCondition'}>EqCondition</Option>
              <Option value={'GtCondition'}>GtCondition</Option>
              <Option value={'LtCondition'}>LtCondition</Option>
              <Option value={'InCondition'}>InCondition</Option>
              <Option value={'MustIncludeFieldsCondition'}>MustIncludeFieldsCondition</Option>
              <Option value={'NeCondition'}>NeCondition</Option>
              <Option value={'PrefixCondition'}>PrefixCondition</Option>
              <Option value={'RangeCondition'}>RangeCondition</Option>
              <Option value={'NotNullCondition'}>NotNullCondition</Option>
              <Option value={'IsNullCondition'}>IsNullCondition</Option>
              <Option value={'AvgCondition'}>AvgCondition</Option>
              <Option value={'SumCondition'}>SumCondition</Option>
              <Option value={'MaxCondition'}>MaxCondition</Option>
              <Option value={'MinCondition'}>MinCondition</Option>
              <Option value={'IncludeCondition'}>IncludeCondition</Option>
            </Select>
          )
        }
      },
      {
        title: '数据类型',
        dataIndex: 'dataType',
        width: 150,
        render: (text, record) => {
          return (
            <Select style={{ width: '100%' }} onChange={(e) => this.getSelect(e, 'dataType', record)} value={text}>
              <Option value={'string'}>string</Option>
              <Option value={'int'}>int</Option>
              <Option value={'date'}>date</Option>
              <Option value={'long'}>long</Option>
              <Option value={'double'}>double</Option>
              <Option value={'bigDecimal'}>bigDecimal</Option>
            </Select>
          )
        }
      },
      {
        title: '单个值',
        dataIndex: 'value',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'value', record)} />
        }
      },
      {
        title: '多个值',
        dataIndex: 'values',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'values', record)} />
        }
      },
      {
        title: '包含字段',
        dataIndex: 'includeList',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'includeList', record)} />
        }
      },
      {
        title: '是否相等',
        dataIndex: 'eq',
        width: 50,
        render: (text, record, index) => {
          return <Checkbox checked={text} onChange={(e) => this.boxChange(e, 'eq', record)} />
        }
      },
      {
        title: '数据集',
        dataIndex: 'isSetTableIndex',
        width: 50,
        render: (text, record, index) => {
          return <Checkbox checked={text} onChange={(e) => this.boxChange(e, 'isSetTableIndex', record)} />
        }
      },
      {
        title: '前缀',
        dataIndex: 'prefix',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'prefix', record)} />
        }
      },
      {
        title: '左边界值',
        dataIndex: 'leftValue',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'leftValue', record)} />
        }
      },
      {
        title: '是否包含左边界',
        dataIndex: 'includeLeft',
        width: 50,
        render: (text, record, index) => {
          return <Checkbox checked={text} onChange={(e) => this.boxChange(e, 'includeLeft', record)} />
        }
      },
      {
        title: '右边界值',
        dataIndex: 'rightValue',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'rightValue', record)} />
        }
      },
      {
        title: '是否包含右边界',
        dataIndex: 'includeRight',
        width: 100,
        render: (text, record, index) => {
          return <Checkbox checked={text} onChange={(e) => this.boxChange(e, 'includeRight', record)} />
        }
      },
      {
        title: '依赖项',
        dataIndex: 'dependIndex',
        width: 100,
        render: (text, record, index) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'dependIndex', record)} />
        }
      },
      {
        title: '是否依赖项',
        dataIndex: ' virtual',
        width: 100,
        render: (text, record, index) => {
          return <Checkbox checked={text} onChange={(e) => this.boxChange(e, ' virtual', record)} />
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 80,
        fixed: 'right',
        render: (text, record, index) => {
          return (
            <a className={'del'} onClick={() => this.del(record, index)}>删除</a>
          )
        }
      },
    ];
    const columns1 = [
      {
        title: '表序号',
        dataIndex: 'tableIndex',
        width: 80,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue1(e, 'tableIndex', record)} />
        }
      },
      // {
      //   title: '序号',
      //   dataIndex: 'tableInfoIndex',
      //   width: 80,
      //   render: (text, record) => {
      //     return <Input value={text} onChange={(e) => this.getValue1(e, 'tableInfoIndex', record)} />
      //   }
      // },
      {
        title: '行号',
        dataIndex: 'index',
        width: 80,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue1(e, 'index', record)} />
        }
      },
      {
        title: '字段名',
        dataIndex: 'fieldName',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue1(e, 'fieldName', record)} />
        }
      },
      {
        title: '条件',
        dataIndex: 'className',
        width: 150,
        render: (text, record) => {
          return (
            <Select style={{ width: '100%' }} onChange={(e) => this.getSelect1(e, 'className', record)} value={text}>
              <Option value={'AnyCondition'}>AnyCondition</Option>
              <Option value={'EmptyCondition'}>EmptyCondition</Option>
              <Option value={'EqCondition'}>EqCondition</Option>
              <Option value={'GtCondition'}>GtCondition</Option>
              <Option value={'LtCondition'}>LtCondition</Option>
              <Option value={'InCondition'}>InCondition</Option>
              <Option value={'MustIncludeFieldsCondition'}>MustIncludeFieldsCondition</Option>
              <Option value={'NeCondition'}>NeCondition</Option>
              <Option value={'PrefixCondition'}>PrefixCondition</Option>
              <Option value={'RangeCondition'}>RangeCondition</Option>
              <Option value={'NotNullCondition'}>NotNullCondition</Option>
              <Option value={'IsNullCondition'}>IsNullCondition</Option>
              <Option value={'AvgCondition'}>AvgCondition</Option>
              <Option value={'JavaCondition'}>JavaCondition</Option>
              <Option value={'SumCondition'}>SumCondition</Option>
              <Option value={'MaxCondition'}>MaxCondition</Option>
              <Option value={'MinCondition'}>MinCondition</Option>
              <Option value={'IncludeCondition'}>IncludeCondition</Option>
            </Select>
          )
        }
      },
      {
        title: '数据类型',
        dataIndex: 'dataType',
        width: 150,
        render: (text, record) => {
          return (
            <Select style={{ width: '100%' }} onChange={(e) => this.getSelect1(e, 'dataType', record)} value={text}>
              <Option value={'string'}>string</Option>
              <Option value={'int'}>int</Option>
              <Option value={'date'}>date</Option>
              <Option value={'long'}>long</Option>
              <Option value={'double'}>double</Option>
              <Option value={'bigDecimal'}>bigDecimal</Option>
            </Select>
          )
        }
      },
      {
        title: '单个值',
        dataIndex: 'value',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue1(e, 'value', record)} />
        }
      },
      {
        title: '多个值',
        dataIndex: 'values',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue1(e, 'values', record)} />
        }
      },
      {
        title: '包含字段',
        dataIndex: 'includeList',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue1(e, 'includeList', record)} />
        }
      },
      {
        title: '是否相等',
        dataIndex: 'eq',
        width: 50,
        render: (text, record, index) => {
          return <Checkbox checked={text} onChange={(e) => this.boxChange1(e, 'eq', record)} />
        }
      },
      {
        title: '数据集',
        dataIndex: 'isSetTableIndex',
        width: 50,
        render: (text, record, index) => {
          return <Checkbox checked={text} onChange={(e) => this.boxChange1(e, 'isSetTableIndex', record)} />
        }
      },
      {
        title: '前缀',
        dataIndex: 'prefix',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue1(e, 'prefix', record)} />
        }
      },
      {
        title: '左边界值',
        dataIndex: 'leftValue',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue1(e, 'leftValue', record)} />
        }
      },
      {
        title: '是否包含左边界',
        dataIndex: 'includeLeft',
        width: 50,
        render: (text, record, index) => {
          return <Checkbox checked={text} onChange={(e) => this.boxChange1(e, 'includeLeft', record)} />
        }
      },
      {
        title: '右边界值',
        dataIndex: 'rightValue',
        width: 150,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue1(e, 'rightValue', record)} />
        }
      },
      {
        title: '是否包含右边界',
        dataIndex: 'includeRight',
        width: 50,
        render: (text, record, index) => {
          return <Checkbox checked={text} onChange={(e) => this.boxChange1(e, 'includeRight', record)} />
        }
      },
      {
        title: '依赖项',
        dataIndex: 'dependIndex',
        width: 100,
        render: (text, record, index) => {
          return <Input value={text} onChange={(e) => this.getValue(e, 'dependIndex', record)} />
        }
      },
      {
        title: '是否依赖项',
        dataIndex: ' virtual',
        width: 100,
        render: (text, record, index) => {
          return <Checkbox checked={text} onChange={(e) => this.boxChange(e, ' virtual', record)} />
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 80,
        fixed: 'right',
        render: (text, record, index) => {
          return (
            <a className={'del'} onClick={() => this.del1(record, index)}>删除</a>
          )
        }
      },
    ];
    const columns2 = [
      {
        title: '表序号',
        dataIndex: 'tableIndex',
        width: 50,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue2(e, 'tableIndex', record)} />
        }
      },
      // {
      //   title: '序号',
      //   dataIndex: 'tableInfoIndex',
      //   width: 50,
      //   render: (text, record) => {
      //     return <Input value={text} onChange={(e) => this.getValue2(e, 'tableInfoIndex', record)} />
      //   }
      // },
      {
        title: '表名',
        dataIndex: 'tableName',
        width: 50,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue2(e, 'tableName', record)} />
        }
      },
      {
        title: '表条件',
        dataIndex: 'condition',
        width: 300,
        render: (text, record) => {
          return <TextArea rows={1} value={text} onChange={(e) => this.getValue2(e, 'condition', record)} />
        }
      },
      {
        title: '分组字段',
        dataIndex: 'groupByField',
        width: 100,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue2(e, 'groupByField', record)} />
        }
      },
      // {
      //   title: '分组函数',
      //   dataIndex: 'functionCode',
      //   width: 100,
      //   render: (text, record) => {
      //     return (
      //       <Select style={{ width: '100%' }} onChange={(e) => this.getSelect1(e, 'functionCode', record)} value={text}>
      //         <Option value={'COUNT'}>COUNT</Option>
      //         <Option value={'SUM'}>SUM</Option>
      //         <Option value={'AVG'}>AVG</Option>
      //         <Option value={'MAX'}>MAX</Option>
      //         <Option value={'MIN'}>MIN</Option>
      //         <Option value={'SUBTRACT'}>SUBTRACT</Option>
      //         <Option value={'MULTIPLY'}>MULTIPLY</Option>
      //         <Option value={'FLOORDIV'}>FLOORDIV</Option>

      //       </Select>
      //     )
      //   }
      // },
      // {
      //   title: '聚合字段',
      //   dataIndex: 'functionField',
      //   width: 100,
      //   render: (text, record) => {
      //     return <Input value={text} onChange={(e) => this.getValue2(e, 'functionField', record)} />
      //   }
      // },
      {
        title: '操作',
        dataIndex: 'action',
        width: 50,
        render: (text, record, index) => {
          return (
            <a className={'del'} onClick={() => this.del2(record, index)}>删除</a>
          )
        }
      },
    ];
    const columns3 = [
      // {
      //   title: '表序号',
      //   dataIndex: 'tableIndex',
      //   width: 200,
      //   render: (text, record) => {
      //     return <Input style={{ width: '150px' }} value={text} onChange={(e) => this.getValue3(e, 'tableIndex', record)} />
      //   }
      // },
      // {
      //   title: '序号',
      //   dataIndex: 'tableInfoIndex',
      //   width: 50,
      //   render: (text, record) => {
      //     return <Input value={text} onChange={(e) => this.getValue2(e, 'tableInfoIndex', record)} />
      //   }
      // },
      {
        title: '行号',
        dataIndex: 'index',
        width: 100,
        render: (text, record) => {
          return <Input style={{ width: '80px' }} value={text} onChange={(e) => this.getValue3(e, 'index', record)} />
        }
      },
      {
        title: '表名',
        dataIndex: 'tableName',
        width: 100,
        render: (text, record) => {
          return <Input value={text} onChange={(e) => this.getValue3(e, 'tableName', record)} />
        }
      },
      {
        title: '表条件',
        dataIndex: 'condition',
        width: 300,
        render: (text, record) => {
          return <TextArea rows={1} value={text} onChange={(e) => this.getValue3(e, 'condition', record)} />
        }
      },
      {
        title: '继承条件',
        dataIndex: 'hasExtend',
        width: 200,
        render: (text, record) => {
          return (
            <Switch defaultChecked checkedChildren="是" unCheckedChildren="否" checked={text} onChange={(e) => this.isHasExtend(record, e)} />
          )
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 50,
        render: (text, record, index) => {
          return (
            <React.Fragment>
              <a className={'del'} onClick={() => this.del3(record, index)}>删除</a>
              {
                !record?.parentId &&
                <a style={{ marginLeft: '12px' }} onClick={() => this.addChild(record, index)}>添加子节点</a>
              }
            </React.Fragment>
          )
        }
      },
    ]
    return (
      <React.Fragment>
        <div style={{ height: '100%' }}>
          <div onMouseLeave={this.onMouseLeave} id={'leftDiv'} style={{ display: 'inline-block', width: '15%', verticalAlign: 'top', overflow: 'hidden scroll', height: '100%' }}>
            <Tree
              onExpand={this.onExpand}
              onSelect={this.onSelect}
              expandedKeys={this.state['expandedKeys']}
              selectedKeys={this.state['selectedKeys'] || [TreeList[0]?.reportCode]}
            // defaultSelectedKeys={[TreeList[0]['reportCode']]}
            >
              {this.renderTreeNodes(TreeList, '-1')}
            </Tree>
          </div>
          <div id={'rightDiv'} style={{ display: 'inline-block', width: '84%', height: '100%', marginLeft: '1%' }}>
            <div style={{ marginTop: '24px' }}>
              <div style={{ display: 'inline-block', verticalAlign: 'middle' }}> <Button onClick={this.save}>保存</Button></div>
              <div style={{ display: 'inline-block', verticalAlign: 'middle' }}>
                <Search
                  placeholder="input search text"
                  enterButton="复制"
                  // size="small"
                  onSearch={this.copyConfig}
                />
              </div>
              {
                key &&
                <span style={{ marginLeft: '12px' }}>当前表：{key}</span>
              }
            </div>
            <Tabs defaultActiveKey="1" onChange={this.callback}>
              <TabPane tab="行" key="1">
                <Button onClick={() => this.add('1')}>添加</Button>
                {
                  !loading &&
                  <ListTable columns={columns} rowKey={record => record?.id} data={rowCell} scroll={{ x: 1000, y: 550, scrollToFirstRowOnChange: true }} pagination={false} />
                }
              </TabPane>
              <TabPane tab="列" key="2">
                <Button onClick={() => this.add('2')}>添加</Button>
                {
                  !loading &&
                  <ListTable columns={columns1} rowKey={record => record?.id} data={colCell} scroll={{ x: 1000, y: 550 }} pagination={false} />
                }

              </TabPane>
              <TabPane tab="设置" key="3">
                <Button onClick={() => this.add('3')}>添加</Button>
                {
                  !loading &&
                  <ListTable columns={columns2} rowKey={record => record?.id} data={tableIndexConfigList} pagination={false} />
                }
              </TabPane>
              <TabPane tab="补充资料" key="4">
                <Button onClick={() => this.add('4')}>添加</Button>
                是否有补充资料 ：<Switch checkedChildren="是" unCheckedChildren="否" checked={hasReplenish} onChange={this.ischeck} />
                <Select style={{ width: 120 }} onChange={this.handleChange} defaultValue={queryType}>
                  <Option value="SQL">SQL</Option>
                  <Option value="JAVA">JAVA</Option>
                </Select>
                {
                  !loading &&
                  <ListTable columns={columns3} rowKey={record => record?.id} data={lineConfigList} scroll={{ x: 1000, y: 550 }} pagination={false} defaultExpandAllRows={true} />
                }
              </TabPane>
              <TabPane tab="预览" key="5">
                <div style={{ marginTop: 50, textAlign: 'center' }} dangerouslySetInnerHTML={{ __html: this.state['Html'] }} />
                {/* <div>{`${datas?.replenish}`}</div>
                {/* <div style={{ width: 300, display: 'inline-block', height: 500, overflow: 'auto' }}>
                  <div style={{ width: 120, display: 'inline-block' }}>
                    {
                      datas?.replenish && Object.keys(datas?.replenish).map((i, index) => {
                        return <div key={index}>{i}:</div>
                      })
                    }
                  </div>
                  <div style={{ width: 120, display: 'inline-block' }}>
                    {
                      datas?.replenish && Object.values(datas?.replenish).map((i:any, index) => {
                        return <div key={index}>{i}</div>
                      })
                    }
                  </div>
                </div>


                <div style={{ width: 300, display: 'inline-block', height: 500, overflow: 'auto' }}>
                  <div style={{ width: 120, display: 'inline-block' }}>
                    {
                      datas?.table0 && Object.keys(datas?.table0).map((i, index) => {
                        return <div key={index}>{i}:</div>
                      })
                    }
                  </div>
                  <div style={{ width: 120, display: 'inline-block' }}>
                    {
                      datas?.table0 && Object.values(datas?.table0).map((j: any, index) => {
                        return <div key={index}>{j}</div>
                      })
                    }
                  </div>

                </div> */}
                {/* <div style={{ width: 100, display: 'inline-block', height: 500, overflow: 'auto' }}>

                </div> */}
              </TabPane>
            </Tabs>
          </div>
          <Pegging tableType={tableType} list={list} pagination={pagination} pageChange={this.findVerData} params={params} ref={e => this['peg'] = e} />
          <ReportExplainModal ref={e => this['ReportExplainModal'] = e} onOK={() => {
            this.selectHtml(key)
          }} isConfiguration={true}></ReportExplainModal>
        </div>
      </React.Fragment>
    );
  }
}
