/**
 * 批量添加 交费
 * */

import React, { useState, useRef, useEffect, useImperativeHandle } from 'react';
import {
  Input,
  Select,
  Form,
  Modal,
  Tabs,
  Button,
  Divider,
  Popconfirm,
  Space,
  Radio,
  InputNumber,
  message,
} from 'antd';
import moment from 'moment';
import MemSelect from '@/components/MemSelect';
import DateTime from '@/components/Date';
import Tip from '@/components/Tip';
import { getSession } from '@/utils/session';
import ListTable from 'src/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _uniqBy from 'lodash/uniqBy';
import _isNumber from 'lodash/isNumber';
import _cloneDeep from 'lodash/cloneDeep';
import { listPayment, batchPayment } from '../../services';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const index = (props: any, ref: any) => {
  const memSelectRef = useRef<any>();
  const org: any = getSession('org') || {};
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('批量添加党费交纳');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [basinInfo, setBasinInfo] = useState<any>({});
  const [listLoading, setListLoading] = useState(false);
  const [timeKey, setTimeKey] = useState(+new Date());
  const dateRef1: any = useRef();
  const [listData, setListData]: any = useState([]);
  const [pagination, setPagination] = useState({ pageSize: 20, current: 1, total: 0 });
  const [selectMem, setSelectMem]: any = useState([]);

  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 40,
      render: (text, record, index) => {
        return index + 1;
      },
    },
    {
      title: '姓名',
      dataIndex: 'memName',
      align: 'center',
      width: 60,
    },
    {
      title: '所属组织',
      dataIndex: 'orgName',
      //   align: 'center',
      width: 100,
    },
    {
      title: '身份证号码',
      dataIndex: 'idCard',
      align: 'center',
      width: 80,
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
          if (text.indexOf('*') > 0) {
            return text;
          }
          return <span>{newVal}</span>;
        } else {
          return '';
        }
      },
    },
    {
      title: '当前党费标准',
      dataIndex: 'standard',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        // 在【批量添加党费交费】时，需做出判断-判断当前党员是否“从党费设置的起交时间起之后的月份是否都是相同的党费交纳标准”，若是，则下方当前党费标准展示唯一的标准金额，若不是则不能适用于批量添加交费功能，在“当前交费标准”这一栏提示“党费标准不统一”，并禁用后续的操作。
        if (record?.startPayDate) {
          if (text) {
            if (text === 'noSameStandard') {
              return <div style={{ color: 'red' }}>党费标准不统一</div>;
            } else {
              return `${text}元`;
            }
          }
        }
      },
    },
    {
      title: '起交时间',
      dataIndex: 'startPayDate',
      align: 'center',
      width: 60,
      render: (text) => {
        if (text) {
          return moment(text).format('YYYY.MM');
        }
      },
    },
    {
      title: '已交到',
      dataIndex: 'lastPayDate',
      width: 90,
      align: 'center',
      render: (text, record, index) => {
        let sameStandard = isSameStandard(record);
        if (record?.startPayDate && sameStandard) {
          return (
            <DateTime
              placeholder=" "
              key="1"
              startTime={moment(record?.startPayDate)} // 不能填起交时间之前的时间
              endTime={moment(record.startPayDate).dayOfYear(365)} // 不能超过本年
              value={text ? moment(text) : undefined}
              mode="YYYY.MM"
              style={{ width: '100%' }}
              onChange={(e) => {
                calcMonthAmount(e, record.payToDate, record);
              }}
            />
          );
        }
      },
    },
    {
      title: '交纳到',
      dataIndex: 'payToDate',
      width: 90,
      align: 'center',
      render: (text, record, index) => {
        let sameStandard = isSameStandard(record);
        if (record?.startPayDate && sameStandard) {
          return (
            <DateTime
              placeholder=" "
              key="2"
              startTime={moment(record?.startPayDate)} // 不能填起交时间之前的时间
              endTime={moment(record.startPayDate).dayOfYear(365)} // 不能超过本年
              value={_isEmpty(text) ? undefined : moment(text)}
              mode="YYYY.MM"
              style={{ width: '100%' }}
              onChange={(e) => {
                calcMonthAmount(record.lastPayDate, e, record);
              }}
            />
          );
        }
      },
    },
    {
      title: '交纳月数',
      width: 60,
      align: 'center',
      dataIndex: 'payMonth',
      render: (text, record, index) => {
        let sameStandard = isSameStandard(record);
        if (record?.startPayDate && sameStandard) {
          return (
            <InputNumber
              disabled
              value={text}
              min={0}
              max={12}
              style={{ width: '100%', color: 'red' }}
            />
          );
        }
      },
    },
    {
      title: '交费金额',
      width: 80,
      align: 'center',
      dataIndex: 'payMoney',
      render: (text, record, index) => {
        let sameStandard = isSameStandard(record);
        if (record?.startPayDate && sameStandard) {
          return (
            <InputNumber
              disabled
              value={text}
              min={0}
              max={999999}
              precision={2}
              style={{ width: '100%' }}
            />
          );
        }
      },
    },
    {
      title: '交费时间',
      dataIndex: 'payDate',
      width: 90,
      align: 'center',
      render: (text, record, index) => {
        let sameStandard = isSameStandard(record);
        if (record?.startPayDate && sameStandard) {
          return (
            <DateTime
              placeholder=" "
              startTime={moment(record?.startPayDate)} // 不能填起交时间之前的时间
              endTime={moment(record.startPayDate).dayOfYear(365)} // 不能超过本年
              value={_isEmpty(text) ? undefined : moment(text)}
              mode="YYYY.MM"
              style={{ width: '100%' }}
              onChange={(e) => {
                console.log('交费时间===', e);

                let newData = listData.map((item: any) => {
                  if (item?.memCode === record?.memCode) {
                    return { ...item, payDate: e ? moment(e).valueOf() : undefined };
                  } else {
                    return item;
                  }
                });
                setListData(newData);
              }}
            />
          );
        }
      },
    },
    {
      title: '提示',
      width: 100,
      align: 'center',
      dataIndex: 'prompt',
      render: (text, record, index) => {
        switch (text) {
          case 'noTime':
            return (
              <div style={{ color: '#1890ff' }}>
                该党员没有设置<div style={{ textDecoration: 'underline' }}>起交时间</div>
              </div>
            );
          case 'noStandard':
            return (
              <div style={{ color: '#1890ff' }}>
                该党员没有设置<div style={{ textDecoration: 'underline' }}>党费交纳标准</div>
              </div>
            );

          default:
            '';
            break;
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 60,
      render: (text, record) => {
        return (
          <div>
            <Popconfirm
              title="确定要删除吗？"
              onConfirm={() => {
                let newArr = listData.filter((item) => item?.memCode !== record?.memCode);
                let newArrMem = selectMem.filter((item) => item?.code !== record?.memCode);
                setListData(newArr);
                setSelectMem(newArrMem);
                memSelectRef?.current?.outChangeselectedRows?.(newArrMem)
                console.log('newArrMem===', newArrMem);
              }}
            >
              <a className={'del'}>删除</a>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  useImperativeHandle(ref, () => ({
    open: (query?: any) => {
      setBasinInfo(query);
      setVisible(true);
    },
  }));
  // 计算交纳月数，交费金额
  const calcMonthAmount = (startDate, endDate, record) => {
    if (startDate && endDate && record.standard && record.standard !== 'noSameStandard') {
      // 计算交纳月数 交费金额
      let monthNum = moment(endDate).month() - moment(startDate).month();
      let payAmount = record.standard * monthNum;
      // 存数据
      const newList = listData.map((it) => {
        if (it.memCode == record.memCode) {
          return {
            ...it,
            payMonth: monthNum,
            payMoney: payAmount,
            lastPayDate: moment(startDate).valueOf(),
            payToDate: moment(endDate).valueOf(),
          };
        } else {
          return it;
        }
      });
      setListData(newList);
    } else if (startDate || endDate) {
      // 存数据
      const newList = listData.map((it) => {
        if (it.memCode == record.memCode) {
          if (startDate) {
            return {
              ...it,
              lastPayDate: moment(startDate).valueOf(),
              payToDate: undefined,
              payMonth: undefined,
              payMoney: undefined,
            };
          }
          if (endDate) {
            return {
              ...it,
              payToDate: moment(endDate).valueOf(),
              lastPayDate: undefined,
              payMonth: undefined,
              payMoney: undefined,
            };
          }
        } else {
          return it;
        }
      });
      setListData(newList);
    }
  };
  // 判断党费标准是否统一
  const isSameStandard = (record) => {
    const { data = [] } = record;
    let sameStandard: any = false;
    if (!_isEmpty(data)) {
      let filterArr = data.filter((item) => {
        return item?.month >= moment(record?.startPayDate).month() + 1;
      });
      sameStandard = _uniqBy(filterArr, 'standard').length === 1;
    }
    return sameStandard;
  };

  const getList = async (memArr: any) => {
    setListLoading(true);
    if (!_isEmpty(memArr)) {
      let idArr: any = memArr.map((item: any) => {
        return { code: item?.code };
      });
      const { code: resCode = 500, data = [] } = await listPayment({
        data: idArr,
      });
      setListLoading(false);
      if (resCode === 0) {
        let listArr = _cloneDeep(data);
        // 计算党费标准、提示
        listArr = listArr.map((item: any, index: any) => {
          let prompt: any = undefined; // 提示
          let standard: any = undefined; // 当前党费标准
          if (!item?.startPayDate) {
            prompt = 'noTime';
          } else if (_isEmpty(item?.data)) {
            prompt = 'noStandard';
          }
          const { data = [] } = item;
          if (!_isEmpty(data)) {
            let filterArr = data.filter((it) => {
              return it?.month >= moment(item?.startPayDate).month() + 1;
            });
            let sameStandard: any = _uniqBy(filterArr, 'standard');
            if (sameStandard.length === 1) {
              // 存值进listData
              standard = sameStandard[0].standard;
            } else {
              standard = 'noSameStandard';
            }
          }
          const find = memArr.find((it) => it.code == item.memCode);
          return { ...find, ...item, prompt, standard };
        });
        setListData(listArr);
      }
    }
  };
  const hadndleFinish = async () => {
    console.log('listData===', listData);
    if (!_isEmpty(listData)) {
      let noDateMem = listData.find((item) => !item?.startPayDate);
      let noStandardMem = listData.find(
        (item) => !item?.standard || item?.standard === 'noSameStandard',
      );
      if (!_isEmpty(noDateMem) || !_isEmpty(noStandardMem)) {
        Tip.error('操作提示', '当前存在未满足批量添加条件的党员');
      } else {
        let lastList = _cloneDeep(listData);
        let tag = false;
        lastList = lastList.map((item, index) => {
          const {
            memCode = undefined,
            lastPayDate = undefined,
            payToDate = undefined,
            payMonth = undefined,
            payMoney = undefined,
            payDate = undefined,
          } = item;
          if (
            _isNumber(lastPayDate) &&
            _isNumber(payToDate) &&
            _isNumber(payMonth) &&
            _isNumber(payMoney) &&
            _isNumber(payDate)
          ) {
            return { code: memCode, lastPayDate, payToDate, payMonth, payMoney, payDate };
          } else {
            tag = true;
          }
        });
        console.log('lastList===', lastList);
        if (tag) {
          Tip.error('操作提示', '请完善党费交纳相关信息');
        } else {
          setConfirmLoading(true);
          const { code: resCode = 500 } = await batchPayment({
            data: lastList,
          });
          setConfirmLoading(false);
          if (resCode == 0) {
            const { onOk } = props;
            Tip.success('操作提示', '操作成功');
            handleCancel();
            onOk && onOk();
          }
        }
      }
    }
  };
  const handleCancel = () => {
    setVisible(false);
    setBasinInfo({});
    setListData([]);
  };

  return (
    <Modal
      maskClosable={false}
      title={title}
      visible={visible}
      onOk={() => {
        hadndleFinish();
      }}
      onCancel={handleCancel}
      width={'1400px'}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
      bodyStyle={{ padding: '20px', overflow: 'auto' }}
    >
      <div style={{ color: '#faad14', paddingBottom: '20px' }}>
        注意：适用【批量添加党费交纳】时，需要满足党员党费标准从起交时间起全年是统一的一个标准，若存在不同月份不同标准的情况下不适用批量操作。
      </div>
      <div style={{ display: 'flex', justifyContent: 'space-between', paddingBottom: '20px' }}>
        <div>当前组织：{org?.name}</div>
        <div>时间：{moment().format('YYYY年MM月DD日')}</div>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', paddingBottom: '20px' }}>
        <div style={{ whiteSpace: 'nowrap', marginRight: '10px' }}>党员姓名</div>
        <MemSelect
          ref={memSelectRef}
          org={org}
          checkType="checkbox"
          selectedRows={selectMem}
          placeholder="请选择"
          onChange={(e) => {
            if (!_isEmpty(e)) {
              setSelectMem(e);
              getList(e);
            } else {
              setSelectMem([]);
              setListData([]);
            }
          }}
        />
      </div>
      <ListTable
        rowKey={'memCode'}
        
        columns={columns}
        data={listData}
        pagination={false}
        // onPageChange={(page, pageSize) => {
        //   getList({ pageNum: page, pageSize });
        // }}
      />
    </Modal>
  );
};

export default React.forwardRef(index);
