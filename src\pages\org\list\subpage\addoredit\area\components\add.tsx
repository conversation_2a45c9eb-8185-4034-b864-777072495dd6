import React, { useState, Fragment, useEffect, useImperativeHandle } from 'react';
import { Modal, Form, Input, InputNumber, Button, Select } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import DictSelect from '@/components/DictSelect';
import { caucusAdd, caucusDetails } from '@/pages/org/services/org';
import Tip from '@/components/Tip';
import moment from 'moment';
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 12 },
};
const NowYear = new Array(5).fill(1).map((it, index) => +moment().format('YYYY') - index);
const index = (props: any, ref: any) => {
  const {
    onOK,
  } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [data, setData] = useState<any>({});
  const handleCancel = () => {
    setVisible(false);
    clear();
  };
  const onFinish = async (e) => {
    const { basicInfo } = props.org;
    setConfirmLoading(true);
    const { code = 500 } = await caucusAdd({
      data: {
        ...e,
        orgCode: basicInfo.code,
        caucusOrgCode: basicInfo.orgCode,
        code: data?.code,
      },
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOK && onOK();
    }
  };
  const getDateInfo = async (query) => {
    const { basicInfo: { code: orgCode = '' } = {} } = props.org;
    const { code: resCode = 500, data = {} } = await caucusDetails({ code: query?.code });
    if (resCode === 0) {
      setData(data);
      form.setFieldsValue(data);
    }
  };
  const YearValidator = (rule, value, callback) => {
    let rep = /^\d{4}$/;
    if (!rep.test(value)) {
      return callback('请输入正确年份');
    } else {
      return callback();
    }
  }
  const clear = () => {
    setConfirmLoading(false);
    setData({});
    form.resetFields();
  };
  const open = () => {
    setVisible(true);
  };
  useImperativeHandle(ref, () => ({
    open: query => {
      if (!_isEmpty(query)) {
        getDateInfo(query).then();
      }
      open();
    },
    clear: () => {
      clear();
    },
  }));
  return (
    <Modal
      title={`${_isEmpty(data) ? '新增' : '编辑'}地方委员会情况`}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={800}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      <Form form={form} {...formItemLayout} onFinish={onFinish}>

        <Form.Item name='year'
          label='年度'
          rules={[{ required: true, message: '请填写' }]}
        >
          <Select style={{ width: '100%' }} >
            {NowYear.map(it => <Select.Option value={it}>{it}</Select.Option>)}
          </Select>
        </Form.Item>

        <Form.Item name='situation'
          label='任届情况'
          rules={[{ required: false, message: '请选择' }]}
        >
          <DictSelect codeType={'dict_d100'}
            initValue={data['situation'] || undefined}
          />
        </Form.Item>

        <Form.Item noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.situation !== currentValues.situation}
        >
          {({ getFieldValue }) => {
            return getFieldValue('situation') == '2' ? <Fragment>
              <Form.Item name='representatives'
                label='本年换届选举党代表'
                rules={[{ required: true, message: '本年换届选举党代表必填' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Fragment> : null;
          }}
        </Form.Item>

        <Form.Item name='congressSituation'
          label='党代会情况'
          rules={[{ required: true, message: '请选择' }]}
        >
          <DictSelect codeType={'dict_d101'}
            initValue={data['congressSituation'] || undefined}
          />
        </Form.Item>


        <Form.Item name='partyCommittee'
          label='党委委员'
          rules={[{ required: true, message: '党委委员必填' }]}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.partyCommittee !== currentValues.partyCommittee}
        >
          {({ getFieldValue }) => {
            return getFieldValue('partyCommittee') > 0 ? <Fragment>
              <Form.Item name='standingCommittee'
                label='党委常委'
                rules={[{ required: true, message: '党委常委必填' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Fragment> : null;
          }}
        </Form.Item>

        <Form.Item name='partyAlternateCommittee'
          label='党委候补委员'
          rules={[{ required: true, message: '党委候补委员必填' }]}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>


        <Form.Item name='inspectionCommittee'
          label='纪委委员'
          rules={[{ required: true, message: '纪委委员必填' }]}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item name='disciplineInspectionCommission'
          label='纪委常委'
          rules={[{ required: true, message: '纪委常委必填' }]}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>

        {/* <Form.Item noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.inspectionCommittee !== currentValues.inspectionCommittee}
        >
          {({ getFieldValue }) => {
            return getFieldValue('inspectionCommittee') > 0 ? <Fragment>
              <Form.Item name='inspectionStandingCommittee'
                label='党委常委'
                rules={[{ required: true, message: '党委常委必填' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Fragment> : null;
          }}
        </Form.Item> */}

        <Form.Item name='wholeSituation'
          label='召开党委全会情况'
          rules={[{ required: true, message: '请选择' }]}
        >
          <DictSelect codeType={'dict_d102'}
            initValue={data['wholeSituation'] || undefined}
          />
        </Form.Item>

        <Form.Item noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.wholeSituation !== currentValues.wholeSituation}
        >
          {({ getFieldValue }) => {
            let value = getFieldValue('wholeSituation');
            if (value && value !== '1') {
              return (
                <Fragment>
                  {/* <Form.Item name='attendMember'
                    label='参加委员'
                    rules={[{ required: true, message: '参加委员必填' }]}
                  >
                    <InputNumber min={0} style={{ width: '100%' }} />
                  </Form.Item> */}
                  {/* 5.信息项召开党委全会情况选择(已召开或已召开两次以上)，新增填写信息项：党委全会参加委员(数字、必填)； */}
                  <Form.Item name='partyCommitteePlenary'
                    label='党委全会参加委员'
                    rules={[{ required: true, message: '党委全会参加委员必填' }]}
                  >
                    <InputNumber min={0} style={{ width: '100%' }} />
                  </Form.Item>
                </Fragment>
              )
            }
          }}
        </Form.Item>

        <Form.Item name='lifeSituation'
          label='领导班子召开民主生活会情况'
          rules={[{ required: true, message: '请选择' }]}
        >
          <DictSelect codeType={'dict_d103'}
            initValue={data['lifeSituation'] || undefined}
          />
        </Form.Item>

        <Form.Item noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.lifeSituation !== currentValues.lifeSituation}
        >
          {({ getFieldValue }) => {
            let value = getFieldValue('lifeSituation');
            if (value && value !== '1') {
              return (
                <Fragment>
                  {/* <Form.Item name='participants'
                    label='参加人员'
                    rules={[{ required: true, message: '参加人员必填' }]}
                  >
                    <InputNumber min={0} style={{ width: '100%' }} />
                  </Form.Item> */}
                  <Form.Item name='ethnicLife'
                    label='民主生活会参加人员'
                    rules={[{ required: true, message: '民主生活会参加人员必填' }]}
                  >
                    <InputNumber min={0} style={{ width: '100%' }} />
                  </Form.Item>
                </Fragment>
              )
            }
          }}
        </Form.Item>

        {/* <div style={{ textAlign: 'center' }}>
          <Button type={'primary'} onClick={() => form.submit()}>保存</Button>
        </div> */}
      </Form>
    </Modal>
  )
};
export default React.forwardRef(index);
