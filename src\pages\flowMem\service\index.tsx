import request from 'src/utils/request';
import qs from 'qs';

export function countIsProvOut(params) {
  return request(`/api/flowmem/countIsProvOut?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
//党员流出申请接口
export function add(params) {
  return request('/api/flowmem/addOutMem', {
    method: 'POST',
    body: params,
  });
}

//党员流出申请接口
export function findOrgFlowController(params) {
  console.log('中间交换区查询params===', params);

  return request('/api/orgFlow/findBranchOrg', {
    method: 'POST',
    body: params,
  });
}
//党员流出申请接口
export function orgFindBranchOrg(params) {
  console.log('/org/findBranchOrg', params);
  return request('/api/org/findBranchOrg', {
    method: 'POST',
    body: params,
  });
}

export function findTransferOrg(params) {
  console.log('中间交换区查询params===', params);
  return request('/api/org/findTransferOrg', {
    method: 'POST',
    body: params,
  });
}
//列表
// export function outMemList(params) {
//   return request(`/api/flowmem/outMemList?pageNum=${params.pageNum}&pageSize=${params.pageSize}&orgCode=${params.orgCode}`,{
//     method:'GET',
//   });
// }
export function outMemList(params) {
  return request('/api/flowmem/outMemList', {
    method: 'POST',
    body: params,
  });
}
//获取已纳入流入地管理 操作消息按钮里面的列表
export function getOutMemFlowMessageList(params) {
  console.log('🚀 ~ getOutMemFlowMessageList ~ params:', params);
  return request('/api/memFlowMessage/list', {
    method: 'POST',
    body: params,
  });
}

//获取已纳入流入地管理 操作消息按钮里面的列表, 添加列表里面的信息
export function addOutMemFlowMessage(params) {
  console.log('🚀 ~ addOutMemFlowMessage ~ params:', params);

  return request('/api/memFlowMessage/add', {
    method: 'POST',
    body: params,
  });
}

//查询单个详情
export function findOutMemByCode(params) {
  return request(`/api/flowmem/findOutMemByCode?code=${params.code}`, {
    method: 'GET',
  });
}
//党员流回
export function backOutMem(params) {
  return request('/api/flowmem/backOutMem', {
    method: 'POST',
    body: params,
  });
}
//省内传入申请
export function addInMem(params) {
  return request('/api/flowmem/addInMem', {
    method: 'POST',
    body: params,
  });
}
//省外流入申请
export function systemInMme(params) {
  return request('/api/flowmem/systemInMme', {
    method: 'POST',
    body: params,
  });
}
//
export function delMemFlow(params) {
  return request('/api/flowmem/delMemFlow', {
    method: 'POST',
    body: params,
  });
}
//流入
export function inMemList(params) {
  return request('/api/flowmem/inMemList', {
    method: 'POST',
    body: params,
  });
}

//继续流动
export function continueFlow(params) {
  return request('/api/flowmem/continueFlow', {
    method: 'POST',
    body: params,
  });
}
//历史
export function historyFlowList(params) {
  return request('/api/flowmem/historyFlowList', {
    method: 'POST',
    body: params,
  });
}
// export function historyFlowList(params) {
//   return request(`/api/flowmem/historyFlowList?pageNum=${params.pageNum}&pageSize=${params.pageSize}&orgCode=${params.orgCode}`,{
//     method:'GET',
//   });
// }
// 党员流出: 获取联系人信息
export function findOrgByCode(params) {
  return request(`/api/flowmem/findOrgByCode?code=${params.code}`, {
    method: 'GET',
  });
}

// 流出管理-列表
export function outManageList(params) {
  console.log('流出管理-列表===', params);

  return request('/api/mem/flow/notIncludedPage', {
    method: 'POST',
    body: params,
  });
}

// 流出管理-查详情
export function outManageFind(params) {
  console.log('流出管理-详情params====', params);

  return request(`/api/mem/flow/outFind?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
//流动党员提醒-查询党员信息
export function findMemByIdCardAndName(params) {
  console.log('流动党员提醒-查询党员信息===', params);
  return request('/api/mem/flow/audit/findMemByIdCardAndName', {
    method: 'POST',
    body: params,
  });
}

// 流出管理-详情-活动信息-列表
export function outManageDetailList(params) {
  console.log('流出管理-详情-活动信息列表===', params);
  return request('/api/mem/flow/events/page', {
    method: 'POST',
    body: params,
  });
}
// 流出管理-详情-活动信息-列表-详情
export function outManageDetailData(params) {
  console.log('流出管理-详情-活动信息列表-详情===', params);
  return request('/api/mem/flow/events/select', {
    method: 'POST',
    body: params,
  });
}
// 流出管理-详情-活动信息-列表-新增
export function outManageDetailAdd(params) {
  console.log('流出管理-详情-活动信息列表-新增===', params);
  return request('/api/mem/flow/events/insert', {
    method: 'POST',
    body: params,
  });
}
// 流出管理-详情-活动信息-列表-修改
export function outManageDetailEdit(params) {
  console.log('流出管理-详情-活动信息列表-修改===', params);
  return request('/api/mem/flow/events/update', {
    method: 'POST',
    body: params,
  });
}
// 流出管理-详情-活动信息-列表-删除
export function outManageDetailDel(params) {
  console.log('流出管理-详情-活动信息列表-删除===', params);
  return request('/api/mem/flow/events/delete', {
    method: 'POST',
    body: params,
  });
}

// 外出地点-连接中间交换区查询的
export function findApprovalOrg(params) {
  console.log('中间交换区查询params===', params);

  return request('/api/org/findApprovalOrg', {
    method: 'POST',
    body: params,
  });
}
// 流入党员流出登记新增 流动党委接口
export function findApprovalFlowOrg(params) {
  console.log('中间交换区查询params===', params);

  return request('/api/orgFlow/search', {
    method: 'POST',
    body: params,
  });
}
// 流出登记
export function outManageRegister(params) {
  console.log('流出登记params===', params);
  return request('/api/mem/flow/register', {
    method: 'POST',
    body: params,
  });
}

// 流出被退回-终止
export function outManageTermination(params) {
  console.log('流出被退回-终止====', params);

  return request(`/api/mem/flow/termination?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

// 流出管理-未纳入流入地-撤销
export function outManageRevoke(params) {
  console.log('流出管理-撤销====', params);

  return request(`/api/mem/flow/outRevoke?${qs.stringify(params)}`, {
    method: 'POST',
    body: params,
  });
}

// 流出管理-未纳入流入地-编辑
export function outManageSomeEdit(params) {
  console.log('出管理-未纳入流入地-编辑params===', params);
  return request('/api/mem/flow/renew', {
    method: 'POST',
    body: params,
  });
}

// 流出管理-未纳入-修改流入行政区
export function updateAdministrativeDivision(params) {
  console.log('流出管理-未纳入-修改流入行政区params===', params);
  return request('/api/mem/flow/updateAdministrativeDivision', {
    method: 'POST',
    body: params,
  });
}

// 流出管理-已纳入流入地-流回
export function outManageFlowBack(params) {
  console.log('出管理-已纳入流入地-流回params===', params);
  return request('/api/mem/flow/outFlowBack', {
    method: 'POST',
    body: params,
  });
}

// 流出管理-流出被退回-重新流出
export function outManageReFlow(params) {
  console.log('流出管理-重新流出params===', params);
  return request('/api/mem/flow/reRegister', {
    method: 'POST',
    body: params,
  });
}

// 流入管理-流动党员提醒-审批通过-重新流出
export function approve1(params) {
  console.log('流出管理-重新流出params===', params);
  return request('/api/mem/flow/audit/approve1', {
    method: 'POST',
    body: params,
  });
}

// 流出管理-流出登记-校验流出登记中的党员是否合理
export function outManageCheck(params) {
  console.log('校验流出登记中的党员是否合理params===', params);
  return request('/api/mem/flow/check', {
    method: 'POST',
    body: params,
  });
}
// 流出管理-流出登记-校验流出登记中的党员是否合理
export function outManageCheckPhone(params) {
  console.log('校验流出登记中的党员的电话号码是否合理params===', params);
  return request('/api/mem/flow/findOrgList', {
    method: 'POST',
    body: params,
  });
}

// 流入管理-列表
export function inManageList(params) {
  console.log('流入管理列表params===', params);
  return request('/api/mem/flow/findInByPage', {
    method: 'POST',
    body: params,
  });
}

// 流入管理-详情
export function inManageFind(params) {
  console.log('流入管理-详情params====', params);

  return request(`/api/mem/flow/findByCode?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

// 流入管理-未纳入-移至县级库
export function inManageMove(params) {
  console.log('移至县级库params===', params);
  return request('/api/mem/flow/toCountyLib', {
    method: 'POST',
    body: params,
  });
}

// 流入管理-未纳入-退回
export function inManageBackTo(params) {
  console.log('未纳入-退回params===', params);
  return request('/api/mem/flow/back', {
    method: 'POST',
    body: params,
  });
}

// 流入管理-未纳入-接收
export function inManageReceive(params) {
  console.log('未纳入-接收params===', params);
  return request('/api/mem/flow/receive', {
    method: 'POST',
    body: params,
  });
}

// 流入管理-已纳入支部管理-业务操作
export function inManageOperate(params) {
  console.log('已纳入支部管理-业务操作params===', params);
  return request('/api/mem/flow/operate', {
    method: 'POST',
    body: params,
  });
}

// 流入管理-已纳入支部管理-流回
export function inManageFlowBack(params) {
  console.log('已纳入支部管理-流回params===', params);
  return request('/api/mem/flow/flowBack', {
    method: 'POST',
    body: params,
  });
}

//流动党组织审核 列表
export function inflowOrganizationDList(params) {
  console.log('🚀 ~ inflowOrganizationDList ~ params:', params);
  return request('/api/org/flow/list', {
    method: 'POST',
    body: params,
  });
}
// 流动党员党组织 修改
export function inflowOrganizationDUpdate(params) {
  console.log('🚀 ~ inflowOrganizationDUpdate ~ params:', params);
  return request('/api/org/flow/update', {
    method: 'POST',
    body: params,
  });
}
// 流动党员党组织 撤销
export function inflowOrganizationDCancel(params) {
  console.log('🚀 ~ inflowOrganizationDSave ~ params:', params);
  return request('/api/org/flow/cancel', {
    method: 'POST',
    body: params,
  });
}
// 流动党员党组织 查看人员信息
export function inflowOrganizationDInfo(params) {
  console.log('🚀 ~ inflowOrganizationDInfo ~ params:', params);
  return request('/api/org/flow/detail', {
    method: 'POST',
    body: params,
  });
}

//流动党组织审核 列表
export function inflowOrganizationList(params) {
  console.log('🚀 ~ inflowOrganizationList ~ params:', params);
  return request('/api/flow/audit/list', {
    method: 'POST',
    body: params,
  });
}
// 流动党组织审核 审核
export function inflowOrganizationSave(params) {
  console.log('🚀 ~ inflowOrganizationSave ~ params:', params);
  return request('/api/flow/audit/save', {
    method: 'POST',
    body: params,
  });
}
// 流动党组织审核 撤销
export function inflowOrganizationCancel(params) {
  console.log('🚀 ~ inflowOrganizationSave ~ params:', params);
  return request('/api/flow/audit/cancel', {
    method: 'POST',
    body: params,
  });
}
// 流动党组织审核 查看人员信息
export function inflowOrganizationInfo(params) {
  console.log('🚀 ~ inflowOrganizationInfo ~ params:', params);
  return request('/api/org/flow/findById', {
    method: 'POST',
    body: params,
  });
}

//新增流入登记
export function inflowadd(params) {
  console.log('🚀 ~ inflowOrganizationSave ~ params:', params);
  return request('/api/mem/flow/sign/add', {
    method: 'POST',
    body: params,
  });
}
//流动党员提醒 列表
export function ReminderMobilePartyMembersList(params) {
  console.log('🚀 ~ ReminderMobilePartyMembersList ~ params:', params);
  return request('/api/mem/flow/audit/list', {
    method: 'POST',
    body: params,
  });
}

//流动党员信息审核
export function flowmemapprove(params) {
  console.log('🚀 ~ ReminderMobilePartyMembersList ~ params:', params);
  return request('/api/mem/flow/audit/approve', {
    method: 'POST',
    body: params,
  });
}

// 流动党员提醒 查看人员信息
export function auditfind(params) {
  console.log('🚀 ~ inflowOrganizationInfo ~ params:', params);
  return request('/api/mem/flow/audit/find', {
    method: 'POST',
    body: params,
  });
}

export function findByCode(params) {
  console.log('流入管理-详情params====', params);

  return request(`/api/mem/findByCode?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function findOrg(params) {//组织基本信息查询
  return request(`/api/org/findByCode?${qs.stringify(params)}`);
}

export function findUniqueCode(params) {//查询唯一码的
  return request(`/api/exchangeLog/findMemFlowUqCodeByCode?${qs.stringify(params)}`);
}

// 获取联系方式
export function findExchangeLxfs(params) {
  console.log('获取联系方式params===', params);
  return request(`/api/mem/flow/findExchangeLxfs?${qs.stringify(params)}`, {
    method: 'Get',
  });
}