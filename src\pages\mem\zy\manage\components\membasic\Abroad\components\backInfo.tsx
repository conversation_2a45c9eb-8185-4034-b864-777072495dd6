// 回国信息
import React, { useState, Fragment, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import { Button, Modal, Form } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import { findDictCodeName, unixMoment } from '@/utils/method.js';
import DictSelect from '@/components/DictSelect';
import Date from '@/components/Date';
import { backMemAbroad } from '@/pages/mem/services/memAbroad';
const Index = forwardRef((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [recordData, setRecordData] = useState({});

  const [form] = Form.useForm();

  const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 12 },
  };

  useImperativeHandle(ref, () => ({
    open: (record: any) => {
      setVisible(true);
      setRecordData(record);
    },
  }));
  const handleCancel = () => {
    setVisible(false);
  };
  const handleOk = () => {
    form.submit();
  };
  const onFinish = async (value: any) => {
    value = unixMoment(['backHomeDate', 'renewOrglifeDate'], value);
    value = findDictCodeName(['d040', 'd037'], value);
    // return
    setConfirmLoading(true);
    const res = await backMemAbroad({
      data:{
        code: recordData.code,
      abroadOrgCode: recordData.abroadOrgCode, //abroadOrgCode	人员所属组织层级码
      ...value,
      }
    });
    setConfirmLoading(false);
    if (res.code === 0) {
      setVisible(false);
      props?.refreshAbroadList && props.refreshAbroadList();
    }
  };
  return (
    <Modal title="添加回国信息" destroyOnClose visible={visible} onOk={handleOk} onCancel={handleCancel} width={'800px'} confirmLoading={confirmLoading}>
      <Form form={form} {...formItemLayout} onFinish={onFinish}>
        <Form.Item
          name="backHomeDate"
          label="回国日期"
          rules={[{ required: true, message: '请输入回国日期' }]}
          // initialValue={}
        >
          <Date />
        </Form.Item>
        <Form.Item
          name="d037Code"
          label="出国党员与党组织联系情况"
          rules={[{ required: true, message: '出国党员与党组织联系情况' }]}
          // initialValue={}
        >
          <DictSelect
            codeType={'dict_d37'}
            //   initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d037Code']}
            backType={'object'}
          />
        </Form.Item>
        <Form.Item
          name="renewOrglifeDate"
          label="申请恢复组织生活日期"
          rules={[{ required: false, message: '请输入申请恢复组织生活日期' }]}
          // initialValue={}
        >
          <Date />
        </Form.Item>
        <Form.Item
          name="d040Code"
          label="恢复组织生活情况"
          rules={[{ required: true, message: '恢复组织生活情况' }]}
          // initialValue={}
        >
          <DictSelect
            codeType={'dict_d40'}
            //   initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d037Code']}
            backType={'object'}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
});
export default Index;
