 import request from "@/utils/request";
 import qs from 'qs';
 export function trainAdd(params) {
    return request(`/api/mem/trainInfo/addOrUpdate`,{
      method:'POST',
      body:params,
    });
  }

  export function listTrainByCode(params) {
    return request(`/api/mem/trainInfo/getList`,{
      method:'POST',
      body:params,
    });
  }

  export function findTrainByCode(params) {
    return request(`/api/mem/trainInfo/findByCode?${qs.stringify(params)}`);
  }

  export function deleteTrainByCode(params) {
    return request(`/api/mem/trainInfo/del?${qs.stringify(params)}`);
  }

