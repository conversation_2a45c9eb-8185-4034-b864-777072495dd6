import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {
  add,
  addGroup,
  addGroupMem,
  addReward,
  delGroup,
  delGroupMem,
  delReward,
  findOrg,
  findHistoryOrg,
  findReward,
  getList,
  groupMemList,
  orgElect,
  orgElectDel,
  orgElectList,
  orgElectUp,
  teamList,
  updated,
  updateGroup,
  updateReward,
  itteeAdd,
  itteeUP,
  itteeDel,
  itteeList,
  addOrUpdate,
  findExtendByCode,
  appraisalAdd,
  appraisalUpdate,
  appraisalRemove,
  appraisalList,
  personSave,
  personUpdate,
  personRemove,
  personList,
  listMem,
  partyGetList,
  getListByIdcard,
  addOrgFlow,
  updateOrgFlow,
  getHistoryList
} from '../services/org';
import {memLockedList} from '@/pages/mem/services';
import {getSession} from 'src/utils/session';
import { changeListPayQuery } from '@/utils/method.js';
import _isEmpty from 'lodash/isEmpty';

const org = modelExtend(listPageModel,{
  namespace: "org",
  state:{
    basicInfo:{},
    basicInfoData:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        const tabType  = JSON.parse(sessionStorage.getItem('orgListTabType')) || 1;
        let isLockUrl = (pathname === '/unlock/locked' || pathname === '/unlock/unlock')  && query.lockObject == '3';
        if(pathname==='/org/list' || isLockUrl){
          console.log("🚀 ~ setup ~ location:", location)
          const org=getSession('org') || {};
          const defaultParas={
            pageNum:1,
            pageSize:10,
          };
          const dictData=['dict_d01','dict_d02','dict_d03','dict_d04',"dict_d194","dict_d195"];
          for(let obj of dictData){
            dispatch({
              type:'commonDict/getDictTree',
              payload:{
                data:{
                  dicName:obj
                }
              }
            });
          }
          if(!_isEmpty(org)){
            if(isLockUrl){
              dispatch({
                type:'getUnlockList',
                payload:{
                  data:{
                    memCode:org['orgCode'],
                    ...defaultParas,
                    ...query,
                  }
                }
              })
            }else{
              if(tabType == 1){
                dispatch({
                  type:'getList',
                  payload:{
                    data:{
                      orgCode:org['orgCode'],
                      ...defaultParas,
                      ...query,
                    }
                  }
                })
              }else{
                dispatch({
                  type:'getHistoryList',
                  payload:{
                    data:{
                      orgCode:org['orgCode'],
                      ...defaultParas,
                      ...query,
                    }
                  }
                })
              }
            }
          }
        }
      });
    }
  },
  effects: {
    // 列表
    *getList({payload}, {call, put,select}) {
      const {filter,orgName}=yield select(state=>state['org']);
      payload['data']={...payload['data'],...filter,orgName};
      const {data = {}} = yield call(getList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    *getHistoryList({payload}, {call, put,select}) {
      const {filter,orgName}=yield select(state=>state['org']);
      console.log("🚀 ~ *getHistoryList ~ orgName:", orgName,payload)
      payload['data']={...payload['data'],...filter,orgName};
      const {data = {}} = yield call(getHistoryList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    *getUnlockList({payload}, {call, put,select}) {
      const {filter,orgName}=yield select(state=>state['org']);
      payload['data']={...payload['data'],...filter,keyword:orgName};
      const {data = {}} = yield call(memLockedList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    // 查找人员列表
    *add({payload}, {call, put}) {
      return yield call(add, payload);
    },
    *update({payload}, {call, put}) {
      return yield call(updated, payload);
    },
      // -----
      *addOrgFlow({payload}, {call, put}) {
        return yield call(addOrgFlow, payload);
      },
      *updateOrgFlow({payload}, {call, put}) {
        return yield call(updateOrgFlow, payload);
      },
    
    *findOrg({payload}, {call, put}) {
      const obj = yield call(findOrg, payload);
      yield put({
        type: 'updateState',
        payload: {
          basicInfo: obj['data'] || {},
        }
      });
    },
    *findHistoryOrg({payload}, {call, put}) {
      const obj = yield call(findHistoryOrg, payload);
      yield put({
        type: 'updateState',
        payload: {
          basicInfo: obj['data'] || {},
        }
      });
    },

    //组织奖惩
    *findReward({payload}, {call, put}) {
      const {data = {}} = yield call(findReward, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'updateState',
        payload: {
          rewardList:res.list,
          rewardPagination:res.pagination
          // -------
          // rewardList: data['list'] || [],
          // rewardPagination: {
          //   current: data['current'],
          //   pageSize: data['pageSize'],
          //   total: data['total'],
          // }
        }
      });
    },
    *addReward({payload}, {call, put}) {
      return yield call(addReward, payload);
    },
    *updateReward({payload}, {call, put}) {
      return yield call(updateReward, payload);
    },
    *delReward({payload}, {call, put}) {
      return yield call(delReward, payload);
    },

    // // 党务工作者情况
    // *workList({payload}, {call, put}) {
    //   const {data = {}} = yield call(workList, payload);
    //   const res = changeListPayQuery(data);
    //   yield put({
    //     type: 'updateState',
    //     payload: {
    //       workListData:res.list,
    //       workPagination:res.pagination
    //     }
    //   });
    // },

    //党小组 teamList,addGroup,updateGroup,delGroup
    *findTeam({payload}, {call, put}) {
      const {data = {}} = yield call(teamList, payload);
      yield put({
        type: 'updateState',
        payload: {
          teamList: data['list'] || [],
        }
      });
    },
    *addGroup({payload}, {call, put}) {
      return yield call(addGroup, payload);
    },
    *updateGroup({payload}, {call, put}) {
      return yield call(updateGroup, payload);
    },
    *delGroup({payload}, {call, put}) {
      return yield call(delGroup, payload);
    },
    //党小组成员 groupMemList addGroupMem delGroupMem
    *groupMemList({payload}, {call, put}) {
      const {keys}=payload;
      const {data = {}} = yield call(groupMemList, payload);
      yield put({
        type: 'updateState',
        payload: {
          [`groupMemList_${keys}`]: data['list'] || [],
        }
      });
    },
    *addGroupMem({payload}, {call, put}) {
      return yield call(addGroupMem, payload);
    },
    *delGroupMem({payload}, {call, put}) {
      return yield call(delGroupMem, payload);
    },


    //领导班子 orgElect,orgElectUp
    *orgElect({payload}, {call, put}) {
      return yield call(orgElect, payload);
    },
    *orgElectUp({payload}, {call, put}) {
      return  yield call(orgElectUp, payload);
    },
    *orgElectList({payload}, {call, put}) {
      const {data = {}} = yield call(orgElectList, payload);
      yield put({
        type: 'updateState',
        payload: {
          electList: data['list'] || [],
        }
      });
    },
    *orgElectDel({payload}, {call, put}) {
      return yield call(orgElectDel, payload);
    },
    //领导班子成员 orgElect,orgElectUp
    *itteeAdd({payload}, {call, put}) {
      return yield call(itteeAdd, payload);
    },
    *itteeUp({payload}, {call, put}) {
      return  yield call(itteeUP, payload);
    },
    *itteeList({payload}, {call, put}) {
      const {keys}=payload;
      const {data = {}} = yield call(itteeList, payload);
      yield put({
        type: 'updateState',
        payload: {
          [`iteList_${keys}`]: data['list'] || [],
        }
      });
    },
    *itteeDel({payload}, {call, put}) {
      return yield call(itteeDel, payload);
    },
    *addOrUpdate({payload}, {call, put}) {
      const info = yield call(addOrUpdate, payload);
        return Promise.resolve(info);
    },

    *findExtendByCode({payload}, {call, put}) {
      const { code, data } = yield call(findExtendByCode, payload);
      if (code === 0) {
        return Promise.resolve(data);
      }
      return Promise.reject();
    },
    //民主评议
    *appraisalAdd({payload}, {call, put}) {
      const info = yield call(appraisalAdd, payload);
        return Promise.resolve(info);
    },

    *appraisalUpdate({payload}, {call, put}) {
      const info = yield call(appraisalUpdate, payload);
        return Promise.resolve(info);
    },

    *appraisalList({payload}, {call, put}) {
      const {data = {}} = yield call(appraisalList, payload);
      yield put({
        type: 'updateState',
        payload: {
          appList: data['list'] || [],
        }
      });
    },

    *appraisalRemove({payload}, {call, put}) {
      const info = yield call(appraisalRemove, payload);
        return Promise.resolve(info);
    },

    *personSave({payload}, {call, put}) {
      const info = yield call(personSave, payload);
        return Promise.resolve(info);
    },
    *personUpdate({payload}, {call, put}) {
      const info = yield call(personUpdate, payload);
        return Promise.resolve(info);
    },
    *personRemove({payload}, {call, put}) {
      const info = yield call(personRemove, payload);
        return Promise.resolve(info);
    },
    *personList({payload}, {call, put}) {
      const {keys}=payload;
      const {data = {}} = yield call(personList, payload);
      yield put({
        type: 'updateState',
        payload: {
          [`personList_${keys}`]: data['list'] || [],
          [`personList_${keys}_totalPage`]:data['totalPage'],
        }
      });
    },
    *listMem({payload}, {call, put}) {
      const {data = {}} = yield call(listMem, payload);
      const { list,...pagination }=data
      yield put({
        type: 'updateState',
        payload: {
          listMemData: list || [],
          listMemPagination:pagination
        }
      });
    },
    // 党组列表
    *partyGetList({payload}, {call, put,select}) {
      const {filter,partyName}=yield select(state=>state['org']);
      payload['data']={...payload['data'],...filter,partyName};
      const {data = {}} = yield call(partyGetList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    *getListByIdcard({ payload }, { call, put }) {
      return yield call(getListByIdcard, payload);
    },
  }
});
export default org;
