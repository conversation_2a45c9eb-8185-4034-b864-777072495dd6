/**
 * 奖惩信息
 **/
import React from 'react';
import ListTable from '@/components/ListTable';
import { PlusOutlined } from '@ant-design/icons';
import {But<PERSON>, Divider, Popconfirm} from 'antd';
import AddRandP from './addRandP';
import Tip from '@/components/Tip';
import moment from 'moment';

const defaultListParams={
  pageNum:1,
  pageSize:10,
};

export default class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      type:'add'
    }
  }
  componentDidMount = () => {
    this.action();
    const dictData=['dict_d42','dict_d47'];//查询字典
    for(let obj of dictData){
      this.props.dispatch({
        type:'commonDict/getDict',
        payload:{
          data:{
            codeType:obj,
            dicName:obj
          }
        },
      });
    }
  };
  action=(page?:number,pageSize?:number)=>{
    const {basicInfo,rewardPagination}=this.props.org;
    let params=defaultListParams;
    if(rewardPagination){
      params={...params,...rewardPagination}
    }
    if(page && pageSize){
      params={...params,pageNum:page, pageSize,}
    }
    if(basicInfo['code']){
      this.props.dispatch({
        type:'org/findReward',
        payload:{
          orgCode:basicInfo['orgCode'],
          ...params,
        },
      });
    }
  };
  add=(e)=>{
    this.setState({
      type:'add',
      dataInfo:undefined
    },()=>{
      this['AddRandP'].showModal();
    });
  };
  edit=(item)=>{
    this.setState({dataInfo:item,type:'edit'},()=>{
      this['AddRandP'].showModal();
    });
  };
  del=async (item)=>{
    const obj=await this.props.dispatch({
      type:'org/delReward',
      payload:{
        code:item['code']
      }
    });
    if(obj && obj['code']===0){
      Tip.success('操作提示','删除成功');
      this.action();
    }
  };
  onPageChange=(page,pageSize)=>{
    this.action(page,pageSize);
  };
  render(): React.ReactNode {
    const { type,dataInfo } = this.state;
    const {rewardList,rewardPagination={},basicInfo={}}=this.props.org;
    const {current,pageSize}=rewardPagination;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:58,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'奖惩名称',
        dataIndex:'d42Name',
      },
      {
        title:'奖惩批准日期',
        dataIndex:'startDate',
        width:110,
        render:(text)=>{
          if(text){
            return moment(text).format('YYYY-MM-DD')
          }
        }
      },
      {
        title:'奖惩批准党组织名称',
        dataIndex:'rewardOrgCode',
        width:150,
        render:(text)=>{
          return basicInfo['name'] || ''
        }
      },
      {
        title:'奖惩依据',
        dataIndex:'remark',
      },
      {
        title:'操作',
        dataIndex:'action',
        width:100,
        render:(text,record)=>{
          return(
            <span>
              <a onClick={()=>this.edit(record)}>编辑</a>
              <Divider type="vertical" />
              <Popconfirm title="是否移除该信息?" onConfirm={()=>this.del(record)} okText="是" cancelText="否">
               <a className={'del'}>移除</a>
              </Popconfirm>
            </span>
          )
        }
      },
    ];
    return (
      <div style={{padding:'0 20px'}}>
        <AddRandP
          queryList={this.action}
          title={type === 'edit' ? '编辑奖惩信息' :'新增奖惩信息'}
          wrappedComponentRef={(e)=>this['AddRandP']=e}
          {...this.props}
          {...this.state}
        />
        <Button type="primary" icon={<PlusOutlined />} style={{marginBottom:10}} onClick={this.add}>添加奖惩</Button>
        <ListTable columns={columns} data={rewardList} pagination={rewardPagination} onPageChange={this.onPageChange}/>
      </div>
    );
  }
}
