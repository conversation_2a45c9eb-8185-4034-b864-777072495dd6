import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {getIndexList, getPercent,saveDevelopPlan} from '../services/index';
import {getSession} from "@/utils/session";
import { changeListPayQuery } from '@/utils/method.js';
const memDevelopPlan = modelExtend(listPageModel,{
  namespace: "memDevelopPlan",
  state:{
    planInfo:{}
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if(pathname==='/developMem/plan'){
          const org=getSession('org') || {};
          const defaultParas={
            pageNum:1,
            pageSize:10,
          };
          const dictData=['dict_d09','dict_d07'];
          // for(let obj of dictData){
          //   dispatch({
          //     type:'commonDict/getDictTree',
          //     payload:{
          //       data:{
          //         dicName:obj
          //       }
          //     }
          //   });
          // }
          dispatch({
            type:'getList',
            payload:{
              data:{
                memOrgCode:org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put,select }) {
      const {filter,memName} = yield select(state=>state['memDevelop']) || {};
      payload['data']={...payload['data'],...filter,memName};
      const {data={}} = yield call(getIndexList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    // 查找信息
    *getPercent({ payload }, { call, put }) {
      const res = yield call(getPercent,payload);
      const {code = 500, data= {}} = res || {};
      yield put({
        type: 'updateState',
        payload: {
          planInfo:code === 0 ? data : {}
        }
      })
    },
    // 修改指标
    *save({ payload }, { call, put }) {
      const res = yield call(saveDevelopPlan,payload);
      return res;
    },
    // *toActive({ payload }, { call, put }){
    //   const res = yield call(becomeActivist,payload);
    //   return res;
    // },
    // *toObject({ payload }, { call, put }){
    //   const res = yield call(becomeDevelopObject,payload);
    //   return res;
    //
    // },
    // *ToPreparation({ payload }, { call, put }){
    //   const res = yield call(becomePreliminary,payload);
    //   return res;
    // },
    // *delDevelop({ payload }, { call, put }){
    //   const res = yield call(delDevelop,payload);
    //   return res;
    // },
    // *backOutStatus({ payload }, { call, put }){
    //   const res = yield call(backOutStatus,payload);
    //   return res;
    // },
  }
});
export default memDevelopPlan;
