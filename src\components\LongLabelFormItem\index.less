.items {
  width: 100%;
  display: flex;
  margin-bottom: 24px;
  //justify-content: center;
  align-items: center;
  :global {
    .ant-form-item {
      margin-bottom:0px !important;
    }
    .ant-legacy-form-item {
      margin-bottom:0px !important;
    }
  }
  .label2{
    word-wrap: break-word;
    text-align: right;
    margin-bottom: 0px;
    margin-right: 2px;
    &::after {
      content: ':';
      margin: 0 8px 0 2px;
    }
  }
  .label {
    word-wrap: break-word;
    text-align: right;
    // margin-bottom: 24px;
    margin-right: 2px;
    &::before {
      display: inline-block;
      margin-bottom: 0px;
      color: #f5222d;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }
    &::after {
      content: ':';
      margin: 0 8px 0 2px;
    }
  }
  .desc {
    //display: flex;
    //justify-content: center;
    //align-items: center;
    width: 100%;
  }
}
