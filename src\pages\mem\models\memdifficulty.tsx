import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {findByMemCode,addMemDifficult,delMemDifficult,getList} from '../services/memDifficulty';
import {getSession} from "@/utils/session"; //模拟数据
import { changeListPayQuery } from '@/utils/method.js';
const memRewards = modelExtend(listPageModel,{
  namespace: "memDifficulty",
  state:{
    difficultyInfo:{}
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if( pathname === '/mem/difficulty'){
          const org=getSession('org') || {};
          const defaultParas={
            pageNum:1,
            pageSize:10,
          };
          dispatch({
            type:'getList',
            payload:{
              orgCode:org['orgCode'],
              ...defaultParas,
              ...query,
            }
          })
        }
      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put, select }) {
      const { filter, memName } = yield select((state) => state['memDifficulty']);
      const {data={}} = yield call(getList,  { ...payload, ...filter, memName });
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    // 新增编辑保存
    *save({ payload }, { call, put }) {
      const {type = '',data = {}} = payload;
      let res;
      switch (type) {
        case 'add':
          res = yield call(addMemDifficult,{data});
          break;
        case 'edit':
          res = yield call(addMemDifficult,{data});
          break;
        default:
          break;
      }
      return res;
    },
    // 回显
    *getInfo({ payload }, { call, put }) {
      const {data = {}} = yield call(findByMemCode, payload);
      yield put({
        type: 'updateState',
        payload: {
          difficultyInfo:data
        }
      })
    },
    // 删除
    *del({ payload }, { call, put }) {
      const res = yield call(delMemDifficult, payload);
      return res;
    },
    // 清除
    *clear({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          difficultyInfo:{},
        }
      })
    },
  }
});
export default memRewards;
