import React, { Fragment, useImperativeHandle, useState, useEffect } from 'react';
import { Form, Input, Modal, DatePicker, InputNumber, Row, Col, Button } from 'antd';
import moment from 'moment';
import Tip from '@/components/Tip';
import {addOrUpdate, findByCode} from './services';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import Date from '@/components/Date'

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};


/**
 * 单位类别为乡镇得时候（912开头）编辑单位的时候，基本信息下增加菜单：乡镇干部队伍补充信息，里面直接增加填写信息项目为：
 * 行政编制数（数字、必填）；
 * 事业编制数（数字、必填）；
 * 工勤编制数（数字、必填）；
 * 空缺行政编制数（数字、必填、不能大于行政编制数字）
 * 空缺事业编制数（数字、必填、不能大于行事业编制数字）；
 * 被借调工作人员总数（数字、必填）；
 * 省级以上借调人数（数字、必填、不能大于被借调工作人员总数）；
 * 省级借调人数（数字、必填、不能大于被借调工作人员总数）；
 * 市级借调人数（数字、必填、不能大于被借调工作人员总数）；
 * 县级借调人数（数字、必填、不能大于被借调工作人员总数）
 */
const index = (props: any, ref) => {
  const {unit:{basicInfo = {}} = {}} = props;
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [title, setTitle] = useState('新增');
  useImperativeHandle(ref, () => ({
    open: (dataInfo) => {
      open(dataInfo);
    },
  }));
  const open = (dataInfo) => {
    // console.log('dataInfo==', dataInfo);
    setVisible(true);
    if (!_isEmpty(dataInfo)) {
      setTitle('编辑');
      setDataInfo(dataInfo);
      form.setFieldsValue({
        ...dataInfo,
        countDate: dataInfo?.countDate ? moment(dataInfo?.countDate) : undefined,
      });
    }
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {

    let arr = [
      {
        big: { key: 'xzbzNum', name: '行政编制数' },
        small: { key: 'kqxzbzNum', name: '空缺行政编制数' },
      },
      {
        big: { key: 'sybzNum', name: '事业编制数' },
        small: { key: 'kqsybzNum', name: '空缺事业编制数' },
      },
      {
        big: { key: 'secondedStaffNum', name: '被借调工作人员总数' },
        small: { key: 'aboveProvincialSecondedStaffNum', name: '省级以上借调人数' },
      },
      {
        big: { key: 'secondedStaffNum', name: '被借调工作人员总数' },
        small: { key: 'provincialSecondedStaffNum', name: '省级借调人数' },
      },
      {
        big: { key: 'secondedStaffNum', name: '被借调工作人员总数' },
        small: { key: 'citySecondedStaffNum', name: '市级借调人数' },
      },
      {
        big: { key: 'secondedStaffNum', name: '被借调工作人员总数' },
        small: { key: 'countySecondedStaffNum', name: '县级借调人数' },
      },
    ];

    for (let index in arr) {
      let item = arr[index];
      if (e[_get(item, 'big.key')] < e[_get(item, 'small.key')]) {
        form.setFields([{
          name: _get(item, 'small.key'),
          value: e[_get(item, 'small.key')],
          errors: [`${_get(item, 'small.name')}不能大于${_get(item, 'big.name')}`]
        }]);
        return
      }
    }
    e.countDate = moment(e.countDate).valueOf();
    setConfirmLoading(true);
    const { code: resCode = 500 } = await addOrUpdate({
      data: {
        ...e,
        unitCode:basicInfo.code,
        orgCode:basicInfo.createOrgCode,
        code:dataInfo?.code || undefined,
      },
    });
    setConfirmLoading(false);
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      props.onOK && props.onOK();
    }
  };

  const getDetail = async () =>{
    const {code = 500 , data = {}}= await findByCode({code:dataInfo.code});
    if(code === 0) {
      setDataInfo(data);
      form.setFieldsValue({
        ...data,
        countDate: data?.countDate ? moment(data?.countDate) : undefined,
      });
    }
  }
  // const YearValidator = (rule, value, callback) => {
  //   if (value && (value < 1900 || value > 2100)) {
  //     return callback('请输入正确年份');
  //   } else {
  //     return callback();
  //   }
  // };
  useEffect(() => {
    if(dataInfo.code){
      getDetail()
    }
  },[])
  return (
    <Fragment>
      <Modal
        title={title}
        destroyOnClose
        visible={visible}
        confirmLoading={confirmLoading}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={'1000px'}
      >
      <Form form={form} {...formItemLayout} onFinish={onFinish}>
        <Row>
        <Col span={24}>
        <Form.Item
                name="countDate"
                label="统计时间"
                // rules={[{ required: true, message: '请输入年份' },{ validator: YearValidator }]}
                rules={[{ required: true, message: '请输入统计时间' }]}
                // initialValue={}
              >
                {/* <DatePicker onChange={() => {}} picker="year" /> */}
                {/* <InputNumber style={{ width: '100%' }} maxLength={4} minLength={4} /> */}
                <Date />
              </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="xzbzNum"
              label="行政编制数"
              rules={[{ required: true, message: '请输入行政编制数' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0}  precision={0}/>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="sybzNum"
              label="事业编制数"
              rules={[{ required: true, message: '请输入事业编制数' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0}  precision={0}/>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="gqbzNum"
              label="工勤编制数"
              rules={[{ required: true, message: '请输入工勤编制数' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0}  precision={0}/>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="kqxzbzNum"
              label="空缺行政编制数"
              rules={[{ required: true, message: '请输入空缺行政编制数' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0}  precision={0}/>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="kqsybzNum"
              label="空缺事业编制数"
              rules={[{ required: true, message: '请输入空缺事业编制数' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0}  precision={0}/>
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item
              name="secondedStaffNum"
              label="被借调工作人员总数"
              rules={[{ required: true, message: '请输入被借调工作人员总数' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0}  precision={0}/>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="aboveProvincialSecondedStaffNum"
              label="省级以上借调人数"
              rules={[{ required: true, message: '请输入省级以上借调人数' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0}  precision={0}/>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="provincialSecondedStaffNum"
              label="省级借调人数"
              rules={[{ required: true, message: '请输入省级借调人数' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0}  precision={0}/>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="citySecondedStaffNum"
              label="市级借调人数"
              rules={[{ required: true, message: '请输入市级借调人数' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0}  precision={0}/>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="countySecondedStaffNum"
              label="县级借调人数"
              rules={[{ required: true, message: '请输入县级借调人数' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0}  precision={0}/>
            </Form.Item>
          </Col>

          {/* <Col span={24} style={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
            <Button type={'primary'} htmlType={'submit'} loading={confirmLoading}>
              保 存
            </Button>
          </Col> */}
        </Row>
      </Form>
      </Modal>
    </Fragment >
  );
};
export default React.forwardRef(index);
