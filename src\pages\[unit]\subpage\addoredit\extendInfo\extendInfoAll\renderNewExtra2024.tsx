import _isNumber from 'lodash/isNumber';
// import { Form } from 'antd';
// const [form] = Form.useForm();
// 当前前端页面的全量字段
export const getZiDuanArr = (form, getDictValue, formData, unitName) => {
  // 单位是331普通高等学校、332中等学校，5211民办高等学校、52121民办普通高中、5213民办中等职业技术学校的时候，增加以下信息项目:
  const school = [
    {
      label: '全体在校学生中研究生人数',
      code: 'graduateStudent',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '全体在校学生中研究生人数' }],
    },
    {
      label: '全体在校学生中大学本科生人数',
      code: 'undergraduateStudent',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '全体在校学生中大学本科生人数' }],
    },
    {
      label: '全体在校学生中大学专科生人数',
      code: 'juniorCollegeStudent',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '全体在校学生中大学专科生人数' }],
    },
    {
      label: '全体在校学生中中专生人数',
      code: 'technicalSecondaryStudent',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '全体在校学生中中专生人数' }],
    },
    {
      label: '全体在校学生中高中、中技人数',
      code: 'middleTechnicalStudents',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '全体在校学生中高中、中技人数' }],
    },
  ];
  // 单位是331普通高等学校和5211民办高等学校时，增加以下信息项目:
  const school2 = [
    {
      label: '高等学校教师人数',
      code: 'teachersInstitutionsHigher',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '高等学校教师人数' }],
    },
    {
      label: '高等学校教师中女性人数',
      code: 'teachersHigherWomen',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '高等学校教师中女性人数' }],
    },
    {
      label: '高等学校教师中35岁及以下人数',
      code: 'teachersAgeThirtyFiveBelow',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '高等学校教师中35岁及以下人数' }],
    },
  ];
  // 显示建立党员服务机构和建立党员志愿者队伍 原来的条件是 911，现在加上 921，9121，9122，922，923
  const zhiyuanzhe = [
    {
      label: '是否建立志愿者服务机构',
      code: 'hasVolunteerOrganization',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立志愿者服务机构' }],
    },
    {
      label: '是否建立党员志愿者队伍',
      code: 'isVolTeam',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立党员志愿者队伍' }],
    },
  ];

  const _data1 = [
    {
      label: '在岗职工数（人）',
      code: 'onPostNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗职工数（人）' }],
    },
    {
      label: '党政机关工作人员',
      code: 'b30A12',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入党政机关工作人员' }],
    },
  ];
  const _data2 = [
    {
      label: '在岗职工数（人）',
      code: 'onPostNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗职工数（人）' }],
    },
    {
      label: '在岗专业技术人员数（人）',
      code: 'tecNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
    },
    {
      label: '专业技术人员中含高级职称（人）',
      code: 'zaigangGaoji',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '专业技术人员中含高级职称（人）' }],
    },
    // {
    //   label: '是否实行院（所）长负责制',
    //   code: 'hasDeanResponsibilitySystem',
    //   type: 'boolean',
    //   codeType: '',
    //   rules: [{ required: true, message: '是否实行院（所）长负责制' }],
    // },
    {
      label: '院所长负责制情况',
      code: 'd112Code',
      type: 'dict',
      codeType: 'dict_d112',
      rules: [{ required: true, message: '院所长负责制情况' }],
    },
    // 2024统计增加项 start
    {
      label: '领导体制已写入院（所）章程的',
      code: 'ldtzyyzc',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '领导体制已写入院（所）章程的' }],
    },
    {
      label: '已修订党组织会议、院（所）长办公会议议事规则的',
      code: 'ysgzIs',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '已修订党组织会议、院（所）长办公会议议事规则的' }],
    },
    {
      label: '党组织书记、院（所）长分设的',
      code: 'isLeaderSeparate',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '党组织书记、院（所）长分设的' }],
    },
    {
      label: '党组织书记不兼任行政领导职务的',
      code: 'hasClerkPosition',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '党组织书记不兼任行政领导职务的' }],
    },
    {
      label: '院（所）长系中共党员的',
      code: 'hasDeanPartyMember',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '院（所）长系中共党员的' }],
    },
    {
      label: '院（所）长担任党组织副书记的',
      code: 'hasDeanPartySecretary',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '院（所）长担任党组织副书记的' }],
    },
    {
      label: '已设立党务工作机构的',
      code: 'ysldwgzjgIs',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '已设立党务工作机构的' }],
    },
    {
      label: '已配备专职党务工作人员的',
      code: 'ypbzzdwgzryIs',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '已配备专职党务工作人员的' }],
    },
  ];
  const _data3 = [
    {
      label: '在岗职工数（人）',
      code: 'onPostNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗职工数（人）' }],
    },
    {
      label: '在岗专业技术人员数（人）',
      code: 'tecNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
    },
    {
      label: '专业技术人员中含高级职称（人）',
      code: 'zaigangGaoji',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '专业技术人员中含高级职称（人）' }],
    },
  ];
  const _data4 = [
    {
      label: '是否设立常委会',
      code: 'hasStandingCommittee',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否设立常委会' }],
    },
    {
      label: '办校类别',
      code: 'd109Code',
      type: 'dict',
      codeType: 'dict_d109',
      // backType:'object',
      rules: [{ required: true, message: '办校类别' }],
    },
    {
      label: '是否向地方党委和主管部委专题报告党委领导下的校长负责制执行',
      code: 'hasReportImplementation',
      type: 'boolean',
      codeType: '',
      rules: [
        {
          required: true,
          message: '是否向地方党委和主管部委专题报告党委领导下的校长负责制执行',
        },
      ],
    },
    {
      label: '是否修订党委全委会、常委会和校长办公会议事规则',
      code: 'hasOfficeProcedure',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否修订党委全委会、常委会和校长办公会议事规则' }],
    },
    {
      label: '学校党委书记是否向地方党委述职',
      code: 'schoolHasReportsLocal',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '学校党委书记是否向地方党委述职' }],
    },
    {
      label: '是否组织开展二级院（系）党组织书记向学校党委述职',
      code: 'hasSecretaryUniversityCommittee',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否组织开展二级院（系）党组织书记向学校党委述职' }],
    },
    {
      label: '校长是否中共党员',
      code: 'hasPresidentPartyMember',
      type: 'boolean2',
      codeType: '',
      rules: [{ required: true, message: '校长是否中共党员' }],
    },
    {
      label: '校长是否担任党委副书记',
      code: 'hasDeputyPartySecretary',
      type: 'boolean2',
      codeType: '',
      rules: [{ required: true, message: '校长是否担任党委副书记' }],
    },
    {
      label: '纪委书记是否担任学校党委委员',
      code: 'hasSecretaryCommittee',
      type: 'boolean2',
      codeType: '',
      rules: [{ required: true, message: '纪委书记是否担任学校党委委员' }],
    },
    {
      label: '组织部长是否担任学校党委委员',
      code: 'hasTissueCommittee',
      type: 'boolean2',
      codeType: '',
      rules: [{ required: true, message: '组织部长是否担任学校党委委员' }],
    },
    {
      label: '宣传部长是否担任学校党委委员',
      code: 'hasPropagandaCommittee',
      type: 'boolean2',
      codeType: '',
      rules: [{ required: true, message: '宣传部长是否担任学校党委委员' }],
    },
    {
      label: '统战部长是否担任学校党委委员',
      code: 'hasFrontCommittee',
      type: 'boolean2',
      codeType: '',
      rules: [{ required: true, message: '统战部长是否担任学校党委委员' }],
    },
    {
      label: '本年度高校党支部书记参加培训人次',
      code: 'yearBranchTraining',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '本年度高校党支部书记参加培训人次' }],
    },
    {
      label: '本年度院系本级党组织书记参加培训人次',
      code: 'yearTraining',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '本年度院系本级党组织书记参加培训人次' }],
    },
    {
      label: '本年度毕业生党员',
      code: 'graduatePartyMember',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '本年度毕业生党员' }],
    },
    {
      label: '尚未转出组织关系的',
      code: 'orgRelationshipNotTransferred',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '尚未转出组织关系的' }],
    },
    ...school,
    ...school2,
  ];
  const _data5 = [
    {
      label: '实行党组织领导的校长负责制的',
      code: 'hasResponsibilitySystem',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '实行党组织领导的校长负责制的' }],
    },
    {
      label: '领导体制已写入学校章程的',
      code: 'ldtzyyzc',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '领导体制已写入学校章程的' }],
    },
    {
      label: '修订学校党组织会议、校长办公会议（校务会议）的会议制度和议事规则的',
      code: 'ysgzIs',
      type: 'boolean',
      codeType: '',
      rules: [
        {
          required: true,
          message: '修订学校党组织会议、校长办公会议（校务会议）的会议制度和议事规则的',
        },
      ],
    },
    {
      label: '党组织书记、校长分设的',
      code: 'isLeaderSeparate',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '党组织书记、校长分设的' }],
    },
    {
      label: '党组织书记不兼任行政领导职务的',
      code: 'hasClerkPosition',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '党组织书记不兼任行政领导职务的' }],
    },
    {
      label: '校长系中共党员的',
      code: 'hasDeanPartyMember',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '校长系中共党员的' }],
    },
    {
      label: '校长担任党组织副书记的',
      code: 'hasDeanPartySecretary',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '校长担任党组织副书记的' }],
    },
    {
      label: '已建立学校党组织书记和校长定期沟通制度的',
      code: 'yjldqgtIs',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '已建立学校党组织书记和校长定期沟通制度的' }],
    },
    {
      label: '已设立党务工作机构的',
      code: 'ysldwgzjgIs',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '已设立党务工作机构的' }],
    },
    {
      label: '已配备专职党务工作人员的',
      code: 'ypbzzdwgzryIs',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '已配备专职党务工作人员的' }],
    },
  ];
  const _data6 = [
    {
      label: '在岗职工数（人）',
      code: 'onPostNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗职工数（人）' }],
    },
    {
      label: '在岗专业技术人员数（人）',
      code: 'tecNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
    },
    {
      label: '专业技术人员中含高级职称（人）',
      code: 'zaigangGaoji',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '专业技术人员中含高级职称（人）' }],
    },
    {
      label: '公益类别',
      code: 'd81Code',
      type: 'dict',
      codeType: 'dict_d81',
      // backType:'object',
      rules: [{ required: true, message: '公益类别' }],
    },
  ];
  const _data7 = [
    {
      label: '医院等级',
      code: 'd95Code',
      type: 'dict',
      codeType: 'dict_d95',
      // backType:'object',
      rules: [{ required: true, message: '医院等级' }],
    },
    // {
    //   label: '办院级别',
    //   code: 'd110Code',
    //   type: 'dict',
    //   codeType: 'dict_d110',
    //   // backType:'object',
    //   rules: [{ required: true, message: '办院级别' }],
    // },
    {
      label: '办院类型',
      code: 'd111Code',
      type: 'dict',
      codeType: 'dict_d111',
      // backType:'object',
      rules: [{ required: true, message: '办院类型' }],
    },
    {
      label: '是否已实行党委领导下的院长负责制',
      code: 'hasResponsibilitySystem',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否已实行党委领导下的院长负责制' }],
    },
    {
      label: '领导体制是否已写入医院章程',
      code: 'ldtzyyzc',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '领导体制是否已写入医院章程' }],
    },
    {
      label: '党建工作要求是否写入医院章程',
      code: 'isPartyWorkWrite',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '党建工作要求是否写入医院章程' }],
    },
    {
      label: '是否开展基层党建述职评议考核',
      code: 'isOpenOrgAssess',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否开展基层党建述职评议考核' }],
    },
    {
      label: '是否配备院长',
      code: 'isAllocateDean',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否配备院长' }],
    },
    {
      label: '是否配备书记',
      code: 'isAllocateSecretary',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否配备书记' }],
    },
    {
      label: '是否党组织书记、院长分设',
      code: 'isLeaderSeparate',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否党组织书记、院长分设' }],
    },
    {
      label: '党组织书记是否兼任行政职务',
      code: 'hasClerkPosition',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '党组织书记是否兼任行政职务' }],
    },
    {
      label: '院长是否中共党员',
      code: 'hasDeanPartyMember',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '院长是否中共党员' }],
    },
    {
      label: '院长是否担任党委副书记',
      code: 'hasDeanPartySecretary',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '院长是否担任党委副书记' }],
    },
  ];
  const _data8 = [
    {
      label: '在岗职工数（人）',
      code: 'onPostNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗职工数（人）' }],
    },
    {
      label: '在岗专业技术人员数（人）',
      code: 'tecNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
    },
    {
      label: '专业技术人员中含高级职称（人）',
      code: 'zaigangGaoji',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '专业技术人员中含高级职称（人）' }],
    },
  ];
  const _data9 = [
    {
      label: '在岗职工数（人）',
      code: 'onPostNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗职工数（人）' }],
    },
    {
      label: '在岗专业技术人员数（人）',
      code: 'tecNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入在岗专业技术人员数（人）' }],
    },
    {
      label: '专业技术人员中含高级职称（人）',
      code: 'zaigangGaoji',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '专业技术人员中含高级职称（人）' }],
    },
    {
      label: '行业分类',
      code: 'd114Code',
      type: 'dict',
      codeType: 'dict_d114',
      rules: [{ required: true, message: '行业分类' }],
    },
    // 经济类型为所有类型都有的
    {
      label: '经济类型',
      code: 'd16Code',
      type: 'dict',
      codeType: 'dict_d16',
      rules: [{ required: true, message: '经济类型' }],
      filter: (res) => {
        // let val = getDictValue('d04Code');
        // let arr = res.filter((it) => it.key === '1' || it.key === '2');
        return res;
      },
    },
  ];
  const _data10 = [
    {
      label: '本科以上学历人数',
      code: 'aboveBkEducation',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '本科以上学历人数' }],
    },
    {
      label: '研究生以上学历人数',
      code: 'aboveYjsEducation',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '研究生以上学历人数' }],
    },
    {
      label: '企业本年度发展的党员数',
      code: 'yearDevelopMem',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '企业本年度发展的党员数' }],
    },
    // {
    //   label: '是否由企业中高层管理人员担任党组织书记',
    //   code: 'hasSecretaryHighLevel',
    //   type: 'boolean',
    //   codeType: '',
    //   rules: [{ required: true, message: '是否由企业中高层管理人员担任党组织书记' }],
    // },
    {
      label: '主要负责人是否党员',
      code: 'hasHeadParty',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '主要负责人是否党员' }],
    },
    {
      label: '主要负责人是否担任党组织书记',
      code: 'hasOrganizationSecretary',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '主要负责人是否担任党组织书记' }],
    },
  ];
  const _data11 = [
    {
      label: '是否配备专职党务工作人员',
      code: 'hasPartyWork',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否配备专职党务工作人员' }],
    },
    {
      label: '是否配备专职副书记',
      code: 'hasMajorDeputySecretary',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否配备专职副书记' }],
    },
    {
      label: '企业规模',
      code: 'd17Code',
      type: 'dict',
      codeType: 'dict_d17',
      rules: [{ required: true, message: '企业规模' }],
    },
    {
      label: '是否企业本级',
      code: 'hasFirmLevel',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否企业本级' }],
    },
    {
      label: '本级企业是否省内企业',
      code: 'hasIndustryProvince',
      type: 'boolean',
      codeType: '',
      onChange: (e) => {
        form.setFieldsValue({
          firmLevelName: undefined,
        });
      },
      rules: [{ required: true, message: '本级企业是否省内企业' }],
    },
    // {
    //   label: '本级企业名称',
    //   code: 'firmLevelName',
    //   type: 'text',
    //   codeType: '',
    //   rules: [{ required: true, message: '本级企业名称' }],
    // },
    {
      label: '企业级别',
      code: 'd115Code',
      type: 'dict',
      codeType: 'dict_d115',
      rules: [{ required: true, message: '企业级别' }],
    },
    {
      label: '是否建立董事会',
      code: 'hasDirectors',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立董事会' }],
    },
    {
      label: '董事长是否担任党组织书记',
      code: 'hasChairmanSecretary',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '董事长是否担任党组织书记' }],
    },
    {
      label: '董事长是否由上级企业有关负责人兼任',
      code: 'hasResponsiblePerson',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '董事长是否由上级企业有关负责人兼任' }],
    },
    {
      label: '党建工作经费是否按上年度工资总额一定比例纳入企业管理费用',
      code: 'hasProportionateFunding',
      type: 'boolean',
      codeType: '',
      rules: [
        {
          required: true,
          message: '党建工作经费是否按上年度工资总额一定比例纳入企业管理费用',
        },
      ],
    },
    {
      label: '人事管理和基层党建是否由一个部门抓',
      code: 'hasBranchToCatch',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '人事管理和基层党建是否由一个部门抓' }],
    },
    {
      label: '人事管理和基层党建是否由一个领导管',
      code: 'hasByLeader',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '人事管理和基层党建是否由一个领导管' }],
    },
    {
      label: '党务工作人员和经营管理人员是否同职级同待遇',
      code: 'hasSameTreatment',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '党务工作人员和经营管理人员是否同职级同待遇' }],
    },
    {
      label: '是否上市公司',
      code: 'hasPublicCompany',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否上市公司' }],
    },
    {
      label: '党建工作是否写入公司章程',
      code: 'hasArticlesIncorporation',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '党建工作是否写入公司章程' }],
    },
    {
      label: '是否党组织研究讨论作为董事会、经理层决策重大问题前置程序',
      code: 'hasPrepositionalProcedure',
      type: 'boolean',
      codeType: '',
      rules: [
        {
          required: true,
          message: '是否党组织研究讨论作为董事会、经理层决策重大问题前置程序',
        },
      ],
    },
    {
      label: '境外分支机构数',
      code: 'branches',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '境外分支机构数' }],
    },
    {
      label: '境外分支机构已建立党组织',
      code: 'haveBeenEstablished',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '境外分支机构已建立党组织' }],
    },
    {
      label: '境外分支机构基层党组织数量',
      code: 'partyOrganizationNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '境外分支机构基层党组织数量' }],
    },
    {
      label: '境外分支机构党员数',
      code: 'partyMembers',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '境外分支机构党员数' }],
    },
    {
      label: '境外分支机构有党员的机构',
      code: 'existMemberBranches',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '境外分支机构有党员的机构' }],
    },
    {
      label: '境外分支机构有党员3人以上、未建立党组织的',
      code: 'threeMemberNoOrgBranches',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '境外分支机构有党员3人以上、未建立党组织的' }],
    },
  ];
  const _data12 = [
    {
      label: '本级企业名称',
      code: 'firmLevelName',
      initName: formData['unitInformation'] || unitName,
      type: 'search',
      codeType: '',
      rules: [{ required: true, message: '本级企业名称' }],
    },
  ];
  const _data121 = [
    {
      label: '本级企业名称',
      code: 'firmLevelName',
      type: 'text',
      codeType: '',
      rules: [{ required: true, message: '本级企业名称' }],
    },
  ];
  const _data13 = [
    {
      label: '是否依托组织部门成立的非公党工委',
      code: 'hasNonPublicParty',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否依托组织部门成立的非公党工委' }],
    },
    {
      label: '是否设立专门办事机构的非公党工委',
      code: 'hasSpecialAgencies',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否设立专门办事机构的非公党工委' }],
    },
    {
      label: '办事机构工作人员编制数',
      code: 'staffOfficeNumbers',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '办事机构工作人员编制数' }],
    },
    {
      label: '组织部门（非公党工委）直接联系的非公企业党组织数',
      code: 'nonPublicEnterprises',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '组织部门（非公党工委）直接联系的非公企业党组织数' }],
    },
    {
      label: '法定代表人是否党员',
      code: 'hasRepresentative',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '法定代表人是否党员' }],
    },
    {
      label: '是否有党建工作指导员联系',
      code: 'hasInstructorContact',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否有党建工作指导员联系' }],
    },
    {
      label: '党建工作指导员人数',
      code: 'communityBuildingNumber',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '党建工作指导员人数' }],
    },
    // 是否建立工会或共青团组织改为是否建立工会、是否建立共青团组织、是否建立妇联组织3个字段
    // {
    //   label: '是否建立工会或共青团组织',
    //   code: 'hasUnionOrganization',
    //   type: 'boolean',
    //   codeType: '',
    //   rules: [{ required: true, message: '是否建立工会或共青团组织' }],
    // },
    {
      label: '是否建立工会',
      code: 'hasLabourUnion',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立工会' }],
    },
    {
      label: '是否建立共青团组织',
      code: 'hasYouthLeague',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立共青团组织' }],
    },
    {
      label: '是否建立妇联组织',
      code: 'hasWomensFederation',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立妇联组织' }],
    },
    {
      label: '吸收未转入组织关系的党员建立党组织数',
      code: 'absorbedTissueNumber',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '吸收未转入组织关系的党员建立党组织数' }],
    },
    {
      label: '未转组织关系党员数',
      code: 'notTurnedParty',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '未转组织关系党员数' }],
    },
  ];
  const _data14 = [
    {
      label: '法定代表人是否兼任党组织书记',
      code: 'hasProperSecretary',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '法定代表人是否兼任党组织书记' }],
    },
  ];
  const _data15 = [
    {
      label: '是否兼任企业党组书记',
      code: 'hasProperSecretary',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否兼任企业党组书记' }],
    },
  ];
  const _data16 = [
    {
      label: '从业人员数',
      code: 'employeesNumber',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '从业人员数' }],
    },
    {
      label: '是否党建工作指导员联系',
      code: 'hasInstructorContact',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否党建工作指导员联系' }],
    },
    {
      label: '党建工作指导员数',
      code: 'communityBuildingNumber',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '党建工作指导员数' }],
    },
    {
      label: '是否将党建工作经费纳入管理费列支、税前扣除',
      code: 'hasWorkingExpenses',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否将党建工作经费纳入管理费列支、税前扣除' }],
    },
    {
      label: '与业务主管单位关系',
      code: 'd159Code',
      type: 'dict',
      backType: 'object',
      codeType: 'dict_d159',
      rules: [{ required: true, message: '与业务主管单位关系' }],
    },
    // {
    //   label: '是否建立工会或共青团组织',
    //   code: 'hasUnionOrganization',
    //   type: 'boolean',
    //   codeType: '',
    //   rules: [{ required: true, message: '是否建立工会或共青团组织' }],
    // },
    {
      label: '是否建立工会',
      code: 'hasLabourUnion',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立工会' }],
    },
    {
      label: '是否建立共青团组织',
      code: 'hasYouthLeague',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立共青团组织' }],
    },
    {
      label: '是否建立妇联组织',
      code: 'hasWomensFederation',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立妇联组织' }],
    },
    {
      label: '吸收未转入组织关系的党员建立党组织数',
      code: 'absorbedTissueNumber',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '吸收未转入组织关系的党员建立党组织数' }],
    },
    {
      label: '吸收未转入组织关系党员数',
      code: 'notTurnedParty',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '吸收未转入组织关系党员数' }],
    },
    {
      label: '主要负责人是否党员',
      code: 'hasHeadParty',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '主要负责人是否党员' }],
    },
    {
      label: '主要负责人是否担任党组织书记',
      code: 'hasOrganizationSecretary',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '主要负责人是否担任党组织书记' }],
    },
  ];
  const _data17 = [
    {
      label: '登记级别',
      code: 'd118Code',
      type: 'dict',
      backType: 'object',
      codeType: 'dict_d118',
      rules: [{ required: true, message: '登记级别' }],
    },
  ];
  const _data18 = [
    {
      label: '是否行业协会商会',
      code: 'isDecouplIndustry',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否行业协会商会' }],
    },
  ];
  const _data19 = [
    {
      label: '在岗职工数',
      code: 'onPostNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '在岗职工数' }],
    },
    {
      label: '专业技术人员数',
      code: 'technicalPersonnel',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '专业技术人员数' }],
    },
    {
      label: '党员中高级职称人员数',
      code: 'partySeniorTitle',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '党员中高级职称人员数' }],
    },
  ];
  const _data20 = [
    // {
    //   label: '是否建档立卡贫困村',
    //   code: 'hasPoorVillage',
    //   type: 'boolean',
    //   codeType: '',
    //   rules: [{ required: true, message: '是否建档立卡贫困村' }],
    // },
    {
      label: '是否实行“四议两公开”工作法',
      code: 'hasFourTwoOpenWork',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否实行“四议两公开”工作法' }],
    },
    {
      label: '是否成立村务监督委员会或其他村务监督机构',
      code: 'hasCommunitySupervisory',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否成立村务监督委员会或其他村务监督机构' }],
    },
    {
      label: '户籍人口',
      code: 'registeredPopulation',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '户籍人口' }],
    },
    {
      label: '户数',
      code: 'householdRegistration',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '户数' }],
    },
  ];
  const _data21 = [
    {
      label: '是否落实社区事务准入制度',
      code: 'hasCommunityAccess',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否落实社区事务准入制度' }],
    },
    {
      label: '建立党群服务中心',
      code: 'isOrgService',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '建立党群服务中心' }],
    },
    {
      label: '实行与驻区单位党建联建共建',
      code: 'hasJointUnits',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '实行与驻区单位党建联建共建' }],
    },
    {
      label: '是否按不低于上年度当地社会平均工资水平确定报酬',
      code: 'hasLowerSocial',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否按不低于上年度当地社会平均工资水平确定报酬' }],
    },
    {
      label: '全部社区工作者年工资总额（万元）',
      code: 'communityWorkersSalary',
      type: 'number',
      addonAfter: '万元',
      codeType: '',
      rules: [{ required: true, message: '全部社区工作者年工资总额（万元）' }],
    },
    // {
    //   label: '社区党组织书记年工资总额（万元）',
    //   code: 'communitySecretarySalary',
    //   type: 'number',
    //   addonAfter: '万元',
    //   codeType: '',
    //   rules: [{ required: true, message: '社区党组织书记年工资总额（万元）' }],
    // },
    {
      label: '党建工作指导员数',
      code: 'communityBuildingNumber',
      type: 'number',
      // addonAfter:'万元',
      codeType: '',
      rules: [{ required: true, message: '党建工作指导员数' }],
    },
    {
      label: '是否建立社区工作者岗位等级序列',
      code: 'hasCommunityPositions',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立社区工作者岗位等级序列' }],
    },
    // {
    //   label: '办公用房面积',
    //   code: 'communityOfficeSpace',
    //   type: 'number',
    //   addonAfter: '平方米',
    //   codeType: '',
    //   rules: [{ required: true, message: '办公用房面积' }],
    // },
    {
      label: '是否实行兼职委员制',
      code: 'hasParttimeSystem',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否实行兼职委员制' }],
    },
    {
      label: '社区纳入财政预算的工作经费总额（万元）',
      code: 'includedFinancial',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '社区纳入财政预算的工作经费总额（万元）' }],
    },
    {
      label: '社区全年服务群众专项经费总额（万元）',
      code: 'specialFundsMasses',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '社区全年服务群众专项经费总额（万元）' }],
    },
    {
      label: '是否开展在职党员到社区报到为群众服务',
      code: 'hasCommunityReport',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否开展在职党员到社区报到为群众服务' }],
    },
    {
      label: '到社区报到的在职党员',
      code: 'reportCommunityMember',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '到社区报到的在职党员' }],
    },
    {
      label: '是否已统一整合设置网格',
      code: 'hasSetGrid',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否已统一整合设置网格' }],
    },
    {
      label: '是否有专职网格员纳入社区工作者管理',
      code: 'hasIncludedGridWorker',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否有专职网格员纳入社区工作者管理' }],
    },
  ];
  const _data22 = [
    {
      label: '参加县级及以上集中培训人数',
      code: 'joinAboveCountyTrainNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '参加县级及以上集中培训人数' }],
    },
    {
      label: '村干部参加城镇职工养老保险人数',
      code: 'villageJoinUrbanWorkerNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '村干部参加城镇职工养老保险人数' }],
    },

    // {
    //   label: '应到村任职选调生人数',
    //   code: 'numberOfStudentsToBeTransferredToTheVillage',
    //   type: 'number',
    //   codeType: '',
    //   rules: [{ required: true, message: '应到村任职选调生人数' }],
    // },
    // {
    //   label: '是否有大学毕业生在村工作',
    //   code: 'whetherThereAreCollegeGraduatesWorkingInTheVillage',
    //   type: 'boolean',
    //   codeType: '',
    //   rules: [{ required: true, message: '是否有大学毕业生在村工作' }],
    // },
  ];
  const _data23 = [
    ...zhiyuanzhe,
    {
      label: '是否赋予区域综合管理权、对上级部门派驻机构负责人的人事考核权等权力',
      code: 'hasExaminationPower',
      type: 'boolean',
      codeType: '',
      rules: [
        {
          required: true,
          message: '是否赋予区域综合管理权、对上级部门派驻机构负责人的人事考核权等权力',
        },
      ],
    },
    {
      label: '是否取消招商引资等职能',
      code: 'hasCancelInvestmentPromotion',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否取消招商引资等职能' }],
    },
    {
      label: '是否整合职能统筹设置党政内设工作机构',
      code: 'hasWorkMechanism',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否整合职能统筹设置党政内设工作机构' }],
    },
    {
      label: '组织委员是否纳入上一级党委管理',
      code: 'hasIncludedCommittee',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '组织委员是否纳入上一级党委管理' }],
    },
    {
      label: '是否建立党群服务中心',
      code: 'hasGroupServiceCenter',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立党群服务中心' }],
    },
    {
      label: '是否实行与驻区单位党建联建共建',
      code: 'hasPartyBuildEndeavor',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否实行与驻区单位党建联建共建' }],
    },
    {
      label: '是否实行兼职委员制',
      code: 'b610',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否实行兼职委员制' }],
    },
  ];
  const _data24 = [
    {
      label: '街道干部人数',
      code: 'streetCadres',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '街道干部人数' }],
    },
    {
      label: '街道干部35岁及以下人数',
      code: 'age35Below',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '街道干部35岁及以下人数' }],
    },
    {
      label: '街道干部36至55岁人数',
      code: 'age36ToAge55',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '街道干部36至55岁人数' }],
    },
    {
      label: '街道干部56岁及以上人数',
      code: 'age56Above',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '街道干部56岁及以上人数' }],
    },
    {
      label: '街道干部大专及以上学历人数',
      code: 'collegeDegreeAbove',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '街道干部大专及以上学历人数' }],
    },
    {
      label: '街道干部高中中专及以下人数',
      code: 'secondarySchoolBelow',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '街道干部高中中专及以下人数' }],
    },
    {
      label: '街道干部公务员人数',
      code: 'streetCadresCivil',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '街道干部公务员人数' }],
    },
    {
      label: '街道干部事业单位人数',
      code: 'streetCadresInstitutions',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '街道干部事业单位人数' }],
    },
    {
      label: '街道干部其他身份人数',
      code: 'cadreOther',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '街道干部其他身份人数' }],
    },
  ];
  const _data25 = [
    // {
    //   label: '是否配备第一书记',
    //   code: 'hasFirstSecretary',
    //   type: 'boolean',
    //   codeType: '',
    //   rules: [{ required: true, message: '是否配备第一书记' }],
    // },
    // {
    //   label: '今年新选派第一书记（人）',
    //   code: 'firstSecretarySelect',
    //   type: 'number',
    //   codeType: '',
    //   rules: [{ required: true, message: '今年新选派第一书记（人）' }],
    // },
    // {
    //   label: '现任第一书记',
    //   code: 'firstSecretaryCode',
    //   type: 'memselect',
    //   codeType: '',
    //   initName: formData['firstSecretaryName'],
    //   rules: [{ required: true, message: '现任第一书记' }],
    // },
    {
      label: '本年各级培训第一书记（人次）',
      code: 'secretaryTrainingNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '本年各级培训第一书记（人次）' }],
    },
    {
      label: '是否为第一书记安排不低于1万元工作经费',
      code: 'hasThousand',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否为第一书记安排不低于1万元工作经费' }],
    },
    {
      label: '是否派出单位落实责任、项目、资金捆绑',
      code: 'hasBundled',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否派出单位落实责任、项目、资金捆绑' }],
    },
    {
      label: '提拔使用或晋级的第一书记数',
      code: 'promotedNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '提拔使用或晋级的第一书记数' }],
    },
    {
      label: '因工作不胜任召回调整的第一书记数',
      code: 'adjustedNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '因工作不胜任召回调整的第一书记数' }],
    },
    {
      label: '运转经费（万元 ∕ 年）',
      code: 'operatingExpenses',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '运转经费（万元 ∕ 年）' }],
    },
    {
      label: '单位办公经费（万元 ∕ 年）',
      code: 'villagePer',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '单位办公经费（万元 ∕ 年）' }],
    },
    {
      label: '党组织书记报酬（万元 ∕ 年）',
      code: 'secretarySalary',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '党组织书记报酬（万元 ∕ 年）' }],
    },
    {
      label: '活动场所面积（㎡）',
      code: 'spaceArea',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '活动场所面积（㎡）' }],
    },
    {
      label: '本年新建或改扩建活动场所数量',
      code: 'newExpandArea',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '本年新建或改扩建活动场所数量' }],
    },
    {
      label: '村党组织书记中录用公务员数',
      code: 'secretaryPartyNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '村党组织书记中录用公务员数' }],
    },
    {
      label: '村党组织书记中录用事业编制工作人员数',
      code: 'secretaryEmploySybzNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '村党组织书记中录用事业编制工作人员数' }],
    },
    {
      label: '从党组织书记中选拔乡镇领导干部人员数',
      code: 'secretaryPromotedNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '从党组织书记中选拔乡镇领导干部人员数' }],
    },
  ];
  const _data26 = [
    {
      label: '纳入财政预算的工作经费总额（万元）',
      code: 'communityMoneyNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '纳入财政预算的工作经费总额（万元）' }],
    },
    {
      label: '全年服务群众专项经费总额（万元）',
      code: 'communityServingPeople',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '全年服务群众专项经费总额（万元）' }],
    },
    // {
    //   label: '是否开展在职党员到报到为群众服务',
    //   code: 'communityMasses',
    //   type: 'boolean',
    //   codeType: '',
    //   rules: [{ required: true, message: '是否开展在职党员到报到为群众服务' }],
    // },
  ];
  const _data27 = [
    {
      label: '不是党委委员的政府领导班子成员人数',
      code: 'numberOfNonGovernmentalMembers',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '不是党委委员的政府领导班子成员人数' }],
    },
  ];
  const _data28 = [
    {
      label: '农村专业技术协会数量',
      code: 'ruralProfessionalTechnicalAssociationNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '农村专业技术协会数量' }],
    },
    {
      label: '农民专业合作社数量',
      code: 'farmerSpecializedCooperativesNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '农民专业合作社数量' }],
    },
    {
      label: '家庭农场数量',
      code: 'familyFarmNum',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '家庭农场数量' }],
    },
  ];
  const _data29 = [
    {
      label: '是否建立履职事项清单',
      code: 'hasPerformedDetail',
      type: 'boolean',
      codeType: '',
      rules: [{ required: true, message: '是否建立履职事项清单' }],
    },
  ];
  const _data30 = [
    {
      label: '境外分支机构共有员工数',
      code: 'branchEmployee',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入境外分支机构共有员工数' }],
    },
    {
      label: '境外分支机构其中国内派出员工',
      code: 'branchEmployeeHome',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入境外分支机构其中国内派出员工' }],
    },
    {
      label: '境外分支机构已建立的党委数',
      code: 'branchCommittee',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入境外分支机构已建立的党委数' }],
    },
    {
      label: '境外分支机构已建立的总支部数',
      code: 'branchGeneral',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入境外分支机构已建立的总支部数' }],
    },
    {
      label: '境外分支机构已建立的支部数',
      code: 'branchNode',
      type: 'number',
      codeType: '',
      rules: [{ required: true, message: '请输入境外分支机构已建立的支部数' }],
    },
  ];
  return {
    school,
    school2,
    zhiyuanzhe,
    _data1,
    _data2,
    _data3,
    _data4,
    _data5,
    _data6,
    _data7,
    _data8,
    _data9,
    _data10,
    _data11,
    _data12,
    _data121,
    _data13,
    _data14,
    _data15,
    _data16,
    _data17,
    _data18,
    _data19,
    _data20,
    _data21,
    _data22,
    _data23,
    _data24,
    _data25,
    _data26,
    _data27,
    _data28,
    _data29,
    _data30,
  };
};

export const renderNewExtra = (props, form, formData, renderItem, getDictValue, unitName) => {
  const { tipMsg } = props;
  const { basicInfo = {} } = props.unit;
  const { getFieldValue } = form;
  let val = basicInfo.d04Code || formData.d04Code; // 单位类别
  console.log(val, 'valvalval');
  let res: any = [];

  const {
    school,
    school2,
    zhiyuanzhe,
    _data1,
    _data2,
    _data3,
    _data4,
    _data5,
    _data6,
    _data7,
    _data8,
    _data9,
    _data10,
    _data11,
    _data12,
    _data121,
    _data13,
    _data14,
    _data15,
    _data16,
    _data17,
    _data18,
    _data19,
    _data20,
    _data21,
    _data22,
    _data23,
    _data24,
    _data25,
    _data26,
    _data27,
    _data28,
    _data29,
    _data30,
  } = getZiDuanArr(form, getDictValue, formData, unitName);

  if (`${val}`.startsWith('1') || `${val}`.startsWith('2') || `${val}`.startsWith('91')) {
    let exData = [..._data1];
    exData.forEach((item, index) => {
      let node = renderItem(item);
      res.push(node);
    });
  }

  if (`${val}`.startsWith('3')) {
    if (`${val}`.startsWith('32')) {
      let exData = [..._data2];

      // 院（所）长系中共党员的 与 院（所）长担任党组织副书记的 联动
      // if (
      //   !_isNumber(getFieldValue('hasDeanPartyMember')) ||
      //   getFieldValue('hasDeanPartyMember') == 0
      // ) {
      //   exData = exData.filter((it) => it.code != 'hasDeanPartySecretary');
      // }
      //梁才改动---↓
      // 院所长负责制情况 与 领导体制已写入院（所）章程的 联动/已修订党组织会议、院（所）长办公会议议事规则的/党组织书记、院（所）长分设的
      if (getFieldValue('d112Code') != '2') {
        let arr = [
          'ldtzyyzc',
          'ysgzIs',
          'isLeaderSeparate',
          'hasClerkPosition',
          'hasDeanPartyMember',
          'hasDeanPartySecretary',
        ];
        exData = exData.filter((it) => !arr.includes(it.code));
      }

      // 党组织书记、院（所）长分设的 是 显示 党组织书记不兼任行政领导职务的/院（所）长系中共党员的
      if (getFieldValue('isLeaderSeparate') != 1) {
        let arr = ['hasClerkPosition', 'hasDeanPartyMember', 'hasDeanPartySecretary'];
        exData = exData.filter((it) => !arr.includes(it.code));
      }
      // 院（所）长系中共党员的 是 显示 院（所）长担任党组织副书记的
      if (getFieldValue('hasDeanPartyMember') != 1) {
        let arr = ['hasDeanPartySecretary'];
        exData = exData.filter((it) => !arr.includes(it.code));
      }

      //梁才改动---↑
      // if(!_isNumber(getFieldValue('hasDeanResponsibilitySystem')) || getFieldValue('hasDeanResponsibilitySystem') == 0){
      //   exData = exData.filter(it=>it.code != 'd112Code');
      // }
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    }

    if (`${val}`.startsWith('33')) {
      let exData = [..._data3];

      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
      if (`${val}`.startsWith('331')) {
        let exData = [..._data4];
        if (
          !_isNumber(getFieldValue('hasPresidentPartyMember')) ||
          getFieldValue('hasPresidentPartyMember') != 1
        ) {
          exData = exData.filter((it) => it.code != 'hasDeputyPartySecretary');
        }
        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
      }
      if (`${val}`.startsWith('332')) {
        let exData = [...school];
        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
      }

      // 当特定出现子类时，增加以下内容
      if (['3333', '3331', '3332', '3321', '3322', '3323', '3324'].find((it) => it == `${val}`)) {
        let exData = [..._data5];
        // 实行党组织领导的校长负责制的 与 领导体制已写入学校章程的/修订学校党组织会议、校长办公会议（校务会议）的会议制度和议事规则的/党组织书记、校长分设的/已建立学校党组织书记和校长定期沟通制度的 联动
        if (
          !_isNumber(getFieldValue('hasResponsibilitySystem')) ||
          getFieldValue('hasResponsibilitySystem') == 0
        ) {
          let arr = [
            'ldtzyyzc',
            'ysgzIs',
            'isLeaderSeparate',
            'yjldqgtIs',
            'hasClerkPosition',
            'hasDeanPartyMember',
            'hasDeanPartySecretary',
          ];
          exData = exData.filter((it) => !arr.includes(it.code));
          // exData = exData.filter((it) => it.code != 'ldtzyyzc');
        }
        if (getFieldValue('isLeaderSeparate') != 1) {
          let arr = ['hasClerkPosition', 'hasDeanPartyMember', 'hasDeanPartySecretary'];
          exData = exData.filter((it) => !arr.includes(it.code));
        }
        // 校长系中共党员的 与 校长担任党组织副书记的 联动
        if (
          !_isNumber(getFieldValue('hasDeanPartyMember')) ||
          getFieldValue('hasDeanPartyMember') == 0
        ) {
          exData = exData.filter((it) => it.code != 'hasDeanPartySecretary');
        }
        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
      }
    }

    if (`${val}`.startsWith('341') || `${val}`.startsWith('342') || `${val}`.startsWith('343')) {
      let exData = [..._data6];
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
      if (`${val}`.startsWith('341')) {
        let exData = [..._data7];

        // 是否配备院长, 是否配备书记 都是, 显示 是否党委书记.院长分设
        // if (
        //   !(getFieldValue('isAllocateDean') == 1 && getFieldValue('isAllocateSecretary') == 1)
        // ) {
        //   let arr = ['isLeaderSeparate'];
        //   exData = exData.filter((it) => !arr.includes(it.code));
        // }
        // 是否配备院长 选是，显示 院长是否中共党员
        // if (!(getFieldValue('isAllocateDean') == 1)) {
        //   let arr = ['hasDeanPartyMember'];
        //   exData = exData.filter((it) => !arr.includes(it.code));
        // }

        // // 是否配备书记 选是， 显示 党组织书记是否兼任行政职务
        // if (!(getFieldValue('isAllocateSecretary') == 1)) {
        //   let arr = ['hasClerkPosition'];
        //   exData = exData.filter((it) => !arr.includes(it.code));
        // }
        // 是否实行党委领导下的院长负责制  选是  显示  领导体制已写入医院章程和党委书记、院长分设
        if (!(getFieldValue('hasResponsibilitySystem') == 1)) {
          let arr = [
            'ldtzyyzc',
            'isLeaderSeparate',
            'hasClerkPosition',
            'hasDeanPartyMember',
            'hasDeanPartySecretary',
          ];
          exData = exData.filter((it) => !arr.includes(it.code));
        }
        // 是否党组织书记、院长分设分设  选是  显示  党组织书记是否兼任行政职务
        if (!(getFieldValue('isLeaderSeparate') == 1)) {
          let arr = ['hasClerkPosition', 'hasDeanPartyMember', 'hasDeanPartySecretary'];
          exData = exData.filter((it) => !arr.includes(it.code));
        }
        // 院长是否中共党员 选是，显示 院长是否担任党委副书记
        if (!(getFieldValue('hasDeanPartyMember') == 1)) {
          let arr = ['hasDeanPartySecretary'];
          exData = exData.filter((it) => !arr.includes(it.code));
        }
        // // 是否党委书记.院长分设，后面的院长是否中共党员，院长是否担任党委副书记不显示
        // if (
        //   !_isNumber(getFieldValue('isLeaderSeparate')) ||
        //   getFieldValue('isLeaderSeparate') == 0
        // ) {
        //   let arr = ['hasDeanPartyMember', 'hasDeanPartySecretary'];
        //   exData = exData.filter((it) => !arr.includes(it.code));
        // }
        // // 院长是否中共党员为是才显示
        // if (
        //   !_isNumber(getFieldValue('hasDeanPartyMember')) ||
        //   getFieldValue('hasDeanPartyMember') == 0
        // ) {
        //   let arr = ['hasDeanPartySecretary'];
        //   exData = exData.filter((it) => !arr.includes(it.code));
        // }

        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
      }
    }

    if (
      `${val}`.startsWith('31') ||
      `${val}`.startsWith('35') ||
      `${val}`.startsWith('36') ||
      `${val}`.startsWith('37') ||
      `${val}`.startsWith('38') ||
      `${val}`.startsWith('39')
    ) {
      let exData = [..._data8];

      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    }
  }

  if (`${val}`.startsWith('4')) {
    let exData = [..._data9];

    // 行业分类 为 互联网 显示以下字段
    let d114Code = getFieldValue('d114Code');
    if (typeof d114Code === 'object') {
      d114Code = d114Code?.key || '';
    }
    // 经济类型 3、4、5开头的 显示以下字段
    let d16Code = getFieldValue('d16Code') || '';
    if (typeof d16Code === 'object') {
      d16Code = d16Code?.key || '';
    }
    if (
      d114Code == 1 ||
      d16Code.startsWith('3') ||
      d16Code.startsWith('4') ||
      d16Code.startsWith('5')
    ) {
      exData = exData.concat(_data10);
    }
    // 当 主要负责人是否党员 选择是的时候才显示 主要负责人是否担任党组织书记
    if (!_isNumber(getFieldValue('hasHeadParty')) || getFieldValue('hasHeadParty') == 0) {
      exData = exData.filter((it) => it.code != 'hasOrganizationSecretary');
    }
    exData.forEach((item, index) => {
      let node = renderItem(item);
      res.push(node);
    });

    // 公有制，是否企业本级只有公有制才有
    if (
      `${val}`.startsWith('411') ||
      `${val}`.startsWith('412') ||
      `${val}`.startsWith('413') ||
      `${val}`.startsWith('414') ||
      `${val}`.startsWith('415') ||
      `${val}`.startsWith('416')
    ) {
      let exData: any = [..._data11];

      // 国有经济下才 境外分支机构有党员的机构/境外分支机构有党员3人以上、未建立党组织的 --梁才
      if (!d16Code.startsWith('1')) {
        let arr = ['existMemberBranches', 'threeMemberNoOrgBranches'];
        exData = exData.filter((it) => !arr.includes(it.code));
      }
      // 是否配备专职党务工作人员为是得时候，才展示改字段
      if (!_isNumber(getFieldValue('hasPartyWork')) || getFieldValue('hasPartyWork') == 0) {
        exData = exData.filter((it) => it.code != 'hasMajorDeputySecretary');
      }

      // 是否企业本级为否的时候，才展增加展现填写此字段
      if (!_isNumber(getFieldValue('hasFirmLevel')) || getFieldValue('hasFirmLevel') == 1) {
        // exData = exData.filter(it => it.code != 'firmLevelName');
        exData = exData.filter((it) => it.code != 'hasResponsiblePerson');
        exData = exData.filter((it) => it.code != 'hasIndustryProvince');
      }
      // 当本级企业是否省内企业选择是的时候,本级企业名称信息项走中间交换区获取选择填写
      if (getFieldValue('hasIndustryProvince') == 1) {
        exData.splice(
          exData.findIndex((it, index) => it.code == 'hasIndustryProvince') + 1,
          0,
          _data12[0],
        );
      }
      // 当本级企业是否省内企业选择否的时候,本级企业名称信息项是现在的填写方式。
      if (getFieldValue('hasIndustryProvince') == 0) {
        exData.splice(
          exData.findIndex((it, index) => it.code == 'hasIndustryProvince') + 1,
          0,
          _data121[0],
        );
      }
      // 是否企业本级 选是时：“本级企业是否省内企业”不显示， “本级企业名称”也不显示
      if (!_isNumber(getFieldValue('hasFirmLevel')) || getFieldValue('hasFirmLevel') == 1) {
        exData = exData.filter((it) => it.code != 'firmLevelName');
      }

      // 当分支机构数大于0的时候，展现该字段
      if (!_isNumber(getFieldValue('branches')) || !(getFieldValue('branches') > 0)) {
        exData = exData.filter((it) => it.code != 'partyMembers');
        exData = exData.filter((it) => it.code != 'partyOrganizationNum');
        exData = exData.filter((it) => it.code != 'haveBeenEstablished');
      }

      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    } else {
      let exData = [..._data13];
      // 单位类别是4开头的， 经济类型控制是（3，4，5）开头的时候，是否兼任企业党组书记修改为法定代表人是否兼任党组织书记
      if (d16Code.startsWith('3') || d16Code.startsWith('4') || d16Code.startsWith('5')) {
        // 单位信息中，单位类型是4，经济类型是3，4，5时，法定代表人是否党员选是时，才弹出法定代表人是否担任党组织书记。
        const hasRepresentative = getFieldValue('hasRepresentative');
        if (hasRepresentative === 1) {
          exData = exData.concat(_data14);
        }
      } else {
        exData = exData.concat(_data15);
      }
      const { basicInfo: { linkedDTOList = [] } = {} } = props.unit;
      let find = linkedDTOList.find((it) => (it?.orgType || '').startsWith('2'));
      if (!find) {
        let arr = [
          'hasNonPublicParty',
          'hasSpecialAgencies',
          'staffOfficeNumbers',
          'nonPublicEnterprises',
        ];
        exData = exData.filter((it) => !arr.includes(it.code));
      }
      // 当吸收未转入组织关系的党员建立党组织数大于0得时候，增加此字段
      if (
        !_isNumber(getFieldValue('absorbedTissueNumber')) ||
        !(getFieldValue('absorbedTissueNumber') > 0)
      ) {
        exData = exData.filter((it) => it.code != 'notTurnedParty');
      }
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    }

    // 单位类别（ d04_code）为“41”开头并且不是“4111”开头
    // && 企业经济类型（d16_code）为“1”开头的国有经济控制   d16Code
    // &&  属于独立法人单位（is_legal） isLegal
    // && 企业级别（d115_code）为‘1’省属 d115Code
    // && 属于企业本级（has_firm_level）hasFirmLevel
    if (`${val}`.startsWith('41') && !`${val}`.startsWith('4111')) {
      let d16Code = getFieldValue('d16Code');
      let isLegal = basicInfo.isLegal;
      let d115Code = getFieldValue('d115Code');
      let hasFirmLevel = getFieldValue('hasFirmLevel');
      if (d16Code?.startsWith('1') && isLegal == 1 && d115Code == 1 && hasFirmLevel == 1) {
        let exData = [..._data30];
        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
      }
    }
    // 不论是非公有还是公有制经济的企业，只要行业分类选择互联网企业的时候
    // 当单位类型是4开头，行业是互联网的时候，是否企业本级党组织书记这个字段（不显示）
    // if (getDictValue('d114Code') == '1') {
    //   let exData = [
    //     // {
    //     //   label: '是否企业本级',
    //     //   code: 'hasFirmLevel',
    //     //   type: 'boolean',
    //     //   codeType: '',
    //     //   rules: [{ required: true, message: '是否企业本级' }],
    //     // },
    //     // {
    //     //   label: '是否企业本级党组织书记',
    //     //   code: 'hasLevelSecretary',
    //     //   type: 'boolean',
    //     //   codeType: '',
    //     //   rules: [{ required: true, message: '是否企业本级党组织书记' }],
    //     // },
    //   ];
    //   // 当企业本级选是的时候添加是否企业本级党组织书记
    //   if (!_isNumber(getFieldValue('hasFirmLevel')) || getFieldValue('hasFirmLevel') == 0) {
    //     exData = exData.filter((it) => it.code != 'hasLevelSecretary');
    //   }
    //   // 当 主要负责人是否党员 选择是得时候增加主要负责人是否担任党组织书记
    //   if (!_isNumber(getFieldValue('hasHeadParty')) || getFieldValue('hasHeadParty') == 0) {
    //     exData = exData.filter((it) => it.code != 'hasOrganizationSecretary');
    //   }
    //   exData.forEach((item, index) => {
    //     let node = renderItem(item);
    //     res.push(node);
    //   });
    // }
  }

  // 单位类型为社会组织的时候（5开头）
  if (`${val}`.startsWith('5')) {
    let exData = [..._data16];

    // 当 主要负责人是否党员 选择是的时候才显示 主要负责人是否担任党组织书记
    if (!_isNumber(getFieldValue('hasHeadParty')) || getFieldValue('hasHeadParty') == 0) {
      exData = exData.filter((it) => it.code != 'hasOrganizationSecretary');
    }
    if (
      !_isNumber(getFieldValue('absorbedTissueNumber')) ||
      !(getFieldValue('absorbedTissueNumber') > 0)
    ) {
      exData = exData.filter((it) => it.code != 'notTurnedParty');
    }
    exData.forEach((item, index) => {
      let node = renderItem(item);
      res.push(node);
    });

    if (`${val}`.startsWith('51') || `${val}`.startsWith('52') || `${val}`.startsWith('53')) {
      // 当单位是社会组织和民办非企业的时候需要有：登记级别
      let exData = [..._data17];

      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
      if (`${val}`.startsWith('51')) {
        let exData = [..._data18];

        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
      }
      if (`${val}`.startsWith('522') || `${val}`.startsWith('521')) {
        let exData = [..._data19];

        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
        // 5211民办高等学校、52121民办普通高中、5213民办中等职业技术学校的时候
        if (
          `${val}`.startsWith('5211') ||
          `${val}`.startsWith('52121') ||
          `${val}`.startsWith('5213')
        ) {
          let exData = [...school];
          if (`${val}`.startsWith('5211')) {
            exData = [...exData, ...school2];
          }
          exData.forEach((item, index) => {
            let node = renderItem(item);
            res.push(node);
          });
        }
      }
    }
  }

  if (`${val}`.startsWith('9')) {
    if (`${val}`.startsWith('92')) {
      let exData = [..._data20];

      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
      if (`${val}`.startsWith('921') || `${val}`.startsWith('922')) {
        let exData = [..._data21];
        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
      }
      if (`${val}`.startsWith('921') || `${val}`.startsWith('922') || `${val}`.startsWith('923')) {
        let exData = [...zhiyuanzhe];
        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
      }
      // 7. 当单位类别为村（社区）（92开头），新增和编辑需要增加信息项目：参加县级及以上集中培训人数（数字、必填）；村干部参加城镇职工养老保险人数（数字、必填）；
      // 14.当单位类别为村（社区）的时候，下面增加信息项目：应到村任职选调生人数（数字、必填）；是否有大学毕业生在村工作（选择框、必填）
      if (`${val}`.startsWith('92')) {
        let exData = [..._data22];

        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
      }
    }
    if (`${val}`.startsWith('911')) {
      let exData = [..._data23];
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    }
    // 合并原来的为街道的单位的额外信息
    if (`${val}`.startsWith('911')) {
      let exData = [..._data24];
      // 当街道干部人数没有值时，屏蔽下面全部
      if (!_isNumber(getFieldValue('streetCadres')) || getFieldValue('streetCadres') == 0) {
        exData = [exData[0]];
      }
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    }
    if (`${val}`.startsWith('92')) {
      let exData = [..._data25];

      // // 是否配备第一书记 选是  出现 今年新选派第一书记（人）等
      // if (
      //   !_isNumber(getFieldValue('hasFirstSecretary')) ||
      //   getFieldValue('hasFirstSecretary') == 0
      // ) {
      //   exData = exData.filter(
      //     (it) =>
      //       it.code != 'firstSecretarySelect' &&
      //       it.code != 'firstSecretaryCode' &&
      //       it.code != 'secretaryTrainingNum' &&
      //       it.code != 'hasThousand',
      //   );
      // }
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });

      if (`${val}`.startsWith('921') || `${val}`.startsWith('922')) {
        let exData = [..._data26];

        exData.forEach((item, index) => {
          let node = renderItem(item);
          res.push(node);
        });
      }
    }

    // 3.当单位类别为乡镇得时候（912开头）新增单位和编辑单位增加信息项目：不是党委委员的政府领导班子成员人数（必填、数字）
    if (`${val}`.startsWith('912')) {
      let exData = [..._data27];
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    }
    if (`${val}`.startsWith('9121') || `${val}`.startsWith('9122')) {
      let exData = [...zhiyuanzhe];
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    }
    // 3.单位类别 911,9121,9122   加三个字段，农村专业技术协会数量，农民专业合作社数量，家庭农场数量
    if (`${val}`.startsWith('911') || `${val}`.startsWith('9121') || `${val}`.startsWith('9122')) {
      let exData = [..._data28];
      exData.forEach((item, index) => {
        let node = renderItem(item);
        res.push(node);
      });
    }
  }
  //限制条件 d04_code in('911','921','922')--梁才
  if (`${val}`.startsWith('911') || `${val}`.startsWith('921') || `${val}`.startsWith('922')) {
    [..._data29].forEach((item, index) => {
      let node = renderItem(item);
      res.push(node);
    });
  }
  return res;
};

// export default renderNewExtra
