// 流动党员-流入管理
import React, { Fragment } from 'react';
import { Tabs } from 'antd';
import { getSession } from '@/utils/session';
import MemberPartyOrganization from './components/MemberPartyOrganization';
import OrganizationReview from './components/OrganizationReview';
import ReminderMobilePartyMembers from './components/ReminderMobilePartyMembers';
import OutSide from './components/outside';

const TabPane = Tabs.TabPane;

export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: '1',
    };
  }

  render() {
    const { activeTab } = this.state;

    return (
      <Fragment>
        <Tabs
          activeKey={activeTab}
          onChange={(e) => {
            this.setState({
              activeTab: e,
            });
          }}
        >
          <TabPane tab="流动党员党组织" key="1" />
          <TabPane tab="流动党组织审核" key="2" />
          <TabPane tab="外省流动党组织审核" key="3" />
          {/* <TabPane tab="流动党员提醒" key="4" /> */}
        </Tabs>
        {activeTab === '1' && <MemberPartyOrganization org={getSession('org')} />}
        {activeTab === '2' && <OrganizationReview org={getSession('org')} />}
        {activeTab === '3' && <OutSide org={getSession('org')} />}
        {/* {activeTab === '4' && <ReminderMobilePartyMembers org={getSession('org')} />} */}
        {/* {activeTab === '3' && <InLibrary org={getSession('org')} />}
        {activeTab === '4' && <History org={getSession('org')} />} */}
      </Fragment>
    );
  }
}
