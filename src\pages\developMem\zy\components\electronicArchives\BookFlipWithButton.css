/* .book-container {
    perspective: 1500px;
    width: 400px;
    height: 500px;
    margin: 50px auto;
    position: relative;
  }
  
  .book {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 1s;
    transform-origin: left center;
  }
  
  .page {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    background: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-radius: 0 4px 4px 0;
    overflow: hidden;
  }
  
  .page-front {
    z-index: 2;
    background: linear-gradient(to right, #f1f1f1, #fff);
  }
  
  .page-back {
    transform: rotateY(180deg);
    background: linear-gradient(to left, #f1f1f1, #fff);
  }
  
  .page-content {
    padding: 30px;
    height: 100%;
    box-sizing: border-box;
    color: #333;
    font-size: 16px;
    line-height: 1.6;
  }
  
  /* 书脊效果 */
  .page::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(to right, #ddd, #fff);
  }
  
  /* 纸张纹理效果 */
  .page::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, 
      rgba(255,255,255,0.2) 25%, 
      transparent 25%, 
      transparent 75%, 
      rgba(255,255,255,0.2) 75%
    );
    background-size: 4px 4px;
    pointer-events: none;
  }
  
  /* 控制按钮样式 */
  .book-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    gap: 20px;
  }
  
  .page-button {
    padding: 8px 16px;
    font-size: 14px;
    color: #fff;
    background: #4a90e2;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .page-button:hover:not(:disabled) {
    background: #357abd;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }
  
  .page-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
  
  .page-number {
    font-size: 14px;
    color: #666;
  }
  
  /* 翻页动画效果 */
  .book[style*="rotateY(-180deg)"] .page-front {
    box-shadow: -4px 4px 8px rgba(0,0,0,0.1);
  }
  
  .book[style*="rotateY(0deg)"] .page-back {
    box-shadow: 4px 4px 8px rgba(0,0,0,0.1);
  }
  
  /* 响应式设计 */
  @media (max-width: 480px) {
    .book-container {
      width: 300px;
      height: 400px;
    }
    
    .page-content {
      padding: 20px;
      font-size: 14px;
    }
    
    .book-controls {
      flex-direction: column;
      gap: 10px;
    }
  } */