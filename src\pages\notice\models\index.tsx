import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {addNotic,getListByType,findReplyByMessageCode,getNoticeByCode,delNotice,stopNotice,updateNotice} from '../services';
import { getSession } from '@/utils/session';
import { changeListPayQuery } from '@/utils/method.js';
const memAbroad = modelExtend(listPageModel,{
  namespace: "notice",
  state:{
    abroadInfo:{},
    list:[],
    pagination:{},
    list2:[],
    pagination2:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if(pathname === '/notice/manage' || pathname === '/notice/mySend'){
          const org=getSession('org') || {};
          const defaultParas={
            pageNum:1,
            pageSize:10,
          };
          const dictData=['dict_d67'];
          for(let obj of dictData){
            dispatch({type:'commonDict/getDictTree',payload:{data:{dicName:obj}}});
          }
          if(pathname === '/notice/manage'){
            dispatch({
              type:'getList',
              payload:{
                data:{
                  code:org['code'],
                  ...defaultParas,
                  ...query,
                }
              }
            })
          }
          if(pathname === '/notice/mySend'){
            dispatch({
              type:'getList2',
              payload:{
                data:{
                  code:org['code'],
                  ...defaultParas,
                  ...query,
                }
              }
            })
          }
        }
      });
    }
  },
  effects: {
    *getList({ payload }, { call, put, select }) {
      const {filter ={},memName}=yield select(state=>state['notice']);
      payload['data']={...payload['data'],...filter,activityName:memName};
      const {data={}} = yield call(getListByType, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    *getList2({ payload }, { call, put, select }) {
      const {filter2 ={},memName2}=yield select(state=>state['notice']);
      payload['data']={...payload['data'],...filter2,memName2};
      const {data={}} = yield call(getNoticeByCode, payload);
      yield put({
        type: 'updateState',
        payload: {
          list2:data['list'],
          pagination2:{
            current:data['pageNumber'],
            pageSize:data['pageSize'],
            total:data['totalRow'],
          }
        }
      })
    },
    *add({ payload }, { call, put, select }) {
      return yield call(addNotic,payload);
    },
    *getTalkList({ payload }, { call, put, select }) {
      return yield call(findReplyByMessageCode, payload);
    },
    *del({ payload }, { call, put, select }) {
      return yield call(delNotice,payload);
    },
    *stopNotice({ payload }, { call, put, select }) {
      return yield call(stopNotice,payload);
    },
    *sendMsg({ payload }, { call, put, select }) {
      return yield call(updateNotice, payload);
    },
  }
});
export default memAbroad;
