import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Modal, Switch, Alert, DatePicker, Upload, message, Button } from 'antd';
import MemSelect from '@/components/MemSelect';
import moment from 'moment';
import Tip from '@/components/Tip';
import { findDictCodeName, unixMoment } from '@/utils/method.js';
import _isNumber from 'lodash/isNumber';
import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';
import _isString from 'lodash/isString';
import Date from '@/components/Date';
import { compare, compareDate, getContextPerson } from '@/pages/developMem/services/index'
import UploadComp, { fitFileUrlForForm } from '@/components/UploadComp';
// import Sure from '@/pages/developMem/develop/components/SureBasicInfoAndChange';
const { TextArea } = Input;

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      toobjContextMem: "",
      toactiveContextPerson: "",
      visible: false,
      memInfo: {},
      hasMemValue: true,
      isFullYear: true,
      showIsFullYear: false,
      timeKey: moment().valueOf(),
      // specialChecked: false,
    }
  }
  // 时间限制
  disabledTomorrow = (current) => {
    const { memInfo: { activeDate = '' } = {} } = this.state;
    const cu = moment(current);
    const start = moment(activeDate).endOf('day');
    // console.log(start.format('YYYY-MM-DD'),'123')
    const end = moment();
    if (_isNumber(activeDate)) {
      return current && (cu.isBefore(start) || cu.isSame(start) || cu.isAfter(end) || cu.isSame(end))
    } else {
      return false
    }
  };
  // 是否满一年的判断
  timeCompare = async (e) => {
    const { code = 500, data = true } = await compare({
      data: {
        code: this.state.memInfo.code,
        type: 3,
        time: moment(e).valueOf()
      }
    });
    if (code === 0) {
      this.setState({
        isFullYear: data
      })
    }
  }
  timeValidator = async (rule, value, callback) => {
    // const { specialChecked } = this.state
    // const { activeDate = moment().valueOf() } = this.state.memInfo;
    const { code = 500, data = true } = await compareDate({
      data: {
        code: this.state.memInfo.code,
        type: 3,
        time: moment(value).valueOf()
      }
    });
    if (code === 0) {
      if (!data) {
        this.setState({ showIsFullYear: false })
        callback(new Error('确定发展对象时间早于确定积极分子时间'));
      } else {
        this.setState({ showIsFullYear: true })
        // if(!specialChecked){
        //   if(value && value.isBefore(moment(activeDate).add(1, 'y'))){
        //     callback(new Error('确认为入党积极分子不满一年，不可确认为发展对象！'));
        //   }
        // }
        callback();
      }
    }
    callback();
  }
  handleDateChange = (e) => {
    let dateValue = e.target.value;
    let id = e.target.id;
    if (dateValue.length === 8) {
      let a = document.getElementById(id)
      a?.blur()
    }
  }
  // // 是否 特殊情况下发展
  // handleSpecialChange=(e)=>{
  //   this.setState({specialChecked: e},()=>{
  //     this.props.form.validateFields(['objectDate'], { force: true });
  //   })
  // }
  memLength = (rule, value, callback) => {
    if (_isArray(value) && !_isEmpty(value)) {
      if (value.length >= 3) {
        callback('只能选择1~2名人员');
      } else {
        callback();
      }
    } else {
      callback();
    }
  };
  handleOk = () => {
    const { submit, memDevelop: { basicInfo = {} } = {} } = this.props;
    const { memInfo, hasMemValue } = this.state;
    const { name, code: memCode, orgCode, orgName, orgZbCode, developOrgCode: logOrgCode, d08Code, d08Name } = memInfo;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      // console.log('valllll11111',val)
      if (!err) {
        if (basicInfo.hasUnitStatistics == null) {
          Tip.error('操作提示', '请完善人事关系是否在党组织关联单位内等信息。');
          return;
        }
        if (_isString(val['toobjContextMem'])) {
          val['toobjContextMem'] = this.state.toactiveContextPerson;
        } else {
          val['toobjContextMem'] = _isEmpty(val['toobjContextMem']) ? "" : hasMemValue ? val['toobjContextMem'].map(item => item['code']).toString() : val['toobjContextMem'];
        }

        ['isHighKnowledge', 'hasWorker', 'hasYoungFarmers', 'isAdvancedModel', 'isDispatch', 'isFarmer', 'isOutSystem', 'hasStaffOrganization'].map(item => {
          val[`${item}`] = val[`${item}`] == '1' ? 1 : 0;
        });
        // 增加字典表的name
        val = findDictCodeName([
          'd49', 'd18', 'd19', 'd20', 'd21', 'd07', 'd09', 'd11', 'd27', 'd28',
          'd06', 'd08', 'd60', 'd88', 'readingProfessional', 'politics', 'advancedModel'
        ], val, basicInfo);

        val = unixMoment(['toobjCultivateDate', 'objectDate'], val);
        val['name'] = name;
        val['memCode'] = memCode;
        val['orgCode'] = orgCode;
        val['orgName'] = orgName;
        val['orgZbCode'] = orgZbCode;
        val['logOrgCode'] = logOrgCode;
        val['d08Code'] = d08Code;
        val['d08Name'] = d08Name;
        // 附件
        val['toobjCheckFileUrl'] = fitFileUrlForForm(val['toobjCheckFileUrl']);

        const res = await this.props.dispatch({
          type: 'memDevelop/toObject',
          payload: { data: { ...val } }
        });
        const { code = 500 } = res;
        if (code === 0) {
          this.handleCancel();
          Tip.success('操作提示', '操作成功');
          submit && submit();
        }

      }
    })
  };
  handleCancel = () => {
    this.setState({
      visible: false
    });
    this.destroy();
  };
  // 确定发展对象获取介绍人
  getReferences = async (record) => {
    const { code = 500, data = {} } = await getContextPerson({
      memCode: record.code,
      d08Code: "5",
    });
    if (code === 0) {
      this.setState({
        toactiveContextPerson: data.toactiveContextPerson,
        toobjContextMem: data.name
      })

    }
  };
  open = (record) => {
    this.getReferences(record)
    this.setState({ visible: true, memInfo: record, timeKey: moment().valueOf() })
    this.props.dispatch({
      type: 'memDevelop/findMem',
      payload: {
        code: record['code']
      }
    })
  };
  destroy = () => {
    this.setState({
      memInfo: {},
      isFullYear: false,
      showIsFullYear: false,
      hasMemValue: true,
      toactiveContextPerson: "",
      toobjContextMem: "",
      //  specialChecked: false,
    })
  };
  hasMemOnChange = (val) => {
    let person = this.props.form.getFieldValue("toobjContextMem")
    if (!val) {
      if (_isArray(person)) {
        let personArr = person.map((item, index) => item.name);
        this.props.form.setFieldsValue({ toobjContextMem: personArr.toString() });
      }
    } else {
      this.props.form.setFieldsValue({ toobjContextMem: person });
    }
    this.props.form.setFieldsValue({ toactiveContextPerson: undefined });
    this.setState({ hasMemValue: val })
  };
  render() {
    const { form, loading: { effects = {} } = {} } = this.props;
    const { getFieldDecorator } = form;
    const { visible, hasMemValue } = this.state;
    return (
      <Modal
        destroyOnClose
        title="确定发展对象"
        visible={visible}
        onOk={this.handleOk}
        maskClosable={false}
        onCancel={this.handleCancel}
        confirmLoading={effects['memDevelop/toObject']}
        width={800}
      >
        {
          visible &&
          <Fragment key={this.state.timeKey}>
            <Alert message="提示：成为发展对象的时间为上级党委备案的时间。" type="info" showIcon />
            <div style={{ marginBottom: '10px' }} />
            <Form>
              <FormItem
                label="培养联系人是否为本组织人员"
                {...formItemLayout}
              >
                {getFieldDecorator('hasStaffOrganization', {
                  rules: [{ required: true, message: '培养联系人是否为本组织人员' }],
                  initialValue: hasMemValue,
                })(
                  <Switch checkedChildren="是" unCheckedChildren="否" onChange={this.hasMemOnChange} checked={hasMemValue} />
                )}
              </FormItem>
              {
                hasMemValue ?
                  <FormItem
                    label="培养联系人"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('toobjContextMem', {
                      rules: [{ required: true, message: '培养联系人' }, { validator: this.memLength }],
                      // initialValue:hasMemValue,
                      initialValue: this.state.toobjContextMem,
                    })(
                      <MemSelect initValue={this.state.toobjContextMem} checkType={'checkbox'} placeholder="请选择" />
                    )}
                  </FormItem>
                  :
                  <FormItem
                    label="培养联系人"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('toobjContextMem', {
                      rules: [{ required: true, message: '培养联系人' }],
                      // initialValue:hasMemValue,
                      initialValue: this.state.toobjContextMem,
                    })(
                      <Input placeholder="请选择" />
                    )}
                  </FormItem>
              }
              {/*<FormItem*/}
              {/*  label="培养教育考察时间"*/}
              {/*  {...formItemLayout}*/}
              {/*>*/}
              {/*  {getFieldDecorator('toobjCultivateDate', {*/}
              {/*    rules: [{ required: false, message: '培养教育考察时间' }],*/}
              {/*    // initialValue:hasMemValue,*/}
              {/*  })(*/}
              {/*    // <DatePicker style={{width:'100%'}} />*/}
              {/*    <Date />*/}
              {/*  )}*/}
              {/*</FormItem>*/}
              {/* <FormItem
                  label="考察材料扫描件"
                  {...formItemLayout}
                >
                  {getFieldDecorator('toobjCheckFileUrl', {
                    rules: [{ required: false, message: '考察材料扫描件' }],
                    // initialValue:hasMemValue,
                  })(
                    <UploadComp/>
                  )}
                </FormItem> */}
              {/* <FormItem
                  label="特殊情况下发展"   // hasSpecialDevelopment
                  {...formItemLayout}
                >
                  {getFieldDecorator('hasSpecialDevelopment', {
                        rules: [{ required: true, message: '是否特殊情况下发展' }],
                        initialValue:false,
                      })(
                        <Switch checkedChildren="是" unCheckedChildren="否" checked={specialChecked} onChange={this.handleSpecialChange}/>
                      )}
                </FormItem> */}
              <FormItem
                onChange={this.handleDateChange}
                label="确定发展对象时间"
                {...formItemLayout}
              >
                {getFieldDecorator('objectDate', {
                  rules: [{ required: true, message: '确定发展对象时间' }, { validator: this.timeValidator, trigger: ['blur', 'change'] }],
                  // initialValue:hasMemValue,
                })(
                  // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}} />
                  <Date disabledDate={this.disabledTomorrow} onChange={e => {
                    this.timeCompare(e)
                  }} />
                )}
              </FormItem>
              {/* {(()=>{console.log('是否满一年',this.state.isFullYear);console.log( this.state.showIsFullYear)})()} */}
              {(!this.state.isFullYear) && this.state.showIsFullYear && <FormItem
                label="确定积极分子不满一年说明"
                {...formItemLayout}
              >
                {getFieldDecorator('instructions', {
                  rules: [{ required: true, message: '确定积极分子不满一年说明' }],
                })(

                  <TextArea rows={2} />
                )}
              </FormItem>}
              {/* <Sure {...this.props} memInfo={this.state.memInfo}/> */}
              {/*<FormItem*/}
              {/*  label="支委会会议记录扫描件"*/}
              {/*  {...formItemLayout}*/}
              {/*>*/}
              {/*  {getFieldDecorator('toobjFileUrl', {*/}
              {/*    rules: [{ required: false, message: '考察材料扫描件' }],*/}
              {/*    // initialValue:hasMemValue,*/}
              {/*  })(*/}
              {/*    <UploadComp/>*/}
              {/*  )}*/}
              {/*</FormItem>*/}
            </Form>
          </Fragment>
        }
      </Modal>
    );
  }
}
export default Form.create()(index);
