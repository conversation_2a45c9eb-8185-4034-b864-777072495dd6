import React, { useEffect } from 'react';
import { MyContext } from '@/utils/global';
import { getSession } from '@/utils/session';
import autofit from 'autofit.js';
import 'lib-flexible';

function refreshScale() {
  console.log('触发');
  let baseWidth = document.documentElement.clientWidth;
  let baseHeight = document.documentElement.clientHeight;
  // let appStyle = document.body.style;
  // let bodyStyle = document.body.style;
  let appStyle = document.getElementById('root').style;
  let realRatio = baseWidth / baseHeight;
  let designRatio = 16 / 9;
  let scaleRate = baseWidth / 1920;
  let hscale = baseHeight / 1080;
  // if (baseWidth < 1900 && realRatio > designRatio) {
  //   scaleRate = baseHeight / 1080;
  // }
  // console.log(realRatio, designRatio, realRatio > designRatio, baseWidth, baseHeight, baseWidth < 1920, "dddddddddddddd");
  appStyle.transformOrigin = 'left top';
  let scaleStr = `scaleX(${scaleRate}) scaleY(${hscale})`;
  if (process.env.NODE_ENV == 'development') {
    appStyle.transform = `scale(${scaleRate})`;
  }
  appStyle.transform = scaleStr;
  // bodyStyle.width = "100%";
  // bodyStyle.height = "100%";
  // appStyle.width = `${baseWidth / scaleRate}px`;

  const htmlStyle = document.getElementById('htmlStyle');
  htmlStyle && htmlStyle.parentNode.removeChild(htmlStyle);
  var style = document.createElement('style');
  style.id = 'htmlStyle';
  style.appendChild(
    document.createTextNode(
      `.ant-select-dropdown {transform: ${scaleStr}} .ant-picker-dropdown{transform: ${scaleStr}} .ant-modal-wrap{transform: ${scaleStr};transform-origin:left top;}`,
    ),
  );
  var head = document.getElementsByTagName('head')[0];
  head.appendChild(style);
}

const index = (props: any) => {
  // useEffect(() => {
  //   const autofitInstance: any = autofit;
  //   autofitInstance.init({
  //     designHeight: 1120,
  //     designWidth: 3400,
  //     renderDom: '#asd',
  //     resize: true,
  //   }); // 初始化 Autofit.js
  //   return () => {
  //     autofitInstance.off(); // 在组件卸载时销毁 Autofit.js 实例
  //   };
  // }, []);

  //   window.onload = () => {
  //     refreshScale();
  //   };
  //   window.addEventListener(
  //     'pageshow',
  //     function (e) {
  //       if (e.persisted) {
  //         // 浏览器后退的时候重新计算
  //         refreshScale();
  //       }
  //     },
  //     false,
  //   );
  return (
    <div
      id={'asd'}
      style={{
        background: '#fff',
        height: '100%',
        overflow: 'auto',
      }}
    >
      <MyContext.Provider value={getSession('org')}>{props.children}</MyContext.Provider>
    </div>
  );
};

export default index;
