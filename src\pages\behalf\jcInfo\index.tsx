/**
 * 届次信息列表
 */
import React from 'react';
import {connect} from "dva";
import ListTable from 'src/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import {Button, Divider, Input, Popconfirm, Tabs,} from 'antd';
import NowOrg from 'src/components/NowOrg';
import {getSession} from "@/utils/session";
import AddEdit from './addEdit'
import styles from './index.less';
import moment from 'moment'
import { setListHeight ,isEmpty} from '@/utils/method';
import Notice from '@/components/Notice';
import ExportInfo from '@/components/Export';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import RuiFilter from '@/components/RuiFilter';
// import { _history as router } from "@/utils/method";
// import qs from 'qs';
const Search = Input.Search;
const TabPane = Tabs.TabPane;

@connect(({behalf,unit,commonDict,loading})=>({behalf,unit,commonDict,loading:loading.effects['unit/getList']}))
export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      dataInfo:undefined,
      filter:{},//筛选器
      search:{},//搜索框
      view:false,
      getList:this.getList
    };
  }
  // addOrEdit=()=>{
  //   this.setState({type:'add',dataInfo:{}},()=>{
  //     this['AddEdit'].showModal();
  //   });
  // };

  componentDidMount() {
    // this.getList();
    setListHeight(this)
  }
  // componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
  //   const org=getSession('org') || {};
  //   if (org['code'] && JSON.stringify(this.state.org)!==JSON.stringify(org)) {
  //     this.setState({
  //       org
  //     },()=>{
  //       this.getList(this.state['page'],this.state['pageSize'],this.state['name'])
  //     })
  //   }
  // }

  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const org=getSession('org') || {};
    const {_org = {},getList, page, pageSize, name} = prevState;
    if(!_isEqual(org, _org)){
      state['org'] = org;
      state['_org'] = org;
      getList(page, pageSize, name);
    }
    return state;
  };

  getList=(pageNum=1,pageSize=10,name='',type?)=>{
    let org=getSession('org')|| {};
    let val={
      positionOrgCode:org['orgCode'],
      positionOrgName:org['name'],
      pageNum:pageNum,
      pageSize:pageSize,
      memName:name,
      type
    };
    for (let obj in val) {
      if (isEmpty(val[obj])) {
        delete val[obj]
      }
    }
    this.props.dispatch({
      type:'behalf/jcList',
      payload:{
        data:{
          ...val
        }
      }
    })
  };
  upList=()=>{
    this.getList()
  };
  onPageChange=(page,pageSize)=>{
    const { name, type } = this.state
    this.setState({
      page,
      pageSize
    },()=>{
      this.getList(page,pageSize,name,type)
    });

  };

  confirm=(record)=>{
     this.props.dispatch({
          type:'behalf/jcDel',
          payload:{
            code:record['code']
          }
        }).then(res=>{
       if (res['code'] === 0) {
         Notice("操作提示",'删除成功!',"check-circle","green");
         this.getList(this.state['page'],this.state['pageSize'],this.state['name'])
       }else {
         Notice("操作提示",res['message'],"exclamation-circle-o","orange");
       }
     });
  };

  // view=(record)=>{
  //   this.setState({type:'edit',dataInfo:record},()=>{
  //     this['AddEdit'].showModal();
  //   });
  // };
  handleOk=()=>{
    this.setState({view:false})
  };
  handleCancel=()=>{
    this.setState({view:false})
  };

  // 筛选
  filterChange = (val) => {
    console.log('val===',val);
    const { type=[] } =val
    this.setState({
      type:isEmpty(val)?undefined:type
    },()=>this.getList(1,this.state['pageSize'],this.state.name,type));
    // const { pagination1 = {} } = this.props.behalf;
    // const { query } = this.props.location;
    // router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination1['pageSize'] })}`)
  };
  search=(value)=>{
    this.setState({
      name:isEmpty(value)?undefined:value
    },()=>this.getList(1,this.state['pageSize'],value,this.state.type));
  };
  searchClear = (e) => {
    this.setState({
      name: e.target.value
    })
    if (!e.target.value) {
      this.getList(1,this.state['pageSize'],'',this.state.type);
    }
  };

  exportInfo= async ()=>{
    this.setState({
      jcInfoDownload:true,
    })
    await this['jcInfo'].submitNoModal();
    this.setState({
      jcInfoDownload:false,
    })
  };

  render() {
    const {loading,behalf:{list1=[],pagination1={}}}=this.props;
    const {dataInfo={},filterHeight}=this.state;
    const {current,pageSize} = pagination1;
    const org = getSession('org') || {};
    let filterData = [
      {
        key: 'type', name: '党代表类型', value: [{ key: '12', name: '省党代表' }, { key: '13', name: '市党代表' }, { key: '14', name: '县党代表' }, { key: '61', name: '乡镇党代表' }],
      },
    ];
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:60,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'人员姓名',
        dataIndex:'memName',
        width:140,
      },
      {
        title:'性别',
        dataIndex:'sexName',
        width:100,
      },
      {
        title:'身份证',
        dataIndex:'memIdcard',
        width:200,
        render: (text,record) => {
          if(typeof text === 'string' && !_isEmpty(text)){
            let newVal=text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
            if(text.indexOf("*") > 0){
              return text
            }
            return (
              <span>{newVal}</span>
            );
          }else{
            return ''
          }
        }
      },
      {
        title:'学历情况',
        dataIndex:'d07Name',
        width:200,
      },
      {
        title:'人员身份',
        dataIndex:'d106Name',
        width:200,
      },
      {
        title:'组织',
        dataIndex:'positionOrgName',
        width:280,
      },
      {
        title:'党代表类型',
        dataIndex:'party',
        width:100,
      },
      // {
      //   title:'操作',
      //   dataIndex:'action',
      //   width:100,
      //   render:(text,record)=>{
      //     return(
      //       <div>
      //         <a href={'#'} onClick={()=>this.view(record)}>编辑</a>
      //         <Divider type="vertical"/>
      //         <Popconfirm title="确定要删除吗？" onConfirm={()=>this.confirm(record)}>
      //           <a href={'#'} className={'del'}>删除</a>
      //         </Popconfirm>
      //       </div>
      //     )
      //   },
      // },
    ];

    return (
      <div>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        {/*<AddEdit dataInfo={dataInfo} title={this.state['type']==='edit'?'编辑届次':'新增届次'} wrappedComponentRef={(e)=>this['AddEdit']=e} onChange={this.upList}/>*/}
        <NowOrg extra={
          <React.Fragment>
            <Button onClick={this.exportInfo} loading={this.state.jcInfoDownload}>导出</Button>
            <Search style={{width:200,marginLeft:16}} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'}/>
            {/*<Button type={'primary'} icon={<LegacyIcon type={'plus'} />} style={{marginLeft:16}} onClick={()=>this.addOrEdit()}>新增届次</Button>*/}
          </React.Fragment>
        }/>
        <RuiFilter
          data={filterData}
          // openCloseChange={() => setListHeight(this, 20)}
          onChange={this.filterChange}
        />
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:filterHeight}}
         
          columns={columns} data={list1} pagination={pagination1} onPageChange={this.onPageChange}/>
        <ExportInfo wrappedComponentRef={e=>this['jcInfo'] = e}
                    tableName={''}
                    noModal={true}
                    tableListQuery={{memName:this.state.name,type:this.state.type,positionOrgCode:org['orgCode'], positionOrgName:org['name'],}}
                    action={'/api/representative/export'}
        />
      </div>
    );
  }
}
