import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Radio, Modal, Switch, Alert, DatePicker, Upload, message, Button, Input, Select, } from 'antd';
import moment from 'moment';
import Tip from '@/components/Tip';
import { unixMoment } from '@/utils/method.js';
import _isEmpty from 'lodash/isEmpty';
import Date from '@/components/Date';
import { backProcessNodeList } from '@/pages/developMem/services'
import { zybackToProbationary } from '@/pages/mem/services'

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
};
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      memInfo: {},
      timeKey: moment().valueOf()
    }
  }
  // 时间限制
  disabledTomorrow = (current) => {
    return current && current > moment().endOf('day');
  };
  changeCanncelCode = async () => {
    const { code = 500, data = [] } = await backProcessNodeList({ cancelCode: 3 })
    console.log(data, 'code,data')
    if (code == 0) {
      this.setState({
        processArr: data
      })
    }
  }
  handleOk = () => {
    const { submit } = this.props;
    const { memInfo, hasMemValue } = this.state;
    const { name, code: memCode, orgCode, orgName, orgZbCode, developOrgCode: logOrgCode, d08Code, d08Name } = memInfo;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {

        const { code = 500 } = await zybackToProbationary({ code: memInfo['code'], processNode: val['processNode'] })

        if (code === 0) {
          this.handleCancel();
          Tip.success('操作提示', '操作成功');
          submit && submit();
        }
      }
    })
  };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  open = (record) => {
    this.setState({ visible: true, memInfo: record, timeKey: moment().valueOf() })
    this.changeCanncelCode()
  };
  destroy = () => {
    this.setState({
      memInfo: {},
    })
  };
  render() {
    const { form, loading: { effects = {} } = {} } = this.props;
    const { getFieldDecorator } = form;
    const { visible, memInfo, processArr = [] } = this.state;
    const { d08Code = '' } = memInfo;
    return (
      <Modal
        destroyOnClose
        title={`退回`}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        confirmLoading={effects['memDevelop/toActive']}
        width={800}
        bodyStyle={{ textAlign: 'center' }}
      >
        {
          visible &&
          <FormItem key={this.state.timeKey}>
            <Form>
              <FormItem
                label="退回流程"
                {...formItemLayout1}
              >
                {getFieldDecorator('processNode', {
                  rules: [{ required: true, message: '必填' }],
                  // initialValue:hasMemValue,
                })(
                  <Select style={{ width: '200px' }}>
                    {
                      processArr.map((item, index) => {
                        return <Select.Option key={index} value={item.node}>{item.nodeName}</Select.Option>
                      })
                    }
                  </Select>
                )}
              </FormItem>
            </Form>
          </FormItem>

        }

      </Modal>
    );
  }
}
export default Form.create()(index);
