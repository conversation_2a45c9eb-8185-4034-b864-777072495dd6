/**
 * 扩展信息
 */
/**
 * 模块名
 */
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Col, Form, Input, InputNumber, Modal, Row, Switch, Tabs, Select } from 'antd';
import Tip from '@/components/Tip';
import moment from 'moment';
import Date from '@/components/Date';
import UploadComp, { fitFileUrlForForm, getInitFileList } from '@/components/UploadComp';
import { formLabel, findDictCodeName } from '@/utils/method';
import DictSelect from '@/components/DictSelect';
import { validateLength} from '@/utils/formValidator';

const FormItem = Form.Item;
const { TabPane } = Tabs;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};

function index(props, ref) {
  const [form] = Form.useForm();
  const {tipMsg} = props;
  const { basicInfo } = props.memBasic;
  const [visible, setVisible] = useState(false);
  const [data, setData]: any = useState({});
  useEffect(() => {
  }, []);
  useImperativeHandle(ref, () => ({
    open: (e) => {
      open(e);
    },
  }));
  const open = (e) => {
    if (e?.type === 'edit') {
      form.setFieldsValue({
        ...e,
        // hasPlan531: e['hasPlan531'] == '1' ? 1 : 0,
        startDate: moment(e['startDate']),
        endDate: moment(e['endDate']),
        // fileUrl: getInitFileList(e['fileUrl']),
      });
    }
    setData(e);
    setVisible(true);
  };
  const onCancel = () => {
    setVisible(false);
    form.resetFields();
    setData({});
    props.onUp();
  };
  const onFinish = (values) => {
    values = findDictCodeName(
      ['d142'],
      values,
      basicInfo
    );
    values['startDate'] = moment(values['startDate']).valueOf();
    values['endDate'] = moment(values['endDate']).valueOf();
    // if (values['hasPlan531']) {
    //   values['hasPlan531'] = 1;
    //   values['fileUrl'] = fitFileUrlForForm(values['fileUrl']);
    // } else {
    //   values['hasPlan531'] = 0;
    //   values['fileUrl'] = undefined;
    // }
    let val: any = [];
    if (data?.id) {
      val = data?.id.map((i, index) => {
        let obj = {
          memCode: i,
          orgCode: basicInfo['orgCode'],
          code: data?.code || undefined,
          // memOrgCode: data.memOrgCode[index],
          ...values,
        };
        return obj;
      });
    } else {
      val = {
        ...values,
        orgCode: basicInfo['orgCode'],
        code: data?.code || undefined,
        memCode: basicInfo?.code,
        // memOrgCode: basicInfo.memOrgCode,
      }
    }
    props.dispatch({
      type: 'memTrain/trainAdd',
      payload: {
        data: val,
      },
    }).then(res => {
      if (res.code === 0) {
        Tip.success('操作提示', data?.code ? '修改成功' : '新增成功');
        onCancel();
      }
    });
  };
  return (
    <Modal
      title={'新增'}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={onCancel}
      width={1000}
      destroyOnClose
      closable={false}
      maskClosable={false}
      centered
    >
      <Form {...formItemLayout} form={form} onFinish={onFinish}>
        <Row>

          <Col span={12}>
            <FormItem
              label={formLabel('主办单位', tipMsg['organizer'])}
              name="organizer"
              rules={[{ required: true, message: '必填!' }, {validator: (...e)=>validateLength(e, 100, 300)}]}
            >
              <Input/>
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('培训机构', tipMsg['trainInstitution'])}
              name={'trainInstitution'}
              rules={[{ required: true, message: '必填!' }]}
            >
              <Input/>
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('班次名称', tipMsg['classOrder'])}
              name={'classOrder'}
              rules={[{ required: true, message: '必填!' }, {validator: (...e)=>validateLength(e, 100, 300)}]}
            >
              <Input/>
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('开班日期', tipMsg['startDate'])}
              name={'startDate'}
              rules={[{ required: true, message: '必填!' }]}
            >
              {/* <DatePicker style={{ width: '100%' }}/> */}
              <Date/>
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('结业日期', tipMsg['endDate'])}
              name="endDate"
              rules={[{ required: true, message: '必填!' }]}
            >
              {/* <DatePicker style={{ width: '100%' }}/> */}
              <Date/>
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('培训学时（天）', tipMsg['trainHours'])}
              name="trainHours"
              rules={[{ required: true, message: '必填!' }]}
            >
              <InputNumber min={0} style={{ width: '100%' }}/>
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('培训主题', tipMsg['trainTheme'])}
              name="trainTheme"
              rules={[{ required: true, message: '必填!' }, {validator: (...e)=>validateLength(e, 100, 300)}]}
            >
              <Input/>
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('入学测试成绩', tipMsg['inTestScores'])}
              name="inTestScores"
              rules={[{ required: false, message: '必填!' }]}
            >
              <InputNumber min={0} max={100} style={{ width: '100%' }} precision={2}/>
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('结业测试成绩', tipMsg['outTestScores'])}
              name="outTestScores"
              rules={[{ required: false, message: '必填!' }]}
            >
              <InputNumber min={0} max={100} style={{ width: '100%' }} precision={2}/>
            </FormItem>
          </Col>

          <Col span={12}>
            <FormItem
              label={formLabel('参训期间奖惩情况', tipMsg['rewardPunishInfo'])}
              name="rewardPunishInfo"
              rules={[{ required: false, message: '必填!' }]}
            >
              <Input/>
            </FormItem>
          </Col>

          {/* <Col span={12}>
            <FormItem
              label={formLabel('有无“531”行动计划表', tipMsg['hasPlan531'])}
              name="hasPlan531"
              // valuePropName="checked"
              rules={[{ required: false, message: '必填!' }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否"
                      onChange={e => setData({ ...data, hasPlan531: e ? 1 : 0 })}/>
                <Select placeholder={'请选择'} onChange={e => setData({ ...data, hasPlan531: e})}>
                  <Select.Option value={1}>是</Select.Option>
                  <Select.Option value={0}>否</Select.Option>
                </Select>

            </FormItem>
          </Col> */}

          {/* {
            (function() {
              let fieldsValue = form.getFieldValue('hasPlan531');
              if (fieldsValue) {
                return (
                  <Col span={12}>
                    <FormItem
                      label={formLabel('“531”行动计划表', tipMsg['fileUrl'])}
                      name="fileUrl"
                      rules={[{ required: false, message: '必填!' }]}
                    >
                      <UploadComp maxLen={1} files={getInitFileList(data['fileUrl'])}/>
                    </FormItem>
                  </Col>
                );
              }
            })()
          } */}
          <Col span={12}>
            <FormItem
              label={formLabel('集中培训情况', tipMsg['d142Code'])}
              name="d142Code"
              rules={[{ required: true, message: '必填!' }]}
            >
                <DictSelect backType={'object'} codeType={'dict_d142'} placeholder="请选择" initValue={data['d142Code'] || undefined} />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label={formLabel('备注', tipMsg['remark'])}
              name="remark"
              rules={[{ required: false, message: '必填!' }]}
            >
              <Input/>
            </FormItem>
          </Col>
        </Row>

      </Form>
    </Modal>

  );
}

export default forwardRef(index);
