.box {
  background: #FFFFFC;
  border-radius: 8px;
  //border: 1px solid #E8E8E8;
  box-shadow: 5px 5px 10px #d9d9d95e;
  color: white;
  display: flex;
  height: 20vh;
  justify-content: space-around;
  align-items: center;
  .avatorInfo{
    padding: 20px;
    // height: 114px;
    .avator {
      float: right;
      margin-right: 10px;
      img {
        width: 50px;
        height: 50px;
      }
      .icon {
        font-size: 50px;
        width: 50px;
        height: 50px;
      }
    }
    .number{
      text-align: center;
      // float: left;
      .text {
        font-size: 16px;
      }
      .num {
        font-size: 26px;
        font-weight: 400;
        .suf {
          font-size: 26px;
        }
      }
    }
  }
  .percent {
    padding: 0 20px 14px 20px;
    clear: both;
    width: 100%;
    :global(.ant-progress-bg){
      border-radius: 0 !important;
    }
  }
  .percent2 {
    padding: 0 20px 6px 20px;
    clear: both;
    width: 100%;
    &>div{
      position: relative;
      top: -36px;
    }
    :global(.ant-progress-bg){
      border-radius: 0 !important;
    }
  }
  .line {
    position: relative;
    height: 14vh;
    width: 1px;
    background: #fff;
    opacity: .9;
    //border: 0.5px solid #E8E8E8;
    // margin-bottom: 10px;
  }
  .extraInfo{
    // padding: 0 20px 10px 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 70%;
  }
}
.echart{
  width: 200px;
}
