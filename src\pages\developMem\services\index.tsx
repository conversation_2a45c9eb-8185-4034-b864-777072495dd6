/**
 * 发展党员党员管理services
 */
import request from "@/utils/request";
import qs from 'qs';


export function exportReadyData(params) {
  return request(`/api/data/develop/exportReadyData`,{
    method:'POST',
    body:params,
  });
}

export function transfer(params) {
  return request(`/api/mem/develop/transfer`,{
    method:'POST',
    body:params,
  });
}


export function getReadyList(params) {
  return request(`/api/mem/develop/getReadyList`,{
    method:'POST',
    body:params,
  });
}

export function getList(params) {
  return request(`/api/mem/develop/getList`,{
    method:'POST',
    body:params,
  });
}
export function getzyList(params) {//遵义入党申请人列表
  return request(`/api/zunyi/mem/develop/getList`,{
    method:'POST',
    body:params,
  });
}
export function addDevelop(params) {
  return request(`/api/mem/develop/addDevelop`,{
    method:'POST',
    body:params,
  });
}
export function updateDevelop(params) {
  return request(`/api/mem/develop/updateDevelop`,{
    method:'POST',
    body:params,
  });
}
//遵义新增入党申请人保存
export function addzunyiDevelop(params) {
  return request(`/api/zunyi/mem/develop/addDevelop`,{
    method:'POST',
    body:params,
  });
}
export function updatezunyiDevelop(params) {
  return request(`/api/zunyi/mem/develop/updateDevelop`,{
    method:'POST',
    body:params,
  });
}
export function uploadFileDigital(params) {
  return request(`/api/zunyi/mem/develop/uploadFileDigital`,{
    method:'POST',
    body:params,
  });
}
export function zybecomeActivist(params) {
  return request(`/api/zunyi/mem/develop/becomeActivist`,{
    method:'Post',
    body:params
  });
}
export function processNodeNext(params) {
  return request(`/api/zunyi/mem/develop/processNodeNext`,{
    method:'Post',
    body:params
  });
}
export function developExtendApproval(params) {
  return request(`/api/zunyi/process/developExtendApproval`,{
    method:'Post',
    body:params
  });
}

export function findDevelopProcess(params) {
  return request(`/api/zunyi/process/findDevelopProcess`,{
    method:'Post',
    body:params
  });
}
export function unlockExtendApproval(params) {
  return request(`/api/zunyi/process/unlockExtendApproval`,{
    method:'Post',
    body:params
  });
}
export function zyprolongationMem(params) {
  return request(`/api/zunyi/mem/new/prolongationMem`,{
    method:'Post',
    body:params
  });
}

export function addDevelopYear(params) {
  return request(`/api/mem/develop/addDevelopYear`,{
    method:'POST',
    body:params,
  });
}
export function updateDevelopYear(params) {
  return request(`/api/mem/develop/updateDevelopYear`,{
    method:'POST',
    body:params,
  });
}

export function findByCode(params) {
  return request(`/api/mem/develop/findByCode?${qs.stringify(params)}`,{
    method:'Get',
  });
}
export function zyfindByCode(params) {
  return request(`/api/zunyi/mem/develop/findByCode?${qs.stringify(params)}`,{
    method:'Get',
  });
}
export function becomeActivist(params) {
  return request(`/api/mem/develop/becomeActivist`,{
    method:'Post',
    body:params
  });
}

export function becomeDevelopObject(params) {
  return request(`/api/mem/develop/becomeDevelopObject`,{
    method:'Post',
    body:params
  });
}

export function zybecomeDevelopObject(params) {
  return request(`/api/zunyi/mem/develop/becomeDevelopObject`,{
    method:'Post',
    body:params
  });
}
export function zydyuploadFileDigital(params) {
  return request(`/api/zunyi/mem/new/uploadFileDigital`,{
    method:'Post',
    body:params
  });
}


export function becomePreliminary(params) {
  return request(`/api/mem/develop/becomePreliminary`,{
    method:'Post',
    body:params
  });
}
export function zybecomePreliminary(params) {
  return request(`/api/zunyi/mem/develop/becomePreliminary`,{
    method:'Post',
    body:params
  });
}

export function fireToParty(params) {
  return request(`/api/mem/develop/fireToParty`,{
    method:'Post',
    body:params
  });
}
export function zyfireToParty(params) {
  return request(`/api/zunyi/mem/develop/fireToParty`,{
    method:'Post',
    body:params
  });
}

export function delDevelop(params) {
  return request(`/api/mem/develop/delDevelop?${qs.stringify(params)}`,{
    method:'Get',
  });
}
export function zydelDevelop(params) {
  return request(`/api/zunyi/mem/develop/delDevelop?${qs.stringify(params)}`,{
    method:'Get',
  });
}

export function zybackOutStatus(params) {
  return request(`/api/zunyi/mem/develop/backOutStatus`,{
    method:'Post',
    body:params
  });
}


// export function delDevelopYear(params) {
//   return request(`/api/mem/develop/delDevelopYear?${qs.stringify(params)}`,{
//     method:'Get',
//   });
// }
export function delDevelopYear(params) {
  return request(`/api/mem/develop/delDevelopYear`,{
    method:'Post',
    body:params
  });
}

export function backOutStatus(params) {
  return request(`/api/mem/develop/backOutStatus`,{
    method:'Post',
    body:params
  });
}

export function getIndexList(params) {
  return request(`/api/mem/develop/getIndexList`,{
    method:'Post',
    body:params
  });
}

export function getPercent(params) {
  return request(`/api/mem/develop/getPercent?${qs.stringify(params)}`,{
    method:'Get',
  });
}

export function saveDevelopPlan(params) {
  return request(`/api/mem/develop/saveDevelopPlan?${qs.stringify(params)}`,{
    method:'Get',
  });
}

// 确定时间是否晚于前一个阶段
export function compare(params) {
  return request(`/api/mem/develop/compare`,{
    method:'Post',
    body:params
  });
}
// 确定时间是否满一年
export function compareDate(params) {
  return request(`/api/mem/develop/compareDate`,{
    method:'Post',
    body:params
  });
}
// 获取入党介绍人
export function getContextPerson(params) {
  return request(`/api/mem/develop/getContextPerson?${qs.stringify(params)}`,{
    method:'Get',
  });
}

// 追认中共党员
export function updateRatification(params) {
  return request(`/api/mem/develop/updateRatification`,{
    method:'Post',
    body:params
  });
}

//批量上传
export function importExcelDevelop(params) {
  return request(`/api/mem/develop/ImportExcelDevelop`,{
    method:'Post',
    body:params
  });
}

export function zyImportExcelDevelop(params) {
  return request(`/api/zunyi/mem/develop/ImportExcelDevelop`,{
    method:'Post',
    body:params
  });
}

export function undo(params) {//撤销
  return request(`/api/activist/transfer/undo`,{
    method:'POST',
    body:params,
  });
}

export function findOutByPage(params) {
  return request(`/api/activist/transfer/findOutByPage`,{
    method:'POST',
    body:params,
  });
}
export function inDetail(params) {//关系转接转入详情
  return request(`/api/activist/transfer/inDetail?${qs.stringify(params)}`);
}


export function outDetail(params) {//关系转接转入详情
  return request(`/api/activist/transfer/outDetail?${qs.stringify(params)}`);
}
export function transferMemInfo(params) {//获取转接人员信息
  return request(`/api/activist/transfer/transferMemInfo?${qs.stringify(params)}`);
}
export function addTransfer(params) {
  return request(`/api/activist/transfer/org`,{
    method:'POST',
    body:params,
  });
}
export function transferMem(params) {
  return request(`/api/activist/transfer/mem`,{
    method:'POST',
    body:params,
  });
}

export function adjustMem(params) {
  return request(`/api/activist/transfer/adjustMem`,{
    method:'POST',
    body:params,
  });
}
export function apply(params) {//审批通过
  return request(`/api/activist/transfer/apply`,{
    method:'POST',
    body:params,
  });
}
export function back(params) {//退回转接
  return request(`/api/activist/transfer/back`,{
    method:'POST',
    body:params,
  });
}
//领导班子成员
export function itteeAdd(params) {
  return request(`/api/activist/committee/add`,{
    method:'POST',
    body:params,
  });
}
export function itteeUP(params) {
  return request(`/api/activist/committee/update`,{
    method:'POST',
    body:params,
  });
}
export function itteeList(params) {
  return request(`/api/activist/committee/list?${qs.stringify(params)}`);
}
export function itteeDel(params) {
  return request(`/api/activist/committee/del`,{
    method:'POST',
    body:params,
  });
}

export function findInByPage(params) {
  return request(`/api/activist/transfer/findInByPage`,{
    method:'POST',
    body:params,
  });
}
export function memTransferInFromSysOut(params) {
  return request(`/api/activist/transfer/memTransferInFromSysOut`,{
     method:'POST',
    body:params,
  });
}
export function changeTargetOrg(params) {//变更目的组织
  return request(`/api/activist/transfer/changeTargetOrg`,{
    method:'POST',
    body:params,
  });
}
export function editTransferForMem(params) {//关系转接调整
  return request(`/api/activist/transfer/editTransferForMem`,{
     method:'POST',
    body:params,
  });
}
export function getProvinces(params) {
  return request(`/api/activist/across/getProvinces?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function exportXsl(params) {//导出
  return request(`/api/activist/transfer/exportXsl`,{
     method:'POST',
    body:params,
  });
}

export function memDigitalContents(params) {
  return request(`/api/zunyi/digital/developMem/memDigitalContents?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function transferMemDigital(params) {
  return request(`/api/zunyi/mem/new/transferMemDigital?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function listMemDigital(params) {
  return request(`/api/zunyi/digital/developMem/listMemDigital?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function transferMemDigitalList(params) {
  return request(`/api/zunyi/digital/transferMemDigitalList?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function preview(params) {//导出
  return request(`/api/minio/download`,{
     method:'POST',
    body:params,
  },'showImg');
}

export function dauploadFileDigital(params) {//导出
  return request(`/api/zunyi/mem/new/uploadFileDigital`,{
     method:'POST',
    body:params,
  });
}

export function uploadDigital(params) {
  return request(`/api/zunyi/digital/uploadDigital`,{
     method:'POST',
    body:params,
  });
}

export function flowcount(params) {
  return request(`/api/zunyi/mem/develop/count`,{
     method:'POST',
    body:params,
  });
}

export function cleanDigital(params) {
  return request(`/api/zunyi/digital/cleanDigital`,{
     method:'POST',
    body:params,
  });
}

export function checkOath(params) {
  return request(`/api/zunyi/mem/new/checkOath?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function shareLink(params) {
  return request(`/api/minio/shareLink?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function sortDigital(params) {
  return request(`/api/zunyi/digital/sortDigital`,{
     method:'POST',
    body:params,
  });
}

export function backProcessNodeList(params) {
  return request(`/api/zunyi/process/backProcessNodeList?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function findAuditProcess(params) {
  return request(`/api/zunyi/process/findAuditProcess`,{
     method:'POST',
    body:params,
  });
}

export function electronic(params) {
  return request(`/api/zunyi/digital/electronic?${qs.stringify(params)}`,{
    method:'GET',
  });
}
export function checkDevelop(params) {
  return request(`/api/zunyi/mem/develop/checkDevelop`,{
     method:'POST',
    body:params,
  });
}
