import React, { useState, useEffect, useRef } from 'react'
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Popconfirm,Select,Modal} from 'antd';
import ListTable from '@/components/ListTable';
import Add from './components/Add';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import { getTrainingPageType, getPageConfig } from './components/config';
import { ExclamationCircleOutlined } from '@ant-design/icons';

const index = (props: any) => {
  const { org: { basicInfo = {} } = {} } = props;
  const addRef:any = useRef();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const [year, setYear] = useState<any>();
  const [pagination, setPagination] = useState<any>({ pageNum: 1, pageSize: 20, total: 0 });

  const PAGETYPE = getTrainingPageType(basicInfo['d01Code']);
  const {cols = [] ,url:tableAction, delUrl} = getPageConfig(PAGETYPE) || {};

  const getList = async (p?) => {
    setLoading(true);
    const {
      code = 500,
      data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await tableAction({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.pageNum,
        orgCode:basicInfo['code'],
        // ...keyword,
        ...p,
      },
    });
    setLoading(false);
    if (code === 0) {
      setList(list);
      setPagination({ pageNum, total, pageSize,current: pageNum});
    }
  };

  useEffect(() => {
    if(!_isEmpty(PAGETYPE)){
      getList({pageNum:1})
    }
  }, [PAGETYPE])

  return (
    <div>
      <div style={{marginBottom:10}}>
      <Button type={'primary'} onClick={()=>{
        addRef.current.open();
      }}>新增</Button>
         年份选择：
        <Select
          style={{ width: 200, marginRight: 42, textAlign: 'left' }}
          value={year}
          onChange={(e) => {
            Modal.confirm({
              title: '当前界面数据未保存，切换后需重新填写，是否切换?',
              icon: <ExclamationCircleOutlined />,
              content: '',
              onOk() {
       
              },
              onCancel() {
                console.log('Cancel');
              },
            });
          
          }}
        >
          {(() => {
            let nowYear = '2024';
            if (!nowYear) return [];
            let arr: any = [];
            for (let y = 2021; y <= parseInt(nowYear); y++) {
              arr = [...arr, y];
            }
            return arr.reverse().map((ot) => (
              <Select.Option key={ot} value={ot}>
                {ot}年
              </Select.Option>
            ));
          })()}
        </Select>
      </div>
      <ListTable
        
        columns={[
          {
            title: '序号',
            dataIndex: 'num',
            align: 'center',
            width: 50,
            render: (text, record, index) => {
              return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
            },
          },
          ...cols,
          {
            title:'操作',
            dataIndex:'action',
            width:100,
            render:(text,record)=>{
              return(
                <span>
                  <a onClick={()=>{
                    addRef.current.open(record);
                  }}>编辑</a>
                  <Divider type="vertical"/>
                  <Popconfirm title="确定要删除吗？" onConfirm={ async()=>{
                    const {code = 500} = await delUrl({code:record.code});
                    if(code === 0){
                      Tip.success('操作提示','删除成功');
                      getList({ pageNum:1})
                    }
                  }}>
                   <a className={'del'}>删除</a>
                  </Popconfirm>
                </span>
              )
            },
          },
        ]}
        data={list}
        pagination={pagination}
        onPageChange={(page, pageSize) => getList({ pageNum: page, pageSize })}
        rowKey={'code'}
      />
      <Add ref={addRef} pageType={PAGETYPE} {...props} onOK={()=>{
        getList({ pageNum:1 })
      }}/>
    </div>
  )
}

export default index;
