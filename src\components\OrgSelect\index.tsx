/**
 * 组织选中器
 */
import React from 'react';
import { Input, Modal, Checkbox, Popover, Tabs } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import OrgTree from '../OrgTree';
import List from './list';
import { connect } from 'dva';
import styles from './index.less';
import { root, rootParent } from 'src/common/config.js';
import { getSession } from '@/utils/session';
import _isEmpty from 'lodash/isEmpty';

// 定义组件的属性类型
interface pType {
  dispatch?: any;
  onChange?: (data: Array<object>) => void;
  onSelectChange?: (data: Array<object>) => void;
  placeholder?: string;
  disabled?: boolean;
  orgTypeList?: Array<string>; // 组织大类:1.党委，2.党总支，3.党支部(包含临时)，4.联合党支部(包含临时)，5.党组
  org?: object;
  common?: any;
  initValue?: any;
  exclude?: any;
  multiple?: boolean;
  isPermissionCheck?: string; //是否权限检验,0--不进行校验,1--进行权限校验,为空默认进行权限检验
  destroyOnClose?: boolean; //关闭时是否重置列表数据
  oorg?: any; // 传入orgCode层级，只能选择当前orgCode层级的数据 subordinate:1/0 是否包含下级， 例 {orgCode: getSession('org').orgCode, subordinate:1}
  selectedRows?: Array<object>; // 传入数组，用于回显列表选中项
  title?: string; // 同普通title，显示标题
  showFilterData?: boolean; // 是否显示列表过滤数据
  otherListQuery: object; // 自定义的其他列表查询条件,
  listType?: 'org' | 'flow' | 'orgAndFlow'; // 'org' 党组织列表 'flow' 流动党组织列表 和 'orgAndFlow' 党组织和流动党组织列表 默认仅展示党组织列表
}
const Search = Input.Search;
// @ts-ignore
@connect(({ common }) => ({ common }))
export default class index extends React.Component<pType, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      value: undefined,
      org: getSession('org'),
      isExOrg: false,
      data: [],
      subordinate: '1',
      isFlowStatus: '1',
    };
  }
  // 显示模态框
  show = () => {
    const { org } = this.props;
    if (org) {
      this.loadData([org['orgCode'] || org['code']]);
    }
    this.setState({
      visible: true,
    });
  };
  // 确认选择
  handleOk = () => {
    const { onChange, multiple } = this.props;
    const { data } = this.state;
    // let obj = data[0] || {};
    onChange && onChange(data);
    this.setState({
      // value: obj['name'],
      value: data.map((it) => it.name).toString(),
    });
    this.handleCancel();
  };
  handleCancel = () => {
    this.setState({
      visible: false,
    });
  };
  loadData = (val) => {
    const { isPermissionCheck = undefined } = this.props;
    this.props.dispatch({
      type: 'common/getTree',
      payload: {
        data: {
          orgCodeList: val,
          excludeOrgCodeList: [],
          isPermissionCheck,
        },
      },
    });
  };
  treeSearch = (val) => {
    this.props.dispatch({
      type: 'common/queryTree',
      payload: {
        name: val,
      },
    });
  };
  onChange = (data = []) => {
    this.setState({
      data,
    });
    this.props.onSelectChange && this.props.onSelectChange(data);
  };
  treeChange = (selectedKeys, e) => {
    const { dataRef } = e.node;
    const { subordinate } = this.state;
    this.setState({
      org: dataRef,
    });
    List['WrappedComponent'].action({ orgCode: dataRef['orgCode'], pageNum: 1, subordinate });
  };
  static getDerivedStateFromProps(props, state) {
    const { initValue, common, org } = props;
    const { mapTreeCode } = common;
    let { value, isExOrg } = state;
    if (initValue && !value) {
      let obj = mapTreeCode.get(initValue);
      if (obj) {
        return { value: obj['name'] };
      } else if (root['parentCode'] === initValue) {
        return { value: rootParent['name'] };
      } else {
        return { value: initValue };
      }
    }
    if (!isExOrg && org) {
      return { org, isExOrg: true };
    }
    // 修改初始值回显不是最新值，或者选了值不回显的问题
    if(!_isEmpty(initValue) && initValue != value) {
      return { value: initValue }
    }
    return null;
  }
  onCheck = (e) => {
    const { org = {} } = this.state;
    this.setState({ subordinate: e.target.checked ? '1' : '0' });
    sessionStorage.setItem('subordinate', e.target.checked ? '1' : '0');
    List['WrappedComponent'].action({
      orgCode: org['orgCode'],
      pageNum: 1,
      subordinate: e.target.checked ? '1' : '0',
    });
  };
  onCheckOrg = (e) => {
    sessionStorage.setItem('isFlowStatus', e.target.checked ? '1' : '0');
    this.setState({ isFlowStatus: e.target.checked ? '1' : '0' });
  };
  renderList = (query?: Object) => {
    const { org } = this.state;
    const { orgTypeList, exclude = [], isPermissionCheck = undefined, multiple = false, selectedRows = [], showFilterData = true, otherListQuery = {} } = this.props;
    let listOrg = org;
    return (
      <List
        org={org}
        subordinate={this.props.oorg?.subordinate == 1 ? 1 : this.props.oorg?.subordinate == 0 ? 0 : this.state.subordinate}
        multiple={multiple}
        exclude={exclude}
        orgTypeList={orgTypeList}
        isPermissionCheck={isPermissionCheck}
        orgCode={listOrg['orgCode'] || listOrg['managerOrgCode']}
        onChange={this.onChange}
        selectedRows={selectedRows}
        showFilterData={showFilterData}
        otherListQuery={{ ...query, ...otherListQuery }}
      />
    );
  };
  render() {
    const { visible, value, org, subordinate, isFlowStatus } = this.state;
    const { common, children, placeholder, disabled = false, exclude = [], destroyOnClose = false, title = '', listType = 'org' } = this.props;
    // if(this.props.org){
    //   listOrg=this.props.org || {};
    // }
    // const subordinate = sessionStorage.getItem('subordinate') || '1';
    // const isFlowStatus = sessionStorage.getItem('isFlowStatus') || '0';
    return (
      <React.Fragment>
        {children ? (
          React.cloneElement(children as any, {
            onClick: this.show,
          })
        ) : (
          <Search title={title} value={value} disabled={disabled} onClick={this.show} onSearch={this.show} placeholder={placeholder || '请点击选择'} enterButton />
        )}
        <Modal title="组织选择器" destroyOnClose={destroyOnClose} visible={visible} onOk={this.handleOk} onCancel={this.handleCancel} width={1200} bodyStyle={{ padding: 0 }}>
          {visible && (
            <div className={styles.content}>
              <div className={styles.tree} style={{ pointerEvents: this.props.oorg ? 'none' : 'auto' }}>
                <div className={styles.tit}>
                  <div> 机构筛选</div>
                  {/* 当组织选择器左侧机构筛选栏设置为不能切换选项时：右边列表数据若包含下级，就把“包含下级”复选框显示出来，否则隐藏 */}
                  {(function (_this) {
                    if (_this.props?.oorg) {
                      if (_this.props?.oorg?.subordinate == 1) {
                        return (
                          <Checkbox
                            defaultChecked={true}
                            // checked={subordinate == 1}
                            onChange={_this.onCheck}
                            style={{ float: 'right' }}
                          >
                            包含下级
                          </Checkbox>
                        );
                      }
                    } else {
                      return (
                        // <Checkbox
                        //     defaultChecked={subordinate == 1}
                        //     // checked={subordinate == 1}
                        //     onChange={_this.onCheck}
                        //     style={{ float: 'right' }}
                        //   >
                        //     包含下级
                        //   </Checkbox>
                        <Popover
                          placement="bottom"
                          content={
                            <div className="o_check">
                              <Checkbox checked={subordinate == '1'} onChange={_this.onCheck} style={{ float: 'right' }}>
                                包含下级
                              </Checkbox>
                              {/* <Checkbox checked={isFlowStatus == '1'} onChange={_this.onCheckOrg} style={{ float: 'right' }}>
                                包含流动党组织
                              </Checkbox> */}
                            </div>
                          }
                          trigger="click"
                        >
                          <div className={styles.check}>
                            显示内容
                            <DownOutlined />
                          </div>
                        </Popover>
                      );
                    }
                  })(this)}
                </div>
                <OrgTree
                  listData={common['listTree']}
                  mapData={common['mapTree']}
                  filterData={common['filterData']}
                  loadData={this.loadData}
                  onSearch={this.treeSearch}
                  onChange={this.treeChange}
                  showSearch={false}
                  rootCode={this.props.org ? this.props.org['code'] : undefined}
                  type={'selector'}
                  exclude={exclude}
                  disabled={this.props.oorg ? true : false}
                  isShowFlowOrg={isFlowStatus == 1 ? true : false}
                />
              </div>
              <div className={styles.list}>
                {/* 同时展示党组织和流动党组织 */}
                {listType == 'orgAndFlow' ? (
                  <React.Fragment>
                    <Tabs destroyInactiveTabPane>
                      <Tabs.TabPane tab="党组织" key="1">
                        {this.renderList()}
                      </Tabs.TabPane>
                      <Tabs.TabPane tab="流动党组织" key="2">
                        {this.renderList({ mustFlowStatus: '1', d01CodeList: ['813'] })}
                      </Tabs.TabPane>
                    </Tabs>
                  </React.Fragment>
                ) : (
                  <React.Fragment>{this.renderList(listType == 'flow' ? { mustFlowStatus: '1', d01CodeList: ['813'] } : {})}</React.Fragment>
                )}
              </div>
            </div>
          )}
        </Modal>
      </React.Fragment>
    );
  }
}
