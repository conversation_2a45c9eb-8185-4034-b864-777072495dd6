// 社区党建有关情况
import React, { Fragment, useEffect, useState } from 'react';
import { Button, Form, InputNumber, Row, Col, Switch, Divider } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import { formItemLayout, formItemLayout2, formItemLayout3 } from './config';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import _cloneDeep from 'lodash/cloneDeep';
import Tip from '@/components/Tip';
import { saveForm8, findZtDataByCode } from '@/pages/[unit]/services/thematic';
import DictSelect from '@/components/DictSelect';

const index = (props: any) => {
  const [form] = Form.useForm();
  const { selectTab, basicInfo } = props;
  const { code: unitCode = '' } = basicInfo;
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);

  const onFinish = async (e) => {
    setLoading(true);
    let val = _cloneDeep(e);
    [
      'hasStandCommittee',
      'hasSecretaryOffice',
      'hasSecretaryNoOffice',
      'hasMinisterOffice',
      'hasMinisterNoOffice',
      'hasPropagandaOffice',
      'hasPropagandaNoOffice',
      'hasUniteOffice',
      'hasUniteNoOffice',
      'isAffiliatedCollege',
      'isNotTurnedRelation',
      'isNotTurnedRelation',
      'isYearChangeSecret',
    ].map((it) => {
      val[it] = val[it] ? 1 : 0;
    });
    const { code = 500 } = await saveForm8({
      data: {
        unitCode,
        ...val,
      },
    });
    setLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '修改成功');
    }
  };
  const getInfo = async () => {
    const { code = 500, data = {} } = await findZtDataByCode({
      unitCode,
      type: '8',
    });
    if (code === 0) {
      setQuery(data);
      form.setFieldsValue(data);
    }
  };
  useEffect(() => {
    getInfo();
  }, []);

  return (
    <Fragment>
      <Form form={form} {...formItemLayout3} onFinish={onFinish}>
        <Row>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasStandCommittee"
              label="是否设常委会"
              initialValue={query['hasStandCommittee'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="specialReport"
              label="向地方党委和主管部委专题报告党委领导下的校长负责制执行情况的"
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="revisedRules" label="修订党委全委会、常委会和校长办公会议事规则的">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="secretaryToDistrict" label="学校党委书记向地方党委述职的">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="secretaryToSchool"
              label="组织开展二级院（系）党组织书记向学校党委述职的"
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="partyMember" label="校长系中共党员的">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="deputySecretary" label="校长担任党委副书记的">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasSecretaryOffice"
              label="纪委书记任职情况担任党委常委"
              initialValue={query['hasSecretaryOffice'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasSecretaryNoOffice"
              label="纪委书记任职情况担任不设常委会的党委委员"
              initialValue={query['hasSecretaryNoOffice'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasMinisterOffice"
              label="组织部长任职情况担任党委常委"
              initialValue={query['hasMinisterOffice'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasMinisterNoOffice"
              label="组织部长任职情况担任不设常委会的党委委员"
              initialValue={query['hasMinisterNoOffice'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasPropagandaOffice"
              label="宣传部长任职情况担任党委常委"
              initialValue={query['hasPropagandaOffice'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasPropagandaNoOffice"
              label="宣传部长任职情况担任不设常委会的党委委员"
              initialValue={query['hasPropagandaNoOffice'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasUniteOffice"
              label="统战部长任职情况担任党委常委"
              initialValue={query['hasUniteOffice'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasUniteNoOffice"
              label="统战部长任职情况担任不设常委会的党委委员"
              initialValue={query['hasUniteNoOffice'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isAffiliatedCollege"
              label="二级院（系）"
              initialValue={query['isAffiliatedCollege'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="d82Code"
              {...formItemLayout2}
              label="二级院系配备专职组织员"
              // initialValue={query['d82Code'] || ''}
            >
              <DictSelect codeType={'dict_d82'} initValue={query['d82Code'] || ''} />
            </Form.Item>
          </Col>
          {/* <Col span={12}>
            <Form.Item
              name="d82Name"
              label="二级院系配备专职组织员名称"
              initialValue={query['d82Name'] || ''}
            >
              <Input />
            </Form.Item>
          </Col> */}
          <Col span={12}>
            <Form.Item name="yearYxdwsjJoinTrainNum" label="本年度院系党委书记参加培训人次">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="yearDzbsjJoinTrainNum" label="本年度党支部书记参加培训人次">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="yearRqjmdyxdzbsjNum" label="本年度任届期满的院系党支部">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isYearChangeSecret"
              label="本年换届"
              initialValue={query['isYearChangeSecret'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="yearDevelopParty" label="本年度发展党员">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="yearDevelopTeacherParty" label="本年度发展教师党员">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="yearDevelopStudentParty" label="本年度发展学生党员">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="yearDevelopGraduateParty" label="本年度毕业生党员">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isNotTurnedRelation"
              label="尚未转出组织关系"
              initialValue={query['isNotTurnedRelation'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="notTurnedRelationBysdyNum" label="累积未转出组织关系的毕业生党员 ">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
        </Row>
        <div style={{ textAlign: 'center' }}>
          <WhiteSpace />
          <Button
            type={'primary'}
            htmlType={'submit'}
            icon={<LegacyIcon type={'check'} />}
            // onClick={() => {}}
            style={{ marginRight: 16 }}
            loading={loading}
          >
            保存
          </Button>
          {/* <Button
            type={'primary'}
            danger
            htmlType={'button'}
            icon={<LegacyIcon type={'delete'} />}
            onClick={() => {}}
          >
            取消
          </Button> */}
        </div>
      </Form>
    </Fragment>
  );
};
export default index;
