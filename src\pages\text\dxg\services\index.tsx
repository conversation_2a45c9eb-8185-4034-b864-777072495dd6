import request from '@/utils/request';
import qs from 'qs';
export function addTableRuler(params) {
  return request(`/api/annual/addTableRuler`, {
    method: 'post',
    body: params
  });
}
export function addTableSingleRuler(params) {
  return request(`/api/annual/addTableSingleRuler`, {
    method: 'post',
    body: params
  });
}
export function findListByTable(params) {
  return request(`/api/annual/findListByTable?${qs.stringify(params)}`,{
    method:'get',
  });
}

export function findListBySingleTable(params) {
  return request(`/api/annual/findListBySingleTable?${qs.stringify(params)}`,{
    method:'get',
  });
}

