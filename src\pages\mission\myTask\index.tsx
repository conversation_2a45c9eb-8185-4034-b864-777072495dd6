import React, {Fragment} from 'react';
import {Tabs} from 'antd';
import _isEmpty from 'lodash/isEmpty'
import List from './components/list';
const TabPane = Tabs.TabPane;
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);

  }
  onChange =(val)=>{

  };
  render(): React.ReactNode {
    const memTabs = [
      {key:'1',title:'基本信息',component: <List {...this.props}/>}
    ];
    return (
      <Fragment>
        <Tabs defaultActiveKey="1" onChange={this.onChange}>
          {
            !_isEmpty(memTabs) && memTabs.map(item=> <TabPane tab={item['title']} key={item['key']}>{item['component']}</TabPane>)
          }
        </Tabs>
      </Fragment>
    )
  }
}
