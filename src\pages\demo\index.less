.contrast {
  display: flex;
  > div {
    width: 50%;
    border: 1px solid red;
  }
}

.scrollBox {
  height: 200px;
  width: 400px;
  > div {
    // height: 100% !important;
  }
  .scorllItem {
    width: 40px;
    height: 20px;
    display: inline-flex;
    background-color: aquamarine;
    justify-content: center;
    align-items: center;
    color: aliceblue;
    &:nth-child(2n + 1) {
      background-color: red;
    }
  }
}

.signContainer {
  background: #6c5438;
  padding: 22px 16px 15px 16px;
  height: 623px;
  box-sizing: border-box;

  & .signContent {
    position: relative;
    width: 100%;

    & .signTip {
      color: #ccc;
      font-size: 30px;
      text-align: center;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  & .canvasContainer {
    border-radius: 10px;
    background: #fff;
  }

  & .buttonContainer {
    display: flex;
    flex-direction: row;
    margin-top: 12px;

    & .clearBtn {
      width: 164px;
      font-size: 18px;
      color: #ac9374;
      border-radius: 5px;
      border-radius: 5px;
      background: none;
      border: 1px solid #ac9374;

      &::before {
        border: none;
      }
    }

    & .signBtn {
      width: 164px;
      margin-left: 15px;
      font-size: 18px;
      background: linear-gradient(135deg, #d8bb9a 0%, #8b6e4c 100%);
      border-radius: 5px;
      color: #fff;
      border: none;

      &::before {
        border: none;
      }
    }
  }
}
