import request from "@/utils/request";
import qs from 'qs';


export function personSave(params) {
  return request(`/api/mem/reviewers/save`,{
    method:'POST',
    body:params,
  });
}

export function personUpdate(params) {
  return request(`/api/mem/reviewers/update`,{
    method:'POST',
    body:params,
  });
}

export function personRemove(params) {
  return request(`/api/mem/reviewers/remove?${qs.stringify(params)}`);
}
export function personList(params) {
  return request(`/api/mem/reviewers/list?${qs.stringify(params)}`);
}

export function year(params) {
  return request(`/api/mem/reviewers/year?${qs.stringify(params)}`);
}