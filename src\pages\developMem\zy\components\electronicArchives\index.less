.bookbody {
  width: 100%;
  height: 740px;
  position: relative;
  display: flex;
}
.bookmenus {
  //  width: 32px;
  position: absolute;
  left: 1px;
  top: 32px;
  z-index: 2;
}
.item {
  width: 100%;
  writing-mode: vertical-rl;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
  text-align: center;
  font-family: Source <PERSON>;
  padding: 16px 2px;
  border-top-left-radius: 8px;
}
.bookcard {
  flex: 1;
  background: url(../../../../../assets/mem/book.png) no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  position: relative;
  .Breadcrumb {
    width: 100%;
    position: absolute;
    left: 60px;
    top: 20px;
    z-index: 2;
    display: flex;
    > div {
      flex: 1;
    }
  }
  .bookcardbox {
    width: 1250px;
    height: 680px;
    margin-top: 17px;
    overflow: hidden;
    position: relative;
    .spine {
      width: 2px;
      height: 100%;
      background: #787676;
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
.electronicArchives {
  .ant-modal-body {
    padding: 0 !important;
  }
  .ant-modal-close-x {
    color: #ff9109;
  }
}
.preview-img {
  .ant-modal-body {
    background: #6c6a6a;
  }
  .ant-modal-close-x {
    color: #fff;
  }
}
.Pagebox {
  width: 100%;
  height: 100%;
  background: #5f1e1e;
}
.book-container {
  width: 600px;
  height: 600px;
  background: #fff;
}

.tables {
  height: 100%;
  overflow: hidden;
  padding: 20px;
  table {
    width: 585px;
    margin: auto;
    td {
      border: 1px solid #ebeef5;
      padding: 13px 12px;
      width: 160px;
      font-family: Source Han Sans;
      font-size: 14px;
      font-weight: normal;
    }
    .label {
      background: #f5f7fa;
    }
  }
}
.imgs {
  width: 600px;
  height: 656px;
  margin-left: 12px;
  margin-top: 25px;
}
.pages {
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: absolute;
  bottom: 60px;
  left: 0;
}

.annotation {
  position: absolute;
  top: 0;
  right: 72px;
  cursor: pointer;
  z-index: 2;
  font-size: 14px;
  span {
    display: inline-block;
    margin-left: 3px;
  }
}
.Breadcrumb {
  .ant-breadcrumb {
    position: relative;
  }
}
