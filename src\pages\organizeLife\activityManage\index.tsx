/**
 * 组织生活-活动管理
 */
import React, { useState, useRef, useEffect, Fragment } from 'react';
import { Input, Select, Form, Modal, Tabs, Button, Divider, Popconfirm } from 'antd';
import moment from 'moment';
import { connect } from 'dva';
import { getSession } from '@/utils/session';
import Tip from '@/components/Tip';
import NowOrg from 'src/components/NowOrg';
import WhiteSpace from '@/components/WhiteSpace';
import ListTable from 'src/components/ListTable';
import RuiFilter from '@/components/RuiFilter';
import ExportInfo from '@/components/Export';
import Date from '@/components/Date';
import AddOrEdit from './addOrEdit';
import { orgLifeList, deleteOrgLife } from '../services';

const Search = Input.Search;
const TabPane = Tabs.TabPane;
const index = (props: any) => {
  const { code = undefined, orgCode = undefined } = getSession('org') || {
    code: undefined,
    orgCode: undefined,
  };
  const exportInfoRef: any = useRef();
  const addOrEditRef: any = useRef();
  const [listLoading, setListLoading] = useState(false);
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 20, total: 0, current: 1 });
  const [listData, setListData] = useState([]);
  const [activityName, setActivityName] = useState<any>(undefined);
  const [startTime, setStartTime] = useState<any>(undefined);
  const [endTime, setEndTime] = useState<any>(undefined);
  const [d158Code, setD158Code] = useState<any>(undefined);
  const [exportInfoLoading, setExportInfoLoading] = useState(false);
  let filterData = [
    {
      key: 'd158Code',
      name: '组织生活类型',
      value: props.commonDict['dict_d158'],
    },
  ];
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 60,
      render: (text, record, index) => {
        return (pagination.current - 1) * pagination.pageSize + index + 1;
      },
    },
    {
      title: '活动名称',
      dataIndex: 'activityName',
      width: 140,
    },
    {
      title: '创建党组织名称',
      dataIndex: 'orgName',
      width: 140,
    },
    {
      title: '参与党组织名称',
      dataIndex: 'joinOrgCodeListName',
      width: 140,
    },
    {
      title: '活动类型',
      dataIndex: 'd158Name',
      align: 'center',
      width: 100,
    },
    {
      title: '活动时间',
      dataIndex: 'activityTime',
      align: 'center',
      width: 100,
      render: (text) => {
        return moment(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '活动记录',
      width: 200,
      align: 'left',
      dataIndex: 'activityRecord',
      render: (text) => {
        return (
          <div
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              display: '-webkit-box',
            }}
          >
            {text}
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      render: (text, record) => {
        const { code = undefined } = getSession('org') || { code: undefined };
        return (
          <div>
            {code === record?.orgCode ? (
              <Fragment>
                <a
                  onClick={() => {
                    addOrEditRef.current.open(record, 'edit');
                  }}
                >
                  编辑
                </a>
                <Divider type="vertical" />
                <Popconfirm
                  title="确定要删除吗？"
                  onConfirm={() => {
                    handleDel(record);
                  }}
                >
                  <a className={'del'}>删除</a>
                </Popconfirm>
              </Fragment>
            ) : (
              <Fragment>
                <a
                  onClick={() => {
                    addOrEditRef.current.open(record, 'readOnly');
                  }}
                >
                  查看
                </a>
              </Fragment>
            )}
          </div>
        );
      },
    },
  ];

  // 导出
  const exportInfo = async () => {
    setExportInfoLoading(true);
    await exportInfoRef.current.submitNoModal();
    setExportInfoLoading(false);
  };
  const filterChange = (val) => {
    console.log('val===', val);
    getList({ pageNum: 1, ...val });
    setD158Code(val);
  };
  const getList = async (p?: any) => {
    setListLoading(true);
    const {
      code: resCode = 500,
      data: { list = [], pageNumber = 1, pageSize = 10, totalPage = 0, totalRow = 0 } = {},
    } = await orgLifeList({
      data: {
        orgCode,
        pageNum: 1,
        pageSize: 10,
        activityName,
        startTime,
        endTime,
        ...d158Code,
        ...p,
      },
    });
    setListLoading(false);
    if (resCode === 0) {
      setListData(list);
      setPagination({ current: pageNumber, total: totalRow, pageSize, pageNum: pageNumber });
    }
  };
  const handleDel = async (p?: any) => {
    const { code = 500 } = await deleteOrgLife({ code: p?.code });
    if (code === 0) {
      Tip.success('操作提示', '删除成功');
      getList({ pageNum: 1 });
    }
  };

  useEffect(() => {
    if (code) {
      getList();
    }
  }, [code]);
  useEffect(() => {
    const dictData = ['dict_d158'];
    for (let obj of dictData) {
      props.dispatch({
        type: 'commonDict/getDictTree',
        payload: {
          data: {
            dicName: obj,
          },
        },
      });
    }
  }, []);

  return (
    <div>
      <Tabs defaultActiveKey="1">
        <TabPane tab="基本信息" key="1" />
      </Tabs>
      <NowOrg
        extra={
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button onClick={exportInfo} loading={exportInfoLoading}>
              导出
            </Button>
            <Button
              onClick={() => {
                addOrEditRef.current.open({}, 'add');
              }}
              type="primary"
              style={{ marginLeft: 16 }}
            >
              新增
            </Button>
            <Search
              allowClear
              style={{ width: 200, marginLeft: 16 }}
              onSearch={(e: any) => {
                console.log('e==', e);
                getList({ pageNum: 1, activityName: e });
                setActivityName(e);
              }}
              placeholder={'请输入活动名称搜索'}
            />
            <div
              style={{
                display: 'flex',
                marginLeft: 16,
                textAlign: 'left',
              }}
            >
              <div style={{ width: '214px' }}>
                <Date
                  placeholder="开始日期 如:20221212"
                  onChange={(e: any) => {
                    console.log('e111==', e);
                    getList({ pageNum: 1, startTime: e ? moment(e).valueOf() : undefined });
                    setStartTime(e ? moment(e).valueOf() : undefined);
                  }}
                />
              </div>
              <div style={{ lineHeight: '33px' }}>至</div>
              <div style={{ width: '214px' }}>
                <Date
                  placeholder="结束日期 如:20221213"
                  onChange={(e: any) => {
                    console.log('e222==', e);
                    getList({ pageNum: 1, endTime: e ? moment(e).valueOf() : undefined });
                    setEndTime(e ? moment(e).valueOf() : undefined);
                  }}
                />
              </div>
            </div>
          </div>
        }
      />
      <RuiFilter
        data={filterData}
        // openCloseChange={() => setListHeight(this, 20)}
        onChange={filterChange}
      />
      <WhiteSpace />
      <WhiteSpace />
      <ListTable
        rowKey={'code'}
        columns={columns}
        data={listData}
        pagination={pagination}
        onPageChange={(page, pageSize) => {
          getList({ pageNum: page, pageSize });
        }}
      />
      <ExportInfo
        wrappedComponentRef={exportInfoRef}
        tableName={''}
        noModal={true}
        tableListQuery={{
          pageNum: pagination.current,
          pageSize: pagination.pageSize,
          orgCode,
          activityName,
          startTime,
          endTime,
          ...d158Code,
        }}
        action={'/api/org/life/outExportXlsx'}
      />
      <AddOrEdit
        ref={addOrEditRef}
        onOk={() => {
          getList({ pageNum: 1 });
        }}
      />
    </div>
  );
};
// @ts-ignore
export default connect(({ commonDict }) => ({ commonDict }))(index);
