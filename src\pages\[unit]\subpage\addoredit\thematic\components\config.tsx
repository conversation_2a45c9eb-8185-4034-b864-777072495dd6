// 村
export const village = [
  { name: '专调表1', key: '1', tableName: 'ccp_zt1_rural_party' },
  { name: '专调表2', key: '2', tableName: 'ccp_zt2_team_optimize' },
  { name: '专调表3', key: '3', tableName: 'ccp_zt3_develop_economy' },
];
// 城市街道社区
export const city = [
  { name: '专调表4', key: '4', tableName: 'ccp_zt4_basic_word' },
  { name: '专调表5', key: '5', tableName: 'ccp_zt5_community_party' },
];
// 高校
export const school = [
  // { name: '专调表7', key: '7' },
  { name: '专调表8', key: '8', tableName: 'ccp_zt78_university' }, //表78合并
];
// 医院
export const hospital = [{ name: '专调表9', key: '9' }];
// 公有制企业
export const publicUnit = [{ name: '专调表10', key: '10', tableName: 'ccp_zt10_state_enterprise' }];
// 非公有制企业
export const notPublicUnit = [
  { name: '专调表11', key: '11', tableName: 'ccp_zt11_non_public_enterprise' },
];
// 互联网行业
export const internet = [{ name: '专调表12', key: '12', tableName: 'ccp_zt12_internet_enterpris' }];
// 社会组织
export const social = [{ name: '专调表13', key: '13', tableName: 'ccp_zt13_social_organ' }];

// Form样式
//span=24
export const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
export const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 13 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 9 },
  },
};
// span=12
export const formItemLayout3 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 3 },
  },
};
