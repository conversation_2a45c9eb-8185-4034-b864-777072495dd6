import React, { Fragment, useState } from 'react';
import { Modal } from 'antd';
import ReactPlayer from 'react-player'

const transVideo = () => {
  const [visible, setVisible] = useState(false);
  const url = window.location.origin + '/operationVideo/transferVideo.mp4';
  const handleCancel = () => {
    setVisible(false);
  }
  return (
    <Fragment>
      <a onClick={() => {
        setVisible(true);
      }} style={{ color: 'red', borderBottom: '1px solid red' }}>关系转接操作指南</a>

      <Modal
        destroyOnClose
        title={"关系转接操作指南"}
        visible={visible}
        onCancel={handleCancel}
        footer={null}
        width="800px"
        maskClosable={false}>
        <ReactPlayer
          playing={true}
          width='100%'
          height='100%'
          controls
          // url={"https://172.16.23.40/operationVideo/transferVideo.mp4"}
          url={url}
          />
      </Modal>
    </Fragment>
  )
}

export default transVideo
