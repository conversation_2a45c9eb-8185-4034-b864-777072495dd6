// 已纳入流入地-操作-流回
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input, Button, Row } from 'antd';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import Date from '@/components/Date';
import Tip from '@/components/Tip';
import { getSession } from '@/utils/session';
import { outManageFlowBack, inManageFlowBack, getOutMemFlowMessageList, addOutMemFlowMessage } from '../../service/index';
import ListTable from '@/components/ListTable';

const FormItem = Form.Item;
const TextArea = Input.TextArea
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      memInfo: {},
      visible: false,
      timeKey: moment().valueOf(),
      confirmLoading: false,
      pagination: { page: 1, pageSize: 10, total: 0, current: 1 },
      MessagesList: [],
      modalType: 1,
      loading: false
    };
  }
  // handleOk = () => {
  //   const { onOk } = this.props;
  //   const { code } = this.state.memInfo;
  //   const { modalType = '' } = this.state;
  //   this.props.form.validateFieldsAndScroll(async (err, val) => {
  //     if (!err) {
  //       val.flowOutDate = moment(val.flowOutDate).valueOf();
  //       let url: any = undefined;
  //       let p: object = {};
  //       if (modalType === 'inFlow') {
  //         url = inManageFlowBack;
  //         p = { flowBackTime: val.flowOutDate };
  //       }
  //       if (modalType === 'outFlow') {
  //         url = outManageFlowBack;
  //         p = { flowOutDate: val.flowOutDate };
  //       }
  //       if (url) {
  //         this.setState(
  //           {
  //             confirmLoading: true,
  //           },
  //           async () => {
  //             const res = await url({ data: { code, ...p } });
  //             this.setState({
  //               confirmLoading: false,
  //             });
  //             if (res.code === 0) {
  //               this.handleCancel();
  //               if (res.data) {
  //                 Tip.error('操作提示', res.data || '操作失败');
  //               } else {
  //                 Tip.success('操作提示', '操作成功');
  //               }
  //               onOk && onOk();
  //             }
  //           },
  //         );
  //       }
  //     }
  //   });
  // };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  // 获取消息列表
  getListData = async () => {
    const { memInfo, pagination } = this.state
    if (memInfo) {
      const { code, data } = await getOutMemFlowMessageList({
        data: {
          memFlowCode: memInfo.code,
          pageNum: pagination.page,
          pageSize: pagination.pageSize
        }
      })
      if (code == 0) {
        console.log("444444444444444444444444444", data);
        this.setState({
          MessagesList: data.list,
          pagination: {
            page: data.pageNumber,
            total: data.totalRow,
            pageSize: data.pageSize,
            current: data.pageNumber
          }
        }, () => {
          console.log("111111111111111111", this.state.MessagesList);
        })
      }
    }
  }
  // 添加消息
  addMessage = async (message) => {
    if (!message) {
      Tip.error('操作提示', '内容不能为空');
      return
    }
    if (message.trim() == '') {
      console.log("🚀 ~ index ~ addMessage= ~ message:", message.trim() == '')
      Tip.error('操作提示', '内容不能为空');
      return
    }
    const { memInfo, modalType } = this.state
    const form = this.props.form
    this.setState({ loading: true })
    if (memInfo) {
      const { code, data } = await addOutMemFlowMessage({
        data: {
          memFlowCode: memInfo.code,
          message: message,
          type: modalType
        }
      })
      if (code == 0) {
        this.getListData()
        Tip.success('操作提示', '发送成功');
      }
    }
    form.resetFields()
    this.setState({ loading: false })
  }
  open = (type: string, record) => {
    // console.log("22222222222222222222222222", record);
    this.setState({ visible: true, memInfo: record, modalType: type, }, () => {
      this.getListData()
    });
  };
  destroy = () => {
    this.setState({
      memInfo: {},
    });
  };
  render() {
    const { form } = this.props;
    const { getFieldDecorator, setFieldsValue, getFieldValue } = form;
    // console.log("🚀 ~ index ~ render ~ form:", form)
    const { visible, confirmLoading, pagination, MessagesList, loading } = this.state;
    const columns = [
      {
        title: '账号',
        dataIndex: 'sendName',
        width: 200,
      },
      {
        title: '发送时间',
        dataIndex: 'sendTime',
        width: 200,
      },
      {
        title: '消息内容',
        dataIndex: 'message',
        width: 200,
        // render: (text, record) => {
        //   return (
        //     <div style={{ width: '100%', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
        //       {text}
        //     </div>
        //   )
        // }
      },
      // {
      //   title: '操作',
      //   dataIndex: 'action',
      //   align: 'center',
      //   width: 100,
      //   render: (text, record) => {
      //     return (
      //       <Fragment>
      //         <a
      //           onClick={() => {
      //           }}
      //         >
      //           已读
      //         </a>
      //       </Fragment>
      //     );
      //   },
      // },
    ]
    return (
      <Modal
        destroyOnClose
        footer={null}
        title="发送消息"
        visible={visible}
        // onOk={this.handleOk}
        maskClosable={false}
        onCancel={this.handleCancel}
        width={1000}
        confirmLoading={confirmLoading}
      >
        {visible && (
          <Fragment key={this.state.timeKey}>
            <ListTable
              scroll={{ y: 500 }}
              columns={columns}
              rowKey='code'
              data={MessagesList}
              pagination={pagination}
              onPageChange={(pageNum: any, pageSize: any) => {
                console.log("11111111111111111", pageNum, pageSize);
                this.setState({
                  pagination: {
                    ...pagination,
                    page: pageNum,
                    pageSize: pageSize
                  }
                }, () => {
                  this.getListData()
                })
              }} />
            <div style={{ height: '30px' }}></div>
            <Form>
              <FormItem label="消息输入框" colon={false}>
                {getFieldDecorator('message', {
                  rules: [{ required: true, message: '请输入消息' }],
                })(<TextArea rows={3} placeholder="请输入消息" maxLength={200}/>)}
              </FormItem>
              <Row justify="end">
                <Button htmlType={'button'} onClick={() => {
                  setFieldsValue({ message: undefined })
                }} loading={false}>清空</Button>
                <Button
                  htmlType={'button'} type='primary' onClick={() => {
                    this.addMessage(getFieldValue("message"))
                  }} loading={loading}
                  style={{ marginLeft: '20px' }}
                >发送</Button>
              </Row>
            </Form>
          </Fragment>
        )}
      </Modal>
    );
  }
}
export default Form.create()(index);
