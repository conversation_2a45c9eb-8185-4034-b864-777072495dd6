import request from '@/utils/request';
import qs from 'qs';
export async function logic(params) {
  return request('/api/logic', {
    method: 'POST',
    body: params
  });
}
export async function exports(params) {
  return request('/api/logic/export', {
    method: 'POST',
    body: params
  },'file');
}

export async function detail(params) {
  return request('/api/logic/detail', {
    method: 'POST',
    body: params
  });
}

export async function ignore(params) {
  return request('/api/logic/ignore', {
    method: 'POST',
    body: params
  });
}
export async function ignoreList(params) {
  return request('/api/logic/ignoreList', {
    method: 'POST',
    body: params
  });
}
export async function logicDel(params) {
  return request('/api/logic/del', {
    method: 'POST',
    body: params
  });
}

// 已忽略信息-导出  
export async function exportIgnoreList(params) {
  return request('/api/logic/exportIgnoreList', {
    method: 'POST',
    body: params
  },'file');
}