/**
 * 新增奖惩信息
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Cascader, Col, DatePicker, Input, Modal, Row } from "antd";
import { findParent, formLabel, isEmpty, jsonToTree } from '@/utils/method';
import DictTreeSelect from '@/components/DictTreeSelect';
import Tip from '@/components/Tip';
import moment from 'moment';
import Date from '@/components/Date';
import _isEqual from 'lodash/isEqual';

const FormItem=Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      visible:false,
      dict_d42:undefined,
      dict_d47:undefined,
      dict_d42Select:[],
      dict_d47Select:[],
    }
  }
  static getDerivedStateFromProps = (nextProps, prevState) => {
    let state:any = {};
    const {dataInfo = {}} = nextProps;
    const {_dataInfo = {}} = prevState;
    if(!_isEqual(dataInfo,_dataInfo)){
      const {d42Code = ''} = dataInfo;
      if(d42Code.startsWith('1') || d42Code === '91'){
        state['noDraw'] = ['92'];
      }else {
        state['noDraw'] = ['91'];
      }
      state['_dataInfo'] = dataInfo;
    }
    return state;
  };
  showModal=()=>{
    this.setState({
      visible:true,
    });
  };
  handleOk=()=>{
    const {dataInfo={}}=this.props;
    const {basicInfo={}}=this.props.org;
    this.props.form.validateFieldsAndScroll(async(err, val) => {
      console.log(err, val,basicInfo,'err, valerr, val');
      if (!err) {
        let obj=undefined;
        if(basicInfo['code']){
          val['rewardOrgCode']=basicInfo['orgCode']
        }
        ['d42Code','d47Code'].map(obj=>{
          let key=obj.split('C')[0];
          if(typeof val[obj] === 'object' ){
            val[`${key}Name`]=val[obj]['name'];
            val[obj]=val[obj]['key']
          }
        });
        if(val['startDate']){
          val['startDate']=val['startDate'].valueOf();
        }
        let type='org/addReward';
        if(dataInfo['code']){
          type='org/updateReward';
          // obj=await this.props.dispatch({
          //   type:'org/updateReward'
          //   payload:{
          //     data:{
          //       ...dataInfo,
          //       ...val,
          //       zbCode:basicInfo['zbCode'],
          //     }
          //   }
          // });
        }
        obj=await this.props.dispatch({
          type,
          payload:{
            data:{
              ...dataInfo,
              ...val,
              zbCode:basicInfo['zbCode'],
              orgCode:basicInfo['code'],
              rewardOrgCode:basicInfo['orgCode'],
            }
          }
        });
        if(obj && obj['code']===0){
          Tip.success('操作提示',dataInfo['code'] ? '修改成功' :'新增成功');
          this.handleCancel();
          this.props.queryList();
        }
      }
    });
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      dict_d42:undefined,
      dict_d47:undefined,
      dict_d42Select:[],
      dict_d47Select:[],
      noDraw:[],
      _dataInfo:undefined
    });
  };
  nameChange=(val)=>{
    if(val){
      const {key}=val;
      if(key.startsWith('1') || key==='91'){//表彰
        this.setState({
          noDraw:['92'],
        });
      }else{//惩罚
        this.setState({
          noDraw:['91'],
        });
      }
    }
    // this['d47Code'].clearAll();
    // this.props.form.setFieldsValue({d47Code:undefined})
  };
  getDictValue = (formKey) => {
    const { getFieldValue } = this.props.form;
    let obj = getFieldValue(formKey);
    let val = obj;
    if (typeof obj == 'object') {
      val = obj['key'];
    }
    return val;
  };
  render() {
    const { getFieldDecorator } = this.props.form;
    const {children,title,dataInfo={},tipMsg={}}=this.props;
    const {basicInfo={}}=this.props.org;
    const {noDraw=[],noKey}=this.state;
    // const {dict_d42Select,dict_d47Select}=this.state;
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        {
          this.state.visible &&
          <Modal
            title={ title || "请输入标题" }
            visible={this.state.visible}
            onOk={()=>this.handleOk()}
            onCancel={this.handleCancel}
            width={800}
            className='add_randp_modal'
            maskClosable={false}
          >
            <Form {...formItemLayout1}>
              <Row>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('批准日期',tipMsg['startDate']) }
                    {...formItemLayout}
                  >
                    {getFieldDecorator('startDate', {
                      initialValue:dataInfo['startDate'] ? moment(dataInfo['startDate']) : undefined,
                      rules: [{ required: true, message: '批准日期' }],
                      // <DatePicker placeholder="请选择" style={{width:'100%'}}/>
                    })(

                      <Date />
                    )}
                  </FormItem>
                </Col>
              </Row>

              <Row>
                <Col span={24}>
                  <FormItem
                    label={ formLabel('奖惩名称',tipMsg['d42Code']) }
                  >
                    {getFieldDecorator('d42Code', {
                      initialValue:dataInfo['d42Code'],
                      rules: [{ required: true, message: '请选择奖惩名称' }],
                    })(
                      <DictTreeSelect backType={'object'} onChange={this.nameChange} initValue={dataInfo['d42Code']} codeType={'dict_d42'} placeholder="请选择奖惩名称" parentDisable={true}/>
                    )}
                  </FormItem>
                </Col>
              </Row>

              <FormItem
                label={ formLabel('组织名称',tipMsg['rewardOrgCode']) }
              >
                {getFieldDecorator('rewardOrgCode', {
                  initialValue:basicInfo['name'],
                  rules: [{ required: true, message: '组织名称' }],
                })(
                <Input disabled={true}/>
                )}
              </FormItem>

              {/* <FormItem
                label={ formLabel('奖惩原因',tipMsg['d47Code']) }
              >
                {getFieldDecorator('d47Code', {
                  initialValue:dataInfo['d47Code'],
                  rules: [{ required: true, message: '请选择奖惩原因' }],
                })(
                  <DictTreeSelect backType={'object'}
                                  ref={e=>this['d47Code']=e}
                                  initValue={dataInfo['d47Code']}
                                  codeType={'dict_d47'}
                                  placeholder="请选择奖惩原因"
                                  noDraw={noDraw}
                                  filter={(data:any)=> {
                                    const {key} = this.state.d42CodeValue || {key:''};
                                    const [str = ''] = noDraw;
                                    if(str == '92'){
                                     return data.filter(it => !((it?.key||'').startsWith('2')))
                                    }else if(isEmpty(noDraw)){
                                      return data;
                                    }else {
                                      return data.filter(it => !((it?.key||'').startsWith('1') ))
                                    }
                                  }}
                                  parentDisable={true}/>
                )}
              </FormItem> */}

              <FormItem
                label={ formLabel('奖惩依据',tipMsg['remark']) }
                {...formItemLayout1}
              >
                {getFieldDecorator('remark', {
                  initialValue:dataInfo['remark'],
                  rules: [{ required: false, message: '' }],
                })(
                  <TextArea rows={4} />
                )}
              </FormItem>

              {/* {(function(_this) {
                let d42Code = _this.getDictValue('d42Code');
                if(`${d42Code}`.startsWith('1')){
                  return (
                    <FormItem
                      label={ formLabel('奖励批准单位名称',tipMsg['unitName']) }
                    >
                      {getFieldDecorator('unitName', {
                        initialValue:basicInfo['unitName'],
                        rules: [{ required: true, message: '奖励批准单位名称' }],
                      })(
                      <Input/>
                      )}
                    </FormItem>
                  )
                }
              })(this)} */}
              <FormItem
                      label={ formLabel('奖惩批准单位名称',tipMsg['approveOrgan']) }
                    >
                      {getFieldDecorator('approveOrgan', {
                        initialValue:dataInfo['approveOrgan'],
                        rules: [{ required: true, message: '奖惩批准单位名称' }],
                      })(
                      <Input/>
                      )}
                    </FormItem>
            </Form>
          </Modal>
        }

      </React.Fragment>
    )
  }
}
//@ts-ignore
export default Form.create<any>()(index);
