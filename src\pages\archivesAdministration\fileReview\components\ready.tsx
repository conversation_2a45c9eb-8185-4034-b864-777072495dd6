import React, { useState, Fragment, useEffect, useRef } from 'react';
import _isEmpty from 'lodash/isEmpty';
import style from './index.less';
import { _history } from "@/utils/method";
import qs from 'qs';
import { Tabs, Input, Modal, Form, Radio, Descriptions, Button, Divider } from 'antd';
import ListTable from 'src/components/ListTable';
import { getMemAuditList, auditMem } from '../../service'
import { getSession } from '@/utils/session';
import NowOrg from 'src/components/NowOrg';
import moment from 'moment'
import tip from '@/components/Tip';
import ElectronicArchives from '@/pages/developMem/zy/components/electronicArchives'


const Search = Input.Search;

const TabPane = Tabs.TabPane;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};
function Index(props: any) {
    const [filterHeight, setFilterHeight] = useState();
    const [loading, setLoading] = useState(false);
    const [list, setList] = useState([]);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [memName, setMemName] = useState('')
    const [visible, setVisible] = useState(false)
    const [confirmLoading, setConfirmLoading] = useState(false)
    const [statu, setStatu] = useState(null)
    const [digitalLotNo, setDigitalLotNo] = useState('')
    const [visible1, setVisible1] = useState(false)
    const [reason, setReason] = useState('')
    const electronicArchives = useRef()
    const [form] = Form.useForm();
    const { location: { pathname = '' } = {} } = _history
    const org = getSession('org') || {};
    const columns = [
        {
            title: '序号',
            dataIndex: 'num',
            width: 60,
            align: 'center',
            render: (text, record, index) => {
                return (pagination['current'] - 1) * pagination['pageSize'] + index + 1
            }
        },
        {
            title: '党员姓名',
            align: 'center',
            dataIndex: 'name',
            width: 100,
        },
        {
            title: '性别',
            dataIndex: 'sexName',
            width: 80,
            align: 'center'
        },
        {
            title: '党员类型',
            dataIndex: 'd08Name',
            width: 100,
            align: 'center'
        },
        {
            title: '所在党支部',
            dataIndex: 'orgName',
            width: 300,
            // render: (text, record) => {
            //     return (
            //         <Fragment>
            //             <a onClick={() => { }}>{text}</a>
            //         </Fragment>
            //     )
            // },
        },
        {
            title: '申请时间',
            dataIndex: 'createTime',
            width: 100,
            align: 'center',
            render: (text, record) => {
                return text ? moment(text).format('YYYY-MM-DD') : ''
            }
        },
        {
            title: '审核状态',
            dataIndex: 'auditStatus',
            width: 100,
            align: 'center',
            render: (text, record) => {
                return (
                    <Fragment>
                        {
                            text == 1 && <span>未审核</span>
                        }
                        {
                            text == 2 && <span>审核通过</span>
                        }
                        {
                            text == 3 && <span>审核未通过</span>
                        }
                    </Fragment>
                )
            }
        },
        {
            title: '操作',
            dataIndex: 'action',
            width: 100,
            render: (text, record) => {
                return (
                    <Fragment>
                        <a onClick={() => digitalArchives(record)}>电子档案</a>
                        <Divider type="vertical" />
                        {
                            record?.approve && <a onClick={() => {
                                setVisible(true)
                                setDigitalLotNo(record?.digitalLotNo)
                            }}>审核</a>}
                        {
                            record?.auditStatus == 3 && <a onClick={() => {
                                setVisible1(true)
                                setReason(record?.reason)
                            }}>查看未通过原因</a>
                        }
                    </Fragment>

                )
            }
        }
    ]
    const digitalArchives = async (record) => {
        electronicArchives.current.showModal(record?.code);
    }
    const searchClear = (e) => {
        setMemName(e.target.value)
    }
    const search = (value) => {
        getList({ pageNum: 1, memName: value })
    }
    const getList = async (p?: any) => {
        setLoading(true)
        const { code = 500, data: { list = [], pageNumber = '', pageSize = '', totalRow = '' } = {} } = await getMemAuditList({ data: { memOrgCode: org['orgCode'], memName, pageNum: 1, pageSize: 10, ...p } })
        setLoading(false)
        if (code == 0) {
            setList(list)
            setPagination({ current: pageNumber, pageSize: pageSize, total: totalRow })
        }

    }
    const handleOk = () => {
        form.submit()
    }
    const handleCancel = () => {
        form.resetFields()
        setStatu(null)
        setVisible(false)
    }
    const handleCancel1 = () => {
        setVisible1(false)
    }
    const hadndleFinish = async (value) => {
        setConfirmLoading(true)
        const { code = 500 } = await auditMem({ data: { digitalLotNo, ...value } })
        if (code == 0) {
            tip.success('操作提示', '操作成功')
            getList()
            handleCancel()
        }
        setConfirmLoading(false)
    }

    useEffect(() => {
        getList({ pageNum: 1, pageSize: 10, memName })
    }, [org['code']])
    return (
        <div>
            <NowOrg extra={
                <React.Fragment>
                    <Search style={{ width: 200, marginLeft: 16 }} onSearch={search} onChange={searchClear} placeholder={'请输入检索关键词'} />
                </React.Fragment>
            } />
            <ListTable scroll={{ y: filterHeight }}
             columns={columns} data={list} pagination={pagination} onPageChange={(page, pageSize) => {
                getList({ pageNum: page, pageSize });
            }} />
            <Modal
                title={'审核'}
                visible={visible}
                onOk={handleOk}
                onCancel={handleCancel}
                confirmLoading={confirmLoading}
            >
                <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
                    <Form.Item
                        label="审核状态"
                        name={'status'}
                        rules={[{ required: true, message: '请选择审核状态' }]}
                    >
                        <Radio.Group onChange={(e) => {
                            setStatu(e.target.value)
                        }}>
                            <Radio value={1}>通过</Radio>
                            <Radio value={0}>不通过</Radio>
                        </Radio.Group>
                    </Form.Item>
                    {
                        function () {
                            console.log(statu, 's')
                            if (statu == 0) {
                                return (
                                    <Form.Item
                                        label="不通过原因"
                                        name={'reason'}
                                        rules={[{ required: true, message: '请填写不通过原因' }]}
                                    >
                                        <Input.TextArea rows={2} />
                                    </Form.Item>
                                )
                            }
                        }()
                    }

                </Form>
            </Modal>
            <Modal
                title={'查看'}
                visible={visible1}
                onCancel={handleCancel1}
                footer={[
                    <Button key="back" onClick={handleCancel1}>关闭</Button>,
                ]}
            >
                <Descriptions title="" column={1}>
                    <Descriptions.Item label="不通过原因">{reason}</Descriptions.Item>
                </Descriptions>
            </Modal>
            <ElectronicArchives ref={electronicArchives} />
        </div>
    );
}
export default Index