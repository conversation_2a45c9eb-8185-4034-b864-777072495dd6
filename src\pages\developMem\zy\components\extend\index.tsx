import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import style from './index.less';
import NowOrg from '@/components/NowOrg';
import { Button, Form, Alert, Modal, Space, Radio, Input } from 'antd';
import ListTable from '@/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import SpinProgress from '@/components/SpinProgress';
import { processNodeNext, developExtendApproval, findDevelopProcess } from '@/pages/developMem/services'
import Tip from '@/components/Tip'
import Date from '@/components/Date';
import moment from 'moment'
const { TextArea } = Input;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};

const index: any = (props, ref) => {
    const {
        tableAction,
        FormComp,
        tableColumns,
        otherBtnsFunc,
        getSearchCallBack,
        progressCallback,
        otherRenderTableColumnsFunc,
        renderTableNumber,
        isDefaultForm = true,
        rowKey = 'id'
    } = props;

    useImperativeHandle(ref, () => ({
        showModal: (obj) => {
            open(obj);
        }
    }));
    const options = [
        { label: '半个月', value: '9' },
        { label: '一个月', value: '1' },
        { label: '两个月', value: '2' },
        { label: '三个月', value: '3' },
        { label: '自定义', value: '0' },
    ];
    const org: any = getSession('org') || {};
    const [form] = Form.useForm();
    const { pathname, query: urlQuery = {} } = window['g_history']?.location || {};

    const [loading, setLoading] = useState(false);

    const [modalVisible, setModalVisible] = useState(false);
    const [isMark, setIsMark] = useState('')
    const [row, setRow] = useState<any>({})
    const [info, setInfo] = useState<any>({})

    const open = (obj) => {
        setRow(obj)
        if (obj.processNode === 'FZ_6_4') {
            getData(obj)
        }

        setModalVisible(true)
    }
    const getData = async (params) => {
        const { code = 500, data = {} } = await findDevelopProcess({
            data: {
                digitalLotNo: params.digitalLotNo,
                processNode: params.processNode,
            }
        })
        if (code === 0) {
            setInfo(data)
        }
        console.log('🚀 ~ code:', data)
    }
    const cancel = () => {
        form.resetFields()
        setIsMark('')
        setRow({})
        setModalVisible(false)
    }
    const hadndleFinish = async (e) => {
        const { onoK } = props
        e['extendEndTime'] = moment(e['extendEndTime']).valueOf()
        const { code = 500, data = {} } = await developExtendApproval({
            data: {
                memCode: row.code,
                digitalLotNo: row.digitalLotNo,
                processNode: row.processNode,
                ...e
            }
        });
        setLoading(false);
        if (code == 0) {
            Tip.success('操作提示', '操作成功');
            cancel()
            onoK && onoK()
        }

    };
    const onChange1 = (e) => {
        console.log(e, 'eee')
        setIsMark(e.target.value)
    }
    useEffect(() => {

    }, []);


    return (
        <Modal
            title={<div style={{ width: '100%', textAlign: 'center' }}>{row.processNode !== 'FZ_6_4' ? '延长审批' : '延长说明'}</div>}
            destroyOnClose
            visible={modalVisible}
            onCancel={cancel}
            width={'600px'}
            // onOk={()=>{
            //     form.submit();
            // }}
            footer={false}
        >
            <Form form={form} onFinish={hadndleFinish}  {...formItemLayout}>
                {
                    row.processNode !== 'FZ_6_4' ?
                        <React.Fragment>
                            <Form.Item
                                name="isMark"
                                label="延长时间"
                                rules={[{ required: true, message: '请输入延长时间' }]}
                            >
                                <Radio.Group options={options} onChange={onChange1} />
                            </Form.Item>
                            {
                                isMark == '0' &&
                                <Form.Item
                                    name="extendEndTime"
                                    label="自定义延长时间"
                                    rules={[{ required: true, message: '请输入自定义延长时间' }]}
                                >
                                    <Date isDefaultEnd={false}/>
                                </Form.Item>
                            }

                            <Form.Item
                                name="extendApproveExplain"
                                label="延长审批说明"
                                rules={[{ required: true, message: '请输入延长审批说明' }]}
                            >
                                <TextArea rows={2} />
                            </Form.Item>
                        </React.Fragment>
                        :
                        <React.Fragment>
                            <Form.Item
                                name=""
                                label="延长时间"
                                rules={[{ required: false, message: '请输入延长时间' }]}
                            >
                                <span>{info.extendEndTimeStr}</span>
                            </Form.Item>
                            <Form.Item
                                name=""
                                label="延长审批说明"
                                rules={[{ required: false, message: '请输入延长审批说明' }]}
                            >
                                <span>{info.extendApproveExplain}</span>
                            </Form.Item>
                        </React.Fragment>
                }

            </Form>
            <div style={{ textAlign: 'center' }}>
                <Space>
                    <Button onClick={cancel}>取消</Button>
                    {
                        row.processNode !== 'FZ_6_4' &&
                        <Button onClick={() => {
                            setLoading(true);
                            form.submit();
                        }} type="primary">确认</Button>
                    }
                </Space>
            </div>
        </Modal>

    );
};
// @ts-ignore
export default React.forwardRef(index);
