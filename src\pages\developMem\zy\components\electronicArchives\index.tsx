import React, { useState, Fragment, useEffect, useImperativeHandle, useCallback, useRef } from 'react';
import _isEmpty from 'lodash/isEmpty';
import './index.less';
import { _history } from '@/utils/method';
import qs from 'qs';
import { Modal, Breadcrumb, Button, Empty } from 'antd';
import HTMLFlipBook from 'react-pageflip';
import Book from './book';
import moment from 'moment';
import { electronic } from '@/pages/developMem/services';
import PreviewImg from '@/components/PreviewImg';
import imgsss from '@/assets/mm.jpeg';
import { MessageOutlined } from '@ant-design/icons';

const Index = (props: any, ref: any) => {
  useImperativeHandle(ref, () => ({
    showModal: (code, transferId) => {
      setVisible(true);
      getInfo(code, transferId);
    },
  }));
  const [checked, setChecked]: any = useState(0);
  const [visible, setVisible] = useState(false);
  const [baseInfo, setBaseInfo] = useState<any>({});
  const [menu, setMenu] = useState<any>([]);
  const [menuArr, setMenuArr] = useState<any>([]);
  // const [currentMenu, setCurrentMenu]: any = useState<any>([])
  const [breadcrumb, setBreadcrumb] = useState<any>({ left: [], right: [] });
  const [currentPage, setCurrentPage] = useState<any>(0);
  const [data, setData] = useState([
    {
      name: '入党申请阶段',
      d08Code: '5',
      bc: '#911919',
      bc1: '#F12A2A',
      list: [],
      key: 0,
      start: 0,
    },
    {
      name: '积极分子阶段',
      d08Code: '4',
      bc: '#996204',
      bc1: '#FFA406',
      list: [],
      key: 1,
      start: 0,
    },
    {
      name: '发展党员阶段',
      d08Code: '3',
      bc: '#046120',
      bc1: '#06A235',
      list: [],
      key: 2,
      start: 0,
    },
    // {
    //     name: '预备党员阶段',
    //     d08Code: '4',
    //     bc: '#146778' ,
    //     bc1: '#21ACC8'
    // },
    {
      name: '预备党员阶段',
      d08Code: '2',
      bc: '#055096',
      bc1: '#0986FA',
      list: [],
      key: 3,
      start: 0,
    },
    {
      name: '其他',
      d08Code: '-1',
      bc: '#055096',
      bc1: '#0986FA',
      list: [],
      key: 4,
      start: 0,
    },
  ]);
  const bookRef = useRef(null);
  const previewImg = useRef(null);
  //是否显示审核批准 showAnnotation
  const { onChange, showAnnotation = false } = props;

  const { location: { pathname = '' } = {} } = _history;
  const findLastIndex = (arr, func) => {
    let index = arr.length;
    while (index--) {
      if (func(arr[index])) {
        return index;
      }
    }
    return -1;
  };
  const getInfo = async (memCode = '', transferId = '') => {
    const { code = 500, data: { mem = {}, phaseMap = {} } = {} } = await electronic({ memCode, transferId });
    if (code == 0) {
      setBaseInfo(mem);
      let arr: any = [];
      for (let o in phaseMap) {
        if (phaseMap[o].length > 0) {
          phaseMap[o].map((i) => {
            let find = data.find((i) => i.d08Code == o);
            if (i['digitalList'].length > 0) {
              i['digitalList'].map((j, k) => {
                j['d08Code'] = o;
                j['jd'] = find?.name || '';
                j['key'] = find?.key || '';
                j['processNode'] = j['processNode'] || '';
                arr.push(j);
              });
            } else {
              arr.push({
                d08Code: o,
                jd: find?.name || '',
                key: find?.key || '',
                processNode: '',
                path: '',
                d222Name: i['d222Name'],
              });
            }
          });
        }
      }
      let _arr = arr.sort((a, b) => b['d08Code'] * 1 - a['d08Code'] * 1);
      console.log(
        arr.sort((a, b) => b['d08Code'] * 1 - a['d08Code'] * 1),
        '原始数组',
      );
      // let rdpage = _arr.findLastIndex(i => i['d08Code'] == '5')
      // let jjpage = _arr.findIndex(i => i['d08Code'] == '4')
      // let jjpage1 = _arr.findLastIndex(i => i['d08Code'] == '4')
      // let fzpage = _arr.findIndex(i => i['d08Code'] == '3')
      // let fzpage1 = _arr.findLastIndex(i => i['d08Code'] == '3')
      // let dypage = _arr.findIndex(i => i['d08Code'] == '2')
      // let dypage1 = _arr.findLastIndex(i => i['d08Code'] == '2')
      // console.log(jjpage, jjpage1, fzpage, fzpage1, dypage, dypage1, '分页')
      // if (jjpage == -1) {
      //     _arr.splice(rdpage + 1, 0, { path: '', jd: '积极分子阶段', d222Name: '', key: 1, d08Code: '4' }, { path: '', jd: '积极分子阶段', d222Name: '', key: 1, d08Code: '4' })
      // }
      // if (fzpage == -1) {
      //     _arr.splice(jjpage1 + 1, 0, { path: '', jd: '发展党员阶段', d222Name: '', key: 2, d08Code: '3' }, { path: '', jd: '发展党员阶段', d222Name: '', key: 2, d08Code: '3' })
      // }
      // if (dypage == -1) {
      //     _arr.splice(fzpage1 + 1, 0, { path: '', jd: '预备党员阶段', d222Name: '', key: 3, d08Code: '2' }, { path: '', jd: '预备党员阶段', d222Name: '', key: 3, d08Code: '2' })
      // }
      _arr.unshift({ jd: '入党申请阶段', d222Name: '基础信息', key: 0 });
      console.log(_arr, '_arr');
      let _jjpage = _arr.findIndex((i) => i['d08Code'] == '4') + 1;
      let _fzpage = _arr.findIndex((i) => i['d08Code'] == '3') + 1;
      let _dypage = _arr.findIndex((i) => i['d08Code'] == '2') + 1;
      let _qtpage = _arr.findIndex((i) => i['d08Code'] == '-1') + 1;
      console.log(_jjpage, _jjpage % 2 != 0 ? _jjpage - 1 : _jjpage, '分页11111111111');
      let d08 = _arr.map((j) => j.d08Code);
      let _d = [
        {
          name: '入党申请阶段',
          d08Code: '5',
          bc: '#911919',
          bc1: '#F12A2A',
          list: [],
          key: 0,
          start: 0,
        },
        {
          name: '积极分子阶段',
          d08Code: '4',
          bc: '#996204',
          bc1: '#FFA406',
          list: [],
          key: 1,
          start: _jjpage % 2 != 0 ? _jjpage - 1 : _jjpage,
        },
        {
          name: '发展党员阶段',
          d08Code: '3',
          bc: '#046120',
          bc1: '#06A235',
          list: [],
          key: 2,
          start: _fzpage % 2 != 0 ? _fzpage - 1 : _fzpage,
        },
        // {
        //     name: '预备党员阶段',
        //     d08Code: '4',
        //     bc: '#146778' ,
        //     bc1: '#21ACC8'
        // },
        {
          name: '预备党员阶段',
          d08Code: '2',
          bc: '#055096',
          bc1: '#0986FA',
          list: [],
          key: 3,
          start: _dypage % 2 != 0 ? _dypage - 1 : _dypage,
        },
        {
          name: '其他',
          d08Code: '-1',
          bc: '#720596',
          bc1: '#c30eff',
          list: [],
          key: 4,
          start: _qtpage % 2 != 0 ? _qtpage - 1 : _qtpage,
        },
      ].filter((i) => d08.includes(i.d08Code));

      setData(_d);
      setMenu(_arr);
      setMenuArr(_arr);
      setBreadcrumb({
        left: [_arr?.[0]?.['jd'], _arr?.[0]?.['d222Name']],
        right: [_arr?.[1]?.['jd'], _arr?.[1]?.['d222Name']],
      });
    }
  };
  const changemenus = (item, index) => {
    //点击目录跳转  取目录开始值
    console.log(item, index, menuArr, '1212121');
    if (index != checked) {
      setChecked(index);
      bookRef.current && bookRef.current.pageFlip().turnToPage(item.start);
      setCurrentPage(item.start);
      if (bookRef.current) {
        bookRef.current.pageFlip().flip(item.start);
      }
      setBreadcrumb({
        left: [menuArr[item.start]?.['jd'], menuArr[item.start]?.['d222Name']],
        right: [menuArr[item.start + 1]?.['jd'], menuArr[item.start + 1]?.['d222Name']],
      });
    }
  };
  const nextpage = () => {
    //下一页为双页跳转 当前页码+2  t为总页数
    let c = currentPage + 2;
    let t = bookRef.current.pageFlip().getPageCount();
    console.log(c, t, currentPage, menuArr, 'cccccccc');
    if (c < t) {
      //如果当前跳转后页面小于等于最大页面数则跳转
      // setBreadcrumb({
      //     left: [menuArr[c]?.['jd'], menuArr[c]?.['name']],
      //     right: [menuArr[c + 1]?.['jd'], menuArr[c + 1]?.['name']]
      // })
      // setChecked(menuArr[c]?.['key']) //获取下一页目录key值
      // setCurrentPage(c)

      setTimeout(() => {
        setCurrentPage(c);
        setBreadcrumb({
          left: [menuArr[c]?.['jd'], menuArr[c]?.['d222Name']],
          right: [menuArr[c + 1]?.['jd'], menuArr[c + 1]?.['d222Name']],
        });
        setChecked(menuArr[c]?.['key']); //获取上一页目录key值
        if (bookRef.current) {
          bookRef.current.pageFlip().flip(c);
        }
      }, 500);
      // bookRef.current && bookRef.current.pageFlip().flipNext()
    }
  };
  const prevpage = () => {
    let c = currentPage - 2;
    console.log(c, menuArr, 'cccccccc');

    if (c >= 0) {
      //如果当前跳转后页码等于0 currentPage小于0时则没有上一页了，跳转到第一页
      // setBreadcrumb([menuArr[currentPage]?.['jd'], menuArr[currentPage]?.['d222Name']])
      // bookRef.current && bookRef.current.pageFlip().flipPrev()
      setTimeout(() => {
        setCurrentPage(c);
        setBreadcrumb({
          left: [menuArr[c]?.['jd'], menuArr[c]?.['d222Name']],
          right: [menuArr[c + 1]?.['jd'], menuArr[c + 1]?.['d222Name']],
        });
        setChecked(menuArr[c]?.['key']); //获取上一页目录key值
        if (bookRef.current) {
          bookRef.current.pageFlip().flip(c);
        }
      }, 500);
    }
    // else {
    //     setCurrentPage(0)
    //     bookRef.current && bookRef.current.pageFlip().flip(0)
    //     setChecked(0)
    //     setBreadcrumb({
    //         left: [menuArr[0]?.['jd'], menuArr[0]?.['d222Name']],
    //         right: [menuArr[1]?.['jd'], menuArr[1]?.['d222Name']]
    //     })
    // }
  };
  const onFlip = (e) => {
    console.log('Current page: ', e);
    // let find = menu[e.data - 1] //当前页第一张
    // setBreadcrumb([find?.['jd'], find?.['d222Name']])
    // if (find.key != checked) {
    //     setChecked(find.key)
    // }

    // setCurrentPage(e.data)
    // if (e.data == 0) {
    //     setBreadcrumb([currentMenu[0]['jd'], currentMenu[0]['d222Name']])
    // } else {
    //     setBreadcrumb([currentMenu[e.data - 1]['jd'], currentMenu[e.data - 1]['d222Name']])
    // }
  };
  const onCancel = () => {
    bookRef.current && bookRef.current.pageFlip().turnToPage(0);
    // bookRef.current && bookRef.current.pageFlip().destroy()
    setCurrentPage(0);
    setBreadcrumb({ left: [], right: [] });
    // setCurrentMenu([])
    setMenu([]);
    setMenuArr([]);
    setBaseInfo({});
    setChecked(0);
    setVisible(false);
  };
  const imgpreview = (url) => {
    console.log('url====', url);
    previewImg && previewImg.current.showModal(url);
  };
  const handleimg = (id) => {
    let canvas = document.createElement('canvas');
    const user = JSON.parse(sessionStorage.getItem('user') || '{}');
    canvas.width = 600;
    canvas.height = 656;
    let context: any = canvas.getContext('2d');
    // 设置字体
    context.font = '22px normal';
    // 设置颜色
    context.fillStyle = '#4c4747';
    // context.rotate((-35 * Math.PI) / 180);
    context.translate(300, 300); // 移动到中心点
    context.rotate(-(Math.PI / 4)); // 旋转45度
    // 设置水平对齐方式
    context.textAlign = 'left';
    // 设置垂直对齐方式
    context.textBaseline = 'top';
    // 绘制文字（参数：要写的字，x坐标，y坐标）
    context.fillText(`本档案仅供${user.account}账户${moment().format('YYYY年MM月DD日')}阅览使用,不具备任何形式的法律效应`, -400, 0);
    let watermark: any = document.getElementById(id);
    if (watermark) {
      watermark.style = `background: url(${context.canvas.toDataURL()});pointer-events:none;opacity:.4;position: absolute;z-index: 9;top: 0;left: 0;width: 600px;height: 656px;margin-top: 25px;margin-left: 12px`;
    }
  };
  useEffect(() => {}, []);
  return (
    <React.Fragment>
      <Modal title="" visible={visible} footer={false} width={1344} onCancel={onCancel} zIndex={9999} destroyOnClose wrapClassName="electronicArchives">
        <div className="bookbody">
          <div className="bookmenus">
            <div className="item" style={{ backgroundColor: '#554EED', color: '#fff' }}>
              目录
            </div>
            {data.map((item: any, index: number) => {
              return (
                <div
                  className="item"
                  style={checked == index ? { backgroundColor: item.bc1, color: '#fff', cursor: 'pointer' } : { backgroundColor: item.bc, color: '#000', cursor: 'pointer' }}
                  onClick={() => changemenus(item, index)}
                >
                  {item.name}
                </div>
              );
            })}
          </div>
          <div className="bookcard">
            <div className="Breadcrumb">
              <div className="Breadcrumb_left">
                <Breadcrumb>
                  {breadcrumb?.left.map((item, index) => {
                    return (
                      <Breadcrumb.Item key={index}>
                        {item}
                        <div className={'annotation'}>
                          <MessageOutlined />
                          <span>插入审批标注</span>
                        </div>
                      </Breadcrumb.Item>
                    );
                  })}
                </Breadcrumb>
              </div>
              <div className="Breadcrumb_right">
                <Breadcrumb>
                  {breadcrumb?.right.map((item, index) => {
                    return (
                      <Breadcrumb.Item key={index}>
                        {item}
                        <div className={'annotation'} style={{ right: 120 }}>
                          <MessageOutlined />
                          <span>插入审批标注</span>
                        </div>
                      </Breadcrumb.Item>
                    );
                  })}
                </Breadcrumb>
              </div>
            </div>
            <div className="bookcardbox">
              <HTMLFlipBook width={625} height={680} drawShadow={true} autoSize={true} size={'fixed'} maxShadowOpacity={1} useMouseEvents={false} ref={bookRef} onFlip={onFlip}>
                {menu &&
                  menu.map((item, index) => {
                    if (index == 0) {
                      return (
                        <Book key={index} number={0} ids="0">
                          <div className="tables">
                            <table>
                              <colgroup>
                                <col style={{ width: 160 }} />
                                <col style={{ width: 160 }} />
                                <col style={{ width: 160 }} />
                                <col style={{ width: 160 }} />
                                <col style={{ width: 160 }} />
                                <col style={{ width: 160 }} />
                              </colgroup>
                              <tbody>
                                <tr>
                                  <td colSpan={6} className="label" style={{ textAlign: 'center', color: '#3D3D3D', fontWeight: 'bold' }}>
                                    {baseInfo?.name}基本信息
                                  </td>
                                </tr>
                                <tr>
                                  <td className="label">人员姓名</td>
                                  <td colSpan={2}>{baseInfo?.name}</td>
                                  <td className="label">性别</td>
                                  <td colSpan={2}>{baseInfo?.sexName}</td>
                                </tr>
                                <tr>
                                  <td className="label">出生日期</td>
                                  <td colSpan={2}>{baseInfo?.birthday && moment(baseInfo?.birthday).format('YYYY-MM-DD')}</td>
                                  <td className="label">籍贯</td>
                                  <td colSpan={2}>{baseInfo?.d48Name}</td>
                                </tr>
                                <tr>
                                  <td className="label">民族</td>
                                  <td colSpan={2}>{baseInfo?.d06Name}</td>
                                  <td className="label">当前工作岗位</td>
                                  <td colSpan={2}>{baseInfo?.d09Name}</td>
                                </tr>
                                <tr>
                                  <td className="label">当前学历情况</td>
                                  <td colSpan={2}>{baseInfo?.d07Name}</td>
                                  <td className="label">知识分子情况</td>
                                  <td colSpan={2}>{baseInfo?.d154Name}</td>
                                </tr>
                                <tr>
                                  <td className="label">{baseInfo?.d08Code == '2' || baseInfo?.d08Code == '1' ? '其他政治面貌' : '政治面貌'}</td>
                                  <td colSpan={2}>{baseInfo?.d89Name}</td>
                                  <td className="label">工作性质</td>
                                  <td colSpan={2}>{baseInfo?.jobNatureCode || baseInfo?.jobNatureName}</td>
                                </tr>
                                <tr>
                                  <td className="label">一线情况</td>
                                  <td colSpan={2}>{baseInfo?.d21Name}</td>
                                  <td className="label">
                                    人事关系是否在
                                    <br />
                                    党组织关联单位
                                  </td>
                                  <td colSpan={2}>{baseInfo?.hasUnitStatistics == '1' ? '是' : '否'}</td>
                                </tr>
                                <tr>
                                  <td className="label">现居住地</td>
                                  <td colSpan={5}>
                                    <div>{baseInfo?.homeAddress}</div>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </Book>
                      );
                    } else {
                      return (
                        <Book key={index} number={index} ids={index}>
                          {item['path'] ? (
                            <React.Fragment>
                              <div id={`watermark${index}`}></div>
                              {index <= currentPage + 3 && index >= currentPage - 3 && (
                                <img
                                  className="imgs"
                                  src={`${window.location.origin}/${item['path']}`}
                                  onDoubleClick={() => imgpreview(`${window.location.origin}/${item['path']}`)}
                                  onLoad={() => handleimg(`watermark${index}`)}
                                />
                              )}
                            </React.Fragment>
                          ) : (
                            <div style={{ width: 600, height: 600, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                              <Empty />
                            </div>
                          )}
                          {/* <p style={{position: 'absolute',left: '300px',bottom: '20px'}}>{index}</p> */}
                          {/* <div id={`watermark${index}`}></div>
                                                    <img
                                                        className='imgs'
                                                        onDoubleClick={() => imgpreview(`${window.location.origin}/${item['path']}`)}
                                                        src={imgsss}
                                                        onLoad={() => handleimg(`watermark${index}`)} /> */}
                        </Book>
                      );
                    }
                  })}
              </HTMLFlipBook>
              <div className="spine"></div>
            </div>
            <div className="pages">
              <Button style={{ marginLeft: '60px' }} onClick={prevpage}>
                上一页
              </Button>
              <Button style={{ marginRight: '60px' }} onClick={nextpage}>
                下一页
              </Button>
            </div>
          </div>
        </div>
      </Modal>
      <PreviewImg ref={previewImg} />
    </React.Fragment>
  );
};
// @ts-ignore
export default React.forwardRef(Index);
