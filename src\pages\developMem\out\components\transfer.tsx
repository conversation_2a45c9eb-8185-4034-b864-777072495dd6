/**
 * 模块名
 */
import React from 'react';
import {connect} from "dva";
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Modal, Radio, Row, Input, Switch, InputNumber, AutoComplete, Divider, Select, Button, Popconfirm } from 'antd';
import OrgSelect from "@/components/OrgSelect";
import MemSelect from "@/components/MemSelect";
import DictArea from '@/components/DictArea';
import ListTable from "@/components/ListTable";
import Tip from '@/components/Tip';
import {root} from '@/common/config.js';
import SearchOrg from '@/components/SearchOrg';
import Dates from '@/components/Date';
import DictTreeSelect from '@/components/DictTreeSelect';
import { findOutsideOrgByName } from '@/services';
import _get from 'lodash/get';
import { findDictCodeName} from '@/utils/method.js';
import { getProvinces } from '../../services'
const {MonthPicker}=DatePicker;
const TextArea=Input.TextArea;
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

@connect((({memDevelop,loading})=>({memDevelop,memTrans:loading.effects['memDevelop/transferMem'],orgTrans:loading.effects['memDevelop/addTransfer']})),undefined,undefined,{forwardRef:true})
class TransferBet extends React.Component<any,any>{
  static open(){}
  static close(){}
  constructor(props){
    super(props);
    this.state={
      visible:false,
      key:new Date().valueOf(),
      showList:false,
      unitList:[],
      loading:false,
      pagination:{current:'1',pageSize:'10',pageNum:'1'}
    };
    TransferBet.open=this.open;
  }
  handleOk=()=>{
    const {memData}=this.state;
    this.props.form.validateFieldsAndScroll(async (err,val)=>{
      console.log(val,'v123456');
      if(!err){
        // 增加字典表的name
        val = findDictCodeName(
          ['d146'],
          val,
          memData,
        );
        let obj=undefined;
        if(val['srcOrgId']){
          val['srcOrgName']=val['srcOrgId'][0]['name'];
          val['srcOrgId']=val['srcOrgId'][0]['code'];
        }
        let data:Array<object>=[];
        if(val['memId']){
          for(let obj of memData){
            let type = '21';//类型 21系统内关系转出
            // 当转接单位选择 转入到全国交换区组织得时候，type 传224 当转接单位选择 转到未接入全国交换区组织，type 传223
            if(val['unitssss'] == '1'){
              type = '224'
            }
            if(val['unitssss'] == '2'){
              type = '223'
            }
            let putObj={
              memId:obj['code'],//人员code
              srcOrgId:obj['orgCode'],//源组织
              srcOrgName:obj['orgName'],
              area:val['area'],
              transferOutTime:val['transferOutTime'] ? val['transferOutTime'].valueOf() : undefined,
              // reportTime:val['reportTime'] ? val['reportTime'].valueOf() : undefined,
              // targetOrgId:val['targetOrgId'][0]['code'],//目的组织
              // targetOrgName:val['targetOrgId'][0]['name'],
              memFeeStandard:obj['dues'],//党费
              memFeeEndTime:obj['duesTime'] ? obj['duesTime'].valueOf() : undefined,//党费时间
              type,
              whetherExtendPrepPeriod:obj['whetherExtendPrepPeriod'],//是否延长预备期
              reason:val['reason'],//原因
              d146Code: val['d146Code'], // 字典表 原因
              d146Name: val['d146Name'],
            };
            if(typeof val['targetOrgId']==='object'){
              putObj['targetOrgId']=val['targetOrgId']['code'];
              putObj['targetOrgName']=val['targetOrgId']['name'];
            }else{
              putObj['targetOrgName']=val['targetOrgName'];
              // putObj['type']='223';//系统外跨省转出
            }
            data.push(putObj);
          }
          obj=await this.props.dispatch({
            type:'memDevelop/transferMem',
            payload:{
              data,
            }
          })
        }else{
          if(val['targetOrgId']){
            val['targetOrgName']=val['targetOrgId']['name'];
            val['targetOrgId']=val['targetOrgId']['code'];
          }
          // 转接理由
          if(val['type']=='d') {
            val['type'] = '226';//转出省外 整建制
          }else{
            val['type'] = '212';//212 系统内区县整建制转出
          }
          obj=await this.props.dispatch({
            type:'memDevelop/addTransfer',
            payload:{
              data:{
                ...val
              }
            }
          })
        }
        if(obj && obj['code']===0){
          Tip.success('操作提示','关系转接申请已提交');
          this.props.refresh();
          this.handleCancel();
        }
      }
    })
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      memData:[],
    })
  };
  open=()=>{
    this.setState({
      visible:true,
    })
  };
  memChange=(data)=>{
    this.setState({
      memData:data,
    })
  };
  del=(item)=>{
    let {memData}=this.state;
    console.log(memData,'memData')
    console.log(item,'id')
    memData=memData.filter(obj=>obj['code']!==item['code']);
    this.setState({
      memData
    });
    let names:Array<string>=[];
    for(let obj of memData){
      names.push(obj['name'])
    }
    this['mem'].setState({
      value:names.join('，'),
    });
  };
  isYb=(val,item)=>{
    let {memData}=this.state;
    let findIndex = memData.findIndex(obj=>obj['code']===item['code']);
    item['whetherExtendPrepPeriod']=val;
    memData[findIndex]=item;
    this.setState({
      memData,
    })
  };
  duesChange=(e,item)=>{
    const {value}=e.nativeEvent.target;
    let {memData}=this.state;
    let findIndex = memData.findIndex(obj=>obj['code']===item['code']);
    item['dues']=value;
    memData[findIndex]=item;
    this.setState({
      memData,
    })
  };
  dateChange=(data,dateString,item)=>{
    console.log(data,dateString,item,'iiiiiii')
    let {memData}=this.state;
    let findIndex = memData.findIndex(obj=>obj['code']===item['code']);
    item['duesTime']=data;
    memData[findIndex]=item;
    this.setState({
      memData,
    })
  };
  getList=async(params?)=>{
    this.setState({loading:true});
    const {
      code = 500,
      data = [],
    } = await getProvinces({
      pageSize: this.state.pagination.pageSize,
      pageNum: this.state.pagination.pageNum,
    });
    this.setState({loading:false});
    if(code == 0) {
      this.setState({unitList:data})
    }
  }
  onPageChange=(page,pageSize)=>{
    this.getList({pageNum:page,pageSize});
  };
  render(){
    const {memTrans,orgTrans}=this.props;
    const {visible,memData,showList,unitList,loading,pagination}=this.state;
    const {getFieldDecorator,getFieldValue}=this.props.form;
    const type=this.props.form.getFieldValue('type') || 'a';
    const unitssss=this.props.form.getFieldValue('unitssss') || '';
    // console.log(type,'statestyatat');
    const columns=[
      {
        title:'序号',
        dataIndex:'id',
        align:'center',
        width:58,
        render:(text,record,index)=>{
          return index+1;
        }
      },
      {
        title:'姓名',
        dataIndex:'name',
        align:'center',
        width:100,
      },
      {
        title:'所在组织',
        dataIndex:'orgName',
        align:'center',
        width:200,
      },
      {
        title:'性别',
        dataIndex:'sexName',
        align:'center',
        width:80,
      },
      {
        title:'联系方式',
        dataIndex:'phone',
        align:'center',
        width:160,
      },
      // {
      //   title:'是否延长预备期',
      //   dataIndex:'whetherExtendPrepPeriod',
      //   width:160,
      //   render:(text,record,index)=>{
      //     return (
      //       <FormItem key={record['id']}>
      //         {getFieldDecorator(`whetherExtendPrepPeriod${record['id']}`, {
      //           initialValue: text ? text : undefined,
      //           rules: [{ required: record['d08Code']=='2', message: '是否延长预备期' }],
      //         })(
      //           <Select placeholder={'请选择'} allowClear disabled={record['d08Code']!='2'} onChange={(val)=>this.isYb(val,record)}>
      //             <Select.Option value={'1'}>是</Select.Option>
      //             <Select.Option value={'0'}>否</Select.Option>
      //           </Select>
      //         )}
      //       </FormItem>
      //     );
      //   }
      // },
      {
        title:'操作',
        dataIndex:'action',
        align:'center',
        width:80,
        render:(text,record,index)=>{
          return(
            <span>
              <a className={'del'} onClick={()=>this.del(record)}>删除</a>
            </span>
          )
        }
      },
    ];
    const unitColumns = [
      {
        title:'序号',
        dataIndex:'id',
        align:'center',
        width:58,
        render:(text,record,index)=>{
          return index+1;
        }
      },
      {
        title:'地区',
        dataIndex:'dzzmc',
        align:'center',
        width:200,
      },
      {
        title:'是否接入',
        dataIndex:'jhqcsz',
        align:'center',
        width:100,
        render:(text,record,index)=>{
          return (
            text == '0' ? '未接入' : text == '1' ? '已接入' : ''
          )
        }
      },
    ]
    // `${_get(getFieldValue('srcOrgId'),'[0]name')} ${_get(getFieldValue('targetOrgId'),'name')}`
    return(
      <div>
        <Modal
          destroyOnClose
          title="关系转出"
          visible={visible}
          // onOk={this.handleOk}
          onCancel={this.handleCancel}
          confirmLoading={type==='c' ? orgTrans : memTrans}
          width={1200}
          bodyStyle={{height:760,overflow:'auto'}}
          footer={[
            <Button htmlType={'button'} onClick={this.handleCancel} key={1}>取消</Button>,
            type == 'c' ? <Popconfirm title={<div style={{width:300}}>
            此操作将把
            <span style={{color: 'red' }}>{_get(getFieldValue('srcOrgId'),'[0]name','')}</span> 组织整建制转移到
            <span style={{color: 'red' }}>{_get(getFieldValue('targetOrgId'),'name','')}</span> 组织下，
            整建制转移涉及所有数据情况将以发起转接时数据为准
            （例：某党员发整建制时姓名为张某某，在对方未接收整建制以前在本地修改为李某某，
            对方接收时党员姓名依旧为张某某，发起后修改的名称李某某不会更新到数据包中），发起转接前请确认已将所有数据已维护至可转移状态
            </div>} onConfirm={this.handleOk} okText="确定" cancelText="取消" key={2}  placement="topRight">
              <Button htmlType={'button'} type={'primary'}>确定</Button>
            </Popconfirm>
            :<Button htmlType={'button'} type={'primary'} onClick={this.handleOk} loading={type==='c' ? orgTrans : memTrans}>确定</Button>
            ,
          ]}
        >
          {
            visible && <React.Fragment>
              <Form {...formItemLayout}>
                <FormItem
                  label="转接类型"
                >
                  {getFieldDecorator('type', {
                    initialValue:"a",
                    rules: [{ required: true, message: '请输入转接类型' }],
                  })(
                    <Radio.Group buttonStyle="solid" onChange={()=>{this.props.form.resetFields();this.setState({memData:[]})}}>
                      <Radio.Button value="a">省内转接（个人）</Radio.Button>
                      {/* <Radio.Button value="b">转出到省外（个人）</Radio.Button>
                      <Radio.Button value="c">省内转接（整建制）</Radio.Button> */}
                      {/* <Radio.Button value="d">转出省外（整建制）</Radio.Button> */}
                    </Radio.Group>
                  )}
                </FormItem>
                {
                  (type == 'b' || type == 'd') && <React.Fragment>
                    <div style={{width:'92%', textAlign:'right', paddingRight:'4px'}}><a onClick={()=>{
                      this.setState({showList:true}),this.getList()
                    }}>全国交换区党组织接入列表</a></div>
                    <FormItem
                      label="转接单位"
                    >
                      {getFieldDecorator('unitssss', {
                        initialValue:'',
                        rules: [{ required: true, message: '请选择转接单位' }],
                      })(
                        <Select style={{ width: '100%' }} onChange={(val)=>{
                          this.props.form.setFieldsValue({
                            area: undefined,
                            targetOrgId:undefined,
                          });
                        }}>
                          <Select.Option title='请与转入党组织联系，确认转入党组织是否接入全国交换区' value={'1'}>转到接入全国交换区党组织（如其他省（区、市）等所属党组织）</Select.Option>
                          <Select.Option title='请与转入党组织联系，确认转入党组织是否未接入全国交换区' value={'2'}>转到未接入全国交换区党组织（如军队、中直机关等所属党组织）</Select.Option>
                        </Select>
                      )}
                    </FormItem>
                  </React.Fragment>
                }
                {
                  type!=='c' && type!=='d' ? <React.Fragment>
                    <FormItem
                      label="转接人员"
                    >
                      {getFieldDecorator('memId', {
                        initialValue:'',
                        rules: [{ required: true, message: '请选择转接人员' }],
                      })(
                        <MemSelect key={type} onChange={this.memChange} ref={e=>this['mem']=e} checkType={type=='b' ? 'radio' : 'checkbox'}/>
                      )}
                    </FormItem>

                    <FormItem
                      label="个人信息"
                    >
                      {getFieldDecorator('outMem2', {
                        initialValue:'',
                        rules: [{ required: false, message: '请选择个人信息' }],
                      })(
                        <ListTable columns={columns} data={memData || []} pagination={false}/>
                      )}
                    </FormItem>

                  </React.Fragment> : <React.Fragment>

                    <FormItem
                      label="需要整建制转移组织"
                    >
                      {getFieldDecorator('srcOrgId', {
                        initialValue:'',
                        rules: [{ required: true, message: '请选择需要整建制转移组织' }],
                      })(
                        <OrgSelect  ref={e=>this['mem']=e}/>
                      )}
                    </FormItem>
                  </React.Fragment>
                }

                {
                  type!='b' && type!='d' ?
                    <FormItem label="目的党组织">
                      {getFieldDecorator('targetOrgId', {
                        rules: [{ required: true, message: '请选择目的党组织' }],
                      })(
                        <SearchOrg showOtherInfo={'transfer'}  key={type} backType={'object'} params={type=='c' ? {orgTypeList:['1','2']} : type=='a' ? {orgTypeList:['3','4']} : {} }/>
                      )}
                    </FormItem> :
                    <React.Fragment>
                      {
                        unitssss == '1' ?
                        <FormItem
                          label="目的党组织"
                        >
                          {getFieldDecorator('targetOrgId', {
                            initialValue:undefined,
                            rules: [{ required: true, message: '请选择目的地区' }],
                          })(
                            <SearchOrg ohterAction={findOutsideOrgByName} showOtherInfo={'transfer'}  key={unitssss} backType={'object'} params={type=='c' ? {orgTypeList:['1','2']} : type=='a' ? {orgTypeList:['3','4']} : {} }/>
                          )}
                        </FormItem> :
                        <React.Fragment>
                          <FormItem
                            label="目的地区"
                          >
                            {getFieldDecorator('area', {
                              initialValue:undefined,
                              rules: [{ required: true, message: '请选择目的地区' }],
                            })(
                              <DictTreeSelect
                                key={unitssss}
                                codeType={'dict_d136'}
                                placeholder={'目的地区'}
                                parentDisable={true}
                              />
                            )}
                          </FormItem>
                          <FormItem
                            label="目的党组织"
                          >
                            {getFieldDecorator('targetOrgName', {
                              initialValue:undefined,
                              rules: [{ required: true, message: '请输入目的党组织' }],
                            })(
                              <Input placeholder={'请输入目的党组织'}/>
                            )}
                          </FormItem>
                        </React.Fragment>
                      }
                      {/* <FormItem
                        label="党员报到日期"
                      >
                        {getFieldDecorator('reportTime', {
                          initialValue:undefined,
                          rules: [{ required: true, message: '请输入党员报到日期' }],
                        })(
                          <Dates />
                        )}
                      </FormItem> */}
                      {/* <FormItem
                        label="单位名称"
                      >
                        {getFieldDecorator('unitName', {
                          initialValue:undefined,
                          rules: [{ required: true, message: '请输入单位名称' }],
                        })(
                          <Input placeholder={'请输入单位名称'}/>
                        )}
                      </FormItem> */}
                    </React.Fragment>

                }
                <FormItem
                  label="转出日期"
                >
                  {getFieldDecorator('transferOutTime', {
                    initialValue:undefined,
                    rules: [{ required: true, message: '请输入转出日期' }],
                  })(
                    <Dates />
                  )}
                </FormItem>
                {
                  type == 'c' ? (
                    <FormItem
                  label="转接原因"
                >
                  {getFieldDecorator('reason', {
                    rules: [{ required: true, message: '请输入转接原因' }],
                  })(
                    <TextArea rows={4} placeholder={'转接原因'}/>
                  )}
                </FormItem>
                  ) : (
                    <FormItem
                        label={'转接原因'}
                      >
                        {getFieldDecorator('d146Code', {
                          rules: [{ required: true, message: '请选择转接原因' }],
                        })(
                          <DictTreeSelect
                            codeType={'dict_d146'}
                            placeholder={'请选择转接原因'}
                            ref={e=>this['d146_code'] = e}
                            parentDisable={true}
                            backType={'object'}/>
                        )}
                      </FormItem>
                  )
                }

              </Form>
            </React.Fragment>
          }
        </Modal>
        <Modal
        title={'全国交换区党组织接入列表'}
        visible={showList}
        onOk={() => {
        }}
        onCancel={()=>{this.setState({showList:false})}}
        width={'500px'}
        destroyOnClose={true}
        // bodyStyle={{maxHeight:'72vh',overflow:'auto'}}
        footer={null}
      >
        <ListTable
          scroll={{y:'60vh'}}
          pagination={false}
          
          columns={unitColumns}
          data={unitList}
          // pagination={pagination}
          // onPageChange={this.onPageChange}
          // scroll={{y:'42vh'}}
          rowKey={'code'}
        />
      </Modal>
      </div>
    )
  }
}
export default Form.create()(TransferBet)
