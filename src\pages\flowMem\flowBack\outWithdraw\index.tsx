/**
 * 党员流回
 */

import React from 'react'
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Avatar, Button, Col, Dropdown, Menu, Modal, Row, Input, Radio, DatePicker } from 'antd';
import qs from 'qs';
import {connect} from "dva";
import ListTable from 'src/components/ListTable';
import {_history as router} from "@/utils/method";
import { isEmpty } from '@/utils/method';
import { getSession } from '@/utils/session';
import DictArea from '@/components/DictArea';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import OrgSelect from '@/components/OrgSelect'
import styles from './index.less'
import moment from 'moment'
import Date from '@/components/Date';

const FormItem=Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 17 },
  },
};
const menuData=[
  {
    code:'1',
    name:'基本信息',
    icon:'star',
  },
  {
    code:'2',
    name:'班子成员',
    icon:'qrcode',
  },
];
@connect(({unit,commonDict,loading})=>({unit,commonDict,loading:loading.effects['unit/getList']}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    let obj=menuData[0];
    this.state={
      visible:false,
      key:obj['code'],
      keyPath:[obj['code']],
      selected:obj,
    };
  }

  showModal=()=>{
    const { data:{ id='' }={},keyword=''} = this.props;
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
    });
  };

  handleOk=()=>{
    this.props.form.validateFieldsAndScroll(async(errors, values) => {
      if (errors){
        return
      }
      const { data={},onChange }=this.props;
      const {backFlowType,org,backFlowJob,backFlowOrgLinkman,backFlowOrgPhone,backFlowReason,backFlowDate,backflow_is_hold  }=values;
      let val={
        code:data['code'],
        backflowDate:isEmpty(backFlowDate)?'':moment(backFlowDate).format('YYYY-MM-DD'),
        // backflowTypeCode:isEmpty(backFlowType)?'':backFlowType['id'],
        // backflowTypeName:isEmpty(backFlowType)?'':backFlowType['name'],
        // backflowOrgName:isEmpty(org)?'':org[0]['name'],
        // backflowOrgOrgCode:isEmpty(org)?'':org[0]['orgCode'],
        // backflowOrgCode:isEmpty(org)?'':org[0]['code'],
        // backflowOrgLinkman:isEmpty(backFlowOrgLinkman)?'':backFlowOrgLinkman,
        // backflowOrgPhone:isEmpty(backFlowOrgPhone)?'':backFlowOrgPhone,
        // backflowJobCode:isEmpty(backFlowJob)?'':backFlowJob['id'],
        // backflowJobName:isEmpty(backFlowJob)?'':backFlowJob['name'],
        // backflowReason:isEmpty(backFlowReason)?'':backFlowReason,
        // backflow_is_hold:backflow_is_hold
      };
      this.props.dispatch({
        type:'flowMem/getBackOutMem',
        payload:{
        data:{
          ...val
        }
        }
      }).then(res=>{
        if (res['code']===0){
          onChange(true)
          this.handleCancel();
        }
      })
    });
  };
  handleCancel=()=>{
    this.setState({
      visible:false
    });
    this.props.form.resetFields()
  };
  open=()=>{
    this.setState({
      visible:true,
    })
  };
  destroy=()=>{
    let obj=menuData[0];
    this.setState({
      visible:false,
      key:obj['code'],
      keyPath:[obj['code']],
      selected:obj,
    });
    this.props.dispatch({//重置model
      type:'unit/updateState',
      payload:{
        basicInfo:{},
      }
    })
  };
  onSelect=(item)=>{
    const {key,keyPath}=item;
    const selected=menuData.find(obj=>obj['code']===key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  disabledDate=(current)=>{
    return current && current > moment().endOf('day');
  };
  validFunction = (rule, value, callback) => {
    let han= /^[^\s]*$/;
    if (value){
      switch (rule.field) {
        case 'backFlowOrgLinkman':
          if (value.length>20){
            return callback('组织联系人不能超过20个字符')
          }else if (!han.test(value)) {
            return callback('联系人名称不合法')
          }
          break;
        case 'backFlowOrgPhone':
          if (!(/^1[345789]\d{9}$/).test(value)){
            return callback('手机号码不合法')
          }
          break;
      }
    }
    callback()
  };
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  render() {
    const {visible,selected,keyPath,key,pagination={}}=this.state;
    const { filterHeight,loading,children,data={} }=this.props;
    const { getFieldDecorator  } = this.props.form;
    return (
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          title="党员流回"
          className='out_Modal'
          destroyOnClose
          closable={false}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={700}
        >
          <div className='container'>
            {/*<div className={styles.head}>*/}
            {/*  <div>*/}
            {/*    <p>党员:{data['memName']}</p>*/}
            {/*    <p>党员类型:{data['memTypeName']}</p>*/}
            {/*  </div>*/}
            {/*  <div>*/}
            {/*    <p><span>从:</span>{data['memOrgName']}</p>*/}
            {/*    <p><span>到:</span>{data['outflowOrgName']}</p>*/}
            {/*  </div>*/}
            {/*</div>*/}
            <Form {...formItemLayout}>
              {/*<FormItem*/}
              {/*  label={'流回类型'}*/}
              {/*>*/}
              {/*  {getFieldDecorator('backFlowType', {*/}
              {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
              {/*    rules: [*/}
              {/*      { required: true, message: '请选择流回类型!' },*/}
              {/*      // { validator: this.validFunction }*/}
              {/*    ],*/}
              {/*  })(*/}
              {/*    <DictSelect codeType={'dict_d34'} backType={'object'}/>*/}
              {/*  )}*/}
              {/*</FormItem>*/}
              {/*<FormItem*/}
              {/*  label={'流回前党支部'}*/}
              {/*>*/}
              {/*  {getFieldDecorator('org', {*/}
              {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
              {/*    rules: [*/}
              {/*      { required: true, message: '请选择流回前党支部!' },*/}
              {/*      // { validator: this.validFunction }*/}
              {/*    ],*/}
              {/*  })(*/}
              {/*    <OrgSelect/>*/}
              {/*  )}*/}
              {/*</FormItem>*/}
              {/*<FormItem*/}
              {/*  label={'流回前工作岗位'}*/}
              {/*>*/}
              {/*  {getFieldDecorator('backFlowJob', {*/}
              {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
              {/*    rules: [*/}
              {/*      { required: true, message: '请选择流回前工作岗位!' },*/}
              {/*      // { validator: this.validFunction }*/}
              {/*    ],*/}
              {/*  })(*/}
              {/*    <DictTreeSelect codeType={'dict_d09'} placeholder={'请选择工作岗位'} parentDisable={true} backType={'object'}/>*/}
              {/*  )}*/}
              {/*</FormItem>*/}
              {/*<FormItem*/}
              {/*  label={'流回前支部联系人'}*/}
              {/*>*/}
              {/*  {getFieldDecorator('backFlowOrgLinkman', {*/}
              {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
              {/*    rules: [*/}
              {/*      { required: true, message: '请选择流回前支部联系人!' },*/}
              {/*      { validator: this.validFunction }*/}
              {/*    ],*/}
              {/*  })(*/}
              {/*    <Input/>*/}
              {/*  )}*/}
              {/*</FormItem>*/}
              {/*<FormItem*/}
              {/*  label={'流回前支部联系人电话'}*/}
              {/*>*/}
              {/*  {getFieldDecorator('backFlowOrgPhone', {*/}
              {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
              {/*    rules: [*/}
              {/*      { required: true, message: '请选择流回前支部联系人电话!' },*/}
              {/*      { validator: this.validFunction }*/}
              {/*    ],*/}
              {/*  })(*/}
              {/*    <Input/>*/}
              {/*  )}*/}
              {/*</FormItem>*/}
              {/*<FormItem*/}
              {/*  label={'流回原因'}*/}
              {/*>*/}
              {/*  {getFieldDecorator('backFlowReason', {*/}
              {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
              {/*    rules: [*/}
              {/*      {pattern:/(^[^\s]*$)/g, message:'不能输入空格'}*/}
              {/*      // { required: true, message: '请填写流回原因!' },*/}
              {/*      // { validator: this.validFunction }*/}
              {/*    ],*/}
              {/*  })(*/}
              {/*    <Input placeholder="请输入" />*/}
              {/*  )}*/}
              {/*</FormItem>*/}
              {/*<FormItem*/}
              {/*  label={'党员活动证发放'}*/}
              {/*>*/}
              {/*  {getFieldDecorator('backflow_is_hold', {*/}
              {/*    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,*/}
              {/*    rules: [*/}
              {/*      { required: true, message: '请选择党员活动证是否发放!' },*/}
              {/*      // { validator: this.validFunction }*/}
              {/*    ],*/}
              {/*  })(*/}
              {/*    <RadioGroup>*/}
              {/*      <Radio value={1}>已发放</Radio>*/}
              {/*      <Radio value={0}>未发放</Radio>*/}
              {/*    </RadioGroup>*/}
              {/*  )}*/}
              {/*</FormItem>*/}
              <FormItem
                label={'流回日期'}
              >
                {getFieldDecorator('backFlowDate', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请选择流回日期!' },
                    // { validator: this.validFunction }
                    // <DatePicker style={{width:'100%'}} disabledDate={this.disabledDate}/>
                  ],
                })(
                  <Date disabledDate={this.disabledDate} />
                )}
              </FormItem>
            </Form>
          </div>
        </Modal>
      </React.Fragment>
    );
  }
}
export default Form.create()(index);
