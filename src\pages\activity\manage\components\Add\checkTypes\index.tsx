import React , {Fragment} from 'react';
import {Tag} from "antd";
import _isEmpty from 'lodash/isEmpty';
import _includes from 'lodash/includes';
import _isEqual from 'lodash/isEqual';
const { CheckableTag } = Tag;
interface Interface {
  data:Array<{
    key:string | number,
    name:string
  }>,
  rule?:Array<any>,
  onChange?:(any)=>void,
  init?:Array<any>,
  canChange?:boolean
}
export default class index extends React.Component<Interface, any> {
  static defaultProps={
    init:[],
    data:[],
    rule:[],
    canChange:true
  };
  constructor(props) {
    super(props);
    this.state = {
      selectedTags:[]
    }
  }
  static getDerivedStateFromProps = (nextProps:any, prevState:any) => {
    const state = [];
    const {init} = nextProps;
    const {_init} = prevState;
    if(!_isEqual(init,_init)){
      state['_init'] = init;
      state['selectedTags'] = init;
    }
    return state;
  };
  handleChange=(item, checked)=>{
    const radio = (arr,rule,item) => {
      if(_includes(rule,item)){
        rule = rule.filter(t => t != item);
        rule.map(it =>{
          if(_includes(arr,it)){
            arr = arr.filter(i => i != it)
          }
        })
      }
      return arr
    };
    const {rule,onChange,canChange} = this.props;
    const { selectedTags } = this.state;
    if(canChange){
      let nextSelectedTags = checked ? [...selectedTags, item.key] : selectedTags.filter(t => t !== item.key);
      nextSelectedTags = radio(nextSelectedTags,rule,item.key);
      onChange && onChange(nextSelectedTags);
      this.setState({
        selectedTags:nextSelectedTags
      })
    }
  };
  render(): React.ReactNode {
    const {data} = this.props;
    const {selectedTags} = this.state;
    return (
      <Fragment>
        {
          !_isEmpty(data) && data.map(item => {
            return (
              <CheckableTag key={item['key']}
                            checked={selectedTags.indexOf(item['key']) > -1}
                            onChange={checked => this.handleChange(item, checked)}
              >
                {item['name']}
              </CheckableTag>
            )
          })
        }
      </Fragment>
    )
  }
}
