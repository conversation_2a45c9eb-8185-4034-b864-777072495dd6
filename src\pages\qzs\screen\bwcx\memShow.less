.searchBg {
  background: url('../../../../assets/qzs/bg1.webp') no-repeat;
  background-size: 100% 100%;
}

.memShow {
  .searchBg;
  width: 100%;
  height: 100%;
  padding-top: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .center {
    background: url('../../../../assets/qzs/box1.png') no-repeat;
    background-size: 100% 100%;
    width: 1500px;
    height: 700px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30px;
    .tit {
      width: 80%;
      margin-bottom: 20px;
    }
    .card {
      width: 90%;
      height: 100%;
      // height: 500px;
      background: #ffffff;
      border-radius: 6px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      .left {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        width: 40%;
        > img {
          // width: calc(270px * 0.7);
          // height: 270px;
          width: 300px;
          height: 280px;
          margin-bottom: 20px;
        }
        .name {
          font-family: Source Han Serif SC;
          font-weight: 800;
          font-size: 30px;
          color: #be0c10;
          margin-bottom: 10px;
        }
        .shengri {
          font-family: Source Han Serif SC;
          font-weight: 800;
          font-size: 21px;
          color: #323232;
          margin-bottom: 10px;
          // width: 236px;
          width: 90%;
          display: flex;

          > div:first-child {
            width: 106px;
            text-align: justify;
            text-align-last: justify;
          }
          > div:last-child {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            /* autoprefixer: on */
          }
        }
        .jieshaoren {
          font-family: Source Han Serif SC;
          font-weight: 800;
          font-size: 21px;
          color: #323232;
          margin-bottom: 10px;
          // width: 236px;
          width: 90%;
          display: flex;

          > div:first-child {
            width: 106px;
            text-align: justify;
            text-align-last: justify;
          }
          > div:last-child {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            /* autoprefixer: on */
          }
        }
        .desc {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 18px;
          color: #62471e;
          margin-bottom: 10px;

          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
          /* autoprefixer: on */
        }
        .line {
          background: url('../../../../assets/qzs/icon2.png') no-repeat;
          background-size: 100% 100%;
          width: 100%;
          height: 10px;
        }
      }
      .right {
        width: 48%;
        height: 480px;
        background: rgba(255, 249, 231, 0.42);
        border-radius: 15px;
        border: 4px solid #c1a984;
        padding: 10px;
        position: relative;
        > img {
          width: 100%;
          height: 100%;
        }
        .defaultName {
          font-family: qtbf;
          position: absolute;
          left: 305px;
          bottom: 112px;
          font-size: 26px;
        }
      }
    }
  }

  .scroll {
    margin-top: 20px;
    width: 1800px;
    height: 200px;
    position: relative;
    // position: absolute;
    // left: -256px;
    .stk {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }
    .scorllItem {
      width: 350px;
      height: 200px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      position: relative;
      .scorllItemph {
        width: 342px;
        height: 142px;
        position: absolute;
        left: 7px;
      }
      .scorllItembg {
        width: 100%;
        height: 100%;
      }
    }
    // > div > div {
    //   width: 1000px !important;
    // }
  }
}
