import React, { Fragment, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Select, Modal, Tree, Layout, Tag, TreeSelect, Input } from 'antd';
import { MenuUnfoldOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import {
  jsonToTree,
  treeToList,
  changeTreeItem,
  selfRowSelection,
  findParent,
} from '@/utils/method';
import SplitterLayout from 'react-splitter-layout';
import styles from './index.less';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _cloneDeep from 'lodash/cloneDeep';
import ListTable from 'src/components/ListTable';
const { Search } = Input;
const { Header, Sider } = Layout;

interface Interface {
  codeType: string;
  commonDict?: any;
  disabled?: boolean;
  value?: any;
  initValue?: any;
  rowSelectionType?: 'radio' | 'checkbox';
  dicName?: string;
  onFocus?: Function;
  getDictWay?: string;
  dispatch?: Function;
  onChange?: Function;
  rowKey?: string;
  parentDisable?: boolean;
  backType?: 'object';
}

const index = (props: Interface) => {
  const {
    codeType,
    commonDict = {},
    disabled = false,
    value = '',
    initValue = '',
    rowSelectionType = 'radio',
    parentDisable = true,
    backType,
  } = props;
  const TableModalRef: any = useRef();
  const treeData = changeTreeItem(commonDict[`${codeType}_tree`] || [], (item) => {
    return {
      ...item,
      title: item.key + ' | ' + item.name,
      disabled: !item.leaf ? parentDisable : false,
    };
  });
  const listData = treeToList(treeData);

  const [selectedValue, setSelectedValue] = useState<any>([]);
  const getDict = () => {
    const { dicName = 'dicName', codeType, onFocus, getDictWay = 'commonDict/getDict' } = props;
    props?.dispatch?.({
      type: getDictWay,
      payload: {
        data: {
          codeType,
          [dicName as string]: codeType,
        },
      },
    });
    // if (onFocus) {
    //   onFocus();
    // }
  };
  const onOK = (keys, items) => {
    setSelectedValue(keys);
    props?.onChange?.(backType == 'object' ? items : keys);
  };
  const onChanges = (e) => {
    let arr = _isArray(e) ? e : [{ value: e }];
    let codes = arr.map((i) => i.value);
    setSelectedValue(codes);
    let objs = codes.map((i) => listData.find((its) => its.key == i));
    props?.onChange?.(backType == 'object' ? objs : codes);
  };

  useEffect(() => {
    if (!commonDict[`${codeType}_tree`]) {
      if (codeType) {
        getDict();
      }
    }
  }, []);

  useEffect(() => {
    setSelectedValue(!_isArray(initValue) ? initValue.split(',') : initValue);
  }, [JSON.stringify(initValue)]);

  return (
    <Fragment>
      <div className={styles.box}>
        <div style={{ width: `calc(100% - 30px)` }}>
          <TreeSelect
            showSearch
            treeLine
            style={{ width: '100%' }}
            value={selectedValue}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            placeholder="情选择"
            allowClear
            onChange={onChanges}
            treeData={treeData}
            treeCheckStrictly={rowSelectionType == 'checkbox'}
            {...(rowSelectionType == 'radio' ? {} : { treeCheckable: true })}
          />
        </div>

        {/* <Select
          allowClear
          value={selectedValue}
          disabled={disabled}
          onChange={onChanges}
          {...(rowSelectionType == 'radio' ? {} : { mode: 'multiple' })}
        >
          {value &&
            !_isEmpty(listData) &&
            value.split(',').map((it) => {
              const find = listData.find((its) => its.key == it);
              if (find) {
                return (
                  <Select.Option value={find.key}>
                    {find.key} | {find.name}
                  </Select.Option>
                );
              }
            })}
          <Select.Option value={'1'}>1</Select.Option>
          <Select.Option value={'2'}>2</Select.Option>
        </Select> */}
        {!disabled && (
          <MenuUnfoldOutlined
            className={styles.icon}
            onClick={() => {
              TableModalRef?.current?.open?.();
            }}
          />
        )}
      </div>
      <TableModal
        ref={TableModalRef}
        treeData={treeData}
        selectedValue={selectedValue}
        {...props}
        onOK={onOK}
      ></TableModal>
    </Fragment>
  );
};

const TableModal = React.forwardRef((props: any, ref) => {
  const {
    treeDatas = [],
    rowSelectionType = 'radio',
    rowKey = 'key',
    parentDisable = false,
    selectedValue = [],
    title = '字典表选择器',
    loadTree = undefined,
    ohterSearchFunc = undefined,
  } = props;
  const [visible, setVisible] = useState<any>(false);
  const [editConfirmLoading, setEditConfirmLoading] = useState<any>(false);
  const [data, setData] = useState<any>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [selectedItems, setSelectedItems] = useState<any>([]);
  const [text, setText] = useState<any>();
  const [selectTreeNode, setSelectTreeNode] = useState<any>({});
  const [isInit, setIsInit] = useState<any>(false);
  const [treeExpandKeys, setTreeExpandKeys] = useState<any>([]);

  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 50,
      align: 'center',
      render: (text, record, index) => {
        return index + 1;
      },
    },
    {
      title: '名称',
      dataIndex: 'name',
      align: 'center',
      render: (text, record, index) => {
        let flag = !getIsLeaf(record) ? parentDisable : false;
        if (flag) {
          return text;
        } else {
          return <div style={{ color: '#1890FF' }}>{text}</div>;
        }
      },
    },
    {
      title: '值',
      dataIndex: 'key',
      width: 200,
      align: 'center',
    },
  ];

  const rowSelection = {
    columnTitle: ' ', // 去掉全选
    hideDefaultSelections: true, // 去掉全选
    getCheckboxProps: (record) => {
      let flag = !getIsLeaf(record) ? parentDisable : false;
      return {
        disabled: flag,
      };
    },
    ...selfRowSelection({
      selectedRowKeys,
      setSelectedRowKeys,
      setSelectedItem: setSelectedItems,
      selectType: rowSelectionType,
      rowKey: rowKey,
    }),
  };

  const getIsLeaf = (item) => {
    return item.leaf || item.is_leaf;
  };
  const open = () => {
    setVisible(true);
  };
  const close = () => {
    setVisible(false);
    setData([]);
    setSelectTreeNode({});
    setSelectedRowKeys([]);
    setSelectedItems([]);
    setTreeExpandKeys([]);
    setIsInit(false);
    setText(undefined);
    props?.close?.();
  };
  const onSelect = (e, { node }) => {
    setText(undefined);
    setSelectTreeNode(node);
    if (loadTree) {
      if (!getIsLeaf(node)) {
        loadTree(node);
      }
    }
    // else {
    //   let currentNodes = [{ name: node.name, key: node.key, id: 1, leaf: getIsLeaf(node) }];
    //   if (!_isEmpty(node.children)) {
    //     currentNodes = [
    //       ...currentNodes,
    //       ...treeToList(_cloneDeep(node.children), true).map((it, index) => ({
    //         name: it.name,
    //         key: it.key,
    //         id: index + 2,
    //         leaf: getIsLeaf(it),
    //       })),
    //     ];
    //   }
    //   setData(currentNodes);
    // }
  };
  const onOk = () => {
    props?.onOK?.(selectedRowKeys, selectedItems);
    close();
    if (!_isEmpty(selectedItems) && ohterSearchFunc) {
      selectedItems.map((it) => {
        let code = it.topCode;
        if (code) {
          loadTree({ key: code });
        }
      });
    }
  };

  useImperativeHandle(ref, () => ({
    open: (val: any) => {
      open();
    },
  }));

  useEffect(() => {
    if (_isEmpty(treeDatas) || isInit) return;
    let keys: any = [];
    let items: any = [];
    const list = treeToList(_cloneDeep(treeDatas));

    let _arr: any = [];
    if (_isArray(selectedValue)) {
      _arr = selectedValue;
    }
    if (typeof selectedValue == 'string') {
      _arr = [selectedValue];
    }
    _arr.map((it, index) => {
      keys = [...keys, it];
      const find = list.find((its) => its.key == it);
      if (find) {
        items = [...items, find];
        if (index == 0) {
          setSelectTreeNode(find);
          let arr = findParent(find.key, list, 'key', 'parent', []);
          setTreeExpandKeys(arr.map((it) => it.key));
        }
      }
    });
    setSelectedRowKeys(keys);
    setSelectedItems(items);
    setIsInit(true);
  }, [JSON.stringify(selectedValue), JSON.stringify(treeDatas)]);

  useEffect(() => {
    if (!_isEmpty(treeDatas) && selectTreeNode?.key) {
      const list = treeToList(_cloneDeep(treeDatas));
      const find = list.find((it) => it.key == selectTreeNode.key);
      let currentNodes = [{ name: find.name, key: find.key, id: 1, leaf: getIsLeaf(find) }];
      if (!_isEmpty(find.children)) {
        currentNodes = [
          ...currentNodes,
          ...treeToList(_cloneDeep(find.children), true).map((it, index) => {
            return {
              name: it.name,
              key: it.key,
              id: index + 2,
              leaf: getIsLeaf(it),
            };
          }),
        ];
      }
      setData(currentNodes);
    }
  }, [JSON.stringify(treeDatas), selectTreeNode?.key]);

  if (!visible) {
    return <div></div>;
  }
  return (
    <Fragment>
      <Modal
        title={title}
        destroyOnClose
        maskClosable={false}
        visible={visible}
        width={1200}
        onCancel={close}
        confirmLoading={editConfirmLoading}
        bodyStyle={{ padding: 4, height: 600, overflow: 'auto' }}
        onOk={onOk}
      >
        <div className={styles.Layout}>
          <SplitterLayout
            secondaryInitialSize={230}
            primaryIndex={1}
            onDragEnd={() => {
              const e = document.createEvent('Event');
              e.initEvent('resize', true, true);
              window.dispatchEvent(e);
            }}
          >
            <div className={styles.tree}>
              {!_isEmpty(treeDatas) && (
                <Tree
                  // checkable
                  showLine
                  loadData={loadTree}
                  onExpand={(e) => {
                    setTreeExpandKeys(e);
                  }}
                  expandedKeys={treeExpandKeys}
                  // onCheck={() => {}}
                  // checkedKeys={[]}
                  onSelect={onSelect}
                  selectedKeys={[selectTreeNode?.key]}
                  treeData={changeTreeItem(treeDatas || [], (item) => {
                    return {
                      ...item,
                      isLeaf: getIsLeaf(item),
                      title: item.key + ' | ' + item.name,
                      // disabled: !item.leaf ? parentDisable : false,
                    };
                  })}
                />
              )}
            </div>
            <div className={styles.table}>
              <div className={styles.infos}>
                已经选中节点：
                {selectedItems.map((it) => {
                  return (
                    <Tag
                      color="blue"
                      closable
                      key={it.key}
                      style={{ fontSize: 16, height: 24, display: 'inline-block', marginBottom: 4 }}
                      onClose={(e) => {
                        e.preventDefault();
                        const keys = selectedRowKeys.filter((its) => its != it.key);
                        const items = selectedItems.filter((its) => its.key != it.key);
                        setSelectedRowKeys(keys);
                        setSelectedItems(items);
                      }}
                    >
                      {it.key} | {it.name}
                    </Tag>
                  );
                })}
              </div>
              <div style={{ marginBottom: 10 }}>
                <Search
                  style={{ width: 400 }}
                  placeholder="搜索关键词"
                  enterButton
                  value={text}
                  onChange={(e) => {
                    setText(e?.target?.value);
                  }}
                  onSearch={async (e) => {
                    setText(e);
                    if (!ohterSearchFunc) {
                      // setSelectTreeNode({});
                      let _data = treeToList(_cloneDeep(treeDatas), true).filter(
                        (it) => it.name.includes(e) || it.key.includes(e),
                      );
                      setData(_data);
                    } else {
                      if (_isEmpty(e) && _isEmpty(selectTreeNode)) {
                        return;
                      }
                      let arr = await ohterSearchFunc({ keyWord: e, parent: selectTreeNode?.key });
                      arr = arr?.map((it, index) => {
                        return {
                          ...it,
                          name: it.name,
                          key: it.key,
                          id: index + 2,
                          leaf: getIsLeaf(it),
                        };
                      });
                      setData(arr);
                    }
                  }}
                />
              </div>
              <ListTable
                key={selectTreeNode?.key}
                scroll={{ y: 350 }}
                rowKey={'key'}
                className={styles.ListTable}
                columns={columns}
                data={data}
                rowSelection={rowSelection}
                onRow={(record) => ({
                  onClick: () => {
                    // this.selectRow(record);
                  },
                })}
              ></ListTable>
            </div>
          </SplitterLayout>
        </div>
      </Modal>
    </Fragment>
  );
});

export default connect(({ commonDict }: any) => ({ commonDict }), undefined, undefined, {
  forwardRef: true,
})(index);

export { TableModal };
