import React, { Fragment, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import _get from 'lodash/get';
import { Input } from 'antd'
import style from './selectMem.less'
import DictSelect from '@/components/DictSelect';
import TimePicker from './TimePicker';
import Date from '@/components/Date';
import DictTreeSelect from '@/components/DictTreeSelect';
import { CaretDownOutlined } from '@ant-design/icons';
import moment from 'moment'
function index(props: any, ref) {
  const { codeType = '', value = '', formType = '', onSelect, valueName = '' } = props;
  const [visible, setVisible] = useState(false);
  useImperativeHandle(ref, () => ({
    open: () => { open() },
  }));
  useEffect(() => {
    // if (value) {
    //   setVisible(true)
    // }
  }, []);

  const open = () => {
    setVisible(true);
  };
  const onChange = (e, type) => {
    let val: any = null
    if (type == 'DATE') {
      val = moment(e).valueOf()
    } else {
      val = e
    }
    onSelect && onSelect(val)
  }
  const nodeItem = () => {
    switch (formType) {
      case 'TEXT':
        return (
          <Input defaultValue={value} onBlur={(e) => onChange(e.target.value, 'TEXT')} />
        );
      case 'DATE':
        return (
          // <TimePicker initValue={value} onBlur={(e) => onChange(e, 'DATE')} />
          <Date onBlur={(e) => onChange(e, 'DATE')} />
        );
      case 'SELECT':
        return (
          <DictSelect getDictWay={'commonDict/queryDict'}  codeType={codeType} backType={'object'} initValue={value} onChange={(e) => onChange(e, 'SELECT')} send={false} />
        );
      case 'TREE':
        return (
          <DictTreeSelect getDictWay={'commonDict/queryDictTree'} style={{ width: '100%' }} backType={'object'} codeType={codeType} initValue={value} onChange={(e) => onChange(e, 'TREE')} send={false} />
        );
      default:
        return null
    }
  }

  return (
    <Fragment>
      {
        visible ? <div onBlur={() => { setVisible(false) }}>{nodeItem()}</div> :
          <div style={{ width: '100%', minHeight: '30px', textAlign: 'center' }} onClick={() => { setVisible(true) }}>
            {valueName ? valueName : value ? formType == 'DATE' ? moment(value).format('YYYY-MM') : value : ''}
            {/* {
              ['SELECT', 'TREE'].includes(formType) ?
                <CaretDownOutlined />
                :
                null
            } */}
          </div>
      }
    </Fragment>
  )
}

export default forwardRef(index);
