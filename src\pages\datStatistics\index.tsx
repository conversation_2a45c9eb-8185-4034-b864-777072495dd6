import React, { useRef ,useEffect} from 'react';
import { ListMain } from '@/pages/desktop/components/MoreCard/unfold';

const index = () => {
  const pageRef: any = useRef();
  const org = JSON.parse(sessionStorage.getItem('org') || '{}');
  useEffect(() => {
    pageRef.current.getList({orgCode:org.orgCode});
  },[JSON.stringify(org)])
  return (
    <div>
      <ListMain ref={pageRef} />
    </div>
  )
}

export default index;
