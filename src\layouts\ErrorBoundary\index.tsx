import React from 'react';
import Exception from "@/components/Exception";
import { Link } from 'umi';
export default class ErrorBoundary extends React.Component<any,{hasError:boolean}> {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error,info) {
    // Update state so the next render will show the fallback UI.
    // console.log(error,info,'eeeeeeeeeeee');
    if(console['_info']){
      console['_info'](error,info,'eeeeeeeeeeee');
    }
    return { hasError: true };
  }

  componentDidCatch(error, info) {
    // You can also log the error to an error reporting services
    // logErrorToMyService(error, info);
  }

  render() {
    if (this.state.hasError) {
      return(
        <Exception
          type="500"
          // @ts-ignore
          linkElement={Link}
          desc={''}
          backText={'返回'}
        />
      );
      return <h1>服务出现异常，请刷新页面若任无法解决请联系管理员.</h1>;
    }

    return this.props.children;
  }
}
