/**
 * 用户权限
 */
import React from 'react';
import { Divide<PERSON>, Mo<PERSON>, Popconfirm, Switch, Tree } from 'antd';
import { isEmpty } from '@/utils/method';
import styles from './index.less'
import ListTable from '@/components/ListTable';
import {jsonToTree,treeToList} from "@/utils/method";
import { connect } from 'dva';
import Notice from 'src/components/Tip';
const TreeNode=Tree.TreeNode;
interface propsType {
  menuData:Array<object>,
  userRole:Array<string>,
  editObj:object,
  onOK?:(Array)=>void,
  manages?:any,
  dispatch?:any,
  tree:Array<object>,
  checkTit:Array<string>,
  onExpand:(any)=>void
}
@connect(({user,login})=>({
  user,
  login
}))
export default class UserRole extends React.Component<propsType,any> {
  static open(){};
  static close(){};
  constructor(props){
    super(props);
    this.state={
      visible:false,
      edit:false,
      tree:[],
      checkTit:[],
      isCheck:false
    };
    UserRole.close=this.handleCancel;
    UserRole.open=this.show;
  }
  show=()=>{
    this.setState({
      visible:true,
    },()=>{
      const { editObj={} }=this.props;
      let a= editObj['manages'].filter(it=>it.isDefault!==0);
      this.selectRole(a[0])
    })
  };
  handleOk=()=>{
    const { tree,checkTit,treeArr,isCheck,parentKey,checkedKeys } = this.state;
    const { menuData }=this.props;
    let arr=checkTit.concat(parentKey);
    
    // 勾选中的权限
    let pArr = [...checkedKeys];
    menuData.map((item,index)=>{
      if(pArr.includes(item?.id.toString())){
        if((!pArr.includes(item?.parentId.toString())) && item?.parentId != -1){
          pArr.push(item?.parentId.toString())
        }
      }
    })
    // let arr=checkedKeys.concat(parentKey);
    let b='';
    if (isEmpty(checkTit)&&!isCheck){
      menuData.map((item)=>{
        if (treeArr.map(i=> i.id).includes(item['id'])) {
          b+=1;
        }else {
          b+=0
        }
      });
    }else {
      menuData.map((item)=>{
        if (arr.map(i=> i).includes(item['id'].toString())) {
          b+=1;
        }else {
          b+=0
        }
      });
    }
    this.props.dispatch({
      type:'user/getPermissionEdit',
      payload:{
        data: [{
          id:this.state.id,
          permissions:pArr.toString(), // 权限列表上勾选的
          permission:b
          // permission:b
          }
        ]
      }
    }).then(res=>{
      if (res.code===0){
        Notice.info("操作提示",'保存成功');
        this.handleCancel();
        let {onOK}=this.props;
        if(onOK){
          onOK(res.code)
        }
      }
    });
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
      edit:false,
      checkedKeys:[],
    })
  };
  //是否勾选
  onChecks=(checkedKeys, e)=>{
    const { checkedNodes=[] }=e;
    let { checkTit } = this.state;
    let parentKey=[];
    if (!isEmpty(e.halfCheckedKeys)) {
      e.halfCheckedKeys.map((item,index)=>{
        parentKey.push(item)
      })
    }
    checkTit= checkedNodes.map((item,index)=>{
        return item.dataRef.code
  });
    this.setState({ checkedKeys,edit:true,checkTit,isCheck:true ,parentKey});
  };

  onExpand=(expandedKeys)=>{
    const {onExpand} = this.props;
    onExpand && onExpand(expandedKeys);
    this.setState({
      expandedKeys,
    });
  };

  rowClick=(val,record)=>{
    this.selectRole(val);
  };

  selectRole=(v)=>{
    this.setState({id:v['userPermissionId']});
    this.props.dispatch({
      type:'user/getPermissionByPermissionId',
      payload:{
        data:{
          permissionId:v['userPermissionId']
        }
      }
    }).then(res=>{
      if (res.code===0){
        const { data }=res;
        // 过滤掉没有返回父级菜单的菜单项
        let dataId = data.map(it=>it?.id);
        dataId = [...dataId, -1]
        let newData =[];
        data.map((item,idnex)=>{
          if(dataId.includes(item?.parentId)){
            newData.push(item)
          }
        })
        let checkedKeys:Array<string>=[];
        // let { checkedKeys }=this.state;
        let a=newData.map((item,index)=>{
          let obj={
            name:item.des,
            parent:item['parentId'].toString(),
            code:item['id'].toString(),
            serverId:item['id'].toString(),
          };
          return obj
        });
        for(let item of a){
          const find=newData.find(obj=>obj['parentId']==item['code']);
          if(!find){
            checkedKeys.push(item.serverId)
          }
        }
        // checkedKeys=a.map((item,index)=>{
        //
        // });
        // console.log(checkedKeys,a,'cccccccccccccccccc')
        let menuTreeData=jsonToTree([...a],'parent','code','-1');
        this.setState({tree:menuTreeData,treeArr:data,checkedKeys})
      }
    });
  };

  renderTreeNodes = data => data.map((item) => {
    // 不显示92	补录 93	错误录入 94	接收预备党员
    let filterArr=['92','93','94']
    if(!filterArr.includes(item?.serverId)){
      if (item.children) {
        return (
          <TreeNode title={item['name']} key={item['serverId']} dataRef={item}>
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode title={item['name']} key={item['serverId']} dataRef={item} />;
    }
  });
  render(): React.ReactNode {
    let { menuData=[],editObj={},userRole=[] }=this.props;
    const { checkedKeys }=this.state;
    let a = menuData.map((item:any,index)=>{
      let obj={
        serverId:item['id'].toString(),
        code:item['id'].toString(),
        parent:item['parentId'].toString(),
        name:item['des']
      };
      return obj
    });
    let menuTreeData=jsonToTree(a,'parent','code','-1');
    let { manages=[] }=editObj;
    const { tree=[] }=this.state;

    const onRow = (record) => {
      return {
        onClick: this.rowClick.bind(this, record),       // 点击行
      }
    };
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        render:(text,record,index)=>{
          return index+1
        }
      },
      {
        title:'角色名称',
        dataIndex:'roleName',
      },
      {
        title:'管理组织',
        dataIndex:'managerOrgName',
      },
    ];
    return(
      <Modal
        title="权限管理"
        destroyOnClose
        maskClosable={false}
        visible={this.state['visible']}
        onOk={this.handleOk}
        width={600}
        onCancel={this.handleCancel}
      >
        {
          this.state['visible'] && <React.Fragment>
            <div className={styles.detail_left}>
              <p>用户名：{editObj['name']}</p>
              <ListTable
                columns={columns}
                data={manages}
                onRow={onRow}
                pagination={false}
              />
            </div>

            <div className={styles.detail_right}>
              <div style={{display:'inline-block',verticalAlign:'top'}}>
                权限：
              </div>
              <div style={{display:'inline-block'}}>
                <Tree
                  checkable
                  defaultExpandAll
                  checkedKeys={checkedKeys}
                  // selectedKeys={checkedKeys}
                  onCheck={this.onChecks}
                >
                  {this.renderTreeNodes(menuTreeData?menuTreeData:[])}
                </Tree>
              </div>
            </div>
          </React.Fragment>
        }

      </Modal>
    );
  }
}
