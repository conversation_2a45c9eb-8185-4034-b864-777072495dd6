// 流动党员-流出管理-未纳入流入地管理
import React, { Fragment } from 'react';
import { connect } from 'dva';
import { Button, Divider, Input, Modal, message } from 'antd';
import { WarningTwoTone } from '@ant-design/icons';
import moment from 'moment';
import { Form } from '@ant-design/compatible';
import UnDo from './undo';
import Date from '@/components/Date';
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import Tip from '@/components/Tip';
import { isEmpty, setListHeight, changeMsgTip } from '@/utils/method';
import NowOrg from 'src/components/NowOrg';
import ExportInfo from '@/components/Export';
import { getSession } from '@/utils/session';
import FlowAddOrEdit from './flowAddOrEdit';
import ChangeArea from './changeArea';
import NewFlowAddOrEditReadOnly from '../../inflowManage/components/newFlowAddOrEdit';
import { outManageRevoke } from '../../service/index';
import DictSelect from '@/components/DictSelect';
import { findExchangeLxfs } from '@/pages/flowMem/service'
import _isEqual from 'lodash/isEqual';

const Search = Input.Search;
const FormItem = Form.Item;

const { confirm } = Modal;

@connect(({ unit, commonDict, loading, flowMem }) => ({
  flowMem,
  unit,
  commonDict,
  loading: loading.effects['unit/getList'],
}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {}, //筛选器
      memName: undefined, //搜索框
      view: false,
      subordinate: getSession('subordinate'),
      exportLoading: false, //导出loading
      searchLoading: false, //查询loading
      getList:this.getList
    };
    this['queryDetailsRef'] = React.createRef();
  }

  setQueryDetailsRef = (ref) => {
    this['queryDetailsRef'] = ref;
  };
  // undo = (record) => {
  //   confirm({
  //     title: '撤销提示',
  //     // content: '确定撤销该流动吗？确定后该流出将取消。',
  //     content: (
  //       <Fragment>
  //         <div>确定撤销该流动吗？确定后该流出将取消。</div>

  //       </Fragment>
  //     ),
  //     okText: '确认',
  //     cancelText: '取消',
  //     onOk: async () => {
  //       const { code = 500 } = await outManageRevoke({
  //         code: record?.code,
  //       });
  //       if (code == 0) {
  //         Tip.success('操作提示', '操作成功');
  //         this.getList({ pageNum: 1 });
  //       }
  //     },
  //   });
  // };
  addOrEdit = (type: string, record?: object) => {
    this['flowAddOrEditRef'].open(type, record);
  };
  unDo = (record?: object) => {
    this['unDoRef'].open(record);
  };
  orgChange = () => {
    this.getList({ pageNum: 1 });
  };
  exportInfo = async () => {
    this.setState({
      exportLoading: true,
    });
    await this['exportRef'].submitNoModal();
    this.setState({
      exportLoading: false,
    });
  };
  filterChange = (val) => {
    console.log("🚀 ~ index ~ val:", val)
    const org = getSession('org') || {};
    this.setState(
      {
        filter: val,
      },
      () => this.getList(),
    );
  };
  handleSearch = (e) => {
    this.setState(
      {
        memName: e,
        searchLoading: true,
      },
      () => {
        this.getList({ memName: e });
      },
    );
  };
  searchChange(e) {
    this.setState({
      memName: e.currentTarget.value || undefined,
    });
  }
  getList = async (params?: object) => {
    const { filter, memName, outAdministrativeDivisionCode } = this.state;
    const org = getSession('org') || {};
    await this.props.dispatch({
      type: 'flowMem/outManageList',
      payload: {
        data: {
          flowType: 1, // 根据当前选中tab菜单 1：未纳入 2：已纳入 3：流出被退回 4：流出历史
          pageNum: 1,
          pageSize: 10,
          memOrgCode: org['orgCode'],
          memName,
          outAdministrativeDivisionCode,
          ...filter,
          ...params,
        },
      },
    });
    this.setState({
      searchLoading: false,
    });
  };
  getContactInformation = async (record) => {
    console.log('record====', record);
    const { code: resCode, data } = await findExchangeLxfs({
      code: record.code,
    })
    if (resCode === 0) {
      Modal.info({
        title: '提示',
        content: '已成功发送获取联系方式请求，需等待对方上传联系方式，请稍后再进行查看！'
      })
      //  message.success('已成功发送获取联系方式请求，需等待对方上传联系方式，请稍后再进行查看！')
    }
  }
  // componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
  //   const org = getSession('org') || {};
  //   const subordinate = getSession('subordinate') || '0';
  //   if (
  //     (!isEmpty(this.state['orgCode']) && this.state['orgCode'] !== org['orgCode']) ||
  //     subordinate !== this.state.subordinate
  //   ) {
  //     this.setState(
  //       {
  //         orgCode: org['orgCode'],
  //         subordinate,
  //       },
  //       () => {
  //         this.getList({ memOrgCode: org['orgCode'] });
  //       },
  //     );
  //   }
  // }
  componentDidMount() {
    const org = getSession('org') || {};
    setListHeight(this);
    // this.setState({ orgCode: org['orgCode'] });
    // this.getList();
  }
  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const org=getSession('org') || {};
    const {_org = {},getList, page, pageSize, name} = prevState;
    if(!_isEqual(org, _org)){
      state['org'] = org;
      state['_org'] = org;
      getList(page, pageSize, name);
    }
    return state;
  };
  render() {
    const { filterHeight, filter, memName, subordinate, searchLoading } = this.state;
    const org = getSession('org') || { d01Code: '' };
    const { d01Code = '' } = org || {};
    const {
      loading,
      commonDict,
      flowMem: { list = [], pagination = {} },
    } = this.props;
    const filterData = [
      {
        key: 'outAdministrativeDivisionCode',
        name: '流入地行政区域',
        value: commonDict[`dict_d151_tree`],
      },
      {
        key: 'outPlaceCode',
        name: '流出类型',
        value: commonDict[`dict_d148`],
      },
      {
        key: 'flowTypeCode',
        name: '流动类型',
        value: commonDict[`dict_d34`],
      },
      {
        // 时间选择器的时候没用到该key
        key: 'timeSelect',
        name: '外出时间',
        value: '',
        // type 1是树选择器 2是时间选择器
        type: 2,
        textArr: ["startOutTime", 'endOutTime']
      },
      {
        key: 'timeSelect',
        name: '登记日期',
        value: '',
        // type 1是树选择器 2是时间选择器
        type: 2,
        textArr: ["startRegisterTime", 'endRegisterTime']
      },
      {
        key: 'timeSelect',
        name: '创建日期',
        value: '',
        // type 1是树选择器 2是时间选择器
        type: 2,
        textArr: ["startCreateTime", 'endCreateTime']
      },
    ];
    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 40,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'memName',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return (
            <Fragment>
              {record['outOverTime'] ? (
                <WarningTwoTone
                  style={{ fontSize: '20px' }}
                  twoToneColor={'#faad14'}
                  title={record['outOverTime']}
                />
              ) : (
                ''
              )}
              <a
                onClick={() => {
                  this['flowAddOrEditReadOnlyRef'].open('readOnly-outTab1', record, 'outFlow');
                }}
              >
                {text}
              </a>
            </Fragment>
          );
        },
      },
      // {
      //   title: '性别',
      //   dataIndex: 'memSexName',
      //   align: 'center',
      //   width: 40,
      // },
      {
        title: '联系电话',
        dataIndex: 'memPhone',
        align: 'center',
        width: 100,
      },
      {
        title: '流出类型',
        dataIndex: 'outPlaceName',
        align: 'left',
        width: 100,
      },
      // {
      //   title: '流入地行政区域',
      //   dataIndex: 'outAdministrativeDivisionName',
      //   align: 'left',
      //   width: 100,
      // render: (text, record, index) => {
      //   if (text) {
      //     if (true) {
      //       return (
      //         <a
      //           style={{
      //             whiteSpace: 'nowrap',
      //             textOverflow: 'ellipsis',
      //             overflow: 'hidden',
      //             display: 'inline-block',
      //             width: 200,
      //           }}
      //           onClick={() => {
      //             Modal.info({
      //               title: '原因说明：',
      //               icon: null,
      //               content: <div>{text}</div>,
      //             });
      //           }}
      //         >
      //           {text}
      //         </a>
      //       );
      //     } else {
      //       return text;
      //     }
      //   }
      // },
      // },
      {
        title: '流动类型',
        dataIndex: 'flowTypeName',
        align: 'left',
        width: 100,
      },
      {
        title: '数据创建时间',
        dataIndex: 'createTime',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '外出日期',
        dataIndex: 'outTime',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '外出时长',
        dataIndex: 'outDuraion',
        align: 'center',
        width: 50,
      },
      {
        title: '流入地党组织联系人',
        dataIndex: 'outOrgContact',
        align: 'left',
        width: 100,
      },
      {
        title: '流入地党组织联系方式',
        dataIndex: 'outOrgContactPhone',
        align: 'left',
        width: 100,
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 100,
        render: (text, record) => {
          return (
            <Fragment>
              <a
                onClick={() => {
                  this['flowAddOrEditReadOnlyRef'].open('readOnly-outTab1', record, 'outFlow');
                }}
              >
                查看
              </a>
              <Divider type="vertical" />
              {(record.outPlaceCode == '3' || record.outPlaceCode == '4') && (
                <Fragment>
                  <a
                    onClick={() => {
                      this['flowAddOrEditReadOnlyRef'].open('edit-outTab1', record, 'outFlow');
                    }}
                  >
                    管理
                  </a>
                  <Divider type="vertical" />
                </Fragment>
              )}
              {/* 流入行政区这个按钮，流出类型为流向县（市、区、旗)才显示 */}
              {/* 2025/2/11 修改屏蔽修改流入行政区功能 */}
              {/* {(record.outPlaceCode == '2') && (
                <Fragment>
                  <a
                    onClick={() => {
                      this['changeAreaRef'].open(record);
                    }}
                  >
                    修改流入行政区
                  </a>
                  <Divider type="vertical" />
                </Fragment>
              )} */}
              <a
                onClick={() => {
                  this.unDo(record);
                }}
              >
                撤销
              </a>
              {/* 获取联系方式的按钮，只有是跨省流动且是流向基层党工委的才显示，点击后发送请求，请求成功后，进行提示：已成功发送获取联系方式请求，需等待对方上传联系方式，请稍后再进行查看！ */}
              {(record.outPlaceCode == '1' && record.flowTypeCode == '1') && (<Fragment>
                <Divider type="vertical" />
                <a
                  onClick={() => {
                    this.getContactInformation(record)
                  }}
                >
                  获取联系方式
                </a>
              </Fragment>)}
            </Fragment>
          );
        },
      },
    ];
    return (
      <Fragment>
        <NowOrg
          extra={
            <Fragment>
              {['631', '632', '634', '931', '932'].includes(d01Code) ? (
                <Button
                  type={'primary'}
                  onClick={() => {
                    this.addOrEdit('add');
                  }}
                >
                  流出登记
                </Button>
              ) : (
                <React.Fragment />
              )}
              <Button
                style={{ marginLeft: 16 }}
                onClick={this.exportInfo}
                loading={this.state.exportLoading}
              >
                导出
              </Button>
              <Search
                loading={searchLoading}
                allowClear
                placeholder="请输入姓名"
                enterButton={'查询'}
                style={{ width: 200, marginLeft: 16 }}
                onSearch={(e) => {
                  this.handleSearch(e);
                }}
                onChange={(e) => {
                  this.searchChange(e);
                }}
              />
            </Fragment>
          }
        />
        <RuiFilter
          // showLine={3}
          data={filterData}
          openCloseChange={() => setListHeight(this, 20)}
          onChange={this.filterChange}
        />
        <ListTable
          rowKey={'id'}
          scroll={{ y: filterHeight, x: 100 }}
          
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={(page, pageSize) => {
            this.getList({ pageNum: page, pageSize });
          }}
        />
        <FlowAddOrEdit
          wrappedComponentRef={(e) => (this['flowAddOrEditRef'] = e)}
          onOk={this.getList}
        />
        <NewFlowAddOrEditReadOnly
          wrappedComponentRef={(e) => (this['flowAddOrEditReadOnlyRef'] = e)}
          onOk={this.getList}
        />
        <ChangeArea wrappedComponentRef={(e) => (this['changeAreaRef'] = e)} onOk={this.getList} />
        <ExportInfo
          wrappedComponentRef={(e) => (this['exportRef'] = e)}
          tableName={''}
          noModal={true}
          tableListQuery={{
            ...filter,
            pageNum: pagination.pageNumber || 1,
            pageSize: pagination.pageSize || 10,
            // subordinate,
            memName,
            memOrgCode: org['orgCode'],
            flowType: '1',
          }}
          action={'/api/mem/flow/outExportXlsx'}
        />
        <UnDo ref={(e) => (this['unDoRef'] = e)} callBack={this.orgChange} />
      </Fragment>
    );
  }
}
