import React,{Fragment} from 'react';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _pullAllBy from 'lodash/pullAllBy';
import {changeAbsenceListb} from '../config';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Input, Select, Switch, Table } from 'antd';

const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const FormItem = Form.Item;

export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state ={
      _absenceList:[]
    }
  }
  static getDerivedStateFromProps = (nextProps:any, prevState:any) => {
    const state = [];
    const {absenceList} = nextProps;
    const {_absenceList} = prevState;
    if(!_isEqual(absenceList,_absenceList)){
      state['_absenceList'] = absenceList;
      let data:Array<object>=[];
      for(let obj of absenceList){
        const find=_absenceList.find(ob=>ob['code']===obj['code']);
        if(find){
          data.push(find);
        }else{
          data.push(obj)
        }
      }
      state['absenceList'] = data;

    }
    return state;
  };
  onChange=({target:{value = ''}={}},record)=>{
    const {absenceList} = this.state;
    const {onChange} = this.props;
    let newArr:Array<any> = [];
    if(!_isEmpty(absenceList)){
      absenceList.forEach(item=>{
        if(record['code'] === item['code']){
          newArr.push({...item,reason:value})
        }else {
          newArr.push({...item})
        }
      });
    }
    onChange && onChange(changeAbsenceListb(newArr));
    this.setState({
      absenceList:newArr
    })
  };
  render(): React.ReactNode {
    const {absenceList,form} = this.props;
    const {getFieldDecorator} = form;
    const columns=[
      {
        title:'序号',
        dataIndex:'id',
        align:'center',
        width:58,
        render:(text,record,index)=>{
          return index+1;
        }
      },
      {
        title:'姓名',
        dataIndex:'name',
        align:'center',
      },
      // {
      //   title:'电话',
      //   dataIndex:'phone',
      //   align:'center',
      // },
      {
        title:'缺席原因',
        dataIndex:'reason',
        render:(text,record,index)=>{
          return (
            <FormItem
            >
              {getFieldDecorator(`reason${index}`, {
                rules: [{ required: true, message: '缺席原因' }],
                initialValue: text
              })(
                <Input placeholder={'请填写缺席原因'} onChange={(e)=>this.onChange(e,record)}/>
              )}
            </FormItem>
          )
        }
      }
    ];
    return (
      <Fragment>
        {
          !_isEmpty(absenceList) &&
          <FormItem
            label={'缺席原因'}
            {...formItemLayout1}
          >
            <Table
              bordered={true}
              columns={columns as any}
              dataSource={absenceList}
              pagination={false}
              rowKey={record=>record['code']}
              size={'middle'}
            />
          </FormItem>

        }
      </Fragment>
    )
  }
}

