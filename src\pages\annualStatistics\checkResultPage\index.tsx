import React, { useEffect, useState, useRef } from 'react';
import ListTable from '@/components/ListTable';
import _get from 'lodash/get';
import { Popconfirm, Modal, Button } from 'antd';
import {
  findCheckTableListByCode,
  delCheck,
  findCheckTableListByCodeDifference,
} from '../services';
import { getSession } from '@/utils/session';
import moment from 'moment';
import { _history, throttle } from '@/utils/method.js';
// import { success } from '@/components/Notice';
import Tip from '@/components/Tip';
import SpinProgress from '@/components/SpinProgress';
import _isEmpty from 'lodash/isEmpty';
import styles from './index.less';

export default function index() {
  const ref = useRef(null);
  // const {type,orgCode} = _get(window,'location.query',{});
  // const { pathname, query: urlQuery = {} } = window['g_history']?.location || {};

  const {
    query: { type, orgCode },
  } = _history.location;

  const [pagination, setPagination] = useState({ current: 1, pageSize: 100, total: 0, pageNum: 1 });
  const [list, setList] = useState([]);
  const [Mlist, setMList] = useState([]);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [visible, setVisible] = useState(false);
  const [checkExportLoading, setCheckExportLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  useEffect(() => {
    getList(1, 100);
  }, []);
  const confirm = async (record) => {
    const { id = undefined } = record;
    const { code = 500 } = await delCheck({ checkId: id });
    if (code === 0) {
      Tip.success('操作提示', '删除成功');
      getList(pagination['current'], pagination['pageSize']);
    }
  };

  // 表名称=remark
  // 错误信息=all_remark
  // 校核时间=check_time

  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 46,
      align: 'center',
      render: (text, record, index) => {
        return (pagination['current'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    {
      title: '表名称',
      dataIndex: 'remark',
      width: 240,
      render: (text, record, index) => {
        return record?.columns?.remark;
      },
    },
    {
      title: '错误信息',
      dataIndex: 'allRemark',
      render: (text, record, index) => {
        return record?.columns?.all_remark;
      },
    },
    // 2025/1/6 添加差看差额 说后续做  下面还有弹窗
    // {
    //   title: '差看差额',
    //   dataIndex: 'aaaaa',
    //   width: 80,
    //   align: 'center',
    //   render: (text, record, index) => {
    //     return <a onClick={() => isModalShow(record)}>查看</a>;
    //   },
    // },
    {
      title: '公式左边计算值',
      dataIndex: 'leftValue',
      align: 'center',
      width: 80,
      render: (text, record, index) => {
        return record?.columns?.left_value;
      },
    },
    {
      title: '公式右边计算值',
      dataIndex: 'rightValue',
      align: 'center',
      width: 80,
      render: (text, record, index) => {
        return record?.columns?.right_value;
      },
    },
    {
      title: '公式匹配类型',
      dataIndex: 'checkType',
      align: 'center',
      width: 80,
      render: (text, record, index) => {
        return record?.columns?.check_type;
      },
    },
    // {
    //   title:'校核时间',
    //   dataIndex:'createTime',
    //   width:120,
    //   render:(text, record)=>{
    //     return(
    //       <div>
    //         { typeof text == 'number' && moment(text).format('YYYY-MM-DD') }
    //       </div>
    //     )
    //   }
    // },
  ];
  const getList = async (pageNum, pageSize) => {
    const {
      code = 500,
      data: { list = [], pageNumber = 1, totalRow = 0 } = {},
    } = await findCheckTableListByCode({ pageNum, pageSize, orgCode, type });
    if (code === 0) {
      setList(list);
      setPagination({
        current: pageNumber,
        pageSize: pageSize,
        total: totalRow,
        pageNum: pageNumber,
      });
    }
  };
  const onPageChange = (page, pageSize) => {
    getList(page, pageSize);
  };
  const handleCancel = () => {
    setVisible(false);
  };
  const isModalShow = (record) => {
    console.log(record.columns.ruler_id);
    setVisible(true);
    setTableLoading(true);
    getModalList(record.columns.ruler_id);
  };
  const exportRes = () => {};

  const pageChange = () => {};

  const ModalColumns = [
    {
      title: '姓名',
      dataIndex: 'name',
      width: 70,
      align: 'center',
    },
    {
      title: '性别',
      dataIndex: 'sex_name',
      width: 70,
      align: 'center',
    },
    {
      title: '身份证号码',
      dataIndex: 'idcard',
      width: 80,
      align: 'center',
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
          if (text.indexOf('*') > 0) {
            return text;
          }
          return <span>{newVal}</span>;
        } else {
          return '';
        }
      },
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: 80,
      align: 'center',
    },
    {
      title: '电话',
      dataIndex: 'phone',
      width: 80,
      align: 'center',
    },
    {
      title: '预备党员日期',
      dataIndex: 'join_org_date',
      width: 80,
      align: 'center',
    },
    {
      title: '转为正式党员日期',
      dataIndex: 'full_member_date',
      width: 80,
      align: 'center',
    },
    {
      title: '单位名称',
      dataIndex: 'unit_name',
      width: 80,
      align: 'center',
    },
    {
      title: '单位类别',
      dataIndex: 'd04_name',
      width: 80,
      align: 'center',
    },
    {
      title: '民族',
      dataIndex: 'd06_name',
      width: 80,
      align: 'center',
    },
    {
      title: '学历',
      dataIndex: 'd07_name',
      width: 80,
      align: 'center',
    },
    {
      title: '党员类型',
      dataIndex: 'd08_name',
      width: 80,
      align: 'center',
    },
    {
      title: '工作岗位',
      dataIndex: 'd09_name',
      width: 80,
      align: 'center',
    },
    {
      title: '新社会阶层',
      dataIndex: 'd20_name',
      width: 80,
      align: 'center',
    },
    {
      title: '转正类型',
      dataIndex: 'd28_name',
      width: 80,
      align: 'center',
    },
    {
      title: '籍贯',
      dataIndex: 'd48_name',
      width: 80,
      align: 'center',
    },
    {
      title: '一线情况',
      dataIndex: 'd21_name',
      width: 80,
      align: 'center',
    },
  ];
  const progressCallback = (res) => {
    // const { progressType } = this.state;
    // switch (progressType) {
    //   case '1':
    //     this.setState({ tbDownLoadLoading: false });
    //     fileDownloadHeader(`/api${res.url}`, res['url'].split('/')[2]);
    //     Modal.success({
    //       content: '导出成功',
    //     });
    //     break;
    // }
  };

  const getModalList = async (rules_id) => {
    try {
      let canshu = {
        orgCode: orgCode,
        id: rules_id,
      };
      console.log(canshu);
      const { code = 500, data } = await findCheckTableListByCodeDifference({
        data: canshu,
      });
      if (code === 0) {
        console.log('🚀 ~ getModalList ~ list:', data);

        // setMList(list);
        // setPagination({
        //   current: pageNumber,
        //   pageSize: pageSize,
        //   total: totalRow,
        //   pageNum: pageNumber,
        // });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setTableLoading(false);
    }
  };

  return (
    <div>
      <h1 style={{ textAlign: 'center', fontWeight: 'bolder' }}>校核有误信息项</h1>
      <div>
        <ListTable
          columns={columns}
          data={list}
          scroll={{ y: document.body.clientHeight - 250 }}
          pagination={pagination}
          onPageChange={onPageChange}
        />
      </div>
      <Modal
        maskClosable={false}
        title={
          <div>
            差看差额
            {/* <Button
              type={'primary'}
              onClick={exportRes}
              loading={checkExportLoading}
              style={{ marginLeft: 10 }}
            >
              导出
            </Button> */}
          </div>
        }
        visible={visible}
        onOk={handleCancel}
        onCancel={handleCancel}
        width={1360}
        footer={null}
        bodyStyle={{ minHeight: 0 }}
      >
        <SpinProgress ref={ref} callback={(res) => progressCallback(res)}>
          <div className={styles.list}>
            <ListTable
              rowKey={'_ids'}
              scroll={{
                x: columns.reduce((total: any, it: any) => {
                  return total + it.width;
                }, 80),
                y: 500,
              }}
              columns={ModalColumns}
              data={Mlist}
              pagination={{
                page,
                pageSize,
                total,
              }}
              onPageChange={pageChange}
            />
          </div>
        </SpinProgress>
      </Modal>
      {/* <ListPagination pagination={pagination} onChange={onPageChange}/> */}
    </div>
  );
}
