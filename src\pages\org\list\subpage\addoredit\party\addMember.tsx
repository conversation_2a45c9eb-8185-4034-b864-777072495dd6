/**
 * 添加党代表
 */
import React,{Fragment} from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Input, Modal, Radio, Row, Switch } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import MemSelect from '@/components/MemSelect';
import Tip from '@/components/Tip';
import {getSession} from "@/utils/session";
import moment from 'moment';
import { formLabel,getIdCardInfo,correctIdcard } from '@/utils/method';
import Date from '@/components/Date';
import { committeeSave, committeeUpdate } from '@/pages/org/services/org';
import _isEmpty from 'lodash/isEmpty';
import { validateLength } from '@/utils/formValidator';
const FormItem=Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};
class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      visible:false,
    }
  }
  showModal=()=>{
    this.setState({
      visible:true,
    });
  };

  handleOk=()=>{
    const {elect,dataInfo={},iteListData=[]}=this.props;
    this.props.form.validateFieldsAndScroll(async(err, val) => {
      if (!err) {
        if (val['memName'] != dataInfo['memName'] || val['memIdcard'] != dataInfo['memIdcard']) {
          let result =await correctIdcard(val['memName'],val['memIdcard']);
        if(result['code']!='200'){
          this.props.form.setFields({
            memIdcard:{
              value:val['memIdcard'],
              errors:[new Error('经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')]
            }
          })
          Tip.error('操作提示','经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')
          return
        }else{
          val['idCardReason']=result['reason']
          val['idCardReasonName']=result['reasonName']
        }
        }
        let obj=undefined;
        ['d106Code','d51Code','d07Code','d124Code'].map(obj=>{
          let key=obj.split('C')[0];
          if(typeof val[obj] === 'object' ){
            val[`${key}Name`]=val[obj]['name'];
            val[obj]=val[obj]['key']
          }
        });
        if(val['memTypeCode']){
          val['memTypeCode']=1;
        }else{
          val['memTypeCode']=0;
        }
        if(val['sexCode']){
          val['sexName']='男'
        }else{
          val['sexName']='女'
        }
        ['startDate','endDate'].map(obj=>{
          if(val[obj]){
            val[obj]=val[obj].valueOf();
          }
        });
        if(val['memCode'] && typeof val['memCode'] === 'object'){
          val['memName']=val['memCode'][0]['name'];
          val['memIdcard']=val['memCode'][0]['idcard'];
          val['sexCode']=val['memCode'][0]['sexCode'];
          val['sexName']=val['memCode'][0]['sexName'];
          val['birthday']=val['memCode'][0]['birthday'];
          val['d07Code']=val['memCode'][0]['d07Code'];
          val['d07Name']=val['memCode'][0]['d07Name'];
          // 是否本单位党员 选是时：把这个人的d09的值给d124 传值过来
          val['d124Code']=val['memCode'][0]['d09Code'];
          val['d124Name']=val['memCode'][0]['d09Name'];

          val['memCode']=val['memCode'][0]['code'];
          const find = iteListData.find(obj=>obj['memCode']==val['memCode']);
          if(find){
            return Tip.warning('操作提示','该届次中已存在该党员任职信息');
          }
        }
        val['electCode']=elect['code'];
        val['zbCode']=elect['zbCode'];
        val['orgCode']=elect['orgCode'];
        val['positionOrgCode']=elect['electOrgCode'];
        val['positionOrgName']=elect['orgName'];
        if(dataInfo['code']){
          obj=await committeeUpdate({
            data:{
              ...dataInfo,
              ...val
            }
          });
        }else{
          obj=await committeeSave({
            data:{
              ...val
            }
          });
        }
        if(obj && obj['code']===0){
          Tip.success('操作提示',dataInfo['code'] ? '修改成功' : '新增成功');
          this.handleCancel();
          this.props.queryList();
        }
      }
    });
  };
  handleCancel=()=>{
    this.props.onClose();
    this.setState({
      visible:false,
    });
  };
  validatorIdcard =async (rule, value, callback) => {
    if (!value) {
      callback('身份证必填');
    }
    if (value && value.length !== 18 && process.env.idCheck != 'false') {
      callback('身份证应该为18位');
    }
    if (getIdCardInfo(value) === 'Error') {
      callback('身份证格式错误,请核对身份证图片');
    } else {
      // memIdcard
      // let fieldValue = this.props.form.getFieldValue('memName');
      // let res=await geitCard({idCard:value,name:fieldValue});
      callback()
    }
  };
  getIDinfo = (e) => {
    const { target: { value = '' } = {} } = e || {};
    let info = getIdCardInfo(value);
    if (value!=undefined && info !== 'Error') {
      this.props.form.setFieldsValue({
        sexCode: info[2] === '女' ? '0' : '1',
        birthday:info[1] ? moment(info[1], 'YYYY-MM-DD') : undefined,
      });
    }
  };
  render() {
    const { getFieldDecorator } = this.props.form;
    const {children,title,dataInfo={},elect={},tipMsg={}}=this.props;
    const org=getSession('org') || {};
    const { basicInfo={} }=this.props.org;
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        {
          this.state.visible&&
          <Modal
            title={ title || "请输入标题" }
            visible={this.state.visible}
            onOk={this.handleOk}
            onCancel={this.handleCancel}
            width={1000}
            className='add_member_modal'
            maskClosable={false}
          >
            <Form {...formItemLayout}>
              <Row>
                <Col span={24}>
                  <FormItem
                    label={ formLabel('任职党组织名称',tipMsg['orgName']) }
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('orgName', {
                      initialValue:org['name'],
                      rules: [{ required: true, message: '请输入上级组织' }],
                    })(
                      <Input placeholder={'组织全称'} disabled/>
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label={ formLabel('是否本单位党员',tipMsg['memTypeCode']) }
                  >
                    {getFieldDecorator('memTypeCode', {
                      valuePropName:'checked',
                      initialValue:dataInfo['memTypeCode']!==undefined ? dataInfo['memTypeCode']==1 : true,
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <Switch/>
                    )}
                  </FormItem>
                </Col>
                {
                  (function (_this) {
                    const{props} = _this;
                    const memTypeCode=props.form.getFieldValue('memTypeCode');
                    return(
                      <React.Fragment>
                        {
                          memTypeCode==0 ? <React.Fragment>
                            <Col span={12}>
                              <FormItem
                                label={ formLabel('人员姓名',tipMsg['memName']) }
                              >
                                {getFieldDecorator('memName', {
                                  initialValue:dataInfo['memName'],
                                  rules: [{ required: true, message: '请输入人员姓名' }, {validator: (...e)=>validateLength(e, 16, 50)}],
                                })(
                                  <Input placeholder="请输入人员姓名"/>
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={ formLabel('性别',tipMsg['sexCode']) }
                              >
                                {getFieldDecorator('sexCode', {
                                  initialValue:dataInfo['sexCode'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <Radio.Group>
                                    <Radio value={'1'}>男</Radio>
                                    <Radio value={'0'}>女</Radio>
                                  </Radio.Group>
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={ formLabel('身份证',tipMsg['memIdcard']) }
                              >
                                {getFieldDecorator('memIdcard', {
                                  initialValue:dataInfo['memIdcard'],
                                  rules: [
                                    { required: true, message: '请输入身份证' },
                                    { validator: _this.validatorIdcard },
                                  ],
                                })(
                                  <Input placeholder="请输入身份证" onBlur={_this.getIDinfo}/>
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={ formLabel('出生日期',tipMsg['birthday']) }
                              >
                                {getFieldDecorator('birthday', {
                                  initialValue:dataInfo['birthday']!==undefined ? moment(dataInfo['birthday']*1) : undefined,
                                  rules: [{ required: true, message: '请输入出生日期' }],
                                })(
                                  <Date />
                                )}
                              </FormItem>
                            </Col>

                            <Col span={12}>
                              <FormItem
                                label={ formLabel('学历情况',tipMsg['d07Code']) }
                              >
                                {getFieldDecorator('d07Code', {
                                  initialValue:dataInfo['d07Code'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <DictTreeSelect backType={'object'} codeType={'dict_d07'} initValue={dataInfo['d07Name']} placeholder="请选择" parentDisable={true}/>
                                )}
                              </FormItem>
                            </Col>
                            <Col span={12}>
                              <FormItem
                                label={ formLabel('职业',tipMsg['d124Code']) }
                              >
                                {getFieldDecorator('d124Code', {
                                  initialValue:dataInfo['d124Code'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <DictTreeSelect backType={'object'} codeType={'dict_d124'} initValue={dataInfo['d124Name']} placeholder="请选择" parentDisable={true}/>
                                )}
                              </FormItem>
                            </Col>
                          </React.Fragment> :
                          <Fragment>
                            <Col span={12}>
                              <FormItem
                                label={ formLabel('党员姓名',tipMsg['memCode']) }
                              >
                                {getFieldDecorator('memCode', {
                                  initialValue:dataInfo['memCode'],
                                  rules: [{ required: true, message: '请选择' }],
                                })(
                                  <MemSelect initValue={dataInfo['memName']} placeholder="请选择党员"/>
                                )}
                              </FormItem>
                            </Col>
                          </Fragment>

                        }
                      </React.Fragment>
                    )
                  })(this)
                }

                <Col span={12}>
                  <FormItem
                    label={ formLabel('人员身份',tipMsg['d106Code']) }
                  >
                    {getFieldDecorator('d106Code', {
                      initialValue:dataInfo['d106Code'],
                      rules: [{ required: true, message: '请选择人员身份' }],
                    })(
                      <DictTreeSelect initValue={dataInfo['d106Code']} backType={'object'} codeType={'dict_d106'} placeholder="请选择" parentDisable={true}/>
                    )}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label={ formLabel('党代表任职起始日期',tipMsg['startDate']) }
                  >
                    {getFieldDecorator('startDate', {
                      initialValue:dataInfo['startDate'] ? moment(dataInfo['startDate']) : elect['tenureStartDate'] ? moment(elect['tenureStartDate']) : undefined,
                      rules: [{ required: true, message: '请选择党代表任职起始日期' }],
                      // <DatePicker placeholder="请选择" style={{width:'100%'}}/>
                    })(

                      <Date />
                    )}
                  </FormItem>
                </Col>

              </Row>
            </Form>
          </Modal>
        }

      </React.Fragment>
    )
  }
}
export default Form.create<any>()(index);
