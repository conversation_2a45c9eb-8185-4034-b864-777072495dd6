import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {add, del, findOrg, getList, updated,itteeList,getElectList,addUnitCommittee,updateUnitCommittee,delUnitCommittee,unitFind,getListByIdcard} from '@/pages/[unit]/services';
import {getSession} from "@/utils/session";
import { changeListPayQuery } from '@/utils/method.js';
import {memLockedList} from '@/pages/mem/services';
import _isEmpty from 'lodash/isEmpty';

const modelOrg = modelExtend(listPageModel,{
  namespace: "unit",
  state:{
    basicInfo:{},
    basicInfoData:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        let isLockUrl = (pathname === '/unlock/locked' || pathname === '/unlock/unlock') && query.lockObject == '2';
        if(pathname==='/unit' || isLockUrl){
          let org=getSession('org') || {};
          let defaultParas={
            pageNum:1,
            pageSize:10,
          };
          const dictData=['dict_d04','dict_d05','dict_d35','dict_d194','dict_d195'];
          for(let obj of dictData){
            dispatch({
              type:'commonDict/getDictTree',
              payload:{
                data:{
                  dicName:obj
                }
              }
            });
          }
          if(!_isEmpty(org)){
            if(isLockUrl){
              dispatch({
                type:'getUnlockList',
                payload:{
                  data:{
                    memCode:org['orgCode'],
                    ...defaultParas,
                    ...query,
                  }
                }
              })
            }else {
              dispatch({
                type:'getList',
                payload:{
                  data:{
                    mainUnitOrgCode:org['orgCode'],
                    manageUnitOrgCode:org['orgCode'],
                    ...defaultParas,
                    ...query,
                  }
                }
              })
            }
          }
        }
      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put,select }) {
      const {filter,unitName}=yield select(state=>state['unit']);
      const {data={}} = yield call(getList, {data:{...payload['data'],...filter,unitName}});
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      });
      yield put({
        type:'updateState',
        payload:{
          isCreateOrg:payload['data']['isCreateOrg'],
        }
      })
    },
    *getUnlockList({ payload }, { call, put,select }) {
      const {filter,unitName}=yield select(state=>state['unit']);
      const {data={}} = yield call(memLockedList, {data:{...payload['data'],...filter,keyword:unitName}});
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      });
      yield put({
        type:'updateState',
        payload:{
          isCreateOrg:payload['data']['isCreateOrg'],
        }
      })
    },
    // 查找人员列表
    *add({ payload }, { call, put }) {
      return yield call(add, payload);
    },
    *update({ payload }, { call, put }) {
      return yield call(updated, payload);
    },
    *del({ payload }, { call, put }) {
      return yield call(del, payload);
    },
    *findOrg({ payload }, { call, put , select}) {
      const obj= yield call(findOrg, payload);
      if(obj?.code == 0){
        let info = obj['data'];
        const {d04Code = ''} = info || {};
        const {dict_d04 = []} = yield select(state=>state['commonDict']);
        let find = dict_d04.find(it=>it.key == d04Code);
        if(!(find && find.leaf)){
          info.d04Code = undefined;
          info.d04Name = undefined;
        }
        yield put({
          type:'updateState',
          payload:{
            basicInfo:info,
          }
        });
      }

    },
    *getElectList({ payload }, { call, put }) {
      const obj= yield call(getElectList, payload);
      const {data={}}=obj;
      yield put({
        type:'updateState',
        payload:{
          iteList:data['list'],
          paginationIte:{
            current:data['pageNumber'],
            pageSize:data['pageSize'],
            total:data['totalRow'],
          }
        }
      });
    },
    *itteeList({ payload }, { call, put }) {
      return yield call(itteeList, payload);
      // const obj= yield call(itteeList, payload);
      // const {data={}}=obj;
      // yield put({
      //   type:'updateState',
      //   payload:{
      //     iteList:data['list'],
      //     paginationIte:{
      //       current:data['pageNumber'],
      //       pageSize:data['pageSize'],
      //       total:data['totalRow'],
      //     }
      //   }
      // });
    },
    *addite({ payload }, { call, put }) {
      return yield call(addUnitCommittee, payload);
    },
    *upite({ payload }, { call, put }) {
      return yield call(updateUnitCommittee, payload);
    },
    *delite({ payload }, { call, put }) {
      return yield call(delUnitCommittee, payload);
    },
    *unitFind({ payload }, { call, put }) {
      return yield call(unitFind, payload);
    },
    *getListByIdcard({ payload }, { call, put }) {
      return yield call(getListByIdcard, payload);
    },
  }
});
export default modelOrg;
