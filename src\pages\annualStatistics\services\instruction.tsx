import request from '@/utils/request';
import qs from 'qs';

export function findTreeByTbsm(params) {
  return request(`/api/tbsmItem/findTreeByTbsm?${qs.stringify(params)}`);
}

export function findFieldList(params) {
  return request(`/api/tbsmItem/findFieldList?${qs.stringify(params)}`);
}
export function queryExcelConfigTreeById(params) {
  return request(`/api/excelconfig/queryExcelConfigTreeById?${qs.stringify(params)}`);
}

export function findTbsmItemList(params) {
  return request(`/api/tbsmItem/findTbsmItemList?${qs.stringify(params)}`);
}

export function addTbsmItemList(params) {
  return request(`/api/tbsmItem/addTbsmItemList`, {
    method: 'post',
    body: params
  });
}
export function updateTbsmItemList(params) {
  return request(`/api/tbsmItem/updateTbsmItemList`, {
    method: 'post',
    body: params
  });
}

export function getMemList(params) {
  return request(`/api/mem/getList?${qs.stringify(params)}`);
}

export function addByBaseSelect(params) {
  return request(`/api/tbsmItem/addByBaseSelect`, {
    method: 'post',
    body: params
  });
}

export function findByPage(params) {
  return request(`/api/mem/findByPage?${qs.stringify(params)}`);
}

export function addByReportSelect(params) {
  return request(`/api/tbsmItem/addByReportSelect`, {
    method: 'post',
    body: params
  });
}

export function deleteTbsmItemList(params) {
  return request(`/api/tbsmItem/deleteTbsmItemList`, {
    method: 'post',
    body: params
  });
}

export function sortTbsmItemList(params) {
  return request(`/api/tbsmItem/sortTbsmItemList`, {
    method: 'post',
    body: params
  });
}
export function qsData(params) {
  return request(`/api/tbsmItem/qsData`, {
    method: 'post',
    body: params
  });
}
export function batchQsData(params) {
  return request(`/api/tbsmItem/batchQsData?${qs.stringify(params)}`);
}
export function dataAggregate(params) {
  return request(`/api/tbsmItem/dataAggregate`, {
    method: 'post',
    body: params
  });
}
export function expTbsm(params) {
  return request(`/api/tbsmItem/expTbsm?${qs.stringify(params)}`);
}
export function addBatchTbsmItemList(params) {
  return request(`/api/tbsmItem/addBatchTbsmItemList`, {
    method: 'post',
    body: params
  });
}
export function checkTable(params) {
  return request(`/api/verification/checkTable?${qs.stringify(params)}`,{
    method:'Get',
  });
}
