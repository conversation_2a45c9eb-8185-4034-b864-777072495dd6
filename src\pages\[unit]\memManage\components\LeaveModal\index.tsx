import React, { Fragment, useImperativeHandle, useState } from 'react';
import Date from '@/components/Date';
import { Col, Form, Input, Modal } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import { delUnitCountryside } from '@/pages/[unit]/services/index';
import Tip from '@/components/Tip';
import moment from 'moment';
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const formItemLayout2 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const index = (props: any, ref) => {
  const { title = '标题', onOK } = props;
  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleCancel = () => {
    setRecord({});
    setVisible(false);
    form.resetFields();
  };
  const onFinish = async (e) => {
    setConfirmLoading(true);
    const { code = 500 } = await delUnitCountryside({
      data: {
        code: record?.code,
        leaveTime: e.leaveTime ? moment(e.leaveTime).valueOf() : '',
        ...e,
      }
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOK && onOK();
    }
  };
  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      setRecord(query);
    },
    clear: () => {
      // clear();
    },
  }));

  return (
    <Modal
      title={'离开'}
      visible={visible}
      onOk={() => {
        form.submit()
      }}
      onCancel={handleCancel}
      width={800}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      {
        visible &&
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Form.Item name='leaveTime'
            label="离开时间"
            rules={[{ required: true, message: '离开时间' }]}
          >
            <Date />
          </Form.Item>

          <Form.Item
            noStyle
            name='d117Name'
            style={{ display: 'none' }}
          >
            <Input style={{ display: 'none' }} />
          </Form.Item>
          <Form.Item name='d117Code'
            label="离开去向"
            rules={[{ required: false, message: '离开去向' }]}
          >
            <DictTreeSelect backType={'object'}
              codeType={'dict_d117'}
              onChange={e => {
                form.setFieldsValue({
                  d117Code: e.key,
                  d117Name: e.name,
                });
              }}
              // initValue={dataInfo['d117Code']}
              placeholder="请选择" parentDisable={true} />
          </Form.Item>

        </Form>
      }

    </Modal>
  )
};
export default React.forwardRef(index);
