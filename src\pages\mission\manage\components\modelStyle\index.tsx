import React from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Avatar, Col, Modal, Row, Menu } from 'antd';
import Add from '../add';
import { getSession } from '@/utils/session';
let menuData=[
  {
    code:'1',
    name:'新增任务',
    icon:'star',
  },
];
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    let obj=menuData[0];
    this.state={
      visible:false,
      key:obj['code'],
      keyPath:[obj['code']],
      selected:obj,
    };
  }
  open=(val)=>{
    this.setState({
      visible:true,
      record:val
    })
  };
  destory=()=>{
    this.setState({
      record:{}
    })
  };
  handleOk=()=>{

  };
  handleCancel=()=>{
    this.destory();
    this.setState({
      visible:false
    })
  };
  onSelect=(item)=>{
    const {key,keyPath}=item;
    const selected=menuData.find(obj=>obj['code']===key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  onClose=()=>{
    const {onClose} = this.props;
    onClose && onClose();
    this.handleCancel();
  };
  render() {
    const {visible,selected,keyPath,key,record}=this.state;
    const user = getSession('user') || {};
    return (
      <Modal
        title=''
        wrapClassName='editModal'
        destroyOnClose
        closable={false}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        width={'calc(90vw)'}
        footer={false}
      >
        <div className='container'>
          <div className='header'>
            <Row>
              <Col span={4} style={{textAlign:'center'}}>
                <Avatar style={{ backgroundColor: '#7265e6',verticalAlign: 'middle' }} size="large">
                  {user['name'] || ''}
                </Avatar>
              </Col>
              <Col span={15}><h2>{record ? '编辑' : '新增'}</h2></Col>
              <Col span={5} className={'close'}><CloseOutlined onClick={this.handleCancel} /></Col>
            </Row>
          </div>
          <div>
            <Row>
              <Col span={4} style={{borderRight:'1px solid rgb(233, 233, 233)'}}>
                <div className='slider'>
                  <LegacyIcon type={selected['icon'] || undefined} style={{marginRight:8}}/>{selected['name']}
                </div>
                <Menu selectedKeys={keyPath} style={{border:'none'}} onSelect={this.onSelect} key={2}>
                  {
                    menuData && menuData.map((obj)=>{
                      return (
                        <Menu.Item key={obj['code']} >
                          <LegacyIcon type={obj['icon'] || undefined} />{obj['name']}
                        </Menu.Item>
                      );
                    })
                  }
                </Menu>
              </Col>
              <Col span={20} style={{padding:20}} className='content'>
                {
                  key==='1' && <Add onclose={this.onClose} initData={record}/>
                }
              </Col>
            </Row>
          </div>
        </div>
      </Modal>
    );
  }
}

