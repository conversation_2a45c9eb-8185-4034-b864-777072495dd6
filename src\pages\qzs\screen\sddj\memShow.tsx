import { getSession } from '@/utils/session';
import { <PERSON><PERSON>, Modal, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import st from './memShow.less';
import { findByCode, screensave, findDomain } from '../services';
import Tip from '@/components/Tip';
import { _history as router } from '@/utils/method';
import _isEmpty from 'lodash/isEmpty';
import Editor from '@/components/Editor';
import SignatureCanvas from 'react-signature-canvas';
import moment from 'moment';
import { pullFile, pullFileQz } from '@/services';
import { changeOrgName2 } from '../utils/tool';

// Base64 转 File
const base64ToFile = (base64, fileName) => {
  let arr = base64.split(','),
    type = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], fileName, { type });
};

const memShow = () => {
  const sessionInfo: any = getSession('sddj_info');

  const [text, setText] = useState<any>();

  const [img, setImg] = useState<any>();

  // 签字
  const [visible, setVisible] = useState<any>(false);
  const [signImg, setSignImg] = useState('');
  const [info, setInfo] = useState<any>({});

  // 选择模板
  const [visibleMB, setVisibleMB] = useState<any>(false);
  const [mbKey, setMbKey] = useState<any>('1');
  const [mbList, setMbList] = useState([]);

  // 签名截图上传
  const JTupload = async () => {
    let file = base64ToFile(signImg, `${info.name}_签名截图_${+new Date()}.png`);
    const formData = new FormData();
    formData.append('file', file);
    const res = await fetch('/api/base/putFile', {
      method: 'POST',
      headers: new Headers({
        Authorization: sessionStorage.getItem('token') || '',
        dataApi: sessionStorage.getItem('dataApi') || '',
      }),
      body: formData,
    }).then((response) => {
      if (response.ok) {
        return response.json();
      }
      throw new Error('Network response was not ok.');
    });
    return res;
  };

  const submit = async () => {
    if (!signImg) {
      Tip.error('操作提示', '请签名');
      return;
    }
    if (!text) {
      Tip.error('操作提示', '请填写时代答卷内容');
      return;
    }

    const { code = 500, data = [] } = await JTupload();
    if (code != 0) return;

    const res = await screensave({
      data: {
        answer: text,
        memCode: sessionInfo.code,
        signaturePath: data?.[0]?.url,
      },
    });
    if (res.code == 0) {
      Tip.success('操作提示', '保存成功');
      setTimeout(() => {
        router.push(`/qzs/screen/sddj`);
      }, 1000 * 5);
    }
  };

  const getInfo = async (record) => {
    const res = await findByCode({ memCode: record.code });
    if (res.code == 0) {
      if (_isEmpty(res.data)) return;

      if (_isEmpty(res.data.photo)) {
        setImg(require('../../../../assets/head.jpg'));
      } else {
        try {
          pullFileQz({ path: res.data.photo }).then((base64) => {
            setImg(base64);
          });
        } catch (error) {
          console.log('🚀 ~ getList ~ error:', error);
        }
      }

      // 签名截图
      try {
        if (res?.data?.signaturePath) {
          pullFileQz({ path: res?.data?.signaturePath }).then((sutra_base64) => {
            setSignImg(sutra_base64);
          });
        }
      } catch (e) {
        console.log('🚀 ~ getInfo ~ e:', e, '签名截图');
      }

      setInfo(res.data);
      if (res.data.answer) {
        setText(res.data.answer);
      } else {
        // const res = await findDomain({ memCode: record.code });
        // setText(res?.data?.[0]);
      }
    }
  };

  const onChange = async (v) => {
    setText(v);
  };

  // 签名
  const onOk = () => {};
  const handleCancel = () => {
    setVisible(false);
  };
  let sigCanvas: any;
  const clearSign = () => {
    sigCanvas.clear();
  };
  const handleSign = () => {
    setSignImg(sigCanvas.toDataURL('image/png'));
    setVisible(false);
  };

  // 模板
  const openMB = async () => {
    const res = await findDomain({ memCode: info.code });
    if (res.code == 0) {
      setMbList(res.data);
      setVisibleMB(true);
    }
    const audio = document.getElementById('backgroundMusic') as HTMLAudioElement;
    if (audio) {
      audio.play().catch((error) => {
        console.error('Error playing audio:', error);
      });
    }
  };
  const mubanChange = async (v) => {
    setMbKey(v);
  };
  const handleMuban = async () => {
    if (!_isEmpty(mbList) && mbKey) {
      const find = mbList[mbKey - 1];
      await setText('');
      setText(find);
      setVisibleMB(false);
    }
  };

  useEffect(() => {
    if (sessionInfo.code) {
      getInfo(sessionInfo);
    }
  }, [sessionInfo.code]);

  return (
    <div className={st.memShow}>
      <audio
        id="backgroundMusic"
        src="/slzzh.mp3"
        // autoPlay
        loop
        style={{ display: 'none' }}
      ></audio>
      {/* <img className={st.tit} src={require('../../../../assets/qzs/sddjtit.png')} alt="" /> */}
      <div className={st.box}>
        <div className={st.imgbox}>
          <img src={img} alt="" />
          <div className={st.name}>{sessionInfo.name}</div>

          <div className={st.shengri}>
            <div>政治生日</div>：
            <div>{moment(sessionInfo.joinOrgDate).format('YYYY年M月D日')}</div>
          </div>
          <div className={st.jieshaoren}>
            <div>所在党支部</div>：<div>{changeOrgName2(info.orgName)}</div>
          </div>
          {/* <div className={st.jieshaoren}><span>入党介绍人</span>：张勇、李强</div> */}
        </div>

        <div className={st.center}>
          <div className={st.info}>
            {text ? (
              <Editor id={'acPlan123'} key={1} onChange={onChange} init={text} />
            ) : (
              <Editor id={'acPlan123'} key={2} onChange={onChange} />
            )}
          </div>

          <div className={st.yulan}>
            {signImg && <img alt="" width={350} height={150} src={signImg} />}
          </div>

          <div className={st.btnBox}>
            <Button className={st.btn} onClick={openMB}>
              选择模板
            </Button>
            <Button
              className={st.btn}
              style={{ marginLeft: 10 }}
              onClick={() => {
                setVisible(true);
                setSignImg('');
              }}
            >
              签 名
            </Button>
            <Button className={st.btn} onClick={submit} style={{ marginLeft: 10 }}>
              保 存
            </Button>
          </div>
        </div>
      </div>
      {/* 模板 */}
      <Modal
        destroyOnClose
        visible={visibleMB}
        onCancel={() => {
          setVisibleMB(false);
        }}
        width={800}
        bodyStyle={{
          background: `url(${require('../../../../assets/qzs/qianmbg.png')})`,
          backgroundSize: '100% 100%',
          height: 500,
        }}
        footer={null}
      >
        <div className={st.muban}>
          <div className={st.mubanTab}>
            <Tabs activeKey={mbKey} type="card" onChange={mubanChange}>
              {mbList.map((it, index) => (
                <Tabs.TabPane tab={`模板${index + 1}`} key={index + 1}></Tabs.TabPane>
              ))}
            </Tabs>
            <div
              key={mbKey}
              className={st.mubanBody}
              dangerouslySetInnerHTML={{ __html: mbList?.[mbKey - 1] || '' }}
            />
          </div>
          <div className={st.buttonMubanTab}>
            {/* <Button onClick={clearSign} className={st.clearBtn}>
              清 除
            </Button> */}
            <Button onClick={handleMuban} className={st.signBtn} type="primary">
              确 认
            </Button>
          </div>
        </div>
      </Modal>
      {/* 签名 */}
      <Modal
        destroyOnClose
        visible={visible}
        onOk={onOk}
        onCancel={handleCancel}
        width={800}
        bodyStyle={{
          background: `url(${require('../../../../assets/qzs/qianmbg.png')})`,
          backgroundSize: '100% 100%',
        }}
        footer={null}
      >
        <div className={st.signContainer}>
          <div className={st.signContent}>
            <SignatureCanvas
              penColor="#000"
              canvasProps={{
                width: 700,
                height: 300,
                className: st.canvasContainer,
              }}
              ref={(ref) => {
                sigCanvas = ref;
              }}
              onBegin={() => {}}
            />
          </div>
          {/* <div className={st.yulan}>
            {signImg && <img alt="" width={500} height={300} src={signImg} />}
          </div> */}
        </div>
        <div className={st.buttonContainer}>
          <Button onClick={clearSign} className={st.clearBtn}>
            清 除
          </Button>
          <Button onClick={handleSign} className={st.signBtn} type="primary">
            确 认
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default memShow;
