import React, { useState, Fragment, useEffect } from 'react';
import _isEmpty from 'lodash/isEmpty';
import style from './index.less';
import { _history } from "@/utils/method";
import qs from 'qs';
import { Tabs } from 'antd';
import Become from './components/become'
import Ready from './components/ready'
import Exp from './components/exp'
import Supplement from './components/supplement'

const TabPane = Tabs.TabPane;
export default function Index(props: any) {
    const [activeTab, setActiveTab] = useState('1');
    const { location: { pathname = '' } = {} } = _history

    useEffect(() => {

    }, [])
    return (
        <div>
            <Tabs
                activeKey={activeTab}
                onChange={(e) => {
                    setActiveTab(e)
                }}
            >
                <TabPane tab="预备党员审核" key="1" />
                <TabPane tab="转正审核" key="2" />
                <TabPane tab="档案导出审核" key="3" />
                <TabPane tab="档案补充审核" key="4" />
            </Tabs>
            {
                activeTab === '1' && <Become /> 
            }
            {
                activeTab === '2' &&  <Ready />
            }
            {
                activeTab === '3' && <Exp/>
            }
            {
                activeTab === '4' && <Supplement/>
            }
        </div>
    );
}
