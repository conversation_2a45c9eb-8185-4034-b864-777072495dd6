import React from 'react';
import ListTable from '@/components/ListTable'
import {Tabs} from "antd";
import NowOrg from "@/components/NowOrg";
import Search from '@/components/Search';
import Preview from '../components/preview';
import {connect} from "dva";
import moment from 'moment';
import Tip from '@/components/Tip';
import {_history as router} from "@/utils/method";
import qs from 'qs';
import {getSession} from "@/utils/session";
import RuiFilter from "@/components/RuiFilter";
import WhiteSpace from '@/components/WhiteSpace';

const TabPane = Tabs.TabPane;

@connect(({workTrend,loading})=>({workTrend,loading:loading.effects['workTrend/checkTrendList']}))
export default class workTrend extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      title:'',
      editObj:undefined,
    }
  }
  addOrEdit=(item?:object)=>{
    if(item){
      this.setState({
        title:'审核动态',
        editObj:item,
      })
    }else{
      this.setState({
        title:'新增动态',
        editObj:undefined,
      })
    }
    this['addEdit'].setState({
      visible:true
    })
  };
  confirm=async (item)=>{//删除
    const obj=await this.props.dispatch({
      type:'workTrend/delTrend',
      payload:{
        data:{
          code:item['code']
        }
      }
    });
    if(obj && obj['code']===0){
      Tip.success('操作提示','删除成功');
      this.refresh();
    }
  };

  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  refresh=()=>{
    const {query}=this.props.location;
    const {current,pageSize}=this.props.workTrend.pagination;
    router.push(`?${qs.stringify({...query,pageNum:current,pageSize})}`)
  };
  previews=async (item)=>{
    const obj=await this.props.dispatch({
      type:'workTrend/findTrendByCode',
      payload:{
        code:item['teredCode']
      }
    });
    if(obj && obj['code']===0){
      this.setState({
        editObj:obj['data'] || undefined,
        prevCode:item['code']
      });
      this['preview'].setState({
        visible:true,
      })
    }
  };
  checkTrend=async (val)=>{
    const obj=await this.props.dispatch({
      type:'workTrend/checkTrend',
      payload:{
        data:{...val}
      }
    });
    if(obj && obj['code']===0){
      Tip.success('操作提示','审核成功');
      this.refresh();
      return true;
    }
  };
  search=(val)=>{
    this.props.dispatch({
      type:'workTrend/updateState',
      payload:{
        trendName:val
      }
    });
    this.action();
  };
  filterChange=(val)=>{
    this.props.dispatch({
      type:'workTrend/updateState',
      payload:{
        filter:val
      }
    });
    this.action();
  };
  action=(pageNum=1,pageSize=10)=>{
    const org=getSession('org') || {};
    this.props.dispatch({
      type:'workTrend/checkTrendList',
      payload:{
        data:{
          pageNum,
          pageSize,
          code:org['code'],
        }
      }
    })
  };
  componentWillUnmount(): void {
    this.props.dispatch({
      type:'workTrend/reSet',
    })
  }
  render(){
    const {title,editObj}=this.state;
    const {list2=[],pagination2={}}=this.props.workTrend;
    const {current,pageSize}=pagination2;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:58,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'标题',
        dataIndex:'tittle',
        width:200,
      },
      {
        title:'发布人',
        dataIndex:'createAccount',
        width:120,
      },
      {
        title:'发布状态',
        dataIndex:'status',
        width:120,
        render:(text)=>{
          switch (text) {
            case 0:
              return '待审核';
            case 1:
              return '已通过';
            case 2:
              return '未通过';
          }
        }
      },
      {
        title:'创建时间',
        dataIndex:'createTime',
        width:120,
        render:(text)=>{
          return moment(text).format('YYYY-MM-DD')
        }
      },
      {
        title:'审核人',
        dataIndex:'checkPerson',
        width:120,
      },
      {
        title:'操作',
        dataIndex:'action',
        width:100,
        render:(text,record)=>{
          return(
            <span>
              {
                record['status']===0 ? <a onClick={()=>this.previews(record)}>审核</a> : <a className={'eventNone'}>已审核</a>
              }
            </span>
          );
        },
      },
    ];
    const filterData=[
      {
        key:'status',name:'审核状态',value:[{key:0,name:'待审核',},{key:1,name:'已通过'},{key:2,name:'未通过'}],
      },
      {
        key:'checkTypeList',name:'推送类型',value:[{key:2,name:'下级审核'},{key:3,name:'我的审核'}],
      },
      {
        key:'typeList',name:'动态类型',value:[{key:1,name:'工作动态'}],
      },
    ];
    return(
      <div style={{height:'100%', overflow:'hidden'}}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        <NowOrg extra={
          <React.Fragment>
            <Search onChange={this.search}/>
          </React.Fragment>
        }/>
        <RuiFilter data={filterData}
                   openCloseChange={()=>setListHeight(this,20)}
                   onChange={this.filterChange}/>
        <WhiteSpace/>
        <WhiteSpace/>
        <Preview title={title} editObj={editObj} prevCode={this.state.prevCode} checkTrend={this.checkTrend} ref={e=>this['preview']=e} isAudit={true}/>
        <ListTable columns={columns} data={list2} pagination={pagination2} onPageChange={this.onPageChange}/>
      </div>
    )
  }
}
