import React, { Fragment, useEffect, useState, useImperativeHandle, useRef } from 'react';
import ListTable from '@/components/ListTable';
import { Modal, Button, Form, Input, } from 'antd';
import { exports, detail, ignore } from '@/pages/dataCheck/services';
import { getSession } from '@/utils/session';
import { fileDownloadbyUrl } from '@/utils/method';
import { checkCols, detailUrl } from './modalConfig';
import { connect } from 'dva';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _last from 'lodash/last';
import _get from 'lodash/get';

import AddEdit from '@/pages/mem/manage/components/membasic/AddEdit';
import AddDevlop from '@/pages/developMem/develop/components/Add';
import AddOrEdit from '@/pages/org/list/subpage/addoredit';
import AddOrEditt from '@/pages/[unit]/subpage/addoredit/';


const { TextArea } = Input;

const IgnoreReason = React.forwardRef((props: any, ref) => {
  const { onOK } = props;
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 },
    },
  };
  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleCancel = () => {
    setVisible(false);
    form.resetFields();
  };
  const onFinish = async (e) => {
    setConfirmLoading(true);
    const { code = 500 } = await ignore({
      data: {
        logicCheckName: _get(record, 'query.condition', undefined),
        logicCheckCode: _get(record, 'query.id', undefined),
        code: _get(record, 'record.code', undefined),
        reason: _get(e, 'reason'),
        type: _get(record, 'query.table', undefined),
      }
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOK && onOK();
    }
  };
  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      setRecord(query);
      // if (query) {
      //   form.setFieldsValue({ ...query.reqcord });
      // }
    },
    close: () => {
      handleCancel();
    },
    clear: () => {
      // clear();
    },
  }));
  return (
    <Modal
      title={'忽略'}
      visible={visible}
      // onOk={() => {
      //   form.submit()
      // }}
      onCancel={handleCancel}
      width={600}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
      footer={[
        <Button key={1} htmlType={'button'} onClick={handleCancel} style={{ marginRight: '5px' }}>取消</Button>,
        <Button key={2} htmlType={'button'} type={'primary'} loading={confirmLoading} onClick={() => { form.submit() }}>确定</Button>
        // <Popconfirm placement="topRight" title={<span>
        //   本功能仅用于原库中党员入党时间有误的修正，请谨慎操作。
        // </span>} onConfirm={() => { form.submit() }}>
        // </Popconfirm>,
      ]}
    >
      {
        visible &&
        <Fragment>
          <Form form={form} {...formItemLayout} onFinish={onFinish}>
            <Form.Item name='reason'
              label="特殊情况说明"
              rules={[{ required: true, message: '忽略原因' },
              { type: 'string', min: 20, message: '不能少于20字' }
              ]}
            >
              <TextArea maxLength={300} placeholder='如有特殊情况，请认真核实后在此填写特殊情况说明，不少于20字。' showCount />
            </Form.Item>
          </Form>
        </Fragment>
      }

    </Modal>
  )
});

const ModalTL = React.forwardRef((props: any, ref) => {
  const {
    title = '详情',
    width = 1000,
    onOK,
  } = props;
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState([]);
  const [query, setQurey] = useState<any>({});
  const [pagination, setPagination] = useState({ pageSize: 10, total: 0, pageNum: 1, current: 1 });
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const org: any = getSession('org') || {};
  const ignoreReason: any = useRef();
  const addDevlopRef: any = useRef();


  const renderMemDetail = (text, record) => {
    return (
      <React.Fragment>
        <a onClick={async () => {
          AddEdit['WrappedComponent'].show();
          if (record && record['code']) {
            await props.dispatch({
              type: 'memBasic/findMem',
              payload: {
                code: record['code'],
              }
            })
          }
        }}>{text}</a>
        {/* <AddEdit
          hideSave={true}
          // destroy={() => onSearch({ list: searchQuery.queryDtos }, { pageNum: 1 })}
          menuDataKey={['1']} /> */}
      </React.Fragment>
    )
  };

  const renderDevelopDetail = (text, record) => {
    return (
      <React.Fragment>
        <a onClick={async () => {
          const { canEdit: _canEdit = [] } = props;
          addDevlopRef.current.destroy();
          if (record && record['code']) {
            await props.dispatch({
              type: 'memDevelop/findMem',
              payload: {
                code: record['code'],
                // type: _get(_canEdit, '[0]', undefined) == 'mems' ? '2' : undefined,
                type: record['type'],
              }
            })
          }
          addDevlopRef.current.open({ canEdit: false, editType: 'default' });
        }}>{text}</a>
        {/* <AddDevlop wrappedComponentRef={addDevlopRef} onsubmit={() => { }} {...props} tipMsg={{}} /> */}
      </React.Fragment>
    )
  };

  const renderOrgDetail = (text, record) => {
    return (
      <React.Fragment>
        <a onClick={async () => {
          AddOrEdit['WrappedComponent'].clear();
          if (record && record['code']) {
            props.dispatch({
              type: 'org/findOrg',
              payload: {
                code: record['code'],
              },
            }).then(res => {
              AddOrEdit['WrappedComponent'].show();
            });
          } else {
            AddOrEdit['WrappedComponent'].show();
          }
        }}>{text}</a>
        {/* <AddOrEdit dataInfo={{}} hideSave={true} menuDataKey={['1']} /> */}
      </React.Fragment>
    )
  };

  const renderUnitDetail = (text, record) => {
    return (
      <React.Fragment>
        <a onClick={async () => {
          AddOrEditt['WrappedComponent'].clear();
          if (record && record['code']) {
            props.dispatch({
              type: 'unit/findOrg',
              payload: {
                code: record['code']
              }
            }).then(res => {
              AddOrEditt['WrappedComponent'].show();
            })
          } else {
            AddOrEditt['WrappedComponent'].show();
          }
        }}>{text}</a>
        {/* <AddOrEditt dataInfo={{}} hideSave={true} menuDataKey={['1']} /> */}
      </React.Fragment>
    )
  };


  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      if (!_isEmpty(query)) {
        setQurey(query);
        getList(query);
      }
    },
    clear: () => {
      // clear();
    },
  }));
  useEffect(() => {
  }, [JSON.stringify(query)])

  const getList = async (record, p = {}) => {
    let url = detailUrl(query);
    setTableLoading(true);
    const { code = 500, data: { list = [], ...ohter } = {} } = await url({
      data: {
        orgCode: org?.orgCode,
        pageNum: pagination?.pageNum,
        pageSize: pagination?.pageSize,
        id: record?.id,
        ...p,
      }
    });
    setTableLoading(false);
    if (code == 0) {
      setList(list);
      setPagination({ pageSize: ohter?.pageSize, total: ohter?.totalRow, pageNum: ohter?.pageNumber, current: ohter?.pageNumber, })
    }
  };
  const handleOk = () => {
    onOK && onOK(query);
    handleCancel();
  };
  const handleCancel = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setQurey({});
    setList([]);
    setPagination({ pageSize: 10, total: 0, pageNum: 1, current: 1 })
  };

  const columns: any = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 50,
      render: (text, record, index) => {
        return (pagination['pageNum'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    // 通过id或者tableName去获取反查的表头， query当前反查的信息 ，
    ...checkCols(query, props, { renderMemDetail, renderDevelopDetail, renderOrgDetail, renderUnitDetail }),
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      render: (text, record) => {
        return (
          <span>
            <a onClick={() => {
              // record 忽略的当条信息 query 外部列表数据
              ignoreReason.current.open({ record, query });
            }}>忽略</a>
          </span>
        );
      },
    },
  ];

  return (
    <Fragment>
      <Modal
        title={title}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={width}
        footer={null}
        destroyOnClose={true}
      >
        <div style={{ marginBottom: 10 }}>
          <Button type={'primary'} loading={downloadLoading} onClick={async () => {
            setDownloadLoading(true);
            const { code = 500, data: { url = '' } = {} } = await exports({
              data: {
                orgCode: org?.orgCode,
                tableName: query?.tableName,
                id: query?.id,
              }
            });
            setDownloadLoading(false);
          }}>导出</Button>
        </div>
        <ListTable
          columns={columns}
          data={list}
          pagination={pagination}
          scroll={{
            y: 500,
          }}

          onPageChange={(pageNum, pageSize) => getList(query, { pageNum, pageSize })}
          rowKey={'id'} />
      </Modal>

      {/* 党员，发展党员，组织，单位详情弹框 */}
      <AddEdit
        hideSave={true}
        // destroy={() => onSearch({ list: searchQuery.queryDtos }, { pageNum: 1 })}
        menuDataKey={['1']} />
      <AddDevlop wrappedComponentRef={addDevlopRef} onsubmit={() => { }} {...props} tipMsg={{}} />
      <AddOrEdit dataInfo={{}} hideSave={true} menuDataKey={['1']} />
      <AddOrEditt dataInfo={{}} hideSave={true} menuDataKey={['1']} />

      <IgnoreReason ref={ignoreReason} onOK={() => {
        handleCancel();
        onOK && onOK(query);
      }} />
    </Fragment>
  )
});

export default connect(({ commonDict, memBasic, memDevelop, org, unit }: any) => ({ commonDict, memBasic, memDevelop, org, unit }), undefined, undefined, { forwardRef: true })(ModalTL)
