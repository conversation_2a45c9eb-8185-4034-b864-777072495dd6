import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import { getList, addMemAbroad, updateMemAbroad, findByCode, delMemAbroad } from '../services/memAbroad';
import {getSession} from "@/utils/session"; //模拟数据
import { changeListPayQuery } from '@/utils/method.js';
const memAbroad = modelExtend(listPageModel,{
  namespace: "memAbroad",
  state:{
    abroadInfo:{},
    list:[],
    pagination:{}
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        // const { pathname, query } = location;
      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put }) {
      const {data={}} = yield call(getList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    // 新增编辑保存
    *save({ payload }, { call, put }) {
      const {type = '',data = {}} = payload;
      let res;
      switch (type) {
        case 'add':
          res = yield call(addMemAbroad,{data});
          break;
        case 'edit':
          res = yield call(updateMemAbroad,{data});
          break;
        default:
          break;
      }
      return res;
    },
    // 回显
    *getInfo({ payload }, { call, put }) {
      const {data = {}} = yield call(findByCode, payload);
      yield put({
        type: 'updateState',
        payload: {
          abroadInfo:data
        }
      })
    },
    // 删除
    *del({ payload }, { call, put }) {
      const res = yield call(delMemAbroad, payload);
      return res;
    },
    //清除
    *clear({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          abroadInfo:{},
          list:[],
          pagination:{}
        }
      })
    },
  }
});
export default memAbroad;
