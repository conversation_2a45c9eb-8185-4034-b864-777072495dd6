.modal-dev-information {
  width: 100%;
  display: flex;
  // height: 600px;
  height: 100%;

  .ant-menu-item-selected,
  .ant-menu-item:hover {
    background-color: red !important;
    color: white;
  }
  .ant-menu-inline .ant-menu-item::after {
    display: none !important;
  }
  .ant-menu-submenu-selected,
  .ant-menu-submenu:hover,
  .ant-menu-submenu-title:hover {
    color: red !important;
  }
}
.modal-dev-information1 {
  background: url('../../../assets/mem/bg.png') no-repeat;
  background-size: 100% 100%;
}
.modal-dev-information-title {
  font-weight: 900;
  text-align: center;
}
.modal-dev-information-content {
  flex: 1;
  height: 100%;
   overflow: auto;

}
.modal-dev-information-menu {
  width: 300px;
  margin-right: 10px;
  //   height: 600px;
  height: 100%;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

.content-children {
  font-size: 16px;
}
.content-title {
  color: black;
  font-size: 18px;
  cursor: pointer;
  &:hover {
    color: black;
  }
}
