import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Modal, Switch, Alert } from 'antd';
import MemSelect from '@/components/MemSelect';
import moment from 'moment';
import Tip from '@/components/Tip';
import { findDictCodeName, unixMoment } from '@/utils/method.js';
import _isNumber from 'lodash/isNumber';
import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';
import Date from '@/components/Date';
import { compare, compareDate } from '@/pages/developMem/services/index';
import UploadComp from '@/components/UploadComp';
import Sure from '../SureBasicInfoAndChange';
const { TextArea } = Input;

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      memInfo: {},
      hasMemValue: true,
      isFullYear: true,
      // showIsFullYear: false,
      timeKey: moment().valueOf(),
      specialChecked: false,
    };
  }
  // 时间限制
  disabledTomorrow = (current) => {
    const { memInfo: { applyDate = '' } = {} } = this.state;
    const cu = moment(current);
    const start = moment(applyDate).endOf('day');
    const end = moment();
    if (_isNumber(applyDate)) {
      return (
        current && (cu.isBefore(start) || cu.isSame(start) || cu.isAfter(end) || cu.isSame(end))
      );
    } else {
      return false;
    }
  };
  // 是否满一年的判断
  // timeCompare=async(e)=>{
  //   const {code = 500,data=true} = await compare({data:{
  //     code:this.state.memInfo.code,
  //     type:4,
  //     time:moment(e).valueOf()
  //   }});
  //   if(code === 0){
  //     this.setState({
  //       isFullYear:data
  //     })
  //   }
  // }
  // timeValidator=async(rule, value, callback)=>{
  //   const {code = 500,data=true} = await compareDate({data:{
  //     code:this.state.memInfo.code,
  //     type:4,
  //     time:moment(value).valueOf()
  //   }});
  //   if(code === 0){
  //     if (!data) {
  //       callback(new Error('确定积极分子时间早于入党申请时间'));
  //     }else{
  //       callback();
  //     }
  //   }
  // }

  // 特殊情况下发展 时间校验
  validatorSpecialTime = (rule, value, callback) => {
    const { specialChecked } = this.state;
    const { applyDate = moment().valueOf() } = this.state.memInfo;
    if (!specialChecked) {
      if (value && value.isBefore(moment(applyDate).add(3, 'M'))) {
        callback(new Error('入党申请不满3个月，不可确定为积极分子！'));
      }
    }
    callback();
  };

  // 是否 特殊情况下发展
  handleSpecialChange = (e) => {
    this.setState({ specialChecked: e },()=>{
      this.props.form.validateFields(['activeDate'], { force: true });
    });
  };
  memLength = (rule, value, callback) => {
    if (_isArray(value) && !_isEmpty(value)) {
      if (value.length >= 3) {
        callback('只能选择1~2名人员');
      } else {
        callback();
      }
    } else {
      callback();
    }
  };
  handleOk = () => {
    const { submit, memDevelop: { basicInfo = {} } = {} } = this.props;
    const { memInfo, hasMemValue } = this.state;
    const {
      name,
      code: memCode,
      orgCode,
      orgName,
      orgZbCode,
      developOrgCode: logOrgCode,
      d08Code,
      d08Name,
    } = memInfo;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if(basicInfo.hasUnitStatistics == null){
          Tip.error('操作提示', '请完善人事关系是否在党组织关联单位内等信息。');
          return;
        }
        [
          'isHighKnowledge',
          'hasWorker',
          'hasYoungFarmers',
          'isAdvancedModel',
          'isDispatch',
          'isFarmer',
          'isOutSystem',
          'hasStaffOrganization',
        ].map((item) => {
          val[`${item}`] = val[`${item}`] == '1' ? 1 : 0;
        });
        // 增加字典表的name
        val = findDictCodeName(
          [
            'd49',
            'd18',
            'd19',
            'd20',
            'd21',
            'd07',
            'd09',
            'd11',
            'd27',
            'd28',
            'd06',
            'd08',
            'd60',
            'd88',
            'readingProfessional',
            'politics',
            'advancedModel',
          ],
          val,
          basicInfo,
        );

        val = unixMoment(['activeDate'], val);
        val['toactiveContextPerson'] = _isEmpty(val['toactiveContextPerson'])
          ? ''
          : hasMemValue
          ? val['toactiveContextPerson'].map((item) => item['code']).toString()
          : val['toactiveContextPerson'];
        val['name'] = name;
        val['memCode'] = memCode;
        val['orgCode'] = orgCode;
        val['orgName'] = orgName;
        val['orgZbCode'] = orgZbCode;
        val['logOrgCode'] = logOrgCode;
        val['d08Code'] = d08Code;
        val['d08Name'] = d08Name;

        const res = await this.props.dispatch({
          type: 'memDevelop/toActive',
          payload: { data: { ...val } },
        });
        const { code = 500 } = res;
        if (code === 0) {
          this.handleCancel();
          Tip.success('操作提示', '操作成功');
          submit && submit();
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  open = (record) => {
    this.setState({ visible: true, memInfo: record, timeKey: moment().valueOf() });
    this.props.dispatch({
      type: 'memDevelop/findMem',
      payload: {
        code: record['code'],
      },
    });
  };
  destroy = () => {
    this.setState({
      memInfo: {},
      hasMemValue: true,
      isFullYear: true,
      //  showIsFullYear: false,
      specialChecked: false,
    });
  };
  hasMemOnChange = (val) => {
    this.props.form.setFieldsValue({ toactiveContextPerson: undefined });
    this.setState({ hasMemValue: val });
  };
  render() {
    const { form, loading: { effects = {} } = {} } = this.props;
    const { getFieldDecorator } = form;
    const { visible, hasMemValue, specialChecked } = this.state;
    return (
      <Modal
        destroyOnClose
        title="确定积极分子"
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        maskClosable={false}
        confirmLoading={effects['memDevelop/toActive']}
        width={800}
      >
        {visible && (
          <Fragment key={this.state.timeKey}>
            <Alert
              message="提示：确定入党积极分子时间为支委会研究确定为入党积极分子的时间。"
              type="info"
              showIcon
            />
            <div style={{ marginBottom: '10px' }} />
            <Form>
              <FormItem
                label="联系人是否为本组织人员" // hasStaffOrganization
                {...formItemLayout}
              >
                {getFieldDecorator('hasStaffOrganization', {
                  rules: [{ required: true, message: '联系人是否为本组织人员' }],
                  initialValue: hasMemValue,
                })(
                  <Switch
                    checkedChildren="是"
                    unCheckedChildren="否"
                    onChange={this.hasMemOnChange}
                    checked={hasMemValue}
                  />,
                )}
              </FormItem>
              {hasMemValue ? (
                <FormItem label="入党积极分子培养联系人" {...formItemLayout}>
                  {getFieldDecorator('toactiveContextPerson', {
                    rules: [
                      { required: true, message: '入党积极分子培养联系人' },
                      { validator: this.memLength },
                    ],
                    // initialValue:hasMemValue,
                  })(<MemSelect checkType={'checkbox'} placeholder="请选择" />)}
                </FormItem>
              ) : (
                <FormItem label="入党积极分子培养联系人" {...formItemLayout}>
                  {getFieldDecorator('toactiveContextPerson', {
                    rules: [{ required: true, message: '入党积极分子培养联系人' }],
                    // initialValue:hasMemValue,
                  })(<Input placeholder="请选择" />)}
                </FormItem>
              )}
              {/* <FormItem
                label="特殊情况下发展" // hasSpecialDevelopment
                {...formItemLayout}
              >
                {getFieldDecorator('hasSpecialDevelopment', {
                  rules: [{ required: true, message: '是否特殊情况下发展' }],
                  initialValue: false,
                })(
                  <Switch
                    checkedChildren="是"
                    unCheckedChildren="否"
                    onChange={this.handleSpecialChange}
                    checked={specialChecked}
                  />,
                )}
              </FormItem> */}
              <FormItem label="确定积极分子时间" {...formItemLayout}>
                {getFieldDecorator('activeDate', {
                  rules: [
                    { required: true, message: '确定积极分子时间' },
                    // { validator: this.validatorSpecialTime, trigger: ['blur', 'change'] },
                  ],
                  // initialValue:hasMemValue,
                  // <DatePicker disabledDate={this.disabledTomorrow} style={{width:'100%'}} />
                })(
                  <Date
                    disabledDate={this.disabledTomorrow}
                    onChange={(e) => {
                      // this.timeCompare(e)
                    }}
                  />,
                )}
              </FormItem>
              {/* <Sure {...this.props} memInfo={this.state.memInfo} /> */}
              {/* {this.state.isFullYear===false && <FormItem
                  label="申请不满一年说明"
                  {...formItemLayout}
                >
                  {getFieldDecorator('instructions', {
                    rules: [{ required: true, message: '申请不满一年说明' }],
                  })(

                    <TextArea rows={2} />
                  )}
                </FormItem>} */}
              {/* <FormItem
                  label="支委会会议记录扫描件"
                  {...formItemLayout}
                >
                  {getFieldDecorator('toactiveApplyScanFile', {
                    rules: [{ required: false, message: '支委会会议记录扫描件' }],
                    // initialValue:hasMemValue,
                  })(
                    <UploadComp />
                  )}
                </FormItem> */}
            </Form>
          </Fragment>
        )}
      </Modal>
    );
  }
}
export default Form.create()(index);
