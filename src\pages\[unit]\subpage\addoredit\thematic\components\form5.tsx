// 社区党建有关情况
import React, { Fragment, useEffect, useState } from 'react';
import { Button, Form, InputNumber, Row, Col, Switch, Divider } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import { formItemLayout, formItemLayout2, formItemLayout3 } from './config';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import _cloneDeep from 'lodash/cloneDeep';
import Tip from '@/components/Tip';
import { saveForm5, findForm } from '@/pages/[unit]/services/thematic';

const index = (props: any) => {
  const [form] = Form.useForm();
  const { selectTab, basicInfo } = props;
  const { code: unitCode = '' } = basicInfo;
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);

  const onFinish = async (e) => {
    setLoading(true);
    let val = _cloneDeep(e);
    ['hasCommitteeSystem'].map((it) => {
      val[it] = val[it] ? 1 : 0;
    });
    if (e['communityWorkWage'] > 200) {
      Tip.error('操作提示', '全部社区工作者年工资总额（万元）不能大于200')
      return;
    }
    if (e['communitySecretaryWage'] > 200) {
      Tip.error('操作提示', '全部社区党组织书记年工资总额（万元）不能大于200')
      return;
    }
    if (e['intoBudgetMoney'] > 200) {
      Tip.error('操作提示', '社区纳入财政预算的工作经费总额（万元）不能大于200')
      return;
    }
    if (e['annualServiceFunds'] > 200) {
      Tip.error('操作提示', '社区全年服务群众专项经费总额（万元）不能大于200')
      return;
    }
    const { code = 500 } = await saveForm5({
      data: {
        unitCode,
        ...val,
      },
    });
    setLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '修改成功');
    }
  };
  const getInfo = async () => {
    const { code = 500, data = {} } = await findForm({
      unitCode,
      type: '5',
    });
    if (code === 0) {
      setQuery(data);
      form.setFieldsValue(data);
    }
  };
  useEffect(() => {
    getInfo();
  }, []);

  return (
    <Fragment>
      <Form form={form} {...formItemLayout3} onFinish={onFinish}>
        <Row>
          <Col span={12}>
            <Form.Item name="partyWorker" label="专职党务工作者">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="collegeOrAbove" label="大专及以上学历">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="governmentStreetsSelect" label="从机关和街道选派的">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="selectFromVeterans" label="从退役军人中选聘的">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="civilServiceRecruitment" label="录用为公务员的">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="selectIntoEstablishment" label="选拔进入事业编制的">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="representativeMember" label="推荐为两代表一委员的">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="lessThanPastYear"
              label="按不低于上年度当地社会平均工资水平确定报酬的社区"
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="communityWorkWage" label="全部社区工作者年工资总额（万元）">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="communitySecretaryWage" label="全部社区党组织书记年工资总额（万元）">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="jobInstructor" label="党建工作指导员">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="jobLevelWorkersCommunity" label="建立社区工作者岗位等级序列的社区">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="practiceFilingManagement"
              label="社区党组织书记实行县级党委备案管理的县（市、区、旗）"
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="intoBudget" label="纳入财政预算的社区">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="intoBudgetMoney" label="社区纳入财政预算的工作经费总额（万元）">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="serviceFunds" label="落实服务群众专项经费的社区">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="annualServiceFunds" label="社区全年服务群众专项经费总额（万元）">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="hasCommitteeSystem"
              label="实行兼职委员制的社区"
              initialValue={query['hasCommitteeSystem'] || 0}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="membersReportAnnualService"
              label="开展在职党员到社区报到为群众服务的社区 "
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="membersReportCommunity" label="到社区报到的在职党员">
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
        </Row>
        <div style={{ textAlign: 'center' }}>
          <WhiteSpace />
          <Button
            type={'primary'}
            htmlType={'submit'}
            icon={<LegacyIcon type={'check'} />}
            // onClick={() => {}}
            style={{ marginRight: 16 }}
            loading={loading}
          >
            保存
          </Button>
          {/* <Button
            type={'primary'}
            danger
            htmlType={'button'}
            icon={<LegacyIcon type={'delete'} />}
            onClick={() => {}}
          >
            取消
          </Button> */}
        </div>
      </Form>
    </Fragment>
  );
};
export default index;
