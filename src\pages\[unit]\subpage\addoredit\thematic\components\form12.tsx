import React, { Fragment, useEffect, useState } from 'react';
import { Button, Col, Form, InputNumber, Row, Switch } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import { formItemLayout3 } from './config';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import { findZtDataByCode, saveZt12Data } from '@/pages/[unit]/services/thematic';
import Tip from '@/components/Tip';
import { Icon as LegacyIcon } from '@ant-design/compatible';

const index = (props: any) => {
  const { selectTab, basicInfo } = props;
  const { code: unitCode = '' } = basicInfo;
  const [form] = Form.useForm();
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);

  const onFinish = async (e) => {
    setLoading(true);
    let val = _cloneDeep(e);
    ['isPrincipalParty'].map((it) => {
      val[it] = val[it] ? 1 : 0;
    });
    const { code = 500 } = await saveZt12Data({
      data: {
        unitCode,
        ...val,
      },
    });
    setLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '修改成功');
      getInfo();
    }
  };
  const getInfo = async () => {
    const { code = 500, data = {} } = await findZtDataByCode({
      unitCode,
      type: '12',
    });
    if (code === 0) {
      setQuery(data);
      form.setFieldsValue(data);
    }
  };
  useEffect(() => {
    getInfo();
  }, []);
  return (
    <Fragment>
      <Form form={form} {...formItemLayout3} onFinish={onFinish}>
        <Row>
          <Col span={12}>
            <Form.Item
              name="buildUniteBranchNum"
              label="建立联合党支部数"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="aboveBachelorNum"
              label="本科以上学历（在岗职工）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="aboveGraduateNum"
              label="研究生以上学历（在岗职工）"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="partyNum" label="党员" rules={[{ required: true }]}>
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="currentYearDevelopNum"
              label="本年度发展的"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="notTurnedRelationNum"
              label="未转组织关系党员人数"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="secretaryNum" label="党组织书记" rules={[{ required: true }]}>
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="secretaryHoldMiddleManagerNum"
              label="由企业中高层管理人员担任的"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isPrincipalParty"
              initialValue={_get(query, 'isPrincipalParty', false)}
              label="企业主要负责人是党员的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>

          <div style={{ width: '100%', textAlign: 'center' }}>
            <WhiteSpace />
            <Button
              icon={<LegacyIcon type={'check'} />}
              type={'primary'}
              onClick={() => {
                form.submit();
              }}
              loading={loading}
            >
              保存
            </Button>
          </div>
        </Row>
      </Form>
    </Fragment>
  );
};
export default index;
