import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, Input, DatePicker, Button, Upload, message } from 'antd';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import Date from '@/components/Date';
import UploadComp from '@/components/UploadComp';
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state={}
  }
  // 时间限制
  disabledTomorrow=(current)=>{
    return current && current > moment().endOf('day');
  };

  render() {
    const {form,memInfo} = this.props;
    const {getFieldDecorator} = form;
    const props = {
      name: 'file',
      action: '/api/base/upload',
      headers: {
        authorization: sessionStorage.getItem('token') || "",dataApi: sessionStorage.getItem('dataApi') || ""
      },
      onChange(info) {
        if (info.file.status !== 'uploading') {
          console.log(info.file, info.fileList);
        }
        if (info.file.status === 'done') {
          message.success(`${info.file.name} 上传成功`);
        } else if (info.file.status === 'error') {
          message.error(`${info.file.name} 上传失败.`);
        }
      },
    };
    return (
      <div>
        <FormItem
          label="转正人员"
          {...formItemLayout}
        >
          {memInfo['name'] || ''}
        </FormItem>
        <FormItem
          label="预备期满时间"
          {...formItemLayout}
        >
          {_isNumber(memInfo['extendPreparDate']) ? moment(memInfo['extendPreparDate']).format('YYYY-MM-DD') : ''}
        </FormItem>
        {/* <FormItem
          label="上级党委审批日期"
          {...formItemLayout}
        >
          {getFieldDecorator('topartCommitteeDate', {
            rules: [{ required: true, message: '请输入上级党委审批日期' }],
            // initialValue:_isEmpty(basicInfo)?undefined:basicInfo['name'],
            // <DatePicker placeholder={'请输入上级党委审批日期'} disabledDate={this.disabledTomorrow} style={{width:'100%'}}/>
          })(
            <Date disabledDate={this.disabledTomorrow} />
          )}
        </FormItem> */}
        {/* <FormItem
          label="上级党委审批通知扫描件"
          {...formItemLayout}
        >
          {getFieldDecorator('topartCommitteeFileUrl', {
            rules: [{ required: false, message: '上级党委审批通知扫描件' }],
          })(
            <UploadComp maxLen={1}/>
          )}支部大会讨论
        </FormItem> */}
        <FormItem
          label="支部大会讨论通过时间"
          {...formItemLayout}
        >
          {getFieldDecorator('topartTurnPartyDate', {
            rules: [{ required: true, message: '支部大会讨论通过时间' }],
            // initialValue:_isEmpty(basicInfo)?undefined:basicInfo['name'],
            // <DatePicker placeholder={'请输入召开支部大会日期'} disabledDate={this.disabledTomorrow} style={{width:'100%'}} />
            // moment(memInfo['extendPreparDate']
          })(
            <Date
            // startTime={
            //   moment(memInfo['extendPreparDate']).valueOf() - moment('2021.12.31').valueOf() >= 0 ? moment('2021.12.31') : moment(memInfo['extendPreparDate'])
            // } endTime={
            //   moment(memInfo['extendPreparDate']).valueOf() - moment('2021.12.31').valueOf() >= 0 ? moment(memInfo['extendPreparDate']) : moment('2021.12.31')
            // }
            isDefaultEnd={false}/>
          )}
        </FormItem>
        {/* <FormItem
          label="支委会会议记录扫描件"
          {...formItemLayout}
        >
          {getFieldDecorator('topartFileUrl', {
            rules: [{ required: false, message: '支委会会议记录扫描件' }],
            // initialValue:_isEmpty(basicInfo)?undefined:basicInfo['name'],
          })(
            <UploadComp maxLen={1}/>
          )}
        </FormItem> */}
        {/* <FormItem
          label="入党宣誓日期"
          {...formItemLayout}
        >
          {getFieldDecorator('topartOathDate', {
            rules: [{ required: true, message: '请输入入党宣誓日期' }],
            // initialValue:_isEmpty(basicInfo)?undefined:basicInfo['name'],
            // <DatePicker placeholder={'请输入入党宣誓日期'} disabledDate={this.disabledTomorrow} style={{width:'100%'}} />
          })(
            <Date  disabledDate={this.disabledTomorrow}/>
          )}
        </FormItem> */}
        {/* <FormItem
          label="入党宣誓照片"
          {...formItemLayout}
        >
          {getFieldDecorator('topartOathDateUrl', {
            rules: [{ required: false, message: '入党宣誓照片' }],
            // initialValue:_isEmpty(basicInfo)?undefined:basicInfo['name'],
          })(
            <UploadComp maxLen={1}/>
          )}
        </FormItem> */}
      </div>
    );
  }
}
export default Form.create<any>()(index);
