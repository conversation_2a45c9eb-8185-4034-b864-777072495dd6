import { Space } from 'antd';
import React, { useState, useRef, useImperativeHandle, useEffect } from 'react';
import ReactCrop, { Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import left from '@/assets/mem/left.png';
import right from '@/assets/mem/right.png';
import { convertToWebpBeforeUpload, _history } from '@/utils/method';

const ImageCropper = (props: any, ref: any) => {
  const [src, setSrc] = useState<string | null>(null); // 图片源
  const [crop, setCrop] = useState<Crop>({
    unit: '%', // 使用百分比单位
    x: 0, // 初始化 x 坐标
    y: 0, // 初始化 y 坐标
    width: 100,
    height: 100,
  });
  useImperativeHandle(ref, () => ({
    show: (str: any) => {
      setSrc(props.src);
    },
    geturl: () => {
      return croppedImageUrl;
    },
    clears: () => {
      clear();
    },
  }));

  const [croppedImageUrl, setCroppedImageUrl]: any = useState(null);
  const [cImageUrl, setCImageUrl]: any = useState(require('@/assets/mm.jpeg'));
  const [consturl, setConsturl]: any = useState(require('@/assets/mm.jpeg'));
  const hasSetWidth = useRef(false);
  const [rotate, setRotate] = useState(0);
  const [scales, setScales] = useState(1);
  const [imgWidth, setImgWidth] = useState(0);
  const [imgHeight, setImgHeight] = useState(0);
  const imageRef = useRef<HTMLImageElement | null>(null);

  const onImageLoaded = (image: HTMLImageElement) => {
    imageRef.current = image;
  };

  const onCropComplete = (crop: Crop) => {
    console.log(imageRef.current, crop, 'imageRef.current');
    if (imageRef.current && crop.width && crop.height) {
      //   const croppedUrl = getCroppedImg(imageRef.current, crop);
      // const canvas = getCroppedImg1(imageRef.current, crop);
      // canvas.toBlob(blob => {
      //     setCImageUrl(URL.createObjectURL(blob));
      // });

      getCroppedImg(imageRef.current, crop).then((croppedBlob) => {
        if (croppedBlob) {
          const file = new File([croppedBlob], 'cropped-image.webp', { type: 'image/webp' });
          setCroppedImageUrl(file);
        }
      });
    }
  };

  const onCropChange = (newCrop: Crop) => {
    console.log(newCrop, 'newCropnewCropnewCrop');
    setCrop(newCrop);
  };

  const getCroppedImg = (image: HTMLImageElement, crop: Crop): Promise<Blob | null> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;
      canvas.width = crop.width!;
      canvas.height = crop.height!;
      const ctx = canvas.getContext('2d');

      if (ctx) {
        ctx.drawImage(image, crop.x! * scaleX, crop.y! * scaleY, crop.width! * scaleX, crop.height! * scaleY, 0, 0, crop.width!, crop.height!);
      }

      canvas.toBlob((blob) => {
        resolve(blob); // 返回裁剪后的 Blob 对象
      }, 'image/webp');
    });
  };
  const getCroppedImg1 = (image: HTMLImageElement, crop: Crop): any => {
    const canvas = document.createElement('canvas');
    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;
    canvas.width = crop.width;
    canvas.height = crop.height;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      ctx.drawImage(image, crop.x * scaleX, crop.y * scaleY, crop.width * scaleX, crop.height * scaleY, 0, 0, crop.width, crop.height);
    }

    return canvas;
  };

  const handleFileChange = () => {
    // const file = e.target.files?.[0];
    // if (file) {
    //   const reader = new FileReader();
    //   reader.onload = () => {
    //     setSrc(reader.result as string);
    //   };
    //   reader.readAsDataURL(file);
    // }
    setSrc(props.src);
  };
  const rotateImage = (type) => {
    // setRotate(rotate + 90)
    const img = document.getElementById('rotateImage') as HTMLImageElement;
    let imgw = img.offsetWidth;
    let imgh = img.offsetHeight;
    const container = img.parentElement as HTMLElement;
    const transform = window.getComputedStyle(img).transform;

    let currentRotation = 0; // 默认旋转角度为 0
    if (transform !== 'none') {
      // 如果 transform 不为 "none"，解析矩阵值
      const values = transform.match(/matrix\(([^)]+)\)/);
      if (values) {
        const matrix = values[1].split(', ');
        const a = parseFloat(matrix[0]); // 矩阵的 a 值
        const b = parseFloat(matrix[1]); // 矩阵的 b 值
        console.log(a, b, '矩阵');
        currentRotation = Math.round(Math.atan2(b, a) * (180 / Math.PI)); // 计算当前旋转角度
      }
    }
    const newRotation = currentRotation + (type === 'z' ? 90 : -90);
    // rotateImage
    console.log(`当前旋转角度: ${newRotation}°`);
    // 动态调整父容器的宽高
    if (newRotation % 180 !== 0) {
      console.log(imgh, imgw, '图片宽高');
      container.style.width = `${imgh}px`; // 容器宽度设置为图片高度
      container.style.height = `${imgw}px`; // 容器高度设置为图片宽度
      img.style.width = `${imgw}px`;
      img.style.height = `${imgh}px`;
      img.style.transformOrigin = 'center center';
      console.log(imgw, img.style.height, '???????????????????');
    } else {
      container.style.width = `${imgw}px`; // 容器宽度设置为图片宽度
      container.style.height = `${imgh}px`; // 容器高度设置为图片高度
      img.style.width = `${imgw}px`;
      img.style.height = `${imgh}px`;
      img.style.transformOrigin = 'center center';
    }
    // 增加旋转角度
    console.log(rotate + 90);
    if (type === 'z') {
      img.style.transform = `rotate(${rotate + 90}deg)`;
      setRotate((pre) => pre + 90);
    } else {
      img.style.transform = `rotate(${rotate - 90}deg)`;
      setRotate((pre) => pre - 90);
    }

    setCrop({
      unit: '%', // 使用百分比单位
      x: 0, // 初始化 x 坐标
      y: 0, // 初始化 y 坐标
      width: 100,
      height: 100,
    });
  };
  // const rotateImage1 = (type: string) => {
  //     const img = document.getElementById('rotateImage') as HTMLImageElement;
  //     const noneImage = document.getElementById('noneImage') as HTMLDivElement;
  //     img.style.height = `100%`;
  //     img.style.width = `100%`;
  //     const canvas = document.createElement('canvas');
  //     const ctx = canvas.getContext('2d');
  //     let _rotate = ((type === 'z' ? 90 : -90) + rotate)
  //     if (!ctx) return;

  //     // // 获取图片的原始宽高
  //     // const imgWidth = img.naturalWidth;
  //     // const imgHeight = img.naturalHeight;
  //     console.log(img.naturalWidth, img.naturalHeight, 'imgWidthimgWidthimgWidth')
  //     console.log(imgWidth, imgHeight, 'imgWidthimgWidthimgWidth')
  //     let _scales = 690 / imgWidth
  //     // 设置 canvas 的宽高
  //     if (_rotate % 180 === 0) {
  //         canvas.width = imgWidth * (690 / imgHeight);
  //         canvas.height = 690;

  //     } else {
  //         canvas.width = imgHeight * (690 / imgWidth);
  //         canvas.height = 690;

  //     }
  //     console.log(canvas.width, canvas.height, '竖向')
  //     // 清空 canvas 并设置旋转
  //     ctx.clearRect(0, 0, canvas.width, canvas.height);
  //     ctx.translate(canvas.width / 2, canvas.height / 2);
  //     ctx.rotate(_rotate * (Math.PI / 180));
  //     if (_rotate % 180 !== 0) {

  //         ctx.drawImage(noneImage, -imgHeight * (690 / imgWidth) / 2, -690 / 2);
  //     } else {

  //         ctx.drawImage(noneImage, -(imgWidth * (690 / imgHeight)) / 2, -(imgHeight * (690 / imgHeight)) / 2, imgWidth * (690 / imgHeight), imgHeight * (690 / imgHeight));
  //     }

  //     // 更新旋转角度
  //     const newRotation = (_rotate + 360) % 360;
  //     setRotate(newRotation);

  //     // 生成旋转后的图片
  //     canvas.toBlob((blob) => {
  //         if (blob) {
  //             const rotatedImageUrl = URL.createObjectURL(blob);
  //             setCImageUrl(rotatedImageUrl); // 更新旋转后的图片 URL
  //             // const file = new File([blob], 'cropped-image.webp', { type: 'image/webp' });
  //             // setCroppedImageUrl(file)
  //         }
  //     });
  // };
  const rotateImage1 = (type: string) => {
    const img = document.getElementById('rotateImage') as HTMLImageElement;
    const noneImage = document.getElementById('noneImage') as HTMLImageElement;
    if (!img || !noneImage) return;

    // 计算新的旋转角度
    const newRotation = ((type === 'z' ? 90 : -90) + rotate + 360) % 360;
    setRotate(newRotation);

    // 获取原始尺寸
    const originalWidth = noneImage.naturalWidth;
    const originalHeight = noneImage.naturalHeight;

    // 创建Canvas进行旋转处理
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置canvas大小（根据旋转角度决定宽高是否需要交换）
    if (newRotation % 180 === 0) {
      canvas.width = originalWidth;
      canvas.height = originalHeight;
    } else {
      canvas.width = originalHeight;
      canvas.height = originalWidth;
    }

    // 设置绘图变换
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.rotate((newRotation * Math.PI) / 180);
    ctx.drawImage(noneImage, -originalWidth / 2, -originalHeight / 2, originalWidth, originalHeight);
    // 重置裁剪区域
    setCrop({
      unit: '%',
      x: 0,
      y: 0,
      width: 100,
      height: 100,
    });
    // 生成新图片并更新UI
    canvas.toBlob(
      (blob) => {
        if (blob) {
          // 清理旧的URL对象以避免内存泄漏
          if (cImageUrl && cImageUrl !== consturl) {
            URL.revokeObjectURL(cImageUrl);
          }

          const rotatedImageUrl = URL.createObjectURL(blob);
          setCImageUrl(rotatedImageUrl);
          const file = new File([blob], 'cropped-image.webp', { type: 'image/webp' });
          setCroppedImageUrl(file);
          // 重置裁剪区域
          setCrop({
            unit: '%',
            x: 0,
            y: 0,
            width: 100,
            height: 100,
          });

          // 调整容器和图片的大小以适应旋转后的图片
          const container = img.parentElement?.parentElement as HTMLElement;
          if (container) {
            requestAnimationFrame(() => {
              const maxHeight = 690; // 最大显示高度

              if (newRotation % 180 !== 0) {
                // 旋转90°或270°，交换宽高
                const aspectRatio = originalWidth / originalHeight;
                const containerHeight = Math.min(maxHeight, originalWidth);
                const containerWidth = containerHeight / aspectRatio;

                container.style.width = `${containerWidth}px`;
                container.style.height = `${containerHeight}px`;
                container.style.margin = '0 auto';
              } else {
                // 0°或180°时不调整容器大小，只旋转图片
                // 如果之前已经调整过，则恢复原始大小
                const aspectRatio = originalHeight / originalWidth;
                const containerWidth = Math.min(maxHeight / aspectRatio, originalWidth);
                const containerHeight = containerWidth * aspectRatio;

                if (containerHeight > maxHeight) {
                  const scale = maxHeight / containerHeight;
                  container.style.width = `${containerWidth * scale}px`;
                  container.style.height = `${maxHeight}px`;
                } else {
                  container.style.width = `${containerWidth}px`;
                  container.style.height = `${containerHeight}px`;
                }
              }

              console.log(container.style.width, container.style.height, originalWidth, originalHeight, 'containerWidthcontainerHeight');

              img.style.width = '100%';
              img.style.height = '100%';
              img.style.objectFit = 'contain';
            });
          }
        }
      },
      'image/webp',
      0.95,
    );
  };
  const clear = () => {
    // 重置裁剪状态
    setCrop({
      unit: '%',
      x: 0,
      y: 0,
      width: 100,
      height: 100,
    });

    // 重置旋转角度
    setRotate(0);

    // 清除裁剪结果
    setCroppedImageUrl(null);

    // 重置图片引用
    imageRef.current = null;

    // 重置图片URL到初始状态
    setCImageUrl(consturl);

    // 重置图片容器和图片样式
    const img = document.getElementById('rotateImage') as HTMLImageElement;
    const container = img?.parentElement as HTMLElement;

    if (img && container) {
      const originalHeight = img.naturalHeight;
      const originalWidth = img.naturalWidth;

      // 计算正确的显示尺寸
      if (originalHeight > 690) {
        const scale = 690 / originalHeight;
        img.style.height = '690px';
        img.style.width = `${originalWidth * scale}px`;

        if (container) {
          container.style.width = `${originalWidth * scale}px`;
          container.style.height = '690px';
        }
      }

      // 清除旋转相关的样式
      img.style.transform = 'none';
      img.style.transformOrigin = 'center center';
    }

    // 重置其他状态
    hasSetWidth.current = false;
  };
  useEffect(() => {
    // 确保先设置图片URL
    if (props.src) {
      const imgUrl = `${window.location.origin}/${props.src}`;
      setCImageUrl(imgUrl);
      setConsturl(imgUrl);
    }

    // 使用setTimeout确保DOM已经更新
    setTimeout(() => {
      const img = document.getElementById('rotateImage') as HTMLImageElement;
      const noneImage = document.getElementById('noneImage') as HTMLImageElement;
      
      if (!img || !noneImage) return;

      const handleNoneImageLoad = () => {
        noneImage.style.height = '100%';
        noneImage.style.width = '100%';
      };

      const handleImageLoad = () => {
        const h = img.naturalHeight;
        const w = img.naturalWidth;
        
        if (h > 690 && !hasSetWidth.current) {
          const aspectRatio = 690 / h;
          img.style.height = '690px';
          img.style.width = `${w * aspectRatio}px`;

          setImgWidth(w);
          setImgHeight(h);
          setScales(aspectRatio);
        }
        hasSetWidth.current = true;
      };

      // 添加事件监听器
      noneImage.addEventListener('load', handleNoneImageLoad);
      img.addEventListener('load', handleImageLoad);

      // 清理函数
      return () => {
        noneImage.removeEventListener('load', handleNoneImageLoad);
        img.removeEventListener('load', handleImageLoad);
      };
    }, 0);
  }, [props.keys, props.src]);
  return (
    <div style={{ textAlign: 'center' }}>
      {/* <input type="file" accept="image/*" onChange={handleFileChange} /> */}
      {/* <Button onClick={handleFileChange}>编辑</Button> */}
      {/* {src && ( */}
      <div>
        <Space>
          <img onClick={() => rotateImage1('f')} src={left} style={{ width: 20, height: 20, cursor: 'pointer' }} />
          <img onClick={() => rotateImage1('z')} src={right} style={{ width: 20, height: 20, cursor: 'pointer' }} />
        </Space>
      </div>
      <img
        id="noneImage"
        // src={`${window.location.origin}/${props.src}`}
        // src={require('@/assets/mm.jpeg')}
        src={consturl}
        alt="Source"
        style={{
          width: '100%',
          height: '100%',
          display: 'none',
          // visibility: 'hidden',
          // transition: 'transform 0.5s ease',
          transformOrigin: 'center center',
          // transform: `rotate(${rotate}deg)`,
          // transform: 'rotate(90deg)'
        }}
      />
      {
        cImageUrl &&
        <ReactCrop
          //   src={'../../../../../assets/mm.png'}
          crop={crop}
          //   onImageLoaded={onImageLoaded}
          onComplete={onCropComplete}
          onChange={onCropChange}
        >
          <div>
            <img
              id="rotateImage"
              ref={imageRef}
              // src={`${window.location.origin}/${props.src}`}
              // src={require('@/assets/海贼王.jpg')}
              src={cImageUrl}
              alt="Source"
              style={{
                width: '100%',
                height: '100%',
                // transition: 'transform 0.5s ease',
                transformOrigin: 'center center',
                // transform: `rotate(${rotate}deg)`,
                // transform: 'rotate(90deg)'
              }}
            />
          </div>
        </ReactCrop>
      }

      {/* )} */}
      {/* {cImageUrl && (
        <div>
          <h3>裁剪后的图片：</h3>
          <img  src={cImageUrl} alt="Cropped"    style={{
                            width: '100%',
                            height: '100%',
                            // transform: 'rotate(90deg)'
                        }}/>
        </div>
      )} */}
    </div>
  );
};

export default React.forwardRef(ImageCropper);
