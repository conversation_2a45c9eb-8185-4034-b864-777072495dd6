/**
 * 字典select组件
 */
 import React, { Fragment } from 'react';
 import { Radio, Select, Checkbox } from 'antd';
 import { connect } from 'dva';``
 import styles from './index.less';

 const Option = Select.Option;
 let ownProps;

 interface propsType {
   codeType: string,
   placeholder?: string,
   codeValue?: string,
   codeName?: string,
   onChange?: any,
   onFocus?: () => void,
   backType?: 'object',
   dispatch?: any,
   commonDict?: any,
   mode?: 'multiple',
   initValue?: any,
   disabled?: boolean,
   searchKey?: Array<string>,
   extendProps?: Object,
   filter?: Function,
   allowClear?: boolean
   send?: boolean//默认发送请求
   rowKey?: string,
   selectType?: 'default' | 'buttons' | 'checkBox',
   buttonSelectAllCodeValue?: string
   buttonSelectAllCodeName?: string
   hasOwnStyle?:any
 }

 // @ts-ignore
 @connect(({ commonDict }, props: any) => {
   const { codeType } = props;
   if (codeType) {
     return {
       commonDict: {
         [codeType]: commonDict[`${codeType}`],
         [`${codeType}List`]: commonDict[`${codeType}List`],
       },
     };
   } else {
     return { commonDict };
   }
 }, null, null, { forwardRef: true })
 export default class index extends React.Component<propsType, any> {
   static defaultProps = {
     codeValue: 'code',
     codeName: 'name',
     searchKey: ['name'],
     placeholder: '请选择',
     mode: undefined,
     disabled: false,
     allowClear: true,
     filter: (item) => true,
     selectType: 'default',
     buttonSelectAllCodeValue: 'all',
     buttonSelectAllCodeName: '全部',
   };

   constructor(props) {
     super(props);
     this.state = {
       value: undefined,
     };
     index.clear = this.clearAll;
   }

   static clear() {
   };

   clearAll = () => {
     this.setState({
       value: undefined,
     });
   };
   // 外部改变更改本项select的值（不是赋初始值）
   setValue = (value) => {
     this.setState({
       value,
     });
   };

   componentDidMount(): void {
     const { codeType, commonDict, send = true } = this.props;
     if (!commonDict[codeType] && send) {
       this.getDict();
     }
     this.setState({
       value: this.props.initValue,
     });
   }

   componentDidUpdate(prevProps: Readonly<propsType>, prevState: Readonly<any>, snapshot?: any): void {
     if (prevProps.initValue !== this.props.initValue) {
       this.setState({
         value: this.props.initValue,
       });
     }
   }

   // shouldComponentUpdate(newProps, newState) {
   //   if (
   //     JSON.stringify(ownProps) == JSON.stringify({ ...newProps, ...newState })
   //   ) {
   //     ownProps = { ...newProps, ...newState };
   //     return false;
   //   }
   //   ownProps = { ...newProps, ...newState };
   //   return true;
   // }
   getDict = () => {
     const { codeType, onFocus } = this.props;
     this.props.dispatch({
       type: 'commonDict/queryDict',
       payload: {
        typeCode:codeType,
       },
     });
     if (onFocus) {
       onFocus();
     }
   };
   onChange = (value) => {
     const { codeType, codeValue = '', onChange, backType, mode, selectType } = this.props;
     // this.setState({value});
     let bool;
     if (backType === 'object') {
       const data = this.props.commonDict[codeType];
       if (data) {
         if (mode) {
           let back: any = [];
           for (let ob of value) {
             let find = data.find(obj => obj[codeValue] === ob);
             if (find) {
               back.push(find);
             }
           }
           bool = onChange(back);
         } else {
           let find = data.find(obj => obj[codeValue] === value);
           if (find && onChange) {
             bool = onChange(Object.assign({}, find));
           } else {
             bool = onChange(undefined);
           }
         }
       }
     } else if (onChange) {
       bool = onChange(value);
     }

     if (bool || bool === undefined) {
       if(selectType === 'default'){
         this.setState({ value });
       }
       if(selectType === 'buttons'){
         this.setState({ value:value?.target?.value || undefined});
       }
     }
   };
   filterOption = (val, node) => {
     const dataRef = node.props;
     return dataRef['value'].includes(val) || dataRef['children'].includes(val);
   };

   render(): React.ReactNode {
     const {
       codeType,
       codeValue = '',
       codeName = '',
       placeholder,
       mode,
       disabled,
       extendProps,
       filter,
       allowClear,
       selectType,
       buttonSelectAllCodeValue,
       buttonSelectAllCodeName,
       hasOwnStyle = undefined,
     } = this.props;
     const data = this.props.commonDict[codeType];
     return (
       <Fragment>
         {
           selectType === 'default' &&
           <Select
             {...extendProps}
             allowClear={allowClear}
             style={{width:'100%'}}
             showSearch
             mode={mode}
             disabled={disabled}
             onFocus={this.getDict}
             onChange={this.onChange}
             filterOption={this.filterOption}
             placeholder={placeholder}
             value={this.state['value']}
             className={styles.change}
             autoFocus
           >
             {
               data && data.filter(filter).map((obj, index) => {
                 return (
                   <Option title={obj[codeValue]} value={obj[codeValue]} key={obj[codeValue] || index}
                           disabled={obj['disabled']}>{obj[codeName]}</Option>
                 );
               })
             }
           </Select>
         }
         {
           selectType === 'buttons' &&
           <Radio.Group value={this.state['value']} onChange={this.onChange}>
             {
               data && data.filter(filter).map((obj, index) => {
                 return (
                   <Fragment key={index}>
                     {
                       index === 0 && <Radio.Button key={buttonSelectAllCodeValue} value={buttonSelectAllCodeValue}
                                                    disabled={obj['disabled']}>{buttonSelectAllCodeName}</Radio.Button>
                     }
                     <Radio.Button key={obj[codeValue] || index} value={obj[codeValue]}
                                   disabled={obj['disabled']}>{obj[codeName]}</Radio.Button>
                   </Fragment>
                 );
               })
             }
           </Radio.Group>
         }
         {
           selectType === 'checkBox' &&
           <Checkbox.Group
             // options={data ? data.map(it=>{
             //   return {
             //     ...it,
             //     label:it.CODE_NAME,
             //     value:it.CODE_VALUE,
             //   }
             // }) : []}
             {...!hasOwnStyle ? { options: data ? data.map(it=>{
                 return {
                   ...it,
                   label:it.CODE_NAME,
                   value:it.CODE_VALUE,
                 }
               }) : [] }  : {}}
             value={this.state['value']}
             onChange={(e)=>{
               if(mode === 'multiple'){
                 this.onChange(e)
               }
             }}
           >
             {
               hasOwnStyle && hasOwnStyle(data)
             }
           </Checkbox.Group>
         }
       </Fragment>
     );
   }
 }
