/**
 * 关系转出
 */
import React from 'react';
import RuiFilter from '@/components/RuiFilter';
import ListTable from '@/components/ListTable';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs, Modal, Radio } from "antd";
import NowOrg from "@/components/NowOrg";
import AdjustMem from './components/adjustMem'; //支部间人员调整
import Transfer from './components/transfer'; //关系转接
import Details from './components/details'; //转接详情
import TransferMemUp from './components/transferMemUp';
import { connect } from "dva";
import moment from 'moment';
import { getSession } from "@/utils/session";
import Tip from '@/components/Tip';
import WhiteSpace from '@/components/WhiteSpace';
// import router from "umi/router";
import { _history as router } from "@/utils/method";
import qs from 'qs';
import ExportInfo from '@/components/Export';
import { ButtonDisabled } from '@/common/config.js'
import { fileDownloadHeader, isFlowingParty, setListHeight } from '@/utils/method';
import { exportLetter } from '@/pages/transfer/services'
import _last from 'lodash/last';
import { ExclamationCircleTwoTone, ExclamationCircleOutlined } from '@ant-design/icons';
import TransVideo from '@/pages/transfer/outflows/components/transVideo';
import { includes } from 'lodash';
import { findOutByPage, cancelHint } from "../services/index"

const TabPane = Tabs.TabPane;
const Search = Input.Search;

export const Letter = (props: any) => {
  const { record, isOut } = props;
  const onClick = async () => {
    const { code = 500, data = {} } = await exportLetter({
      data: {
        id: record?.id,
        isOut
      }
    })
    let dataApi = sessionStorage.getItem('dataApi') || "";
    if (code == 0) {
      Tip.success('操作提示', '下载成功，正在下载...')
      // let url = data.url.split('/');
      // let name = _last(url).split('.')[0]
      // console.log("🚀 ~ file: index.tsx ~ line 40 ~ onClick ~ name", encodeURI(name))
      // console.log("🚀 ~ file: index.tsx ~ line 39 ~ onClick ~ url", url)
      fileDownloadHeader(`/api${data.url}`, _last(data.url.split('/')), { dataApi });
    }
  };
  // 212 117 整建制没有下载介绍信
  if (record['type'] == '212' || record['type'] == '117') {
    return null
  } else {
    return (
      <a onClick={onClick}>下载介绍信</a>
    )
  }
};
@connect(({ transferOut, commonDict, loading }) => ({ transferOut, loading: loading.effects['transferOut/findOutByPage'], commonDict: commonDict['dict_d58_tree'], }))
export default class extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      transferId: undefined,
      memCode: undefined,
      messageVisible: false,
      showWeekDataVisible: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination1: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination2: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination4: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      weekType: 3,
      weekData: [],
      weekData1: [],
      weekData2: [],
      weekData4: [],
      modalListLoading: false,
      countDown: 10,
      isOpen: true,
      selectedRowKeys: [],
    }
  }
  confirm = async (item) => {
    const obj = await this.props.dispatch({
      type: 'transferOut/undo',
      payload: {
        data: {
          id: item['id'],
          reason: '撤销'
        }
      }
    });
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', '撤销成功');
      this.refresh();
    }
  };
  filterChange = (val) => {
    this.props.dispatch({
      type: 'transferOut/updateState',
      payload: {
        filter: val
      }
    });
    this.refresh();
  };
  search = (val) => {
    this.props.dispatch({
      type: 'transferOut/updateState',
      payload: {
        keyWord: val
      }
    });
    this.refresh({ pageNum: 1 });
  };
  searchClear = (e) => {
    if (!e.target.value) {
      this.props.dispatch({
        type: 'transferOut/updateState',
        payload: { keyWord: undefined }
      });
      this.refresh();
    }
  };
  adjust = () => {//支部间人员调整
    this['AdjustMem'].open();
  };
  addOrEdit = () => {//关系转接
    this['Transfer'].open();
  };
  refresh = (params?: any) => {//刷新列表
    const { outPagination = {} } = this.props.transferOut;
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'transferOut/findOutByPage',
      payload: {
        isHistory: false,
        orgId: org['code'],
        pageNum: outPagination['current'] || 1,
        pageSize: outPagination['pageSize'] || 10,
        ...params,
      }
    });
  };
  onPageChange = (page, pageSize) => {
    console.log(page, pageSize);
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`)
  };
  onPageChange1 = (page, pageSize) => {
    console.log(page, pageSize, 'ccccccccccccccccccccccc');
    if (this.state.weekType == 3) {
      this.setState({
        pagination: {
          current: page,
          pageSize: pageSize,
          total: this.state.pagination.total
        }
      }, () => {
        this.getWeekData()
      })
    }
    if (this.state.weekType == 1) {
      this.setState({
        pagination1: {
          current: page,
          pageSize: pageSize,
          total: this.state.pagination1.total
        }
      }, () => {
        this.getMessageList(1)
      })
    }
    if (this.state.weekType == 2) {
      this.setState({
        pagination2: {
          current: page,
          pageSize: pageSize,
          total: this.state.pagination2.total
        }
      }, () => {
        this.getMessageList(2)
      })
    }
    if (this.state.weekType == 4) {
      this.setState({
        pagination4: {
          current: page,
          pageSize: pageSize,
          total: this.state.pagination4.total
        }
      }, () => {
        this.getMessageList(4)
      })
    }
  };
  //组织关系转入剩余7天列表 请求方法
  getWeekData = async () => {
    const { pagination } = this.state
    const org = getSession('org') || {};
    await this.setState({ modalListLoading: true })
    const { data, code } = await findOutByPage({
      data: {
        isHistory: false,
        orgId: org['code'],
        pageNum: pagination['current'] || 1,
        // pageNum: 1,
        pageSize: pagination['pageSize'] || 10,
        // pageSize: 9999,
        subordinate: 1,
        isReminder: '1',
        reminderDayInt: 7,
      }
    })
    if (code == 0) {
      this.setState({
        weekData: data?.list,
        pagination: {
          current: data['pageNumber'],
          // pageSize: data['pageSize'],
          pageSize: 10,
          total: data['totalRow']
        }
      })
    }
    this.setState({ modalListLoading: false }, () => {
      if (this.state.isOpen) {
        this.openTimer()
        this.setState({
          isOpen: false
        })
      }
    })
  }
  // 提示弹窗： 省外上传交换区失败的、主动撤销的、超期自动退回
  getMessageList = async (type) => {
    const { pagination } = this.state
    const org = getSession('org') || {};
    const currentPagination = this.state[`pagination${type}`];
    await this.setState({ modalListLoading: true })
    const { data, code } = await findOutByPage({
      data: {
        isHistory: true,
        orgId: org['code'],
        pageNum: currentPagination['current'] || 1,
        // pageNum: 1,
        pageSize: currentPagination['pageSize'] || 10,
        // pageSize: 9999,
        subordinate: 1,
        hintTypes: [type], // 提醒记录类型：1-上传交换区失败的， 2-主动撤销的，4-超期自动退回
        isFlowStatus: "1"
      }
    })
    this.setState({
      modalListLoading: false,
    })
    if (code == 0) {
      if (type == 1) {
        this.setState({
          weekData1: data?.list,
          pagination1: {
            current: data['pageNumber'],
            // pageSize: data['pageSize'],
            pageSize: 10,
            total: data['totalRow']
          }
        })
      }
      if (type == 2) {
        this.setState({
          weekData2: data?.list,
          pagination2: {
            current: data['pageNumber'],
            // pageSize: data['pageSize'],
            pageSize: 10,
            total: data['totalRow']
          }
        })
      }
      if (type == 4) {
        this.setState({
          weekData4: data?.list,
          pagination4: {
            current: data['pageNumber'],
            // pageSize: data['pageSize'],
            pageSize: 10,
            total: data['totalRow']
          }
        })
      }
    }
  }

  componentWillUnmount(): void {
    this.props.dispatch({
      type: 'transferOut/destroy',
    })
  }

  // 开启确定按钮倒计时
  openTimer = () => {
    const timer = setInterval(() => {
      const { countDown } = this.state
      if (countDown > 0) {
        this.setState({
          countDown: countDown - 1
        })
      } else {
        clearInterval(timer)
      }
    }, 1000)
  }
  checkData = () => {
    //组织关系转出剩余7天, 1-上传交换区失败的， 2-主动撤销的，4-超期自动退回。请求接口有数据的就弹出提示框，点确定之后下次就不再弹出
    Promise.all([this.getWeekData(), this.getMessageList(1), this.getMessageList(2), this.getMessageList(4)]).then(() => {
      const { weekData, weekData1, weekData2, weekData4 } = this.state
      // 有数据才弹出提示
      if (weekData.length > 0 || weekData1.length > 0 || weekData2.length > 0 || weekData4.length > 0) {
        // 初始选择项
        if (weekData.length > 0) {
          this.setState({
            weekType: 3
          })
        } else if (weekData1.length > 0) {
          this.setState({
            weekType: 1
          })
        } else if (weekData2.length > 0) {
          this.setState({
            weekType: 2
          })
        } else if (weekData4.length > 0) {
          this.setState({
            weekType: 4
          })
        }
        this.setState({
          showWeekDataVisible: true,
        })
      } else {
        this.setState({
          showWeekDataVisible: false,
        })
      }
    })
  }
  componentDidMount(): void {
    setListHeight(this)
    this.checkData()
  }
  exportInfo = async () => {
    this.setState({
      flowBackDownload: true,
    })
    await this['flowBack'].submitNoModal();
    this.setState({
      flowBackDownload: false,
    })
  };
  handleOk = () => {
    this.handleCancel();
    this.addOrEdit();
  };
  handleOk1 = () => {
    // this.cancelMessage()
    if (this.state.weekType == 3) {
      this.handleCancel1();
    } else {
      this.cancelMessage()
    }
  };
  handleCancel = () => { this.setState({ messageVisible: false }) };
  handleCancel1 = () => {
    // const { countDown } = this.state
    // if (countDown <= 0) {
    //   this.setState({ showWeekDataVisible: false })
    // }
    this.setState({ showWeekDataVisible: false })

  };
  cancelMessage = async () => {
    const type = this.state.weekType
    let ids = []
    // if(type==3){
    //   ids= this.state.selectedRowKeys
    // }
    // if (type == 1) {
    //   ids = this.state.weekData1.map(item => item.id)
    // }
    // if (type == 2) {
    //   ids = this.state.weekData2.map(item => item.id)
    // }
    // if (type == 4) {
    //   ids = this.state.weekData4.map(item => item.id)
    // }
    ids= this.state.selectedRowKeys
    console.log('cancelMessage====', ids);
    console.log('data:', {
      transferIdList: ids,
      hintType: type, // 提醒记录类型：1-上传交换区失败的， 2-主动撤销的，4-超期自动退回
    })
    const { code } = await cancelHint({
      data: {
        transferIdList: ids,
        hintType: type, // 提醒记录类型：1-上传交换区失败的， 2-主动撤销的，4-超期自动退回
      }
    })
    if (code == 0) {
      this.setState({
       selectedRowKeys: []
      })
     }
    this.checkData()
  }
  onSelectChange = (selectedRowKeys, infos) => {
    this.setState({ selectedRowKeys })
  }
  render() {
    const { loading, transferOut } = this.props;
    const { outList = [], outPagination = false } = transferOut;
    const { current, pageSize } = outPagination;
    const { weekType, pagination, weekData, pagination1, weekData1, pagination2, weekData2, pagination4, weekData4, modalListLoading, countDown, selectedRowKeys } = this.state
    const org = getSession('org') || {};
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 50,
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1
        }
      },
      {
        title: '姓名',
        dataIndex: 'name',
        width: 120,
      },
      {
        title: '申请日期',
        dataIndex: 'createTime',
        width: 100,
        render: (text) => {
          return moment(text).format('YYYY-MM-DD')
        }
      },
      {
        title: '源组织',
        dataIndex: 'srcOrgName',
        width: 200,
      },
      {
        title: '目的组织',
        dataIndex: 'targetOrgName',
        width: 200,
      },
      {
        title: '转接类型',
        dataIndex: 'typeName',
        width: 100,
      },
      {
        title: '转接状态',
        dataIndex: 'status',
        width: 80,
        render: (text) => {
          switch (text) {
            case 0:
              return '转接中';
            case 1:
              return '已完成';
            case 2:
              return '已撤销';
            case 4:
              return '超期自动退回';
            default:
          }
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 140,
        render: (text, record, index) => {
          // 当关系转接 转出表中的数据 type= 224 的时候， 点击撤销按钮的时候，需要加一个提示
          let cancelTitle = '确定要撤销吗？';
          if (record?.type == '224') {
            cancelTitle = '因全国组织关系转接过程中存在数据延迟性，接收方接收后次日发起方才能收到接收消息。发起撤销以前请与接收方党组织联系对方是否已经进行接收，如对方未接收情况下可进行撤销；如对方已接收，不要发起撤销，请于对方接收次日重新查看转接状态，如次日依然显示对方未接收，请联系管理员咨询。跨省关系转接撤销请务必谨慎操作。'
          }
          return (
            <span>
              <a
                onClick={() => {
                  this.props.dispatch({
                    type: 'transferOut/outDetail',
                    payload: {
                      transferId: record['id']
                    }
                  }).then(res =>

                    this.setState({
                      transferId: record['id'],
                      memCode: record['memId'],
                    }, () => {
                      this['Details'].open(record)
                    })
                  )
                }}
              >
                {`${record['isCheck']}` == '1' ? <span style={{ color: 'red' }}>操作</span> : '查看'}
              </a>
              <Divider type="vertical" />
              {/*@ts-ignore*/}
              <a
                onClick={() => {
                  this.props.dispatch({
                    type: 'transferOut/transferMemInfo',
                    payload: {
                      recordId: record['id']
                    }
                  }).then(res => {
                    this['TransferMemUp'].open()
                  })
                }}
                className={record['isOrg'] ? 'eventNone' : undefined}
              >
                编辑
              </a>
              {/* {
                // 系统外跨省转入时， 需屏蔽撤销
                record.type != '224' &&
                <React.Fragment>
                  <Divider type="vertical"/>
                  <Popconfirm title="确定要撤销吗？" onConfirm={()=>this.confirm(record)}>
                  <a className={'del'}>撤销</a>
                  </Popconfirm>
                </React.Fragment>
              } */}
              <Divider type="vertical" />
              <Popconfirm overlayStyle={record?.type == '224' ? { width: '500px' } : {}} title={cancelTitle} onConfirm={() => this.confirm(record)}>
                <a className={'del'}>撤销</a>
              </Popconfirm>
              <Divider type="vertical" />
              <Letter {...this.props} record={record} isOut={'1'} />
            </span>
          )
        }
      },
    ];

    const weekColumns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 50,
        render: (text, record, index) => {
          return (pagination.current - 1) * pagination.pageSize + index + 1
        }
      },
      ...columns.slice(1, columns.length - 1)
      // ...columns.slice(1)
    ]
    const filterData = [
      {
        key: 'types', name: '转接类型', value: this.props.commonDict,
      },
    ];
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
    };
    return (
      <div>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1" />
        </Tabs>
        {/*<AddOrEdit dataInfo={dataInfo}/>*/}
        {/*支部间人员调整*/}
        <AdjustMem refresh={() => this.refresh({ pageNum: 1 })} wrappedComponentRef={e => this['AdjustMem'] = e} />
        {/*关系转接*/}
        <Transfer refresh={() => this.refresh({ pageNum: 1 })} wrappedComponentRef={e => this['Transfer'] = e} />
        {/*转接详情*/}
        <Details refresh={this.refresh} transferId={this.state.transferId} memCode={this.state.memCode} type={'out'} wrappedComponentRef={e => this['Details'] = e} />
        {/*修改党员转接信息*/}
        <TransferMemUp wrappedComponentRef={e => this['TransferMemUp'] = e} />
        <NowOrg extra={
          <React.Fragment>
            <TransVideo></TransVideo>
            <Button onClick={this.exportInfo} loading={this.state.flowBackDownload} style={{ marginLeft: 16 }}>导出</Button>
            {
              (!ButtonDisabled.statistics2021 && isFlowingParty()) && <React.Fragment>
                <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.adjust} style={{ marginLeft: 16 }}>支部间人员调整</Button>
                {/* 关系转接--关系转出 ，新增关系转出 */}
                <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={() => { this.addOrEdit() }} style={{ marginLeft: 16 }}>新增关系转出</Button>
              </React.Fragment>
            }
            <Search style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'} />
          </React.Fragment>
        } />
        <RuiFilter data={filterData} onChange={this.filterChange} />
        <WhiteSpace />
        <WhiteSpace />
        <ListTable scroll={{ y: this.state.filterHeight }}

          columns={columns} data={outList} pagination={outPagination} onPageChange={this.onPageChange} />
        <ExportInfo wrappedComponentRef={e => this['flowBack'] = e}
          tableName={''}
          noModal={true}
          tableListQuery={{ isHistory: false, orgId: org['code'], ...this.props.transferOut.filter, keyWord: this.props.transferOut.keyWord }}
          action={'/api/transfer/exportOut'}
        />
        <Modal
          destroyOnClose
          title={<div style={{ color: '#1890ff' }}><ExclamationCircleOutlined /> 提示</div>}
          visible={this.state.messageVisible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={500}
        >
          <div>
            根据相关要求，党政机关县处级以上党员领导干部（不含职级公务员和参照管理的企事业单位领导人员等）不通过线上进行组织关系转接，应通过纸质介绍信随干部调动程序一并进行，在本系统中这类党员转接时，转出党组织请选择转出到省外（个人），转入党组织通过关系转入入口进行转接录入。
          </div>
        </Modal>
        {/* 展示组织关系转出剩余7天的数据 */}
        <Modal
          closable={countDown > 0 ? false : true}
          destroyOnClose
          keyboard={countDown > 0 ? false : true}
          maskClosable={false}
          title={<React.Fragment>
            <Radio.Group onChange={(e) => {
              const type = e.target.value
              this.setState({
                weekType: type,
                pagination: {
                  current: 1,
                  pageSize: 10,
                  total: 0,
                },
                pagination1: {
                  current: 1,
                  pageSize: 10,
                  total: 0,
                },
                pagination2: {
                  current: 1,
                  pageSize: 10,
                  total: 0,
                },
                pagination4: {
                  current: 1,
                  pageSize: 10,
                  total: 0,
                },
                selectedRowKeys:[]
              }, () => {
                // this.refresh({ pageNum: 1 })
              })
              if (type === 3) {
                this.getWeekData()
              } else {
                this.getMessageList(type)
              }

            }} value={weekType}>
              {weekData.length > 0 && <Radio.Button value={3}>组织关系转出剩余7天</Radio.Button>}
              {weekData1.length > 0 && <Radio.Button value={1}>交换区上传失败</Radio.Button>}
              {weekData2.length > 0 && <Radio.Button value={2}>主动撤销</Radio.Button>}
              {weekData4.length > 0 && <Radio.Button value={4}>超期自动退回</Radio.Button>}
            </Radio.Group>
          </React.Fragment>}
          visible={this.state.showWeekDataVisible}
          onOk={this.handleOk1}
          onCancel={this.handleCancel1}
          width={1400}
          footer={
            <>
              {/* <Button size={'middle'} style={{ width: '70px' }} type="primary" disabled={countDown > 0} onClick={this.handleCancel1}>取消{countDown > 0 ? countDown : ''}</Button> */}
              <Button size={'middle'} style={{ width: '70px' }} type="primary" disabled={countDown > 0} onClick={this.handleOk1}>确定{countDown > 0 ? countDown : ''}</Button>
            </>
          }
        >
          <div>
            {weekType == 3 && <ListTable scroll={{ y: this.state.filterHeight }} columns={weekColumns} data={weekData} pagination={pagination} onPageChange={this.onPageChange1} />}
            {weekType == 1 && <ListTable rowKey={record => record.id} rowSelection={rowSelection} scroll={{ y: this.state.filterHeight }} columns={weekColumns} data={weekData1} pagination={pagination1} onPageChange={this.onPageChange1} />}
            {weekType == 2 && <ListTable rowKey={record => record.id} rowSelection={rowSelection} scroll={{ y: this.state.filterHeight }} columns={weekColumns} data={weekData2} pagination={pagination2} onPageChange={this.onPageChange1} />}
            {weekType == 4 && <ListTable rowKey={record => record.id} rowSelection={rowSelection} scroll={{ y: this.state.filterHeight }} columns={weekColumns} data={weekData4} pagination={pagination4} onPageChange={this.onPageChange1} />}
          </div>
        </Modal>
      </div>
    );
  }
}
