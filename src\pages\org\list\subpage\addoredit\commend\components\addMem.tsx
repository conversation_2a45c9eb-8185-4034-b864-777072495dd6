import React, { Fragment, useImperativeHandle, useState, useEffect } from 'react';
import { Form, Modal, Select } from 'antd';
import moment from 'moment';
import { recognitionSituationAdd, recognitionSituationGetList } from '@/pages/org/services/org';
import Tip from '@/components/Tip';
import _isNumber from 'lodash/isNumber';
import ListTable from '@/components/ListTable';
import MemSelect from '@/components/MemSelect';

const nowYear = new Array(5).fill('').map((it, index) => moment().subtract(index, 'year').format('YYYY'));
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
export const FormComp = (props: any) => {
  const { form, onFinish, isDetail = false, orgCode } = props;
  const [itemData, setItemData] = useState([]);
  const [pagination, setPagination] = useState({
    pageNum: 1,
    pageSize: 10,
    total: 0,
    current: 1
  });
  const columns = [
    {
      title: '表彰年度',
      dataIndex: 'annual',
      width: 100,
    },
    {
      title: '姓名',
      dataIndex: 'memName',
      width: 195,
    },
    {
      title: '离开时所在组织',
      dataIndex: 'leaveOrgName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      align: 'center',
      render: (text, record) => {
        return (
          <div>
            {_isNumber(text) ? moment(text).format('YYYY-MM-DD') : ''}
          </div>
        );
      },
    },
  ];
  useEffect(() => {
    getTableList()
  }, [])
  const getTableList = async (p: any = {}) => {
    const { code = 500, data = [] } = await recognitionSituationGetList({
      data: {
        pageNum: p?.pageNum || pagination.pageNum,
        pageSize: p?.pageSize || pagination.pageSize,
        orgCode: orgCode,
        subordinate: 1
      }
    })
    if (code === 0) {
      console.log(data);
      setPagination({
        ...pagination
        , ...p,
        total: data.totalRow
      })
      setItemData(data.list)
    }
  }
  return (
    <Form form={form} {...formItemLayout} onFinish={onFinish} style={{ pointerEvents: isDetail ? 'none' : 'auto' }}>
      <Form.Item name='annual'
        label='表彰年度'
        rules={[{ required: true, message: '请输入' }]}
      >
        <Select>
          {
            nowYear.map((it, index) => (
              <Select.Option key={index} value={it}>{it}</Select.Option>
            ))
          }
        </Select>
      </Form.Item>

      <Form.Item name='memCode'
        label="选择历史人员"
        rules={[{ required: true, message: '选择历史人员' }]}
      >
        <MemSelect
          memType={'history'}
          checkType={'checkbox'}
          // initValue={dataInfo['memName']}
          placeholder="选择历史人员" />
      </Form.Item>
      <ListTable data={itemData} columns={columns} pagination={pagination}
        onPageChange={(page, pageSize) => {
          getTableList({ pageNum: page, current: page, pageSize });
        }}
      />
    </Form>
  );
};
const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const {
    width = 1000,
  } = props;

  useImperativeHandle(ref, () => ({
    open: async query => {
      const { code } = query || { code: undefined };
      setDataInfo({});
      form.resetFields();
      if (code) {
        // const { code: resCode = 500, data = {} } = await findRecognitionByCode({ code });
        // if (resCode === 0) {
        //   let _data = {
        //     ...data,
        //     information: data.information.map((it, index) => {
        //       return { ...it, _key: index + 1 };
        //     }),
        //   };
        //   setDataInfo(_data);
        //   setList(_data.information);
        //   form.setFieldsValue(_data);
        // }
      }
      open();
    },
  }));
  const open = () => {
    setVisible(true);
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    setConfirmLoading(false);
    form.resetFields();
  };
  const handleCancel = () => {
    close();
  };

  const onFinish = async (e) => {
    const { annual, fileNo, committeeParty, committeeWorker, difficultParty, difficultWorker, ...other } = e;
    const { basicInfo } = props.org;
    const { code = 500 } = await recognitionSituationAdd({
      data: {
        ...e,
        memCode: e.memCode ? e.memCode.map(it => it.code).toString() : '',
        orgCode: basicInfo.code,
        situationOrgCode: basicInfo.orgCode,
        code: dataInfo?.code,
      },
    });
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      props.onOK && props.onOK();
    }

  };

  return (
    <Fragment>
      <Modal
        title={dataInfo.id ? '追授情况' : '追授情况'}
        visible={visible}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
      >
        {
          visible && <Fragment>
            <FormComp form={form} onFinish={onFinish} orgCode={props.org.basicInfo.code} />
          </Fragment>
        }
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);

