import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Col, Form, Input, Modal, Radio, Row, Switch, Select } from 'antd';
import MemSelect from '@/components/MemSelect';
import Date from '@/components/Date';
import DictTreeSelect from '@/components/DictTreeSelect';
import DictSelect from '@/components/DictSelect';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import moment from 'moment';
import Tip from '@/components/Tip';
import { findDictCodeName } from '@/utils/method';
import { quitParty } from '@/pages/mem/services/memLeave';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const { width = 600 } = props;

  useImperativeHandle(ref, () => ({
    open: (record) => {
      if (record) {
        setDataInfo(record);
      }
      setVisible(true);
    },
  }));
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    setConfirmLoading(false);
    form.resetFields();
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    const { onOK } = props;
    let val = {
      ...e,
      approvalTime: e.approvalTime ? moment(e.approvalTime).valueOf() : '',
    };
    val = findDictCodeName(['d52'], val, {});
    setConfirmLoading(true);
    let url = quitParty;
    const { code = 500 } = await url({
      data: {
        ...val,
        code: dataInfo.code,
      },
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  return (
    <Fragment>
      <Modal
        title={'失联党员'}
        visible={visible}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
      >
        {visible && (
          <Fragment>
            <Form form={form} {...formItemLayout} onFinish={onFinish} initialValues={dataInfo}>
              <Row>
                <Col span={24}>
                  <Form.Item
                    name="approvalOrganName"
                    label="批准机构名称"
                    rules={[{ required: true, message: '请输入批准机构名称' }]}
                  >
                    <Input placeholder={'请输入批准机构名称'} />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    name="d52Code"
                    label="批准机构级别"
                    rules={[{ required: true, message: '请选择批准机构级别' }]}
                  >
                    <DictTreeSelect initValue={dataInfo['d52Code']} codeType={'dict_d52'} backType={'object'} parentDisable={true}/>
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    name="approvalTime"
                    label="批准时间"
                    rules={[{ required: true, message: '请输入批准时间' }]}
                  >
                    <Date />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Fragment>
        )}
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
