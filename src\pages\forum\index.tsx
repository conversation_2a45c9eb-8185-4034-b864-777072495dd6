import React, { Fragment, useEffect, useRef, useState } from 'react';
import { Avatar, Button, Form, Input, Tooltip } from 'antd';
import style from '@/pages/desktop/index.less';
import { getSession } from '@/utils/session';
import Editors from '@/components/Editor';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import NowOrg from '@/components/NowOrg';
import ListTable from '@/components/ListTable';
import Add from './components/add';
import { getList } from './services';
import { _history as router } from '@/utils/method';

const Search = Input.Search;
const { TextArea } = Input;
const headItems = [
  { name: 'find', iocn: require('@/assets/desktop/find.svg') },
  { name: 'light', iocn: require('@/assets/desktop/light.svg') },
  { name: 'bell', iocn: require('@/assets/desktop/bell.svg') },
];
const Editor = (props: any) => {
  const [form] = Form.useForm();
  const { onSubmit, submitting } = props;
  const onFinish = (e) => {
    console.log(e);
  };
  return (
    <div style={{ padding: 20 }}>
      <Form form={form} onFinish={onFinish}>
        <Form.Item name='talk'>
          <Editors id={'forum'} onChange={(val) => {
            form.setFieldsValue({
              talk: val,
            });
          }}
            // init={_isEmpty(details) ? '' : details['acPlan']}
          />
        </Form.Item>
        <Form.Item>
          <Button loading={submitting}
                  onClick={() => {
                    form.submit();
                  }} type="primary">
            发表帖子
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

const index = () => {
  const newAddRef: any = useRef();
  let filterHeight =`calc(100vh - ${288}px)`;
  const [pagination, setPagination] = useState({ pageSize: 20, current: 1, total: 0 });
  const [listLoading, setListLoading] = useState(false);
  const [list, setList] = useState<any>([]);
  const user = getSession('user') || {};

  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 50,
      align: 'center',
      render: (text, record, index) => {
        return (pagination['current'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    {
      title: '问题标题',
      width: 200,
      dataIndex: 'questionsBriefly',
      render: (text, record, index) => {
        return <a onClick={() => {
          newAddRef.current.open({
            info: { code: record.code },
            type: 'detail',
          });
        }}>{text}</a>;
      },
    },
    {
      title: '回复状态',
      width: 200,
      dataIndex: 'replySituation',
    },
    {
      title: '查看人数',
      width: 100,
      dataIndex: 'lookNumber',
      align: 'center',
    },
    {
      title: '修复状态',
      width: 100,
      dataIndex: 'stateRepair',
      align: 'center',
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   width: 100,
    //   render: (text, record) => {
    //     return (
    //       <div>
    //         <a onClick={() =>{
    //           newAddRef.current.open({
    //             info:{code:record.code},
    //             type:'edit'
    //           });
    //         }}>编辑</a>
    //         <Divider type="vertical"/>
    //         <Popconfirm title="确定要删除吗？" onConfirm={async () => {
    //           const {code = 500 } = await feedbackDelete({code:record.code});
    //           if(code === 0){
    //             Tip.success('操作提示', '操作成功');
    //             getLists({ pageNum: 1 });
    //           }
    //         }}>
    //           <a href={'#'} className={'del'}>删除</a>
    //         </Popconfirm>
    //       </div>
    //     )
    //   },
    // }
  ];

  const getLists = async (p = {}) => {
    setListLoading(true);
    const {
      code = 500,
      data: {
        list = [],
        records = [],
        pageNumber: current = 1,
        pageSize = 20,
        totalRow: total = 0,
      } = {},
    } = await getList({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
        type: '2',
        ...p,
      },
    });
    setListLoading(false);
    if (code === 0) {
      setList(list);
      setPagination({ current, total, pageSize });
    }
  };

  useEffect(() => {
    getLists({ pageNum: 1 });
  }, []);

  return (
    <Fragment>
      <div className={style.bg} style={{ backgroundColor: 'white' }}>
        <div className={style.head}>
          <div className={style.logoTable}>
            <div className={style.logo} style={{cursor: 'pointer'}} onClick={()=>{
              router.push('/desktop');
            }}>
              <img src={require('@/assets/desktop/title2.png')}/>
            </div>
            <div className={style.btns}>
              {/* {
                headItems.map((item, index) => {
                  return (
                    <div key={index} className={style.btnsIcon}>
                      <img src={item['iocn']}/>
                    </div>
                  );
                })
              } */}
              <div className={style.avator}>
                <Avatar style={{ backgroundColor: '#7265e6', verticalAlign: 'middle' }}
                        size="large">{user['name'] || ''}</Avatar>
                <span className={style.avatorName}>
                   <Tooltip title={user['name']}>
                     {/*{isEmpty(user['name']) ? '' : user['name'].length > 3 ? user['name'].substring(0,3) + '...' : user['name'] }*/}
                     {user['name']}
                  </Tooltip>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div style={{ padding: '0 10px' }}>
          <NowOrg
            extra={
              <Fragment>
                <Search
                  style={{ width: 200, marginLeft: 16 }}
                  placeholder={'请输入检索关键词'}
                  onSearch={(value) => {
                    getLists({ orgName: value });
                  }}
                />
                <Button
                  type="primary"
                  style={{ margin:'0 16px'}}
                  icon={<LegacyIcon type={'plus'}/>}
                  onClick={() => {
                    newAddRef.current.open({
                      info: {},
                      type: 'add',
                    });
                  }}
                >
                  发布
                </Button>
                <Button onClick={() => router.push('/desktop')}>返回首页</Button>
              </Fragment>
            }
          />
          <ListTable
            scroll={{ y: filterHeight }}
            columns={columns}
            data={list}
            pagination={pagination}
            showQuickJumper={true}
            onPageChange={(page, pageSize) => {
              getLists({ pageNum: page, pageSize });
            }}
          />
        </div>
        <Add ref={newAddRef} onOK={() => {
          getLists({ pageNum: 1 });
        }}/>
      </div>
    </Fragment>
  );
};
export default index;
