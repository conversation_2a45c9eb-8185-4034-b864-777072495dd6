import React,{Fragment} from 'react';
import { Avatar, List, Spin } from 'antd';
import request from "@/utils/request";
import qs from 'qs';
interface Interface {
  action:string,
  payload:object,
  renderItem:(any)=>React.ReactNode,
  method?:string,
  listExtraConfig?:object,
}
export default class index extends React.Component<Interface, any> {
  static defaultProps={
    action:'',
    payload:{},
    renderItem:(item)=><div/>,
    method:'POST',
    listExtraConfig:{},
    loading:false,
  };
  constructor(props) {
    super(props);
    this.state ={
      talkList:[],
      talkPagination:{},
      hasMore:true
    }
  }
  componentDidMount(): void {
    this.getTalkList(1)
  }
  getTalkList =(pageNum,pageSize = 10)=>{
    const {talkList} = this.state;
    const {action,payload = {},method} = this.props;
    const requestRes=(res)=>{
      const { code = 500,data:{list = [],totalRow = 0, pageSize:resPageSize = 10,pageNumber = 1}={}} = res;
      if(code===0){
        this.setState({
          talkList:talkList.concat(list),
          hasMore:list.length === 10,
          talkPagination:{
            current:pageNumber,
            pageSize:resPageSize,
            total:totalRow,
          }
        })
      }
    };
    let data = {
      pageNum,
      pageSize,
      ...payload,
    };
    let body = {
      data:{
        ...data
      }
    };
    if(action){
      switch (method) {
        case 'POST':
          request(action,{
            method,
            body,
          }).then(res=>requestRes(res));
          break;
        case 'Get':
          request(`${action}?${qs.stringify(data)}`,{
            method,
          }).then(res=>requestRes(res));
          break;
      }
    }
  };
  onLoadMore=()=>{
    const {hasMore,talkPagination = {}} = this.state;
    const {current = 1} = talkPagination;
    if(hasMore){
      this.getTalkList(current+1)
    }
  };
  render() {
    const {renderItem,listExtraConfig} = this.props;
    const {hasMore,talkList} = this.state;
    return (
      <Fragment>
        <List
          {...listExtraConfig}
          itemLayout="horizontal"
          loadMore={ hasMore ? (
            <div
              style={{
                textAlign: 'center',
                marginTop: 12,
                height: 32,
                lineHeight: '32px',
              }}
            >
              <a onClick={this.onLoadMore}>加载更多...</a>
            </div>
          ):null}
          dataSource={talkList}
          renderItem={(item)=>renderItem(item)}
        />
      </Fragment>
    );
  }
}
