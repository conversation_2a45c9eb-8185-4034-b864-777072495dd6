import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import style from './index.less';
import NowOrg from '@/components/NowOrg';
import { Button, Form, Alert, Modal, Space, Radio, Input, Row, Col, Switch } from 'antd';
import ListTable from '@/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import SpinProgress from '@/components/SpinProgress';
import { zymemInfo } from '@/pages/mem/services'
import Tip from '@/components/Tip'
import Date from '@/components/Date';
import moment from 'moment'
import { connect } from "dva";
import { _history, unixMoment } from '@/utils/method';
import MemSelect from '@/components/MemSelect';
import _isNumber from 'lodash/isNumber';
import _isString from 'lodash/isString';
import { compare, compareDate, getContextPerson } from '@/pages/developMem/services/index'
const { TextArea } = Input;
const RadioGroup = Radio.Group;

const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};
const formItemLayout1 = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 9 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
    },
};
const formItemLayout2 = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 12 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 },
    },
};
const index: any = (props, ref) => {
    const [form] = Form.useForm();
    const {
        dataInfo = {},
        commonDict: { dict_d28 = [] } = {},
        onOk
    } = props;
    console.log(dataInfo, 'dataInfodataInfodataInfo')
    useImperativeHandle(ref, () => ({
        showModal: (obj) => {
            open(obj);
        },
        submit: () => {
            form.submit()
        }
    }));

    const { location: { pathname = '' } = {} } = _history

    const [loading, setLoading] = useState(false);

    const [modalVisible, setModalVisible] = useState(false);
    const [row, setRow] = useState<any>({})
    const [info, setInfo] = useState<any>({})
    const [hasMemValue, setHasMemValue] = useState(true)
    const [showIsFullYear, setShowIsFullYear] = useState(false)
    const [isFullYear, setIsFullYear] = useState(true)
    const [toactiveContextPerson, setToactiveContextPerson] = useState<any>('')
    const [toobjContextMem, setToobjContextMem] = useState<any>('')

    const open = (obj) => {
        setRow(obj)
        // getData(obj)
        setModalVisible(true)
    }
  
    const getData = async () => {
        const { code = 500, data = {} } = await zymemInfo({ memCode: dataInfo.code }) //预备党员转正-人员信息
        if (code === 0) {
            form.setFieldsValue({ topartTurnPartyDate: moment(data.topartTurnPartyDate) })
            setInfo({ name: data.name, extendPreparDate: moment(data.extendPreparDate).format('YYYY-MM-DD') })
        }
    }
    const cancel = () => {
        setRow({})
        setModalVisible(false)
    }
    const hadndleFinish = async (e) => {
        const { orgCode, orgName, memOrgCode, code: memCode, d08Code, d08Name, name, orgZbCode } = dataInfo;
        console.log('🚀 ~ e:', e);
        // e['extendEndTime'] = moment(e['extendEndTime']).valueOf()
        e = unixMoment(['topartCommitteeDate', 'topartTurnPartyDate', 'topartOathDate', 'activeDate', 'objectDate'], e);
        [
            'hasStaffOrganization',
        ].map((item) => {
            e[`${item}`] = e[`${item}`] == '1' ? 1 : 0;
        });
        e['toactiveContextPerson'] = _isEmpty(e['toactiveContextPerson'])
            ? ''
            : hasMemValue
                ? e['toactiveContextPerson'].map((item) => item['code']).toString()
                : e['toactiveContextPerson'];
        if (_isString(e['toobjContextMem'])) {
            e['toobjContextMem'] = toactiveContextPerson;
        } else {
            e['toobjContextMem'] = _isEmpty(e['toobjContextMem']) ? "" : hasMemValue ? e['toobjContextMem'].map(item => item['code']).toString() : e['toobjContextMem'];
        }

        if (!_isEmpty(dict_d28)) {
            dict_d28.forEach(item => {
                if (item['key'] === e['d28Code']) {
                    e['d28Name'] = item['name']
                }
            })
        }
        e['logOrgCode'] = memOrgCode;
        e['orgName'] = orgName;
        e['orgCode'] = orgCode;
        e['memCode'] = memCode;
        e['d08Code'] = d08Code;
        e['d08Name'] = d08Name;
        e['name'] = name;
        e['orgZbCode'] = orgZbCode;
        console.log(e, '表单值')
        onOk && onOk(e)
    };

    const RadioGroupOnChange = ({ target: { value = '' } = {} } = {}) => {
        // this.setState({d28Value:value})
    };

  

    useEffect(() => {
        form.setFieldsValue({ hasStaffOrganization: true })
        if (dataInfo.processNode.startsWith('YBQ')) {
            getData()
        }
    }, [dataInfo]);


    return (
        <div style={{ paddingTop: '2px' }}>
            <Form form={form} onFinish={hadndleFinish}  {...formItemLayout}>
                <Row>
                    <Col span={4}>
                        <Form.Item
                            name="name"
                            label="转正人员"
                            {...formItemLayout1}
                            rules={[{ required: false, message: '请输入考察结果' }]}
                        >
                            <span>{dataInfo.name}</span>
                        </Form.Item>
                    </Col>
                    <Col span={4}>
                        <Form.Item
                            name="extendPreparDate"
                            label="预备期满时间"
                            labelCol={
                                { span: 11 }
                            }
                            wrapperCol={
                                { span: 13 }
                            }
                            rules={[{ required: false, message: '请输入考察结果' }]}
                        >
                            <span>{dataInfo?.extendPreparDate ? moment(dataInfo.extendPreparDate).format('YYYY-MM-DD') : ''}</span>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item
                            name="topartTurnPartyDate"
                            label="支部大会讨论通过时间"
                            {...formItemLayout1}
                            rules={[{ required: true, message: '请输入支部大会讨论通过时间' }]}
                        >
                            <Date isDefaultEnd={false} />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item
                            name="d28Code"
                            label="转正类型"
                            rules={[{ required: true, message: '请输入转正类型' }]}
                        >
                            <RadioGroup onChange={RadioGroupOnChange}>
                                {
                                    !_isEmpty(dict_d28) && dict_d28.map(item => {
                                        if (row['isExtendPrepare'] === 1) {
                                            if (item['key'] === '12') {
                                                return (
                                                    <Radio value={item['key']} key={item['key']}>{item['name']}</Radio>
                                                )
                                            }
                                        } else {
                                            if (item['key'] === '11' || item['key'] === '13') {
                                                return (
                                                    <Radio value={item['key']} key={item['key']}>{item['name']}</Radio>
                                                )
                                            }
                                        }
                                    })
                                }
                            </RadioGroup>
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </div>


    );
};
// @ts-ignore
// export default React.forwardRef(index);
export default connect(({ commonDict }: any) => ({ commonDict }), undefined, undefined, { forwardRef: true })(React.forwardRef(index))
