/**
 * 流动党员历史
 */
import React from 'react';
import {connect} from "dva";
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import WhiteSpace from '@/components/WhiteSpace';
import { Button, Divider, Input, Popconfirm, Tabs, Dropdown, Menu, Modal, Row, Col } from 'antd';
import NowOrg from 'src/components/NowOrg';
import moment from 'moment';
import { isEmpty, setListHeight } from '@/utils/method';
import {getSession} from "@/utils/session";
import { isJSXNamespacedName } from '@babel/types';
const Search = Input.Search;
const TabPane = Tabs.TabPane;

@connect(({unit,commonDict,loading,flowMem})=>({flowMem,unit,commonDict,loading:loading.effects['unit/getList']}))
export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      dataInfo:undefined,
      filter:{},//筛选器
      search:{},//搜索框
      subordinate:getSession('subordinate')
    };
  }

  componentDidMount() {
    const org=getSession('org') || {};
    this.setState({orgCode:org['orgCode']});
    setListHeight(this);
    this.selectList(1 ,10,org['orgCode']);
  }
  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org=getSession('org') || {};
    const subordinate = getSession('subordinate') || '0';
    if ((!isEmpty(this.state['orgCode']) && this.state['orgCode'] !== org['orgCode']) || (subordinate !== this.state.subordinate)) {
      this.setState({
        orgCode:org['orgCode'],
        subordinate
      },()=>{
        this.selectList(1,10,org['orgCode'])
      })
    }
  }
  selectList=( pageNum=1,size=10,code='')=>{
    this.props.dispatch({
      type:'flowMem/getHistory',
      payload:{
        data:{
          pageNum:pageNum,
        pageSize:size,
        orgCode:code,
        ...this.state.filter,
        }
      }
    })
  };

  onPageChange=(page,pageSize)=>{
    this.selectList(page,pageSize,this.state.orgCode)
    // let {query}=this.props.location;
    // router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  filterChange=(val)=>{
    this.setState({
      filter:val,
    },()=>this.selectList(1,10,this.state['orgCode']));
  };
  action=(val?:any)=>{
    // const {search,filter}=this.state;
    // const {pagination={}}=this.props.unit;
    // const {current,pageSize}=pagination;
    // const {query}=this.props.location;
    // const org=getSession('org')||{};
    // this.props.dispatch({
    //   type:'unit/getList',
    //   payload:{
    //     data:{
    //       mainUnitOrgCode:org['orgCode'],
    //       manageUnitOrgCode:org['orgCode'],
    //       isCreateOrg:0,
    //       pageNum:current,
    //       pageSize,
    //       ...query,
    //       ...search,
    //       ...filter,
    //       ...val,
    //     }
    //   }
    // })
  };
  confirm=(record)=>{
    this.props.dispatch({
      type:'flowMem/del',
      payload:{
        data:{
          code:record['code']
        }
      }
    }).then(res=>{
      if (res['code'] === 0) {
        this.selectList(1,10,this.state['orgCode'])
      }
    });
  };
  search=(value)=>{
    this.setState({
      search:{unitName:value}
    },()=>this.action());
  };

  render() {
    const {loading,commonDict,flowMem:{list=[],pagination={}}}=this.props;
    const {dataInfo,filterHeight,detailInfo={}}=this.state;
    const columns=[
      {
        title:'姓名',
        dataIndex:'memName',
        width:200,
      },
      {
        title:'所在组织',
        dataIndex:'memOrgName',
        width:200,
      },
      {
        title:'流出组织',
        dataIndex:'outflowOrgName',
        width:200,
      },
      {
        title:'流出原因',
        dataIndex:'outflowReasonName',
        width:200,
      },

      {
        title:'流动状态',
        dataIndex:'flowStatus',
        width:200,
        render:(text)=>{
          return <span>{text===2? '停止流动':'流动中'}</span>
        }
      },
      {
        title:'流出时间',
        dataIndex:'outflowDate',
        width:200,
        render:(text)=>{
          return <span>{isEmpty(text)?'':moment(text).format('YYYY-MM-DD')}</span>
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        width:100,
        render:(text,record)=>{
          return(
            <Popconfirm title="确定要删除吗？" onConfirm={()=>this.confirm(record)}>
              <a href={'#'} className='del'>删除</a>
            </Popconfirm>
          )
        },
      },
    ];
    const filterData=[
      {
        key:'sexCodeList',name:'人员性别',value:[{key:'1',name:'男'},{key:'0',name:'女'}],
      },
      {
        key:'d09CodeList',name:'工作岗位',value:commonDict[`dict_d09_tree`],
      },
      {
        key:'d07CodeList',name:'学历教育',value:commonDict[`dict_d07_tree`],
      },
      {
        key:'d41CodeList',name:'原因类型',value:commonDict[`dict_d41_tree`],
      },
    ];
    return(
      <div style={{height:'100%', overflow:'hidden'}}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        <NowOrg extra={
          <React.Fragment>
            {/*<Search style={{width:200,marginLeft:16}} onSearch={this.search}/>*/}
          </React.Fragment>
        }/>
        <RuiFilter data={filterData}
                   openCloseChange={()=>setListHeight(this,20)}
                   onChange={this.filterChange}/>
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:filterHeight}}
         
          columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
      </div>
    )
  }
}
