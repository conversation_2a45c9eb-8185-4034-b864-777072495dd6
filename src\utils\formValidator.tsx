// 字节长度
function byteLength(str) {
  const encoder = new TextEncoder('utf-8');
  const buffer = encoder.encode(str);
  return buffer.length;
}

// 判断是否为中国大陆手机号码
function isMobilePhoneNumber(input) {
  const mobilePattern = /^1\d{10}$/;
  return mobilePattern.test(input);
}

// 判断是否为中国大陆座机号码
function isLandlinePhoneNumber(input) {
  const landlinePattern = /^\d{3,4}-\d{7,8}$|^\d{7,8}$/;
  return landlinePattern.test(input);
}
// 是否是汉字
function isChinese(str) {
  var regex = /[\u4e00-\u9fa5]/g;
  return regex.test(str);
}

// 校验长度
export const validateLength = (arg: any, len: number, charLen) => {
  const [rule, value, callback] = arg;
  if (value) {
    if (isChinese(value)) {
      if (value?.length > len) {
        callback(`长度不能超过${len}个汉字`);
      }
    } else {
      if (byteLength(value) > charLen) {
        callback(`长度不能超过${charLen}字节`);
      }
    }
  }
  callback();
};

// 校验电话
export const validateMobilePhoneNumber = (rule, value, callback) => {
  if (value) {
    if (value?.split('-').length == 2 && !isLandlinePhoneNumber(value)) {
      callback('请输入正确的座机号码');
    }
    if (value?.split('-').length != 2 && !isMobilePhoneNumber(value)) {
      callback('请输入正确的手机号码');
    }
  }
  callback();
};
