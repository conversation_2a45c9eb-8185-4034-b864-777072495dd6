import React, { Fragment, useState, useImperativeHandle, useRef } from 'react'
import { Modal, Form, Radio, Input, Checkbox, Row, Col } from 'antd';
import MemSelect from '@/components/MemSelect';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import _cloneDeep from 'lodash/cloneDeep';
import _isEmpty from 'lodash/isEmpty';
import { lockFiled } from '../../../../services/index';

import { getSession } from '@/utils/session';
const { Group } = Checkbox;
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 19 },
};

const lockArr = [
  { key: 'name', value: '姓名' },
  { key: 'sexCode', value: '性别' },
  { key: 'idcard', value: '身份证号' },
  { key: 'birthday', value: '出生日期' },
  { key: 'd48Code', value: '籍贯' },
  { key: 'd06Code', value: '民族' },
  { key: 'd09Code', value: '岗位' },
  { key: 'd07Code', value: '学历' },
  { key: 'phone', value: '联系电话' },
  { key: 'd21Code', value: '一线情况' },
  { key: 'd27Code', value: '加入党组织方式' },
  { key: 'd49Code', value: '党费交纳情况' },
  { key: 'homeAddress', value: '家庭住址' },
];
export { lockArr };
const SearchModal = (props: any, ref) => {
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const searchRef: any = useRef();
  const [form] = Form.useForm();

  const onCancel = () => {
    setVisible(false);
    form.resetFields();
  }
  const onOk = () => {
    let infos = searchRef.current.getInfo();
    props.onOk && props.onOk(infos);
    setVisible(false);
  }
  useImperativeHandle(ref, () => ({
    open: (val: any) => {
      form.setFieldsValue({ lockType: '1' })
      setVisible(true);
    },
  }));
  const onFinish = async (val) => {
    const { lockType, codeList, orgCodeList } = val;
    switch (lockType) {
      case '1':
        val['codeList'] = codeList.map(it => it.code);
        break;
      case '2':
        val['orgCodeList'] = orgCodeList.map(it => it.code);
        break;
    }
    setConfirmLoading(true);
    const { code = 500 } = await lockFiled({ data: val });
    setConfirmLoading(false);
    if (code == 0) {
      Tip.success('操作提示', '操作成功');
      onCancel();
    }
  }
  return (
    <Fragment>
      <Modal
        title={'锁定'}
        visible={visible}
        onCancel={onCancel}
        onOk={() => form.submit()}
        width={800}
        confirmLoading={confirmLoading}
        destroyOnClose={true}
        bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
      >
        {visible && <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Form.Item
            name='lockType'
            label="锁定类型"
            rules={[{ required: true, message: '请选择锁定类型' }]}
          >
            <Radio.Group>
              <Radio value={'1'}>选择党员</Radio>
              <Radio value={'2'}>选择组织</Radio>
              {/* <Radio value={'3'}>自定义组织层级码</Radio> */}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.lockType !== currentValues.lockType}
          >
            {({ getFieldValue }) => {
              if (getFieldValue('lockType') === '1') {
                return (
                  <Form.Item name='codeList'
                    {...formItemLayout}
                    label="选择党员"
                    rules={[{ required: true, message: '选择党员' }]}
                  >
                    <MemSelect placeholder="请选择党员" checkType={'checkbox'} />
                  </Form.Item>
                );
              }
              if (getFieldValue('lockType') === '2') {
                return (
                  <Form.Item name='orgCodeList'
                    {...formItemLayout}
                    label="选择组织"
                    rules={[{ required: true, message: '选择组织' }]}
                  >
                    <OrgSelect
                      multiple={true}
                      // oorg={{ orgCode: getSession('user')?.orgCode }}
                      // initValue={basicInfo['manageOrgName']}
                      placeholder={'选择组织'}
                    />
                  </Form.Item>
                );
              }
              if (getFieldValue('lockType') === '3') {
                return (
                  <Form.Item name='memOrgCodeList'
                    {...formItemLayout}
                    label="组织层级码"
                    rules={[{ required: true, message: '自定义组织层级码' }]}
                  >
                    <Input />
                  </Form.Item>
                );
              }
            }}
          </Form.Item>
          <Form.Item name='filed'
            {...formItemLayout}
            label="锁定内容"
            rules={[{ required: true, message: '锁定内容' }]}
          >
            <Group>
              <Row>
                {
                  !_isEmpty(lockArr) && lockArr.map((item, index) => {
                    return (
                      <Col span={6} key={index}><Checkbox value={item['key']}>{item['value']}</Checkbox></Col>
                    )
                  })
                }
              </Row>
            </Group>
          </Form.Item>
        </Form>}
      </Modal>
    </Fragment>
  )
}
export default React.forwardRef(SearchModal);
