import React from 'react';
import ListTable from '@/components/ListTable';
import { getSession } from '@/utils/session';
import { Divider, Popconfirm, Button } from 'antd';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import { Alert } from 'antd';
import moment from 'moment';
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {

    }
  }
  componentDidMount(): void {
    const {memBasic:{basicInfo:{code = ''} = {}}={}} = this.props;
    this.getList(1,100, code);
  };
  getList=(pageNum = 1, pageSize = 100, memCode = '')=>{
    !_isEmpty(memCode) && this.props.dispatch({
      type:'memBasic/administrative',
      payload:{pageNum,pageSize, memCode}
    })
  };
  onPageChange=(page)=>{
    const {memBasic:{basicInfo:{code = ''} = {}}={}} = this.props;
    this.getList(page,100,code);
  };
  componentWillUnmount(): void {
    this.props.dispatch({
      type:'memBasic/updateState',
      payload:{list_administrative:[],pagination_administrative:{}}
    })
  }

  render() {
    const {memBasic = {},loading:{effects = {}} ={}} = this.props;
    const {list_administrative, pagination_administrative = {}} = memBasic;
    const {current, pageSize} = pagination_administrative;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:58,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'班子成员职务',
        dataIndex:'d25Name',
      },
      {
        title:'任职开始时间',
        dataIndex:'startDate',
        render:(text, record)=>{
          return (<div>{!_isNumber(text) ? '' : moment(text).format('YYYY-MM-DD')}</div>)
        }
      },
      {
        title:'任职结束时间',
        dataIndex:'endDate',
        render:(text, record)=>{
          return (<div>{!_isNumber(text) ? '' : moment(text).format('YYYY-MM-DD')}</div>)
        }
      },
      {
        title:'任职所在单位',
        dataIndex:'unitName'
      },
      {
        title:'兼任情况说明',
        dataIndex:'d26Name',
      },
    ];
    return (
      <div>
        <Alert message="行政职务可在党单位管理>编辑>班子成员中进行添加以及维护" type="info" showIcon />
        <div style={{marginBottom:10}}/>
        <ListTable columns={columns} data={list_administrative} pagination={pagination_administrative} onPageChange={this.onPageChange}/>
      </div>
    );
  }
}
