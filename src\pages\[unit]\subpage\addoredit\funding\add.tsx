import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Form, Input, InputNumber, Modal } from 'antd';
import Tip from '@/components/Tip';
import { add, updateSecondary } from './services';
import _isEmpty from 'lodash/isEmpty';

import { findDictCodeName } from '@/utils/method';
import { getSession } from '@/utils/session';

const formItemLayout2 = {
  labelCol: { span: 12 },
  wrapperCol: { span: 12 },
};
const formItemLayout = {
  labelCol: { span: 18 },
  wrapperCol: { span: 6 },
};

const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const { width = 800 } = props;
  const [title, setTitle] = useState('新增');
  useImperativeHandle(ref, () => ({
    open: (dataInfo) => {
      open(dataInfo);
    },
  }));
  const open = (dataInfo) => {
    setVisible(true);
    if (!_isEmpty(dataInfo)) {
      setTitle('编辑');
      setDataInfo(dataInfo);
      form.setFieldsValue({
        ...dataInfo,
      });
    }
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e: any) => {
    const { unit: { basicInfo = {} } = {}, onOK } = props;
    let val = { ...dataInfo, ...e };
    let url = add;
    const org = getSession('org') || {};
    setConfirmLoading(true);
    const { code: resCode = 500 } = await url({
      data: {
        ...val,
        unitCode: basicInfo['code'],
        orgCode: org['orgCode'],
        orgName: org['name'],
      },
    });
    if (e['fiscalFunds'] > 200) {
      Tip.error('操作提示', '财政专项列支非公企业党建工作经费（万元）不能大于200')
      return;
    }
    if (e['partyExpenses'] > 200) {
      Tip.error('操作提示', '党费拨补非公企业党建工作经费（万元）不能大于200')
      return;
    }
    setConfirmLoading(false);
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  return (
    <Fragment>
      <Modal
        title={title}
        destroyOnClose
        visible={visible}
        confirmLoading={confirmLoading}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={'550px'}
      >
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Form.Item
            {...formItemLayout2}
            name="time"
            label="时间"
            rules={[{ required: true, message: '请输入时间' }]}
          >
            <Input type={"number"} addonAfter={<span>年</span>} min={0} />
          </Form.Item>
          <Form.Item
            name="fiscalFunds"
            label="财政专项列支非公企业党建工作经费（万元）"
            rules={[{ required: false, message: '财政专项列支非公企业党建工作经费（万元）' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="partyExpenses"
            label="党费拨补非公企业党建工作经费（万元）"
            rules={[{ required: false, message: '党费拨补非公企业党建工作经费（万元）' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="activityServiceCenter"
            label="非公企业集聚区综合性党群活动服务中心（个）"
            rules={[{ required: false, message: '请输入院系名称' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="newActivityServiceCenter"
            label="新建立非公企业集聚区综合性党群活动服务中心（个）"
            rules={[{ required: false, message: '请输入院系名称' }]}
          >
            <InputNumber style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
