import request from "@/utils/request";
import qs from 'qs';



export function unitMv(params) {
  return request(`/api/unit/unitMv`, {
    method: 'POST',
    body: params,
  });
}

export function committeeBackOut(params) {
  return request(`/api/unit/committee/backOut`, {
    method: 'POST',
    body: params,
  });
}

export function delUnitCountryside(params) {
  return request(`/api/unit/countryside/delUnitCountryside`, {
    method: 'POST',
    body: params,
  });
}

export function addElect(params) {
  return request(`/api/unit/committee/addElect`, {
    method: 'POST',
    body: params,
  });
}
export function updateElect(params) {
  return request(`/api/unit/committee/updateElect`, {
    method: 'POST',
    body: params,
  });
}


export function getMemByOrg(params) {
  console.log('getMemByOrg====', params);
  return request(`/api/unit/getMemByOrg`, {
    method: 'POST',
    body: params,
  });
}
// 替换/unit/getMemByOrg的接口
export function getMemList(params) {
  console.log('getMemList====', params);
  return request(`/api/unit/countryside/getList`, {
    method: 'POST',
    body: params,
  });
}

export function collectiveEconomicAdd(params) {
  return request(`/api/unit/collectiveEconomic/add`, {
    method: 'POST',
    body: params,
  });
}
export function incomeAdd(params) {
  return request(`/api/unit/income/add`, {
    method: 'POST',
    body: params,
  });
}

export function getListByUnitCode(params) {
  return request(`/api/unit/income/getListByUnitCode`, {
    method: 'POST',
    body: params,
  });
}

export function incomeGetList(params) {
  console.log('incomeGetList====', params);
  return request(`/api/unit/income/getList`, {
    method: 'POST',
    body: params,
  });
}

export function incomeDelete(params) {
  return request(`/api/unit/income/delete?${qs.stringify(params)}`);
}


export function incomeDetails(params) {
  return request(`/api/unit/income/details?${qs.stringify(params)}`);
}
// export function delUnitCountryside(params) {
//   return request(`/api/unit/countryside/delUnitCountryside?${qs.stringify(params)}`);
// }
export function addUnitCountryside(params) {
  console.log('addUnitCountryside====', params);
  return request(`/api/unit/countryside/addUnitCountryside`, {
    method: 'POST',
    body: params,
  });
}
export function updateUnitCountryside(params) {
  console.log('updateUnitCountryside====', params);
  return request(`/api/unit/countryside/updateUnitCountryside`, {
    method: 'POST',
    body: params,
  });
}

export function countrysideGetList(params) {
  return request(`/api/unit/countryside/getList?${qs.stringify(params)}`);
}

export function getList(params) {
  return request(`/api/unit/getList`, {
    method: 'POST',
    body: params,
  });
}
export function collectiveEconomicGetList(params) {
  console.log('collectiveEconomicGetList====', params);
  return request(`/api/unit/collectiveEconomic/getList`, {
    method: 'POST',
    body: params,
  });
}
export function collectiveEconomicDelete(params) {
  return request(`/api/unit/collectiveEconomic/delete?${qs.stringify(params)}`);
}

export function collectiveEconomicDetails(params) {
  return request(`/api/unit/collectiveEconomic/details?${qs.stringify(params)}`);
}

export function add(params) {
  return request(`/api/unit/addUnit`, {
    method: 'POST',
    body: params,
  });
}

export function updated(params) {
  return request(`/api/unit/updateUnit`, {
    method: 'POST',
    body: params,
  });
}

export function del(params) {
  return request(`/api/unit/delUnit`, {
    method: 'POST',
    body: params,
  });
}

export function findOrg(params) {//基本信息查询
  return request(`/api/unit/findByCode?${qs.stringify(params)}`);
}

//班子成员
export function itteeList(params) {
  return request(`/api/unit/committee/getList?${qs.stringify(params)}`);
}
// 届次列表
export function getElectList(params) {
  return request(`/api/unit/committee/getElectList?${qs.stringify(params)}`);
}

export function addUnitCommittee(params) {
  return request(`/api/unit/committee/addUnitCommittee`, {
    method: 'POST',
    body: params,
  });
}
export function updateUnitCommittee(params) {
  console.log('updateUnitCommittee====', params);
  return request(`/api/unit/committee/updateUnitCommittee`, {
    method: 'POST',
    body: params,
  });
}
export function delUnitCommittee(params) {
  return request(`/api/unit/committee/delUnitCommittee?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function unitFind(params) {
  return request(`/api/unit/committee/findByCode?${qs.stringify(params)}`, {
    method: 'GET',
  });
}

export function delElect(params) {
  return request(`/api/unit/committee/delElect?${qs.stringify(params)}`, {
    method: 'GET',
  });
}

// 城市基层党建情况新增＆修改
export function cityAdd(params) {
  return request(`/api/unit/citySituation/add`, {
    method: 'POST',
    body: params,
  });
}


export function cityFind(params) {
  return request(`/api/unit/citySituation/findByCode?${qs.stringify(params)}`, {
    method: 'GET',
  });
}

// 班子成员根据党员姓名查找信息
export function getUnitCommitteeSrcOrg(para) {
  console.log('getUnitCommitteeSrcOrg.params===', para);
  return request(`/api/unit/committee/getUnitCommitteeSrcOrg?${qs.stringify(para)}`, {
    method: 'get',
  });
}
// 班子成员新增校验
export function getListByIdcard(params) {
  return request(`/api/committee/getListByIdcard?${qs.stringify(params)}`, {
    method: 'GET',
  });
}
// 选调生列表
export function selectlist(params) {
  return request(`/api/unit/memSelect/list`, {
    method: 'POST',
    body: params,
  });
}
export function selectadd(params) {
  console.log('selectadd.params====',params);
  const { data = {} } = params;
  let act = '/api/unit/memSelect/add';
  if (data.code) {
    act = '/api/unit/memSelect/update';
  }
  return request(act, {
    method: 'POST',
    body: params,
  });
}

export function selectdel(params) {
  return request(`/api/unit/memSelect/delete?${qs.stringify(params)}`, {
    method: 'GET',
  });
}

export function selectleave(params) {
  return request(`/api/unit/memSelect/leave`, {
    method: 'POST',
    body: params,
  });
}