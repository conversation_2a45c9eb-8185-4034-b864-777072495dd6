import React, {
  Fragment,
  useImperativeHandle,
  useState,
} from 'react';
import { Form, Modal, Input, Spin, Button } from 'antd';
import { findOnUniqueCode } from '@/pages/transfer/services'
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import Tip from '@/components/Tip';

const index = (props: any, ref: any) => {
  const {
    closeCallBack,
    width = 800,
    refresh,
  } = props;
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleOk = async (e: any) => {
    const { uniqueCode } = e || {};
    setConfirmLoading(true);
    const { code = 500 } = await findOnUniqueCode({ uniqueCode });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      handleCancel();
      refresh && refresh();
    }
  };
  const handleCancel = () => {
    if (!confirmLoading) {
      setVisible(false);
      clear();
    }

  };
  const validatorIdcard = (rule, value, callback) => {

    const reg = /^[0-9]{11,}$/

    if (_isEmpty(value)) {
      return callback('请输入介绍信唯一编码');
    } else {
      if (!reg.test(value)) {
        return callback('只能输入数字且必须10位数以上');
      }
      return callback();
    }
  };
  useImperativeHandle(ref, () => ({
    open: () => {
      setVisible(true);
    },
    clear: () => {
      clear();
    },
  }));


  const clear = () => {
    form.resetFields();
  };


  return (
    <Fragment>
      <Modal
        title={"全国组织关系转接查询"}
        visible={visible}
        // onOk={() => {
        //   form.submit();
        // }}
        onCancel={handleCancel}
        width={width}
        maskClosable={false}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
        footer={
          [
            <Button
            type="primary"
            loading={confirmLoading}
            onClick={() => {
              form.submit();
            }} key={1}>确定</Button>,
          ]
        }
      >
        <Spin spinning={confirmLoading}>
          <Form form={form} onFinish={handleOk}>
            <Form.Item name={'uniqueCode'}
              rules={[
                // { required: true, message: '请输入介绍信唯一编码' },
                { validator: validatorIdcard },
              ]}>
              <Input></Input>
            </Form.Item>
          </Form>
        </Spin>
      </Modal>
    </Fragment >
  );
};

// @ts-ignore
export default React.forwardRef(index);
