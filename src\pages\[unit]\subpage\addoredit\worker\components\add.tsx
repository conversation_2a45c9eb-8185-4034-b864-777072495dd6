import React, { Fragment, useImperativeHandle, useRef, useState } from 'react';
import { Col, Form, Input, Modal, Radio, Row, Select, Switch, InputNumber } from 'antd';
import { getSession } from '@/utils/session';
import MemSelect from '@/components/MemSelect';
import Date from '@/components/Date';
import DictTreeSelect from '@/components/DictTreeSelect';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { addUnitCountryside, updateUnitCountryside } from '@/pages/[unit]/services';
import moment from 'moment';
import Tip from '@/components/Tip';
import UnitSelect from '@/components/UnitSelect';
import { formLabel, getIdCardInfo, correctIdcard } from '@/utils/method.js';
import { validateLength } from '@/utils/formValidator';

const formItemLayout = {
  labelCol: { span: 10 },
  wrapperCol: { span: 16 },
};
const formItemLayout2 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const { TextArea } = Input;
const RadioGroup = Radio.Group;
const index = (props: any, ref) => {
  const { pageType = '', unit: { basicInfo = {} } = {}, onOK } = props;
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [list, setList] = useState([]);
  const [pagination, setPagination] = useState({ pageSize: 10, total: 0, page: 1 });
  const [tableLoading, setTableLoading] = useState(false);
  const [selectD144Code, setSelectD144Code] = useState();
  const [isWorkVillageTag, setIsWorkVillageTag]: any = useState(0);
  const [hasLeadersHelpPeopleTag, setHasLeadersHelpPeopleTag]: any = useState(0)
  const dictTreeSelectref = useRef(null);
  const {
    width = 1200,
  } = props;

  useImperativeHandle(ref, () => ({
    open: query => {
      if (query) {
        setDataInfo(query);
        setIsWorkVillageTag(query?.isWorkVillage);
        setHasLeadersHelpPeopleTag(query?.hasLeadersHelpPeople);
        setSelectD144Code(query?.hasVillageTransferStudent);
        form.setFieldsValue({
          ...query,
          memTypeCode: query?.memTypeCode ? +query.memTypeCode : undefined,
        });
      } else {
        form.setFieldsValue({
          unitCode: basicInfo?.code,
          unitName: basicInfo?.name,
        })
      }
      open();
      // setQurey({ ...query, _key: +new Date() });
    },
  }));
  const open = () => {
    setVisible(true);
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    setConfirmLoading(false);
    setSelectD144Code(undefined);
    setIsWorkVillageTag(undefined);
    setHasLeadersHelpPeopleTag(undefined)
    form.resetFields();
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    if (e['memName'] != dataInfo['memName'] || e['memIdcard'] != dataInfo['memIdcard']) {
      let result = await correctIdcard(e['memName'], e['memIdcard']);
      if (result['code'] != '200') {
        form.setFields([{
          name: 'memIdcard',
          value: e['memIdcard'],
          errors: ['经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。']
        }])
        return
      } else {
        e['idCardReason'] = result['reason']
        e['idCardReasonName'] = result['reasonName']
      }
    }
    let val = {
      ...e,
      memTypeCode: e.memTypeCode ? '1' : '0',
      sexName: e.sexCode === '1' ? '男' : '女',
      memName: typeof e.memName === 'string' ? e.memName : _get(e, 'memName[0].name', ''),
      birthday: e.birthday ? moment(e.birthday).valueOf() : '',
    };

    // 是否村任职选调生选否的时候清空选调单位层级的值
    if (val['hasVillageTransferStudent'] === 0) {
      val['d144Code'] = undefined;
      val['d144Name'] = undefined;
      val['isDoubleFirst'] = undefined;
    }

    if (e['subsidies'] > 200) {
      Tip.error('操作提示', '到村任职补助经费（万元）不能大于200')
      return;
    }
    // 是否县乡领导班子成员帮带人 选否时清空 帮带人单位、帮带人姓名
    if( val['hasLeadersHelpPeople'] === 0){
      val['helpUnit'] = '';
      val['helpMem'] = '';

    }

    setConfirmLoading(true);
    let url = !dataInfo?.id ? addUnitCountryside : updateUnitCountryside;
    const { code = 500 } = await url({
      data: {
        ...val,
        id: !dataInfo.id ? undefined : dataInfo.id,
        code: !dataInfo.code ? undefined : dataInfo.code,
        type: pageType,
      },
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  const validatorIdcard = async (rule, value, callback) => {
    if (!value) {
      throw new Error('身份证必填');
      callback('身份证必填');
    }
    if (value && value.length !== 18 && process.env.idCheck != 'false') {
      throw new Error('身份证应该为18位');
      callback('身份证应该为18位');
    }
    if (getIdCardInfo(value) === 'Error') {
      throw new Error('身份证格式错误,请核对身份证图片');
      callback('身份证格式错误');
    } else {
      // let fieldValue = form.getFieldValue('memName');
      // let res =await geitCard({idCard:value,name:fieldValue});
      callback()
    }
  };
  const getIDinfo = (e) => {
    const { target: { value = '' } = {} } = e || {};
    let info = getIdCardInfo(value);
    if (!_isEmpty(value) && info !== 'Error') {
      form.setFieldsValue({
        sexCode: info[2] === '女' ? '0' : '1',
        birthday: info[1] ? moment(info[1], 'YYYY-MM-DD') : undefined,
      });
    }
  };
  let title = pageType == '1' ? '社区工作者' : '后备干部';

  return (
    <Fragment>
      <Modal
        title={dataInfo.id ? `编辑${title}` : `新增${title}`}
        visible={visible}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
      >
        {
          visible && <Fragment>
            <Form form={form} {...formItemLayout} onFinish={onFinish}>
              <Row>
                <Col span={12}>
                  <Form.Item
                    name={'memTypeCode'}
                    // valuePropName="checked"
                    // initialValue={false}
                    rules={[{ required: true, message: '是否党员' }]}
                    label={'是否党员'}>
                    {/* <Switch checkedChildren="是"
                            unCheckedChildren="否"
                      // checked={_isEmpty(dataInfo.memTypeCode) === '1'}
                    /> */}
                    <Select placeholder={'请选择'}>
                      <Select.Option value={1}>是</Select.Option>
                      <Select.Option value={0}>否</Select.Option>
                    </Select>
                  </Form.Item>

                </Col>
                <Col span={12}>
                  <Form.Item noStyle
                    name={'unitName'}
                    style={{ display: 'none' }}>
                    <Input style={{ display: 'none' }} />
                  </Form.Item>
                  <Form.Item
                    name={'unitCode'}
                    rules={[{ required: true, message: '请选择单位' }]}
                    label={'选择单位'}>
                    <UnitSelect
                      disabled={true}
                      org={dataInfo['orgCode'] && dataInfo['code'] ? { orgCode: dataInfo['orgCode'], code: dataInfo['code'] } : getSession('org')}
                      // initValue={dataInfo['unitName'] ? dataInfo['unitName'] : record['unit'] ? record['unit']['name'] : undefined}
                      initValue={dataInfo['unitName'] || basicInfo?.name}
                      unitType={['92', '921', '922', '923']}
                      isCreateOrg={undefined}
                      onChange={(val) => {
                        const [unit] = val;
                        form.setFieldsValue({
                          unitCode: unit.code,
                          unitName: unit.name,
                        });
                      }} />
                  </Form.Item>
                </Col>
              </Row>
              <Row>

                <Col span={12}>
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) => prevValues.memTypeCode !== currentValues.memTypeCode}
                  >
                    {({ getFieldValue }) => {
                      return getFieldValue('memTypeCode') ? (
                        <Form.Item name='memName'
                          label="人员姓名"
                          rules={[{ required: true, message: '请输入人员姓名' }]}
                        >
                          <MemSelect initValue={_isEmpty(dataInfo.memName) ? undefined : dataInfo.memName}
                            onChange={([e]) => {
                              if (e) {
                                form.setFieldsValue({
                                  ...e,
                                  memName: e['name'],
                                  memIdcard: e['idcard'] || undefined,
                                });
                                setDataInfo(ee => {
                                  return {
                                    ...ee,
                                    d07Code: e['d07Code'],
                                    d07Name: e['d07Name'],
                                  };
                                });
                              }
                            }} />
                        </Form.Item>
                      ) : (
                        <Form.Item
                          name={'memName'}
                          rules={[{ required: true, message: '请输入人员姓名' }, { validator: (...e) => validateLength(e, 16, 50) }]}
                          label={'人员姓名'}>
                          <Input style={{ width: '100%' }} />
                        </Form.Item>
                      );
                    }}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name='sexCode'
                    initialValue={'1'}
                    label="性别"
                    rules={[{ required: true, message: '请输入人员性别' }]}
                  >
                    <RadioGroup>
                      <Radio value={'1'}>男</Radio>
                      <Radio value={'0'}>女</Radio>
                    </RadioGroup>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name='memIdcard'
                    label="身份证"
                    rules={[
                      { required: true, message: '请输入人员身份证' },
                      { validator: validatorIdcard },
                    ]}
                  >
                    <Input placeholder="请输入身份证" onBlur={getIDinfo} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name='birthday'
                    // initialValue={_isEmpty(dataInfo.birthday)? undefined :dataInfo.birthday}
                    label="出生日期"
                    rules={[{ required: true, message: '请输入人员出生日期' }]}
                  >
                    <Date />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    noStyle
                    name='d07Name'
                    style={{ display: 'none' }}
                  >
                    <Input style={{ display: 'none' }} />
                  </Form.Item>
                  <Form.Item name='d07Code'
                    // initialValue={_isEmpty(dataInfo.d07Code)? undefined :dataInfo.d07Code}
                    label="学历"
                    rules={[{ required: true, message: '请选择人员学历' }]}
                  >
                    <DictTreeSelect backType={'object'}
                      codeType={'dict_d07'}
                      onChange={e => {
                        form.setFieldsValue({
                          d07Code: e.key,
                          d07Name: e.name,
                        });
                      }}
                      initValue={dataInfo['d07Code']}
                      placeholder="请选择" parentDisable={true} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name='phone'
                    // initialValue={_isEmpty(dataInfo.phone)? undefined :dataInfo.phone}
                    label="电话"
                    rules={[{ required: true, message: '请输入人员电话' }]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
                {/* 新增和编辑村社区干部的时候，增加信息项：是否村任职选调生（选择框、必填）、到村任职补助经费（万元）（数字[保留两委小数]，必填） */}
                {/* 11. 新增和编辑村（社区后备干部）的时候，增加信息项：是否县乡领导班子成员帮带人（是否选择按钮，必填） */}
                {/* 添加社区工作者时也增加以下3个字段：是否选调生和选调单位层级,到村任职补助经费 */}

                <Col span={12}>
                  <Form.Item name='hasLeadersHelpPeople'
                    label="是否县乡领导班子成员帮带人"
                    rules={[{ required: true, message: '是否县乡领导班子成员帮带人' }]}
                  >
                    <Select style={{ width: '100%' }} onChange={e => { 
                      setHasLeadersHelpPeopleTag(e)
                      if( e == 0){
                        form.setFieldsValue({
                          helpUnit: '',// 清空帮带人单位
                          helpMem: '',// 清空帮带人姓名
                        })
                      }
                     }}>
                      <Select.Option value={1}>是</Select.Option>
                      <Select.Option value={0}>否</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                {hasLeadersHelpPeopleTag === 1 && pageType === '2' &&
                  <Fragment>
                    <Col span={12}>
                      <Form.Item name='helpUnit'
                        label="帮带人单位"
                        rules={[{ required: true, message: '帮带人单位' }]}
                      >
                        <Input maxLength={100} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name='helpMem'
                        label="帮带人姓名"
                        rules={[{ required: true, message: '帮带人姓名' }]}
                      >
                        <Input maxLength={100} />
                      </Form.Item>
                    </Col>
                  </Fragment>
                }
                {/* <Col span={12}>
                  <Form.Item name='hasVillageTransferStudent'
                    label="是否村任职选调生"
                    rules={[{ required: true, message: '是否村任职选调生' }]}
                  >
                    <Select onChange={(e: any) => { setSelectD144Code(e) }} style={{ width: '100%' }}>
                      <Select.Option value={1}>是</Select.Option>
                      <Select.Option value={0}>否</Select.Option>
                    </Select>
                  </Form.Item>
                </Col> */}
                {/* { //6、是否村任职选调生 选择是的时候，要弹出来一个填写框：选调单位层级
                  //6、是否村任职选调生 选择是的时候，要弹出来一个填写框：是否双一流大学生
                  selectD144Code == 1 &&
                  (
                    <React.Fragment>
                      <Col span={12}>
                        <Form.Item
                          name="d144Code"
                          label="选调单位层级"
                          rules={[{ required: true, message: '选调单位层级' }]}
                        >
                          <DictTreeSelect backType={'object'} codeType={'dict_d144'} placeholder="请选择" parentDisable={true} onChange={e => {
                            form.setFieldsValue({
                              d144Code: e.key,
                              d144Name: e.name,
                            });
                          }} initValue={dataInfo['d144Code']} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="isDoubleFirst"
                          label="是否双一流大学生"
                          rules={[{ required: true, message: '是否双一流大学生' }]}
                        >
                          <Select style={{ width: '100%' }}>
                            <Select.Option value={1}>是</Select.Option>
                            <Select.Option value={0}>否</Select.Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </React.Fragment>
                  )
                } */}

                {pageType === '2' &&
                  (<Col span={12}>
                    <Form.Item name='isWorkVillage'
                      // initialValue={_isEmpty(dataInfo.phone)? undefined :dataInfo.phone}
                      label="是否在村工作"
                      rules={[{ required: true, message: '是否在村工作' }]}
                    >
                      <Select style={{ width: '100%' }} onChange={e => {
                        setIsWorkVillageTag(e)
                        form.setFieldsValue({
                          d143Name: '',
                          d143Code: '',
                        });
                   dictTreeSelectref.current.clearAll()
                      }}>
                        <Select.Option value={1}>是</Select.Option>
                        <Select.Option value={0}>否</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>)
                }
                {isWorkVillageTag === 1 && pageType === '2' &&
                  <Col span={12}>
                    <Form.Item name='nowJob'
                      // initialValue={_isEmpty(dataInfo.phone)? undefined :dataInfo.phone}
                      label="目前就业岗位"
                      rules={[{ required: true, message: '目前就业岗位' }]}
                    >
                      <Input maxLength={100} />
                    </Form.Item>
                  </Col>
                }

                <Col span={12}>
                  <Form.Item name='subsidies'
                    // initialValue={_isEmpty(dataInfo.phone)? undefined :dataInfo.phone}
                    label="到村任职补助经费（万元）"
                    rules={[{ required: false, message: '到村任职补助经费（万元）' }]}
                  >
                    <InputNumber style={{ width: "100%" }} step='0.01' min={0} max={30} precision={2} />
                  </Form.Item>
                </Col>


                {
                  pageType === '1' && <Fragment>
                    <Col span={12}>
                      <Form.Item name='hasPartyWork'
                        label="是否专职党务工作者"
                        rules={[{ required: true, message: '是否专职党务工作者' }]}
                      >
                        <Select style={{ width: '100%' }}>
                          <Select.Option value={1}>是</Select.Option>
                          <Select.Option value={0}>否</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name='hasTwoOneMember'
                        label="是否推荐为两代表一委员"
                        rules={[{ required: true, message: '是否推荐为两代表一委员' }]}
                      >
                        <Select style={{ width: '100%' }}>
                          <Select.Option value={1}>是</Select.Option>
                          <Select.Option value={0}>否</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        noStyle
                        name='d116Name'
                        style={{ display: 'none' }}
                      >
                        <Input style={{ display: 'none' }} />
                      </Form.Item>
                      <Form.Item name='d116Code'
                        label="录用来源"
                        rules={[{ required: true, message: '录用来源' }]}
                      >
                        <DictTreeSelect backType={'object'}
                          codeType={'dict_d116'}
                          onChange={e => {
                            form.setFieldsValue({
                              d116Code: e.key,
                              d116Name: e.name,
                            });
                          }}
                          initValue={dataInfo['d116Code']}
                          placeholder="请选择" parentDisable={true} />
                      </Form.Item>
                    </Col>
                  </Fragment>
                }
                <Col span={12}>
                  <Form.Item
                    noStyle
                    name='d143Name'
                    style={{ display: 'none' }}
                  >
                    <Input style={{ display: 'none' }} />
                  </Form.Item>
                  <Form.Item
                    name={'d143Code'}
                    rules={[{ required: true, message: '岗位' }]}
                    label={'岗位'}>
                    <DictTreeSelect 
                     ref={dictTreeSelectref}
                    backType={'object'}
                      codeType={'dict_d143'}
                      onChange={e => {
                        form.setFieldsValue({
                          d143Name: e.name,
                          d143Code: e.key,
                        });
                      }}
                      initValue={dataInfo['d143Code']}
                      filter={(data) => {
                        let isWorkVillages = form.getFieldValue('isWorkVillage')
                        let _data = data.map(it => {
                          if (it.key == '3') {
                            it.name = pageType === '1' ? `其他村（社区）工作者` : `其他村（社区）干部`
                          }
                          return it
                        })
                        if (isWorkVillages == 1) {
                          return data.filter(i => i.key != '0')
                        }
                        if (isWorkVillages == 0) {
                          return data.filter(i => i.key == '0')
                        }
                        return data
                      }}
                      placeholder="请选择" parentDisable={true} />
                  </Form.Item>
                </Col>

                <Col span={24}>
                  <Form.Item name='remark'
                    {...formItemLayout2}
                    // initialValue={_isEmpty(dataInfo.phone)? undefined :dataInfo.phone}
                    label="备注"
                    rules={[{ required: false, message: '请输入备注' }]}
                  >
                    <TextArea placeholder={'备注'} rows={3} maxLength={300} />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Fragment>
        }
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
