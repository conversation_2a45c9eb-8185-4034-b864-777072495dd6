import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Form, Input, InputNumber, Modal, Table, Button, Popconfirm } from 'antd';
import { incomeAdd } from '@/pages/[unit]/services';
import Tip from '@/components/Tip';
import DateTime from '@/components/Date';
import { PlusOutlined } from '@ant-design/icons';
import DictTreeSelect from '@/components/DictTreeSelect';
import moment from 'moment';
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 19 },
};

const { TextArea } = Input;
const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [data1, setData1]: any = useState([]);
  const [data2, setData2]: any = useState([]);
  const [data3, setData3]: any = useState([]);
  const [data4, setData4]: any = useState([]);
  const { width = 1000 } = props;

  useImperativeHandle(ref, () => ({
    open: (query) => {
      if (query) {
        setDataInfo(query);
        setData1(query['income'] || []);
        setData2(query['outlay'] || []);
        setData3(query['property'] || []);
        setData4(query['ownership'] || []);
        form.setFieldsValue(query);
      }
      open();
      // setQurey({ ...query, _key: +new Date() });
    },
  }));
  const open = () => {
    setVisible(true);
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    setConfirmLoading(false);
    form.resetFields();
    setData1([]);
    setData2([]);
    setData3([]);
    setData4([]);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    const { unitIn: { basicInfo = {} } = {}, onOK } = props;
    const { dataInfo: datas } = props;
    if (data1['lastItem']?.amount > 200) {
      Tip.error('操作提示', '收入金额（万元）不能大于200')
      return;
    }
    if (data2['lastItem']?.amount > 200) {
      Tip.error('操作提示', '支出金额（万元）不能大于200')
      return;
    }
    // console.log("🚀 ~ onFinish ~ e11111111:", data1, data2, data3, data4)
    setConfirmLoading(true);
    e['createTime'] = moment(e['createTime']).valueOf();
    const { code = 500 } = await incomeAdd({
      data: {
        ...e,
        income: data1,
        outlay: data2,
        property: data3,
        ownership: data4,
        economicCode: datas?.code,
        code: dataInfo?.code,
      },
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  const dele = (record, type) => {
    let data: any;
    switch (type) {
      case '1':
        data = data1.filter((obj) => obj['id'] != record['id']);
        setData1(data);
        break;
      case '2':
        data = data2.filter((obj) => obj['id'] != record['id']);
        setData2(data);
        break;
      case '3':
        data = data3.filter((obj) => obj['id'] != record['id']);
        setData3(data);
        break;
      case '4':
        data = data4.filter((obj) => obj['id'] != record['id']);
        setData4(data);
        break;
    }
  };
  const getColumns = (type) => {
    let til = '收入类别',
      til2 = '收入金额（万元）',
      codeType = 'dict_d130';
    switch (type) {
      case '2':
        til = '支出类别';
        til2 = '支出金额（万元）';
        codeType = 'dict_d1301';
        break;
      case '3':
        til = '资产类别';
        til2 = '资产金额（万元）';
        codeType = 'dict_d1302';
        break;
      case '4':
        til = '权益类别';
        til2 = '资产金额（万元）';
        codeType = 'dict_d1303';
        break;
    }
    return [
      {
        title: til,
        dataIndex: 'type',
        render: (text, record) => (
          <DictTreeSelect
            parentDisable={true}
            key={record['id']}
            initValue={record['type']}
            codeType={codeType}
            onChange={(val) => (record['type'] = val)}
          />
        ),
      },
      {
        title: til2,
        dataIndex: 'amount',
        width: 160,
        render: (text, record) => (
          <InputNumber
            key={record['id']}
            defaultValue={record['amount']}
            min={0}
            style={{ width: '100%' }}
            onChange={(val) => (record['amount'] = val)}
          />
        ),
      },
      {
        title: '备注',
        dataIndex: 'remark',
        width: 200,
        render: (text, record) => (
          <Input
            key={record['id']}
            defaultValue={record['remark']}
            onChange={(e) => (record['remark'] = e.target.value)}
          />
        ),
      },
      {
        title: '操作',
        width: 60,
        render: (text, record) => (
          <Popconfirm
            key={record['id']}
            title={'是否确认删除'}
            onConfirm={() => dele(record, type)}
          >
            <a className="del">删除</a>
          </Popconfirm>
        ),
      },
    ];
  };
  return (
    <Fragment>
      <Modal
        title={dataInfo.id ? '编辑' : '新增'}
        visible={visible}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        confirmLoading={confirmLoading}
        bodyStyle={{ maxHeight: '72vh', overflow: 'auto' }}
      >
        {visible && (
          <Fragment>
            <Form form={form} {...formItemLayout} onFinish={onFinish}>
              <Form.Item
                name="createTime"
                label="记录时间"
                rules={[{ required: true, message: '请选择记录时间' }]}
              >
                <DateTime />
              </Form.Item>
              {/* <Form.Item
               name='incomeAmount'
               label="收益金额（万元）"
               rules={[{ required: true, message: '请输入收益金额（万元）' }]}
              >
                <InputNumber placeholder={'收益金额（万元）'} min={0} style={{width:'100%'}}/>
              </Form.Item> */}
              {/* <Form.Item
               name='incurDebtsAmount'
               label="负债金额（万元）"
               rules={[{ required: true, message: '请输入负债金额（万元）' }]}
              >
                <InputNumber placeholder={'负债金额（万元）'} min={0} style={{width:'100%'}}/>
              </Form.Item> */}
              <Form.Item
                name="quarterlyIncome"
                label="收入情况"
                rules={[{ required: false, message: '请输入负债金额（万元）' }]}
              >
                <Table
                  bordered={true}
                  columns={getColumns('1')}
                  dataSource={data1}
                  pagination={false}
                  footer={() => {
                    return (
                      <div style={{ textAlign: 'center' }}>
                        <Button
                          type="primary"
                          onClick={() => setData1([...data1, { id: new Date().valueOf() }])}
                          style={{ width: '30%' }}
                          size={'small'}
                        >
                          <PlusOutlined />
                          点击添加
                        </Button>
                      </div>
                    );
                  }}
                  size={'middle'}
                />
              </Form.Item>
              <Form.Item
                name="quarterlyIncome"
                label="支出情况"
                rules={[{ required: false, message: '请输入负债金额（万元）' }]}
              >
                <Table
                  bordered={true}
                  columns={getColumns('2')}
                  dataSource={data2}
                  pagination={false}
                  footer={() => {
                    return (
                      <div style={{ textAlign: 'center' }}>
                        <Button
                          type="primary"
                          onClick={() => setData2([...data2, { id: new Date().valueOf() }])}
                          style={{ width: '30%' }}
                          size={'small'}
                        >
                          <PlusOutlined />
                          点击添加
                        </Button>
                      </div>
                    );
                  }}
                  size={'middle'}
                />
              </Form.Item>
              {/* <Form.Item
               name='quarterlyIncome'
               label="资产情况"
               rules={[{ required: false, message: '请输入负债金额（万元）' }]}
              >
                <Table
                  bordered={true}
                  columns={getColumns('3')}
                  dataSource={data3}
                  pagination={false}
                  footer={()=>{
                    return (
                      <div style={{textAlign:'center'}}>
                        <Button type="primary" onClick={()=>setData3([...data3,{id:new Date().valueOf()}])} style={{ width: '30%' }} size={'small'}>
                          <PlusOutlined />点击添加
                        </Button>
                      </div>
                    );
                  }}
                  size={'middle'}
                />
              </Form.Item> */}
              {/* <Form.Item
               name='quarterlyIncome'
               label="所有者权益情况"
               rules={[{ required: false, message: '请输入负债金额（万元）' }]}
              >
                <Table
                  bordered={true}
                  columns={getColumns('4')}
                  dataSource={data4}
                  pagination={false}
                  footer={()=>{
                    return (
                      <div style={{textAlign:'center'}}>
                        <Button type="primary" onClick={()=>setData4([...data4,{id:new Date().valueOf()}])} style={{ width: '30%' }} size={'small'}>
                          <PlusOutlined />点击添加
                        </Button>
                      </div>
                    );
                  }}
                  size={'middle'}
                />
              </Form.Item> */}
            </Form>
          </Fragment>
        )}
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
