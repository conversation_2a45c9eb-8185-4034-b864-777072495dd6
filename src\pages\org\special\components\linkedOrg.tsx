/**
 * 模块名
 */
import React from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Switch, Table } from 'antd';
import OrgSpecialSelect from '@/components/OrgSpecialSelect';


export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      initial: 1,
      data: [
        {
          id: '1',
          isOrgMain: 0,
        },
      ],
    };
  }

  static getDerivedStateFromProps = (props: any, state: any) => {
    const { data } = props;
    const { initial } = state;
    if (data && initial) {
      return { data };
    }
    return null;
  };

  propsChange = (data) => {
    this.setState({
      data,
      initial: 0,
    });
    let resData = [...data];
    resData = resData.filter(obj => obj['org'] || obj['code']);
    const { onChange } = this.props;
    onChange && onChange(resData);
  };

  add = () => {
    let { data } = this.state;
    let obj = { id: `${data.length + 1}`, org: '', isOrgMain: 0 };
    data.push(obj);
    this.setState({
      data,
    });
  };

  del = (obj) => {
    let { data } = this.state;
    data = data.filter(ob => ob['id'] !== obj['id']);
    this.propsChange(data);
  };

  onChange = (val, obj) => {
    let { data } = this.state;
    const index = data.findIndex(ob => ob['id'] === obj['id']);
    if (index > -1) {
      data[index] = { ...data[index], org: val[0] };
    }
    console.log(data,'data');
    this.propsChange(data);
  };

  switchChange = (e, obj) => {
    let { data } = this.state;
    const index = data.findIndex(ob => ob['id'] === obj['id']);
    if (index > -1) {
      data[index] = { ...data[index], isOrgMain: e ? 1 : 0 };
    }
    this.propsChange(data);
  };

  render() {
    let { data } = this.state;
    const {
      orgSelectDisabled = false,
      addDisabled = false,
      delDisabled = false,
      renderTableCol,
    } = this.props;
    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 58,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '关联集体经济',
        dataIndex: 'unitName',
        align: 'center',
        width: 258,
        render: (text, record) => {
          return (
            <OrgSpecialSelect key={record['id']}
                              disabled={orgSelectDisabled}
                              initValue={record['orgName'] ? record['orgName'] : record['org'] ? record['org']['industryName'] : undefined}
                              onChange={(val) => this.onChange(val, record)} />
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 58,
        render: (text, record, index) => {
          return (
            <span>
              {
                !delDisabled ?
                  <a className={'del'} onClick={() => this.del(record)}>删除</a> :
                  null
              }
            </span>
          );
        },
      },
    ];
    return (
      <React.Fragment>
        <Table
          bordered={true}
          columns={!renderTableCol ? columns as any : renderTableCol(columns)}
          dataSource={[...data]}
          pagination={false}
          rowKey={record => record['id']}
          footer={() => {
            return (
              <div style={{ textAlign: 'center' }}>
                <Button type="primary" onClick={this.add} disabled={addDisabled} style={{ width: '30%' }}
                        size={'small'}>
                  <PlusOutlined />点击添加
                </Button>
              </div>
            );
          }}
          size={'middle'}
        />
      </React.Fragment>
    );
  }
}
