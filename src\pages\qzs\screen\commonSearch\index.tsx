import React, { useRef, useState } from 'react';
import st from './index.less';
import { Form, Input, Spin, Tabs, Tooltip } from 'antd';
import { _history as router } from '@/utils/method';
import { InfoCircleOutlined } from '@ant-design/icons';
import { screenlist } from '../services';
import _isEmpty from 'lodash/isEmpty';
import { pullFile } from '@/services';
import Tip from '@/components/Tip';
import { compressAccurately } from 'image-conversion'; // 压缩图片插件

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 0 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
};
const index = (props: any) => {
  const input1Ref: any = useRef();
  const input2Ref: any = useRef();
  const input3Ref: any = useRef();

  const [spinning, setSpinning] = useState<any>(false);
  const [tabKey, setTabKey] = useState<any>('1');

  const {
    // 人员搜索表单标题图片
    titImg = require('../../../../assets/qzs/bwcx.png'),
    // 跳转路由
    goToPath = '/qzs/screen/bwcx/memSearch',
    // 储存key 当只有一个党员时跳过人员点击页面
    infoKey = 'bwcx_info',
    // 跳转路由 当只有一个党员时跳过人员点击页面
    goToPath2 = '/qzs/screen/bwcx/memShow',
  } = props;

  const [form] = Form.useForm();

  const getList = async (p = {}) => {
    setSpinning(true);
    const {
      code: mainResCode = 500,
      data: { list: mainsList = [], ...others } = {},
    } = await screenlist({
      data: {
        pageNum: 1,
        pageSize: 10,
        // idCard: idcard,
        // name: name,
        ...p,
      },
    });
    setSpinning(false);
    if (mainResCode == 0) {
      if (mainsList.length == 0) {
        Tip.error('操作提示', '没有找到党员信息');
        return;
      }
      if (mainsList.length == 1) {
        const item = mainsList[0];
        if (_isEmpty(item.photo)) {
          // item['photo_base64'] = require('../../../../assets/head.jpg');
        } else {
          try {
            // setSpinning(true);
            // const base64 = await pullFile({ path: item.photo });
            // setSpinning(false);
            // const nb = new Promise(async (resolve, reject) => {
            //   compressAccurately(base64, 1024 * 1).then((res) => {
            //     resolve(res);
            //   });
            // });
            // item['photo_base64'] = nb;
          } catch (error) {
            console.log('🚀 ~ getList ~ error:', error);
          }
        }
        sessionStorage.setItem(infoKey, JSON.stringify(item));
        router.push(goToPath2);
      } else {
        router.push(
          `${goToPath}?name=${p.name || ''}&idcard=${p.idCard || ''}&zzm=${p.idcard1 || ''}-${p.joinOrgDate || ''
          }-${p.lastIdcard || ''}`,
        );
      }
    }
  };

  const hadndleFinish = async (v) => {
    getList({ ...v });
  };

  const inp1_onkeyup = async (e) => {
    let text = e.target.value;
    if (text.length === 3) {
      input2Ref?.current?.focus?.();
    }
  };
  const inp2_onkeyup = async (e) => {
    let text = e.target.value;
    if (text.length === 8) {
      input3Ref?.current?.focus?.();
    }
    if (text.length === 0) {
      input1Ref?.current?.focus?.();
    }
  };
  const inp3_onkeyup = async (e) => {
    let text = e.target.value;
    if (text.length === 0) {
      input2Ref?.current?.focus?.();
    }
  };

  return (
    <div className={st.searchPage} style={props?.style || {}}>
      <Spin spinning={spinning}>
        <div className={st.center}>
          <img className={st.tit} style={{ height: infoKey == 'bwcx_info' ? 180 : 65, marginLeft: infoKey == 'bwcx_info' ? 50 : 0 }} src={titImg} alt="" />
          <Tabs
            activeKey={tabKey}
            onChange={(v) => {
              setTabKey(v);
              form.resetFields();
            }}
          >
            <Tabs.TabPane tab={`通过姓名身份证`} key={'1'}></Tabs.TabPane>
            <Tabs.TabPane tab={`通过政治生日码`} key={'2'}></Tabs.TabPane>
          </Tabs>
          {tabKey == '1' && (
            <Form
              key={tabKey}
              className={st.form}
              form={form}
              onFinish={hadndleFinish}
              {...formItemLayout}
            >
              <Input readOnly={true} autoComplete="off" style={{ display: "none" }} />
              <Form.Item name={'name'} rules={[{ required: true, message: '必填' }]}>
                <Input placeholder="请输入姓名" type={'text'} autoComplete={'off'}></Input>
              </Form.Item>
              <Form.Item name={'idCard'} rules={[{ required: true, message: '必填' }]}>
                <Input placeholder="请输入身份证号" type={'text'} autoComplete={'off'}></Input>
              </Form.Item>
            </Form>
          )}
          {tabKey == '2' && (
            <Form
              key={tabKey}
              className={st.form}
              form={form}
              onFinish={hadndleFinish}
              {...formItemLayout}
            >
              <Input readOnly={true} autoComplete="off" style={{ display: "none" }} />
              <div className={st.zzsrm}>
                <div className={st.zzsrmItem}>
                  <Form.Item
                    name={'idcard1'}
                    initialValue={'GQZ'}
                    rules={[{ required: true, message: '必填' }]}
                  >
                    <Input
                      placeholder=""
                      ref={input1Ref}
                      maxLength={3}
                      onKeyUp={inp1_onkeyup}
                      autoComplete="new-user"
                    ></Input>
                  </Form.Item>
                </div>
                <div className={st.zzsrmItemLine}>-</div>
                <div className={st.zzsrmItem}>
                  <Form.Item name={'joinOrgDate'} rules={[{ required: false, message: '必填' }]}>
                    <Input
                      placeholder=""
                      ref={input2Ref}
                      maxLength={8}
                      onKeyUp={inp2_onkeyup}
                    ></Input>
                  </Form.Item>
                </div>
                <div className={st.zzsrmItemLine}>-</div>
                <div className={st.zzsrmItem}>
                  <Form.Item name={'lastIdcard'} rules={[{ required: false, message: '必填' }]}>
                    <Input
                      placeholder=""
                      ref={input3Ref}
                      maxLength={4}
                      onKeyUp={inp3_onkeyup}
                    ></Input>
                  </Form.Item>
                </div>
              </div>
              <div className={st.tooltip}>
                <Tooltip title="政治生日码格式为：G(贵州)Q(清镇)Z(政治)+政治生日年月日+身份证后4位">
                  <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                </Tooltip>
              </div>
            </Form>
          )}

          <div
            className={st.btn}
            onClick={() => {
              form.submit();
            }}
          >
            搜 &nbsp; 索
          </div>
        </div>
      </Spin>
    </div>
  );
};

export default index;
