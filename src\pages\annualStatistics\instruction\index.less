.box {
  width: 100%;
  height: 100%;
  // margin: -16px 0;
  display: flex;
  flex-direction: row;
  .tree {
    width: 220px;
    min-width: 220px;
    //height: '100vh';
    height: calc(100vh - 70px );
    overflow: auto;
    border-right: 1px solid #ccc;
  }
  .content {
    // padding:0 6px;
    margin-right: -16px;
    flex: 1;
    overflow: auto;
    height: calc(100vh - 70px );
    .title {
      // background-color: #EEEEEE;
      height: 40px;
      line-height: 40px;
      display: flex;
      justify-content: space-between;
      align-items:center;
    }
    .right{
      height: calc(100% - 40px);
      overflow: scroll;
    }
      :global(.ant-collapse-header) {
        background: #fff;
        padding: 9px 16px !important;
      }
    .lists {
      height: 40px;
      line-height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      border: 1px solid #EBEEF5;
      span:nth-child(1) {
        padding-left: 16px;
      }
      span:nth-child(2) {
        width: 200px;
        text-align: right;
        padding-right: 8px;
      }
    }
    .caption{
      background: #fff;
      padding: 9px 16px;
      color:#008AF0;
      font-weight: bold;
    }
  }

    }
.static{
  table{
    width: 100% !important;
  }
  th:hover{
    &>td{
      background-color: #bcd6fd;
    }
  }
  tbody>tr:hover{
    &>td{
      background-color: #bcd6fd;
    }
  }
}
.sortItem {
  // width: 140px;
  height: 50px;
  // display: inline-block;
  color: rgba(0, 0, 0, 0.65);
  border-bottom: 1px solid #e8e8e8;
  margin: 20px;
  border-radius: 5px;
  // text-align: center;
  // line-height: 140px;
  z-index: 9999;
  // >span {
  //   display: inline-block;
  //   vertical-align: middle;
  //   line-height: 30px;
  // }
}
:global(.clickRowStyl){
  background-color:#A6D4F2;
  .ant-table-tbody>.clickRowStyl:hover>td {
    background-color:#A6D4F2 !important;
  }
  .ant-table-tbody>.clickRowStyl>td {
    color: #fff !important;
  }
}
.tree1{
  display: flex;
  flex-direction: row;
  height: 500px;
  overflow: hidden;
  >div{
    width: 50%;
    overflow: scroll;
    padding-left: 20px;
  }
}
