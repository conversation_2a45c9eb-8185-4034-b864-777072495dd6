import request from "@/utils/request";
import qs from 'qs';

export function addUnitCommunity(params) {
  return request(`/api/unit/community/addUnitCommunity`,{
    method:'POST',
    body:params,
  });
}

export function communityfindByCode(params) {
  return request(`/api/unit/community/findByCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function findCountYear(params){
  return request(`/api/unit/community/findCountYear?${qs.stringify(params)}`,{
    method:'GET',
  });
}