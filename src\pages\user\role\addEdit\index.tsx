import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { DatePicker, Input, Modal, TreeSelect } from 'antd';
import { FormComponentProps } from '@ant-design/compatible/lib/form';
import moment from 'moment';
import { isEmpty } from '@/utils/method';
import Date from '@/components/Date';

const FormItem=Form.Item;
const TreeNode = TreeSelect.TreeNode;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

interface propsType extends FormComponentProps{
  title?:string,
  handleOk?:(any)=>void,
  initData?:object,
  dispatch?:any,
  role?:any,
  lvArr:Array<{
    key:string,
    name:string
  }>,
  onClickItem?:()=>void
}
class index extends React.Component<propsType,any>{
  // static close=()=>{};
  constructor(props){
    super(props);
    this.state={
      visible:false,
      lvValue:undefined
    }
    // this.close=this.handleCancel.bind(this);
  }
  showModal=()=>{
    const {onClickItem} = this.props;
    if(onClickItem){
      onClickItem();
    }
    this.setState({
      visible:true,
    });
  };
  handleOk=()=>{
    const {handleOk}=this.props;
    const {authorization,permission} = sessionStorage;
    this.props.form.validateFieldsAndScroll((err,val)=>{
      if(!err){
        if(val['valid_time']){
          val['valid_time']=val['valid_time'].valueOf();
        }
        let finalVal = {
          ...val,
          permission
        };
        if(handleOk){
          handleOk(finalVal);
        }
      }
    });
  };
  renderTreeNodes = data => data.map((item) => {
    if (item.children) {
      return (
        <TreeNode title={item['name']} key={item['code']} value={item['id']} dataRef={item}>
          {this.renderTreeNodes(item.children)}
        </TreeNode>
      );
    }
    return <TreeNode title={item['name']} key={item['code']} value={item['id']} dataRef={item} />;
  });
  handleCancel=()=>{
    this.setState({
      visible:false,
      lvValue:undefined
    })
  };
  lvOnchange =(val)=> {
    this.setState({lvValue:val})
  };

  superior=(a,b,c)=>{
    if (isEmpty(a)) {
      if (isEmpty(b)) {
        if (isEmpty(c)) {
          return ''
        }else {
          return c[0]['id']
        }
      }else {
        return b
      }
    }else {
      return a
    }
  };

  render(): React.ReactNode {
    const {title,initData={},children,lvArr=[]}=this.props;
    const { getFieldDecorator } = this.props.form;
    const {account} = sessionStorage;
    const {lvValue} = this.state;
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          maskClosable={false}
          destroyOnClose={true}
          title={ title || "请输入标题" }
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
        >
          {
            this.state.visible &&
            <Form {...formItemLayout}>
              <FormItem
                label={'角色名称'}
              >
                {getFieldDecorator('name', {
                  rules: [{ required: true, message: '请输入角色名称!' }],
                  initialValue:initData['name'] || undefined,
                })(
                  <Input placeholder="请输入角色名称" />
                )}
              </FormItem>
              {/*<FormItem*/}
                {/*label={'角色说明'}*/}
              {/*>*/}
                {/*{getFieldDecorator('desc', {*/}
                  {/*rules: [{ required: true, message: '请输入!' }],*/}
                  {/*initialValue:initData['userName'] || undefined,*/}
                {/*})(*/}
                  {/*<TextArea placeholder="请输入角色说明" />*/}
                {/*)}*/}
              {/*</FormItem>*/}
              <FormItem
                label={'上级角色'}
              >
                {getFieldDecorator('parent_id', {
                  rules: [{ required: true, message: '请输入!' }],
                  initialValue:this.superior(lvValue,initData['parent_id'],lvArr),
                })(
                  <TreeSelect
                    // value={lvValue}
                    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    placeholder="请选择父级"
                    // defaultValue={'8d089ede5a044ce4ae8b4102fdc10bcc'}
                    onChange={this.lvOnchange}
                  >
                    {
                      lvArr && this.renderTreeNodes(lvArr)
                    }
                  </TreeSelect>
                )}
              </FormItem>
              <FormItem
                label={'过期时间'}
              >
                {getFieldDecorator('valid_time', {
                  rules: [{ required: false, message: '请输入!' }],
                  initialValue:isEmpty(initData['valid_time']) ? undefined:moment(initData['valid_time'])  ,
                  // <DatePicker placeholder="请输入过期时间" style={{width:'100%'}}/>
                })(
                  <Date isDefaultEnd={false}/>
                )}
              </FormItem>
              <FormItem
                label={'创建人'}
              >
                {getFieldDecorator('create', {
                  rules: [{ required: false, message: '请输入!' }],
                  initialValue:initData['create'] || account,
                })(
                  <Input placeholder="请输入创建人" disabled={true}/>
                )}
              </FormItem>
            </Form>
          }
        </Modal>
      </React.Fragment>
    );
  }
}
export default Form.create()(index);
