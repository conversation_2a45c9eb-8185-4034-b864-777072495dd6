---
description: 
globs: 
alwaysApply: false
---
# Git 工作流

## 提交规范

提交信息格式应遵循以下规范：
- feat: 新功能
- fix: 修复 bug
- docs: 文档修改
- style: 代码格式修改
- refactor: 重构代码
- test: 测试相关
- chore: 构建过程或辅助工具变动

## 分支管理

- master/main: 主分支，保持稳定可发布状态
- develop: 开发分支
- feature/xxx: 功能分支
- bugfix/xxx: 修复分支

## 开发流程

1. **新功能开发**：
   - 从 develop 分支创建 feature 分支
   - 开发完成后提交 Merge Request 到 develop 分支
   - 通过 Code Review 后合并

2. **Bug 修复**：
   - 从 develop 分支创建 bugfix 分支
   - 修复完成后提交 Merge Request 到 develop 分支

