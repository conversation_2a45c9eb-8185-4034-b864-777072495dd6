// 业务操作-党费缴纳情况
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {Modal, Input} from 'antd';
import moment from 'moment';
import Date from '@/components/Date';
import Tip from '@/components/Tip';
import {delDevelop} from '@/pages/developMem/services/index'
const TextArea=Input.TextArea;

const FormItem=Form.Item;
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      memInfo:{},
      visible:false,
      timeKey:moment().valueOf(),
    }
  }
  handleOk=()=>{
    const {submit,memDevelop:{basicInfo = {}}={}} = this.props;
    const { code } = this.state.memInfo;
    this.props.form.validateFieldsAndScroll( async (err,val)=>{
      if(!err){
        // const res = await delDevelop({code, cancelDevelopReason:val.cancelDevelopReason });
        // if(res.code === 0){
        //   this.handleCancel();
        //   Tip.success('操作提示','操作成功');
        //   submit && submit();
        // }
        console.log('时间==',val);
        
      }
    })
  };
  handleCancel=()=>{
    this.setState({visible:false});
    this.destroy();
  };
  open=(type:string,record)=>{
    this.setState({visible:true,memInfo:record,timeKey:moment().valueOf()})
  };
  destroy=()=>{
   this.setState({
     memInfo:{},
   })
  };
  render() {
    const {form,loading:{effects = {}}={}} = this.props;
    const { getFieldDecorator } = form;
    const {visible} = this.state;
    return (
      <Modal
        destroyOnClose
        title="党费缴纳情况"
        visible={visible}
        onOk={this.handleOk}
        maskClosable={false}
        onCancel={this.handleCancel}
        width={400}
      >
        {
          visible &&
            <Fragment key={this.state.timeKey}>
              <Form>
                <FormItem
                  label="交到流出地至"
                >
                  {getFieldDecorator('cancelDevelopReason', {
                    rules: [{ required: true, message: '请填写时间' }],
                  })(
                    <Date />
                  )}
                </FormItem>
              </Form>
            </Fragment>
        }
      </Modal>
    );
  }
}
export default Form.create()(index);
