.page {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 120px;
}
.person {
  width: 100%;
  height: 1.8em;
  position: relative;
  //box-shadow: 0px 0px 18px 0px rgba(7, 219, 255, 0.52);
}
.clear:after {
  display: block;
  content: '.';
  height: 0;
  visibility: hidden;
  clear: both;
}
.experience {
  width: 100%;
  height: 2px;
  background: #417486;
  margin: 0.4em 0;
  & > div:nth-child(2n) {
    & > label {
      top: -1.88em;
    }
    & > span {
      transform: rotate(45deg);
      top: -180%;
    }
  }
  & > div:before {
    position: absolute;
    top: -1.1em;
    right: 0;
    bottom: 0;
    left: 0;
    display: block;
    width: 0.5em;
    height: 0.5em;
    border-radius: 50%;
    margin: auto;
    background-color: #2be0ea;
    content: '';
    pointer-events: auto;
  }
  & > div {
    width: auto;
    height: 1.3em;
    display: inline-block;
    font-size: 0.12em;
    color: white;
    position: absolute;
    .itemSpan {
      position: relative;
      top: -65%;
      right: 0;
      bottom: 0;
      left: 0;
      display: block;
      width: 0.5em;
      height: 0.5em;
      margin: auto;
      background-color: #15d599;
      content: '';
      pointer-events: auto;
      transform: rotate(45deg);
      overflow: hidden;
    }
    .clickItemSpan {
      .itemSpan;
      background-color: #2f81f8;
    }
    .default {
      z-index: 1;
      cursor: pointer;
      position: relative;
      top: 60%;
      white-space: nowrap;
      min-width: 30em;
      min-height: 16em;
      padding: 0.1em 0.5em;
      color: #fff;
      text-align: left;
      text-decoration: none;
      word-wrap: break-word;
      background-color: #15d599;
      border-radius: 2px;
      &:hover {
       background: #2f81f8;
      }
      &:hover +.itemSpan {
        background: #2f81f8;
      }
    }
    .clickItem {
      .default;
      background: #2f81f8;
    }
  }
}
.personCont {
  margin: 0.1em auto 0;
  width: 98%;
  height: 0.85em;
  background: rgba(7, 219, 255, 0.1);
  border: 1px solid #07dbff;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .desc {
    display: inline-block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow:ellipsis;
  }

  div {
    margin: 0;
    // color: #ccc;
    font-size: 0.2em;
    font-weight: bold;
    div {
      display: inline-block;
      width: 3em;
      height: 3em;
      border-radius: 50%;
      margin-right: 2em;
    }
    span {
      font-size: 0.4em;
      margin: 0 0.16em;
    }
  }
}
