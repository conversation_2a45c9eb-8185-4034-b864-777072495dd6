.main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .panel_body {
    display: inline-block;
    vertical-align: middle;
    border: 1px solid #eee;
    margin: 5px;
    div:nth-child(1){
      img{
        width: 106px;
        height: 110px;
      }
    }
    h4{
      text-align: center;
      margin-top: 5px;
    }
    .link_edit{
      margin-bottom: 6px;
      text-align: center;
     a {
       font-size: 12px;
       display: inline-block;
       margin: 0 2px;
       text-align: center;
     }
      a:link{
        color: #bbb;
      }
      a:hover {color: #666666}
    }
  }
  .add {
    display: inline-block;
    position: relative;
    width: 128px;
    height: 158px;
    vertical-align: top;
    text-align: center;
    border: 1px solid #eee;
    margin: 5px;
  }
  :global(.ant-collapse-content-box){
    position: relative !important;
  }
  .pagination {
    height: 100%;
    display:flex;
    width: 60px;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .pagination2 {
      display:flex;
      .imgLeft, .imgRight{
        height: 100%;
        display: inline-block;
        width: 30px;
        cursor:pointer;
        display:flex;
        align-items: center;
        img {
          width: 100%;
        }
      }
    }
  }

}
