import React, {Fragment} from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, Input, DatePicker, Button, Upload, message, Modal, Radio } from 'antd';
import Same from '../ToPositiveSame';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import { findDictCodeName, unixMoment } from '@/utils/method';
import Tip from '@/components/Tip';
import {fitFileUrlForForm, getInitFileList} from '@/components/UploadComp';
import { formLabel } from '@/utils/method';
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state={
      visible:false,
      d28Value:'11',
      memInfo:{}
    }
  }
  // componentDidMount(): void {
  //   this.props.dispatch({
  //     type:'commonDict/getDict',
  //     payload:{
  //       data:{
  //         dicName:'dict_d28',
  //       }
  //     }
  //   })
  // }
  submit= async ()=>{
    const {onClose,commonDict:{dict_d28 = []} = {}} = this.props;
    const {memInfo} = this.state;
    const {orgCode,orgName,memOrgCode,code:memCode,d08Code,d08Name,name,orgZbCode} = memInfo;
    let flag = false;
    let final = {};
    this['Same'].props.form.validateFieldsAndScroll((errs, vals)=>{
      if(!errs){
        vals = unixMoment(['topartCommitteeDate','topartTurnPartyDate','topartOathDate'],vals);
        final = {
          ...vals,
          topartCommitteeFileUrl:fitFileUrlForForm(vals['topartCommitteeFileUrl']),
          topartFileUrl:fitFileUrlForForm(vals['topartFileUrl']),
          topartOathDateUrl:fitFileUrlForForm(vals['topartOathDateUrl']),
        };
      }else {
        flag = true
      }
    });
    this.props.form.validateFieldsAndScroll((err, val) => {
      if (!err) {
        if(!_isEmpty(dict_d28)){
          dict_d28.forEach(item=>{
            if(item['key'] === val['d28Code']){
              val['d28Name'] = item['name']
            }
          })
        }
        final = {...final,...val};
      }else {
        flag = true
      }
    });
    if(!flag){
      final['logOrgCode'] = memOrgCode;
      final['orgName'] = orgName;
      final['orgCode'] = orgCode;
      final['memCode'] = memCode;
      final['d08Code'] = d08Code;
      final['d08Name'] = d08Name;
      final['name'] = name;
      final['orgZbCode'] = orgZbCode;
      // console.log(final,'final');
      const res = await this.props.dispatch({
        type:'memToPositive/becomeFullMem',
        payload:{
          data:{...final}
        }
      });
      const {code = 500} = res || {};
      if(code === 0){
        onClose && onClose();
        this.close();
        Tip.success('操作提示','操作成功');
      }
    }
  };
  destroy=()=>{
    this.setState({
      memInfo:{}
    });
    // this.props.dispatch({
    //   type:'memLeaveOrg/clear',
    //   payload:{}
    // })
  };
  open=(val)=>{
    const {isExtendPrepare} = val;
    this.setState({
      visible:true,
      memInfo:val,
      d28Value:isExtendPrepare === 1 ? '12' :'11'
    })
  };
  close=()=>{
    this.destroy();
    this.setState({visible:false})
  };
  RadioGroupOnChange=({target:{value = ''}={}} = {})=>{
    this.setState({d28Value:value})
  };
  render() {
    const {form,loading:{effects={}}={},commonDict:{dict_d28 = []} = {}} = this.props;
    const {getFieldDecorator} = form;
    const {visible,d28Value,memInfo={}} = this.state;
    const {isExtendPrepare} = memInfo;
    let msg = '由于党组织根据实际情况适当推迟召开支部大会讨论转正的，转正类型依然为按期转正，转正时间为预备期满时间。 “推迟讨论转正”系指基层党组织对转入的预备党员，在其预备期满时，如认为有必要，可推迟讨论其转正问题，推迟时间不超过六个月，转为正式党员的，其转正时间自预备期满之日算起。'
    return (
      <Fragment>
        <Modal
          title={'预备党员转正'}
          destroyOnClose
          visible={visible}
          onOk={this.submit}
          onCancel={this.close}
          width={'800px'}
          confirmLoading={effects['memToPositive/becomeFullMem']}
        >
          <Same wrappedComponentRef={e => this['Same'] = e} memInfo={memInfo}/>
          <Form key={1}>
            <FormItem
              label={formLabel("转正类型",msg)}
              {...formItemLayout}
            >
              {getFieldDecorator('d28Code', {
                rules: [{ required: true, message: '转正类型' }],
                initialValue:d28Value,
              })(
                <RadioGroup onChange={this.RadioGroupOnChange}>
                  {
                    !_isEmpty(dict_d28) && dict_d28.map(item=>{
                      if(isExtendPrepare === 1){
                        if(item['key'] === '12'){
                          return (
                            <Radio value={item['key']} key={item['key']}>{item['name']}</Radio>
                          )
                        }
                      }else {
                        if(item['key'] === '11' || item['key'] === '13'){
                          return (
                            <Radio value={item['key']} key={item['key']}>{item['name']}</Radio>
                          )
                        }
                      }
                    })
                  }
                </RadioGroup>
              )}
            </FormItem>
          </Form>
        </Modal>

      </Fragment>
    );
  }
}
export default Form.create<any>()(index);
