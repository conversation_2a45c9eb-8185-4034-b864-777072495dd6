/**
 * 领导班子
 **/
import React, {useState} from 'react'
import {connect} from "dva";
import { PlusOutlined } from '@ant-design/icons';
import { Button, Collapse, Modal, Popconfirm, Select, Tooltip, Divider, Input } from 'antd';
import AddJcInfo from './addJcInfo';
import AddMember from './addMember';
import Tip from '@/components/Tip';
import head from '@/assets/head.jpg';
import styles from './index.less';
import moment from 'moment';
import _isNumber from 'lodash/isNumber';
import {
  itteeList,
  itteeUP,
  electQueryList,
  electDel,
  committeeQueryList,
  committeeDel, committeeStop,
  congressCommitteeBackOut,
  revokePeople,
  historyListPeople,
  revoke,
  hoistoryList

} from '../../../../services/org.js';
import tip from '@/components/Tip';
import DateTime from '@/components/Date';
import WhiteSpace from '@/components/WhiteSpace';

import <PERSON>bale, {PanelTable, PanelListTable} from '@/components/CollapseTable';
import ListTable from '@/components/ListTable';

const Panel = Collapse.Panel;
const Search=Input.Search;
// @ts-ignore
@connect(({ org, commonDict }) => ({ org, commonDict }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      type: 'add',
      type1: 'addMem',
      visible: false,
      visibleHistory: false,
      visiblePeople: false,
      activeKey: undefined,
      filterHeight: `calc(100vh - ${400}px)`,
      selectedRowKeys: [],
      selectedItems: [],
    };
  }
  componentDidMount(): void {
    this.props.dispatch({
      type: 'commonDict/getDict',
      payload: {
        data: {
          codeType: 'dict_d105',
          dicName: 'dict_d105',
        },
      },
    });
  }
  del = async (e, item) => {
    e.stopPropagation();
    const obj = await electDel({
      data: {
        id: Number(item['id']),
      },
    });
    if (obj && obj['code'] === 0) {
      Tip.success('操作提示', '删除成功');
      this['CTbale'].getList();
    }
  };
  getHistoryList = async (item, key) => {
    const payload = {
      electCode: item['code'],
      keys: key,
      pageNum: 1,
      pageSize: 100,
    };
    committeeQueryList({ ...payload, leave: 1 }).then((res) => {
      let data: any = [];
      if (res['code'] == '0') {
        data = res['data']['list'];
      }
      this.setState({
        hisData: data,
        visible: true,
      });
    });
  };
  getHistoryListPeople = async (item) => {
    const payload = {
      electCode: item['code'],
      pageNum: 1,
      pageSize: 100,
    };
    historyListPeople({ ...payload, leave: 1 }).then((res) => {
      let data: any = [];
      if (res['code'] == '0') {
        data = res['data'];
      }
      let _map = new Map();
      data.map((it, index) => {
        if (!_map.has(it.deleteTime)) {
          let color = `rgb(${Math.round(Math.random() * 255)},${Math.round(
            Math.random() * 255,
          )},${Math.round(Math.random() * 255)})`;
          _map.set(it.deleteTime, color);
        }
      });
      data = data.map((it) => {
        return {
          ...it,
          color: _map.get(it.deleteTime),
        };
      });

      // // 动态合并行
      // let startItem=data[0]
      // startItem.rowSpan = 1
      // data.map((item, index)=>{
      //   let nextItem = data[index + 1] || {}
      //   if(item['color'] === nextItem['color']){
      //     startItem.rowSpan++
      //   }else{
      //     startItem = nextItem
      //     startItem.rowSpan = 1
      //   }
      // })
      
      this.setState({
        hisDataPeople: data,
        visiblePeople: true,
      });
    });
  };
  // 历史届次
  getHistoryListOut = async () => {
    const payload = {
      orgCode: this.props.org.basicInfo.orgCode,
      pageNum: 1,
      pageSize: 200,
    };
    hoistoryList({ ...payload }).then((res) => {
      let data: any = [];
      if (res['code'] == '0') {
        data = res['data'];
      }
      this.setState({
        historyData: data,
        visibleHistory: true,
      });
    });
  };
  handleClick = () => {
    if (this.state.selectedRowKeys.length > 0) {
      const { selectedRowKeys } = this.state;
      //  其中含有XX条恢复至历史任职人员:d105Code不为空的数据
      let d105CodeArr = this.state.selectedItems.filter((item) => {
        return _isNumber(item.d105Code);
      });
      Modal.confirm({
        title: '提示',
        content: `此操作将撤销${selectedRowKeys.length || 0}个历史党代表，其中有${
          d105CodeArr.length || 0
        }条恢复至历史任职人员！`,
        onOk: this.revokeMuch,
        okText: '确定',
        onCancel() {},
      });
    }
  };
  revokeMuch = async () => {
    const { code = 500 } = await revokePeople({ data: { electCodes: this.state.selectedRowKeys } });
    if (code === 0) {
      this.state.selectedRowKeys.map((j) => {
        this.setState({
          hisDataPeople: this.state.hisDataPeople.filter((it) => it.code != j),
        });
      });
      this.setState({
        selectedRowKeys: [],
        selectedItems: [],
      });
      this['PanelTable'].getList();
    }
  };
  header = (item, key) => {
    return (
      <span className={styles.header}>
        <span>
          换届日期：
          {item['tenureStartDate'] && moment(item['tenureStartDate']).format('YYYY-MM-DD')}~
          {item['tenureEndDate'] && moment(item['tenureEndDate']).format('YYYY-MM-DD')}
        </span>
        {/*<span>选举方式：{item['electName']}</span>*/}
        <div>
          <a
            onClick={(e) => {
              e.stopPropagation();
              this['CTbale'].setActiveKeys(key);
              this.getHistoryList(item, key);
            }}
          >
            届内历史任职
          </a>
          <a
            onClick={(e) => {
              e.stopPropagation();
              this['CTbale'].setActiveKeys(key);
              this.getHistoryListPeople(item);
            }}
          >
            届内历史人员
          </a>
          <a onClick={(e) => this.editJc(e, item)}>编辑</a>
          <div style={{ display: 'inline-block' }} onClick={(e: any) => e.stopPropagation()}>
            <Popconfirm
              title="是否删除该信息?"
              onConfirm={(e) => this.del(e, item)}
              okText="是"
              cancelText="否"
              onCancel={(e: any) => e.stopPropagation()}
            >
              <a onClick={(e) => e.stopPropagation()} className={'del'}>
                删除
              </a>
            </Popconfirm>
          </div>
        </div>
      </span>
    );
  };
  editJc = (e, item) => {
    e.stopPropagation();
    this.setState({ type: 'edit', dataInfo: item }, () => this['AddJcInfo'].showModal());
  };
  editMem = (item) => {
    this.setState({ type: 'edit', dataInfo: item }, () => {
      this['AddMember'].showModal();
    });
  };
  end = (item) => {
    let endTime, d105Code;
    const { dict_d105 = [] } = this.props.commonDict;
    Modal.confirm({
      icon: <div />,
      title: '',
      content: (
        <div>
          终止原因：
          <Select
            placeholder="请选择"
            onChange={(val) => (d105Code = val)}
            style={{ width: '100%' }}
          >
            {dict_d105.map((item) => (
              <Select.Option value={item['key']}>{item['name']}</Select.Option>
            ))}
          </Select>
          <WhiteSpace />
          结束任期时间：
          <DateTime onChange={(val) => (endTime = val)} />
        </div>
      ),
      onOk: () => {
        return new Promise<void>((resolve, reject) => {
          setTimeout(() => {
            if (endTime && d105Code) {
              committeeStop({
                data: {
                  ...item,
                  d105Code,
                  endDate: endTime.valueOf(),
                },
              }).then((res) => {
                if (res['code'] == '0') {
                  this['PanelTable'].getList();
                  resolve();
                }
              });
            } else {
              tip.error('操作提示', !endTime ? '请输入结束任期时间' : '请选择终止原因');
              reject();
            }
          }, 150);
        });
      },
    });
  };
  clos = () => {
    this.setState({
      visible: false,
      hisData: undefined,
    });
  };
  closeHistory = () => {
    this.setState({
      visibleHistory: false,
      historyData: undefined,
    });
  };
  closeHistoryPeople = () => {
    this.setState({
      visiblePeople: false,
      hisDataPeople: undefined,
      selectedRowKeys: [],
      selectedItems: [],
    });
  };
  onSelectChange = (selectedRowKeys, record) => {
    this.setState({
      selectedRowKeys,
      selectedItems: record,
    });
  };
  render() {
    const {
      selectedRowKeys,
      type,
      hisData = [],
      filterHeight,
      historyData = [],
      hisDataPeople = [],
      visible,
      visibleHistory,
      visiblePeople,
      electList = [],
      electListData = [],
      activeKey,
      memName = undefined,
    } = this.state;
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
      type: 'checkbox',
    };
    return (
      <div style={{ padding: '0 20px' }}>
        <Modal
          title={'届内历史任职'}
          visible={visible}
          onOk={this.clos}
          onCancel={this.clos}
          width={1000}
          bodyStyle={{ maxHeight: '60vh', overflow: 'auto' }}
        >
          <ListTable
            pagination={false}
            columns={[
              {
                title: '序号',
                dataIndex: 'num',
                align: 'center',
                width: 50,
                render: (text, record, index) => {
                  return index + 1;
                },
              },
              {
                title: '姓名',
                dataIndex: 'memName',
                width: 50,
                align: 'center',
              },
              {
                title: '终止原因',
                dataIndex: 'd105Name',
                width: 150,
              },
              {
                title: '结束任期时间',
                dataIndex: 'endDate',
                width: 100,
                align: 'center',
                render: (text, record) => {
                  return moment(text).format('YYYY-MM-DD');
                },
              },
              {
                title: '人员身份',
                dataIndex: 'd106Name',
                width: 100,
              },
              {
                title: '党代表任职起始日期',
                dataIndex: 'startDate',
                width: 100,
                align: 'center',
                render: (text, record) => {
                  return moment(text).format('YYYY-MM-DD');
                },
              },
              {
                title: '操作',
                dataIndex: 'action',
                width: 50,
                align: 'center',
                render: (text, record) => {
                  return (
                    <div>
                      <a
                        onClick={async () => {
                          const { code = 500 } = await congressCommitteeBackOut({ data: record });
                          if (code === 0) {
                            this.setState({
                              hisData: this.state.hisData.filter((it) => it.code != record.code),
                            });
                            this['PanelTable'].getList();
                          }
                        }}
                      >
                        撤销
                      </a>
                    </div>
                  );
                },
              },
            ]}
            data={hisData}
          />
          {/* {
            hisData.map((item,ind)=>{
              return (
                <div key={ind} className={styles.panel_body}>
                  <Tooltip title={item['d022Name']}>
                    <div><img src={head} style={{width:128,height:158}}/></div>
                  </Tooltip>
                  <div>
                    <h4>{item.memName}</h4>
                    <h4>
                      <a onClick={ async ()=>{
                        const {code = 500 } = await congressCommitteeBackOut({data:item});
                        if(code === 0){
                          this.setState({
                            hisData:this.state.hisData.filter(it=>it.code != item.code)
                          })
                          this['PanelTable'].getList();
                        }
                      }}>撤销</a>
                      </h4>
                  </div>
                </div>
              )
            })
          } */}
        </Modal>
        <Modal
          footer={null}
          title={'历史届次'}
          visible={visibleHistory}
          onOk={this.closeHistory}
          onCancel={this.closeHistory}
          width={1000}
          bodyStyle={{ maxHeight: '60vh', overflow: 'auto' }}
        >
          <ListTable
            pagination={false}
            columns={[
              {
                title: '序号',
                dataIndex: 'num',
                align: 'center',
                width: 50,
                render: (text, record, index) => {
                  return index + 1;
                },
              },
              {
                title: '届次开始时间',
                dataIndex: 'tenureStartDate',
                width: 100,
                align: 'center',
                render: (text, record) => {
                  return moment(text).format('YYYY-MM-DD');
                },
              },
              {
                title: '届次结束时间',
                dataIndex: 'tenureEndDate',
                width: 100,
                align: 'center',
                render: (text, record) => {
                  return moment(text).format('YYYY-MM-DD');
                },
              },
              {
                title: '删除时间',
                dataIndex: 'deleteTime',
                width: 100,
                align: 'center',
                render: (text, record) => {
                  return moment(text).format('YYYY-MM-DD');
                },
              },
              {
                title: '操作',
                dataIndex: 'action',
                width: 50,
                align: 'center',
                render: (text, record) => {
                  return (
                    <div>
                      <a
                        onClick={async () => {
                          const { code = 500 } = await revoke({
                            data: {
                              id: record['id'],
                            },
                          });
                          if (code === 0) {
                            this.getHistoryListOut();
                            this['CTbale'].getList();
                          }
                        }}
                      >
                        撤销
                      </a>
                    </div>
                  );
                },
              },
            ]}
            data={historyData}
          />
        </Modal>
        <Modal
          footer={null}
          title={'届内历史人员'}
          visible={visiblePeople}
          onOk={this.closeHistoryPeople}
          onCancel={this.closeHistoryPeople}
          width={1000}
          bodyStyle={{ maxHeight: '73vh', overflow: 'auto' }}
        >
          <Button
            onClick={this.handleClick}
            disabled={selectedRowKeys.length == 0}
            type="primary"
            style={{ marginBottom: '10px' }}
          >
            批量撤销
          </Button>
          <ListTable
            rowKey={'code'}
            rowSelection={rowSelection}
            scroll={{ y: filterHeight }}
            pagination={false}
            columns={[
              {
                title: '序号',
                dataIndex: 'num',
                align: 'center',
                width: 50,
                render: (text, record, index) => {
                  return index + 1;
                },
              },
              {
                title: '姓名',
                dataIndex: 'memName',
                width: 50,
                align: 'center',
              },
              {
                title: '人员身份',
                dataIndex: 'd106Name',
                width: 100,
              },
              {
                title: '党代表任职起始日期',
                dataIndex: 'startDate',
                width: 100,
                align: 'center',
                render: (text, record) => {
                  return moment(text).format('YYYY-MM-DD');
                },
              },
              {
                title: '删除时间',
                dataIndex: 'deleteTime',
                width: 100,
                align: 'center',
                render: (text, record) => {
                  return (<span style={{ color: record.color }}>{moment(text).format('YYYY-MM-DD')}</span>);
                },

                //  // 动态合并行：合并删除时间相同的行
                // render: (text, record) => {
                //   return {
                //       children: <span style={{ color: record.color }}>{moment(text).format('YYYY-MM-DD')}</span>,
                //       props: { rowSpan: record.rowSpan || 0 },
                //     }
                // },
              },
              {
                title: '操作',
                dataIndex: 'action',
                width: 50,
                align: 'center',
                render: (text, record) => {
                  return (
                    <div>
                      <a
                        onClick={async () => {
                          const { code = 500 } = await revokePeople({
                            data: {
                              electCodes: [record.code],
                            },
                          });
                          if (code === 0) {
                            this.setState({
                              hisDataPeople: this.state.hisDataPeople.filter(
                                (it) => it.code != record.code,
                              ),
                              selectedRowKeys: this.state.selectedRowKeys.filter(
                                (it) => it != record.code,
                              ),
                              selectedItems: this.state.selectedItems.filter(
                                (it) => it.code != record.code,
                              ),
                            });
                            this['PanelTable'].getList();
                          }
                        }}
                      >
                        撤销
                      </a>
                    </div>
                  );
                },
              },
            ]}
            data={hisDataPeople}
          />
        </Modal>
        <AddJcInfo
          title={type === 'edit' ? '编辑届次信息' : '新增届次信息'}
          wrappedComponentRef={(e) => (this['AddJcInfo'] = e)}
          onClose={() => this.setState({ dataInfo: undefined, type: undefined })}
          queryList={() => {
            this['CTbale'].getList();
          }}
          {...this.props}
          {...this.state}
        >
          <Button type="primary" icon={<PlusOutlined />} style={{ marginBottom: '10px' }}>
            添加届次信息
          </Button>
        </AddJcInfo>
        <Button
          onClick={this.getHistoryListOut}
          type="primary"
          style={{ marginBottom: '10px', marginLeft: '10px' }}
        >
          历史届次
        </Button>
        <CTbale
          tableActionOtherQueries={{ orgCode: this.props.org.basicInfo.orgCode }}
          tableListAction={electQueryList}
          ref={(e) => (this['CTbale'] = e)}
          mains={(item, index) => {
            return (
              <React.Fragment>
                <AddMember
                  title={type === 'edit' ? '编辑党代表' : '新增党代表'}
                  wrappedComponentRef={(e) => (this['AddMember'] = e)}
                  onClose={() => this.setState({ dataInfo: undefined, type: undefined })}
                  queryList={() => {
                    this['PanelTable'].getList();
                  }}
                  elect={item} /*届次信息*/
                  iteListData={electListData} /*当前届次的列表数据*/
                  {...this.props}
                  {...this.state}
                >
                  <Button type="primary">添加人员</Button>
                </AddMember>
                <Search
                  placeholder="请输入姓名搜索"
                  onSearch={async (e) => {
                    console.log('e===', e);
                    await this.setState({
                      memName: e || undefined,
                    });
                    this['PanelTable'].getList();
                  }}
                  style={{ width: '300px', marginLeft: '10px' }}
                />
                <div style={{ marginBottom: 10 }} />
                <PanelListTable
                  data={item}
                  mainsListAction={committeeQueryList}
                  mainsActionOtherQueries={{ electCode: item['code'], leave: 0, memName }}
                  ref={(e) => (this['PanelTable'] = e)}
                  columns={[
                    {
                      title: '姓名',
                      dataIndex: 'memName',
                      width: 100,
                    },
                    {
                      title: '人员身份',
                      dataIndex: 'd106Name',
                      width: 150,
                    },
                    {
                      title: '人员身份证号',
                      dataIndex: 'memIdcard',
                      width: 150,
                    },
                    {
                      title: '党代表任职起始日期',
                      dataIndex: 'startDate',
                      width: 100,
                      render: (text, record) => {
                        return moment(text).format('YYYY-MM-DD');
                      },
                    },
                    {
                      title: '操作',
                      dataIndex: 'action',
                      width: 130,
                      render: (text, record) => {
                        return (
                          <div>
                            <a onClick={() => this.editMem(record)}>编辑</a>
                            <Divider type="vertical" />
                            <a onClick={() => this.end(record)}>终止资格</a>
                            <Divider type="vertical" />
                            <Popconfirm
                              title="删除仅用于错误录入的情况，正常终止请选择终止资格。是否删除?"
                              onConfirm={() => {}}
                              onCancel={async () => {
                                const { code = 500 } = await committeeDel({
                                  data: {
                                    id: record['id'],
                                  },
                                });
                                if (code === 0) {
                                  Tip.success('操作提示', '操作成功');
                                  this['PanelTable'].getList();
                                }
                              }}
                              okText="否"
                              cancelText="是"
                            >
                              <a onClick={(e) => e.stopPropagation()}>删除</a>
                            </Popconfirm>
                          </div>
                        );
                      },
                    },
                  ]}
                />
              </React.Fragment>
            );
            // return (
            //   <PanelTable
            //     ref={e => this['PanelTable'] = e}
            //     data={item}
            //     mainsListAction={committeeQueryList}
            //     mainsActionOtherQueries={{ electCode: item['code'], leave: 0 }}
            //     add={() => {
            //       return (
            //         <AddMember
            //           title={type === 'edit' ? '编辑党代表' : '新增党代表'}
            //           wrappedComponentRef={(e) => this['AddMember'] = e}
            //           onClose={() => this.setState({ dataInfo: undefined, type: undefined })}
            //           queryList={() => {
            //             this['PanelTable'].getList();
            //           }}
            //           elect={item} /*届次信息*/
            //           iteListData={electListData} /*当前届次的列表数据*/
            //           {...this.props}
            //           {...this.state}
            //         >
            //           <div className={styles.add}><PlusOutlined style={{ fontSize: '50px', transform: 'translateY(100%)' }} /></div>
            //         </AddMember>
            //       )
            //     }}
            //     linkEdit={(items) => {
            //       return (
            //         <React.Fragment>
            //           <a onClick={() => this.editMem(items)}>编辑</a>
            //           <a onClick={() => this.end(items)}>转为历史任职</a>
            //           <Popconfirm title="是否删除?" onConfirm={ async () => {
            //             const {code = 500} = await committeeDel({data: {
            //               id: items['id'],
            //             }});
            //             if(code === 0){
            //               Tip.success('操作提示','操作成功');
            //               this['PanelTable'].getList();
            //             }
            //            }} okText="是" cancelText="否">
            //             <a onClick={e => e.stopPropagation()}>删除</a>
            //           </Popconfirm>
            //         </React.Fragment>
            //       )
            //     }} />
            // )
          }}
          panelHeader={(obj, listLndex) => {
            return this.header(obj, listLndex);
          }}
        />
      </div>
    );
  }
}
