/**
 * 新增/编辑  党费标准
 * */

import React, { useState, useRef, useEffect, useImperativeHandle, Fragment } from 'react';
import {
  Input,
  Select,
  Form,
  Modal,
  Tabs,
  Button,
  Divider,
  Popconfirm,
  Space,
  Radio,
  InputNumber,
} from 'antd';
import _isEmpty from 'lodash/isEmpty';
import _isObject from 'lodash/isObject';
import { getSession } from '@/utils/session';
import { findDictCodeName } from '@/utils/method';
import Tip from '@/components/Tip';
import DictSelect from '@/components/DictSelect';
import { settingStandard } from '../../services';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const index = (props: any, ref: any) => {
  const { code = undefined, orgCode = undefined } = getSession('org') || {
    code: undefined,
    orgCode: undefined,
  };
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('新增党费标准');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [basinInfo, setBasinInfo] = useState<any>({});
  const [currentMonth, setCurrentMonth]: any = useState({});
  useImperativeHandle(ref, () => ({
    open: (record: any, month: number, year: string) => {
      const { data = [] } = record;
      let thisMonth: any = {};
      if (!_isEmpty(data)) {
        data.map((item) => {
          if (item?.month === month) {
            thisMonth = item;
          }
        });
      }
      console.log('thisMonth===', thisMonth);

      // if (thisMonth?.standard) {
        setTitle('修改党费标准');
        form.setFieldsValue({
          d49Code: thisMonth?.d49Code,
          standard: thisMonth?.standard,
          isYearly: thisMonth?.isYearly,
          base: thisMonth?.base,
          name: record?.memName,
          reason:thisMonth?.reason,
        });
        if (thisMonth?.base && thisMonth?.d49Code == '2') {
          onBlur({ target: { value: thisMonth?.base } });
        }
      // } else {
      //   setTitle('新增党费标准');
      //   form.setFieldsValue({
      //     isYearly: 0,
      //     d49Code: '1',
      //     name: record?.memName,
      //   });
      // }
      setBasinInfo({ ...record, year, month });
      setCurrentMonth(thisMonth);
      setVisible(true);
    },
  }));
  const hadndleFinish = async (vals: any) => {
    // setConfirmLoading(true);
    // vals = findDictCodeName(['d49Code'], vals, basinInfo);
    // if (!_isEmpty(vals?.d49Code)) {
    //   if (_isObject(vals?.d49Code)) {
    //     vals.d49Name = vals?.d49Code?.name;
    //     vals.d49Code = vals?.d49Code?.key;
    //   } else {
    //     vals.d49Name = currentMonth?.d49Name;
    //     vals.d49Code = currentMonth?.d49Code;
    //   }
    // }
    let val = {
      memOrgOrgCode: orgCode,
      memCode: basinInfo?.memCode,
      code: currentMonth?.code,
      year: currentMonth?.year || basinInfo?.year,
      month: currentMonth?.month || basinInfo?.month,
      d49Name:
        vals.d49Code == '1'
          ? '按标准交纳'
          : vals.d49Code == '2'
          ? '按工资比例交纳'
          : vals.d49Code == '3'
          ? '少交'
          : '免交',
      ...vals,
    };

    const { code: resCode = 500 } = await settingStandard({
      data: {
        ...val,
      },
    });
    setConfirmLoading(false);
    if (resCode == 0) {
      const { onOk } = props;
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOk && onOk();
    }
  };
  const handleCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
    form.resetFields();
    setBasinInfo({});
  };

  const onBlur = (v) => {
    let val = v.target.value;
    let proportion = 0;
    if (val !== '') {
      if (val < 3001) {
        proportion = 0.005;
      } else if (val < 5001) {
        proportion = 0.01;
      } else if (val < 10001) {
        proportion = 0.015;
      } else {
        proportion = 0.02;
      }
      let standards = val * proportion;
      form.setFieldsValue({
        standard: standards.toFixed(2),
        proportion,
      });
    }
  };
  return (
    <Modal
      title={title}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={'700px'}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
        <Form.Item
          label="党员姓名"
          name={'name'}
          rules={[{ required: false, message: '党员姓名' }]}
        >
          <Input disabled></Input>
        </Form.Item>
        <Form.Item
          label="交费类型"
          name={'d49Code'}
          rules={[{ required: true, message: '交费类型' }]}
        >
          <Radio.Group
            onChange={() => {
              form.resetFields(['standard', 'reason', 'base', 'proportion']);
            }}
          >
            <Radio value={'1'}>按标准交纳</Radio>
            <Radio value={'2'}>按工资比例交纳</Radio>
            <Radio value={'3'}>少交</Radio>
            <Radio value={'4'}>免交</Radio>
          </Radio.Group>
          {/* <DictSelect
            showConstant={false}
            backType="object"
            codeType={'dict_d49'}
            onChange={(obj) => {
              form.setFieldsValue({
                d49Code: obj.key,
                d49Name: obj.name,
              });
            }}
            initValue={currentMonth['d49Code'] || ''}
          /> */}
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.d49Code !== currentValues.d49Code}
        >
          {({ getFieldValue }) => {
            const d49 = getFieldValue('d49Code');
            if (d49 == '2') {
              return (
                <Fragment>
                  <Form.Item
                    label="党费基数"
                    name={'base'}
                    rules={[{ required: true, message: '党费基数' }]}
                  >
                    <Input onBlur={(value) => onBlur(value)}></Input>
                  </Form.Item>
                  <Form.Item
                    label="计算比例"
                    name={'proportion'}
                    rules={[{ required: true, message: '党费基数' }]}
                  >
                    <Input placeholder={'系统自动计算比例'} disabled />
                  </Form.Item>
                </Fragment>
              );
            }
            if (d49 == '3') {
              return (
                <Form.Item
                  label="少交原因"
                  name={'reason'}
                  rules={[{ required: true, message: '少交原因' }]}
                >
                  <Input placeholder={'请填写少交原因'} />
                </Form.Item>
              );
            }
            if (d49 == '4') {
              return (
                <Form.Item
                  label="免交原因"
                  name={'reason'}
                  rules={[{ required: true, message: '免交原因' }]}
                >
                  <Input placeholder={'请填写少交原因'} />
                </Form.Item>
              );
            }
            return '';
          }}
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.d49Code !== currentValues.d49Code}
        >
          {({ getFieldValue }) => {
            const d49 = getFieldValue('d49Code');
            if (d49 != '4') {
              return (
                <Form.Item
                  label="党费标准(元)"
                  name={'standard'}
                  rules={[{ required: true, message: '党费标准' }]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    precision={2}
                    minLength={0}
                    maxLength={8}
                    disabled={d49 == '2'}
                  ></InputNumber>
                </Form.Item>
              );
            }
            return '';
          }}
        </Form.Item>

        <Form.Item
          label="应用范围"
          name={'isYearly'}
          rules={[{ required: true, message: '应用范围' }]}
        >
          <Radio.Group>
            <Radio value={'1'}>全年</Radio>
            <Radio value={'2'}>本月及之后</Radio>
            <Radio value={'0'}>本月</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default React.forwardRef(index);
