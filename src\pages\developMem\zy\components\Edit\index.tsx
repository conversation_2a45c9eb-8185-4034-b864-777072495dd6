import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import {
  Row,
  Col,
  Radio,
  Button,
  Modal,
  Menu,
  Space,
  Spin,
  Tooltip,
  message
} from 'antd';
import { Icon as LegacyIcon } from '@ant-design/compatible';

import _isEmpty from 'lodash/isEmpty';
import _iisArray from 'lodash/isArray';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _isEqual from 'lodash/isEqual';
import _isNumber from 'lodash/isNumber';
import _trim from 'lodash/trim';
import Base from './base'
import Base1 from './BasicInfo'
import Files from './files'
import Info from '../Add/info'
import { listMemDigital, preview, dauploadFileDigital, uploadDigital, zydyuploadFileDigital, transferMemDigitalList } from '@/pages/developMem/services'
import { findNewFillAudit } from '@/pages/mem/services'
import Tip from '@/components/Tip'
import { _history } from "@/utils/method";
import style from './index.less'

const { SubMenu } = Menu;
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      nowAge: undefined,
      hasLost: false,
      _basicInfo: {},
      modalVisible: false,
      d08Code: '',
      hasAppointment: false,
      isOutSystem_state: false,
      canEdit: false,
      fileMenus: [],
      selectKey: '',
      openKey: '1',
      selectRow: {},
      fileObj: {},
      loading: false,
      fillAudit: {
        isUpload: true,
        applicantEndDate: ''
      }
    };
  }
  getmenus = (code, mc) => {
    const { location: { pathname = '' } = {} } = _history
    const { dispatch } = this.props;
    const { memCode = '', row, transdigitalLotNo = '' } = this.state;
    let url = 'memDevelop/memDigitalContents'
    let obj = {
      isCatalogue: code,
      d08Code: row?.d08Code || undefined,
      processNode: row?.processNode || undefined,
      memCode: memCode || mc,
    }
    // if (pathname.startsWith('/transfer')) {
    //   url = 'memDevelop/transferMemDigital'
    //   obj = {
    //     memCode: memCode || mc
    //   }
    // }
    dispatch({
      type: url,
      payload: {
        ...obj,

      }
    }).then(res => {
      const { code = 500, data = [] } = res
      if (res.code == 0 && data.length > 0) {
        // if (pathname.startsWith('/transfer')) {
        //   this.setState({
        //     fileMenus: res['data']['records'] || [],
        //     transdigitalLotNo: res['data']['digLotNo'] ? res['data']['digLotNo'] : transdigitalLotNo
        //   })
        // } else {

        // }
        this.setState({
          fileMenus: data || [],
          selectRow: data?.[0]?.children ? data?.[0]?.children?.[0] : data?.[0],
          selectKey: data?.[0]?.children ? data?.[0]?.children?.[0]?.key : data?.[0]?.key
        }, () => {
          this.getFileList({ key: data?.[0]?.children?.[0]?.key || data?.[0]?.key })
        })
      }

    })
  }
  // 保存
  submit = async () => {
    const { memCode, processNode, row, selectKey } = this.state;
    const { istransfer = false, memDevelop: { basicInfo: bd = {} } = {}, memBasic: { basicInfo: bb = {} } = {} } = this.props;
    const { location: { pathname = '' } = {} } = _history
    if (istransfer) {
      this.cancel()
    } else {
      // if (selectKey != '1-1-1') {
      //   let obj = { ...bd }
      //   let o = this['fileRef'].submit();

      //   if (pathname == '/mem/zy/manage') {
      //     obj = { ...bb }
      //   }
      //   this['Info'].showModal({ data: obj, file: { value: [o] }, rowCode: memCode })
      //   this.setState({
      //     fileValue: [o]
      //   })
      // } else {
      //   this.cancel()
      // }
      let obj = { ...bd }
      let o = this['fileRef'].submit();

      if (pathname == '/mem/zy/manage') {
        obj = { ...bb }
      }
      this['Info'].showModal({ data: obj, file: { value: [o] }, rowCode: memCode })
      this.setState({
        fileValue: [o]
      })
    }
  };
  cancel = () => {
    this.setState({
      modalVisible: false, d154CodeNoDraw: [], selectKey: '',
      selectRow: {},
      fileObj: {},
      fileMenus: []
    });
    this.destroy();
    this.props.onclose && this.props.onclose();
  };
  getfindNewFillAudit = async (codes) => {
    const { code = 500, data = {} } = await findNewFillAudit({ memCode: codes })
    if (code == 0) {
      this.setState({
        fillAudit: {
          ...data
        }
      })
    }
  }
  open = (e) => {
    console.log(e,'1111111111111111111111111111')
    const { canEdit, editType = '', type, code, processNode, d08 = '', digitalLotNo } = e;
    // const {istransfer = false } = this.props
    if (e.isArchived == 1) {
      this.getfindNewFillAudit(code)
    }
    this.setState({
      modalVisible: true,
      canEdit,
      editType,
      type,
      memCode: code,
      processNode,
      transdigitalLotNo: digitalLotNo,
      row: d08 ? { ...e, d08Code: d08 } : e
    }, () => {
      this.getmenus(e?.isCatalogue, code)
    });

  };
  destroy = () => {
    this.setState({
      hasLost: false,
      _basicInfo: {},
      d08Code: '5',
      hasAppointment: false,
      isOutSystem_state: false,
    });
    this.props.dispatch({
      type: 'memDevelop/updateState',
      payload: {
        basicInfo: {},
      },
    });
  };
  isOutSystemOnChange = (value) => {
    // this.props.form.setFieldsValue({
    //   appliedOrgCode:undefined,
    //   appliedOrgName:undefined
    // });
    this.setState({ isOutSystem_state: value == '1' });
  };
  changemenu = (obj) => {
    const { fileloading } = this.state
    let ch = this['fileRef']?.getc() || false
    if (fileloading) return message.error('文件正在上传中，请稍后再试')
    if (ch) {
      Modal.confirm({
        title: '提示',
        content: '当前操作未保存，切换后会数据丢失，确定要切换吗？',
        onOk: () => {
          this.setState({
            selectKey: obj.key,
            selectRow: obj,
          })
          this['fileRef'].setc()
          this.getFileList({ key: obj.key })
        },
      })
    } else {
      this.setState({
        selectKey: obj.key,
        selectRow: obj,
        // openKey: obj.key,
      })
      this.getFileList({ key: obj.key })
    }

  }
  getFileList = async (obj) => {
    this.setState({
      loading: true,
    })
    const { memCode, processNode = '', transdigitalLotNo = '', row } = this.state;
    const {
      memBasic: {
        basicInfo: { digitalLotNo: digitalLotNo3 = '' } = {},
      } = {},
    } = this.props

    let act = listMemDigital
    let param = {
      memCode, digitalLotNo: row?.digitalLotNo || digitalLotNo3, d222Code: obj.key
    }
    // if (this.props.istransfer) {
    //   act = transferMemDigitalList
    //   param = {
    //     digitalLotNo: transdigitalLotNo, d222Code: obj.key
    //   }
    // }
    const { code = 500, data: { digitals = [], operaterLogs = [] } = {} } = await act({ ...param });
    if (code == 0) {
      this.setState({
        loading: false,
        fileObj: {
          digitals,
          operaterLogs,
          selectKey: obj.key,
          randomkey: Math.random(),//
        },
      });
    }

  }
  confirmAgain = async (obj) => {

    const { node } = obj
    const {
      memDevelop: {
        basicInfo: { digitalLotNo: digitalLotNo1 = '' } = {},
        transDetail: { digitalLotNo: digitalLotNo2 = '' } = {}
      } = {},
      memBasic: {
        basicInfo: { digitalLotNo: digitalLotNo3 = '' } = {},
      } = {},
    } = this.props
    let str = digitalLotNo1 || digitalLotNo2 || digitalLotNo3
    const { oprationUser, isClose } = obj
    const { location: { pathname = '' } = {} } = _history
    if (!isClose) {
      const { row, selectKey, fileValue = [],fillAudit } = this.state;

      let fileList: any = []
      fileValue.map(i => {
        i.fileList.map(j => {
          delete j['baseurl']
          let obj = {
            ...i,
            ...j
          }
          obj['path'] = j['url'] ? j['url'] : j['path']
          delete obj['fileList']
          delete obj['processNode']
          fileList.push({ ...obj })
        })
      })

      // let act = uploadDigital
      // if (pathname == '/mem/zy/manage') {
      //   act = zydyuploadFileDigital
      // }
      const { code = 500, data = {} } = await uploadDigital({
        data: {
          digitalLotNo: str,
          filesList: fileList.filter(i => !(!i['code'] && i['isDelete'] == 1)),
          // oprationUser: node['oprationUser'],
          d08Code: row.d08Code,
          code: row.code,
          // processNode: row.processNode,
          ...node,
          applicantEndDate:fillAudit.applicantEndDate
        }
      })
      if (code == 0) {
        Tip.success('操作提示', '保存成功')
        this['fileRef'].setc()
        if (!digitalLotNo3 && pathname == '/mem/zy/manage') {
          this.upbaseInfo()
        } else {
          this.getFileList({ key: selectKey })
        }
      }
      this['Info'].closeModal();
    } else {
      this['Info'].closeModal();
    }
  }

  upbaseInfo = async () => {
    const { memCode, selectKey } = this.state
    if (memCode) {
      await this.props.dispatch({
        type: 'memBasic/findMem',
        payload: {
          code: memCode,
        }
      })

    }
    this.getFileList({ key: selectKey })
  }
  render(): React.ReactNode {
    const {
      form,
      memDevelop: {
        basicInfo: { digitalLotNo: digitalLotNo1 = '' } = {},
        transDetail: { digitalLotNo: digitalLotNo2 = '' } = {}
      } = {},
      memBasic: {
        basicInfo: { digitalLotNo: digitalLotNo3 = '' } = {},
      } = {},
      loading: { effects = {} } = {},
      tipMsg = {},
      activeTab,
      istransfer = false //关系转接详情查看数字档案  只能查看文件列表无操作
    } = this.props;
    // transDetail['digitalLotNo']
    const {
      modalVisible,
      loading,
      fileObj = [],
      fileMenus = [],
      selectKey,
      openKey,
      row,
      selectRow,
      fileloading,
      fillAudit
    } = this.state;
    const { location: { pathname = '' } = {} } = _history

    return (
      <Fragment>
        <Modal
          title={
            <div>
              <Space>
                <span>数字档案</span>
                <div className={style.fzdy} onClick={() => {
                  // this['MembersWorkProcedures'].open('发展党员规程')
                  window.open('/archivesAdministration/membersWorkProcedures')
                }}>发展党员规程</div>
              </Space>

            </div>

          }
          destroyOnClose
          visible={modalVisible}
          onCancel={this.cancel}
          // onOk={this.submit}
          width={'1600px'}
          footer={
            fillAudit.isUpload ? [
              <Button onClick={this.cancel}>取消</Button>,
              <Button type="primary" onClick={this.submit} loading={fileloading}>
                {'保存'}
              </Button>,
            ] : [
              <Button onClick={this.cancel}>取消</Button>
            ]
          }
        // confirmLoading={effects['memAbroad/save']}
        >
          <div>

            <Row>
              <Col span={4}>
                <div className={style.editLeft}>
                  <div className={style.sliders}>
                    <LegacyIcon type="snippets" />
                    <span style={{ paddingLeft: 6 }}>档案材料</span>
                  </div>
                  <Menu
                    // onClick={this.handleClick}
                    style={{ width: 256 }}
                    selectedKeys={[selectKey]}
                    // openKeys={[openKey]}
                    defaultOpenKeys={['1']}
                    mode="inline"
                  >
                    {/* {
                      !istransfer && <Menu.Item key='1-1-1' onClick={() => this.changemenu({ key: '1-1-1' })}>
                        <LegacyIcon type="user" />
                        <span>基本信息</span>
                      </Menu.Item>
                    } */}

                    {
                      fileMenus.map((item, index) => {
                        if (item.children) {
                          return (
                            <SubMenu key={item.key} title={item.name}>
                              {
                                item.children.map((j, f) => {
                                  return (
                                    <Menu.Item key={j.key} onClick={() => this.changemenu(j)}>
                                      <Tooltip title={j.name} >
                                        <span> {j.name}</span>
                                      </Tooltip>
                                    </Menu.Item>
                                  )
                                })
                              }
                            </SubMenu>
                          )
                        } else {
                          return (
                            <Menu.Item key={item.key} onClick={() => this.changemenu(item)}>
                              <Tooltip title={item.name} >
                                <span> {item.name}</span>
                              </Tooltip>

                            </Menu.Item>
                          )
                        }

                      })
                    }
                  </Menu>
                </div>

              </Col>
              <Col span={20}>
                <div style={{ height: '600px', overflow: 'hidden' }}>
                  <Files dataInfo={{ ...row, ...fileObj, selectRow, istransfer, fillAudit }} ref={e => this['fileRef'] = e} upList={() => this.getFileList({ key: selectRow.key })} load={(loading) => {
                    this.setState({ fileloading: loading })
                  }}>
                    <Spin spinning={loading} style={{ width: '100%', height: '100%' }}></Spin>
                  </Files>
                </div>
              </Col>
            </Row>

          </div>
        </Modal>
        <Info ref={e => this['Info'] = e} change={this.confirmAgain} />
      </Fragment>
    );
  }
}
export default Form.create()(index);
