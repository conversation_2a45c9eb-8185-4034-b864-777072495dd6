import React from "react";
import _isEmpty from 'lodash/isEmpty';
import { isEmpty, fileDownloadHeader } from '@/utils/method';
import moment from 'moment';

export const newTextColumn = [
    { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'name', width: 70 } },
    { key: '2', label: '性别', value: { title: '性别', dataIndex: 'sex_name', width: 70 } },
    {
        key: '3',
        label: '身份证号码',
        value: {
            title: '身份证号码',
            dataIndex: 'idcard',
            width: 80,
            render: (text, record) => {
                if (typeof text === 'string' && !_isEmpty(text)) {
                    let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
                    if (text.indexOf('*') > 0) {
                        return text;
                    }
                    return <span>{newVal}</span>;
                } else {
                    return '';
                }
            },
        },
    },
    { key: '4', label: '年龄', value: { title: '年龄', dataIndex: 'age', width: 80 } },
    { key: '5', label: '电话', value: { title: '电话', dataIndex: 'phone', width: 80 } },
    // { key: '6', label: '党员类型', value: { title: '党员类型', dataIndex: 'd08_name', width: 80 } },
    {
        key: '6',
        label: '预备党员日期',
        value: { title: '预备党员日期', dataIndex: 'join_org_date', width: 80 },
    },
    {
        key: '7',
        label: '转为正式党员日期',
        value: { title: '转为正式党员日期', dataIndex: 'full_member_date', width: 80 },
    },
    { key: '8', label: '单位名称', value: { title: '单位名称', dataIndex: 'unit_name', width: 80 } },
    { key: '9', label: '单位类别', value: { title: '单位类别', dataIndex: 'd04_name', width: 80 } },
    { key: '10', label: '民族', value: { title: '民族', dataIndex: 'd06_name', width: 80 } },
    { key: '11', label: '学历', value: { title: '学历', dataIndex: 'd07_name', width: 80 } },
    { key: '12', label: '党员类型', value: { title: '党员类型', dataIndex: 'd08_name', width: 80 } },
    { key: '13', label: '工作岗位', value: { title: '工作岗位', dataIndex: 'd09_name', width: 80 } },
    {
        key: '14',
        label: '新社会阶层',
        value: { title: '新社会阶层', dataIndex: 'd20_name', width: 80 },
    },
    { key: '15', label: '转正类型', value: { title: '转正类型', dataIndex: 'd28_name', width: 80 } },
    { key: '16', label: '籍贯', value: { title: '籍贯', dataIndex: 'd48_name', width: 80 } },
    { key: '17', label: '一线情况', value: { title: '一线情况', dataIndex: 'd21_name', width: 80 } },
    { key: '18', label: '其他电话', value: { title: '其他电话', dataIndex: 'other_tel', width: 80 } },
    { key: '19', label: '党费交纳情况name', value: { title: '党费交纳情况name', dataIndex: 'd49_name', width: 80 } },
    {
        key: '20',
        label: '创建时间',
        value: {
            title: '创建时间',
            dataIndex: 'create_time',
            render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
            width: 80,
        },
    },
    { key: '21', label: '备注', value: { title: '备注', dataIndex: 'remark', width: 80 } },
    {
        key: '22',
        label: '更新时间',
        value: {
            title: '更新时间',
            dataIndex: 'update_time',
            render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
            width: 80,
        },
    },
    { key: '23', label: '聘任专业技术职务名称', value: { title: '聘任专业技术职务名称', dataIndex: 'd19_name', width: 80 } },
    { key: '24', label: '人员单位班子成员在任职务代码', value: { title: '人员单位班子成员在任职务代码', dataIndex: 'd25_name', width: 80 } },
    { key: '25', label: '人员组织班子中人员来源', value: { title: '人员组织班子中人员来源', dataIndex: 'd121_name', width: 80 } },
    { key: '26', label: '人员单位班子中人员来源', value: { title: '人员单位班子中人员来源', dataIndex: 'unit_d121_name', width: 80 } },
    { key: '27', label: '人员组织班子党内职务名称', value: { title: '人员组织班子党内职务名称', dataIndex: 'd022_name', width: 80 } },
    { key: '28', label: '人员最新民主评议数据', value: { title: '人员最新民主评议数据', dataIndex: 'org_code_reviewers', width: 80 } },
    { key: '29', label: '民主评议结果', value: { title: '民主评议结果', dataIndex: 'result', width: 80 } },
    { key: '30', label: '民主评处理情况', value: { title: '民主评处理情况', dataIndex: 'situation', width: 80 } },
    { key: '31', label: '民主评议最新年份', value: { title: '民主评议最新年份', dataIndex: 'reviewers_year', width: 80 } },
    { key: '32', label: '党员离开党组织的类别', value: { title: '党员离开党组织的类别', dataIndex: 'd12_name', width: 80 } },
    { key: '33', label: '党员出党原因', value: { title: '党员出党原因', dataIndex: 'd50_name', width: 80 } },
    { key: '34', label: '出党原因', value: { title: '出党原因', dataIndex: 'd50_code_text', width: 80 } },
    {
        key: '35',
        label: '是否农民工',
        value: {
            title: '是否农民工', dataIndex: 'is_farmer', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;

            },
        },
    },
    { key: '36', label: '出国境原因', value: { title: '出国境原因', dataIndex: 'd033_name', width: 80 } },
    {
        key: '37', label: '出国（境）日期', value: {
            title: '出国（境）日期', dataIndex: 'abroad_date', width: 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        key: '38', label: '所至国家（地区）', value: {
            title: '所至国家（地区）', dataIndex: 'country_name', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '港澳台' : (text == 2 ? '国外' : '')}</span>;
            },
        }
    },
    { key: '39', label: '出国境党员与党组织联系情况', value: { title: '出国境党员与党组织联系情况', dataIndex: 'd037_name', width: 80 } },

    { key: '40', label: '党籍处理方式说明', value: { title: '党籍处理方式说明', dataIndex: 'd038_name', width: 80 } },

    { key: '41', label: '党员出国恢复组织生活情况说明', value: { title: '党员出国恢复组织生活情况说明', dataIndex: 'd040_name', width: 80 } },

    { key: '42', label: '党代表职业名称', value: { title: '党代表职业名称', dataIndex: 'd124_name', width: 80 } },

    { key: '43', label: '党代表人员身份名称', value: { title: '党代表人员身份名称', dataIndex: 'd106_name', width: 80 } },

    { key: '44', label: '党代表终止类型', value: { title: '党代表终止类型', dataIndex: 'd105_name', width: 80 } },

    {
        key: '45', label: '党代表任职开始时间', value: {
            title: '党代表任职开始时间', dataIndex: 'start_date', width: 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },

    {
        key: '46', label: '党代表任职结束时间', value: {
            title: '党代表任职结束时间', dataIndex: 'end_date', width: 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },

    {
        key: '47', label: '是否省（区、市）代表', value: {
            title: '是否省（区、市）代表', dataIndex: 'is_province_party', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == 2 ? '否' : '')}</span>
            }
        }
    },

    {
        key: '48', label: '是否市（州）代表', value: {
            title: '是否市（州）代表', dataIndex: 'is_city_party', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },

    {
        key: '49', label: '是否县（市、区）代表', value: {
            title: '是否县（市、区）代表', dataIndex: '"is_County_party"', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },

    {
        key: '50', label: '是否乡（镇）代表', value: {
            title: '是否乡（镇）代表', dataIndex: 'is_town_party', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '51',
        label: '是否参加评议',
        value: {
            title: '是否参加评议', dataIndex: 'has_join_reviewers', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            },
        }
    },
    {
        key: '52', label: '是否结束评议', value: {
            title: '是否结束评议', dataIndex: 'has_end_reviewers', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '53', label: '是否本年的出国境', value: {
            title: '是否本年的出国境', dataIndex: 'is_curr_year_abroad', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '54', label: '行业分类', value: { title: '行业分类', dataIndex: 'd114_name', width: 80 } },
    {
        key: '55',
        label: '人事关系是否在党组织关联单位内',
        value: {
            title: '人事关系是否在党组织关联单位内', dataIndex: 'has_unit_statistics', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;

            },
        },

    },
    { key: '56', label: '人事关系所在单位名称', value: { title: '人事关系所在单位名称', dataIndex: 'unit_information', width: 80 } },
    {
        key: '57',
        label: '人事关系所在单位是否省内单位',
        value: {
            title: '人事关系所在单位是否省内单位', dataIndex: 'has_unit_province', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            },
        },

    },
    {
        key: '58',
        label: '是否省外转入',
        value: {
            title: '是否省外转入', dataIndex: 'is_outland_transfer_in', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            },
        },

    },
    {
        key: '59', label: '省外转入年份', value: {
            title: '省外转入年份', dataIndex: 'outland_transfer_in_year', width: 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        key: '60',
        label: '预备期到的时间',
        value: {
            title: '预备期到的时间',
            dataIndex: 'extend_prepar_date',
            render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
            width: 80,
        },
    },
    {
        key: '61', label: '乡镇，街道情况', value: {
            title: ' 乡镇，街道情况', dataIndex: 'is_towns', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '乡' : (text == 2 ? "镇" : (text == 3 ? '街道' : ''))}</span>;
            }
        }
    },

    {
        key: '62', label: '行政村，城市社区，乡镇社区情况', value: {
            title: '行政村，城市社区，乡镇社区情况', dataIndex: 'is_community', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '行政村' : (text == 2 ? "城市社区" : (text == 3 ? '乡镇社区' : ''))}</span>;
            }
        }
    },

    { key: '63', label: '组织班子中离任原因', value: { title: '组织班子中离任原因', dataIndex: 'd120_name', width: 80 } },

    { key: '64', label: '人员组织班子中是否参加县级集中轮训', value: { title: '人员组织班子中是否参加县级集中轮训', dataIndex: 'has_part_training', width: 80 } },
    {
        key: '65', label: '预备期到的年度', value: {
            title: '预备期到的年度', dataIndex: 'extend_prepar_date_year', width: 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    { key: '66', label: '进入支部类型', value: { title: '进入支部类型', dataIndex: 'd11_name', width: 80 } },
    { key: '67', label: '工作单位及职务', value: { title: '工作单位及职务', dataIndex: 'work_post', width: 80 } },
    { key: '68', label: '工作单位及职务', value: { title: '工作单位及职务', dataIndex: 'work_post', width: 80 } },

    { key: '69', label: '恢复党籍时间', value: { title: '恢复党籍时间', dataIndex: 'recover_party_date', width: 80 } },

    { key: '70', label: '离开党组织日期', value: { title: '离开党组织日期', dataIndex: 'leave_org_date', width: 80 } },
    {
        key: '71',
        label: '出生日期',
        value: {
            title: '出生日期',
            dataIndex: 'birthday',
            render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
            width: 80,
        },
    },
    { key: '72', label: '在读院校', value: { title: '在读院校', dataIndex: 'reading_college', width: 80 } },
    { key: '73', label: '在读专业名称', value: { title: '在读专业名称', dataIndex: 'reading_professional_name', width: 80 } },
    { key: '74', label: '学制', value: { title: '学制', dataIndex: 'educational_system', width: 80 } },
    { key: '75', label: '补录类型', value: { title: '补录类型', dataIndex: 'd135_name', width: 80 } },
    {
        key: '76',
        label: '补录时间',
        value: {
            title: '补录时间',
            dataIndex: 'replenish_input_date',
            render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
            width: 80,
        },
    },
    { key: '77', label: '加入党组织类别', value: { title: '加入党组织类别', dataIndex: 'd27_name', width: 80 } },

    {
        key: '78', label: '回国时间', value: { title: '回国时间', dataIndex: 'back_home_date', width: 80 },
        render: (text) => {
            return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
        },
    },

    {
        key: '79', label: '出国境是否半年以上的', value: {
            title: '出国境是否半年以上的', dataIndex: 'abroad_half_year', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            },
        }
    },

    {
        key: '80', label: '组织关系出国境时是否转往国（境）外', value: {
            title: '组织关系出国境时是否转往国（境）外', dataIndex: 'abroad_transfer_out', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            },
        }
    },

    {
        key: '81', label: '是否党组织软弱涣散村', value: {
            title: '是否党组织软弱涣散村', dataIndex: 'has_org_slack_village', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            },
        }
    },

    { key: '82', label: '预备党员转正情况类型', value: { title: '预备党员转正情况类型', dataIndex: 'prepare_full_type', width: 80 } },

    { key: '83', label: '国民经济行业', value: { title: '国民经济行业', dataIndex: 'd194_name', width: 80 } },
    { key: '84', label: '生产性服务行业', value: { title: '生产性服务行业', dataIndex: 'd195_name', width: 80 } },
    { key: '85', label: '第几产业', value: { title: '第几产业', dataIndex: 'industry', width: 80 } },
];

export const newTableOrg = [
    { key: '1', label: '组织名称', value: { title: '组织名称', dataIndex: 'name', width: 80 } },
    {
        key: '2',
        label: '上级党组织名称',
        value: { title: '上级党组织名称', dataIndex: 'parent_name', width: 80 },
    },
    { key: '3', label: '党组织类型名称', value: { title: '党组织类型名称', dataIndex: 'd01_name', width: 80 } },
    {
        key: '4',
        label: '党组织所在单位情况名称',
        value: { title: '党组织所在单位情况名称', dataIndex: 'd02_name', width: 80 },
    },
    {
        key: '5',
        label: '党组织隶属关系名称',
        value: { title: '党组织隶属关系名称', dataIndex: 'd03_name', width: 80 },
    },
    { key: '6', label: '单位类别', value: { title: '单位类别', dataIndex: 'd04_name', width: 80 } },
    { key: '7', label: '建立党组织情况', value: { title: '建立党组织情况', dataIndex: 'd05_name', width: 80 } },
    { key: '8', label: '党组织书记', value: { title: '党组织书记', dataIndex: 'secretary', width: 80 } },
    { key: '9', label: '联系人', value: { title: '联系人', dataIndex: 'contacter', width: 80 } },
    { key: '10', label: '联系方式', value: { title: '联系方式', dataIndex: 'contact_phone', width: 80 } },
    { key: '11', label: '单位名称', value: { title: '单位名称', dataIndex: 'main_unit_name', width: 80 } },
    { key: '12', label: '组织简称', value: { title: '组织简称', dataIndex: 'short_name' }, width: 80 },
    { key: '13', label: '组织全称拼音', value: { title: '组织全称拼音', dataIndex: 'pinyin', width: 80 } },
    {
        key: '14', label: '党组织大分类', value: {
            title: '党组织大分类', dataIndex: 'org_type', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '党委' : (text == 2 ? '党总支' : (text == 3 ? '党支部' : (text == 4 ? '联合党支部' : (text == 5 ? '党组' : ''))))}</span>;
            }
        }
    },
    {
        key: '15', label: '是否离退休', value: {
            title: '是否离退休', dataIndex: 'is_retire', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '16', label: '单位隶属关系', value: { title: '单位隶属关系', dataIndex: 'd35_name', width: 80 } },
    {
        key: '17', label: '创建时间', value: {
            title: '创建时间', dataIndex: 'create_time', width: 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            }
        }
    },
    {
        key: '18', label: '更新时间', value: {
            title: '更新时间', dataIndex: 'update_time', width: 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            }
        }
    },
    { key: '19', label: '最新党代会任届情况', value: { title: '最新党代会任届情况', dataIndex: 'situation', width: 80 } },
    { key: '20', label: '最新党代会本年换届选举党代表', value: { title: '最新党代会本年换届选举党代表', dataIndex: 'congress_situation', width: 80 } },
    { key: '21', label: '最新党代会本年换届选举党代表', value: { title: '最新党代会本年换届选举党代表', dataIndex: 'representatives', width: 80 } },
    { key: '22', label: '最新党代会党委委员', value: { title: '最新党代会党委委员', dataIndex: 'party_committee', width: 80 } },
    { key: '23', label: '最新党代会党委委员其中常委', value: { title: '最新党代会党委委员其中常委', dataIndex: 'standing_committee', width: 80 } },
    { key: '24', label: '最新党代会党委候补委员', value: { title: '最新党代会党委候补委员', dataIndex: 'party_alternate_committee', width: 80 } },
    { key: '25', label: '最新党代会纪委委员', value: { title: '最新党代会纪委委员', dataIndex: 'inspection_committee', width: 80 } },
    { key: '26', label: '最新党代会纪委委员其中常委', value: { title: '最新党代会纪委委员其中常委', dataIndex: 'inspection_standing_committee', width: 80 } },
    { key: '27', label: '最新党代会召开党委全会情况', value: { title: '最新党代会召开党委全会情况', dataIndex: 'whole_situation', width: 80 } },
    { key: '28', label: '最新党代会领导班子召开民主生活会情况', value: { title: '最新党代会领导班子召开民主生活会情况', dataIndex: 'life_situation', width: 80 } },
    { key: '29', label: '参加委员', value: { title: '参加委员', dataIndex: 'attend_member', width: 80 } },
    { key: '30', label: '参加人员', value: { title: '参加人员', dataIndex: 'participants', width: 80 } },
    {
        key: '31', label: '人员民主评议最新年份', value: {
            title: '人员民主评议最新年份', width: 80, dataIndex: 'reviewers_year', render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            }
        }
    },
    {
        key: '32', label: '是否是开展评议', value: {
            title: '是否是开展评议', dataIndex: 'has_join_reviewers', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '33', label: '经济控制类型', value: { title: '经济控制类型', dataIndex: 'd16_name', width: 80 } },
    {
        key: '34', label: '是否本年度任届期满的内设机构党支部', value: {
            title: '是否本年度任届期满的内设机构党支部', dataIndex: 'has_internal_institutions', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '35', label: '是否本年度任届期满的内设机构党支部且本年换届', value: {
            title: '是否本年度任届期满的内设机构党支部且本年换届', dataIndex: 'has_internal_institutions_transition', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '36', label: '年度', value: { title: '年度', dataIndex: 'year', width: 80 } },
    { key: '37', label: '培训总人数', value: { title: '培训总人数', dataIndex: 'train_total', width: 80 } },
    { key: '38', label: '学校培训人数', value: { title: '学校培训人数', dataIndex: 'school_train', width: 80 } },
    { key: '39', label: '新党员培训人次', value: { title: '新党员培训人次', dataIndex: 'new_train', width: 80 } },
    { key: '40', label: '青年党员培训人次', value: { title: '青年党员培训人次', dataIndex: 'youth_train', width: 80 } },
    { key: '41', label: '老年党员培训人次', value: { title: '老年党员培训人次', dataIndex: 'elderly_train', width: 80 } },
    { key: '42', label: '流动党员培训人次', value: { title: '流动党员培训人次', dataIndex: 'flow_train', width: 80 } },
    { key: '43', label: '下岗失业人员中党员培训人次', value: { title: '下岗失业人员中党员培训人次', dataIndex: 'laid_off_train', width: 80 } },
    { key: '44', label: '民族地区党员培训人次', value: { title: '民族地区党员培训人次', dataIndex: 'minority_areas_train', width: 80 } },
    { key: '45', label: '党组织书记培训人次', value: { title: '党组织书记培训人次', dataIndex: 'organization_secretary', width: 80 } },
    { key: '46', label: '参加县级以上党委集中轮训人次', value: { title: '参加县级以上党委集中轮训人次', dataIndex: 'county_party_committee', width: 80 } },
    { key: '47', label: '新任基层党组织书记培训人次', value: { title: '新任基层党组织书记培训人次', dataIndex: 'level_organizations_secretary', width: 80 } },
    { key: '48', label: '社区党务工作者培训人次', value: { title: '社区党务工作者培训人次', dataIndex: 'community_party', width: 80 } },
    {
        key: '49', label: '是否按照规定开展三会一课', value: {
            title: '是否按照规定开展三会一课', dataIndex: 'has_will_lesson', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '50', label: '是否按照规定开展主题党日', value: {
            title: '是否按照规定开展主题党日', dataIndex: 'has_party_day', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '51', label: '省级举办培训班期', value: { title: '省级举办培训班期', dataIndex: 'provincial_train_class', width: 80 } },
    { key: '52', label: '省级举办培训班培训人次', value: { title: '省级举办培训班培训人次', dataIndex: 'provincial_train_member', width: 80 } },
    { key: '53', label: '市级举办培训班期数', value: { title: '市级举办培训班期数', dataIndex: 'city_train_class', width: 80 } },
    { key: '54', label: '市级举办培训班培训人次', value: { title: '市级举办培训班培训人次', dataIndex: 'city_train_member', width: 80 } },
    { key: '55', label: '县级举办培训班期数', value: { title: '县级举办培训班期数', dataIndex: 'county_train_class', width: 80 } },
    { key: '56', label: '县级举办培训班培训人次', value: { title: '县级举办培训班培训人次', dataIndex: 'county_train_member', width: 80 } },
    { key: '57', label: '基层党委举办培训班期数', value: { title: '基层党委举办培训班期数', dataIndex: 'level_party_class', width: 80 } },
    { key: '58', label: '基层党委举办培训班培训人次', value: { title: '基层党委举办培训班培训人次', dataIndex: 'level_party_member', width: 80 } },
    { key: '59', label: '直接组织开展农村党员集中培训的乡镇个数', value: { title: '直接组织开展农村党员集中培训的乡镇个数', dataIndex: 'rural_party_villages', width: 80 } },
    { key: '60', label: '党员干部现代远程教育终端站点共个数', value: { title: '党员干部现代远程教育终端站点共个数', dataIndex: 'remote_education', width: 80 } },
    { key: '61', label: '终端远程乡镇个数', value: { title: '终端远程乡镇个数', dataIndex: 'remote_education_villages', width: 80 } },
    { key: '62', label: '终端远程行政村个数', value: { title: '终端远程行政村个数', dataIndex: 'remote_education_administrative_village', width: 80 } },
    { key: '63', label: '终端远程社区居委会个数', value: { title: '终端远程社区居委会个数', dataIndex: 'remote_education_committee', width: 80 } },
    { key: '64', label: '通过互联网传播个数', value: { title: '通过互联网传播个数', dataIndex: 'internet', width: 80 } },
    { key: '65', label: '有线传播个数', value: { title: '有线传播个数', dataIndex: 'wired', width: 80 } },
    { key: '66', label: '卫星传播个数', value: { title: '卫星传播个数', dataIndex: 'satellite', width: 80 } },
    { key: '67', label: '站点管理员', value: { title: '站点管理员', dataIndex: 'site_administrator', width: 80 } },
    { key: '68', label: '乡镇干部数', value: { title: '乡镇干部数', dataIndex: 'villages_cadres', width: 80 } },
    { key: '69', label: '村社区干部数', value: { title: '村社区干部数', dataIndex: 'village_community', width: 80 } },
    { key: '70', label: '志愿者', value: { title: '志愿者', dataIndex: 'volunteers', width: 80 } },
    { key: '71', label: '农村党员远程教育培训党员数', value: { title: '农村党员远程教育培训党员数', dataIndex: 'rural_remote_education_party', width: 80 } },
    { key: '72', label: '新兴领域党员培训', value: { title: '新兴领域党员培训', dataIndex: 'xxlydypx', width: 80 } },
    { key: '73', label: '其他党员培训', value: { title: '其他党员培训', dataIndex: 'qtdypx', width: 80 } },
    { key: '74', label: '流动党员培训', value: { title: '流动党员培训', dataIndex: 'lddypx', width: 80 } },
    { key: '75', label: '乡镇党员（不包括乡镇社区党员）培训', value: { title: '乡镇党员（不包括乡镇社区党员）培训', dataIndex: 'xzdypx', width: 80 } },
    { key: '76', label: '举办脱产培训班/期数', value: { title: '举办脱产培训班/期数', dataIndex: 'jbtcpxbgq', width: 80 } },
    { key: '77', label: '举办脱产培训班/人次', value: { title: '举办脱产培训班/人次', dataIndex: 'jbtcpxbgrc', width: 80 } },
    { key: '78', label: '视频培训班/期', value: { title: '视频培训班/期', dataIndex: 'sppxbq', width: 80 } },
    { key: '79', label: '视频培训班/人次', value: { title: '视频培训班/人次', dataIndex: 'sppxbrc', width: 80 } },
    { key: '80', label: '组织集体学习共/次', value: { title: '组织集体学习共/次', dataIndex: 'zzjtxxgc', width: 80 } },
    { key: '81', label: '讲党课/次', value: { title: '讲党课/次', dataIndex: 'jdkc', width: 80 } },
    { key: '82', label: '举办专题讲座、报告会等/场', value: { title: '举办专题讲座、报告会等/场', dataIndex: 'jbztjzbghc', width: 80 } },
    { key: '83', label: '党员年度集中学习培训学时达标', value: { title: '党员年度集中学习培训学时达标', dataIndex: 'dyndjzxxpxxsdb', width: 80 } },
    { key: '84', label: '基层党组织书记年度集中学习培训学时达标', value: { title: '基层党组织书记年度集中学习培训学时达标', dataIndex: 'jcdzzsjndjzxxpxxsdb', width: 80 } },
    { key: '85', label: '参加县级以上党委举办的脱产培训学时达标', value: { title: '参加县级以上党委举办的脱产培训学时达标', dataIndex: 'cjxjysdwjbdtcpxxsdb', width: 80 } },
    { key: '86', label: '宣传思想文化系统党员培训', value: { title: '宣传思想文化系统党员培训', dataIndex: 'xcsxwhxtdypx', width: 80 } },
    { key: '87', label: '教育系统党员培训', value: { title: '教育系统党员培训', dataIndex: 'jyxtdypx', width: 80 } },
    { key: '88', label: '学生党员培训', value: { title: '学生党员培训', dataIndex: 'xsdypx', width: 80 } },
    { key: '89', label: '科研机构党员培训', value: { title: '科研机构党员培训', dataIndex: 'kyjgdypx', width: 80 } },
    { key: '90', label: '医药卫生系统党员培训', value: { title: '医药卫生系统党员培训', dataIndex: 'yywsxtdypx', width: 80 } },
    { key: '91', label: '国有企业党员培训', value: { title: '国有企业党员培训', dataIndex: 'gyqydypx', width: 80 } },
    { key: '92', label: '非公有制企业党员培训', value: { title: '非公有制企业党员培训', dataIndex: 'fgyzqydypx', width: 80 } },
    { key: '93', label: '金融企业党员培训', value: { title: '金融企业党员培训', dataIndex: 'jrqydypx', width: 80 } },
    { key: '94', label: '乡镇、街道等基层党校/个', value: { title: '乡镇、街道等基层党校/个', dataIndex: 'xzjddjcdxg', width: 80 } },
    { key: '95', label: '乡镇党校/个', value: { title: '乡镇党校/个', dataIndex: 'xzdx', width: 80 } },
    { key: '96', label: '街道党校/个', value: { title: '街道党校/个', dataIndex: 'jddx', width: 80 } },
    { key: '97', label: '现场教学点/个', value: { title: '现场教学点/个', dataIndex: 'xcjxd', width: 80 } },
    {
        key: '98', label: '是否流动党员党组织', value: {
            title: '是否流动党员党组织', dataIndex: 'is_flow', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '99', label: '传真号', value: { title: '传真号', dataIndex: 'fax_number', width: 80 } },
    { key: '100', label: '通讯地址', value: { title: '通讯地址', dataIndex: 'post_address', width: 80 } },
    { key: '101', label: '邮政编码', value: { title: '邮政编码', dataIndex: 'post_code', width: 80 } },
    {
        key: '102', label: '党组织建立日期', value: {
            title: '党组织建立日期', dataIndex: 'create_date', width: 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    { key: '103', label: '备注', value: { title: '备注', dataIndex: 'remark', width: 80 } },
    { key: '104', label: '组织撤销文号', value: { title: '组织撤销文号', dataIndex: 'document_num', width: 80 } },
    { key: '105', label: '组织撤销原因', value: { title: '组织撤销原因', dataIndex: 'reason', width: 80 } },
    {
        key: '106', label: '是否依托组织部门成立非公党工', value: {
            title: '是否依托组织部门成立非公党工', dataIndex: 'has_work_committee', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '107', label: '是否设立专门办事机构', value: {
            title: '是否设立专门办事机构', dataIndex: 'has_working_body', width: 80, width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '108', label: '办事机构工作人员编制', value: { title: '办事机构工作人员编制', dataIndex: 'working_body_number', width: 80 } },
    { key: '109', label: '组织部门（非公党工委）直接管理的非公企业党组织数', value: { title: '组织部门（非公党工委）直接管理的非公企业党组织数', dataIndex: 'manage_organization_number', width: 80 } },
    { key: '110', label: '组织部门（非公党工委）直接联系的非公企业党组织数', value: { title: '组织部门（非公党工委）直接联系的非公企业党组织数', dataIndex: 'connect_organization_number', width: 80 } },
    { key: '111', label: '财政专项列支非公企业党建工作经费（万元）', value: { title: '财政专项列支非公企业党建工作经费（万元）', dataIndex: 'fiscal_funds', width: 80 } },
    { key: '112', label: '党费拨补非公企业党建工作经费 （万元）', value: { title: '党费拨补非公企业党建工作经费 （万元）', dataIndex: 'party_expenses', width: 80 } },
    { key: '113', label: '非公企业集聚区综合性党群活动服务中', value: { title: '非公企业集聚区综合性党群活动服务中', dataIndex: 'activity_service_center', width: 80 } },
    { key: '114', label: '新建立非公企业集聚区综合性党群活动服务中心', value: { title: '新建立非公企业集聚区综合性党群活动服务中心', dataIndex: 'new_activity_service_center', width: 80 } },
    {
        key: '115', label: '乡镇，街道情况', value: {
            title: '乡镇，街道情况', dataIndex: 'is_towns', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '乡' : (text == 2 ? "镇" : (text == 3 ? '街道' : ''))}</span>;
            }
        }
    },
    {
        key: '116', label: '行政村，城市社区，乡镇社区情况', value: {
            title: '行政村，城市社区，乡镇社区情况', dataIndex: 'is_community', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '行政村' : (text == 2 ? "城市社区" : (text == 3 ? '乡镇社区' : ''))}</span>;
            }
        }
    },
    {
        key: '117', label: '是否国务院和地方政府工作部门下属组织', value: {
            title: '是否国务院和地方政府工作部门下属组织', dataIndex: 'is_state_council', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '118', label: '是否农村专业技术协会下属组织', value: {
            title: '是否农村专业技术协会下属组织', dataIndex: 'is_rural_expertise', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '119', label: '是否农民专业合作社下属组织', value: {
            title: '是否农民专业合作社下属组织', dataIndex: 'is_farmer_major', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '120', label: '是否家庭农场下属组织', value: {
            title: '是否家庭农场下属组织', dataIndex: 'is_family_farm', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '121', label: '党组织书记数量', value: { title: '党组织书记数量', dataIndex: 'secretary_count', width: 80 } },
    { key: '122', label: '本年度发展党员', value: { title: '本年度发展党员', dataIndex: 'year_develop_mem', width: 80 } },
    { key: '123', label: '本年度发展卫生技术人员党员', value: { title: '本年度发展卫生技术人员党员', dataIndex: 'year_develop_mem_medicine', width: 80 } },
    { key: '124', label: '本年度发展教师党员', value: { title: '本年度发展教师党员', dataIndex: 'year_develop_teacher', width: 80 } },
    { key: '125', label: '本年度发展学生党员', value: { title: '本年度发展学生党员', dataIndex: 'year_develop_student', width: 80 } },
    { key: '126', label: '本年度毕业生党员', value: { title: '本年度毕业生党员', dataIndex: 'year_develop_graduate', width: 80 } },
    { key: '127', label: '累积未转出组织关系的毕业生党员', value: { title: '累积未转出组织关系的毕业生党员', dataIndex: 'year_develop_graduate_not_transfer', width: 80 } },
    { key: '128', label: '办校类别', value: { title: '办校类别', dataIndex: 'd109_name', width: 80 } },
    { key: '129', label: '从乡镇事业编制人员中选拔乡镇领导干部（人）', value: { title: '从乡镇事业编制人员中选拔乡镇领导干部（人）', dataIndex: 'lead_cadres_towns', width: 80 } },
    { key: '130', label: '从到村任职过的选调生中选拔乡镇领导干部（人）', value: { title: '从到村任职过的选调生中选拔乡镇领导干部（人）', dataIndex: 'village_lead_cadres', width: 80 } },
    { key: '131', label: '从第一书记中选拔乡镇领导干部（人）', value: { title: '从第一书记中选拔乡镇领导干部（人）', dataIndex: 'first_secretary_cadres', width: 80 } },
    { key: '132', label: '从驻村工作队员中选拔乡镇领导干部（人）', value: { title: '从驻村工作队员中选拔乡镇领导干部（人）', dataIndex: 'village_leaders', width: 80 } },
    { key: '133', label: '已消除集体经济薄弱村空壳村的县', value: { title: '已消除集体经济薄弱村空壳村的县', dataIndex: 'has_remove_income_less_5w', width: 80 } },
    { key: '134', label: '超过50%村没有集体经济收入的县（市、区、旗）', value: { title: '超过50%村没有集体经济收入的县（市、区、旗）', dataIndex: 'has_over_fifty_no_income', width: 80 } },
    { key: '135', label: '已消除集体经济薄弱村空壳村的县', value: { title: '已消除集体经济薄弱村空壳村的县', dataIndex: 'has_remove_income_less_5w_csq', width: 80 } },
    { key: '136', label: '超过50%村没有集体经济收入的县（市、区、旗）', value: { title: '超过50%村没有集体经济收入的县（市、区、旗）', dataIndex: 'has_over_fifty_no_income_csq', width: 80 } },
    {
        key: '137', label: '是否试行乡镇党代会年会制', value: {
            title: '是否试行乡镇党代会年会制', width: 80, dataIndex: 'has_annual_meet', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '138', label: '本年度列为入党积极分子数', value: { title: '本年度列为入党积极分子数', width: 80, dataIndex: 'year_active_mem' } },
    {
        key: '139', label: '是否垂直管理部门', value: {
            title: '是否垂直管理部门', width: 80, dataIndex: 'is_czglbm', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '140', label: '流动党员对应的行政区划', value: { title: '流动党员对应的行政区划', width: 80, dataIndex: 'administrative_region' } },
    {
        key: '141', label: '是否建立党建引领基层治理领导协调机制', value: {
            title: '是否建立党建引领基层治理领导协调机制', width: 80, dataIndex: 'has_leading_basic_coordinating', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '142', label: '教师党支部书记是否是“双带头人”', value: {
            title: '教师党支部书记是否是“双带头人”', width: 80, dataIndex: 'has_teachers_double_leaders', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '143', label: '在岗职工数（人）', value: { title: '在岗职工数（人）', width: 80, dataIndex: 'on_post_num' } },
    {
        key: '144', label: '法定代表人是否党员', value: {
            title: '法定代表人是否党员', width: 80, dataIndex: 'has_representative', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '145', label: '是否兼任企业党组书记', value: {
            title: '是否兼任企业党组书记', width: 80, dataIndex: 'has_proper_secretary', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '146', label: '是否脱钩行业协会商会', value: {
            title: '是否脱钩行业协会商会', width: 80, dataIndex: 'is_decoupl_industry', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '147', label: '党支部书记是否“双带头人”', value: {
            title: '党支部书记是否“双带头人”', width: 80, dataIndex: 'has_sjsdtr', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '148', label: '党支部书记是否内设机构负责人', value: {
            title: '党支部书记是否内设机构负责人', width: 80, dataIndex: 'has_secretaryisinsideleader', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '149', label: '市、区、街道、社区均建立党建联席会议制度的城市', value: { title: '市、区、街道、社区均建立党建联席会议制度的城市', width: 80, dataIndex: 'has_build_joint_meeting' } },
    { key: '150', label: '社区党组织书记实行县级党委备案管理的区', value: { title: '社区党组织书记实行县级党委备案管理的区', width: 80, dataIndex: 'has_secretary_build_county_filing' } },
    {
        key: '151', label: '是否属于关联单位本级', value: {
            title: '是否属于关联单位本级', dataIndex: 'has_unit_own_level', width: 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '152', label: '是否由企业中高层管理人员担任党组织书记', value: {
            title: '是否由企业中高层管理人员担任党组织书记', width: 80, dataIndex: 'has_secretary_high_level', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '153', label: '上级党组织是否全额下拨本党组织上缴党费 ', value: {
            title: '上级党组织是否全额下拨本党组织上缴党费 ', width: 80, dataIndex: 'has_pull_down_all_party_fee', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '154', label: '上级党组织是否给予经费支持', value: {
            title: '上级党组织是否给予经费支持', width: 80, dataIndex: 'has_give_funds_support', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '155', label: '是否自有党组织活动场所', value: {
            title: '是否自有党组织活动场所', width: 80, dataIndex: 'has_own_activity_place', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '156', label: '是否配备专职党务工作者', value: {
            title: '是否配备专职党务工作者', width: 80, dataIndex: 'has_party_affairs_workers', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        key: '157', label: '党组织关联主单位是否法人单位', value: {
            title: '党组织关联主单位是否法人单位', width: 80, dataIndex: 'is_legal', render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    { key: '158', label: '国民经济', value: { title: '国民经济', dataIndex: 'd194_name', width: 80 } },
    { key: '159', label: '生产性服务行业', value: { title: '生产性服务行业', dataIndex: '9d1945_name', width: 80 } },
    { key: '160', label: '第几产业', value: { title: '第几产业', dataIndex: 'industry', width: 80 } },
];

export const newTableUnit = [
    {
        "key": "1",
        "label": "关联组织",
        "value": {
            "title": "关联组织",
            "dataIndex": "main_org_name",
            "width": 80
        }
    },
    {
        "key": "2",
        "label": "党建指导组织",
        "value": {
            "title": "党建指导组织",
            "dataIndex": "manage_org_name",
            "width": 80
        }
    },
    {
        "key": "3",
        "label": "单位名称",
        "value": {
            "title": "单位名称",
            "dataIndex": "name",
            "width": 80
        }
    },
    {
        "key": "4",
        "label": "单位代码",
        "value": {
            "title": "单位代码",
            "dataIndex": "credit_code",
            "width": 80
        }
    },
    {
        "key": "5",
        "label": "单位类别",
        "value": {
            "title": "单位类别",
            "dataIndex": "d04_name",
            "width": 80
        }
    },
    {
        "key": "6",
        "label": "经济类型",
        "value": {
            "title": "经济类型",
            "dataIndex": "d16_name",
            "width": 80
        }
    },
    {
        "key": "7",
        "label": "企业规模",
        "value": {
            "title": "企业规模",
            "dataIndex": "d17_name",
            "width": 80
        }
    },
    {
        "key": "8",
        "label": "单位隶属关系",
        "value": {
            "title": "单位隶属关系",
            "dataIndex": "d35_name",
            "width": 80
        }
    },
    {
        "key": "9",
        "label": "所在地区",
        "value": {
            "title": "所在地区",
            "dataIndex": "d48_name",
            "width": 80
        }
    },
    {
        "key": "10",
        "label": "公益分类",
        "value": {
            "title": "公益分类",
            "dataIndex": "d81_name",
            "width": 80
        }
    },
    {
        "key": "11",
        "label": "办院类型",
        "value": {
            "title": "办院类型",
            "dataIndex": "d111_name",
            "width": 80
        }
    },
    {
        "key": "12",
        "label": "所长负责制情况",
        "value": {
            "title": "所长负责制情况",
            "dataIndex": "d112_name",
            "width": 80
        }
    },
    {
        "key": "13",
        "label": "在岗职工数（人）",
        "value": {
            "title": "在岗职工数（人）",
            "dataIndex": "on_post_num",
            "width": 80
        }
    },
    {
        "key": "14",
        "label": "建立党员服务机构",
        "value": {
            "title": "建立党员服务机构",
            "dataIndex": "is_org_service",
            "width": 80
        }
    },
    {
        "key": "15",
        "label": "建立党员志愿者队伍",
        "value": {
            "title": "建立党员志愿者队伍",
            "dataIndex": "is_vol_team",
            "width": 80
        }
    },
    {
        "key": "16",
        "label": "是否配备专职党务工作人员",
        "value": {
            "title": "是否配备专职党务工作人员",
            "dataIndex": "has_party_work",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "17",
        "label": "配备专职副书记",
        "value": {
            "title": "配备专职副书记",
            "dataIndex": "has_major_deputy_secretary",
            "width": 80
        }
    },
    {
        "key": "18",
        "label": "法定代表人是否党员",
        "value": {
            "title": "法定代表人是否党员",
            "dataIndex": "has_representative",
            "width": 80
        }
    },
    {
        "key": "19",
        "label": "是否建立工会或共青团组织",
        "value": {
            "title": "是否建立工会或共青团组织",
            "dataIndex": "has_union_organization",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "20",
        "label": "是否党建工作指导员联系",
        "value": {
            "title": "是否党建工作指导员联系",
            "dataIndex": "has_instructor_contact",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "21",
        "label": "是否主要负责人担任党组织书记",
        "value": {
            "title": "是否主要负责人担任党组织书记",
            "dataIndex": "has_organization_secretary",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "22",
        "label": "是否行业协会商会",
        "value": {
            "title": "是否行业协会商会",
            "dataIndex": "is_decoupl_industry",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "23",
        "label": "中央一级机关党组数量",
        "value": {
            "title": "中央一级机关党组数量",
            "dataIndex": "unit_party_central",
            "width": 80
        }
    },
    {
        "key": "24",
        "label": "省（区、市）一级机关党组",
        "value": {
            "title": "省（区、市）一级机关党组",
            "dataIndex": "unit_party_province",
            "width": 80
        }
    },
    {
        "key": "25",
        "label": "市（地、州、盟）一级机关党组",
        "value": {
            "title": "市（地、州、盟）一级机关党组",
            "dataIndex": "unit_party_city",
            "width": 80
        }
    },
    {
        "key": "26",
        "label": "县（市、区、旗）一级机关党组",
        "value": {
            "title": "县（市、区、旗）一级机关党组",
            "dataIndex": "unit_party_county",
            "width": 80
        }
    },
    {
        "key": "27",
        "label": "吸收未转入组织关系的党员建立党组织数",
        "value": {
            "title": "吸收未转入组织关系的党员建立党组织数",
            "dataIndex": "absorbed_tissue_number",
            "width": 80
        }
    },
    {
        "key": "28",
        "label": "未转组织关系党员数",
        "value": {
            "title": "未转组织关系党员数",
            "dataIndex": "not_turned_party",
            "width": 80
        }
    },
    {
        "key": "29",
        "label": "党建工作指导员数",
        "value": {
            "title": "党建工作指导员数",
            "dataIndex": "bZT6_10",
            "width": 80
        }
    },
    {
        "key": "30",
        "label": "党政机关工作人员",
        "value": {
            "title": "党政机关工作人员",
            "dataIndex": "b30_A12",
            "width": 80
        }
    },
    {
        "key": "31",
        "label": "在岗专业技术人员数（人）",
        "value": {
            "title": "在岗专业技术人员数（人）",
            "dataIndex": "tec_num",
            "width": 80
        }
    },
    {
        "key": "32",
        "label": "在岗专业技术人员（高级职称）（人）",
        "value": {
            "title": "在岗专业技术人员（高级职称）（人）",
            "dataIndex": "zaigang_gaoji",
            "width": 80
        }
    },
    {
        "key": "33",
        "label": "从业人员数",
        "value": {
            "title": "从业人员数",
            "dataIndex": "employees_number",
            "width": 80
        }
    },
    {
        "key": "34",
        "label": "建立分党组",
        "value": {
            "title": "建立分党组",
            "dataIndex": "create_party_group",
            "width": 80
        }
    },
    {
        "key": "35",
        "label": "建立党组",
        "value": {
            "title": "建立党组",
            "dataIndex": "create_party_team",
            "width": 80
        }
    },
    {
        "key": "36",
        "label": "建立党组性质党委",
        "value": {
            "title": "建立党组性质党委",
            "dataIndex": "create_party_committee",
            "width": 80
        }
    },
    {
        "key": "37",
        "label": "最新第一书记年份",
        "value": {
            "title": "最新第一书记年份",
            "dataIndex": "year",
            "width": 80
        }
    },
    {
        "key": "38",
        "label": "今年选派第一书记",
        "value": {
            "title": "今年选派第一书记",
            "dataIndex": "first_secretary_select",
            "width": 80
        }
    },
    {
        "key": "39",
        "label": "本年各级培训第一书记",
        "value": {
            "title": "本年各级培训第一书记",
            "dataIndex": "secretary_training_num",
            "width": 80
        }
    },
    {
        "key": "40",
        "label": "是否为第一书记安排不低于1万元工作经费",
        "value": {
            "title": "是否为第一书记安排不低于1万元工作经费",
            "dataIndex": "has_thousand",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "41",
        "label": "是否派出单位落实责任、项目、资金捆绑的",
        "value": {
            "title": "是否派出单位落实责任、项目、资金捆绑的",
            "dataIndex": "has_bundled",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "42",
        "label": "提拔使用或晋级的第一书记数",
        "value": {
            "title": "提拔使用或晋级的第一书记数",
            "dataIndex": "promoted_num",
            "width": 80
        }
    },
    {
        "key": "43",
        "label": "因工作不胜任召回调整的第一书记数",
        "value": {
            "title": "因工作不胜任召回调整的第一书记数",
            "dataIndex": "adjusted_num",
            "width": 80
        }
    },
    {
        "key": "44",
        "label": "运转经费",
        "value": {
            "title": "运转经费",
            "dataIndex": "operating_expenses",
            "width": 80
        }
    },
    {
        "key": "45",
        "label": "每村办公经费",
        "value": {
            "title": "每村办公经费",
            "dataIndex": "village_per",
            "width": 80
        }
    },
    {
        "key": "46",
        "label": "党组织书记平均报酬（万元）",
        "value": {
            "title": "党组织书记平均报酬（万元）",
            "dataIndex": "secretary_salary",
            "width": 80
        }
    },
    {
        "key": "47",
        "label": "全部社区工作者年工资总额（万元）",
        "value": {
            "title": "全部社区工作者年工资总额（万元）",
            "dataIndex": "community_workers_salary",
            "width": 80
        }
    },
    {
        "key": "48",
        "label": "社区纳入财政预算的工作经费总额（万元）",
        "value": {
            "title": "社区纳入财政预算的工作经费总额（万元）",
            "dataIndex": "included_financial",
            "width": 80
        }
    },
    {
        "key": "49",
        "label": "全年服务群众专项经费总额（万元）",
        "value": {
            "title": "全年服务群众专项经费总额（万元）",
            "dataIndex": "special_funds_masses",
            "width": 80
        }
    },
    {
        "key": "50",
        "label": "全部专职网格员年工资总额（万元）",
        "value": {
            "title": "全部专职网格员年工资总额（万元）",
            "dataIndex": "zzngzze",
            "width": 80
        }
    },
    {
        "key": "51",
        "label": "社区中的住宅小区总数",
        "value": {
            "title": "社区中的住宅小区总数",
            "dataIndex": "residential_areas",
            "width": 80
        }
    },
    {
        "key": "52",
        "label": "配备专职网格员数",
        "value": {
            "title": "配备专职网格员数",
            "dataIndex": "grid_members",
            "width": 80
        }
    },
    {
        "key": "53",
        "label": "社区工作者人数",
        "value": {
            "title": "社区工作者人数",
            "dataIndex": "community_worker_count",
            "width": 80
        }
    },
    {
        "key": "54",
        "label": "拼音",
        "value": {
            "title": "拼音",
            "dataIndex": "pinyin",
            "width": 70
        }
    },
    {
        "key": "55",
        "label": "党建工作指导员数",
        "value": {
            "title": "党建工作指导员数",
            "dataIndex": "bZT6_10",
            "width": 70
        }
    },
    {
        "key": "56",
        "label": "专业技术人员数",
        "value": {
            "title": "专业技术人员数",
            "dataIndex": "technical_personnel",
            "width": 70
        }
    },
    {
        "key": "57",
        "label": "党员中高级职称人员数",
        "value": {
            "title": "党员中高级职称人员数",
            "dataIndex": "party_senior_title",
            "width": 70
        }
    },
    {
        "key": "58",
        "label": "党政机关工作人员",
        "value": {
            "title": "党政机关工作人员",
            "dataIndex": "b30_a12",
            "width": 70
        }
    },
    {
        "key": "59",
        "label": "创建时间",
        "value": {
            "title": "创建时间",
            "dataIndex": "create_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "60",
        "label": "更新时间",
        "value": {
            "title": "更新时间",
            "dataIndex": "update_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "61",
        "label": "活动场所面积",
        "value": {
            "title": "活动场所面积",
            "dataIndex": "space_area",
            "width": 70
        }
    },
    {
        "key": "62",
        "label": "本年新建或改扩建活动场所数量",
        "value": {
            "title": "本年新建或改扩建活动场所数量",
            "dataIndex": "new_expand_area",
            "width": 70
        }
    },
    {
        "key": "63",
        "label": "村党组织书记中录用公务员数",
        "value": {
            "title": "村党组织书记中录用公务员数",
            "dataIndex": "secretary_party_num",
            "width": 70
        }
    },
    {
        "key": "64",
        "label": "村党组织书记中录用事业编制工作人员数",
        "value": {
            "title": "村党组织书记中录用事业编制工作人员数",
            "dataIndex": "secretary_employ_sybz_num",
            "width": 70
        }
    },
    {
        "key": "65",
        "label": "从村党组织书记中选拔乡镇领导干部人员数",
        "value": {
            "title": "从村党组织书记中选拔乡镇领导干部人员数",
            "dataIndex": "secretary_promoted_num",
            "width": 70
        }
    },
    {
        "key": "66",
        "label": "社区纳入财政预算的工作经费总额",
        "value": {
            "title": "社区纳入财政预算的工作经费总额",
            "dataIndex": "community_money_num",
            "width": 70
        }
    },
    {
        "key": "67",
        "label": "社区全年服务群众专项经费总额",
        "value": {
            "title": "社区全年服务群众专项经费总额",
            "dataIndex": "community_serving_people",
            "width": 70
        }
    },
    {
        "key": "68",
        "label": "是否开展在职党员到社区报到为群众服务",
        "value": {
            "title": "是否开展在职党员到社区报到为群众服务",
            "dataIndex": "community_masses",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "69",
        "label": "是否赋予区域综合管理权、对上级部门派驻机构负责人的人事考核权等权力",
        "value": {
            "title": "是否赋予区域综合管理权、对上级部门派驻机构负责人的人事考核权等权力",
            "dataIndex": "has_examination_power",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "70",
        "label": "是否取消招商引资等职能",
        "value": {
            "title": "是否取消招商引资等职能",
            "dataIndex": "has_cancel_investment_promotion",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "71",
        "label": "是否整合职能统筹设置党政内设工作机构",
        "value": {
            "title": "是否整合职能统筹设置党政内设工作机构",
            "dataIndex": "has_work_mechanism",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "72",
        "label": "是否组织委员是否纳入上一级党委管理",
        "value": {
            "title": "是否组织委员是否纳入上一级党委管理",
            "dataIndex": "has_included_committee",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "73",
        "label": "是否建立党群服务中心",
        "value": {
            "title": "是否建立党群服务中心",
            "dataIndex": "has_group_service_center",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "74",
        "label": "是否实行与驻区单位党建联建共建",
        "value": {
            "title": "是否实行与驻区单位党建联建共建",
            "dataIndex": "has_party_build_endeavor",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "75",
        "label": "社区工作者中专职党务工作者数",
        "value": {
            "title": "社区工作者中专职党务工作者数",
            "dataIndex": "party_worker_num",
            "width": 70
        }
    },
    {
        "key": "76",
        "label": "社区工作者中大专学历以上人员人数",
        "value": {
            "title": "社区工作者中大专学历以上人员人数",
            "dataIndex": "college_degree_num",
            "width": 70
        }
    },
    {
        "key": "77",
        "label": "社区工作者中从机关和街道选派的人数",
        "value": {
            "title": "社区工作者中从机关和街道选派的人数",
            "dataIndex": "offices_and_streets",
            "width": 70
        }
    },
    {
        "key": "78",
        "label": "社区工作者中从退役军人中选聘的人数",
        "value": {
            "title": "社区工作者中从退役军人中选聘的人数",
            "dataIndex": "veterans",
            "width": 70
        }
    },
    {
        "key": "79",
        "label": "社区工作者中录用为公务员的人数",
        "value": {
            "title": "社区工作者中录用为公务员的人数",
            "dataIndex": "civil_servants",
            "width": 70
        }
    },
    {
        "key": "80",
        "label": "社区工作者中选拔进入事业编制的人数",
        "value": {
            "title": "社区工作者中选拔进入事业编制的人数",
            "dataIndex": "establishment",
            "width": 70
        }
    },
    {
        "key": "81",
        "label": "社区工作者中推荐为两代表一委人数",
        "value": {
            "title": "社区工作者中推荐为两代表一委人数",
            "dataIndex": "two_and_one",
            "width": 70
        }
    },
    {
        "key": "82",
        "label": "是否按不低于上年度当地全口径城镇单位就业人员平均工资水平确定报酬",
        "value": {
            "title": "是否按不低于上年度当地全口径城镇单位就业人员平均工资水平确定报酬",
            "dataIndex": "has_lower_social",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "83",
        "label": "社区党组织书记年工资总额",
        "value": {
            "title": "社区党组织书记年工资总额",
            "dataIndex": "community_secretary_salary",
            "width": 70
        }
    },
    {
        "key": "84",
        "label": "党建工作指导员数",
        "value": {
            "title": "党建工作指导员数",
            "dataIndex": "community_building_number",
            "width": 70
        }
    },
    {
        "key": "85",
        "label": "是否建立社区工作者岗位等级序列",
        "value": {
            "title": "是否建立社区工作者岗位等级序列",
            "dataIndex": "has_community_positions",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "86",
        "label": "是否开展在职党员到社区报到为群众服务",
        "value": {
            "title": "是否开展在职党员到社区报到为群众服务",
            "dataIndex": "has_community_report",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "87",
        "label": "社区办公用房面积",
        "value": {
            "title": "社区办公用房面积",
            "dataIndex": "community_office_space",
            "width": 70
        }
    },
    {
        "key": "88",
        "label": "是否实行兼职委员制",
        "value": {
            "title": "是否实行兼职委员制",
            "dataIndex": "has_parttime_system",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "89",
        "label": "是否设立常委会",
        "value": {
            "title": "是否设立常委会",
            "dataIndex": "has_standing_committee",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "90",
        "label": "办校类别",
        "value": {
            "title": "办校类别",
            "dataIndex": "d109_name",
            "width": 70
        }
    },
    {
        "key": "91",
        "label": "是否向地方党委和主管部委专题报告党委领导下的校长负责制执行",
        "value": {
            "title": "是否向地方党委和主管部委专题报告党委领导下的校长负责制执行",
            "dataIndex": "has_report_implementation",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "92",
        "label": "是否修订党委全委会、常委会和校长办公会议事规则",
        "value": {
            "title": "是否修订党委全委会、常委会和校长办公会议事规则",
            "dataIndex": "has_office_procedure",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "93",
        "label": "学校党委书记是否向地方党委述职",
        "value": {
            "title": "学校党委书记是否向地方党委述职",
            "dataIndex": "school_has_reports_local",
            "width": 70
        }
    },
    {
        "key": "94",
        "label": "是否组织开展二级院（系）党组织书记向学校党委述职",
        "value": {
            "title": "是否组织开展二级院（系）党组织书记向学校党委述职",
            "dataIndex": "has_secretary_university_committee",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "95",
        "label": "校长是否中共党员",
        "value": {
            "title": "校长是否中共党员 1是，0否  2 未配备",
            "dataIndex": "has_president_party_member",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "96",
        "label": "校长是否担任党委副书记",
        "value": {
            "title": "校长是否担任党委副书记 1是，0否  2 未配备",
            "dataIndex": "has_deputy_party_secretary",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "97",
        "label": "纪委书记是否担任学校党委常委",
        "value": {
            "title": "纪委书记是否担任学校党委常委",
            "dataIndex": "has_secretary_committee",
            "width": 70
        }
    },
    {
        "key": "98",
        "label": "组织部长是否担任学校党委常委",
        "value": {
            "title": "组织部长是否担任学校党委常委",
            "dataIndex": "has_tissue_committee",
            "width": 70
        }
    },
    {
        "key": "99",
        "label": "宣传部长是否担任学校党委常委",
        "value": {
            "title": "宣传部长是否担任学校党委常委",
            "dataIndex": "has_propaganda_committee",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "100",
        "label": "统战部长是否担任学校党委常委",
        "value": {
            "title": "统战部长是否担任学校党委常委",
            "dataIndex": "has_front_committee",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "101",
        "label": "二级院（系）个数",
        "value": {
            "title": "二级院（系）个数",
            "dataIndex": "secondary_college",
            "width": 70
        }
    },
    {
        "key": "102",
        "label": "二级院（系）中建立党委的",
        "value": {
            "title": "二级院（系）中建立党委的",
            "dataIndex": "secondary_college_committee",
            "width": 70
        }
    },
    {
        "key": "103",
        "label": "二级院（系）中建立总支部的",
        "value": {
            "title": "二级院（系）中建立总支部的",
            "dataIndex": "secondary_college_always_branch",
            "width": 70
        }
    },
    {
        "key": "104",
        "label": "二级院（系）中建立支部的",
        "value": {
            "title": "二级院（系）中建立支部的",
            "dataIndex": "secondary_college_branch",
            "width": 70
        }
    },
    {
        "key": "105",
        "label": "二级院（系）配备1名专职组织员的",
        "value": {
            "title": "二级院（系）配备1名专职组织员的",
            "dataIndex": "secondary_college_with_one",
            "width": 70
        }
    },
    {
        "key": "106",
        "label": "二级院（系）配备2名及以上专职组织员的",
        "value": {
            "title": "二级院（系）配备2名及以上专职组织员的",
            "dataIndex": "secondary_college_greater_two",
            "width": 70
        }
    },
    {
        "key": "107",
        "label": "是否已实行党委领导下的院长负责制",
        "value": {
            "title": "是否已实行党委领导下的院长负责制",
            "dataIndex": "has_responsibility_system",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "108",
        "label": "领导体制是否已写入医院章程的",
        "value": {
            "title": "领导体制是否已写入医院章程的",
            "dataIndex": "ldtzyyzc",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "109",
        "label": "是否党建工作要求写入医院章程",
        "value": {
            "title": "是否党建工作要求写入医院章程",
            "dataIndex": "is_party_work_write",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "110",
        "label": "是否开展基层党建述职评议考核",
        "value": {
            "title": "是否开展基层党建述职评议考核",
            "dataIndex": "is_open_org_assess",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "111",
        "label": "是否党委书记、院长分设",
        "value": {
            "title": "是否党委书记、院长分设",
            "dataIndex": "is_leader_separate",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "112",
        "label": "是否院长系中共党员",
        "value": {
            "title": "是否院长系中共党员",
            "dataIndex": "leader_is_gcdy",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "113",
        "label": "是否院长担任党委副书记",
        "value": {
            "title": "是否院长担任党委副书记",
            "dataIndex": "is_leader_deputy_secretary",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "114",
        "label": "医院内设机构党支部（个）",
        "value": {
            "title": "医院内设机构党支部（个）",
            "dataIndex": "is_set_org_party",
            "width": 70
        }
    },
    {
        "key": "115",
        "label": "党支部书记是内设机构负责人（个）",
        "value": {
            "title": "党支部书记是内设机构负责人（个）",
            "dataIndex": "secretary_is_inside_leader",
            "width": 70
        }
    },
    {
        "key": "116",
        "label": "党支部书记是“双带头人”的（个）",
        "value": {
            "title": "党支部书记是“双带头人”的（个）",
            "dataIndex": "sjsdtr",
            "width": 70
        }
    },
    {
        "key": "117",
        "label": "医院本年度任届期满的内设机构党支部",
        "value": {
            "title": "医院本年度任届期满的内设机构党支部",
            "dataIndex": "internal_institutions",
            "width": 70
        }
    },
    {
        "key": "118",
        "label": "医院本年度任届期满的内设机构党支部且本年换届",
        "value": {
            "title": "医院本年度任届期满的内设机构党支部且本年换届",
            "dataIndex": "internal_institutions_transition",
            "width": 70
        }
    },
    {
        "key": "119",
        "label": "医院本年度发展党员",
        "value": {
            "title": "医院本年度发展党员",
            "dataIndex": "hospitals_develop",
            "width": 70
        }
    },
    {
        "key": "120",
        "label": "医院本年度发展卫生技术人员党员",
        "value": {
            "title": "医院本年度发展卫生技术人员党员",
            "dataIndex": "hospitals_develop_technology",
            "width": 70
        }
    },
    {
        "key": "121",
        "label": "医院本年度列为入党积极分子",
        "value": {
            "title": "医院本年度列为入党积极分子",
            "dataIndex": "hospitals_active",
            "width": 70
        }
    },
    {
        "key": "122",
        "label": "国有经济企业是否建立董事会",
        "value": {
            "title": "国有经济企业是否建立董事会",
            "dataIndex": "has_directors",
            "width": 70
        }
    },
    {
        "key": "123",
        "label": "国有经济企业董事长是否担任书记",
        "value": {
            "title": "国有经济企业董事长是否担任书记",
            "dataIndex": "has_chairman_secretary",
            "width": 70
        }
    },
    {
        "key": "124",
        "label": "国有经济企业党建工作经费是否按上年度工资总额一定比例纳入企业管理费用",
        "value": {
            "title": "国有经济企业党建工作经费是否按上年度工资总额一定比例纳入企业管理费用",
            "dataIndex": "has_proportionate_funding",
            "width": 70
        }
    },
    {
        "key": "125",
        "label": "国有经济企业人事管理和基层党建是否由一个部门抓",
        "value": {
            "title": "国有经济企业人事管理和基层党建是否由一个部门抓",
            "dataIndex": "has_branch_to_catch",
            "width": 70
        }
    },
    {
        "key": "126",
        "label": "国有经济企业人事管理和基层党建是否由一个领导管",
        "value": {
            "title": "国有经济企业人事管理和基层党建是否由一个领导管",
            "dataIndex": "has_by_leader",
            "width": 70
        }
    },
    {
        "key": "127",
        "label": "国有经济企业党务工作人员和经营管理人员是否同职级同待遇",
        "value": {
            "title": "国有经济企业党务工作人员和经营管理人员是否同职级同待遇",
            "dataIndex": "has_same_treatment",
            "width": 70
        }
    },
    {
        "key": "128",
        "label": "国有经济企业是否上市公司",
        "value": {
            "title": "国有经济企业是否上市公司",
            "dataIndex": "has_public_company",
            "width": 70
        }
    },
    {
        "key": "129",
        "label": "国有经济企业党建工作是否写入公司章程",
        "value": {
            "title": "国有经济企业党建工作是否写入公司章程",
            "dataIndex": "has_articles_incorporation",
            "width": 70
        }
    },
    {
        "key": "130",
        "label": "国有经济企业是否党组织研究讨论作为董事会、经理层决策重大问题前置程序",
        "value": {
            "title": "国有经济企业是否党组织研究讨论作为董事会、经理层决策重大问题前置程序",
            "dataIndex": "has_prepositional_procedure",
            "width": 70
        }
    },
    {
        "key": "131",
        "label": "国有经济企业董事长是否由上级企业有关负责人兼任",
        "value": {
            "title": "国有经济企业董事长是否由上级企业有关负责人兼任",
            "dataIndex": "has_responsible_person",
            "width": 70
        }
    },
    {
        "key": "132",
        "label": "国有经济企业分支机构数",
        "value": {
            "title": "国有经济企业分支机构数",
            "dataIndex": "branches",
            "width": 70
        }
    },
    {
        "key": "133",
        "label": "国有经济企业 境外分支机构境外党员数",
        "value": {
            "title": "国有经济企业 境外分支机构境外党员数",
            "dataIndex": "party_members",
            "width": 70
        }
    },
    {
        "key": "134",
        "label": "是否依托组织部门成立的非公党工委",
        "value": {
            "title": "是否依托组织部门成立的非公党工委",
            "dataIndex": "has_non_public_party",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "135",
        "label": "是否设立专门办事机构的非公党工委",
        "value": {
            "title": "是否设立专门办事机构的非公党工委",
            "dataIndex": "has_special_agencies",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "136",
        "label": "行业分类",
        "value": {
            "title": "行业分类",
            "dataIndex": "d114_name",
            "width": 70
        }
    },
    {
        "key": "137",
        "label": "国名经济行业",
        "value": {
            "title": "国名经济行业",
            "dataIndex": "d194_name",
            "width": 70
        }
    },
    {
        "key": "138",
        "label": "生产服务行行业",
        "value": {
            "title": "生产服务行行业",
            "dataIndex": "d195_name",
            "width": 70
        }
    },
    {
        "key": "139",
        "label": "未建立党组织情况",
        "value": {
            "title": "未建立党组织情况",
            "dataIndex": "d05_name",
            "width": 70
        }
    },
    {
        "key": "140",
        "label": "法定代表人兼任党组织书记",
        "value": {
            "title": "法定代表人兼任党组织书记",
            "dataIndex": "legal_is_secretary",
            "width": 70
        }
    },
    {
        "key": "141",
        "label": "联合党支部数",
        "value": {
            "title": "联合党支部数",
            "dataIndex": "party_branches_number",
            "width": 70
        }
    },
    {
        "key": "142",
        "label": "在岗职工中的党员数",
        "value": {
            "title": "在岗职工中的党员数",
            "dataIndex": "in_party_members",
            "width": 70
        }
    },
    {
        "key": "143",
        "label": "技术人员中的党员数",
        "value": {
            "title": "技术人员中的党员数",
            "dataIndex": "technology_party_members",
            "width": 70
        }
    },
    {
        "key": "144",
        "label": "本科以上学历人数",
        "value": {
            "title": "本科以上学历人数",
            "dataIndex": "above_bk_education",
            "width": 70
        }
    },
    {
        "key": "145",
        "label": "研究生以上学历人数",
        "value": {
            "title": "研究生以上学历人数",
            "dataIndex": "above_yjs_education",
            "width": 70
        }
    },
    {
        "key": "146",
        "label": "是否由企业中高层管理人员担任党组织书记的",
        "value": {
            "title": "是否由企业中高层管理人员担任党组织书记的",
            "dataIndex": "has_secretary_high_level",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "147",
        "label": "本年度发展党员",
        "value": {
            "title": "本年度发展党员",
            "dataIndex": "year_develop_mem",
            "width": 70
        }
    },
    {
        "key": "148",
        "label": "主要负责人是否党员",
        "value": {
            "title": "主要负责人是否党员",
            "dataIndex": "has_head_party",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "149",
        "label": "是否企业本级",
        "value": {
            "title": "是否企业本级",
            "dataIndex": "has_firm_level",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "150",
        "label": "是否企业本级党组织书记",
        "value": {
            "title": "是否企业本级党组织书记",
            "dataIndex": "has_level_secretary",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "151",
        "label": "是否法人单位",
        "value": {
            "title": "是否法人单位",
            "dataIndex": "is_legal",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "152",
        "label": "国有经济企业 境外分支机构基层党组织数量",
        "value": {
            "title": "国有经济企业 境外分支机构基层党组织数量",
            "dataIndex": "party_organization_num",
            "width": 70
        }
    },
    {
        "key": "153",
        "label": "国有经济企业 境外分支机构已建立党组织",
        "value": {
            "title": "国有经济企业 境外分支机构已建立党组织",
            "dataIndex": "have_been_established",
            "width": 70
        }
    },
    {
        "key": "154",
        "label": "高校党支部",
        "value": {
            "title": "高校党支部",
            "dataIndex": "college_party_branch",
            "width": 70
        }
    },
    {
        "key": "155",
        "label": "教师党支部",
        "value": {
            "title": "教师党支部",
            "dataIndex": "teacher_party_branch",
            "width": 70
        }
    },
    {
        "key": "156",
        "label": "教师党支部书记是否是“双带头人” ",
        "value": {
            "title": "教师党支部书记是否是“双带头人” ",
            "dataIndex": "has_teachers_double_leaders",
            "width": 70
        }
    },
    {
        "key": "157",
        "label": "学生党支部",
        "value": {
            "title": "学生党支部",
            "dataIndex": "student_party_branch",
            "width": 70
        }
    },
    {
        "key": "158",
        "label": "机关党支部",
        "value": {
            "title": "机关党支部",
            "dataIndex": "organ_party_branch",
            "width": 70
        }
    },
    {
        "key": "159",
        "label": "年度院系本级党组织书记参加培训人次",
        "value": {
            "title": "年度院系本级党组织书记参加培训人次",
            "dataIndex": "year_training",
            "width": 70
        }
    },
    {
        "key": "160",
        "label": "本年度毕业生党员",
        "value": {
            "title": "本年度毕业生党员",
            "dataIndex": "graduate_party_member",
            "width": 70
        }
    },
    {
        "key": "161",
        "label": "本年换届",
        "value": {
            "title": "本年换届",
            "dataIndex": "is_year_org_change",
            "width": 70
        }
    },
    {
        "key": "162",
        "label": "是否落实社区事务准入制度",
        "value": {
            "title": "是否落实社区事务准入制度",
            "dataIndex": "has_community_access",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "163",
        "label": "是否实行“四议两公开”工作法",
        "value": {
            "title": "是否实行“四议两公开”工作法",
            "dataIndex": "has_four_two_open_work",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "164",
        "label": "是否成立村务监督委员会或其他村务监督机构",
        "value": {
            "title": "是否成立村务监督委员会或其他村务监督机构",
            "dataIndex": "has_community_supervisory",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "165",
        "label": "是否建立组织",
        "value": {
            "title": "是否建立组织",
            "dataIndex": "is_create_org",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "166",
        "label": "配备专职党务工作人员",
        "value": {
            "title": "配备专职党务工作人员",
            "dataIndex": "has_major_worker",
            "width": 70
        }
    },
    {
        "key": "167",
        "label": "法定代表人是否为党员",
        "value": {
            "title": "法定代表人是否为党员",
            "dataIndex": "legal_is_member",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "168",
        "label": "是否是分支单位",
        "value": {
            "title": "是否是分支单位",
            "dataIndex": "is_branch",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "169",
        "label": "单位地址",
        "value": {
            "title": "单位地址",
            "dataIndex": "address",
            "width": 70
        }
    },
    {
        "key": "170",
        "label": "单位电话号码",
        "value": {
            "title": "单位电话号码",
            "dataIndex": "telephone",
            "width": 70
        }
    },
    {
        "key": "171",
        "label": "医院等级",
        "value": {
            "title": "医院等级",
            "dataIndex": "d95_name",
            "width": 70
        }
    },
    {
        "key": "172",
        "label": "是否党组",
        "value": {
            "title": "是否党组",
            "dataIndex": "has_party",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "173",
        "label": "现任第一书记名称",
        "value": {
            "title": "现任第一书记名称",
            "dataIndex": "first_secretary_name",
            "width": 70
        }
    },
    {
        "key": "174",
        "label": "街道干部人数",
        "value": {
            "title": "街道干部人数",
            "dataIndex": "street_cadres",
            "width": 70
        }
    },
    {
        "key": "175",
        "label": "街道干部35岁及以下人数",
        "value": {
            "title": "街道干部35岁及以下人数",
            "dataIndex": "age35_below",
            "width": 70
        }
    },
    {
        "key": "176",
        "label": "街道干部36至55岁人数",
        "value": {
            "title": "街道干部36至55岁人数",
            "dataIndex": "age36_to_age55",
            "width": 70
        }
    },
    {
        "key": "177",
        "label": "街道干部56岁及以上人数",
        "value": {
            "title": "街道干部56岁及以上人数",
            "dataIndex": "age_56_above",
            "width": 70
        }
    },
    {
        "key": "178",
        "label": "街道干部大专及以上学历人数",
        "value": {
            "title": "街道干部大专及以上学历人数",
            "dataIndex": "college_degree_above",
            "width": 70
        }
    },
    {
        "key": "179",
        "label": "街道干部高中中专及以下人数",
        "value": {
            "title": "街道干部高中中专及以下人数",
            "dataIndex": "secondary_school_below",
            "width": 70
        }
    },
    {
        "key": "180",
        "label": "街道干部公务员人数",
        "value": {
            "title": "街道干部公务员人数",
            "dataIndex": "street_cadres_civil",
            "width": 70
        }
    },
    {
        "key": "181",
        "label": "街道干部事业单位人数",
        "value": {
            "title": "街道干部事业单位人数",
            "dataIndex": "street_cadres_institutions",
            "width": 70
        }
    },
    {
        "key": "182",
        "label": "街道干部其他身份人数",
        "value": {
            "title": "街道干部其他身份人数",
            "dataIndex": "cadre_other",
            "width": 70
        }
    },
    {
        "key": "183",
        "label": "是否年经营性收入5万元以下薄弱村空壳村",
        "value": {
            "title": "是否年经营性收入5万元以下薄弱村空壳村",
            "dataIndex": "income_less_5w",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "184",
        "label": "是否年经营性收入50-100万元的村",
        "value": {
            "title": "是否年经营性收入50-100万元的村",
            "dataIndex": "income_50w_100w",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "185",
        "label": "是否年经营性收入100万元以上的村",
        "value": {
            "title": "是否年经营性收入100万元以上的村",
            "dataIndex": "income_above_100w",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "186",
        "label": "是否有集体经济组织的村",
        "value": {
            "title": "是否有集体经济组织的村",
            "dataIndex": "has_collective_economy",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "187",
        "label": "是否村党组织书记担任村级集体经济组织负责人的村",
        "value": {
            "title": "是否村党组织书记担任村级集体经济组织负责人的村",
            "dataIndex": "has_economic_village",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "188",
        "label": "到社区报到的在职党员",
        "value": {
            "title": "到社区报到的在职党员",
            "dataIndex": "report_community_member",
            "width": 70
        }
    },
    {
        "key": "189",
        "label": "全体在校学生中研究生人数",
        "value": {
            "title": "全体在校学生中研究生人数",
            "dataIndex": "graduate_student",
            "width": 70
        }
    },
    {
        "key": "190",
        "label": "全体在校学生中大学本科生人数",
        "value": {
            "title": "全体在校学生中大学本科生人数",
            "dataIndex": "undergraduate_student",
            "width": 70
        }
    },
    {
        "key": "191",
        "label": "全体在校学生中大学专科生人数",
        "value": {
            "title": "全体在校学生中大学专科生人数",
            "dataIndex": "junior_college_student",
            "width": 70
        }
    },
    {
        "key": "192",
        "label": "全体在校学生中高中、中技人数",
        "value": {
            "title": "全体在校学生中高中、中技人数",
            "dataIndex": "middle_technical_students",
            "width": 70
        }
    },
    {
        "key": "193",
        "label": "高等学校教师人数",
        "value": {
            "title": "高等学校教师人数",
            "dataIndex": "teachers_institutions_higher",
            "width": 70
        }
    },
    {
        "key": "194",
        "label": "高等学校教师中女性人数",
        "value": {
            "title": "高等学校教师中女性人数",
            "dataIndex": "teachers_higher_women",
            "width": 70
        }
    },
    {
        "key": "195",
        "label": "高等学校教师中35岁及以下人数",
        "value": {
            "title": "高等学校教师中35岁及以下人数",
            "dataIndex": "teachers_age_thirty_five_below",
            "width": 70
        }
    },
    {
        "key": "196",
        "label": "全体在校学生中中专生人数",
        "value": {
            "title": "全体在校学生中中专生人数",
            "dataIndex": "technical_secondary_student",
            "width": 70
        }
    },
    {
        "key": "197",
        "label": "年经营性收入",
        "value": {
            "title": "年经营性收入",
            "dataIndex": "year_amount",
            "width": 70
        }
    },
    {
        "key": "198",
        "label": "党委书记是否兼任行政职务",
        "value": {
            "title": "党委书记是否兼任行政职务",
            "dataIndex": "has_clerk_position",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "199",
        "label": "二级院系配备专职组织员的总数",
        "value": {
            "title": "二级院系配备专职组织员的总数",
            "dataIndex": "secondary_college_with_one_two",
            "width": 70
        }
    },
    {
        "key": "200",
        "label": "有村党组织书记后备人选的行政村",
        "value": {
            "title": "有村党组织书记后备人选的行政村",
            "dataIndex": "has_country_secretary_hbgb",
            "width": 70
        }
    },
    {
        "key": "201",
        "label": "是否兼任企业党组书记",
        "value": {
            "title": "是否兼任企业党组书记",
            "dataIndex": "has_proper_secretary",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "202",
        "label": "是否机关党组",
        "value": {
            "title": "是否机关党组",
            "dataIndex": "has_authority",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "203",
        "label": "是否配备院长",
        "value": {
            "title": "是否配备院长",
            "dataIndex": "is_allocate_dean",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "204",
        "label": "是否配备书记",
        "value": {
            "title": "是否配备书记",
            "dataIndex": "is_allocate_secretary",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "205",
        "label": "是否建立工会",
        "value": {
            "title": "是否建立工会",
            "dataIndex": "has_labour_union",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "206",
        "label": "是否建立共青团组织",
        "value": {
            "title": "是否建立共青团组织",
            "dataIndex": "has_youth_league",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "207",
        "label": "是否建立妇联组织",
        "value": {
            "title": "是否建立妇联组织",
            "dataIndex": "has_womens_federation",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "208",
        "label": "村（社区）类别",
        "value": {
            "title": "村（社区）类别",
            "dataIndex": "d155_name",
            "width": 70
        }
    },
    {
        "key": "209",
        "label": "是否已统一整合设置网格",
        "value": {
            "title": "是否已统一整合设置网格",
            "dataIndex": "has_set_grid",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "210",
        "label": "是否有专职网格员纳入社区工作者管理",
        "value": {
            "title": "是否有专职网格员纳入社区工作者管理",
            "dataIndex": "has_included_grid_worker",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "211",
        "label": "是否党组织软弱涣散村",
        "value": {
            "title": "是否党组织软弱涣散村",
            "dataIndex": "has_org_slack_village",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "212",
        "label": "是否实行与驻区单位党建联建共建",
        "value": {
            "title": "是否实行与驻区单位党建联建共建",
            "dataIndex": "has_joint_units",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "213",
        "label": "农村专业技术协会数量",
        "value": {
            "title": "农村专业技术协会数量",
            "dataIndex": "rural_professional_technical_association_num",
            "width": 70
        }
    },
    {
        "key": "214",
        "label": "农民专业合作社数量",
        "value": {
            "title": "农民专业合作社数量",
            "dataIndex": "farmer_specialized_cooperatives_num",
            "width": 70
        }
    },
    {
        "key": "215",
        "label": "家庭农场数量",
        "value": {
            "title": "家庭农场数量",
            "dataIndex": "family_farm_num",
            "width": 70
        }
    },
    {
        "key": "216",
        "label": "本年度高校党支部书记参加培训人次",
        "value": {
            "title": "本年度高校党支部书记参加培训人次",
            "dataIndex": "year_branch_training",
            "width": 70
        }
    },
    {
        "key": "217",
        "label": "尚未转出组织关系的",
        "value": {
            "title": "尚未转出组织关系的",
            "dataIndex": "org_relationship_not_transferred",
            "width": 70
        }
    },
    {
        "key": "218",
        "label": "是否将党建工作经费纳入管理费列支、税前扣除",
        "value": {
            "title": "是否将党建工作经费纳入管理费列支、税前扣除",
            "dataIndex": "has_working_expenses",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "219",
        "label": "与业务主管单位关系",
        "value": {
            "title": "与业务主管单位关系",
            "dataIndex": "d159_name",
            "width": 70
        }
    },
    {
        "key": "220",
        "label": "是否集体经济收入5万元以下薄弱村空壳村",
        "value": {
            "title": "是否集体经济收入5万元以下薄弱村空壳村",
            "dataIndex": "all_income_less_5w",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "221",
        "label": "集体经济情况收入情况",
        "value": {
            "title": "集体经济情况收入情况",
            "dataIndex": "year_amount_all",
            "width": 70
        }
    },
    {
        "key": "222",
        "label": "本级企业是否省内企业",
        "value": {
            "title": "本级企业是否省内企业",
            "dataIndex": "has_industry_province",
            "width": 70
        }
    },
    {
        "key": "223",
        "label": "是否年经营收益5万元以下村",
        "value": {
            "title": "是否年经营收益5万元以下村",
            "dataIndex": "earnings_less_5w",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "224",
        "label": "是否年经营性收益50-100万元的村",
        "value": {
            "title": "是否年经营性收益50-100万元的村",
            "dataIndex": "earnings_50w_100w",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "225",
        "label": "是否年经营收益100万元以上的村",
        "value": {
            "title": "是否年经营收益100万元以上的村",
            "dataIndex": "earnings_above_100w",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "226",
        "label": "当年经营收益金额",
        "value": {
            "title": "当年经营收益金额",
            "dataIndex": "earnings_amount",
            "width": 70
        }
    },
    {
        "key": "227",
        "label": "是否已修订党组织会议、院（所）长办公会议议事规则",
        "value": {
            "title": "是否已修订党组织会议、院（所）长办公会议议事规则",
            "dataIndex": "ysgz_is",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "228",
        "label": "是否已建立学校党组织书记和校长定期沟通制度的",
        "value": {
            "title": "是否已建立学校党组织书记和校长定期沟通制度的",
            "dataIndex": "yjldqgt_is",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "229",
        "label": "是否已设立党务工作机构的",
        "value": {
            "title": "是否已设立党务工作机构的",
            "dataIndex": "ysldwgzjg_is",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "230",
        "label": "是否已配备专职党务工作人员的",
        "value": {
            "title": "是否已配备专职党务工作人员的",
            "dataIndex": "ypbzzdwgzry_is",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "231",
        "label": "是否建立履职事项清单",
        "value": {
            "title": "是否建立履职事项清单",
            "dataIndex": "has_performed_detail",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "232",
        "label": "境外分支机构有党员的机构",
        "value": {
            "title": "境外分支机构有党员的机构",
            "dataIndex": "exist_member_branches",
            "width": 70
        }
    },
    {
        "key": "233",
        "label": "境外分支机构有党员3人以上、未建立党组织",
        "value": {
            "title": "境外分支机构有党员3人以上、未建立党组织",
            "dataIndex": "three_member_no_org_branches",
            "width": 70
        }
    },
    {
        "key": "234",
        "label": "划分网格数",
        "value": {
            "title": "划分网格数",
            "dataIndex": "grids",
            "width": 70
        }
    },
    {
        "key": "235",
        "label": "在居民住宅小区、楼院等划分的网格数",
        "value": {
            "title": "在居民住宅小区、楼院等划分的网格数姓名",
            "dataIndex": "jmzzlyWgs",
            "width": 70
        }
    },
    {
        "key": "236",
        "label": "在居民住宅小区、楼院等划分已建立党组织的网格数",
        "value": {
            "title": "在居民住宅小区、楼院等划分已建立党组织的网格数",
            "dataIndex": "jmzzlyDzWgs",
            "width": 70
        }
    },
    {
        "key": "237",
        "label": "在商务楼宇、商圈市场等单独划定的网格数",
        "value": {
            "title": "在商务楼宇、商圈市场等单独划定的网格数",
            "dataIndex": "swsqWgs",
            "width": 70
        }
    },
    {
        "key": "238",
        "label": "在商务楼宇、商圈市场等单独划定已建立党组织的网格数",
        "value": {
            "title": "在商务楼宇、商圈市场等单独划定已建立党组织的网格数",
            "dataIndex": "swsqDzWgs",
            "width": 70
        }
    },
    {
        "key": "239",
        "label": "街道（乡镇）领导班子成员直接联系的网格数",
        "value": {
            "title": "街道（乡镇）领导班子成员直接联系的网格数",
            "dataIndex": "jdldWgs",
            "width": 70
        }
    },
    {
        "key": "240",
        "label": "由社区工作者担任的专职网格员数",
        "value": {
            "title": "由社区工作者担任的专职网格员数",
            "dataIndex": "zzWgys",
            "width": 70
        }
    },
    {
        "key": "241",
        "label": "全部专职网格员年工资总额（万元）",
        "value": {
            "title": "全部专职网格员年工资总额（万元）",
            "dataIndex": "zzNgzze",
            "width": 70
        }
    },
    {
        "key": "242",
        "label": "配备兼职网格员数",
        "value": {
            "title": "配备兼职网格员数",
            "dataIndex": "jzWgys",
            "width": 70
        }
    },
    {
        "key": "243",
        "label": "物业管理的小区数",
        "value": {
            "title": "物业管理的小区数",
            "dataIndex": "tube_plots",
            "width": 70
        }
    },
    {
        "key": "244",
        "label": "成立党组织的物业企业数",
        "value": {
            "title": "成立党组织的物业企业数",
            "dataIndex": "organization_companies",
            "width": 70
        }
    },
    {
        "key": "245",
        "label": "成立业委会的小区数",
        "value": {
            "title": "成立业委会的小区数",
            "dataIndex": "industry_authority_community",
            "width": 70
        }
    },
    {
        "key": "246",
        "label": "成立业委会党组织数",
        "value": {
            "title": "成立业委会党组织数",
            "dataIndex": "industry_authority_organization",
            "width": 70
        }
    },
    {
        "key": "247",
        "label": "建立社区党组织领导下，居委会、物业企业、业委会“三方共议”“双向进入”的小区数",
        "value": {
            "title": "建立社区党组织领导下，居委会、物业企业、业委会“三方共议”“双向进入”的小区数",
            "dataIndex": "three_parties_communities",
            "width": 70
        }
    },
    {
        "key": "248",
        "label": "有经济控制的企业境外分支共有员工数",
        "value": {
            "title": "有经济控制的企业境外分支共有员工数",
            "dataIndex": "branch_employee",
            "width": 70
        }
    },
    {
        "key": "249",
        "label": "有经济控制的企业境外分支其中国内派出员工",
        "value": {
            "title": "有经济控制的企业境外分支其中国内派出员工",
            "dataIndex": "branch_employee_home",
            "width": 70
        }
    },
    {
        "key": "250",
        "label": "境外分支机构已建立的党委数",
        "value": {
            "title": "境外分支机构已建立的党委数",
            "dataIndex": "branch_committee",
            "width": 70
        }
    },
    {
        "key": "251",
        "label": "境外分支机构已建立的总支部数",
        "value": {
            "title": "境外分支机构已建立的总支部数",
            "dataIndex": "branch_general",
            "width": 70
        }
    },
    {
        "key": "252",
        "label": "境外分支机构已建立的支部数",
        "value": {
            "title": "境外分支机构已建立的支部数",
            "dataIndex": "branch_node",
            "width": 70
        }
    }
]
export const newTableDevMem = [
    {
        "key": "1",
        "label": "姓名",
        "value": {
            "title": "姓名",
            "dataIndex": "name",
            "width": 70
        }
    },
    {
        "key": "2",
        "label": "性别",
        "value": {
            "title": "性别",
            "dataIndex": "sex_name",
            "width": 70
        }
    },
    {
        "key": "3",
        "label": "身份证号码",
        "value": {
            "title": "身份证号码",
            "dataIndex": "idcard",
            "width": 80,
            render: (text, record) => {
                if (typeof text === 'string' && !_isEmpty(text)) {
                    let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
                    if (text.indexOf('*') > 0) {
                        return text;
                    }
                    return <span>{newVal}</span>;
                } else {
                    return '';
                }
            },
        }
    },
    {
        "key": "4",
        "label": "年龄",
        "value": {
            "title": "年龄",
            "dataIndex": "age",
            "width": 80
        }
    },
    {
        "key": "5",
        "label": "电话",
        "value": {
            "title": "电话",
            "dataIndex": "phone",
            "width": 80
        }
    },
    {
        "key": "6",
        "label": "单位名称",
        "value": {
            "title": "单位名称",
            "dataIndex": "unit_name",
            "width": 80
        }
    },
    {
        "key": "7",
        "label": "单位类别",
        "value": {
            "title": "单位类别",
            "dataIndex": "d04_name",
            "width": 80
        }
    },
    {
        "key": "8",
        "label": "民族",
        "value": {
            "title": "民族",
            "dataIndex": "d06_name",
            "width": 80
        }
    },
    {
        "key": "9",
        "label": "学历",
        "value": {
            "title": "学历",
            "dataIndex": "d07_name",
            "width": 80
        }
    },
    {
        "key": "10",
        "label": "党员类型",
        "value": {
            "title": "党员类型",
            "dataIndex": "d08_name",
            "width": 80
        }
    },
    {
        "key": "11",
        "label": "工作岗位",
        "value": {
            "title": "工作岗位",
            "dataIndex": "d09_name",
            "width": 80
        }
    },
    {
        "key": "12",
        "label": "新社会阶层",
        "value": {
            "title": "新社会阶层",
            "dataIndex": "d20_name",
            "width": 80
        }
    },
    {
        "key": "13",
        "label": "一线情况",
        "value": {
            "title": "一线情况",
            "dataIndex": "d21_name",
            "width": 80
        }
    },
    {
        "key": "14",
        "label": "籍贯",
        "value": {
            "title": "籍贯",
            "dataIndex": "d48_name",
            "width": 80
        }
    },
    {
        "key": "15",
        "label": "拼音",
        "value": {
            "title": "拼音",
            "dataIndex": "pinyin",
            "width": 80
        }
    },
    {
        "key": "16",
        "label": "出生日期",
        "value": {
            "title": "出生日期",
            "dataIndex": "birthday",
            "width": 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "17",
        "label": "是否劳务派遣",
        "value": {
            "title": "是否劳务派遣",
            "dataIndex": "is_dispatch",
            "width": 80
        }
    },
    {
        "key": "18",
        "label": "聘任专业技术职务名称",
        "value": {
            "title": "聘任专业技术职务名称",
            "dataIndex": "d19_name",
            "width": 80
        }
    },
    {
        "key": "19",
        "label": "政治面貌",
        "value": {
            "title": "政治面貌",
            "dataIndex": "politics_name",
            "width": 80
        }
    },
    {
        "key": "20",
        "label": "是否死亡",
        "value": {
            "title": "是否死亡",
            "dataIndex": "has_dead",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "21",
        "label": "死亡时间",
        "value": {
            "title": "死亡时间",
            "dataIndex": "dead_time",
            "width": 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "22",
        "label": "追认时间",
        "value": {
            "title": "追认时间",
            "dataIndex": "ratification_time",
            "width": 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "23",
        "label": "创建时间",
        "value": {
            "title": "创建时间",
            "dataIndex": "create_time",
            "width": 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "24",
        "label": "更新时间",
        "value": {
            "title": "更新时间",
            "dataIndex": "update_time",
            "width": 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "25",
        "label": "加入党组织类别",
        "value": {
            "title": "加入党组织类别",
            "dataIndex": "d27_name",
            "width": 80
        }
    },
    {
        "key": "26",
        "label": "是否本年申请",
        "value": {
            "title": "是否本年申请",
            "dataIndex": "apply_date_curr_year",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "27",
        "label": "人事关系所在单位类别",
        "value": {
            "title": "人事关系所在单位类别",
            "dataIndex": "personnel_d04_name",
            "width": 80
        }
    },
    {
        "key": "28",
        "label": "人事关系是否在党组织关联单位内",
        "value": {
            "title": "人事关系是否在党组织关联单位内",
            "dataIndex": "has_unit_statistics",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "29",
        "label": "人事关系所在单位",
        "value": {
            "title": "人事关系所在单位",
            "dataIndex": "unit_information",
            "width": 80
        }
    },
    {
        "key": "30",
        "label": "人事关系所在单位是否省内单位",
        "value": {
            "title": "人事关系所在单位是否省内单位",
            "dataIndex": "has_unit_province",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "31",
        "label": "乡镇，街道情况",
        "value": {
            "title": "乡镇，街道情况",
            "dataIndex": "is_towns",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '乡' : (text == 2 ? "镇" : (text == 3 ? '街道' : ''))}</span>;
            }
        }
    },
    {
        "key": "32",
        "label": "行政村，城市社区，乡镇社区情况",
        "value": {
            "title": "行政村，城市社区，乡镇社区情况",
            "dataIndex": "is_community",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '行政村' : (text == 2 ? '城市社区' : (text == 3 ? '乡镇社区' : ''))}</span>;
            }
        }
    },
    {
        "key": "33",
        "label": "是否农民工",
        "value": {
            "title": "是否农民工",
            "dataIndex": "is_farmer",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "34",
        "label": "发展对象年度",
        "value": {
            "title": "发展对象年度",
            "dataIndex": "topre_join_org_date_year",
            "width": 80
        }
    },
    {
        "key": "35",
        "label": "是否在关系转接中",
        "value": {
            "title": "是否在关系转接中",
            "dataIndex": "is_transfer",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "36",
        "label": "国民经济行业",
        "value": {
            "title": "国民经济行业",
            "dataIndex": "d194_name",
            "width": 80
        }
    },
    {
        "key": "37",
        "label": "生产性服务行业",
        "value": {
            "title": "生产性服务行业",
            "dataIndex": "d195_name",
            "width": 80
        }
    }
]

export const newTableFlowMem = [
    {
        "key": "1",
        "label": "姓名",
        "value": {
            "title": "姓名",
            "dataIndex": "mem_name",
            "width": 70
        }
    },
    {
        "key": "2",
        "label": "性别",
        "value": {
            "title": "性别",
            "dataIndex": "gender_name",
            "width": 70
        }
    },
    {
        "key": "3",
        "label": "身份证号码",
        "value": {
            "title": "身份证号码",
            "dataIndex": "idcard",
            "width": 80,
            render: (text, record) => {
                if (typeof text === 'string' && !_isEmpty(text)) {
                    let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
                    if (text.indexOf('*') > 0) {
                        return text;
                    }
                    return <span>{newVal}</span>;
                } else {
                    return '';
                }
            },
        }
    },
    {
        "key": "4",
        "label": "出生日期",
        "value": {
            "title": "出生日期",
            "dataIndex": "birthday",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "5",
        "label": "组织单位类别",
        "value": {
            "title": "组织单位类别",
            "dataIndex": "d04_name",
            "width": 80
        }
    },
    {
        "key": "6",
        "label": "工作岗位",
        "value": {
            "title": "工作岗位",
            "dataIndex": "d09_code",
            "width": 80
        }
    },
    {
        "key": "7",
        "label": "经济控制类型",
        "value": {
            "title": "经济控制类型",
            "dataIndex": "d16_name",
            "width": 80
        }
    },
    {
        "key": "8",
        "label": "新社会阶层",
        "value": {
            "title": "新社会阶层",
            "dataIndex": "d20_code",
            "width": 80
        }
    },
    {
        "key": "9",
        "label": "流动类型",
        "value": {
            "title": "流动类型",
            "dataIndex": "is_prov_out_name",
            "width": 80
        }
    },
    {
        "key": "10",
        "label": "流出或流入日期",
        "value": {
            "title": "流出或流入日期",
            "dataIndex": "outflow_date",
            "width": 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "11",
        "label": "流出或流入类型代码",
        "value": {
            "title": "流出或流入类型代码",
            "dataIndex": "outflow_type_name",
            "width": 80
        }
    },
    {
        "key": "12",
        "label": "流出或流入地党组织",
        "value": {
            "title": "流出或流入地党组织",
            "dataIndex": "outflow_org_name",
            "width": 80
        }
    },
    {
        "key": "13",
        "label": "流出或流入原因",
        "value": {
            "title": "流出或流入原因",
            "dataIndex": "outflow_reason_name",
            "width": 80
        }
    },
    {
        "key": "14",
        "label": "流出或流入单位类型",
        "value": {
            "title": "流出或流入单位类型",
            "dataIndex": "outflow_unit_type_name",
            "width": 80
        }
    },
    {
        "key": "15",
        "label": "流出或流入地",
        "value": {
            "title": "流出或流入地",
            "dataIndex": "outflow_area_name",
            "width": 80
        }
    },
    {
        "key": "16",
        "label": "流出或流入党组织联系人",
        "value": {
            "title": "流出或流入党组织联系人",
            "dataIndex": "outflow_org_linkman",
            "width": 80
        }
    },
    {
        "key": "17",
        "label": "流出或流入党组织联系方式",
        "value": {
            "title": "流出或流入党组织联系方式",
            "dataIndex": "outflow_org_phone",
            "width": 80
        }
    },
    {
        "key": "18",
        "label": "流出或流入",
        "value": {
            "title": "流出或流入",
            "dataIndex": "flow_add_type",
            "width": 80
        }
    },
    {
        "key": "19",
        "label": "党员学历",
        "value": {
            "title": "党员学历",
            "dataIndex": "outflow_edu_name",
            "width": 80
        }
    },
    {
        "key": "20",
        "label": "原职业(工作岗位)",
        "value": {
            "title": "原职业(工作岗位)",
            "dataIndex": "outflow_job_name",
            "width": 80
        }
    },
    {
        "key": "21",
        "label": "组织单位",
        "value": {
            "title": "组织单位",
            "dataIndex": "unit_name",
            "width": 80
        }
    },
    {
        "key": "22",
        "label": "人员类别",
        "value": {
            "title": "人员类别",
            "dataIndex": "mem_type_name",
            "width": 80
        }
    },
    {
        "key": "23",
        "label": "民族",
        "value": {
            "title": "民族",
            "dataIndex": "nation_name",
            "width": 80
        }
    },
    {
        "key": "24",
        "label": "人员所在组织",
        "value": {
            "title": "人员所在组织",
            "dataIndex": "mem_org_name",
            "width": 80
        }
    },
    {
        "key": "25",
        "label": "是否发放流动党员活动证",
        "value": {
            "title": "是否发放流动党员活动证",
            "dataIndex": "is_hold",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "26",
        "label": "是否已明确流入党组织",
        "value": {
            "title": "是否已明确流入党组织",
            "dataIndex": "is_explicit_inflow_org",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "27",
        "label": "流动类型",
        "value": {
            "title": "流动类型",
            "dataIndex": "is_prov_out",
            "width": 80, render: (text, record) => {
                return <span>{text == 1 ? '跨省（直辖市）流动' : '省内跨市（地）流动'}</span>;
            }
        }
    },
    {
        "key": "28",
        "label": "流出或流入原因",
        "value": {
            "title": "流出或流入原因",
            "dataIndex": "outflow_reason",
            "width": 80
        }
    },
    {
        "key": "29",
        "label": "流出或流入单位名称",
        "value": {
            "title": "流出或流入单位名称",
            "dataIndex": "outflow_unit_name",
            "width": 80
        }
    },
    {
        "key": "30",
        "label": "流回日期",
        "value": {
            "title": "流回日期",
            "dataIndex": "backflow_date",
            "width": 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "31",
        "label": "流回原因",
        "value": {
            "title": "流回原因",
            "dataIndex": "backflow_reason",
            "width": 80
        }
    },
    {
        "key": "32",
        "label": "流回类型",
        "value": {
            "title": "流回类型",
            "dataIndex": "backflow_type_name",
            "width": 80
        }
    },
    {
        "key": "33",
        "label": "流回前党组织层级码名称",
        "value": {
            "title": "流回前党组织层级码名称",
            "dataIndex": "backflow_org_name",
            "width": 80
        }
    },
    {
        "key": "34",
        "label": "流回前单位",
        "value": {
            "title": "流回前单位",
            "dataIndex": "backflow_unit_name",
            "width": 80
        }
    },
    {
        "key": "35",
        "label": "流回前单位类型",
        "value": {
            "title": "流回前单位类型",
            "dataIndex": "backflow_unit_type_name",
            "width": 80
        }
    },
    {
        "key": "36",
        "label": "流回前党组织联系人",
        "value": {
            "title": "流回前党组织联系人",
            "dataIndex": "backflow_org_linkman",
            "width": 80
        }
    },
    {
        "key": "37",
        "label": "流回前党组织联系方式",
        "value": {
            "title": "流回前党组织联系方式",
            "dataIndex": "backflow_org_phone",
            "width": 80
        }
    },
    {
        "key": "38",
        "label": "流回前党员工作岗位",
        "value": {
            "title": "流回前党员工作岗位",
            "dataIndex": "backflow_job_name",
            "width": 80
        }
    },
    {
        "key": "39",
        "label": "状态",
        "value": {
            "title": "状态（1： 流动中，2：流回）",
            "dataIndex": "flow_status",
            "width": 80
        }
    },
    {
        "key": "40",
        "label": "创建时间",
        "value": {
            "title": "创建时间",
            "dataIndex": "create_time",
            "width": 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "41",
        "label": "更新时间",
        "value": {
            "title": "更新时间",
            "dataIndex": "update_time",
            "width": 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "42",
        "label": "新社会阶层类型名称",
        "value": {
            "title": "新社会阶层类型名称",
            "dataIndex": "stratum_type_name",
            "width": 80
        }
    },
    {
        "key": "43",
        "label": "匹配情况",
        "value": {
            "title": "匹配情况",
            "dataIndex": "match_situation",
            "width": 80
        }
    },
    {
        "key": "44",
        "label": "入党时间",
        "value": {
            "title": "入党时间",
            "dataIndex": "join_org_date",
            "width": 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    }
]

export const newTextRewardMem = [
    {
        "key": "1",
        "label": "姓名",
        "value": {
            "title": "姓名",
            "dataIndex": "name",
            "width": 70
        }
    },
    {
        "key": "2",
        "label": "年龄",
        "value": {
            "title": "年龄",
            "dataIndex": "age",
            "width": 70
        }
    },
    {
        "key": "3",
        "label": "预备党员日期",
        "value": {
            "title": "预备党员日期",
            "dataIndex": "join_org_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "4",
        "label": "转为正式党员日期",
        "value": {
            "title": "转为正式党员日期",
            "dataIndex": "full_member_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "5",
        "label": "党员受奖/惩名称",
        "value": {
            "title": "党员受奖/惩名称",
            "dataIndex": "d029_name",
            "width": 70
        }
    },
    {
        "key": "6",
        "label": "奖/惩原因名称",
        "value": {
            "title": "奖/惩原因名称",
            "dataIndex": "d030_name",
            "width": 70
        }
    },
    {
        "key": "7",
        "label": "党员奖/惩批准日期",
        "value": {
            "title": "党员奖/惩批准日期",
            "dataIndex": "start_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "8",
        "label": "创建时间",
        "value": {
            "title": "创建时间",
            "dataIndex": "create_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "9",
        "label": "更新时间",
        "value": {
            "title": "更新时间",
            "dataIndex": "update_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    }
]

export const newrewardTextColumn = [
    {
        key: '1',
        label: '党组织名称',
        value: { title: '党组织名称', dataIndex: 'orgName', width: 70 },
    },
    { key: '2', label: '处分类型', value: { title: '处分类型', dataIndex: 'd42Name', width: 70 } },
    {
        key: '3',
        label: '处分时间',
        value: {
            title: '处分时间',
            dataIndex: 'startDate',
            width: 70,
            render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        },
    },
    { key: '4', label: '处分原因', value: { title: '处分原因', dataIndex: 'remark', width: 70 } },
];

export const newTextOrgSlack = [
    {
        "key": "1",
        "label": "组织名称",
        "value": {
            "title": "组织名称",
            "dataIndex": "name",
            "width": 70
        }
    },
    {
        "key": "2",
        "label": "整顿年度",
        "value": {
            "title": "整顿年度",
            "dataIndex": "year",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY')}</span>;
            },
        }
    },
    {
        "key": "3",
        "label": "整顿开始时间",
        "value": {
            "title": "整顿开始时间",
            "dataIndex": "neaten_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "4",
        "label": "整备结束时间",
        "value": {
            "title": "整备结束时间",
            "dataIndex": "neaten_endtime",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "5",
        "label": "是否整顿",
        "value": {
            "title": "是否整顿",
            "dataIndex": "hasNeaten",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "6",
        "label": "属于党组织班子配备不齐、书记长期缺职、工作处于停滞状态的",
        "value": {
            "title": "属于党组织班子配备不齐、书记长期缺职、工作处于停滞状态的",
            "dataIndex": "d74_code1",
            "width": 70
        }
    },
    {
        "key": "7",
        "label": "属于党组织书记不胜任现职、工作不在状态、严重影响班子整体战斗力的",
        "value": {
            "title": "属于党组织书记不胜任现职、工作不在状态、严重影响班子整体战斗力的",
            "dataIndex": "d74_code2",
            "width": 70
        }
    },
    {
        "key": "8",
        "label": "属于班子不团结、内耗严重、工作不能正常开展的",
        "value": {
            "title": "属于班子不团结、内耗严重、工作不能正常开展的",
            "dataIndex": "d74_code3",
            "width": 70
        }
    },
    {
        "key": "9",
        "label": "属于组织制度形同虚设、不开展党组织活动的",
        "value": {
            "title": "属于组织制度形同虚设、不开展党组织活动的",
            "dataIndex": "d74_code4",
            "width": 70
        }
    },
    {
        "key": "10",
        "label": "属于换届选举拉票贿选问题突出的",
        "value": {
            "title": "属于换届选举拉票贿选问题突出的",
            "dataIndex": "d74_code5",
            "width": 70
        }
    },
    {
        "key": "11",
        "label": "属于宗族和黑恶势力干扰渗透严重的",
        "value": {
            "title": "属于宗族和黑恶势力干扰渗透严重的",
            "dataIndex": "d74_code6",
            "width": 70
        }
    },
    {
        "key": "12",
        "label": "属于宗教势力干扰渗透的",
        "value": {
            "title": "属于宗教势力干扰渗透的",
            "dataIndex": "d74_code7",
            "width": 70
        }
    },
    {
        "key": "13",
        "label": "属于村务居务财务公开和民主管理混乱的",
        "value": {
            "title": "属于村务居务财务公开和民主管理混乱的",
            "dataIndex": "d74_code8",
            "width": 70
        }
    },
    {
        "key": "14",
        "label": "属于社会治安问题和信访矛盾纠纷集中的",
        "value": {
            "title": "属于社会治安问题和信访矛盾纠纷集中的",
            "dataIndex": "d74_code9",
            "width": 70
        }
    },
    {
        "key": "15",
        "label": "属于无固定办公活动场所及便民服务设施的",
        "value": {
            "title": "属于无固定办公活动场所及便民服务设施的",
            "dataIndex": "d74_code10",
            "width": 70
        }
    },
    {
        "key": "16",
        "label": "属于党组织服务意识差、服务能力弱、群众意见大的",
        "value": {
            "title": "属于党组织服务意识差、服务能力弱、群众意见大的",
            "dataIndex": "d74_code11",
            "width": 70
        }
    },
    {
        "key": "17",
        "label": "年初缺配的软弱涣散基层党组织书记数（人）",
        "value": {
            "title": "年初缺配的软弱涣散基层党组织书记数（人）",
            "dataIndex": "early_qp_secretary",
            "width": 70
        }
    },
    {
        "key": "18",
        "label": "年初需调整的软弱涣散基层党组织书记数（人）",
        "value": {
            "title": "年初需调整的软弱涣散基层党组织书记数（人）",
            "dataIndex": "early_tz_secretary",
            "width": 70
        }
    },
    {
        "key": "19",
        "label": "本年度已选配",
        "value": {
            "title": "本年度已选配",
            "dataIndex": "has_year_selected",
            "width": 70
        }
    },
    {
        "key": "20",
        "label": "本年度已调整",
        "value": {
            "title": "本年度已调整",
            "dataIndex": "has_year_adjust",
            "width": 70
        }
    },
    {
        "key": "21",
        "label": "培训软弱涣散基层党组织书记（人）",
        "value": {
            "title": "培训软弱涣散基层党组织书记（人）",
            "dataIndex": "train_secretary",
            "width": 70
        }
    },
    {
        "key": "22",
        "label": "联村的县级领导班子成员（人）",
        "value": {
            "title": "联村的县级领导班子成员（人）",
            "dataIndex": "lc_county_level_leader",
            "width": 70
        }
    },
    {
        "key": "23",
        "label": "包村的县级领导班子成员（人）",
        "value": {
            "title": "包村的县级领导班子成员（人）",
            "dataIndex": "bc_county_level_leader",
            "width": 70
        }
    },
    {
        "key": "24",
        "label": "选派第一书记（人）",
        "value": {
            "title": "选派第一书记（人）",
            "dataIndex": "first_secretary",
            "width": 70
        }
    },
    {
        "key": "25",
        "label": "结对帮扶的县级及以上机关单位（人）",
        "value": {
            "title": "结对帮扶的县级及以上机关单位（人）",
            "dataIndex": "jdbf_county_level_unit",
            "width": 70
        }
    },
    {
        "key": "26",
        "label": "省市两级挂牌督办的村（个）",
        "value": {
            "title": "省市两级挂牌督办的村（个）",
            "dataIndex": "two_levels_listed",
            "width": 70
        }
    },
    {
        "key": "27",
        "label": "开展专项整治（项）",
        "value": {
            "title": "开展专项整治（项）",
            "dataIndex": "special_rectification",
            "width": 70
        }
    },
    {
        "key": "28",
        "label": "解决各类问题（个）",
        "value": {
            "title": "解决各类问题（个）",
            "dataIndex": "solve_problems",
            "width": 70
        }
    },
    {
        "key": "29",
        "label": "查处违纪违法行为（例）",
        "value": {
            "title": "查处违纪违法行为（例）",
            "dataIndex": "look_into_laws",
            "width": 70
        }
    },
    {
        "key": "30",
        "label": "单位类别",
        "value": {
            "title": "单位类别",
            "dataIndex": "d04_name",
            "width": 70
        }
    },
    {
        "key": "31",
        "label": "单位名称",
        "value": {
            "title": "单位名称",
            "dataIndex": "unit_name",
            "width": 70
        }
    },
    {
        "key": "32",
        "label": "是否整顿",
        "value": {
            "title": "是否整顿",
            "dataIndex": "has_neaten",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "33",
        "label": "创建时间",
        "value": {
            "title": "创建时间",
            "dataIndex": "create_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "34",
        "label": "更新时间",
        "value": {
            "title": "更新时间",
            "dataIndex": "update_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    }
]

export const newTextOrgRecognition = [
    { key: '1', label: '组织名称', value: { title: '组织名称', dataIndex: 'org_name', width: 70 } },
    { key: '2', label: '组织类别', value: { title: '组织类别', dataIndex: 'd01_code', width: 70 } },
    { key: '3', label: '人数', value: { title: '人数', dataIndex: 'number', width: 70 } },
    {
        key: '4',
        label: '表彰对象',
        value: { title: '表彰对象', dataIndex: 'recognition_object', width: 70 },
    },
    {
        key: '5',
        label: '表彰级别',
        value: { title: '表彰级别', dataIndex: 'recognition_level', width: 70 },
    },
    {
        key: '6',
        label: '表彰类型',
        value: { title: '表彰类型', dataIndex: 'recognition_type', width: 70 },
    },
    {
        key: '7',
        label: '党委(总支部、支部)书记优秀共产党员名数',
        value: {
            title: '党委(总支部、支部)书记优秀共产党员名数',
            dataIndex: 'committee_party',
            width: 70,
        },
    },
    {
        key: '8',
        label: '党委(总支部、支部)书记优秀党务工作者名数',
        value: {
            title: '党委(总支部、支部)书记优秀党务工作者名数',
            dataIndex: 'committee_worker',
            width: 70,
        },
    },
    {
        key: '9',
        label: '生活困难优秀共产党员名数',
        value: { title: '生活困难优秀共产党员名数', dataIndex: 'difficult_party', width: 70 },
    },
    {
        key: '10',
        label: '生活困难优秀党务工作者名数',
        value: { title: '生活困难优秀党务工作者名数', dataIndex: 'difficult_worker', width: 70 },
    },
    {
        key: '11',
        label: '追授优秀共产党员名数',
        value: { title: '追授优秀共产党员名数', dataIndex: 'add_good_party', width: 70 },
    },
    {
        key: '12',
        label: '建党50周年情况（数）',
        value: { title: '建党50周年情况（数）', dataIndex: 'anniversary_situation', width: 70 },
    },
    { key: '13', label: '表彰年度', value: { title: '表彰年度', dataIndex: 'year', width: 70 } },
    { key: '14', label: '表彰文件号', value: { title: '表彰文件号', dataIndex: 'file_no', width: 70 } },
];

export const newTextOrgIndustry = [
    {
        key: '1',
        label: '组织名称',
        value: { title: '组织名称', dataIndex: 'industry_org_name', width: 70 },
    },
    {
        key: '2',
        label: '组织类别',
        value: { title: '组织类别', dataIndex: 'industry_org_type', width: 70 },
    },
    {
        key: '3',
        label: '行业分类',
        value: { title: '行业分类', dataIndex: 'industry_classification', width: 70 },
    },
    {
        key: '4',
        label: '所属层级',
        value: { title: '所属层级', dataIndex: 'subordinate_level', width: 70 },
    },
    {
        key: '5',
        label: '隶属关系',
        value: { title: '隶属关系', dataIndex: 'membership_function', width: 70 },
    },
    {
        key: '6',
        label: '书记是否由行业主管部门党员负责同志担任',
        value: {
            title: '书记是否由行业主管部门党员负责同志担任  ',
            dataIndex: 'has_secretary_industry',
            width: 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        },
    },
    {
        key: '7',
        label: '专职工作人员数',
        value: { title: '专职工作人员数', dataIndex: 'worker_number', width: 70 },
    },
    {
        key: '8',
        label: '是否有所属党组织',
        value: {
            title: '是否有所属党组织', dataIndex: 'has_party_organizations', width: 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        },
    },
    {
        key: '9',
        label: '管理的党组织数',
        value: { title: '管理的党组织数', dataIndex: 'manage_org_count', width: 70 },
    },
    {
        key: '10',
        label: '管理的党员数',
        value: { title: '管理的党员数', dataIndex: 'manage_mem_count', width: 70 },
    },
    {
        key: '11',
        label: '覆盖社会组织数',
        value: { title: '覆盖社会组织数', dataIndex: 'cover_social_org', width: 70 },
    },
    {
        key: '12', label: '创建时间', value: {
            title: '创建时间', dataIndex: 'create_time', width: 50, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        key: '13', label: '修改时间', value: { title: '修改时间', dataIndex: 'update_time', width: 50 }, render: (text) => {
            return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
        },
    },
];

export const newdevelopStepLog = [
    {
        "key": "1",
        "label": "姓名",
        "value": {
            "title": "姓名",
            "dataIndex": "name",
            "width": 70
        }
    },
    {
        "key": "2",
        "label": "年龄",
        "value": {
            "title": "年龄",
            "dataIndex": "age",
            "width": 50
        }
    },
    {
        "key": "3",
        "label": "性别",
        "value": {
            "title": "性别",
            "dataIndex": "sex_name",
            "width": 50
        }
    },
    {
        "key": "4",
        "label": "发展党员身份证",
        "value": {
            "title": "发展党员身份证",
            "dataIndex": "idcard",
            "width": 80,
            render: (text, record) => {
                if (typeof text === 'string' && !_isEmpty(text)) {
                    let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, '$1***********$2');
                    if (text.indexOf('*') > 0) {
                        return text;
                    }
                    return <span>{newVal}</span>;
                } else {
                    return '';
                }
            },
        }
    },
    {
        "key": "5",
        "label": "民族",
        "value": {
            "title": "民族",
            "dataIndex": "d06_name",
            "width": 70
        }
    },
    {
        "key": "6",
        "label": "学历",
        "value": {
            "title": "学历",
            "dataIndex": "d07_name",
            "width": 70
        }
    },
    {
        "key": "7",
        "label": "党员类型",
        "value": {
            "title": "党员类型",
            "dataIndex": "d08_name",
            "width": 70
        }
    },
    {
        "key": "8",
        "label": "工作岗位",
        "value": {
            "title": "工作岗位",
            "dataIndex": "d09_name",
            "width": 70
        }
    },
    {
        "key": "9",
        "label": "进入支部类型",
        "value": {
            "title": "进入支部类型",
            "dataIndex": "d11_name",
            "width": 70
        }
    },
    {
        "key": "10",
        "label": "一线情况",
        "value": {
            "title": "一线情况",
            "dataIndex": "d21_name",
            "width": 70
        }
    },
    {
        "key": "11",
        "label": "审批结果",
        "value": {
            "title": "审批结果",
            "dataIndex": "d28_name",
            "width": 70
        }
    },
    {
        "key": "12",
        "label": "籍贯",
        "value": {
            "title": "籍贯",
            "dataIndex": "d48_name",
            "width": 70
        }
    },
    {
        "key": "13",
        "label": "加入共产党类型",
        "value": {
            "title": "加入共产党类型",
            "dataIndex": "join_org_name",
            "width": 70
        }
    },
    {
        "key": "14",
        "label": "召开支委会日期(成为正式党员日期)",
        "value": {
            "title": "召开支委会日期(成为正式党员日期)",
            "dataIndex": "topart_turn_party_date",
            "width": 70
        }
    },
    {
        "key": "15",
        "label": "延长预备期到的时间",
        "value": {
            "title": "延长预备期到的时间",
            "dataIndex": "extend_prepar_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "16",
        "label": "召开支委会日期(成为积极分子日期)",
        "value": {
            "title": "召开支委会日期(成为积极分子日期)",
            "dataIndex": "active_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "17",
        "label": "召开支委会日期(成为发展对象时间)",
        "value": {
            "title": "召开支委会日期(成为发展对象时间)",
            "dataIndex": "object_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "18",
        "label": "组织名称",
        "value": {
            "title": "组织名称",
            "dataIndex": "org_name",
            "width": 70
        }
    },
    {
        "key": "19",
        "label": "短期集中培训开始时间(发展对象=》预备党员）",
        "value": {
            "title": "短期集中培训开始时间(发展对象=》预备党员）",
            "dataIndex": "short_training_begin_time",
            "width": 70
        }
    },
    {
        "key": "20",
        "label": "短期集中培训结束时间(发展对象=》预备党员）",
        "value": {
            "title": "短期集中培训结束时间(发展对象=》预备党员）",
            "dataIndex": "short_training_end_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "21",
        "label": "政治审查结论性意见落款时间(发展对象=》预备党员）",
        "value": {
            "title": "政治审查结论性意见落款时间(发展对象=》预备党员）",
            "dataIndex": "review_conclusion_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "22",
        "label": "入党介绍人（发展对象=》预备党员）",
        "value": {
            "title": "入党介绍人（发展对象=》预备党员）",
            "dataIndex": "topre_introduction_mem",
            "width": 70
        }
    },
    {
        "key": "23",
        "label": "上级党委审批日期（发展对象=》预备党员）",
        "value": {
            "title": "上级党委审批日期（发展对象=》预备党员）",
            "dataIndex": "topre_committee_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "24",
        "label": "入党志愿书编号（发展对象=》预备党员）",
        "value": {
            "title": "入党志愿书编号（发展对象=》预备党员）",
            "dataIndex": "topre_join_book_num",
            "width": 70
        }
    },
    {
        "key": "25",
        "label": "召开支委会日期(成为预备党员日期)",
        "value": {
            "title": "召开支委会日期(成为预备党员日期)",
            "dataIndex": "topre_join_org_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "26",
        "label": "入党介绍人(预备党员=》正式党员）",
        "value": {
            "title": "入党介绍人(预备党员=》正式党员）",
            "dataIndex": "topart_introduction_mem",
            "width": 70
        }
    },
    {
        "key": "27",
        "label": "上级党委审批日期(预备党员=》正式党员）",
        "value": {
            "title": "上级党委审批日期(预备党员=》正式党员）",
            "dataIndex": "topart_committee_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "28",
        "label": "入党宣誓日期(预备党员=》正式党员）",
        "value": {
            "title": "入党宣誓日期(预备党员=》正式党员）",
            "dataIndex": "topart_oath_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "29",
        "label": "入党申请人联系人(申请人=》积极分子）",
        "value": {
            "title": "入党申请人联系人(申请人=》积极分子）",
            "dataIndex": "toactive_context_person",
            "width": 70
        }
    },
    {
        "key": "30",
        "label": "培养教育考察时间(积极分子=》发展对象）",
        "value": {
            "title": "培养教育考察时间(积极分子=》发展对象）",
            "dataIndex": "toobj_cultivate_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "31",
        "label": "培养联系人(积极分子=》发展对象）",
        "value": {
            "title": "培养联系人(积极分子=》发展对象）",
            "dataIndex": "toobj_context_mem",
            "width": 70
        }
    },
    {
        "key": "32",
        "label": "取消资格方式(1=退回入党申请人阶段,2=退回积极分子阶段)",
        "value": {
            "title": "取消资格方式(1=退回入党申请人阶段,2=退回积极分子阶段)",
            "dataIndex": "canncel_date",
            "width": 70
        }
    },
    {
        "key": "33",
        "label": "取消资格时间",
        "value": {
            "title": "取消资格时间",
            "dataIndex": "canncel_name",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "34",
        "label": "创建时间",
        "value": {
            "title": "创建时间",
            "dataIndex": "create_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "35",
        "label": "更新时间",
        "value": {
            "title": "更新时间",
            "dataIndex": "update_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "36",
        "label": "是否系统外",
        "value": {
            "title": "是否系统外",
            "dataIndex": "is_out_system",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "37",
        "label": "系统外发展时所在党支部",
        "value": {
            "title": "系统外发展时所在党支部",
            "dataIndex": "out_branch_org_name",
            "width": 70
        }
    },
    {
        "key": "38",
        "label": "出生日期",
        "value": {
            "title": "出生日期",
            "dataIndex": "birthday",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "39",
        "label": "电话",
        "value": {
            "title": "电话",
            "dataIndex": "phone",
            "width": 70
        }
    },
    {
        "key": "40",
        "label": "是否农民工",
        "value": {
            "title": "是否农民工",
            "dataIndex": "is_farmer",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "41",
        "label": "是否劳务派遣",
        "value": {
            "title": "是否劳务派遣",
            "dataIndex": "is_dispatch",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "42",
        "label": "聘任专业技术职务名称",
        "value": {
            "title": "聘任专业技术职务名称",
            "dataIndex": "d19_name",
            "width": 70
        }
    },
    {
        "key": "43",
        "label": "政治面貌",
        "value": {
            "title": "政治面貌",
            "dataIndex": "d89_name",
            "width": 70
        }
    },
    {
        "key": "44",
        "label": "追认时间",
        "value": {
            "title": "追认时间",
            "dataIndex": "ratification_time",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "45",
        "label": "新社会阶层类型名称",
        "value": {
            "title": "新社会阶层类型名称",
            "dataIndex": "d20_name",
            "width": 70
        }
    },
    {
        "key": "46",
        "label": "单位类别",
        "value": {
            "title": "单位类别",
            "dataIndex": "d04_name",
            "width": 70
        }
    },
    {
        "key": "47",
        "label": "成为预备党员年度",
        "value": {
            "title": "成为预备党员年度",
            "dataIndex": "topre_join_org_date_year",
            "width": 70
        }
    },
    {
        "key": "48",
        "label": "乡镇，街道情况",
        "value": {
            "title": "乡镇，街道情况",
            "dataIndex": "is_towns",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '乡' : (text == 2 ? "镇" : (text == 3 ? '街道' : ''))}</span>;
            }
        }
    },
    {
        "key": "49",
        "label": "行政村，城市社区，乡镇社区情况",
        "value": {
            "title": "行政村，城市社区，乡镇社区情况",
            "dataIndex": "is_community",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '行政村' : (text == 2 ? '城市社区' : (text == 3 ? '乡镇社区' : ''))}</span>;
            }
        }
    },
    {
        "key": "50",
        "label": "毕业院校",
        "value": {
            "title": "毕业院校",
            "dataIndex": "byyx",
            "width": 70
        }
    },
    {
        "key": "51",
        "label": "毕业专业",
        "value": {
            "title": "毕业专业",
            "dataIndex": "d88_name",
            "width": 70
        }
    },
    {
        "key": "52",
        "label": "人员单位名称",
        "value": {
            "title": "人员单位名称",
            "dataIndex": "unit_name",
            "width": 70
        }
    },
    {
        "key": "53",
        "label": "人事关系所在单位是否省内单位",
        "value": {
            "title": "人事关系所在单位是否省内单位",
            "dataIndex": "has_unit_province",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "54",
        "label": "人事关系是否在党组织关联单位内",
        "value": {
            "title": "人事关系是否在党组织关联单位内",
            "dataIndex": "has_unit_statistics",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "55",
        "label": "申请入党日期",
        "value": {
            "title": "申请入党日期",
            "dataIndex": "apply_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "56",
        "label": "家庭住址",
        "value": {
            "title": "家庭住址",
            "dataIndex": "home_address",
            "width": 70
        }
    },
    {
        "key": "57",
        "label": "离开党组织日期",
        "value": {
            "title": "离开党组织日期",
            "dataIndex": "leave_org_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "58",
        "label": "是否产业工人",
        "value": {
            "title": "是否产业工人",
            "dataIndex": "has_worker",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "59",
        "label": "是否高知识群体",
        "value": {
            "title": "是否高知识群体",
            "dataIndex": "has_high_knowledge",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "60",
        "label": "是否高层次人才",
        "value": {
            "title": "是否高层次人才",
            "dataIndex": "has_high_level_talents",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "61",
        "label": "是否民族院校",
        "value": {
            "title": "是否民族院校",
            "dataIndex": "has_national_colleges",
            "width": 70, render: (text, record) => {
                return <span>{text == 1 ? '是' : (text == "" ? '' : '否')}</span>;


            }
        }
    },
    {
        "key": "62",
        "label": "知识分子情况",
        "value": {
            "title": "知识分子情况",
            "dataIndex": "d154_name",
            "width": 70
        }
    },
    {
        "key": "63",
        "label": "学制",
        "value": {
            "title": "学制",
            "dataIndex": "educational_system",
            "width": 70
        }
    },
    {
        "key": "64",
        "label": "入学时间",
        "value": {
            "title": "入学时间",
            "dataIndex": "enter_school_date",
            "width": 70, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        "key": "65",
        "label": "国民经济行业",
        "value": {
            "title": "国民经济行业",
            "dataIndex": "d194_name",
            "width": 70
        }
    },
    {
        "key": "66",
        "label": "生产性服务行业",
        "value": {
            "title": "生产性服务行业",
            "dataIndex": "d195_name",
            "width": 70
        }
    },
    {
        "key": "67",
        "label": "第几产业",
        "value": {
            "title": "第几产业",
            "dataIndex": "industry",
            "width": 70
        }
    }
]

export const neworgPartCongress = [
    { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'mem_name', width: 70 } },
    { key: '2', label: '性别', value: { title: '性别', dataIndex: 'sex_name', width: 70 } },
    { key: '3', label: '学历情况', value: { title: '学历情况', dataIndex: 'd07_name', width: 80 } },
    { key: '4', label: '人员身份', value: { title: '人员身份', dataIndex: 'd106_name', width: 80 } },
    {
        key: '5',
        label: '组织层级码',
        value: { title: '组织层级码', dataIndex: 'position_org_code', width: 80 },
    },
    {
        key: '6',
        label: '组织名称',
        value: { title: '组织名称', dataIndex: 'position_org_name', width: 80 },
    },
]

export const neworgParty = [
    { key: '1', label: '党组名称', value: { title: '党组名称', dataIndex: 'party_name', width: 70 } },
    { key: '2', label: '党组类别', value: { title: '党组类别', dataIndex: 'd108_name', width: 70 } },
    { key: '3', label: '关联单位', value: { title: '关联单位', dataIndex: 'unit_name', width: 80 } },
    {
        key: '4',
        label: '创建时间',
        value: {
            title: '创建时间', dataIndex: 'builde_time', width: 80, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        },
    },
    { key: '5', label: '组织名称', value: { title: '组织名称', dataIndex: 'org_name', width: 80 } },
    {
        key: '6', label: '创建时间', value: {
            title: '创建时间', dataIndex: 'create_time', width: 50, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
    {
        key: '7', label: '修改时间', value: {
            title: '修改时间', dataIndex: 'update_time', width: 50, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            },
        }
    },
];

export const newmemFlow = [
    { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'mem_name', width: 50 } },
    { key: '2', label: '性别', value: { title: '性别', dataIndex: 'mem_sex_name', width: 70 } },
    { key: '3', label: '联系电话', value: { title: '联系电话', dataIndex: 'mem_phone', width: 100 } },
    {
        key: '4',
        label: '流动类型',
        value: { title: '流动类型', dataIndex: 'flow_type_name', width: 100 },
    },
    {
        key: '5',
        label: '流出地党支部',
        value: { title: '流出地党支部', dataIndex: 'mem_org_name', width: 50 },
    },
    {
        key: '6',
        label: '流入地党支部',
        value: { title: '流入地党支部', dataIndex: 'in_org_name', width: 50 },
    },
    {
        key: '7',
        label: '接收时间',
        value: {
            title: '接收时间', dataIndex: 'in_receiving_time', width: 50, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            }
        },
    },
    {
        key: '8', label: '外出日期', value: {
            title: '外出日期', dataIndex: 'out_time', width: 50, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            }
        }
    },
    {
        key: '9', label: '创建时间', value: {
            title: '创建时间', dataIndex: 'create_time', width: 50, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            }
        }
    },
    {
        key: '10', label: '更新时间', value: {
            title: '更新时间', dataIndex: 'update_time', width: 50, render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            }
        }
    },
];

export const neworgTransfer = [
    { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'transfer_mem_name', width: 50 } },
    {
        key: '2',
        label: '申请日期',
        value: { title: '申请日期', dataIndex: 'transfer_time', width: 70 },
    },
    {
        key: '3',
        label: '源组织',
        value: { title: '源组织', dataIndex: 'transfrer_out_org_name', width: 100 },
    },
    {
        key: '4',
        label: '目的组织',
        value: { title: '目的组织', dataIndex: 'transfer_in_org_name', width: 100 },
    },
    {
        key: '5',
        label: '转接类型',
        value: { title: '转接类型', dataIndex: 'transfer_type_name', width: 50 },
    },
    {
        key: '6',
        label: '接收时间',
        value: { title: '接收时间', dataIndex: 'tansfer_receive_time', width: 50 },
    },
];

export const newexcellentTextColumn = [
    { key: '1', label: '姓名', value: { title: '姓名', dataIndex: 'name', width: 80 } },
    { key: '2', label: '性别', value: { title: '性别', dataIndex: 'sex_name', width: 80 } },
    { key: '3', label: '民族', value: { title: '民族', dataIndex: 'd06_name', width: 80 } },
    {
        key: '4',
        label: '出生年月',
        value: {
            title: '出生年月',
            dataIndex: 'birthday',
            render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            }, width: 80,
        },
    },
    {
        key: '5',
        label: '入党时间',
        value: {
            title: '入党时间',
            dataIndex: 'apply_date',
            render: (text) => {
                return <span>{isEmpty(text) ? '' : moment(text).format('YYYY-MM-DD')}</span>;
            }, width: 80,
        },
    },
    { key: '6', label: '工作单位及职务', value: { title: '工作单位及职务', dataIndex: 'd09_name', width: 80 } },
];