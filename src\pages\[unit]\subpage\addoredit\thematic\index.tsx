import React, { Fragment, useEffect, useState } from 'react';
import { Tabs } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import {
  city,
  hospital,
  notPublicUnit,
  publicUnit,
  school,
  social,
  village,
  internet,
} from './components/config';
import { findZtDataByCode } from '@/pages/[unit]/services/thematic';
import Form1 from './components/form1';
import Form2 from './components/form2';
import Form3 from './components/form3';
import Form4 from './components/form4';
import Form5 from './components/form5';
// import Form6 from './components/form6';
// import Form7 from './components/form7';
import Form8 from './components/form8';
// import Form9 from './components/form9';
import Form10 from './components/form10';
import Form11 from './components/form11';
import Form12 from './components/form12';
import Form13 from './components/form13';

const { TabPane } = Tabs;

const index = (props: any) => {
  const {
    unit: { basicInfo = {} },
  } = props;
  const { d04Code = '', d16Code = '' } = basicInfo;
  const [tabItems, setTabItems] = useState<any>([]);
  const [selectTab, setSelectTab] = useState<any>({});
  useEffect(() => {
    // 村
    if (d04Code === '923') {
      setSelectTab(village[0]);
      setTabItems(village);
    }
    // 城市街道
    if (d04Code === '911' || d04Code === '921' || d04Code === '922') {
      setSelectTab(city[0]);
      setTabItems(city);
    }
    // 高校
    if (d04Code.startsWith('331')) {
      setSelectTab(school[0]);
      setTabItems(school);
    }
    // 医院
    if (d04Code.startsWith('341')) {
      setSelectTab(hospital[0]);
      setTabItems(hospital);
    }
    // 公有制企业
    if (d04Code.startsWith('4') && d16Code.startsWith('1')) {
      setSelectTab(publicUnit[0]);
      setTabItems(publicUnit);
    }
    // 非有制企业
    if (d04Code.startsWith('4') && d16Code.startsWith('2')) {
      setSelectTab(notPublicUnit[0]);
      setTabItems(notPublicUnit);
    }
    // 社会组织
    if (d04Code.startsWith('5')) {
      setSelectTab(social[0]);
      setTabItems(social);
    }
    // setSelectTab(internet[0]);
    // setTabItems(internet);
  }, [d04Code, d16Code]);
  return (
    <Fragment>
      <div>
        {!_isEmpty(tabItems) && (
          <Fragment>
            <Tabs
              tabBarStyle={{ padding: '0 10px' }}
              activeKey={selectTab.key}
              onChange={(e) => {
                let item = tabItems.find((it) => it.key === e);
                setSelectTab(item);
              }}
            >
              {tabItems.map((it: any) => {
                return <TabPane tab={it.name} key={it.key} />;
              })}
            </Tabs>
          </Fragment>
        )}
      </div>
      <div>
        {selectTab.key === '1' && <Form1 basicInfo={basicInfo} />}
        {selectTab.key === '2' && <Form2 basicInfo={basicInfo} />}
        {selectTab.key === '3' && <Form3 basicInfo={basicInfo} />}
        {selectTab.key === '4' && <Form4 basicInfo={basicInfo} />}
        {selectTab.key === '5' && <Form5 basicInfo={basicInfo} />}
        {/* {selectTab.key === '6' && <Form6 />} */}
        {/* {selectTab.key === '7' && <Form7 />} */}
        {selectTab.key === '8' && <Form8 basicInfo={basicInfo} selectTab={selectTab} />}
        {/* {selectTab.key === '9' && <Form9 />} */}

        {selectTab.key === '10' && <Form10 basicInfo={basicInfo} />}
        {selectTab.key === '11' && <Form11 basicInfo={basicInfo} />}
        {selectTab.key === '12' && <Form12 basicInfo={basicInfo} />}
        {selectTab.key === '13' && <Form13 basicInfo={basicInfo} />}
      </div>
    </Fragment>
  );
};
export default index;
