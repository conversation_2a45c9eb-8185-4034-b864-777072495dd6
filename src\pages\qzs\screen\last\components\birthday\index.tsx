import { <PERSON><PERSON>, Col, Modal, Pa<PERSON><PERSON>, <PERSON>, Spin } from 'antd';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import ReactSeamlessScroll from 'rc-seamless-scroll';
import st from './index.less';
import moment from 'moment';
import { screenexport, politicalBirthday } from '../../../services';
import _isEmpty from 'lodash/isEmpty';
import { pullFile } from '@/services';

const index = (props) => {
  const { joinOrgDate = '', memCode = '' } = props;
  const ref = useRef();

  const [mainList, setMainList] = useState([]);
  const [mainPagination, setMainPagination] = useState<any>({
    pageNum: 1,
    pageSize: 8,
    total: 0,
    totalPage: 0,
  });
  const [showB, setShowB] = useState<any>(false);
  const [spinning, setSpinning] = useState<any>(false);

  const getBirth = async (p = {}) => {
    setSpinning(true);
    const res = await politicalBirthday({
      data: {
        pageNum: mainPagination.pageNum,
        pageSize: mainPagination.pageSize,
        memCode,
        joinOrgDate: moment(joinOrgDate).format('M'),
        ...p,
      },
    });
    if (res.code == 0) {
      const { data: { list: mainsList = [], ...others } = {} } = res;

      for (let i = 0; i < mainsList.length; i++) {
        const item: any = mainsList[i];
        if (_isEmpty(item.photo)) {
          item['photo_base64'] = require('../../../../../../assets/head.jpg');
          continue;
        }
        const base64 = await pullFile({ path: item.photo });
        item['photo_base64'] = base64;
      }
      setSpinning(false);
      setMainList(mainsList);
      setMainPagination({ ...others, total: others.totalRow, pageNum: others.pageNumber });
    }
  };

  const onPageChange = async (page, size) => {
    getBirth({ pageNum: page, pageSize: size });
  };

  useEffect(() => {
    joinOrgDate && getBirth();
  }, [joinOrgDate]);

  return (
    <Fragment>
      <div
        id={'_birthdays'}
        className={st.birthday}
        onClick={() => {
          setShowB(true);
          getBirth();
        }}
      >
        <Alert
          banner
          style={{ width: 300 }}
          message={
            <ReactSeamlessScroll
              list={new Array(10)}
              ref={ref}
              hover={true}
              direction={'left'}
              wrapperHeight={30}
              isWatch={true}
              step={0.3}
              // singleWidth={250}
            >
              {new Array(10).fill('').map((it) => {
                return (
                  <div className={st.birthdayItem}>
                    和你今日共同渡过政治生日的人数为：{mainPagination.total} 人
                  </div>
                );
              })}
            </ReactSeamlessScroll>
          }
        />
      </div>

      <Modal
        destroyOnClose
        visible={showB}
        // getContainer={() => document.getElementById('_birthdays')}
        style={{ top: 19, right: -628 }}
        onOk={() => {
          setShowB(false);
        }}
        onCancel={() => {
          setShowB(false);
          setMainList([]);
          setMainPagination({
            pageNum: 1,
            pageSize: 8,
          });
        }}
        width={600}
        bodyStyle={{
          background: `url(${require('../../../../../../assets/qzs/qianmbg.png')})`,
          backgroundSize: '100% 100%',
          padding: '0 10px 10px 10px',
        }}
        footer={null}
      >
        <Spin spinning={spinning}>
          <div className={st.searchMem}>
            <Row style={{ width: '100%' }} gutter={[8, 8]}>
              {mainList.map((it: any, index) => {
                return (
                  <Col span={12} key={index}>
                    <div className={st.listItem}>
                      <div className={st.photo}>
                        <img src={it.photo_base64} alt="" />
                      </div>
                      <div className={st.info}>
                        <div className={st.name}>
                          <div>{it.name}</div>
                          <span>{it.d06Name}</span>
                        </div>
                        <div className={st.desc}>
                          出生年月:{moment(it.birthday).format('YYYY年M月')}
                        </div>
                        <div className={st.desc}>
                          政治生日:{moment(it.joinOrgDate).format('YYYY年M月')}
                        </div>
                      </div>
                    </div>
                  </Col>
                );
              })}
            </Row>
            <div className={st.pagination}>
              <Pagination
                size="small"
                showSizeChanger={false}
                {...mainPagination}
                onChange={onPageChange}
              />
            </div>
          </div>
        </Spin>
      </Modal>
    </Fragment>
  );
};

export default index;
