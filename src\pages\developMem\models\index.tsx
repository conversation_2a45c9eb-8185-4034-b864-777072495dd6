import modelExtend from 'dva-model-extend';
import { listPageModel } from 'src/utils/common-model';
import Tip from '@/components/Tip';
import {
  getList, delDevelopYear, addDevelop, addDevelopYear, updateDevelop, updateDevelopYear, findByCode, becomeActivist, becomeDevelopObject,zybecomeActivist,zybecomeDevelopObject,zyfindByCode,zybecomePreliminary,memDigitalContents,zybackOutStatus,flowcount,zyfireToParty,
  becomePreliminary, delDevelop, backOutStatus, fireToParty, getReadyList, findOutByPage, inDetail, outDetail, undo, transferMemInfo,
  addTransfer, adjustMem, apply, back, transferMem, itteeDel, itteeList, itteeUP, changeTargetOrg, editTransferForMem, exportXsl,checkDevelop,
  getzyList,addzunyiDevelop,updatezunyiDevelop,uploadFileDigital,zydyuploadFileDigital,transferMemDigital
} from '../services/index';
import { getSession } from "@/utils/session";
import { statSync } from 'fs'; //模拟数据
import { changeListPayQuery, fileDownloadHeader } from '@/utils/method.js';
const memDevelop = modelExtend(listPageModel, {
  namespace: "memDevelop",
  state: {
    basicInfo: {} // 发展党员基本信息
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        const org = getSession('org') || {};
        const dictData = ['dict_d09', 'dict_d07','dict_d194','dict_d195'];
        let defaultParas: any = {
          pageNum: 1,
          pageSize: 10,
          filter: undefined,
        };
        switch (pathname) {
          case '/developMem/apply':
            case '/developMem/zy/apply':
            defaultParas['type'] = '5';
            break;
          case '/developMem/active':
            case '/developMem/zy/active':
            defaultParas['type'] = '4';
            break;
          case '/developMem/object':
            case '/developMem/zy/object':
            defaultParas['type'] = '3';
            break;
        }
        if (pathname === '/developMem/apply' || pathname === '/developMem/active' || pathname === '/developMem/object') {
          for (let obj of dictData) {
            dispatch({
              type: 'commonDict/getDictTree',
              payload: {
                data: {
                  dicName: obj
                }
              }
            });
          }
          dispatch({
            type: 'getList',
            payload: {
              data: {
                memOrgCode: org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
        if (pathname === '/developMem/zy/apply' || pathname === '/developMem/zy/active' || pathname === '/developMem/zy/object') {
          for (let obj of dictData) {
            dispatch({
              type: 'commonDict/getDictTree',
              payload: {
                data: {
                  dicName: obj
                }
              }
            });
          }
        setTimeout(() => {
          dispatch({
            type: 'getzyList',
            payload: {
              data: {
                memOrgCode: org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        },500)
          dispatch({
            type: 'flowcount',
            payload: {
              data: {
                memOrgCode: org['orgCode'],
                ...query,
                ...defaultParas,
              }
            }
          })
          
        }
        if (pathname === '/developMem/mems') {
          for (let obj of dictData) {
            dispatch({
              type: 'commonDict/getDictTree',
              payload: {
                data: {
                  dicName: obj
                }
              }
            });
          }
          dispatch({
            type: 'getListMems',
            payload: {
              data: {
                memOrgCode: org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
        if (pathname === '/developMem/out') {
          dispatch({
            type: 'findOutByPage',
            payload: {
              orgId: org['code'],
              isHistory: false,
              ...defaultParas,
              ...query,
            }
          })
        }

      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put, select }) {
      const { filter, memName } = yield select(state => state['memDevelop']) || {};
      payload['data'] = { ...payload['data'], ...filter, memName, idcard: memName };
      const { data = {} } = yield call(getList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
      // 遵义列表
      *getzyList({ payload }, { call, put, select }) {
        const { filter, memName,processNode } = yield select(state => state['memDevelop']) || {};
        payload['data'] = {  ...payload['data'],...filter, memName, idcard: memName,processNode };
        const { data = {} } = yield call(getzyList, payload);
        const res = changeListPayQuery(data);
        yield put({
          type: 'querySuccess',
          payload: {
            ...res,
          }
        })
      },
      
      *flowcount({ payload }, { call, put }) {
        const { data = {} } = payload;
        let res = yield call(flowcount, { data });
        yield put({
          type: 'updateState',
          payload: {
            flowCount:res.data,
          }
        })
      },
    // 获取本年度发展党员列表
    *getListMems({ payload }, { call, put, select }) {
      const { filter, memName } = yield select(state => state['memDevelop']) || {};
      payload['data'] = { ...payload['data'], ...filter, memName, idcard: memName };
      const { data = {} } = yield call(getReadyList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res, 
        }
      })
    },
    // 查找人员信息
    *findMem({ payload }, { call, put }) {
      const res = yield call(findByCode, payload);
      const { code = 500, data = {} } = res || {};
      yield put({
        type: 'updateState',
        payload: {
          basicInfo: code === 0 ? data : {}
        }
      })
    },
    *zyfindByCode({ payload }, { call, put }) {
      const res = yield call(zyfindByCode, payload);
      const { code = 500, data = {} } = res || {};
      yield put({
        type: 'updateState',
        payload: {
          basicInfo: code === 0 ? data : {}
        }
      })
    },
    
    // 新增编辑保存
    *save({ payload }, { call, put }) {
      const { type = '', data = {} } = payload;
      let res;
      switch (type) {
        case 'add':
          res = yield call(addDevelop, { data });
          break;
        case 'edit':
          res = yield call(updateDevelop, { data });
          break;
        default:
          break;
      }
      return res;
    },
    *zycheckDevelop({ payload }, { call, put }) {
      const { type = '', data = {} } = payload;
      let res = yield call(checkDevelop, { data });
      return res;
    },
    
        // 遵义新增编辑保存
        *zysave({ payload }, { call, put }) {
          const { type = '', data = {} } = payload;
          let res;
          switch (type) {
            case 'add':
              res = yield call(addzunyiDevelop, { data });
              break;
            case 'edit':
              res = yield call(updatezunyiDevelop, { data });
              break;
            default:
              break;
          }
          return res;
        },
        *uploadFileDigital({ payload }, { call, put }) {
          const { data = {} } = payload;
          let   res = yield call(uploadFileDigital, { data });
          return res;
        },
        *zydyuploadFileDigital({ payload }, { call, put }) {
          const { data = {} } = payload;
          let   res = yield call(zydyuploadFileDigital, { data });
          return res;
        },
        
    *saveYear({ payload }, { call, put }) {
      const { type = '', data = {} } = payload;
      let res;
      switch (type) {
        case 'add':
          res = yield call(addDevelopYear, { data });
          break;
        case 'edit':
          res = yield call(updateDevelopYear, { data });
          break;
        default:
          break;
      }
      return res;
    },
    *toActive({ payload }, { call, put }) {
      const res = yield call(becomeActivist, payload);
      return res;
    },
    *tozyActive({ payload }, { call, put }) {
      const res = yield call(zybecomeActivist, payload);
      return res;
    },
    *toObject({ payload }, { call, put }) {
      const res = yield call(becomeDevelopObject, payload);
      return res;
    },
    *zytoObject({ payload }, { call, put }) {
      const res = yield call(zybecomeDevelopObject, payload);
      return res;
    },
    
    *ToPreparation({ payload }, { call, put }) {
      const res = yield call(becomePreliminary, payload);
      return res;
    },
    *zyToPreparation({ payload }, { call, put }) {
      const res = yield call(zybecomePreliminary, payload);
      return res;
    },
    *ToFirePreparation({ payload }, { call, put }) {
      const res = yield call(fireToParty, payload);
      return res;
    },
    *zyToFirePreparation({ payload }, { call, put }) {
      const res = yield call(zyfireToParty, payload);
      return res;
    },
    
    *delDevelop({ payload }, { call, put }) {
      const res = yield call(delDevelopYear, {data:payload});
      return res;
    },
    *backOutStatus({ payload }, { call, put }) {
      const res = yield call(backOutStatus, payload);
      return res;
    },
    *zybackOutStatus({ payload }, { call, put }) {
      const res = yield call(zybackOutStatus, payload);
      return res;
    },
    
    *findOutByPage({ payload }, { call, put, select }) {//转出列表
      const { filter, keyWord } = yield select(state => state['memDevelop']);
      payload = { ...payload, ...filter, keyWord };
      const { data = {} } = yield call(findOutByPage, { data: payload });
      yield put({
        type: 'updateState',
        payload: {
          outList: data['list'],
          outPagination: {
            current: data['pageNumber'],
            pageSize: data['pageSize'],
            total: data['totalRow'],
          }
        }
      })
    },
    *inDetail({ payload }, { call, put }) {
      const { data } = yield call(inDetail, payload);
      yield put({
        type: 'updateState',
        payload: {
          transDetail: data,
        }
      });
    },
    *outDetail({ payload }, { call, put }) {
      const { data } = yield call(outDetail, payload);
      yield put({
        type: 'updateState',
        payload: {
          transDetail: data,
        }
      });
    },
    *undo({ payload }, { call, put }) {
      return yield call(undo, payload);
    },
    *transferMemInfo({ payload }, { call, put }) {
      const { data } = yield call(transferMemInfo, payload);
      yield put({
        type: 'updateState',
        payload: {
          transferMemInfo: data,
        }
      });
    },
    *addTransfer({ payload }, { call, put }) {//整建制转接
      return yield call(addTransfer, payload);
    },
    *transferMem({ payload }, { call, put }) {//人员转接
      return yield call(transferMem, payload);
    },
    *adjustMem({ payload }, { call, put }) {
      return yield call(adjustMem, payload);
    },
    *apply({ payload }, { call, put }) {//审批通过
      return yield call(apply, payload);
    },
    *back({ payload }, { call, put }) {
      return yield call(back, payload);
    },
    *changeTargetOrg({ payload }, { call, put }) {
      return yield call(changeTargetOrg, payload);
    },
    *editTransferForMem({ payload }, { call, put }) {
      return yield call(editTransferForMem, payload);
    },

    *itteeUp({ payload }, { call, put }) {
      return yield call(itteeUP, payload);
    },
    *itteeList({ payload }, { call, put }) {
      const { keys } = payload;
      const { data = {} } = yield call(itteeList, payload);
      yield put({
        type: 'updateState',
        payload: {
          [`iteList_${keys}`]: data['list'] || [],
        }
      });
    },
    *itteeDel({ payload }, { call, put }) {
      return yield call(itteeDel, payload);
    },
    *exportXsl({ payload }, { call, put }) {
      const { code = 500, data } = yield call(exportXsl, payload);
      if (code === 0) {
        fileDownloadHeader(`/api${data?.url}`, data?.url.split('/')[3]);
        Tip.success('操作提示', '导出成功')
      }
    },
    
    *memDigitalContents({ payload }, { call, put }) {
      return yield call(memDigitalContents, payload);
    },
    *transferMemDigital({ payload }, { call, put }) {
      return yield call(transferMemDigital, payload);
    },
  }
});
export default memDevelop;
