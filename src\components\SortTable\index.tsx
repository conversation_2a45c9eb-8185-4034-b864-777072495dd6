import React, { Fragment } from 'react';
import { Divider, Input, Modal, Popconfirm, Tabs } from 'antd';
// import arrayMove from 'array-move';
import ListTable from '@/components/ListTable';

interface propsType {
  sortData: Array<Object>,
  onSortEnd: (any) => void,
  render: (...any) => any
  title?: string,
  dispatch?: Function,
  onClose?: () => void,
  renderOtherBtns?: (...any) => any
}

class index extends React.Component<propsType & any, any>{
  static open() { };
  static loading(bool) { };
  static defaultProps = {
    title: '排序',
  };
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      sortData: [],
    };
    index.open = this.show;
    index.loading = this.loadings;
  };
  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    return {
      sortData: nextProps.sortData
    }
  };
  loadings = (bool) => {
    this.setState({
      loading: bool
    })
  }

  show = () => {
    this.setState({
      visible: true,
    });
  };
  handleCancel = () => {
    const { onClose } = this.props;
    onClose && onClose();
    this.setState({
      visible: false,
      sortData: [],
    });
  };

  sort = ({ oldIndex, newIndex }) => {
    // const newArr = arrayMove(this.state.sortData, oldIndex, newIndex);
    this.props.onSortEnd([]);
  };
  tableSort = (oldIndex, newIndex) => {
    const { sortData } = this.state;
    const data = [...sortData];
    const temp = data[oldIndex - 1];

    if (oldIndex != newIndex && newIndex != undefined && newIndex != "") {
      data.splice(oldIndex - 1, 1);
      data.splice(newIndex - 1, 0, temp);
      this.props.onSortEnd(data);
    }
  };
  render(): React.ReactNode {
    const { visible, sortData } = this.state;
    const { title } = this.props;
    const columns = [
      {
        title: '组织名称',
        dataIndex: 'name',
        width: 270,
      },
      {
        title: '组织类别',
        width: 200,
        dataIndex: 'd01Name',
      },
      {
        title: '联系人',
        width: 100,
        dataIndex: 'contacter',
      },
      {
        title: '联系方式',
        width: 110,
        dataIndex: 'contactPhone',
      },
      {
        title: '党组织书记',
        width: 100,
        dataIndex: 'secretary',
      },
      {
        title: '当前序号',
        dataIndex: '_sort',
        width: 80,
        render: (text, record, index) => {
          return <Input key={+new Date() + index} defaultValue={record['_sort']} onBlur={e => this.tableSort(text, e.target.value)} />
        }
      },
    ]
    return (
      <Modal
        destroyOnClose
        width={1000}
        maskClosable={false}
        footer={null}
        title={title}
        visible={visible}
        onCancel={this.handleCancel}
      >
        {this.props.renderOtherBtns && this.props.renderOtherBtns()}
        {
          sortData.length > 0 && true ? <React.Fragment>
            <ListTable columns={columns} data={sortData} loading={this.state.loading} />
            {/* <Tabs className={styles.tabs}>
              <Tabs.TabPane tab={'拖动排序'} key={'1'}>
                <SortTable
                  items={sortData}
                  onSortEnd={this.sort}
                  render={(value, index) => {
                    return this.props.render(value, index)}
                  }/>
              </Tabs.TabPane>
              <Tabs.TabPane tab={'输入排序'} key={'2'}>
                <ListTable columns={columns} data={sortData}/>
              </Tabs.TabPane>
            </Tabs> */}
          </React.Fragment> : <React.Fragment>
            {/* <SortTable
                items={sortData}
                onSortEnd={this.sort}
                render={(value, index) => {
                  return this.props.render(value, index)}
                }/> */}
          </React.Fragment>
        }

      </Modal>
    );
  }
}

export default index;
