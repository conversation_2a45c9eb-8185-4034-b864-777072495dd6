/**
 * 扩展信息
 */
/**
 * 模块名
 */
import React, { Fragment, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import DictSelect from '@/components/DictSelect';
import Tip from '@/components/Tip';
import { Col, Input, Button, Switch, Row, InputNumber, Tabs, Form } from "antd";
const FormItem = Form.Item;
const { TabPane } = Tabs;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
};
function index(props, ref) {
    const [form] = Form.useForm();
    const { data = {} } = props
    const { basicInfo } = props.org
  const [loading, setLoading] = useState(false);
    useEffect(() => {
        form.setFieldsValue({
            ...data,
            hasAcceptFlowMem: data?.hasAcceptFlowMem === 0,
            notRelationMemBuild: data?.notRelationMemBuild === 0
        })
    }, [data]);
    const onFinish = (value) => {
        value['hasAcceptFlowMem'] = value['hasAcceptFlowMem'] ? 0 : 1
        value['notRelationMemBuild'] = value['notRelationMemBuild'] ? 0 : 1
      setLoading(true);
        props.dispatch({
            type: 'org/addOrUpdate',
            payload: {
                data: {
                    ...value,
                    zbCode: basicInfo['zbCode'],
                    orgCode: basicInfo['orgCode'],
                    code: data?.code || undefined
                }
            },
        }).then(res => {
          setLoading(false);
            if (res.code === 0) {
                // Tip.success('操作提示', res['code'] ? '修改成功' : '新增成功');
                Tip.success('操作提示', '修改成功');
              props.onOK && props.onOK();
            }
        })
    }
    return (

        <Form {...formItemLayout} form={form} onFinish={onFinish} name='dypxInfo'>
            <Row>
                <Col span={12}>
                    <FormItem
                        label="党员培训总人次"
                        name="trainMemTotal"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="基层党组织书记培训人次"
                        name="basicSecretaryTrain"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="农村党员远程教育培训人次"
                        name="villageLongEduMemNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="非公有制经济组织和社会组织党员培训人次"
                        name="nonPublicSocietyOrganTrainMem"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="新党员培训人次"
                        name="trainNewMem"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        label="流动党员培训人次 "
                        name="trainMemFlow"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="边疆民族地区基层党员教育培训人次"
                        name="trainFrontierBasicMem"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="trainSkillMem"
                        rules={[{ required: true, message: '请填写' }]}
                        label="党员创业就业技能培训人次"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="countrySecretaryTrain"
                        rules={[{ required: true, message: '请填写' }]}
                        label="农村党组织书记培训人次"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="workerTrain"
                        rules={[{ required: true, message: '请填写' }]}
                        label="社区党务工作者培训人次"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="stateOwnedSecretaryTrain"
                        rules={[{ required: true, message: '请填写' }]}
                        label="国有企业二级及以下单位（部门）党组织书记培训人次"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="nonPublicCompanySecretaryTrain"
                        rules={[{ required: true, message: '请填写' }]}
                        label="非公有制经济控制企业党组织书记培训人次"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>


                <Col span={12}>
                    <FormItem
                        name="trainMemStandardBasicOrgan"
                        rules={[{ required: true, message: '请填写' }]}
                        label="党员年度集中学习培训达标的基础党组织个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>


                <Col span={12}>
                    <FormItem
                        name="trainSecretaryStandardBasicOrgan"
                        rules={[{ required: true, message: '请填写' }]}
                        label="基层党组织书记和班子成员年度集中学习培训达标的基层党组织个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>


                <Col span={12}>
                    <FormItem
                        name="trainMemTowns"
                        rules={[{ required: true, message: '请填写' }]}
                        label="直接组织开展农村党员集中培训的乡镇"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="memEduSiteNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="党员干部现代远程教育终端站点总个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="townEduSiteNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="乡镇（街道）远程教育终端站点个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="villageEduSiteNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="建制村远程教育终端站点个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="communityEduSiteNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="社区（居委会）远程教育终端站点个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="internetEduSiteNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="互联网传播远程教育终端站点个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="wiredEduSiteNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="有线远程教育终端站点个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="satelliteEduSiteNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="卫星远程教育终端站点个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="eduSiteManageNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="远程教育终端站点管理员个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        name="townEduSiteManageNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="街道（乡镇）远程教育终端站点干部个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        name="villageEduSiteManageNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="村、社区远程教育终端站点干部个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        name="eduSiteVolunteerNum"
                        rules={[{ required: true, message: '请填写' }]}
                        label="远程教育终端站点志愿者个数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>





                <Col span={12}>
                    <FormItem
                        label="学校支部类型"
                        name="d73Code"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <DictSelect placeholder={'根据所选单位自动生成届次类别'} codeType={'dict_d73'} initValue={data['d73Code'] ? data['d73Code'].toString() : ''} />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        label="本年度追授优秀共产党员数"
                        name="currentYearAwardMemNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        label="其中抗灾救灾追授党员数"
                        name="earthquakeReliefAwardMemNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        label="是否接受了流动党员"
                        name="hasAcceptFlowMem"
                        valuePropName="checked"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Switch />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        label="是否未转入组织关系的党员单独建立的"
                        name="notRelationMemBuild"
                        valuePropName="checked"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Switch />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        name="outPartyAbroadMem"
                        rules={[{ required: true, message: '请填写' }]}
                        label="出国（境）人员在外期间已作出党处理人数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        name="outPartyBackHomeMem"
                        rules={[{ required: true, message: '请填写' }]}
                        label="回国后作出党处理人数"
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

            </Row>

            <div style={{ textAlign: 'center' }}>
                <Button type={'primary'} htmlType={'submit'} style={{ marginRight: 16 }} loading={loading}>保存</Button>
                <Button type={'primary'} danger htmlType={'button'} onClick={() => props.close()}>取消</Button>
            </div>
        </Form>
    );
}
export default forwardRef(index);
