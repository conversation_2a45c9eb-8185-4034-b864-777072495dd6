import { listPageModel } from '@/utils/common-model';
import modelExtend from "dva-model-extend";
import {
  queryExcelConfigTreeById, findTreeByTbsm, findFieldList, findTbsmItemList, addTbsmItemList, updateTbsmItemList,
  addByBaseSelect, addByReportSelect, deleteTbsmItemList, sortTbsmItemList, qsData, batchQsData, dataAggregate,
  expTbsm, addBatchTbsmItemList
} from '@/pages/annualStatistics/services/instruction';
import { isEmpty, jsonToTree, unique } from '@/utils/method';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import Tip from '@/components/Tip';
// import { success } from '@/components/Notice';
import { forEach } from 'lodash';

const annualstatsPerson = modelExtend(listPageModel, {
  namespace: "instruction",
  state: {
    statsType: '1',
    listTree: undefined, // 平层树数组
    TreeList: undefined, // 树结构数组
    levelTree: undefined, // 统计层次树
    type: '', // 树类型
    treeOrg: {}, // 机构树选择的组织
    treeLevel: {}, // 层次树选择
    memType: {}, // 菜单上方选择框,
    columnsArr: []
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname = '' } = location;
        if (pathname.startsWith('/annualStatistics/instruction')) {
          // dispatch({
          //   type:'getFirstAnnualstatsTree',
          //   payload:{
          //     id:-1
          //   }
          // });
          // dispatch({
          //   type:'queryFirstUnitTree',
          //   payload:{}
          // })
        }
      });
    }
  },
  effects: {
    *updateTree({ payload }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          TreeList: undefined,
          listTree: undefined,
        }
      });
      yield put({
        type: 'getFirstAnnualstatsTree',
        payload: {
          id: -1
        }
      })
    },
    *getFirstAnnualstatsTree({ payload }, { call, put, select }) {
      let { listTree, TreeList } = yield select(state => state['instruction']);
      let { memType, type } = yield select(state => state['annualstats']);
      if (listTree === undefined) {
        const { code = 500, data = [] } = yield call(queryExcelConfigTreeById, { ...payload, type: memType, isTbsm: '1' });
        if (code === 0) {
          if (!isEmpty(data)) {
            listTree = data.map(item => { return { ...item, children: [], parentCode: '-1' } });
            TreeList = jsonToTree(listTree, 'parentCode', 'levelCode', '-1');
          } else {
            listTree = [];
            TreeList = [];
          }
        }
        let { id } = _get(TreeList, '[0]', {});
        const { code: code2 = 500, data: data2 = [] } = yield call(queryExcelConfigTreeById, { id, type: memType, isTbsm: '1' });
        if (code2 === 0) {
          if (!isEmpty(data2)) {
            listTree = listTree.concat(data2);
            TreeList = jsonToTree(listTree, 'parentCode', 'levelCode', '-1');
          }
        }
        // 树展开项
        let { id: expandedKeys = '' } = _get(TreeList, '[0]', {});
        let level = _get(TreeList, '[0].children.[0].levelCode', '');
        yield put({
          type: 'updateState',
          payload: {
            TreeList,
            listTree,
            expandedKeys: [`${expandedKeys}`]
          }
        });
        const { levelCode = '', id: orgId = '', unitId = '' } = getSession('annualstatsTreeOrg') || {};
        const { code: lcode = '' } = getSession('annualstatsLevel') || {};
        let text = type === 'a' ? 'orgCode' : 'countLevelCode';
        let obj = {
          orgLevelCode: levelCode,
          reportLevelCode: level,
        };
        // //选择事业单位 需要穿org的id
        // if(memType === '3' && type === 'a'){
        //   obj = {
        //     ...obj,
        //     orgCode:orgId+`,${unitId}`
        //   }
        // }
        yield put({
          type: 'findTreeByTbsm',
          payload: {
            ...obj
          }
        })
      }
    },
    *getAnnualstatsTree({ payload }, { call, put, select }) {
      let { listTree, TreeList } = yield select(state => state['instruction']);
      let { memType } = yield select(state => state['annualstats']);
      const { code = 500, data = [] } = yield call(queryExcelConfigTreeById, { ...payload, type: memType, isTbsm: '1' });
      if (code === 0 && !isEmpty(listTree)) {
        if (!isEmpty(data)) {
          listTree = listTree.concat(data);
        }
        listTree = unique(listTree, 'id');
        TreeList = jsonToTree(listTree, 'parentCode', 'levelCode', '-1');
      }
      yield put({
        type: 'updateState',
        payload: {
          TreeList,
          listTree
        }
      })
    },
    *findTreeByTbsm({ payload }, { call, put, select }) {
      const { code = 500, data = [] } = yield call(findTreeByTbsm, { ...payload })
      if (code === 0 && data) {
        let arr: any = []
        data.map(i => {
          arr.push(i)
          if (i.children) {
            i.children.map(j => {
              arr.push(j)
            })
          }
        })
        yield put({
          type: 'updateState',
          payload: {
            listData: arr
          }
        })
      }
    },
    *findFieldList({ payload }, { call, put, select }) {
      const { tti005, tti000, orgLevelCode } = payload
      const { code = 500, data = [] } = yield call(findFieldList, { tti005 })
      if (code === 0 && data) {
        let arr1: any = []
        let forcalculate = ''
        let arr = data.map(i => {
          if (i.forcalculate) {
            forcalculate = i.forcalculate
          }
          let obj = {
            title: i.name,
            dataIndex: i.code,
            aboutcode: i.aboutcode || '',
            formtype: i.formtype,
            gridwidth: i.gridwidth,
            forcalculate: i.forcalculate
          }
          arr1.push(i.code)
          return obj
        })
        yield put({
          type: 'findTbsmItemList',
          payload: {
            tti000,
            tti005,
            orgLevelCode,
            columnsArr: arr1
          }
        })
        yield put({
          type: 'updateState',
          payload: {
            columnsList: [
              {
                title: '序号',
                dataIndex: 'index',
                fixed: 'left',
              },
              ...arr
            ],
            forcalculate,
          }
        })
      }
    },

    *findTbsmItemList({ payload }, { call, put, select }) {
      // const { columnsArr=[], } = payload
      const { code = 500, data = [] } = yield call(findTbsmItemList, { tti000: payload.tti000, tti005: payload.tti005, orgLevelCode: payload.orgLevelCode })
      if (code === 0 && data) {
        let arr: any = []
        for (let i = data.length; i < 50; i++) {
          let obj = {
            index: i + 1,
          }
          arr.push(obj)
        }
        let aa: any = []
        data.map(j => {
          let a: any = []
          payload.columnsArr.forEach(t => {
            a.push(j[t])
          })
          let obj = {
            title: a.join('/'),
            ttit000: j.ttit000
          }
          aa.push(obj)
        })
        yield put({
          type: 'updateState',
          payload: {
            tableList: [...data, ...arr],
            originally: aa
          }
        })
      }
    },

    *addTbsmItemList({ payload }, { call, put, select }) {
      const { code = 500, data = [] } = yield call(addTbsmItemList, { ...payload })
      if (code === 0) {
        Tip.success('操作提示','保存成功')
      }
    },

    *updateTbsmItemList({ payload }, { call, put, select }) {
      const { code = 500, data = [] } = yield call(updateTbsmItemList, { ...payload })
      if (code === 0) {
        Tip.success('操作提示','修改成功')
      }
    },

    *addByBaseSelect({ payload }, { call, put, select }) {
      const info = yield call(addByBaseSelect, { ...payload })
      return Promise.resolve(info);
    },
    *addByReportSelect({ payload }, { call, put, select }) {
      const info = yield call(addByReportSelect, { ...payload })
      return Promise.resolve(info);
    },
    *deleteTbsmItemList({ payload }, { call, put, select }) {
      const info = yield call(deleteTbsmItemList, { ...payload })
      return Promise.resolve(info);
    },

    *sortTbsmItemList({ payload }, { call, put, select }) {
      const info = yield call(sortTbsmItemList, { ...payload })
      return Promise.resolve(info);
    },

    *qsData({ payload }, { call, put, select }) {
      const info = yield call(qsData, { ...payload })
      return Promise.resolve(info);
    },

    *clear({ payload }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          originally: []
        }
      })
    },

    *batchQsData({ payload }, { call, put, select }) {
      const info = yield call(batchQsData, { ...payload })
      return Promise.resolve(info);
    },

    *dataAggregate({ payload }, { call, put, select }) {
      const info = yield call(dataAggregate, { ...payload })
      return Promise.resolve(info);
    },
    *expTbsm({ payload }, { call, put, select }) {
      const info = yield call(expTbsm, { ...payload })
      // fileDownload(`/api${res['data']['url']}`);
      return Promise.resolve(info);
    },
    *addBatchTbsmItemList({ payload }, { call, put, select }) {
      const info = yield call(addBatchTbsmItemList, { ...payload })
      // fileDownload(`/api${res['data']['url']}`);
      return Promise.resolve(info);
    },
  }
});
export default annualstatsPerson;
