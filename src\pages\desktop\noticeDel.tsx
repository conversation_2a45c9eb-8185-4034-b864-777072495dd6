import React, { useEffect, useImperativeHandle, useRef, useState, Fragment } from 'react';
import { Modal, Spin, Checkbox } from 'antd';
import { read } from '../desktop/services/index';
import Tip from '@/components/Tip';
const index = (props, ref) => {
  const {onOK} = props;
  const org = JSON.parse(sessionStorage.getItem('org') || '{}');
  useImperativeHandle(ref, () => ({
    open: (query) => {
      console.log(query, 'query');
      setIsModalVisible(true);
      setData({ ...query });
    },
    clear: () => {
      // clear();
    },
  }));
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [data, setData]: any = useState({});
  const [check, setCheck] = useState<any>(false);

  const handleOk = async () => {
    if (check) {
      const res = await read({ id: data.id });
      if (res.code == 0) {
        onOK?.(data);
        Tip.success('操作提示', '操作成功');
      }
    }
    handleCancel();
  };

  const handleCancel = () => {
    onOK?.(data);
    setCheck(false);
    setData({});
    setIsModalVisible(false);
  };
  // const sendUrl = async () => {
  //   setLoading(true);
  //   setLoading(false);
  // }
  // useEffect(() => {
  //   if (org.orgCode) {
  //     sendUrl();
  //   }
  // }, [org.orgCode]);
  return (
    <Modal
      title={data.title}
      forceRender
      visible={isModalVisible}
      onOk={handleOk}
      onCancel={handleCancel}
      // footer={false}
      bodyStyle={{
        height:590,
        overflow: 'auto',
      }}
      destroyOnClose
      width={'calc(80vw)'}
    >
      <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <div style={{ flex: 1 }} dangerouslySetInnerHTML={{ __html: data.content }}></div>
        <div>
          <Checkbox
            onChange={async (e) => {
              setCheck(e);
            }}
          >
            已阅读，勾选下次不再弹出
          </Checkbox>
        </div>
      </div>
    </Modal>
  );
};
export default React.forwardRef(index);
