// 流动人员详情-仅作展示信息使用，不可编辑基本信息
import React, { Fragment } from 'react';
import { connect } from 'dva';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import _isObject from 'lodash/isObject';
import { Form } from '@ant-design/compatible';
import { Input, Modal, InputNumber, Button, Select } from 'antd';
import moment from 'moment';
import { getSession } from '@/utils/session';
import { formLabel, findDictCodeName } from '@/utils/method';
import Tip from '@/components/Tip';
import { inflowOrganizationInfo, inflowOrganizationDInfo, inflowOrganizationDUpdate } from '../../service/index';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

// @ts-ignore
@connect(
  ({ unit, commonDict, loading, flowMem }) => ({
    flowMem,
    unit,
    commonDict,
    loading: loading.effects['unit/getList'],
  }),
  undefined,
  undefined,
  { forwardRef: true },
)
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
  }

  open = (type: any, edit: string, record?: any) => {
    let title = '党组织信息查看';
    this.setState(
      {
        modalType: type,
        modalTitle: title,
        visible: true,
      },
      () => {
        if (!_isEmpty(record['code'])) {
          if (type == 1) {
            this.getBasicInfo(record['id']);
          } else {
            this.getBasicInfo(record['code']);
          }
        }
      },
    );
  };
  getBasicInfo = async (code: string) => {
    let url: any = undefined;
    let rdata: any = {}
    url = inflowOrganizationInfo;
    rdata.id = code
    const { code: rescode = 500, data = {} } = await url({
      data: rdata
    });
    if (rescode == 0) {
      // console.log("datadatadatadatadatadata", data);
      this.setState({
        basicInfo: data,
      });
    }
  };
  handleOk = async () => {
    const { onOk } = this.props;
    const { basicInfo } = this.state;
    const org: any = getSession('org');
    this.props.form.validateFieldsAndScroll(async (error, values) => {
      if (error) {
        return;
      }
      let url: any = inflowOrganizationDUpdate;
      if (url) {
        this.setState(
          {
            confirmLoading: true,
          },
          async () => {
            const { code = 500 } = await url({
              data: {
                ...basicInfo.orgFlow,
                ...values
              },
            });
            this.setState({
              confirmLoading: false,
            });
            if (code == 0) {
              Tip.success('操作提示', '操作成功');
              this.handleCancel();
              onOk && onOk();
            }
          },
        );
      }
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      modalTitle: '',
      basicInfo: {},
      confirmLoading: false,
    });
  };
  render() {
    const { children, tipMsg = {}, commonDict } = this.props;
    const {
      visible,
      modalTitle = '',
      basicInfo = {},
      edit = true
    } = this.state;
    const { getFieldDecorator, setFieldsValue, getFieldValue } = this.props.form;
    const org: object = getSession('org') || {};
    console.log(basicInfo);
    return (
      <div>
        {children
          ? React.cloneElement(children as any, {
            onClick: this.open,
            key: 'container',
          })
          : null}
        <Modal
          footer={null}
          bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
          destroyOnClose
          width={1100}
          title={modalTitle}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
        >
          <Form {...formItemLayout}>
            <FormItem label={formLabel('党组织名称', tipMsg['name'])}>
              {getFieldDecorator('name', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.name,
                rules: [{ required: true, message: '请输入党组织名称' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('党组织类型', tipMsg['d01Name'])}>
              {getFieldDecorator('d01Name', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.d01Name,
                rules: [{ required: true, message: '请输入党组织类型' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('党组织成立类型', tipMsg['d200Name'])}>
              {getFieldDecorator('d200Name', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.d200Name,
                rules: [{ required: true, message: '请输入党组织成立类型' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('是否有效', tipMsg['isEnableName'])}>
              {getFieldDecorator('isEnableName', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.isEnableName,
                rules: [{ required: true, message: '请输入是否有效' }],
              })(
                <Input placeholder="请输入" disabled={edit} />
                // <Select
                //   disabled={edit}
                //   placeholder="请选择"
                //   style={{ width: '100%' }}
                // >
                //   <Select.Option value={'0'}>是</Select.Option>
                //   <Select.Option value={'1'}>否</Select.Option>
                // </Select>
              )}
            </FormItem>
            <FormItem label={formLabel('上级党组织类型', tipMsg['parentFlowTypeName'])}>
              {getFieldDecorator('parentFlowTypeName', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.parentFlowTypeName,
                rules: [{ required: true, message: '请输入党组织类型' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('成立日期', tipMsg['createDate'])}>
              {getFieldDecorator('createDate', {
                initialValue: _isEmpty(basicInfo) ? undefined : moment(basicInfo.createDate).format('YYYY-MM-DD'),
                rules: [{ required: true, message: '请输入成立日期' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('联系人', tipMsg['contacter'])}>
              {getFieldDecorator('contacter', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.contacter,
                rules: [{ required: true, message: '请输入联系人' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('联系方式', tipMsg['contactPhone'])}>
              {getFieldDecorator('contactPhone', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.contactPhone,
                rules: [{ required: true, message: '请输入组织名称' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('是否需要中央交换区流', tipMsg['isExchangeName'])}>
              {getFieldDecorator('isExchangeName', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.isExchangeName,
                rules: [{ required: true, message: '是否需要中央交换区流' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('审批状态', tipMsg['statusName'])}>
              {getFieldDecorator('statusName', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.statusName,
                rules: [{ required: true, message: '请输入审批状态' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('审批时间', tipMsg['auditTime'])}>
              {getFieldDecorator('auditTime', {
                initialValue: _isEmpty(basicInfo) ? undefined : moment(basicInfo.auditTime).format('YYYY-MM-DD'),
                rules: [{ required: true, message: '请输入审批时间' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('审批人', tipMsg['auditUserName'])}>
              {getFieldDecorator('auditUserName', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.auditUserName,
                rules: [{ required: true, message: '请输入审批人' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            <FormItem label={formLabel('审批单位', tipMsg['auditOrgName'])}>
              {getFieldDecorator('auditOrgName', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.auditOrgName,
                rules: [{ required: true, message: '请输入审批单位' }],
              })(<Input placeholder="请输入" disabled={edit} />)}
            </FormItem>
            {basicInfo.status == "2" && <FormItem label={formLabel('审批理由', tipMsg['reason'])}>
              {getFieldDecorator('reason', {
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.reason,
                rules: [{ required: true, message: '请输入组织名称' }],
              })(<Input.TextArea
                disabled={edit}
                placeholder="请输入"
                showCount
                maxLength={100}
                rows={4}
              />)}
            </FormItem>}
          </Form>
        </Modal>
      </div >
    );
  }
}

export default Form.create()(index);
