import React, { useState, Fragment, useEffect, useRef } from 'react';
import _isEmpty from 'lodash/isEmpty';
import style from './index.less';
import { _history } from "@/utils/method";
import qs from 'qs';
import { Tabs, Input, Modal, Form, Radio, Button, Descriptions, Divider } from 'antd';
import ListTable from 'src/components/ListTable';
import { getMemDevelopAuditList, auditMemDevelop, comprehensiveList } from '../service'
import { getSession } from '@/utils/session';
import NowOrg from 'src/components/NowOrg';
import moment from 'moment'
import { connect } from "dva";
import tip from '@/components/Tip';
import ElectronicArchives from '@/pages/developMem/zy/components/electronicArchives'
import Icon from '@ant-design/icons';
import Fx from './fx';
const Search = Input.Search;

const TabPane = Tabs.TabPane;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};

function Index(props: any) {
    const [filterHeight, setFilterHeight] = useState();
    const [loading, setLoading] = useState(false);
    const [list, setList] = useState([]);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [memName, setMemName] = useState('')
    const [visible, setVisible] = useState(false)

    const [confirmLoading, setConfirmLoading] = useState(false)
    const [statu, setStatu] = useState(null)
    const [digitalLotNo, setDigitalLotNo] = useState('')
    const [visible1, setVisible1] = useState(false)
    const [reason, setReason] = useState('')
    const electronicArchives = useRef()
    const [form] = Form.useForm();
    const { location: { pathname = '' } = {} } = _history
    const fxref = useRef();
    const org = getSession('org') || {};
    const columns = [
        {
            title: '序号',
            dataIndex: 'num',
            width: 60,
            align: 'center',
            render: (text, record, index) => {
                return index + 1
            }
        },
        {
            title: '党组织名称',
            align: 'center',
            dataIndex: 'orgName',
            width: 100,
        },
        {
            title: '入党申请人-确定为积极分子',
            width: 200,
            align: 'center',
            children: [
                {
                    title: '预警',
                    dataIndex: 'rdsqrYJ',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '警告',
                    dataIndex: 'rdsqrJG',
                    width: 100,
                    align: 'center'
                },

            ]
        },
        {
            title: '积极分子-确定为发展对象',
            width: 200,
            align: 'center',
            children: [
                // {
                //     title: '预警',
                //     dataIndex: 'jjfzYJ',
                //     width: 100,
                //     align: 'center',
                // },
                {
                    title: '警告',
                    dataIndex: 'jjfzJG',
                    width: 100,
                    align: 'center'
                },

            ]
        },
        {
            title: '发展对象-接收为预备党员',
            width: 200,
            align: 'center',
            children: [
                {
                    title: '预警',
                    dataIndex: 'fzdxYJ',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '警告',
                    dataIndex: 'fzdxJG',
                    width: 100,
                    align: 'center'
                },
                {
                    title: '待审查或待审核',
                    dataIndex: 'fzdxSC',
                    width: 100,
                    align: 'center'
                },
            ]
        },
        {
            title: '预备党员-正式党员',
            width: 200,
            align: 'center',
            children: [
                {
                    title: '预警',
                    dataIndex: 'ybdyYJ',
                    width: 100,
                    align: 'center',
                },
                {
                    title: '警告',
                    dataIndex: 'ybdyJG',
                    width: 100,
                    align: 'center'
                },
                {
                    title: '待审查或待审核',
                    dataIndex: 'ybdySC',
                    width: 100,
                    align: 'center'
                },
            ]
        },
        {
            title: '转入（含档案）人员',
            dataIndex: 'transferIn',
            width: 120,
            align: 'center',
        },
        {
            title: '转出（含档案）人员',
            dataIndex: 'transferOut',
            width: 120,
            align: 'center',
        }
    ]

    const getList = async (p?: any) => {
        setLoading(true)
        const { code = 500, data = [] } = await comprehensiveList({ orgLevelCode: org['orgCode'] })
        setLoading(false)
        if (code == 0) {
            setList(data)
        }

    }
    const handleOk = () => {
        form.submit()
    }
    const handleCancel = () => {
        form.resetFields()
        setStatu(null)
        setVisible(false)
    }
    const handleCancel1 = () => {
        setVisible1(false)
    }
    const hadndleFinish = async (value) => {
        setConfirmLoading(true)
        const { code = 500 } = await auditMemDevelop({ data: { digitalLotNo, ...value } })
        if (code == 0) {
            tip.success('操作提示', '操作成功')
            getList()
            handleCancel()
        }
        setConfirmLoading(false)
    }
    useEffect(() => {
        getList({ pageNum: 1, pageSize: 10, memName })
    }, [org['code']])
    return (
        <div>
            {/* <NowOrg extra={
                <React.Fragment>
                    <Search style={{ width: 200, marginLeft: 16 }} onSearch={search} onChange={searchClear} placeholder={'请输入检索关键词'} />
                </React.Fragment>
            } /> */}
            <ListTable scroll={{ y: filterHeight }} 
            
             columns={columns} data={list} pagination={false} />

            <Fx ref={fxref} />
        </div>
    );
}
export default Index