// 流入管理-未纳入-退回
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import { Modal, Input, Select } from 'antd';
import moment from 'moment';
import DictSelect from '@/components/DictSelect';
import Tip from '@/components/Tip';
import { inManageBackTo } from '../../service/index';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
};

class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      memInfo: {},
      visible: false,
      timeKey: moment().valueOf(),
      confirmLoading: false,
    };
  }
  handleOk = () => {
    const { onOk } = this.props;
    const { code } = this.state.memInfo;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      // console.log("🚀 ~ index ~ this.props.form.validateFieldsAndScroll ~ val:", val)
      if (!err) {
        val.rejectReasonName = val.rejectReasonCode?.name || undefined;
        val.rejectReasonCode = val.rejectReasonCode?.key || undefined;
        this.setState(
          {
            confirmLoading: true,
          },
          async () => {
            const res = await inManageBackTo({ data: { code, ...val } });
            this.setState({
              confirmLoading: false,
            });
            if (res.code === 0) {
              this.handleCancel();
              Tip.success('操作提示', '操作成功');
              onOk && onOk();
            }
          },
        );
      }
    });
  };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  open = (record) => {
    this.setState({ visible: true, memInfo: record, timeKey: moment().valueOf() });
  };
  destroy = () => {
    this.setState({
      memInfo: {},
    });
  };
  render() {
    const { form } = this.props;
    const { getFieldDecorator, getFieldValue } = form;
    const { visible, confirmLoading } = this.state;
    // console.log(getFieldValue("rejectReasonCode"));
    return (
      <Modal
        destroyOnClose
        title="退回提示"
        visible={visible}
        onOk={this.handleOk}
        maskClosable={false}
        onCancel={this.handleCancel}
        width={600}
        confirmLoading={confirmLoading}
      >
        {visible && (
          <Fragment key={this.state.timeKey}>
            <Form {...formItemLayout} labelAlign="left">
              <FormItem label="退回原因">
                {getFieldDecorator('rejectReasonCode', {
                  rules: [{ required: true, message: '请选择退回原因' }],
                })(<DictSelect codeType={'dict_d153'} backType={'object'} />)}
              </FormItem>
              {getFieldValue("rejectReasonCode")?.key == 9 && (
                <FormItem label="情况说明">
                  {getFieldDecorator('rejectOtherReasonText', {
                    rules: [{ required: true, message: '请选择情况说明' }],
                  })(<Input.TextArea placeholder="请输入" showCount maxLength={80} rows={4} />)}
                </FormItem>
              )}
              <FormItem label="与流动党员本人联系记录">
                {getFieldDecorator('rejectContactMemRecord', {
                  rules: [{ required: true, message: '请填写与流动党员本人联系记录' }],
                })(<Input.TextArea rows={4} placeholder="请输入" maxLength={800} />)}
              </FormItem>
              <FormItem label="与流出地党组织联系记录">
                {getFieldDecorator('rejectContactOrgRecord', {
                  rules: [{ required: true, message: '请填写与流出地党组织联系记录' }],
                })(<Input.TextArea rows={4} placeholder="请输入" maxLength={800} />)}
              </FormItem>
            </Form>
          </Fragment>
        )}
      </Modal>
    );
  }
}
export default Form.create()(index);
