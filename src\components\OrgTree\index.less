.page {
  height: auto;
  background: #fff;
  .tit {
    font-size: 16px;
    margin: 0 0 2px;
    padding: 0 8px;
    color: #2e2e2e;
    background: #e7eaef;
    height: 40px;
    line-height: 40px;
    display: flex;
    & > div:first-child {
      flex: 1;
    }
  }
  .check {
    font-size: 15px;
    & > span {
      margin-left: 2px;
      position: relative;
      top: 1px;
      color: #1890ff;
    }
  }
  :global {
    .ant-checkbox + span {
      padding-right: 0;
    }
    .ant-checkbox-wrapper + .ant-checkbox-wrapper {
      margin-left: 0;
    }
    .ant-anchor-wrapper {
      overflow: unset;
    }
  }
}

:global {
  .o_check {
    display: flex;
    flex-direction: column;
    .ant-checkbox + span {
      padding-right: 0;
    }
    .ant-checkbox-wrapper + .ant-checkbox-wrapper {
      margin-left: 0;
    }
    .ant-anchor-wrapper {
      overflow: unset;
    }
  }
}
