// 流动党员-流入管理-未纳入流入地管理
import React, { Fragment } from 'react';
import { connect } from 'dva';
import { Button, Divider, Input, Modal } from 'antd';
import { WarningTwoTone } from '@ant-design/icons';
import moment from 'moment';
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import { isEmpty, setListHeight, changeMsgTip } from '@/utils/method';
import NowOrg from 'src/components/NowOrg';
import ExportInfo from '@/components/Export';
import Tip from '@/components/Tip';
import { getSession } from '@/utils/session';
// import FlowAddOrEdit from './flowAddOrEdit';
import FlowAddOrExamine from './flowAddORexamine';
import FlowAddOrEdit from '@/pages/org/list/subpage/flowaddoredit';

const Search = Input.Search;
//@ts-ignore
@connect(({ unit, commonDict, loading, flowMem, org }) => ({
  flowMem,
  unit,
  commonDict,
  org,
  loading: loading.effects['unit/getList'],
}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {}, //筛选器
      memName: undefined, //搜索框
      view: false,
      subordinate: getSession('subordinate'),
      exportLoading: false, //导出loading
      searchLoading: false, //查询loading
      statusArr: ['待审批', "审批通过", "审批未通过", "撤销审批"]
    };
    this['queryDetailsRef'] = React.createRef();
  }

  exportInfo = async () => {
    this.setState({
      exportLoading: true,
    });
    await this['exportRef'].submitNoModal();
    this.setState({
      exportLoading: false,
    });
  };
  filterChange = (val) => {
    this.setState(
      {
        filter: val,
      },
      () => this.getList(),
    );
  };
  handleSearch = (e) => {
    this.setState(
      {
        memName: e,
        searchLoading: true,
      },
      () => {
        this.getList({ memName: e });
      },
    );
  };
  searchChange(e) {
    this.setState({
      memName: e.currentTarget.value || undefined,
    });
  }
  getList = async (params?: object) => {
    const { filter, memName } = this.state;
    // return
    const org = getSession('org') || {};
    await this.props.dispatch({
      type: 'flowMem/inflowOrganizationDList',
      payload: {
        data: {
          pageNum: 1,
          pageSize: 10,
          orgCode: org['orgCode'],
          name: memName || '',
          ...params,
        },
      },
    });
    this.setState({
      searchLoading: false,
    });
  };
  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org = getSession('org') || {};
    const subordinate = getSession('subordinate') || '0';
    if (
      (!isEmpty(this.state['orgCode']) && this.state['orgCode'] !== org['orgCode']) ||
      subordinate !== this.state.subordinate
    ) {
      this.setState(
        {
          orgCode: org['orgCode'],
          subordinate,
        },
        () => {
          this.getList({ orgCode: org['orgCode'] });
        },
      );
    }
  }
  componentDidMount() {
    const org = getSession('org') || {};
    setListHeight(this);
    this.setState({ orgCode: org['orgCode'] });
    this.getList();
  }
  flowAddOrEdit = (record?: object) => {
    FlowAddOrEdit['WrappedComponent'].clear();
    FlowAddOrEdit['WrappedComponent'].show(record);
  }
  render() {
    const { filterHeight, filter, memName, subordinate, searchLoading, statusArr } = this.state;
    const org = getSession('org') || {};
    const {
      loading,
      commonDict,
      flowMem: { list = [], pagination = { pageNum: 1, pageSize: 10 } },
    } = this.props;

    const filterData = [
      {
        key: 'flowType',
        name: '流动类型',
        value: commonDict[`dict_d34`],
      },
    ];
    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 60,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '组织名称',
        dataIndex: 'name',
        align: 'center',
        width: 150,
        render: (text, record, index) => {
          return (
            <Fragment>
              {record['flowMessage'] ? (
                <WarningTwoTone
                  style={{ fontSize: '20px' }}
                  twoToneColor={'#faad14'}
                  title={record['flowMessage']}
                />
              ) : (
                ''
              )}
              <a
                onClick={() => {
                  // this['flowAddOrEditRef'].open(0, false, record);
                  this.flowAddOrEdit({ ...record, isEdit: true });
                }}
              >
                {text}
              </a>
            </Fragment>
          );
        },
      },
      {
        title: '党组织类型名称',
        dataIndex: 'd01Name',
        align: 'center',
        width: 150,
      },
      {
        title: '联系人',
        dataIndex: 'contacter',
        align: 'center',
        width: 100,
      },
      {
        title: '联系方式',
        dataIndex: 'contactPhone',
        align: 'center',
        width: 120,
      },
      {
        title: '批准成立的党组织（县级以上党委）',
        dataIndex: 'approveName',
        align: 'center',
        width: 300,
      },
      {
        title: '行政区划',
        dataIndex: 'administrativeDivisionName',
        align: 'center',
        width: 100,
      },
      {
        title: '审批状态',
        dataIndex: 'status',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          return statusArr[parseInt(text)];
        },
      },
      // {
      //   title: '依托单位名称',
      //   dataIndex: 'd201Name',
      //   align: 'center',
      //   width: 150,
      // },
      {
        title: '成立日期',
        dataIndex: 'createDate',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 150,
        render: (text, record) => {
          console.log(record);
          return (
            <Fragment>
              <a
                onClick={() => {
                  this.flowAddOrEdit({ ...record, isEdit: false });
                }}
              >
                查看
              </a>

              {record.isEnable == 1 &&
                <Fragment>
                  <Divider type="vertical" />
                  <a
                    onClick={() => {
                      // this['flowAddOrEditRef'].open(0, true, record);
                      this.flowAddOrEdit({ ...record, isEdit: true })
                    }}
                  >
                    修改
                  </a>
                </Fragment>
              }
              {/* <Divider type="vertical" /> */}
              {/* <a
                onClick={() => {
                  Modal.confirm({
                    title: '移至县级库提示：',
                    content: '确定不接收该党员并将其移入县级流入库吗？',
                    onOk: async () => {
                      const { code = 500 } = await inManageMove({
                        data: {
                          code: record.code,
                        },
                      });
                      if (code == 0) {
                        Tip.success('操作提示', '操作成功');
                        this.getList({ pageNum: 1 });
                      }
                    },
                  });
                }}
              >
                移至县级库
              </a> */}
              {record.status == 0 && <Fragment>
                <Divider type="vertical" />
                <a
                  onClick={() => {
                    this['flowAddOrExamineRef'].open(record, "cancel", 0);
                  }}
                >
                  撤销
                </a>
              </Fragment>}
            </Fragment>
          );
        },
      },
    ];
    return (
      <Fragment>
        <NowOrg
          extra={
            <Fragment>
              {/* <Button
                style={{ marginLeft: 16 }}
                onClick={this.exportInfo}
                loading={this.state.exportLoading}
              >
                导出
              </Button> */}
              <Search
                loading={searchLoading}
                allowClear
                placeholder="请输入组织名称"
                enterButton={'查询'}
                style={{ width: 200, marginLeft: 16 }}
                onSearch={(e) => {
                  this.handleSearch(e);
                }}
                onChange={(e) => {
                  this.searchChange(e);
                }}
              />
            </Fragment>
          }
        />
        {/* <RuiFilter
          data={filterData}
          openCloseChange={() => setListHeight(this, 20)}
          onChange={this.filterChange}
        /> */}
        <ListTable
          rowKey={'id'}
          scroll={{ y: filterHeight, x: 100 }}
          
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={(page, pageSize) => {
            this.getList({ pageNum: page, pageSize });
          }}
        />
        <ExportInfo
          wrappedComponentRef={(e) => (this['exportRef'] = e)}
          tableName={''}
          noModal={true}
          tableListQuery={{
            ...filter,
            pageNum: pagination.pageNumber || 1,
            pageSize: pagination.pageSize || 10,
            subordinate,
            memName,
            orgCode: org['orgCode'],
            type: '1',
          }}
          action={'/api/mem/flow/exportXsl'}
        />
        <FlowAddOrEdit
          wrappedComponentRef={(e) => (this['flowAddOrEditRef'] = e)}
          onClose={this.getList}
        />
        <FlowAddOrExamine
          wrappedComponentRef={(e) => (this['flowAddOrExamineRef'] = e)}
          onOk={this.getList}
        />
        {/* <FlowAddOrEdit dataInfo={{}} /> */}
      </Fragment>
    );
  }
}
