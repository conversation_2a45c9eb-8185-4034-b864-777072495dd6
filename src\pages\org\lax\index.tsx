import React, { Fragment, useState, useRef, useEffect } from 'react';
import { Button, Input, Divider, Popconfirm, Tabs, message, Modal } from 'antd';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import WhiteSpace from '@/components/WhiteSpace';
import ListTable from 'src/components/ListTable';
import NowOrg from '@/components/NowOrg';
import NewAdd from './newAdd';
import Rectify from './components/rectify';
import { getSession } from '@/utils/session';
import { getListLax, delSlackOrg, updateSlackOrg } from '../services/lax.js';
import Date from '@/components/Date';
import tip from '@/components/Tip';
import moment from 'moment';
import ExportInfo from '@/components/Export/index';
import DateTime from '@/components/Date';
import { hookSetListHeight } from '@/utils/method';

const Search = Input.Search;
const TabPane = Tabs.TabPane;

const DataCompStyle = {
  display: 'inline-block',
  width: 240,
};

const Lax = () => {
  const newAddRef: any = useRef();
  const rectifyRef: any = useRef();
  const [pagination, setPagination] = useState({ pageSize: 20, current: 1, total: 0 });
  const [listLoading, setListLoading] = useState(false);
  const [list, setList] = useState<any>([]);
  const downloadRef: any = useRef();
  const [search, setSearch] = useState<any>(undefined);
  const [loading, setLoading] = useState<any>(false);

  const [filterHeight, setFilterHeight] = useState(`100px`);

  const [timeFilter, setTimeFilter] = useState<any>({
    neatenTimePre: undefined,
    neatenTimePrx: undefined,
    neatenEndTimepre: undefined,
    neatenEndTimeprx: undefined,
  });
  const subordinate = getSession('subordinate') || '0';
  const org = getSession('org') || {};
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 50,
      align: 'center',
      render: (text, record, index) => {
        return (pagination['current'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    {
      title: '涣散组织',
      width: 200,
      dataIndex: 'name',
      render: (text, record, index) => (
        <a
          onClick={() => {
            newAddRef.current.open(record);
          }}
        >
          {text}
        </a>
      ),
    },
    {
      title: '涣散类型',
      width: 200,
      dataIndex: 'd74Name',
    },
    {
      title: '是否整顿',
      width: 60,
      align: 'center',
      dataIndex: 'hasNeaten',
      render: (text, record, index) => {
        return text == 0 ? '否' : '是';
      },
    },
    {
      title: '整顿开始时间',
      width: 100,
      dataIndex: 'neatenTime',
      align: 'center',
      render: (text) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '整顿结束时间',
      width: 100,
      dataIndex: 'neatenEndTime',
      align: 'center',
      render: (text) => {
        return text ? moment(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 160,
      align: 'center',
      render: (text, record, index) => {
        // 当有整顿结束时间，不让操作
        if (record?.neatenEndTime) {
          return (
            <Fragment>
              <a
                onClick={() => {
                  newAddRef.current.open(record);
                }}
              >
                编辑
              </a>
              <Divider type="vertical" />
              <a
                onClick={() => {
                  rectifyRef.current.open(record);
                }}
              >
                整顿
              </a>
              <Divider type="vertical" />
              <Popconfirm
                title="确定要删除吗？"
                onConfirm={async () => {
                  const { code = 500 } = await delSlackOrg({ code: record.code });
                  if (code === 0) {
                    message.success('操作成功');
                    getLists();
                  }
                }}
              >
                <a className={'del'}>删除</a>
              </Popconfirm>
            </Fragment>
          );
        }
        return (
          <Fragment>
            <a
              onClick={() => {
                newAddRef.current.open(record);
              }}
            >
              编辑
            </a>
            <Divider type="vertical" />
            <a
              onClick={() => {
                rectifyRef.current.open(record);
              }}
            >
              整顿
            </a>
            <Divider type="vertical" />
            <a
              onClick={() => {
                let neatenEndTime;
                Modal.confirm({
                  title: '结束整顿',
                  content: <Date onChange={(val) => (neatenEndTime = val)} />,
                  onOk: () => {
                    return new Promise<void>((resolve, reject) => {
                      setTimeout(() => {
                        if (neatenEndTime) {
                          updateSlackOrg({
                            data: {
                              ...record,
                              neatenEndTime: neatenEndTime.valueOf(),
                            },
                          }).then((res) => {
                            if (res['code'] == '0') {
                              tip.success('操作提示', '结束整顿成功');
                              getLists();
                              resolve();
                            } else {
                              reject();
                            }
                          });
                        } else {
                          tip.error('操作提示', '请输入结束整顿时间');
                          reject();
                        }
                      }, 150);
                    });
                  },
                });
              }}
            >
              结束整顿
            </a>
            <Divider type="vertical" />
            <Popconfirm
              title="确定要删除吗？"
              onConfirm={async () => {
                const { code = 500 } = await delSlackOrg({ code: record.code });
                if (code === 0) {
                  message.success('操作成功');
                  getLists();
                }
              }}
            >
              <a className={'del'}>删除</a>
            </Popconfirm>
          </Fragment>
        );
      },
    },
  ];
  // 获取涣散组织列表
  const getLists = async (p = {}) => {
    setListLoading(true);
    const { code = 500, data: { list = [], pageNumber: current = 1, pageSize = 20, totalRow: total = 0 } = {} } = await getListLax({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
        orgCode: org['orgCode'],
        orgName: '',
        // d74CodeList: [],
        ...p,
      },
    });
    setListLoading(false);
    if (code === 0) {
      setList(list);
      setPagination({ current, total, pageSize });
    }
  };
  const onBlur = (v, key) => {
    const obj = { ...timeFilter, [key]: v ? moment(v).valueOf() : undefined };

    // neatenTimePre: undefined,
    // neatenTimePrx: undefined,
    // neatenEndTimepre: undefined,
    // neatenEndTimeprx: undefined,

    let flag = true;
    if (obj?.neatenTimePre && obj?.neatenTimePrx && obj?.neatenTimePre > obj?.neatenTimePrx) {
      flag = false;
      tip.error('操作提示', '整顿开始时间范围有误，请重新输入');
    }
    if (obj?.neatenEndTimepre && obj?.neatenEndTimeprx && obj?.neatenEndTimepre > obj?.neatenEndTimeprx) {
      flag = false;
      tip.error('操作提示', '整顿结束时间范围有误，请重新输入');
    }

    setTimeFilter(obj);
    flag && getLists({ ...obj });
  };

  useEffect(() => {
    getLists();
    hookSetListHeight(setFilterHeight, 100);
  }, [org['orgCode'], subordinate]);

  return (
    <Fragment>
      <Tabs defaultActiveKey="1">
        <TabPane tab="基本信息" key="1" />
      </Tabs>
      <NowOrg
        extra={
          <Fragment>
            <Button
              onClick={async () => {
                setLoading(true);
                await downloadRef.current.submitNoModal();
                setLoading(false);
              }}
              loading={loading}
            >
              导出
            </Button>
            <Button
              type="primary"
              style={{ marginLeft: 16 }}
              icon={<LegacyIcon type={'plus'} />}
              onClick={() => {
                newAddRef.current.open();
              }}
            >
              新增
            </Button>
            <Search
              style={{ width: 200, marginLeft: 16 }}
              placeholder={'请输入检索关键词'}
              onChange={(e) => {
                if (!e.target.value) {
                  setSearch('');
                  getLists({ orgName: '', pageNum: 1 });
                }
              }}
              onSearch={(value) => {
                setSearch(value);
                getLists({ orgName: value, pageNum: 1 });
              }}
            />
          </Fragment>
        }
      />
      <div>
        <div style={{ marginBottom: 10 }}>
          整顿开始时间：
          <DateTime style={DataCompStyle} onBlur={(v) => onBlur(v, 'neatenTimePre')}></DateTime> -
          <DateTime style={DataCompStyle} onChange={(e) => {}} onBlur={(v) => onBlur(v, 'neatenTimePrx')}></DateTime>
        </div>
        <div>
          整顿结束时间：
          <DateTime style={DataCompStyle} onBlur={(v) => onBlur(v, 'neatenEndTimepre')}></DateTime>-
          <DateTime style={DataCompStyle} onBlur={(v) => onBlur(v, 'neatenEndTimeprx')}></DateTime>
        </div>
      </div>
      {/* <RuiFilter data={filterData} onChange={this.filterChange}/> */}
      <WhiteSpace />
      <WhiteSpace />
      <ListTable
        rowClassName={(record) => {
          if (record['code'] && record['code'] === org['code']) {
            return 'toptable';
          }
          return '';
        }}
        scroll={{
          x: columns.reduce((total: any, it: any) => {
            return total + it.width;
          }, 80),
          y: filterHeight,
        }}

        columns={columns}
        data={list}
        pagination={pagination}
        onPageChange={(page, pageSize) => {
          getLists({ pageNum: page, pageSize });
        }}
      />

      <NewAdd
        ref={newAddRef}
        onOk={() => {
          getLists({ pageNum: 1 });
          newAddRef.current.close();
        }}
      />
      <Rectify
        ref={rectifyRef}
        onOk={() => {
          getLists({ pageNum: 1 });
        }}
      />
      <ExportInfo wrappedComponentRef={downloadRef} tableName={''} noModal={true} tableListQuery={{ orgName: search, orgCode: org['orgCode'] }} action={'/api/org/slack/export'} />
    </Fragment>
  );
};
export default Lax;
