import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Form, Input, Modal, Popconfirm, Row, Col, Select, Button } from 'antd';
import moment from 'moment';
import Tip from '@/components/Tip';
import { ArrowDownOutlined } from '@ant-design/icons';
import { moveOrg } from '@/pages/org/services/org';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import _get from 'lodash/get';
// import DictSelect from '@/components/DictSelect';
import OrgSelect from '@/components/OrgSelect';
// import MemSelect from '@/components/MemSelect';
// import { findDictCodeName } from '@/utils/method';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

const index = (props: any, ref) => {
  const { unit: { basicInfo = {} } = {}, onOK } = props;
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [title, setTitle] = useState('组织架构调整');
  const [currentOrg, setCurrentOrg] = useState({ name: '' });
  const [targetOrg, setTargetOrg] = useState({ name: '' });

  useImperativeHandle(ref, () => ({
    open: (dataInfo) => {
      open(dataInfo);
    },
  }));
  const open = (dataInfo) => {
    setVisible(true);
    // if (!_isEmpty(dataInfo)) {
    //   setTitle('编辑');
    //   if (_isNumber(dataInfo['year'])) {
    //     dataInfo['year'] = moment(dataInfo['year']).format('YYYY');
    //   }
    //   setDataInfo(dataInfo);
    //   form.setFieldsValue({
    //     ...dataInfo,
    //   });
    // }
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {

    setConfirmLoading(true);
    const { code: resCode = 500 } = await moveOrg({
      data: {
        srcOrgId: _get(e, 'srcOrgId[0].code', undefined),
        targetOrgId: _get(e, 'targetOrgId[0].code', undefined),
      },
    });
    setConfirmLoading(false);
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK(e);
    }
  };
  return (
    <Fragment>
      <Modal
        title={title}
        destroyOnClose
        visible={visible}
        confirmLoading={confirmLoading}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={'800px'}
        footer={[
          <Button key={1} htmlType={'button'} onClick={handleCancel} style={{ marginRight: '5px' }}>取消</Button>,
          <Popconfirm placement="topRight" title={<span>
            此操作将会把 <span style={{ color: 'red' }}>{currentOrg.name}</span> 组织及其相关信息移动到<span style={{ color: 'red' }}>{targetOrg.name}</span>组织下，请谨慎操作！
          </span>} onConfirm={() => { form.submit() }} disabled={_isEmpty(currentOrg.name) || _isEmpty(targetOrg.name)}>
            <Button key={2} htmlType={'button'} type={'primary'} disabled={_isEmpty(currentOrg.name) || _isEmpty(targetOrg.name)} loading={confirmLoading}>确定</Button>
          </Popconfirm>,
        ]}
      >
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Form.Item
            name="srcOrgId"
            label="拟调整党组织"
            rules={[
              { required: true, message: '拟调整党组织' },
            ]}
          >
            <OrgSelect key={1}
              destroyOnClose={true}
              onChange={(e: any) => {
                if (!_isEmpty(e)) {
                  setCurrentOrg(e[0])
                }
              }}
              placeholder={'请选择拟调整党组织'} />
          </Form.Item>
          <div style={{ textAlign: 'center', fontSize: 30, marginBottom: 16 }}>
            <ArrowDownOutlined />
          </div>
          <Form.Item
            name="targetOrgId"
            label="目的上级党组织"
            rules={[
              { required: true, message: '目的上级党组织' },
            ]}
          >
            <OrgSelect orgTypeList={["1", "2", "5"]}
             key={2}
             destroyOnClose={true}
              onChange={(e: any) => {
                if (!_isEmpty(e)) {
                  setTargetOrg(e[0])
                }
              }}
              placeholder={'请选择目的上级党组织'} />
          </Form.Item>
        </Form>
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
