import React, { Fragment, useState, useRef, useEffect } from 'react';
import { Button, Input, Divider, Popconfirm, Tabs, message, Modal } from 'antd';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import WhiteSpace from '@/components/WhiteSpace';
import ListTable from 'src/components/ListTable';
import NowOrg from '@/components/NowOrg';
import { getSession } from '@/utils/session';
import { natureGetList, natureDelete } from '../services/org';
import Add from './components/add';
import Date from '@/components/Date';
import tip from '@/components/Tip';
import ExportInfo from '@/components/Export/index';
import RuiFilter from 'src/components/RuiFilter';
import { connect } from 'dva';
const Search = Input.Search;
const TabPane = Tabs.TabPane;

const index = (props:any) => {
  const newAddRef: any = useRef();
  const [pagination, setPagination] = useState<any>({ pageSize: 20, current: 1, total: 0 });
  const [listLoading, setListLoading] = useState(false);
  const [list, setList] = useState<any>([]);
  const org = getSession('org') || {};
  const downloadRef:any = useRef();
  const [search, setSearch] = useState<any>(undefined);
  const [ruiFilter, setRuiFilter] = useState<any>({});
  const [loading, setLoading] = useState<any>(false);
  const subordinate = getSession('subordinate') || '0';
  const filterData = [
    {
      key: 'd122CodeList', name: `组织类别`, value: props.commonDict[`dict_d122_tree`],
    },
    {
      key: 'd96CodeList', name: '组织性质 ', value: props.commonDict[`dict_d96_tree`],
    },
    // {
    //   key: 'd01CodeList', name: '党组织类别', value: props.commonDict[`dict_d01_tree`],
    // },
  ];
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 50,
      align: 'center',
      render: (text, record, index) => {
        return (pagination['current'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    {
      title: '组织名称',
      width: 200,
      dataIndex: 'industryOrgName',
    },
    {
      title: '行业分类',
      width: 200,
      dataIndex: 'industryClassificationName',
    },
    {
      title: '隶属关系',
      width: 100,
      dataIndex: 'membershipFunctionName',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 160,
      align: 'center',
      render: (text, record, index) => {
        return (
          <Fragment>
            <a
              onClick={() => {
                newAddRef.current.open(record.code);
              }}
            >
              编辑
            </a>
            <Divider type="vertical" />
            <Popconfirm
              title="确定要删除吗？"
              onConfirm={async () => {
                const { code = 500 } = await natureDelete({ code: record.code });
                if (code === 0) {
                  message.success('操作成功');
                  getLists();
                }
              }}
            >
              <a className={'del'}>删除</a>
            </Popconfirm>
          </Fragment>
        );
      },
    },
  ];
  const getLists = async (p = {}) => {
    setListLoading(true);
    const {
      code = 500,
      data: {
        list = [],
        records = [],
        pageNumber: current = 1,
        pageSize = 20,
        totalRow: total = 0,
      } = {},
    } = await natureGetList({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
        orgCode: org['orgCode'],
        orgName:search,
        ...ruiFilter,
        ...p,
      },
    });
    setListLoading(false);
    if (code === 0) {
      setList(list);
      setPagination({ current, total, pageSize });
    }
  };

  const filterChange=(val)=>{
    getLists({ pageNum: 1,...val});
    setRuiFilter(val);
  }
  useEffect(() => {
    const dictData=['dict_d122','dict_d01','dict_d96'];
    for(let obj of dictData){
      props.dispatch({
        type:'commonDict/getDictTree',
        payload:{
          data:{
            dicName:obj
          }
        }
      });
    }
  }, []);
  useEffect(() => {
    if(org['orgCode']){
      getLists();
    }
  }, [org['orgCode'],subordinate]);


  return (
    <Fragment>
      <Tabs defaultActiveKey="1">
        <TabPane tab="基本信息" key="1" />
      </Tabs>
      <NowOrg
        extra={
          <Fragment>
            <Button onClick={async ()=>{
            setLoading(true);
            await downloadRef.current.submitNoModal();
            setLoading(false);
          }} loading={loading}>导出</Button>
            <Button
              type="primary"
              style={{ marginLeft: 16 }}
              icon={<LegacyIcon type={'plus'} />}
              onClick={() => {
                newAddRef.current.open();
              }}
            >
              新增
            </Button>
            <Search
              style={{ width: 200, marginLeft: 16 }}
              placeholder={'请输入检索关键词'}
              onChange={(e)=>{
                if(!e.target.value) {
                  setSearch('');
                  getLists({ orgName: '', pageNum: 1 });
                }
              }}
              onSearch={(value) => {
                setSearch(value);
                getLists({ orgName: value,pageNum:1 });
              }}
            />
          </Fragment>
        }
      />
      <RuiFilter data={filterData}
                   onChange={filterChange}
                   openCloseChange={() => {
                   }}/>
      <WhiteSpace />
      <WhiteSpace />
      <ListTable
        scroll={{
          x: columns.reduce((total: any, it: any) => {
            return total + it.width;
          }, 80),
        }}

        columns={columns}
        data={list}
        pagination={pagination}
        onPageChange={(page, pageSize) => {
          getLists({ pageNum: page, pageSize });
        }}
      />
      <Add ref={newAddRef} onOK={()=>{
        getLists({pageNum:1})
      }}/>
       <ExportInfo wrappedComponentRef={downloadRef}
                    tableName={''}
                    noModal={true}
                    tableListQuery={{orgName:search,orgCode:org['orgCode'],...ruiFilter}}
                    action={'/api/org/special/export'}
        />
    </Fragment>
  );
};
// @ts-ignore
export default connect(({ commonDict }) => ({ commonDict }))(index);

