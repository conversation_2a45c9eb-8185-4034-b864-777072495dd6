import {listPageModel, ListPageStore} from "@/utils/common-model";
import modelExtend from "dva-model-extend";
import {
  userAdd,
  userList,
  userUpdate,
  userDelete,
  userChangePW,
  userBulk,
  getDictionary,
  getList,
  getOrgTree,
  updateUser<PERSON>er<PERSON>son,
  updateRoleId,
  findUserByAccount,
  getUserByPage,
  editUserList,
  edit,
  upPassword,
  unlock,
  lock,
  batchEdit,
  findByPage,
  exist,
  getListAndValid,
  findPermissionByRoleId,
  findPermissionByPermissionId,
  permissionEdit,
  permissionList,
  searchUserByKeyword
} from  "../service"
import Notice from '../../../../components/Notice';
const user=modelExtend(listPageModel,{
  namespace:'user',
  state:{
    data:[],
    getList:[],
    getRole:[],
    isDelete:false,
    treeData:[],
    isAdd:false,
    isUp:null,
    isCg:null,
    RoleIdInfo:[],
  },
  subscriptions:{

  },
  effects:{
    *add({payload},{call,put}){
      const info = yield call(userAdd, payload);
      return Promise.resolve(info);
    },
    *list({payload},{call,put}){
      const info = yield call(getUserByPage, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'querySuccess',
          payload: {list:list,pagination:pagination}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *editUsers({payload},{call,put}) {
      const info = yield call(editUserList, payload);
      return Promise.resolve(info);
    },
    //
    *getPermissionByRoleId({payload},{call,put}) {
      const res = yield call(findPermissionByRoleId, payload);
      let userRole:Array<string>=[];
      if(res['data']){
        for(let obj of res['data']){
          if(obj['id']){
            userRole.push(`${obj['id']}`);
          }
        }
      }
      yield put({
        type:'updateState',
        payload:{
          userRole
        }
      })
    },
    *getByPage({payload},{call,put}) {
      const info = yield call(findByPage, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'updateState',
          payload: {list2:list,pagination2:pagination}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    //用户权限
    *permissionList({payload},{call,put}) {
      const info = yield call(permissionList, payload);
      return Promise.resolve(info);
    },
    *getPermissionByPermissionId({payload},{call,put}) {
      const info = yield call(findPermissionByPermissionId, payload);
      return Promise.resolve(info);
    },
    //用户权限保存permissionEdit
    *getPermissionEdit({payload},{call,put}) {
      const info = yield call(permissionEdit, payload);
      return Promise.resolve(info);
    },
    *edit({payload},{call,put}) {
      const info = yield call(edit, payload);
      return Promise.resolve(info);
    },
    //判断用户名是否存在
    *getExist({payload},{call,put}) {
      const info = yield call(exist, payload);
      return Promise.resolve(info);
    },
    *upPassword({payload},{call,put}) {
      const info = yield call(upPassword, payload);
      return info;
      // return Promise.resolve(info);
    },
    *getBatchEdit({payload},{call,put}) {
      const info = yield call(batchEdit, payload);
      return Promise.resolve(info);
    },
    //搜索
    *getSearchUserByKeyword({payload},{call,put}) {
      const info = yield call(searchUserByKeyword, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'querySuccess',
          payload: {list:list,pagination:pagination}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *getSearchUserByKeyword1({payload},{call,put}) {
      const info = yield call(searchUserByKeyword, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'updateState',
          payload: {list1:list,pagination1:pagination}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *listSelect({payload},{call,put}){
      const info = yield call(getUserByPage, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'updateState',
          payload: {list1:list,pagination1:{...pagination,total:pagination['totalRow']}}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },

    *delete({payload},{call,put}){
      const info = yield call(userDelete,payload);
      return Promise.resolve(info);
    },
    *isLocks({payload},{call,put}){
      const info = yield call(lock,payload);
      return Promise.resolve(info);
    },
    *isUnLocks({payload},{call,put}){
      const info = yield call(unlock,payload);
      return Promise.resolve(info);
    },
    *userChange({payload},{call,put}){
      const info = yield call(userChangePW,payload);
      const { code=0,message='操作失败' } = info;
      if (code===0){
        Notice("操作提示",message,"check-circle","green");
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *userBulking({payload},{call,put}){
      const info = yield call(userBulk,payload);
      const { code=0,message='操作失败' } = info;
      if (code===0){
        Notice("操作提示",message,"check-circle","green");
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *getDictionarying({payload},{call,put}){
      const info = yield call(getDictionary,payload);
      const { code=0,message='操作失败' } = info;
      if (code===0){
        const { data } = info;
        yield put({
          type:'success',
          payload:{
           data
          }
        })
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    //获取角色列表
    *getListing({payload},{call,put}){
      const info = yield call(getListAndValid,payload);
      const { code=0,data:{ list=[] }={},message='操作失败' } = info;
      if (code===0){
        yield put({
          type:'success',
          payload:{
            getList:list
          }
        })
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },

    *orgTree({payload},{call,put,select}){
      const state=yield select(state=>state['common']);
      const info = yield call(getOrgTree,payload);
      const { code=0,data=[],message='操作失败' } = info;
      let {mapTree,mapTreeCode}=state;
      if (code===0){
        for(let obj of info['data']){
          mapTree.set(obj['orgCode'],obj);
          mapTreeCode.set(obj['code'],obj);
        }
        let data:Array<object>=[];
        for(let obj of mapTree){
          data.push(obj[1])
        }
        yield put({
          type:'updateState',
          payload:{
            listTree:data,
            mapTree,
            mapTreeCode
          }
        });
        // yield put({
        //   type:'success',
        //   payload:{
        //     treeData:data
        //   }
        // })
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *updateUserPermisson({payload},{call,put}){
      const info = yield call(updateUserPermisson,payload);
      const { code=0,data=[],message='操作失败' } = info;
      if (code===0){
        yield put({
          type:'success',
          payload:{
            treeData:data
          }
        })
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    //updateRoleId
    *updateRoleIding({payload},{call,put}){
      const info = yield call(updateRoleId,payload);
      const { code=0,data=[],message='操作失败' } = info;
      if (code===0){
        yield put({
          type:'success',
          payload:{
            RoleIdInfo:data
          }
        })
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *findUserByAccounting({payload},{call,put}){
      const info = yield call(findUserByAccount,payload);
      const { code=0,message='操作失败',data={} } = info;
      if (code===0){
        // let a=false;
        // if (data.code===0){
        //   a=true
        // } else {
        //   a=false
        // }
        //
        //   yield put({
        //   type:'success',
        //   payload:{
        //     repeat:a
        //   }
        // })
        return data
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *clear({ payload }, { call, put, select }){
      yield put({
        type:'updateState',
        payload:{
          org:undefined,
          listTree:undefined,
          mapTree:undefined,
          mapTreeId:undefined,
        }
      });
    },
  },
  reducers: {
    success(state, {payload}) {
      return {...state, ...payload};
    },
  }
});
export default user
