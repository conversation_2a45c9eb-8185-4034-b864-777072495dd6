import React, { Fragment, useImperativeHandle, useState } from 'react';
import { Form, Input, Modal, DatePicker, InputNumber, Row, Col } from 'antd';
// import moment from 'moment';
import Tip from '@/components/Tip';
import { add } from './services';
import _isEmpty from 'lodash/isEmpty';

const formItemLayout = {
  labelCol: { span: 12 },
  wrapperCol: { span: 10 },
};

const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [title, setTitle] = useState('新增');
  const [streetCadres, setStreetCadres] = useState(0);
  useImperativeHandle(ref, () => ({
    open: (dataInfo) => {
      open(dataInfo);
    },
  }));
  const open = (dataInfo) => {
    // console.log('dataInfo==', dataInfo);
    setVisible(true);
    if (!_isEmpty(dataInfo)) {
      setTitle('编辑');
      setDataInfo(dataInfo);
      form.setFieldsValue({
        ...dataInfo,
      });
    }
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
    setStreetCadres(0);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    // if (!_isEmpty(e)) {
    //   e['year'] = moment(e['year'], 'YYYY').valueOf();
    // }
    const { unit: { basicInfo = {} } = {}, onOK } = props;
    let val = {
      ...e,
      unitCode: basicInfo?.code,
      // orgCode: basicInfo?.createUnitOrgCode,
      orgCode: basicInfo?.mainOrgCode,
      orgName: basicInfo?.mainOrgName,
      // id: dataInfo?.id,
      code: dataInfo?.code,
    };
    // console.log(val, 'val');

    setConfirmLoading(true);
    const { code: resCode = 500 } = await add({
      data: {
        ...val,
      },
    });
    setConfirmLoading(false);
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  const streetCadresChange = (val) => {
    // console.log('val===', val);
    setStreetCadres(val);
  };
  const YearValidator = (rule, value, callback) => {
    if (value && (value < 1900 || value > 2100)) {
      return callback('请输入正确年份');
    } else {
      return callback();
    }
  };
  return (
    <Fragment>
      <Modal
        title={title}
        destroyOnClose
        visible={visible}
        confirmLoading={confirmLoading}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={'1000px'}
      >
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Row>
            <Col span={12}>
              <Form.Item
                name="year"
                label="年份"
                rules={[{ required: true, message: '请输入年份' }, { validator: YearValidator }]}
              >
                {/* <DatePicker onChange={() => {}} picker="year" /> */}
                <InputNumber style={{ width: '100%' }} maxLength={4} minLength={4} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="streetCadres"
                label="街道干部人数"
                rules={[{ required: true, message: '请输入街道干部人数' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={9999999999}
                  onChange={streetCadresChange}
                />
              </Form.Item>
            </Col>
            {(streetCadres > 0 || form.getFieldValue('streetCadres') > 0) && (
              <Fragment>
                <Col span={12}>
                  <Form.Item
                    name="age35Below"
                    label="街道干部35岁及以下人数"
                    rules={[{ required: true, message: '请输入街道干部35岁及以下人数' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={form.getFieldValue('streetCadres')}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="age36ToAge55"
                    label="街道干部36至55岁人数"
                    rules={[{ required: true, message: '请输入街道干部36至55岁人数' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={form.getFieldValue('streetCadres')}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="age56Above"
                    label="街道干部56岁及以上人数"
                    rules={[{ required: true, message: '请输入街道干部56岁及以上人数' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={form.getFieldValue('streetCadres')}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="collegeDegreeAbove"
                    label="街道干部大专及以上学历人数"
                    rules={[{ required: true, message: '请输入街道干部大专及以上学历人数' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={form.getFieldValue('streetCadres')}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="secondarySchoolBelow"
                    label="街道干部高中中专及以下人数"
                    rules={[{ required: true, message: '请输入街道干部高中中专及以下人数' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={form.getFieldValue('streetCadres')}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="streetCadresCivil"
                    label="街道干部公务员人数"
                    rules={[{ required: true, message: '请输入街道干部公务员人数' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={form.getFieldValue('streetCadres')}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="streetCadresInstitutions"
                    label="街道干部事业单位人数"
                    rules={[{ required: true, message: '请输入街道干部事业单位人数' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={form.getFieldValue('streetCadres')}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="cadreOther"
                    label="街道干部其他身份人数"
                    rules={[{ required: true, message: '请输入街道干部其他身份人数' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={form.getFieldValue('streetCadres')}
                    />
                  </Form.Item>
                </Col>
              </Fragment>
            )}
          </Row>
        </Form>
      </Modal>
    </Fragment>
  );
};
export default React.forwardRef(index);
