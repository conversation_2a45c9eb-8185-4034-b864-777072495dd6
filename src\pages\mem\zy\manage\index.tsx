/*
* 人员管理
* */
import React, {Fragment} from 'react';
import {Tabs} from 'antd';
import _isEmpty from 'lodash/isEmpty'
import {connect} from 'dva';
import MemBasic from './components/membasic';

const TabPane = Tabs.TabPane;
// @ts-ignore
@connect(({loading,memBasic})=>({loading,memBasic}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);

  }
  onChange =(val)=>{
  };
  render(): React.ReactNode {
    const memTabs = [
      {key:'1',title:'基本信息',component: <MemBasic {...this.props}/>},
      // {key:'2',title:'纠错信息',component: <Error {...this.props}/>}
    ];
    return (
      <div style={{height:'100%',overflow:'hidden'}}>
        {/* <Tabs defaultActiveKey="1" onChange={this.onChange}>
          {
            !_isEmpty(memTabs) && memTabs.map(item=> <TabPane tab={item['title']} key={item['key']}/>)
          }
        </Tabs> */}
        <MemBasic {...this.props}/>
      </div>
    )
  }
}
