import request from '@/utils/request';
import qs from 'qs';
export async function uploadReport2020Data(params) {
  return request('/api/annual/uploadReport2020Data',{ // 上传往年数据
    method:'POST',
    body:params
  });
}
export async function job(params) {
  return request(`/api/jobInfo/getList?${qs.stringify(params)}`,{//获取领导岗位列表
    method:'GET',
  });
}
export async function saveExcelConfig(params) {
  return request('/api/annual/saveReportRuleConfig',{//获取领导岗位
    method:'POST',
    body:params
  });
}
export function queryExcelConfigTreeById(params) {
  return request(`/api/annual/getReportTree?${qs.stringify(params)}`);
}
export function queryExcelConfigTreeById1(params) {
  return request(`/api/annual/getReportTree?${qs.stringify(params)}`);
}
export function findExcelConfigByKey(params) {
  return request(`/api/annual/findReportRuleConfig?${qs.stringify(params)}`);
}
// export function queryExcelConfigReturnHtml(params) {
//   return request(`/api/annual/getReportHtml?${qs.stringify(params)}`);
// }

// 2022统计半年报
export function queryExcelConfigReturnHtmlExplain(params) {
  // return request(`/api/annual/getHalfYearReportHtml?${qs.stringify(params)}`);
  return request(`/api/annual/getReportExplainHtml?${qs.stringify(params)}`);
}
export function queryExcelConfigReturnHtml(params) {
  // return request(`/api/annual/getHalfYearReportHtml?${qs.stringify(params)}`);
  return request(`/api/annual/getReportHtml?${qs.stringify(params)}`);
}
// 2022统计半年统说明
export function queryExcelConfigReturnHtmlIns(params) {
  return request(`/api/annual/getHalfYearReportRosterHtml?${qs.stringify(params)}`);
}
// 往年统计项
export function overTheYears(params) {
  return request(`/api/annual/previous`);
}

export function copyConfig(params) {
  return request(`/api/annual/copyReportRuleConfig?${qs.stringify(params)}`);
}
export async function getReportPegging(params) {
  return request('/api/annual/getReportPegging',{//获取领导岗位
    method:'POST',
    body:params
  });
}
