import React, { useState } from "react";
import { Form, Input, Button, Descriptions } from "antd"
import ListTable from '@/components/ListTable';

import { getUniqueCodeProcess } from "../../../services/index"

const index = () => {
    const [form] = Form.useForm();
    const [formData, setFormData] = useState<any>({});



    const columns: any = [
        {
            title: '序号',
            dataIndex: 'num',
            align: 'center',
            width: 60,
            render: (text, record, index) => {
                return index + 1;
            }
        },
        {
            title: '电子介绍信唯一码',
            dataIndex: 'uniqueKey',
            align: 'center',
            width: 150
        },
        {
            title: '经办党组织代码',
            dataIndex: 'blgc0001',
            align: 'center',
            width: 60
        },
        {
            title: '经办党组织名称',
            dataIndex: 'blgc0002',
            align: 'center',
            width: 60
        },
        {
            title: '经办人',
            dataIndex: 'blgc0003',
            align: 'center',
            width: 60
        },
        {
            title: '经办人联系电话',
            dataIndex: 'blgc0004',
            align: 'center',
            width: 60
        },
        {
            title: '办理日期',
            dataIndex: 'blgc0005',
            align: 'center',
            width: 60
        },
        {
            title: '办理情况',
            dataIndex: 'blgc0006',
            align: 'center',
            width: 60
        },
        {
            title: '办理意见',
            dataIndex: 'blgc0007',
            align: 'center',
            width: 60
        },
        {
            title: '与党员见面人',
            dataIndex: 'blgc0009',
            align: 'center',
            width: 60
        },
        {
            title: '党员报到日期',
            dataIndex: 'blgc0010',
            align: 'center',
            width: 60
        },
        // {
        //     title: '顺序号',
        //     dataIndex: 'blgc0011',
        //     align: 'center',
        //     width: 60
        // },
        {
            title: '是否由上级觉组织代办',
            dataIndex: 'blgc0012',
            align: 'center',
            width: 60
        },
        // {
        //     title: '备注',
        //     dataIndex: 'blgc0008',
        //     align: 'center',
        //     width: 60
        // }
    ];

    const onFinish = async (values) => {
        console.log(values);
        const { code, data } = await getUniqueCodeProcess({ ...values })
        if (code == 0) {
            console.log(data);
            setFormData(data)
        }
    }
    return (
        <div>
            <Form layout={'inline'} onFinish={onFinish} form={form}>
                <Form.Item label="唯一码" name="uniqueCode"
                    rules={[
                        { required: true, message: '请输入' }
                    ]}
                >
                    <Input style={{ width: '300px' }} placeholder={'请输入唯一码'} />
                </Form.Item>
                <Form.Item>
                    <Button type={'primary'} onClick={form.submit}>
                        查询
                    </Button>
                </Form.Item>
            </Form>
            <div style={{ width: '100%', marginTop: '20px' }}>
                <Descriptions column={2} title="基本信息">
                    <Descriptions.Item span={1} label="源组织">{formData?.srcOrg}</Descriptions.Item>
                    <Descriptions.Item span={1} label="目的地组织">{formData?.traOrg}</Descriptions.Item>
                    <Descriptions.Item span={1} label="姓名">{formData?.name}</Descriptions.Item>
                    <Descriptions.Item span={1} label="身份证号">{formData?.idCard}</Descriptions.Item>
                    <Descriptions.Item span={1} label="介绍信唯一编码">{formData?.uniqueKey}</Descriptions.Item>
                </Descriptions>
                <h3 style={{ fontWeight: 800 }}>办理过程</h3>
                <ListTable
                    columns={columns}
                    data={formData?.blgc || []}
                    pagination={undefined}
                    scroll={{
                        y: 500,
                    }}
                    onPageChange={undefined} />
            </div>
        </div>
    )
}

export default index;