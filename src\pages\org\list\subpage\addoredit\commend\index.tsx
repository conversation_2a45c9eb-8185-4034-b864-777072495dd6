import React, { Fragment, useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>lapse, Divider, Form, Input, Popconfirm, Alert } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import Add from './components/add';
import {recognitionGetList,getListByAnnual, findRecognitionByCode, recognitionDelete} from '@/pages/org/services/org'
import styles from '@/pages/org/list/subpage/addoredit/leader.less';
import {FormComp} from './components/add';
import AddMem from './components/addMem';
import Tip from '@/components/Tip';
import ListTable from '@/components/ListTable';
import moment from 'moment';

const Panel = Collapse.Panel;
const index = (props: any) => {
  const addRef:any = useRef();
  const addMemRef:any = useRef();
  const [form] = Form.useForm();
  const [data, setData] = useState([]);
  const [activeKey, setActiveKey]:any = useState(undefined);
  const [timeKey, setTimeKey] = useState(+new Date());
  const [itemData, setItemData] = useState([]);

  const collapseCallback = async (key) => {
    if(key){
      let k=key.pop();
      setActiveKey([k]);
      setItemData([]);
      const { basicInfo } = props.org;
      const { code:resCode = 500 , data = {}} = await getListByAnnual({
        data:{
          annual:moment(k).valueOf(),
          orgCode:basicInfo.code,
          pageNum:1,
          pageSize:100
        }
      })
      if(resCode === 0){
        setItemData(data['list'] || []);
      }
    }
  };
  const edit= (item)=>{
    addRef?.current?.open({code:item.code});
  }
  const detail = (item) =>{
    addRef?.current?.open({code:item.code},true);
  }
  const getList = async () =>{
    const { basicInfo } = props.org;
    const {code = 500 , data:{list = []} = {}} = await recognitionGetList({
      data:{
        orgCode:basicInfo.code,
        pageNum:1,
        pageSize:100
      }
    });
    if(code === 0){
      setData(list);
    }
  };
  const header=(item)=>{
    return (
      <span className={styles.header}>
        <span>表彰年度：{item['years']}</span>
      </span>
    )
  };

  useEffect(()=>{
    getList();
  },[]);
  const columns=[
    {
      title:'表彰时间',
      dataIndex:'annual',
      width:100,
      render:(text)=>{
        return text ? moment(text).format('YYYY.MM.DD') : undefined
      }
    },
    {
      title:'表彰文件号',
      dataIndex:'fileNo',
      width:100,
    },
    {
      title:'情况说明',
      dataIndex:'',
      render:(text,record)=>{
        return(
          <span>
            其中党委(总支部、支部)书记优秀共产党员{record['committeeParty'] || 0}名；
            党委(总支部、支部)书记优秀党务工作者{record['committeeWorker'] || 0}名；
            生活困难优秀共产党员{record['difficultParty'] || 0}名；
            生活困难优秀党务工作者{record['difficultWorker'] || 0}名
          </span>
        )
      }
    },
    {
      title:'操作',
      dataIndex: 'action',
      width:190,
      render:(text,record)=>{
        return(
          <React.Fragment>
            <a onClick={(e)=>edit(record)}>编辑</a>
            <Divider type={'vertical'}/>
            <a onClick={e=>detail(record)}>表彰情况</a>
            <Divider type={'vertical'}/>
            <a style={{color: 'red'}} onClick={async () =>{
              const {code = 500} = await recognitionDelete({code:record['code']});
              if(code == 0){
                Tip.success('操作提示', '操作成功');
                collapseCallback(activeKey).then(r => {});
              }
            }}>删除</a>
          </React.Fragment>
        )
      }
    }
  ]
  return (
    <Fragment>
      <Add ref={addRef} onOK={ async ()=>{
        getList();
        collapseCallback(activeKey).then(r => {});
      }} {...props}/>
      <AddMem ref={addMemRef} {...props}/>
      <Alert message="提示：填写本单位发出的表彰，不是填写本单位内党员获得的表彰，即本单位发出的表彰文件才进行统计。上下级之间不要重复统计。" type="info" showIcon />
      <div style={{marginBottom:10}}/>
      <Button type="primary"
        // @ts-ignore
              icon={<PlusOutlined/>}
              style={{ marginBottom: 10 }} onClick={()=>{
        addRef?.current?.open();
      }}>添加党内表彰情况</Button>
      <Button style={{marginLeft:10}} type="primary" onClick={()=>{
        addMemRef?.current?.open()
      }}>追授情况</Button>
      <Collapse activeKey={activeKey} onChange={collapseCallback}>
        {
          data.map((item, index) => {
            return (
              <Panel header={header(item)} key={item['years']} >
                <ListTable data={itemData} columns={columns} pagination={false} scroll={{y:300}}/>
              </Panel>
            );
          })
        }
      </Collapse>
    </Fragment>
  );
};
export default index;
