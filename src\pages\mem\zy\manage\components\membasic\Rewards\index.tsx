import React from 'react';
import ListTable from '@/components/ListTable';
import { getSession } from '@/utils/session';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Divider, Popconfirm, Button } from 'antd';
import RewardsAdd from './components/addEdit';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import moment from 'moment';
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      rewardType:''
    }
  }
  componentDidMount(): void {
    const {memBasic:{basicInfo:{code = ''} = {}}={}} = this.props;
    this.getList(1,10,code);
  };
  getList=(pageNum = 1, pageSize = 10, memCode = '')=>{
    !_isEmpty(memCode) && this.props.dispatch({
      type:'memRewards/getList',
      payload:{pageNum,pageSize, memCode}
    })
  };
  onPageChange=(page)=>{
    const {memBasic:{basicInfo:{code = ''} = {}}={}} = this.props;
    this.getList(page,10,code);
  };
  // 奖惩编辑
  add=(type)=>{
    this.setState({rewardType:type});
    this['rewardsAdd'].destroy();
    this['rewardsAdd'].open();
  };
  edit=(record = {})=>{
    this['rewardsAdd'].destroy();
    if(!_isEmpty(record['code'])){
      this.getRewardInfo(record['code']);
    }
    this['rewardsAdd'].open();
  };
  getRewardInfo=(code)=>{
    this.props.dispatch({
      type:'memRewards/getInfo',
      payload:{
        code
      }
    })
  };
  componentWillUnmount(): void {
    this.props.dispatch({
      type:'memRewards/clear',
      payload:{}
    })
  }
  //奖惩删除
  del=async({code = ''}={})=>{
    const {memBasic:{basicInfo:{code:memCode = ''} = {}}={}} = this.props;
    if(!_isEmpty(code)){
      const res = await this.props.dispatch({
        type:'memRewards/del',
        payload:{code}
      });
      const {code:resCode = 500} = res || {};
      if(resCode === 0){
        Tip.success('操作提示','删除成功');
        this.getList(1,10,memCode)
      }
    }
  };
  onclose=()=>{
    this.setState({rewardType:''})
  };
  render() {
    const {memRewards = {},loading:{effects = {}} ={}} = this.props;
    const {list, pagination} = memRewards;
    const {current, pageSize} = pagination;
    const {rewardType} = this.state;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:58,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'奖惩内容',
        dataIndex:'d029Name',
      },
      {
        title:'奖惩原因',
        dataIndex:'d01Name',
        render:(text,record,index)=>( <span>{record['type'] === 1 ? `${record['d47Name'] || ''}`:`${record['d030Name'] || ''}` }</span> )
      },
      {
        title:'奖惩批准机关',
        dataIndex:'orgName',
      },
      {
        title:'奖惩生效日期',
        dataIndex:'startDate',
        render:(text,record,index)=>{
          return (
            <div>{text && moment(text).format('YYYY-MM-DD')}</div>
          )
        }
      },
      {
        title:'奖惩类型',
        dataIndex:'type',
        render:(text,record,index)=>{
          return (
            <div>
              {text === 1 ? '奖励' : '惩戒'}
            </div>
          )
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        width:150,
        render:(text,record)=>{
          return(
            <span>
              <a onClick={()=>this.edit(record)}>编辑</a>
              <Divider type="vertical"/>
              <Popconfirm title="确定要移除吗？" onConfirm={()=>this.del(record)}>
               <a className={'del'} >移除</a>
              </Popconfirm>
            </span>
          )
        },
      },
    ];
    // console.log(rewardType,'rewardType');
    return (
      <div>
        <span style={{marginRight:'10px'}}><Button htmlType={'button'} type={'primary'} onClick={()=>this.add('1')}><LegacyIcon type={'add'}/>添加奖励信息</Button></span>
        <Button htmlType={'button'} type={'primary'} onClick={()=>this.add('0')}><LegacyIcon type={'add'}/>添加惩罚及出党信息</Button>
        <div style={{marginBottom:'20px'}}/>
        <ListTable columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
        <RewardsAdd wrappedComponentRef={e=>this['rewardsAdd'] = e} {...this.props} type={rewardType} onclose={this.onclose}/>
      </div>
    );
  }
}
