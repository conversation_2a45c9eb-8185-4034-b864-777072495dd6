import React from 'react';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Menu } from 'antd';
import { SliderMenu } from 'src/common/menu';
// import {_history as router} from "@/utils/method";
import { history } from 'umi';
import { getSession } from '@/utils/session';
import styles from './index.less';
export default class index extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedKeys: [],
    };
  }
  onSelect = (item) => {
    const { data } = this.props;
    sessionStorage.setItem('menuItemKey', item['key']);
    this.setState({
      selectedKeys: item['keyPath'],
    });
    const find = data.find((obj) => obj['code'] === item['key']);
    if (find) {
      history.push(find['url']);
    }
  };
  render() {
    const keys = getSession('menuItemKey');
    // const menuKey=getSession('menuKey');
    const { data } = this.props;
    let { selectedKeys } = this.state;
    if (selectedKeys.length === 0) {
      selectedKeys = [`${keys}`];
    } else {
      if (!selectedKeys[0].startsWith('menuKey')) {
        selectedKeys = [`${keys}`];
      }
    }
    // console.log(selectedKeys,keys,'keyskeyskeys')
    return (
      <Menu
        mode="inline"
        selectedKeys={selectedKeys}
        onSelect={this.onSelect}
        className={'newFishInfo2'}
        style={{ border: 'none' }}
      >
        {data &&
          data.map((obj) => (
            <Menu.Item key={obj['code']}>
              <div
                className={styles.icv}
                style={obj['color'] ? { background: obj['color'] } : undefined}
              >
                <LegacyIcon type={obj['icon']} style={{ color: 'white' }} />
              </div>
              {obj['name']}
            </Menu.Item>
          ))}
      </Menu>
    );
  }
}
