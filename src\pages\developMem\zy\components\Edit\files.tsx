import React, { useEffect, useImperativeHandle, useRef, useState, useMemo } from 'react';
import style from './index.less';
import { Button, Form, Alert, Modal, Space, Upload, Popconfirm, Input, message, Empty, Spin, Progress, Skeleton } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import Tip from '@/components/Tip';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import {
    convertToWebpBeforeUpload,
    _history
} from '@/utils/method';
import { listMemDigital, preview, dauploadFileDigital, cleanDigital, shareLink, sortDigital, daglupload } from '@/pages/developMem/services'
import moment from 'moment';
// import ReactWatermark from 'react-watermark-module'
import imgsss from '@/assets/mm.jpeg'
import { LoadingOutlined } from '@ant-design/icons';
import LazyImage from './lazyImage'
import ImageCropper from '@/components/ImageCropper'


const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};

const index: any = (props, ref) => {
    const { dataInfo: { digitals = [], operaterLogs = [], digitalLotNo = '', selectRow = {}, processNode
        = '', istransfer, randomkey = '', fillAudit } = {}, upList, load } = props;
    console.log(digitals, 'selectRowselectRow')

    useImperativeHandle(ref, () => ({
        submit: () => {
            let bj = {
                d222Name: selectRow.name,
                d222Code: selectRow.key,
                code: selectRow.code,
                processNode: processNode,
                fileList: [],
                ischange
            }
            bj.fileList = allList.map((i: any) => {
                return {
                    path: i?.path,
                    url: i?.url,
                    name: i?.name,
                    sort: i?.sort,
                    id: i?.id,
                    previewPath: i?.previewPath,
                    code: i?.code,
                    isDelete: i?.isDelete || undefined,
                    fileSize: i?.fileSize,
                }
            })
            return bj
        },
        setc: () => {
            setIschange(false)
            // setUploadloading(false)
        },
        getc: () => {
            return ischange
        }
    }));
    const [loading, setLoading] = useState(false);
    const [previewVisible, setPreviewVisible] = useState(false);
    const [previewVisible1, setPreviewVisible1] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const [allList, setAllList]: any = useState([])
    const [dataList, setDataList]: any = useState([])  //中间变量数组用来存储本来就有图片
    const [addList, setAddList] = useState([])
    const [fileList, setFileList] = useState<any>([]);
    const [checkList, setCheckList] = useState<any>([]);
    const [checkList1, setCheckList1] = useState<any>([]);
    const [sorto, setSorto] = useState({})
    const [ischange, setIschange] = useState(false)
    const [uploadloading, setUploadloading] = useState(false)
    const [form] = Form.useForm();
    const [filepercent, setFilepercent] = useState(0)
    const [renderKey, setRenderKey] = useState(0)
    const [isDragging, setIsDragging] = useState(false)
    const [renderfile, setRenderfile] = useState(false)
    const [filedone, setFiledone] = useState(false)
    const [prevent, setPrevent] = useState(false)
    const [cropperVisible, setCropperVisible] = useState(false)
    const imgContainerRef = useRef(null);
    const ImageCropperRef = useRef(null);
    let time = null
    const fileChange = async ({ fileList, file, event, }: any) => {
        console.log(fileList, 'fileList')

        if (prevent) return
        if (file.status === 'done') {
            const { response: { code = 500, message = '' } = {} } = file || {};
            if (code !== 0) {
                Tip.error('操作提示', message);
                fileList.pop();
            } else {
                getfu(fileList)
                // if (fileList.filter(i => i.status == 'done').length == fileList.length) {
                //     getbase64(fileList)
                // }
                // Tip.success('操作提示', '上传成功');

            }
        } else if (file.status === 'error') {
            Tip.error('操作提示', '上传失败');
            setUploadloading(false)
        }
        setFileList(fileList.map(i => {
            i['percent'] = (i['percent'] * 1).toFixed(0)
            return i
        }))
        setIschange(true)
    }
    const beforeUpload = (file, fileList) => {
        message.destroy();

        let imgs: any = []
        let pdfs: any = []
        fileList.forEach(i => {
            if (i.type.startsWith('image')) {
                imgs.push(i)
            }
            if (i.type.startsWith('application/pdf')) {
                pdfs.push(i)
            }
        })
        if (imgs.length > 0 && pdfs.length > 0) {
            message.error('请勿同时上传pdf和图片')
            setPrevent(true)
            return false
        }
        if (imgs.length > 10) {
            message.error('一次性最多上传10个文件')
            setPrevent(true)
            return false
        }
        if (pdfs.length > 1) {
            message.error('一次性最多上传1个pdf文件')
            setPrevent(true)
            return false
        }

        let fileSize: number = file['size'] / 1024 / 1024;
        if (fileSize < 50) {
            setUploadloading(true)
            setPrevent(false)
            load && load(true)
            if (allList.length == 0) {
                setRenderfile(true)
            }
            return convertToWebpBeforeUpload(file)
        } else {
            message.error('请上传小于50M的文件!');
            setPrevent(true)
            return false
        }
    }
    const getfu = (fileList) => {
        let done = false;
        let anum = 0
        if (time) clearInterval(time)
        time = setInterval(() => {
            fileList.forEach((file) => {
                const { response: { data = [], code = 500 } = {} } = file
                if (code !== 0) {
                    done = false
                    anum++
                    return
                }
            })

            if (anum == 0) {
                let arr: any = []
                fileList.map((i, index) => {
                    const { response: { data = [] } = {} } = i
                    data.map(j => {
                        let obj = {
                            path: j.url,
                            ...j
                        }
                        arr.push(obj)
                    })
                })
                let newarr = [...allList, ...arr].map((i, k) => {
                    return {
                        ...i,
                        sort: k + 1,
                    }
                })
                setAllList([...newarr])

                clearInterval(time)
                setRenderfile(false)
                setUploadloading(false)
                load && load(false)
                setFileList([])
                // anum=0
                Tip.success('操作提示', '上传成功');
                // getbase64(fileList)
            }
        }, 3000)

    }
    // useEffect(() => {
    //     getbase64()
    // }, [JSON.stringify(allList)])
    const uploadFile = (file, num) => {
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file', file.originFileObj);

            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/minio/upload?model=dacl');
            xhr.setRequestHeader('Authorization', sessionStorage.getItem('token') || '');
            xhr.setRequestHeader('dataApi', sessionStorage.getItem('dataApi') || '');

            xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                    file.percent = Math.round((event.loaded / event.total) * 100);
                    console.log(file.percent, '进度')

                    // 更新allList，添加新上传的文件
                    //   setAllList(prev => [...prev, ...fileData]);
                    setFileList(fileList => [...fileList]); // 更新UI
                }
            };

            xhr.onload = () => {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        file.response = response;

                        // 如果上传成功且有数据，添加到显示列表
                        if (response && response.code === 0 && response.data && response.data.length > 0) {
                            // if(type== 'application/pdf') {
                            //     setAllList(prev => [...prev, ...response.data]);
                            // }
                            // 更新allList，添加新上传的文件

                            const fileData = response.data.map(i => {
                                return {
                                    path: i.url,
                                    url: i.url,
                                    name: i.name,
                                    // pathurl: fileData.url,
                                    id: String(Date.now() + Math.floor(Math.random() * 1000)),
                                }
                            });
                            setAllList(prev => [...prev, ...fileData]);
                        }
                        resolve(response);
                    } catch (e) {
                        reject(e);
                    }
                } else {
                    reject(new Error('上传失败'));
                }
            };
            xhr.onerror = () => reject(new Error('网络错误'));
            xhr.send(formData);
        });
    };

    const getbase64 = (fileList) => {
        console.log(allList, fileList, 'allListallListallList')
        const { } = fileList
        // setFilepercent(0)
        let _allList: any = []
        const promise = fileList.map((i, index) => {
            i?.response?.data.map(async j => {
                const result = await shareLink({ path: i?.url })
                return {
                    pathurl: result?.data,
                    sort: index + 1,
                    ...i,
                }
            })

        })
        Promise.all(promise).then(res => {
            res.map((i, k) => {
                // let obj = {
                //     ...i,
                //     path: i['url'],
                //     pathurl: i?.data,

                // }
                _allList.push(i)
            })
            setAddList(_allList)
            setAllList([..._allList])
        })
    }
    const check = (obj) => {
        if(obj?.history) return
        let arr: any = []
        if (checkList.includes(obj?.id)) {
            arr = checkList.filter(i => i !== obj?.id)
        } else {
            arr = [...checkList, obj?.id]
        }
        setCheckList([...new Set([...arr])])
    }

    const onFinish = async (e) => {
        const { code = 500 } = await cleanDigital({
            data: {
                digitalLotNo: digitalLotNo,
                codeList: checkList,
                ...e
            }
        })
        if (code == 0) {
            let o2 = allList.filter(i => !checkList.includes(i.code))
            Tip.success('操作提示', '删除成功')
            setCheckList([])
            // setCheckList1([])
            setPreviewVisible1(false)
            setAllList(o2)
        }
    }
    const delFile = () => {
        let o = fileList.filter(i => !checkList.includes(i.response.data[0].id))
        // let o1 = addList.filter(i => !checkList1.includes(i.id))
        let o2 = allList.filter(i => {
            if (checkList.includes(i.id)) {
                i['isDelete'] = 1
            }
            return i
        })
        setFileList([...o])
        // setAddList(o1)
        setAllList(o2)
        setIschange(true)
    }
    const dragstarts = (e, item) => {
        e.dataTransfer.setData('text/plain', JSON.stringify(item));
        setSorto(item)
        setIsDragging(true)
        // let arr = allList.filter(i => !i.code)
        // if (arr.length > 0) {
        //     message.error('请先保存文件')
        // } else {
        //     setSorto(item)
        // }
    }
    const drops = (e, item) => {
        e.preventDefault();
        // let newrr: any = []
        // if (sorto?.code) {
        //     newrr = arr.filter(i => i.code != sorto.code)
        // } else {
        //     newrr = arr.filter(i => i.id != sorto.id)
        // }
        let arr = allList
        let newrr = arr.filter(i => i.id != sorto.id)
        newrr.splice(item.sort - 1, 0, sorto)
        // let _allList = arr.filter((i, index) => index !== sorto.old)
        let newrr1 = newrr.map((item, index) => {
            item['sort'] = index + 1
            return item
        })
        setIschange(true)
        setAllList(newrr1)
        setIsDragging(false)
        // sort(newrr1)
    }
    // const sort = async (arr) => {
    //     const { code = 500 } = await sortDigital({
    //         data: {
    //             digitalLotNo: digitalLotNo,
    //             sortDTOList: arr.map(i => {
    //                 return {
    //                     code: i.code,
    //                     sort: i.sort
    //                 }
    //             })
    //         }
    //     })
    //     if (code == 0) {
    //         Tip.success('操作提示', '排序成功')
    //     }
    // }
    const toswitch = (key) => {
        let arr = allList.filter(i => i['isDelete'] != 1)
        let findIndex = arr.findIndex(i => i.previewPath == previewImage)
        if (key == 'up') {
            if (findIndex == 0) {
                message.error('已经是第一个了')
            } else {
                setPreviewImage(arr[findIndex - 1]?.previewPath)

            }


        } else {
            if (findIndex == arr.length - 1) {
                message.error('已经是最后一个了')
            } else {
                setPreviewImage(arr[findIndex + 1]?.previewPath)
            }
        }
    }
    // const newarr = useMemo(() => {
    //     return digitals
    // }, [digitals])
    const handleimg = (id) => {
        let canvas = document.createElement('canvas');
        const user = JSON.parse(sessionStorage.getItem('user') || '{}')
        canvas.width = 200;
        canvas.height = 300;
        let context: any = canvas.getContext('2d');
        // 设置字体
        context.font = '10px normal';
        // 设置颜色
        context.fillStyle = '#4c4747';
        //  context.fillStyle = 'red';
        // context.rotate((-35 * Math.PI) / 180);
        context.translate(80, 150); // 移动旋转中心点
        context.rotate(-(Math.PI / 3.1)); // 旋转
        // 设置水平对齐方式
        context.textAlign = 'left';
        // 设置垂直对齐方式
        context.textBaseline = 'top';
        // 绘制文字（参数：要写的字，x坐标，y坐标）
        context.fillText(`本档案仅供${user.account}账户${moment().format('YYYY年MM月DD日')}阅览使用,不具备任何形式的法律效应`, -190, 20);
        let watermark: any = document.getElementById(id)
        if (watermark) {
            watermark.style = `background: url(${context.canvas.toDataURL()});pointer-events:none;opacity:.4;position: absolute;z-index: 9;top: 0;left: 0;width: 200px;height: 300px`;
        }
    }
    const handleimg1 = (id) => {
        let canvas = document.createElement('canvas');
        const user = JSON.parse(sessionStorage.getItem('user') || '{}')
        canvas.width = 1000;
        canvas.height = 800;
        let context: any = canvas.getContext('2d');
        // 设置字体
        context.font = '28px normal';
        // 设置颜色
        context.fillStyle = '#4c4747';
        // context.rotate((-35 * Math.PI) / 180);
        context.translate(200, 460); // 移动到中心点
        context.rotate(-(Math.PI / 4)); // 旋转45度
        // 设置水平对齐方式
        context.textAlign = 'left';
        // 设置垂直对齐方式
        context.textBaseline = 'top';
        // 绘制文字（参数：要写的字，x坐标，y坐标）
        context.fillText(`本档案仅供${user.account}账户${moment().format('YYYY年MM月DD日')}阅览使用,不具备任何形式的法律效应`, -350, 100);
        let watermark: any = document.getElementById(id)
        if (watermark) {
            watermark.style = `background: url(${context.canvas.toDataURL()});pointer-events:none;opacity:.4;position: absolute;z-index: 9;top: 0;left: 0;width: 100%;height: 100%`;
        }
    }

    // 添加处理滚动的方法
    const handleDragOver = (e, containerRef) => {
        e.preventDefault();

        if (!isDragging || !containerRef) return;

        const container = containerRef;
        const containerRect = container.getBoundingClientRect();
        const mouseY = e.clientY;

        // 计算滚动速度 - 离边缘越近滚动越快
        const calculateSpeed = (distance, maxSpeed = 30) => {
            // 只在靠近边缘70px内触发滚动
            const threshold = 70;
            if (distance > threshold) return 0;

            // 距离边缘越近，速度越快
            return Math.ceil((threshold - distance) / 2);
        };

        // 鼠标在容器顶部区域，向上滚动
        if (mouseY < containerRect.top + 70) {
            const distance = mouseY - containerRect.top;
            const speed = calculateSpeed(distance);
            if (speed > 0) {
                container.scrollTop -= speed;
            }
        }

        // 鼠标在容器底部区域，向下滚动
        if (mouseY > containerRect.bottom - 70) {
            const distance = containerRect.bottom - mouseY;
            const speed = calculateSpeed(distance);
            if (speed > 0) {
                container.scrollTop += speed;
            }
        }
    }

    const handleGlobalDragOver = (e) => {
        if (!isDragging || !imgContainerRef.current) return;
        handleDragOver(e, imgContainerRef.current);
    }

    const croppersave = async (file) => {
        console.log(file, previewImage, 'fffffffffffffffff')
        setCropperVisible(false)
        const formData = new FormData();
        formData.append('file', file);
        try {
            const response = await fetch('/api/minio/upload?model=dacl', {
                method: 'POST',
                body: formData,
                headers: {
                    Authorization: sessionStorage.getItem('token') || '',
                    dataApi: sessionStorage.getItem('dataApi') || '',
                }
            });

            if (response.ok) {
                const { code = 500, data = [] } = await response.json();
                let _allList = [...allList]
                console.log(_allList, '_allList_allList_allList')
                _allList.map(i => {
                    if (i.path === previewImage || i.previewPath === previewImage) {
                        i.path = data[0].url
                        i.previewPath = data[0].previewPath
                        i.url = data[0].url
                    }
                    return i
                })
                setAllList([..._allList])
                console.log('Success:', data);
            } else {
                console.error('Error:', response.statusText);
            }
        } catch (error) {
            console.error('Error during file upload:', error);
        }
    }

    useEffect(() => {
        console.log(randomkey, 'randomkeyrandomkeyrandomkey')
        setFileList([])
        setCheckList([])
        setCheckList1([])
        if (digitals.length > 0) {
            setAllList([...digitals.map((i, index) => {
                i['previewPath'] = i['path'],
                    i['sort'] = index + 1,
                    i['id'] = (index + 1).toString()
                return i
            })])
            setDataList([...digitals.map((i, index) => {
                i['previewPath'] = i['path'],
                    i['sort'] = index + 1,
                    i['id'] = (index + 1).toString()
                return i
            })])
        } else {
            setAllList([])
            setDataList([])
        }
        // 滚动到顶部
        if (imgContainerRef.current) {
            imgContainerRef.current.scrollTop = 0;
        }

    }, [randomkey]);
    // useEffect(() => {
    //     console.log(filedone, 'filedone')
    //     if (filedone) {
    //         // time.clear()
    //         clearInterval(time);
    //         getbase64()
    //     }
    // }, [filedone])

    // 添加全局拖拽事件监听
    useEffect(() => {
        document.addEventListener('dragover', handleGlobalDragOver);
        return () => {
            document.removeEventListener('dragover', handleGlobalDragOver);
        }
    }, [isDragging]);

    const d: any = {
        action: `/api/minio/upload?model=dacl`,
        // accept: '.jpg,.png,.jpeg,.webp,.pdf,.tiff',
        accept: '.jpg,.png,.jpeg,.webp,.pdf',
        headers: {
            Authorization: sessionStorage.getItem('token') || '',
            dataApi: sessionStorage.getItem('dataApi') || '',
        },
        progress: {
            strokeColor: {
                '0%': '#108ee9',
                '100%': '#87d068',
            },
            strokeWidth: 3,
            format: percent => percent && `${parseFloat(percent.toFixed(2))}%`,
        },
    };



    return (
        <div className={style.flexbox}>

            <div className={style.left}>

                <div className={style.flexboxTit}>
                    <div style={istransfer ? { maxWidth: 700 } : {}}>{selectRow?.name}</div>
                    {
                        !istransfer &&
                        <React.Fragment>
                            <div className={style.flexboxTip} >温馨提示：拖动图片可以进行排序</div>
                            <div className={style.upbtn}>
                                {
                                    fillAudit.isUpload &&
                                    <Space>
                                        <Spin spinning={uploadloading} size={'small'} indicator={<LoadingOutlined spin />}>
                                            <Upload
                                                style={{ marginBottom: 10 }}
                                                {...d}
                                                multiple
                                                onChange={fileChange}
                                                beforeUpload={beforeUpload}
                                                fileList={fileList}
                                                showUploadList={false}
                                            >
                                                <Button disabled={!randomkey}>上传</Button>
                                            </Upload>
                                        </Spin>
                                        <Button danger disabled={checkList.length > 0 ? false : true} onClick={delFile}>删除</Button>
                                    </Space>
                                }
                            </div>
                        </React.Fragment>
                    }
                </div>
                <div className={style.imglist} key={renderKey} ref={imgContainerRef}>
                    {
                        renderfile ?
                            <React.Fragment>
                                {
                                    fileList.map((item: any) => {
                                        return (
                                            <div className={style.previewImage}>
                                                <Skeleton.Image style={{ width: '100%', height: '100%' }} />
                                                <Progress percent={item.percent} size="small" />
                                            </div>
                                        )
                                    })
                                }
                            </React.Fragment>
                            : <React.Fragment>
                                <React.Fragment >
                                    {
                                        // Empty 
                                        allList.length > 0 ?
                                            allList.map((file, i) => {
                                                if (file['isDelete'] != 1) {
                                                    return (
                                                        <div
                                                        draggable={!file.history}
                                                        onDragStart={(e) => {
                                                            if (file.history) {
                                                                e.preventDefault();
                                                                return;
                                                            }
                                                            dragstarts(e, { ...file, old: i })
                                                        }}
                                                            onDrop={(e) => {
                                                                if(file.history) {
                                                                    e.preventDefault();
                                                                    return;
                                                                }
                                                                drops(e, file)
                                                            }}
                                                            onDragOver={(e) => e.preventDefault()}
                                                            key={i}
                                                            className={(checkList.includes(file?.code) || checkList.includes(file?.id)) ? `${style.previewImage} ${style.cImage}` : style.previewImage}
                                                            onClick={() => check(file)}
                                                        // data-watermark="本档案仅供admin102账户2024年4月14日阅览使用，不具备任何形式的法律效应"
                                                        >
                                                            {
                                                                checkList.includes(file?.id) &&
                                                                <div className={style.cicon}>
                                                                    <LegacyIcon type="check" />
                                                                </div>
                                                            }
                                                            <div id={`watermark${i}`}></div>
                                                            {/* <img src={imgsss} onLoad={() => handleimg(`watermark${i}`)} /> */}
                                                            {/* 
                                                            <img src={`${window.location.origin}/${file.previewPath}`} onLoad={() => handleimg(`watermark${i}`)} /> */}
                                                            <LazyImage
                                                                dataSrc={`${window.location.origin}/${file.previewPath}`}
                                                                alt="file-preview"
                                                                className={style.simg}
                                                                onLoad={() => handleimg(`watermark${i}`)}
                                                            />
                                                            {
                                                                !file.history&&
                                                                <div className={style.cropper} onClick={(e) => {
                                                                    e.stopPropagation()
                                                                    setPreviewImage(file.previewPath)
                                                                    setCropperVisible(true)
                                                                }}>
                                                                    <Button size="small" type='primary' >编辑</Button>
                                                                </div>
                                                            }
                                                           
                                                            <div className={style.preview} onClick={(e) => {
                                                                e.stopPropagation()
                                                                setPreviewImage(file.previewPath)
                                                                setPreviewVisible(true)
                                                            }}>
                                                                <Button size="small" type='primary'>预览</Button>
                                                            </div>
                                                        </div>
                                                    )
                                                }

                                            })
                                            :
                                            <Empty style={{ margin: 'auto' }} />
                                    }

                                </React.Fragment>
                                <React.Fragment >
                                    {
                                        fileList.map((item: any) => {
                                            return (
                                                <div className={style.previewImage}>
                                                    <Skeleton.Image style={{ width: '100%', height: '100%' }} />
                                                    <Progress percent={item.percent} size="small" />
                                                </div>
                                            )
                                        })
                                    }
                                </React.Fragment>
                            </React.Fragment>

                    }
                </div>
            </div>
            <div className={style.oprationlist}>
                <div className={style.flexboxTit}>
                    <div>操作日志</div>
                </div>
                {
                    operaterLogs.length > 0 ?
                        operaterLogs.map((item, index) => {
                            return (
                                <table key={index}>
                                    <tr><td style={{ textAlign: 'center' }} className={style.label} colSpan={2}>{item.oprationType == '1' ? '上传' : '删除'}</td></tr>
                                    <tr>
                                        <td className={style.label}>上传（党）组织</td>
                                        <td>{item.oprationOrgName}</td>
                                    </tr>
                                    <tr>
                                        <td className={style.label}>操作人</td>
                                        <td>{item.createUser}({item.oprationUser})</td>
                                    </tr>
                                    <tr>
                                        <td className={style.label}>操作时间</td>
                                        <td>{moment(item.oprationTime).format('YYYY-MM-DD HH:mm:ss')}</td>
                                    </tr>
                                    <tr>
                                        <td className={style.label}>资料</td>
                                        <td><div>{
                                            item.digitalNames && item.digitalNames.split(',').map((i, k) => {
                                                return <div key={k}>{i}</div>
                                            })
                                        }</div></td>
                                    </tr>
                                </table>
                            )
                        }) :
                        <Empty style={{ marginTop: '50%' }} />
                }

            </div>
            <Modal width={'1000px'} visible={previewVisible} footer={null} onCancel={() => {
                setPreviewImage('')
                setPreviewVisible(false)
            }}>
                <div style={{ width: '780px', height: '800px', position: 'relative', margin: 'auto' }}>
                    {/* <ImageCropper src={imgsss}/> */}
                    <LegacyIcon className={style.changeicon} type="left" onClick={() => toswitch('up')} />
                    <div id='previewwater'></div>
                    <img style={{ width: '100%', height: '100%', userSelect: 'none' }} alt="example" src={`${window.location.origin}/${previewImage}`} onLoad={() => handleimg1('previewwater')} />
                    {/* <img style={{ width: '100%', height: '100%', userSelect: 'none' }} src={imgsss} onLoad={() => handleimg1('previewwater')} /> */}
                    <LegacyIcon className={style.changeicon1} onClick={() => toswitch('below')} type="right" />
                </div>

            </Modal>
            <Modal title={'文件删除'} visible={previewVisible1} onOk={() => {
                form.submit()
            }} onCancel={() => {
                setPreviewVisible1(false)
            }}>
                <Form form={form} {...formItemLayout} onFinish={onFinish} layout={'inline'} style={{ marginBottom: 10 }}>
                    <Form.Item name='oprationUser'
                        label="删除人"
                        rules={[{ required: true, message: '请填写删除人' }]}
                    >
                        <Input allowClear style={{ width: 260 }} />
                    </Form.Item>
                </Form>
            </Modal>
            {
                cropperVisible &&
                <Modal width={'1360px'} visible={cropperVisible} onOk={() => {
                    croppersave(ImageCropperRef.current.geturl())
                    ImageCropperRef.current.clears()
                }} onCancel={() => {
                    ImageCropperRef.current.clears()
                    setPreviewImage('')
                    setCropperVisible(false)
                }}>
                    <div style={{ height: '690px', position: 'relative', margin: 'auto' }}>
                        <ImageCropper keys={Math.random()} src={previewImage} ref={ImageCropperRef} />

                    </div>

                </Modal>
            }

        </div>

    );
};
// @ts-ignore
export default React.forwardRef(index);
