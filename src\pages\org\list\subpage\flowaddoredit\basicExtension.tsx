/**
 * 扩展信息
 */
/**
 * 模块名
 */
import React, { Fragment, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { Col, Input, Button, Switch, Row, InputNumber, Tabs, Form } from "antd";
import Tip from '@/components/Tip';
const FormItem = Form.Item;
const { TabPane } = Tabs;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
};
function index(props, ref) {
    const [form] = Form.useForm();
    const { data } = props
    const { basicInfo } = props.org
  const [loading, setLoading] = useState(false);
    useEffect(() => {
        form.setFieldsValue({
            ...data,
            currentYearEndTenure: data?.currentYearEndTenure === 0,
            currentYearChangeTerm: data?.currentYearChangeTerm === 0
        })
    }, [data]);
    const onFinish = (values) => {
        values['currentYearEndTenure'] = values['currentYearEndTenure'] ? 0 : 1
        values['currentYearChangeTerm'] = values['currentYearChangeTerm'] ? 0 : 1
      setLoading(true);
        props.dispatch({
            type: 'org/addOrUpdate',
            payload: {
                data: {
                    ...values,
                    zbCode: basicInfo['zbCode'],
                    orgCode: basicInfo['orgCode'],
                    code: data?.code || undefined
                }
            },
        }).then(res => {
          setLoading(false);
            if (res.code === 0) {
              // Tip.success('操作提示', res['code'] ? '修改成功' : '新增成功');
              Tip.success('操作提示', '修改成功');
              props.onOK && props.onOK();
            }
        })
    };
    return (

        <Form {...formItemLayout} form={form} onFinish={onFinish} name='basicExtension'>
            <Row>
                <Col span={12}>
                    <FormItem
                        label="是否本年届满"
                        name={'currentYearEndTenure'}
                        valuePropName="checked"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Switch />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="是否本年换届"
                        valuePropName="checked"
                        name="currentYearChangeTerm"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <Switch />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="上年底党员总数"
                        name={'lastYearMemTotal'}
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="本年重新入党人数"
                        name="currentYearJoinNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="本年恢复党籍人数"
                        name="currentYearReconvertNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>
                <Col span={12}>
                    <FormItem
                        label="停止党籍后本年恢复党籍人数"
                        name="stopCurrentYearReconvertNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="本年转入组织关系数"
                        name="currentYearIntoRelationNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="本年整建制转入数"
                        name="currentYearFixedIntoNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="本年转出组织关系数"
                        name="currentYearOutRelationNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="本年整建制转出数"
                        name="currentYearFixedOutNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="本年死亡党员数"
                        name="currentYearDieMemNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>


                <Col span={12}>
                    <FormItem
                        label="本年转出组织关系介绍信回执数"
                        name="currentYearOutRelationLetterNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>


                <Col span={12}>
                    <FormItem
                        label="省级党委审批追认的共产党员数"
                        name="provincialApprovalMemNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>


                <Col span={12}>
                    <FormItem
                        label="开展公推直选标识"
                        name="electedByDirect"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="书记副书记选举方式"
                        name="deputySecretaryElect"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="开展党员定期评议基层党组织领导班子标识"
                        name="appraiseBasicLeaderTeam"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="参加评议党员数"
                        name="joinReviewMems"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="开展党员旁听会议的基层党委数"
                        name="attendMeetBasicOrganNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>

                <Col span={12}>
                    <FormItem
                        label="参加旁听的党员数"
                        name="joinAuditMemNum"
                        rules={[{ required: true, message: '请填写' }]}
                    >
                        <InputNumber min={0} />
                    </FormItem>
                </Col>
            </Row>

            <div style={{ textAlign: 'center' }}>
                <Button type={'primary'} htmlType={'submit'} style={{ marginRight: 16 }} loading={loading}>保存</Button>
                <Button type={'primary'} danger htmlType={'button'} onClick={() => props.close()}>取消</Button>
            </div>
        </Form>
    );
}
export default forwardRef(index);
