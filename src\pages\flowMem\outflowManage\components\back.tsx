// 流动党员-流出管理-流出被退回
import React, { Fragment } from 'react';
import { connect } from 'dva';
import { Button, Divider, Input, Modal } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import { isEmpty, setListHeight, changeMsgTip } from '@/utils/method';
import NowOrg from 'src/components/NowOrg';
import ExportInfo from '@/components/Export';
import { getSession } from '@/utils/session';
// import FlowAddOrEditReadOnly from '../../inflowManage/components/flowAddOrEdit';
import FlowAddOrEditReadOnly from './flowAddOrEditBackFlow';
import { outManageTermination } from '../../service/index';
import moment from 'moment';
import _isEqual from 'lodash/isEqual';

const Search = Input.Search;
const { confirm } = Modal;

@connect(({ unit, commonDict, loading, flowMem }) => ({
  flowMem,
  unit,
  commonDict,
  loading: loading.effects['unit/getList'],
}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {}, //筛选器
      search: undefined, //搜索框
      view: false,
      subordinate: getSession('subordinate'),
      exportLoading: false, //导出loading
      searchLoading: false, //查询loading
      getList:this.getList
    };
    this['queryDetailsRef'] = React.createRef();
  }

  setQueryDetailsRef = (ref) => {
    this['queryDetailsRef'] = ref;
  };
  stop = (record) => {
    confirm({
      title: '终止提示',
      content: '确定终止该流动吗？确定后该流出将取消。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const { code = 500 } = await outManageTermination({
          code: record['code'],
        });
        if (code == 0) {
          Tip.success('操作提示', '操作成功');
          this.getList({ pageNum: 1, pageSize: 10 });
        }
      },
    });
  };
  addOrEdit = (type: string, record?: object) => {
    this['flowAddOrEditRef'].open(type, record);
  };
  exportInfo = async () => {
    this.setState({
      exportLoading: true,
    });
    await this['exportRef'].submitNoModal();
    this.setState({
      exportLoading: false,
    });
  };
  filterChange = (val) => {
    const org = getSession('org') || {};
    this.setState(
      {
        filter: val,
      },
      () => this.getList(),
    );
  };
  handleSearch = (e) => {
    this.setState(
      {
        memName: e,
        searchLoading: true,
      },
      () => {
        this.getList({ memName: e });
      },
    );
  };
  searchChange(e) {
    this.setState({
      memName: e.currentTarget.value || undefined,
    });
  }
  getList = async (params?: object) => {
    const { filter, memName, outAdministrativeDivisionCode } = this.state;
    // return
    const org = getSession('org') || {};
    await this.props.dispatch({
      type: 'flowMem/outManageList',
      payload: {
        data: {
          flowType: 3, // 根据当前选中tab菜单 1：未纳入 2：已纳入 3：流出被退回 4：流出历史
          pageNum: 1,
          pageSize: 10,
          memOrgCode: org['orgCode'],
          memName,
          outAdministrativeDivisionCode,
          ...filter,
          ...params,
        },
      },
    });
    this.setState({
      searchLoading: false,
    });
  };
  // componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
  //   const org = getSession('org') || {};
  //   const subordinate = getSession('subordinate') || '0';
  //   if (
  //     (!isEmpty(this.state['orgCode']) && this.state['orgCode'] !== org['orgCode']) ||
  //     subordinate !== this.state.subordinate
  //   ) {
  //     this.setState(
  //       {
  //         orgCode: org['orgCode'],
  //         subordinate,
  //       },
  //       () => {
  //         this.getList({ memOrgCode: org['orgCode'] });
  //       },
  //     );
  //   }
  // }
  componentDidMount() {
    const org = getSession('org') || {};
    setListHeight(this);
    // this.setState({ orgCode: org['orgCode'] });
    // this.getList();
  }
  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const org=getSession('org') || {};
    const {_org = {},getList, page, pageSize, name} = prevState;
    if(!_isEqual(org, _org)){
      state['org'] = org;
      state['_org'] = org;
      getList(page, pageSize, name);
    }
    return state;
  };
  render() {
    const { filterHeight, filter, subordinate, memName, searchLoading } = this.state;
    const org = getSession('org') || {};
    const {
      loading,
      commonDict,
      flowMem: { list = [], pagination = {} },
    } = this.props;

    const filterData = [
      {
        key: 'flowTypeCode',
        name: '流动类型',
        value: commonDict[`dict_d34`],
      },
      {
        // 时间选择器的时候没用到该key
        key: 'timeSelect',
        name: '外出时间',
        value: '',
        // type 1是树选择器 2是时间选择器
        type: 2,
        textArr: ["startOutTime", 'endOutTime']
      },
      {
        key: 'timeSelect',
        name: '登记日期',
        value: '',
        // type 1是树选择器 2是时间选择器
        type: 2,
        textArr: ["startRegisterTime", 'endRegisterTime']
      },
      {
        key: 'timeSelect',
        name: '创建日期',
        value: '',
        // type 1是树选择器 2是时间选择器
        type: 2,
        textArr: ["startCreateTime", 'endCreateTime']
      },
    ];
    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 40,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'memName',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return (
            <a
              onClick={() => {
                this['flowAddOrEditReadOnlyRef'].open('readOnly-outTab3', record, 'outFlow');
              }}
            >
              {text}
            </a>
          );
        },
      },
      {
        title: '性别',
        dataIndex: 'memSexName',
        align: 'center',
        width: 40,
      },
      {
        title: '联系电话',
        dataIndex: 'memPhone',
        align: 'center',
        width: 100,
      },
      {
        title: '流动类型',
        dataIndex: 'flowTypeName',
        align: 'left',
        width: 50,
      },
      {
        title: '流出地党支部',
        dataIndex: 'memOrgName',
        align: 'left',
        width: 100,
      },
      {
        title: '流入地党支部',
        dataIndex: 'inOrgName',
        align: 'left',
        width: 100,
      },
      {
        title: '数据创建时间',
        dataIndex: 'createTime',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '退回原因',
        dataIndex: 'rejectReasonName',
        align: 'left',
        width: 200,
        render: (text, record, index) => {
          // 当文字等于其他的时候 判断一下rejectOtherReasonText是否为空 不为空的时候拼在其他后面
          if (text == "其他" && (record['rejectOtherReasonText'] && record['rejectOtherReasonText'] != '')) {
            return text + ' : ' + record['rejectOtherReasonText']
          }
          return text
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 110,
        render: (text, record) => {
          return (
            <Fragment>
              <a
                onClick={() => {
                  this['flowAddOrEditReadOnlyRef'].open('readOnly-outTab3', record, 'outFlow');
                }}
              >
                查看
              </a>
              <Divider type="vertical" />
              <a
                onClick={() => {
                  this['flowAddOrEditReadOnlyRef'].open('edit-outTab3', record, 'outFlow');
                }}
              >
                重新流出
              </a>
              {/* 2025/2/13 终止功能废弃 */}
              <Divider type="vertical" />
              <a
                onClick={() => {
                  this.stop(record);
                }}
              >
                终止流动
              </a>
            </Fragment>
          );
        },
      },
    ];
    return (
      <Fragment>
        <NowOrg
          extra={
            <Fragment>
              <Button
                style={{ marginLeft: 16 }}
                onClick={this.exportInfo}
                loading={this.state.exportLoading}
              >
                导出
              </Button>
              <Search
                loading={searchLoading}
                allowClear
                placeholder="请输入姓名"
                enterButton={'查询'}
                style={{ width: 200, marginLeft: 16 }}
                onSearch={(e) => {
                  this.handleSearch(e);
                }}
                onChange={(e) => {
                  this.searchChange(e);
                }}
              />
            </Fragment>
          }
        />
        <RuiFilter
          showLine={3}
          data={filterData}
          openCloseChange={() => setListHeight(this, 20)}
          onChange={this.filterChange}
        />
        <ListTable
          rowKey={'id'}
          scroll={{ y: filterHeight, x: 100 }}
          
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={(page, pageSize) => {
            this.getList({ pageNum: page, pageSize });
          }}
        />
        <ExportInfo
          wrappedComponentRef={(e) => (this['exportRef'] = e)}
          tableName={''}
          noModal={true}
          tableListQuery={{
            ...filter,
            pageNum: pagination.pageNumber || 1,
            pageSize: pagination.pageSize || 10,
            // subordinate,
            memName,
            memOrgCode: org['orgCode'],
            flowType: '3',
          }}
          action={'/api/mem/flow/outExportXlsx'}
        />
        <FlowAddOrEditReadOnly
          wrappedComponentRef={(e) => (this['flowAddOrEditReadOnlyRef'] = e)}
          onOk={this.getList}
        />
      </Fragment>
    );
  }
}
