// 业务操作-表现反馈
import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input } from 'antd';
import moment from 'moment';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import { inManageOperate } from '../../service/index';

const FormItem = Form.Item;

class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      memInfo: {},
      visible: false,
      timeKey: moment().valueOf(),
      confirmLoading: false,
    };
  }
  handleOk = () => {
    const { onOk } = this.props;
    const { code } = this.state.memInfo;
    const { modalType = '' } = this.state;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        let url: any = undefined;
        if (modalType === 'inFlow') {
          url = inManageOperate;
        }
        if (url) {
          this.setState(
            {
              confirmLoading: true,
            },
            async () => {
              const res = await url({ data: { code, ...val } });
              this.setState({
                confirmLoading: false,
              });
              if (res.code === 0) {
                this.handleCancel();
                Tip.success('操作提示', '操作成功');
                onOk && onOk();
              }
            },
          );
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  open = (type: string, record) => {
    this.setState({ visible: true, memInfo: record, modalType: type, timeKey: moment().valueOf() });
  };
  destroy = () => {
    this.setState({
      memInfo: {},
      modalType: '',
    });
  };
  render() {
    const { form, loading: { effects = {} } = {} } = this.props;
    const { getFieldDecorator } = form;
    const { visible, confirmLoading, memInfo } = this.state;
    return (
      <Modal
        destroyOnClose
        title="表现反馈"
        visible={visible}
        onOk={this.handleOk}
        maskClosable={false}
        onCancel={this.handleCancel}
        width={400}
        confirmLoading={confirmLoading}
      >
        {visible && (
          <Fragment key={this.state.timeKey}>
            <Form labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
              <FormItem label="请输入流动党员表现反馈">
                {getFieldDecorator('inFeedback', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['inFeedback'],
                  rules: [{ required: true, message: '请输入流动党员表现反馈' }],
                })(<Input.TextArea placeholder="请输入" showCount maxLength={100} rows={4} />)}
              </FormItem>
            </Form>
          </Fragment>
        )}
      </Modal>
    );
  }
}
export default Form.create()(index);
