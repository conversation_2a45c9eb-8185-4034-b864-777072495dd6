/**
 * 联络机构列表
 */
import React from 'react';
import {connect} from "dva";
import ListTable from 'src/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs, Dropdown, Menu, Modal, Row, Col } from 'antd';
import NowOrg from 'src/components/NowOrg';
import {getSession} from "@/utils/session";
import AddEdit from './addEdit'
import styles from './index.less';
import { setListHeight ,isEmpty} from '@/utils/method';
import Notice from '@/components/Notice';
const Search = Input.Search;
const TabPane = Tabs.TabPane;

@connect(({behalf,unit,commonDict,loading})=>({behalf,unit,commonDict,loading:loading.effects['unit/getList']}))
export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      dataInfo:undefined,
      filter:{},//筛选器
      search:{},//搜索框
      view:false,
    };
  }
  addOrEdit=(record?:object)=>{
    // OutRegistration.show()
  };
  export=()=>{

  };

  componentDidMount() {
    let org=getSession('org')|| {};
    this.setState({
      orgCode:org['orgCode']
    },()=>{
      this.getList();
      setListHeight(this);
    });
  }
  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org=getSession('org') || {};
    if (!isEmpty(this.state['orgCode'])&&this.state['orgCode']!==org['orgCode']) {
      this.setState({
        orgCode:org['orgCode']
      },()=>{
        this.getList(this.state['page'],this.state['pageSize'],this.state['name'])
      })
    }
  }
  getList=(pageNum=1,pageSize=10,name='')=>{
    let val={
      orgCodeLevel:this.state['orgCode'],
      pageNum:pageNum,
      pageSize:pageSize,
      name:name,
    };
    for (let obj in val) {
      if (isEmpty(val[obj])) {
        delete val[obj]
      }
    }
   this.props.dispatch({
     type:'behalf/contactAgencyList',
     payload:{
       data:{
         ...val
       }
     }
   })
  };
  onPageChange=(page,pageSize)=>{
    this.setState({
      page,
      pageSize
    },()=>{
      this.getList(page,pageSize,this.state['name'])
    });
  };

  confirm=(record)=>{
   this.props.dispatch({
      type:'behalf/contactAgencyDel',
      payload:{
        code:record['code']
      }
    }).then(res=>{
      if (res['code'] === 0) {
        Notice("操作提示",'删除成功!',"check-circle","green");
        this.getList()
      }else {
        Notice("操作提示",res['message'],"exclamation-circle-o","orange");
      }
   });
  };

  view=(record)=>{
    this.setState({type:'edit',data:record},()=>{
      this['AddEdit'].showModal();
    });
  };
  handleOk=()=>{
    this.setState({view:false})
  };
  handleCancel=()=>{
    this.setState({view:false})
  };
  changeList=(v)=>{
    v&&this.getList();
  };
  search=(value)=>{
    this.setState({
      name:isEmpty(value)?undefined:value
    },()=>this.getList(1,this.state['pageSize'],value));
  };
  render() {
    const {behalf:{list=[],pagination={}}={}}=this.props;
    const {loading}=this.props;
    const {dataInfo,filterHeight,data={}}=this.state;
    const {current,pageSize} = pagination;

    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'机构名称',
        dataIndex:'contactName',
        width:300
      },
      {
        title:'所属党组织',
        dataIndex:'orgName',
        width:300
      },
      {
        title:'机构级别',
        dataIndex:'d62Name',
        width:100
      },
      {
        title:'编制数',
        dataIndex:'bzNum',
        width:100
      },
      {
        title:'操作',
        dataIndex:'action',
        width:100,
        render:(text,record)=>{
          return(
            <div>
              <a href={'#'} onClick={()=>this.view(record)}>编辑</a>
              <Divider type="vertical"/>
              <Popconfirm title="确定要删除吗？" onConfirm={()=>this.confirm(record)}>
                <a href={'#'} className='del'>删除</a>
              </Popconfirm>
            </div>
          )
        },
      },
    ];

    return (
      <div>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        <NowOrg extra={
          <React.Fragment>
            <Search style={{width:200,marginLeft:16}} onSearch={this.search} placeholder={'请输入检索关键词'}/>
            <AddEdit data={data} title={this.state['type']==='edit'?'编辑联络机构':'新增联络机构'} wrappedComponentRef={(e)=>this['AddEdit']=e} onChange={this.changeList}>
              <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} style={{marginLeft:16}} onClick={()=>this.addOrEdit()}>新增联络机构</Button>
            </AddEdit>
          </React.Fragment>
        }/>
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:filterHeight}}
         
          columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
      </div>
    );
  }
}
