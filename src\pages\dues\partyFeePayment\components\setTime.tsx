/**
 * 设置起交时间
 * */

import React, { useState, useRef, useEffect, useImperativeHandle } from 'react';
import {
  Input,
  Select,
  Form,
  Modal,
  Tabs,
  Button,
  Divider,
  Popconfirm,
  Space,
  Radio,
  InputNumber,
  Row,
  Col,
} from 'antd';
import moment from 'moment';
import { getSession } from '@/utils/session';
import Tip from '@/components/Tip';
import DateTime from '@/components/Date';
import{settingDate}from '../../services'

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 15 },
  },
};

const index = (props: any, ref: any) => {
  const { code = undefined, orgCode = undefined } = getSession('org') || {
    code: undefined,
    orgCode: undefined,
  };
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('设置起交时间');
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [basinInfo, setBasinInfo] = useState<any>({});
  useImperativeHandle(ref, () => ({
    open: (query?: any) => {
      console.log('query===',query);
      
      if (query?.lastPayDate) {
        setTitle('修改起交时间');
        form.setFieldsValue({lastPayDate:moment(query.lastPayDate)});
      } else {
        setTitle('设置起交时间');
      }
      setBasinInfo(query);
      setVisible(true);
    },
  }));
  const hadndleFinish = async (vals: any) => {
    setConfirmLoading(true);
    const { code: resCode = 500 } = await settingDate({
      data: {
        // orgCode: code,
        // orgLevelCode: orgCode,
        code:basinInfo.memCode,
        // ...basinInfo,
        // ...vals,
        lastPayDate:moment(vals?.lastPayDate).valueOf()
      },
    });
    setConfirmLoading(false);
    if (resCode == 0) {
      const { onOk } = props;
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOk && onOk();
    }
  };
  const handleCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
    form.resetFields();
    setBasinInfo({});
  };
  return (
    <Modal
      title={title}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={'500px'}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      <div style={{ color: '#a5a5a5', paddingBottom: '20px' }}>
        温馨提示：该党员没有设置上次交纳时间，请选择一个时间来作为该党员的交纳起始时间，该时间为本系统最后交纳日期，从此时间下一个月开始生效。
      </div>
      <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
        <Row>
          <Col span={24}>
            <Form.Item
              label="党员姓名"
              name={'name'}
              rules={[{ required: false, message: '党员姓名' }]}
            >
              <div>{basinInfo?.memName || ''}</div>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="党费交纳起始时间"
              name={'lastPayDate'}
              rules={[{ required: true, message: '党费交纳起始时间' }]}
            >
              <DateTime isDefaultEnd={false} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default React.forwardRef(index);
