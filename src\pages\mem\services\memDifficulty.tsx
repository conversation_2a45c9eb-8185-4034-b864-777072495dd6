/**
 * 困难党员services
 */
import request from "@/utils/request";
import qs from 'qs';
export function getList(params) {
  return request(`/api/mem/difficult/getList?${qs.stringify(params)}`,{
    method:'Get',
  });
}

export function findByMemCode(params) {
  return request(`/api/mem/difficult/findByMemCode?${qs.stringify(params)}`,{
    method:'Get',
  });
}
export function addMemDifficult(params) {
  return request(`/api/mem/difficult/addMemDifficult`,{
    method:'POST',
    body:params,
  });
}
export function updateMemDifficult(params) {
  return request(`/api/mem/difficult/updateMemDifficult`,{
    method:'POST',
    body:params,
  });
}

export function delMemDifficult(params) {
  return request(`/api/mem/difficult/delMemDifficult?${qs.stringify(params)}`,{
    method:'Get',
  });
}


