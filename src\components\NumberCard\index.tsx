import React, {Fragment, PureComponent} from 'react';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import {Avatar, Card, Tooltip} from 'antd';
import CountUp from 'react-countup';
import style from './index.less';

export interface Interface {
  src?:string,
  iconColor:string,
  title:string,
  end:number,
  icon?:string,
  tooltipTitle?:string,
  prefix?:string,
  suffix?:string,
  subtitle?:object,
  bordered?:boolean ,
  bodyStyle?:object,
  tooltipPlacement?:string,
  duration?:number,
  separator?:string,
  decimals?:number,
  decimal?:string,
  onClick?:()=>void
}
export default class index extends PureComponent<Interface, {}> {
  static defaultProps = {
    // 自定义图片样式
    src:'',
    // 图标样式
    icon: 'smile',
    //文字提示内容
    tooltipTitle:'',
    // 图标颜色
    iconColor:'#ccc',
    // 标题
    title: '暂无标题',
    // 结束数字
    end: 0,
    // 前缀
    prefix:'',
    // 后缀
    suffix:'',
    // 是否有边框
    bordered:true,
    // 边框样式
    bodyStyle:{},
    // 动画持续时间
    duration:2,
    // 分组
    separator:',',
    // 进制
    decimals:0,
    decimal:'.',
    // 数字点击事件
    onClick:() => {console.log('没有点击事件');}
  }
  constructor(props){
    super(props);
    this.state = {

    };
  }

  render(){
    const {
      icon, tooltipTitle, iconColor, title, end, prefix, suffix, bordered,
      bodyStyle, duration, separator, decimals, decimal, children, onClick,
      src
    } = this.props;
    return (
      <div className={style.noPaddingCard}>
        <div className={style.card}>
          <div className={style.cardFlex}>
            <div className={style.cardCell}>

              <div className={style.icon}>
                <Tooltip
                  title={tooltipTitle}
                >
                  {
                    src ?
                      <div className={style.imgBox} style={{background:`${iconColor}`}}>
                        <img src={src} className={style.img}/>
                      </div>
                      :
                      <Avatar size={64} icon={<LegacyIcon type={icon} />} style={{backgroundColor:`${iconColor}`}}/>
                  }
                </Tooltip>
              </div>

              <div className={style.info}>
                <div className={style.words}>{title}</div>
                <div className={style.countUpNmber}>
                  {
                    !isNaN(end) ?
                      <div onClick={onClick}>
                        <CountUp
                          className="custom-count"
                          start={0}
                          end={end}
                          duration={duration}
                          separator={separator}
                          decimals={decimals}
                          decimal={decimal}
                          prefix={prefix}
                          suffix={suffix}
                        />
                      </div> :
                      <Fragment>0</Fragment>
                  }
                </div>
                <div style={{marginBottom:'10px'}}>
                  {children}
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    );
  }
}
