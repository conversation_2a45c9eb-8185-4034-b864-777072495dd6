import { basicModel } from '@/utils/common-model';
import modelExtend from "dva-model-extend";
import { _history } from '@/utils/method';
import { history } from 'umi';
import Tip from '@/components/Tip';
import { login, getOrgTree, getMenu, getPermission, getRole, getProgress, getProgressBar } from '../services/index.js';
import MenuData from '@/common/menu.js';
import { jsonToTree, treeToList } from "@/utils/method";

const loginModel = modelExtend(basicModel, {
  namespace: "login",
  state: {
    authorization: undefined,
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen((location) => {
        const { pathname, search } = location;
        let { permission } = sessionStorage;
        if (permission) {
          dispatch({
            type: 'getMenu',
            payload: {}
          });
        }
        if (pathname == '/login') {
          dispatch({
            type: 'clear',
            payload: {}
          });
        }
      });
    },
  },

  effects: {
    // 清镇伪登录
    *qzLogin({ payload }, { call, put, take }) {
      const res = yield call(login, payload);
      const { code = 500, data = {}, message = '操作失败' } = res || {};
      // 账号的节点
      sessionStorage.setItem('token', data['token']);
      // api
      sessionStorage.setItem('dataApi', data['dataKey']);
      if (payload.account == 'qztest001' || payload.account == 'qztest002' || payload.account == 'qztest003') {
        sessionStorage.setItem('dataApi', '3b571e29df3445a8');
      }
      // exchange
      sessionStorage.setItem('exchange', data['exchange']);
      if (code !== 0) {
        sessionStorage.clear();
      } else {
        yield put({
          type: 'updateState',
          payload: {
            authorization: data['authorization'],
            token: data['token']
          }
        });
        // 账号
        sessionStorage.setItem('account', payload['data']['account']);
        // 是否登录
        sessionStorage.setItem('authorization', data['token']);
      }
      return res
    },

    // 登录
    *login({ payload }, { call, put, take }) {
      const res = yield call(login, payload);
      const { code = 500, data = {}, message = '操作失败' } = res || {};
      // 账号的节点
      sessionStorage.setItem('token', data['token']);
      // api
      sessionStorage.setItem('dataApi', data['dataKey']);

      // exchange
      sessionStorage.setItem('exchange', data['exchange']);
      sessionStorage.setItem('isFlowStatus', '1');
      if (code !== 0) {
        sessionStorage.clear();
      } else {
        // if(data['needUpdatePassword']) return res
        yield put({
          type: 'getMenu',
          payload: {
            type: 'login'
          },
        });
        yield take('getMenu'); //菜单请求完成后跳转
        yield put({
          type: 'updateState',
          payload: {
            authorization: data['authorization'],
            token: data['token']
          }
        });

        // 一级菜单
        // sessionStorage.setItem('menuKey','11');
        // sessionStorage.setItem('menuItemKey','1110');
        sessionStorage.setItem('menuType', '1');
        // 账号
        sessionStorage.setItem('account', payload['data']['account']);
        // 是否登录
        sessionStorage.setItem('authorization', data['token']);
        // 是否需要修改密码
        sessionStorage.setItem('needUpdatePassword', data['needUpdatePassword']);
        // 是否已经弹出修改密码框
        sessionStorage.setItem('isOpenUpdatePassword', 'false');
      }
      console.log(res,'rrrrrrrrrrr')
      return res;
      // payload.resolve(res);
    },
    *getMenu({ payload }, { call, put, select, all }) {
      const state = yield select((state) => state['login']);
      const { type } = payload;
      if (!state['menuData'] || type) {
        //菜单为空 或者 重新登录的时候重新请求
        const [res, role] = yield all([call(getMenu, payload), call(getRole, payload)]);
        if (res['code'] === 0 && role['code'] === 0) {
          const { data = {} } = role;
          if (data['isNodeManage'] && data['isNodeManage'] === 1) {
            sessionStorage.setItem('systemBlock', 'zy');
          }
          // 用户管理系统
          sessionStorage.setItem('managementSystem', data['managementSystem']);

          const { manages = [] } = data;

          let defaultRole = manages.find((obj) => obj['isDefault']) || {};
          yield put({
            //拥有权限后查询组织树
            type: 'common/initTree',
            payload: {
              data: {
                orgCodeList: [defaultRole['managerOrgCode'] || ''],
              },
            },
          });
          let serverMenu = res['data'];
          // 存储权限列表id
          let pidArr = serverMenu.map((item) => item?.id);
          sessionStorage.setItem('pid', JSON.stringify(pidArr));
          delete data['manages'];

          // let serverIds:Array<number>=[];

          // for(let obj of res['data']){

          //   serverIds.push(obj['id']);

          // }
          let menuData: Array<object> = [];
          let system = sessionStorage.getItem('systemBlock');
          for (let obj of MenuData) {
            const find = serverMenu.find((ob) => ob['id'] == obj['serverId']);
            if (find) {
              let filter = MenuData.filter((mObj) => mObj.serverId == obj.serverId);
              if (filter.length == 1) {
                menuData.push({ ...obj, type: find['type'] });
              } else {
                let findJointly;
                if (system) {
                  findJointly = filter.find((sObj) => sObj.system == system);
                } else {
                  findJointly = filter.find((sObj) => sObj.system == undefined);
                }
                if (findJointly) {
                  if (!menuData.find((lObj: any) => lObj.serverId == findJointly.serverId)) {
                    menuData.push({ ...findJointly, type: find['type'] });
                  }
                }
              }
            }

          }

          // let menuData=MenuData.filter(obj=>serverIds.includes(Number(obj['serverId'])));
          let menuTreeData = jsonToTree(menuData, 'parent', 'code', '-1');
          yield put({
            type: 'updateState',
            payload: {
              menuData,

              menuTreeData,
              roles: defaultRole,
              user: data,
              permission: defaultRole['permissionCode'],
            },
          });

          // 权限
          sessionStorage.setItem('user', JSON.stringify(data));
          sessionStorage.setItem('permission', defaultRole['permissionCode']);
          sessionStorage.setItem('roles', JSON.stringify(defaultRole));
          if (type) {
            const { query } = _history.location;
            if (query['appcode'] && query['token'] && query['targetUrl']) {
              let findMenu = menuData.find((obj) => obj['url'] == query['targetUrl']);
              if (findMenu) {
                if (findMenu['parent']) {
                  sessionStorage.setItem('menuKey', findMenu['parent']);
                  sessionStorage.setItem('menuItemKey', findMenu['code']);
                } else {
                  sessionStorage.setItem('menuKey', findMenu['code']);
                }

                //延迟跳转 等待机构树数据查询完成
                setTimeout(() => {
                  history.push(query['targetUrl']);
                }, 200);
              } else {
                return Tip.error('提示', '暂无访问权限');
              }
            } else {
              // sessionStorage.setItem('menuKey','11');

              // sessionStorage.setItem('menuItemKey','1110');



              // 清镇市大屏展示项目直接跳转路由

              //不忘初心

              if (data['managementSystem'].includes('3')) {

                history.push('/qzs/screen/bwcx');

                return

              }

              //时代答卷

              if (data['managementSystem'].includes('4')) {

                history.push('/qzs/screen/sddj');

                return

              }

              //阶梯教室

              if (data['managementSystem'].includes('5')) {

                history.push('/qzs/screen/last');

                return

              }



              history.push('/desktop');

            }

          }
        } else {
          Tip.error('操作提示', '数据初始化异常');
          window.location.replace('/login');

          // history.push('/login')

          sessionStorage.clear();

        }

      }

    },
    *clear({ payload }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          menuData: undefined,
          timeKey: +new Date(),
        },
      });

    },

    // 重新请求role接口，更新 managementSystem
    *getRoleToRefresh({ payload }, { call, put, select, all }) {
      const { data = {} } = yield call(getRole, payload);
      sessionStorage.setItem('managementSystem', data['managementSystem']);
      history.push('?');

    },

    *getProgress({ payload }, { put, call }) {

      const { type, projectName } = payload;

      let res;

      if (projectName === 'niandu') {

        res = yield call(getProgress, { type });

      } else {

        res = yield call(getProgressBar, { type });

      }

      const { code = undefined, data = undefined, message = undefined } = res || {};

      if (code === 0) {

        return Promise.resolve(data);

      }

      return Promise.reject();

    },
  },
});

export default loginModel;