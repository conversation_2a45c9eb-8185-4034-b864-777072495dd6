import React, { useEffect, useState, Fragment, useRef } from 'react';
import { Divider } from 'antd';
import style from './index.less';
import { getSession } from '@/utils/session';
import { Button } from 'antd';
import { getYearReport, exportYearReport } from '@/pages/desktop/services';
import ExportInfo from '@/components/Export';
import _get from 'lodash/get';
const index = () => {
  const org = getSession('org') || { name: '', orgCode: '' };
  const { name = '', orgCode = '' } = org;
  const [query, setQurey] = useState<any>({});
  const exportRef: any = useRef();
  const exportInfo = async () => {
    await exportRef.current.submitNoModal();
  };
  const getInfo = async () => {
    const res = await getYearReport({
      data: {
        orgCode,
      },
    });
    if (res['code'] === 0) {
      setQurey(res['data']);
    }
  };
  useEffect(() => {
    getInfo();
  }, [orgCode]);
  return (
    <Fragment>
      <div>
        <Button type={'primary'} onClick={exportInfo}>
          导出
        </Button>
      </div>
      <div className={style.page}>
        <div className={style.tit1}>{name}</div>
        <div className={style.tit1} style={{ fontSize: 20 }}>
          {_get(query, 'year', '')}年中国共产党党内统计简报
        </div>
        <Divider />
        <div style={{ lineHeight: '20px', marginBottom: '24px', color: 'red' }}>
          按照党组织隶属关系，截至{_get(query, 'endReportDate', '')}
          本单位（系统）党员和党组织基本情况。
        </div>
        <div className={style.tit2}> 一、党员情况</div>
        <ol>
        {/* 1 */}
          <li>
            本单位（系统）共有中国共产党党员
            <span className={style.nums}>{_get(query, 'list[0].count', 0)}</span>名，其中，女性党员
            <span className={style.nums}>{_get(query, 'list[1].count', 0)}</span>名，少数民族党员
            <span className={style.nums}>{_get(query, 'list[2].count', 0)}</span>名；
          </li>
          {/* 2 */}
          <li>
            本单位（系统）中国共产党党员中，党政机关工作人员
            <span className={style.nums}>{_get(query, 'list[3].count', 0)}</span>名，事业编制人员
            <span className={style.nums}>{_get(query, 'list[4].count', 0)}</span>人（含参公管理事业单位人员），公有制企业人员
            <span className={style.nums}>{_get(query, 'list[5].count', 0)}</span>人，非公有制企业人员
            <span className={style.nums}>{_get(query, 'list[6].count', 0)}</span>人，社会组织人员
            <span className={style.nums}>{_get(query, 'list[7].count', 0)}</span>人，现役军人
            <span className={style.nums}>{_get(query, 'list[8].count', 0)}</span>人，农牧渔民
            <span className={style.nums}>{_get(query, 'list[9].count', 0)}</span>人，离退休人员
            <span className={style.nums}>{_get(query, 'list[10].count', 0)}</span>名，学生
            <span className={style.nums}>{_get(query, 'list[11].count', 0)}</span>名，工勤技能人员
            <span className={style.nums}>{_get(query, 'list[12].count', 0)}</span>名，其他身份党员
            <span className={style.nums}>{_get(query, 'list[13].count', 0)}</span>名（备注说明）；
          </li>
          {/* 3 */}
          <li>
            本单位（系统）本年内发展党员
            <span className={style.nums}>{_get(query, 'list[14].count', 0)}</span>名，其中发展女党员
            <span className={style.nums}>{_get(query, 'list[15].count', 0)}</span>名，少数民族党员
            <span className={style.nums}>{_get(query, 'list[16].count', 0)}</span>名，特殊情况下发展党员
            <span className={style.nums}>{_get(query, 'list[17].count', 0)}</span>名。党政机关工作人员
            <span className={style.nums}>{_get(query, 'list[18].count', 0)}</span>名，事业编制人员
            <span className={style.nums}>{_get(query, 'list[19].count', 0)}</span>人（含参公管理事业单位人员），公有制企业人员
            <span className={style.nums}>{_get(query, 'list[20].count', 0)}</span>人，非公有制企业人员
            <span className={style.nums}>{_get(query, 'list[21].count', 0)}</span>人，社会组织人员
            <span className={style.nums}>{_get(query, 'list[22].count', 0)}</span>人，现役军人
            <span className={style.nums}>{_get(query, 'list[23].count', 0)}</span>人，农牧渔民
            <span className={style.nums}>{_get(query, 'list[24].count', 0)}</span>人，离退休人员
            <span className={style.nums}>{_get(query, 'list[25].count', 0)}</span>名，学生
            <span className={style.nums}>{_get(query, 'list[26].count', 0)}</span>名，工勤技能人员
            <span className={style.nums}>{_get(query, 'list[27].count', 0)}</span>名，其他身份党员
            <span className={style.nums}>{_get(query, 'list[28].count', 0)}</span>名（备注说明）；新的社会阶层人员
            <span className={style.nums}>{_get(query, 'list[29].count', 0)}</span>名；
          </li>
          {/* 4 */}
          <li>
            本单位（系统）共有入党申请人
            <span className={style.nums}>{_get(query, 'list[30].count', 0)}</span>名，其中，入党积极分子
            <span className={style.nums}>{_get(query, 'list[31].count', 0)}</span>名，发展对象
            <span className={style.nums}>{_get(query, 'list[32].count', 0)}</span>名；
          </li>
          {/* 5 */}
          <li>
            本单位（系统）本年内受党内表彰的党员
            <span className={style.nums}>{_get(query, 'list[33].count', 0)}</span>名，受纪律处分党员
            <span className={style.nums}>{_get(query, 'list[34].count', 0)}</span>名，其中，开除党籍
            <span className={style.nums}>{_get(query, 'list[35].count', 0)}</span>名；完成民主评议党员
            <span className={style.nums}>{_get(query, 'list[36].count', 0)}</span>名（开展评议时间在当年内的统计）；
          </li>
          {/* 6 */}
          <li>
            本单位（系统）党员本年内参加培训
            <span className={style.nums}>{_get(query, 'list[37].count', 0)}</span>人次，基层党委开展培训
            <span className={style.nums}>{_get(query, 'list[38].count', 0)}</span>期，基层党委开展培训
            <span className={style.nums}>{_get(query, 'list[39].count', 0)}</span>人次。
          </li>
        </ol>
        <div className={style.tit2}> 二、党组织基本情况</div>
        <ol>
        {/* 1 */}
          <li>
            本单位（系统）共有党组（党委）
            <span className={style.nums}>{_get(query, 'list[40].count', 0)}</span>个，派出工委
            <span className={style.nums}>{_get(query, 'list[41].count', 0)}</span>个，基层党组织
            <span className={style.nums}>{_get(query, 'list[42].count', 0)}</span>个，其中，基层党委
            <span className={style.nums}>{_get(query, 'list[43].count', 0)}</span>个，党总支
            <span className={style.nums}>{_get(query, 'list[44].count', 0)}</span>个，党支部
            <span className={style.nums}>{_get(query, 'list[45].count', 0)}</span>个；
          </li>
          {/* 2 */}
          <li>
            本单位（系统）基层党组织中，公有制企业党组织
            <span className={style.nums}>{_get(query, 'list[46].count', 0)}</span>个，非公有制企业党组织
            <span className={style.nums}>{_get(query, 'list[47].count', 0)}</span>个，事业单位党组织
            <span className={style.nums}>{_get(query, 'list[48].count', 0)}</span>个，机关党组织
            <span className={style.nums}>{_get(query, 'list[49].count', 0)}</span>个，社会组织党组织
            <span className={style.nums}>{_get(query, 'list[50].count', 0)}</span>个，其他党组织
            <span className={style.nums}>{_get(query, 'list[51].count', 0)}</span>个（备注说明）；
          </li>
          {/* 3 */}
          <li>
            本单位（系统）本年内应换届基层党组织
            <span className={style.nums}>{_get(query, 'list[52].count', 0)}</span>个，已完成换届基层党组织
            <span className={style.nums}>{_get(query, 'list[53].count', 0)}</span>个。
          </li>
        </ol>
        <div className={style.tit2}> 三、法人单位基本情况</div>
        <ol>
        {/* 1 */}
          <li>
            本单位（系统）共有机关单位
            <span className={style.nums}>{_get(query, 'list[54].count', 0)}</span>个，事业单位
            <span className={style.nums}>{_get(query, 'list[55].count', 0)}</span>个，街道
            <span className={style.nums}>{_get(query, 'list[56].count', 0)}</span>个，乡镇
            <span className={style.nums}>{_get(query, 'list[57].count', 0)}</span>个，行政村
            <span className={style.nums}>{_get(query, 'list[58].count', 0)}</span>个，城市社区
            <span className={style.nums}>{_get(query, 'list[59].count', 0)}</span>个，农村社区
            <span className={style.nums}>{_get(query, 'list[60].count', 0)}</span>个，公有制企业
            <span className={style.nums}>{_get(query, 'list[61].count', 0)}</span>个，非公有制企业
            <span className={style.nums}>{_get(query, 'list[62].count', 0)}</span>个，社会团体
            <span className={style.nums}>{_get(query, 'list[63].count', 0)}</span>个，民办非企业
            <span className={style.nums}>{_get(query, 'list[64].count', 0)}</span>个，其他单位
            <span className={style.nums}>{_get(query, 'list[65].count', 0)}</span>个（备注说明）
          </li>
        </ol>
      </div>
      <ExportInfo
        wrappedComponentRef={exportRef}
        tableName={''}
        noModal={true}
        tableListQuery={{ orgCode }}
        action={'/api/chart/exportYearReport'}
      />
    </Fragment>
  );
};

export default index;
