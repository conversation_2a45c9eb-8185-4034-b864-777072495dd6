import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Modal, Switch, Alert, DatePicker, Upload, message, But<PERSON>, Row, Col } from 'antd';
import MemSelect from '@/components/MemSelect';
import OrgSelect from '@/components/OrgSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import DictSelect from '@/components/DictSelect';
import moment from 'moment';
import Tip from '@/components/Tip';
import { unixMoment, findDictCodeName } from '@/utils/method.js';
import _isNumber from 'lodash/isNumber';
import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';
import _isString from 'lodash/isString';
import Date from '@/components/Date';
import { getContextPerson, compareDate } from '@/pages/developMem/services/index'
// import Sure from '../SureBasicInfoAndChange';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      memInfo: {},
      hasMemValue: true,
      timeKey: moment().valueOf(),
      d89Code: "",
      specialChecked: false,
    }
  }
  // 时间限制
  disabledTomorrow = (current) => {
    const { memInfo: { objectDate = '' } = {} } = this.state;
    const cu = moment(current);
    const start = moment(objectDate).endOf('day');
    // console.log(start.format('YYYY-MM-DD'),'123')
    const end = moment();
    if (_isNumber(objectDate)) {
      return current && (cu.isBefore(start) || cu.isSame(start) || cu.isAfter(end) || cu.isSame(end))
    } else {
      return false
    }
  };

  // 是否 特殊情况下发展
  handleSpecialChange = (e) => {
    this.setState({ specialChecked: e });
  };

  memLength = (rule, value, callback) => {
    if (_isArray(value) && !_isEmpty(value)) {
      if (value.length < 2) {
        callback('至少选择2名人员');
      } else {
        callback();
      }
    } else {
      callback();
    }
  };
  handleOk = () => {
    const { submit } = this.props;
    const { memInfo, hasMemValue } = this.state;
    const { name, code: memCode, orgCode, orgName, orgZbCode, developOrgCode: logOrgCode, d08Code, d08Name } = memInfo;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        val[`hasStaffOrganization`] = val[`hasStaffOrganization`] == '1' ? 1 : 0;
        val = findDictCodeName(['d89', 'd49', 'd18', 'd19', 'd20', 'd21', 'd07', 'd09', 'd11', 'd27', 'd28', 'd06', 'd08', 'd60', 'd88', 'readingProfessional', 'politics', 'advancedModel'], val, memInfo);
        if (val['joinOrgCode'] === '112') {
          val['joinOrgName'] = '特殊情况下发展'
        } else {
          val = findDictCodeName(['joinOrg'], val, {});
        }
        val = unixMoment(['topreJoinOrgDate', 'topreCommitteeDate'], val);
        if (_isString(val['topreIntroductionMem'])) {
          val['topreIntroductionMem'] = this.state.toactiveContextPerson;
        } else {
          val['topreIntroductionMem'] = _isEmpty(val['topreIntroductionMem']) ? '' : hasMemValue ? val['topreIntroductionMem'].map(item => item['code']).toString() : val['topreIntroductionMem'];
        }
        val['name'] = name;
        val['d11Name'] = '新发展';
        val['memCode'] = memCode;
        val['d08Code'] = d08Code;
        val['d08Name'] = d08Name;

        val['orgZbCode'] = typeof val['orgCode'] === 'object' ? val['orgCode'][0]['zbCode'] : memInfo['orgZbCode'];
        val['logOrgCode'] = typeof val['orgCode'] === 'object' ? val['orgCode'][0]['orgCode'] : memInfo['developOrgCode'];
        val['orgName'] = typeof val['orgCode'] === 'object' ? val['orgCode'][0]['name'] : memInfo['orgName'];
        val['orgCode'] = typeof val['orgCode'] === 'object' ? val['orgCode'][0]['code'] : memInfo['orgCode'];

        // console.log(val,'val');

        const res = await this.props.dispatch({
          type: 'memDevelop/ToFirePreparation',
          payload: { data: { ...val } }
        });
        const { code = 500 } = res;
        if (code === 0) {
          this.handleCancel();
          Tip.success('操作提示', '操作成功');
          submit && submit();
        }

      }
    })
  };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  open = (record) => {
    this.getReferences(record)
    this.setState({ visible: true, memInfo: record, timeKey: moment().valueOf() })
    this.props.dispatch({
      type: 'memDevelop/findMem',
      payload: {
        code: record['code']
      }
    })
  };
  destroy = () => {
    this.setState({
      memInfo: {},
      hasMemValue: true,
      d89Code: [],
      toactiveContextPerson: "",
      toobjContextMem: "",
      specialChecked: false,
    })
  };
  hasMemOnChange = (val) => {
    // topreIntroductionMem: true时输入值是数组，false是字符串("topreIntroductionMem"))
    let person = this.props.form.getFieldValue("topreIntroductionMem")
    if (!val) {
      if (_isArray(person)) {
        let personArr = person.map((item, index) => item.name);
        this.props.form.setFieldsValue({ topreIntroductionMem: personArr.toString() });
      }
    }
    this.props.form.setFieldsValue({ toactiveContextPerson: undefined });
    this.setState({ hasMemValue: val })
  };
  joinOrgChange = (e) => {
    if (e.key == "121" || e.key == "121_temp") {
      this.setState({
        memInfo: {
          d89Code: ["12"]
        }
      })
    }
  }
  // 接收预备党员获取介绍人
  getReferences = async (record) => {
    const { code = 500, data = {} } = await getContextPerson({
      memCode: record.code,
      d08Code: "4",
    });
    if (code === 0) {
      this.setState({
        toactiveContextPerson: data.toobjContextMem,
        toobjContextMem: data.name
      })

    }
  };
  timeValidator = async (rule, value, callback) => {
    const { code = 500, data = true } = await compareDate({
      data: {
        code: this.state.memInfo.code,
        type: 2,
        time: moment(value).valueOf()
      }
    });
    if (code === 0) {
      if (!data) {
        callback(new Error('支部党员大会讨论通过时间早于确定发展对象时间'));
      } else {
        callback();
      }
    }
  }
  render() {
    const { form, loading: { effects = {} } = {} } = this.props;
    const { getFieldDecorator } = form;
    const { visible, hasMemValue, memInfo, specialChecked } = this.state;
    return (
      <Modal
        destroyOnClose
        title="火线入党"
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        confirmLoading={effects['memDevelop/ToFirePreparation']}
        width={1200}
      >
        {
          visible && <Fragment key={this.state.timeKey}>
            {/* <Alert message="提示：支部党员大会讨论通过时间为成为预备党员时间。" type="info" showIcon /> */}
            <div style={{ marginBottom: '10px' }} />
            <Form>
              <Row>
                <Col span={12}>
                  <FormItem
                    label="入党介绍人是否为本组织人员"
                    {...formItemLayout2}
                  >
                    {getFieldDecorator('hasStaffOrganization', {
                      rules: [{ required: true, message: '入党介绍人是否为本组织人员' }],
                      initialValue: hasMemValue,
                    })(
                      <Switch checkedChildren="是" unCheckedChildren="否" onChange={this.hasMemOnChange} checked={hasMemValue} />
                    )}
                  </FormItem>
                </Col>
                <Col span={24}>
                  {
                    hasMemValue ?
                      <FormItem
                        label="入党介绍人"
                        {...formItemLayout1}
                      >
                        {getFieldDecorator('topreIntroductionMem', {
                          rules: [{ required: true, message: '入党介绍人' }, { validator: this.memLength }],
                          // initialValue:hasMemValue,
                          initialValue: this.state.toobjContextMem,
                        })(
                          <MemSelect initValue={this.state.toobjContextMem} checkType={'checkbox'} placeholder="请选择" />
                        )}
                      </FormItem>
                      :
                      <FormItem
                        label="入党介绍人"
                        {...formItemLayout1}
                      >
                        {getFieldDecorator('topreIntroductionMem', {
                          rules: [{ required: true, message: '入党介绍人' }],
                          // initialValue:hasMemValue,
                          initialValue: this.state.toobjContextMem,
                        })(
                          <Input placeholder="请选择" />
                        )}
                      </FormItem>
                  }
                </Col>
                <Col span={24}>
                  <FormItem
                    label="所在党组织"
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('orgCode', {
                      rules: [{ required: true, message: '请选择所在党支部' }],
                      initialValue: _isEmpty(memInfo) ? undefined : memInfo['orgCode'],
                    })(
                      <OrgSelect orgTypeList={['3', '4']} initValue={memInfo['orgName']} placeholder={'请选择所在党组织'} />
                    )}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label="支部党员大会讨论通过时间"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('topreJoinOrgDate', {
                      rules: [{ required: true, message: '召开支部大会日期' },
                      // { validator: this.timeValidator, trigger: ['blur', 'change'] }
                    ],
                      // initialValue:hasMemValue,
                    })(
                      // <DatePicker style={{width:'100%'}} disabledDate={this.disabledTomorrow}/>
                      <Date isDefaultEnd={true} /> // 支部党员大会讨论通过时间 不能大于当前时间
                    )}
                  </FormItem>
                </Col>
                {/*<Col span={12}>*/}
                {/*  <FormItem*/}
                {/*    label="上级党委审批日期"*/}
                {/*    {...formItemLayout}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('topreCommitteeDate', {*/}
                {/*      rules: [{ required: true, message: '上级党委审批日期' }],*/}
                {/*      // initialValue:hasMemValue,*/}
                {/*    })(*/}
                {/*      // <DatePicker style={{width:'100%'}}  />*/}
                {/*      <Date />*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                <Col span={12}>
                  <FormItem
                    label="加入中共组织的类别"
                    {...formItemLayout}

                  >
                    {getFieldDecorator('joinOrgCode', {
                      rules: [{ required: true, message: '加入中共组织的类别' }],
                      initialValue: '112',
                    })(
                      <DictTreeSelect
                        backType={'object'}
                        codeType={'dict_d27'}
                        placeholder="请选择"
                        disabled={true}
                        noDraw={specialChecked ? ['2', '3', '4', '5', '6', '21', '22', '31', '111', '1111', '1112'] : ['2', '3', '4', '5', '6', '21', '22', '31']}
                        parentDisable={true}
                        initValue={'112'}
                        onChange={this.joinOrgChange}
                        filter={(data = {}) => {
                          if (specialChecked) {
                            let item = []
                            item = item.concat(data.filter(obj => ['11'].includes(obj['key'])))
                            return item
                          }
                          return data
                        }}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="进入支部类型"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('d11Code', {
                      rules: [{ required: false, message: '进入支部类型' }],
                      initialValue: '1',
                    })(
                      <DictSelect backType={'object'} codeType={'dict_d11'} placeholder="请选择" disabled={true} initValue={'1'} />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="入党志愿书编号"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('topreJoinBookNum', {
                      rules: [{ required: false, message: '入党志愿书编号' }],
                      // initialValue:_isEmpty(memInfo)?undefined:memInfo['d09Code'],
                    })(
                      <Input placeholder={'入党志愿书编号'} />
                    )}
                  </FormItem>
                </Col>
              </Row>
              {/* <Sure {...this.props} memInfo={this.state.memInfo} /> */}
            </Form>
          </Fragment>
        }
      </Modal>
    );
  }
}
export default Form.create()(index);
