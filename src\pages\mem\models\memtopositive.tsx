import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import { becomeFullMem , prolongationMem,repealProlongationMem } from '../services';
const memToPositive = modelExtend(listPageModel,{
  namespace: "memToPositive",
  state:{
    abroadInfo:{},
    list:[],
    pagination:{}
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        // const { pathname, query } = location;
      });
    }
  },
  effects: {
    // 转正
    *becomeFullMem({ payload }, { call, put }) {
      const res = yield call(becomeFullMem,payload);
      return res;
    },

    // 延长预备期
    *prolongationMem({ payload }, { call, put }) {
      const res = yield call(prolongationMem,payload);
      return res;
    },
    // 撤销延长预备期
    *repealProlongationMem({ payload }, { call, put }) {
      const res = yield call(repealProlongationMem,payload);
      return res;
    },
}
});
export default memToPositive;
