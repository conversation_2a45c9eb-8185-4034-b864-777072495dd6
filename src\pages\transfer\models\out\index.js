import modelExtend from 'dva-model-extend';
import { listPageModel } from 'src/utils/common-model';
import {
  addTransfer,
  adjustMem,
  apply,
  back,
  changeTargetOrg,
  editTransferForMem,
  findOutByPage,
  inDetail,
  itteeDel,
  itteeList,
  itteeUP,
  outDetail,
  transferMem,
  transferMemInfo,
  undo,
  findMemInfo,
  checkMemInfo
} from '../../services';
import { getSession } from 'src/utils/session';

const org = modelExtend(listPageModel, {
  namespace: "transferOut",
  state: {
    basicInfo: {},
    basicInfoData: {},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if (pathname === '/transfer/outflows' || pathname === '/transfer/historyout') {
          const org = getSession('org') || {};
          const dictData = ['dict_d58'];
          let isHistory = false;
          for (let obj of dictData) {
            dispatch({
              type: 'commonDict/getDictTree',
              payload: {
                data: {
                  dicName: obj
                }
              }
            });
          }
          const defaultParas = {
            pageNum: 1,
            pageSize: 10,
          };
          if (pathname === '/transfer/historyout') {
            isHistory = true;
          }
          dispatch({
            type: 'findOutByPage',
            payload: {
              orgId: org['code'],
              isHistory,
              ...defaultParas,
              ...query,
            }
          })
        }
      });
    }
  },
  effects: {
    *findOutByPage({ payload }, { call, put, select }) {//转出列表
      const { filter, keyWord } = yield select(state => state['transferOut']);
      payload = { ...payload, ...filter, keyWord };
      const { data = {} } = yield call(findOutByPage, { data: payload });
      yield put({
        type: 'updateState',
        payload: {
          outList: data['list'],
          outPagination: {
            current: data['pageNumber'],
            pageNum: data['pageNumber'],
            // current: data['pageNum'],
            pageSize: data['pageSize'],
            total: data['totalRow'],
          }
        }
      })
    },
    *addTransfer({ payload }, { call, put }) {//整建制转接
      return yield call(addTransfer, payload);
    },
    *transferMem({ payload }, { call, put }) {//人员转接
      return yield call(transferMem, payload);
    },
    *adjustMem({ payload }, { call, put }) {
      return yield call(adjustMem, payload);
    },
    *inDetail({ payload }, { call, put }) {
      const { data } = yield call(inDetail, payload);
      yield put({
        type: 'updateState',
        payload: {
          transDetail: data,
        }
      });
    },
    *outDetail({ payload }, { call, put }) {
      const { data } = yield call(outDetail, payload);
      yield put({
        type: 'updateState',
        payload: {
          transDetail: data,
        }
      });
    },

    *apply({ payload }, { call, put }) {//审批通过
      return yield call(apply, payload);
    },
    *back({ payload }, { call, put }) {
      return yield call(back, payload);
    },
    *changeTargetOrg({ payload }, { call, put }) {
      return yield call(changeTargetOrg, payload);
    },


    *undo({ payload }, { call, put }) {
      return yield call(undo, payload);
    },
    *transferMemInfo({ payload }, { call, put }) {
      const { data } = yield call(transferMemInfo, payload);
      yield put({
        type: 'updateState',
        payload: {
          transferMemInfo: data,
        }
      });
    },
    *editTransferForMem({ payload }, { call, put }) {
      return yield call(editTransferForMem, payload);
    },

    *itteeUp({ payload }, { call, put }) {
      return yield call(itteeUP, payload);
    },
    *itteeList({ payload }, { call, put }) {
      const { keys } = payload;
      const { data = {} } = yield call(itteeList, payload);
      yield put({
        type: 'updateState',
        payload: {
          [`iteList_${keys}`]: data['list'] || [],
        }
      });
    },
    *itteeDel({ payload }, { call, put }) {
      return yield call(itteeDel, payload);
    },
    *findMem({ payload }, { call, put }) {
      const res = yield call(findMemInfo, payload);
      const { code = 500, data = {} } = res || {};
      yield put({
        type: 'updateState',
        payload: {
          basicInfo: code === 0 ? data : {}
        }
      })
    },
    *checkMemInfo({ payload }, { call, put }) {
      const { type = '', data = {} } = payload;
      let res = yield call(checkMemInfo, { data });
      return res;
    },
  }
});
export default org;
