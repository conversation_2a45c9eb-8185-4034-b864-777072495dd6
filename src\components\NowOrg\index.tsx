import React from 'react';
import WhiteSpace from '../WhiteSpace';
import {withContext} from '@/utils/global.jsx';
import styles from './index.less';
import {Space} from "antd";
interface Interface {
  context:object,
  extra?:React.ReactNode,
}
class index extends React.Component<Interface, any> {
  render(): React.ReactNode {
    const {context={},extra} = this.props;
    // return(
    //   <Space style={{margin:'10px 0'}} className={styles.content} align={'center'}>
    //     <span style={{textAlign:'left',float:'left',fontSize:20}}>{context['name'] || ''}</span>
    //     <div className={styles.right}>{extra}</div>
    //   </Space>
    // )
    return (
      <div className={styles.newContent}>
        <span style={{textAlign:'left',fontSize:20}}>{context['name'] || ''}</span>
        <div className={styles.right}>{extra}</div>
      </div>
    )
  }
}
export default withContext(index)
