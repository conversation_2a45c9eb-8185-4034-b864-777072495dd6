import React, { useState, Fragment, useEffect, useRef, useImperativeHandle } from 'react';
import _isEmpty from 'lodash/isEmpty';
import style from './index.less';
import { _history } from "@/utils/method";
import qs from 'qs';
import { Tabs, Input, Modal, Form, Radio, Button, Descriptions, Divider, Row, Col } from 'antd';
import ListTable from 'src/components/ListTable';
import { getMemDevelopAuditList, auditMemDevelop, digitalCountListDetail, expdigitalCountListDetail } from '../service'
import { getSession } from '@/utils/session';
import NowOrg from 'src/components/NowOrg';
import moment from 'moment'
import { connect } from "dva";
import tip from '@/components/Tip';
import ElectronicArchives from '@/pages/developMem/zy/components/electronicArchives'
import Icon from '@ant-design/icons';
import styles from './index.less';
const Search = Input.Search;

const TabPane = Tabs.TabPane;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};

function Index(props: any, ref: any) {
    const [filterHeight, setFilterHeight] = useState();
    const [loading, setLoading] = useState(false);
    const [list, setList] = useState([]);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [memName, setMemName] = useState('')
    const [columns, setColumns]: any = useState([])
    const [value, setValue] = useState('')
    const [visible, setVisible] = useState(false)
    const [record, setRecord]: any = useState({})
    const [statu, setStatu] = useState(0)
    const [form] = Form.useForm();
    const { location: { pathname = '' } = {} } = _history
    const org = getSession('org') || {};


    const getList = async (p?: any) => {
        setLoading(true)
        console.log({
            pageNum: 1,
            pageSize: 100,
            orgLevelCode: record.orgLevelCode,
            colName: record.colName,
            match: statu == 1 ? true : statu == 2 ? false : '',
            memName: memName,
            ...p
        }, '大撒打算打算的')
        const { code = 500, data: { list = [], pageNumber = '', pageSize = '', totalRow = '' } = {} } = await digitalCountListDetail({
            data: {
                pageNum: 1,
                pageSize: 100,
                orgLevelCode: record.orgLevelCode,
                colName: record.colName,
                match: statu == 1 ? true : statu == 2 ? false : '',
                memName: memName,
                ...p
            }
        })
        setLoading(false)
        if (code == 0) {
            setList(list)
            setPagination({ current: pageNumber, pageSize: pageSize, total: totalRow })
        }

    }
    const handleOk = () => {
        form.submit()
    }
    const handleCancel = () => {
        form.resetFields()
        setStatu(0)
        setRecord({})
        setMemName('')
        setVisible(false)
        setValue('')
    }

    const changebtn = (key) => {
        setStatu(key)
    }
    const exp = async () => {
        const { code } = await expdigitalCountListDetail({
            data: {
                pageNum: pagination.current,
                pageSize: pagination.pageSize,
                orgLevelCode: record.orgLevelCode,
                colName: record.colName,
                match: statu == 1 ? true : statu == 2 ? false : '',
                memName: memName,
            }
        })
    }
    useEffect(() => {
        if (visible) {
            getList({ pageNum: 1, pageSize: 10 })
        }

    }, [JSON.stringify(record), memName, statu])

    // 渲染状态标签的通用方法
    const renderStatusTag = (text) => {
        if (text === '未完成') {
            return (
                <div
                    style={{
                        borderRadius: '4px',
                        background: '#FF4343',
                        color: '#fff',
                        display: 'inline-block',
                        padding: '4px 10px'
                    }}
                >
                    {text}
                </div>
            );
        }
        if (text === '已完成') {
            return (
                <div
                    style={{
                        borderRadius: '4px',
                        background: '#00CD29',
                        color: '#fff',
                        display: 'inline-block',
                        padding: '4px 10px'
                    }}
                >
                    {text}
                </div>
            );
        }
        return (
            <div
                style={{
                    borderRadius: '4px',
                    background: 'rgb(206, 206, 206)',
                    color: '#fff',
                    display: 'inline-block',
                    padding: '4px 10px'
                }}
            >
                未到达
            </div>
        );
    };
    useImperativeHandle(ref, () => ({
        open: (val: any) => {
            // setStatu(val)
            setRecord(val)
            setStatu(val.match ? 1 : 2)
            setVisible(true)
            setColumns([
                {
                    title: '序号',
                    dataIndex: 'num',
                    width: 60,
                    align: 'center',
                    render: (text, record, index) => {
                        return (pagination['current'] - 1) * pagination['pageSize'] + index + 1
                    }
                },
                {
                    title: '姓名',
                    align: 'center',
                    dataIndex: 'memName',
                    width: 120,
                    render: (text) => (
                        <div style={{
                            width: '120px',
                            wordBreak: 'break-all',
                            wordWrap: 'break-word',
                            whiteSpace: 'normal',
                            lineHeight: '1.5',
                            minHeight: '32px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            {text}
                        </div>
                    )
                },
                {
                    title: '人员类别',
                    align: 'center',
                    dataIndex: 'd08Name',
                    width: 120,
                },
                {
                    title: '党组织名称',
                    align: 'center',
                    dataIndex: 'orgName',
                    width: 300,
                },

                {
                    title: <span style={val.colName == 'step1' ? { fontWeight: 'bold', fontSize: '18px' } : {}}>第一阶段<br />(入党申请人)</span>,
                    dataIndex: 'step1',
                    width: 120,
                    align: 'center',
                    render: (text) => renderStatusTag(text)
                },
                {
                    title: <span style={val.colName == 'step2' ? { fontWeight: 'bold', fontSize: '18px' } : {}}>第二阶段<br />(入党申请人)</span>,
                    dataIndex: 'step2',
                    width: 120,
                    align: 'center',
                    render: (text) => renderStatusTag(text)
                },
                {
                    title: <span style={val.colName == 'step3' ? { fontWeight: 'bold', fontSize: '18px' } : {}}>第三阶段<br />(积极分子)</span>,
                    dataIndex: 'step3',
                    width: 120,
                    align: 'center',
                    render: (text) => renderStatusTag(text)
                },
                {
                    title: <span style={val.colName == 'step4' ? { fontWeight: 'bold', fontSize: '18px' } : {}}>第四阶段<br />(发展对象)</span>,
                    dataIndex: 'step4',
                    width: 120,
                    align: 'center',
                    render: (text) => renderStatusTag(text)
                },
                {
                    title: <span style={val.colName == 'step5' ? { fontWeight: 'bold', fontSize: '18px' } : {}}>第五阶段<br />(预备党员)</span>,
                    dataIndex: 'step5',
                    width: 120,
                    align: 'center',
                    render: (text) => renderStatusTag(text)
                },
                {
                    title: <span style={val.colName == 'stepOther' ? { fontWeight: 'bold', fontSize: '18px' } : {}}>其他</span>,
                    dataIndex: 'stepOther',
                    width: 120,
                    align: 'center',
                    render: (text) => renderStatusTag(text)
                },

            ])
        },
    }));

    return (
        <Modal
            title={'查看'}
            visible={visible}
            onCancel={handleCancel}
            width={1400}
            footer={[
                <Button key="back" onClick={handleCancel}>关闭</Button>,
            ]}
        >
            <div style={{ marginBottom: 16 }}>
                <Row>
                    <Col span={6} style={{ display: 'flex', alignItems: 'center' }}>

                        <span>完成情况:</span>
                        <div className={styles.btns}>
                            <div onClick={() => changebtn('0')} className={statu == 0 ? `${styles.items} ${styles.checks}` : `${styles.items}`}>全部</div>
                            <div onClick={() => changebtn('1')} className={statu == 1 ? `${styles.items} ${styles.checks}` : `${styles.items}`}>已完成</div>
                            <div onClick={() => changebtn('2')} className={statu == 2 ? `${styles.items} ${styles.checks}` : `${styles.items}`}>未完成</div>
                        </div>
                    </Col>
                    <Col span={6}>
                        <Search
                            placeholder="请输入姓名搜索"
                            value={value}
                            onChange={e => {
                                setValue(e.target.value);
                            }}
                            onSearch={value => {
                                setMemName(value);
                            }}
                            style={{ width: 200 }}
                        />
                        <Button type="primary" style={{ marginLeft: 10 }} onClick={exp}>导出</Button>
                    </Col>

                </Row>
            </div>
            <ListTable scroll={{ y: filterHeight }}

                columns={columns} data={list} pagination={pagination} onPageChange={(page, pageSize) => {
                    getList({ pageNum: page, pageSize });
                }} />
        </Modal>
    );
}
export default React.forwardRef(Index)