// 流动人员详情-仅作展示信息使用，不可编辑基本信息
import React, { Fragment } from 'react';
import { connect } from 'dva';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import _isObject from 'lodash/isObject';
import { Form } from '@ant-design/compatible';
import { Input, Modal, InputNumber, Button, Select, Popconfirm, Divider, Row, Col } from 'antd';
import moment from 'moment';
import { getSession } from '@/utils/session';
import { formLabel, findDictCodeName } from '@/utils/method';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import DictArea from '@/components/DictArea';
import Tip from '@/components/Tip';
import SearchOrg from '@/components/SearchOrg';
import Date from '@/components/Date';
import OrgSelect from '@/components/OrgSelect';
import ListTable from '@/components/ListTable';
import MemSelect from '@/components/MemSelect';
import ListEdit from '../../inflowManage/components/flowAddOrEditListEdit';
import {
  findApprovalOrg,
  inManageOperate,
  inManageFind,
  outManageFind,
  outManageReFlow,
  outManageSomeEdit,
  outManageDetailList,
  outManageDetailDel,
  orgFindBranchOrg,
  findApprovalFlowOrg,
  approve1
} from '../../service/index';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

// @ts-ignore
@connect(
  ({ unit, commonDict, loading, flowMem }) => ({
    flowMem,
    unit,
    commonDict,
    loading: loading.effects['unit/getList'],
  }),
  undefined,
  undefined,
  { forwardRef: true },
)
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      paginationHd: { pageSize: 10, current: 1, total: 0 },
      loadingHd: false,
      visibleHd: false,
      basicInfoHd: {},
    };
  }

  open = (type: string, record?: any, urlType?: string) => {
    console.log('modalType===', type, record, urlType);
    let title = '流动流出登记';

    this.setState(
      {
        readOnly: true,
        modalType: type,
        modalTitle: title,
        visible: true,
        urlType: urlType ? urlType : undefined,
        basicInfo: record
      },
      () => {
        // if (!_isEmpty(record['code'])) {
        //   this.getBasicInfo(record['code']);
        // }
      },
    );
  };
  // getBasicInfo = async (code: string) => {
  //   const { urlType } = this.state;
  //   let url: any = inManageFind;
  //   if (urlType === 'outFlow') {
  //     url = outManageFind;
  //   }
  //   const { code: rescode = 500, data = {} } = await url({
  //     code,
  //   });
  //   if (rescode == 0) {
  //     this.setState({
  //       basicInfo: data,
  //       showInUnitD16Code: `${data?.inOrgD04Code}`.startsWith('4') ? true : false,
  //     });
  //     if (
  //       `${data?.inMemD09Code}`.startsWith('02') ||
  //       `${data?.inMemD09Code}`.startsWith('03') ||
  //       `${data?.inMemD09Code}`.startsWith('505') ||
  //       `${data?.inMemD09Code}` == '504'
  //     ) {
  //       this.setState({
  //         showInMemD20Code: true,
  //       });
  //     } else {
  //       this.setState({
  //         showInMemD20Code: false,
  //       });
  //     }
  //     if (['edit-inTab2', 'edit-outTab2'].includes(this.state.modalType)) {
  //       this.getListHd({ memFlowCode: data.code, pageNum: 1 });
  //     }
  //   }
  // };
  handleOk = async () => {

    const { basicInfo, modalType } = this.state; // modalType说明：例如modalType:edit-outTab1→   列表"编辑"操作-流出管理下的tab1选项卡（未纳入流入地管理）
    const org: any = getSession('org');
    this.props.form.validateFieldsAndScroll(async (error, values) => {
      if (error) {
        return;
      }
      if (!_isEmpty(values['outTime'])) {
        values['outTime'] = moment(values['outTime']).valueOf();
      }
      const {
        mzAppraisal,
        inOrgLifeCode,
        inOrgLifeName,
        inOrgLife,
        inFeedback,
        flowReasonCode,
        outTime,
        inOrgD04Code,
        isHold,
        outOrgRemarks,
        partyExpensesOutTime,
        pairedContact,
        outAdministrativeDivisionCode,
        outPlaceCode,
      } = values;
      let someData = {};
      someData = {
        ...values,
        codeList: [basicInfo.code],
        code: basicInfo.code,
        outPlaceCode,
        flowReasonCode,
        outTime,
        inOrgD04Code: _isObject(inOrgD04Code) ? inOrgD04Code['key'] : inOrgD04Code,
        inOrgD04Name: _isObject(inOrgD04Code) ? inOrgD04Code['name'] : basicInfo['inOrgD04Name'],
        isHold,
        partyExpensesOutTime: moment(partyExpensesOutTime).valueOf() || undefined,
        pairedContact: pairedContact[0]?.name,
        pairedContactPhone: pairedContact[0]?.phone,
        pairedContactCode: pairedContact[0]?.code,
        outAdministrativeDivisionCode: outAdministrativeDivisionCode,
        isProvince: this.state.orgSearchType
      };
      if (!_isEmpty(values['outOrgBranchName']) && values['outPlaceCode'] == 1) {
        if (values['outOrgBranchName'].includes('code')) {
          const outOrgBranchName = JSON.parse(values['outOrgBranchName']);
          someData['outOrgBranchName'] = outOrgBranchName.name || undefined;
          someData['outOrgBranchOrgCode'] = outOrgBranchName.orgCode || undefined;
          someData['outOrgBranchCode'] = outOrgBranchName.code || undefined;
        }
      } else {
        if (!_isEmpty(values['outOrgCode'])) {
          someData['outOrgBranchOrgCode'] = values['outOrgCode'].orgCode || undefined;
        }
      }
      // 如果是5，流动党支部变为搜索框,是一个对象,传给后端一个名字
      if (!_isEmpty(values['outOrgBranchName']) && values['outPlaceCode'] == 5) {
        // console.log("values['outOrgBranchName']", values['outOrgBranchName']);
        someData['outOrgBranchName'] = values['outOrgBranchName'].name || undefined;
        someData['outOrgBranchOrgCode'] = values['outOrgBranchName'].orgCode || undefined;
        someData['outOrgBranchCode'] = values['outOrgBranchName'].code || undefined;
        someData['outOrgContact'] = values['outOrgBranchName'].contacter || undefined;
        someData['outOrgContactPhone'] = values['outOrgBranchName'].contactPhone || undefined;
      }
      if (!_isEmpty(values['outOrgCode'])) {
        someData['outOrgName'] = values['outOrgCode'].name || undefined;
        someData['outOrgContact'] = values['outOrgCode'].contacter || undefined;
        someData['outOrgContactPhone'] = values['outOrgCode'].contactPhone || undefined;
        someData['outOrgCode'] = values['outOrgCode'].code || undefined;
      }
      console.log(someData, basicInfo);
      this.setState(
        {
          confirmLoading: true,
        },
        async () => {
          const { code = 500, data } = await approve1({
            data: {

              memFlowSignAuditDto: {
                status: 1,
                signAuditId: basicInfo?.signAuditId
              },
              memFlowRegisterDTO: {
                ...someData,
              }
            },
          });
          this.setState({
            confirmLoading: false,
          });
          if (code == 0) {
            Tip.success('操作提示', "操作成功");
            this.handleCancel();

          }
        },
      );
    });
  };
  handleCancel = () => {
    const { onOk } = this.props;
    this.setState({
      visible: false,
      modalTitle: '',
      memData: [],
      basicInfo: {},
      outPlaceCode: '',
      flowTypeCode: undefined,
      orgSearchType: '', // 组织查询选择器返回的type
      readOnly: false,
      confirmLoading: false,
    });
    onOk && onOk();
  };
  memChange = (data) => {
    this.setState({
      memData: data,
    });
  };
  del = (item) => {
    const { getFieldValue, setFieldsValue } = this.props.form;
    let { memData } = this.state;
    // let memData =  getFieldValue('codeList');
    memData = memData.filter((obj) => obj['id'] !== item['id']);
    setFieldsValue({ codeList: memData });
    this.setState({
      memData,
    });
    let names: Array<string> = [];
    for (let obj of memData) {
      names.push(obj['name']);
    }
    this['mem'].setState({
      value: names.join('，'),
      data: memData,
    });
  };
  getTime = (rule, value, callback) => {
    if (!value) {
      callback('外出时间必填');
    }
    let now = moment().subtract(180, 'days').format('YYYY-MM-DD');
    let time = moment(value).format('YYYY-MM-DD');
    if (!moment(time).isBefore(now)) {
      callback('外出日期只能填写早于当前日期180天');
    } else {
      callback();
    }
    callback();
  };
  getListHd = async (p: any) => {
    this.setState({
      loadingHd: true,
    });
    const {
      code: rescode = 500,
      data: { list = [], pageNumber = 1, pageSize = 10, totalPage = 0 } = {},
    } = await outManageDetailList({
      data: {
        pageNum: this.state.paginationHd['current'],
        pageSize: this.state.paginationHd['pageSize'],
        memFlowCode: p.memFlowCode,
        ...p,
      },
    });
    this.setState({
      loadingHd: false,
    });
    if (rescode == 0) {
      this.setState({
        listHd: list,
        paginationHd: {
          ...this.state.paginationHd,
          pageSize: pageSize,
          current: pageNumber,
          total: totalPage,
        },
      });
    }
  };
  delHd = async (item) => {
    const { code = 500, data = {} } = await outManageDetailDel({
      data: {
        id: item['id'],
      },
    });
    if (code == 0) {
      Tip.success('操作提示', '删除成功');
      this.getListHd({ memFlowCode: this.state.basicInfo['code'] });
    }
  };

  // 流向基层党工委 当流动单位里面type为1的时候 流动党支部变为下拉框然后去请求数据
  getSelectOutOrgBranchName = async (outOrgCode, orgCode) => {
    console.log("🚀 ~ index ~ getSelectOutOrgBranchName= ~ orgCode:", getSession("org"))
    const org = getSession("org") || {}

    if (outOrgCode) {
      const { code, data } = await orgFindBranchOrg({
        data: {
          code: orgCode,
          pageNum: 1, //选择框写死的
          pageSize: 999, //选择框写死的
          excludeOrgCode: org?.orgCode
        },
      });
      if (code == 0) {
        this.setState({
          outOrgBranchNameList: data.list
        });
      }
    }
  };
  outOrgBranchNameSelect = () => {
    const { outOrgBranchNameList = [] } = this.state;
    // console.log('11111111111111', outOrgBranchNameList);
    return (
      <Select placeholder="请选择" >
        {outOrgBranchNameList.length > 0 &&
          outOrgBranchNameList.map((item: any, index) => {
            return <Select.Option value={JSON.stringify(item)}>{item.name}</Select.Option>;
          })}
      </Select>
    );
  };
  render() {
    const { children, tipMsg = {}, commonDict } = this.props;
    const {
      visible,
      modalTitle = '',
      modalType = '',
      basicInfo = {},
      outPlaceCode = '1',
      orgSearchType = '',
      flowTypeCode = '',
      readOnly = false,
      confirmLoading = false,
      showInUnitD16Code = false,
      showInMemD20Code = false,
      itemsDisabled = [],
      listHd = [],
      loadingHd = false,
      paginationHd = { pageSize: 10, current: 1, total: 0 },
      outAdministrativeDivisionCodeDisabled = false,  //行政区划是否可选
    } = this.state;
    console.log(basicInfo, 'basicInfobasicInfobasicInfobasicInfobasicInfo');
    const { getFieldDecorator, setFieldsValue, getFieldValue } = this.props.form;
    const org: object = getSession('org') || {};
    const noOperationColumns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return (paginationHd['current'] - 1) * paginationHd['pageSize'] + index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'name',
        align: 'center',
        width: 60,
      },
      {
        title: '性别',
        dataIndex: 'sexName',
        align: 'center',
        width: 40,
      },
      {
        title: '身份证号码',
        dataIndex: 'idcard',
        align: 'center',
        width: 100,
      },
      {
        title: '联系电话',
        dataIndex: 'phone',
        align: 'center',
        width: 100,
      },
      {
        title: '所在组织',
        dataIndex: 'orgName',
        width: 160,
      },
      {
        title: '工作岗位',
        dataIndex: 'd09Name',
        width: 80,
      },
    ];
    const columns = [
      ...noOperationColumns,
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return (
            <span>
              <a className={'del'} onClick={() => this.del(record)}>
                删除
              </a>
            </span>
          );
        },
      },
    ];

    // const Reflowout = modalType === 'edit-outTab3'

    return (
      <div>
        {children
          ? React.cloneElement(children as any, {
            onClick: this.open,
            key: 'container',
          })
          : null}
        <Modal
          footer={
            <Fragment>
              <Button
                onClick={() => {
                  this.handleCancel();
                }}
              >
                取消
              </Button>
              <Button
                loading={confirmLoading}
                onClick={() => {
                  this.handleOk();
                }}
                type="primary"
              >
                确定
              </Button>
            </Fragment>
          }
          bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
          destroyOnClose
          // maskClosable={false}
          width={1100}
          title={modalTitle}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
        >
          <Row>
            <h3>党员信息</h3>
            <ListTable
              scroll={{ x: '100%' }}
              rowKey={'code'}
              columns={readOnly ? noOperationColumns : columns}
              data={[basicInfo] || []}
              pagination={false}
            />
          </Row>
          <Row>
            <h3>流动信息</h3>
            <Col span={24}>
              <Form {...formItemLayout}>
                {/* <FormItem label={formLabel('选择党员', tipMsg['codeList'])}>
                {getFieldDecorator('codeList', {
                  // initialValue: '',
                  rules: [{ required: true, message: '请选择党员' }],
                })(
                  <MemSelect
                    org={getSession('org')}
                    // initValue={memInfo['memName']}
                    onChange={this.memChange}
                    ref={(e) => (this['mem'] = e)}
                    checkType={'checkbox'}
                  />,
                )}
              </FormItem> */}
                {/* <FormItem label={formLabel('党员信息', tipMsg['onlyShow'])}>
                  {getFieldDecorator('onlyShow', {
                    rules: [{ required: false, message: '请选择党员' }],
                  })(
                    <ListTable
                      scroll={{ x: '100%' }}
                      rowKey={'code'}
                      columns={readOnly ? noOperationColumns : columns}
                      data={[basicInfo] || []}
                      pagination={false}
                    />,
                  )}
                </FormItem> */}
                {/* {modalType !== 'readOnly-outTab1' &&
                  modalType !== 'readOnly-inTab1' &&
                  basicInfo?.flowUqCode && (
                    <FormItem label={formLabel('流动党员唯一码', tipMsg['flowUqCode'])}>
                      {getFieldDecorator('flowUqCode', {
                        initialValue: _isEmpty(basicInfo.flowUqCode) ? undefined : basicInfo.flowUqCode,
                        rules: [{ required: false, message: '流动党员唯一码' }],
                      })(<Input disabled />)}
                    </FormItem>
                  )} */}
                <FormItem label={formLabel('外出地点', tipMsg['outPlaceCode'])}>
                  {getFieldDecorator('outPlaceCode', {
                    // initialValue: Reflowout ? undefined : _isEmpty(basicInfo) ? undefined : basicInfo.outPlaceCode,
                    initialValue: "1",
                    rules: [{ required: true, message: '请选择外出地点' }],
                  })(
                    <DictSelect
                      // initValue={Reflowout ? undefined : _isEmpty(basicInfo) ? undefined : basicInfo.outPlaceCode}
                      initValue={"1"}
                      // disabled={Reflowout ? false : readOnly}
                      disabled={true}
                      codeType={'dict_d148'}
                      onChange={(e) => {
                        let arr = [];
                        if (e == '2') {
                          arr = commonDict['dict_d151_flow']
                            .filter((i) => i.key.startsWith('520') || i.key.startsWith('522'))
                            .map((j) => j.key);
                        }
                        setFieldsValue({ flowTypeCode: undefined });
                        setFieldsValue({ outAdministrativeDivisionCode: '' });
                        this.setState({
                          outPlaceCode: e,
                          flowTypeCode: undefined,
                          itemsDisabled: arr,
                          timeKey: String(Math.random()),
                          administrativeRegion: '',
                        });
                        setFieldsValue({
                          outAdministrativeDivisionCode: undefined,
                          outOrgBranchName: undefined,
                          outOrgCode: undefined
                        });
                      }}
                      itemsDisabled={['3', '4']}
                    />,
                  )}
                </FormItem>
                {(getFieldValue("outPlaceCode") == 1) && (
                  <FormItem label={formLabel('流入党委', tipMsg['outOrgCode'])} colon={false}>
                    {getFieldDecorator('outOrgCode', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.outOrgName,
                      rules: [{ required: true, message: '请选择' }],
                    })
                      (
                        // <Input disabled={!(Reflowout)} />
                        <SearchOrg
                          // disabled={readOnly}
                          onChange={(e) => {
                            // console.log('组织查询选择==', e);
                            const { type = '', insideCity = '', orgCode = '', administrativeRegion = '' } = e;
                            // type 0 省外， 1 省内 ；insideCity  0 市外， 1 市内； administrativeRegion 行政区划编码
                            this.setState({
                              orgSearchType: type,
                              orgSearchCode: orgCode,
                              administrativeRegion,
                              timeKey: String(Math.random()),
                            });
                            if (e.type == 1) {
                              this.getSelectOutOrgBranchName(e.type, e.code);
                              this.setState({ outAdministrativeDivisionCodeDisabled: true })
                            } else {
                              this.setState({ outAdministrativeDivisionCodeDisabled: false })
                            }
                            // setFieldsValue({ outOrgBranchCode: undefined });
                            setFieldsValue({ outOrgBranchName: undefined });
                            setFieldsValue({ outAdministrativeDivisionCode: e?.divisionCode == '' ? undefined : e?.divisionCode });
                          }}
                          // ohterAction={findApprovalFlowOrg}
                          ohterAction={findApprovalOrg}
                          showOtherInfo={'transfer'}
                          backType={'object'}
                          params={{
                            orgCode: org['orgCode'],
                            d01Code: 811, //写死的流动党委key
                          }}
                        />
                      )}
                  </FormItem>
                )}
                {/* {(basicInfo.outPlaceCode == 1 || basicInfo.outPlaceCode == 2 || basicInfo.outPlaceCode == 5) && (
                  <Fragment>
                    <FormItem label={formLabel('流入党支部', tipMsg['outOrgBranchName'])}>
                      {getFieldDecorator('outOrgBranchName', {
                        initialValue: Reflowout ? undefined : _isEmpty(basicInfo) ? undefined : basicInfo['outOrgBranchName'],
                        rules: [{ required: true, message: '请选择党支部' }],
                      })(<Input disabled={!(Reflowout)} placeholder="请输入" />)}
                    </FormItem>
                  </Fragment>
                )}
                {basicInfo.outPlaceCode != 3 && basicInfo.outPlaceCode != 4 && (
                  <FormItem label={formLabel('流入行政区', tipMsg['outAdministrativeDivisionCode'])}>
                    {getFieldDecorator('outAdministrativeDivisionCode', {
                      initialValue: Reflowout ? undefined : _isEmpty(basicInfo)
                        ? undefined
                        : basicInfo['outAdministrativeDivisionCode'],
                      rules: [{ required: !readOnly, message: '请选择所在行政区域' }],
                    })(
                      <DictTreeSelect
                        parentDisable={true}
                        placeholder={'请选择行政区域'}
                        initValue={
                          Reflowout ? undefined : _isEmpty(basicInfo) ? undefined : basicInfo.outAdministrativeDivisionCode
                        }
                        disabled={!(Reflowout)}
                        codeType={'dict_d151_flow'}
                        itemsDisabled={itemsDisabled}
                      />,
                    )}
                  </FormItem>
                )} */}
                {getFieldValue("outPlaceCode") == '1' &&
                  (() => {
                    const outOrgCodeType = getFieldValue('outOrgCode')?.type == 1;
                    return (
                      <FormItem label={formLabel('流入党支部', tipMsg['outOrgBranchName'])}>
                        {getFieldDecorator('outOrgBranchName', {
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['outOrgBranchName'],
                          rules: [{ required: outOrgCodeType ? false : true, message: '请输入' }],
                        })(outOrgCodeType ? this.outOrgBranchNameSelect() : <Input placeholder="请输入" />)}
                      </FormItem>
                    );
                  })()}
                {getFieldValue("outPlaceCode") == '5' && (
                  <FormItem label={formLabel('流入党支部', tipMsg['outOrgBranchName'])}>
                    {getFieldDecorator('outOrgBranchName', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['outOrgBranchName'],
                      rules: [{ required: true, message: '请输入' }],
                    })(
                      <Input placeholder="请输入" />
                    )}
                  </FormItem>
                )}
                {getFieldValue("outPlaceCode") != 3 &&
                  getFieldValue("outPlaceCode") != 4 &&
                  (() => {
                    const outOrgCode = getFieldValue('outOrgCode');
                    const initValue = outOrgCode ? outOrgCode?.divisionCode : _isEmpty(basicInfo) ? undefined : basicInfo.outAdministrativeDivisionCode;
                    return (
                      <FormItem label={formLabel('流入行政区', tipMsg['outAdministrativeDivisionCode'])}>
                        {getFieldDecorator('outAdministrativeDivisionCode', {
                          initialValue: (outOrgCode == undefined) ? undefined : initValue,
                          rules: [
                            // 外出地点选2，行政区划必填
                            {
                              required: (outPlaceCode === '2' || !outAdministrativeDivisionCodeDisabled) ? true : false,
                              message: '请选择所在行政区域',
                            },
                          ],
                        })(
                          <DictTreeSelect
                            parentDisable={true}
                            placeholder={'请选择行政区域'}
                            initValue={(outOrgCode == undefined) ? undefined : (initValue == "" ? undefined : initValue)}
                            // disabled={outPlaceCode === '1' ? true : false}
                            disabled={orgSearchType == 1 ? true : false}
                            codeType={'dict_d151_flow'}
                            itemsDisabled={itemsDisabled}
                          />,
                        )}
                      </FormItem>
                    );
                  })()}

                {/* outPlaceCode等于2的时候需要和流入行政区交换位置所以这单独判断 */}
                {getFieldValue("outPlaceCode") == '2' && (
                  <FormItem label={formLabel('流入党支部', tipMsg['outOrgBranchName'])}>
                    {getFieldDecorator('outOrgBranchName', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['outOrgBranchName'],
                      rules: [{ required: true, message: '请输入' }],
                    })(<Input placeholder="请输入" />)}
                  </FormItem>
                )}
                {/* 外出地点-不掌握流向 */}
                {basicInfo.outPlaceCode == 4 && (
                  <Fragment>
                    <FormItem label={formLabel('失去联系情形', tipMsg['lostContactCode'])}>
                      {getFieldDecorator('lostContactCode', {
                        initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['lostContactCode'],
                        rules: [{ required: true, message: '请选择' }],
                      })(
                        <DictSelect
                          codeType={'dict_d18'}
                          backType={'object'}
                          initValue={_isEmpty(basicInfo) ? undefined : basicInfo['lostContactCode']}
                        />,
                      )}
                    </FormItem>
                  </Fragment>
                )}
                {getFieldValue("outPlaceCode") == 4 &&
                  (function () {
                    const lostContactCode = getFieldValue('lostContactCode');
                    return (
                      lostContactCode && (
                        <Fragment>
                          <FormItem label={formLabel('失去联系日期', tipMsg['lostContactTime'])}>
                            {getFieldDecorator('lostContactTime', {
                              // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                              rules: [{ required: true, message: '请选择' }],
                            })(<Date />)}
                          </FormItem>
                        </Fragment>
                      )
                    );
                  })()}
                {/* 外出地点-无固定地点 */}
                {(basicInfo?.outPlaceCode == 3 || basicInfo?.outPlaceCode == 4) && (
                  <FormItem label={formLabel('情况说明', tipMsg['outOrgRemarks'])}>
                    {getFieldDecorator('outOrgRemarks', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['outOrgRemarks'],
                      rules: [{ required: true, message: '请填写' }],
                    })(
                      <Input.TextArea
                        placeholder="请对情况进行详细说明"
                        showCount
                        maxLength={100}
                        rows={4}
                      />,
                    )}
                  </FormItem>
                )}
                {basicInfo.outPlaceCode != 5 && <FormItem label={formLabel('外出原因', tipMsg['flowReasonCode'])}>
                  {getFieldDecorator('flowReasonCode', {
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d146Code'],
                    rules: [
                      {
                        required: true,
                        message: '请选择流动原因',
                      },
                    ],
                  })(
                    <DictSelect
                      codeType={'dict_d204'}
                      // backType={'object'}
                      initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d146Code']}
                    />,
                  )}
                </FormItem>}
                <FormItem label={formLabel('外出日期', tipMsg['outTime'])}>
                  {getFieldDecorator('outTime', {
                    initialValue: _isEmpty(basicInfo) ? undefined : moment(basicInfo['outDate']),
                    rules: [
                      { required: false, message: '请填写外出日期' },],
                  })(
                    <Date />,
                  )}
                </FormItem>
                <FormItem label={formLabel('登记日期', tipMsg['registerTime'])}>
                  {getFieldDecorator('registerTime', {
                    initialValue: _isEmpty(basicInfo) ? undefined : moment(basicInfo['registerTime']),
                    rules: [
                      { required: true, message: '请填写登记日期' },],
                  })(
                    <Date disabled={false} endTime={moment()} startTime={moment().subtract(60, 'days')}
                      tipMessage="登记日期只能填写当前时间往前60天以内的时间！" />,
                  )}
                </FormItem>
                {getFieldValue("outPlaceCode") == 1 && (
                  <FormItem label={formLabel('外出地点(党组织)补充说明', tipMsg['outOrgRemarks'])}>
                    {getFieldDecorator('outOrgRemarks', {
                      initialValue: _isEmpty(basicInfo) ? null : basicInfo.outInstructions,
                      rules: [{ required: true, message: '请填写' }],
                    })(<Input.TextArea placeholder="请对情况进行详细说明" showCount maxLength={100} rows={4} />)}
                  </FormItem>
                )}
                {(getFieldValue("outPlaceCode") == 1 || getFieldValue("outPlaceCode") == 2 || getFieldValue("outPlaceCode") == 5) && (
                  <FormItem label={formLabel('流入地党组织单位类型', tipMsg['inOrgD04Code'])}>
                    {getFieldDecorator('inOrgD04Code', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['inOrgD04Code'],
                      rules: [
                        {
                          required: true,
                          message: '流入地党组织单位类型',
                        },
                      ],
                    })(
                      <DictTreeSelect
                        onChange={(e) => {
                          const { key = '' } = e;
                          if (key.startsWith('4')) {
                            this.setState({
                              showInUnitD16Code: true,
                            });
                          } else {
                            this.setState({
                              showInUnitD16Code: false,
                            });
                          }
                        }}
                        parentDisable={true}
                        codeType={'dict_d205'}
                        backType={'object'}
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['inOrgD04Code']}
                      />,
                    )}
                  </FormItem>
                )}
                {/* 流入地党组织单位类型选择企业4开头的字典表时,添加经济类型 */}
                {/* {showInUnitD16Code && (
                  <FormItem label={formLabel('经济类型', tipMsg['inUnitD16Code'])}>
                    {getFieldDecorator('inUnitD16Code', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['inUnitD16Code'],
                      rules: [{ required: !readOnly, message: '请选择经济类型' }],
                    })(
                      <DictTreeSelect
                        disabled={readOnly}
                        parentDisable={true}
                        codeType={'dict_d16'}
                        // backType={'object'}
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['inUnitD16Code']}
                      />,
                    )}
                  </FormItem>
                )} */}
                {/* // 工作岗位以02、03、505开头的，等于504的 显示新社会阶层 */}
                {/* {showInMemD20Code && (
                  <FormItem label="新社会阶层">
                    {getFieldDecorator('inMemD20Code', {
                      initialValue: Reflowout ? undefined : _isEmpty(basicInfo) ? undefined : basicInfo['inMemD20Code'],
                      rules: [{ required: false, message: '请选择新社会阶层' }],
                    })(
                      <DictTreeSelect
                        disabled={true}
                        placeholder="请选择新社会阶层"
                        parentDisable={true}
                        codeType={'dict_d20'}
                        // backType={'object'}
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['inMemD20Code']}
                      />,
                    )}
                  </FormItem>
                )} */}
                <FormItem label={formLabel('流出地党费交至日期', tipMsg['partyExpensesOutTime'])}>
                  {getFieldDecorator('partyExpensesOutTime', {
                    initialValue: _isEmpty(basicInfo)
                      ? undefined
                      : moment(basicInfo['partyExpensesOutTime']),
                    rules: [{ required: true, message: '流出地党费交至日期' }],
                  })(<Date />)}
                </FormItem>
                <FormItem label={formLabel('流动党员活动证', tipMsg['isHold'])}>
                  {getFieldDecorator('isHold', {
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['isHold'],
                    rules: [
                      {
                        required: false,
                        message: '请选择',
                      },
                    ],
                  })(
                    <Select

                      placeholder="请选择"
                      style={{ width: '100%' }}
                    >
                      <Select.Option value={'1'}>已发放</Select.Option>
                      <Select.Option value={'0'}>未发放</Select.Option>
                    </Select>,
                  )}
                </FormItem>
                <FormItem label={formLabel('流出地党支部', tipMsg['memOrgName'])}>
                  {getFieldDecorator('memOrgName', {
                    initialValue: _isEmpty(basicInfo) ? org['name'] : basicInfo.memOrgName,
                    rules: [{ required: false, message: '请输入流出地党支部' }],
                  })(<Input placeholder="请输入" disabled={true} />)}
                </FormItem>
                <FormItem label={formLabel('流出地党支部联系电话', tipMsg['memOrgPhone'])}>
                  {getFieldDecorator('memOrgPhone', {
                    initialValue: _isEmpty(basicInfo) ? org['contactPhone'] : basicInfo.memOrgPhone,
                    rules: [{ required: false, message: '请输入流出地党支部联系电话' }],
                  })(<Input placeholder="请输入" disabled={true} />)}
                </FormItem>

                <FormItem label={formLabel('结对联系人', tipMsg['pairedContact'])}>
                  {getFieldDecorator('pairedContact', {
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.pairedContact,
                    rules: [{ required: true, message: '请输入结对联系人' }],
                  })(
                    <MemSelect
                      org={getSession('org')}
                      // initValue={memInfo['memName']}
                      onChange={(e: any) => {
                        setFieldsValue({
                          pairedContactPhone: e[0]?.phone,
                        });
                      }}
                      ref={(e) => (this['pairedContact'] = e)}
                      checkType={'radio'}
                    />
                  )}
                </FormItem>
                <FormItem label={formLabel('结对联系方式', tipMsg['pairedContactPhone'])}>
                  {getFieldDecorator('pairedContactPhone', {
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.pairedContactPhone,
                    // initialValue: getFieldValue('pairedContact')
                    //   ? getFieldValue('pairedContact')[0]?.phone
                    //   : undefined,
                    rules: [{ required: true, message: '请输入结对联系方式' }],
                  })(
                    <Input
                      placeholder="请输入"
                      // disabled={[
                      //   'readOnly-inTab1',
                      //   'readOnly-inTab2',
                      //   'readOnly-inTab3',
                      //   'readOnly-inTab4',
                      //   'readOnly-outTab1',
                      //   'readOnly-outTab2',
                      //   'readOnly-outTab3',
                      //   'readOnly-outTab4',
                      //   'edit-inTab2',
                      //   'edit-outTab2',
                      // ].includes(modalType)}
                      disabled={true}
                    />,
                  )}
                </FormItem>
              </Form>
            </Col>
          </Row>
          {/* 活动信息列表 */}
        </Modal>
        <ListEdit
          wrappedComponentRef={(e) => (this['listEditRef'] = e)}
          onOk={() => {
            this.getListHd({ memFlowCode: basicInfo.code, pageNum: 1 });
          }}
          memInfo={basicInfo}
          modalType={modalType}
        />
      </div>
    );
  }
}

export default Form.create()(index);
