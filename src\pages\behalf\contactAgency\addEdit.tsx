/**
 * 联络机构新增和编辑
 */
import React from 'react'
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, Modal, Row, Input, Radio, DatePicker, Switch, Alert } from 'antd';
import qs from 'qs';
import {connect} from "dva";
import {_history as router} from "@/utils/method";
import { isEmpty } from '@/utils/method';
import { getSession } from '@/utils/session';
import WhiteSpace from '@/components/WhiteSpace';
import OrgSelect from '@/components/OrgSelect'
import styles from './index.less'
import DictSelect from '@/components/DictSelect';
import Notice from '@/components/Notice';
import DictTreeSelect from '@/pages/flowMem/inflows/outsideCity';
const FormItem=Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const menuData=[
  {
    code:'1',
    name:'基本信息',
    icon:'star',
  },
  {
    code:'2',
    name:'',
    icon:'qrcode',
  },
];
@connect(({behalf,unit,commonDict,loading})=>({behalf,unit,commonDict,loading:loading.effects['unit/getList']}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    let obj=menuData[0];
    this.state={
      visible:false,
      key:obj['code'],
      keyPath:[obj['code']],
      selected:obj,
    };
  }

  showModal=()=>{
    const { data:{ id='' }={},keyword=''} = this.props;
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
      orgCode:org['orgCode']
    });
  };

  handleOk=()=>{
    const { onChange,title,data }=this.props;
    this.props.form.validateFieldsAndScroll((err,val)=>{
      if (err){
        return
      }
      const { d62,org,...value }=val;
      if (title === '编辑联络机构') {
       this.props.dispatch({
         type:'behalf/contactAgencyUp',
         payload:{
           data: {
             ...value,
             code:data['code'],
             orgCode:org[0]['code']||data['orgCode'],
             contactOrgCode:org[0]['orgCode']||data['contactOrgCode'],
             orgName:org[0]['name']||data['orgName'],
             d62Code:d62['id']||data['d62Code'],
             d62Name:d62['name']||data['d62Name']
           }
         }
       }).then(res=>{
         if (res['code']===0){
           Notice("操作提示",'保存成功!',"check-circle","green");
           this.handleCancel();
           onChange&&onChange(true)
         } else {
           Notice("操作提示",res['message'],"exclamation-circle-o","orange");
         }
         // this.handleCancel();
       })
      }else {
        this.props.dispatch({
          type:'behalf/contactAgencyAdd',
          payload:{
            data:{
              ...value,
              orgCode:org[0]['code']||'',
              contactOrgCode:org[0]['orgCode']||'',
              orgName:org[0]['name']||'',
              d62Code:d62['id']||'',
              d62Name:d62['name']||''
            }
          }
        }).then(res=>{
          if (res['code']===0){
            Notice("操作提示",'保存成功!',"check-circle","green");
            this.handleCancel();
            onChange&&onChange(true)
          } else {
            Notice("操作提示",res['message'],"exclamation-circle-o","orange");
          }
          // this.handleCancel();
        })
      }
    });
  };
  handleCancel=()=>{
    this.setState({
      visible:false
    })
  };
  open=()=>{
    this.setState({
      visible:true,
    })
  };

  onSelect=(item)=>{
    const {key,keyPath}=item;
    const selected=menuData.find(obj=>obj['code']===key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  render() {
    const {visible}=this.state;
    const {basicInfo={}}=this.props.unit;
    const { children ,title,data={}}=this.props;
    const { getFieldDecorator  } = this.props.form;

    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          title={title||''}
          className='out_Modal'
          destroyOnClose
          closable={false}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1000}
        >
          <div className='container'>
            <Alert
              message="温馨提示"
              description={
                <div>
                  <p>1.党组织类型4种 ：“11 | 中共中央委员会”、“123 | 中共中央直辖市委员会”、“134 | 中共中央直辖市属区委员会”、“145 | 中共中央直辖市属县委员会”。</p>
                  <p>2.党组织关联单位类型2种：“9121 | 乡”、“9122 | 镇”。</p>
                  <p>3.所选单位必须符合以上 <span style={{color:'red'}}>六种类型</span>（党组织类型+党组织关联单位类型）中的其中一种，方可创建党代表届次</p>
                </div>
              }
              type="info"
              showIcon
            />
            <WhiteSpace/>
            <WhiteSpace/>
            <Form {...formItemLayout}>
              <FormItem
                label={'机构名称'}
              >
                {getFieldDecorator('contactName', {
                  initialValue:isEmpty(data)?null:data.contactName,
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <Input/>
                )}
              </FormItem>
              <FormItem
                label={'所属党组织名称'}
              >
                {getFieldDecorator('org', {
                  initialValue:isEmpty(data)?null:data.orgCode,
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <OrgSelect initValue={data['orgName'] ? data['orgName'] : data['org'] ? data['org']['name'] : undefined}/>
                )}
              </FormItem>
              <FormItem
                label={'机构级别'}
              >
                {getFieldDecorator('d62', {
                  initialValue:isEmpty(data)?null:data['d62Code'],
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <DictSelect codeType={'dict_d62'} backType={'object'} initValue={data['d62Code'] || undefined}/>
                )}
              </FormItem>
              <FormItem
                label={'编制数'}
              >
                {getFieldDecorator('bzNum', {
                  initialValue:isEmpty(data)?null:data.bzNum,
                  rules: [
                    { required: true, message: '必填!' },
                    // { validator: this.validFunction }
                  ],
                })(
                  <Input placeholder={'请输入'}/>
                )}
              </FormItem>
            </Form>
          </div>
        </Modal>
      </React.Fragment>

    )
  }
}
export default Form.create()(index);
