.dropdownClass {
  :global {
    .more-options-button {
      // position: absolute;
      // bottom: 0;
      // left: 0;
      // right: 0;
      text-align: center;
      padding: 4px 0;
      background: #fff;
      border-top: 1px solid #f0f0f0;
      color: #1890ff;
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;

      &:hover {
        background: #f5f5f5;
      }

      .arrow-icon {
        margin-left: 4px;
        font-size: 12px;
      }
    }
  }
}

// 添加全局样式，确保在body中的传送门元素上也能应用样式
:global {
  .dict-select-dropdown {
    .more-options-button {
      // position: absolute;
      // bottom: 0;
      // left: 0;
      // right: 0;
      text-align: center;
      padding: 4px 0;
      background: #fff;
      border-top: 1px solid #f0f0f0;
      color: #1890ff;
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;

      &:hover {
        background: #f5f5f5;
      }

      .arrow-icon {
        margin-left: 4px;
        font-size: 12px;
      }
    }
  }
}
