/**
 * 党费下拨
 * */
import React from 'react';
import ListTable from '@/components/ListTable';
import ListFilter from '@/components/ListFilter';
import NowOrg from '@/components/NowOrg';
import { Button, Input, Tag, Tabs, Select, DatePicker, Modal, Divider, Badge, Popconfirm, Progress } from 'antd';
import styles from "./index.less";
import { connect } from 'dva';
import moment from 'moment';
import RuiFilter from 'src/components/RuiFilter';
import AddEdit from './addEdit';
import Bill from './bill';
import Set from './set';
import Check from './check'
import { isEmpty, setListHeight } from '@/utils/method';
import {getSession} from "@/utils/session";
import Notice from '@/components/Notice';
const TabPane = Tabs.TabPane;
const Search=Input.Search;
const Option = Select.Option;
const { MonthPicker, } = DatePicker;
@connect(({dues,login,commonDict})=>({
  dues,
  login,
  commonDict
}))
export default class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      memName:'',
      orgCode:'',
      type:'add',
      visible:false,
      menuTreeData:[],
      date:moment().valueOf(),
      tab:'1',
      years:moment().format('YYYY'),
      progress:0,
    }
  }
  //JSON.parse(sessionStorage.getItem('org') || "")['code']||
  componentDidMount = () => {
    let org=getSession('org')|| {};
    this.setState({
      orgCode:org['orgCode'],
      code:org['code'],
      isLeaf:org['isLeaf']
    },()=>{
      if (org['isLeaf'] !== 1) {
        this.onPage();
      }else {
        this.isLeaf()
      }
    });
    setListHeight(this);
  };
  isLeaf=()=>{
    this.props.dispatch({
      type:'dues/isLeaf',
      payload:{}
    })
  }
  componentWillReceiveProps =(nextProps: Readonly<any>, nextContext: any) => {
    if (!isEmpty(this.state.orgCode)&&this.state.orgCode!==JSON.parse(sessionStorage.org)['orgCode']) {
      this.setState({
        orgCode:JSON.parse(sessionStorage.org)['orgCode'],
        code:JSON.parse(sessionStorage.org)['code'],
      },()=>{
        if (JSON.parse(sessionStorage.org)['isLeaf'] !== 1) {
          this.onPage();
        }else {
          this.isLeaf()
        }
      })
    }
  };
  onPage = ( pageNum=1,size=10) => {
    let val = {
      allocateOrgCode:this.state['orgCode'],
      pageNum:pageNum,
      pageSize:size,
      allocateTime:this.state['date'],
      orgName:this.state['memName'],
    };
    for (let obj in val) {
      if (isEmpty(val[obj])) {
        delete val[obj]
      }
    }
    this.props.dispatch({
      type:'dues/getListxb',
      payload:{
        data:{
          ...val
        }
      }
    })
  };
  // onChange = (page) => {
  //   this.onPage(page);
  // };

  isSearch = (value) => {
    this.setState({memName:value},()=>{
      this.onPage()
    })
  };

  changePage=(v,k)=>{
    this.onPage(v,k);
    this.setState({page:v,pageNum:k})
  };
  changeList=()=>{

    this.onPage();
  };

  export=()=>{
    this.setState({dataInfo:{},type:'add'},()=>{
      this['AddEdit'].showModal();
    });
  };

  changeInfo=(e)=>{
    this.setState({tab:e},()=>{
    })
  };

  filterChange=(val)=>{
    this.setState({
      d68CodeList:val['d68CodeList'],
    },()=>{
      this.onPage()
    });
  };
  changeDate=(v)=>{
    if (!isEmpty(v)) {
      this.setState({
        date:moment(v).valueOf()
      },()=>{
        this.onPage();
      })
    }
  };
  disabledTomorrow=(current) =>{
    // Can not select days before today and today
    return current && current < moment('2019')||current>moment().endOf('day');
  };

  edit=(record)=>{
    this.setState({
      dataInfo:record,
      type:'edit'
    },()=>{
      this['Set'].showModal();
    })
  };
  check=(record)=>{
    // this['Check'].showModal();
    clearInterval(this['timer']);
    this.setState({
      progress:0
    },()=>{
      let { progress }=this.state;
      this['timer']=setInterval(() => {
        progress += 10;
        if(progress > 99){
          progress = 100;
          clearInterval(this['timer']);
          // this.onPage();
        }
        this.setState({progress})
      }, 200);
    });
  };
  confirm = (record) =>{
    clearInterval(this['timer']);
    this.setState({
      progress:0
    });
  };

  bill=()=>{
    this['Bill'].showModal();
  };

  render(): React.ReactNode {
    const { dues:{ list=[],pagination:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={} }={},login:{userRole=[]}, loading:{effects = {}} = {},commonDict} =this.props;
    const { type,id,menuTreeData,dataInfo={} ,filterHeight,org={},tab}=this.state;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },

      {
        title:'组织名称',
        dataIndex:'orgName',
        width:200,
      },
      {
        title:'正式党员',
        dataIndex:'fullMemNum',
        width:100,

      },
      {
        title:'预备党员',
        dataIndex:'proMemNum',
        width:100,
      },
      {
        title:'离退休党员',
        dataIndex:'retirementMemNum',
        width:100,
      },
      {
        title:'下拨比例',
        dataIndex:'allocateRatio',
        width:100,
        render:text => {
          return <span>{isEmpty(text)?'暂无':text*100+'%'}</span>
        }
      },
      {
        title:'下拨金额',
        dataIndex:'allocateMoney',
        width:100,
        render:(text,record) => {
          return (
            <div>
              <span >{isEmpty(text)?'暂无':'￥'+text}</span>
              {
                !isEmpty(text)&&
                <span style={{ backgroundColor: '#52c41a',fontSize:'12px',padding:'2px',marginLeft:'10px',color:'#fff' }} >{record['allocateType']=='1'?'自动计算':'手动输入'}</span>
              }
            </div>

          )
        }
      },
      {
        title:'是否已拨',
        dataIndex:'isAllocate',
        width:100,
        render:text => {
          return <span>{text=='1'?'是':'否'}</span>
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        width:100,
        render:(text,record)=>{
          return(
            <React.Fragment>
              <a href={'#'} onClick={()=>this.edit(record)}>设置</a>
              <Divider type="vertical"/>
              <Popconfirm
                placement="topLeft"
                title={ <Progress  style={{width:200}} percent={this.state['progress']} />}
                icon={false}
                onConfirm={()=>this.confirm}
                // footer={false}
                okText="是"
                cancelText='否'
              >
                <a href={'#'} onClick={()=>this.check(record)}>检查</a>
              </Popconfirm>
            </React.Fragment>
          )
        },
      },
    ];
    // const filterData=[
    //   {
    //     key:'d68CodeList',name:'收支项目',value:commonDict[`dict_d68_tree`],
    //   },
    // ];
    return(
      <div className={styles.container}>
        <Tabs defaultActiveKey={tab} onChange={this.changeInfo}>
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        <NowOrg  extra={
          <ListFilter>
            <Search
              placeholder="请输入检索关键词"
              onSearch={value => this.isSearch(value)}
              style={{ width: 200 }}
              className={styles.filter}
            />
            <MonthPicker
              disabledDate={this.disabledTomorrow}
              placeholder="请选择日期"
              style={{marginLeft:20}}
              defaultValue={moment()}
              format={'YYYY/MM'}
              onChange={this.changeDate}/>
            <Button
              style={{marginLeft:20}}
              type="primary"
              onClick={this.bill}
            >流水下拨</Button>
          </ListFilter>
        }/>
        {/*<RuiFilter data={filterData} onChange={this.filterChange}/>*/}
        <AddEdit wrappedComponentRef={(e)=>this['AddEdit']=e} data={dataInfo} type={type} title={type=='edit'?'编辑':'新增'} onChange={this.changeList}/>
        <Set wrappedComponentRef={(e)=>this['Set']=e} data={dataInfo} onChange={this.changeList}/>
        <Bill wrappedComponentRef={(e)=>this['Bill']=e}  onChange={this.changeList}/>
        <Check wrappedComponentRef={(e)=>this['Check']=e}  onChange={this.changeList}/>
        {/*<ExportFile wrappedComponentRef={(e)=>this['ExportFile']=e} onChange={this.changeList}/>*/}
        <ListTable
          columns={columns}
          data={list}
          scroll={{y:filterHeight}}
          pagination={{pageSize,total:totalRow,page,current:pageNumber}}
          onPageChange={this.changePage}/>
      </div>
    );
  }
}
