import React, { useEffect } from 'react';
import Search from '../commonSearch';
import { connect } from 'dva';
const CryptoJS = require('crypto-js');

const encryptedData = CryptoJS.AES.encrypt(
  'qaz@123456',
  CryptoJS.enc.Utf8.parse('AESNBHB3ZA==HKXt'),
  { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 },
);
const password = encryptedData.toString();
// 入党感言
const index = (props:any) => {
  useEffect(() => {
    sessionStorage.removeItem('dataApi');
    if(true){
      let account = 'qztest001';
      props.dispatch({
        type: 'login/qzLogin',
        payload: {
          data:{
            password,
            account,
            captchaCode:'U32xS4DACRRP0XtJPhOjlQ=='
          }
        }
      }).then(({code}:any)=>{})
    }

  }, []);
  return (
    <React.Fragment>
      <Search
        titImg={require('../../../../assets/qzs/xxk.png')}
        infoKey={'bwcx_info'}
        goToPath={'/qzs/screen/bwcx/memSearch'}
        goToPath2={'/qzs/screen/bwcx/memShow'}
      ></Search>
    </React.Fragment>
  );
};

export default connect(({ login }) => ({ login }))(index);
