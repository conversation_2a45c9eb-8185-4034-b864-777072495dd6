.photoBox {
  width: 100%;
  height: 500px;
  border: 1px solid #ccc;
  margin-top: 10px;
  overflow: auto;
  // display: flex;
  // justify-content: center;
  // align-items: center;
  // cursor: pointer;
}

.trans {
  width: 140px;
  text-align: center;
}
.contrast {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // > div {
  //   width: 50%;
  //   border: 1px solid red;
  // }
}
.contrast2 {
  
}
.tit {
  font-weight: bolder;
}

.step_1 {
  .contrast;
  height: 554px;
  .leftBox {
    display: flex;
    flex-direction: column;
    width: 48%;
    height: 100%;
    overflow: auto;
    .photoBox {
      flex: 1;
      width: 100%;
      height: 100%;
      border: 1px solid #ccc;
      margin-top: 10px;
      overflow: auto;
    }
  }
  .rightBox {
    width: 48%;
    height: 100%;
    .one {
      margin-top: 10px;
      border: 1px solid #ccc;
      height: 200px;
    }
    .ph {
      height: 300px;
      margin-top: 20px;
      display: flex;
      flex-direction: column;
      .phLabel {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .phBox {
        margin-top: 10px;
        flex: 1;
        overflow: auto;
        border: 1px solid #ccc;
      }
    }
  }
}
