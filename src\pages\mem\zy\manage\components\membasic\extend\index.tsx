import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, Input, DatePicker, Button, Upload, message, Modal, Radio } from 'antd';
import DictSelect from '@/components/DictSelect';
import moment from 'moment';
import _isNumber from 'lodash/isNumber';
import _isEmpty from 'lodash/isEmpty';
import { findDictCodeName, unixMoment } from '@/utils/method';
import Tip from '@/components/Tip';
import Date from '@/components/Date';
import { zyprolongationMem } from '@/pages/developMem/services'
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 14 },
    },
};
class index extends React.Component<any, any>{
    constructor(props) {
        super(props);
        this.state = {
            visible: false,
            isMarkValue: '0'
        }
    }
    // 时间限制
    disabledTomorrow = (current) => {
        const { memInfo: { extendPreparDate = '' } = {} } = this.state;
        if (_isNumber(extendPreparDate)) {
            return current && current.valueOf() < extendPreparDate
        } else {
            return false
        }
    };
    // componentDidMount(): void {
    //   this.props.dispatch({
    //     type:'commonDict/getDict',
    //     payload:{
    //       data:{
    //         dicName:'dict_d28',
    //       }
    //     }
    //   })
    // }
    submit = async () => {
        const { onClose, commonDict: { dict_d28 = [] } = {} } = this.props;
        const { memInfo } = this.state;
        const { orgCode, orgName, memOrgCode, code: memCode, d08Code, d08Name, name, orgZbCode } = memInfo;
        let flag = false;
        let final = {};
        // this['Same'].props.form.validateFieldsAndScroll((errs, vals)=>{
        //   if(!errs){
        //     vals = unixMoment(['topartCommitteeDate','topartTurnPartyDate','topartOathDate'],vals);
        //     final = {...vals};
        //   }else {
        //     flag = true
        //   }
        // });
        this.props.form.validateFieldsAndScroll(async (err, val) => {
            if (!err) {
                val = findDictCodeName(['d28'], val, {});
                val = unixMoment(['extendPreparDate'], val);
                final = { ...final, ...val };

                if (!flag) {
                    final['logOrgCode'] = memOrgCode;
                    final['orgName'] = orgName;
                    final['orgCode'] = orgCode;
                    final['memCode'] = memCode;
                    final['d08Code'] = d08Code;
                    final['d08Name'] = d08Name;
                    final['name'] = name;
                    final['orgZbCode'] = orgZbCode;
                    const res = await zyprolongationMem({ data: { ...final } })
                    //   const res = await this.props.dispatch({
                    //     type:'memToPositive/prolongationMem',
                    //     payload:{
                    //       data:{...final}
                    //     }
                    //   });
                    const { code = 500 } = res || {};
                    if (code === 0) {
                        onClose && onClose();
                        this.close();
                        Tip.success('操作提示', '操作成功');
                    }
                }
            }
        });

    };
    destroy = () => {
        this.setState({
            memInfo: {},
            isMarkValue: '0'
        });
        // this.props.dispatch({
        //   type:'memLeaveOrg/clear',
        //   payload:{}
        // })
    };
    open = (val) => {
        this.setState({
            visible: true,
            memInfo: val
        })
    };
    close = () => {
        this.destroy();
        this.setState({ visible: false })
    };
    RadioGroupOnChange = ({ target: { value = '' } = {} } = {}) => {
        this.setState({ isMarkValue: value })
    };
    validatorIdcard = (rule, value, callback) => {
        const { memInfo = {} } = this.state;
        if (moment(memInfo['extendPreparDate']).add(0.5, 'years') > value || moment(memInfo['extendPreparDate']).add(1, 'years') < value) {
            callback('延长预备期时间不能少于半年，不能多于一年');
        }
        callback()
    }
    render() {
        const { form, loading: { effects = {} } = {} } = this.props;
        const { getFieldDecorator } = form;
        const { visible, isMarkValue, memInfo = {} } = this.state;
        return (
            <Fragment>
                <Modal
                    title={'延长预备期'}
                    destroyOnClose
                    visible={visible}
                    onOk={this.submit}
                    onCancel={this.close}
                    width={'800px'}
                    confirmLoading={effects['memToPositive/prolongationMem']}
                >
                    {/*<Same wrappedComponentRef={e => this['Same'] = e} memInfo={memInfo}/>*/}
                    <Form key={1}>
                        {/*<FormItem*/}
                        {/*  label="审批结果"*/}
                        {/*  {...formItemLayout}*/}
                        {/*>*/}
                        {/*  {getFieldDecorator('d28Code', {*/}
                        {/*    rules: [{ required: true, message: '审批结果' }],*/}
                        {/*    initialValue:'1',*/}
                        {/*  })(*/}
                        {/*    <DictSelect*/}
                        {/*      // initValue={_isEmpty(basicInfo)?undefined:basicInfo['d28Code']}*/}
                        {/*      codeType={'dict_d28'}*/}
                        {/*      placeholder={'审批结果'}*/}
                        {/*      noDraw={['11','12','13','1']}*/}
                        {/*      // itemsDisabled={d28Disabled}*/}
                        {/*      backType={'object'}*/}
                        {/*    />*/}
                        {/*  )}*/}
                        {/*</FormItem>*/}
                        <FormItem
                            label="转正人员"
                            {...formItemLayout}
                        >
                            {memInfo['name'] || ''}
                        </FormItem>
                        <FormItem
                            label="预备期满时间"
                            {...formItemLayout}
                        >
                            {_isNumber(memInfo['extendPreparDate']) ? moment(memInfo['extendPreparDate']).format('YYYY-MM-DD') : ''}
                        </FormItem>
                        <FormItem
                            label="延长预备期时间"
                            {...formItemLayout}
                        >
                            {getFieldDecorator('isMark', {
                                rules: [{ required: true, message: '延长预备期时间' }],
                                initialValue: isMarkValue,
                            })(
                                <RadioGroup onChange={this.RadioGroupOnChange}>
                                    <Radio value={'0'}>半年</Radio>
                                    <Radio value={'1'}>一年</Radio>
                                    <Radio value={'2'}>自定义时间</Radio>
                                </RadioGroup>
                            )}
                        </FormItem>
                        {
                            isMarkValue === '2' &&
                            <FormItem
                                label="自定义延长预备期时间"
                                {...formItemLayout}
                            >
                                {getFieldDecorator('extendPreparDate', {
                                    rules: [{ required: true, message: '自定义延长预备期时间' }, { validator: this.validatorIdcard }],
                                    // initialValue:isMarkValue,
                                    // <DatePicker placeholder={'自定义时间'} disabledDate={this.disabledTomorrow} style={{width:'100%'}} />
                                })(
                                    <Date isDefaultEnd={false} />
                                )}
                            </FormItem>
                        }
                    </Form>
                </Modal>

            </Fragment>
        );
    }
}
export default Form.create<any>()(index);
