import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Select, Col, Input, Modal, Row, DatePicker, Button, InputNumber, Switch } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import DictSelect from '@/components/DictSelect';
import Tip from '@/components/Tip';
import moment from 'moment';
import { findDictCodeName, unixMoment, timeSort } from '@/utils/method.js';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import _isNumber from 'lodash/isNumber';
import _map from 'lodash/map';
import { object } from 'prop-types';
import { formLabel } from '@/utils/method';
import Date from '@/components/Date';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import YN from '@/components/YesOrNoSelect';
import UploadComp, { getInitFileList, fitFileUrlForForm } from '@/components/UploadComp';
import { findCountYear } from '@/pages/[unit]/subpage/addoredit/extendInfo/extendInfoAll/services';
const FormItem = Form.Item;
const Option = Select.Option;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      isBackHome: false,
      showOffTime: false,
      allowYear: '',
      d038CodeDisabled: false, 
      noDrawD038Code: ['3']
    };
  }
  componentDidMount(): void {
    this.getYear();
  }
  getYear = async () => {
    const res = await findCountYear({});
    if (res?.code == 0) {
      this.setState({
        allowYear: res?.data?.year || '',
      });
    }
  };
  open = ({type}) => {
    const { abroadInfo = {} } = this.props.memAbroad;
    if (abroadInfo?.d038Code == '3') {
      this.setState({
        showOffTime: true,
      });
    } else {
      this.setState({
        showOffTime: false,
      });
    }
    this.setState({
      visible: true,
    });
    if(type === 'read'){
      this.setState({
        onlyRead: true,
      });
    }else{
      this.setState({
        onlyRead: false,
      });
    }
  };
  destroy = () => {
    this.setState({
      visible: false,
      isBackHome: false,
      showOffTime: false,
      allowYear: '',
      d038CodeDisabled: false,
    });
    this.props.dispatch({
      //重置model
      type: 'memAbroad/updateState',
      payload: {
        abroadInfo: {},
      },
    });
  };
  submit = () => {
    const {
      form,
      loading: { effects = {} } = {},
      memBasic: { basicInfo = {} } = {},
      memAbroad: { abroadInfo = {} } = {},
    } = this.props;
    form.validateFieldsAndScroll(async (err, val) => {
      // return
      if (!err) {
        if (val['stopBasis'] && typeof val['stopBasis'] == 'object') {
          val['stopBasis'] = fitFileUrlForForm(val['stopBasis']);
        }
        val['memCode'] = _get(basicInfo, 'code', '');
        val['abroadOrgCode'] = _get(basicInfo, 'memOrgCode', '');
        val['orgCode'] = _get(basicInfo, 'orgCode', '');
        // val['orgEntryCode'] = _get(basicInfo,'orgCode','');

        //bool改为int
        ['isTransfer'].map((item) => {
          val[`${item}`] = val[`${item}`] ? 1 : 0;
        });
        val = findDictCodeName(['d033', 'd038', 'd039', 'd040', 'd127'], val, abroadInfo);
        // ['d033','d037','d038','d039','d040'].map(item=>{
        //   if(!_isEmpty(val[`${item}Code`])){
        //     if(_isEmpty(abroadInfo)){
        //       val[`${item}Name`] = val[`${item}Code`]['name'];
        //       val[`${item}Code`] = val[`${item}Code`]['key'];
        //     }else {
        //       if(val[`${item}Code`] !== abroadInfo[`${item}Code`]){
        //         val[`${item}Name`] = val[`${item}Code`]['name'];
        //         val[`${item}Code`] = val[`${item}Code`]['key'];
        //       }else {
        //         val[`${item}Name`] = abroadInfo[`${item}Name`];
        //       }
        //     }
        //   }
        // });
        val['countryName'] = val['countryCode'] === '1' ? '港澳台' : '国外';
        val = unixMoment(['abroadDate', 'backHomeDate', 'renewOrglifeDate', 'stopPartyDate', 'expectedBackHomeDate'], val);
        // ['abroadDate','backHomeDate','renewOrglifeDate'].forEach(item=>{
        //   val[`${item}`] = _isEmpty(val[`${item}`]) ? undefined : moment(val[`${item}`],'YYYY-MM-DD').valueOf();
        // });
        // console.log(val,'sssss');
        const res = await this.props.dispatch({
          type: 'memAbroad/save',
          payload: {
            data: val,
            type: _isEmpty(val['code']) ? 'add' : 'edit',
          },
        });
        const { code = 500 } = res || {};
        if (code === 0) {
          Tip.success('操作提示', !_isEmpty(val['code']) ? '修改成功' : '新增成功');
          this.destroy();
          this.props.dispatch({
            type: 'memAbroad/getList',
            payload: { pageNum: 1, pageSize: 10, memCode: basicInfo['code'] },
          });
          // 如果是选了停止党籍，关闭基本信息编辑页面，且刷新外面列表
          if( val['d038Code'] == '3'){
            if(this.props.callBack){
              this.props.callBack()
            }
          }
        }
      }
    });
  };
  timeCheck = (rule, value, callback) => {
    const { form } = this.props;
    const { abroadDate = '', backHomeDate = '', expectedBackHomeDate = '' } = form.getFieldsValue() || {};
    const errs = form.getFieldsError();
    for (let obj of Object.entries(errs || {})) {
      !_isEmpty(obj[1]) && form.validateFields([obj[0]]);
    }
    let sortArr = [
      { text: '出国（境）日期', value: abroadDate },
      { text: '回国日期', value: backHomeDate },
    ];
    let text = '';
    const backFunc = (val) => {
      text = val;
    };
    if (timeSort(sortArr, backFunc)) {
      callback(text);
    }
    if (value && expectedBackHomeDate && moment(value).isAfter(expectedBackHomeDate)) {
      callback('出国（境）日期不能晚于预计回国日期');
    } 
     callback();
  };
  backTimeCheck = (rule, value, callback) => {
    const { form } = this.props;
    const { abroadDate = '' } = form.getFieldsValue() || {};
    // 预计回国日期 这个日期一定要大于 出国境日期
    if (value && abroadDate && moment(value).isBefore(abroadDate)) {
      callback('预计回国日期不能早于出国（境）日期');
    } else {
      callback();
    }
  };


  backHomeDateChange = (value) => {
    if (!_isEmpty(value)) {
      this.setState({ isBackHome: true });
    } else {
      this.setState({ isBackHome: false });
    }
  };
  // 是否显示停止党籍时间
  handleChange = (e) => {
    if (e?.key === '3') {
      this.setState({ showOffTime: true });
    } else {
      this.setState({ showOffTime: false });
    }
  };
  doD033CodeChange = (val) => {
    if(!_isEmpty(val)){
      const {key}=val
    const { form } = this.props;
    const { d038Code = '' } = form.getFieldsValue() || {};
    if(key.startsWith('23')){
      form.setFieldsValue({d038Code:{ key:'3', name: '停止党籍' }});
      this.setState({d038CodeDisabled: true})
      this.d038CodeRef.setState({value: '3'})
      this.handleChange({ key: '3' });
    }else{
      this.setState({d038CodeDisabled: false})
      if(form.getFieldValue('d038Code')?.key == '3'){
        form.setFieldsValue({d038Code:undefined})
        this.d038CodeRef.setState({value: ' '})
      }
    }
    }else{
      this.setState({d038CodeDisabled: false})
    }
  }
  render() {
    const {
      form,
      memAbroad,
      loading: { effects = {} } = {},
      memBasic: { basicInfo = {} } = {},
      tipMsg = {},
    } = this.props;
    const { abroadInfo = {} } = memAbroad;
    const { getFieldDecorator, getFieldsValue } = form;
    const { visible, isBackHome, showOffTime, allowYear, d038CodeDisabled = false, onlyRead = false } = this.state;

    return (
      <Modal
        title={onlyRead ? '详情' : abroadInfo['code'] ? '编辑' : '新增'}
        destroyOnClose
        visible={visible}
        onOk={this.submit}
        onCancel={this.destroy}
        footer={onlyRead ? null : <Fragment>
          <Button onClick={this.destroy}>取消</Button>
          <Button type="primary" onClick={this.submit} loading={effects['memAbroad/save']}>确定</Button>
        </Fragment>}
        width={'800px'}
        confirmLoading={effects['memAbroad/save']}
      >
        <div style={{ height: '500px', overflow: 'auto' }}>
          <Form>
            <FormItem label={formLabel('所至国家（地区）名称', tipMsg['countryCode'])} {...formItemLayout}>
              {getFieldDecorator('countryCode', {
                rules: [{ required: true, message: '请填写所至国家（地区）名称' }],
                initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['countryCode'],
              })(
                <Select placeholder={'所至国家（地区）名称'} disabled={onlyRead}>
                  <Option value="1">1 | 港澳</Option>
                  <Option value="2">2 | 国外</Option>
                  <Option value="3">3 | 台湾</Option>
                </Select>,
              )}
            </FormItem>
            {(function (_this) {
              const { countryCode = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof countryCode === 'string' ? countryCode : countryCode?.key;
              if (_key === '2') {
                return (
                  <FormItem label={formLabel('所至国家', tipMsg['d127Code'])} {...formItemLayout}>
                    {getFieldDecorator('d127Code', {
                      rules: [{ required: true, message: '请填写所至国家' }],
                      initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['d127Code'],
                    })(<DictTreeSelect 
                    disabled={onlyRead} 
                    codeType={'dict_d127'} 
                    initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d127Code']} backType={'object'} 
                    filter={(data)=>{
                      return data.filter(it => it.key != '1');
                    }}
                    />)}
                  </FormItem>
                );
              }
            })(this)}
            {getFieldDecorator('code', { initialValue: abroadInfo['code'] })(<div style={{ display: 'none' }}>123</div>)}
            {/* 预计回国日期 这个日期一定要大于 出国境日期] */}
            <FormItem label={formLabel('出国（境）日期', tipMsg['abroadDate'])} {...formItemLayout}>
              {getFieldDecorator('abroadDate', {
                rules: [{ required: true, message: `请填写出国（境）日期` }, { validator: this.timeCheck }],
                initialValue: !_isNumber(abroadInfo['abroadDate']) ? undefined : moment(abroadInfo['abroadDate']),
                // <DatePicker placeholder="请选择" style={{width:'100%'}} disabledDate={this.disabledTomorrow} />
              })(
                <Date startTime={moment(allowYear)} disabled={onlyRead} />,
                // <Date/>
              )}
            </FormItem>
            {/* 出国（境）目的d033Code 选因私-定居 时，则党籍处理方式d038Code固定选停止党籍不能编辑，，并且保存后前端关闭该页面 */}
            <FormItem label={formLabel('出国（境）目的', tipMsg['d033Code'])} {...formItemLayout}>
              {getFieldDecorator('d033Code', {
                rules: [{ required: true, message: '出国（境）目的' }],
                initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['d033Code'],
              })(
                <DictTreeSelect
                  disabled={onlyRead}
                  initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d033Code']}
                  codeType={'dict_d33'}
                  placeholder={`出国（境）目的`}
                  parentDisable={true}
                  backType={'object'}
                  onChange={(e) => {
                    this.doD033CodeChange(e);
                  }}
                />,
              )}
            </FormItem>
            {/* <FormItem
              label={formLabel('出国党员与党组织联系情况', tipMsg['d037Code'])}
              {...formItemLayout}
            >
              {getFieldDecorator('d037Code', {
                rules: [{ required: true, message: '出国党员与党组织联系情况' }],
                initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['d037Code'],
              })(
                <DictSelect
                  codeType={'dict_d37'}
                  initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d037Code']}
                  backType={'object'}
                />,
              )}
            </FormItem> */}
            {/* 出国（境）目的d033Code 不是【因私-定居】 时，则党籍处理方式d038Code不显示【停止党籍】 */}
            <FormItem label={formLabel('党籍处理方式', tipMsg['d038Code'])} {...formItemLayout}>
              {getFieldDecorator('d038Code', {
                rules: [{ required: true, message: '党籍处理方式' }],
                initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['d038Code'],
              })(
                <DictSelect
                  ref={(e) => (this.d038CodeRef = e)}
                  onChange={this.handleChange}
                  codeType={'dict_d38'}
                  initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d038Code']}
                  backType={'object'}
                  disabled={d038CodeDisabled || onlyRead}
                  noDraw={this.state.noDrawD038Code}
                />,
              )}
            </FormItem>
            {showOffTime ? (
              <Fragment>
                <FormItem label={formLabel('停止党籍时间', tipMsg['stopPartyDate'])} {...formItemLayout}>
                  {getFieldDecorator('stopPartyDate', {
                    rules: [{ required: true, message: `请填写停止党籍时间` }],
                    initialValue: !_isNumber(abroadInfo['stopPartyDate']) ? undefined : moment(abroadInfo['stopPartyDate']),
                  })(<Date disabled={onlyRead} />)}
                </FormItem>
                <FormItem label={formLabel('工作单位及职务', tipMsg['workPost'])} {...formItemLayout}>
                  {getFieldDecorator('workPost', {
                    rules: [{ required: true, message: `请填写工作单位及职务` }],
                    initialValue: abroadInfo['workPost'],
                  })(<Input disabled={onlyRead} />)}
                </FormItem>
                <FormItem label={formLabel('停止党籍文件依据', tipMsg['stopBasis'])} {...formItemLayout}>
                  {getFieldDecorator('stopBasis', {
                    rules: [{ required: false, message: `请上传停止党籍文件依据` }],
                    initialValue: getInitFileList(abroadInfo['stopBasis']),
                  })(<UploadComp disabled={onlyRead} maxLen={1} files={getInitFileList(abroadInfo['stopBasis'])} />)}
                </FormItem>
              </Fragment>
            ) : null}
            {/* <FormItem
              label={formLabel('申请保留党籍的时间（月）', tipMsg['partyKeepMonth'])}
              {...formItemLayout}
            >
              {getFieldDecorator('partyKeepMonth', {
                rules: [{ required: false, message: '申请保留党籍的时间（月）' }],
                initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['partyKeepMonth'],
              })(
                <InputNumber
                  min={1}
                  style={{ width: '100%' }}
                  placeholder={`申请保留党籍的时间（月）`}
                />,
              )}
            </FormItem> */}
            <FormItem label={formLabel('出国（境）事项说明', tipMsg['remark'])} {...formItemLayout}>
              {getFieldDecorator('remark', {
                rules: [{ required: false, message: '出国（境）事项说明' }],
                initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['remark'],
              })(<Input disabled={onlyRead} placeholder={'出国（境）事项说明'} />)}
            </FormItem>
            <LongLabelFormItem
              label={'组织关系出国境时是否转往国（境）外'}
              required={true}
              code={'isTransfer'}
              tipMsg={tipMsg}
              formItemLayout={formItemLayout}
              formItem={(formItemLayout, code) => {
                return (
                  <FormItem {...formItemLayout}>
                    {getFieldDecorator(code, {
                      rules: [{ required: true, message: '组织关系出国境时是否转往国（境）外' }],
                      initialValue: abroadInfo[code],
                    })(<YN disabled={onlyRead} init={abroadInfo[code]} />)}
                  </FormItem>
                );
              }}
            />
            {/* <FormItem
              label={formLabel('组织关系出国境时是否转往国（境）外', tipMsg['isTransfer'])}
              {...formItemLayout}
            >
              {getFieldDecorator('isTransfer', {
                rules: [{ required: true, message: '组织关系出国境时是否转往国（境）外' }],
                initialValue: abroadInfo['isTransfer'] || 1,
              })(
                <Switch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  defaultChecked={abroadInfo['isTransfer'] === 1}
                />,
              )}
            </FormItem> */}
            <FormItem label={formLabel('预计回国日期', tipMsg['expectedBackHomeDate'])} {...formItemLayout}>
              {getFieldDecorator('expectedBackHomeDate', {
                rules: [{ required: true, message: '预计回国日期' }, { validator: this.backTimeCheck }],
                initialValue: !_isNumber(abroadInfo['expectedBackHomeDate']) ? undefined : moment(abroadInfo['expectedBackHomeDate']),
                // <DatePicker placeholder="请选择" style={{width:'100%'}} disabledDate={this.disabledTomorrow} onChange={this.backHomeDateChange}/>
              })(<Date isDefaultEnd={false} disabled={onlyRead} />)}
            </FormItem>
            {/* <FormItem label={formLabel('回国日期', tipMsg['backHomeDate'])} {...formItemLayout}>
              {getFieldDecorator('backHomeDate', {
                rules: [{ required: false, message: '回国日期' }, { validator: this.timeCheck }],
                initialValue: !_isNumber(abroadInfo['backHomeDate'])
                  ? undefined
                  : moment(abroadInfo['backHomeDate']),
                // <DatePicker placeholder="请选择" style={{width:'100%'}} disabledDate={this.disabledTomorrow} onChange={this.backHomeDateChange}/>
              })(<Date disabledDate={this.disabledTomorrow} />)}
            </FormItem> */}
            {/* <FormItem
              label={formLabel('申请恢复组织生活日期', tipMsg['renewOrglifeDate'])}
              {...formItemLayout}
            >
              {getFieldDecorator('renewOrglifeDate', {
                rules: [{ required: isBackHome, message: '申请恢复组织生活日期' }],
                initialValue: !_isNumber(abroadInfo['renewOrglifeDate'])
                  ? undefined
                  : moment(abroadInfo['renewOrglifeDate']),
                // <DatePicker placeholder="请选择" style={{width:'100%'}}/>
              })(<Date />)}
            </FormItem> */}
            {/* <FormItem label={formLabel('回国情况', tipMsg['d039Code'])} {...formItemLayout}>
              {getFieldDecorator('d039Code', {
                rules: [{ required: isBackHome, message: '回国情况' }],
                initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['d039Code'],
              })(
                <DictTreeSelect
                  initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d039Code']}
                  codeType={'dict_d39'}
                  placeholder={`回国情况`}
                  parentDisable={true}
                  backType={'object'}
                />,
              )}
            </FormItem> */}
            {/* <FormItem label={formLabel('恢复组织生活情况', tipMsg['d040Code'])} {...formItemLayout}>
              {getFieldDecorator('d040Code', {
                rules: [{ required: isBackHome, message: '恢复组织生活情况' }],
                initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['d040Code'],
              })(
                <DictSelect
                  codeType={'dict_d40'}
                  initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d040Code']}
                  backType={'object'}
                />,
              )}
            </FormItem> */}
            {/* 回国后详情需展示回国信息 */}
            {_isNumber(abroadInfo['backHomeDate']) && (
              <Fragment>
                <FormItem label={formLabel('回国日期', tipMsg['backHomeDate'])} {...formItemLayout}>
                  {getFieldDecorator('backHomeDate', {
                    rules: [{ required: false, message: '回国日期' }, { validator: this.timeCheck }],
                    initialValue: !_isNumber(abroadInfo['backHomeDate']) ? undefined : moment(abroadInfo['backHomeDate']),
                    // <DatePicker placeholder="请选择" style={{width:'100%'}} disabledDate={this.disabledTomorrow} onChange={this.backHomeDateChange}/>
                  })(<Date disabled />)}
                </FormItem>
                <FormItem label={formLabel('出国党员与党组织联系情况', tipMsg['d037Code'])} {...formItemLayout}>
                  {getFieldDecorator('d037Code', {
                    rules: [{ required: true, message: '出国党员与党组织联系情况' }],
                    initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['d037Code'],
                  })(<DictSelect disabled codeType={'dict_d37'} initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d037Code']} backType={'object'} />)}
                </FormItem>
                <FormItem label={formLabel('申请恢复组织生活日期', tipMsg['renewOrglifeDate'])} {...formItemLayout}>
                  {getFieldDecorator('renewOrglifeDate', {
                    rules: [{ required: isBackHome, message: '申请恢复组织生活日期' }],
                    initialValue: !_isNumber(abroadInfo['renewOrglifeDate']) ? undefined : moment(abroadInfo['renewOrglifeDate']),
                  })(<Date disabled />)}
                </FormItem>
                <FormItem label={formLabel('恢复组织生活情况', tipMsg['d040Code'])} {...formItemLayout}>
                  {getFieldDecorator('d040Code', {
                    rules: [{ required: isBackHome, message: '恢复组织生活情况' }],
                    initialValue: _isEmpty(abroadInfo) ? undefined : abroadInfo['d040Code'],
                  })(<DictSelect disabled codeType={'dict_d40'} initValue={_isEmpty(abroadInfo) ? undefined : abroadInfo['d040Code']} backType={'object'} />)}
                </FormItem>
              </Fragment>
            )}
          </Form>
        </div>
      </Modal>
    );
  }
}
export default Form.create()(index);
