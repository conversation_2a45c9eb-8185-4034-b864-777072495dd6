import React, { useEffect } from 'react';
import { _history as router } from '@/utils/method';
import { connect } from 'dva';
const CryptoJS = require('crypto-js');

const login = (props:any) => {
  const { config = '' } = router.location.query;

  const encryptedData = CryptoJS.AES.encrypt(
    'qaz@123456',
    CryptoJS.enc.Utf8.parse('AESNBHB3ZA==HKXt'),
    { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 },
  );
  const password = encryptedData.toString();

  useEffect(() => {
    router.push('/qzs/screen/last')
    // sessionStorage.setItem('dataApi','3b571e29df3445a8');
    // if(true){
    //   let account = 'qztest003';
    //   // if(config == '1'){
    //   //   account = 'qztest001'
    //   // }
    //   // if(config == '2'){
    //   //   account = 'qztest002'
    //   // }
    //   // if(config == '3'){
    //   //   account = 'qztest003'
    //   // }

    //   props.dispatch({
    //     type: 'login/login',
    //     payload: {
    //       data:{
    //         password,
    //         account,
    //         captchaCode:'U32xS4DACRRP0XtJPhOjlQ=='
    //       }
    //     }
    //   }).then(({code}:any)=>{})
    // }

  }, []);

  return <div></div>;
};

export default connect(({ login }) => ({ login }))(login);
