import React, { Fragment, useRef, useEffect, useState } from 'react';
import { TableCol, getCheckTableCols, TableColDepMem, TableColDepMemStatistical, config } from './config';
import { Spin, Button, Select, Form } from 'antd';
import Date from '@/components/Date';
import { Colgroup, Head as SelfHead, fakeLine } from '@/components/DynamicTableHead';
import ReactDataSheet from 'react-datasheet';
import CheckBack from './check';
import { getSession } from '@/utils/session';
import _isEmpty from 'lodash/isEmpty';
import { reportExcel, peggingExcel, exportExcel, supportExcel } from '../services';
import { unixMoment } from '@/utils/method.js';
import Tip from '@/components/Tip';
import moment from 'moment';
import { tableSelect } from '@/pages/dataSearch/services';
const index = (props: any) => {
  const tdStyle: any = {
    height: 28,
    border: '1px solid black',
    wordBreak: 'break-all',
    textAlign: 'center',
  };

  const [form] = Form.useForm();
  const org: any = getSession('org') || {};
  const checkRef: any = useRef();
  const { briefingTab } = sessionStorage;
  const NowYear = new Array(5).fill(1).map((it, index) => +moment().format('YYYY') - index);
  const [year, setYear] = useState<any>(NowYear[1]);
  const [tableData, setTabData] = useState<any>([]);
  const [tableLoading, setTabLoading] = useState<any>(false);
  const [selectList, setSelectList] = useState<any>([]);

  const [time, setTime] = useState<any>(moment().valueOf());

  const [table, setTable] = useState<any>({});
  const [search, setSearch] = useState<any>({});

  const getTable = async (p: any = {}) => {
    setTabLoading(true);
    let initData = {
      ...search,
      orgCode: org.orgCode,
      ...p,
    };
    if (table?.reportCode == 'excel_8') {
      initData.year = year;
    }
    const { code = 500, data = [] } = await reportExcel({
      data: initData,
    });
    setTabLoading(false);
    if (code == 0) {
      setTabData(data);
    }
  };

  const getSelectList = async () => {
    const { code = 500, data = [] } = await supportExcel({});
    if (code == 0) {
      let _list = data.map((it) => it.reportCode);
      let final = config.filter((it) => _list.includes(it.reportCode));
      setSelectList(final);
    }
  };

  const exportExcels = async () => {
    const { code = 500 } = await exportExcel({
      data: {
        orgCode: org.orgCode,
        ...search,
      },
    });
    if (code == 0) {
      Tip.success('操作提示', '操作成功');
    }
  };

  const onFinish = async (event) => {
    let times = ['startDate', 'endDate'];
    let val = unixMoment(times, event);
    sessionStorage.setItem('briefingTab', val.reportCode);
    let table = selectList.find((it) => it.reportCode == val.reportCode) || {};
    await setTable(table);
    await setSearch(val);
    setTabData([]);
  };

  const searchInit = () => {
    form.resetFields();
    let obj = {
      reportCode: briefingTab || selectList[0].reportCode,
    };
    form.setFieldsValue(obj);
    onFinish(obj);
  };

  useEffect(() => {
    // 当存在搜索存在初始化的时候
    if (_isEmpty(search)) {
      form.submit();
    } else {
      getTable();
    }
  }, [search]);

  useEffect(() => {
    if (!_isEmpty(selectList)) {
      searchInit();
    }
  }, [selectList]);

  useEffect(() => {
    setSelectList([]);
    getSelectList();
  }, [org.orgCode, org.subordinate]);

  if (_isEmpty(table)) {
    return <div></div>;
  }

  return (
    <div style={{ width: '100%', height: '100%', overflow: 'auto' }}>
      <div style={{ marginBottom: 10 }}>
        <Form layout={'inline'} onFinish={onFinish} form={form}>
          <Form.Item label="请选择调度表" name="reportCode">
            <Select style={{ width: 300 }} onChange={async (event: any) => {}}>
              {!_isEmpty(selectList) &&
                selectList.map((it) => (
                  <Select.Option value={it.reportCode} key={it.reportCode}>
                    {it.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.reportCode !== currentValues.reportCode}>
            {({ getFieldValue }) => {
              const reportCode = getFieldValue('reportCode');
              return reportCode === 'excel_2' || reportCode === 'excel_3' ? (
                <React.Fragment>
                  <Form.Item label="请选择起止时间">
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Form.Item name="startDate">
                        <Date onChange={(e) => {}}></Date>
                      </Form.Item>
                      至&nbsp;&nbsp;
                      <Form.Item name="endDate">
                        <Date onChange={(e) => {}}></Date>
                      </Form.Item>
                    </div>
                  </Form.Item>
                </React.Fragment>
              ) : reportCode === 'excel_8' ? (
                <Form.Item name="year" label="年份" initialValue={year}>
                  <Select
                    style={{ width: '150px' }}
                    defaultValue={year}
                    onChange={(value) => {
                      setYear(value);
                    }}
                    placeholder={'请选择年份'}
                  >
                    {NowYear.map((it) => (
                      <Select.Option value={it}>{it}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          <Form.Item>
            <Button type={'primary'} onClick={form.submit}>
              查询
            </Button>
          </Form.Item>
          <Form.Item>
            <Button type={'primary'} onClick={exportExcels}>
              导出excel
            </Button>
          </Form.Item>
        </Form>
      </div>
      <div style={{ width: table?.width || 'auto' }}>
        <h2 style={{ marginTop: '20px', width: '100%', textAlign: 'center' }}>{table?.name}</h2>
        <table style={{ width: '100%' }}>
          <SelfHead tree={table?.head} nodeName={'k0505'} key={time} />
          <Colgroup tree={table?.head} nodeWith={table?.nodeWith} />
        </table>
        <ReactDataSheet
          data={tableData}
          valueRenderer={(cell: any) => cell['v']}
          sheetRenderer={(props: any) => {
            return (
              <table style={{ width: '100%' }}>
                <Colgroup tree={table?.head} nodeWith={table?.nodeWith} />
                <tbody>{props.children}</tbody>
              </table>
            );
          }}
          cellRenderer={(props: any) => {
            const { cell = {} } = props || {};
            return (
              <td
                style={{ ...tdStyle, width: props.col === 0 ? 200 : table?.nodeWith }}
                onClick={() => {
                  if (cell.v && props.col !== 0) {
                    checkRef.current.open({
                      ...cell,
                      year,
                      reportCode: form.getFieldValue('reportCode'),
                      startDate: form.getFieldValue('startDate') ? form.getFieldValue('startDate').valueOf() : undefined,
                      endDate: form.getFieldValue('endDate') ? form.getFieldValue('endDate').valueOf() : undefined,
                    });
                  }
                }}
              >
                {cell['v']}
              </td>
            );
          }}
        />
      </div>
      <CheckBack
        // showItemDtetails={['mem']}
        detailListAction={peggingExcel}
        // detailListAction={detail}
        tableColumns={(query, props, { renderMemDetail, renderDevelopDetail, renderOrgDetail, renderUnitDetail }) => {
          return getCheckTableCols(query?.t);
        }}
        ref={checkRef}
      />
    </div>
  );
};
export default index;
