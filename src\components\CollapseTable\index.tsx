import React, { useState, useEffect, Fragment, useImperativeHandle } from 'react';
import styles from './index.less';
import { Button, <PERSON>lapse, Modal, Popconfirm, Tooltip } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import head from '@/assets/head.jpg';
import { PlusOutlined } from '@ant-design/icons';
import ListTable from '@/components/ListTable';

interface Interface {
  list?: Array<any>,
  panelHeader: Function,
  mains: Function,
  tableListAction: Function,
  tableActionOtherQueries?: object,
  children?: React.ReactNode
}
interface PanelTableInterface {
  data: object,
  mainsListAction: Function,
  mainsActionOtherQueries?: object,
  linkEdit?: Function,
  add: Function,
}
interface PanelListTableInterface {
  data: object,
  mainsListAction: Function,
  mainsActionOtherQueries?: object,
  linkEdit?: Function,
  rowKey?:string,
  columns?: Array<any>
}
const PanelTable = React.forwardRef((props: PanelTableInterface, ref) => {
  const {
    data = {},
    mainsListAction,
    mainsActionOtherQueries = {},
    linkEdit,
    add,
  } = props;
  const [mainList, setMainList] = useState([]);
  const [mainPagination, setMainPagination] = useState({ pageNum: 1, pageSize: 5, total: 0, totalPage: 0 });

  useImperativeHandle(ref, () => ({
    getList: (query: any) => {
      const { pageNum = 1 } = query || {};
      getmainsList({ pageNum });
    },
  }));

  const onPage = (flag) => {
    getmainsList({
      pageNum: mainPagination.pageNum + (flag ? 1 : -1)
    });
  };
  const getmainsList = async (p = {}) => {
    const { code: mainResCode = 500, data: { list: mainsList = [], ...others } = {} } = await mainsListAction({
      pageNum: mainPagination.pageNum,
      pageSize: mainPagination.pageSize,
      ...mainsActionOtherQueries,
      ...p
    });
    if (mainResCode == 0) {
      setMainList(mainsList);
      setMainPagination({ ...others, pageNum: others.pageNumber })
    }
  };
  useEffect(() => {
    getmainsList({ pageNum: 1 });
  }, [])
  return (
    <div className={styles.main}>
      <div style={{ flex: 1 }}>
        {
          mainList.map((item, ind) => {
            // d022Name-党内职务  d25Name-行政职务
            let memName=item['memName'] || '';
            let d022Name= item['d022Name'] ? `(${item['d022Name']})` : item['d25Name'] ? `(${item['d25Name']})` : '';
            let lastString = `${memName + d022Name}`;
            if(lastString.length > 11){
              lastString = `${lastString.slice(0,11)}...`
            }
            return (
              <div key={ind} className={styles.panel_body}>
                {/* <div><img src={head} style={{ width: 128, height: 158, margin: '0 auto', display: 'block' }} /></div> */}
                <div><img alt='头像' src={ _isEmpty(item['photoPath']) ? head : item['photoPath'] } style={{ width: 128, height: 158, margin: '0 auto', marginTop:'4px', display: 'block' }} /></div>
                <div>
                <Tooltip title={item['d022Name'] || item['d25Name']}>
                  <h4>{lastString}</h4>
                </Tooltip>
                  <div style={{ textAlign: 'center', fontSize: '14px' }}>{item['resultName']}</div>
                  <div className={styles.link_edit}>
                    {linkEdit && linkEdit(item)}
                  </div>
                </div>
              </div>
            );
          })
        }
        {add()}
        {/* <div className={styles.add}>
          <PlusOutlined style={{ fontSize: '50px', transform: 'translateY(100%)' }} onClick={add} />
        </div> */}
      </div>
      <div className={styles.pagination}>
        <div className={styles.pagination2}>
          <div style={{ width: 30 }}>
            {
              mainPagination.pageNum > 1 &&
              <div className={styles.imgLeft} onClick={() => onPage(false)}>
                <img src={require('@/assets/left.png')} />
              </div>
            }
          </div>
          <div style={{ width: 30 }}>
            {
              mainPagination.totalPage > 1 && mainPagination.pageNum < mainPagination.totalPage &&
              <div className={styles.imgRight} onClick={() => onPage(true)}>
                <img src={require('@/assets/right.png')} />
              </div>
            }
          </div>
        </div>
        <div>
          {mainPagination.totalPage > 1 && <React.Fragment>
            {mainPagination.pageNum}/{mainPagination.totalPage}
          </React.Fragment>}
        </div>
      </div>
    </div>
  )
});
const PanelListTable = React.forwardRef((props: PanelListTableInterface, ref) => {
  const {
    data = {},
    mainsListAction,
    mainsActionOtherQueries = {},
    linkEdit,
    rowKey,
    columns = [
      // {
      //   title:'序号',
      //   dataIndex:'num',
      //   render:(text,record,index)=>{
      //     return (current-1)*pageSize+index+1
      //   }
      // },
      {
        title: '姓名',
        dataIndex: 'memName',
        width: 100,
      },
    ],
  } = props;
  const [mainList, setMainList] = useState([]);
  const [mainPagination, setMainPagination] = useState({ pageNum: 1, pageSize: 5, total: 0, totalPage: 0 });

  useImperativeHandle(ref, () => ({
    getList: (query: any) => {
      const { pageNum = 1 } = query || {};
      getmainsList({ pageNum });
    },
  }));
  const getmainsList = async (p = {}) => {
    const { code: mainResCode = 500, data: { list: mainsList = [], ...others } = {} } = await mainsListAction({
      pageNum: mainPagination.pageNum,
      pageSize: mainPagination.pageSize,
      ...mainsActionOtherQueries,
      ...p
    });
    if (mainResCode == 0) {
      setMainList(mainsList);
      setMainPagination({ ...others, pageNum: others.pageNumber, current: others.pageNumber, total: others.totalRow })
    }
  };
  useEffect(() => {
    getmainsList({ pageNum: 1 });
  }, [])
  return (
    <div className={styles.main}>
      <ListTable columns={[
        {
          title: '序号',
          dataIndex: 'num',
          align: 'center',
          width: 50,
          render: (text, record, index) => {
            return ((mainPagination['pageNum'] - 1) * mainPagination['pageSize']) + index + 1;
          },
        },
        ...columns
      ]} data={mainList} scroll={{x:800}} rowKey={rowKey} pagination={mainPagination} onPageChange={(pageNum, pageSize) => getmainsList({ pageNum, pageSize })} />
    </div>
  )
});
const index = React.forwardRef((props: Interface, ref) => {
  const {
    panelHeader,
    mains,
    tableListAction,
    tableActionOtherQueries = {},
    children,
  } = props;
  const [activeKey, setactiveKey] = useState('');
  const [list, setList] = useState<any>([]);
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 100, total: 0 });


  const callback = (expandedKeys) => {
    setactiveKey(expandedKeys);
  };

  const getTableList = async (p = {}) => {
    const { code = 500, data: { list = [] } = {} } = await tableListAction({
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...tableActionOtherQueries,
      ...p,
    });
    if (code == 0) {
      setList(list);
    }
  };
  useImperativeHandle(ref, () => ({
    getList: (query: any) => {
      const { pageNum = 1 } = query || {};
      setactiveKey('');
      getTableList({ pageNum });
    },
    setActiveKeys: (key: string) => {
      setactiveKey(key);
    }
  }));
  useEffect(() => {
    getTableList({ pageNum: 1 });
    return () => {
      setList([]);
    }
  }, []);
  return (
    <Fragment>
      <Collapse onChange={callback} accordion={true} activeKey={activeKey}>
        {
          !_isEmpty(list) && list.map((it, listIndex) => {
            return (
              <Collapse.Panel key={listIndex} header={panelHeader(it, listIndex)}>
                {`${activeKey}`.includes(listIndex.toString()) && mains(it, listIndex)}
              </Collapse.Panel>
            )
          })
        }
      </Collapse>
      {children}
    </Fragment>
  )
})

export default index;
export { PanelTable, PanelListTable }
