import React, { useState, Fragment, useEffect, useImperativeHandle, useCallback, useRef } from 'react';
import _isEmpty from 'lodash/isEmpty';
import { _history } from "@/utils/method";
import qs from 'qs';
import { Mo<PERSON>, <PERSON><PERSON>crumb, Button, Empty } from 'antd';
import moment from 'moment'
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import './index.less'
import imgsss from '@/assets/mm.jpeg'

const Index = (props: any, ref: any) => {
    useImperativeHandle(ref, () => ({
        showModal: (url: any) => {
            setVisible(true)
            setUrl(url)
        }
    }));
    const [visible, setVisible] = useState(false)
    const [url, setUrl] = useState('')

    const bookRef = useRef(null);
    const {
        onChange,
    } = props;

    const { location: { pathname = '' } = {} } = _history


    const onCancel = () => {

        setVisible(false)
    }
    const imgpreview = () => {

    }
      const handleimg = () => {
            let canvas = document.createElement('canvas');
            const user = JSON.parse(sessionStorage.getItem('user') || '{}')
            canvas.width = 430;
            canvas.height = 700;
            let context: any = canvas.getContext('2d');
            // 设置字体
            context.font = '20px normal';
            // 设置颜色
            context.fillStyle = '#4c4747';
            // context.rotate((-35 * Math.PI) / 180);
            context.translate(215, 350); // 移动到中心点
            context.rotate(-(Math.PI / 3)); // 旋转45度
            // 设置水平对齐方式
            context.textAlign = 'left';
            // 设置垂直对齐方式
            context.textBaseline = 'top';
            // 绘制文字（参数：要写的字，x坐标，y坐标）
            context.fillText(`本档案仅供${user.account}账户${moment().format('YYYY年MM月DD日')}阅览使用,不具备任何形式的法律效应`, -360, 0);
            let watermark: any = document.getElementById('previewwater')
            if (watermark) {
                watermark.style = `background: url(${context.canvas.toDataURL()});pointer-events:none;opacity:.4;position: absolute;z-index: 9;top: 0;left: 0;width: 100%;height: 700px`;
            }
        }
    useEffect(() => {
    }, [])
    return (
        <Modal
            title=''
            visible={visible}
            footer={false}
            width={1344}
            onCancel={onCancel}
            zIndex={99999}
            destroyOnClose
            wrapClassName='preview-img'
        >
            <div style={{width:'100%',height:'75vh',overflow:'hidden'}}>
            <TransformWrapper initialScale={1} minScale={0.1} >
                <TransformComponent contentClass='wrappers3' wrapperClass='wrappers4'>
                <div id='previewwater'></div>
                    <img src={url} alt="示例图片"  style={{width:'100%',height:'700px'}} onLoad={handleimg}/>
                    {/* <img src={imgsss} alt="示例图片"  style={{width:'100%',height:'700px'}} onLoad={handleimg}/> */}
                </TransformComponent>
            </TransformWrapper>
            </div>
           
        </Modal>

    );
}
// @ts-ignore
export default React.forwardRef(Index);