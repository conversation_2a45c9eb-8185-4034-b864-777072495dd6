/**
 * 字典treeSelect组件
 */
import React from 'react';
import { Divider, TreeSelect } from 'antd';
import { connect } from 'dva';
import { TreeSelectProps } from 'antd/lib/tree-select';
import { getSession, getLocalSession } from '@/utils/session';
const TreeNode = TreeSelect.TreeNode;
import _isArray from 'lodash/isArray';
import _cloneDeep from 'lodash/cloneDeep';
import { isEmpty } from '@/utils/method';
import styles from './index.less';
import { OrderedListOutlined, DownOutlined } from '@ant-design/icons';
import { TableModal } from '../DictMoalTableTreeSelect';
import { PlusSquareOutlined, MinusSquareOutlined } from '@ant-design/icons';
import { log } from 'console';
interface propsType extends TreeSelectProps<any> {
  codeType: string;
  placeholder?: string;
  codeValue?: string;
  codeName?: string;
  onChange?: (any) => void;
  onFocus?: () => void;
  backType?: 'object';
  dispatch?: any;
  commonDict?: any;
  renderItem?: () => {};
  parentDisable?: boolean;
  initValue?: string | Array<any>;
  searchKey?: Array<string>;
  dicName?: string;
  itemsDisabled?: Array<string>;
  noDraw?: Array<string>;
  filter?: Function;
  getDictWay?: String;
  showModalIcon?: boolean;
  showMoreBtn?: boolean;
  pageSize?: number;
}

function debounce(func, wait) {
  let timeout;
  return function (this: any, ...args) {
    const context = this; // 保存 this 上下文
    clearTimeout(timeout); // 清除之前的计时器
    timeout = setTimeout(() => {
      func.apply(context, args); // 调用原始函数
    }, wait);
  };
}

// @ts-ignore
@connect(({ commonDict }) => ({ commonDict }), undefined, undefined, { forwardRef: true })
export default class index extends React.Component<propsType & any, any> {
  static defaultProps = {
    codeValue: 'key', //node 唯一key
    codeName: 'name', //node 显示名称
    searchKey: ['key', 'name', 'pinyin'], //默认查询条件
    dicName: 'dicName',
    placeholder: '请选择',
    searchPlaceholder: '请输入关键词检索',
    parentDisable: false,
    treeData: undefined,
    itemsDisabled: [], // 子节点自定义disabled
    getDictWay: 'commonDict/getDict',
    showModalIcon: false,
    showMoreBtn: true,
    pageSize: 20,
  };
  static clear() { }
  static destroy() { }

  // 生成唯一实例ID
  private instanceId: string;
  // 防抖函数
  private handleInputChangeDebounced: Function;

  constructor(props) {
    super(props);
    this.state = {
      value: undefined,
      clear: false,
      expandedKeys: [],
      hasScrollbar: false,
      currentPage: 1,
      loading: false,
      isAtBottom: false,
    };
    index.clear = this.clearAll;
    index.destroy = this.destroy;
    // 绑定防抖函数到组件实例
    this.handleInputChangeDebounced = debounce(this.handleInputChange, 500);
    // 初始化唯一实例ID
    this.instanceId = `dict-tree-${props.codeType}-${Math.random().toString(36).slice(2, 10)}`;
  }
  componentDidMount(): void {
    const { codeType, commonDict } = this.props;
    if (!commonDict[`${codeType}_tree`]) {
      this.getDict();
    }
  }
  destroy = () => {
    this.state = {
      value: undefined,
      clear: false,
    };
  };
  clearAll = () => {
    this.setState({
      value: undefined,
      clear: true,
    });
  };
  // 强行改变 value的值
  changeValue = (value) => {
    this.setState({
      value
    })
  }
  getDict = () => {
    const { dicName, codeType, onFocus, getDictWay } = this.props;
    this.props.dispatch({
      // type:'commonDict/getDict',
      type: getDictWay,
      payload: {
        data: {
          codeType,
          [dicName as string]: codeType,
        },
      },
    });
    if (onFocus) {
      onFocus();
    }
  };
  onChange = (value, label, extra) => {
    const { treeCheckable } = this.props;
    if (!treeCheckable) {
      if (value && value.includes('_temp')) {
        value = value.split('_temp')[0];
      }
    }
    const { codeType, codeValue, onChange, backType } = this.props;
    this.setState({
      value,
      clear: !value,
    });
    if (backType === 'object') {
      const data = this.props.commonDict[codeType];
      if (data) {
        if (treeCheckable) {
          // 多选是数组且还有常用选项，需要处理
          let _val: any = [];
          if (_isArray(value)) {
            _val = _cloneDeep(value).reduce((arr, it) => {
              if (it && it.includes('_temp')) {
                return [...arr, it.split('_temp')[0]];
              } else {
                return [...arr, it];
              }
            }, []);
          }
          let finds = _val.reduce((arr, item) => {
            let find = data.find((obj) => obj[codeValue as string] !== undefined && obj[codeValue as string] === item);
            if (find) {
              return [...arr, find];
            } else {
              return arr;
            }
          }, []);
          if (finds && onChange) {
            onChange(isEmpty(finds) ? undefined : finds);
          } else {
            onChange && onChange(isEmpty(finds) ? undefined : finds);
          }
        } else {
          let find = data.find((obj) => obj[codeValue as string] !== undefined && obj[codeValue as string] === value);
          if (find && onChange) {
            onChange(Object.assign({}, find));
          } else {
            onChange && onChange(find);
          }
        }
      }
    } else if (onChange) {
      onChange(value, label, extra);
    }
  };
  onSearch = (val) => { };
  onSelect = (value, node, extra) => {
    const { dataRef } = node;
    const { codeValue = '', codeType } = this.props;
    const data = this.props.commonDict[codeType];
    let item: any = [];
    let itemTemp: any = getLocalSession(`${codeType}_temp`) || [];
    let find = itemTemp.find((obj) => obj[codeValue] == dataRef[codeValue]);
    if (find) {
      find['onCount']++;
    } else {
      itemTemp.push({ [codeValue]: dataRef[codeValue], onCount: 1 });
    }
    //排序
    itemTemp.sort((a, b) => b['onCount'] - a['onCount']);
    //查询数据并缓存
    itemTemp.forEach((obj, index) => {
      if (index < 3) {
        let find1 = data.find((item) => item[codeValue] == obj[codeValue]);
        if (find1) {
          item.push({ ...find1 });
        }
      }
    });
    localStorage.setItem(codeType, JSON.stringify(item));
    localStorage.setItem(`${codeType}_temp`, JSON.stringify(itemTemp));
  };
  onFocus = () => {
    const { onFocus } = this.props;
    if (onFocus) {
      onFocus();
    }
  };
  renderTreeNodes = (data, index) => {
    const { expandedKeys = [] } = this.state;
    const { codeName, codeValue, parentDisable, itemsDisabled = [], noDraw = [] } = this.props;
    return data.map((item) => {
      const title = <span title={item['remark']}>{`${item[codeValue || '']} | ${item[codeName || '']}`}</span>;
      if (item.enabled) {
        return <React.Fragment />;
      }
      if (item.children) {
        //是否启用文字点击展开收起节点
        let bool = false;
        //是否禁选节点
        if (parentDisable || itemsDisabled.includes(item[codeValue || ''])) {
          bool = true;
        }
        return (
          <TreeNode
            key={item[codeValue || '']}
            value={item[codeValue || '']}
            title={
              <span
                style={{ width: '100%', display: 'inline-block' }}
                onClick={() => {
                  bool && this.nodeExpand(item[codeValue || '']);
                }}
              >
                {title}
              </span>
            }
            switcherIcon={expandedKeys.includes(item[codeValue || '']) ? <MinusSquareOutlined /> : <PlusSquareOutlined />}
            dataRef={item}
            disabled={item.disabled || parentDisable || itemsDisabled.includes(item[codeValue || ''])}
            style={{ display: noDraw.includes(item[codeValue || '']) ? 'none' : '' }}
          >
            {this.renderTreeNodes(item.children, index + 1)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          className={index == 1 ? 'rootNode' : ''}
          key={item[codeValue || '']}
          value={item[codeValue || '']}
          title={title}
          dataRef={item}
          disabled={item.disabled || itemsDisabled.includes(item[codeValue || ''])}
          style={{ display: noDraw.includes(item[codeValue || '']) ? 'none' : '' }}
        />
      );
    });
  };

  handleInputChange = debounce(() => {
    this.setState({});
  }, 200);

  filterTreeNode = (val, node) => {
    const { codeValue, searchKey } = this.props;
    const { expandedKeys = [] } = this.state;
    const { dataRef = {} } = node.props;
    let resData = [dataRef[codeValue || ''].includes(val)];
    for (let obj of searchKey as Array<string>) {
      resData.push(dataRef[obj] && dataRef[obj].includes(val));
    }
    if (resData.includes(true)) {
      if (!expandedKeys.includes(node.key)) {
        // @ts-ignore
        this.state.expandedKeys = [...expandedKeys, node.key];
      }
      //防抖 所有筛选执行完成后刷新页面
      this.handleInputChange(dataRef);
    }
    return resData.includes(true);
  };
  nodeExpand = (key) => {
    const { expandedKeys = [] } = this.state;
    let newKeys = [...expandedKeys];
    if (newKeys.includes(key)) {
      newKeys = newKeys.filter((keys) => keys != key);
    } else {
      newKeys.push(key);
    }
    this.setState({
      expandedKeys: newKeys,
    });
  };
  onTreeExpand = (expandedKeys) => {
    this.setState(
      {
        expandedKeys,
      },
      () => {
        setTimeout(() => {
          // 使用特定的类名来定位下拉框
          const dropdown = document.querySelector(`.${this.instanceId} .ant-select-tree-list-holder`);
          if (dropdown) {
            const hasScrollbar = dropdown.scrollHeight > dropdown.clientHeight;
            if (this.state.hasScrollbar !== hasScrollbar) {
              this.setState({ hasScrollbar });
            }
          }
        }, 300);
      },
    );
  };

  // 处理点击更多选项按钮
  handleMoreClick = () => {
    const dropdown = document.querySelector(`.${this.instanceId}`);
    if (dropdown) {
      // 获取虚拟列表的滚动容器
      const listHolder = dropdown.querySelector('.ant-select-tree-list-holder');

      if (listHolder) {
        // 方法1: 直接使用scrollTop - 已确认有效
        const currentScroll = listHolder.scrollTop;
        listHolder.scrollTop = currentScroll + 100;

        // 触发滚动事件确保虚拟列表更新渲染
        setTimeout(() => {
          const event = new Event('scroll', { bubbles: true });
          listHolder.dispatchEvent(event);

          // 检查是否滚动到底部
          this.checkScrollPosition(listHolder);
        }, 50);
      }
    }
  };

  // 检查滚动位置
  checkScrollPosition = (scrollElement) => {
    if (scrollElement) {
      // 如果滚动位置 + 容器高度 >= 内容总高度 - 1 (允许1px误差)，则认为已滚动到底部
      const isAtBottom = scrollElement.scrollTop + scrollElement.clientHeight >= scrollElement.scrollHeight - 1;
      if (isAtBottom !== this.state.isAtBottom) {
        this.setState({ isAtBottom });
      }
    }
  };

  render(): React.ReactNode {
    const {
      codeType,
      placeholder,
      searchPlaceholder,
      dropdownStyle,
      renderItem,
      treeData,
      initValue,
      disabled,
      commonDict,
      codeValue,
      codeName,
      itemsDisabled = [],
      filter,
      treeCheckable = false,
      extendProps = {},
      showConstant = true,
      showModalIcon = false,
    } = this.props;
    const data = commonDict[`${codeType}_tree`] || [];
    const constant = getLocalSession(codeType) || [];
    const { clear, expandedKeys = [] } = this.state;
    let value = this.state['value'] || initValue;
    if (clear) {
      value = undefined;
    }
    if (data.length == 0) {
      return <TreeSelect />;
    }
    let filterConstant = filter ? filter(constant) : constant;
    let dfProps = {
      treeDefaultExpandAll: true,
      treeExpandedKeys: expandedKeys,
    };
    console.log("TreeSelectvalue", value);
    return (
      <div className={styles.box}>
        <div style={{ width: `calc(100% - ${showModalIcon ? 30 : 0}px)` }}>
          <TreeSelect
            {...extendProps}
            {...dfProps}
            style={{ width: '100%' }}
            value={value}
            showSearch
            allowClear
            disabled={disabled}
            treeCheckable={treeCheckable}
            dropdownStyle={dropdownStyle ? dropdownStyle : { maxHeight: 400, overflow: 'auto' }}
            placeholder={placeholder}
            searchPlaceholder={searchPlaceholder}
            treeData={treeData}
            onFocus={this.onFocus}
            onSelect={this.onSelect}
            onChange={this.onChange}
            filterTreeNode={this.filterTreeNode}
            dropdownClassName={`${styles.dropdownClass} ${this.instanceId}`}
            onTreeExpand={this.onTreeExpand}
            onDropdownVisibleChange={(open) => {
              if (open) {
                setTimeout(() => {
                  // 使用特定的类名来定位下拉框
                  const dropdown = document.querySelector(`.${this.instanceId} .ant-select-tree-list-holder`);
                  if (dropdown) {
                    const hasScrollbar = dropdown.scrollHeight > dropdown.clientHeight;
                    if (this.state.hasScrollbar !== hasScrollbar) {
                      this.setState({ hasScrollbar });
                    }

                    // 添加滚动事件监听器
                    dropdown.addEventListener('scroll', () => this.checkScrollPosition(dropdown));
                  }
                }, 100);
              }
            }}
            dropdownRender={(menu) => (
              <div>
                {menu}
                {this.props.showMoreBtn && this.state.hasScrollbar ? (
                  <div className="more-options-button" onClick={this.handleMoreClick}>
                    {this.state.isAtBottom ? (
                      '已到底部'
                    ) : (
                      <React.Fragment>
                        更多选项
                        <DownOutlined className="arrow-icon" />
                      </React.Fragment>
                    )}
                  </div>
                ) : (
                  <React.Fragment />
                )}
              </div>
            )}
          >
            {!treeCheckable && showConstant && filterConstant.length > 0 && (
              <React.Fragment>
                <TreeNode title={'常用选项'} value={'-1'} disabled dataRef={{ key: '-1', name: '常用选项' }} />
                {filterConstant.map((item, index) => (
                  <TreeNode
                    key={`${item[codeValue || '']}_temp`}
                    value={`${item[codeValue || '']}_temp`}
                    title={`${item[codeValue || '']} | ${item[codeName || '']}`}
                    dataRef={item}
                    disabled={itemsDisabled.includes(item[codeValue || ''])}
                    style={index == filterConstant.length - 1 ? { borderBottom: '1px solid #d9d9d9' } : {}}
                  />
                ))}
              </React.Fragment>
            )}
            {renderItem ? renderItem : this.renderTreeNodes(filter ? filter(data) : data, '1')}
          </TreeSelect>
        </div>
        {!disabled && showModalIcon && (
          <OrderedListOutlined
            className={styles.icon}
            onClick={() => {
              this.setState(
                {
                  showModals: true,
                },
                () => {
                  setTimeout(() => {
                    this?.[codeType + 'TableModalRef']?.open?.();
                  }, 500);
                },
              );
            }}
          />
        )}
        {this.state.showModals && (
          <TableModal
            {...this.props}
            ref={(e) => (this[codeType + 'TableModalRef'] = e)}
            treeDatas={filter ? filter(data) : data}
            rowSelectionType={treeCheckable ? 'checkbox' : 'radio'}
            selectedValue={value}
            onOK={(selectedRowKeys, selectedItems) => {
              this.onChange(treeCheckable ? selectedRowKeys : selectedRowKeys.toString(), {}, {});
            }}
            close={() => {
              this.setState({
                showModals: false,
              });
            }}
          ></TableModal>
        )}
      </div>
    );
  }
}
