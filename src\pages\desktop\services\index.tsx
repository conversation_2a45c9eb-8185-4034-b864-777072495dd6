/**
 * 党员管理services
 */
import request from "@/utils/request";
import qs from 'qs';
// export function getList(params) {
//   return request(`/api/mem/getList`,{
//     method:'POST',
//     body:params,
//   });
// }
export function getOrgTotal(params) {
  return request(`/api/chart/org/getOrgTotal`, {
    method: 'POST',
    body: params,
  });
}
export function getVcLogin(params) {
  return request(`/api/st/vc/login`, {
    method: 'POST',
    body: params,
  });
}
// export function findMem(params) {
//   return request(`/api/mem/findByCode?${qs.stringify(params)}`,{
//     method:'Get',
//   });
// }

export function activitySituation(params) {
  return request(`/api/activity/situation?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function getFlowJurisdiction(params) {
  return request(`/api/user/getUserIsTop?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function userVerifyDecryption(params) {
  return request(`/api/user/verifyDecryption?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function paymentSituation(params) {
  return request(`/api/fee/order/paymentSituation`, {
    method: 'POST',
    body: params,
  });
}
export function memberRecruitment(params) {
  return request(`/api/chart/develop/memberRecruitment`, {
    method: 'POST',
    body: params,
  });
}
export function getMemTotal(params) {
  return request(`/api/chart/mem/getMemTotal`, {
    method: 'POST',
    body: params,
  });
}
export function feedbackList(params) {
  return request(`/api/problem/feedback/getList`, {
    method: 'POST',
    body: params,
  });
}


export function exportYearReport(params) {
  return request(`/api/chart/exportYearReport`, {
    method: 'POST',
    body: params,
  });
}

export function getYearReport(params) {
  return request(`/api/chart/getYearReport`, {
    method: 'POST',
    body: params,
  });
}

export function getIndexDataPegging(params) {
  return request(`/api/chart/getIndexDataPegging`, {
    method: 'POST',
    body: params,
  });
}

export function getIndexData(params) {
  return request(`/api/chart/getIndexData`, {
    method: 'POST',
    body: params,
  });
}
export function getIndexCount(params) {
  return request(`/api/chart/getIndexCount`, {
    method: 'POST',
    body: params,
  });
}

export function getAnnouncements(params) {
  return request(`/api/notice/getAnnouncements?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
export function read(params) {
  return request(`/api/notice/read?${qs.stringify(params)}`, {
    method: 'Get',
  });
}
// export function getOrgLevelList(params) {
//   return request(`/api/mem/getMemOrgList?${qs.stringify(params)}`,{
//     method:'Get',
//   });
// }
// export function findByNameAndIdcard(params) {
//   return request(`/api/mem/findByNameAndIdcard?${qs.stringify(params)}`,{
//     method:'Get',
//   });
// }
// // 转正
// export function becomeFullMem(params) {
//   return request(`/api/mem/becomeFullMem`,{
//     method:'POST',
//     body:params,
//   });
// }
// // 延长预备期
// export function prolongationMem(params) {
//   return request(`/api/mem/prolongationMem`,{
//     method:'POST',
//     body:params,
//   });
// }
// // 撤销延长预备期
// export function repealProlongationMem(params) {
//   return request(`/api/mem/repealProlongationMem`,{
//     method:'POST',
//     body:params,
//   });
// }
//
// // 根据人员获取职务信息列表
// export function getListByMemCode(params) {
//   return request(`/api/unit/committee/getListByMemCode?${qs.stringify(params)}`,{
//     method:'Get',
//   });
// }
//
// // 根据人员获取职务信息列表
// export function partyPosition(params) {
//   return request(`/api/committee/getListByMemCode?${qs.stringify(params)}`,{
//     method:'Get',
//   });
// }

// 帮助手册
export function help(params) {
  return request(`/api/help`, {
    method: 'Get',
  });
}

// 消息提醒-列表
export function messageList(params) {
  return request(`/api/message/list`, {
    method: 'POST',
    body: params,
  });
}
// 消息提醒-查看
export function messageView(params) {
  return request(`/api/message/cat`, {
    method: 'POST',
    body: params,
  });
}
// 消息提醒-忽略
export function messageIgnore(params) {
  return request(`/api/message/ignore`, {
    method: 'POST',
    body: params,
  });
}

// 贵州综合党务系统首页，显示数据更新时间 接口返回
export function lastUpdateTime(params) {
  return request(`/api/data/lastUpdateTime`, {
    method: 'POST',
    body: params,
  });
}

//数据统计 后三项  王国超反查接口
export function getIndexDataCountPegging(params) {
  return request(`/api/chart/getIndexDataCountPegging`, {
    method: 'POST',
    body: params,
  });
}
//数据统计 前三项   
//共有党员总数
export function getIndexDataPeggingList(params) {
  return request(`/api/chart/getIndexDataPeggingList`, {
    method: 'POST',
    body: params,
  });
}

export async function exportIndexDataCountPegging(params) {
  return request('/api/chart/exportIndexDataCountPegging', {
    method: 'POST',
    body: params
  }, 'file');
}

export async function exportData(params) {
  return request('/api/data/index/exportData', {
    method: 'POST',
    body: params
  }, 'file');
}

