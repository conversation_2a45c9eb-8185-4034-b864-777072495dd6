import React, { Fragment, useEffect, useImperativeHandle, useRef, useState } from 'react';
import style from './index.less';
import NowOrg from '@/components/NowOrg';
import { Button, Form, Alert, Input, Row, Col, Select, Radio, InputNumber, Switch, Modal, Card } from 'antd';
import ListTable from '@/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import SpinProgress from '@/components/SpinProgress';
import { validateLength } from '@/utils/formValidator';
import {
    findDictCodeName,
    unixMoment,
    timeSort,
    getIdCardInfo,
    formLabel,
    correctIdcard,
} from '@/utils/method';
import moment from 'moment'
import Date from '@/components/Date';
import DictSelect from 'src/components/DictSelect';
import DictTreeSelect from 'src/components/DictTreeSelect';
import { selectadd } from '@/pages/[unit]/services/index';
import tip from '@/components/Tip';
import MemSelect from '@/components/MemSelect';
import WhiteSpace from '@/components/WhiteSpace';

const Option = Select.Option;
const RadioGroup = Radio.Group;
const { TextArea } = Input;
const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 11 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 11 },
    },
};
const formItemLayout1 = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
    },
};
const formItemLayout2 = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
};
const formItemLayout3 = {
    labelCol: { span: 20 },
    wrapperCol: { span: 4 },
};
const formItemLayoutLong = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 12 },  // 增加label的宽度
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 },
    },
};
const index: any = (props, ref) => {
    console.log(props, 'ppppp')
    const {
        unit: { basicInfo: bs } = {}
    } = props;


    const progressRef: any = useRef();
    const [form] = Form.useForm();
    const [basicInfo, setBasicInfo]: any = useState({});
    const [d022CodeDisabled, setD022CodeDisabled]: any = useState([]);
    const [visible, setVisible] = useState(false);
    const [unitMem, setUnitMem] = useState(false)
    const [doubleFirst, setDoubleFirst] = useState(false)
    const [orgMem, setOrgMem] = useState(false)
    const [cadreMem, setCadreMem] = useState(false)
    const [workMem, setWorkMem] = useState(false)
    const [reserveMem, setReserveMem] = useState(false)
    const [title, setTitle] = useState('新增');
    const [showMemSelect, setShowMemSelect] = useState(false);
    const [isWorkVillageTag, setIsWorkVillageTag]: any = useState(0);
    const [isreserveMemHasLeadersHelpPeople, setIsreserveMemHasLeadersHelpPeople]: any = useState(0);

  const dictTreeSelectref = useRef();
    const politicsCoderef = useRef();
    const d07Coderef = useRef();
    const d022CodeRef = useRef();
    const memSelectRef = useRef();
    const [confirmLoading, setConfirmLoading] = useState(false);

    useImperativeHandle(ref, () => ({
        open: (val, type) => {
            console.log('open.val====', val, type);

            if (type === 'add') {
                setTitle('新增')
                setBasicInfo({ d04Code: bs.d04Code })
                setTimeout(() => {
                    form.setFieldsValue({
                        isReserveMem: false,
                        isWorkMem: false,
                        isCadreMem: false,
                        isOrgMem: false,
                        isUnitMem: false,
                    });
                    // 同时设置状态
                    setReserveMem(false);
                    setWorkMem(false);
                    setCadreMem(false);
                    setOrgMem(false);
                    setUnitMem(false);
                }, 100);
            }
            if (type === 'edit') {
                setTitle('编辑')
                if (val.isUnitMem == 1) {
                    setUnitMem(true)
                } else {
                    setUnitMem(false)
                }
                if (val.isDoubleFirst == 1) {
                    setDoubleFirst(true)
                } else {
                    setDoubleFirst(false)
                }
                if (val.isOrgMem == 1) {
                    setOrgMem(true)
                } else {
                    setOrgMem(false)
                }
                if (val.isCadreMem == 1) {
                    setCadreMem(true)
                } else {
                    setCadreMem(false)
                }
                if (val.isWorkMem == 1) {
                    setWorkMem(true)
                } else {
                    setWorkMem(false)
                }
                if (val.isReserveMem == 1) {
                    setReserveMem(true)
                } else {
                    setReserveMem(false)
                }
                if (val.memTypeCode == '1') {
                    setShowMemSelect(true)
                } else {
                    setShowMemSelect(false)
                }
                setIsWorkVillageTag(val.reserveMemIsWorkVillage ?? 0)
                if (val.d89Code && typeof (val.d89Code) == 'string') {
                    val.d89Code = val.d89Code.split(',')
                }
                // 根据 是否县乡领导班子成员帮带人 判断是否显示 帮带人单位、帮带人姓名
                if (val.reserveMemHasLeadersHelpPeople == 1) {
                    setIsreserveMemHasLeadersHelpPeople(1)  
                }else{
                    setIsreserveMemHasLeadersHelpPeople(0)
                }
                setBasicInfo(prev => ({ ...prev, ...val, type }))
                const obj = {
                    ...val,
                    birthday: val.birthday ? moment(val.birthday) : undefined,
                    cadreMemStartDate: val.cadreMemStartDate ? moment(val.cadreMemStartDate) : undefined,
                    cadreMemResidentDate: val.cadreMemResidentDate ? moment(val.cadreMemResidentDate) : undefined,
                    startTime: val.startTime ? moment(val.startTime) : undefined,
                    endTime: val.endTime ? moment(val.endTime) : undefined,
                    isReserveMem: val['isReserveMem'] == 1 ? true : false,
                    isWorkMem: val['isWorkMem'] == 1 ? true : false,
                    isCadreMem: val['isCadreMem'] == 1 ? true : false,
                    isOrgMem: val['isOrgMem'] == 1 ? true : false,
                    isUnitMem: val['isUnitMem'] == 1 ? true : false,
                }
                form.setFieldsValue(obj)
            }
            setVisible(true)
        },
        getProgress: (val) => {
            progressRef.pageNum.getProgress(val);
        },
        search: (val, other = {}) => {
        },
        restForm: () => {
            restForm();
        }
    }));

    const restForm = () => {
        form.resetFields();
    };
    const nameValidator = (rule, value, callback) => {
        console.log('nameValidator===');

        // let reg = /(^[\u4e00-\u9fa5]{1}[\u4e00-\u9fa5\.·。]{0,18}[\u4e00-\u9fa5]{1}$)|(^[a-zA-Z]{1}[a-zA-Z\s]{0,18}[a-zA-Z]{1}$)/
        let reg = /(^[\u4e00-\u9fa5]{1}[\u4e00-\u9fa5·]{0,18}[\u4e00-\u9fa5]{1}$)/;
        // let reg = /^[\u4e00-\u9fa5]+$/;
        console.log('test===', reg.test(value));

        if (reg.test(value)) {
            // console.log('通过');
            // validateLength([rule, value, callback], 16, 50);
            callback();
        } else {
            callback('只能是汉字，不能有空格或特殊字符');
        }
    };
    const getIDinfo = (e) => {
        const { target: { value = '' } = {} } = e || {};
        let info = getIdCardInfo(value);
        if (!_isEmpty(value) && info !== 'Error') {
            // 大陆身份证自动赋值，港澳台身份证不自动赋值
            if (`${value}`.length === 18 || `${value}`.length === 15) {

                form.setFieldsValue({
                    sexCode: info[2] === '女' ? '0' : '1',
                    birthday: info[1] ? moment(info[1], 'YYYY-MM-DD') : undefined,
                    d48Code: info[3],
                    politicsCode: undefined,
                })
                // this['politicsCode'].clearAll();
            }
        }
    };
    const validatorIdcard = async (rule, value, callback) => {
        if (_isEmpty(value)) {
            callback('身份证必填');
        }
        // if (value && value.length !== 18 && process.env.idCheck != 'false') {
        //   callback('身份证应该为18位');
        // }
        if (getIdCardInfo(value) === 'Error') {
            callback('身份证格式错误,请核对身份证图片');
        } else {
            // let fieldValue = this.props.form.getFieldValue('name');
            // let res =await geitCard({idCard:value,name:fieldValue});
            callback();
        }
    };
    const disabledTomorrow = (current) => {
        return current && current > moment().endOf('day');
    };

    const onFinish = async (val) => {
        console.log('val=====', val);
        // return
        setConfirmLoading(true);
        val['memTypeName'] = val['memTypeCode'] == '1' ? '是' : '否';
        val['sexName'] = val['sexCode'] == '1' ? '男' : '女';
        ['orgMemHasMiddleManagement'].forEach(key => {
            if (val[key]) {
                val[key] = 1;
            } else {
                val[key] = 0;
            }
        })
        val = unixMoment(
            [
                'birthday',
                'startTime',
                'endTime',
                'unitMemStartDate',
                'orgMemStartDate',
                'cadreMemStartDate',
                'cadreMemResidentDate'
            ],
            val,
        );
        val['unitName'] = props.unit.basicInfo.name;
        val['unitCode'] = props.unit.basicInfo.code;
        if (val['memCode'] && typeof val['memCode'] === 'object') {
            val['memName'] = val['memCode'][0]['name'];
            val['memCode'] = val['memCode'][0]['code']
        }
        if (_isArray(val['d89Code'])) {
            val['d89Code'] = val['d89Code'].join(',')
        }
        if (_isArray(val['orgMemD022Code'])) {
            val['orgMemD022Code'] = val['orgMemD022Code'].join(',')
        }
        val['isUnitMem'] = val['isUnitMem'] ? 1 : 0
        val['isOrgMem'] = val['isOrgMem'] ? 1 : 0
        val['isCadreMem'] = val['isCadreMem'] ? 1 : 0
        val['isWorkMem'] = val['isWorkMem'] ? 1 : 0
        val['isReserveMem'] = val['isReserveMem'] ? 1 : 0
        // 是否县乡领导班子成员帮带人 选否时，清空 帮带人单位、帮带人姓名
        if( val['reserveMemHasLeadersHelpPeople'] === 0 ){
            val['reserveMemHelpUnit'] = ''
            val['reserveMemHelpMem'] = ''
        }
        const { code = '' } = await selectadd({
            data: {
                ...val,
                code: basicInfo.code,
            }
        });
        setConfirmLoading(false);
        if (code == 0) {
            tip.success('操作提示', '操作成功');
            onCancel()
            props.upList && props.upList(val);
        }

    }
    const onCancel = () => {
        setConfirmLoading(false);
        setVisible(false);
        restForm();
        setBasicInfo({});
        setShowMemSelect(false);
        setReserveMem(false);
        setWorkMem(false);
        setCadreMem(false);
        setOrgMem(false);
        setDoubleFirst(false);
        setUnitMem(false);
    }
    useEffect(() => {
        console.log(props, 'pppppppppppppppp');
        //  form.setFieldsValue({
        //         unitMemD25Name:props.name,
        //         unitMemD25IdCard:props.idCard
        //     })
    }, []);

    const renderbzcy = () => {
        return (
            <Card title="单位班子" style={{ width: '100%', marginTop: 10, boxShadow: '0 1px 4px rgba(0, 0, 0, 0.15)' }}>
                <Row>
                    <Col span={12}>
                        <Form.Item
                            noStyle
                            name='unitMemD25Name'
                            style={{ display: 'none' }}
                        >
                            <Input style={{ display: 'none' }} disabled={basicInfo.type == 'edit'} />
                        </Form.Item>
                        <Form.Item label="行政职务" name="unitMemD25Code"
                            rules={[
                                { required: true, message: '请输入' },
                            ]}
                        >
                            <DictTreeSelect
                                initValue={basicInfo['unitMemD25Code']}
                                backType={'object'}
                                codeType={'dict_d25'}
                                placeholder="请选择"
                                onChange={e => {
                                    form.setFieldsValue({
                                        unitMemD25Code: e.key,
                                        unitMemD25Name: e.name,
                                    });
                                }}
                                disabled={basicInfo.type == 'edit'}
                                filter={(data) => {
                                    const { d04Code } = basicInfo
                                    console.log(d04Code, 'd04Coded04Coded04Code')
                                    // 921城市社区 922乡镇社区 923村 4企业 9121乡 9122镇
                                    if (d04Code === '9121') {
                                        return data.filter(it => it.key === '2');
                                    }
                                    // if(d04Code === '9122'){
                                    //   return data.filter(it=>it.key==='3');
                                    // }
                                    if (d04Code === '923') {
                                        return data.filter(it => it.key === '4');
                                    }
                                    if (d04Code === '921' || d04Code === '922') {
                                        return data.filter(it => it.key === '5');
                                    }

                                    // 单位类型911 ，政府班子行政职务只显示 1开头的
                                    // 单位类型9121 ，政府班子行政职务只显示 2开头的
                                    // 单位类型9122 ，政府班子行政职务只显示 3开头的
                                    if (d04Code === '911') {
                                        return data.filter(it => it.key.startsWith('1'));
                                    }
                                    if (d04Code === '9121') {
                                        return data.filter(it => it.key.startsWith('2'));
                                    }
                                    if (d04Code === '9122') {
                                        return data.filter(it => it.key.startsWith('3'));
                                    }
                                    return data;
                                }}
                                parentDisable={true} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="决定或批准任职的文号" name="unitMemFileNumber"
                            rules={[
                                { required: false, message: '请输入' },
                            ]}
                        >
                            <Input.TextArea
                                rows={1}
                                maxLength={50}
                                placeholder="决定或批准任职的文号"
                                disabled={basicInfo.type == 'edit'}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="行政任职起始日期" name="unitMemStartDate"
                            rules={[
                                { required: true, message: '请输入' },
                            ]}
                        >
                            <Date
                                disabled={basicInfo.type == 'edit'}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="报酬（万元/年）" name="unitMemReward"
                            rules={[
                                { required: true, message: '请输入' },
                            ]}
                        >
                            <InputNumber
                                style={{ width: '100%' }}
                                min={0}
                                max={50}
                                precision={2}
                                disabled={basicInfo.type == 'edit'}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否参加城镇职工养老保险" name="unitMemEndowmentInsuranceForUrbanEmployees"
                            rules={[
                                { required: true, message: '请输入' },
                            ]}
                            // labelCol={{
                            //     style: {
                            //         whiteSpace: 'normal',
                            //         height: 'auto',
                            //         lineHeight: '20px',
                            //         width: '180px'  // 控制label宽度
                            //     }
                            // }}
                        >
                            <Select style={{ width: '100%' }}
                                disabled={basicInfo.type == 'edit'}
                            >
                                <Select.Option value={1}>是</Select.Option>
                                <Select.Option value={0}>否</Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            noStyle
                            name='unitMemD0121Name'
                            style={{ display: 'none' }}
                        >
                            <Input style={{ display: 'none' }} disabled={basicInfo.type == 'edit'} />
                        </Form.Item>
                        <Form.Item label="人员来源" name="unitMemD0121Code"
                            rules={[
                                { required: true, message: '请输入' },
                            ]}
                        >
                            <DictTreeSelect
                                initValue={_isEmpty(basicInfo) ? undefined : basicInfo['unitMemD0121Code']}
                                backType={'object'}
                                codeType={'dict_d121'}
                                placeholder="请选择"
                                parentDisable={true}
                                onChange={e => {
                                    form.setFieldsValue({
                                        unitMemD0121Code: e.key,
                                        unitMemD0121Name: e.name,
                                    });
                                }}
                                disabled={basicInfo.type == 'edit'}
                            />
                        </Form.Item>
                    </Col>

                    {/* <Col span={12}>
                        <Form.Item
                            label={formLabel('是否在任')}
                            name={'unitMemIsIncumbent'}
                            rules={[{ required: true, message: '请选择' }]}
                        >
                            <Switch checkedChildren="是" unCheckedChildren="否" />
                        </Form.Item>
                    </Col> */}
                    {
                        !showMemSelect &&
                        <Col span={12}>
                            <Form.Item
                                noStyle
                                name='unitMemD26Name'
                                style={{ display: 'none' }}
                            >
                                <Input style={{ display: 'none' }} disabled={basicInfo.type == 'edit'} />
                            </Form.Item>
                            <Form.Item
                                label={formLabel('兼职情况')}
                                name={'unitMemD26Code'}
                                rules={[{ required: false, message: '请选择' }]}
                            >
                                <DictTreeSelect
                                    initValue={basicInfo['unitMemD26Code']}
                                    backType={'object'}
                                    parentDisable={true}
                                    codeType={'dict_d26'}
                                    placeholder="请选择"
                                    onChange={e => {
                                        form.setFieldsValue({
                                            unitMemD26Code: e.key,
                                            unitMemD26Name: e.name,
                                        });
                                    }}
                                    disabled={basicInfo.type == 'edit'}
                                />
                            </Form.Item>
                        </Col>
                    }

                    <Col span={24}>
                        <Form.Item {...formItemLayout1} label="行政职务说明" name="unitMemRemark"
                            rules={[
                                { required: false, message: '请输入' },
                            ]}
                        >
                            <TextArea rows={4} placeholder={'党内职务说明'} maxLength={200}
                                disabled={basicInfo.type == 'edit'}
                            />
                        </Form.Item>
                    </Col>
                </Row>
            </Card>
        )
    }
    const renderdzzbzcy = () => {
        return (
            <Card title="党组织班子" style={{ width: '100%', marginTop: 10, boxShadow: '0 1px 4px rgba(0, 0, 0, 0.15)' }}>
                <Row>
                    <Col span={12}>
                        {/* <Form.Item
                        noStyle
                        name='orgMemD022Name'
                        style={{ display: 'none' }}
                    >
                        <Input style={{ display: 'none' }} />
                    </Form.Item> */}
                        <Form.Item
                            label={formLabel('党内职务')}
                            name="orgMemD022Code"
                            rules={[
                                { required: true, message: '请选择党内职务' },
                            ]}
                        >
                            <DictTreeSelect
                                ref={d022CodeRef}
                                treeCheckable={true}
                                disabled={basicInfo.type == 'edit'}
                                initValue={basicInfo['orgMemD022Code'] ? basicInfo['orgMemD022Code'].split(',') : form.getFieldValue('orgMemD022Code') ? form.getFieldValue('orgMemD022Code') : undefined}
                                // backType={'object'}
                                codeType={'dict_d22'}
                                placeholder="请选择"
                                //   parentDisable={(basicInfo.d01Code == '61' || `${basicInfo.d01Code}`.startsWith('2')) ? false : true}
                                itemsDisabled={d022CodeDisabled}
                                //   extendProps={{ treeCheckStrictly: basicInfo.d01Code == '61' || `${basicInfo.d01Code}`.startsWith('2') }}
                                onChange={
                                    (e) => {
                                        e = e.map(it => {
                                            if (typeof it != 'string') {
                                                return it.value
                                            }
                                            return it
                                        });
                                        // form.setFieldsValue({
                                        //     orgMemD022Code: e.key,
                                        //     orgMemD022Name: e.name,
                                        // });
                                        if (e && e.length > 0) {
                                            if (basicInfo.d01Code == '61' || `${basicInfo.d01Code}`.startsWith('2')) {
                                                let disabled: any = [];
                                                if (e.includes('1')) {
                                                    disabled = ['2', '3', '31', '32', '33', '34', '35'];
                                                }
                                                if (e.includes('2')) {
                                                    disabled = [...disabled, '1']
                                                }
                                                if (e.includes('3')) {
                                                    disabled = [...disabled, '1', '31', '32', '33', '34', '35']
                                                }
                                                if (e.find(it => it.startsWith('3')) && !e.includes('3')) {
                                                    disabled = [...disabled, '1', '3']
                                                }
                                                setD022CodeDisabled(disabled)
                                            } else {
                                                if (e.includes('1')) {
                                                    setD022CodeDisabled(['2', '3', '31', '32', '33', '34', '35'])
                                                }
                                                if (!e.includes('1')) {
                                                    setD022CodeDisabled(['1'])
                                                }
                                            }
                                        } else {
                                            setD022CodeDisabled([])
                                        }
                                    }
                                }
                            />
                        </Form.Item>
                    </Col>

                    <Col span={12}>
                        <Form.Item
                            label={formLabel('党内任职起始日期')}
                            name={"orgMemStartDate"}
                            rules={[{ required: true, message: '请选择党内任职起始日期' }]}
                        >
                            <Date disabled={basicInfo.type == 'edit'} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label={formLabel('决定或批准任职的文号')}
                            name="orgMemFileNumber"
                            rules={[{ required: false, message: '请输入决定或批准任职的文号' }]}
                        >
                            <Input.TextArea placeholder="决定或批准任职的文号" disabled={basicInfo.type == 'edit'} maxLength={50} />
                        </Form.Item>
                    </Col>
                    {/* <Col span={12}>
                    <Form.Item
                        label={'是否中层管理人员'}
                        name={"orgMemHasMiddleManagement"}
                        rules={[{ required: true, message: '请选择是否中层管理人员' }]}
                    >
                        <Switch checkedChildren="是" unCheckedChildren="否" disabled={basicInfo.type == 'edit'} />
                    </Form.Item>
                </Col> */}
                    <Col span={12}>
                        <Form.Item
                            noStyle
                            name='orgMemD121Name'
                            style={{ display: 'none' }}
                        >
                            <Input style={{ display: 'none' }} disabled={basicInfo.type == 'edit'} />
                        </Form.Item>
                        <Form.Item
                            label={'人员来源'}
                            name={'orgMemD121Code'}
                            rules={[{ required: true, message: '请选择人员来源' }]}
                        >
                            <DictTreeSelect initValue={!_isEmpty(basicInfo['orgMemD121Code']) ? basicInfo['orgMemD121Code'] : !_isEmpty(form.getFieldValue('orgMemD121Code')) ? form.getFieldValue('orgMemD121Code') : undefined}
                                backType={'object'}
                                codeType={'dict_d121'}
                                placeholder="请选择"
                                parentDisable={true}
                                disabled={basicInfo.type == 'edit'}
                                onChange={e => {
                                    form.setFieldsValue({
                                        orgMemD121Code: e.key,
                                        orgMemD121Name: e.name,
                                    });
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label={'是否参加县级集中轮训'}
                            name={'orgMemHasPartTraining'}
                            rules={[{ required: true, message: '请选择是否参加县级集中轮训' }]}
                        >
                            <Select disabled={basicInfo.type == 'edit'} >
                                <Select.Option value={1}>是</Select.Option>
                                <Select.Option value={0}>否</Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label={'报酬（万元/年）'}
                            name={'orgMemReward'}
                            rules={[{ required: true, message: '请输入报酬（万元/年）' }]}
                        >
                            <InputNumber disabled={basicInfo.type == 'edit'} style={{ width: '100%' }} min={0} max={50} precision={2} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item label="是否参加城镇职工养老保险" name="orgMemEndowmentInsuranceForUrbanEmployees"
                            rules={[
                                { required: true, message: '请输入' },
                            ]}
                            // labelCol={{
                            //     style: {
                            //         whiteSpace: 'normal',
                            //         height: 'auto',
                            //         lineHeight: '20px',
                            //         width: '180px'  // 控制label宽度
                            //     }
                            // }}
                        >
                            <Select style={{ width: '100%' }}
                                disabled={basicInfo.type == 'edit'}
                            >
                                <Select.Option value={1}>是</Select.Option>
                                <Select.Option value={0}>否</Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item
                            label={formLabel('党内职务说明')}
                            name="orgMemDutyExplain"
                            // @ts-ignore
                            {...formItemLayout1}
                            rules={[
                                { required: false, message: '请输入党内职务说明' },
                            ]}
                        >
                            <TextArea rows={4} placeholder={'党内职务说明'} disabled={basicInfo.type == 'edit'} maxLength={200} />
                        </Form.Item>
                    </Col>
                </Row>
            </Card>

        )
    }
    const renderdzcgb = () => {
        return (
            <Card title="驻村干部" style={{ width: '100%', marginTop: 10, boxShadow: '0 1px 4px rgba(0, 0, 0, 0.15)' }}>
                <Row>
                    <Col span={12}>
                        <Form.Item
                            noStyle
                            name='cadreMemD140Name'
                            style={{ display: 'none' }}
                        >
                            <Input style={{ display: 'none' }} disabled={basicInfo.type == 'edit'} />
                        </Form.Item>
                        <Form.Item
                            label={formLabel('人员身份')}
                            name={'cadreMemD140Code'}
                            rules={[{ required: true, message: '请选择人员身份' }]}
                        >
                            <DictTreeSelect
                                backType={'object'}
                                codeType={'dict_d140'}
                                initValue={basicInfo['cadreMemD140Code']}
                                placeholder="请选择"
                                parentDisable={true}
                                disabled={basicInfo.type == 'edit'}
                                onChange={e => {
                                    form.setFieldsValue({
                                        cadreMemD140Code: e.key,
                                        cadreMemD140Name: e.name,
                                    });
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            noStyle
                            name='cadreMemD141Name'
                            style={{ display: 'none' }}
                        >
                            <Input style={{ display: 'none' }} disabled={basicInfo.type == 'edit'} />
                        </Form.Item>
                        <Form.Item
                            label={formLabel('人员来源')}
                            name={'cadreMemD141Code'}
                            rules={[{ required: true, message: '请选择人员来源' }]}
                        >
                            <DictTreeSelect
                                backType={'object'}
                                codeType={'dict_d141'}
                                initValue={basicInfo['cadreMemD141Code']}
                                placeholder="请选择"
                                disabled={basicInfo.type == 'edit'}
                                parentDisable={true}
                                onChange={e => {
                                    form.setFieldsValue({
                                        cadreMemD141Code: e.key,
                                        cadreMemD141Name: e.name,
                                    });
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label={formLabel('驻村开始时间 ')}
                            name={'cadreMemStartDate'}
                            rules={[{ required: true, message: '驻村开始时间' }]}
                        >
                            <Date disabled={basicInfo.type == 'edit'} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label={formLabel('预计驻村结束时间')}
                            name={'cadreMemResidentDate'}
                            rules={[{ required: true, message: '预计驻村结束时间' }]}
                        >
                            <Date isDefaultEnd={false} disabled={basicInfo.type == 'edit'} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            label={formLabel('派出单位名称及职务')}
                            name={'cadreMemDispatchPosition'}
                            rules={[{ required: true, message: '派出单位名称及职务' }]}
                        >
                            <Input placeholder="派出单位名称及职务" disabled={basicInfo.type == 'edit'} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            noStyle
                            name='cadreMemD197Name'
                            style={{ display: 'none' }}
                        >
                            <Input style={{ display: 'none' }} />
                        </Form.Item>
                        <Form.Item
                            label={formLabel('选派层级')}
                            name={'cadreMemD197Code'}
                            rules={[{ required: true, message: '请选择' }]}
                        >
                            <DictSelect
                                backType={'object'}
                                codeType={'dict_d197'}
                                initValue={basicInfo['cadreMemD197Code']}
                                placeholder="请选择"
                                disabled={basicInfo.type == 'edit'}
                                onChange={e => {
                                    form.setFieldsValue({
                                        cadreMemD197Code: e.key,
                                        cadreMemD197Name: e.name,
                                    });
                                }}
                            />
                        </Form.Item>
                    </Col>
                </Row>
            </Card>

        )
    }
    const rendersq = () => {
        return (
            <Card title="村（社区）工作者" style={{ width: '100%', marginTop: 10, boxShadow: '0 1px 4px rgba(0, 0, 0, 0.15)' }}>
                <Row>
                    <Col span={12}>
                        <Form.Item
                            name='workMemHasLeadersHelpPeople'
                            label="是否县乡领导班子成员帮带人"
                            rules={[{ required: true, message: '是否县乡领导班子成员帮带人' }]}
                            // labelCol={{
                            //     style: {
                            //         whiteSpace: 'normal',
                            //         height: 'auto',
                            //         lineHeight: '20px',
                            //         width: '180px'  // 控制label宽度
                            //     }
                            // }}
                        >
                            <Select disabled={basicInfo.type == 'edit'} style={{ width: '100%' }} >
                                <Select.Option value={1}>是</Select.Option>
                                <Select.Option value={0}>否</Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item name='workMemSubsidies'
                            // initialValue={_isEmpty(dataInfo.phone)? undefined :dataInfo.phone}
                            label="到村任职补助经费（万元）"
                            rules={[{ required: false, message: '到村任职补助经费（万元）' }]}
                        >
                            <InputNumber disabled={basicInfo.type == 'edit'} style={{ width: "100%" }} step='0.01' min={0} max={30} precision={2} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item name='workMemHasPartyWork'
                            label="是否专职党务工作者"
                            rules={[{ required: true, message: '是否专职党务工作者' }]}
                        >
                            <Select disabled={basicInfo.type == 'edit'} style={{ width: '100%' }}>
                                <Select.Option value={1}>是</Select.Option>
                                <Select.Option value={0}>否</Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item name='workMemHasTwoOneMember'
                            label="是否推荐为两代表一委员"
                            rules={[{ required: true, message: '是否推荐为两代表一委员' }]}
                            // labelCol={{
                            //     style: {
                            //         whiteSpace: 'normal',
                            //         height: 'auto',
                            //         lineHeight: '20px',
                            //         width: '180px'  // 控制label宽度
                            //     }
                            // }}
                        >
                            <Select disabled={basicInfo.type == 'edit'} style={{ width: '100%' }}>
                                <Select.Option value={1}>是</Select.Option>
                                <Select.Option value={0}>否</Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            noStyle
                            name='workMemD116Name'
                            style={{ display: 'none' }}
                        >
                            <Input disabled={basicInfo.type == 'edit'} style={{ display: 'none' }} />
                        </Form.Item>
                        <Form.Item name='workMemD116Code'
                            label="录用来源"
                            rules={[{ required: true, message: '录用来源' }]}
                        >
                            <DictTreeSelect backType={'object'}
                                codeType={'dict_d116'}
                                disabled={basicInfo.type == 'edit'}
                                onChange={e => {
                                    form.setFieldsValue({
                                        workMemD116Code: e.key,
                                        workMemD116Name: e.name,
                                    });
                                }}
                                initValue={basicInfo['workMemD116Code']}
                                placeholder="请选择" parentDisable={true} />
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            noStyle
                            name='workMemD143Name'
                            style={{ display: 'none' }}
                        >
                            <Input disabled={basicInfo.type == 'edit'} style={{ display: 'none' }} />
                        </Form.Item>
                        <Form.Item
                            name={'workMemD143Code'}
                            rules={[{ required: true, message: '岗位' }]}
                            label={'岗位'}>
                            <DictTreeSelect backType={'object'}
                                codeType={'dict_d143'}
                                onChange={e => {
                                    form.setFieldsValue({
                                        workMemD143Name: e.name,
                                        workMemD143Code: e.key,
                                    });
                                }}
                                initValue={basicInfo['workMemD143Code']}
                                placeholder="请选择" parentDisable={true}
                                disabled={basicInfo.type == 'edit'}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={24}>
                        <Form.Item
                            {...formItemLayout1}
                            name={'workMemRemark'}
                            rules={[{ required: false, message: '备注' }]}
                            label={'备注'}>
                            <Input.TextArea rows={4} placeholder="请输入" disabled={basicInfo.type == 'edit'} maxLength={300} />
                        </Form.Item>
                    </Col>
                </Row>
            </Card>
        )
    }
    const renderhbgb = () => {
        return (
            <Card title="村（社区）后备干部" style={{ width: '100%', marginTop: 10, boxShadow: '0 1px 4px rgba(0, 0, 0, 0.15)' }}>
                <Row>

                    <Col span={12}>
                        <Form.Item name='reserveMemHasLeadersHelpPeople'
                            label="是否县乡领导班子成员帮带人"
                            rules={[{ required: true, message: '是否县乡领导班子成员帮带人' }]}
                            // labelCol={{
                            //     style: {
                            //         whiteSpace: 'normal',
                            //         height: 'auto',
                            //         lineHeight: '20px',
                            //         width: '180px'  // 控制label宽度
                            //     }
                            // }}
                        >
                            <Select disabled={basicInfo.type == 'edit'} style={{ width: '100%' }} onChange={(e) => {
                                setIsreserveMemHasLeadersHelpPeople(e)
                                if( e == 0){
                                    form.setFieldsValue({
                                        reserveMemHelpUnit: '',// 清空帮带人单位
                                        reserveMemHelpMem: '',// 清空帮带人姓名
                                    })
                                }
                            }}>
                                <Select.Option value={1}>是</Select.Option>
                                <Select.Option value={0}>否</Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item name='reserveMemSubsidies'
                            // initialValue={_isEmpty(dataInfo.phone)? undefined :dataInfo.phone}
                            label="到村任职补助经费（万元）"
                            rules={[{ required: false, message: '到村任职补助经费（万元）' }]}
                        >
                            <InputNumber disabled={basicInfo.type == 'edit'} style={{ width: "100%" }} step='0.01' min={0} max={30} precision={2} />
                        </Form.Item>
                    </Col>

                    <Col span={12}>
                        <Form.Item name='reserveMemIsWorkVillage'
                            // initialValue={_isEmpty(dataInfo.phone)? undefined :dataInfo.phone}
                            label="是否在村工作"
                            rules={[{ required: true, message: '是否在村工作' }]}
                        >
                            <Select onChange={e => { setIsWorkVillageTag(e) 
                                  form.setFieldsValue({
                                    reserveMemD143Name: '',
                                        reserveMemD143Code: '',
                                  });
                                  dictTreeSelectref.current.clearAll()
                            }} disabled={basicInfo.type == 'edit'} style={{ width: '100%' }} >
                                <Select.Option value={1}>是</Select.Option>
                                <Select.Option value={0}>否</Select.Option>
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item
                            noStyle
                            name='reserveMemD143Name'
                            style={{ display: 'none' }}
                        >
                            <Input disabled={basicInfo.type == 'edit'} style={{ display: 'none' }} />
                        </Form.Item>
                        <Form.Item
                            name={'reserveMemD143Code'}
                            rules={[{ required: true, message: '岗位' }]}
                            label={'岗位'}>
                            <DictTreeSelect
                                ref={dictTreeSelectref}
                                disabled={basicInfo.type == 'edit'}
                                backType={'object'}
                                codeType={'dict_d143'}
                                onChange={e => {
                                    form.setFieldsValue({
                                        reserveMemD143Name: e.name,
                                        reserveMemD143Code: e.key,
                                    });
                                }}
                                initValue={basicInfo['reserveMemD143Code']}
                                filter={(data) => {
                                    let isWorkVillages = form.getFieldValue('isWorkVillage')
                                    let _data = data.map(it => {
                                        if (it.key == '3') {
                                            it.name = `其他村（社区）干部`
                                        }
                                        return it
                                    })
                                    if (isWorkVillageTag == 1) {
                                        return data.filter(i => i.key != '0')
                                    }
                                    if (isWorkVillageTag == 0) {
                                        return data.filter(i => i.key == '0')
                                    }
                                    return data
                                }}
                                placeholder="请选择" parentDisable={true} />
                        </Form.Item>
                    </Col>
                    {
                        isreserveMemHasLeadersHelpPeople == 1 &&
                        <>
                            <Col span={12}>
                                <Form.Item name='reserveMemHelpUnit'
                                    label="帮带人单位"
                                    rules={[{ required: true, message: '帮带人单位' }]}
                                >
                                    <Input disabled={basicInfo.type == 'edit'} maxLength={100} />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item name='reserveMemHelpMem'
                                    label="帮带人姓名"
                                    rules={[{ required: true, message: '帮带人姓名' }]}
                                >
                                    <Input disabled={basicInfo.type == 'edit'} maxLength={100} />
                                </Form.Item>
                            </Col>
                        </>
                    }
                    {
                        isWorkVillageTag == 1 &&
                        <Col span={12}>
                            <Form.Item name='reserveMemNowJob'
                                label="目前就业岗位"
                                rules={[{ required: true, message: '目前就业岗位' }]}
                            >
                                <Input disabled={basicInfo.type == 'edit'} maxLength={100} />
                            </Form.Item>
                        </Col>
                    }

                    <Col span={24}>
                        <Form.Item name='reserveMemRemark'
                            {...formItemLayout1}
                            // initialValue={_isEmpty(dataInfo.phone)? undefined :dataInfo.phone}
                            label="备注"
                            rules={[{ required: false, message: '请输入备注' }]}
                        >
                            <TextArea disabled={basicInfo.type == 'edit'} placeholder={'备注'} rows={3} maxLength={300} />
                        </Form.Item>
                    </Col>
                </Row>
            </Card>
        )
    }
    return (
        <Modal
            destroyOnClose
            title={title + '选调生'}
            visible={visible}
            onOk={() => {
                console.log('onOk.v=====', form.getFieldsValue());

                form.submit()
            }}
            onCancel={onCancel}
            width={1200}
            confirmLoading={confirmLoading}
        >
            <div className={style.selectBox}>
                <Form onFinish={onFinish} form={form} {...formItemLayout}>
                    <Row>
                        <Col span={12}>
                            <Form.Item label="是否党员" name="memTypeCode"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <Select onChange={(e) => {
                                    setShowMemSelect(e == "1")

                                    form.setFieldsValue({
                                        memCode: undefined,
                                        memName: undefined,
                                        memIdcard: undefined,
                                        birthday: undefined,
                                        d89Code: undefined,
                                    })
                                    if (e == '1') {
                                        politicsCoderef.current.clearAll()
                                        setTimeout(() => {
                                            console.log('memSelectRef====', memSelectRef.current);
                                            memSelectRef?.current?.setState({ value: ' ' });
                                        }, 100);
                                    }
                                }} >
                                    <Option value={"1"}>是</Option>
                                    <Option value={"0"}>否</Option>
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            {showMemSelect && (<Fragment>
                                <Form.Item style={{ display: 'none' }} label="姓名" name="memName"
                                    rules={[
                                        { required: false, message: '请输入' },
                                        // { validator: nameValidator },
                                    ]}
                                >
                                    {/* 是否党员：否的时候不需要选择器 */}
                                    <Input placeholder="请输入姓名" />
                                </Form.Item>
                                <Form.Item label="姓名" name="memCode"
                                    rules={[
                                        { required: true, message: '请输入' },
                                        // { validator: nameValidator },
                                    ]}
                                >
                                    {/* 是否党员：否的时候不需要选择器 */}
                                    <MemSelect onChange={(e) => {
                                        console.log('e===', e);

                                        if (!_isEmpty(e[0])) {
                                            const { name = '', code = '', idcard = '', sexCode = '', birthday = '', d89Code = '', d89Name = '', phone, d07Code = '', d07Name = '' } = e[0]
                                            // let d89 = d89Code.split(',')
                                            // let d89n = d89Name.split(',')
                                            // console.log(d89, 'd89============')
                                            form.setFieldsValue({
                                                memName: name,
                                                memCode: code,
                                                memIdcard: idcard,
                                                sexCode: sexCode,
                                                birthday: moment(birthday),
                                                d89Code: ['14'],
                                                d89Name: '中共党员',
                                                phone,
                                                d07Code,
                                                d07Name
                                            })
                                            setBasicInfo({
                                                ...basicInfo,
                                                d89Code: ['14'],
                                                d07Code: d07Code,
                                            })
                                        } else {
                                            setBasicInfo({
                                                ...basicInfo,
                                                d89Code: undefined,
                                                d07Code: undefined,
                                            })
                                            form.setFieldsValue({
                                                memName: undefined,
                                                memCode: undefined,
                                                memIdcard: undefined,
                                                sexCode: undefined,
                                                birthday: undefined,
                                                d89Code: undefined,
                                                d89Name: undefined,
                                                phone: undefined,
                                                d07Code: undefined,
                                                d07Name: undefined
                                            })
                                        }
                                    }} initValue={basicInfo['memName']} placeholder="请选择党员" ref={memSelectRef} />
                                </Form.Item>
                            </Fragment>)}
                            {!showMemSelect && (
                                <Form.Item label="姓名" name="memName"
                                    rules={[
                                        { required: true, message: '请输入' },
                                        { validator: nameValidator },
                                    ]}
                                >
                                    {/* 是否党员：否的时候不需要选择器 */}
                                    <Input placeholder="请输入姓名" />
                                </Form.Item>
                            )}
                        </Col>
                        <Col span={12}>
                            <Form.Item label="性别" name="sexCode"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <RadioGroup>
                                    <Radio value={'1'}>男</Radio>
                                    <Radio value={'0'}>女</Radio>
                                </RadioGroup>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item label="身份证" name="memIdcard"
                                rules={[
                                    { required: true, message: '请输入' },
                                    { validator: validatorIdcard },
                                ]}
                            >
                                <Input placeholder={'请输入身份证'} max={18} onBlur={getIDinfo} />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item label="出生日期" name="birthday"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <Date
                                    // @ts-ignore
                                    disabledDate={disabledTomorrow}
                                    onChange={() => {
                                        // this.props.form.setFieldsValue({
                                        //     politicsCode: undefined,
                                        // });
                                        // this['politicsCode'].clearAll();
                                    }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                noStyle
                                name='d89Name'
                                style={{ display: 'none' }}
                            >
                                <Input style={{ display: 'none' }} />
                            </Form.Item>
                            <Form.Item label="政治面貌" name="d89Code"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <DictSelect
                                    disabled={showMemSelect}
                                    backType={'object'}
                                    // @ts-ignore
                                    ref={politicsCoderef}
                                    initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d89Code']}
                                    codeType={'dict_d89'}
                                    placeholder="请选择"
                                    mode={'multiple'}
                                    key={showMemSelect}
                                    onChange={e => {
                                        console.log(e, 'eeeeeeeeeeeeeeee')
                                        if (e) {
                                            form.setFieldsValue({
                                                d89Code: e.map(i => i.key).join(','),
                                                d89Name: e.map(i => i.name).join(','),
                                            });
                                        } else {
                                            form.setFieldsValue({
                                                d89Code: '',
                                                d89Name: '',
                                            });
                                        }

                                    }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                noStyle
                                name='d07Name'
                                style={{ display: 'none' }}
                            >
                                <Input style={{ display: 'none' }} />
                            </Form.Item>
                            <Form.Item label="学历情况" name="d07Code"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <DictTreeSelect
                                    initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d07Code']}
                                    codeType={'dict_d07'}
                                    placeholder={'当前学历情况'}
                                    // @ts-ignore
                                    ref={d07Coderef}
                                    parentDisable={true}
                                    backType={'object'}
                                    onChange={e => {
                                        form.setFieldsValue({
                                            d07Code: e.key,
                                            d07Name: e.name,
                                        });
                                    }}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item label="电话" name="phone"
                                rules={[
                                    { required: true, message: '请输入' },
                                    { pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: '请输入正确的手机号' }
                                ]}
                            >
                                <Input placeholder={'请输入联系电话'} />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item label="到村任职开始时间" name="startTime"
                                rules={[
                                    { required: true, message: '请输入' },
                                ]}
                            >
                                <Date disabledDate={disabledTomorrow} />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item label="到村任职结束时间（预计）" name="endTime"
                                rules={[
                                    { required: true, message: '请输入' },
                                ]}
                            >
                                <Date isDefaultEnd={false} />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                noStyle
                                name='d144Name'
                                style={{ display: 'none' }}
                            >
                                <Input style={{ display: 'none' }} />
                            </Form.Item>
                            <Form.Item label="选调单位层级" name="d144Code"
                                rules={[
                                    { required: true, message: '请输入' },
                                ]}
                            >
                                <DictTreeSelect
                                    initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d144Code']}
                                    codeType={'dict_d144'}
                                    placeholder={'选调单位层级'}
                                    // @ts-ignore
                                    ref={d07Coderef}
                                    parentDisable={true}
                                    backType={'object'}
                                    onChange={e => {
                                        form.setFieldsValue({
                                            d144Code: e.key,
                                            d144Name: e.name,
                                        });
                                    }}
                                />
                            </Form.Item>
                        </Col>

                        {/* <Col span={12}>
                            <Form.Item label="任职单位名称" name="unitName"
                                rules={[
                                    { required: true, message: '请输入' },
                                ]}
                            >
                                <Input placeholder={'任职单位名称'} />
                            </Form.Item>
                        </Col> */}
                        <Col span={12}>
                            <Form.Item label="是否双一流大学生" name="isDoubleFirst"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <Select onChange={(e) => {
                                    if (e == 1) {
                                        setDoubleFirst(true)
                                    } else {
                                        setDoubleFirst(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select>
                            </Form.Item>
                        </Col>
                        {
                            doubleFirst && (function () {
                                return (
                                    <Col span={12}>
                                        <Form.Item label="双一流大学名称" name="doubleFirstName"
                                            rules={[
                                                { required: true, message: '请输入' }
                                            ]}
                                        >
                                            <Input placeholder='双一流大学名称' />
                                        </Form.Item>
                                    </Col>
                                )
                            })()
                        }

                        <Row>
                            <Col span={6}>
                                <Form.Item valuePropName="checked" {...formItemLayout3} label="是否本单位班子成员" name="isUnitMem"
                                    rules={[
                                        { required: true, message: '请输入' }
                                    ]}
                                >
                                    <Switch checkedChildren="是" unCheckedChildren="否" disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                        setUnitMem(e)
                                        if (!e) {
                                            form.setFieldsValue({
                                                unitMemD25Name: undefined,
                                                unitMemD25Code: undefined,
                                                unitMemFileNumber: undefined,
                                                unitMemStartDate: undefined,
                                                unitMemReward: undefined,
                                                unitMemEndowmentInsuranceForUrbanEmployees: undefined,
                                                unitMemD0121Code: undefined,
                                                unitMemD0121Name: undefined,
                                                unitMemD26Code: undefined,
                                                unitMemD26Name: undefined,
                                                unitMemRemark: undefined
                                            });
                                        }
                                    }} />
                                    {/* <Select disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                    if (e == 1) {
                                        setUnitMem(true)
                                    } else {
                                        setUnitMem(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select> */}
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item valuePropName="checked" {...formItemLayout3} label="是否党组织班子成员" name="isOrgMem"
                                    rules={[
                                        { required: true, message: '请输入' }
                                    ]}
                                >
                                    <Switch checkedChildren="是" unCheckedChildren="否" disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                        setOrgMem(e)
                                        if (!e) {
                                            // 当切换为false时，清空党组织班子相关字段
                                            form.setFieldsValue({
                                                orgMemD022Code: undefined,
                                                orgMemD022Name: undefined,
                                                orgMemDutyExplain: undefined,
                                                orgMemStartDate: undefined,
                                                orgMemFileNumber: undefined,
                                                orgMemHasMiddleManagement: undefined,
                                                orgMemD121Code: undefined,
                                                orgMemD121Name: undefined,
                                                orgMemHasPartTraining: undefined,
                                                orgMemReward: undefined,
                                                orgMemEndowmentInsuranceForUrbanEmployees: undefined
                                            });
                                            // 重置相关状态
                                            setD022CodeDisabled([]);
                                        }
                                    }} />
                                    {/* <Select disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                    if (e == 1) {
                                        setOrgMem(true)
                                    } else {
                                        setOrgMem(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select> */}
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item valuePropName="checked" {...formItemLayout3} label="是否驻村干部" name="isCadreMem"
                                    rules={[
                                        { required: true, message: '请输入' }
                                    ]}
                                >
                                    <Switch checkedChildren="是" unCheckedChildren="否" disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                        setCadreMem(e)

                                        if (!e) {
                                            // 当切换为false时，清空驻村干部相关字段
                                            form.setFieldsValue({
                                                cadreMemD140Code: undefined,
                                                cadreMemD140Name: undefined,
                                                cadreMemD141Code: undefined,
                                                cadreMemD141Name: undefined,
                                                cadreMemStartDate: undefined,
                                                cadreMemResidentDate: undefined,
                                                cadreMemDispatchPosition: undefined,
                                                cadreMemD197Code: undefined,
                                                cadreMemD197Name: undefined
                                            });
                                        }
                                    }} />
                                    {/* <Select disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                    if (e == 1) {
                                        setCadreMem(true)
                                    } else {
                                        setCadreMem(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select> */}
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item valuePropName="checked" {...formItemLayout3} label="是否村（社区）工作者" name="isWorkMem"
                                    rules={[
                                        { required: true, message: '请输入' }
                                    ]}
                                >
                                    <Switch checkedChildren="是" unCheckedChildren="否" disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                        setWorkMem(e)
                                        if (!e) {
                                            // 当切换为false时，清空社区工作者相关字段
                                            form.setFieldsValue({
                                                workMemHasLeadersHelpPeople: undefined,
                                                workMemSubsidies: undefined,
                                                workMemHasPartyWork: undefined,
                                                workMemHasTwoOneMember: undefined,
                                                workMemD116Code: undefined,
                                                workMemD116Name: undefined,
                                                workMemD143Code: undefined,
                                                workMemD143Name: undefined,
                                                workMemRemark: undefined
                                            });
                                        }
                                    }} />
                                    {/* <Select disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                    if (e == 1) {
                                        setWorkMem(true)
                                    } else {
                                        setWorkMem(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select> */}
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item valuePropName="checked" {...formItemLayout3} label="是否村（社区）后备干部" name="isReserveMem"
                                    rules={[
                                        { required: true, message: '请输入' }
                                    ]}
                                >
                                    <Switch checkedChildren="是" unCheckedChildren="否" disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                        setReserveMem(e)
                                        if (!e) {
                                            // 当切换为false时，清空后备干部相关字段
                                            form.setFieldsValue({
                                                reserveMemHasLeadersHelpPeople: undefined,
                                                reserveMemSubsidies: undefined,
                                                reserveMemIsWorkVillage: undefined,
                                                reserveMemD143Code: undefined,
                                                reserveMemD143Name: undefined,
                                                reserveMemHelpUnit: undefined,
                                                reserveMemHelpMem: undefined,
                                                reserveMemNowJob: undefined,
                                                reserveMemRemark: undefined
                                            });
                                            // 重置工作状态
                                            setIsWorkVillageTag(0);
                                        }
                                    }} />
                                    {/* <Select disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                    if (e == 1) {
                                        setReserveMem(true)
                                    } else {
                                        setReserveMem(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select> */}
                                </Form.Item>
                            </Col>
                        </Row>
                        {/* <Col span={24}>
                            <Form.Item   {...formItemLayout1} label="是否本单位班子成员" name="isUnitMem"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <Select disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                    if (e == 1) {
                                        setUnitMem(true)
                                    } else {
                                        setUnitMem(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select>
                            </Form.Item>
                        </Col> */}
                        {
                            unitMem &&
                            renderbzcy()
                        }
                        {/* <Col span={24}>
                            <Form.Item {...formItemLayout1} label="是否党组织班子成员" name="isOrgMem"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <Select disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                    if (e == 1) {
                                        setOrgMem(true)
                                    } else {
                                        setOrgMem(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select>
                            </Form.Item>
                        </Col> */}
                        {
                            orgMem && renderdzzbzcy()
                        }
                        {/* <Col span={24}>
                            <Form.Item {...formItemLayout1} label="是否驻村干部" name="isCadreMem"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <Select disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                    if (e == 1) {
                                        setCadreMem(true)
                                    } else {
                                        setCadreMem(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select>
                            </Form.Item>
                        </Col> */}
                        {
                            cadreMem && renderdzcgb()
                        }
                        {/* <Col span={24}>
                            <Form.Item {...formItemLayout1} label="是否村（社区）工作者" name="isWorkMem"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <Select disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                    if (e == 1) {
                                        setWorkMem(true)
                                    } else {
                                        setWorkMem(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select>
                            </Form.Item>
                        </Col> */}
                        {
                            workMem && rendersq()
                        }
                        {/* <Col span={24}>
                            <Form.Item {...formItemLayout1} label="是否村（社区）后备干部" name="isReserveMem"
                                rules={[
                                    { required: true, message: '请输入' }
                                ]}
                            >
                                <Select disabled={basicInfo.type == 'edit'} onChange={(e) => {
                                    if (e == 1) {
                                        setReserveMem(true)
                                    } else {
                                        setReserveMem(false)
                                    }
                                }}>
                                    <Option value={1}>是</Option>
                                    <Option value={0}>否</Option>
                                </Select>
                            </Form.Item>
                        </Col> */}
                        {
                            reserveMem && renderhbgb()
                        }
                    </Row>



                </Form>
            </div>
        </Modal>

    );
};
// @ts-ignore
export default React.forwardRef(index);
