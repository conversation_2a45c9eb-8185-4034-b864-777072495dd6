import request from "@/utils/request";
import qs from 'qs';



export function feedbackReplyGetList(params) {
  return request(`/api/problem/feedbackReply/getList`,{
    method:'POST',
    body:params,
  });
}

// export function feedbackReplyGetList(params) {
//   return request(`/api/problem/feedbackReply/getList?${qs.stringify(params)}`,{
//     method:'GET',
//   });
// }

export function feedbackReplyAdd(params) {
  return request(`/api/problem/feedbackReply/add`,{
    method:'POST',
    body:params,
  });
}

export function getList(params) {
  return request(`/api/problem/feedback/getList`,{
    method:'POST',
    body:params,
  });
}
export function feedbackAdd(params) {
  return request(`/api/problem/feedback/add`,{
    method:'POST',
    body:params,
  });
}

export function details(params) {
  return request(`/api/problem/feedback/details?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function feedbackDelete(params) {
  return request(`/api/problem/feedback/delete?${qs.stringify(params)}`,{
    method:'GET',
  });
}


