import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Modal, Switch, Alert, DatePicker, Upload, message, But<PERSON>, Row, Col, Popconfirm } from 'antd';
import MemSelect from '@/components/MemSelect';
import OrgSelect from '@/components/OrgSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import DictSelect from '@/components/DictSelect';
import moment from 'moment';
import Tip from '@/components/Tip';
import { unixMoment, findDictCodeName, convertToWebpBeforeUpload } from '@/utils/method.js';
import _isNumber from 'lodash/isNumber';
import _isArray from 'lodash/isArray';
import _isEmpty from 'lodash/isEmpty';
import _isString from 'lodash/isString';
import Date from '@/components/Date';
import { getContextPerson, compareDate } from '@/pages/developMem/services/index'
import Sure from '../SureBasicInfoAndChange';
import UploadComp, { getInitFileList, fitFileUrlForForm } from '@/components/UploadComp';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      memInfo: {},
      hasMemValue: true,
      timeKey: moment().valueOf(),
      d89Code: "",
      // specialChecked: false,
      noDrawArr: ['2', '3', '4', '5', '6', '21', '22', '31', '1111', '112'],
      subLoading: false
    }
  }
  // 时间限制
  disabledTomorrow = (current) => {
    const { memInfo: { objectDate = '' } = {} } = this.state;
    const cu = moment(current);
    const start = moment(objectDate).endOf('day');
    // console.log(start.format('YYYY-MM-DD'),'123')
    const end = moment();
    if (_isNumber(objectDate)) {
      return current && (cu.isBefore(start) || cu.isSame(start) || cu.isAfter(end) || cu.isSame(end))
    } else {
      return false
    }
  };

  // // 是否 特殊情况下发展
  // handleSpecialChange = (e) => {
  //   this.setState({ specialChecked: e });
  // };

  memLength = (rule, value, callback) => {
    if (_isArray(value) && !_isEmpty(value)) {
      if (value.length < 2) {
        callback('至少选择2名人员');
      } else {
        callback();
      }
    } else {
      callback();
    }
  };
  handleOk = () => {
    const { submit, memDevelop: { basicInfo = {} } = {} } = this.props;
    const { memInfo, hasMemValue } = this.state;
    const { name, code: memCode, orgCode, orgName, orgZbCode, developOrgCode: logOrgCode, d08Code, d08Name } = memInfo;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
       
        const { shortTrainingBeginTime, shortTrainingEndTime, reviewConclusionTime, topreJoinOrgDate } = val


        // // 获取去年第四季度的起始时间
        // const lastYearQ4Start = moment().subtract(1, 'years').quarter(4).startOf('quarter');
        // // 获取去年第四季度的结束时间
        // const lastYearQ4End = moment().subtract(1, 'years').quarter(4).endOf('quarter');
        // // 检查输入时间是否在去年第四季度之内
        // const isInLastYearQ4 = moment(topreJoinOrgDate).isBetween(lastYearQ4Start, lastYearQ4End);
        // // 判断是否是今年
        // const isNowYear = moment(topreJoinOrgDate).format('YYYY') == moment().format('YYYY')
        // if (!(isInLastYearQ4 || isNowYear)) {
        //   this.props.form.setFields({
        //     topreJoinOrgDate:{
        //       value:val['topreJoinOrgDate'],
        //       errors:[new Error('此时间应在去年第四季至今年内')]
        //     }
        //   })
        //   return
        // }

        if (moment(shortTrainingEndTime).diff(moment(shortTrainingBeginTime), 'days') < 2) {
          this.props.form.setFields({
            shortTrainingEndTime: {
              value: val['shortTrainingEndTime'],
              errors: [new Error('开始时间与结束时间差必须大于等于3天')]
            }
          })
          return
        }
        if ([shortTrainingBeginTime, shortTrainingEndTime, reviewConclusionTime].find(it => it > topreJoinOrgDate)) {
          this.props.form.setFields({
            topreJoinOrgDate: {
              value: val['topreJoinOrgDate'],
              errors: [new Error('该时间必须大于短期集中培训开始时间，短期集中培训结束时间和政治审查结论性意见落款时间')]
            }
          })
          return
        }
        this.props.form.setFields({
          memIdcard: {
            value: val['memIdcard'],
            errors: [new Error('经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')]
          }
        })

        if (basicInfo.hasUnitStatistics == null) {
          Tip.error('操作提示', '请完善人事关系是否在党组织关联单位内等信息。');
          return;
        }
        val[`hasStaffOrganization`] = val[`hasStaffOrganization`] == '1' ? 1 : 0;
        val = findDictCodeName(['d89', 'd49', 'd18', 'd19', 'd20', 'd21', 'd07', 'd09', 'd11', 'd27', 'd28', 'd06', 'd08', 'd60', 'd88', 'readingProfessional', 'politics', 'advancedModel'], val, memInfo);
        if (val['joinOrgCode'] === '11') {
          val['joinOrgName'] = '党员推荐'
        } else {
          val = findDictCodeName(['joinOrg'], val, {});
        }
        val = unixMoment(['topreJoinOrgDate', 'topreCommitteeDate', 'shortTrainingBeginTime', 'shortTrainingEndTime', 'reviewConclusionTime'], val);

        if (_isArray(val['topreIntroductionMem'])) {
          val['topreIntroductionMem'] = val['topreIntroductionMem'].map(item => item['code']).toString()
        }
        val['name'] = name;
        val['d11Name'] = '新发展';
        val['memCode'] = memCode;
        val['d08Code'] = d08Code;
        val['d08Name'] = d08Name;

        val['orgZbCode'] = typeof val['orgCode'] === 'object' ? val['orgCode'][0]['zbCode'] : memInfo['orgZbCode'];
        val['logOrgCode'] = typeof val['orgCode'] === 'object' ? val['orgCode'][0]['orgCode'] : memInfo['developOrgCode'];
        val['orgName'] = typeof val['orgCode'] === 'object' ? val['orgCode'][0]['name'] : memInfo['orgName'];
        val['orgCode'] = typeof val['orgCode'] === 'object' ? val['orgCode'][0]['code'] : memInfo['orgCode'];


        val.objectDate = memInfo.objectDate
        let arr = []
        let num = 0
        val['filesList'].map(i => {
          const { data = [] } = i?.response
          data.map((j, k) => {
            num++
            let obj = {
              d222Code: '408',
              d222Name: '向上级党委组织部门的备案报告和批复（复印件）',
              name: j?.name,
              path: j?.url,
              sort: num,
            }
            arr.push(obj)
          })
        })
        console.log(arr,'arrarrarr')
        val['filesList'] = arr
        this.setState({subLoading: true})
        const res = await this.props.dispatch({
          type: 'memDevelop/zyToPreparation',
          payload: { data: { ...val } }
        });
        const { code = 500 } = res;
        this.setState({subLoading: false})
        if (code === 0) {
          this.handleCancel();
          Tip.success('操作提示', '操作成功');
          submit && submit();
        }

      }
    })
  };
  handleCancel = () => {
    this.setState({ visible: false });
    this.destroy();
  };
  open = async (record) => {
    this.getReferences(record)
    this.setState({ visible: true, memInfo: record, timeKey: moment().valueOf() })
    await this.props.dispatch({
      type: 'memDevelop/zyfindByCode',
      payload: {
        code: record['code']
      }
    })
    // politicsCode==03 共青团员  政治面貌里包括共青团员且年龄不超过28才显示团员推优

    const { memDevelop:{ basicInfo:{ politicsCode = '', birthday = '', topreJoinOrgDate = undefined}={}  }={}  } = this.props
    if (topreJoinOrgDate) {
      this.props.form.setFieldsValue({ topreJoinOrgDate: moment(topreJoinOrgDate) })
    }
    let lessThan28 = moment(birthday).add(28, 'y').isAfter(moment())
    if (politicsCode?.includes('03') && lessThan28) {
      this.setState({ 'noDrawArr': ['2', '3', '4', '5', '6', '21', '22', '31', '112'] })
    } else {
      this.setState({ 'noDrawArr': ['2', '3', '4', '5', '6', '21', '22', '31', '1111', '112'] })
    }
  };
  destroy = () => {
    this.setState({
      memInfo: {},
      hasMemValue: true,
      d89Code: [],
      toactiveContextPerson: "",
      toobjContextMem: "",
      //  specialChecked: false,
      noDrawArr: ['2', '3', '4', '5', '6', '21', '22', '31', '1111', '112']
    })
  };
  hasMemOnChange = (val) => {
    this.props.form.setFieldsValue({ topreIntroductionMem: undefined });
    // topreIntroductionMem: val=true时输入值是数组，false是字符串("topreIntroductionMem"))
    // let person=this.props.form.getFieldValue("topreIntroductionMem")
    // console.log('person===',person);

    // if(val){
    //   if(_isArray(person)){
    //     let personArr=person.map((item,index)=>item.name);
    //     this.props.form.setFieldsValue({topreIntroductionMem:personArr.toString()});
    //   }
    // }
    // this.props.form.setFieldsValue({toactiveContextPerson:undefined});
    this.setState({ hasMemValue: val })
  };
  joinOrgChange = (e) => {
    if (e && (e.key == "121" || e.key == "121_temp")) {
      this.setState({
        memInfo: {
          d89Code: ["12"]
        }
      })
    }
  }
  // 接收预备党员获取介绍人
  getReferences = async (record) => {
    const { code = 500, data = {} } = await getContextPerson({
      memCode: record.code,
      d08Code: "4",
    });
    if (code === 0) {
      this.setState({
        toactiveContextPerson: data.toobjContextMem,
        toobjContextMem: data.name
      })

    }
  };
  timeValidator = async (rule, value, callback) => {
    const { memDevelop: { basicInfo = {} } = {} } = this.props
    const { code = 500, data = true } = await compareDate({
      data: {
        code: this.state.memInfo.code,
        type: 2,
        time: moment(value).valueOf()
      }
    });

    if (code === 0) {
      if (!data) {
        callback(new Error('该时间早于确定发展对象时间'));
      } else {
        callback();
      }
    }
  }
  timeValidator1 = async (rule, value, callback) => {
    if (!value) {
      callback(new Error('必填'));
    } else {
      const { memDevelop: { basicInfo = {} } = {} } = this.props
      const { code = 500, data = true } = await compareDate({
        data: {
          code: this.state.memInfo.code,
          type: 2,
          time: moment(value).valueOf()
        }
      });

      if (code === 0) {
        if (!data) {
          callback(new Error('该时间早于确定发展对象时间'));
        } else {
          const checkDate = moment(basicInfo['objectDate']);
          const daysDiff = value.diff(checkDate, 'days');
          if (daysDiff < 7) {
            callback(new Error('该时间距离确定发展对象时间不足7天'));
          } else {
            callback();
          }

        }
      }
    }

  }
  // // 政治面貌变化时    politicsCode==03 共青团员
  // handlePoliticsCodeChange=(val)=>{
  //   let leagueMember = val && val.find(item=>item['key']=='03');
  //   if(leagueMember){
  //     this.setState({'noDrawArr':['2','3','4','5','6','21','22','31']})

  //   }else{
  //     this.setState({'noDrawArr':['2','3','4','5','6','21','22','31','1111']})

  //   }
  // }
  beforeUpload = (file, fileList) => {
    message.destroy();
    if (fileList.length > 10) {
      message.error('一次性最多上传10个文件')
      fileList=[]
      return false
    }
    let imgs: any = []
    let pdfs: any = []
    fileList.forEach(i => {
      if (i.type.startsWith('image')) {
        imgs.push(i)
      }
      if (i.type.startsWith('application/pdf')) {
        pdfs.push(i)
      }
    })
    if (imgs.length > 0 && pdfs.length > 0) {
      message.error('请勿同时上传pdf和图片')
      fileList=[]
      return false
    }
    let fileSize: number = file['size'] / 1024 / 1024;
    if (fileSize < 50) {
      return convertToWebpBeforeUpload(file)
    } else {
      message.error('请上传小于50M的文件!');
      fileList=[]
      return false
    }
  }
  render() {
    const { form, loading: { effects = {} } = {} } = this.props;
    const { getFieldDecorator } = form;
    const { visible, hasMemValue, memInfo, noDrawArr,subLoading } = this.state;
    const { memDevelop:{ basicInfo:{ politicsCode = ''}={}  }={}  } = this.props
    return (
      <Modal
        destroyOnClose
        title="接收预备党员"
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        confirmLoading={effects['memDevelop/ToPreparation']}
        width={1200}
        footer={[
          <Button htmlType={'button'} onClick={this.handleCancel} key={1}>取消</Button>,
          <Button 
          htmlType={'button'} 
          type={'primary'} 
          key={2}
          loading={subLoading}
          onClick={() => {
            Modal.confirm({
              title: '操作提示',
              content: '确认预备党员操作不可逆，点确定后不可撤销，请务必谨慎操作！',
              onOk: () => {
                this.handleOk();
              }
            });
          }}
        >
          确定
        </Button>
          // <Popconfirm title="确认预备党员操作不可逆，点确定后不可撤销，请务必谨慎操作！" onConfirm={this.handleOk} okText="确定" cancelText="取消" key={2} placement="topRight">
          //   <Button htmlType={'button'} type={'primary'} loading={subLoading}>确定</Button>
          // </Popconfirm>
          ,
        ]}
      >
        {
          visible && <Fragment key={this.state.timeKey}>
            <Alert message="提示：支部党员大会讨论通过时间为成为预备党员时间。" type="info" showIcon />
            <div style={{ marginBottom: '10px' }} />
            <Form>
              <Row>
                <Col span={24}>
                  <FormItem
                    label="入党介绍人是否为本组织人员"
                    {...formItemLayout2}
                  >
                    {getFieldDecorator('hasStaffOrganization', {
                      rules: [{ required: true, message: '入党介绍人是否为本组织人员' }],
                      initialValue: hasMemValue,
                    })(
                      <Switch checkedChildren="是" unCheckedChildren="否" onChange={this.hasMemOnChange} checked={hasMemValue} />
                    )}
                  </FormItem>
                </Col>
                {/* <Col span={12}>
                <FormItem
                  label="特殊情况下发展" // hasSpecialDevelopment
                  {...formItemLayout}
                >
                  {getFieldDecorator('hasSpecialDevelopment', {
                    rules: [{ required: true, message: '是否特殊情况下发展' }],
                    initialValue: false,
                  })(
                    <Switch
                      checkedChildren="是"
                      unCheckedChildren="否"
                      onChange={this.handleSpecialChange}
                      checked={specialChecked}
                    />,
                  )}
                </FormItem>
                </Col> */}
                <Col span={24}>
                  {
                    hasMemValue ?
                      <FormItem
                        label="入党介绍人"
                        {...formItemLayout1}
                      >
                        {getFieldDecorator('topreIntroductionMem', {
                          rules: [{ required: true, message: '入党介绍人' }, { validator: this.memLength }],
                          // initialValue:hasMemValue,
                          initialValue: this.state.toobjContextMem,
                        })(
                          <MemSelect initValue={this.state.toobjContextMem} checkType={'checkbox'} placeholder="请选择" />
                        )}
                      </FormItem>
                      :
                      <FormItem
                        label="入党介绍人"
                        {...formItemLayout1}
                      >
                        {getFieldDecorator('topreIntroductionMem', {
                          rules: [{ required: true, message: '入党介绍人' }],
                          // initialValue:hasMemValue,
                          initialValue: this.state.toobjContextMem,
                        })(
                          <Input placeholder="请输入" />
                        )}
                      </FormItem>
                  }
                </Col>
                <Col span={24}>
                  <FormItem
                    label="所在党组织"
                    {...formItemLayout1}
                  >
                    {getFieldDecorator('orgCode', {
                      rules: [{ required: true, message: '请选择所在党支部' }],
                      initialValue: _isEmpty(memInfo) ? undefined : memInfo['orgCode'],
                    })(
                      <OrgSelect orgTypeList={['3', '4']} initValue={memInfo['orgName']} placeholder={'请选择所在党组织'} />
                    )}
                  </FormItem>
                </Col>

                <Col span={12}>
                  <FormItem
                    label="支部党员大会讨论通过时间"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('topreJoinOrgDate', {
                      rules: [{ required: true, message: '召开支部大会日期' }, { validator: this.timeValidator1, trigger: ['blur', 'change'] }],
                      // initialValue: hasMemValue,
                    })(
                      // <DatePicker style={{width:'100%'}} disabledDate={this.disabledTomorrow}/>
                      <Date isDefaultEnd={true} /> // 支部党员大会讨论通过时间 不能大于当前时间
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="短期集中培训开始时间"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('shortTrainingBeginTime', {
                      rules: [
                        { required: true, message: '短期集中培训开始时间' },
                        { validator: this.timeValidator, trigger: ['blur', 'change'] }
                      ],
                    })(
                      <Date isDefaultEnd={true} />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="短期集中培训结束时间"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('shortTrainingEndTime', {
                      rules: [
                        { required: true, message: '短期集中培训结束时间' },
                        { validator: this.timeValidator, trigger: ['blur', 'change'] }
                      ],
                    })(
                      <Date isDefaultEnd={true} />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="政治审查结论性意见落款时间"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('reviewConclusionTime', {
                      rules: [
                        { required: true, message: '政治审查结论性意见落款时间' },
                        // {validator:this.timeValidator,trigger: ['blur', 'change']}
                      ],
                    })(
                      <Date isDefaultEnd={true} />
                    )}
                  </FormItem>
                </Col>
                {/*<Col span={12}>*/}
                {/*  <FormItem*/}
                {/*    label="上级党委审批日期"*/}
                {/*    {...formItemLayout}*/}
                {/*  >*/}
                {/*    {getFieldDecorator('topreCommitteeDate', {*/}
                {/*      rules: [{ required: true, message: '上级党委审批日期' }],*/}
                {/*      // initialValue:hasMemValue,*/}
                {/*    })(*/}
                {/*      // <DatePicker style={{width:'100%'}}  />*/}
                {/*      <Date />*/}
                {/*    )}*/}
                {/*  </FormItem>*/}
                {/*</Col>*/}
                <Col span={12}>
                {/* // 在发展对象接收预备党员的时候，如果党员政治面貌里包括团员，加入中共组织的类别，默认为1111团员推优，不能够更改，下拉框变为禁选状态 */}
                  <FormItem
                    label="加入中共组织的类别"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('joinOrgCode', {
                      rules: [{ required: true, message: '加入中共组织的类别' }],
                      initialValue: politicsCode?.includes('03') ? { key: '1111', name:'团员“推优”' }  : undefined,
                    })(
                      <DictTreeSelect
                        backType={'object'}
                        codeType={'dict_d27'}
                        disabled={politicsCode?.includes('03')}
                        placeholder="请选择"
                        noDraw={noDrawArr}
                        parentDisable={true}
                        initValue={politicsCode?.includes('03') ? '1111'  : undefined}
                        onChange={this.joinOrgChange}
                      // filter={(data={})=>{
                      //    if(specialChecked) {
                      //     let item=[]
                      //   item=item.concat(data.filter(obj=>['11'].includes(obj['key'])))
                      //     return item
                      //    }
                      //     return data
                      // }}
                      />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="进入支部类型"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('d11Code', {
                      rules: [{ required: false, message: '进入支部类型' }],
                      initialValue: '1',
                    })(
                      <DictSelect backType={'object'} codeType={'dict_d11'} placeholder="请选择" disabled={true} initValue={'1'} />
                    )}
                  </FormItem>
                </Col>
                {/* <Col span={12}>
                  <FormItem
                    label="工作岗位"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('d09Code', {
                      rules: [{ required: true, message: '工作岗位' }],
                      initialValue:_isEmpty(memInfo)?undefined:memInfo['d09Code'],
                    })(
                      <DictTreeSelect backType={'object'} codeType={'dict_d09'} placeholder="请选择" initValue={_isEmpty(memInfo)?undefined:memInfo['d09Code']} parentDisable={true}/>
                    )}
                  </FormItem>
                </Col> */}
                <Col span={12}>
                  <FormItem
                    label="入党志愿书编号"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('topreJoinBookNum', {
                      rules: [{ required: false, message: '入党志愿书编号' }],
                      // initialValue:_isEmpty(memInfo)?undefined:memInfo['d09Code'],
                    })(
                      <Input placeholder={'入党志愿书编号'} />
                    )}
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label="上传人"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('oprationUser', {
                      rules: [{ required: true, message: '请填写上传人' }],
                      // initialValue:_isEmpty(memInfo)?undefined:memInfo['d09Code'],
                    })(
                      <Input placeholder={'上传人'} />
                    )}
                  </FormItem>
                </Col>
                <Col span={24}>
                  <FormItem
                    label="向上级党委组织部门的备案报告和批复（复印件）"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('filesList', {
                      rules: [{ required: true, message: '向上级党委组织部门的备案报告和批复（复印件）' }],
                      // initialValue:_isEmpty(memInfo)?undefined:memInfo['d09Code'],
                    })(
                      <UploadComp beforeUpload={this.beforeUpload} accept='.jpg,.png,.jpeg,.webp,.pdf' multiple action='/api/minio/upload?model=fzdx' />
                    )}
                  </FormItem>
                </Col>
                {/* <Col span={12}>
                  <FormItem
                    label="其他政治面貌"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('d89Code', {
                      rules: [{ required: false, message: '其他政治面貌' }],
                      // initialValue:_isEmpty(memInfo)?undefined:memInfo['d09Code'],
                      initialValue:memInfo.d89Code,
                    })(
                      <DictSelect initValue={memInfo.d89Code} backType={'object'} codeType={'dict_d89'} placeholder="请选择" mode={'multiple'}  />
                    )}
                  </FormItem>
                </Col> */}
              </Row>
              {/* <Sure {...this.props} memInfo={this.state.memInfo} handlePoliticsCodeChange={this.handlePoliticsCodeChange.bind(this)} /> */}
            </Form>
          </Fragment>
        }

      </Modal>
    );
  }
}
export default Form.create()(index);
