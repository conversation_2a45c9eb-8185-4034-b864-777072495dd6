/*
* 困难党员
* */
import React,{Fragment} from 'react';
import { Divide<PERSON>, <PERSON>con<PERSON>rm, Ta<PERSON>,Button,Input,Modal } from 'antd';
import RuiFilter from 'src/components/RuiFilter';
import ListTable from 'src/components/ListTable';
import NowOrg from 'src/components/NowOrg';
import Edit from '../../../manage/components/membasic/Difficulty';
import WhiteSpace from '@/components/WhiteSpace';
import _isEmpty from 'lodash/isEmpty';
import {withContext} from 'src/utils/global.jsx';
import {connect} from "dva";
import {_history as router} from "@/utils/method";
import {getSession} from "@/utils/session";
import Tip from '@/components/Tip';
import qs from 'qs';
import { setListHeight } from '@/utils/method';
const {Search} = Input;
@withContext
@connect(({memBasic,commonDict,loading,memDifficulty})=>({memBasic,commonDict,loading,memDifficulty}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state={
      filterHeight:100,
      modalVisible:false
    }
  }
  componentDidMount(): void {
    setListHeight(this);
  }
  getList=(pageNum = 1, pageSize = 10, orgCode = '')=>{
    const {search,filter}=this.state;
    !_isEmpty(orgCode) && this.props.dispatch({
      type:'memDifficulty/getList',
      payload:{
        pageNum,
        pageSize,
        orgCode,
        search,
        filter
      }
    })
  };
  // 筛选
  filterChange=(val)=>{
    this.setState({
      filter:val
    },()=>this.action())
  };
  // 分页
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  action=(val?:object)=>{
    const {pagination={}}=this.props.memDifficulty;
    const {current,pageSize}=pagination;
    const {search,filter}=this.state;
    const org=getSession('org') || {};
    this.props.dispatch({
      type:'memDifficulty/getList',
      payload:{
        orgCode:org['orgCode'],
        pageNum:current,
        pageSize,
        ...search,
        ...filter,
        ...val
      }
    })
  };
  search=(value)=>{
    this.props.dispatch({
      type:'memDifficulty/updateState',
      payload:{
        memName:value
      }
    });
    this.setState({
      search:{memName:value}
    },()=>{
      const {pagination={}}=this.props.memBasic;
      const {query}=this.props.location;
      router.push(`?${qs.stringify({...query,pageNum:1,pageSize:pagination['pageSize']})}`)
    });

  };
  // 人员编辑
  addOrEdit=async(record)=>{
    this.destroy();
    if(record && record['memCode']){
      await this.props.dispatch({
        type:'memBasic/findMem',
        payload:{
          code:record['memCode']
        }
      });
    }
    this.open();
  };

  // 人员删除
  del=async (record)=>{
    const res = await this.props.dispatch({
      type:'memDifficulty/del',
      payload:{code:record['code']}
    });
    const {code = 500} = res || {};
    if(code === 0){
      Tip.success('操作提示','删除成功');
      // const org=getSession('org') || {};
      // this.getList(1,10,org['orgCode'])
      this.action();
    }
  };
  open=()=>{
    this.setState({modalVisible:true})
  };
  canncel=()=>{
    this.destroy();
    // const org=getSession('org') || {};
    // this.getList(1,10,org['orgCode']);
    this.action();
    this.setState({modalVisible:false})
  };
  destroy=()=>{
    this.props.dispatch({
      type:'memDifficulty/updateState',//重置model
      payload:{difficultyInfo:{}}
    });
  };
  render(): React.ReactNode {
    const {memDifficulty ={},loading:{effects = {}} ={},commonDict} = this.props;
    const {list, pagination} = memDifficulty;
    const {current,pageSize} = pagination;
    const {filterHeight,modalVisible}=this.state;
    const filterData = [
      {
        key:'d09CodeList',name:'工作岗位',value:commonDict['dict_d09_tree'],
      },
      {
        key:'sexCodeList',name:'人员性别',value:[{key:'1',name:'男'},{key:'0',name:'女'}],
      },
      {
        key:'d08CodeList',name:'人员类型',value:[{key:'1',name:'正式党员'},{key:'2',name:'预备党员'}],
      },
      {
        key:'d07CodeList',name:'学历教育',value:commonDict['dict_d07_tree'],
      },
    ];
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:60,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'姓名',
        dataIndex:'memName',
        width:100,
        render:(text,record)=>{
          return(
            <a onClick={()=>this.addOrEdit(record)} >{text}</a>
          )
        }
      },
      {
        title:'性别',
        dataIndex:'sexCode',
        width:100,
        render:(text)=>{
          return (
            <span> {text === '1' ?  '男' : '女'} </span>
          )
        }
      },
      // {
      //   title:'公民身份证',
      //   dataIndex:'idcard',
      //   width:160,
      // },
      // {
      //   title:'电话',
      //   width:100,
      //   dataIndex:'phone',
      // },
      {
        title:'党员类型',
        width:120,
        dataIndex:'d08Name',
      },
      {
        title:'所在组织',
        width:260,
        dataIndex:'orgName',
      },
      {
        title:'困难类型',
        width:260,
        dataIndex:'d53Name',
      },
      {
        title:'困难原因',
        width:260,
        dataIndex:'difficultyReason',
      },
      {
        title:'操作',
        dataIndex:'action',
        width:220,
        render:(text,record)=>{
          return(
            <span>
              <a onClick={()=>this.addOrEdit(record)}>编辑</a>
              <Divider type="vertical"/>
              <Popconfirm title="确定要删除吗？" onConfirm={()=>this.del(record)}>
               <a className={'del'} >删除</a>
              </Popconfirm>
            </span>
          )
        },
      },
    ];
    return (
      <Fragment>
        <NowOrg
          extra={
            <React.Fragment>
              <Search style={{width:200,marginLeft:16}} onSearch={this.search} placeholder={'请输入检索关键词'}/>
            </React.Fragment>
          }
        />
        <WhiteSpace/>
        <ListTable columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
        <Modal
          title={'编辑'}
          destroyOnClose
          visible={modalVisible}
          // onOk={this.submit}
          onCancel={this.canncel}
          width={'calc(100vw - 350px)'}
          confirmLoading={effects['memAbroad/save']}
          footer={null}
        >
          <Edit {...this.props} onEnd={this.canncel}/>
        </Modal>

      </Fragment>
    )
  }
}
