import React, { useEffect } from 'react';
import Search from '../commonSearch';
import { connect } from 'dva';
const CryptoJS = require('crypto-js');

const encryptedData = CryptoJS.AES.encrypt(
  'qaz@123456',
  CryptoJS.enc.Utf8.parse('AESNBHB3ZA==HKXt'),
  { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 },
);
const password = encryptedData.toString();

// 时代答卷
const index = (props:any) => {
  useEffect(() => {
    sessionStorage.removeItem('dataApi');
    if(true){
      let account = 'qztest002';
      props.dispatch({
        type: 'login/qzLogin',
        payload: {
          data:{
            password,
            account,
            captchaCode:'U32xS4DACRRP0XtJPhOjlQ=='
          }
        }
      }).then(({code}:any)=>{})
    }

  }, []);

  return (
    <React.Fragment>
      <Search
        infoKey="sddj_info"
        goToPath2="/qzs/screen/sddj/memShow"
        goToPath={'/qzs/screen/sddj/memSearch'}
        titImg={require('../../../../assets/qzs/sddj.png')}
      ></Search>
    </React.Fragment>
  );
};

export default connect(({ login }) => ({ login }))(index);