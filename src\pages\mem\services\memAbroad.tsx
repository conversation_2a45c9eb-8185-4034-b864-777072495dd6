/**
 * 出国出境services
 */
import request from "@/utils/request";
import qs from 'qs';
export function getList(params) {
  return request(`/api/mem/abroad/getList?${qs.stringify(params)}`,{
    method:'Get',
  });
}
//新增党员出入国信息
export function addMemAbroad(params) {
  return request(`/api/mem/abroad/addMemAbroad`,{
    method:'POST',
    body:params,
  });
}
export function findByCode(params) {
  return request(`/api/mem/abroad/findByCode?${qs.stringify(params)}`,{
    method:'Get',
  });
}
export function updateMemAbroad(params) {
  console.log('updateMemAbroad===',params);
  
  return request(`/api/mem/abroad/updateMemAbroad`,{
    method:'POST',
    body:params,
  });
}
export function delMemAbroad(params) {
  return request(`/api/mem/abroad/delMemAbroad?${qs.stringify(params)}`,{
    method:'Get',
  });
}

// 遵义-党员管理-出国出境-添加回国信息
export function backMemAbroad(params) {
  console.log('backMemAbroad===',params);
  return request(`/api/mem/abroad/backMemAbroad`,{
    method:'POST',
    body:params,
  });
}

// 遵义-党员管理-出国出境-停止党籍
export function handleStop(params) {
  console.log('stop===',params);
  return request(`/api/mem/abroad/stop`,{
    method:'POST',
    body:params,
  });
}

