// 往年年度统计
import React from 'react';
import ListTable from '@/components/ListTable';
import { Tabs, Input, Select, Tree, Modal, Switch, Button, Checkbox, Popconfirm, Spin } from 'antd';
import moment from 'moment'
import { connect } from 'dva';
import _get from 'lodash/get'
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import { isEmpty, changeListPayQuery, fileDownloadHeader } from '@/utils/method';
import { ExclamationCircleOutlined } from '@ant-design/icons';
// import { success } from '@/components/Notice';
import Tip from '@/components/Tip';
import Pegging from '../../text/components/Pegging';
import ExtractDataModal from '../components/extractDataModal'
import { checkTable } from '../services';
import { getSession } from "@/utils/session";
import SpinProgress from '@/components/SpinProgress';
import CheckModal from '../components/checkModal';
import { exportNestedTable } from '../services'
import { _history as router } from "@/utils/method";
const { TabPane } = Tabs;
const { Option } = Select;
const TreeNode = Tree.TreeNode;
const { TextArea } = Input;
const { Search } = Input;
//@ts-ignore

@connect(({ tmwTable, loading }) => ({ tmwTable, loading: loading.effects }), null, null, { forwardRef: true })
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      modalTableLoading: false,
      timeKey: +new Date(),
      tbCheckLoading: false,
      selectHtml: this.selectHtml,
      TreeListCodes: [],
      selectItem: undefined,
      overTheYearsSelects: undefined,
    }
  }

  static getDerivedStateFromProps = (nextProps, prevState) => {
    const { location: { query = {}, pathname='' } = {} } = router;
    const state = {};
    const { tmwTable: { TreeList = [], overTheYearsSelects=[]} = {}, } = nextProps;
    const org = JSON.parse(sessionStorage.getItem('org') || '{}')
    const { _org, key, selectHtml, _TreeList, _overTheYearsSelects, selectItem} = prevState;

    if(!_isEqual(overTheYearsSelects, _overTheYearsSelects) && pathname === '/annualStatistics/old' && !_isEmpty(overTheYearsSelects)){
      state['_overTheYearsSelects'] = overTheYearsSelects; 
      state['selectItem'] = overTheYearsSelects[0];
      nextProps.dispatch({
        type: 'tmwTable/queryExcelConfigTreeById1',
        payload: {
          reportCode: -1,
          year: state['selectItem']?.year || ''
        }
      })
    }

    // 当树有值且第一次加载时
    if (!_isEqual(TreeList, _TreeList) && !_isEmpty(TreeList)) {
      state['_TreeList'] = TreeList;
      if(pathname === '/annualStatistics/old' && !_isEmpty(overTheYearsSelects) && !_isEmpty(selectItem)){
        // let selectItem = overTheYearsSelects[0] || {}
        const { type=[] } = selectItem
        if(_isEqual(type,['1'])){
          state['TreeListCodes'] = TreeList.filter(it => {
            return it.type === '1'
          }).map(it => it.levelCode);
          state['key'] = state['TreeListCodes'][0];
          
        } else if(_isEqual(type,['2','3'])){
          state['TreeListCodes'] = TreeList.filter(it => {
            return it.type === '3' || it.type === '2'
          }).map(it => it.levelCode);
          state['key'] = state['TreeListCodes'][0];
        }else{
          state['TreeListCodes'] = TreeList.map(it => it.levelCode);
          state['key'] = state['TreeListCodes'][0];
        }
      }
      

     if (state['key'] && !_isEmpty(TreeList)) {
        let find = TreeList.find(tree => tree.levelCode == state['key']);
        if (find) {
          selectHtml(find.levelCode, find.type);
        }
      }
    }

    // 当切换左侧组织树时
    if (!_isEqual(org, _org)) {
      state['_org'] = org;

      // 当选择统计项的选项需要在切换组织树后重置时，才需要以下内容：
      // state['_overTheYearsSelects'] = undefined;
      // state['_TreeList'] = undefined;
      // state['selectItem'] = undefined;
      // nextProps.dispatch({
      //   type: 'tmwTable/updateState',
      //   payload: {
      //     TreeList: [],
      //     overTheYearsSelects: [],
      //   }
      // })

      // if(pathname === '/annualStatistics/old'){
      //   nextProps.dispatch({
      //     type: 'tmwTable/getOverTheYears',
      //     payload: {}
      //   });
      // }


      let find = TreeList.find(tree => tree.levelCode == key);
      if (find) {
        selectHtml(find.levelCode, find.type);
      }
    }
    return state;
  };

  componentDidMount = () => {
    let script = document.createElement("script"), _this = this;
    script.type = "text/javascript";
    script.src = '/js/check.js';
    document.body.appendChild(script);
    window.addEventListener('message', async function (e) {
      const { data } = e;
      if (data && typeof data == 'string') {
        const { tableCellIndex='', tableName='', btnType=undefined } = JSON.parse(data);
        let tableRowLast = '';
        if( btnType=== '1'){
          tableRowLast = '171'
        }
        if( btnType=== '2'){
          tableRowLast = '172'
        }
        if( btnType=== '3'){
          tableRowLast = '173'
        }
        if( btnType=== '4'){
          tableRowLast = '174'
        }
        // 重新履行入党手续
        if( btnType=== '5'){
          tableRowLast = '175'
        }
        // 排查整顿发展党员违规违纪问题中除名处置
        if( btnType=== '6'){
          tableRowLast = '176'
        }
        if(btnType){
          // let txt = tableCellIndex.split('_');
          let name = tableName ? tableName.split('_') : [];
          let type = name.includes('replenish') ? '2' : '1'
          // 表中的特殊按钮
          _this.setState({
            tableRow: tableRowLast,
            showButtonModal: false,
            type, //反查类型，1--普通表，2--补充资料
          });
          _this.findVerData(1, 10).then(({ list = [] }) => {
            _this['peg'] && _this['peg'].setState({
              visible: true,
              showButtonModal: false,
              type,
            })
          });
        }else{
          let txt = tableCellIndex ? tableCellIndex.split('_') : []
          let name = tableName ? tableName.split('_') : [];
          let type = name.includes('replenish') ? '2' : '1'
          txt = txt.slice(1, txt.length);
          _this.setState({
            tableRow: txt[0],
            tableColumn: txt[1],
            type,
            name,
            showButtonModal: true,
          });
        }
      }
    })
  };

  componentWillUnmount() {
    this.setState({
      selectItem: undefined,
      _overTheYears: undefined,
      _org: undefined,
      key: undefined,
      _TreeList: undefined,
    })
    this.props.dispatch({
      type: 'tmwTable/updateState',
      payload: {
        TreeList: [],
        overTheYears: []
      }
    })
  }
// 筛选统计项
handleSelect=(item:any)=>{
  
  const { type=[] } = item || {}
  if(_isEqual(type,['1'])){
    let a = this.state._TreeList.filter(it => {
      return it.type === '1'
    }).map(it => it.levelCode)
    this.setState({
      TreeListCodes:a,
      key:a[0]
    })
  } else if(_isEqual(type,['2','3'])){
    let a = this.state._TreeList.filter(it => {
      return it.type === '2' || it.type === '3'
    }).map(it => it.levelCode);
    this.setState({
      TreeListCodes:a,
      key:a[0]
    })
  }else{
    let a = this.state._TreeList.map(it => it.levelCode)
    this.setState({TreeListCodes:a,
    key:a[0]
    })
    
 }
 this.props.dispatch({
  type: 'tmwTable/updateState',
  payload: {
    TreeList: [],
    overTheYears: []
  }
})
}
  // 渲染html
  selectHtml = (val,type?) => {
    const {selectItem } = this.state;
    let url = 'tmwTable/queryExcelConfigReturnHtml';
    if (type == '3') {
      url = 'tmwTable/queryExcelConfigReturnHtmlIns';
    }
    const org = JSON.parse(sessionStorage.getItem('org') || '{}')
    this.props.dispatch({
      type: url,
      payload: {
        reportCode: val,
        orgCode: org?.code,
        orgLevelCode: org?.orgCode,
        year: selectItem.year || undefined,
        
      }
    }).then(res => {
      this.setState({
        Html: res,
        timeKey: +new Date(),
      }, () => {
        // 设置单位名称
        if (document.getElementById('unitName')) {
          document.getElementById('unitName').innerText = `填报单位：${org.name}`;
        }
      })
    })
  };

  // 表切换
  onSelect = (key) => {
    const { tmwTable: { TreeList = [] } = {}, otherExportPageInfo} = this.props;
    let find = TreeList.find(tree => tree.levelCode == key);
    if (find) {
      this.setState({
        key,
      });
      this.selectHtml(key, find.type);
    }
  };

  // 反查详情
  findVerData = async (pageNum?, pageSize?, levelCode?) => {
    const { tableRow, tableColumn, key, type, selectItem } = this.state;
    // const { type='', treeLevel, treeOrg, memType } = this.props.annualstats;
    // const { treeOrg= } =this.props.positionNum;
    let lastCode = levelCode || this.state['levelCode'];
    const org = JSON.parse(sessionStorage.getItem('org') || '{}')
    let params = {
      reportCode: key,
      rowIndex: tableRow,
      colIndex: tableColumn,
      orgCode: org?.code,
      orgLevelCode: org?.orgCode,
      pageNum: pageNum || 1,
      pageSize: pageSize || 10,
      type,
      year: selectItem.year || undefined,
    };
    // if (type == 'a') {
    //   if (memType === '3') {
    //     params['orgCode'] = treeOrg['orgCode'] + `,${treeOrg['unitId']}`;
    //   } else {
    //     params['orgCode'] = treeOrg['levelCode'];
    //   }
    // }
    // if (type == 'b') {
    //   params['countLevel'] = treeLevel['code'];
    // }
    this.setState({
      modalTableLoading: true,
    })
    const obj = await this.props.dispatch({
      type: 'tmwTable/findVerData',
      payload: { data: { ...params } }
    });
    const { data = {} } = obj;
    let changeList = changeListPayQuery(data || { list: [] });
    this.setState({
      params,
      ...changeList,
      levelCode: lastCode,
      modalTableLoading: false,
      tableType: data ? data['type'] : undefined,
    });
    return {
      ...changeList
    }
  };
  // 打印
  print = () => {
    const el: any = window.document.getElementById('page');
    const iframe: any = document.createElement('IFRAME');
    let doc: any = null;
    iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:500px;top:500px;');
    document.body.appendChild(iframe);
    doc = iframe.contentWindow.document;
    doc.write(el.innerHTML);
    doc.close();
    // 获取iframe的焦点，从iframe开始打印
    iframe.contentWindow.focus();
    iframe.contentWindow.print();
    if (navigator.userAgent.indexOf("MSIE") > 0) {
      document.body.removeChild(iframe);
    }
  }
  // 上一页下一页
  nextPage = (type) => {
    const { TreeListCodes = [], key, _TreeList } = this.state;
    if (!_isEmpty(TreeListCodes)) {
      let _key = type == 'add' ? TreeListCodes[TreeListCodes.indexOf(this.state.key) + 1] : TreeListCodes[TreeListCodes.indexOf(this.state.key) - 1]
      this.setState({
        key: _key,
      });
      let htmlType = '';
      _TreeList.map((item:any)=>{
        if(item.levelCode === _key){
          htmlType = item.type
        }
      })
      this.selectHtml(_key,htmlType);
    }
  }
  // 上一页下一页disabled
  disabledNextPageBtn = (type) => {
    const { TreeListCodes = [] } = this.state;
    if (!_isEmpty(TreeListCodes)) {
      if (TreeListCodes.indexOf(this.state.key) == 0 && type == 'pre') {
        return true;
      }
      if (TreeListCodes.indexOf(this.state.key) == TreeListCodes.length - 1 && type == 'add') {
        return true;
      }
    } else {
      return true
    }
  };
  // 报表校核
  check = async () => {
    let org: any = getSession('org') || {};
    this.setState({ tbCheckLoading: true });
    const { code = 500, data = '' } = await checkTable({ orgCode: org.orgCode, type: this.state.key });
    if (code === 0) {
      this.setState({ tbCheckLoading: false });
      this['CheckModal'].open(data);
    }

    // const { code = 500, data: { key = '' } = {} } = await checkTable({ orgCode: org.orgCode, type: this.state.key });
    // if(code === 0){
    //   this.setState({ tbCheckLoading: true, progressType: 'check' });
    //   this['SpinProgress'].getProgress(key,'niandu');
    // }

    // if (code === 0) {
    //   this.setState({ tbCheckLoading: true, progressType: 'check' });
    //   let _this = this;
    //   Modal.confirm({
    //     title: '校核完成',
    //     content: `${data}`,
    //     onOk() {
    //       _this.setState({ tbCheckLoading: false});
    //       // console.log(window.open(`/annualStatistics/checkResultPage?type=${key}&orgCode=${org.orgCode}`));ssssss
    //     },
    //     // okText: '查看',
    //     onCancel() {},
    //   });
    // }
  };
  // 报表导出
  newDownLoad = async () => {
    const {selectItem } = this.state;
    let org: any = getSession('org') || {};
    this.setState({ tbCheckLoading: true });
    const { code = 500 } = await exportNestedTable({
      orgLevelCode: org.orgCode,
      orgCode: org.code,
      // reportYear:moment().format('YYYY')
      year: selectItem.year || undefined,
    });
    this.setState({ tbCheckLoading: false });
    if (code == 0) {
      Tip.success('操作提示', '导出成功')
    }
  }
  //进度条
  progressCallback = (res) => {
    const { progressType, key } = this.state;
    let org: any = getSession('org') || {};
    switch (progressType) {
      case 'download':
        const { code: codes = undefined } = res;
        if (codes === '2') {
          this.setState({ tbCheckLoading: false });
          fileDownloadHeader(`/api${res.url}`, res['url'].split('/')[2]);
          Modal.success({
            content: '导出成功',
          });
        } else {
          Modal.error({
            title: '导出失败',
          });
        }
        break;
      case 'check':
        this.setState({ tbCheckLoading: false });
        // const {
        //   positionNum:{ treeOrg:{parentCode = '' ,levelCode:levelCodes = ''} = {} }={},
        //   annualstats:{memType = '', treeOrg:{ levelCode = '',unitId = undefined, id = undefined }={} }={}
        // } = this.props;
        // let orgCode = levelCode;
        // if(memType === '3'){
        //   orgCode = `${id},${unitId}`;
        // }
        const { code = 500, data: { haveErr = true, errSize = 0 } = {} } = res;

        if (code === '2') {
          if (haveErr) {
            this.setState({
              checkPass: false
            });
            Modal.confirm({
              title: '校核完成',
              content: `共${errSize}个错误未通过数据校核，请点击查看校核结果进行修改！`,
              onOk() {
                console.log(window.open(`/annualStatistics/checkResultPage?type=${key}&orgCode=${org.orgCode}`));
              },
              okText: '查看',
              onCancel() { },
            });
          } else {
            this.setState({
              checkPass: true
            });
            Modal.success({
              content: '校核完成，未找到错误.',
            });
          }
        }
        if (code === '3') {
          this.setState({
            checkPass: false
          });
          Modal.error({
            title: '校核失败',
          });
        }
        break;
    }
  };

  render(): React.ReactNode {
    const { rowCell, colCell, tableIndexConfigList, key = '', modalTableLoading, lineConfigList, hasReplenish, queryType, datas = {}, list = [], pagination, tableType, tbCheckLoading, params, selectItem } = this.state;
    // 增加otherExportPageInfo，便于往年报表导入
    const { tmwTable: { TreeList = [], overTheYearsSelects = [] } = {}, otherExportPageInfo, pegging = true } = this.props;
    return (
      <React.Fragment>
        <div style={{ height: '100%' }}>
          <Spin spinning={this.props.loading['tmwTable/queryExcelConfigReturnHtml'] || tbCheckLoading}>
            <div id={'rightDiv'}>
            <React.Fragment>
              <span>请选择统计项：</span>
              <Select value={this.state?.selectItem?.id} style={{ width: 200 ,marginRight:'10px'}} onChange={(e)=>{
                debugger
                let year = '' ;
                overTheYearsSelects.map((item:any)=>{
                  if(item.id === e){
                    this.setState({
                      selectItem:item,
                      key: undefined,
                      _TreeList: undefined});
                    year = item.year;
                    this.handleSelect(item)
                  }
                })
                this.props.dispatch({
                  type: 'tmwTable/updateState',
                  payload: {
                    TreeList: [],
                  }
                });
                
                this.props.dispatch({
                  type: 'tmwTable/queryExcelConfigTreeById1',
                  payload: {
                    reportCode: -1,
                    year
                  }
                });
              }}>
                {overTheYearsSelects && overTheYearsSelects.map((item:any, index:any)=>{
                  return <Option key={index} value={item.id}>{item.name}</Option>
                })}
              </Select>
              </React.Fragment>
              <Select style={{ width: 300 }} onChange={this.onSelect} value={key}>
                {TreeList && TreeList.map(tree => {
                  if(_isEqual(selectItem?.type, ['2','3'])){
                    if (tree.type === '3' || tree.type === '2') {
                      return (
                        <Option key={tree.levelCode} value={tree.levelCode}>{tree.shortName}</Option>
                      )
                    }
                  }else{
                    if (tree.type !== '3' && tree.type !== '2') {
                      return (
                        <Option key={tree.levelCode} value={tree.levelCode}>{tree.shortName}</Option>
                      )
                    }
                  } 
                })}
              </Select>

              <div style={{ display: 'inline-block', marginTop: 4 }}>
                <Button style={{ marginLeft: 10 }} onClick={() => this.nextPage('pre')} disabled={this.disabledNextPageBtn('pre')}>上一表</Button>
                <Button style={{ marginLeft: 10 }} onClick={() => this.nextPage('add')} disabled={this.disabledNextPageBtn('add')}>下一表</Button>
                <Button onClick={this.print} style={{ marginLeft: 10 }}>打印</Button>
                {/* {
                  otherExportPageInfo ? otherExportPageInfo({ state: this.state, props: this.props }) :
                    <React.Fragment>
                      <Button onClick={this.check} type={'primary'} style={{ marginLeft: 10 }} loading={tbCheckLoading}>报表校核</Button>
                      <Button onClick={this.newDownLoad} type={'primary'} style={{ marginLeft: 10 }} loading={tbCheckLoading}>报表导出</Button>
                    </React.Fragment>
                } */}
                <Button onClick={this.newDownLoad} type={'primary'} style={{ marginLeft: 10 }} loading={tbCheckLoading}>报表导出</Button>
              </div>
              <SpinProgress ref={e => this['SpinProgress'] = e}
                callback={(res) => this.progressCallback(res)}>
                <div key={this.state.timeKey} id={'page'} style={{ marginTop: 50, textAlign: 'center' }} dangerouslySetInnerHTML={{ __html: this.state['Html'] }} />
              </SpinProgress>
            </div>
          </Spin>
          {
            pegging &&
            <Pegging tableType={tableType}
              list={list}
              pagination={pagination}
              pageChange={this.findVerData}
              
              params={{ ...params, reportType: tableType }} ref={e => this['peg'] = e} />
          }
        </div>
        <CheckModal ref={e => this['CheckModal'] = e} htmlType={this.state.key} />
        <ExtractDataModal ref={e => this['ExtractDataModal'] = e} />
        <Modal
          visible={this.state.showButtonModal}
          closable={false}
          style={{
            position: 'absolute',
            left: '50%',
            top: '40%',
          }}
          onCancel={() => {
            this.setState({ showButtonModal: false });
          }}
          width={230}
          bodyStyle={{ padding: 0 }}
          footer={[
            <Button onClick={() => {
              const { tableRow, tableColumn, key, type, selectItem } = this.state;
              const org = JSON.parse(sessionStorage.getItem('org') || '{}')
              let params = {
                reportCode: key,
                rowIndex: tableRow,
                colIndex: tableColumn,
                orgCode: org?.code,
                orgLevelCode: org?.orgCode,
                type,
                year: selectItem.year
              };
              this['ExtractDataModal'].open(params);
              this.setState({ showButtonModal: false });
            }}>数据提取</Button>,
            <Button type={'primary'} style={{ marginLeft: 10 }} onClick={() => {
              this.findVerData(1, 10, this.state.name.join('-')).then(({ list = [] }) => {
                this['peg'] && this['peg'].setState({
                  visible: true,
                  showButtonModal: false,
                })
              });
            }}>数据反查</Button>
          ]}>
        </Modal>
      </React.Fragment>
    );
  }
}
