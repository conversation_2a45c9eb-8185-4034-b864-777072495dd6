import React, { Fragment, useImperativeHandle, useState, useRef } from 'react';
import { Alert, Button, Form, Input, Modal, Radio, Spin, Avatar, Comment } from 'antd';
import { getSession } from '@/utils/session';
import Tip from '@/components/Tip';
import { details, feedbackAdd, feedbackReplyAdd, feedbackReplyGetList } from '@/pages/forum/services';
import Editors from '@/components/Editor';
import _isEmpty from 'lodash/isEmpty';
import style from './add.less'
import Talk from '../components/talk';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const { TextArea } = Input;
const index = (props: any, ref) => {
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [isDetail, setIsDetail] = useState(false);
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [getDetailLoading, setgGetDetailLoading] = useState(false);
  const [timeKey, setTimeKey] = useState<any>(+new Date());
  const [callbackComment, setCallbackComment] = useState<any>({});
  const [talkTableLoading, setTalkTableLoading] = useState(false);
  const [listData, setListData] = useState([]);
  const [pagination, setPagination] = useState<any>({ pageNum: 1, pageSize: 5, total: 0 });
  const user = getSession('user') || {};
  const {
    width = 1500,
  } = props;

  useImperativeHandle(ref, () => ({
    open: query => {
      const { info = {}, type = '' } = query;
      setIsDetail(type === 'detail');
      if (info?.code) {
        getDetail(info?.code);
      }
      open();
    },
  }));
  const getDetail = async (code) => {
    setgGetDetailLoading(true);
    const { code: resCode = 500, data } = await details({ code });
    setgGetDetailLoading(false);
    if (resCode === 0) {
      let _data = {
        ...data,
      };
      setTimeKey(+new Date())
      setDataInfo(_data);
      form.setFieldsValue(_data);
      getCommitteeList(_data.code);
    }
  };
  const getCommitteeList = async (code, p = {}) => {
    let data ={
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      code,
      ...p,
    };
    setTalkTableLoading(true);
    const { code: resCode = 500, data: {
      list = [], pageNumber: pageNum = 1, totalRow: total = 0, pageSize = 5 } = {}
    } = await feedbackReplyGetList({
      data
    });
    setTalkTableLoading(false);
    if (resCode == 0) {
      setPagination({ pageNum, total, pageSize, current: pageNum });
      setListData(list.map(it => {
        return { ...it, open: false }
      }));
    }
  };
  const open = () => {
    setVisible(true);
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    setIsDetail(false);
    form.resetFields();
    setPagination({ pageNum: 1, pageSize: 5, total: 0})
    setConfirmLoading(false);
    setTalkTableLoading(false);
    setgGetDetailLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    const { onOK } = props;
    const { code: orgCode = '', orgCode: specialOrgCode = '' } = getSession('org') || { code: '', orgCode: '' };
    let val = {
      ...e,
    };

    setConfirmLoading(true);
    const { code = 500 } = await feedbackAdd({
      data: {
        ...val,
        code: dataInfo?.code,
      },
    });
    setConfirmLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      onOK && onOK();
    }
  };
  const renderCallBack = (item, index) => {
    const { children, ...other } = item;
    if (children) {
      return (
        <div style={{ margin: '4px', padding: '2px', }}>
          <ExampleComment item={other} key={index}>
            {children.map((it, indexx) => renderCallBack(it, indexx))}
          </ExampleComment>
        </div>
      )
    }
    return <div style={{ margin: '2px', padding: '2px', }}>
      <ExampleComment item={item} key={index} />
    </div>

  }
  const ExampleComment = (props: any) => {
    const { item, children } = props;
    return (
      <Fragment>
        <Comment
          actions={[<span key="comment-nested-reply-to" onClick={() => {
            setCallbackComment(item);
          }}>回复</span>]}
          author={<a>{item.itemId == 1 ? item.replyUserName : item.userName}</a>}
          avatar={<Avatar style={{ backgroundColor: '#7265e6', verticalAlign: 'middle', marginRight: 10 }} size="large">{item.itemId == 1 ? item.replyUserName : item.userName}</Avatar>}
          content={
            <div dangerouslySetInnerHTML={{
              __html: item.content,
            }}>
            </div>
          }
        >
          {children}
        </Comment>
      </Fragment>
    )
  };

  return (
    <div className={style.box}>
      <Modal
        title={'问题反馈'}
        visible={visible}
        onCancel={handleCancel}
        width={width}
        destroyOnClose={true}
        footer={isDetail ? null : [
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={confirmLoading} onClick={() => form.submit()}>
            提交反馈
          </Button>,
        ]}
      >
        <Spin spinning={getDetailLoading}>
          {
            !isDetail && <Alert message="欢迎您提出宝贵的建议，您留下来的每个字都将用来改善我们的产品"
              showIcon
              type="success" style={{ marginBottom: 10 }} />
          }
          {
            visible && <Fragment>
              <Form form={form} {...formItemLayout} onFinish={onFinish}>
                <Form.Item name='questionsBriefly'
                  label="问题简述"
                  rules={[{ required: true, message: '请输入问题简述' }]}
                >
                  <Input style={{ pointerEvents: isDetail ? 'none' : 'auto' }} />
                </Form.Item>
                <Form.Item name='problemDetails'
                  label="问题详情"
                  rules={[{ required: true, message: '请输入问题详情' }]}
                >
                  {
                    _isEmpty(dataInfo.problemDetails) ?
                      <Fragment>
                        <Editors id={'forum'}
                          key={1}
                          onChange={(val) => {
                            form.setFieldsValue({
                              problemDetails: val,
                            });
                          }}
                        />
                      </Fragment> :
                      <Fragment>
                        <Editors id={'forum'}
                          key={2}
                          onChange={(val) => {
                            form.setFieldsValue({
                              problemDetails: val,
                            });
                          }}
                          disabled={isDetail}
                          init={dataInfo['problemDetails']}
                        />
                      </Fragment>
                  }
                  {/*<TextArea rows={5} showCount maxLength={200} autoSize={{ minRows: 5 }}*/}
                  {/*          style={{ pointerEvents: isDetail ? 'none' : 'auto' }}/>*/}
                </Form.Item>
                {/* <Form.Item name='contact'
                  label="联系方式"
                  rules={[{ required: true, message: '请输入问题详情' }]}
                >
                  <Input style={{ pointerEvents: isDetail ? 'none' : 'auto' }} />
                </Form.Item>
                {
                  isDetail && <div style={{ pointerEvents: isDetail ? 'none' : 'auto' }}>
                    <Form.Item name='replySituation'
                      label="回复情况"
                    >
                      <TextArea rows={5} />
                    </Form.Item>
                    <Form.Item label="修复状态" name='stateRepair'>
                      <Input />
                    </Form.Item>
                  </div>
                } */}
              </Form>
              <Spin spinning={talkTableLoading}>
                {
                  isDetail &&
                  <Talk
                    data={listData}
                    pagination={pagination}
                    onPageChange={(page, pageSize) => {
                      getCommitteeList(dataInfo.code, { pageNum: page });
                    }}
                    submitFunc={async (e) => {
                      const { code = 500 } = await feedbackReplyAdd({
                        data: {
                          ...e,
                          problemId: dataInfo?.code,
                          replyUserId: user['id'],
                          replyUserName: user['name']
                        }
                      });
                      if (code == 0) {
                        Tip.success('操作提示', '发送成功');
                        getCommitteeList(dataInfo.code, { pageNum: 1 });
                      }
                      return code;
                    }}
                  />
                }
              </Spin>
            </Fragment>
          }
        </Spin>
      </Modal>
    </div>
  );
};
export default React.forwardRef(index);
