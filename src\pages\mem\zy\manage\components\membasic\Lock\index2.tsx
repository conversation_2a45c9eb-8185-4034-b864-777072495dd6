import React, { Fragment, useState, useImperativeHandle, useRef } from 'react'
import { Modal, Form, Checkbox, Row, Col, Divider, Popconfirm, Button } from 'antd';
import Tip from '@/components/Tip';
import _cloneDeep from 'lodash/cloneDeep';
import _isEmpty from 'lodash/isEmpty';
import { getSession } from '@/utils/session';
import { memUnlocked, memBatchLock } from '../../../../../services/index';

const { Group } = Checkbox;
const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 24 },
};

const SearchModal = (props: any, ref) => {
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const [list, setList] = useState<any>([]);
  const { code: orgCode = '' } = getSession('org') || { code: '' }
  const searchRef: any = useRef();
  const [form] = Form.useForm();

  const onCancel = () => {
    setVisible(false);
    setIndeterminate(false);
    setCheckAll(false);
    form.resetFields();
  }
  const onOk = () => {
    let infos = searchRef.current.getInfo();
    props.onOk && props.onOk(infos);
    setVisible(false);
  }
  useImperativeHandle(ref, () => ({
    open: (val: any) => {
      setVisible(true);
      getInfo();
    },
  }));
  const getInfo = async () => {
    const { code = 500, data = [] } = await memUnlocked({ data: { orgCode } });
    if (code === 0) {
      setList(data);
    }
  };
  const onFinish = async (val) => {
    setConfirmLoading(true);
    const { code = 500 } = await memBatchLock({ data: val });
    setConfirmLoading(false);
    if (code == 0) {
      Tip.success('操作提示', '操作成功');
      onCancel();
      props.onOK && props.onOK();
    }
  }
  const onCheckAllChange = e => {
    form.setFieldsValue({ memCode: e.target.checked ? list.map(it => it.code) : [] })
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  const onCheckChange = (val) => {
    setIndeterminate(val.length < list.length && val.length != 0);
    setCheckAll(val.length === list.length);
  }
  return (
    <Fragment>
      <Modal
        title={'批量锁定'}
        visible={visible}
        onCancel={onCancel}
        // onOk={}
        width={1200}
        confirmLoading={confirmLoading}
        destroyOnClose={true}
        bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
        footer={
          <Fragment>
            <Popconfirm title={<div style={{width:'300px'}}>信息经锁定后，将只能由具有解锁权限的上级党组织授权才能解锁，是否需要批量锁定?</div>} onConfirm={form.submit} >
              <Button type='primary'>确定</Button>
            </Popconfirm>
            <Button onClick={onCancel}>取消</Button>
          </Fragment>
        }
      >
        {visible && <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
            全选
          </Checkbox>
          <Divider />
          <Form.Item name='memCode'
            rules={[{ required: true, message: '请锁定人员' }]}
          >
            <Group onChange={onCheckChange}>
              <Row>
                {
                  !_isEmpty(list) && list.map((item, index) => {
                    return (
                      <Col span={6} key={index}><Checkbox value={item['code']}>{item['value']}</Checkbox></Col>
                    )
                  })
                }
              </Row>
            </Group>
          </Form.Item>
        </Form>}
      </Modal>
    </Fragment>
  )
}
export default React.forwardRef(SearchModal);
