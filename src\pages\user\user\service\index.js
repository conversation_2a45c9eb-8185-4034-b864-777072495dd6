import request from 'src/utils/request';
import qs from 'qs';
export function add(params) {
  // http://localhost:8080/role/addRole?parentId=8284b8a5942c4366a9f52ae127878c4b
  return request('/api/role/addRole', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params
  });
}

export function userAdd(params) {
  return request('/api/user/add', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}
export function userAddMpKey(params) {
  return request(`/api/user//bindUkey?${qs.stringify(params)}`, {
    method: 'GET',
  });
}
export function findValidUser(params) {
  return request('/api/user/findValidUser', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}
export function updateMS(params) {
  return request('/api/user/updateMS', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}

export function userList(params) {
  return request('/api/user/findList', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}

export function userUpdate(params) {
  return request('/api/user/updateUserInfo', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}

export function userDelete(params) {
  return request('/api/user/del', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}

export function userChangePW(params) {
  return request('/api/user/updatePassword', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}

export function userBulk(params) {
  return request('/api/user/batchUpdateUser', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}

export function getDictionary(params) {
  return request('/api/dictionary/getDictionaryList', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}

export function getList(params) {
  return request(`/api/role/getList?pageNum=${1}&pageSize=${100}`, {
    method: 'GET',
    // body:JSON.stringify(params),
    // body:params,
  });
}


export function getOrgTree(params) {
  return request('/api/org/getOrgTree', {
    method: 'POST',
    body: params,
  });
}


export function updateUserPermisson(params) {
  return request('/api/user/updateUserPermisson', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}

export function updateRoleId(params) {
  return request(`/api/user/findPermissionByUserIdAndRoleId?userId=${params.roleID}&roleId=${params.userID}`, {
    method: 'GET',
  });
}

export function findUserByAccount(params) {
  return request(`/api/user/findUserByAccount?account=${params.value}`, {
    method: 'GET',
  });
}

export function getUserByPage(params) {
  return request('/api/user/getUserByPage', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}

export function editUserList(params) {
  return request(`/api/user?id=${params.id}`, {
    method: 'GET',
  });
}
///user/password

export function edit(params) {
  return request('/api/user/edit', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}
export function upPassword(params) {
  return request('/api/user/password', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}
// /user/lock
export function lock(params) {
  return request('/api/user/lock', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}

export function unlock(params) {
  return request('/api/user/unlock', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}
//
export function batchEdit(params) {
  return request('/api/user/batchEdit', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}
//
export function findByPage(params) {
  return request('/api/log/findByPage', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}
//
export function exist(params) {
  return request('/api/user/exist', {
    method: 'POST',
    // body:JSON.stringify(params),
    body: params,
  });
}
//
export function getListAndValid(params) {
  return request(`/api/role/getListAndValid?pageNum=1&pageSize=100`, {
    method: 'Get',
  });
}
export function findPermissionByRoleId(params) {
  return request('/api/permission/findPermissionByRoleId', {
    method: 'POST',
    body: params,
  });
}
//
export function permissionList(params) {
  return request(`/api/permission/permissionList`, {
    method: 'Get',
  });
}


export function findPermissionByPermissionId(params) {
  return request(`/api/permission/findPermissionByPermissionId?permissionId=${params.data.permissionId}`, {
    method: 'Get',
  });
}

//
export function permissionEdit(params) {
  return request('/api/user/permission/edit', {
    method: 'POST',
    body: params,
  });
}
//
export function searchUserByKeyword(params) {
  return request(`/api/user/searchUserByKeyword?pageNum=${params.pageNum}&pageSize=${params.pageSize}&keyword=${params.keyword}&orgCode=${params.orgCode}`, {
    method: 'Get',
  });
}
