import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import style from './index.less';
import NowOrg from '@/components/NowOrg';
import { Button, Form, Alert, Modal, Space } from 'antd';
import ListTable from '@/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import SpinProgress from '@/components/SpinProgress';
import { processNodeNext } from '@/pages/developMem/services'
import Tip from '@/components/Tip'

const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};

const index: any = (props, ref) => {
    const {
        upList
    } = props;

    useImperativeHandle(ref, () => ({
        showModal: (obj) => {
            open(obj.code);
        }
    }));
    const org: any = getSession('org') || {};
    const [form] = Form.useForm();
    const { pathname, query: urlQuery = {} } = window['g_history']?.location || {};

    const [loading, setLoading] = useState(false);

    const [modalVisible, setModalVisible] = useState(false);
    const [codes, setCodes] = useState('')

    const open = (rowcode) => {
        setCodes(rowcode)
        setModalVisible(true)
    }
    const cancel = () => {
        setCodes('')
        setModalVisible(false)
    }
    const hadndleFinish = async (e) => {
        console.log('🚀 ~ e:', e);
        console.log(upList,'onOkonOkonOkonOk')
        const { code = 500, data = {} } = await processNodeNext({
            data: {
                code: codes,
                processNode: "JJ_5",
                paramMap: {
                    nextCode: "JJ_6"
                }
            }
        });
        if (code == 0) {
            Tip.success('操作提示', '考察完成');
            cancel()
            upList && upList()
        }
    };

    useEffect(() => {

    }, []);


    return (
        <Modal
            title={<div style={{ width: '100%', textAlign: 'center' }}>结束考察</div>}
            destroyOnClose
            visible={modalVisible}
            onCancel={cancel}
            width={'600px'}
            // onOk={()=>{
            //     form.submit();
            // }}
            footer={false}
        >
            <div style={{ textAlign: 'center'}}>
                <Space>
                    <Button onClick={cancel}>取消</Button>
                    <Button onClick={hadndleFinish} type="primary">确认</Button>
                </Space>
            </div>

        </Modal>

    );
};
// @ts-ignore
export default React.forwardRef(index);
