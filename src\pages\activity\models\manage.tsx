import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import { getList,add,findAcByCode,updateAc,cancelAc,groupList,groupMem,committeeList} from '../services/index';
import { getSession } from '@/utils/session';
import { changeListPayQuery } from '@/utils/method.js';
const memAbroad = modelExtend(listPageModel,{
  namespace: "activityManage",
  state:{
    abroadInfo:{},
    list:[],
    pagination:{}
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if(pathname === '/activity/manage'){
          const org=getSession('org') || {};
          const defaultParas={
            pageNum:1,
            pageSize:10,
          };
          const dictData=['dict_activity_type'];
          for(let obj of dictData){
            dispatch({type:'commonDict/getDictTree',payload:{data:{dicName:obj}}});
          }
          dispatch({
            type:'getList',
            payload:{
              data:{
                type:'2',
                org_org_code:org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
      });
    }
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put, select }) {
      const {filter,memName}=yield select(state=>state['activityManage']);
      payload['data']={...payload['data'],...filter,activityName:memName};
      const {data={}} = yield call(getList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
    *add({ payload }, { call, put, select }) {
      return yield call(add, payload);
    },
    *getDtail({ payload }, { call, put, select }){
      const {data = {},code} = yield call(findAcByCode, payload);
      yield put({
        type:'updateState',
        payload:{
          details:data
        }
      });
      return code
    },
    *edit({ payload }, { call, put, select }) {
      return yield call(updateAc, payload);
    },
    *del({ payload }, { call, put, select }) {
      return yield call(cancelAc, payload);
    },
    *getGrouop({ payload }, { call, put, select }){
      const defaultPayload = {
        pageNum:1,
        pageSize:100
      };
      const {data={}} = yield call(groupList, {...payload,...defaultPayload});
      return data['list'] || []
    },
    *getGrouopMem({ payload }, { call, put, select }){
      const {data={}} = yield call(groupMem, {...payload});
      return data['list'] || []
    },
    *getCommitteeList({ payload }, { call, put, select }){
      const res = yield call(committeeList, {...payload});
      return res
    }
  }
});
export default memAbroad;
