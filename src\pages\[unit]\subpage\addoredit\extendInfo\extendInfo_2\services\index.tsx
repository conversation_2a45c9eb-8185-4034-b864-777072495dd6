import request from "@/utils/request";
import qs from 'qs';

// 村社区扩展信息列表
export function getList(params) {
  return request(`/api/unit/community/getList`,{
    method:'POST',
    body:params,
  });
}

// 村社区扩展信息新增
export function addUnitCommunity(params) {
  return request(`/api/unit/community/addUnitCommunity`,{
    method:'POST',
    body:params,
  });
}

// 村社区扩展信息编辑
export function updateUnitCommunity(params) {
  return request(`/api/unit/community/updateUnitCommunity`,{
    method:'POST',
    body:params,
  });
}

// 村社区扩展信息删除
export function delUnitCommunity(params) {
  return request(`/api/unit/community/delUnitCommunity?${qs.stringify(params)}`,{
    method:'GET',
  });
}

