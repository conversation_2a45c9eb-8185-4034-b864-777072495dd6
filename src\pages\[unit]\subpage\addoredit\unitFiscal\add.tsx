import React, { Fragment, useImperativeHandle, useState, useEffect } from 'react';
import { Form, Input, Modal, DatePicker, InputNumber, Row, Col, Button, Select } from 'antd';
import Tip from '@/components/Tip';
import { addOrUpdate, findByCode, add } from './services';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import LongLabelFormItem from '@/components/LongLabelFormItem'

// const formItemLayout = {
//   labelCol: { span: 6 },
//   wrapperCol: { span: 14 },
// };
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 19 },
  },
};

const index = (props: any, ref) => {
  const { unit: { basicInfo = {} } = {} } = props;
  const [form] = Form.useForm();
  const [dataInfo, setDataInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [title, setTitle] = useState('新增');
  const [hasFinancialSupport, sethasFinancialSupport]: any = useState()
  useImperativeHandle(ref, () => ({
    open: (dataInfo) => {
      open(dataInfo);
    },
  }));
  const open = (dataInfo) => {
    setVisible(true);
    if (!_isEmpty(dataInfo)) {
      setTitle('编辑');
      setDataInfo(dataInfo);
      sethasFinancialSupport(dataInfo.hasFinancialSupport)
      form.setFieldsValue({
        ...dataInfo,
      });
    }
  };
  const close = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setDataInfo({});
    form.resetFields();
    setConfirmLoading(false);
  };
  const handleCancel = () => {
    close();
  };
  const onFinish = async (e) => {
    setConfirmLoading(true);
    let type = add;
    if (dataInfo.code) {
      type = addOrUpdate;
    }
    if (e['financialSupportEnforced'] > 200) {
      Tip.error('操作提示', '中央和省级财政扶持资金（万元）不能大于200')
      return;
    }
    if (e['incomeObtained'] > 200) {
      Tip.error('操作提示', '已获得收益(万元)不能大于200')
      return;
    }
    const { code: resCode = 500 } = await type({
      data: {
        ...e,
        unitCode: basicInfo.code,
        orgCode: basicInfo.createOrgCode,
        code: dataInfo?.code || undefined,
      },
    });
    setConfirmLoading(false);
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      close();
      props.onOK && props.onOK();
    }
  };

  const getDetail = async () => {
    // const {code = 500 , data = {}}= await findByCode({code:dataInfo.code});
    // if(code === 0) {
    //   setDataInfo(data);
    //   form.setFieldsValue({
    //     ...data,
    //   });
    // }
  }
  const YearValidator = (rule, value, callback) => {
    if (value && (value < 1900 || value > 2100)) {
      return callback('请输入正确年份');
    } else {
      return callback();
    }
  };
  useEffect(() => {
    if (dataInfo.code) {
      getDetail()
    }
  }, [])
  return (
    <Fragment>
      <Modal
        title={title}
        destroyOnClose
        visible={visible}
        confirmLoading={confirmLoading}
        onOk={() => {
          form.submit();
        }}
        onCancel={handleCancel}
        width={'1000px'}
      >
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Row>
            <Col span={24}>
              <Form.Item
                name="year"
                label="获扶持资金年度"
                rules={[{ required: true, message: '获扶持资金年度' }, { validator: YearValidator }]}
              >
                <InputNumber style={{ width: '100%' }} maxLength={4} minLength={4} />
              </Form.Item>
            </Col>
            <Col span={24}>
              {/* <Form.Item
              name="hasFinancialSupport"
              label="是否获中央和省级财政扶持资金"
              rules={[{ required: true, message: '请选择是否获中央和省级财政扶持资金' }]}
            >
              <Select placeholder={'请选择是否获中央和省级财政扶持资金'} style={{ width: '100%'  }} onChange={(e) => { sethasFinancialSupport(e) }}>
                <Select.Option value={1}>是</Select.Option>
                <Select.Option value={0}>否</Select.Option>
              </Select>
            </Form.Item> */}
              <LongLabelFormItem label={'是否获中央和省级财政扶持资金'}
                required={true}
                code={'hasFinancialSupport'}
                tipMsg={{}}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item {...formItemLayout}
                      name={code}
                      rules={[{ required: true, message: '是否获中央和省级财政扶持资金' }]}
                    >
                      <Select onChange={(e) => { sethasFinancialSupport(e) }} placeholder={'请选择'} style={{ width: '100%' }}>
                        <Select.Option value={1}>是</Select.Option>
                        <Select.Option value={0}>否</Select.Option>
                      </Select>
                    </Form.Item>
                  )
                }} />
            </Col>
            {hasFinancialSupport == 1 ?
              (
                <Col span={24}>
                  <LongLabelFormItem label={'中央和省级财政扶持资金（万元）'}
                    required={true}
                    code={'financialSupportEnforced'}
                    tipMsg={{}}
                    formItemLayout={formItemLayout}
                    formItem={(formItemLayout, code) => {
                      return (
                        <Form.Item {...formItemLayout}
                          name={code}
                          rules={[{ required: true, message: '中央和省级财政扶持资金（万元）' }]}
                        >
                          <InputNumber style={{ width: '100%' }} min={0} max={9999999} precision={2} />
                        </Form.Item>
                      )
                    }} />
                </Col>
              ) : null
            }
            <Col span={24}>
              {/* <Form.Item
              name="enforced"
              label="中央和省级财政扶持资金执行率（%）"
              rules={[{ required: true, message: '中央和省级财政扶持资金执行率' }]}
            >
              <InputNumber style={{ width: '100%' }} min={0} max={100}  precision={0}/>
            </Form.Item> */}
              <LongLabelFormItem label={'中央和省级财政扶持资金执行率(%)'}
                required={true}
                code={'enforced'}
                tipMsg={{}}
                formItemLayout={formItemLayout}
                formItem={(formItemLayout, code) => {
                  return (
                    <Form.Item {...formItemLayout}
                      name={code}
                      rules={[{ required: true, message: '中央和省级财政扶持资金执行率' }]}
                    >
                      <InputNumber style={{ width: '100%' }} min={0} max={100} />
                    </Form.Item>
                  )
                }} />
            </Col>
            <Col span={24}>
              <Form.Item
                name="completedAcceptanceProjects"
                label="已完工验收项目数"
                rules={[{ required: true, message: '已完工验收项目数' }]}
              >
                <InputNumber style={{ width: '100%' }} min={0} max={99999999} precision={0} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="incomeObtained"
                label="已获得收益(万元)"
                rules={[{ required: true, message: '已获得收益' }]}
              >
                <InputNumber style={{ width: '100%' }} min={0} precision={0} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </Fragment >
  );
};
export default React.forwardRef(index);
