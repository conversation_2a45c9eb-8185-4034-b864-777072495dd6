import React, { Fragment } from 'react';
import {Radio} from 'antd';
import style from './index.less';
const RadioButton = Radio.Button;
const RadioGroup = Radio.Group;
interface Interface {
  data:Array<object>,
  onClick:(any)=>void,
  defaultValue?:string,
}
export default class index extends React.Component<Interface, any> {
  constructor(props) {
    super(props);

  }
  onClick=({target:{value = ''}={}}={})=>{
    const {onClick} = this.props;
    onClick && onClick(value)
  };
  render() {
    const {data,defaultValue} = this.props;
    return (
      <Fragment>
        <div className={data.length !== 1 ? style.box : style.box2}>
          <RadioGroup defaultValue={defaultValue || data[0]['name']}>
            {
              data.length > 0 && data.map(item=>(
                <RadioButton value={item['name']} key={item['name']} onChange={this.onClick}>{item['text']}</RadioButton>
              ))
            }
          </RadioGroup>
        </div>
      </Fragment>

    );
  }
}
