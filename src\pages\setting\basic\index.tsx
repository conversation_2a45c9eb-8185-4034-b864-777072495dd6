import React, { useEffect, useState } from 'react';
import Structure from '../components/Structure';
import request from '@/utils/request';
import WhiteSpace from '@/components/WhiteSpace';
import Tree from '../components/tree';
import tip from '@/components/Tip';
export default function(props) {
  const [tree,setTree]=useState([]);
  const [list,setList]=useState([]);
  const [treeKey,setTreeKey]:any=useState([]);
  useEffect(()=>{
    request('/api/table/tableSelect').then(res=>{
      if(res['code']=='0'){
        setTree(res['data']);
        if(res['data'].length>0){
          const item=res['data'][0];
          treeChange(item);
          setTreeKey([item['id']]);
        }
      }
    })
  },[]);
  const treeChange=(item)=>{
    request(`/api/table/tableFind?id=${item['id']}`).then(res=>{
      if(res['code']=='0'){
        setList(res['data']);
      }
    })
  };
  const listChange=(data)=>{
    request(`/api/table/tableUp`,{
      method:'POST',
      body:{
        data
      }
    }).then(res=>{
      if(res['code']=='0'){
        tip.success('提示信息','备注修改成功');
      }
    });
  };
  return(
    <div style={{display:'flex',height:'100%'}}>
      <div style={{width:200,borderRight:'1px solid #f2f2f2',marginRight:12}}>
        <WhiteSpace/>
        <Tree selectedKeys={treeKey} data={tree} onChange={treeChange}/>
      </div>
      <div style={{width:'100%',padding:'12px 0'}}>
        <Structure data={list} showDesc={false} onChange={listChange}/>
      </div>
    </div>
  )
}
