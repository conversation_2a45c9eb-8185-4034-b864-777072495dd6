import { Modal, Select, Input, Button, InputNumber, Popconfirm } from 'antd';
import { Fragment, useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import React from 'react';
import ListTable from '@/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';
import { isEmpty, changeArrItem } from '@/utils/method';
import _sortBy from 'lodash/sortBy';
import _last from 'lodash/last';
import _get from 'lodash/get';
// import { error } from '@/components/Notice';
import Tip from '@/components/Tip';
const {Option} = Select;
const DictType = [
  {value:1,label:'行'},
  {value:2,label:'列'},
  {value:3,label:'数字'},
  {value:4,label:'补充资料'},
];
const initObjStyle = {
  id:new Date().valueOf(),
  type:1,
  type_y:2,
  operating:undefined,
  number:undefined,
  number_y:undefined,
  table:undefined,
  tableName:undefined,
  cloumCheck:'+',
  rowCheck:'+',
  rowNumber:0,
  cloumNumber:0,
};
function index (props,ref){
  const {onOk,selData} = props;
  const [ visible , setVisible ] = useState(false);
  const [ value, setValue ] = useState([{...initObjStyle}]);
  const [ line, setLine ] = useState({min:0,max:0});
  useImperativeHandle(ref, () => ({
    open:(val)=>{
      open(val)
    },
  }));
  const handleOk=()=>{
    let flag = true;
    if(!isEmpty(value)){
      value.map((item,index)=>{
        const { type, operating, number, number_y, table, id } = item;
        if(typeof number !== 'number'){
          Tip.error('操作提示',`请检查${index+1}行 行数字 是否正确`);
          flag = false;
          return
        }
        if(typeof number_y !== 'number'&& (type !== 3 && type !== 4)){
          Tip.error('操作提示',`请检查${index+1}行 列数字 是否正确`);
          flag = false;
          return
        }
        if(isEmpty(table) && (type !== 3) ){
          Tip.error('操作提示', `请检查${index+1}行 表名称 是否正确`);
          flag = false;
          return
        }
        if(isEmpty(operating)){
          const { id:lastId = '' } = _last(value) || {};
          if( lastId !== id ){ // 最后一行不需要操作符
            Tip.error('操作提示',`请检查${index+1}行 操作符号 是否正确`);
            flag = false;
            return
          }
        }
      })
    }
    for (let i = 0; i < value.length; i++) {
      if(i+1==value.length){
        value[i]={...value[i],operating:''}
      }
    }
    if(flag){
      onOk && onOk(value);
      handleCancel();
    }
  };
  const destroy=()=>{
    setValue([initObjStyle]);
  };
  const handleCancel=()=>{
    destroy();
    setVisible(false);
  };
  const open=(val)=>{
    if(!isEmpty(val)){
      const last=JSON.parse(JSON.stringify(val));
      for (let i = 0; i <last.length ; i++) {
        if(i+1==last.length){
          last[i]={...last[i],operating:'+'}
        }
      }
      setValue(last);
    }else {
      const { tmwTable:{treeOrg:{levelCode = '', shortName:name  = ''} = {} } = {}} = props;
      setValue([{...initObjStyle,table:levelCode,tableName:name,operating:'+'}]);
    }
    setVisible(true);
  };
  const add=()=>{
    const { tmwTable:{treeOrg:{levelCode = '', shortName:name  = ''} = {} } = {}} = props;
    setValue([...value,{...initObjStyle,table:levelCode,tableName:name,id:new Date().valueOf(),operating:'+'}]);
  };
  const adds=(type)=>{
    const { tmwTable:{treeOrg:{levelCode = '', name  = ''} = {} } = {}} = props;
    const {min,max}=line;
    let data=[];
    for (let i = min; i <=max ; i++) {
      data.push({...initObjStyle,table:levelCode,id:`${new Date().valueOf()}_${i}`,operating:'+',number:i})
    }
    if(type=='clear'){
      setValue([...data])
    }else{
      if(value.length<2){
        setValue([...data])
      }else{
        setValue([...value,...data])
      }
    }
  };
  const onchangeItems= (val,record)=>{
    let arr = changeArrItem(val,record['id'],[...value]);
    setValue(arr);
  };
  const deleteItem=({id = undefined} = {})=>{
    const arr = [...value];
    setValue(arr.filter(it=> it['id'] !== id ));
  };
  //批量设置表名，请谨慎使用本功能
  const selSelect=async (val)=>{
    let newData = [];
    for (let i = 0; i <value.length; i++) {
      let obj=value[i];
      newData.push({...obj,table:val})
    }
    setValue(newData);
  };
  const columns= [
    {
      title:'序号',
      dataIndex:'serialNumber',
      width:50,
      render:(text,record,index)=><div>{index+1}</div>
    },
    {
      title:'表名称',
      dataIndex:'table',
      width:200,
      render:(text,record)=>{
        const { tmwTable:{ TreeList = [] , treeOrg:{levelCode = ''} = {} } = {}} = props;
        // let code = levelCode.split('—')[0];
        // let [find] = TreeList.filter(item =>item['levelCode'] === code);
        // const { children = [] } = find || {};
        const  onChange = async (val)=>{
          let [find] = selData.filter(it=>it['levelCode']===val);
          const {shortName = ''} = find || {};
          await onchangeItems({table:val,tableName:shortName}, record);
        };
        const {type = ''} = record || {};
        return (
          <Fragment>
            {
              type !== 3 &&
              <Select style={{width:'100%',maxWidth:200}} value={text} onChange={(val)=>onChange(val)}>
                { !isEmpty(selData) && selData.map((item,index) => <Option key={index} value={item['levelCode']}>{item['shortName']}</Option> ) }
              </Select>
            }
          </Fragment>
        )
      }
    },

    {
      title:'行数据类型',
      dataIndex:'type',
      width:120,
      // render:(text,record)=>{
      //   return (
      //     <div>行</div>
      //   )
      // }
      render:(text,record)=>{
        const onChange= (val)=>{
          const [typeFind] =  DictType.filter(it=>it['value']=== val);
          const {label = ''} = typeFind || {};
          if(val === 3){
            onchangeItems({
              type:val,
              typeName:label,
              table:undefined,
              tableName:undefined,
              cloumCheck:undefined,
              rowCheck:undefined,
              rowNumber:undefined,
              cloumNumber:undefined,
            },record);
          }else {
            onchangeItems({type:val,typeName:label},record);
          }
        };
        return (
          <Select style={{width:'100%'}} onChange={(val)=>onChange(val)} value={text}>
            {
              DictType.filter(it=>it.value !== 2).map((item,index)=><Option key={index} value={item['value']}>{item['label']}</Option>)
            }
          </Select>
        )
      }
    },
    {
      title:'行数字',
      dataIndex:'number',
      width:120,
      render:(text,record)=>{
        return (
          <InputNumber onChange={(val)=>onchangeItems({number:val},record)} value={text} min={0} style={{width:'100%'}}/>
        )
      }
    },

    {
      title:'列数据类型',
      dataIndex:'type_y',
      width:120,
      render:(text,record)=>{
        const {type = ''} = record || {};
        return (
          <Fragment>
            { (type !== 3 && type !== 4) && <div>列</div>}

          </Fragment>
        )
      }
    },
    {
      title:'列数字',
      dataIndex:'number_y',
      width:120,
      render:(text,record)=>{
        const {type = ''} = record || {};
        return (
          <Fragment>
             { (type !== 3 && type !== 4) && <InputNumber onChange={(val)=>onchangeItems({number_y:val},record)} value={text} min={0} style={{width:'100%'}}/>}
          </Fragment>
        )
      }
    },

    {
      title:'行偏移符号',
      dataIndex:'rowCheck',
      width:120,
      render:(text,record)=>{
        const {type = ''} = record || {};
        return (
          <Fragment>
            {
              type !== 3 &&
              <Select style={{width:'100%'}} value={text} onChange={(val)=>onchangeItems({rowCheck:val},record)} >
                <Option value={'+'}>+</Option>
                <Option value={'-'}>-</Option>
                <Option value={'*'}>*</Option>
                <Option value={'/'}>/</Option>
              </Select>
            }
          </Fragment>
        )
      }
    },
    {
      title:'行偏移数',
      dataIndex:'rowNumber',
      width:120,
      render:(text,record)=>{
        const {type = ''} = record || {};
        return (
          <Fragment>
            { type !== 3 && <InputNumber onChange={(val)=>onchangeItems({rowNumber:val},record)} value={text} min={0} style={{width:'100%'}}/> }
          </Fragment>
        )
      }
    },
    {
      title:'列偏移符号',
      dataIndex:'cloumCheck',
      width:120,
      render:(text,record)=>{
        const {type = ''} = record || {};
        return (
          <Fragment>
            {
              type !== 3 &&
              <Select style={{width:'100%'}} value={text} onChange={(val)=>onchangeItems({cloumCheck:val},record)} >
                <Option value={'+'}>+</Option>
                <Option value={'-'}>-</Option>
                <Option value={'*'}>*</Option>
                <Option value={'/'}>/</Option>
              </Select>
            }
          </Fragment>
        )
      }
    },
    {
      title:'列偏移数',
      dataIndex:'cloumNumber',
      width:120,
      render:(text,record)=>{
        const {type = ''} = record || {};
        return (
          <Fragment>
            {
              type !== 3 && <InputNumber onChange={(val)=>onchangeItems({cloumNumber:val},record)} value={text} min={0} style={{width:'100%'}}/>
            }
          </Fragment>
        )
      }
    },
    {
      title:'操作符号',
      dataIndex:'operating',
      width:120,
      render:(text,record)=>{
        const { id = '' } = _last(value) || {};
        // const {id = ''} = _last(_sortBy(value,['id'])) || {};
        return (
          <Fragment>
            {
              id !== record['id'] &&
              <Select style={{width:'100%'}} value={text} onChange={(val)=>onchangeItems({operating:val},record)} >
                <Option value={'+'}>+</Option>
                <Option value={'-'}>-</Option>
                <Option value={'*'}>*</Option>
                <Option value={'/'}>/</Option>
              </Select>
            }
          </Fragment>
        )
      }
    },
    {
      title:'操作',
      dataIndex: 'action',
      width:60,
      render:(text,record)=>{
        const {id:lastId = ''} = _last(value) || {};
        return(
          <Fragment>
            {
              lastId !== record['id'] && <React.Fragment>
                {/*<Popconfirm title={'是否确认删除'} onConfirm={()=>deleteItem(record)}>*/}
                {/*  <a className={'del'}>删除</a>*/}
                {/*</Popconfirm>*/}
                <a onClick={()=>deleteItem(record)} className={'del'}>删除</a>
              </React.Fragment>
            }
          </Fragment>

        )
      }
    },
  ];
  return (
    <Fragment>
      <Modal
        title={'规则详情'}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        maskClosable={false}
        width={1200}
        destroyOnClose={true}
      >
        {
          visible &&
            <Fragment>
              <span>
                批量设置表名:
                <Select placeholder={'请谨慎操作'} style={{width:190}} onChange={selSelect} showSearch
                        filterOption={(val,option)=>{
                          const {children}=option.props;
                          return children.includes(val);
                        }}
                >
                  {
                    selData.map(obj=>{
                      return(
                        <Select.Option value={obj['levelCode']} key={obj['levelCode']}>{obj['shortName']}</Select.Option>
                      )
                    })
                  }
                </Select>
              </span>
              <span style={{marginLeft:12}}>
                 批量增加行:
                 <InputNumber min={0} onChange={(val=0)=>setLine(obj=>{return {...obj,min:val}})} /> 到 <InputNumber min={0} onChange={(val=0)=>setLine(obj=>{return {...obj,max:val}})}/>
                 <Button type={'primary'} style={{marginLeft:12}} onClick={adds}>追加</Button>
                 <Button type={'primary'} style={{marginLeft:12}} onClick={()=>adds('clear')}>替换</Button>
              </span>
              <WhiteSpace/>
              <Button type={'primary'} style={{width:'100%'}} onClick={add}>增加行</Button>
              <div style={{marginTop:10}}/>
              <ListTable columns={columns} data={value} pagination={false}/>
            </Fragment>
        }
      </Modal>
    </Fragment>
  )
}
export default forwardRef(index)
