import React, { Fragment, useEffect, useRef, useState } from 'react';
import ListTable from '@/components/ListTable';
import { But<PERSON>, Divider, Popconfirm } from 'antd';
import { incomeDelete, incomeGetList } from '@/pages/[unit]/services/index';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import Add from './components/add';
import Tip from '@/components/Tip';
import moment from 'moment';
const index = (props: any) => {
  const addRef: any = useRef();
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0 });
  const [list, setList] = useState([]);

  const action = async (p = {}) => {
    const { unitIn: { basicInfo: { code: economicCode = '' } = {} } = {} } = props;
    const {
      code = 500,
      data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await incomeGetList({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.page,
        economicCode,
        ...p,
      },
    });
    if (code === 0) {
      setList(list);
      setPagination({ page: pageNum, total, pageSize });
    }
  };

  const addOrEdit = (record: any) => {
    addRef.current.open(record);
  };
  const confirm = async (record: any) => {
    const { code } = record;
    const { code: resCode = 500 } = await incomeDelete({ code });
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      action();
    }
  };

  const columns = [
    {
      title: '记录时间',
      dataIndex: 'createTime',
      width: 200,
      render:(text)=>text ? moment(text).format('YYYY.MM.DD') : text
    },
    {
      title: '收入金额（万元）',
      dataIndex: 'incomeAmount',
      width: 200,
    },
    // {
    //   title: '负债金额（万元）',
    //   dataIndex: 'incurDebtsAmount',
    //   width: 200,
    // },
    {
      title: '操作',
      dataIndex: 'action',
      width: 200,
      render: (text, record) => {
        return (
          <span>
              <a onClick={() => addOrEdit(record)}>编辑</a>
              <Divider type="vertical"/>
              <Popconfirm title="确定要删除吗？" onConfirm={() => confirm(record)}>
               <a className={'del'}>删除</a>
              </Popconfirm>
            </span>
        );
      },
    },
  ];

  useEffect(() => {
    action({ pageNum: 1 });
  }, []);
  return (
    <Fragment>
      <div style={{ marginBottom: 10 }}>
        <Button type={'primary'}
                onClick={() => {
                  if (addRef.current.open) {
                    addRef.current.open();
                  }
                }}
                icon={<LegacyIcon type={'plus'}/>}>添加</Button>
      </div>
      <ListTable columns={columns}
                 data={list}
                 pagination={pagination}
                 onPageChange={(page: any, pageSize: any) => {
                   action({ pageNum: page, pageSize });
                 }}/>
      <Add ref={addRef} {...props} onOK={() => {
        action({ pageNum: 1 });
      }}/>
    </Fragment>
  );
};
export default index;
