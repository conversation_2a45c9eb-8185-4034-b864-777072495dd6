import React, { useRef, useState } from 'react';
import st from './index.less';
import { Form, Input, Spin, Tooltip } from 'antd';
import { _history as router } from '@/utils/method';
import { politicalBirthday } from '../services';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import { InfoCircleOutlined } from '@ant-design/icons';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 0 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
};
const index = (props: any) => {
  const input1Ref: any = useRef();
  const input2Ref: any = useRef();
  const input3Ref: any = useRef();

  const [spinning, setSpinning] = useState<any>(false);

  const {
    // 人员搜索表单标题图片
    titImg = require('../../../../assets/qzs/ourb.png'),
  } = props;

  const [form] = Form.useForm();

  const getList = async (p = {}) => {
    setSpinning(true);
    const {
      code: mainResCode = 500,
      data: { list: mainsList = [], ...others } = {},
    } = await politicalBirthday({
      data: {
        pageNum: 1,
        pageSize: 10,
        // idCard: idcard,
        // name: name,
        ...p,
      },
    });
    setSpinning(false);
    if (mainResCode == 0) {
      if (mainsList.length == 0) {
        Tip.error('操作提示', '没有找到党员信息');
        return;
      }
      router.push(`/qzs/screen/birthSearch/list?joinOrgDate=${p.joinOrgDate}`);
    }
  };

  const hadndleFinish = async (v) => {
    getList({ ...v });
  };

  return (
    <div className={st.searchPage}>
      <Spin spinning={spinning}>
        <div className={st.center}>
          <img className={st.tit} src={titImg} alt="" />
          <Form className={st.form} form={form} onFinish={hadndleFinish} {...formItemLayout}>
            <Form.Item name={'joinOrgDate'} rules={[{ required: false, message: '必填' }]}>
              <Input placeholder="请输入政治生日" maxLength={8}></Input>
            </Form.Item>
            <div className={st.tooltip}>
              <Tooltip title="（例如10月10日，格式为1010）">
                <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
              </Tooltip>
            </div>
          </Form>
          <div
            className={st.btn}
            onClick={() => {
              form.submit();
            }}
          >
            搜 &nbsp; 索
          </div>
        </div>
      </Spin>
    </div>
  );
};

export default index;
