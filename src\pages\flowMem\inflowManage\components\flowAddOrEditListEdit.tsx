import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import { Input, Modal, Button } from 'antd';
import moment from 'moment';
import DictSelect from '@/components/DictSelect';
import { formLabel, findDictCodeName } from '@/utils/method';
import Date from '@/components/Date';
import _isEmpty from 'lodash/isEmpty';
import Tip from '@/components/Tip';
import { outManageDetailAdd, outManageDetailEdit, outManageDetailData } from '../../service/index';
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
class ListEdit extends React.Component<any, any> {
  // 构造函数
  constructor(props: any) {
    super(props);
    // 初始化state
    this.state = {
      visibleHd: false,
      modalTitleHd: '活动信息',
      confirmLoadingHd: false,
      basicInfoHd: {},
      editType: '',
    };
  }
  open = ({ record, type }) => {
    if (type === 'add') {
      this.setState({
        modalTitleHd: '新增活动',
        basicInfoHd: {},
      });
    }
    if (type === 'edit') {
      this.setState({
        modalTitleHd: '编辑活动',
      });
      this.getBasicInfoHd(record.id);
    }
    if (type === 'readonly') {
      this.setState({
        modalTitleHd: '查看活动',
      });
      this.getBasicInfoHd(record.id);
    }
    this.setState({
      visibleHd: true,
      editType: type,
    });
  };
  getBasicInfoHd = async (id) => {
    const { code = 500, data = {} } = await outManageDetailData({
      data: {
        id: id,
      },
    });
    if (code === 0) {
      this.setState({
        basicInfoHd: data,
      });
    }
  };
  // 确认按钮点击事件
  handleOkHd = async () => {
    const { basicInfoHd } = this.state;
    const { memInfo, modalType } = this.props;
    console.log('memInfo===', memInfo);
    let flowDirectionType = '';
    if (modalType === 'edit-outTab2') {
      flowDirectionType = '1';
    }
    if (modalType === 'edit-inTab2') {
      flowDirectionType = '2';
    }

    // console.log('modalType===', modalType);
    // console.log('basicInfoHd===', basicInfoHd);
    // console.log('form===', this.props);

    this.props.form.validateFields(async (err, values) => {
      if (!err) {
        this.setState({
          confirmLoadingHd: true,
        });
        if (!_isEmpty(values.eventDate)) {
          values.eventDate = moment(values.eventDate).valueOf();
        }
        let params = {
          flowDirection: flowDirectionType,
          cityCode: memInfo.outAdministrativeDivisionCode,
          memFlowCode: memInfo.code,
          memCode: memInfo.memCode,
          ...values,
        };
        console.log("🚀 ~ ListEdit ~ this.props.form.validateFields ~ params:", params)
        let url = outManageDetailAdd;
        if (!_isEmpty(basicInfoHd)) {
          params = {
            ...basicInfoHd,
            ...params,
          };
          url = outManageDetailEdit;
        }
        const { code } = await url({
          data: params,
        });
        if (code === 0) {
          Tip.success('操作提示', '操作成功');
          this.handleCancelHd();
          this.props.onOk();
        }
      }
    });
  };
  // 取消按钮点击事件
  handleCancelHd = () => {
    this.setState({
      visibleHd: false,
      confirmLoadingHd: false,
    });
  };
  render() {
    const { getFieldDecorator, setFieldsValue, getFieldValue } = this.props.form;
    const { children, tipMsg = {}, commonDict } = this.props;
    const {
      confirmLoadingHd = false,
      modalTitleHd = '活动信息',
      visibleHd = false,
      basicInfoHd = {},
      editType = 'add',
    } = this.state;
    return (
      <Modal
        footer={
          editType === 'readonly' ? null : (
            <Fragment>
              <Button
                onClick={() => {
                  this.handleCancelHd();
                }}
              >
                取消
              </Button>
              <Button
                loading={confirmLoadingHd}
                onClick={() => {
                  this.handleOkHd();
                }}
                type="primary"
              >
                发送
              </Button>
            </Fragment>
          )
        }
        destroyOnClose
        width={800}
        title={modalTitleHd}
        visible={visibleHd}
        onOk={this.handleOkHd}
        onCancel={this.handleCancelHd}
      >
        <Form {...formItemLayout}>
          <FormItem label={formLabel('活动类型', tipMsg['eventType'])}>
            {getFieldDecorator('eventType', {
              initialValue: _isEmpty(basicInfoHd) ? undefined : basicInfoHd.eventType,
              rules: [{ required: true, message: '请选择活动类型' }],
            })(
              <DictSelect
                disabled={editType === 'readonly'}
                initValue={_isEmpty(basicInfoHd) ? undefined : basicInfoHd.eventType}
                // 20250217 更改活动类型字典表为dict_d210
                codeType={'dict_d210'}
                // codeType={'dict_d158'}
                onChange={(e) => {
                  console.log('e==', e);
                }}
              />,
            )}
          </FormItem>
          <FormItem label={formLabel('活动日期', tipMsg['eventDate'])}>
            {getFieldDecorator('eventDate', {
              initialValue: _isEmpty(basicInfoHd) ? undefined : basicInfoHd.eventDate,
              rules: [{ required: true, message: '请选择活动日期' }],
            })(<Date disabled={editType === 'readonly'} />)}
          </FormItem>
          <FormItem label={formLabel('活动名称', tipMsg['eventName'])}>
            {getFieldDecorator('eventName', {
              initialValue: _isEmpty(basicInfoHd) ? undefined : basicInfoHd.eventName,
              rules: [{ required: true, message: '请填写活动名称' }],
            })(
              <Input.TextArea
                disabled={editType === 'readonly'}
                rows={2}
                placeholder="活动名称"
                maxLength={100}
                showCount
              />,
            )}
          </FormItem>
          <FormItem label={formLabel('组织活动说明', tipMsg['eventExplain'])}>
            {getFieldDecorator('eventExplain', {
              initialValue: _isEmpty(basicInfoHd) ? undefined : basicInfoHd.eventExplain,
              rules: [{ required: true, message: '请填写组织活动说明' }],
            })(
              <Input.TextArea
                disabled={editType === 'readonly'}
                rows={4}
                placeholder="组织活动说明"
                maxLength={300}
                showCount
              />,
            )}
          </FormItem>
        </Form>
      </Modal>
    );
  }
}
export default Form.create()(ListEdit);
