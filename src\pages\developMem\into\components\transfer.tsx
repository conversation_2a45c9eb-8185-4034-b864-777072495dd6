/**
 * 模块名
 */
import React, { Fragment } from 'react';
import { connect } from "dva";
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  Col,
  DatePicker,
  Modal,
  Radio,
  Row,
  Input,
  Switch,
  InputNumber,
  AutoComplete,
  Divider,
  Select,
  Alert, Dropdown, Menu, Popconfirm,
} from 'antd';
import OrgSelect from "@/components/OrgSelect";
import MemSelect from "@/components/MemSelect";
import DictArea from '@/components/DictArea';
import ListTable from "@/components/ListTable";
import Tip from '@/components/Tip';
import { root } from '@/common/config.js';
import WhiteSpace from '@/components/WhiteSpace';
import { findDZBOrgByOrgId, orgTrans } from '@/pages/transfer/services';
import UploadComp from '@/components/UploadComp';

const TextArea = Input.TextArea;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 2 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 21 },
  },
};

// @ts-ignore
@connect((({ memDevelop, loading }) => ({ memDevelop, memTrans: loading.effects['memDevelop/transferMem'], orgTrans: loading.effects['memDevelop/addTransfer'] })), undefined, undefined, { forwardRef: true })
class TransferBet extends React.Component<any, any> {
  static open() { }
  static close() { }
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      key: new Date().valueOf(),
    };
    TransferBet.open = this.open;
  }
  handleOk = () => {
    const { orgData = [] } = this.state;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        let uploadList: any = [];
        if (val['srcOrgId']) {
          val['srcOrgName'] = val['srcOrgId'][0]['name'];
          val['srcOrgId'] = val['srcOrgId'][0]['code'];
        }
        val['type'] = '212';//212 系统内区县整建制转出
        delete val['outMem'];
        delete val['_excelPath'];
        orgData.forEach(item => {
          if (item['excelPath']) {
            uploadList.push({
              code: item['code'],
              orgCode: item['orgCode'],
              zbCode: item['zbCode'],
              excelPath: item['excelPath'],
            })
          }
        });
        if (uploadList.length > 0) {
          orgTrans({
            data: {
              ...val,
              uploadList
            }
          }).then(res => {
            if (res && res['code'] === 0) {
              Tip.success('操作提示', '关系转接申请已提交');
              this.props.refresh();
              this.handleCancel();
            }
          })
        }
      }
    })
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      orgData: []
    })
  };
  open = () => {
    this.setState({
      visible: true,
      orgData: []
    })
  };
  getOrgList = (data) => {
    findDZBOrgByOrgId({
      data: {
        orgCode: data[0]['orgCode'],
        pageNum: 1,
        pageSize: 200,
      }
    }).then(res => {
      if (res['code'] == 0) {
        this.setState({
          orgData: res['data']['list']
        })
      } else {
        this.setState({
          orgData: []
        })
      }
    })
  }
  del = (item) => {
    let { memData } = this.state;
    memData = memData.filter(obj => obj['id'] !== item['id']);
    this.setState({
      memData
    });
    let names: Array<string> = [];
    for (let obj of memData) {
      names.push(obj['name'])
    }
    this['mem'].setState({
      value: names.join('，'),
    });
  };
  render() {
    const { visible, orgData = [] } = this.state;
    const { getFieldDecorator } = this.props.form;
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 50,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '组织名称',
        dataIndex: 'name',
        width: 270,
      },
      {
        title: '组织类别',
        width: 200,
        dataIndex: 'd01Name',
      },
      {
        title: '隶属关系',
        width: 160,
        dataIndex: 'd03Name',
      },
      {
        title: '联系人',
        width: 100,
        dataIndex: 'contacter',
      },
      {
        title: '联系方式',
        width: 110,
        dataIndex: 'contactPhone',
      },
      {
        title: '党组织书记',
        width: 100,
        dataIndex: 'secretary',
      },
      {
        title: '新增人员',
        dataIndex: 'action',
        width: 100,
        // render: (text, record) => {
        //   return (
        //     <Fragment>
        //       <FormItem>
        //         {getFieldDecorator('_excelPath', {
        //           initialValue:'',
        //           rules: [{ required: true, message: '请选择' }],
        //         })(
        //           <UploadComp
        //             maxLen={1}
        //             onChange={(list)=>{
        //               if(list.length>0){
        //                 const {response}=list[0];
        //                 if(response){
        //                   const {url=''}=response['data']['0'];
        //                   record['excelPath']=url;
        //                 }
        //               }else{
        //                 record['excelPath']=undefined;
        //               }
        //             }}
        //           />
        //         )}
        //       </FormItem>
        //     </Fragment>
        //   )
        // },
      },
    ];
    return (
      <div>
        <Modal
          destroyOnClose
          title="新增关系转入（整建制）"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1300}
          bodyStyle={{ height: 560, overflow: 'auto' }}
        >
          <Form {...formItemLayout}>
            <WhiteSpace />
            <FormItem label="转入组织">
              {getFieldDecorator('srcOrgId', {
                initialValue: '',
                rules: [{ required: true, message: '请选择' }],
              })(
                <OrgSelect ref={e => this['mem'] = e} onChange={this.getOrgList} />
              )}
            </FormItem>
            <FormItem
              label="组织列表"
            >
              {getFieldDecorator('outMem', {
                initialValue: '',
                rules: [{ required: false, message: '请选择' }],
              })(
                <ListTable columns={columns} data={orgData || []} pagination={false} />
              )}
            </FormItem>
            <FormItem label="转接原因">
              {getFieldDecorator('reason', {
                rules: [{ required: true, message: '请输入转接原因' }],
              })(
                <TextArea rows={4} placeholder={'转接原因'} />
              )}
            </FormItem>
          </Form>
        </Modal>
      </div>
    )
  }
}
export default Form.create()(TransferBet)
