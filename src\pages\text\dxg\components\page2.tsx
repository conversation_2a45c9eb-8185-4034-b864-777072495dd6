import React, { useRef, useEffect, useState, Fragment, forwardRef, useImperativeHandle } from 'react';
import ListTable from '@/components/ListTable';
import Modal from './index2';
import { Input, Select, Button, InputNumber, Checkbox, Popconfirm, Divider } from 'antd';
import { isEmpty } from '@/utils/method';
import {changeArrItem} from '@/utils/method.js';
import {addTableRuler,findListByTable, addTableSingleRuler, findListBySingleTable} from '../services';
// import { error, success } from '@/components/Notice';
import Tip from '@/components/Tip';
const { TextArea } = Input;
const {Option} = Select;
const clientHeight=document.body.clientHeight;
const initObjStyle = {
  id:new Date().valueOf(),
  rightJson:undefined,
  isColumnAll:0,
  isRowAll:0,
  contrast:undefined,
  leftJson:undefined,
  maxRow:undefined,
  maxColumn:undefined,
  remark:undefined,
  minRow:1,
  minColumn:1
};

function sortNumber(itemA,itemB) {
  let a=itemA['tableRuler'],b=itemB['tableRuler'],aSeparator,bSeparator;
  if(a.includes('.')){
    aSeparator='.'
  }
  if(a.includes('．')){
    aSeparator='．'
  }
  if(b.includes('.')){
    bSeparator='.'
  }
  if(b.includes('．')){
    bSeparator='．'
  }
  if(aSeparator && bSeparator){
    let aIndex=a.indexOf(aSeparator)
    let bIndex=b.indexOf(bSeparator)
    let aNum=a.substring(0,aIndex);
    let bNum=b.substring(0,bIndex);
    let res=Number(aNum)-Number(bNum);
    if(isNaN(res)){
      return 0
    }
    return res;
  }
  return false
}


function index(props,ref) {
  const { tmwTable:{ TreeList = [] , treeOrg = {} }={} } = props || {};
  const {name = '',levelCode = '', parentCode = ''} = treeOrg;
  const modalRef:any = useRef(null);
  const [ allVal, setAllVal ] = useState([initObjStyle]);
  const [id,setId] = useState(0);
  const [ loR, setLoR ] = useState(null); // 记录点击某项
  const [ selVal, setSelVal ] = useState();
  const [ link, setLink ] = useState([]);
  useEffect(()=>{
    if(!isEmpty(levelCode)){
      getList(levelCode);
    }
  },[levelCode]);
  useImperativeHandle(ref, () => ({
    reset:()=>{
      setSelVal(undefined);
      setLink([]);
    },
  }));
  const onClick=(record,type)=>{
    setId(record['id']);
    setLoR(type);
    modalRef.current.open(record[type]);
  };
  const add=()=>{
    setAllVal([...allVal,{ ...initObjStyle,id:new Date().valueOf()}]);
  };
  const getList= async (levelCode)=>{
    setAllVal([initObjStyle]);
    setId(0);
    setLoR(null);
    const {code = 500 ,data = [] } =await findListBySingleTable({reportCode:levelCode});
    if(code === 0 && !isEmpty(data)){
      let arr:any = [];
      arr.sort(sortNumber)
      setAllVal(data);
      // let arr:any = [];
      // data.map(item=>{
      //   arr.push({
      //     ...JSON.parse(item['frontData'])
      //   })
      // });
      // setAllVal(arr);
    }
  };
  const selSelect=(val)=>{
    getList(val).then(r => {});
    setSelVal(val);
  };
  // 模态框确定
  const onOk=(val)=>{
    // @ts-ignore
    let arr = changeArrItem({ [loR]:val },id,[...allVal]);
    setAllVal(arr);
  };
  const boxChange=(e,type,record)=>{
    setId(record['id']);
    const { target:{ checked = false } = {} } = e || {};
    let arr = changeArrItem({[type]:checked ? 1 : 0},record['id'],[...allVal]);
    setAllVal(arr);
  };
  const renderJson=(arr)=>{
    console.log(arr,'arr');
    let str = '';
    if(!isEmpty(arr)){
      // _last(arr).operating = undefined;
      arr.map(item=>{
        const {operating = '',number = '', number_y = '', tableName = '', rowCheck = '', rowNumber = '', cloumCheck = '', cloumNumber = '' } = item || {};
        let opera =isEmpty(operating) ? ' ' : ` ${operating} `;
        let offsetStr = (rowNumber === 0 && cloumNumber === 0) ? '' :`(行偏移${rowCheck}${rowNumber},列偏移${cloumCheck}${cloumNumber})`;
        str = str + `${tableName},行${number},列 ${number_y} ${offsetStr}${opera}`;
      })
    }
    return str;
  };
  // 保存
  const save=async()=>{
    let arr = [...allVal];
    if(!isEmpty(arr)){
      arr = arr.map(item=>{
        let obj = {
          ...item,
          table:levelCode,
          tableName:name,
          type:parentCode,
        };
        return {...obj,frontData:obj}
      })
    }
    let flag = true;
    arr.map((item,index)=>{
      if(isEmpty(item['leftJson']) || isEmpty(item['rightJson'])){
        Tip.error('操作提示',`请检查${index+1}行 等式左右2侧 必填`);
        flag = false;
        return
      }
      // if(!(typeof item['maxColumn'] === 'number' || typeof item['maxRow'] === 'number')){
      //   Tip.error('操作提示',`请检查${index+1}行 表最大行列数 必填`);
      //   flag = false;
      //   return
      // }
      if(isEmpty(item['contrast'])){
        Tip.error('操作提示',`请检查${index+1}行 符号 必填`);
        flag = false;
        return
      }
      // if( !((item['isRowAll'] === 0 && item['isColumnAll'] === 1) || (item['isRowAll'] === 1 && item['isColumnAll'] === 0) ) ){
      //   Tip.error('操作提示',`请检查${index+1}行 拉通行列2选1 必填`);
      //   flag = false;
      //   return
      // }
      if( typeof item['rulerType'] !== 'number' ){
        Tip.error('操作提示',`请检查${index+1}行 规则类别 必填`);
        flag = false;
        return
      }
      if(isEmpty(item['remark'])){
        Tip.error('操作提示',`请检查${index+1}行 规表备注 必填`);
        flag = false;
        return
      }
      if(isEmpty(item['tableRuler'])){
        Tip.error('操作提示',`请检查${index+1}行 规则描述 必填`);
        flag = false;
        return
      }
      // if(isEmpty(item['connectionTable'])){
      //   error(`请检查${index+1}行 连接表 必填`);
      //   flag = false;
      //   return
      // }
    });
    if(flag){
      const {code = 500} = await addTableSingleRuler({data:arr});
      if(code === 0){
        Tip.success('操作提示','');
      }
    }
  };
  const onchangeItems= (val,record)=>{
    let arr = changeArrItem(val,record['id'],[...allVal]);
    setAllVal(arr);
  };
  const linkChange=(val)=>{
    let newData:any=[];
    for (let i = 0; i <allVal.length; i++) {
      let obj=allVal[i];
      newData.push({...obj,connectionTable:val})
    }
    setAllVal(newData);
  };
  //批量设置公式右侧表名 谨慎操作
  const rightTableChange=(val)=>{
    // console.log(val,allVal,'vvvvvvvvvvvvvvvvvvvvvv');
    let find = TreeList.find(obj=>obj['levelCode']==val),newData:any=[];
    for (const item of allVal) {
      let {rightJson=[]}=item;
      for (let i = 0; i <rightJson.length ; i++) {
        // @ts-ignore
        rightJson[i]={...rightJson[i],table:val,tableName:find['name']};
      }
      newData.push({...item})
    }
    // console.log(newData,'last')
    setAllVal(newData);
  }
  //设置最大列 最大行
  const changeMax=(val,key)=>{
    // console.log(allVal,'aaaaaaaaaaaaaaa');
    let newData:any=[];
    for (const obj of allVal) {
      if(key=='row'){
        newData.push({...obj,maxRow:Number(val)})
      }
      if(key=='column'){
        newData.push({...obj,maxColumn:Number(val)})
      }
    }
    setAllVal(newData);
  };
  //设置表备注
  const changeRemark=(val)=>{
    // console.log(allVal,'aaaaaaaaaaaaaaa');
    let newData:any=[];
    for (const obj of allVal) {
      newData.push({...obj,remark:val})
    }
    setAllVal(newData);
  };
  const columns= [
    {
      title:'序号',
      dataIndex:'tableIndex',
      width:50,
      render:(text, record, index)=><div>{index+1}</div>
    },
    {
      title:'公式左侧',
      dataIndex:'leftJson',
      width:300,
      render:(text,record)=>{
        let str = renderJson(text);
        return (
          <TextArea rows={4} onClick={()=>onClick(record,'leftJson')} value={str}/>
        )
      }
    },
    {
      title:'符号',
      dataIndex:'contrast',
      width:80,
      render:(text,record)=>{
        return (
          <Select style={{width:'100%'}} onChange={(val)=>onchangeItems({contrast:val},record)} value={text}>
            <Option value={'>'}>{`>`}</Option>
            <Option value={'<'}>{`<`}</Option>
            <Option value={'=='}>{`=`}</Option>
            <Option value={'>='}>{`>=`}</Option>
            <Option value={'<='}>{`<=`}</Option>
          </Select>
        )
      }
    },
    {
      title:'公式右侧',
      dataIndex:'rightJson',
      width:300,
      render:(text,record)=>{
        let str = renderJson(text);
        return (
          <TextArea rows={4} onClick={()=>onClick(record,'rightJson')} value={str}/>
        )
      }
    },
    // {
    //   title:'表最小列数',
    //   dataIndex:'minColumn',
    //   width:80,
    //   render:(text,record)=>{
    //     return (
    //       <InputNumber min={0} style={{width:'100%'}} onChange={(val)=>onchangeItems({minColumn:val},record)} value={text}/>
    //     )
    //   }
    // },
    // {
    //   title:'表最大列数',
    //   dataIndex:'maxColumn',
    //   width:80,
    //   render:(text,record)=>{
    //     return (
    //       <InputNumber min={0} style={{width:'100%'}} onChange={(val)=>onchangeItems({maxColumn:val},record)} value={text}/>
    //     )
    //   }
    // },
    // {
    //   title:'表最小行数',
    //   dataIndex:'minRow',
    //   width:80,
    //   render:(text,record)=>{
    //     return (
    //       <InputNumber min={0} style={{width:'100%'}} onChange={(val)=>onchangeItems({minRow:val},record)} value={text}/>
    //     )
    //   }
    // },
    // {
    //   title:'表最大行数',
    //   dataIndex:'maxRow',
    //   width:80,
    //   render:(text,record)=>{
    //     return (
    //       <InputNumber min={0} style={{width:'100%'}} onChange={(val)=>onchangeItems({maxRow:val},record)} value={text}/>
    //     )
    //   }
    // },
    // {
    //   title:'是否拉通列',
    //   dataIndex:'isRowAll',
    //   width:50,
    //   render:(text,record)=>{
    //     return (
    //       <Checkbox onChange={(e)=>boxChange(e,'isRowAll',record)} checked={text === 1}/>
    //     )
    //   }
    // },
    // {
    //   title:'是否拉通行',
    //   dataIndex:'isColumnAll',
    //   width:50,
    //   render:(text,record)=>{
    //     return (
    //       <Checkbox onChange={(e)=>boxChange(e,'isColumnAll',record)} checked={text === 1}/>
    //     )
    //   }
    // },
    {
      title:'规则类别',
      dataIndex:'rulerType',
      width:100,
      render:(text,record)=>{
        return (
          <Select style={{width:'100%'}} onChange={(val)=>onchangeItems({rulerType:val},record)} value={text}>
            <Option value={1}>表内</Option>
            <Option value={2}>表间</Option>
          </Select>
        )
      }
    },
    {
      title:'连接表',
      dataIndex:'connectionTable',
      width:200,
      render:(text,record)=>{
        const { tmwTable:{ TreeList = [] , treeOrg:{parentCode = ''} = {} } = {}} = props;
        let [find] = TreeList.filter(item =>item['levelCode'] === parentCode);
        const { children = [] } = find || {};
        return (
          <Select style={{width:'100%'}} onChange={(val)=>onchangeItems({connectionTable:val},record)} value={text} mode={'multiple'}>
            { !isEmpty(TreeList) && TreeList.map((item,index) => <Option key={index} value={item['levelCode']}>{item['shortName']}</Option> ) }
          </Select>
        )
      }
    },
    {
      title:'规则描述',
      dataIndex:'tableRuler',
      width:150,
      render:(text,record)=>{
        return (
          <TextArea rows={4} onChange={(val)=>onchangeItems({tableRuler:val.target.value},record)} value={text}/>
        )
      }
    },
    {
      title:'表备注',
      dataIndex:'remark',
      width:150,
      render:(text,record)=>{
        return (
          <TextArea rows={4} onChange={(val)=>onchangeItems({remark:val.target.value},record)} value={text}/>
        )
      }
    },
    {
      title:'操作',
      dataIndex: 'action',
      width:100,
      render:(text,record)=>{
        const deleteItem=(record)=>{
          let arr = [...allVal].filter(it=>it['id'] != record['id']);
          setAllVal(arr);
        };
        const copy=()=>{
          setAllVal([...allVal,{...record,id:new Date().valueOf()}])
        };
        return(
          <Fragment>
            <a onClick={copy}>复制</a>
            <Divider type={'vertical'}/>
            <Popconfirm title={'是否确认删除'} onConfirm={()=>deleteItem(record)}>
              <a className={'del'}>删除</a>
            </Popconfirm>
          </Fragment>
        )
      }
    },
  ];
  let opt=[];
  if(levelCode.includes('-')){
    let lastLevelCode=levelCode.split('-')[0] || '';
    let find1 = TreeList.find(obj=>obj['levelCode']==lastLevelCode);
    if(find1){
      opt=find1['children']
    }
  }else{
    let find1 = TreeList.find(obj=>obj['levelCode']==levelCode);
    if(find1){
      opt=find1['children']
    }
  }

  const classSpan={marginRight:12,display:'inline-block',marginBottom:10};
  return (
    <Fragment>
      <div>
        <div style={{fontSize:24,fontWeight:600,color:isEmpty(name)?'red':'black'}}>当前位置 {name}({levelCode})</div>
        <Button type={'primary'} onClick={add} style={{marginRight:10}}>增加</Button>
        <Button type={'primary'} onClick={save} style={{marginRight:10}}>保存</Button>
        <Select placeholder={'获取其它表规则，请谨慎操作'} style={{...classSpan,width:200}} value={selVal} onChange={selSelect} allowClear>
          {
            TreeList.map(obj=>{
              return(
                <Select.Option value={obj['levelCode']} key={obj['levelCode']}>{obj['name']}</Select.Option>
              )
            })
          }
        </Select>
        <span style={classSpan}>
            批量设置连接表：
             <Select placeholder={'批量设置连接表，请谨慎操作'} value={link} style={{width:250}} onChange={(val)=>setLink(val)} onBlur={()=>linkChange(link)}  mode={'multiple'} allowClear>
                {
                  TreeList.map(obj=>{
                    return(
                      <Select.Option value={obj['levelCode']} key={obj['levelCode']}>{obj['name']}</Select.Option>
                    )
                  })
                }
             </Select>
          </span>
        <span style={classSpan}>
            批量设置公式左右侧表名:
            <Select placeholder={'请谨慎操作'} style={{width:190}} onChange={rightTableChange} showSearch
                    filterOption={(val,option)=>{
                      return `${option?.children}`.includes(val);
                    }}
            >
              {
                opt.map(obj=>{
                  return(
                    <Select.Option value={obj['levelCode']} key={obj['levelCode']}>{obj['name']}</Select.Option>
                  )
                })
              }
            </Select>
          </span>
        <span style={classSpan}>
            批量设置最大列:<InputNumber key={levelCode} min={0} style={{width:60}} onBlur={e=>changeMax(e.target.value,'column')}/>
          </span>
        <span style={classSpan}>
            批量设置最大行:<InputNumber key={levelCode} min={0} style={{width:60}} onBlur={e=>changeMax(e.target.value,'row')}/>
          </span>
        <span style={classSpan}>
            批量设置表备注:<Input key={levelCode} style={{width:400}} onBlur={e=>changeRemark(e.target.value)}/>
          </span>
      </div>
      <ListTable columns={columns} data={allVal} scroll={{x:1000,y:clientHeight-250}} pagination={false}/>
      <Modal ref={modalRef} onOk={(val)=>onOk(val)} selData={TreeList} {...props}/>
    </Fragment>
  )
}
export default forwardRef(index);
