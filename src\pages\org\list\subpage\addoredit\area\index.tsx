import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Popconfirm } from 'antd';
import { caucusDelete, caucusGetList } from '@/pages/org/services/org';
import ListTable from '@/components/ListTable';
import Tip from '@/components/Tip';
import Add from './components/add';

const index = (props: any) => {
  const [pagination, setPagination] = useState<any>({ pageNum: 1, pageSize: 10, total: 0 });
  const [list, setList] = useState([]);
  const [tableLoading, setTableLoading] = useState(false);
  const addRef: any = useRef();
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 58,
      align: 'center',
      render: (text, record, index) => {
        return (pagination.pageNum - 1) * pagination.pageSize + index + 1;
      },
    },
    {
      title: '年度',
      dataIndex: 'year',
      width: 110,
      align: 'center',
    },
    {
      title: '党代会情况',
      dataIndex: 'congressSituationName',
      width: 110,
      align: 'center',
    },
    {
      title: '任届情况',
      dataIndex: 'situationName',
      width: 110,
      align: 'center',
    },
    {
      title: '党委委员',
      dataIndex: 'partyCommittee',
      width: 110,
      align: 'center',
    },
    {
      title: '党委候补委员',
      dataIndex: 'partyAlternateCommittee',
      width: 110,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 200,
      render: (text, record) => {
        const { type = '' } = record;
        return (
          <span>
            <a onClick={() => {
              addRef.current.open(record);
            }}>编辑</a>
            <Divider type="vertical" />
            <Popconfirm title="确定删除?" onConfirm={async () => {
              const { code = 500 } = await caucusDelete({ code: record?.code });
              if (code === 0) {
                Tip.success('操作提示', '操作成功');
                getList({ pageNum: 1 });
              }
            }}>
              <a className={'del'}>删除</a>
            </Popconfirm>
          </span>
        );
      },
    },
  ];

  const getList = async (p = {}) => {
    const { org: { basicInfo: { orgCode: orgCode = '' } = {} } = {} } = props;
    const { code = 500, data: { list = [], pageNumber: pageNum = 1, pageSize = 10, totalRow: total = 0 } = {} } = await caucusGetList({
      data: {
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
        orgCode,
        ...p
      }
    });
    if (code === 0) {
      setList(list);
      setPagination({ pageNum, pageSize, total, current: pageNum });
    }
  };
  useEffect(() => {
    getList({ pageNum: 1 });
  }, [])
  return (
    <div>
      <div style={{ marginBottom: '10px' }}>
        <Button type={'primary'} onClick={() => {
          addRef.current.open();
        }}>新增</Button>
      </div>
      <ListTable
        columns={columns}
        data={list}
        pagination={pagination}
        onPageChange={(page, pageSize) => {
          getList({ pageNum: page , pageSize});
        }}
        rowKey={'d42Name'}
      />
      <Add ref={addRef} onOK={() => {
        getList({ pageNum: 1 });
      }} {...props} />
    </div>
  )
};
export default index;
