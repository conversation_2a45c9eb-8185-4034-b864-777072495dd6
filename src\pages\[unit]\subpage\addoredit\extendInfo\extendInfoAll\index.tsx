import React, { Fragment, useEffect, useState, useRef } from 'react';
import {
  Form,
  Input,
  Modal,
  DatePicker,
  InputNumber,
  Row,
  Col,
  Select,
  Button,
  Divider,
  Spin,
  Popconfirm,
} from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import DictSelect from 'src/components/DictSelect';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import SearchUnit from '@/components/SearchUnit';
import { communityfindByCode, addUnitCommunity, findCountYear } from './services';
import style from '@/pages/[unit]/subpage/addoredit/basic.less';
import { findDictCodeName, formLabel, getCredit } from '@/utils/method';
import _isNumber from 'lodash/isNumber';
import _isEmpty from 'lodash/isEmpty';
import _trim from 'lodash/trim';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import moment from 'moment';
import { parseInt } from 'lodash';
import { DownOutlined } from '@ant-design/icons';
import MemSelect from '@/components/MemSelect';
import { getUnitName, normalList } from '@/services';
import { getSession } from '@/utils/session';
import { renderNewExtra, getZiDuanArr } from './renderNewExtra2024';
import renderNewExtra2023andBefore from './renderNewExtra2023andBefore';
import { ExclamationCircleOutlined } from '@ant-design/icons';

const formItemLayout = {
  labelCol: { span: 12 },
  wrapperCol: { span: 10 },
};
const formItemLayout4 = {
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
};

const index = (props) => {
  const { isEndModal } = props;
  const { basicInfo = {} } = props.unit;
  console.log(basicInfo,'basicInfobasicInfobasicInfo')
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [timeKey, setTimeKey] = useState(+new Date());
  const [year, setYear] = useState<any>();
  const [canEditYear, setCanEditYear] = useState<any>();
  const [showMore, setShowMore] = useState<any>(false);
  const [loading, setLoading] = useState<any>(false);
  const [countYear, setCountYear] = useState<any>(undefined);

  const [formData, setFormData] = useState<any>({});
  const [unitCode, setUnitCode] = useState<any>(basicInfo['firmLevelName']);
  const [unitName, setUnitName] = useState<any>();
  const [isGetName, setisGetName] = useState<any>(false);

  const clear = () => {
    form.resetFields();
    setConfirmLoading(false);
  };
  const onFinish = async (val) => {
    const org = getSession('org') || {};
    val = findDictCodeName(
      [
        'd04',
        'd05',
        'd35',
        'd95',
        'd112',
        'd109',
        'd81',
        'd95',
        'd110',
        'd111',
        'd118',
        'd159',
        'd194',
        'd195',
      ],
      val,
      formData,
    );
    if (val['isCzglbm'] && val['isCzglbm']['id']) {
      val['isCzglbmName'] = val['isCzglbm']['name'];
      val['isCzglbm'] = val['isCzglbm']['key'];
    }
    // if(val['d04Code'] && val['d04Code']['id']){
    //   val['d04Name']=val['d04Code']['name'];
    //   val['d04Code']=val['d04Code']['key'];
    // }
    // if(val['d05Code'] && val['d05Code']['id']){
    //   val['d05Name']=val['d05Code']['name'];
    //   val['d05Code']=val['d05Code']['key'];
    // }
    // if(val['d35Code'] && val['d35Code']['id']){
    //   val['d35Name']=val['d35Code']['name'];
    //   val['d35Code']=val['d35Code']['key'];
    // }
    // if(val['d48Code'] && val['d48Code']['id']){
    //   val['d48Name']=val['d48Code']['name'];
    //   val['d48Code']=val['d48Code']['key'];
    // }
    Object.keys(val).map((obj) => {
      // if (typeof val[obj] == 'boolean') {
      //   if (val[obj]) {
      //     val[obj] = 1;
      //   } else {
      //     val[obj] = 0;
      //   }
      // }
      if (typeof val[obj] == 'boolean') {
        if (val[obj] == 1) {
          val[obj] = 1;
        } else if (val[obj] == 0) {
          val[obj] = 0;
        }
      }
    });
    if (
      !val['isCreateOrg'] &&
      val['manageUnitOrgCode'] &&
      typeof val['manageUnitOrgCode'] === 'object'
    ) {
      if (!_isEmpty(val['manageUnitOrgCode'])) {
        val['manageOrgCode'] = val['manageUnitOrgCode'][0]['code'];
        val['manageOrgName'] = val['manageUnitOrgCode'][0]['name'];
        val['manageUnitOrgCode'] = val['manageUnitOrgCode'][0]['orgCode'];
      } else {
        val['manageUnitOrgCode'] = undefined;
      }
    }
    // val['createUnitOrgCode'] = org['orgCode'];
    // val['createOrgZbCode'] = org['zbCode'];
    if (val['linkedDTOList']) {
      let data: Array<object> = [];
      for (let obj of val['linkedDTOList']) {
        const { org = {} } = obj;
        if (org['code']) {
          //新增的关联组织
          data.push({
            orgCode: org['code'],
            orgName: org['name'],
            orgType: org['d01Code'],
            orgTypeName: org['d01Name'],
            linkedOrgCode: org['orgCode'],
            orgTypeCode: org['orgType'],
            isOrgMain: obj['isOrgMain'],
          });
        } else {
          //已关联的组织
          data.push(obj);
        }
      }
      val['linkedDTOList'] = data;
    }
    if (val['onPostNum'] < val['b30A12']) {
      Tip.error('提示', '党政机关工作人数必须小于在岗职工数');
      return;
    }
    if (val['zaigangGaoji'] > val['tecNum']) {
      Tip.error('提示', '专业技术人员中含高级职称必须小于在岗专业技术人员数');
      return;
    }
    if (val['specialFundsMasses'] > 200) {
      Tip.error('提示', '社区全年服务群众专项经费总额（万元）输入值必须小于200', 5);
      return;
    }
    if (val['operatingExpenses'] > 200) {
      Tip.error('提示', '运转经费（万元 ∕ 年）输入值必须小于200', 5);
      return;
    }
    if (val['villagePer'] > 100) {
      Tip.error('提示', '单位办公经费（万元 ∕ 年）输入值必须小于100', 5);
      return;
    }
    if (val['secretarySalary'] > 100) {
      Tip.error('提示', '党组织书记报酬（万元 ∕ 年）输入值必须小于100', 5);
      return;
    }
    if (val['includedFinancial'] > 200) {
      Tip.error('提示', '社区纳入财政预算的工作经费总额（万元）输入值必须小于200', 5);
      return;
    }
    if (val['communityWorkersSalary'] > 200) {
      Tip.error('提示', '全部社区工作者年工资总额（万元）输入值必须小于200', 5);
      return;
    }
    if (val['communityMoneyNum'] > 200) {
      Tip.error('提示', '纳入财政预算的工作经费总额（万元）输入值必须小于200', 5);
      return;
    }
    if (val['communityServingPeople'] > 200) {
      Tip.error('提示', '全年服务群众专项经费总额（万元）输入值必须小于200', 5);
      return;
    }
    // 当单位类别为街道的时候，当街道干部数字大于0的时候，
    // 后面填写的各年龄阶段相加应该等于总数；
    // 各学历阶段数字相加应该等于总数，
    // 各个身份数量相加应该等于总数
    if (basicInfo.d04Code.startsWith('911') && val.streetCadres > 0) {
      const { age35Below, age36ToAge55, age56Above } = val;
      const { collegeDegreeAbove, secondarySchoolBelow } = val;
      const { streetCadresCivil, streetCadresInstitutions, cadreOther } = val;
      let flag = false;
      let text = '';
      if (age35Below + age36ToAge55 + age56Above != val.streetCadres) {
        text =
          '各年龄阶段(街道干部35岁及以下人数,街道干部36至55岁人数,街道干部56岁及以上人数)相加应该等于街道干部人数';
        flag = true;
      }
      if (collegeDegreeAbove + secondarySchoolBelow != val.streetCadres) {
        text =
          '各学历阶段(街道干部大专及以上学历人数,街道干部高中中专及以下人数)数字相加应该等于街道干部人数';
        flag = true;
      }
      if (streetCadresCivil + streetCadresInstitutions + cadreOther != val.streetCadres) {
        text =
          '各个身份数量(街道干部公务员人数,街道干部事业单位人数,街道干部其他身份人数)相加应该等于街道干部人数';
        flag = true;
      }
      if (flag) {
        Tip.error('提示', text, 5);
        return;
      }
    }
    // d155Code 村（社区）类别 传参改为字符串
    if (!_isEmpty(val['d155Code'])) {
      if (typeof val['d155Code'] === 'object') {
        let nameArr: any = [];
        let codeArr: any = [];
        val['d155Code'].map((item: any, index) => {
          const { key = '', name = '' } = item;
          nameArr.push(name);
          codeArr.push(key);
        });
        val['d155Name'] = nameArr.toString();
        val['d155Code'] = codeArr.toString();
      } else {
        val['d155Name'] = formData['d155Name'];
      }
    }

    // 中间交换区
    val['firmLevelName'] =
      typeof val['firmLevelName'] == 'object' ? val['firmLevelName']?.code : val['firmLevelName'];
    if (val['hasIndustryProvince'] == 0) {
      val['d04Codes'] = '';
      val['unitInformation'] = '';
    }

    if (_isEmpty(val.d194Code)) {
      val.d194Code = '';
      val.d194Name = '';
    }
    if (_isEmpty(val.d195Code)) {
      val.d195Code = '';
      val.d195Name = '';
    }
    if (val['d195Code'] == 'V0000') {
      val['d195Name'] = '无';
    }

    if (typeof val['firstSecretaryCode'] === 'object') {
      val['firstSecretaryName'] = val['firstSecretaryCode'][0]['name'];
      val['firstSecretaryCode'] = val['firstSecretaryCode'][0]['code'];
    } else {
      val['firstSecretaryName'] = formData['firstSecretaryName'];
    }
    setConfirmLoading(true);
    const { code: resCode = 500 } = await addUnitCommunity({
      data: {
        year: year,
        code: formData.code,
        d04Code: formData.d04Code,
        ...val,
      },
    });
    setConfirmLoading(false);
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      getInfo(year);
      props.dispatch({
        type: 'unit/findOrg',
        payload: {
          code: basicInfo['code'],
        },
      });
      // onOK && onOK();
    }
  };
  const renderItem = (item, formItemLayout = {}) => {
    const { tipMsg = {} } = props;
    const { basicInfo = {} } = props.unit;
    const { getFieldValue } = form;
    const { lockFields = [] } = basicInfo;
    const {
      label,
      code,
      type,
      codeType,
      rules,
      boolText = {},
      backType = undefined,
      filter,
      initName = '',
      onChange,
    } = item;
    let node = <Input />;
    switch (`${type}`) {
      case 'boolean':
        // node = <Switch defaultChecked={basicInfo[code] == 1} />;
        node = (
          <Select
            style={{ width: '100%' }}
            disabled={lockFields.includes(code)}
            placeholder={'请选择'}
            onChange={(e) => {
              setTimeKey(+new Date());
              onChange && onChange(e);
            }}
          >
            <Select.Option value={1}>{boolText?.yes || '是'}</Select.Option>
            <Select.Option value={0}>{boolText?.no || '否'}</Select.Option>
          </Select>
        );
        break;
      case 'boolean2':
        // node = <Switch defaultChecked={basicInfo[code] == 1} />;
        node = (
          <Select
            style={{ width: '100%' }}
            disabled={lockFields.includes(code)}
            placeholder={'请选择'}
            onChange={() => {
              setTimeKey(+new Date());
            }}
          >
            <Select.Option value={1}>是</Select.Option>
            <Select.Option value={0}>否</Select.Option>
            <Select.Option value={2}>未配备</Select.Option>
          </Select>
        );
        break;
      case 'dict':
        node = (
          <DictTreeSelect
            key={code}
            disabled={lockFields.includes(code)}
            codeType={codeType}
            initValue={formData[code]}
            backType={backType}
            parentDisable={true}
            filter={filter}
            onChange={() => {
              setTimeKey(+new Date());
            }}
          />
        );
        break;
      case 'memselect':
        node = <MemSelect initValue={initName} placeholder="请选择" />;
        break;
      case 'number':
        node = (
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            max={99999999}
            placeholder={'请输入'}
            onChange={() => {
              setTimeKey(+new Date());
            }}
            disabled={lockFields.includes(code)}
          />
        );
        break;
      case 'search':
        node = (
          <SearchUnit
            disabled={lockFields.includes(code)}
            params={{ orgTypeList: ['3', '4'] }}
            initName={initName}
            initCode={formData[code]}
            backType={'object'}
            onChange={(e) => {
              form.setFieldsValue({
                d04Codes: e.d04Code,
                unitInformation: e.name,
              });
            }}
          />
        );
        break;
    }
    return (
      <Col span={12} style={{ display: 'flex' }}>
        <Row className={style.items}>
          <Col
            className={`${rules.find((it) => it.required) ? style.label : style.label2
              } ant-col ant-col-xs-24 ant-col-sm-10`}
          >
            <label htmlFor={code}>{formLabel(label, tipMsg[code])}</label>
          </Col>
          <Col span={12} className={style.desc}>
            <Form.Item
              key={code}
              name={code}
              rules={rules}
              {...formItemLayout4}
            // label={formLabel(label, tipMsg[code])}
            >
              {node}
            </Form.Item>
          </Col>
        </Row>
      </Col>
    );
  };
  const getDictValue = (formKey) => {
    const { getFieldValue } = form;
    let obj = getFieldValue(formKey);
    let val = obj;
    if (typeof obj == 'object') {
      val = obj['key'];
    }
    return val;
  };
  const clearForm = () => {
    form.resetFields();
    setFormData({});
  };

  const getInfo = async (years) => {
    setLoading(true);
    const res = await communityfindByCode({ unitCode: basicInfo.code, year: years });
    let newdata = res.data;
    let obj = {
      code: newdata.code,
      d04Code: newdata.d04Code,
    };
    if (years == 2023 && year == 2024) {
      let arrs = getZiDuanArr(form, getDictValue, formData, unitName);
      let arr: any = [];
      for (let o in arrs) {
        arr = arr.concat(arrs[o]);
      }
      for (let o in res.data) {
        if (arr.findIndex((i) => i.code == o) != -1) {
          obj[o] = res.data[o];
        }
      }
      newdata = obj;
    }

    setLoading(false);
    if (res.code == 0 && res.data) {
      form.setFieldsValue({
        // isAllocateDean:1,
        // isAllocateSecretary:1,
        // hasDeanPartyMember:1,e
        ...newdata,
      });
      setFormData(res.data);
      setUnitCode(res.data.firmLevelName);
      setTimeKey(+new Date());
    }
  };
  const getShow = () => {
    // let node = document.getElementById('modalend');
    // if (node) {
    //   setShowMore(node.offsetTop > 750);
    // }
    const div = document.getElementById('content');
    if (div) {
      let flag = div.scrollHeight > div.clientHeight || div.scrollWidth > div.clientWidth;
      setShowMore(flag);
    } else {
      setShowMore(false);
    }
  };
  const getYears = async () => {
    const res = await findCountYear({});
    const year = res?.data?.year ? parseInt(res?.data?.year) : undefined;
    setCanEditYear(year);
    setYear(year);
    getInfo(year);
    setCountYear(year)
  };
  const confirm = (e: React.MouseEvent<HTMLElement>) => {
    console.log(e);
    getInfo(2023);
  };

  const cancel = (e: React.MouseEvent<HTMLElement>) => {
    console.log(e);
  };

  useEffect(() => {
    getShow();
    getYears();
  }, [basicInfo.code]);

  useEffect(() => {
    getShow();
  }, [JSON.stringify(formData)]);

  useEffect(() => {
    window.addEventListener('resize', getShow);
  }, []);

  useEffect(() => {
    if (!isGetName && basicInfo && basicInfo['firmLevelShowName'] == 'zenith') {
      // @ts-ignore
      setisGetName(true);
      getUnitName({
        data: {
          pageNum: 1,
          pageSize: 100,
          code: unitCode || basicInfo['firmLevelName'],
        },
      }).then((res) => {
        if (res['code'] == '0') {
          const { list = [] } = res['data'];
          if (list.length > 0) {
            let obj = list[0];
            setUnitName(obj['name']);
          }
        }
      });
    }
  }, [unitCode]);

  const canEdit = year == canEditYear;
  return (
    <Spin spinning={loading}>
      <div style={{ textAlign: 'right' }}>
        {year == countYear && (
          <Popconfirm
            title="引用上年度数据将会覆盖当前填写数据，是否引用？"
            onConfirm={confirm}
            onCancel={cancel}
            okText="是"
            cancelText="否"
          >
            <Button
              style={{ marginRight: '10px' }}
              icon={<LegacyIcon type={'plus'} />}
              type="primary"
            >
              引用上年度数据
            </Button>
          </Popconfirm>
        )}
        年份选择：
        <Select
          style={{ width: 200, marginRight: 42, textAlign: 'left' }}
          value={year}
          onChange={(e) => {
            Modal.confirm({
              title: '当前界面数据未保存，切换后需重新填写，是否切换?',
              icon: <ExclamationCircleOutlined />,
              content: '',
              onOk() {
                setYear(e);
                getInfo(e);
                clearForm();
                props?.changeIsEndModal?.(false);
                let node2 = document.getElementById('content');
                if (node2) {
                  node2.scrollTop = 0;
                }
              },
              onCancel() {
                console.log('Cancel');
              },
            });
          }}
        >
          {(() => {
            let nowYear = canEditYear;
            if (!nowYear) return [];
            let arr: any = [];
            for (let y = 2021; y <= parseInt(nowYear); y++) {
              arr = [...arr, y];
            }
            return arr.reverse().map((ot) => (
              <Select.Option key={ot} value={ot}>
                {ot}年
              </Select.Option>
            ));
          })()}
        </Select>
      </div>
      <Divider></Divider>
      <div style={{ pointerEvents: !canEdit ? 'none' : 'auto' }}>
        <Form form={form} onFinish={onFinish}>
          {(function () {
            let renders;
            if (year >=2024) {
              renders = renderNewExtra;
            } else {
              renders = renderNewExtra2023andBefore;
            }
            return (
              <Row style={{ width: '100%' }}>
                {renders?.(props, form, formData, renderItem, getDictValue, unitName)}
              </Row>
            );
          })()}
        </Form>
      </div>
      {canEdit && (
        <div style={{ textAlign: 'center' }}>
          <WhiteSpace />
          <Button
            type={'primary'}
            htmlType={'submit'}
            icon={<LegacyIcon type={'check'} />}
            onClick={form.submit}
            style={{ marginRight: 16 }}
            loading={confirmLoading}
          >
            保存
          </Button>
          <Button
            danger
            type={'primary'}
            htmlType={'button'}
            icon={<LegacyIcon type={'delete'} />}
            onClick={() => props?.close?.({})}
          >
            取消
          </Button>
        </div>
      )}

      {showMore && !isEndModal && (
        <div style={{ position: 'relative' }}>
          <img
            src={require('@/assets/arrowd.png')}
            style={{
              position: 'fixed',
              bottom: '80px',
              left: '53%',
              color: '#40A9FF',
              fontSize: 20,
              width: 20,
              cursor: 'pointer',
              border: '1px solid #40A9FF',
              borderRadius: '50%',
              padding: 4,
            }}
            onClick={() => {
              let node = document.getElementById('modalend');
              if (node) {
                node.scrollIntoView({ behavior: 'smooth', block: 'end' });
              }
            }}
          />
        </div>
      )}
    </Spin>
  );
};
export default index;
