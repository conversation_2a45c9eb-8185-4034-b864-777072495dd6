/**
 * 新增编辑收支
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import {
  <PERSON><PERSON>,
  Button,
  Col,
  DatePicker,
  Divider,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Switch,
  Tag,
  Tooltip,
} from 'antd';
import {connect} from "dva";
import moment from 'moment'
import Notice from '@/components/Notice';
import { getSession } from '@/utils/session';
import ListTable from '@/components/ListTable';
import { isEmpty } from '@/utils/method';
import DictSelect from '@/components/DictSelect';
const FormItem=Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

@connect(({dues,login})=>({dues,login}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      queryDate:moment().format('YYYY-MM-DD'),
      stand:'1',
      months:1,
      url:'',
      editVisible:false,
      selectedRowKeys:[],
      isFirst:'1',
      selections:[],
    };
  }
  showModal=()=>{
    let org=getSession('org')|| {};
    this.setState({
      visible:true,
      org
    },()=>{
      this.onPage()
    });
  };
  disabledDate=(current)=>{
    return current && current > moment().endOf('day')||current<moment('2019');
  };
  onPage=( pageNum=1,pageSize=5)=>{
    this.props.dispatch({
      type:'dues/getWxBillList',
      payload:{
        data:{
          queryDate:moment(this.state['queryDate']).valueOf(),
          pageNum:pageNum,
          pageSize:pageSize,
          isFirst:this.state['isFirst']
        }
      }
    })
  };
  handleOk=()=>{
    const { selectedRowKeys } = this.state;
    if (isEmpty(selectedRowKeys)) {
      message.info('请选择流水');
    }else {
      this.setState({
        editVisible:true,
      })
    }
  };
  handleCancel=()=>{
    const { onChange } = this.props;
    onChange();
    this.setState({
      visible:false,
      queryDate:moment().format('YYYY-MM-DD'),
    });
  };
  isOk=()=>{
    const { org={},selectedRowKeys,selectedRowInfos,arrInfos }=this.state;
    const { onChange } = this.props;
    this.props.form.validateFieldsAndScroll(async(errors, values) => {
      if (errors){
        return
      }
      let feeDisburseDTOList =selectedRowInfos.map((item,index)=>{
        let obj={
          money:item['totalFee'],
          bankNum:item['outTradeNo'],
          recordTime:item['tradeTime'],
        };
        return obj
      });
      const { d68,...val } = values;
      let obj={
        orgCode:org['code'],
        disburseOrgCode:org['orgCode'],
        d68Code:d68['key'],
        d68Name:d68['name'],
        ...val,
        feeDisburseDTOList
      };
      for (let o in obj) {
        if (isEmpty(obj[o])) {
          delete obj[o]
        }
      }
      this.props.dispatch({
        type:'dues/saveByWxBill',
        payload:{
          data:{
            ...obj
          }
        }
      }).then(res=>{
        if (res['code'] == 0) {
          Notice("操作提示",'保存成功',"check-circle","green");
          this.isCancel();
          onChange()
        }else {
          Notice("操作提示",res['message'],"exclamation-circle-o","orange");
        }
      })

    })
  };
  isCancel=()=>{
    this.setState({
      editVisible:false,
      selectedRowKeys:[],
      selectedRowInfos:[],
    },()=>{
      this.onPage()
    });
    this.props.form.resetFields()
  };
  changePage=(v,k)=>{
    this.setState({page:v,pageNum:k,isFirst:'0'},()=>{
      this.onPage(v,k);
    })
  };
  onSelectChange=(selectedRowKeys,infos)=>{
    let { selectedRowInfos=[],arrInfos=[] }=this.state;
    infos.forEach(i => {
      if (!selectedRowInfos.map(i => i.outTradeNo).includes(i.outTradeNo)) {
        selectedRowInfos.push(i);
      }
    });
    if (selectedRowKeys.length < selectedRowInfos.length) {
      selectedRowInfos=selectedRowInfos.filter((it)=> selectedRowKeys.includes(it.outTradeNo));
    }
    this.setState({ selectedRowKeys,selectedRowInfos,arrInfos:infos});
  };
  changeTime=(v)=>{
    this.setState({queryDate:v,isFirst:'1'},()=>{
      this.onPage();
    })
  };
  disabledTomorrow=(current)=>{
    return current && current > moment().endOf('day')||current<moment('2019');
  };
  render(){
    const {visible,filterHeight,editVisible,selectedRowKeys,selections}=this.state;
    const {title='', data={},dues:{ list1=[],pagination1:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={} }={},loading:{effects = {}} = {},children }=this.props;
    const {getFieldDecorator}=this.props.form;
    let stirMoney=0
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },

      {
        title:'支出名称',
        dataIndex:'productName',
        width:100,
      },
      {
        title:'流水号',
        dataIndex:'outTradeNo',
        width:150,
        render:text => {
          return(
            <Tooltip title={text}>
              <span>{!isEmpty(text)&&text.substring(0,15)+'...'}</span>
            </Tooltip>
          )
        }
      },
      {
        title:'支出时间',
        dataIndex:'tradeTime',
        width:150,
        render:text => {
          return <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
        }
      },
      {
        title:'支出金额',
        dataIndex:'totalFee',
        width:100,
        render:text => {
          return <span>{'￥'+text}</span>
        }
      }
    ];
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
      getCheckboxProps: (record) => {
        return { disabled: !isEmpty(record['deleteTime']) };
      }
    };
    return (
      <div>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.showModal,
            key: 'container'
          }) : null
        }
        <Modal
          destroyOnClose
          title={'银行流水'}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={800}
          bodyStyle={{height:'auto',overflow:'auto'}}
        >
          <React.Fragment>
            <DatePicker defaultValue={moment(moment().format('YYYY/MM/DD'))} onChange={this.changeTime} disabledDate={this.disabledTomorrow} style={{marginBottom:'20px'}}/>
            <ListTable
              columns={columns}
              rowKey={record=>record.outTradeNo}
              data={list1}
              scroll={{y:filterHeight}}
              rowSelection={rowSelection}
              pagination={{pageSize,total:totalRow,page,current:pageNumber}}
              onPageChange={this.changePage}/>
          </React.Fragment>
        </Modal>
        <Modal
          destroyOnClose
          title={title||''}
          visible={editVisible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={600}
          bodyStyle={{height:'auto',overflow:'auto'}}
        >
          <Form {...formItemLayout}>
            <Row>
              <Col span={24}>
                <Alert
                  message="温馨提示"
                  description={
                    <div>
                      <p>1.该组织目前正式党员<span style={{color:'red'}}>{data['fullMemNum']}</span>人,预备党员
                        <span style={{color:'red'}}>{data['proMemNum']}</span>人
                        其中离退休党员<span style={{color:'red'}}>{data['retirementMemNum']}</span>人,设置标准应收金额
                        <span style={{color:'red'}}>{data['shouldPayMoney']}</span>元,已收金额<span style={{color:'red'}}>{data['money']}</span>元</p>
                      <p>2.党费返还是上级党委从每年的党费收入中拿出一部分资金作为基层党委的党建经费，主要用于党员教育、党员学习、党员活动场所建设等方面，是专款专用。按照离退休支部按每年收缴党费的50％返还，其余基层组织按上一年收缴党费的20％返还。 </p>
                      <p>3.按照离退休支部按每年收缴党费的50％返还，其余基层组织按上一年收缴党费的20％返还。</p>
                    </div>
                  }
                  type="info" showIcon />
              </Col>
              <Col span={24}>
                <FormItem
                  label="下拨比例"
                  style={{marginTop:'20px'}}
                >
                  {getFieldDecorator('allocateRatio', {
                    initialValue:isEmpty(data['allocateRatio'])?0:data['allocateRatio']*100,
                    rules: [
                      { required: true, message: '请选择!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <InputNumber
                      min={0}
                      max={100}
                      style={{width:'100%'}}
                      formatter={value => `${value}%`}
                      // parser={value => value.replace('%', '')}
                      placeholder={'请设置下拨比例'}
                      // onBlur={(value)=>this.calculate(value)}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label={'下拨金额'}
                >
                  {getFieldDecorator('allocateMoney', {
                    initialValue:isEmpty(data['allocateMoney'])?stirMoney:data['allocateMoney'],
                    rules: [
                      { required: true, message: '请填写金额!' },
                      // { validator: this.validFunction }
                    ],
                    normalize: (a, prev) => {
                      if (a && !/^(([1-9]\d*)|0)(\.\d{0,2}?)?$/.test(a)) {
                        if (a === '.') {
                          return '0.';
                        }
                        return prev;
                      }
                      return a;
                    }
                  })(
                    <Input placeholder={'请填写金额'}/>
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label="正式党员"
                >
                  {getFieldDecorator('fullMemNum', {
                    initialValue:isEmpty(data['fullMemNum'])?0:data['fullMemNum'],
                    rules: [
                      { required: true, message: '请选择!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <InputNumber
                      min={0}
                      // max={100}
                      style={{width:'100%'}}
                      formatter={value => `${value}人`}
                      // parser={value => value.replace('%', '')}
                      placeholder={'请设置下拨比例'}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label="预备党员"
                >
                  {getFieldDecorator('proMemNum', {
                    initialValue:isEmpty(data['proMemNum'])?0:data['proMemNum'],
                    rules: [
                      { required: true, message: '请选择!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <InputNumber
                      min={0}
                      // max={100}
                      style={{width:'100%'}}
                      formatter={value => `${value}人`}
                      // parser={value => value.replace('%', '')}
                      placeholder={'请设置下拨比例'}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label="离退休党员"
                >
                  {getFieldDecorator('retirementMemNum', {
                    initialValue:isEmpty(data['retirementMemNum'])?0:data['retirementMemNum'],
                    rules: [
                      { required: true, message: '请选择!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <InputNumber
                      min={0}
                      // max={100}
                      style={{width:'100%'}}
                      formatter={value => `${value}人`}
                      // parser={value => value.replace('%', '')}
                      placeholder={'请设置下拨比例'}
                    />
                  )}
                </FormItem>
              </Col>
              <Col span={24}>
                <FormItem
                  label="是否已拨"
                >
                  {getFieldDecorator('isAllocate', {
                    initialValue:isEmpty(data['isAllocate'])?true:data['isAllocate']=='1',
                    valuePropName:'checked',
                    rules: [
                      { required: true, message: '请选择!' },
                      // { validator: this.validFunction }
                    ],
                  })(
                    <Switch checkedChildren="是" unCheckedChildren="否"  />
                  )}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </Modal>
      </div>
    );
  }
}
export default Form.create()(index)
