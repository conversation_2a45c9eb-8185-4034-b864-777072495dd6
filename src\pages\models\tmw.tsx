import { listPageModel, ListPageStore } from "@/utils/common-model";
import modelExtend from "dva-model-extend";
import {
  saveExcelConfig, queryExcelConfigTreeById, findExcelConfigByKey, queryExcelConfigReturnHtmlExplain, queryExcelConfigReturnHtml, queryExcelConfigReturnHtmlIns, copyConfig, queryExcelConfigTreeById1, getReportPegging, overTheYears
} from "@/services/tmw";
import { isEmpty, jsonToTree, unique } from '@/utils/method';
import _get from 'lodash/get';
const user = modelExtend(listPageModel, {
  namespace: 'tmwTable',
  state: {

  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname = '' } = location;
        const org = JSON.parse(sessionStorage.getItem('org') || '{}')
        if (['/annualStatistics/old'].includes(pathname)) {
          dispatch({
            type: 'getOverTheYears',
            payload: {
              overTheYearsSelects: undefined
            }
          })
        }
        if (['/text', '/text/dxg', '/annualStatistics', '/annualStatistics/instruction', '/annualStatistics/half2022'].includes(pathname)) {
          dispatch({
            type: 'updateState',
            payload: {
              TreeList: undefined,
              treeOrg: undefined,
            }
          })
          dispatch({
            type: 'queryExcelConfigTreeById1',
            payload: {
              reportCode: -1,
              // ...(pathname === '/annualStatistics/old' ? {year:20211231}:{})
            }
          });
          // dispatch({
          //   type: 'queryExcelConfigReturnHtml',
          //   payload: {
          //     reportCode: '',
          //     orgCode: org?.code,
          //     orgLevelCode: org?.orgCode
          //   }
          // });
        }
      });
    }
  },
  effects: {
    *add({ payload }, { call, put }) {
      const info = yield call(saveExcelConfig, payload);
      return Promise.resolve(info);
    },
    *select({ payload }, { call, put }) {
      const info = yield call(findExcelConfigByKey, payload);
      return Promise.resolve(info);
    },
    *copyConfig({ payload }, { call, put }) {
      const info = yield call(copyConfig, payload);
      return Promise.resolve(info);
    },
    *updateTree({ payload }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          TreeList: undefined,
          listTree: undefined,
        }
      });
      yield put({
        type: 'getFirstAnnualstatsTree',
        payload: {
          id: -1
        }
      })
    },
    *getFirstAnnualstatsTree({ payload }, { call, put, select }) {
      let { listTree, TreeList } = yield select(state => state['tmwTable']);
      if (listTree === undefined) {
        const { code = 500, data = [] } = yield call(queryExcelConfigTreeById, { ...payload, type: 0 });
        if (code === 0) {
          if (!isEmpty(data)) {
            listTree = data.map(item => { return { ...item, children: [], parentCode: '-1' } });
            TreeList = jsonToTree(listTree, 'parentCode', 'levelCode', '-1');
          } else {
            listTree = [];
            TreeList = [];
          }
        }
        let { id } = _get(TreeList, '[0]', {});
        const { code: code2 = 500, data: data2 = [] } = yield call(queryExcelConfigTreeById, { id, type: 0 });
        if (code2 === 0) {
          if (!isEmpty(data2)) {
            listTree = listTree.concat(data2);
            TreeList = jsonToTree(listTree, 'parentCode', 'levelCode', '-1');
          }
        }
        // 树展开项
        let { id: expandedKeys = '' } = _get(TreeList, '[0]', {});
        yield put({
          type: 'updateState',
          payload: {
            TreeList,
            listTree,
            expandedKeys: [`${expandedKeys}`]
          }
        })
      }
    },

    *getAnnualstatsTree({ payload }, { call, put, select }) {
      let { listTree, TreeList } = yield select(state => state['tmwTable']);
      const { code = 500, data = [] } = yield call(queryExcelConfigTreeById, { ...payload, type: 0 });
      if (code === 0 && !isEmpty(listTree)) {
        if (!isEmpty(data)) {
          listTree = listTree.concat(data);
        }
        listTree = unique(listTree, 'id');
        TreeList = jsonToTree(listTree, 'parentCode', 'levelCode', '-1');
      }
      yield put({
        type: 'updateState',
        payload: {
          TreeList,
          listTree
        }
      })
    },
    *queryExcelConfigReturnHtmlExplain({ payload }, { call, put, select }) {
      const { treeOrg } = yield select((state) => state['tmwTable']);
      let val = {}
      if (treeOrg) {
        val['reportCode'] = treeOrg?.levelCode
        val = {
          ...payload,
          reportCode: treeOrg?.levelCode,
          // year: window.location.pathname === '/annualStatistics/old' ? '20211231' : undefined,
        }
      } else {
        val = {
          ...payload,
          // year: window.location.pathname === '/annualStatistics/old' ? '20211231' : undefined,
        }
      }
      if (val['reportCode']) {
        const info = yield call(queryExcelConfigReturnHtmlExplain, { reportCode: val['reportCode'] });
        return Promise.resolve(info);
      }

    },
    *queryExcelConfigReturnHtml({ payload }, { call, put, select }) {
      const { treeOrg } = yield select((state) => state['tmwTable']);
      let val = {}
      if (treeOrg) {
        val['reportCode'] = treeOrg?.levelCode
        val = {
          ...payload,
          reportCode: treeOrg?.levelCode,
          // year: window.location.pathname === '/annualStatistics/old' ? '20211231' : undefined,
        }
      } else {
        val = {
          ...payload,
          // year: window.location.pathname === '/annualStatistics/old' ? '20211231' : undefined,
        }
      }
      if (val['reportCode']) {
        const info = yield call(queryExcelConfigReturnHtml, val);
        return Promise.resolve(info);
      }

    },
    *queryExcelConfigReturnHtmlIns({ payload }, { call, put, select }) {
      const { treeOrg } = yield select((state) => state['tmwTable']);
      let val = {}
      if (treeOrg) {
        val['reportCode'] = treeOrg?.levelCode
        val = {
          ...payload,
          reportCode: treeOrg?.levelCode,
        }
      } else {
        val = payload
      }
      if (val['reportCode']) {
        const info = yield call(queryExcelConfigReturnHtmlIns, val);
        return Promise.resolve(info);
      }

    },
    *queryExcelConfigTreeById1({ payload }, { call, put, select }) {
      const { code = 0, data = [] } = yield call(queryExcelConfigTreeById1, payload);
      let newData = [];
      if (code === 0) {
        newData = data.map(it => {
          return {
            shortName: it.reportName,
            levelCode: it.reportCode,
            hasSub: '0',
            children: [],
            type: it.type,
          }
        })
      }
      yield put({
        type: 'updateState',
        payload: {
          TreeList: newData,
        }
      })
    },

    *findVerData({ payload }, { call, put, select }) {
      console.log("🚀 ~ *findVerData ~ payload:", payload)
      const info = yield call(getReportPegging, payload);
      return Promise.resolve(info);
    },
    // 获取往年年度统计项 
    *getOverTheYears({ payload }, { call, put, select }) {
      yield put({
        type: 'updateState',
        payload: {
          overTheYearsSelects: []
        }
      })
      const { code = 0, data = [] } = yield call(overTheYears, payload);
      let overTheYearsSelects = [];
      if (code === 0) {
        overTheYearsSelects = data
      }
      yield put({
        type: 'updateState',
        payload: {
          overTheYearsSelects
        }
      })
    },
  },
  reducers: {
    success(state, { payload }) {
      return { ...state, ...payload };
    },
  }
});
export default user
