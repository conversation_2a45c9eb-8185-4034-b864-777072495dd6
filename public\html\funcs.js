function getBlueAndInput(data,countAllRow,countAllCol,doNotAction = []){
  let initData = JSON.parse(JSON.stringify(data));
  if(doNotAction.length > 0){
        let arr= [...doNotAction];
        arr.reverse().map(item=>{
          let row = parseInt(item.split(',')[0]);
          let col = parseInt(item.split(',')[1]);
          initData[row-1].splice(col-1,1);
        })
      }
      if(data.length == 0){
        return { blueCount:[], inputCount:[]}
      }
      let blueCount = [];
      let inputCount = [];
      initData.map((item,index)=>{
        if(countAllRow.includes(index+1)){
          blueCount.push(...item)
        }else {
          // 算出除开横行的合计的列
          let arr = item.filter((it,ind)=> countAllCol.includes( ind + 1 ) );
      blueCount.push(...arr);

      let arr2 = item.filter((it,ind)=> !countAllCol.includes( ind + 1 ) );
      inputCount = inputCount.concat(arr2)
    }
  });
  return {
    blueCount,inputCount
  }
}

function fixedTheChangeValue(data,val,row,col,doNotAction) {

  // let thisRowDoNotActions = doNotAction.filter( item=>!item.indexOf(row+',') );
  // if(data.length === 0){
  //   return data;
  // }
  //
  // if(thisRowDoNotActions.length !== 0){
  //   thisRowDoNotActions.map(item=>{
  //     if(col > parseInt(item.split(',')[1])){
  //       data[row-1][col - thisRowDoNotActions.length - 1 ] = typeof val === 'string'  ? parseInt(val) : val;
  //     }else {
  //       data[row-1][col - 1] = typeof val === 'string'  ? parseInt(val) : val;
  //     }
  //   });
  // }else {
  //   data[row-1][col - 1] = typeof val === 'string'  ? parseInt(val) : val;
  // }
  // //重新添加-的值
  // if(doNotAction.length > 0){
  //   doNotAction.map(item=>{
  //     let row = parseInt(item.split(',')[0]);
  //     let col = parseInt(item.split(',')[1]);
  //     data[row-1].splice(col-1,0,0);
  //   })
  // }
  data[row-1][col - 1] = typeof val === 'string'  ? Number(val) : val;

  return data
}
