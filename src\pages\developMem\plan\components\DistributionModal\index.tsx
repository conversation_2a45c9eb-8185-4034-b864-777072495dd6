import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, InputNumber, Button, Popconfirm } from 'antd';
import { findDictCodeName, unixMoment } from '@/utils/method';
import moment from 'moment';
import Tip from '@/components/Tip';
import { getSession } from '@/utils/session';
const FormItem=Form.Item;
const confirm = Modal.confirm;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state={
      visible:false,
      record:{},
      planInfo:{}
    }
  }
  handleOk=()=>{
    const {record,planInfo} = this.state;
    const org = getSession('org') || {} ;
    this.props.form.validateFieldsAndScroll(async(err, val) => {
      if (!err) {
        val['code'] = record['code'];
        val['orgCode'] = record['orgCode'];
        const _this = this;
        if(planInfo['unfinishedNumber'] - (val['totalNumber'] - record['totalNumber']) < 0  && org['parentCode'] !== '21d2ca26fd5d11e8a5f36c92bf562df8'){
          confirm({
            title: '操作提示',
            okText: '确定',
            cancelText: '取消',
            content: `你目前未分配的指标为${planInfo['unfinishedNumber']},是否确定继续分配${val['totalNumber']}?`,
            async onOk() {
              const res = await _this.props.dispatch({
                type:'memDevelopPlan/save',
                payload:{...val}
              });
              const {code = 500} = res || {};
              if(code === 0){
                _this.handleCancel();
                Tip.success('操作提示', '操作成功');
                const {onClose} = _this.props;
                onClose && onClose();
              }
            },
            onCancel() {},
          });
        }else {
          const res = await _this.props.dispatch({
            type:'memDevelopPlan/save',
            payload:{...val}
          });
          const {code = 500} = res || {};
          if(code === 0){
            this.handleCancel();
            Tip.success('操作提示', '操作成功');
            const {onClose} = _this.props;
            onClose && onClose();
          }
        }
      }
    })
  };
  handleCancel=()=>{
    this.destory();
    this.setState({
      visible:false,
    })
  };
  destory=()=>{
    this.setState({
      record:{},
      planInfo:{}
    })
  };
  open=(record,planInfo)=>{
    this.setState({
      visible:true,
      record,planInfo
    })
  };
  render() {
    const {visible} = this.state;
    const {form} = this.props;
    const { getFieldDecorator } = form;
    return (
      <div>
        <Modal
          title="分配指标"
          destroyOnClose
          visible={visible}
          // onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={'600px'}
          footer={[
            <Button key={1} htmlType={'button'} onClick={this.handleCancel} style={{marginRight:'5px'}}>取消</Button>,
            <Button key={2} htmlType={'button'} onClick={this.handleOk} type={'primary'}>确定</Button>,
          ]}
        >
          <Form>
            <FormItem
              label={'分配指标'}
              {...formItemLayout}
            >
              {getFieldDecorator('totalNumber', {
                rules: [{ required: true, message: '分配指标' }],
              })(
                <InputNumber placeholder={'分配指标必填'} style={{width:'100%'}} min={1}/>
              )}
            </FormItem>
          </Form>
        </Modal>
      </div>
    );
  }
}
export default Form.create()(index);
