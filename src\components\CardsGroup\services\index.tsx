import request from "@/utils/request";
import qs from 'qs';
export function getList(params) {
  return request(`/api/mem/getList`,{
    method:'POST',
    body:params,
  });
}
// 保存用户概况信息
export function saveChart(params) {
  return request(`/api/chart/saveChart`,{
    method:'POST',
    body:params,
  });
}
// 根据类型获取模块概况
export function findByChartType(params) {
  return request(`/api/chart/findByChartType?${qs.stringify(params)}`,{
    method:'Get',
  });
}
// 获取概况字典表
export function getDictChartList(params) {
  return request(`/api/chart/getDictChartList?${qs.stringify(params)}`,{
    method:'Get',
  });
}


