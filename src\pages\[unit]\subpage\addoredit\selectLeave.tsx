import React, { Fragment, useImperativeHandle, useState } from 'react';
import Date from '@/components/Date';
import { Col, Form, Input, Modal } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import { selectleave } from '@/pages/[unit]/services/index';
import Tip from '@/components/Tip';
import moment from 'moment';


const { confirm } = Modal;
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
const formItemLayout2 = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const { TextArea } = Input;
const index = (props: any, ref) => {
  const { title = '标题', onOK } = props;
  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);

  const handleCancel = () => {
    setRecord({});
    setVisible(false);
    form.resetFields();
  };
  const onFinish = async (e) => {
   
    const roles = [ {
      name: '村（社区）后备干部',
      code: 'isReserveMem'
    }, {
      name: '村（社区）工作者',
      code: 'isWorkMem'
    }, {
      name: '驻村干部',
      code: 'isCadreMem'
    }, {
      name: '党组织班子成员',
      code: 'isOrgMem'
    }, {
      name: '本单位班子成员',
      code: 'isUnitMem'
    }].filter(key => record[key.code] == 1);
    console.log(roles,'rolesrolesrolesroles')
    if(roles.length>0) {
      let str = roles.map(key => key.name).join('、');
      confirm({
        title: '操作提示',
        content: `该选调生包含（${str}）相关信息，离开后相关信息会进入历史任职，村（社区）工作者和后备干部会直接离开`,
        async onOk() {
          setConfirmLoading(true);
          const { code = 500 } = await selectleave({
            data: {
              code: record?.code,
              leaveTime: e.leaveTime ? moment(e.leaveTime).valueOf() : '',
              ...e,
            }
          });
          setConfirmLoading(false);
          if (code === 0) {
            Tip.success('操作提示', '操作成功');
            handleCancel();
            onOK && onOK();
          }
        },
        onCancel() {
          console.log('Cancel');
        },
      });
    } else {
      setConfirmLoading(true);
      const { code = 500 } = await selectleave({
        data: {
          code: record?.code,
          leaveTime: e.leaveTime ? moment(e.leaveTime).valueOf() : '',
          ...e,
        }
      });
      setConfirmLoading(false);
      if (code === 0) {
        Tip.success('操作提示', '操作成功');
        handleCancel();
        onOK && onOK();
      }
    }
 
  };
  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      setRecord(query);
    },
    clear: () => {
      // clear();
    },
  }));

  return (
    <Modal
      title={'离开'}
      visible={visible}
      onOk={() => {
        form.submit()
      }}
      onCancel={handleCancel}
      width={800}
      destroyOnClose={true}
      confirmLoading={confirmLoading}
    >
      {
        visible &&
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Form.Item name='leaveTime'
            label="离开时间"
            rules={[{ required: true, message: '离开时间' }]}
          >
            <Date />
          </Form.Item>
          <Form.Item name='leaveRemark'
            label="离开描述"
            rules={[{ required: true, message: '离开描述' }]}
          >
            <TextArea rows={4} placeholder="请输入离开描述" />
          </Form.Item>

        </Form>
      }

    </Modal>
  )
};
export default React.forwardRef(index);
