/**
 * 模块名
 */
import React, { Fragment } from 'react';
import { connect } from 'dva';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Modal, Radio, Row, Input, Switch, InputNumber, AutoComplete, Divider, Select, Button, Popconfirm, Tooltip, Alert } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { getSession } from '@/utils/session';
import OrgSelect from '@/components/OrgSelect';
import MemSelect from '@/components/MemSelect';
import DictArea from '@/components/DictArea';
import ListTable from '@/components/ListTable';
import Tip from '@/components/Tip';
import { root } from '@/common/config.js';
import SearchOrg from '@/components/SearchOrg';
import Dates from '@/components/Date';
import DictTreeSelect from '@/components/DictTreeSelect';
import { findOutsideOrgByName } from '@/services';
import _get from 'lodash/get';
import { findDictCodeName } from '@/utils/method.js';
import { getProvinces, transferMsg } from '../../services';
import { formLabel } from '@/utils/method';
import styles from './transfer.less'
const { MonthPicker } = DatePicker;
const TextArea = Input.TextArea;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

@connect(
  ({ transferOut, loading }) => ({
    transferOut,
    memTrans: loading.effects['transferOut/transferMem'],
    orgTrans: loading.effects['transferOut/addTransfer'],
  }),
  undefined,
  undefined,
  { forwardRef: true },
)
class TransferBet extends React.Component<any, any> {
  static open() { }
  static close() { }
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      key: new Date().valueOf(),
      timeKey: new Date().valueOf(),
      showList: false,
      unitList: [],
      loading: false,
      pagination: { current: '1', pageSize: '10', pageNum: '1' },
      visibleCheckMem: false,
      checkMemData: [],
    };
    TransferBet.open = this.open;
  }
  handleOk = () => {
    const { memData } = this.state;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        // 增加字典表的name
        val = findDictCodeName(['d146'], val, memData);
        let obj = undefined;
        if (val['srcOrgId']) {
          val['srcOrgName'] = val['srcOrgId'][0]['name'];
          val['srcOrgId'] = val['srcOrgId'][0]['code'];
        }
        let data: Array<object> = [];
        if (val['memId']) {
          for (let obj of memData) {
            let type = '21'; //类型 21系统内关系转出
            // 当转接单位选择 转入到全国交换区组织得时候，type 传224 当转接单位选择 转到未接入全国交换区组织，type 传223
            if (val['unitssss'] == '1') {
              type = '224';
            }
            if (val['unitssss'] == '2') {
              type = '223';
            }
            let putObj = {
              memId: obj['code'], //人员code
              srcOrgId: obj['orgCode'], //源组织
              srcOrgName: obj['orgName'],
              area: val['area'],
              transferOutTime: val['transferOutTime'] ? val['transferOutTime'].valueOf() : undefined,
              // reportTime:val['reportTime'] ? val['reportTime'].valueOf() : undefined,
              // targetOrgId:val['targetOrgId'][0]['code'],//目的组织
              // targetOrgName:val['targetOrgId'][0]['name'],
              memFeeStandard: obj['dues'], //党费
              memFeeEndTime: obj['duesTime'] ? obj['duesTime'].valueOf() : undefined, //党费时间
              type,
              whetherExtendPrepPeriod: obj['whetherExtendPrepPeriod'], //是否延长预备期
              reason: val['reason'], //原因
              d146Code: val['d146Code'], // 字典表 原因
              d146Name: val['d146Name'],
              letterValidity: val['letterValidity'], // 介绍信有效期（天）
            };
            if (typeof val['targetOrgId'] === 'object') {
              putObj['targetOrgId'] = val['targetOrgId']['code'];
              putObj['targetOrgName'] = val['targetOrgId']['name'];
            } else {
              putObj['targetOrgName'] = val['targetOrgName'];
              // putObj['type']='223';//系统外跨省转出
            }
            data.push(putObj);
          }
          obj = await this.props.dispatch({
            type: 'transferOut/transferMem',
            payload: {
              data,
            },
          });
        } else {
          if (val['targetOrgId']) {
            val['targetOrgName'] = val['targetOrgId']['name'];
            val['targetOrgId'] = val['targetOrgId']['code'];
          }
          // 转接理由
          if (val['type'] == 'd') {
            val['type'] = '226'; //转出省外 整建制
          } else {
            val['type'] = '212'; //212 系统内区县整建制转出
          }
          obj = await this.props.dispatch({
            type: 'transferOut/addTransfer',
            payload: {
              data: {
                ...val,
              },
            },
          });
        }
        if (obj && obj['code'] === 0) {
          Tip.success('操作提示', '关系转接申请已提交');
          this.props.refresh();
          this.handleCancel();
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      memData: [],
    });
  };
  open = () => {
    this.setState({
      visible: true,
    });
  };
  ceheckMem = async(memArr) => {
    let memCodeArr = memArr.map((item) => item['code']);
    const { code = 500, data = [] } = await transferMsg({
      data:{
        codeList: memCodeArr,
      }
    })
    if (code == 0) {
      if(data.length > 0){
        this.setState({
          visibleCheckMem: true,
          checkMemData: data,
        })
        // 过滤调校验不合格的党员（如果resumeVoList.value都是“党代表任职情况”，提示后该人员可以继续选择）
        // const memNotPassCodeArr = data.map((item) => item['memCode']);
        
        let memNotPassCodeArr = [] 
        data.map((item,index)=>{
          let resumeVoListTypes=item.resumeVoList.map((i)=>{
            return i.value
          })
          // 去重
          resumeVoListTypes = [...new Set(resumeVoListTypes)]
          if(resumeVoListTypes.length==1 && resumeVoListTypes[0]=='党代表任职情况'){
          }else{
            memNotPassCodeArr.push(item['memCode'])
          }

        })
        const memPassArr = memArr.filter((item) => !memNotPassCodeArr.includes(item['code'])) || [];
        console.log('memPassArr===',memPassArr);
        
         this.setState({
            memData: memPassArr,
          });
          this.props.form.setFieldsValue({
            memId: memPassArr,
          })
          this['mem'].setState({
            value: memPassArr.map((item) => item['name']).join('，'),
          })
      }else{
        this.setState({
          memData: memArr,
        });
      }
    }
  }
  memChange = (data) => {
    console.log('data===',data);
    
    // 组织关系转接发起时，选择党员后，进行校验，必须先进行任职离任后才可以进行该党员的组织关系转接（党组织任职、单位任职、流动党员）；具体做法为，党员只要发生组织关系转接就需要进行上述校验并且处理完相关业务才可以对该党员发起转接
    if (data.length > 0) {
      this.ceheckMem(data);
    }
    // this.setState({
    //   memData: data,
    // });
  };
  del = (item) => {
    let { memData } = this.state;
    memData = memData.filter((obj) => obj['id'] !== item['id']);
    this.setState({
      memData,
    });
    let names: Array<string> = [];
    for (let obj of memData) {
      names.push(obj['name']);
    }
    this['mem'].setState({
      value: names.join('，'),
    });
  };
  isYb = (val, item) => {
    let { memData } = this.state;
    let findIndex = memData.findIndex((obj) => obj['id'] === item['id']);
    item['whetherExtendPrepPeriod'] = val;
    memData[findIndex] = item;
    this.setState({
      memData,
    });
  };
  duesChange = (e, item) => {
    const { value } = e.nativeEvent.target;
    let { memData } = this.state;
    let findIndex = memData.findIndex((obj) => obj['id'] === item['id']);
    item['dues'] = value;
    memData[findIndex] = item;
    this.setState({
      memData,
    });
  };
  dateChange = (data, dateString, item) => {
    let { memData } = this.state;
    let findIndex = memData.findIndex((obj) => obj['id'] === item['id']);
    item['duesTime'] = data;
    memData[findIndex] = item;
    this.setState({
      memData,
    });
  };
  getList = async (params?) => {
    this.setState({ loading: true });
    const { code = 500, data = [] } = await getProvinces({
      pageSize: this.state.pagination.pageSize,
      pageNum: this.state.pagination.pageNum,
    });
    this.setState({ loading: false });
    if (code == 0) {
      this.setState({ unitList: data });
    }
  };
  onPageChange = (page, pageSize) => {
    this.getList({ pageNum: page, pageSize });
  };
  render() {
    const {
      memTrans,
      orgTrans,
      tipMsg = {
        targetOrgId: '省内组织关系转接，转入党组织需要精确到党支部。只需要输入转入党支部的关键字即可匹配。因少部分党组织全称不规范，输入全称匹配可能存在匹配不上的情况。',
        unitssss:
          '目前全国组织关系转接交换区已开通，全国各省（区、市）均已接入，但军队、中直机关、国务院国资委监管企业、银行、铁路等单位尚未接入，这些单位的转接请选择转到未接入全国交换区党组织。如转到已接入全国交换的党组织，但未找到转入基层党委的，请在钉钉群内联系管理员。是否接入全国交换区请点击列表查看。',
        targetOrgId1:
          '根据《中国共产党党员教育管理工作条例》，具有审批预备党员权限的基层党委，可以在全国范围直接相互转移和接收党员组织关系。请输入转入的具有审批预备党员权限的基层党委的关键字进行匹配。如转到已接入全国交换的党组织，但未找到转入基层党委的，请在钉钉群内联系管理员。是否接入全国交换区请点击列表查看。',
        targetPartyBranch: '因跨省组织关系转接只能转入到具有审批预备党员权限的基层党委，如已明确转入党支部的，请手工输入转入党支部。如未明确转入党支部的，请忽略此项。',
        area:
          '如转入党组织未接入全国交换区，请采用纸质介绍信线下进行转接，并在收到介绍信回执后，在线上选择转到未接入全国交换区，并选择转入的单位。转到未接到全国交换区的，对方的系统中不会收到转接信息，请一定要与转入党组织做好沟通，由对方在对方系统中作相应处理，确保不发生漏人或重复的问题。',
        targetOrgName:
          '如转入党组织未接入全国交换区，请采用纸质介绍信线下进行转接，并在收到介绍信回执后，在线上选择转到未接入全国交换区，并手工输入转入的具体党组织。转到未接到全国交换区的，对方的系统中不会收到转接信息，请一定要与转入党组织做好沟通，由对方在对方系统中作相应处理，确保不发生漏人或重复的问题。',
      },
    } = this.props;
    const { visible, memData, showList, unitList, loading, pagination, timeKey, visibleCheckMem, checkMemData } = this.state;
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const type = this.props.form.getFieldValue('type') || 'a';
    const unitssss = this.props.form.getFieldValue('unitssss') || '';
    // console.log(type,'statestyatat');
    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 58,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'name',
        align: 'center',
        width: 100,
      },
      {
        title: '所在组织',
        dataIndex: 'orgName',
        align: 'center',
        width: 200,
      },
      {
        title: '党费交纳标准(元/月)',
        dataIndex: 'dues',
        align: 'center',
        width: 160,
        render: (text, record, index) => {
          return (
            <FormItem key={record['id']}>
              {getFieldDecorator(`dues${record['id']}`, {
                initialValue: text,
                rules: [{ required: true, message: '党费交纳标准(元/月)' }],
              })(<InputNumber key={record['id']} min={0} placeholder={'党费交纳标准(元/月)'} style={{ width: '100%' }} onBlur={(e) => this.duesChange(e, record)} />)}
            </FormItem>
          );
        },
      },
      {
        title: '党费最后交纳到月份',
        dataIndex: 'duesTime',
        align: 'center',
        width: 160,
        render: (text, record, index) => {
          return (
            <FormItem key={record['id']}>
              {getFieldDecorator(`duesTime${record['id']}`, {
                initialValue: text ? text : undefined,
                rules: [{ required: true, message: '党费最后交纳到月份' }],
              })(<MonthPicker placeholder={'请选择日期'} onChange={(data, dateString) => this.dateChange(data, dateString, record)} />)}
            </FormItem>
          );
        },
      },
      {
        title: '是否延长预备期',
        dataIndex: 'whetherExtendPrepPeriod',
        width: 160,
        render: (text, record, index) => {
          return (
            <FormItem key={record['id']}>
              {getFieldDecorator(`whetherExtendPrepPeriod${record['id']}`, {
                initialValue: text ? text : undefined,
                rules: [{ required: record['d08Code'] == '2', message: '是否延长预备期' }],
              })(
                <Select placeholder={'请选择'} allowClear disabled={record['d08Code'] != '2'} onChange={(val) => this.isYb(val, record)}>
                  <Select.Option value={'1'}>是</Select.Option>
                  <Select.Option value={'0'}>否</Select.Option>
                </Select>,
              )}
            </FormItem>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 80,
        render: (text, record, index) => {
          return (
            <span>
              <a className={'del'} onClick={() => this.del(record)}>
                删除
              </a>
            </span>
          );
        },
      },
    ];
    const unitColumns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 58,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '地区',
        dataIndex: 'dzzmc',
        align: 'center',
        width: 200,
      },
      {
        title: '是否接入',
        dataIndex: 'jhqcsz',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          return text == '0' ? '未接入' : text == '1' ? '已接入' : '';
        },
      },
    ];
    const columnsCheckMem = [
      {
        title: <div style={{padding:'12px 8px'}}>姓名</div>,
        dataIndex: 'memName',
        width: 100,
        render: (text, record, index) => {
          return <div style={{padding:'6px 10px'}}>{text}</div>
        },
      },
      // {
      //   title: '任职详情',
      //   dataIndex: 'resumeVoList',
      //   width: 600,
      //   render: (text, record, index) => {
      //     if(text && text.length > 0){
      //       return text.map((item,i)=>{
      //         // 查找有几个换行符
      //         let num = item.desc.match(/\n/g) ? item.desc.match(/\n/g).length : 0;
      //         // 高度根据换行数决定, 最后一行没有下边框
      //         return <div title={item.desc} style={{whiteSpace:'pre-wrap', width: '100%', borderBottom: i == text.length - 1 ? 'none' : '1px solid #e8e8e8' , height: 30 * (num + 1), padding:'6px 10px', overflow: 'hidden', textOverflow: 'ellipsis', display: '-webkit-box', WebkitBoxOrient: 'vertical', WebkitLineClamp: num + 1}} key={i}>{item.desc}</div>
      //       })
      //     }
      //   },
      // },
      // {
      //   title: '处理方式',
      //   dataIndex: 'resumeVoList',
      //   // width: 100,
      //   render: (text, record, index) => {
      //     if(text && text.length > 0){
      //       return text.map((item,i)=>{
      //           // 查找有几个换行符
      //           let num = item.desc.match(/\n/g) ? item.desc.match(/\n/g).length : 0;
      //           // 高度根据换行数决定, 最后一行没有下边框
      //         return <div title={item.solve} style={{ width: '100%', borderBottom: i == text.length - 1 ? 'none' : '1px solid #e8e8e8', height: 30 * (num + 1), padding:'6px 10px', overflow: 'hidden', textOverflow: 'ellipsis', display: '-webkit-box', WebkitBoxOrient: 'vertical', WebkitLineClamp: num + 1}} key={i}>{item.solve}</div>
      //       })
      //     }
      //   },
      // },
      {
        title: <div style={{display:'flex'}}><span style={{width:'600px', borderRight:'1px solid #e8e8e8', padding:'12px 8px'}}>任职详情</span><span style={{flex:1, padding:'12px 8px'}}>处理方式</span></div>,
        dataIndex: 'resumeVoList',
        // width: 100,
        render: (text, record, index) => {
          if(text && text.length > 0){
            return text.map((item,i)=>{
              return <div style={{display:'flex', borderBottom: i == text.length - 1 ? 'none' : '1px solid #e8e8e8'}} key={i}>
                  {/* desc */}
                  <div style={{whiteSpace:'pre-wrap',width:'600px', borderRight:'1px solid #e8e8e8', padding:'12px 8px'}}>{item.desc}</div>
                  {/* solve */}
                  <div style={{flex:1, whiteSpace:'pre-wrap', padding:'12px 8px'}}>{item.solve}</div>
               </div>
            })
          }
        },
      },
    ]
    // `${_get(getFieldValue('srcOrgId'),'[0]name')} ${_get(getFieldValue('targetOrgId'),'name')}`
    // 权限列表有95	才显示转接单位的选项'2'，默认只显示'1'
    const pidArr: any = getSession('pid') || [];
    return (
      <div>
        <Modal
          destroyOnClose
          title="关系转出"
          visible={visible}
          // onOk={this.handleOk}
          onCancel={this.handleCancel}
          confirmLoading={type === 'c' ? orgTrans : memTrans}
          width={1200}
          bodyStyle={{ height: 760, overflow: 'auto' }}
          footer={[
            <Button htmlType={'button'} onClick={this.handleCancel} key={1}>
              取消
            </Button>,
            type == 'c' ? (
              <Popconfirm
                title={
                  <div style={{ width: 300 }}>
                    此操作将把
                    <span style={{ color: 'red' }}>{_get(getFieldValue('srcOrgId'), '[0]name', '')}</span>
                    组织整建制转移到
                    <span style={{ color: 'red' }}>{_get(getFieldValue('targetOrgId'), 'name', '')}</span>
                    组织下， 整建制转移涉及所有数据情况将以发起转接时数据为准 （例：某党员发整建制时姓名为张某某，在对方未接收整建制以前在本地修改为李某某，
                    对方接收时党员姓名依旧为张某某，发起后修改的名称李某某不会更新到数据包中），发起转接前请确认已将所有数据已维护至可转移状态
                  </div>
                }
                onConfirm={this.handleOk}
                okText="确定"
                cancelText="取消"
                key={2}
                placement="topRight"
              >
                <Button htmlType={'button'} type={'primary'}>
                  确定
                </Button>
              </Popconfirm>
            ) : (
              <Button htmlType={'button'} type={'primary'} onClick={this.handleOk} loading={type === 'c' ? orgTrans : memTrans}>
                确定
              </Button>
            ),
          ]}
        >
          {visible && (
            <React.Fragment>
              <Form {...formItemLayout} key={timeKey}>
                <FormItem
                  label={
                    <Fragment>
                      <span>转接类型</span>
                      <Tooltip title="根据相关要求，党政机关县处级及以上党员领导干部（不含职级公务员和参照管理的企事业单位领导人员等）不通过线上进行组织关系转接，应通过纸质介绍信随干部调动程序一并进行，在本系统中这类党员转接时，转出党组织请选择转出到省外（个人），转入党组织通过关系转入入口进行转接录入。">
                        <ExclamationCircleOutlined
                          style={{
                            color: 'rgb(155, 152, 152)',
                            lineHeight: '14px',
                            verticalAlign: 'middle',
                            margin: '0 2px',
                          }}
                        />
                      </Tooltip>
                    </Fragment>
                  }
                >
                  {getFieldDecorator('type', {
                    initialValue: 'a',
                    rules: [{ required: true, message: '请输入转接类型' }],
                  })(
                    <Radio.Group
                      buttonStyle="solid"
                      onChange={() => {
                        this.props.form.resetFields();
                        this.setState({ memData: [], timeKey: new Date().valueOf() });
                      }}
                    >
                      <Radio.Button value="a">省内转接（个人）</Radio.Button>
                      <Radio.Button value="b">跨省转接（个人）</Radio.Button>
                      <Radio.Button value="c">省内转接（整建制）</Radio.Button>
                      {/* <Radio.Button value="d">转出省外（整建制）</Radio.Button> */}
                    </Radio.Group>,
                  )}
                </FormItem>
                {(type == 'b' || type == 'd') && (
                  <React.Fragment>
                    <div style={{ width: '92%', textAlign: 'right', paddingRight: '4px' }}>
                      <a
                        onClick={() => {
                          this.setState({ showList: true }), this.getList();
                        }}
                      >
                        全国交换区党组织接入列表
                      </a>
                    </div>
                    <FormItem label={formLabel('转接单位', tipMsg['unitssss'])}>
                      {getFieldDecorator('unitssss', {
                        initialValue: undefined,
                        rules: [{ required: true, message: '请选择转接单位' }],
                      })(
                        <Select
                          placeholder={"是否接入关系转接交换区全国，如无法确定对方党委是否已接入全国交换区，可点击《全国交换区党组织接入列表》查看"}
                          style={{ width: '100%' }}
                          onChange={(val) => {
                            this.props.form.setFieldsValue({
                              area: undefined,
                              targetOrgId: undefined,
                            });
                          }}
                        >
                          <Select.Option title="请与转入党组织联系，确认转入党组织是否接入全国交换区" value={'1'}>
                            转到接入全国交换区党组织（如其他省（区、市）等所属党组织）
                          </Select.Option>
                          {
                            <Select.Option title="请与转入党组织联系，确认转入党组织是否未接入全国交换区" value={'2'}>
                              转到未接入全国交换区党组织（如军队、中直机关等所属党组织）
                            </Select.Option>
                          }
                        </Select>,
                      )}
                    </FormItem>
                  </React.Fragment>
                )}
                {type !== 'c' && type !== 'd' ? (
                  <React.Fragment>
                    <FormItem label="转接人员">
                      {getFieldDecorator('memId', {
                        initialValue: '',
                        rules: [{ required: true, message: '请选择转接人员' }],
                      })(<MemSelect key={type} onChange={this.memChange} ref={(e) => (this['mem'] = e)} checkType={type == 'b' ? 'radio' : 'checkbox'} />)}
                    </FormItem>

                    <FormItem label="个人信息">
                      {getFieldDecorator('outMem2', {
                        initialValue: '',
                        rules: [{ required: false, message: '请选择个人信息' }],
                      })(<ListTable columns={columns} data={memData || []} pagination={false} />)}
                    </FormItem>
                  </React.Fragment>
                ) : (
                  <React.Fragment>
                    <FormItem label="需要整建制转移的党组织">
                      {getFieldDecorator('srcOrgId', {
                        initialValue: '',
                        rules: [{ required: true, message: '请选择需要整建制转移的党组织' }],
                      })(<OrgSelect ref={(e) => (this['mem'] = e)} />)}
                    </FormItem>
                  </React.Fragment>
                )}
                {type != 'b' && type != 'd' ? (
                  <FormItem label={formLabel('转入党组织', type == 'a' ? tipMsg['targetOrgId'] : '')}>
                    {getFieldDecorator('targetOrgId', {
                      rules: [{ required: true, message: '请选择转入党组织' }],
                    })(
                      <SearchOrg
                        showOtherInfo={'transfer'}
                        key={type}
                        backType={'object'}
                        params={type == 'c' ? { orgTypeList: ['1', '2'] } : type == 'a' ? { orgTypeList: ['3', '4'] } : {}}
                      />,
                    )}
                  </FormItem>
                ) : (
                  <React.Fragment>
                    {/* 转接类型b且转接人员1时：目的党组织改为目的党（工）委，字段参数不变。增加目的党支部文本框50字，非必填 */}
                    {unitssss == '1' && type == 'b' ? (
                      <Fragment>
                        <FormItem label={formLabel('转入党（工）委', tipMsg['targetOrgId1'])}>
                          {getFieldDecorator('targetOrgId', {
                            initialValue: undefined,
                            rules: [{ required: true, message: '请选择转入党（工）委' }],
                          })(
                            <SearchOrg
                              ohterAction={findOutsideOrgByName}
                              showOtherInfo={'transfer'}
                              key={unitssss}
                              backType={'object'}
                              params={type == 'c' ? { orgTypeList: ['1', '2'] } : type == 'a' ? { orgTypeList: ['3', '4'] } : {}}
                            />,
                          )}
                        </FormItem>
                        <FormItem label={formLabel('转入党支部', tipMsg['targetPartyBranch'])}>
                          {getFieldDecorator('targetPartyBranch', {
                            initialValue: undefined,
                            rules: [{ required: false, message: '' }],
                          })(<Input.TextArea rows={3} maxLength={50} />)}
                        </FormItem>
                      </Fragment>
                    ) : (
                      <React.Fragment>
                        <FormItem label={formLabel('转入单位', tipMsg['area'])}>
                          {getFieldDecorator('area', {
                            initialValue: undefined,
                            rules: [{ required: true, message: '请选择转入单位' }],
                          })(<DictTreeSelect key={unitssss} codeType={'dict_d136'} placeholder={'转入单位'} parentDisable={true} />)}
                        </FormItem>
                        <FormItem label={formLabel('转入党组织', tipMsg['targetOrgName'])}>
                          {getFieldDecorator('targetOrgName', {
                            initialValue: undefined,
                            rules: [{ required: true, message: '请输入转入党组织' }],
                          })(<Input placeholder={'请输入转入党组织'} />)}
                        </FormItem>
                      </React.Fragment>
                    )}
                    {/* <FormItem
                        label="党员报到日期"
                      >
                        {getFieldDecorator('reportTime', {
                          initialValue:undefined,
                          rules: [{ required: true, message: '请输入党员报到日期' }],
                        })(
                          <Dates />
                        )}
                      </FormItem> */}
                    {/* <FormItem
                        label="单位名称"
                      >
                        {getFieldDecorator('unitName', {
                          initialValue:undefined,
                          rules: [{ required: true, message: '请输入单位名称' }],
                        })(
                          <Input placeholder={'请输入单位名称'}/>
                        )}
                      </FormItem> */}
                  </React.Fragment>
                )}
                <FormItem label="转出日期">
                  {getFieldDecorator('transferOutTime', {
                    initialValue: undefined,
                    rules: [{ required: true, message: '请输入转出日期' }],
                  })(<Dates />)}
                </FormItem>
                {type == 'c' ? (
                  <FormItem label="转接原因">
                    {getFieldDecorator('reason', {
                      rules: [{ required: true, message: '请输入转接原因' }],
                    })(<TextArea rows={4} placeholder={'转接原因'} />)}
                  </FormItem>
                ) : (
                  <FormItem label={'转接原因'}>
                    {getFieldDecorator('d146Code', {
                      rules: [{ required: true, message: '请选择转接原因' }],
                    })(<DictTreeSelect codeType={'dict_d146'} placeholder={'请选择转接原因'} ref={(e) => (this['d146_code'] = e)} parentDisable={true} backType={'object'} />)}
                  </FormItem>
                )}
                {(type === 'a' || type === 'b') && (
                  <FormItem label={'介绍信有效期(天)'}>
                    {getFieldDecorator('letterValidity', {
                      rules: [{ required: true, message: '请填写介绍信有效期' }],
                    })(<InputNumber style={{ width: '100%' }} placeholder="介绍信有效期不能大于90天" max={90} min={0} />)}
                  </FormItem>
                )}
              </Form>
            </React.Fragment>
          )}
        </Modal>
        <Modal
          title={'全国交换区党组织接入列表'}
          visible={showList}
          onOk={() => { }}
          onCancel={() => {
            this.setState({ showList: false });
          }}
          width={'500px'}
          destroyOnClose={true}
          // bodyStyle={{maxHeight:'72vh',overflow:'auto'}}
          footer={null}
        >
          <ListTable
            scroll={{ y: '60vh' }}
            pagination={false}
            
            columns={unitColumns}
            data={unitList}
            // pagination={pagination}
            // onPageChange={this.onPageChange}
            // scroll={{y:'42vh'}}
            rowKey={'code'}
          />
        </Modal>
        <Modal
          title={'关系转接人员信息校验'}
          visible={visibleCheckMem}
          onOk={() => {  }}
          onCancel={() => {
            this.setState({ visibleCheckMem: false });
          }}
          width={'1500px'}
          destroyOnClose={true}
          // bodyStyle={{maxHeight:'72vh',overflow:'auto'}}
          footer={null}
        >
            <Alert message="该党员存在以下业务关联，请注意查看处理方式进行处理核实。" type="warning" showIcon />
            <div style={{ height: 12 }} />
            <div className={styles.list1}>
              <ListTable
                scroll={{ y: '60vh' }}
                pagination={false}
                columns={columnsCheckMem}
                data={checkMemData}
                rowKey={'memCode'}
              />
            </div>
        </Modal>
      </div>
    );
  }
}
export default Form.create()(TransferBet);
