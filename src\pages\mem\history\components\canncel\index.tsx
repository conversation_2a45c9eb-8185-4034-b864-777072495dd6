import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input } from 'antd';
import Tip from '@/components/Tip';
import { isEmpty } from '@/utils/method';
import _trim from 'lodash/trim';
const FormItem=Form.Item;
const {TextArea} = Input;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
class index extends React.Component<any, any>{
  constructor(props) {
    super(props);
    this.state = {
      visible:false,
      memInfo:{}
    }
  }
  handleOk=()=>{
    const {onClose} = this.props;
    const {memInfo:{code = ''}={}} = this.state;
    this.props.form.validateFieldsAndScroll(async(err, val) => {
      if (!err) {
        const res = await this.props.dispatch({
          type:'memLeaveOrg/del',
          payload:{data:{...val,code}}
        });
        const {code:resCode = 500} = res;
        if(resCode === 0){
          this.handleCancel();
          Tip.success('操作提示','操作成功');
          onClose && onClose();
        }
      }
    });
  };
  handleCancel=()=>{
    this.destory();
    this.setState({visible:false})
  };
  destory=()=>{
    this.setState({memInfo:{}})
  };
  open=(record)=>{
    this.setState({visible:true,memInfo:record})
  };
  validFunction = (rule, value, callback) => {
    if(isEmpty(_trim(value))){
      callback('必填');
    }
    callback();
  };
  render(): React.ReactNode {
    const {form} = this.props;
    const {getFieldDecorator} = form;
    const {visible,memInfo} = this.state;
    return (
      <Modal
        title={'撤销'}
        className='out_Modal'
        destroyOnClose
        closable={false}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        width={600}
      >
        <Form {...formItemLayout}>
          <FormItem
            {...formItemLayout}
            label="姓名"
          >
            {memInfo['name'] || ''}
          </FormItem>
          <FormItem
            {...formItemLayout}
            label="撤销原因"
          >
            {getFieldDecorator('backoutReason', {
              rules: [
                { required: true, message: '请填写撤销原因' },
                { validator: this.validFunction }
              ],
              // initialValue:info['name']
            })(
              <TextArea rows={3} placeholder={'请填写撤销原因'}/>
            )}
          </FormItem>
        </Form>

      </Modal>
    )
  }
}
export default Form.create()(index);
