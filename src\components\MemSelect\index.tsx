/**
 * 人员选中器
 */
import React from 'react';
import { Input, Modal, Button, Checkbox } from 'antd';
import OrgTree from '../OrgTree';
import List from './list';
import ListHistory from './listHistory';
import { connect } from 'dva';
import styles from './index.less';
import { root, rootParent } from 'src/common/config.js';
import { getSession } from '@/utils/session';
import { CloseCircleFilled } from '@ant-design/icons';
import _isEqual from 'lodash/isEqual';

const Search = Input.Search;
interface pType {
  onChange?: (data: Array<object>) => void;
  checkType?: 'checkbox' | 'radio';
  dispatch?: any;
  disabled?: boolean;
  placeholder?: string;
  org?: object;
  common?: any;
  initValue?: any;
  isPermissionCheck?: string; //是否权限检验,0--不进行校验,1--进行权限校验,为空默认进行权限检验
  searchType?: string;
  isFee?: string;
  memType?: string;
  filterTree?: Array<any>; //机构树筛选 此参数传递后 rootCode将失效
  selectedRows?: Array<string>; // 人员列表选中项
  title?: string; // 同普通title，显示标题
  d08CodeList?: Array<string>; // 机构树筛选
}
@connect(({ common }) => ({ common }), undefined, undefined, { forwardRef: true })
export default class index extends React.Component<pType, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      value: undefined,
      org: getSession('org'),
      isExOrg: false,
      data: [],
      reset: false, //是否点击清空按钮
    };
  }
  show = () => {
    const { org, filterTree = [] } = this.props;
    if (org) {
      this.loadData([org['orgCode'] || org['code']]);
    }
    if (filterTree.length > 0) {
      this.treeChange([], { node: { props: { dataRef: filterTree[0] } } });
      let codes = filterTree.map((obj) => obj['orgCode']);
      if (codes.length > 0) {
        this.loadData(codes);
      }
    }
    this.setState({
      visible: true,
    });
  };
  clearAll = () => {
    const { checkType = 'radio' } = this.props;
    this.setState({
      visible: false,
      value: checkType === 'checkbox' ? [] : undefined,
      org: getSession('org'),
      isExOrg: false,
      data: [],
    });
  };
  handleOk = (e) => {
    const { onChange, checkType = 'radio' } = this.props;
    const { data } = this.state;
    console.log(onChange,'onChangeonChangeonChange')
    onChange && onChange(data);
    if (checkType === 'checkbox') {
      let names: Array<string> = [];
      for (let obj of data) {
        names.push(obj['name']);
      }
      this.setState({
        value: names.join('，'),
      });
    } else {
      let obj = data[0] || {};
      this.setState({
        value: obj['name'],
      });
    }
    this.handleCancel();
  };
  handleCancel = () => {
    this.setState({
      selectedRows: [],
      visible: false,
    });
  };
  loadData = (val) => {
    const { isPermissionCheck = undefined, isFee = undefined } = this.props;
    this.props.dispatch({
      type: 'common/getTree',
      payload: {
        data: {
          orgCodeList: val,
          excludeOrgCodeList: [],
          isPermissionCheck,
          isFee,
          //禁止请求流动党组织
          isFlowStatus: 0,
        },
      },
    });
  };
  treeSearch = (val) => {
    this.props.dispatch({
      type: 'common/queryTree',
      payload: {
        name: val,
      },
    });
  };
  onChange = (data = []) => {
    this.setState({
      data,
    });
  };
  treeChange = (selectedKeys, e) => {
    const { dataRef } = e.node.props;
    const { subordinate } = this.state;
    this.setState({
      org: dataRef,
    });
    let COMP = List;
    if (this.props.memType == 'history') {
      COMP = ListHistory;
    }
    COMP['WrappedComponent'].action({ memOrgCode: dataRef['orgCode'], pageNum: 1, subordinate });
  };
  static getDerivedStateFromProps(props, state) {
    const { initValue, common, org, selectedRows, value: _value } = props;
    const { mapTreeCode } = common;
    let { value, isExOrg } = state;
    if (initValue && !value) {
      let obj = mapTreeCode.get(initValue);
      if (obj) {
        return { value: obj['shortName'] };
      } else if (root['parentCode'] === initValue) {
        return { value: rootParent['shortName'] };
      } else {
        return { value: initValue };
      }
    }
    // // 外部条件修改内部弹出框列表勾选，需要确定selectedRows和初始勾选值结构一样
    // if (selectedRows?.length && selectedRows?.length > 0 && value) {
    //   return { data: selectedRows, value: selectedRows.map((it) => it.name), timeKey: +new Date() };
    // }
    if (!isExOrg && org) {
      return { org, isExOrg: true };
    }
    return null;
  }
  // 外部条件修改内部弹出框列表勾选，需要确定selectedRows和初始勾选值结构一样
  outChangeselectedRows = (selectedRows) => {
    this.setState({
      data: selectedRows,
      value: selectedRows.map((it) => it.name),
      timeKey: +new Date(),
    });
  };
  onCheck = (e) => {
    const { org = {} } = this.state;
    this.setState({ subordinate: e.target.checked ? 1 : 0 });
    let COMP = List;
    if (this.props.memType == 'history') {
      COMP = ListHistory;
    }
    COMP['WrappedComponent'].action({
      orgCode: org['orgCode'],
      pageNum: 1,
      subordinate: e.target.checked ? 1 : 0,
    });
  };
  render() {
    const { visible, data, value, org, reset, timeKey = 1 } = this.state;
    const {
      common,
      children,
      placeholder,
      disabled = false,
      checkType,
      searchType,
      isFee,
      memType,
      filterTree = [],
      selectedRows = [],
      onChange,
      title = '',
      d08CodeList = [],
    } = this.props;
    let listOrg = org;
    // if(this.props.org){
    //   listOrg=this.props.org || {};
    // }
    let listData = common['listTree'];
    if (filterTree.length > 0) {
      let codes = filterTree.map((obj) => obj['orgCode']);
      listData = listData.filter((obj) => codes.includes(obj['orgCode']));
    }
    const { subordinate } = sessionStorage;
    return (
      <React.Fragment>
        {children ? (
          React.cloneElement(children as any, {
            onClick: this.show,
          })
        ) : (
          <Search
            title={title}
            value={value}
            disabled={disabled}
            onClick={this.show}
            onSearch={this.show}
            placeholder={placeholder || '请点击选择'}
            suffix={
              // 清空按钮
              <CloseCircleFilled
                style={{ color: 'rgba(0, 0, 0, 0.25)' }}
                onClick={() => {
                  this.clearAll();
                  this.setState({
                    reset: true,
                    timeKey: +new Date(),
                  });
                  onChange && onChange([]);
                }}
              />
            }
            enterButton
            readOnly
          />
        )}
        <Modal
          title="人员选择器"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          destroyOnClose={true}
          width={1200}
          bodyStyle={{ padding: 0 }}
          zIndex={1009}
          footer={[
            <Button onClick={this.handleCancel}>取消</Button>,
            <Button type="primary" onClick={this.handleOk} disabled={!(this.state.data.length > 0)}>
              确定
            </Button>,
          ]}
        >
          <div className={styles.content}>
            <div className={styles.tree}>
              <div className={styles.tit}>
                机构筛选
                <Checkbox
                  defaultChecked={subordinate == 1}
                  onChange={this.onCheck}
                  style={{ float: 'right' }}
                >
                  包含下级
                </Checkbox>
              </div>
              <OrgTree
                listData={listData}
                mapData={common['mapTree']}
                filterData={common['filterData']}
                loadData={this.loadData}
                onSearch={this.treeSearch}
                onChange={this.treeChange}
                showSearch={false}
                rootCode={
                  this.props.org ? this.props.org['orgCode'] : filterTree.length > 0 ? 0 : undefined
                }
                type={'selector'}
              />
            </div>
            <div className={styles.list} key={timeKey}>
              {memType == 'history' ? (
                <ListHistory
                  selectedRows={reset ? [] : selectedRows}
                  subordinate={this.state.subordinate}
                  checkType={checkType}
                  isFee={isFee}
                  searchType={searchType}
                  orgCode={listOrg['orgCode'] || listOrg['managerOrgCode']}
                  onChange={this.onChange}
                />
              ) : (
                <List
                  selectedRows={reset ? [] : selectedRows}
                  subordinate={this.state.subordinate}
                  checkType={checkType}
                  isFee={isFee}
                  searchType={searchType}
                  orgCode={listOrg['orgCode'] || listOrg['managerOrgCode']}
                  onChange={this.onChange}
                  d08CodeList={d08CodeList}
                />
              )}
            </div>
          </div>
        </Modal>
      </React.Fragment>
    );
  }
}
