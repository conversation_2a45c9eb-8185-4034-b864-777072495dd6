const { BrowserWindow, ipcMain } = require('electron');
const path = require('path');

const isDevelopment = process.env.NODE_ENV === 'development';
let mainWindow = null;

function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1920,
    height: 1080,
    minHeight: 1920,
    minWidth: 1080,
    show: false,
    frame: false,
    title: 'Harbour',
    // webPreferences: {
    //     nodeIntegration: true,
    //     preload: path.resolve(__dirname, '../utils/contextBridge.js')
    // },
    // icon: path.resolve(__dirname, '../assets/logo.png')
  });

  if (isDevelopment) {
    mainWindow.loadURL('http://localhost:8000/');
  } else {
    const entryPath = path.resolve(__dirname, '../index.html');
    mainWindow.loadFile(entryPath);
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });
}

module.exports = { createMainWindow };
