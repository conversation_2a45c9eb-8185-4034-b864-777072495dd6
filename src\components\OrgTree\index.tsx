import React from 'react';
import style from './index.less';
import {
  SearchOutlined,
  FileFilled,
  FolderAddOutlined,
  FolderOpenOutlined,
  FolderAddFilled,
  FolderFilled,
  FolderOpenFilled,
  DownOutlined,
} from '@ant-design/icons';
import { AutoComplete, Input, Select, Tree, Checkbox, Collapse, Popover } from 'antd';
import { connect } from 'dva';
import { _history, throttle } from '@/utils/method.js';
import { getSession } from '@/utils/session';
import qs from 'qs';

const TreeNode = Tree.TreeNode;

interface proType {
  dispatch?: any;
  onChange?: (selectedKeys: Array<string>, e: object) => void;
  onSearch?: (value: string) => void;
  code?: string;
  rootCode?: string;
  nodeCode?: string;
  nodeName?: string;
  nodeParent?: string;
  listData: Array<any>;
  mapData: Map<string, object>;
  filterData?: Array<any>;
  loadData?: (data: Array<string>, callback?: Function, type: string) => void;
  showSearch?: boolean;
  deductHeight?: any;
  type?: 'selector' | 'tree' | 'userTree';
  exclude?: Array<string>;
  disabled?: boolean;
  isShowFlowOrg?: boolean; //是否显示流动党员党组织 默认值 false 不显示
}

// @ts-ignore
@connect()
export default class index extends React.Component<proType, {}> {
  static defaultProps = {
    code: 'code',
    nodeCode: 'orgCode',
    nodeName: 'name',
    nodeParent: 'parentCode',
    listData: [],
    type: 'tree',
    mapData: new Map(),
    filterData: [],
    showSearch: true,
  };
  constructor(props) {
    super(props);
    const org = getSession('org') || {};
    this.state = {
      selectedKeys: [],
      expandedKeys: [],
      filterData: [],
      initExpand: false,
      subordinate: org['subordinate'] || 0,
      isFlowStatus: sessionStorage.getItem('isFlowStatus') ?? 1,
    };
  }
  onExpand = (expandedKeys, e) => {
    //展开树节点
    this.setState({
      expandedKeys,
    });
  };
  onCheck = (e) => {
    this.setState({ subordinate: e.target.checked ? 1 : 0 }, () => {
      const { selectedKeys }: any = this.state;
      const session = getSession('org');
      sessionStorage.setItem('subordinate', e.target.checked ? '1' : '0');
      this.onSelect(selectedKeys, { selected: true, node: { dataRef: session } });
    });
  };
  onCheckOrg = (e) => {
    this.setState({ isFlowStatus: e.target.checked ? 1 : 0 }, () => {
      const { selectedKeys, expandedKeys }: any = this.state;
      const session = getSession('org');
      const roles: any = getSession('roles') || {};
      sessionStorage.setItem('isFlowStatus', e.target.checked ? '1' : '0');
      this.onSelect(selectedKeys, { selected: true, node: { dataRef: session } });
      const { loadData } = this.props;
      let maxKey = roles?.managerOrgCode;
      let codeList: any = [];
      for (const key of expandedKeys) {
        if (key.length >= maxKey.length) {
          if (!codeList.includes(key)) {
            codeList.push(key);
          }
        }
      }
      if (loadData) {
        loadData(codeList, () => { }, 'initTree');
      }
    });
  };
  onSelect = (selectedKeys, e) => {
    //选中树节点
    const { selected } = e;
    if (selected) {
      //选中执行后面的操作
      const { onChange, type } = this.props;
      const { subordinate, isFlowStatus }: any = this.state;
      const { dataRef } = e.node;
      dataRef['subordinate'] = subordinate;
      onChange && onChange(selectedKeys, e);
      this.setState({
        selectedKeys,
        item: e,
      });
      if (e.node) {
        if (type === 'tree') {
          let org = { ...e.node.dataRef };
          sessionStorage.setItem('org', JSON.stringify(org));
          const { query } = _history.location;
          if (query['pageNum']) {
            query['pageNum'] = 1;
          }
          _history.push(`?${qs.stringify(query)}`);
        }
      }
    }
  };
  onSearch = (val: string) => {
    //搜索内容变更
    const { onSearch } = this.props;
    onSearch && throttle(onSearch, val, 500, 1000);
  };
  handleChange = async (val, option) => {
    //搜索确定节点
    if (val) {
      const { subordinate }: any = this.state;
      const org = getSession('roles') || {};
      let len = 0; //账号管理节点长度
      if (org['managerOrgCode']) {
        len = org['managerOrgCode'].length;
      }
      let expandedKeys: Array<string> = [];
      const { loadData, mapData, type, onChange, nodeCode } = this.props;
      if (loadData) {
        let len = val.length;
        let num = len / 3;
        let data: Array<string> = [];
        for (let i = 0; i < num; i++) {
          let code = val.substring(0, i * 3);
          expandedKeys.push(code);
          if (code && code.length > len && !mapData.get(code)) {
            //不允许搜索比自己管理节点高的组织树
            data.push(code);
          }
        }
        //@ts-ignore
        let resData: object = await loadData(data);
        console.log(resData, val, data, 'resDataresData');
        //数据延迟响应需设置延时
        setTimeout(() => {
          if (type === 'tree') {
            let obj = mapData.get(val);
            if (!obj && resData) {
              obj = resData['data'].find((obj) => obj['orgCode'] === val);
            }
            console.log(obj, 'objobjobjobjobj');
            if (obj) {
              obj['subordinate'] = subordinate;
              sessionStorage.setItem('org', JSON.stringify(obj));
              const { query } = _history.location;
              if (query['pageNum']) {
                query['pageNum'] = 1;
              }
              let ele = document.getElementById(`#${val}`);
              ele && ele.scrollIntoView(true);
              _history.push(`?${qs.stringify(query)}`);
              onChange && onChange([obj[nodeCode || '']], { node: { dataRef: obj } });
            }
          }
        }, 500);
      }
      this.setState({
        selectedKeys: [val],
        expandedKeys,
      });
    }
  };
  static getDerivedStateFromProps(nextProps, prevState) {
    //刷新页面 默认展开树节点及选中节点
    const { nodeCode, listData, type } = nextProps;
    let selectedKeys = [...prevState['selectedKeys']];
    let { expandedKeys, initExpand } = prevState;
    if (listData.length > 0 && !initExpand) {
      expandedKeys.push(listData[0][nodeCode || '']);
      initExpand = true;
      if (selectedKeys.length === 0) {
        // if(type==='tree'){
        const org = getSession('org') || {};
        let code = org[nodeCode];
        if (org && code && code.length > 0) {
          const num = code.length / 3;
          for (let i = 0; i < num; i++) {
            expandedKeys.push(code.substring(0, i * 3));
          }
        }
        selectedKeys.push(code);
        // }else{
        //   selectedKeys.push(listData[0][nodeCode]);
        // }
        if (type === 'userTree') {
          selectedKeys = [];
        }
      }
      return {
        expandedKeys,
        selectedKeys,
        initExpand,
      };
    }
    return null;
  }
  renderRootNodes = (root) => {
    //渲染树节点
    const { listData, code, nodeCode, nodeName, isShowFlowOrg = false } = this.props;
    const { d01Code } = root;
    let flow: any = '';
    if (`${d01Code}`.startsWith('8')) {
      if (!isShowFlowOrg) {
        return null;
      }
      flow = <span style={{ color: 'red' }}>「流」</span>;
    }
    return (
      <TreeNode
        title={`${flow}${root[nodeName || '']}`}
        key={root[nodeCode || '']}
        isLeaf={root['isLeaf'] == '1'}
        dataRef={{ ...root }}
      >
        {this.renderTreeNodes(listData, root[code || ''])}
      </TreeNode>
    );
  };
  renderTreeNodes = (data, rootCode) => {
    //渲染树节点
    const {
      code,
      nodeCode,
      nodeName,
      nodeParent,
      exclude = [],
      isShowFlowOrg = false,
    } = this.props;
    const filterData = data.filter((obj) => obj[nodeParent || ''] === rootCode);
    return filterData.map((item) => {
      const children = data.find((obj) => obj[nodeParent || ''] === item[code || '']);
      const { d01Code } = item;
      let flow: any = '';
      if (`${d01Code}`.startsWith('8')) {
        if (!isShowFlowOrg) {
          return null;
        }
        flow = <span style={{ color: 'red' }}>「流」</span>;
      }
      if (exclude.includes(item[nodeCode || ''])) {
        return null;
      }
      if (children) {
        const nextData = data.filter((obj) => obj[nodeParent || ''] !== rootCode);
        return (
          // @ts-ignore
          <TreeNode
            // title={item[nodeName || ""]}
            title={
              <span id={`#${item[nodeCode || '']}`}>
                {flow}
                {item[nodeName || '']}
              </span>
            }
            key={item[nodeCode || '']}
            switcherIcon={
              this.state.expandedKeys.includes(item[nodeCode || '']) ? (
                <React.Fragment>
                  <FolderOpenFilled style={{ color: '#ffb02c' }} />
                </React.Fragment>
              ) : (
                <FolderAddFilled style={{ color: '#ffb02c', fontSize: 16 }} />
              )
            }
            isLeaf={item['isLeaf'] == '1'}
            dataRef={{ ...item }}
          >
            {this.renderTreeNodes(nextData, item[code || ''])}
          </TreeNode>
        );
      }
      // @ts-ignore
      return (
        <TreeNode
          // title={item[nodeName || ""]}
          title={
            <span id={`#${item[nodeCode || '']}`}>
              {flow}
              {item[nodeName || '']}
            </span>
          }
          key={item[nodeCode || '']}
          switcherIcon={
            item['isLeaf'] == '1' ? (
              <FileFilled style={{ color: '#999' }} />
            ) : (
              <FolderAddFilled style={{ color: '#ffb02c', fontSize: 16 }} />
            )
          }
          isLeaf={item['isLeaf'] == '1'}
          dataRef={{ ...item }}
        />
      );
    });
  };
  renderTreeNodes2 = (data) => {
    //渲染树节点
    const { nodeCode, nodeName, isShowFlowOrg = false } = this.props;
    return data.map((item) => {
      const { d01Code } = item;
      let flow: any = '';
      if (`${d01Code}`.startsWith('8')) {
        if (!isShowFlowOrg) {
          return null;
        }
        flow = <span style={{ color: 'red' }}>「流」</span>;
      }
      return (
        <TreeNode
          // title={item[nodeName || ""]}
          title={
            <span id={`#${item[nodeCode || '']}`}>
              {flow}
              {item[nodeName || '']}
            </span>
          }
          key={item[nodeCode || '']}
          switcherIcon={
            item['isLeaf'] == '1' ? (
              <FileFilled style={{ color: '#999' }} />
            ) : (
              <FolderAddFilled style={{ color: '#ffb02c', fontSize: 16 }} />
            )
          }
          isLeaf={item['isLeaf'] == '1'}
          dataRef={{ ...item }}
        />
      );
    });
  };
  loadData = (node) => {
    //异步加载数据
    const { listData, mapData, code, nodeCode, nodeParent, loadData } = this.props;
    const { dataRef } = node.props;
    const find = listData.find((obj) => obj[nodeParent || ''] === dataRef[code || '']);
    if (find) {
      return new Promise<void>((resolve) => {
        return resolve();
      });
    }
    return new Promise<void>(async (resolve) => {
      if (loadData) {
        await loadData([dataRef[nodeCode || '']]);
      }
      resolve();
    });
  };
  // 多选
  onTreeCheck = (checkedKeys, info) => {
    const { checkedNodes } = info;
    let orgIds: any = [];
    for (let obj of checkedNodes) {
      const { dataRef = {} } = obj;
      if (dataRef['id']) {
        orgIds.push(dataRef['id']);
      }
    }
    this.props.dispatch({
      type: 'login/updateState',
      payload: {
        orgCodes: checkedKeys.checked,
        orgIds,
      },
    });
  };
  render(): React.ReactNode {
    const { query } = _history.location;
    const checkable = query['box'] !== undefined;
    const { orgCodes = [] }: any = this.props;

    const roles = getSession('roles') || {};
    const { subordinate, isFlowStatus }: any = this.state;
    const {
      listData = [],
      rootCode = roles['managerOrgCode'],
      nodeName,
      code,
      filterData,
      nodeCode,
      showSearch,
      deductHeight,
      disabled = false,
    } = this.props;
    let defaultExpandedKeys: Array<string> = [];
    let root: any = undefined;
    if (listData.length > 0) {
      defaultExpandedKeys = [listData[0][nodeCode || '']];
      if (rootCode) {
        root = listData.find(
          (obj) => obj[code || ''] === rootCode || obj[nodeCode || ''] === rootCode,
        ); //组织选择器 人员选择器 需传递父节点 code 或者 orgCode
      }
    }
    return (
      <div className={`${style.page} newFishInfo3`}>
        {showSearch && (
          <React.Fragment>
            <div className={style.tit}>
              <div>机构筛选</div>
              <div>
                <Popover
                  placement="bottom"
                  content={
                    <div className="o_check">
                      <Checkbox
                        checked={subordinate == 1}
                        onChange={this.onCheck}
                        style={{ float: 'right' }}
                      >
                        包含下级
                      </Checkbox>
                      <Checkbox
                        checked={isFlowStatus == 1}
                        onChange={this.onCheckOrg}
                        style={{ float: 'right' }}
                      >
                        包含流动党组织
                      </Checkbox>
                    </div>
                  }
                  trigger="click"
                >
                  <div className={style.check}>
                    显示内容
                    <DownOutlined />
                  </div>
                </Popover>
              </div>
            </div>
            <Select
              allowClear
              showSearch
              placeholder={'请输入搜索关键词'}
              style={{ width: '97.5%', margin: '0 1%' }}
              defaultActiveFirstOption={false}
              filterOption={false}
              onSearch={this.onSearch}
              onChange={this.handleChange}
              notFoundContent={null}
              suffixIcon={<SearchOutlined />}
            >
              {filterData?.map((item) => (
                <Select.Option value={item['orgCode']}>{item['name']}</Select.Option>
              ))}
            </Select>
          </React.Fragment>
        )}
        <div style={{ height: `calc(100vh - ${deductHeight + 140}px)`, overflow: 'auto' }}>
          {listData.length > 0 && (
            <Tree
              onExpand={this.onExpand}
              onSelect={this.onSelect}
              loadData={this.loadData}
              defaultExpandedKeys={defaultExpandedKeys}
              expandedKeys={this.state['expandedKeys']}
              selectedKeys={this.state['selectedKeys']}
              disabled={disabled}
              checkable={checkable}
              checkStrictly={checkable}
              checkedKeys={orgCodes}
              onCheck={this.onTreeCheck}
              showLine
              showIcon
            >
              {root
                ? this.renderRootNodes(root)
                : rootCode
                  ? this.renderTreeNodes(listData, rootCode)
                  : this.renderTreeNodes2(listData)}
            </Tree>
          )}
        </div>
      </div>
    );
  }
}
