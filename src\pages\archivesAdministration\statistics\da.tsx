import React, { useState, Fragment, useEffect, useRef } from 'react';
import _isEmpty from 'lodash/isEmpty';
import style from './index.less';
import { _history } from "@/utils/method";
import qs from 'qs';
import { Tabs, Input, Modal, Form, Radio, Button, Descriptions, Divider } from 'antd';
import ListTable from 'src/components/ListTable';
import { getMemDevelopAuditList, auditMemDevelop, digitalCountList, expdigitalCountList } from '../service'
import { getSession } from '@/utils/session';
import NowOrg from 'src/components/NowOrg';
import moment from 'moment'
import { connect } from "dva";
import tip from '@/components/Tip';
import Icon from '@ant-design/icons';
import Fx from './fx';


function Index(props: any) {
    const [loading, setLoading] = useState(false);
    const [list, setList] = useState([]);
    const [columns, setColumns]: any = useState([
    ])
    const fxref = useRef();
    const org = getSession('org') || {};

    const getList = async (p?: any) => {
        setLoading(true)
        const { code = 500, data: { list = [], ...other } = {} } = await digitalCountList({ orgLevelCode: org['orgCode'] })
        setLoading(false)
        if (code == 0) {
            setList(list)
            setColumns([
                {
                    title: '序号',
                    dataIndex: 'num',
                    width: 60,
                    align: 'center',
                    render: (text, record, index) => {
                        return index + 1
                    }
                },
                {
                    title: '党组织名称',
                    align: 'orgName',
                    dataIndex: 'orgName',
                    width: 300,
                },
                {
                    title: <span>全阶段档案情况(未完成<a onClick={() => {
                        fxref.current.open({ orgLevelCode: org['orgCode'], colName: 'stepsum', match: false })
                    }
                    }>{other['stepMissSum']}</a>/已完成<a onClick={() => {
                        fxref.current.open({ orgLevelCode: org['orgCode'], colName: 'stepsum', match: true })
                    }
                    }>{other['stepDoneSum']}</a>)</span>,
                    width: 80,
                    align: 'center',
                    children: [
                        {
                            title: <span>第一阶段<br/>（入党申请人）</span>,
                            dataIndex: 'step1',
                            width: 120,
                            align: 'center',
                            render: (text, record) => {
                                return <span>
                                    <a onClick={() => {
                                        fxref.current.open({
                                            ...record,
                                            colName: 'step1',
                                            match: false
                                        })
                                    }}>{record['step1Miss']}</a>
                                    /
                                    <a onClick={() => {
                                        fxref.current.open({
                                            ...record,
                                            colName: 'step1',
                                            match: true
                                        })
                                    }}>{record['step1Done']}</a>
                                </span>
                            }
                        },
                        {
                            title: <span>第二阶段<br/>（入党申请人）</span>,
                            dataIndex: 'step2',
                            width: 120,
                            align: 'center',
                            render: (text, record) => {
                                return <span>
                                    <a onClick={() => {
                                        fxref.current.open({
                                            ...record,
                                            colName: 'step2',
                                            match: false
                                        })
                                    }}>{record['step2Miss']}</a>
                                    /
                                    <a onClick={() => {
                                        fxref.current.open({
                                            ...record,
                                            colName: 'step2',
                                            match: true
                                        })
                                    }}>{record['step2Done']}</a>
                                </span>
                            }
                        },
                        {
                            title: <span>第三阶段<br/>（积极分子）</span>,
                            dataIndex: 'step3',
                            width: 120,
                            align: 'center',
                            render: (text, record) => {
                                return <span>
                                    <a onClick={() => {
                                        fxref.current.open({
                                            ...record,
                                            colName: 'step3',
                                            match: false
                                        })
                                    }}>{record['step3Miss']}</a>
                                    /
                                    <a onClick={() => {
                                        fxref.current.open({
                                            ...record,
                                            colName: 'step3',
                                            match: true
                                        })
                                    }
                                    }>{record['step3Done']}</a>
                                </span>
                            }
                        },
                        {
                            title: <span>第四阶段<br/>（发展对象）</span>,
                            dataIndex: 'step4',
                            width: 120,
                            align: 'center',
                            render: (text, record) => {
                                return <span>
                                    <a onClick={() => {
                                        fxref.current.open({
                                            ...record,
                                            colName: 'step4',
                                            match: false
                                        })
                                    }
                                    }>{record['step4Miss']}</a>
                                    /
                                    <a onClick={
                                        () => {
                                            fxref.current.open({
                                                ...record,
                                                colName: 'step4',
                                                match: true
                                            })
                                        }}>{record['step4Done']}</a>
                                </span>
                            }
                        },
                        {
                            title: <span>第五阶段<br/>（预备党员）</span>,
                            dataIndex: 'step5',
                            width: 120,
                            align: 'center',
                            render: (text, record) => {
                                return <span>
                                    <a onClick={() => {
                                        fxref.current.open({
                                            ...record,
                                            colName: 'step5',
                                            match: false
                                        })
                                    }}>{record['step5Miss']}</a>
                                    /
                                    <a onClick={() => {
                                        fxref.current.open({
                                            ...record,
                                            colName: 'step5',
                                            match: true
                                        })
                                    }}>{record['step5Done']}</a>
                                </span>
                            }
                        },
                        {
                            title: '其他',
                            dataIndex: 'stepOther',
                            width: 100,
                            align: 'center',
                            render: (text, record) => {
                                return <span>
                                    <a
                                        onClick={() => {
                                            fxref.current.open({
                                                ...record,
                                                colName: 'stepOther',
                                                match: false
                                            })
                                        }}
                                    >{record['stepOtherMiss']}</a>
                                    /
                                    <a onClick={() => {
                                     fxref.current.open({
                                        ...record,
                                        colName: 'stepOther',
                                        match: true
                                    })
                                }}>{record['stepOtherDone']}</a></span>
                            }
                        },
                    ]
                },
                {
                    title: <div>入党申请人-确定为积极份子 <br />（未完成<span>{other['rdsqrMissSum']}</span>/已完成<span>{other['rdsqrDoneSum']}</span>）</div>,
                    dataIndex: 'rdsqr',
                    width: 200,
                    align: 'center',
                    render: (text, record) => {
                        return <span>{record['rdsqrMiss']}/{record['rdsqrDone']}</span>
                    }
                },
                {
                    title: <div>积极份子-确定为发展对象 <br />（未完成<span>{other['jjfzMissSum']}</span>/已完成<span>{other['jjfzDoneSum']}</span>）</div>,
                    dataIndex: 'jjfz',
                    width: 200,
                    render: (text, record) => {
                        return <span>{record['jjfzMiss']}/{record['jjfzDone']}</span>
                    }
                },
                {
                    title: <div>发展对象-接收为预备党员 <br />（未完成<span>{other['fzdxMissSum']}</span>/已完成<span>{other['fzdxMissSum']}</span>）</div>,
                    dataIndex: 'fzdx',
                    width: 200,
                    render: (text, record) => {
                        return <span>{record['fzdxMiss']}/{record['fzdxDone']}</span>
                    }
                },
                {
                    title: <div>预备党员-正式党员 <br />（未完成<span>{other['ybdyMissSum']}</span>/已完成<span>{other['ybdyDoneSum']}</span>）</div>,
                    dataIndex: 'ybdy',
                    width: 200,
                    render: (text, record) => {
                        return <span>{record['ybdyMiss']}/{record['ybdyDone']}</span>
                    }
                },
                {
                    title: <div>其他<Icon style={{ color: 'red' }} type="exclamation-circle" /><br />（未完成<span>{other['otherMissSum']}</span>/已完成<span>{other['otherDoneSum']}</span>）</div>,
                    dataIndex: 'other',
                    width: 200,
                    render: (text, record) => {
                        return <span>{record['otherMiss']}/{record['otherDone']}</span>
                    }
                },

            ])
        }

    }
    const exp = async () => {
        const { code = 500, data = {} } = await expdigitalCountList({ orgLevelCode: org['orgCode'] })
    }

    useEffect(() => {
        getList()
    }, [org['orgCode']])
    return (
        <div>
            {/* <NowOrg extra={
                <React.Fragment>
                    <Search style={{ width: 200, marginLeft: 16 }} onSearch={search} onChange={searchClear} placeholder={'请输入检索关键词'} />
                </React.Fragment>
            } /> */}
            <div style={{ marginBottom: 10 ,textAlign: 'right'}}>
                <Button onClick={exp} type='primary'>导出</Button>
            </div>
            <ListTable
             
              columns={columns} data={list} pagination={false} onPageChange={(page, pageSize) => {
                getList({ pageNum: page, pageSize });
            }} />

            <Fx ref={fxref} />
        </div>
    );
}
export default Index