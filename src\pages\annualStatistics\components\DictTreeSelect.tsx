/**
 * 字典treeSelect组件
 */
 import React from 'react';
 import {TreeSelect} from "antd";
 import {connect} from "dva";
 import { isEmpty } from '@/utils/method';
 import {TreeSelectProps} from "antd/lib/tree-select";
 const TreeNode = TreeSelect.TreeNode;
 interface propsType extends TreeSelectProps<any>{
   codeType:string,
   placeholder?:string,
   codeValue?:string,
   codeName?:string,
   onChange?:(any)=>void,
   onFocus?:()=>void,
   backType?:'object',
   dispatch?:any,
   commonDict?:object,
   renderItem?:(any)=>{},
   parentDisable?:boolean,
   treeCheckable?:boolean,
   initValue?:string | Array<any>,
   searchKey?:Array<string>,
   disabled?: boolean,
   extendProps?: Object,
   size?:"large"| "small",
   style?:object,
   disSlt?:Array<string>,//不可选择的项
   canSlt?:Array<string>,//可选择的项
   addOther?:Array<any>, // 额外增加字典表没有的字段
   filter?:any,
   send?:boolean,//默认发送请求
 }
 // @ts-ignore
 @connect( ({commonDict},props:any)=>{
   const {codeType}=props;
   if(codeType){
     return {
       commonDict:{
         [codeType]:commonDict[`${codeType}`],
         [`${codeType}List`]:commonDict[`${codeType}List`],
       }
     }
   }else{
     return {commonDict}
   }
 },null,null,{forwardRef:true})
 export default class index extends React.PureComponent<propsType & any,any> {
   static defaultProps = {
     codeValue:'code',//node 唯一key
     codeName:'name',//node 显示名称
     searchKey:['name'],//默认查询条件
     placeholder:'请选择',
     searchPlaceholder:'请输入关键词检索',
     parentDisable:false,
     treeData:undefined,
     mode:undefined,
     treeCheckable:false,
     disabled:false,
   };
   static clear(){};
   constructor(props){
     super(props);
     this.state={
       value:undefined
     };
     index.clear=this.clearAll;
   }
   componentDidMount(): void {
     const {codeType,commonDict,initValue,send=true}=this.props;
     if(!commonDict[codeType] && send){
       this.getDict();
     }
     if(initValue){
       this.setState({
         value: initValue,
       });
     }
   }

   componentDidUpdate(prevProps: Readonly<propsType>, prevState: Readonly<any>, snapshot?: any): void {
     if (prevProps.initValue !== this.props.initValue) {
       this.setState({
         value: this.props.initValue,
       });
     }
   }

   static getDerivedStateFromProps = (nextProps:any, prevState:any) => {
     const state = {};
     const {commonDict,addOther,codeType,backType,codeValue} = nextProps;
     let data = commonDict[codeType] || [];
     if(!isEmpty(addOther)){
       data = data.concat(addOther);
     }
     if('value' in nextProps){
       if(backType==='object'){
         if(nextProps['value']){
           if(typeof nextProps['value']=='object'){
             state['value'] = nextProps['value'][codeValue];
           }else{
             state['value'] = nextProps['value'];
           }
         }else{
           state['value'] = undefined;
         }
       }else{
         state['value'] = nextProps['value'];
       }
     }
     state['data'] = data;
     return state;
   };
   clearAll=()=>{
     this.setState({
       value: undefined,
     })
   };
   getDict=()=>{
     const {codeType,onFocus}=this.props;
     this.props.dispatch({
       type:'commonDict/queryDictTree',
       payload:{
        typeCode:codeType,
       }
     });
     if(onFocus){
       onFocus();
     }
   };
   onChange=(value, label, extra)=>{
     const {codeType,codeValue,onChange,backType}=this.props;
     this.setState({
       value,
     });
     if(backType==='object'){
       const data=this.props.commonDict[`${codeType}List`];
       if(data){
         let find = data.find(obj=>obj[codeValue]===value);
         if(find){
           onChange && onChange(Object.assign({}, find))
         }else{
           onChange && onChange(undefined)
         }
       }
     }else if(onChange){
       onChange(value)
     }
   };
   onSearch=(val)=>{
     console.log(val)
   };
   onSelect=(value)=>{

   };
   onFocus=()=>{
     const {onFocus}=this.props;
     if(onFocus){
       onFocus();
     }
   };
   renderTreeNodes = data => {
     const {codeName, codeValue, parentDisable,disSlt=[],canSlt=[]} = this.props;
     return data.map((item:any) => {
       if(disSlt.includes(item[codeValue])){
         return <React.Fragment key={item[codeValue]}/>;
       }
       if(canSlt.length>0 && !canSlt.includes(item[codeValue])){
         return <React.Fragment key={item[codeValue]}/>;
       }
       if (item.children) {
         return (
           <TreeNode title={item[codeName]} key={item[codeValue]} value={item[codeValue]} dataRef={item} disabled={parentDisable}>
             {this.renderTreeNodes(item.children)}
           </TreeNode>
         );
       }
       return <TreeNode title={item[codeName]} key={item[codeValue]} value={item[codeValue]} dataRef={item} />;
     });
   };
   filterTreeNode=(val,node)=>{
     const {codeValue,searchKey} = this.props;
     const { dataRef }=node.props;
     let resData=[dataRef[codeValue].includes(val)];
     for(let obj of searchKey){
       resData.push(dataRef[obj].includes(val))
     }
     return resData.includes(true);
   };
   render(): React.ReactNode {
     const {placeholder,searchPlaceholder,dropdownStyle,renderItem, treeData,
       treeCheckable, disabled, extendProps,size='default',style,filter }=this.props;
     // const data=this.props.commonDict[codeType] || [];
     const { data } = this.state;
     return(
       <TreeSelect
         {...extendProps}
         value={this.state['value']}
         showSearch
         allowClear
         treeCheckable={treeCheckable}
         // treeDefaultExpandAll={parentDisable}
         dropdownStyle={dropdownStyle?dropdownStyle:{ maxHeight: 400, overflow: 'auto' }}
         placeholder={placeholder}
         treeData={treeData}
         onFocus={this.getDict}
         onSelect={this.onSelect}
         onChange={this.onChange}
         filterTreeNode={this.filterTreeNode}
         disabled={disabled}
         autoFocus
         size={size}
         style={style}
       >
         {
           renderItem ? renderItem(data) : this.renderTreeNodes(filter ? filter(data) : data)
         }
       </TreeSelect>
     );
   }
 }
