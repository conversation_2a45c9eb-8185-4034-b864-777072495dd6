import React, { useEffect, useImperativeHandle, useState } from 'react';
import { Input, Modal } from 'antd';
import styles from './diffRule.less'
import { validCard } from '@/utils/rules';
const TextArea=Input.TextArea;
function sortNumber(a,b) {
  let aSeparator,bSeparator;
  if(a.includes('.')){
    aSeparator='.'
  }
  if(a.includes('．')){
    aSeparator='．'
  }
  if(b.includes('.')){
    bSeparator='.'
  }
  if(b.includes('．')){
    bSeparator='．'
  }
  if(aSeparator && bSeparator){
    let aIndex=a.indexOf(aSeparator)
    let bIndex=b.indexOf(bSeparator)
    let aNum=a.substring(0,aIndex);
    let bNum=b.substring(0,bIndex);
    let res=Number(aNum)-Number(bNum);
    if(isNaN(res)){
      return 0
    }
    return res;
  }
  return false
}
function index(props,ref){
  const [visible,setVisible]=useState(false);
  const [oldRule,setOldRule]=useState([]);
  const [newRule,setNewRule]=useState([]);
  const [text,setText]=useState();

  useImperativeHandle(ref,()=>({
    open:(val)=>open(val)
  }));
  useEffect(()=>{
    if(text){
      diffValue(text)
    }
  },[JSON.stringify(oldRule),text])
  const open=(data)=>{
    // let rule:any=["9.","8.","5."];
    let rule:any=[];
    for (const item of data) {
      let val=item['tableRuler'];
      if(val){
        let lastVal=val.replace(/\s+/g,"");
        rule.push(lastVal)
      }
    }
    rule = Array.from([...new Set(rule)]);
    rule.sort(sortNumber);
    setOldRule(rule);
    setNewRule([]);
    setVisible(true)
  }
  const diffValue=(val)=>{
    let temp:any=[];
    setText(val);
    if(val){
      val=val.replace(/\s+/g,"");
      let data=val.split(';');
      data.sort(sortNumber);
      // console.log(oldRule,'ddddddddddddddd')
      for (const rule of data) {
        if(rule){
          let find = oldRule.find(obj=>obj==rule);
          if(find){
            temp.push({value:rule,color:'black'})
          }else{
            let find = oldRule.find(obj=>{
              let aSeparator,bSeparator;
              if(`${obj}`.includes('.')){
                aSeparator='.'
              }
              if(`${obj}`.includes('．')){
                aSeparator='．'
              }
              if(`${rule}`.includes('.')){
                bSeparator='.'
              }
              if(`${rule}`.includes('．')){
                bSeparator='．'
              }
              let aIndex=`${obj}`.indexOf(aSeparator)
              let bIndex=`${rule}`.indexOf(bSeparator)
              let aText=`${obj}`.substring(aIndex+1);
              let bText=`${rule}`.substring(bIndex+1);
              if(aText){
                aText=aText.replace(/[.]/g,"");
                aText=aText.replace(/[·]/g,"");
                aText=aText.replace(/[•]/g,"");
              }
              if(bText){
                bText=bText.replace(/[.]/g,"");
                bText=bText.replace(/[·]/g,"");
                bText=bText.replace(/[•]/g,"");
              }
              return aText==bText
            });
            if(find){
              temp.push({value:rule,color:'orange'})
            }else{
              temp.push({value:rule,color:'red'})
            }
          }
        }
      }
    }
    setNewRule(temp);
  }
  const txtChange=(e)=>{
    let val=e.target.value;
    if(val){
      val=val.replace(/\s+/g,"");
      setText(val)
    }
  }
  const onCancel=()=>{
    setVisible(false);
    setNewRule([]);
    setOldRule([]);
  }
  return(
    <Modal
      title={'对比规则'}
      visible={visible}
      width={1200}
      onOk={onCancel}
      onCancel={onCancel}
      bodyStyle={{
        height:'72vh'
      }}
    >
      <React.Fragment>
        <TextArea rows={3} onBlur={txtChange}/>
        <div className={styles.page}>
          <div>
            <p style={{fontWeight:'bold'}}>上年度规则</p>
            {
              oldRule.map((val,index)=><p key={index}>{val}</p>)
            }
          </div>
          <div>
            <p style={{fontWeight:'bold'}}>本年度规则</p>
            {
              newRule.map((item,index)=><p key={index} style={{color:`${item['color']}`}}>{item['value']}</p>)
            }
          </div>
        </div>
      </React.Fragment>

    </Modal>
  )
}

export default React.forwardRef(index)
