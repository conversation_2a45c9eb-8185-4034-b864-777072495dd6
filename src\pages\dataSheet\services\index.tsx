import request from '@/utils/request';
import qs from 'qs';
export async function getOrgReportList(params) {
  return request('/api/orgreport/getOrgReportList', {
    method: 'POST',
    body: params
  });
}
export async function updateOrgReport(params) {
  return request('/api/orgreport/updateOrgReport', {
    method: 'POST',
    body: params
  });
}

export async function saveOrgReport(params) {
  return request('/api/orgreport/saveOrgReport', {
    method: 'POST',
    body: params
  });
}

export async function publishOrgReport(params) {
  return request('/api/orgreport/publishOrgReport', {
    method: 'POST',
    body: params
  });
}
