import React, { useState, useEffect, useRef } from 'react';
import { Mo<PERSON>, Bread<PERSON>rumb, Button, Empty } from 'antd';

 const LazyBook = (props) => {
    const [isVisible, setIsVisible] = useState(false);
    const bookRef = useRef(null);

    useEffect(() => {
        console.log(bookRef.current,'ppppppppppppp')
        const observer = new IntersectionObserver(
            ([entry]) => {
                console.log(entry,'entryentryentry')
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    setTimeout(() => {
                        observer.disconnect(); // 延迟停止观察
                    }, 100); 
                }
            },
            { root: null, rootMargin: '0px', threshold: 0.1 }
        );

        if (bookRef.current) {
            observer.observe(bookRef.current);
        }
        // setIsVisible(true);
        return () => {
            if (bookRef.current) {
                observer.unobserve(bookRef.current);
            }
        };
    }, []);

    return (
        <div ref={bookRef}>
            {isVisible ? (
                <React.Fragment>
                    {props.children}
                </React.Fragment>
            ) : (
                <div
                    style={{
                        width: 600,
                        height: 600,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                >
                    <Empty />
                </div>
            )}
        </div>
    );
};

export default LazyBook