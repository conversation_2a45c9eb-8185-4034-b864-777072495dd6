import React, { Fragment, useState, useRef, useEffect } from 'react';
import { Button, Input, Divider, Popconfirm, Tabs, message, Modal, Descriptions } from 'antd';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import WhiteSpace from '@/components/WhiteSpace';
import ListTable from 'src/components/ListTable';
import NowOrg from '@/components/NowOrg';
import { getSession } from '@/utils/session';
import { rightsGetList, rightsSave, rightsDel, mdfind } from '../services/org';
import OrgSelect from '@/components/OrgSelect';
import _isEmpty from 'lodash/isEmpty'
import ExportInfo from '@/components/Export/index';
import RuiFilter from 'src/components/RuiFilter';
import { connect } from 'dva';
import Tip from '@/components/Tip';
const Search = Input.Search;
const TabPane = Tabs.TabPane;

const index = (props: any) => {
  const newAddRef: any = useRef();
  const [timeKey, setTimeKey] = useState(+new Date())
  const [pagination, setPagination] = useState<any>({ pageSize: 20, current: 1, total: 0 });
  const [listLoading, setListLoading] = useState(false);
  const [listLoading1, setListLoading1] = useState(false);
  const [mdLoading, setMdLoading] = useState(false);
  const [list, setList] = useState<any>([]);
  const org = getSession('org') || {};
  const downloadRef: any = useRef();
  const [search, setSearch] = useState<any>(undefined);
  const [ruiFilter, setRuiFilter] = useState<any>({});
  const [loading, setLoading] = useState<any>(false);
  const [visible, setVisible] = useState<any>(false)
  const [mdinfo, setMdinfo] = useState<any>(false)
  const subordinate = getSession('subordinate') || '0';
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 58,
      render: (text, record, index) => {
        return (pagination.current - 1) * pagination.pageSize + index + 1;
      },
    },
    {
      title: '组织名称',
      dataIndex: 'name',
      width: 270,
    },
    {
      title: '组织类别',
      width: 200,
      dataIndex: 'd01Name',
    },
    {
      title: '隶属关系',
      width: 160,
      dataIndex: 'd03Name',
    },
    {
      title: '联系人',
      width: 100,
      dataIndex: 'contacter',
    },
    {
      title: '联系方式',
      width: 110,
      dataIndex: 'contactPhone',
    },
    {
      title: '党组织书记',
      width: 100,
      dataIndex: 'secretary',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
      render: (text, record) => {
        return (
          <span>
            <a onClick={() => getMdList(record)}>查看</a>
            <Divider type="vertical" />
            <Popconfirm title="确定要撤销吗？" onConfirm={async () => {
               setListLoading(true);
              const { code = 500 } = await rightsDel({ code: record.code });
              setListLoading(false);
              if (code == 0) {
                Tip.success('操作提示', '操作成功');
                getLists({ pageNum: 1 });
              }
            }}>
              <a>撤销</a>
            </Popconfirm>
          </span>
        );
      },
    },
  ];

  const getLists = async (p = {}) => {
    setListLoading(true);
    const {
      code = 500,
      data: {
        list = [],
        records = [],
        pageNumber: current = 1,
        pageSize = 20,
        totalRow: total = 0,
      } = {},
    } = await rightsGetList({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
        orgCode: org['orgCode'],
        orgName: search,
        ...ruiFilter,
        ...p,
      },
    });
    setListLoading(false);
    if (code === 0) {
      setList(list);
      setPagination({ current, total, pageSize });
    }
  };
  const getMdList = async (item) => {
    const res = await mdfind({ orgCode: item.code })
    if (res.code == 0) {
      setVisible(true)
      setMdinfo(res.data)
    }

  }
  const filterChange = (val) => {
    getLists({ pageNum: 1, ...val });
    setRuiFilter(val);
  }
  const handleOk = () => { handleCancel() }
  const handleCancel = () => {
    setVisible(false)
  }
  useEffect(() => {
    if (org['orgCode']) {
      getLists();
    }
  }, [org['orgCode'], subordinate]);


  return (
    <Fragment>
      <Tabs defaultActiveKey="1">
        <TabPane tab="基本信息" key="1" />
      </Tabs>
      <NowOrg
        extra={
          <Fragment>
            <OrgSelect key={timeKey} orgTypeList={["1"]} multiple={true} onChange={async (data) => {
              if (!_isEmpty(data)) {
                let codeList = data.map((it: any) => it?.code);
                const { code = 500 } = await rightsSave({
                  data: {
                    codeList
                  }
                });
                if (code == 0) {
                  Tip.success('操作提示', '操作成功');
                  getLists({ pageNum: 1 });
                }
              }
              setTimeKey(+new Date());
            }}
            >
              <Button type="primary" style={{ marginLeft: 16 }}> 新增 </Button>
            </OrgSelect>
            <Search
              style={{ width: 200, marginLeft: 16 }}
              placeholder={'请输入检索关键词'}
              onChange={(e)=>{
                if(!e.target.value) {
                  setSearch('');
                  getLists({ orgName: '', pageNum: 1 });
                }
              }}
              onSearch={(value) => {
                setSearch(value);
                getLists({ orgName: value, pageNum: 1 });
              }}
            />
          </Fragment>
        }
      />
      {/* <RuiFilter data={filterData}
                   onChange={filterChange}
                   openCloseChange={() => {
                   }}/> */}
      <WhiteSpace />
      <WhiteSpace />
      <ListTable
        scroll={{
          x: columns.reduce((total: any, it: any) => {
            return total + it.width;
          }, 80),
        }}
        
        columns={columns}
        data={list}
        pagination={pagination}
        onPageChange={(page, pageSize) => {
          getLists({ pageNum: page, pageSize });
        }}
      />
      <Modal
        title={"详情"}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={1000}
        className='add_member_modal'
        maskClosable={false}
      >
        <Descriptions column={2}>
          <Descriptions.Item label="上级党组织名称">{mdinfo?.parentOrgName}</Descriptions.Item>
          <Descriptions.Item label="组织名称">{mdinfo?.orgName}</Descriptions.Item>
          <Descriptions.Item label="组织联系人">{mdinfo?.contacter}</Descriptions.Item>
          <Descriptions.Item label="联系电话">{mdinfo?.contactPhone}</Descriptions.Item>
          <Descriptions.Item label="中组部唯一编码">
            {mdinfo?.d01001}
          </Descriptions.Item>
          <Descriptions.Item label="通讯地址">{mdinfo?.postAddress}</Descriptions.Item>
        </Descriptions>
      </Modal>
    </Fragment>
  );
};
// @ts-ignore
export default connect(({ commonDict }) => ({ commonDict }))(index);

