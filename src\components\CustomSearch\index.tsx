import React, { Fragment, useState, useEffect, useImperativeHandle, useRef } from 'react'
import { Button, Modal } from 'antd';
import { PlusSquareOutlined } from '@ant-design/icons';
import WhiteSpace from '@/components/WhiteSpace';
import ListTable from '@/components/ListTable';
import styles from './index.less';
import _cloneDeep from 'lodash/cloneDeep';
import { connect } from 'dva';

const operatorList = [
  {
    label: '不等于',
    rule: '!=',
    value: 'notEqual',
    type: ['NUMBER', 'VARCHAR2', 'DATE', 'BOOLEAN'],
  },
  {
    label: '等于',
    rule: '=',
    value: 'eq',
    type: ['NUMBER', 'VARCHAR2', 'DATE', 'BOOLEAN'],
  },
  {
    label: '小于',
    rule: '<',
    value: 'LessThan',
    type: ['NUMBER'],
  },
  {
    label: '大于',
    rule: '>',
    value: 'more',
    type: ['NUMBER'],
  },
  {
    label: '小于等于',
    rule: '<=',
    value: 'LessThanEq',
    type: ['NUMBER'],
  },
  {
    label: '大于等于',
    rule: '>=',
    value: 'moreEq',
    type: ['NUMBER'],
  },
  {
    label: '包含(值)',
    rule: '包含',
    value: 'contain',
    type: ['CODE'],
  },
  {
    label: '不包含(值)',
    rule: '不包含',
    value: 'notContain',
    type: ['CODE'],
  },
];
const Structure = connect(({ commonDict }: any) => ({ commonDict }), undefined, undefined, { forwardRef: true })(React.forwardRef((props: any, ref) => {
  const { showDesc = true, renderTableCol } = props;

  const [list, setList]: Array<any> = useState([{ _key: +new Date() }]);
  const [desc, setMsg]: any = useState([]);
  const [timeKey, setTimeKey] = useState(+new Date());

  useEffect(() => {
    setList(props.list && props.list.length > 0 ? props.list : [{ _key: +new Date() }]);
    getDesc(props.list && props.list.length > 0 ? props.list : [{ _key: +new Date() }]);
  }, [JSON.stringify(props.list)]);

  useImperativeHandle(ref, () => ({
    getInfo: () => {
      return { list, desc };
    },
  }));

  const valChange = async (val, key, index) => {
    let data = [...list];
    data[index][key] = val;
    setList(data);
    getDesc(data);
  }
  const getDesc = (data?) => {
    props.getDesc && props.getDesc(data, setMsg);
  }
  const del = async (_key) => {
    let data = list.filter(obj => obj._key !== _key);
    getDesc(data);
    setList([...data]);
  };
  const add = async () => {
    setList([...list, { _key: +new Date() }])
  };
  const valRest = (val, key, index) => {
    let data = [...list];
    data[index][key] = val;
    setList(data);
  }
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 80,
      align: 'center',
      render: (text, record, index) => {
        return index + 1;
      },
    },
    ...renderTableCol({ valRest, valChange, operatorList, del, setList, list })
  ]
  return (
    <Fragment>
      {
        showDesc && <React.Fragment>
          <div style={{ minHeight: 180, border: '1px solid #d9d9d9', padding: '12px' }}>
            {
              desc.map((msg, index) => {
                return (
                  <p key={index} style={{ margin: 'unset' }}>
                    {index + 1}.{msg}
                  </p>
                )
              })
            }
          </div>
          <WhiteSpace />
          <WhiteSpace />
        </React.Fragment>
      }
      <div className={styles.table} onBlur={() => getDesc(list)}>
        <ListTable columns={columns} data={list} pagination={false} />
        <Button style={{ marginTop: 10, height: 43 }} type="dashed" onClick={add} block>
          <PlusSquareOutlined />
          增加条件
        </Button>
      </div>
    </Fragment>
  )
}));
const SearchModal = (props: any, ref) => {
  const [visible, setVisible] = useState(false);
  const searchRef: any = useRef();
  const onCancel = () => {
    setVisible(false);
  }
  const onOk = () => {
    let infos = searchRef.current.getInfo();
    props.onOk && props.onOk(infos);
    setVisible(false);
  }
  useImperativeHandle(ref, () => ({
    open: () => {
      setVisible(true);
      // setEdit(val);
      // if(val['colId']){
      //   request(`/api/table/tableColSecect?${qs.stringify({
      //     id:val['colId'],
      //     val:val['key']
      //   })}`).then(res=>{
      //     if(res['code']=='0'){
      //       let data:any=[];
      //       for(let obj of res['data']){
      //         data.push(JSON.parse(obj['data']))
      //       }
      //       setList(data);
      //     }
      //   });
      // }
    },
  }));
  return (
    <Fragment>
      <Modal
        title={'条件配置'}
        visible={visible}
        onCancel={onCancel}
        onOk={onOk}
        width={1360}
        // destroyOnClose={true}
        bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
      >
        <Structure ref={searchRef} renderTableCol={props.renderTableCol} {...props} />
      </Modal>
    </Fragment>
  )
}
export { Structure };
export default React.forwardRef(SearchModal);
