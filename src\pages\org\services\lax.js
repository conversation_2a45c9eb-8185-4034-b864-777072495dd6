import request from "@/utils/request";
import qs from 'qs';

// 获取涣散组织列表
export function getListLax(params) {
    return request('/api/org/slack/getList', {
        method: 'POST',
        body: params,
    });
}
// 删除涣散组织
export function delSlackOrg(para) {
    return request(`/api/org/slack/delSlackOrg?${qs.stringify(para)}`, {
        method: 'get',
    });
}

// 新增涣散组织
export function addSlackOrg(params) {
    return request('/api/org/slack/addSlackOrg', {
        method: 'POST',
        body: params,
    });
}

// 编辑涣散组织
export function updateSlackOrg(params) {
    return request('/api/org/slack/updateSlackOrg', {
        method: 'POST',
        body: params,
    });
}

// 根据code查找组织
export function findByCode(para) {
    return request(`/api/org/slack/findByCode?${qs.stringify(para)}`, {
        method: 'get',
    });
}

export function saveSlackRectificationList(para) {
  return request(`/api/org/slack/rectification/saveSlackRectificationList`, {
    method: 'POST',
    body:para
  });
}
export function slackGetList(para) {
  return request(`/api/org/slack/rectification/getList?${qs.stringify(para)}`, {
    method: 'get',
  });
}
