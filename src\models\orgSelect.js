import modelExtend from 'dva-model-extend';
import {listPageModel} from 'src/utils/common-model';
import {getSelectorList} from 'src/pages/org/services/org';
import { changeListPayQuery } from '@/utils/method.js';
const org = modelExtend(listPageModel,{
  namespace: "orgSelect",
  state:{
    basicInfo:{},
    basicInfoData:{},
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        // const { pathname, query } = location;
      });
    }
  },
  effects: {
    // 查找人员列表
    *getList({ payload }, { call, put }) {
      const {data={}} = yield call(getSelectorList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        }
      })
    },
  }
});
export default org;
