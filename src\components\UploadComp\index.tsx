import React, { Fragment } from 'react';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Upload } from 'antd';
import Tip from '@/components/Tip';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import { fileDownloadHeader, uuid } from '@/utils/method';

interface Interface {
  init?: Array<any>;
  onChange?: (any) => void;
  btnStyle?: 'btn' | 'a' | 'text';
  ohterHeaders?: any;
}

export const fitFileUrlForForm = (fileList: any, returnKey = 'url') => {
  if (!_isEmpty(fileList)) {
    fileList = fileList
      .map((it) => {
        const { response = {} } = it || {};
        if (!_isEmpty(response)) {
          const { code = 500, data = [] } = response;
          if (code === 0) {
            return data.map((i) => i[returnKey]).toString();
          }
        } else {
          return it[returnKey];
        }
      })
      .toString();
    return fileList;
  } else {
    return '';
  }
};
export const getInitFileList = (files, filesName?, fileIds?) => {
  // 文件改成ttp://*************:9000//314f4a5230344e4d4647344a52364e38//image//XXX.jpg 这样的外网直接访问地址了，不再用拼接api的内网地址
  if (!_isEmpty(files)) {
    let final: any = [];
    let ids = !_isEmpty(fileIds) ? fileIds.split(',') : [];
    files.split(',').forEach((it, index) => {
      // console.log('it===',it);

      let _name = [];
      let _name1 = it.split('\\') || [];
      let _name2 = it.split('/') || [];
      _name = _name1.length > _name2.length ? _name1 : _name2;
      let finalUrl = _name.join('/');
      if (it.startsWith('http')) {
        finalUrl = it;
      }
      final = [
        ...final,
        {
          name: filesName || [..._name].pop(),
          url: finalUrl,
          uid: uuid(),
          id: ids?.[index],
        },
      ];
    });
    // console.log('final===',final);

    return final;
  } else {
    return [];
  }
};
export default class index extends React.Component<Interface & any, any> {
  constructor(props) {
    super(props);
    this.state = {
      files: [],
      fileList: [],
      _fileList: [],
    };
  }

  static getDerivedStateFromProps(nextProps: any, prevState: any) {
    const { _fileList = [] } = prevState;
    const { files = [], onChange } = nextProps;

    let new_files = files.map((it) => it.url || '');
    let new_fileList = _fileList.map((it) => it.url || '');

    if (JSON.stringify(new_fileList) !== JSON.stringify(new_files)) {
      onChange && onChange(files);
      return { fileList: files, _fileList: files };
    }
    return null;
  }

  fileChange = ({ fileList, file, event }: any) => {
    const { onChange, children } = this.props;
    if (file.status === 'done') {
      const { response: { code = 500, message = '' } = {} } = file || {};
      if (code !== 0) {
        Tip.error('操作提示', message);
        fileList.pop();
      } else {
        Tip.success('操作提示', '上传成功');
      }
    } else if (file.status === 'error') {
      Tip.error('操作提示', '上传失败');
    }
    this.setState({
      fileList: fileList,
    });
    onChange && onChange(fileList, file);
  };

  render(): React.ReactNode {
    const {
      onChange,
      children,
      maxLen,
      files = [],
      listType = '',
      onPreview,
      buttonText = '上传文件',
      showUploadList = true,
      btnStyle = 'btn',
      ohterHeaders = {},
      beforeUpload = null, // 限制上传文件的格式
      fileDownFunc,
    } = this.props;
    const { fileList = [] } = this.state;
    const props: any = {
      action: '/api/base/upload',
      headers: {
        Authorization: sessionStorage.getItem('token') || '',
        dataApi: sessionStorage.getItem('dataApi') || '',
        ...ohterHeaders,
      },
      ...this.props,
    };
    return (
      <Fragment>
        <Upload
          {...props}
          key={'1'}
          onChange={this.fileChange}
          fileList={fileList}
          showUploadList={showUploadList}
          beforeUpload={beforeUpload}
          onPreview={(...e) => {
            if (onPreview) {
              onPreview(e);
            } else {
              if (_get(e, '[0].url', undefined)) {
                let imgUrl = _get(e, '[0].url', '');
                const { name } = e[0];
                let func = fileDownFunc ? fileDownFunc : fileDownloadHeader;
                if (imgUrl.startsWith('http')) {
                  func(`${_get(e, '[0].url')}`, name, ohterHeaders);
                } else {
                  func(`/api${_get(e, '[0].url')}`, name, ohterHeaders);
                }
                // fileDownload(`/api${_get(e, '[0].url')}`, name);
              }
            }
          }}
        >
          {showUploadList ? (
            fileList.length == maxLen ? null : children ? (
              children
            ) : listType == 'picture-card' ? (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>{buttonText}</div>
              </div>
            ) : (
              <Button>
                <UploadOutlined /> {buttonText}
              </Button>
            )
          ) : (
            <Fragment>
              {btnStyle == 'btn' && (
                <Button>
                  <UploadOutlined /> {buttonText}
                </Button>
              )}
              {btnStyle == 'a' && <a>{buttonText}</a>}
              {btnStyle == 'text' && buttonText}
            </Fragment>
          )}
        </Upload>
      </Fragment>
    );
  }
}
