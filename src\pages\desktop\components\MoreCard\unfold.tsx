import React, { useEffect, useImperativeHandle, useRef, useState, Fragment } from 'react';
import style from './unfold.less';
import { Modal, Spin, Button } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import ListTable from '@/components/ListTable';
import _get from 'lodash/get';
import { getIndexData, getIndexDataPegging, getIndexDataCountPegging, getIndexDataPeggingList, exportIndexDataCountPegging ,exportData} from '../../services';
import moment from 'moment';


const RebackModal = React.forwardRef((props, ref) => {
  const {
  } = props;
  const org = JSON.parse(sessionStorage.getItem('org') || '{}');
  const [query, setQurey] = useState<any>({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 20, total: 0, current: 1 });
  const [columns, setColumns] = useState<any>([])
  const [downloadLoading, setDownloadLoading] = useState(false)

  const dycolumns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 100,
    },
    {
      title: '性别',
      dataIndex: 'sexName',
      width: 100,
    },
    {
      title: '党员类型',
      width: 120,
      dataIndex: 'd08Name',
    },
    {
      title: '评议结果',
      width: 120,
      dataIndex: 'result',
    },
    {
      title: '所在组织',
      width: 260,
      dataIndex: 'orgName',
    },
  ]
  const dwcolumns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '单位名称',
      dataIndex: 'name',
    },
    {
      title: '单位类别',
      dataIndex: 'd04Name',
      width: 100,
    },
    {
      title: '隶属关系',
      dataIndex: 'd35Name',
      width: 100,
    },
    {
      title: '关联组织',
      dataIndex: 'mainOrgName',
    },
    {
      title: '是否法人单位',
      dataIndex: 'isLegal',
      width: 60,
      render: (text) => {
        return text == '1' ? '是' : '否'
      }
    },
    {
      title: '单位所在党组织',
      dataIndex: 'createOrgCodeName',
    },
  ]
  const dzcolumns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '党组名称',
      dataIndex: 'partyName',
    },
    {
      title: '党组类别',
      dataIndex: 'd108Name',
      width: 100,
    },
    {
      title: '关联单位',
      dataIndex: 'unitName',
    },
    {
      title: '创建时间',
      dataIndex: 'buildeTime',
      width: 100,
      render: (text, record) => {
        if (text) {
          return moment(text).format('YYYY-MM-DD')
        }
      },
    },
  ]
  const zzcolumns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '组织名称',
      dataIndex: 'name',
    },
    {
      title: '组织类别',
      dataIndex: 'd01Name',
      width: 100,
    },
    {
      title: '联系人',
      dataIndex: 'contacter',
      width: 100,
    },
    {
      title: '联系方式',
      dataIndex: 'contactPhone',
      width: 100,
    },
    {
      title: '党组织书记',
      dataIndex: 'secretary',
      width: 100,
    },
  ]
  const csqcolumns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '人员名称',
      dataIndex: 'memName',
    },
    {
      title: '性别',
      dataIndex: 'sexName',
    },
    {
      title: '身份证',
      dataIndex: 'memIdcard',
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          // let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z\(\)\[\]]{4})/, "$1***********$2"); //增加港澳台身份证马赛克
          if (text.indexOf("*") > 0) {
            return text
          }
          return (
            <span>{newVal}</span>
          );
        } else {
          return ''
        }
      }
    },
    {
      title: '学历',
      dataIndex: 'd07Name',
    },
    {
      title: '人员类别',
      dataIndex: 'type',
    },
    {
      title: '出生日期',
      dataIndex: 'birthday',
      width: 100,
      render: (text, record) => {
        if (text) {
          return moment(text).format('YYYY-MM-DD')
        }
      },
    },
    {
      title: '关联单位',
      dataIndex: 'unitName',
    },
  ]
  const unit_all = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '单位名称',
      dataIndex: 'name',
    },
    {
      title: '单位类别',
      dataIndex: 'd04Name',
      width: 100,
    },
    {
      title: '隶属关系',
      dataIndex: 'd35Name',
      width: 80,
    },
    {
      title: '关联组织',
      dataIndex: 'mainOrgName',
    },
    {
      title: '是否法人单位',
      dataIndex: 'isLegal',
      width: 60,
      render: (text) => {
        return text == '1' ? '是' : '否'
      }
    },
    {
      title: '单位所在党组织',
      dataIndex: 'createOrgCodeName',
    },
    {
      title: '年经营性收入（万元）',
      dataIndex: 'yearAmount',
      width: 120,
    },
  ]
  const mem_report = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '人员名称',
      dataIndex: 'name',
    },
    {
      title: '性别',
      dataIndex: 'sexName',
      width: 80,
    },
    {
      title: '学历',
      dataIndex: 'd07Name',
      width: 80,
    },
    {
      title: '行政职务',
      dataIndex: 'd25Name',

    },
    {
      title: '党内职务',
      dataIndex: 'd022Name',
    },
    {
      title: '出生日期',
      dataIndex: 'birthday',
      width: 100,
      render: (text, record) => {
        if (text) {
          return moment(text).format('YYYY-MM-DD')
        }
      },
    },
    {
      title: '所在组织',
      dataIndex: 'orgName',
    },
    {
      title: '人员单位',
      dataIndex: 'unitName',
    },
  ]
  const rdsqr = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 100,
    },
    {
      title: '性别',
      dataIndex: 'sexName',
      width: 100,
    },
    {
      title: '公民身份证',
      dataIndex: 'idcard',
      width: 160,
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          // let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z\(\)\[\]]{4})/, "$1***********$2"); //增加港澳台身份证马赛克
          if (text.indexOf("*") > 0) {
            return text
          }
          return (
            <span>{newVal}</span>
          );
        } else {
          return ''
        }
      }
    },
    {
      title: '电话',
      width: 100,
      dataIndex: 'phone',
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          let newVal = text.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
          if (text.indexOf("*") > 0) {
            return text
          }
          return (
            <span>{newVal}</span>
          );
        } else {
          return ''
        }
      }
    },
    {
      title: '党员类型',
      width: 120,
      dataIndex: 'd08Name',
    },
    {
      title: '所在组织',
      width: 260,
      dataIndex: 'orgName',
    },
  ]
  const hycom = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '组织名称',
      width: 200,
      dataIndex: 'orgName',
    },
    {
      title: '行业分类',
      width: 200,
      dataIndex: 'industryClassification',
    },
    {
      title: '隶属关系',
      width: 100,
      dataIndex: 'membershipFunction',
      align: 'center',
    },
  ]
  const ddb = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '人员姓名',
      dataIndex: 'memName',
      width: 140,
    },
    {
      title: '性别',
      dataIndex: 'sexName',
      width: 100,
    },
    {
      title: '身份证',
      dataIndex: 'memIdcard',
      width: 200,
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
          if (text.indexOf("*") > 0) {
            return text
          }
          return (
            <span>{newVal}</span>
          );
        } else {
          return ''
        }
      }
    },
    {
      title: '学历情况',
      dataIndex: 'd07Name',
      width: 200,
    },
    {
      title: '人员身份',
      dataIndex: 'd106Name',
      width: 200,
    },
    {
      title: '组织',
      dataIndex: 'orgName',
      width: 280,
    },
    {
      title: '党代表类型',
      dataIndex: 'party',
      width: 100,
    },
  ]
  const ldcolumns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return ((pagination['pageNum'] - 1) * pagination['pageSize']) + index + 1;
      },
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 100,
    },
    {
      title: '性别',
      dataIndex: 'sexName',
      width: 100,
    },
    {
      title: '公民身份证',
      dataIndex: 'idcard',
      width: 160,
      render: (text, record) => {
        if (typeof text === 'string' && !_isEmpty(text)) {
          // let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
          let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z\(\)\[\]]{4})/, "$1***********$2"); //增加港澳台身份证马赛克
          if (text.indexOf("*") > 0) {
            return text
          }
          return (
            <span>{newVal}</span>
          );
        } else {
          return ''
        }
      }
    },
    {
      title: '所在组织',
      width: 260,
      dataIndex: 'orgName',
    },
  ]

  const handleOk = () => {
    setIsModalVisible(false);
  };
  const handleCancel = () => {
    setIsModalVisible(false);
    setColumns([]);
    setLoading(false);
    setList([]);
    setQurey({});
    setPagination({ pageNum: 1, pageSize: 10, total: 0, current: 1 });
  };

  const getList = async (p = {}) => {

    const { callBackFunc, index = '', ...other } = query;
    let changeorg = {
      orgCode: org?.code,
      orgLevelCode: org?.orgCode,
    }
    if (index.startsWith('A') || index.startsWith('B') || index.startsWith('C')) {
      changeorg = {
        orgCode: org?.orgCode,
        orgLevelCode: org?.code,
      }
    }
    let params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...changeorg,
      name: query.value,
      ...other,
      ...p,
      index
    }
    console.log("🚀 ~ getList ~ params:", params)
    setLoading(true);
    if (!callBackFunc) return;
    const { code = 500, data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0, dateType = '' } = {} } = await callBackFunc?.({
      data: {
        ...params,
      }
    });
    if (dateType) {
      switch (dateType) {
        case 'unit':
          setColumns(dwcolumns);
          break;
        case 'org_party':
          setColumns(dzcolumns);
          break;
        case 'org':
          setColumns(zzcolumns);
          break;
        case 'unit_countryside':
          setColumns(csqcolumns);
          break;
        case 'unit_all':
          setColumns(unit_all);
          break;
        case 'mem_report':
          setColumns(mem_report);
          break;

      }
    } else {
      let a1 = ['入党申请人', '入党积极分子', '发展对象', '本年发展党员', '本年内参加民主评议的党员', '本年内受纪律处分党员']
      let a4 = ['流入党员', '流出党员']
      let a2 = ['党委', '党总支', '党支部', '行业党组织', '行业党组织', '本年内开展民主评议的支部', '在任党组织书记']
      let a3 = ['乡镇(法人)', '城市社区(法人)', '乡镇社区(法人)', '行政村(法人)',]
      // if(index.startsWith('A')) {
      //   setColumns(rdsqr);
      // }
      // if(index.startsWith('B')) {
      //   setColumns(zzcolumns);
      // }

      // if(index.startsWith('C')) {
      //   setColumns(unit_all);
      // }
      if (a1.includes(query.value)) {
        setColumns(rdsqr);
      }
      if (a2.includes(query.value)) {
        setColumns(zzcolumns);
      }
      if (a3.includes(query.value)) {
        setColumns(dwcolumns);
      }
      if (a4.includes(query.value)) {
        setColumns(ldcolumns);
      }
      if (query.value == '行业党组织') {
        setColumns(hycom);
      }
      if (query.value == '党代表') {
        setColumns(ddb);
      }
      if (query.value == '本年内党员参加培训人次') {
        setColumns([...zzcolumns, {
          title: '总培训党员人次',
          dataIndex: 'trainTotal',
        },]);
      }
      if (query.value == '本年内参加民主评议的党员') {
        setColumns([...rdsqr, {
          title: '评议结果',
          width: 100,
          dataIndex: 'result',
        },]);
      }
      if (query.value == '本年内受纪律处分党员') {
        setColumns([...rdsqr,
          {
            title: '受奖惩名称',
            width: 100,
            dataIndex: 'd029Name',
          },
          {
            title: '受奖惩原因',
            width: 140,
            dataIndex: 'd030Name',
          },
        ]);
      }
    }

    setLoading(false);
    if (code == 0) {
      setList(list);
      setPagination({ pageNum, pageSize, total, current: pageNum })
    }
  };
  const exp=async()=>{
    setDownloadLoading(true);
    let action = exportIndexDataCountPegging
    const { callBackFunc, index = '', ...other } = query;
    let changeorg = {
      orgCode: org?.code,
      orgLevelCode: org?.orgCode,
    }
    if (index.startsWith('A') || index.startsWith('B') || index.startsWith('C')) {
      changeorg = {
        orgCode: org?.orgCode,
        orgLevelCode: org?.code,
      }
      action = exportData
    }
    let params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...changeorg,
      name: query.value,
      ...other,
      index
    }
    console.log(params,'paramsparamsparamsparamsparams')
    const { code = 500, data: { url = '' } = {} } = await action({
      data: {
       ...params
      }
    });
    setDownloadLoading(false);
  }
  useEffect(() => {
    getList({ pageNum: 1 });
  }, [query.index])

  useImperativeHandle(ref, () => ({
    open: (query) => {
      if (query.callBackFunc) {
        setQurey(query);
        setIsModalVisible(true);
      }
    },
    clear: () => {
      // clear();
    },
  }));
  return (
    <Modal
      title="反查"
      visible={isModalVisible}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={false}
      footer={false}
      width={1000}>
      <div style={{ marginBottom: 10 }}>
        <Button type={'primary'} loading={downloadLoading} onClick={exp}>导出</Button>
      </div>
      <ListTable columns={columns} data={list} scroll={{ y: 600 }}
       
        pagination={pagination} onPageChange={(pageNum, pageSize) => getList({ pageNum, pageSize })} />
    </Modal>
  );
});

const ListMain = React.forwardRef((props: any, ref) => {
  const color = [
    '#A4CAFF', '#F9B3B1', '#F6CD83', '#70D4BB', '#85D6F2', '#8B008B'
  ]

  const modalRef: any = useRef();
  const org = JSON.parse(sessionStorage.getItem('org') || '{}')
  const [list, setList] = useState([]);

  const getList = async (p = {}) => {
    const { code, data } = await getIndexData({ data: { orgCode: org.orgCode, ...p } })
    if (code == 0) {
      let arr: any = []
      for (let o in data) {
        arr.push([...data[o]])
      }
      arr = arr.map((it, index) => {
        let callBackFunc = getIndexDataPegging;
        let fxshow = false
        if (index >= 3 && index <= 5) {
          callBackFunc = getIndexDataCountPegging;
          fxshow = true
        } else {
          callBackFunc = getIndexDataPeggingList
          fxshow = true
        }

        return it.map((its, ind) => {
          if (index == 0) {
            its['index'] = 'A' + ind
          }
          if (index == 1) {
            its['index'] = 'B' + ind
          }
          if (index == 2) {
            its['index'] = 'C' + ind
          }
          return {
            ...its,
            callBackFunc,
            fxshow,
          }
        })
      })
      console.log(arr, 'arr')
      setList(arr);
    }
  }
  useImperativeHandle(ref, () => ({
    getList: async (query) => {
      await getList(query);
    },
  }));
  // 获取当前数据最小宽度
  let minWidth: any = 30;
  if (!_isEmpty(list)) {
    list.forEach((it: any, k) => {
      const [head, ...ohter] = it || [];
      let _width = ohter.length <= 0 ? minWidth : 100 / (ohter.length % 4 > 0 ? (ohter.length - ohter.length % 4) / 4 + 1 : ohter.length / 4);
      minWidth = minWidth > _width ? _width : minWidth;
    });
  }
  return (
    <Fragment>
      {
        !_isEmpty(list) && list.map((it: any, k) => {
          const [head, ...ohter] = it || [];
          return (
            <div className={style.unfoldBody} style={{ borderBottom: list.length - 1 == k ? '' : '1px solid #E9E9E9' }}>
              <div className={style.bodyLeft} style={{ borderRight: `2px solid ${color[k]}` }}>
                <div>{head?.count}{head?.suffix || ''}</div>
                <div>{head?.value}</div>
              </div>
              <div className={style.bodyRight}>
                {ohter.map((item, index) => {
                  if (index % 4 === 0) {
                    return (
                      <div className={style.cont} style={{ width: `${minWidth}%` }}>
                        <div className={style.content}>
                          <span>{ohter[index + 0]?.value}</span>
                          {
                            ohter[index + 0]?.fxshow ?
                              <a style={{ color: '#49C2ED' }} onClick={() => {
                                modalRef.current.open(ohter[index + 0]);
                              }}><span>{!_isEmpty(ohter[index + 0]) && `${ohter[index + 0]?.count}${ohter[index + 0]?.suffix || ''}`}</span></a>
                              :
                              <span>{!_isEmpty(ohter[index + 0]) && `${ohter[index + 0]?.count}${ohter[index + 0]?.suffix || ''}`}</span>
                          }

                        </div>
                        <div className={style.content}>
                          <span>{ohter[index + 1]?.value}</span>
                          {
                            ohter[index + 1]?.fxshow ?
                              <a style={{ color: '#49C2ED' }} onClick={() => {
                                modalRef.current.open(ohter[index + 1]);
                              }}><span>{!_isEmpty(ohter[index + 1]) && `${ohter[index + 1]?.count}${ohter[index + 1]?.suffix || ''}`}</span></a>
                              :
                              <span>{!_isEmpty(ohter[index + 1]) && `${ohter[index + 1]?.count}${ohter[index + 1]?.suffix || ''}`}</span>
                          }
                        </div>
                        <div className={style.content}>
                          <span>{ohter[index + 2]?.value}</span>
                          {
                            ohter[index + 2]?.fxshow && ohter[index + 2]?.value != '本年内基层党委开展培训期数' ?
                              <a style={{ color: '#49C2ED' }} onClick={() => {
                                modalRef.current.open(ohter[index + 2]);
                              }}><span>{!_isEmpty(ohter[index + 2]) && `${ohter[index + 2]?.count}${ohter[index + 2]?.suffix || ''}`}</span></a>
                              :
                              <span>{!_isEmpty(ohter[index + 2]) && `${ohter[index + 2]?.count}${ohter[index + 2]?.suffix || ''}`}</span>
                          }
                        </div>
                        <div className={style.content}>
                          <span>{ohter[index + 3]?.value}</span>
                          {
                            ohter[index + 3]?.fxshow && ohter[index + 3]?.value != '本年内基层党委开展培训人次' ?
                              <a style={{ color: '#49C2ED' }} onClick={() => {
                                modalRef.current.open(ohter[index + 3]);
                              }}><span>{!_isEmpty(ohter[index + 3]) && `${ohter[index + 3]?.count}${ohter[index + 3]?.suffix || ''}`}</span></a>
                              :
                              <span>{!_isEmpty(ohter[index + 3]) && `${ohter[index + 3]?.count}${ohter[index + 3]?.suffix || ''}`}</span>
                          }
                        </div>
                      </div>
                    )
                  }
                })}
              </div>
            </div>
          )
        })
      }
      <RebackModal ref={modalRef} />
    </Fragment>
  )
});
const index = (props, ref) => {
  const {
    orgCode = undefined
  } = props;
  const _ref: any = useRef();
  const org = JSON.parse(sessionStorage.getItem('org') || '{}')
  useImperativeHandle(ref, () => ({
    open: query => {
      setIsModalVisible(true);
    },
    clear: () => {
      // clear();
    },
  }));
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleOk = () => {
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };
  const sendUrl = async () => {
    setLoading(true);
    await _ref.current.getList({ orgCode: orgCode });
    setLoading(false);
  }
  useEffect(() => {
    if (orgCode) {
      sendUrl();
    }
  }, [orgCode]);
  return (
    <Modal
      title=""
      forceRender
      visible={isModalVisible}
      onOk={handleOk}
      onCancel={handleCancel}
      footer={false}
      width={1400}
      style={{ margin: 'auto' }}
      closable={false}>
      <Spin spinning={loading}>
        <div style={{ height: '810px', overflow: 'auto' }}>
          <div style={{ width: '100%', textAlign: 'right' }}>
            <img style={{ width: 26, cursor: 'pointer' }} src={require('@/assets/desktop/close.png')} onClick={handleCancel} />
          </div>
          <ListMain ref={_ref} />
        </div>
      </Spin>
    </Modal>
  );
};
export default React.forwardRef(index);
export { ListMain };

