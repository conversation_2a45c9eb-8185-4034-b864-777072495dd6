import React from 'react';
import ReactEchartsCore from "echarts-for-react/lib/core";
import echarts from "echarts";
import "echarts/lib/component/tooltip";
import "echarts/lib/chart/bar";
import "echarts/lib/chart/line";
import "echarts/lib/chart/gauge";
import "echarts/lib/chart/pie";



interface Interface {
  option:object,
  style?:any,
}
export default class index extends React.Component<Interface,any> {
  constructor(props) {
    super(props);

  }

  render() {
    const {option, style} = this.props;

    return (
      <div>
        <ReactEchartsCore
          style={style}
          ref={e=>this['ech']=e}
          echarts={echarts}
          option={option}
          notMerge={true}
          lazyUpdate={true}
          theme={'light'}
        />
      </div>
    );
  }
}
