import request from 'src/utils/request';
import qs from 'qs';


// 党员档案管理列表
export function getList(params) {
  console.log("🚀 ~ getList ~ params:", params)
  return request('/api/zunyi/digital/mem/list', {
    method: 'POST',
    body: params,
  });
}
// 发展档案管理列表
export function getDevList(params) {
  return request('/api/zunyi/digital/developMem/list', {
    method: 'POST',
    body: params,
  });
}

export function determine(params) {
  console.log("🚀 ~ determine ~ params:", qs.stringify(params))

  return request(`/api/zunyi/digital/sure?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

export function getMemDevelopAuditList(params) {
  return request('/api/zunyi/process/getMemDevelopAuditList', {
    method: 'POST',
    body: params,
  });
}
export function getMemAuditList(params) {
  return request('/api/zunyi/process/getMemAuditList', {
    method: 'POST',
    body: params,
  });
}

export function auditMemDevelop(params) {
  return request('/api/zunyi/process/auditMemDevelop', {
    method: 'POST',
    body: params,
  });
}
export function auditMem(params) {
  return request('/api/zunyi/process/auditMem', {
    method: 'POST',
    body: params,
  });
}

export function digitalCountList(params) {
  return request(`/api/zunyi/digital/digitalCountList?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

export function digitalCountListDetail(params) {
  return request('/api/zunyi/digital/digitalCountListDetail', {
    method: 'POST',
    body: params,
  });
}

export function expdigitalCountList(params) {
  return request(`/api/zunyi/digital/export/digitalCountList?${qs.stringify(params)}`, {
    method: 'POST',
  }, 'file');
}
export function expdigitalCountListDetail(params) {
  return request('/api/zunyi/digital/export/digitalCountListDetail', {
    method: 'POST',
    body: params,
  }, 'file');
}

export function comprehensiveList(params) {
  return request(`/api/zunyi/digital/comprehensiveList?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

export function exportDigitalData(params) {
  return request('/api/zunyi/digital/exportDigitalData', {
    method: 'POST',
    body: params,
  });
}
export function exportDigitalLogs(params) {
  return request('/api/zunyi/digital/exportDigitalLogs', {
    method: 'POST',
    body: params,
  });
}
export function digitalauditlist(params) {
  return request('/api/zunyi/digitalaudit/list', {
    method: 'POST',
    body: params,
  });
}
export function digitalauditapply(params) {
  return request('/api/zunyi/digitalaudit/apply', {
    method: 'POST',
    body: params,
  });
}
export function digitalauditsave(params) {
  return request('/api/zunyi/digitalaudit/save', {
    method: 'POST',
    body: params,
  });
}

export function isArchived(params) {
  return request(`/api/zunyi/mem/new/isArchived?${qs.stringify(params)}`, {
    method: 'Get',
  });
}

