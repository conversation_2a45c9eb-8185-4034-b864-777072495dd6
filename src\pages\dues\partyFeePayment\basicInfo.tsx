/**
 * 党费交纳-基本信息
 * */
import React, { useState, useRef, useEffect } from 'react';
import {
  Input,
  Select,
  Form,
  Modal,
  Tabs,
  Button,
  Divider,
  Popconfirm,
  Space,
  Dropdown,
  Menu,
} from 'antd';
import { FormOutlined, DownOutlined } from '@ant-design/icons';
import moment from 'moment';
import ListTable from 'src/components/ListTable';
import { getSession } from '@/utils/session';
import Tip from '@/components/Tip';
import NowOrg from '@/components/NowOrg';
import Standard from './components/standard';
import StandardBatch from './components/standardBatch';
import Pay from './components/pay';
import PayBatch from './components/payBatch';
import SetTime from './components/setTime';
import SetTimeBatch from './components/payDate';
import { paymentList } from '../services';
import UploadComp from '@/components/UploadComp';
import _last from 'lodash/last';
import { changeMsgTip, fileDownloadHeader, hookSetListHeight } from '@/utils/method';
import {
  importExcelStand,
  importExcelDude,
  templateDues,
  templateStand,
} from '@/pages/dues/services';

const TabPane = Tabs.TabPane;
const Search = Input.Search;
const index = (props: any) => {
  const { code = undefined, orgCode = undefined, subordinate = undefined } = getSession('org') || {
    code: undefined,
    orgCode: undefined,
    subordinate: undefined,
  };
  const standardRef = useRef<any>();
  const standardBatchRef = useRef<any>();
  const payRef = useRef<any>();
  const payBatchRef = useRef<any>();
  const setTimeRef = useRef<any>();
  const SetTimeBatchRef = useRef<any>();
  const [listLoading, setListLoading] = useState(false);
  const [listData, setListData]: any = useState([]);
  const [pagination, setPagination] = useState({ pageSize: 20, current: 1, total: 0 });
  const [year, setYear]: any = useState(2023);
  const [memName, setMemName]: any = useState(undefined);
  const [filterHeight, setFilterHeight] = useState(`100px`);
  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      fixed: 'left',
      width: 60,
      render: (text, record, index) => {
        return (pagination.current - 1) * pagination.pageSize + index + 1;
      },
    },
    {
      title: '姓名',
      dataIndex: 'memName',
      align: 'center',
      fixed: 'left',
      width: 80,
    },
    {
      title: '所属组织',
      dataIndex: 'orgName',
      align: 'left',
      fixed: 'left',
      width: 150,
    },
    {
      title: '一月',
      dataIndex: 'activityName1',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 1),
    },
    {
      title: '二月',
      dataIndex: 'activityName2',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 2),
    },
    {
      title: '三月',
      dataIndex: 'activityName3',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 3),
    },
    {
      title: '四月',
      dataIndex: 'activityName4',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 4),
    },
    {
      title: '五月',
      dataIndex: 'activityName5',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 5),
    },
    {
      title: '六月',
      dataIndex: 'activityName6',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 6),
    },
    {
      title: '七月',
      dataIndex: 'activityName7',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 7),
    },
    {
      title: '八月',
      dataIndex: 'activityName8',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 8),
    },
    {
      title: '九月',
      dataIndex: 'activityName9',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 9),
    },
    {
      title: '十月',
      dataIndex: 'activityName10',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 10),
    },
    {
      title: '十一月',
      dataIndex: 'activityName11',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 11),
    },
    {
      title: '十二月',
      dataIndex: 'activityName12',
      align: 'center',
      width: 200,
      render: (text, record, index) => getMonthItem(record, 12),
    },
    {
      title: '起交时间设置',
      dataIndex: 'lastPayDate',
      align: 'center',
      fixed: 'right',
      width: 120,
      render: (text, record) => {
        if (text) {
          return (
            <Popconfirm
              title="修改为原先起交之前的时间，列表上原来的设置保留，增加可设置的月份；修改为原先起交之后的时间，那么会直接移除之前的设置。"
              okText="修改"
              onConfirm={() => {
                setTimeRef.current.open(record);
              }}
            >
              <a
                style={{ color: '#faad14', whiteSpace: 'nowrap' }}
              // onClick={() => {
              //   setTimeRef.current.open(record);
              // }}
              >
                {moment(text).format('YYYY-MM-DD')} &nbsp;
                <FormOutlined />
              </a>
            </Popconfirm>
          );
        } else {
          return (
            <a
              // style={{ color: '#52c41a', whiteSpace: 'nowrap' }}
              style={{ whiteSpace: 'nowrap' }}
              onClick={() => {
                setTimeRef.current.open(record);
              }}
            >
              请设置起交时间
            </a>
          );
        }
      },
    },
  ];
  const getMonthItem = (record, m) => {
    if (record?.data && record?.lastPayDate) {
      let obj = record.data.find((item) => item?.month === m);
      if (m > moment(record.lastPayDate).month()) {
        return (
          <div style={{ textAlign: 'left', width: 'fit-content', margin: '0 auto' }}>
            <div>
              {obj?.standard && obj?.d49Code != '4' ? (
                <a
                  style={{ color: '#faad14', whiteSpace: 'nowrap' }}
                  onClick={() => {
                    standardRef.current.open(record, m, year);
                  }}
                >
                  党费标准：{obj?.standard}（元）
                </a>
              ) : (
                <a
                  style={{ color: '#1890ff', whiteSpace: 'nowrap' }}
                  onClick={() => {
                    standardRef.current.open(record, m, year);
                  }}
                >
                  党费标准：{obj?.d49Code == '4' ? '免交' : '未设置'}
                </a>
              )}
            </div>
            <div style={{ marginTop: 10 }}></div>
            <div>
              {obj?.payMoney && obj?.d49Code != '4' ? (
                <a
                  style={{ whiteSpace: 'nowrap', color: '#52c41a' }}
                  onClick={() => {
                    payRef.current.open(record, m, year);
                  }}
                >
                  交费金额：{obj?.payMoney}（元）
                </a>
              ) : (
                <React.Fragment>
                  {obj?.d49Code == '4' ? (
                    <span style={{ whiteSpace: 'nowrap', color: '#52c41a' }}>交费金额：免交</span>
                  ) : (
                    <a
                      style={{ whiteSpace: 'nowrap' }}
                      onClick={() => {
                        if (obj?.standard) {
                          payRef.current.open(record, m, year);
                        } else {
                          Tip.error('操作提示', '请先设置党费标准再交费');
                        }
                      }}
                    >
                      交费金额：未交纳
                    </a>
                  )}
                </React.Fragment>
              )}
            </div>
            <div style={{ marginTop: 10 }}></div>
            {obj?.standard && obj?.payMoney ? (
              <div>交费时间：{moment(obj?.payDate).format('YYYY年MM月DD日')}</div>
            ) : (
              <div>交费时间：无</div>
            )}
          </div>
        );
      }
    }
  };
  const getList = async (p?: any) => {
    const {
      code: resCode = 500,
      data: { list = [], pageNumber = 1, pageSize = 10, totalRow = 0 } = {},
    } = await paymentList({
      data: {
        // orgCode,
        pageNum: 1,
        pageSize: 10,
        orgOrgCode: orgCode,
        memName,
        year,
        ...p,
      },
    });
    if (resCode === 0) {
      setListData(list);
      setPagination({ current: pageNumber, total: totalRow, pageSize });
    }
  };
  useEffect(() => {
    if (code) {
      getList();
    }
    hookSetListHeight(setFilterHeight, 20);
  }, [code, subordinate]);

  return (
    <div>
      <NowOrg
        extra={
          <Space>
            {/* <Button
              type="primary"
              onClick={() => {
                standardBatchRef.current.open();
              }}
            >
              批量设置党费标准
            </Button> */}
            {/* <Button
              type="primary"
              onClick={() => {
                payBatchRef.current.open();
              }}
            >
              批量添加交费
            </Button> */}
            {/* <Button
              type="primary"
              onClick={() => {
                SetTimeBatchRef.current.open();
              }}
            >
              批量设置起交时间
            </Button> */}

            <Dropdown
              overlay={
                <Menu>
                  <Menu.Item>
                    <a
                      onClick={async () => {
                        const res = await templateStand({ data: { orgCode } });
                        if (res.code == 0) {
                          Tip.success('操作提示', '正在下载...');
                          let name = _last(res?.data?.url?.split('/') || ['']);
                          let dataApi = sessionStorage.getItem('dataApi') || '';
                          if (name) {
                            fileDownloadHeader('/api' + res.data.url, '党费标准模板' + name, {
                              dataApi,
                            });
                          }
                        }
                      }}
                    >
                      党费标准模板
                    </a>
                  </Menu.Item>
                  <Menu.Item>
                    <a
                      onClick={async () => {
                        const res = await templateDues({ data: { orgCode } });
                        if (res.code == 0) {
                          Tip.success('操作提示', '正在下载...');
                          let dataApi = sessionStorage.getItem('dataApi') || '';
                          let name = _last(res?.data?.url?.split('/') || ['']);
                          if (name) {
                            fileDownloadHeader('/api' + res.data.url, '党费交纳模板' + name, {
                              dataApi,
                            });
                          }
                        }
                      }}
                    >
                      党费交纳模板
                    </a>
                  </Menu.Item>
                </Menu>
              }
            >
              <Button onClick={async () => { }}>
                模板导出 <DownOutlined />
              </Button>
            </Dropdown>

            <Dropdown
              overlay={
                <Menu>
                  {/* <Menu.Item>
                    <UploadComp
                      btnStyle={'text'}
                      buttonText={'党费标准导入'}
                      maxLen={1}
                      showUploadList={false}
                      onChange={async (fileList, file) => {
                        const { response: { code = 500, data = [] } = {} } = file || {};
                        if (code === 0) {
                          const { url = '' } = data[0] || {};
                          const { code: code2 = 500 } = await importExcelStand({
                            data: {
                              excelFile: url,
                              orgCode: orgCode,
                            },
                          });
                          if (code2 == 0) {
                            Tip.success('操作提示', '导入成功，正在请求最新数据');
                            getList({ pageNum: 1 });
                          }
                        }
                      }}
                    />
                  </Menu.Item>
                  <Menu.Item>
                    <UploadComp
                      btnStyle={'text'}
                      buttonText={'党费交纳导入'}
                      maxLen={1}
                      showUploadList={false}
                      onChange={async (fileList, file) => {
                        const { response: { code = 500, data = [] } = {} } = file || {};
                        if (code === 0) {
                          const { url = '' } = data[0] || {};
                          const { code: code2 = 500 } = await importExcelDude({
                            data: {
                              excelFile: url,
                              orgCode: orgCode,
                            },
                          });
                          if (code2 == 0) {
                            Tip.success('操作提示', '导入成功，正在请求最新数据');
                            getList({ pageNum: 1 });
                          }
                        }
                      }}
                    />
                  </Menu.Item> */}
                </Menu>
              }
            >
              <Button>
                模板导入 <DownOutlined />
              </Button>
            </Dropdown>

            {/* <Select
              style={{ width: '90px' }}
              defaultValue={2023}
              onChange={(e: any) => {
                if (e === 'all') {
                  setYear(undefined);
                  getList({ year: undefined, pageNum: 1 });
                } else {
                  setYear(e);
                  getList({ year: e, pageNum: 1 });
                }
              }}
            >
              <Select.Option value={2018}>2018&nbsp;&nbsp;</Select.Option>
              <Select.Option value={2019}>2019&nbsp;&nbsp;</Select.Option>
              <Select.Option value={2020}>2020&nbsp;&nbsp;</Select.Option>
              <Select.Option value={2021}>2021&nbsp;&nbsp;</Select.Option>
              <Select.Option value={2022}>2022&nbsp;&nbsp;</Select.Option>
              <Select.Option value={2023}>2023&nbsp;&nbsp;</Select.Option>
              <Select.Option value={'all'}>全部&nbsp;&nbsp;</Select.Option>
            </Select> */}

            <Search
              placeholder="请输入姓名查找"
              onSearch={(e) => {
                setMemName(e || undefined);
                getList({
                  pageNum: 1,
                  memName: e || undefined,
                });
              }}
            />
          </Space>
        }
      />
      <ListTable
        rowKey={'memCode'}
        
        columns={columns}
        data={listData}
        pagination={pagination}
        scroll={{ x: 1500, y: filterHeight }}
        onPageChange={(page, pageSize) => {
          getList({ pageNum: page, pageSize });
        }}
      />
      <Standard
        ref={standardRef}
        onOk={() => {
          getList({ pageNum: pagination.current });
        }}
      />
      <StandardBatch
        ref={standardBatchRef}
        onOk={() => {
          getList({ pageNum: 1 });
        }}
      />
      <Pay
        ref={payRef}
        onOk={() => {
          getList({ pageNum: pagination.current });
        }}
      />
      <PayBatch
        ref={payBatchRef}
        onOk={() => {
          getList({ pageNum: 1 });
        }}
      />
      <SetTime
        ref={setTimeRef}
        onOk={() => {
          getList({ pageNum: pagination.current });
        }}
      />
      <SetTimeBatch
        ref={SetTimeBatchRef}
        onOk={() => {
          getList({ pageNum: 1 });
        }}
      />
    </div>
  );
};

export default index;
