// 流动人员详情-仅作展示信息使用，不可编辑基本信息
import React, { Fragment } from 'react';
import { connect } from 'dva';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import _isObject from 'lodash/isObject';
import { Form } from '@ant-design/compatible';
import { Input, Modal, InputNumber, Button, Select, Popconfirm, Divider, Row, Col } from 'antd';
import moment from 'moment';
import { getSession } from '@/utils/session';
import { formLabel, findDictCodeName } from '@/utils/method';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import DictArea from '@/components/DictArea';
import Tip from '@/components/Tip';
import SearchOrg from '@/components/SearchOrg';
import Date from '@/components/Date';
import OrgSelect from '@/components/OrgSelect';
import ListTable from '@/components/ListTable';
import MemSelect from '@/components/MemSelect';
import ListEdit from './flowAddOrEditListEdit';
import {
  inManageOperate,
  inManageFind,
  outManageFind,
  outManageReFlow,
  outManageSomeEdit,
  outManageDetailList,
  outManageDetailDel,
  findUniqueCode
} from '../../service/index';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

// @ts-ignore
@connect(
  ({ unit, commonDict, loading, flowMem }) => ({
    flowMem,
    unit,
    commonDict,
    loading: loading.effects['unit/getList'],
  }),
  undefined,
  undefined,
  { forwardRef: true },
)
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      paginationHd: { pageSize: 10, current: 1, total: 0 },
      loadingHd: false,
      visibleHd: false,
      basicInfoHd: {},
      flowUqCode: undefined
    };
  }

  open = (type: string, record?: any, urlType?: string) => {
    // console.log('modalType===', type);
    let title = '党员信息查看';
    if (`${type}`.startsWith('readOnly-')) {
      title = '流动党员信息查看';
    }
    if (type === 'edit-outTab1') {
      title = '未纳入流入地党员信息管理';
    }
    if (type === 'edit-outTab3') {
      title = '重新流出';
    }
    if (type === 'edit-inTab2') {
      title = '已纳入支部流动党员信息管理';
    }
    if (type === 'edit-outTab2') {
      title = '已纳入流入地管理党员信息管理';
    }

    this.setState(
      {
        readOnly: true,
        modalType: type,
        modalTitle: title,
        visible: true,
        urlType: urlType ? urlType : undefined,
      },
      () => {
        if (!_isEmpty(record['code'])) {
          this.getBasicInfo(record['code']);
        }
      },
    );
  };
  getBasicInfo = async (code: string) => {
    const { urlType } = this.state;
    let url: any = inManageFind;
    if (urlType === 'outFlow') {
      url = outManageFind;
    }
    const { code: rescode = 500, data = {} } = await url({
      code,
    });
    if (rescode == 0) {
      this.setState({
        basicInfo: data,
        showInUnitD16Code: `${data?.inOrgD04Code}`.startsWith('4') ? true : false,
      }, () => {
        // 	流动类型是跨省的时候  判断是否有唯一码 没有就去请求 有就直接复制
        if (data?.flowTypeCode == 1) {
          if (!data?.flowUqCode) {
            this.getUniqueCode(data?.code)
          } else {
            this.setState({
              flowUqCode: data?.flowUqCode
            })
          }
        }
      });
      if (
        `${data?.inMemD09Code}`.startsWith('02') ||
        `${data?.inMemD09Code}`.startsWith('03') ||
        `${data?.inMemD09Code}`.startsWith('505') ||
        `${data?.inMemD09Code}` == '504'
      ) {
        this.setState({
          showInMemD20Code: true,
        });
      } else {
        this.setState({
          showInMemD20Code: false,
        });
      }
      this.getListHd({ memFlowCode: data.code, pageNum: 1 });
    }
  };
  handleOk = async () => {
    const { onOk } = this.props;
    const { basicInfo, modalType } = this.state; // modalType说明：例如modalType:edit-outTab1→   列表"编辑"操作-流出管理下的tab1选项卡（未纳入流入地管理）
    const org: any = getSession('org');
    this.props.form.validateFieldsAndScroll(async (error, values) => {
      if (error) {
        return;
      }
      if (modalType === 'edit-inTab2') {
        if (!_isEmpty(values['inOrgLifeCode'])) {
          if (typeof values['inOrgLifeCode'] === 'object') {
            values['inOrgLifeName'] = values['inOrgLifeCode'].name || undefined;
            values['inOrgLifeCode'] = values['inOrgLifeCode'].key || undefined;
          } else {
            values['inOrgLifeName'] =
              values['inOrgLifeName'] || basicInfo['inOrgLifeName'] || undefined;
          }
        }
      }
      if (modalType === 'edit-outTab3' || modalType === 'edit-outTab1') {
        if (!_isEmpty(values['outTime'])) {
          values['outTime'] = moment(values['outTime']).valueOf();
        }
        if (!_isEmpty(values['registerTime'])) {
          values['registerTime'] = moment(values['registerTime']).valueOf();
        }
      }
      const {
        mzAppraisal,
        inOrgLifeCode,
        inOrgLifeName,
        inOrgLife,
        inFeedback,
        flowReasonCode,
        outTime,
        registerTime,
        inOrgD04Code,
        isHold,
        outOrgRemarks,
        partyExpensesOutTime,
        pairedContact,
        pairedContactPhone
      } = values;
      let someData = {};
      console.log('111111111111111111111111', pairedContact);
      // 流入管理-已纳入支部管理-管理  需要传的参数
      if (modalType === 'edit-inTab2') {
        someData = {
          code: basicInfo.code,
          mzAppraisal,
          inOrgLifeCode,
          inOrgLifeName,
          inOrgLife,
          inFeedback,
          registerTime
        };
      }
      if (!_isEmpty(values['pairedContact'])) {
        values['pairedContactCode'] = values['pairedContact'][0].code || undefined;
        values['pairedContact'] = values['pairedContact'][0].name || undefined;
      }
      // 流出管理-流出被退回-重新流出  需要传的参数
      if (modalType === 'edit-outTab3') {
        someData = {
          code: basicInfo.code,
          flowReasonCode,
          outTime,
          registerTime,
          inOrgD04Code: _isObject(inOrgD04Code) ? inOrgD04Code['key'] : inOrgD04Code,
          inOrgD04Name: _isObject(inOrgD04Code) ? inOrgD04Code['name'] : basicInfo['inOrgD04Name'],
          isHold,
          partyExpensesOutTime: moment(partyExpensesOutTime).valueOf() || undefined,
        };
      }
      // 流出管理-未纳入流入地-管理  需要传的参数
      if (modalType === 'edit-outTab1') {
        someData = {
          code: basicInfo.code,
          outOrgRemarks,
          flowReasonCode,
          outTime,
          registerTime,
          isHold,
          pairedContactPhone,
          pairedContactCode: pairedContact[0]?.code,
          pairedContact: pairedContact[0]?.name
        };
      }
      let url: any = undefined;
      if (modalType === 'edit-inTab2') {
        url = inManageOperate;
      }
      if (modalType === 'edit-outTab3') {
        url = outManageReFlow;
      }
      if (modalType === 'edit-outTab1') {
        url = outManageSomeEdit;
      }

      // console.log('data===', someData);
      // return;
      if (url) {
        this.setState(
          {
            confirmLoading: true,
          },
          async () => {
            const { code = 500 } = await url({
              data: {
                ...someData,
              },
            });
            this.setState({
              confirmLoading: false,
            });
            if (code == 0) {
              Tip.success('操作提示', '操作成功');
              this.handleCancel();
              onOk && onOk();
            }
          },
        );
      }
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      modalTitle: '',
      memData: [],
      basicInfo: {},
      outPlaceCode: '',
      flowTypeCode: undefined,
      orgSearchType: '', // 组织查询选择器返回的type
      readOnly: false,
      confirmLoading: false,
      showInMemD20Code: false,
    });
  };
  memChange = (data) => {
    this.setState({
      memData: data,
    });
  };
  del = (item) => {
    const { getFieldValue, setFieldsValue } = this.props.form;
    let { memData } = this.state;
    // let memData =  getFieldValue('codeList');
    memData = memData.filter((obj) => obj['id'] !== item['id']);
    setFieldsValue({ codeList: memData });
    this.setState({
      memData,
    });
    let names: Array<string> = [];
    for (let obj of memData) {
      names.push(obj['name']);
    }
    this['mem'].setState({
      value: names.join('，'),
      data: memData,
    });
  };
  getTime = (rule, value, callback) => {
    if (!value) {
      callback('外出时间必填');
    }
    let now = moment().subtract(180, 'days').format('YYYY-MM-DD');
    let time = moment(value).format('YYYY-MM-DD');
    if (!moment(time).isBefore(now)) {
      callback('外出日期只能填写早于当前日期180天');
    } else {
      callback();
    }
    callback();
  };
  getListHd = async (p: any) => {
    this.setState({
      loadingHd: true,
    });
    const {
      code: rescode = 500,
      data: { list = [], pageNumber = 1, pageSize = 10, totalPage = 0 } = {},
    } = await outManageDetailList({
      data: {
        pageNum: this.state.paginationHd['current'],
        pageSize: this.state.paginationHd['pageSize'],
        memFlowCode: p.memFlowCode,
        ...p,
      },
    });
    this.setState({
      loadingHd: false,
    });
    if (rescode == 0) {
      this.setState({
        listHd: list,
        paginationHd: {
          ...this.state.paginationHd,
          pageSize: pageSize,
          current: pageNumber,
          total: totalPage,
        },
      });
    }
  };
  delHd = async (item) => {
    const { code = 500, data = {} } = await outManageDetailDel({
      data: {
        id: item['id'],
      },
    });
    if (code == 0) {
      Tip.success('操作提示', '删除成功');
      this.getListHd({ memFlowCode: this.state.basicInfo['code'] });
    }
  };

  getUniqueCode = async (memCode) => {
    const { data, code } = await findUniqueCode({
      flowCode: memCode
    })
    if (data) {
      this.setState({
        flowUqCode: data
      })
    }
  }
  render() {
    const { children, tipMsg = {}, commonDict } = this.props;
    const {
      visible,
      modalTitle = '',
      modalType = '',
      basicInfo = {},
      outPlaceCode = '',
      orgSearchType = '',
      flowTypeCode = '',
      readOnly = false,
      confirmLoading = false,
      showInUnitD16Code = false,
      showInMemD20Code = false,
      itemsDisabled = [],
      listHd = [],
      loadingHd = false,
      paginationHd = { pageSize: 10, current: 1, total: 0 },
      flowUqCode
    } = this.state;
    // console.log(modalType, 'modalTypemodalTypemodalType');
    const { getFieldDecorator, setFieldsValue, getFieldValue } = this.props.form;
    const org: object = getSession('org') || {};
    const noOperationColumns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return (paginationHd['current'] - 1) * paginationHd['pageSize'] + index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'memName',
        align: 'center',
        width: 60,
      },
      {
        title: '性别',
        dataIndex: 'memSexName',
        align: 'center',
        width: 60,
      },
      {
        title: '身份证号码',
        dataIndex: 'memIdcard',
        align: 'center',
        width: 100,
      },
      {
        title: '联系电话',
        dataIndex: 'memPhone',
        align: 'center',
        width: 100,
      },
      {
        title: '所在组织',
        dataIndex: 'memOrgName',
        width: 160,
      },
      {
        title: '工作岗位',
        dataIndex: 'memD09Name',
        width: 80,
      },
    ];
    const columns = [
      ...noOperationColumns,
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return (
            <span>
              <a className={'del'} onClick={() => this.del(record)}>
                删除
              </a>
            </span>
          );
        },
      },
    ];
    const columnsHd = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '活动名称',
        dataIndex: 'eventName',
        align: 'center',
        width: 200,
      },
      {
        title: '活动日期',
        dataIndex: 'eventDate',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '填写组织名称',
        dataIndex: 'fillerOrgName',
        align: 'center',
        width: 200,
      },
      {
        title: '维护日期',
        dataIndex: 'maintainDate',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 120,
        render: (text, record, index) => {
          return (
            <span>
              <a
                onClick={() => {
                  this['listEditRef'].open({ type: 'readonly', record: record });
                }}
              >
                查看
              </a>
              <Divider type="vertical" />
              <a
                onClick={() => {
                  this['listEditRef'].open({ type: 'edit', record: record });
                }}
              >
                修改
              </a>
              <Divider type="vertical" />
              <Popconfirm title="确定要删除吗？" onConfirm={() => this.delHd(record)}>
                <a className={'del'}>删除</a>
              </Popconfirm>
            </span>
          );
        },
      },
    ];
    return (
      <div>
        {children
          ? React.cloneElement(children as any, {
            onClick: this.open,
            key: 'container',
          })
          : null}
        <Modal
          footer={
            // modalType === 'edit-inTab2' ||
            modalType === 'edit-outTab1' || modalType === 'edit-outTab3' ? (
              <Fragment>
                <Button
                  onClick={() => {
                    this.handleCancel();
                  }}
                >
                  取消
                </Button>
                <Button
                  loading={confirmLoading}
                  onClick={() => {
                    this.handleOk();
                  }}
                  type="primary"
                >
                  确定
                </Button>
              </Fragment>
            ) : null
          }
          bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
          destroyOnClose
          // maskClosable={false}
          width={1100}
          title={modalTitle}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
        >
          <Row>
            <h3>党员信息</h3>
            <ListTable
              scroll={{ x: '100%' }}
              rowKey={'code'}
              columns={readOnly ? noOperationColumns : columns}
              data={[basicInfo] || []}
              pagination={false}
            />
          </Row>
          <Row style={{ marginTop: 20 }}>
            <h3 style={{ marginBottom: 10 }}>流动信息</h3>
            <Col span={24}>
              <Form {...formItemLayout}>
                {
                  modalType !== 'readOnly-inTab1' &&
                  basicInfo?.flowTypeCode == 1 && (
                    <FormItem label={formLabel('流动党员唯一码', tipMsg['flowUqCode'])}>
                      {getFieldDecorator('flowUqCode', {
                        initialValue: flowUqCode,
                        rules: [{ required: false, message: '流动党员唯一码' }],
                      })(<Input disabled />)}
                    </FormItem>
                  )}
                <FormItem label={formLabel('外出地点', tipMsg['outPlaceCode'])}>
                  {getFieldDecorator('outPlaceCode', {
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.outPlaceCode,
                    rules: [{ required: !readOnly, message: '请选择外出地点' }],
                  })(
                    <DictSelect
                      initValue={_isEmpty(basicInfo) ? undefined : basicInfo.outPlaceCode}
                      disabled={readOnly}
                      codeType={'dict_d148'}
                      onChange={(e) => {
                        let arr = [];
                        if (e == '2') {
                          arr = commonDict['dict_d151_flow']
                            .filter((i) => i.key.startsWith('520') || i.key.startsWith('522'))
                            .map((j) => j.key);
                        }
                        setFieldsValue({ flowTypeCode: undefined });
                        setFieldsValue({ outAdministrativeDivisionCode: '' });
                        this.setState({
                          outPlaceCode: e,
                          flowTypeCode: undefined,
                          itemsDisabled: arr,
                          timeKey: String(Math.random()),
                          administrativeRegion: '',
                        });
                      }}
                      itemsDisabled={['2', '4']}
                    />,
                  )}
                </FormItem>
                {(outPlaceCode == 1 || basicInfo?.outPlaceCode == 1) && (
                  <FormItem label={formLabel('流入党委', tipMsg['outOrgCode'])} colon={false}>
                    {getFieldDecorator('outOrgCode', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.outOrgName,
                      rules: [{ required: false, message: '请选择' }],
                    })(<Input disabled />)}
                  </FormItem>
                )}
                {(basicInfo.outPlaceCode == 1 || basicInfo.outPlaceCode == 2 || basicInfo.outPlaceCode == 5) && (
                  <Fragment>
                    <FormItem label={formLabel('流入党支部', tipMsg['outOrgBranchName'])}>
                      {getFieldDecorator('outOrgBranchName', {
                        initialValue: _isEmpty(basicInfo)
                          ? undefined
                          : basicInfo['outOrgBranchName'],
                        rules: [{ required: true, message: '请选择党支部' }],
                      })(<Input disabled placeholder="请输入" />)}
                    </FormItem>
                  </Fragment>
                )}
                {basicInfo.outPlaceCode != 3 && basicInfo.outPlaceCode != 4 && (
                  <FormItem
                    label={formLabel('流入行政区', tipMsg['outAdministrativeDivisionCode'])}
                  >
                    {getFieldDecorator('outAdministrativeDivisionCode', {
                      initialValue: _isEmpty(basicInfo)
                        ? undefined
                        : basicInfo['outAdministrativeDivisionCode'],
                      rules: [{ required: !readOnly, message: '请选择所在行政区域' }],
                    })(
                      <DictTreeSelect
                        parentDisable={true}
                        placeholder={'请选择行政区域'}
                        initValue={
                          _isEmpty(basicInfo) ? undefined : basicInfo.outAdministrativeDivisionCode
                        }
                        disabled={true}
                        codeType={'dict_d151_flow'}
                        itemsDisabled={itemsDisabled}
                      />,
                    )}
                  </FormItem>
                )}
                {/* 外出地点-不掌握流向 */}
                {basicInfo.outPlaceCode == 4 && (
                  <Fragment>
                    <FormItem label={formLabel('失去联系情形', tipMsg['lostContactCode'])}>
                      {getFieldDecorator('lostContactCode', {
                        initialValue: _isEmpty(basicInfo)
                          ? undefined
                          : basicInfo['lostContactCode'],
                        rules: [{ required: false, message: '请选择' }],
                      })(
                        <DictSelect
                          disabled={readOnly}
                          codeType={'dict_d18'}
                          backType={'object'}
                          initValue={_isEmpty(basicInfo) ? undefined : basicInfo['lostContactCode']}
                        />,
                      )}
                    </FormItem>
                  </Fragment>
                )}
                {basicInfo.outPlaceCode == 4 &&
                  (function () {
                    const lostContactCode = getFieldValue('lostContactCode');
                    return (
                      lostContactCode && (
                        <Fragment>
                          <FormItem label={formLabel('失去联系日期', tipMsg['lostContactTime'])}>
                            {getFieldDecorator('lostContactTime', {
                              initialValue: _isEmpty(basicInfo)
                                ? undefined
                                : basicInfo['lostContactTime'],
                              rules: [{ required: true, message: '请选择' }],
                            })(<Date disabled={readOnly} />)}
                          </FormItem>
                        </Fragment>
                      )
                    );
                  })()}
                {/* 外出地点-无固定地点 */}
                {(basicInfo?.outPlaceCode == 3 || basicInfo?.outPlaceCode == 4) && (
                  <FormItem label={formLabel('情况说明', tipMsg['outOrgRemarks'])}>
                    {getFieldDecorator('outOrgRemarks', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['outOrgRemarks'],
                      rules: [{ required: true, message: '请填写' }],
                    })(
                      <Input.TextArea
                        disabled={modalType === 'edit-outTab1' ? false : readOnly}
                        placeholder="请对情况进行详细说明"
                        showCount
                        maxLength={100}
                        rows={4}
                      />,
                    )}
                  </FormItem>
                )}
                {(basicInfo?.outPlaceCode != 3 && basicInfo?.outPlaceCode != 5) && <FormItem label={formLabel('外出原因', tipMsg['flowReasonCode'])}>
                  {getFieldDecorator('flowReasonCode', {
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['flowReasonCode'],
                    rules: [
                      {
                        required:
                          modalType === 'edit-outTab3' || modalType === 'edit-outTab1'
                            ? true
                            : !readOnly,
                        message: '请选择外出原因',
                      },
                    ],
                  })(
                    <DictSelect
                      disabled={
                        modalType === 'edit-outTab3' || modalType === 'edit-outTab1'
                          ? false
                          : readOnly
                      }
                      codeType={'dict_d204'}
                      // backType={'object'}
                      initValue={_isEmpty(basicInfo) ? undefined : basicInfo['flowReasonCode']}
                    />,
                  )}
                </FormItem>}
                <FormItem label={formLabel('外出日期', tipMsg['outTime'])}>
                  {getFieldDecorator('outTime', {
                    initialValue: _isEmpty(basicInfo) ? undefined : moment(basicInfo['outTime']),
                    rules: [
                      {
                        required:
                          modalType === 'edit-outTab3' || modalType === 'edit-outTab1'
                            ? true
                            : !readOnly,
                        message: '请填写外出日期',
                      },
                      // { validator: this.getTime }
                    ],
                  })(
                    <Date
                      disabled={
                        modalType === 'edit-outTab3' || modalType === 'edit-outTab1'
                          ? false
                          : readOnly
                      }
                    />,
                  )}
                </FormItem>
                {(basicInfo.outPlaceCode != 3 && basicInfo.outPlaceCode != 4) && <FormItem label={formLabel('登记日期', tipMsg['registerTime'])}>
                  {getFieldDecorator('registerTime', {
                    initialValue: _isEmpty(basicInfo) ? undefined : moment(basicInfo['registerTime']),
                    rules: [
                      {
                        required:
                          modalType === 'edit-outTab3' || modalType === 'edit-outTab1'
                            ? true
                            : !readOnly,
                        message: '请填写登记日期',
                      },
                      // { validator: this.getTime }
                    ],
                  })(
                    <Date
                      disabled={
                        modalType === 'edit-outTab3' || modalType === 'edit-outTab1'
                          ? false
                          : readOnly
                      }
                    />,
                  )}
                </FormItem>}
                {basicInfo?.outPlaceCode == 1 && (
                  <FormItem label={formLabel('外出地点(党组织)补充说明', tipMsg['outOrgRemarks'])}>
                    {getFieldDecorator('outOrgRemarks', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['outOrgRemarks'],
                      rules: [
                        {
                          required:
                            modalType === 'edit-outTab3' || modalType === 'edit-outTab1'
                              ? true
                              : !readOnly,
                          message: '请输入外出地点(党组织)补充说明',
                        },
                      ],
                    })(
                      <Input.TextArea
                        disabled={
                          modalType === 'edit-outTab3' || modalType === 'edit-outTab1'
                            ? false
                            : readOnly
                        }
                        placeholder="请对情况进行详细说明"
                        showCount
                        maxLength={100}
                        rows={4}
                      />,
                    )}
                  </FormItem>
                )}
                {(basicInfo.outPlaceCode == 1 || basicInfo.outPlaceCode == 2 || basicInfo.outPlaceCode == 5) && (
                  <FormItem label={formLabel('流入地党组织单位类型', tipMsg['inOrgD04Code'])}>
                    {getFieldDecorator('inOrgD04Code', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['inOrgD04Code'],
                      rules: [
                        {
                          required: modalType === 'edit-outTab3' ? true : !readOnly,
                          message: '流入地党组织单位类型',
                        },
                      ],
                    })(
                      <DictTreeSelect
                        disabled={modalType === 'edit-outTab3' ? false : readOnly}
                        onChange={(e) => {
                          const { key = '' } = e;
                          if (key.startsWith('4')) {
                            this.setState({
                              showInUnitD16Code: true,
                            });
                          } else {
                            this.setState({
                              showInUnitD16Code: false,
                            });
                          }
                        }}
                        parentDisable={true}
                        codeType={'dict_d205'}
                        backType={'object'}
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['inOrgD04Code']}
                      />,
                    )}
                  </FormItem>
                )}
                {/* 流入地党组织单位类型选择企业4开头的字典表时,添加经济类型 */}
                {/* {showInUnitD16Code && (
                  <FormItem label={formLabel('经济类型', tipMsg['inUnitD16Code'])}>
                    {getFieldDecorator('inUnitD16Code', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['inUnitD16Code'],
                      rules: [{ required: !readOnly, message: '请选择经济类型' }],
                    })(
                      <DictTreeSelect
                        disabled={readOnly}
                        parentDisable={true}
                        codeType={'dict_d16'}
                        // backType={'object'}
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['inUnitD16Code']}
                      />,
                    )}
                  </FormItem>
                )} */}
                {/* // 工作岗位以02、03、505开头的，等于504的 显示新社会阶层 */}
                {showInMemD20Code && (
                  <FormItem label="新社会阶层">
                    {getFieldDecorator('inMemD20Code', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['inMemD20Code'],
                      rules: [{ required: false, message: '请选择新社会阶层' }],
                    })(
                      <DictTreeSelect
                        disabled={true}
                        placeholder="请选择新社会阶层"
                        parentDisable={true}
                        codeType={'dict_d20'}
                        // backType={'object'}
                        initValue={_isEmpty(basicInfo) ? undefined : basicInfo['inMemD20Code']}
                      />,
                    )}
                  </FormItem>
                )}
                <FormItem label={formLabel('流出地党费交至日期', tipMsg['partyExpensesOutTime'])}>
                  {getFieldDecorator('partyExpensesOutTime', {
                    initialValue: _isEmpty(basicInfo)
                      ? undefined
                      : moment(basicInfo['partyExpensesOutTime']),
                    rules: [
                      { required: modalType === 'edit-outTab3', message: '流出地党费交至日期' },
                    ],
                  })(<Date disabled={!(modalType === 'edit-outTab3')} />)}
                </FormItem>
                <FormItem label={formLabel('流动党员活动证', tipMsg['isHold'])}>
                  {getFieldDecorator('isHold', {
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['isHold'],
                    rules: [
                      {
                        required:
                          modalType === 'edit-outTab3' || modalType === 'edit-outTab1'
                            ? true
                            : !readOnly,
                        message: '请选择',
                      },
                    ],
                  })(
                    <Select
                      disabled={
                        modalType === 'edit-outTab3' || modalType === 'edit-outTab1'
                          ? false
                          : readOnly
                      }
                      placeholder="请选择"
                      style={{ width: '100%' }}
                    >
                      <Select.Option value={'1'}>已发放</Select.Option>
                      <Select.Option value={'0'}>未发放</Select.Option>
                    </Select>,
                  )}
                </FormItem>
                <FormItem label={formLabel('流出地党支部', tipMsg['memOrgName'])}>
                  {getFieldDecorator('memOrgName', {
                    initialValue: _isEmpty(basicInfo) ? org['name'] : basicInfo.memOrgName,
                    rules: [{ required: false, message: '流出地党支部' }],
                  })(<Input disabled />)}
                </FormItem>
                <FormItem label={formLabel('流出地党支部联系电话', tipMsg['memOrgPhone'])}>
                  {getFieldDecorator('memOrgPhone', {
                    initialValue: _isEmpty(basicInfo) ? org['contactPhone'] : basicInfo.memOrgPhone,
                    rules: [{ required: false, message: '请输入流出地党支部联系电话' }],
                  })(<Input placeholder="请输入" disabled />)}
                </FormItem>
                {modalType === 'readOnly-outTab3' && (
                  <Fragment>
                    <FormItem label={formLabel('退回操作党组织', tipMsg['rejectOrgName'])}>
                      {getFieldDecorator('rejectOrgName', {
                        initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.rejectOrgName,
                        rules: [{ required: false, message: '退回操作党组织' }],
                      })(<Input disabled />)}
                    </FormItem>
                    <FormItem label={formLabel('退回操作党组织联系电话', tipMsg['rejectOrgPhone'])}>
                      {getFieldDecorator('rejectOrgPhone', {
                        initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.rejectOrgPhone,
                        rules: [{ required: false, message: '退回操作党组织联系电话' }],
                      })(<Input disabled />)}
                    </FormItem>
                    <FormItem label={formLabel('退回原因', tipMsg['rejectReasonCode'])}>
                      {getFieldDecorator('rejectReasonCode', {
                        initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.rejectReasonCode,
                        rules: [{ required: false, message: '退回原因' }],
                      })(
                        <DictSelect
                          codeType={'dict_d153'}
                          backType={'object'}
                          initValue={
                            _isEmpty(basicInfo) ? undefined : basicInfo['rejectReasonCode']
                          }
                        />,
                      )}
                    </FormItem>
                  </Fragment>
                )}
                {(modalType === 'readOnly-outTab2' ||
                  modalType === 'readOnly-outTab4' ||
                  modalType === 'readOnly-inTab2' ||
                  modalType === 'readOnly-inTab4') && (
                    <Fragment>
                      <FormItem label={formLabel('流入地党支部', tipMsg['inOrgName'])}>
                        {getFieldDecorator('lcd', {
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.inOrgName,
                          rules: [{ required: false, message: '流入地党支部' }],
                        })(<Input disabled />)}
                      </FormItem>
                      <FormItem label={formLabel('流入地党支部联系电话', tipMsg['inOrgPhone'])}>
                        {getFieldDecorator('inOrgPhone', {
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.inOrgPhone,
                          rules: [{ required: false, message: '请输入流入地党支部联系电话' }],
                        })(<Input placeholder="请输入" disabled />)}
                      </FormItem>
                    </Fragment>
                  )}
                {(modalType === 'readOnly-outTab2' ||
                  modalType === 'readOnly-outTab4' ||
                  modalType === 'readOnly-inTab2' ||
                  modalType === 'readOnly-inTab4' ||
                  modalType === 'edit-inTab2') && (
                    <Fragment>
                      <FormItem label={formLabel('参与组织生活情况', tipMsg['inOrgLifeCode'])}>
                        {getFieldDecorator('inOrgLifeCode', {
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.inOrgLifeCode,
                          rules: [{ required: true, message: '参与组织生活情况' }],
                        })(
                          <DictSelect
                            disabled={readOnly}
                            codeType={'dict_d152'}
                            backType={'object'}
                            initValue={_isEmpty(basicInfo) ? undefined : basicInfo.inOrgLifeCode}
                            onChange={(e) => {
                              console.log('e===', e);
                            }}
                          />,
                        )}
                      </FormItem>
                      <FormItem label={formLabel('', tipMsg['inOrgLife'])} colon={false}>
                        {getFieldDecorator('inOrgLife', {
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.inOrgLife,
                          rules: [{ required: true, message: '请填写' }],
                        })(
                          <Input.TextArea
                            disabled={readOnly}
                            placeholder="请描述参与组织生活情况"
                            showCount
                            maxLength={100}
                            rows={4}
                          />,
                        )}
                      </FormItem>
                      {/* <FormItem label={formLabel('参与民主评议情况', tipMsg['mzAppraisal'])}>
                    {getFieldDecorator('mzAppraisal', {
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.mzAppraisal,
                      rules: [{ required: true, message: '请选择' }],
                    })(
                      <Select
                        disabled={modalType === 'edit-inTab2' ? false : readOnly}
                        placeholder="参与民主评议情况"
                        style={{ width: '100%' }}
                      >
                        <Select.Option value={'1'}>参加流入党支部民主评议</Select.Option>
                        <Select.Option value={'0'}>未参加流入党支部民主评议</Select.Option>
                      </Select>,
                    )}
                  </FormItem> */}
                      {/* 参加民主评议情况修改为重要事反馈，并且使用代码表 */}
                      <FormItem label={formLabel('重要事反馈', tipMsg['mzAppraisal'])}>
                        {getFieldDecorator('mzAppraisal', {
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.mzAppraisal,
                          rules: [{ required: true, message: '请选择' }],
                        })(
                          <DictSelect
                            disabled={readOnly}
                            codeType={'dict_d209'}
                            // backType={'object'}
                            initValue={_isEmpty(basicInfo) ? undefined : basicInfo['mzAppraisal']}
                          />,
                        )}
                      </FormItem>

                      <FormItem label={formLabel('表现反馈', tipMsg['inFeedback'])}>
                        {getFieldDecorator('inFeedback', {
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.inFeedback,
                          rules: [{ required: true, message: '请填写' }],
                        })(
                          <Input.TextArea
                            disabled={readOnly}
                            placeholder="请输入"
                            showCount
                            maxLength={100}
                            rows={4}
                          />,
                        )}
                      </FormItem>
                    </Fragment>
                  )}
                <FormItem label={formLabel('结对联系人', tipMsg['pairedContact'])}>
                  {getFieldDecorator('pairedContact', {
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.pairedContact,
                    rules: [{ required: true, message: '请输入结对联系人' }],
                  })(
                    <Input
                      placeholder="请输入"
                      disabled={[
                        'readOnly-inTab1',
                        'readOnly-inTab2',
                        'readOnly-inTab3',
                        'readOnly-inTab4',
                        'readOnly-outTab1',
                        'readOnly-outTab2',
                        'readOnly-outTab3',
                        'readOnly-outTab4',
                        'edit-inTab2',
                        'edit-outTab2',
                      ].includes(modalType) || ['3', '4'].includes(basicInfo.outPlaceCode)}
                    />,
                    // <MemSelect
                    //   org={getSession('org')}
                    //   // initValue={memInfo['memName']}
                    //   onChange={(e: any) => {
                    //     setFieldsValue({
                    //       pairedContactPhone: e[0]?.phone,
                    //     });
                    //   }}
                    //   disabled={[
                    //     'readOnly-inTab1',
                    //     'readOnly-inTab2',
                    //     'readOnly-inTab3',
                    //     'readOnly-inTab4',
                    //     'readOnly-outTab1',
                    //     'readOnly-outTab2',
                    //     'readOnly-outTab3',
                    //     'readOnly-outTab4',
                    //     'edit-inTab2',
                    //     'edit-outTab2',
                    //   ].includes(modalType)}
                    //   ref={(e) => (this['pairedContact'] = e)}
                    //   checkType={'radio'}
                    // />
                  )}
                </FormItem>
                {
                  (() => {
                    const phone = getFieldValue('pairedContact') || [];
                    return (
                      <FormItem label={formLabel('结对联系方式', tipMsg['pairedContactPhone'])}>
                        {getFieldDecorator('pairedContactPhone', {
                          initialValue: _isEmpty(basicInfo) ? phone[0]?.phone : basicInfo.pairedContactPhone,
                          rules: [{ required: true, message: '请输入结对联系方式' }],
                        })(
                          <Input
                            placeholder="请输入"
                            disabled={true}
                          />,
                        )}
                      </FormItem>
                    )
                  })()
                }
                {/* <FormItem label={formLabel('是否为农民工', tipMsg['isFarmer'])}>
                  {getFieldDecorator('isFarmer', {
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.isFarmer,
                    rules: [{ required: true, message: '请选择是否为农民' }],
                  })(
                    <Select disabled={readOnly}>
                      <Select.Option value={'1'}>是</Select.Option>
                      <Select.Option value={'0'}>否</Select.Option>
                    </Select>
                  )}
                </FormItem> */}
              </Form>
              {/* 活动信息列表 */}
              {(modalType === 'edit-outTab2' || modalType === 'edit-inTab2') && (
                <Fragment>
                  <div style={{ margin: '0 20px' }}>
                    <Divider />
                    <div style={{ width: '100%', textAlign: 'right', marginBottom: '10px' }}>
                      <Button
                        type="primary"
                        onClick={() => {
                          this['listEditRef'].open({ type: 'add', record: {} });
                          // this.setState({
                          //   visibleHd: true,
                          //   modalTitleHd: '新增活动',
                          // });
                        }}
                      >
                        管理
                      </Button>
                    </div>
                    <ListTable
                      // scroll={{ x: '100%' }}
                      rowKey={'id'}
                      columns={columnsHd}
                      data={listHd}
                      pagination={paginationHd}
                      onPageChange={(page, size) => {
                        this.getListHd({
                          memFlowCode: basicInfo.code,
                          pageNum: page,
                          pageSize: size,
                        });
                      }}
                    />
                  </div>
                </Fragment>
              )}
            </Col>
          </Row>
        </Modal>
        <ListEdit
          wrappedComponentRef={(e) => (this['listEditRef'] = e)}
          onOk={() => {
            this.getListHd({ memFlowCode: basicInfo.code, pageNum: 1 });
          }}
          memInfo={basicInfo}
          modalType={modalType}
        />
      </div>
    );
  }
}

export default Form.create()(index);
