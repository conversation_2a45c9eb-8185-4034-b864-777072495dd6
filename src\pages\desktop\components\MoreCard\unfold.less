.unfoldBody{
  display: flex;
  height: 150px;
 padding: 10px 0;
  .bodyLeft{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 180px;
    >div:nth-child(1) {
      color: #49C2ED;
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    >div:nth-child(2) {
      font-size: 14px;
    }
  }
  .bodyRight{
    flex: 1;
    display: flex;
    flex-direction: row;
    width: 100%;
    // display: grid;
    // grid-auto-flow:column;
    // grid-template-rows: auto auto auto auto;
    // .cont {
    //   max-width: 280px;
    // }
    .content{
      padding-left: 20px;
      // width: 280px;
      width: 100%;
      height: 30px;
      display: flex;
      justify-content: space-between;
      font-family:PingFangSC-Medium;
      font-weight: bold;
      >span:nth-child(2) {
        color: #49C2ED;
      }
    }
  }
}
  :global(.ant-modal-body) {
    padding: 12px !important;
  }
