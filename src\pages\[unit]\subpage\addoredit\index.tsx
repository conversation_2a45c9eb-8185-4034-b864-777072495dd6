/**
 * 模块名
 */

import React from 'react'
import { BookOutlined, CloseOutlined, CodeOutlined, CopyOutlined, SolutionOutlined, StarOutlined, UserOutlined, BankOutlined } from '@ant-design/icons';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Avatar, Col, Menu, Modal, Row } from 'antd';
import Basic from './basic';
import Thematic from './thematic';
import Worker from './worker';
import WhiteSpace from '@/components/WhiteSpace';
import Leader from './leader';
import Faculty from './Faculty';
import Funding from './funding';
import ExtendInfo from './extendInfo/extendInfoAll';
import ExtendInfo2 from './extendInfo/extendInfo_2';
import City from './city';
import Village from './Village';
import Income from './income';
import Team from './cadresTeam';
import UnitFiscal from './unitFiscal';
import First from './firstSecretary';
import Economic from './economic'
import MemManage from '@/pages/[unit]/memManage'
import GovernmentTeam from './governmentTeam'
import Select from './select';
import { connect } from "dva";
import { _history as router, changeMsgTip } from '@/utils/method';
import { tableColConfig } from '@/services';
import { LockMsg } from '@/pages/user/lock';
import _isEmpty from 'lodash/isEmpty';


const menuData = [
  {
    code: '1',
    name: '基本信息',
    icon: <StarOutlined />,
  },
  {
    code: '2',
    name: '村（居）委成员',
    icon: <CodeOutlined />,
  },
  {
    code: '18',
    name: '政府班子成员',
    icon: <CodeOutlined />,
  },
  // {
  //   code:'3',
  //   name:'专题调查',
  //   icon:<BookOutlined/>,
  // },
  // {
  //   code:'4',
  //   name:'社区工作者',
  //   icon:<CopyOutlined/>,
  // },
  // {
  //   code:'5',
  //   name:'村后备干部',
  //   icon:<SolutionOutlined/>,
  // }
  {
    code: '6',
    name: '二级院系情况',
    icon: <SolutionOutlined />,
  },
  {
    code: '7',
    name: '经费场地情况',
    icon: <SolutionOutlined />,
  },
  {
    code: '8',
    name: '扩展信息', //街道（现在为所有
    icon: <SolutionOutlined />,
  },
  // {
  //   code:'9',
  //   name:'扩展信息', //社区、村
  //   icon:<SolutionOutlined/>,
  // },
  {
    code: '10',
    // name:'城市基层党建情况',
    name: '城市社区基层党建情况',
    icon: <BookOutlined />,
  },
  // {
  //   code:'11',
  //   name:'集体经济资产情况',
  //   icon:<BookOutlined/>,
  // },
  {
    code: '12',
    name: '乡镇干部队伍补充信息',
    icon: <BookOutlined />,
  },
  {
    code: '13',
    name: '驻村干部',
    icon: <BookOutlined />,
  },
  {
    code: '14',
    name: '单位财政扶持壮大村级集体经济情况',
    icon: <BookOutlined />,
  },
  // 单位类别d04Code为912乡镇开头时，新增小菜单：乡镇班子成员（操作模式同村社区）
  // {
  //   code:'15',
  //   name:'乡镇班子成员',
  //   icon:<CodeOutlined />,
  // },
  {
    code: '16',
    name: '集体经济情况',
    icon: <BankOutlined />,
  },
  {
    code: '17',
    name: '村(社区)工作者和后备干部情况',
    icon: <UserOutlined />,
  },
  {
    code: '19',
    // name: '乡镇基层党建情况',
    name: '乡镇社区基层党建情况',
    icon: <BookOutlined />,
  },
  {
    code: '20',
    // name: '乡镇基层党建情况',
    name: '选调生',
    icon: <BookOutlined />,
  },
  // 91开头 街道乡镇显示：政府班子成员

];
// @ts-ignore

@connect(({ unit, commonDict, loading }) => ({
  unit, commonDict,
  unitAdd: loading.effects['unit/add'],
  unitUpdate: loading.effects['unit/update'],
}))
export default class index extends React.Component<any, any> {
  static show() { };
  static close() { };
  static clear() { };
  constructor(props) {
    super(props);
    let obj = menuData[0];
    this.state = {
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    };
    index.show = this.open.bind(this);
    index.close = this.handleCancel.bind(this);
    index.clear = this.destroy.bind(this);
  }
  componentDidMount(): void {
    tableColConfig({ id: 'ccp_unit' }).then(res => {
      if (res['code'] == '0') {
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg: msg,
        });
      }
    });



  }
  handleOk = () => {
    this.handleCancel();
  };
  handleCancel = (e?) => {
    const basicLoading = this.props.unitAdd || this.props.unitUpdate;
    if (basicLoading) {
      return;
    }
    const { colseType = '' } = e;
    const { location: { search = undefined } = {} } = router;
    router.push(_isEmpty(search) || colseType === 'add' ? '?' : search)
    this.destroy();
    this.props.onClose && this.props.onClose();
  };
  open = () => {
    this.setState({
      visible: true,
    }, () => {
      setTimeout(() => {
        let a = document.getElementById('content');
        a?.addEventListener('scroll', this.onScroll)
      }, 500)
    })
  };
  destroy = () => {
    let obj = menuData[0];
    this.setState({
      visible: false,
      key: obj['code'],
      keyPath: [obj['code']],
      selected: obj,
    });
    this.props.dispatch({//重置model
      type: 'unit/updateState',
      payload: {
        basicInfo: {},
      }
    });
  };
  onSelect = async (item) => {
    let node = document.getElementById('content')
    if (node) {
      node.scrollTop = 0
    }
    this.setState({
      key: ''
    }, () => {
      const { key, keyPath } = item;
      const selected = menuData.find(obj => obj['code'] === key);
      this.setState({
        key,
        keyPath,
        selected,
        isEndModal: false
      });
    })
  };
  onScroll = (e) => {
    // 监听滚动目前只有在8扩展信息时才会使用，其他情况不监听，不setState使其渲染画面
    if (this.state.key != '8') return;
    function isScrolledToBottom(element) {
      return element.scrollHeight - element.scrollTop <= element.clientHeight + 10;
    }
    let node = document.getElementById('content');
    let a = isScrolledToBottom(node)
    this.setState({
      isEndModal: a
    })
  }
  render() {
    const { visible, selected, keyPath, key, tipMsg = {} } = this.state;
    const { basicInfo = {} } = this.props.unit;
    const { d04Code = '', d16Code = '', linkedDTOList = [] } = basicInfo || {};
    let bool = true;//菜单禁用
    if (basicInfo['code']) {
      bool = false
    }
    let _menuData: any = [...menuData];
    console.log(_menuData, '_menuData')
    if (d04Code === '921' || d04Code === '922') {
      _menuData = _menuData.filter(it => it.code !== '5');
    } else if (d04Code === '923') {
      _menuData = _menuData.filter(it => it.code !== '4');
    } else {
      _menuData = _menuData.filter(it => !(it.code === '4' || it.code === '5'));
    }
    // 单位管理中，只有村和社区的时候才展示班子成员
    if (!`${d04Code}`.startsWith('92')) {
      _menuData = _menuData.filter(it => it.code !== '2');
    }
    if (!`${d04Code}`.startsWith('331')) {
      _menuData = _menuData.filter(it => it.code !== '6');
    }
    if (
      !(d04Code == '923' ||
        d04Code === '911' ||
        d04Code === '921' ||
        d04Code === '922' ||
        d04Code.startsWith('331') ||
        d04Code.startsWith('341') ||
        (d04Code.startsWith('4') && d16Code.startsWith('1')) ||
        (d04Code.startsWith('4') && d16Code.startsWith('2')) ||
        d04Code.startsWith('5'))
    ) {
      _menuData = _menuData.filter(it => it.code !== '3');
    }
    //
    // if(
    //   !(d04Code === '911')
    // ){
    //   _menuData = _menuData.filter(it=>it.code !== '8');
    // }
    if (
      !(d04Code.startsWith('92'))
    ) {
      _menuData = _menuData.filter(it => it.code !== '9' && it.code !== '2' && it.code !== '14'); // 只有村和社区才填写 村（居）委成员
    }
    let find = linkedDTOList.find(it => (it?.orgType || '').startsWith('2'));
    if (!find || d04Code == '911') {
      _menuData = _menuData.filter(it => it.code !== '7');
    }
    if (!(d04Code === '911' || d04Code === '921')) {
      _menuData = _menuData.filter(it => it.code !== '10');
    }
    if (!(d04Code === '922')) {
      _menuData = _menuData.filter(it => it.code !== '19');
    }
    // 社区、村
    if (!(d04Code.startsWith('92'))) {
      _menuData = _menuData.filter(it => it.code !== '11' && it.code !== '16' && it.code !== '17');
    }
    if (!(d04Code.startsWith('912'))) {
      _menuData = _menuData.filter(it => it.code !== '12' && it.code !== '15');
    }
    if (!(d04Code.startsWith('92'))) {
      _menuData = _menuData.filter(it => it.code !== '13'&& it.code !== '20');
    }
    // 外部控制只展示某些menu item项
    if (!_isEmpty(this.props.menuDataKey)) {
      _menuData = _menuData.filter(it => this.props.menuDataKey.includes(it.code))
    }

    if (!`${d04Code}`.startsWith('91')) {
      _menuData = _menuData.filter(it => it.code !== '18');
    }


    // 2023年统新的扩展信息逻辑
    // 所有93-97，没有拓展信息 所有6开头的，没有扩展信息
    if (
      `${d04Code}`.startsWith('93') ||
      `${d04Code}`.startsWith('94') ||
      `${d04Code}`.startsWith('95') ||
      `${d04Code}`.startsWith('96') ||
      `${d04Code}`.startsWith('97') ||
      `${d04Code}`.startsWith('6')
    ) {
      _menuData = _menuData.filter((it) => it.code !== '8');
    }


    const basicLoading = this.props.unitAdd || this.props.unitUpdate
    return (
      <Modal
        title=""
        wrapClassName='editModal'
        destroyOnClose
        closable={false}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        width={'calc(90vw)'}
        footer={false}
      >
        <div className='container' style={{ pointerEvents: basicLoading ? 'none' : 'auto' }}>
          <div className='header'>
            <Row>
              <Col span={4} style={{ textAlign: 'center' }}>
                <Avatar style={{ backgroundColor: '#7265e6', verticalAlign: 'middle' }} size="large">
                  admin
                </Avatar>
              </Col>
              <Col span={15}><h2>{basicInfo['code'] ? <div>
                编辑单位
                {/* {key == '1' && <LockMsg basicInfo={{...basicInfo,unlockObject:'2'}}/>} */}
              </div> : '新增单位'}</h2></Col>
              <Col span={5} className={'close'}><CloseOutlined onClick={this.handleCancel} /></Col>
            </Row>
          </div>
          <div>
            <Row>
              <Col span={3} style={{ borderRight: '1px solid rgb(233, 233, 233)' }}>
                <div className='slider'>
                  <LegacyIcon type={selected['icon'] || undefined} style={{ marginRight: 8 }} />{selected['name']}
                </div>
                <Menu mode="inline" selectedKeys={keyPath} onSelect={this.onSelect}>
                  {
                    _menuData && _menuData.map((obj, index) => {
                      return (
                        <Menu.Item title={obj['name'] || ''} key={obj['code']} disabled={index > 0 ? bool : false} icon={obj['icon']}>
                          {obj['name']}
                        </Menu.Item>
                      );
                    })
                  }
                </Menu>
              </Col>
              <Col span={21} style={{ paddingTop: 20 }} id='content' className='content'>
                {
                  key === '1' && <Basic tipMsg={tipMsg} {...this.props} close={(e) => { this.handleCancel(e) }} />
                }
                {
                  key === '2' && <Leader tipMsg={tipMsg} {...this.props} />
                }
                {
                  key === '3' && <Thematic {...this.props} />
                }
                {
                  key === '4' && <Worker {...this.props} pageType={'1'} />
                }
                {
                  key === '5' && <Worker {...this.props} pageType={'2'} />
                }
                {
                  key === '6' && <Faculty {...this.props} />
                }
                {
                  key === '7' && <Funding {...this.props} />
                }
                {
                  key === '8' && <ExtendInfo tipMsg={tipMsg}
                    {...this.props}
                    isEndModal={this.state.isEndModal}
                    changeIsEndModal={(e) => {
                      this.setState({
                        isEndModal: false
                      })
                    }}
                    close={(e) => { this.handleCancel(e) }} />
                }
                {/* {
                  key==='9' && <ExtendInfo2 {...this.props}/>
                } */}
                {
                  key === '10' && <City {...this.props} />
                }
                {
                  key === '11' && <Income {...this.props} />
                }
                {
                  key === '12' && <Team {...this.props} />
                }
                {
                  key === '13' && <First {...this.props} />
                }
                {
                  key === '14' && <UnitFiscal {...this.props} />
                }
                {
                  key === '15' && <Leader tipMsg={tipMsg} {...this.props} />
                }
                {
                  key === '16' && <Economic {...this.props} />
                }
                {
                  key === '17' && <MemManage {...this.props} />
                }
                {
                  key === '18' && <GovernmentTeam {...this.props} />
                }
                {
                  key === '19' && <Village {...this.props} />
                }
                  {
                  key === '20' && <Select {...this.props} />
                }
                
                <WhiteSpace />
                <WhiteSpace />
                <WhiteSpace />
                <WhiteSpace />
                <div id={'modalend'}></div>
              </Col>
            </Row>
          </div>
        </div>
      </Modal>
    );
  }
}
