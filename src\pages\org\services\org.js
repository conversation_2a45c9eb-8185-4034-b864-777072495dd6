import request from "@/utils/request";
import qs from 'qs';



export function rightsGetList(params) {
  return request(`/api/org/develop/rights/getList`,{
    method:'POST',
    body:params,
  });
}

export function rightsSave(params) {
  return request(`/api/org/develop/rights/save`,{
    method:'POST',
    body:params,
  });
}

export function rightsDel(params) {
  return request(`/api/org/develop/rights/del?${qs.stringify(params)}`);
}


export function delNonPublic(params) {
  return request(`/api/org/nonPublic/party/delNonPublic?${qs.stringify(params)}`);
}

export function nonPublicGetList(params) {
  return request(`/api/org/nonPublic/party/getList`,{
    method:'POST',
    body:params,
  });
}

export function addNonPublic(params) {
  return request(`/api/org/nonPublic/party/addNonPublic`,{
    method:'POST',
    body:params,
  });
}

export function updateNonPublic(params) {
  return request(`/api/org/nonPublic/party/updateNonPublic`,{
    method:'POST',
    body:params,
  });
}


export function moveOrg(params) {
  return request(`/api/transfer/moveOrg`,{
    method:'POST',
    body:params,
  });
}

export function recognitionDelete(params) {
  return request(`/api/org/recognition/delete?${qs.stringify(params)}`);
}

export function townshipDelByCode(params) {
  return request(`/api/org/township/delByCode?${qs.stringify(params)}`);
}

export function townshipAdd(params) {
  return request(`/api/org/township/add`,{
    method:'POST',
    body:params,
  });
}

export function townshipGetList(params) {
  return request(`/api/org/township/getList`,{
    method:'POST',
    body:params,
  });
}


export function listTrainByCode(params) {
  return request(`/api/mem/train/listTrainByCode`,{
    method:'POST',
    body:params,
  });
}

export function trainAddOrUpdate(params) {
  return request(`/api/mem/train/addOrUpdate`,{
    method:'POST',
    body:params,
  });
}

export function deleteTrainByCode(params) {
  return request(`/api/mem/train/deleteTrainByCode?${qs.stringify(params)}`);
}


export function congressCommitteeBackOut(params) {
  return request(`/api/org/party/congress/committee/backOut`,{
    method:'POST',
    body:params,
  });
}

export function committeeBackOut(params) {
  return request(`/api/committee/backOut`,{
    method:'POST',
    body:params,
  });
}


export function caucusDelete(params) {
  return request(`/api/org/caucus/delete?${qs.stringify(params)}`);
}


export function caucusGetList(params) {
  return request(`/api/org/caucus/getList`,{
    method:'POST',
    body:params,
  });
}

export function caucusDetails(params) {
  return request(`/api/org/caucus/details?${qs.stringify(params)}`);
}

export function caucusAdd(params) {
  return request(`/api/org/caucus/add`,{
    method:'POST',
    body:params,
  });
}

export function recognitionGetList(params) {
  return request(`/api/org/recognition/getList`,{
    method:'POST',
    body:params,
  });
}
export function getListByAnnual(params) {
  return request(`/api/org/recognition/getListByAnnual`,{
    method:'POST',
    body:params,
  });
}
export function natureDelete(params) {
  return request(`/api/org/special/nature/delete?${qs.stringify(params)}`);
}

export function natureDetails(params) {
  return request(`/api/org/special/nature/details?${qs.stringify(params)}`);
}

export function natureGetList(params) {
  return request(`/api/org/special/nature/getList`,{
    method:'POST',
    body:params,
  });
}

export function natureAdd(params) {
  return request(`/api/org/special/nature/add`,{
    method:'POST',
    body:params,
  });
}

export function recognitionAdd(params) {
  return request(`/api/org/recognition/add`,{
    method:'POST',
    body:params,
  });
}
export function recognitionSituationAdd(params) {
  return request(`/api/org/recognitionSituation/add`,{
    method:'POST',
    body:params,
  });
}

export function recognitionSituationGetList(params) {
  return request(`/api/org/recognitionSituation/getList`,{
    method:'POST',
    body:params,
  });
}

export function findRecognitionByCode(params) {
  return request(`/api/org/recognition/findRecognitionByCode?${qs.stringify(params)}`);
}

export function getListAssess(params) {
  return request(`/api/org/assess/getListAssess`,{
    method:'POST',
    body:params,
  });
}
export function deleteAssess(params) {
  return request(`/api/org/assess/deleteAssess?${qs.stringify(params)}`);
}
export function addOrUpdateAssess(params) {
  return request(`/api/org/assess/addOrUpdateAssess`,{
    method:'POST',
    body:params,
  });
}

export function getSelectorList(params) {//组织选择器列表
  return request(`/api/org/getSelectorList`,{
    method:'POST',
    body:params,
  });
}
export function getSelectorCollectiveEconomicList(params) {
  return request(`/api/unit/collectiveEconomic/getList`,{
    method:'POST',
    body:params,
  });
}


export function getList(params) {
  return request(`/api/org/getList`,{
    method:'POST',
    body:params,
  });
}

export function getHistoryList(params) {
  console.log("🚀 ~ getHistoryList ~ params:", params)
  return request(`/api/org/history/getList`,{
    method:'POST',
    body:params,
  });
}

export function add(params) {
  return request(`/api/org/addOrg`,{
    method:'POST',
    body:params,
  });
}

export function updated(params) {
  return request(`/api/org/updateOrg`,{
     method:'POST',
    body:params,
  });
}

export function findOrg(params) {//组织基本信息查询
  return request(`/api/org/findByCode?${qs.stringify(params)}`);
}

export function findHistoryOrg(params) {//历史组织基本信息查询
  return request(`/api/org/history/findByCode?${qs.stringify(params)}`);
}

export function findReward(params) {//组织奖惩查询
  return request(`/api/org/reward/getList?${qs.stringify(params)}`);
}

export function addReward(params) {
  return request(`/api/org/reward/addReward`,{
    method:'POST',
    body:params,
  });
}

export function updateReward(params) {
  return request(`/api/org/reward/updateReward`,{
     method:'POST',
    body:params,
  });
}

export function delReward(params) {
  return request(`/api/org/reward/delReward?${qs.stringify(params)}`,);
}


//党小组
export function teamList(params) {
  return request(`/api/org/group/getList?${qs.stringify(params)}`,);
}
export function addGroup(params) {
  return request(`/api/org/group/addGroup`,{
    method:'POST',
    body:params,
  });
}
export function updateGroup(params) {
  return request(`/api/org/group/updateGroup`,{
     method:'POST',
    body:params,
  });
}
export function delGroup(params) {
  return request(`/api/org/group/delGroup?${qs.stringify(params)}`,{
    method:'Get',
  });
}
//党小组成员
export function groupMemList(params) {
  return request(`/api/org/group/mem/getList?${qs.stringify(params)}`);
}
export function addGroupMem(params) {
  return request(`/api/org/group/mem/addGroupMem`,{
    method:'POST',
    body:params,
  });
}
export function delGroupMem(params) {
  return request(`/api/org/group/mem/delGroupMem?${qs.stringify(params)}`,{
    method:'Get',
  });
}

//领导班子
export function orgElect(params) {
  return request(`/api/org/orgElect/add`,{
    method:'POST',
    body:params,
  });
}
export function orgElectUp(params) {
  return request(`/api/org/orgElect/update`,{
    method:'POST',
    body:params,
  });
}
export function orgElectList(params) {
  return request(`/api/org/orgElect/list?${qs.stringify(params)}`);
}
export function orgElectDel(params) {
  return request(`/api/org/orgElect/del`,{
    method:'POST',
    body:params,
  });
}
//领导班子成员
export function itteeAdd(params) {
  return request(`/api/committee/add`,{
    method:'POST',
    body:params,
  });
}
export function itteeUP(params) {
  return request(`/api/committee/update`,{
    method:'POST',
    body:params,
  });
}
export function itteeList(params) {
  return request(`/api/committee/list?${qs.stringify(params)}`);
}
export function itteeDel(params) {
  return request(`/api/committee/del`,{
    method:'POST',
    body:params,
  });
}


export function addOrUpdate(params) {
  return request(`/api/org/orgExtend/addOrUpdate`,{
    method:'POST',
    body:params,
  });
}
export function combined(params) {
  return request(`/api/org/combined`,{
    method:'POST',
    body:params,
  });
}

export function findExtendByCode(params) {
  return request(`/api/org/orgExtend/findExtendByCode?${qs.stringify(params)}`);
}

export function split(params) {
  return request(`/api/org/split`,{
    method:'POST',
    body:params,
  });
}

export function dissolve(params) {
  return request(`/api/org/dissolve`,{
    method:'POST',
    body:params,
  });
}
export function dissolveMsg(params) {
  return request(`/api/org/dissolveMsg`,{
    method:'POST',
    body:params,
  });
}
export function memGetList(params) {
  return request(`/api/mem/getList`,{
    method:'POST',
    body:params,
  });
}

export function appraisalAdd(params) {
  return request(`/api/org/appraisal/save`,{
    method:'POST',
    body:params,
  });
}

export function appraisalUpdate(params) {
  return request(`/api/org/appraisal/update`,{
    method:'POST',
    body:params,
  });
}

export function appraisalList(params) {
  return request(`/api/org/appraisal/list?${qs.stringify(params)}`);
}

export function appraisalRemove(params) {
  return request(`/api/org/appraisal/remove?${qs.stringify(params)}`);
}

export function personSave(params) {
  return request(`/api/org/reviewers/save`,{
    method:'POST',
    body:params,
  });
}

export function personUpdate(params) {
  return request(`/api/org/reviewers/update`,{
    method:'POST',
    body:params,
  });
}

export function personRemove(params) {
  return request(`/api/org/reviewers/remove?${qs.stringify(params)}`);
}
export function personList(params) {
  return request(`/api/org/reviewers/list?${qs.stringify(params)}`);
}

export function listMem(params) {
  return request(`/api/org/reviewers/listMem?${qs.stringify(params)}`);
}
// 党务工作者情况列表
export function workList(params) {
  return request(`/api/org/party/work/list?${qs.stringify(params)}`,{
    method:'Get',
  });
}
// 增加党务工作者
export function workSave(params) {
  return request('/api/org/party/work/save',{
    method:'POST',
    body:params
  });
}
// 删除党务工作者
export function workDel(params) {
  return request('/api/org/party/work/del',{
    method:'POST',
    body:params
  });
}
export function electQueryList(params) {
  return request(`/api/org/party/congress/elect/list?${qs.stringify(params)}`);
}
export function electSave(params) {
  return request('/api/org/party/congress/elect/save',{
    method:'POST',
    body:params
  });
}
export function electUpdate(params) {
  return request('/api/org/party/congress/elect/update',{
    method:'POST',
    body:params
  });
}
export function electDel(params) {
  return request('/api/org/party/congress/elect/del',{
    method:'POST',
    body:params
  });
}

export function committeeQueryList(params) {
  return request(`/api/org/party/congress/committee/list?${qs.stringify(params)}`);
}
// 添加党代表情况届次人员
export function committeeSave(params) {
  return request('/api/org/party/congress/committee/save',{
    method:'POST',
    body:params
  });
}
export function committeeUpdate(params) {
  return request('/api/org/party/congress/committee/update',{
    method:'POST',
    body:params
  });
}
export function committeeDel(params) {
  return request('/api/org/party/congress/committee/del',{
    method:'POST',
    body:params
  });
}
//终止党代表资格
export function committeeStop(params) {
  return request('/api/org/party/congress/committee/termination',{
    method:'POST',
    body:params
  });
}

// 党组管理-新增党组
export function addParty(params) {
  return request('/api/org/party/addParty', {
      method: 'POST',
      body: params,
  });
}

// 党组管理-党组列表
export function partyGetList(params) {
  return request('/api/org/party/getList', {
      method: 'POST',
      body: params,
  });
}

// 党组管理-删除党组
export function delParty(params) {
  return request(`/api/org/party/delParty?${qs.stringify(params)}`,{
    method:'Get',
  });
}

// 党组管理-编辑党组
export function updateParty(params) {
  return request('/api/org/party/updateParty', {
      method: 'POST',
      body: params,
  });
}

// 党组织管理-编辑-基本信息：获取上级党组织关联的主单位
export function getMainUnitByOrg(params) {
  return request(`/api/unit/getMainUnitByOrg?${qs.stringify(params)}`,{
    method:'Get',
  });
}

// 历史届次信息
export function hoistoryList(params) {
  return request(`/api/org/party/congress/elect/hoistoryList?${qs.stringify(params)}`,{
    method:'Get',
  });
}
// 撤销党代表历史届次
export function revoke(params) {
  return request(`/api/org/party/congress/elect/revoke`,{
    method:'POST',
    body:params,
  });
}
// 查询党代表届内历史人员
export function historyListPeople(params) {
  return request(`/api/org/party/congress/committee/historyList?${qs.stringify(params)}`,{
    method:'Get',
  });
}

//  撤销党代表届内历史人员
export function revokePeople(params) {
  return request(`/api/org/party/congress/committee/revoke`,{
    method:'POST',
    body:params,
  });
}

export function mdfind(params) {
  return request(`/api/org/develop/rights/find?${qs.stringify(params)}`,{
    method:'Get',
  });
}
// 班子成员新增校验
export function getListByIdcard(params) {
  return request(`/api/committee/getListByIdcard?${qs.stringify(params)}`,{
    method:'GET',
  });
}


//新增流动党组织
export function addOrgFlow(params) {
  return request(`/api/org/flow/addOrgFlow`,{
    method:'POST',
    body:params,
  });
}
// 编辑流动党组织
export function updateOrgFlow(params) {
  return request(`/api/org/flow/update`,{
    method:'POST',
    body:params,
  });
}
