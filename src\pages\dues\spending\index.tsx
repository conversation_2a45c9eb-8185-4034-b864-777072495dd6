/**
 * 党费支出
 * */
import React from 'react';
import ListTable from '@/components/ListTable';
import ListFilter from '@/components/ListFilter';
import NowOrg from '@/components/NowOrg';
import { Button, Input, Tag, Tabs, Select, DatePicker, Modal, Divider, Popconfirm } from 'antd';
import styles from "./index.less";
import { connect } from 'dva';
import moment from 'moment';
import RuiFilter from 'src/components/RuiFilter';
import AddEdit from './addEdit';
import Bill from './bill'
import { isEmpty, setListHeight } from '@/utils/method';
import {getSession} from "@/utils/session";
import Notice from '@/components/Notice';
const TabPane = Tabs.TabPane;
const Search=Input.Search;
const Option = Select.Option;
const { MonthPicker, } = DatePicker;
@connect(({dues,login,commonDict})=>({
  dues,
  login,
  commonDict
}))
export default class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state={
      memName:'',
      orgCode:'',
      type:'add',
      visible:false,
      menuTreeData:[],
      date:moment().valueOf(),
      tab:'1',
      years:moment().format('YYYY')
    }
  }
  //JSON.parse(sessionStorage.getItem('org') || "")['code']||
  componentDidMount = () => {
    let org=getSession('org')|| {};
    this.setState({
      orgCode:org['orgCode'],
      code:org['code'],
    },()=>{
      this.onPage();
    });
    setListHeight(this);
  };
  componentWillReceiveProps =(nextProps: Readonly<any>, nextContext: any) => {
    if (!isEmpty(this.state.orgCode)&&this.state.orgCode!==JSON.parse(sessionStorage.org)['orgCode']) {
      this.setState({
        orgCode:JSON.parse(sessionStorage.org)['orgCode'],
        code:JSON.parse(sessionStorage.org)['code'],
      },()=>{
        this.onPage();
      })
    }
  };
  onPage = ( pageNum=1,size=10) => {
    let val = {
      orgCode:this.state['code'],
      disburseOrgCode:this.state['orgCode'],
      pageNum:pageNum,
      pageSize:size,
      recordTime:this.state['date'],
      recorder:this.state['memName'],
      d68CodeList:this.state['d68CodeList'],
      recordTypeList:this.state['recordTypeList']
    };
    for (let obj in val) {
      if (isEmpty(val[obj])) {
        delete val[obj]
      }
    }
    this.props.dispatch({
      type:'dues/getListzc',
      payload:{
        data:{
          ...val
        }
      }
    })
  };
  // onChange = (page) => {
  //   this.onPage(page);
  // };

  isSearch = (value) => {
    this.setState({memName:value},()=>{
      this.onPage()
    })
  };

  changePage=(v,k)=>{
    this.onPage(v,k);
    this.setState({page:v,pageNum:k})
  };
  changeList=()=>{

    this.onPage();
  };

  export=()=>{
    this.setState({dataInfo:{},type:'add'},()=>{
      this['AddEdit'].showModal();
    });
  };
  import=()=>{
    this.setState({dataInfo:{},type:'add'},()=>{
      this['Bill'].showModal();
    });
  };

  changeInfo=(e)=>{
    this.setState({tab:e},()=>{
    })
  };

  filterChange=(val)=>{
    this.setState({
      d68CodeList:val['d68CodeList'],
      recordTypeList:val['d04CodeList']
    },()=>{
      this.onPage()
    });
  };
  changeDate=(v)=>{
    if (!isEmpty(v)) {
      this.setState({
        date:moment(v).valueOf()
      },()=>{
        this.onPage();
      })
    }
  };
  disabledTomorrow=(current) =>{
    // Can not select days before today and today
    return current && current < moment('2019')||current>moment().endOf('day');
  };

  edit=(record)=>{
    this.setState({
      dataInfo:record,
      type:'edit'
    },()=>{
      this['AddEdit'].showModal();
    })
  };
  confirm = (record) =>{
    this.props.dispatch({
      type:'dues/delFeeDisburse',
      payload:{
        code:record['code']
      }
    }).then(res=>{
      if (res['code'] == 0) {
        Notice("操作提示",'删除成功!',"check-circle","green");
        this.onPage()
      }else {
        Notice("操作提示",res['message'],"exclamation-circle-o","orange");
      }
    })
  };

  render(): React.ReactNode {
    const { dues:{ list=[],pagination:{ pageSize=0,totalRow=0,page=1,pageNumber=1 }={}}={},login:{userRole=[]}, loading:{effects = {}} = {},commonDict} =this.props;
    const { type,id,menuTreeData,dataInfo={} ,filterHeight,org={},tab}=this.state;

    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:50,
        render:(text,record,index)=>{
          return ((pageNumber-1)*pageSize)+index+1
        }
      },

      {
        title:'支部名称',
        dataIndex:'orgName',
        width:200,
        render:(text,record) => {
          return <span>{isEmpty(text)?record['creatorAccount']:text}</span>
        }
      },
      {
        title:'金额',
        dataIndex:'money',
        width:100,
        render:text => {
          return <span>{text=='0'?'免交':text+'元'}</span>
        }
      },
      {
        title:'收支项目',
        dataIndex:'d68Name',
        width:100,
      },
      {
        title:'记录日期',
        dataIndex:'createTime',
        width:100,
        render:text => {
          return <span>{moment(text).format('YYYY-MM-DD')}</span>
        }
      },
      {
        title:'记录人',
        dataIndex:'recorder',
        width:100,
      },
      {
        title:'录入类型',
        dataIndex:'recordType',
        width:100,
        render:text => {
          return <span>{text=='0'?'手动录入':'账单录入'}</span>
        }
      },
      {
        title:'操作',
        dataIndex:'action',
        width:100,
        render:(text,record)=>{
          return(
            <React.Fragment>
              <a href={'#'} onClick={()=>this.edit(record)}>编辑</a>
              <Divider type="vertical"/>
              <Popconfirm title="确定要删除吗？" onConfirm={()=>this.confirm(record)}>
                <a href={'#'} className='del'>删除</a>
              </Popconfirm>
            </React.Fragment>
          )
        },
      },
    ];
    const filterData=[
      {
        key:'d04CodeList',name:'录入类型',value:[{key:'0',name:'手动录入'},{key:'1',name:'账单录入'}],
      },
      {
        key:'d68CodeList',name:'收支项目',value:commonDict[`dict_d68_tree`],
      },
    ];
    return(
      <div className={styles.container}>
        <Tabs defaultActiveKey={tab} onChange={this.changeInfo}>
          <TabPane tab="基本信息" key="1"/>
          {/*<TabPane tab="统计信息" key="2"/>*/}
        </Tabs>
        <NowOrg  extra={
          <ListFilter>
            <Search
              placeholder="请输入检索关键词"
              onSearch={value => this.isSearch(value)}
              style={{ width: 200 }}
              className={styles.filter}
            />
            <MonthPicker
              disabledDate={this.disabledTomorrow}
              placeholder="请选择日期"
              style={{marginLeft:20}}
              defaultValue={moment()}
              format={'YYYY/MM'}
              onChange={this.changeDate}/>
            <Button style={{marginLeft:20}} type="primary" onClick={this.export}>新增支出</Button>
            <Button style={{marginLeft:20}} type="primary" onClick={this.import}>流水导入</Button>
          </ListFilter>
        }/>
        <RuiFilter data={filterData} onChange={this.filterChange}/>
        <AddEdit wrappedComponentRef={(e)=>this['AddEdit']=e} data={dataInfo} type={type} title={type=='edit'?'编辑支出':'新增支出'} onChange={this.changeList}/>
        <Bill wrappedComponentRef={(e)=>this['Bill']=e}  onChange={this.changeList}/>
        {/*<ExportFile wrappedComponentRef={(e)=>this['ExportFile']=e} onChange={this.changeList}/>*/}
        <ListTable
          columns={columns}
          data={list}
          scroll={{y:filterHeight}}
          pagination={{pageSize,total:totalRow,page,current:pageNumber}}
          onPageChange={this.changePage}/>
      </div>
    );
  }
}
