import request from "@/utils/request";
import qs from 'qs';


export function updateByFileType(params) {
  return request(`/api/transfer/updateByFileType`,{
    method:'POST',
    body:params,
  });
}
export function exportLetter(params) {
  return request(`/api/transfer/exportLetter`,{
    method:'POST',
    body:params,
  });
}

export function findInByPage(params) {
  console.log('findInByPage.params====',params);
  return request(`/api/transfer/findInByPage`,{
    method:'POST',
    body:params,
  });
}


export function findOutByPage(params) {
  console.log('findOutByPage.params====',params);
  return request(`/api/transfer/findOutByPage`,{
    method:'POST',
    body:params,
  });
}

export function addTransfer(params) {
  return request(`/api/transfer/org`,{
    method:'POST',
    body:params,
  });
}
export function transferMem(params) {
  return request(`/api/transfer/mem`,{
    method:'POST',
    body:params,
  });
}

export function adjustMem(params) {
  return request(`/api/transfer/adjustMem`,{
    method:'POST',
    body:params,
  });
}



export function inDetail(params) {//关系转接转入详情
  return request(`/api/transfer/inDetail?${qs.stringify(params)}`);
}


export function outDetail(params) {//关系转接转入详情
  return request(`/api/transfer/outDetail?${qs.stringify(params)}`);
}

export function apply(params) {//审批通过
  return request(`/api/transfer/apply`,{
    method:'POST',
    body:params,
  });
}

export function back(params) {//退回转接
  return request(`/api/transfer/back`,{
    method:'POST',
    body:params,
  });
}

export function changeTargetOrg(params) {//变更目的组织
  return request(`/api/transfer/changeTargetOrg`,{
    method:'POST',
    body:params,
  });
}


//党小组
export function undo(params) {//撤销
  return request(`/api/transfer/undo`,{
    method:'POST',
    body:params,
  });
}
export function transferMemInfo(params) {//获取转接人员信息
  return request(`/api/transfer/transferMemInfo?${qs.stringify(params)}`);
}
export function editTransferForMem(params) {//关系转接调整
  return request(`/api/transfer/editTransferForMem`,{
     method:'POST',
    body:params,
  });
}
export function memTransferInFromSysOut(params) {
  return request(`/api/transfer/memTransferInFromSysOut`,{
     method:'POST',
    body:params,
  });
}
//党小组成员
export function groupMemList(params) {
  return request(`/api/org/group/mem/getList?${qs.stringify(params)}`);
}
export function addGroupMem(params) {
  return request(`/api/org/group/mem/addGroupMem`,{
    method:'POST',
    body:params,
  });
}
export function delGroupMem(params) {
  return request(`/api/org/group/mem/delGroupMem?${qs.stringify(params)}`,{
    method:'Get',
  });
}

//领导班子
export function orgElect(params) {
  return request(`/api/org/orgElect/add`,{
    method:'POST',
    body:params,
  });
}
export function orgElectUp(params) {
  return request(`/api/org/orgElect/update`,{
    method:'POST',
    body:params,
  });
}
export function orgElectList(params) {
  return request(`/api/org/orgElect/list?${qs.stringify(params)}`);
}
export function orgElectDel(params) {
  return request(`/api/org/orgElect/del`,{
    method:'POST',
    body:params,
  });
}
//领导班子成员
export function itteeAdd(params) {
  return request(`/api/committee/add`,{
    method:'POST',
    body:params,
  });
}
export function itteeUP(params) {
  return request(`/api/committee/update`,{
    method:'POST',
    body:params,
  });
}
export function itteeList(params) {
  return request(`/api/committee/list?${qs.stringify(params)}`);
}
export function itteeDel(params) {
  return request(`/api/committee/del`,{
    method:'POST',
    body:params,
  });
}
export function addMem(params) {
  return request(`/api/transfer/addMem`,{
    method:'POST',
    body:params,
  });
}
export function findDZBOrgByOrgId(params) {
  return request(`/api/transfer/findDZBOrgByOrgId`,{
    method:'POST',
    body:params,
  });
}
export function orgTrans(params) {
  return request(`/api/transfer/org`,{
    method:'POST',
    body:params,
  });
}

// 全国交换区党组织接入列表
export function getProvinces(params) {
  return request(`/api/across/getProvinces?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function findOnUniqueCode(params) {
  return request(`/api/transfer/findOnUniqueCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}

// 组织关系转接发起时，选择党员后，进行校验
export function transferMsg(params) {
  console.log('transferMsg====',params);
  return request(`/api/transfer/transferMsg`,{
    method:'POST',
    body:params,
  });
}

export function findMemInfo(params) {
  return request(`/api/transfer/findMemInfo?${qs.stringify(params)}`,{
    method:'GET',
  });
}

export function checkMemInfo(params) {
  console.log('transferMsg====',params);
  return request(`/api/transfer/checkMemInfo`,{
    method:'POST',
    body:params,
  });
}

// 取消提示
export function cancelHint(params) {
  console.log('cancelHint====',params);
  return request(`/api/transfer/cancelHint`,{
    method:'POST',
    body:params,
  });
}