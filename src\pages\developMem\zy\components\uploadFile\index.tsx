import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import style from './index.less';
import NowOrg from '@/components/NowOrg';
import { Button, Form, Alert,Modal,Space } from 'antd';
import ListTable from '@/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import SpinProgress from '@/components/SpinProgress';

const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  };

const index: any = (props, ref) => {
  const {
    tableAction,
    FormComp,
    tableColumns,
    otherBtnsFunc,
    getSearchCallBack,
    progressCallback,
    otherRenderTableColumnsFunc,
    renderTableNumber,
    isDefaultForm = true,
    rowKey = 'id'
  } = props;

  useImperativeHandle(ref, () => ({
    showModal:()=>{
      open();
    }
  }));
  const org:any = getSession('org') || {};
  const [form] = Form.useForm();
  const { pathname, query: urlQuery = {} } = window['g_history']?.location || {};
  const { login: { listTree = [] } = {} } = props;
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 20, total: 0, current: 1 });
  const [keyword, setKeyword] = useState({});
  const [modalVisible,setModalVisible] = useState(false);
  const restForm = () => {
    form.resetFields();
    setList([]);
    setPagination({ pageNum: 1, pageSize: 20, total: 0, current: 1 })
  };
const open=()=>{

}
const cancel=()=>{

}
const hadndleFinish = async (e) => {
    console.log('🚀 ~ e:', e);
  };

  useEffect(() => {
 
  }, [_get(listTree, '[0][0].code', ''), urlQuery.box]);

 
  return (
    <Modal
     title={<div><span>{`新增${org?.name}组织发展党员`}</span><div>发展党员工作规程</div> </div>}
    destroyOnClose
    visible={modalVisible}
    // onCancel={cancel}
    width={'1200px'}
    // onOk={()=>{
    //     form.submit();
    // }}
    footer={
        <React.Fragment>
            <Space>
            <Button onClick={cancel}>上一步</Button>
             <Button onClick={cancel}>取消</Button>
            <Button type="primary" onClick={()=>{form.submit()}}>
              保存
            </Button>
            </Space>
           
        </React.Fragment>
      }
    >
        
    <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
    <Form.Item label="党员姓名" name={'name'} rules={[{ required: true, message: '党员姓名' }]}></Form.Item>
    </Form>
 
    </Modal>
          
  );
};
// @ts-ignore
export default React.forwardRef(index);
