import React, { Fragment, useEffect, useState } from 'react';
import { Button, Col, Divider, Form, InputNumber, Row, Switch } from 'antd';
import WhiteSpace from '@/components/WhiteSpace';
import { formItemLayout3 } from './config';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import { findZtDataByCode, saveZt10Data } from '@/pages/[unit]/services/thematic';
import Tip from '@/components/Tip';
import { Icon as LegacyIcon } from '@ant-design/compatible';

const index = (props: any) => {
  const { selectTab, basicInfo } = props;
  const { code: unitCode = '' } = basicInfo;
  const [form] = Form.useForm();
  const [query, setQuery] = useState({});
  const [loading, setLoading] = useState(false);

  const onFinish = async (e) => {
    setLoading(true);
    let val = _cloneDeep(e);
    [
      'isBuildBoard',
      'isSecretaryChairmanByOne',
      'isIntakeBusinessOverhead',
      'isPersonnelPartyByDepartment',
      'isChairmanBySuperiorPrincipal',
      'isPartyRequiredWriteRule',
      'isOrganStudyAsPerProcessors',
      'isBuildOrgan',
      'isPersonnelPartyByLeader',
      'isPartyManagerialSameRank',
    ].map((it) => {
      val[it] = val[it] ? 1 : 0;
    });
    const { code = 500 } = await saveZt10Data({
      data: {
        unitCode,
        ...val,
      },
    });
    setLoading(false);
    if (code === 0) {
      Tip.success('操作提示', '修改成功');
      getInfo();
    }
  };
  const getInfo = async () => {
    const { code = 500, data = {} } = await findZtDataByCode({
      unitCode,
      type: '10',
    });
    if (code === 0) {
      setQuery(data);
      form.setFieldsValue(data);
    }
  };
  useEffect(() => {
    getInfo();
  }, []);
  return (
    <Fragment>
      <Form form={form} {...formItemLayout3} onFinish={onFinish}>
        <Row>
          <Col span={24}>
            <Divider plain>企业本级情况</Divider>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isBuildBoard"
              initialValue={_get(query, 'isBuildBoard', false)}
              label="建立董事会的企业数"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isSecretaryChairmanByOne"
              initialValue={_get(query, 'isSecretaryChairmanByOne', false)}
              label="党组织书记、董事长由一人担任的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isIntakeBusinessOverhead"
              initialValue={_get(query, 'isIntakeBusinessOverhead', false)}
              label="党建工作经费按上年度工资总额一定比例纳入企业管理费用的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isPersonnelPartyByDepartment"
              initialValue={_get(query, 'isPersonnelPartyByDepartment', false)}
              label="人事管理和基层党建由一个部门抓的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isPersonnelPartyByLeader"
              initialValue={_get(query, 'isPersonnelPartyByLeader', false)}
              label="人事管理和基层党建由一个领导管的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isPartyManagerialSameRank"
              initialValue={_get(query, 'isPartyManagerialSameRank', false)}
              label="党务工作人员和经营管理人员同职级同待遇的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Divider plain>所属各级国有独资、全资、国有控股企业情况</Divider>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isChairmanBySuperiorPrincipal"
              initialValue={_get(query, 'isChairmanBySuperiorPrincipal', false)}
              label="董事长由上级企业有关负责人兼任的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="listedCompanyNum" label="上市公司数" rules={[{ required: true }]}>
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isPartyRequiredWriteRule"
              initialValue={_get(query, 'isPartyRequiredWriteRule', false)}
              label="其中党建工作要求写入公司章程的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isOrganStudyAsPerProcessors"
              initialValue={_get(query, 'isOrganStudyAsPerProcessors', false)}
              label="把党组织研究讨论作为董事会、经理层决策重大问题前置程序的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Divider plain>所属境外分支机构情况</Divider>
          </Col>
          <Col span={12}>
            <Form.Item name="branchOfficesNum" label="分支机构数" rules={[{ required: true }]}>
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              valuePropName="checked"
              name="isBuildOrgan"
              initialValue={_get(query, 'isBuildOrgan', false)}
              label="已建党组织的"
              rules={[{ required: true }]}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="primaryPartyOrganNum"
              label="基层党组织数"
              rules={[{ required: true }]}
            >
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="branchPartyNum" label="党员数" rules={[{ required: true }]}>
              <InputNumber max={99999999} min={0} />
            </Form.Item>
          </Col>

          <div style={{ width: '100%', textAlign: 'center' }}>
            <WhiteSpace />
            <Button
              icon={<LegacyIcon type={'check'} />}
              type={'primary'}
              onClick={() => form.submit()}
              loading={loading}
            >
              保存
            </Button>
          </div>
        </Row>
      </Form>
    </Fragment>
  );
};
export default index;
