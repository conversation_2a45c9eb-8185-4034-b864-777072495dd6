import React from 'react';
import {Steps, Modal} from 'antd';
import First from '../first';
import Last from '../last';
import Tip from '@/components/Tip';
const Step = Steps.Step;
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible:false,
      current:0,
      info:{} // 查询的人的信息
    }
  }
  handleCancel=()=>{
    this.destroy()
  };
  destroy=()=>{
    this.setState({
      visible:false,
      current:0,
      info:{}
    });
    this.props.dispatch({
      type: 'memMultiple/updateState',
      payload:{
        multipleInfo:{}
      }
    })
  };
  open=()=>{
    this.setState({visible:true})
  };
  firstSubmit=({code = 500 , data = {}} = {})=>{
    code === 0 && this.setState({info:data,current:1})
  };
  lastSubmit= async (val)=>{
    const {onsubmit} = this.props;
    const res = await this.props.dispatch({
      type:'memMultiple/save',
      payload:{data:{...val},type:'add'}
    });
    const {code = 500} = res || {};
    if(code === 0){
      this.handleCancel();
      Tip.success('操作提示','操作成功');
      onsubmit && onsubmit()
    }
  };
  render() {
    const {visible,current,info} = this.state;
    return (
      <Modal
        title="关联"
        destroyOnClose
        visible={visible}
        footer={null}
        onCancel={this.handleCancel}
        width={800}
      >
        <Steps current={current}>
          <Step title="关联党员" />
          <Step title="任职信息" />
        </Steps>
        <div style={{marginBottom:'10px'}}/>
        {
          current === 0 && <First submit={this.firstSubmit} {...this.props}/>
        }
        {
          current === 1 && <Last memInfo={info} submit={this.lastSubmit} {...this.props}/>
        }
      </Modal>
    );
  }
}
