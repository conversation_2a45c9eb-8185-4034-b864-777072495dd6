import React, {Fragment} from 'react';
import style from './index.less';
import CardsGroupModal from '@/components/CardsGroup/CheckModal';
import { isEmpty } from '@/utils/method';
import MemTotal from './cards';
import Charts from './charts';
import {findByChartType,getDictChartList} from '@/components/CardsGroup/services';
import {arrSort} from '@/utils/method';
import _sortBy from 'lodash/sortBy';
// import {configList, chartsConfigList} from './config';
import _isEmpty from 'lodash/isEmpty';
import _startsWith from 'lodash/startsWith';
import _concat from 'lodash/concat';
import _difference from 'lodash/difference';
import { Row, Col } from 'antd';
import _random from 'lodash/random';
interface Interface {
  timeRange:object,
  cardParent:string,
  cardConfig?:Array<object>,
  chartConfig?:Array<object>,
}
export default class index extends React.Component<Interface, any> {
  constructor(props) {
    super(props);
    this.state = {}
  }
  componentDidMount(): void {
    const {cardParent} = this.props;
    const {user}=sessionStorage;
    user && this.getList(cardParent);
  }
  getList= async (cardParent)=>{
    const res = await getDictChartList({chartType:cardParent});
    const {code = 500,data = []} = res;
    if(code === 0){
      let final = [];
      let final2 = [];
      let main;
      if(!_isEmpty(data)){
        main = data.filter(it=> it['parent'] === '-1');
        final = data.filter(it=> it['type'] === 'card');
        final2 =  data.filter(it=> it['type'] === 'chart');
        // const [it] = main;
        // const {para_name = ''} = it ||{};
        let para = cardParent.substring(0,1).toUpperCase()+cardParent.substring(1);
        // 获取已有模块
        const res = await findByChartType({chartType:para});
        const {code = 500, data:checkedDate = {} } = res;
        if(code === 0){
          let arr = checkedDate[`${cardParent}`];
          let arrString:Array<string> = [];
          let arrCard:Array<string> = [];
          if(!_isEmpty(arr)){
            arr.forEach(item=>{
              arrString.push(item['key'])
            });
            arr.forEach(item=>{
              final.map(it=>{
                if(it['key'] === item['key']){
                  arrCard.push(it['key'])
                }
              })
            });
            this.setState({
              checkedAll:[...new Set(arrSort(arrString))],
              checked:[...new Set(arrSort(arrCard))],
              checkedChart:[...new Set(_difference(arrString,arrCard))]
            })
          }else {
            // 初始没有设置模块，使用default配置
            let cardDefaultChecked = final.filter(item=>item['default'] === '1');
            let chartDefaultChecked = final2.filter(item=>item['default'] === '1');
            cardDefaultChecked.forEach(item=>{
              arrCard.push(item['key'])
            });
            chartDefaultChecked.forEach(item=>{
              arrString.push(item['key'])
            });
            this.setState({
              checkedAll:_concat(arrCard,arrString),
              checked:arrSort(arrCard),
              checkedChart:arrSort(arrString)
            })
          }
        }
        this.setState({final,final2,main})
      }
    }
  };
  cardSubmit=(val)=>{
    const {final2} = this.state;
    let checkedCharts:Array<string> = [];
    if(!_isEmpty(final2)){
      val.forEach(item=>{
        final2.map(it=>{
          if(it['key'] === item){
            checkedCharts.push(item)
          }
        })
      })
    }
    this.setState({
      checkedAll:val,
      checked:_difference(val,checkedCharts),
      checkedChart:checkedCharts
    })
  };
  open=()=>{
    this['CardsGroupModal'].open();
  };
  render(): React.ReactNode {
    const {cardConfig = [],chartConfig = []} = this.props;
    const {checked,checkedChart,checkedAll,final,final2,main} = this.state;
    // card和chart被选中的数字数组
    let checkArr:Array<object> = [];
    let checkArr2:Array<object> = [];
    if(!isEmpty(checked)){
      checked.forEach(it=>{
        cardConfig && cardConfig.forEach(item=>{
          if(item['key'] === it){
            checkArr.push(item)
          }
        })
      });
    }
    if(!isEmpty(checkedChart)){
      checkedChart.forEach(it=>{
        chartConfig && chartConfig.forEach(item=>{
          if(item['key'] === it){
            checkArr2.push(item)
          }
        })
      })
    }
    // 把选中的card和chart整合config的配置
    let last:Array<object> = [];
    if(!_isEmpty(final)){
      final.forEach(item=>{
        cardConfig.forEach(it=>{
          if(item['key'] === it['key']){
            last.push({...item,...it})
          }
        })
      })
    }
    let last2:Array<object> = [];
    if(!_isEmpty(final2)){
      final2.forEach(item=>{
        chartConfig.forEach(it=>{
          if(item['key'] === it['key']){
            last2.push({...item,...it})
          }
        })
      })
    }
    return (
      <div className={style.cards}>
        <Row justify="start" gutter={16}>
          {
            checkArr && checkArr.map((item,index)=>{
              return (
                <Col span={6} key={index}>
                  <div className={style.cardsItem}>
                    <MemTotal
                      key={item['key']}
                      {...this.props}
                      cardData={item['value']}
                    />
                  </div>
                </Col>
              )
            })
          }
        </Row>
        {/*<div style={{marginBottom:10}}/>*/}
        <Row gutter={16}>
          {
            checkArr2 && checkArr2.map((item,index)=>{
              let number = checkArr2.length;
              let quzheng = parseInt(`${number/3}`);
              let quyu = number%3;
              return (
                <Col span={quzheng * 3 > index ? 8 : quyu === 1 ? 24 : 12} key={index}>
                  <div className={style.cardsItem} style={{padding:10}}>
                    <Charts chartsConfig={item['value']} {...this.props} key={`${item['key']}_${new Date().valueOf()}`}/>
                  </div>
                </Col>
              )
            })
          }
        </Row>

        <CardsGroupModal ref={e=>this['CardsGroupModal'] = e} submit={this.cardSubmit} data={_sortBy(_concat(last,last2),'key')} parent={main} checked={checkedAll} />
      </div>
    )
  }
}
