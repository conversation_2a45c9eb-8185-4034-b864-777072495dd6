import React, {Fragment} from 'react';
import style from './index.less';
import ListTable from '@/components/ListTable';
import ListFilter from '@/components/ListFilter';
import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, Divider, Drawer, Popconfirm, Modal, Input, Tabs } from 'antd';
import AddEdit from './addEdit';
import {connect} from "dva";
import moment from 'moment';
import Notice from '@/components/Notice';
import { changeListPayQuery, isEmpty, setListHeight } from '@/utils/method';
import ModalRole from './modalRole';
import styles from '@/pages/user/user/index.less';
import { getSession } from '@/utils/session';
const Search=Input.Search;
const TabPane = Tabs.TabPane;
@connect(({role, login, loading})=>({role, login, loading}))
export default class index extends React.Component<any,any> {
  constructor(props){
    super(props);
    this.state = {
      type:'',
      upItem:undefined,
      drawerVisible:false,
      userListData:{},
      clickItemId:'',
      warningVsb:false,
      orgCode:''
    }
  }
  componentDidMount(): void {
    let org=getSession('org')|| {};
    this.setState({
      orgCode:org['orgCode']
    },()=>{
      this.getList(1,10);
      setListHeight(this);
    });
  }
  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org=getSession('org') || {};
    if (!isEmpty(this.state['orgCode'])&&this.state['orgCode']!==org['orgCode']) {
      this.setState({
        orgCode:org['orgCode']
      },()=>{
        this.getList(1,10)
      })
    }
  }
  getList =(page = 1, size)=>{
    this.props.dispatch({
      type:'role/queryList',
      payload:{
        keyword:this.state['keyword']||undefined,
        pageNum:page,
        pageSize:size,
        orgCode:this.state['orgCode']
      }
    })
  };
  pageChange = (page,pageNum) => {
    this.getList(page,pageNum);
    this.setState({page:page})
  };
  pageUserChange= (page,pageNum) => {
    this.getUserList(this.state.clickItemId,page,pageNum);
  };
  handleOk= async (val, type)=>{
    let text = 'role/add';
    if(type === 'edit'){
      text = 'role/edit';
    }
    const res = await this.props.dispatch({
      type:text,
      payload:{
        data:val,
      }
    });
    const {code = 500,message = ''} = res || {};
    if(code === 0){
      Notice("操作提示",'操作成功',"check-circle","green");
      this['AddEdit1'].handleCancel();
      this.getList(1,10);
    }else {
      Notice("操作提示",message,"exclamation-circle-o","orange");
    }
  };
  editOk=(val)=>{
    const {editInfo = {}} =this.props.role;
    let type = 'edit';
    this.handleOk({...editInfo, ...val}, type);
  };
  addOk=(val)=>{
    let type = 'add';
    this.handleOk(val, type);
  };
  del= async (val)=>{
    const res = await this.props.dispatch({
      type:'role/del',
      payload:{
        ...val
      }
    });
    const {code = 500, message = '操作失败', data:{ faiIIds =[] } = {}} = res;
    let msg = '';
    if(code !== 0){
      Notice("操作提示",message,"exclamation-circle-o","orange");
    }else {
      if(!isEmpty(faiIIds)){
        faiIIds.map(item=>{
          msg += `${val.name}  ${item.msg}`
        });
        Notice("操作提示",msg,"exclamation-circle-o","orange");
      }else {
        Notice("操作提示",message,"check-circle","green");
        this.getList(1,10);
      }
    }
  };
  openEdit = (rec) =>{
    this.getLV();
    this.setState({type:"edit"});
    this['AddEdit1'].showModal();
    this.props.dispatch({
      type:'role/getEditInfo',
      payload:{
        ...rec
      }
    })
  };
  openAdd = () =>{
    this.getLV();
    this.setState({type:"add"});
    this['AddEdit1'].showModal();
  };
  getLV = () =>{
    this.props.dispatch({
      type:'role/getLV',
      payload:{

      }
    })
  };
  roleUp=(item,disabled)=>{
    this.setState({upItem:item,disabled});
    this.props.dispatch({
      type:'role/getUserRole',
      payload:{
        data:{
          "roleId": item['id'],
          "isPaging": false
        }
      }
    });
    ModalRole.open();
  };
  ModalRoleOk=async (val=[],edit,parentKey)=>{
    const {role,login:{menuData=[] }={}} = this.props;
    const {userRole=[],userRoleAll=[]}=role;
    //menuData 全部菜单
    //userRole 默认选中菜单
    //val 操作选择项
    this.setState({parentKey},()=>{
      if (menuData.length===userRole.length){//全选 子菜单数与总菜单数一致
        if (isEmpty(val)){//如果选择项为空  有两种情况  1.什么操作也没做 2.全部取消选中
          if (edit){
            this.setState({warningVsb:true,valData:val})
          } else {
            this.isOk(userRole)
          }
        } else {//如果不为空 在子菜单与总菜单相等时 变化值只能小于子菜单
          this.setState({warningVsb:true,valData:val})
        }
      } else { //没全选
        if (isEmpty(val)){//如果选择项为空  有两种情况  1.什么操作也没做 2.全部取消选中
          if (edit){
            this.setState({warningVsb:true,valData:val})
          } else {
            this.isOk(userRoleAll)
          }
        } else {//如果不为空  有变化 值一定发生了更改  变化分为 增加 、取消和取消一个增加一个原长度不变
          if (isEmpty(userRole)) {
            this.isOk(val)
          }else {
            let a=[];
            val.forEach(j => {
              if (userRole.map(i => i).includes(j)) {
                a.push(j);
              }
            });
            if (userRole.length===a.length){
              this.isOk(val)
            } else {
              this.setState({warningVsb:true,valData:val})
            }
          }
        }
      }
    });
  };
  openDrawer=(record)=>{
    const {id} = record;
    this.getUserList(id,1,10);
    this.setState({drawerVisible:true,clickItemId:id})
  };
  getUserList= async (id,page=1,size = 10)=>{
    const res = await this.props.dispatch({
      type:'role/userList',
      payload:{
        roleId:id,
        pageNum:page,
        pageSize:size
      }
    });
    const {code = 500, data ={}} = res || {};
    if(code === 0){
      this.setState({
        userListData:changeListPayQuery(data)
      })
    }
  };
  drawerOnClose=()=>{
    this.setState({drawerVisible:false,userListData:{},clickItemId:''})
  };
  drawerPageChange=(page)=>{
    const {clickItemId} = this.state;
    this.getUserList(clickItemId,page,10);
  };
  roleType=(v)=>{
    switch (v) {
      case 1:
        return '超级管理员';
      break;
      case 2:
        return '内置角色';
        break;
      case 3:
        return '自定义角色';
        break;
    }
  };
  permissions=(v)=>{
   if (isEmpty(v['valid_time'])) {
     return true
   }else {
     if (moment(moment().format('YYYY-MM-DD')).isBefore(moment(v['valid_time']).format('YYYY-MM-DD'))) {
       return true
     }else {
       return false
     }
   }
  };
  ok=()=>{
    this.cancel();
    const { valData }=this.state;
    this.isOk(valData)
  };
  cancel=()=>{
    this.setState({warningVsb:false})
  };
  isOk=async(val)=>{
    if (this.state['disabled'] === 'none') {
      ModalRole.close();
    }else {
      let { upItem }=this.state;
      let permissionList:Array<object>=[];
      if (isEmpty(val)){
        permissionList=[]
      } else {
        for(let obj of val){
          permissionList.push({id:obj})
        }
      }
      let res=await this.props.dispatch({
        type:'role/upUserRole',
        payload:{
          data:{
            roleId:upItem['id'],
            permissionList:permissionList.concat(this.state['parentKey'] || []),
          }
        }
      });
      if(res.code===0){
        Notice('操作提示',res.message,"check-circle","green");
        ModalRole.close();
      }
    }

  };
  isSearch = (value) => {
    this.setState({keyword:value},()=>{
      const {clickItemId,page} = this.state;
      this.getList(page,10);
    })
  };
  isChange=(e)=>{
    if (isEmpty(e.target.value)) {
      this.setState({keyword:e.target.value},()=>{
        const {clickItemId,page} = this.state;
        this.getList(page,10);
      })
    }
  };
  render(): React.ReactNode {
    const {role, loading:{effects = {}} = {},login:{menuTreeData=[]}} = this.props;
    const {list, pagination, lvArr, editInfo,userRole=[]}=role;
    const {type,drawerVisible ,userListData,filterHeight} = this.state;
    const {list:userList = [],pagination:userPagination ={}} = userListData;
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:100,
        render:(text,record,index)=>{
          return ((pagination['page']-1)*pagination['pageSize'])+index+1
        }
      },
      {
        title:'角色名称',
        dataIndex:'name',
        width:200,
      },
      {
        title:'创建人',
        dataIndex:'create_account',
        width:200,
      },
      {
        title:'创建时间',
        dataIndex:'create_time',
        width:200,
        render:(text)=>(<span>{moment(text).format('YYYY-MM-DD')}</span>)
      },
      {
        title:'角色类型',
        dataIndex:'role_type_code',
        width:200,
        render:(text)=>(<span>{this.roleType(text)}</span>)
      },
      {
        title:'有效时间',
        dataIndex:'valid_time',
        width:200,
        render:(text)=>(<span>{isEmpty(text)?'长期有效':moment(moment(text).format('YYYY-MM-DD')).isBefore(moment().format('YYYY-MM-DD'))?<span className={'del'}>已过期</span>:moment(text).format('YYYY-MM-DD')}</span>)
      },
      // {
      //   title:'使用人',
      //   dataIndex:'userNames',
      //   width:200,
      //   render:(text,record)=>(
      //     <span>
      //       {/* {
      //         !isEmpty(text) ? text.split(',').length >= 3 ? <a onClick={()=>this.openDrawer(record)}>{text.split(',').slice(0,2).toString()}...</a> :
      //           <a onClick={()=>this.openDrawer(record)}>{text}</a> : '暂未使用'
      //       } */}
      //     </span>
      //   )
      // },
      {
        title:'操作',
        dataIndex:'action',
        width:200,
        render:(text,record)=>{
          return(
            <span>
             {
              record['role_type_code'] === 3 &&
                <Fragment>

                  <a onClick={()=>this.openEdit(record)}>编辑</a>
                  {
                    this.permissions(record)&&
                    <Divider type="vertical" />
                  }
                  {
                   this.permissions(record)&&
                    <a onClick={()=>this.roleUp(record,'unset')}>权限</a>
                  }
                  {
                    // record['valid_time']===undefined&& <a onClick={()=>this.roleUp(record)}>查看权限</a>
                    // isEmpty(record['valid_time'])&&
                    //                     moment(moment().format('YYYY-MM-DD')).isBefore(moment(record['valid_time']).format('YYYY-MM-DD'))
                  }
                  <Divider type="vertical" />
                  <Popconfirm title="确定删除吗?" onConfirm={() => this.del(record)}>
                    <a className={'del'}>删除</a>
                  </Popconfirm>
                </Fragment>
              }
              {
                record['role_type_code'] !== 3 &&
                <Fragment>
                  {
                    record['valid_time']===undefined&& <a onClick={()=>this.roleUp(record,'none')}>查看权限</a>
                  }
                </Fragment>
              }
            </span>
          )
        }
      },
    ];
    const columns2 = [
      {
        title:'序号',
        dataIndex:'num',
        width:60,
        render:(text,record,index)=>{
          return ((userPagination['page']-1)*userPagination['pageSize'])+index+1
        }
      },
      {
        title:'登录名',
        dataIndex:'account',
      },
      {
        title:'姓名',
        dataIndex:'name',
      },
      {
        title:'所在单位',
        dataIndex:'orgName',
      }
    ];
    return (
      <div id={'roleList'}>
        <Tabs defaultActiveKey="1">
          <TabPane tab="基本信息" key="1"/>
        </Tabs>
        <ModalRole menuData={menuTreeData} userRole={userRole} editObj={this.state['upItem']} onOK={this.ModalRoleOk} disabled={this.state.disabled}/>
        <ListFilter>
          <div style={{textAlign:'right'}}>
            <Search
              placeholder="请输入检索关键词"
              onSearch={value => this.isSearch(value)}
              style={{ width: 200 }}
              onChange={(value)=>this.isChange(value)}
              className={styles.filter}
            />
            <Button type="primary" icon={<PlusOutlined />} style={{marginRight:8}} onClick={this.openAdd} htmlType={'button'}>新增角色</Button>
          </div>
        </ListFilter>
        {/*<NowOrg/>*/}
        <ListTable scroll={{y:filterHeight}} columns={columns} data={list} pagination={pagination} onPageChange={this.pageChange}/>
        <AddEdit
          title={type === 'edit' ? '编辑角色' :'新增角色'}
          handleOk={type === 'add' ?this.addOk : type === 'edit' ? this.editOk : (any)=>{} }
          lvArr={lvArr}
          wrappedComponentRef={(e)=>this['AddEdit1']=e}
          initData={type === 'add' ? {}: editInfo}
        />
        <Modal
          title={<span>警告</span>}
          destroyOnClose
          maskClosable={false}
          visible={this.state['warningVsb']}
          onOk={this.ok}
          onCancel={this.cancel}
        >
        <span>
          取消此项权限将会取消该角色所有下级的此项权限
        </span>
        </Modal>
        <Drawer
          title="使用人详情"
          placement="right"
          closable={false}
          onClose={this.drawerOnClose}
          visible={drawerVisible}
          width={500}
        >
          <div className={style.table}>
            <ListTable columns={columns2} data={userList} pagination={userListData['pagination']} onPageChange={this.pageUserChange}/>
          </div>
        </Drawer>
      </div>
    );
  }
}
