import request from "@/utils/request";
import qs from 'qs';

// export function addOrUpdate(params) {
//   return request(`/api/unit/township/addOrUpdate`,{
//     method:'POST',
//     body:params,
//   });
// }

// export function findByUnitCode(params) {
//   return request(`/api/unit/township/findByUnitCode?${qs.stringify(params)}`,{
//     method:'GET',
//   });
// }


// 获取列表
export function getList(params) {
  return request(`/api/unit/township/getList`,{
    method:'POST',
    body:params,
  });
}

// 删除
export function del(params) {
  return request(`/api/unit/township/del?${qs.stringify(params)}`,{
    method:'GET',
  });
}

// 新增或修改
export function addOrUpdate(params) {
  console.log('params===',params);
  
  return request(`/api/unit/township/addOrUpdate`,{
    method:'POST',
    body:params,
  });
}

// 根据code查找
export function findByCode(params) {
  return request(`/api/unit/township/findByCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}