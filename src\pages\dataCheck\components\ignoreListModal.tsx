import React, { Fragment, useEffect, useState, useImperativeHandle, useRef } from 'react';
import ListTable from '@/components/ListTable';
import { Modal, Button, Form, Input, Popconfirm } from 'antd';
import moment from 'moment';
import { ignoreList, logicDel, exportIgnoreList} from '@/pages/dataCheck/services';
import { getSession } from '@/utils/session';
import { fileDownloadbyUrl } from '@/utils/method';
import { checkCols, detailUrl } from './modalConfig';
import { connect } from 'dva';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _last from 'lodash/last';
import _get from 'lodash/get';


const { TextArea } = Input;

const ModalTL = React.forwardRef((props: any, ref) => {
  const {
    title = '详情',
    width = 1000,
    onOK,
  } = props;
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState([]);
  const [query, setQurey] = useState<any>({});
  const [pagination, setPagination] = useState({ pageSize: 10, total: 0, pageNum: 1, current: 1 });
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const org: any = getSession('org') || {};
  const ignoreReason: any = useRef();
  const addDevlopRef: any = useRef();



  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      if (!_isEmpty(query)) {
        setQurey(query);
        getList(query);
      }
    },
    clear: () => {
      // clear();
    },
  }));
  useEffect(() => {
  }, [JSON.stringify(query)])

  const getList = async (record, p = {}) => {
    setTableLoading(true);
    const { code = 500, data: { list = [], ...ohter } = {} } = await ignoreList({
      data: {
        orgCode: org?.orgCode,
        tableName: record?.table,
        pageNum: pagination?.pageNum,
        pageSize: pagination?.pageSize,
        id: record?.id,
        ...p,
      }
    });
    setTableLoading(false);
    if (code == 0) {
      setList(list);
      setPagination({ pageSize: ohter?.pageSize, total: ohter?.totalRow, pageNum: ohter?.pageNumber, current: ohter?.pageNumber, })
    }
  };
  const handleOk = () => {
    onOK && onOK(query);
    handleCancel();
  };
  const handleCancel = () => {
    setVisible(false);
    setDownloadLoading(false);
    clear();
    onOK && onOK();
  };
  const clear = () => {
    setQurey({});
    setList([]);
    setPagination({ pageSize: 10, total: 0, pageNum: 1, current: 1 })
  };

  const cancelIgnore = async (val) => {
    const res = await logicDel({
      data: {
        id: val.id,
      }
    });
    if (res.code === 0) {
      Tip.success('操作提示', '操作成功');
      getList(query, { pageNum: 1 });
    }
  }

  // 导出已忽略信息
  const exportInfo = async () => {
    setDownloadLoading(true);
    const { code = 500 } = await exportIgnoreList({
      data: {
        orgCode: org?.orgCode,
        tableName: query?.table,
        pageNum: pagination?.pageNum,
        pageSize: pagination?.pageSize,
        id: query?.id,
      }
    });
    setDownloadLoading(false);
    if (code == 0) {
      Tip.success('操作提示', '操作成功')
    }
  };

  let columns: any = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 50,
      render: (text, record, index) => {
        return (pagination['pageNum'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    {
      title: '组织名称',
      dataIndex: 'orgName',
      align: 'center',
    },
    {
      title: '名称',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '逻辑校核名称',
      dataIndex: 'logicCheckName',
      align: 'center',
    },
    {
      title: '原因',
      dataIndex: 'reason',
      align: 'center',
    },
    {
      title: '操作账号',
      dataIndex: 'updateAccount',
      align: 'center',
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      align: 'center',
      // render: (text, record) => {
      //   if(text){
      //     return moment(text).format('YYYY-MM-DD')
      //   }
      // }
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      render: (text, record) => {
        return (
          <React.Fragment>
            <Popconfirm title="确定要取消忽略吗？" onConfirm={() => cancelIgnore(record)}>
              <a className={'del'}>取消忽略</a>
            </Popconfirm>
          </React.Fragment>
        )
      }
    }
  ];
  switch (query.table) {
    case 'ccp_mem':
    case 'ccp_mem_all':
      columns = [
        {
          title: '序号',
          dataIndex: 'num',
          align: 'center',
          width: 50,
          render: (text, record, index) => {
            return (pagination['pageNum'] - 1) * pagination['pageSize'] + index + 1;
          },
        },
        {
          title: '组织名称',
          dataIndex: 'orgName',
          align: 'center',
        },
        {
          title: '名称',
          dataIndex: 'name',
          align: 'center',
        },
        {
          title: '出生日期',
          dataIndex: 'birthday',
          width: 100,
          render: (text, record) => {
            if(text){
              return moment(text).format('YYYY-MM-DD')
            }
          },
        },
        {
          title: '转正时间',
          dataIndex: 'fullMemberDate',
          width: 100,
          render: (text, record) => {
            if(text){
              return moment(text).format('YYYY-MM-DD')
            }
          },
        },
        {
          title: '入党时间',
          dataIndex: 'joinOrgDate',
          width: 100,
          render: (text, record) => {
            if(text){
              return moment(text).format('YYYY-MM-DD')
            }
          },
        },
        {
          title: '逻辑校核名称',
          dataIndex: 'logicCheckName',
          align: 'center',
        },
        {
          title: '原因',
          dataIndex: 'reason',
          align: 'center',
        },
        {
          title: '操作账号',
          dataIndex: 'updateAccount',
          align: 'center',
        },
        {
          title: '操作时间',
          dataIndex: 'createTime',
          align: 'center',
          render: (text, record) => {
            if(text){
              return moment(text).format('YYYY-MM-DD')
            }
          }
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 80,
          render: (text, record) => {
            return (
              <React.Fragment>
                <Popconfirm title="确定要取消忽略吗？" onConfirm={() => cancelIgnore(record)}>
                  <a className={'del'}>取消忽略</a>
                </Popconfirm>
              </React.Fragment>
            )
          }
        }
      ];
      break;
  }

  return (
    <Fragment>
      <Modal
        title={<Fragment>
          <span style={{marginRight:'10px'}}>已忽略信息</span><Button loading={downloadLoading} type='primary' onClick={exportInfo}>导出</Button>
        </Fragment>}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={width}
        footer={null}
        destroyOnClose={true}
      >
        <ListTable
          columns={columns}
          data={list}
          pagination={pagination}
          scroll={{
            y: 500,
          }}
          onPageChange={(pageNum, pageSize) => getList(query, { pageNum, pageSize })}
          rowKey={'id'} />
      </Modal>
    </Fragment>
  )
});

export default connect(({ commonDict, memBasic, memDevelop, org, unit }: any) => ({ commonDict, memBasic, memDevelop, org, unit }), undefined, undefined, { forwardRef: true })(ModalTL)
