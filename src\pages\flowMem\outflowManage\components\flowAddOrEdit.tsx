// 流动人员详情-流出登记
import React, { Fragment } from 'react';
import { connect } from 'dva';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import { Form } from '@ant-design/compatible';
import { Input, Modal, InputNumber, Button, Select, message } from 'antd';
import moment from 'moment';
import _cloneDeep from 'lodash/cloneDeep';
import _isArray from 'lodash/isArray';
import { getSession } from '@/utils/session';
import { formLabel, findDictCodeName } from '@/utils/method';
import DictSelect from '@/components/DictSelect';
import DictTreeSelect from '@/components/DictTreeSelect';
import DictArea from '@/components/DictArea';
import Tip from '@/components/Tip';
import SearchOrg from '@/components/SearchOrg';
import Date from '@/components/Date';
import OrgSelect from '@/components/OrgSelect';
import ListTable from '@/components/ListTable';
import MemSelect from '@/components/MemSelect';
import { findApprovalOrg, findApprovalFlowOrg, outManageCheck, outManageCheckPhone, findOrgFlowController, outManageRegister, orgFindBranchOrg } from '../../service/index';

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

// @ts-ignore
@connect(
  ({ unit, commonDict, loading, flowMem }) => ({
    flowMem,
    unit,
    commonDict,
    loading: loading.effects['unit/getList'],
  }),
  undefined,
  undefined,
  { forwardRef: true },
)
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      flowOutStatus: undefined,
      outOrgBranchNameList: [],
      outAdministrativeDivisionCodeDisabled: false,  //行政区划是否可选
    };
  }

  open = (type: string, record?: any) => {
    let title = '流出党员登记';
    console.log("11111111111111111111", moment().subtract(60, 'days'));
    this.setState({
      modalType: type,
      modalTitle: title,
      visible: true,
      basicInfo: {},
    });
    // if(!_isEmpty(record.code)){
    //   this.getBasicInfo(record.code)
    // }
  };
  getBasicInfo = async (code: string) => {
    // const { code: rescode = 500, data = {} } = await outManageFind({
    //   code,
    // });
    // if (rescode == 0) {
    //   this.setState({
    //     basicInfo: data,
    //   });
    // }
  };

  handleOk = async () => {
    await this.setState({
      confirmLoading: true,
    });
    const { onOk } = this.props;
    const { basicInfo } = this.state;
    const org: any = getSession('org');
    // 再次检查所选人员能否进行流出登记
    // await this.memCheck(this.props.form.getFieldValue('codeList'));
    console.log(this.props.form.getFieldValue('codeList'), 'xxxxxxxxxxx888888888888');
    let memList = this.props.form.getFieldValue('codeList') ?? [];
    let codeList = memList.map((it, i) => {
      return it.code;
    });
    outManageCheck({
      data: {
        codeList,
      },
    }).then((res) => {
      if (res.code == 0) {
        if (!res.data) {
          this.props.form.validateFields((error, values) => {
            console.log('🚀 ~ index ~ this.props.form.validateFieldsAndScroll ~ values:', values, error);
            // if (values['outTime']) {
            //   console.log(moment(values['outTime']).format('YYYY-MM-DD HH:mm:ss'));
            // }
            if (error) {
              this.setState({
                confirmLoading: false,
              });
            } else {
              let finalValuse = _cloneDeep(values);
              if (!_isEmpty(finalValuse['outTime'])) {
                finalValuse['outTime'] = moment(finalValuse['outTime']).valueOf();
              }
              if (!_isEmpty(finalValuse['registerTime'])) {
                finalValuse['registerTime'] = moment(finalValuse['registerTime']).valueOf();
              }
              if (!_isEmpty(finalValuse['partyExpensesOutTime'])) {
                finalValuse['partyExpensesOutTime'] = moment(finalValuse['partyExpensesOutTime']).valueOf();
              }
              if (!_isEmpty(finalValuse['lostContactTime'])) {
                finalValuse['lostContactTime'] = moment(finalValuse['lostContactTime']).valueOf();
              }
              if (!_isEmpty(finalValuse['codeList'])) {
                finalValuse['codeList'] = finalValuse['codeList'].map((it, i) => {
                  return it.code;
                });
              }
              if (!_isEmpty(finalValuse['pairedContact'])) {
                finalValuse['pairedContactCode'] = values['pairedContact'][0].code || undefined;
                finalValuse['pairedContact'] = values['pairedContact'][0].name || undefined;
              }
              if (!_isEmpty(finalValuse['inOrgD04Code'])) {
                finalValuse['inOrgD04Name'] = values['inOrgD04Code'].name || undefined;
                finalValuse['inOrgD04Code'] = values['inOrgD04Code'].key || undefined;
              }
              //第三个搜索框没有选择时，把第二个框选择的数据中的orgCode 的值给out_org_branch_org_code传到后台
              // if (!_isEmpty(finalValuse['outOrgCode'])) {
              //   finalValuse['outOrgName'] = values['outOrgCode'].name || undefined;
              //   finalValuse['outOrgCode'] = values['outOrgCode'].code || undefined;
              // }
              if (!_isEmpty(finalValuse['outOrgCode'])) {
                finalValuse['outOrgName'] = values['outOrgCode'].name || undefined;
                finalValuse['outOrgCode'] = values['outOrgCode'].code || undefined;
                finalValuse['outOrgContact'] = values['outOrgCode'].contacter || undefined;
                finalValuse['outOrgContactPhone'] = values['outOrgCode'].contactPhone || undefined;
              }
              if (!_isEmpty(finalValuse['outOrgBranchName']) && values['outPlaceCode'] == 1) {
                if (finalValuse['outOrgBranchName'].includes('code')) {
                  const outOrgBranchName = JSON.parse(finalValuse['outOrgBranchName']);
                  finalValuse['outOrgBranchName'] = outOrgBranchName.name || undefined;
                  finalValuse['outOrgBranchOrgCode'] = outOrgBranchName.orgCode || undefined;
                  finalValuse['outOrgBranchCode'] = outOrgBranchName.code || undefined;
                }
              } else {
                if (!_isEmpty(finalValuse['outOrgCode'])) {
                  finalValuse['outOrgBranchOrgCode'] = values['outOrgCode'].orgCode || undefined;
                }
              }
              // 如果是5，流动党支部变为搜索框,是一个对象,传给后端一个名字
              if (!_isEmpty(finalValuse['outOrgBranchName']) && values['outPlaceCode'] == 5) {
                console.log("finalValuse['outOrgBranchName']", finalValuse['outOrgBranchName']);
                finalValuse['outOrgBranchName'] = values['outOrgBranchName'].name || undefined;
                finalValuse['outOrgBranchOrgCode'] = values['outOrgBranchName'].orgCode || undefined;
                finalValuse['outOrgBranchCode'] = values['outOrgBranchName'].code || undefined;
                finalValuse['outOrgContact'] = values['outOrgBranchName'].contacter || undefined;
                finalValuse['outOrgContactPhone'] = values['outOrgBranchName'].contactPhone || undefined;
              }
              const { onlyShow, memOrgPhone, memOrgName, ...finalData } = finalValuse;
              console.log('finalData===', finalData);
              outManageRegister({
                data: {
                  ...finalData,
                  orgCode: org.code || '',
                  isProvince: this.state.orgSearchType,
                  flowOutStatus: this.state.flowOutStatus,
                },
              }).then((res) => {
                const { code, data } = res;
                if (code === 0) {
                  Tip.success('操作提示', '操作成功');
                  this.handleCancel();
                  onOk && onOk();
                }
              });
              // this.props
              //   .dispatch({
              //     type: 'flowMem/outManageAdd',
              //     payload: {
              //       data: {
              //         ...finalData,
              //         orgCode: org.code || '',
              //         isProvince: this.state.orgSearchType,
              //       },
              //     },
              //   })
              //   .then((res) => {
              //     this.setState({
              //       confirmLoading: false,
              //     });
              //     if (res.code === 0) {
              //       Tip.success('操作提示', '操作成功');
              //       this.handleCancel();
              //       onOk && onOk();
              //     }
              //   });
            }
          });
        } else {
          Tip.error('操作提示', res.data || '选人失败');
        }
      }
    });
  };
  handleCancel = () => {
    this.setState({
      visible: false,
      modalTitle: '',
      memData: [],
      basicInfo: {},
      outPlaceCode: '',
      orgSearchType: '', // 组织查询选择器返回的type
      orgSearchCode: undefined,
      flowOutStatus: undefined,
      readOnly: false,
      administrativeRegion: undefined,
      confirmLoading: false,
    });
  };
  // 检查当前选中人员是否能进行流出登记
  memCheck = async (memList) => {
    if (!_isArray(memList)) {
      return;
    }
    const { getFieldValue, setFieldsValue } = this.props.form;
    let codeList = memList.map((it, i) => {
      return it.code;
    });
    let orgCodeList = memList.map((it, i) => {
      return it.orgCode;
    });
    const { code = 500, data = '' } = await outManageCheck({
      data: {
        codeList,
      },
    });
    if (code == 0) {
      if (!_isEmpty(data)) {
        Tip.error('操作提示', data || '选人失败');
        this['mem'].clearAll();
        setFieldsValue({ codeList: undefined });
        this.setState({
          memData: [],
        });
      } else {
        this.setState(
          {
            memData: memList,
          },
          () => {
            // 这里是单独去获取	党支部联系电话 通过orgCodeList里面的orgCode去请求,然后在比对放入页面
            this.memCheckPhone(orgCodeList);
          },
        );
      }
    }
  };
  memCheckPhone = async (codeList) => {
    const { memData } = this.state;
    const memPhoneData: any[] = [];
    const { code = 500, data = '' } = await outManageCheckPhone({
      data: {
        orgCodeList: codeList,
      },
    });
    if (code == 0) {
      if (!_isEmpty(data)) {
        memData.forEach((item: any) => {
          return data.data.forEach((item1): any => {
            if (item1.code == item.orgCode) {
              memPhoneData.push({
                ...item,
                contactPhone: item1.contactPhone,
              });
            }
          });
        });
        // console.log(memPhoneData);
        this.setState({
          memData: memPhoneData,
        });
      }
    }
  };
  memChange = (data) => {
    console.log('🚀 ~ index ~ data:', data);

    this.memCheck(data);
    // this.setState({
    //   memData: data,
    // });
  };
  del = (item) => {
    const { getFieldValue, setFieldsValue } = this.props.form;
    let { memData } = this.state;
    // let memData =  getFieldValue('codeList');
    memData = memData.filter((obj) => obj['id'] !== item['id']);
    setFieldsValue({ codeList: memData });
    this.setState({
      memData,
    });
    let names: Array<string> = [];
    for (let obj of memData) {
      names.push(obj['name']);
    }
    this['mem'].setState({
      value: names.join('，'),
      data: memData,
    });
  };
  getTime = (rule, value, callback) => {
    if (!value) {
      callback('外出时间必填');
    }
    let now = moment().subtract(180, 'days').format('YYYY-MM-DD');
    let time = moment(value).format('YYYY-MM-DD');
    if (!moment(time).isBefore(now)) {
      callback('外出日期只能填写早于当前日期180天');
    } else {
      callback();
    }
    callback();
  };

  outOrgBranchNameSelect = () => {
    const { outOrgBranchNameList = [] } = this.state;
    console.log('11111111111111', outOrgBranchNameList);
    return (
      <Select placeholder="请选择">
        {outOrgBranchNameList.length > 0 &&
          outOrgBranchNameList.map((item: any, index) => {
            return <Select.Option value={JSON.stringify(item)}>{item.name}</Select.Option>;
          })}
      </Select>
    );
  };

  // 流向基层党工委 当流动单位里面type为1的时候 流动党支部变为下拉框然后去请求数据
  getSelectOutOrgBranchName = async (outOrgCode, orgCode) => {
    console.log("🚀 ~ index ~ getSelectOutOrgBranchName= ~ orgCode:", getSession("org"))
    const org = getSession("org") || {}

    if (outOrgCode) {
      const { code, data } = await orgFindBranchOrg({
        data: {
          code: orgCode,
          pageNum: 1, //选择框写死的
          pageSize: 999, //选择框写死的
          excludeOrgCode: org?.orgCode
        },
      });
      if (code == 0) {
        this.setState({
          outOrgBranchNameList: data.list
        });
      }
    }
  };

  render() {
    const { filterHeight, loading, children, tipMsg = {}, basicInfo = {}, commonDict } = this.props;
    const {
      visible,
      memData = [],
      modalTitle = '',
      dataInfo = {},
      outPlaceCode = '',
      readOnly = false,
      timeKey = '123',
      administrativeRegion = '', // 行政区划
      confirmLoading = false,
      itemsDisabled = [],
      outAdministrativeDivisionCodeDisabled = false,  //行政区划是否可选
    } = this.state;
    const { getFieldDecorator, setFieldsValue, getFieldValue, resetFields } = this.props.form;
    const org: object = getSession('org') || {};
    const noOperationColumns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'name',
        align: 'center',
        width: 60,
      },
      {
        title: '性别',
        dataIndex: 'sexCode',
        align: 'center',
        width: 40,
        render: (text) => {
          return <span> {text === '1' ? '男' : '女'} </span>;
        },
      },
      // {
      //   title: '身份证号码',
      //   dataIndex: 'idcard',
      //   align: 'center',
      //   width: 100,
      // },
      {
        title: '联系电话',
        dataIndex: 'phone',
        align: 'center',
        width: 100,
      },
      {
        title: '所在党支部',
        dataIndex: 'orgName',
        width: 160,
      },
      {
        title: '党支部联系电话',
        dataIndex: 'contactPhone',
        width: 160,
      },
      // {
      //   title: '工作岗位',
      //   dataIndex: 'd09Name',
      //   width: 80,
      // },
    ];
    const columns = [
      ...noOperationColumns,
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return (
            <span>
              <a className={'del'} onClick={() => this.del(record)}>
                删除
              </a>
            </span>
          );
        },
      },
    ];
    return (
      <div>
        {children
          ? React.cloneElement(children as any, {
            onClick: this.open,
            key: 'container',
          })
          : null}
        <Modal
          bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
          destroyOnClose
          maskClosable={false}
          width={1100}
          title={modalTitle}
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          confirmLoading={confirmLoading}
        >
          <Form {...formItemLayout}>
            <FormItem label={formLabel('选择党员', tipMsg['codeList'])}>
              {getFieldDecorator('codeList', {
                // initialValue: '',
                rules: [{ required: true, message: '请选择党员' }],
              })(
                <MemSelect
                  org={getSession('org')}
                  // initValue={memInfo['memName']}
                  onChange={this.memChange}
                  ref={(e) => (this['mem'] = e)}
                  checkType={'checkbox'}
                />,
              )}
            </FormItem>
            <FormItem label={formLabel('党员信息', tipMsg['onlyShow'])}>
              {getFieldDecorator('onlyShow', {
                initialValue: '',
                rules: [{ required: false, message: '请选择党员' }],
              })(<ListTable scroll={{ x: '100%' }} rowKey={'code'} columns={columns} data={memData || []} pagination={false} />)}
            </FormItem>
            <FormItem label={formLabel('外出地点', tipMsg['outPlaceCode'])}>
              {getFieldDecorator('outPlaceCode', {
                // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                rules: [{ required: true, message: '请选择外出地点' }],
              })(
                <DictSelect
                  disabled={readOnly}
                  codeType={'dict_d148'}
                  onChange={(e) => {
                    let arr = [];
                    const onlyShow = getFieldValue('onlyShow');
                    if (e == '2') {
                      arr = commonDict['dict_d151_flow'].filter((i) => i.key.startsWith('520') || i.key.startsWith('522')).map((j) => j.key);
                    }
                    if (e == '1') {
                      this.setState({ outAdministrativeDivisionCodeDisabled: true })
                    } else {
                      this.setState({ outAdministrativeDivisionCodeDisabled: false })
                    }
                    this.setState({
                      outPlaceCode: e,
                      itemsDisabled: arr,
                      timeKey: String(Math.random()),
                      administrativeRegion: undefined,
                    });
                    setFieldsValue({
                      outAdministrativeDivisionCode: undefined,
                      outOrgBranchName: undefined,
                      outOrgCode: undefined
                    });
                  }}
                // itemsDisabled={['2','4']}
                />,
              )}
            </FormItem>
            {outPlaceCode == 1 && (
              <FormItem label={formLabel('流入党委', tipMsg['outOrgCode'])}>
                {getFieldDecorator('outOrgCode', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [{ required: true, message: '请选择' }],
                })(
                  <SearchOrg
                    // disabled={readOnly}
                    onChange={(e) => {
                      // console.log('组织查询选择==', e);
                      const { type = '', insideCity = '', orgCode = '', administrativeRegion = '' } = e;
                      // type 0 省外， 1 省内 ；insideCity  0 市外， 1 市内； administrativeRegion 行政区划编码
                      this.setState({
                        orgSearchType: type,
                        orgSearchCode: orgCode,
                        administrativeRegion,
                        timeKey: String(Math.random()),
                      });
                      if (e.type == 1) {
                        this.getSelectOutOrgBranchName(e.type, e.code);
                        this.setState({ outAdministrativeDivisionCodeDisabled: true })
                      } else {
                        this.setState({ outAdministrativeDivisionCodeDisabled: false })
                      }
                      // setFieldsValue({ outOrgBranchCode: undefined });
                      setFieldsValue({ outOrgBranchName: undefined });
                      setFieldsValue({ outAdministrativeDivisionCode: e?.divisionCode == '' ? undefined : e?.divisionCode });
                    }}
                    // ohterAction={findApprovalFlowOrg}
                    ohterAction={findApprovalOrg}
                    showOtherInfo={'transfer'}
                    backType={'object'}
                    params={{
                      orgCode: org['orgCode'],
                      d01Code: 811, //写死的流动党委key
                    }}
                  />,
                )}
              </FormItem>
            )}
            {outPlaceCode === '1' &&
              (() => {
                const outOrgCodeType = getFieldValue('outOrgCode')?.type == 1;
                return (
                  <FormItem label={formLabel('流入党支部', tipMsg['outOrgBranchName'])}>
                    {getFieldDecorator('outOrgBranchName', {
                      // initialValue: _isEmpty(basicInfo)?undefined:basicInfo['d48Code'],
                      rules: [{ required: outOrgCodeType ? false : true, message: '请输入' }],
                    })(outOrgCodeType ? this.outOrgBranchNameSelect() : <Input placeholder="请输入" />)}
                  </FormItem>
                );
              })()}
            {outPlaceCode === '5' && (
              <FormItem label={formLabel('流入党支部', tipMsg['outOrgBranchName'])}>
                {getFieldDecorator('outOrgBranchName', {
                  // initialValue: _isEmpty(basicInfo)?undefined:basicInfo['d48Code'],
                  rules: [{ required: true, message: '请输入' }],
                })(
                  <SearchOrg
                    // disabled={readOnly}
                    onChange={(e) => {
                      // console.log('组织查询选择==', e);
                      const { type = '', insideCity = '', orgCode = '', administrativeRegion = '', flowOutStatus } = e;
                      // type 0 省外， 1 省内 ；insideCity  0 市外， 1 市内； administrativeRegion 行政区划编码
                      this.setState({
                        orgSearchType: type,
                        orgSearchCode: orgCode,
                        administrativeRegion,
                        flowOutStatus,
                        timeKey: String(Math.random()),
                      });
                      // setFieldsValue({ outOrgBranchCode: undefined });
                      // setFieldsValue({ outAdministrativeDivisionCode: administrativeRegion });
                    }}
                    ohterAction={findApprovalFlowOrg}
                    showOtherInfo={'transfer'}
                    backType={'object'}
                    params={{
                      // pageNum: 1,
                      // pageSize: 10,
                      // orgName: org['name'],
                      orgCode: org['orgCode'],
                      // d01Code: 813, //写死的流动党支部key
                      // type:0
                    }}
                  />,
                )}
              </FormItem>
            )}
            {outPlaceCode != 3 &&
              outPlaceCode != 4 &&
              (() => {
                const outOrgCode = getFieldValue('outOrgCode');
                // console.log('outOrgCodeoutOrgCodeoutOrgCodeoutOrgCodeoutOrgCode', outOrgCode);
                const initValue = outOrgCode ? outOrgCode?.divisionCode : _isEmpty(administrativeRegion) ? undefined : administrativeRegion;
                // console.log("🚀 ~ index ~ render ~ outOrgCode:", outOrgCode, initValue)
                return (
                  <FormItem key={timeKey} label={formLabel('流入行政区', tipMsg['outAdministrativeDivisionCode'])}>
                    {getFieldDecorator('outAdministrativeDivisionCode', {
                      initialValue: initValue == '' ? undefined : initValue,
                      rules: [
                        // 外出地点选2，行政区划必填
                        {
                          required: (outPlaceCode === '2' || !outAdministrativeDivisionCodeDisabled) ? true : false,
                          message: '请选择所在行政区域',
                        },
                      ],
                    })(
                      <DictTreeSelect
                        parentDisable={true}
                        placeholder={'请选择行政区域'}
                        initValue={initValue == '' ? undefined : (initValue == "" ? undefined : initValue)}
                        // disabled={readOnly}
                        // disabled={outPlaceCode === '1' ? true : false}
                        disabled={outAdministrativeDivisionCodeDisabled}
                        codeType={'dict_d151_flow'}
                        itemsDisabled={itemsDisabled}
                      />,
                    )}
                  </FormItem>
                );
              })()}
            {/* outPlaceCode等于2的时候需要和流入行政区交换位置所以这单独判断 */}
            {outPlaceCode === '2' && (
              <FormItem label={formLabel('流入党支部', tipMsg['outOrgBranchName'])}>
                {getFieldDecorator('outOrgBranchName', {
                  // initialValue: _isEmpty(basicInfo)?undefined:basicInfo['d48Code'],
                  rules: [{ required: true, message: '请输入' }],
                })(<Input placeholder="请输入" />)}
              </FormItem>
            )}
            {/* 外出地点-不掌握流向 */}
            {outPlaceCode == 4 && (
              <Fragment>
                <FormItem label={formLabel('失去联系情形', tipMsg['lostContactCode'])}>
                  {getFieldDecorator('lostContactCode', {
                    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                    rules: [{ required: false, message: '请选择' }],
                  })(
                    <DictSelect
                      disabled={readOnly}
                      // codeType={'dict_d18'}  //以前的
                      codeType={'dict_d206'}
                    // backType={'object'}
                    // initValue={this.state.account2?.id}
                    />,
                  )}
                </FormItem>
              </Fragment>
            )}
            {outPlaceCode == 4 &&
              (function () {
                const lostContactCode = getFieldValue('lostContactCode');
                return (
                  lostContactCode && (
                    <Fragment>
                      <FormItem label={formLabel('失去联系日期', tipMsg['lostContactTime'])}>
                        {getFieldDecorator('lostContactTime', {
                          // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                          rules: [{ required: true, message: '请选择' }],
                        })(<Date disabled={readOnly} />)}
                      </FormItem>
                    </Fragment>
                  )
                );
              })()}
            {/* 外出地点-无固定地点 || 不掌握流向 */}
            {(outPlaceCode == 3 || outPlaceCode == 4) && (
              <FormItem label={formLabel('情况说明', tipMsg['outOrgRemarks'])}>
                {getFieldDecorator('outOrgRemarks', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [{ required: true, message: '请填写' }],
                })(<Input.TextArea disabled={readOnly} placeholder="请对情况进行详细说明" showCount maxLength={100} rows={4} />)}
              </FormItem>
            )}
            {/* 2025/2/27 谭明武  外出原因为5的时候也放出来*/}
            {/* {outPlaceCode != 5 && ( */}
            {(<FormItem label={formLabel('外出原因', tipMsg['flowReasonCode'])}>
              {getFieldDecorator('flowReasonCode', {
                // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                rules: [{ required: true, message: '请选择外出原因' }],
              })(
                <DictSelect
                  disabled={readOnly}
                  codeType={'dict_d204'}
                // backType={'object'}
                // initValue={this.state.account2?.id}
                />,
              )}
            </FormItem>
            )}
            <FormItem label={formLabel('外出日期', tipMsg['outTime'])}>
              {getFieldDecorator('outTime', {
                // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                rules: [
                  { required: true, message: '请填写外出日期' },
                  // { validator: this.getTime }
                ],
              })(<Date disabled={readOnly} />)}
            </FormItem>
            {outPlaceCode != 3 &&
              outPlaceCode != 4 && <FormItem label={formLabel('登记日期', tipMsg['registerTime'])}>
                {getFieldDecorator('registerTime', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [
                    { required: true, message: '请填写登记日期' },
                    // { validator: this.getTime }
                  ],
                })(<Date disabled={readOnly} endTime={moment()} startTime={moment().subtract(60, 'days')}
                  tipMessage="登记日期只能填写当前时间往前60天以内的时间！"
                />)}
              </FormItem>}
            {outPlaceCode == 1 && (
              <FormItem label={formLabel('外出地点(党组织)补充说明', tipMsg['outOrgRemarks'])}>
                {getFieldDecorator('outOrgRemarks', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [{ required: true, message: '请填写' }],
                })(<Input.TextArea disabled={readOnly} placeholder="请对情况进行详细说明" showCount maxLength={100} rows={4} />)}
              </FormItem>
            )}
            {(outPlaceCode == 1 || outPlaceCode == 2 || outPlaceCode == 5) && (
              <FormItem label={formLabel('流入地党组织单位类型', tipMsg['inOrgD04Code'])}>
                {getFieldDecorator('inOrgD04Code', {
                  // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                  rules: [{ required: true, message: '流入地党组织单位类型' }],
                })(
                  <DictTreeSelect
                    disabled={readOnly}
                    parentDisable={true}
                    codeType={'dict_d205'}
                    backType={'object'}
                  // initValue={this.state.account2?.id}
                  />,
                )}
              </FormItem>
            )}
            {/* 流出登记时，外出地点等于1或2 && 流入地党组织单位类型选择企业4开头的字典表时,添加经济类型 */}
            {/* {(outPlaceCode == 1 || outPlaceCode == 2) &&
              getFieldValue('inOrgD04Code')?.key?.startsWith('4') && (
                <FormItem label={formLabel('经济类型', tipMsg['inUnitD16Code'])}>
                  {getFieldDecorator('inUnitD16Code', {
                    // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                    rules: [{ required: true, message: '请选择经济类型' }],
                  })(
                    <DictTreeSelect
                      disabled={readOnly}
                      parentDisable={true}
                      codeType={'dict_d16'}
                      // backType={'object'}
                      // initValue={this.state.account2?.id}
                    />,
                  )}
                </FormItem>
              )} */}
            <FormItem label={formLabel('流出地党费交至日期', tipMsg['partyExpensesOutTime'])}>
              {getFieldDecorator('partyExpensesOutTime', {
                // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                rules: [{ required: true, message: '流出地党费交至日期' }],
              })(<Date />)}
            </FormItem>
            <FormItem label={formLabel('流动党员活动证', tipMsg['isHold'])}>
              {getFieldDecorator('isHold', {
                // initialValue:isEmpty(dataInfo)?null:dataInfo.account,
                rules: [{ required: true, message: '请选择' }],
              })(
                <Select disabled={readOnly} placeholder="请选择" style={{ width: '100%' }}>
                  <Select.Option value={1}>已发放</Select.Option>
                  <Select.Option value={0}>未发放</Select.Option>
                </Select>,
              )}
            </FormItem>
            {/* ********  屏蔽 */}
            {/* {outPlaceCode != 5 && outPlaceCode != 1 && (
              <FormItem label={formLabel('流出地党支部', tipMsg['memOrgName'])}>
                {getFieldDecorator('memOrgName', {
                  initialValue: _isEmpty(dataInfo) ? org['name'] : dataInfo.account,
                  rules: [{ required: false, message: '流出地党支部' }],
                })(<Input disabled />)}
              </FormItem>
            )}
            {outPlaceCode != 5 && outPlaceCode != 1 && (
              <FormItem label={formLabel('流出地党支部联系电话', tipMsg['memOrgPhone'])}>
                {getFieldDecorator('memOrgPhone', {
                  initialValue: _isEmpty(dataInfo) ? org['contactPhone'] : dataInfo.account,
                  rules: [{ required: false, message: '请输入流出地党支部联系电话' }],
                })(<Input placeholder="请输入" disabled />)}
              </FormItem>
            )} */}
            {/*
            新加
            */}
            {/* <FormItem label={formLabel('结对联系人', tipMsg['pairedContact'])}>
              {getFieldDecorator('pairedContact', {
                // initialValue: _isEmpty(dataInfo) ? org['pairedContact'] : dataInfo.pairedContact,
                rules: [{ required: true, message: '请输入结对联系人' }],
              })(<Input placeholder="请输入" />)}
            </FormItem> */}
            <FormItem label={formLabel('结对联系人', tipMsg['pairedContact'])}>
              {getFieldDecorator('pairedContact', {
                // initialValue: '',
                rules: [{ required: true, message: '请结对联系人' }],
              })(
                <MemSelect
                  org={getSession('org')}
                  // initValue={memInfo['memName']}
                  onChange={(e: any) => {
                    setFieldsValue({
                      pairedContactPhone: e[0]?.phone,
                    });
                  }}
                  ref={(e) => (this['pairedContact'] = e)}
                  checkType={'radio'}
                />,
              )}
            </FormItem>
            {(() => {
              const phone = getFieldValue('pairedContact') || [];
              // console.log('9999999999999999999', phone, phone[0]?.phone);
              return (
                <FormItem label={formLabel('结对联系方式', tipMsg['pairedContactPhone'])}>
                  {getFieldDecorator('pairedContactPhone', {
                    initialValue: _isEmpty(dataInfo) ? undefined : phone[0]?.phone,
                    rules: [{ required: true, message: '请输入结对联系方式' }],
                  })(<Input placeholder="请输入" disabled />)}
                </FormItem>
              );
            })()}
            {/* <FormItem label={formLabel('是否为农民工', tipMsg['isFarmer'])}>
              {getFieldDecorator('isFarmer', {
                initialValue: '0',
                rules: [{ required: true, message: '请选择是否为农民' }],
              })(
                <Select>
                  <Select.Option value={'1'}>是</Select.Option>
                  <Select.Option value={'0'}>否</Select.Option>
                </Select>
              )}
            </FormItem> */}
          </Form>
        </Modal>
      </div>
    );
  }
}

export default Form.create()(index);
