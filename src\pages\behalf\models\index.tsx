import {listPageModel, ListPageStore} from "@/utils/common-model";
import modelExtend from "dva-model-extend";
import {
  getList,
  addContact,
  findByCode,
  updateContact,
  delContact,
  findByUnitCode,
  checkElectDate,
  addElect,
  getjcList,
  delElect,
  getUnitByOrgCode,
  getSubElectList,
  addRepresentative,
  representativeList,
  bfFindByCode,
  representativeUp,
  delRepresentative,
  updateElect, getQueryList,
} from '../service';
import Notice from '../../../components/Notice';
import { add } from '@/pages/flowMem/service';
import { getSession } from '@/utils/session';
const behalf=modelExtend(listPageModel,{
  namespace:'behalf',
  state:{
  },
  subscriptions:{
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if(pathname==='/behalf'){
          const org=getSession('org') || {};
          const defaultParas={
            pageNum:1,
            pageSize:10,
          };
          const dictData=['dict_d44','dict_d45','dict_d46','dict_d61'];
          for(let obj of dictData){
            dispatch({
              type:'commonDict/getDictTree',
              payload:{
                data:{
                  dicName:obj
                }
              }
            });
          }
          dispatch({
            type:'getList',
            payload:{
              data:{
                orgCode:org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
      });
    }
  },
  effects:{
    *contactAgencyList({payload},{call,put}){
      const info = yield call(getList, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'querySuccess',
          payload: {
            list:list,
            pagination:{
              current:pagination['pageNumber'],
              pageSize:pagination['pageSize'],
              total:pagination['totalRow'],
            }}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },
    *contactAgencyAdd({payload},{call,put}){
      const info = yield call(addContact, payload);
      return Promise.resolve(info);
    },
    *contactAgencyDetail({payload},{call,put}){
      const info = yield call(findByCode, payload);
      return Promise.resolve(info);
    },
    *contactAgencyUp({payload},{call,put}){
      const info = yield call(updateContact, payload);
      return Promise.resolve(info);
    },
    *contactAgencyDel({payload},{call,put}){
      const info = yield call(delContact, payload);
      return Promise.resolve(info);
    },
    //-----------------------届次信息-----------------------------

    *jcFindByUnitCode({payload},{call,put}){
      const info = yield call(findByUnitCode, payload);
      return Promise.resolve(info);
    },

    *jcCheckElectDate({payload},{call,put}){//时间校验
      const info = yield call(checkElectDate, payload);
      return Promise.resolve(info);
    },

    *jcAddElect({payload},{call,put}){
      const info = yield call(addElect, payload);
      return Promise.resolve(info);
    },

    *jcList({payload},{call,put}){
      const info = yield call(getQueryList, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'updateState',
          payload: {
            list1:list,
            pagination1:{
              current:pagination['pageNumber'],
              pageSize:pagination['pageSize'],
              total:pagination['totalRow'],
        }}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },

    *jcDel({payload},{call,put}){//时间校验
      const info = yield call(delElect, payload);
      return Promise.resolve(info);
    },

    *jcUp({payload},{call,put}){//时间校验
      const info = yield call(updateElect, payload);
      return Promise.resolve(info);
    },
    //------------------------------党代表----------------------------------
    *bfUnitByOrgCode({payload},{call,put}){
      const info = yield call(getUnitByOrgCode, payload);
      return Promise.resolve(info);
    },

    *bfSubElectList({payload},{call,put}){
      const info = yield call(getSubElectList, payload);
      return Promise.resolve(info);
    },

    *bfAdd({payload},{call,put}){
      const info = yield call(addRepresentative, payload);
      return Promise.resolve(info);
    },

    *bfList({payload},{call,put}){
      const info = yield call(representativeList, payload);
      const { data:{ list=[],...pagination } ={},message='操作失败',code=null } = info;
      if (code === 0){
        yield put({
          type: 'updateState',
          payload: {
            list2:list,
            pagination2:{
              current:pagination['pageNumber'],
              pageSize:pagination['pageSize'],
              total:pagination['totalRow'],
            }}
        });
      }else {
        Notice("操作提示",message,"exclamation-circle-o","orange");
      }
    },

    *bfFindByCode({payload},{call,put}){
      const info = yield call(bfFindByCode, payload);
      return Promise.resolve(info);
    },

    *bfUp({payload},{call,put}){
      const info = yield call(representativeUp, payload);
      return Promise.resolve(info);
    },

    *bfDel({payload},{call,put}){
      const info = yield call(delRepresentative, payload);
      return Promise.resolve(info);
    },
  },
  reducers: {
    success(state, {payload}) {
      return {...state, ...payload};
    },
  }
});
export default behalf
