import React, { useState, useEffect, useRef, Fragment } from 'react';
import { Tabs, Button } from 'antd';
import moment from 'moment';
import CommonSerchPage from '@/components/CommonSerchPage';
import { searchMem, searchFlowMem,dataQuery } from '../services';
import Searches from '../components/analysisForm';
import { getSession } from '@/utils/session';
import AddEdit from '@/pages/mem/manage/components/membasic/AddEdit';
import ExportInfo from '@/components/Export';
import AddDevlop from '@/pages/developMem/develop/components/Add';
import { _history as router } from "@/utils/method";
import { connect } from 'dva';
import qs from 'qs';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray'
import _get from 'lodash/get';
const TabPane = Tabs.TabPane;

const index = (props: any) => {
  const pageRef: any = useRef();
  const addDevlopRef: any = useRef();
  const exportInfoRef: any = useRef();
  const org = getSession('org') || { orgCode: '' };
  const { orgCode = '' } = org;
  const [searchQuery, setSearchQuery] = useState<any>({});
  const { query = {} } = props.location || {};

  const tableColumns = () => {
    return [
      {
        title: '姓名',
        dataIndex: 'name',
        width: 100,
      },
      {
        title: '性别',
        dataIndex: 'sexCode',
        width: 100,
        render: (text) => {
          return (
            <span> {text === '1' ? '男' : '女'} </span>
          )
        }
      },
      {
        title: '公民身份证',
        dataIndex: 'idcard',
        width: 160,
        render: (text, record) => {
          if (typeof text === 'string' && !_isEmpty(text)) {
            let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
            if (text.indexOf("*") > 0) {
              return text
            }
            return (
              <span>{newVal}</span>
            );
          } else {
            return ''
          }
        }
      },
      {
        title: '电话',
        width: 100,
        dataIndex: 'phone',
        render: (text, record) => {
          return text ? '***********' : ''
        }
      },
      {
        title: '党员类型',
        width: 120,
        dataIndex: 'd08Name',
      },
      {
        title: '所在组织',
        width: 260,
        dataIndex: 'orgName',
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        render: (text, record) => {
          return (
            <span>
              <a onClick={async () => {

                if (query.lockObject == '1') {
                  AddEdit['WrappedComponent'].show();
                  if (record && record['code']) {
                    await props.dispatch({
                      type: 'memBasic/findMem',
                      payload: {
                        code: record['code']
                      }
                    })
                  }
                }
                if (query.lockObject == '2') {
                  const { canEdit: _canEdit = [] } = props;
                  addDevlopRef.current.destroy();
                  if (record && record['code']) {
                    await props.dispatch({
                      type: 'memDevelop/findMem',
                      payload: {
                        code: record['code'],
                        type: '2',
                      }
                    })
                  }
                  addDevlopRef.current.open({ canEdit: true, editType: 'benNian' });
                }
              }}>编辑</a>
            </span>
          )
        },
      },
    ];
  };
  // 流动党员信息
  const flowColumns = ()=>{
    return [
      {
        title: '姓名',
        dataIndex: 'memName',
        align: 'center',
        width: 50,
      },
      {
        title: '性别',
        dataIndex: 'memSexName',
        align: 'center',
        width: 40,
      },
      {
        title: '联系电话',
        dataIndex: 'memPhone',
        align: 'center',
        width: 100,
      },
      {
        title: '工作岗位',
        dataIndex: 'memD09Name',
        align: 'center',
        width: 100,
      },
      {
        title: '流动类型',
        dataIndex: 'flowTypeName',
        align: 'left',
        width: 100,
      },
      {
        title: '流出地党支部',
        dataIndex: 'memOrgName',
        align: 'left',
        width: 100,
      },
      {
        title: '流入地党支部',
        dataIndex: 'inOrgName',
        align: 'left',
        width: 100,
      },
      {
        title: '流出日期',
        dataIndex: 'outTime',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '流回日期',
        dataIndex: 'flowBackTime',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '流动状态',
        dataIndex: 'flowStep',
        align: 'center',
        width: 100,
      },
    ]
  }
  const rdColumns = ()=>{
    return [
      {
        title: '姓名',
        dataIndex: 'name',
        width: 100,
      },
      {
        title: '性别',
        dataIndex: 'sexName',
        width: 100,
        // render: (text) => {
        //   return (
        //     <span> {text === '1' ? '男' : '女'} </span>
        //   )
        // }
      },
      {
        title: '公民身份证',
        dataIndex: 'idcard',
        width: 160,
        render: (text, record) => {
          if (typeof text === 'string' && !_isEmpty(text)) {
            // let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
            let newVal = text.replace(/(\d{3})\d*([0-9a-zA-Z\(\)\[\]]{4})/, "$1***********$2"); //增加港澳台身份证马赛克
            if (text.indexOf("*") > 0) {
              return text
            }
            return (
              <span>{newVal}</span>
            );
          } else {
            return ''
          }
        }
      },
      {
        title: '电话',
        width: 100,
        dataIndex: 'phone',
        render: (text, record) => {
          if (typeof text === 'string' && !_isEmpty(text)) {
            let newVal = text.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
            if (text.indexOf("*") > 0) {
              return text
            }
            return (
              <span>{newVal}</span>
            );
          } else {
            return ''
          }
        }
      },
      {
        title: '党员类型',
        width: 120,
        dataIndex: 'd08Name',
      },
      {
        title: '所在组织',
        width: 260,
        dataIndex: 'orgName',
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        render: (text, record) => {
          return (
            <span>
              <a  onClick={async ()=>{
                  const { canEdit: _canEdit = [] } = props;
                  addDevlopRef.current.destroy();
                  if (record && record['code']) {
                    await props.dispatch({
                      type: 'memDevelop/findMem',
                      payload: {
                        code: record['code'],
                        type: query.lockObject,
                      }
                    })
                  }
                  addDevlopRef.current.open({ canEdit: '', editType: 'benNian' });
              }}>详情</a>
            </span>
          )
        },
      },
    ]
  }
  const getSearchCallBack = (val, ohter) => {
    const ArrToString = ['d06Code', 'd89Code','d07Code', 'd08Code', 'd09Code', 'd20Code','d22Code','d25Code','d19Code','d21Code', 'd27Code', 'd11Code', 'd49Code', 'sexCode', 'lostContactCode', 'memSexCode', 'inOrgLifeCode', 'memD09Code', 'outOrgD04Code', 'flowReasonCode', 'inOrgD04Code', 'flowTypeCode'];
    ArrToString.forEach(it => {
      val[it] = (!_isEmpty(val[it]) && _isArray(val[it])) ? val[it].toString() : undefined;
    })
    setSearchQuery({ ...val, orgLevelCode: orgCode, type: query.lockObject, orgCode: org.code, ...ohter })
    return { code: 0, value: { ...val, pageNum: 1, orgLevelCode: orgCode, type: query.lockObject, ...ohter } }
  };

  const memTabs = [
    { key: '1', title: '党员信息' },
    { key: '2', title: '发展党员信息' },
    { key: '3', title: '流动党员信息' },
    { key: '4', title: '入党申请人' },
    { key: '5', title: '积极分子' },
    { key: '6', title: '发展对象' },
  ];
  // 导出
  const exportInfo = async () => {
    exportInfoRef.current.open();
  };
  const otherBtnsFunc = ({ list, keyword }: any = {}) => {
    return (
      <Fragment>
        <Button onClick={exportInfo} disabled={_isEmpty(list)}>导出</Button>
      </Fragment>
    )
  };
  const tabChange = (val) => {
    const { query } = props.location;
    setSearchQuery({});
    pageRef.current.restForm();
    router.push(`?${qs.stringify({ ...query, lockObject: val })}`)
  }
  const getact=()=>{
    let act
    switch(query.lockObject) {
      case '1':
        case '2':
          act =searchMem;
          break;
          case '3':
            act =searchFlowMem;
            break;
            default:
            act =dataQuery;
    }
    return act
  }
  const getcolumn=()=> {
    let com:any = []
    switch(query.lockObject) {
      case '1':
        case '2':
          com =tableColumns;
          break;
          case '3':
            com =flowColumns;
            break;
            default:
              com =rdColumns;
    } 
    return com
  }
  return (
    <div>
      <Tabs activeKey={query.lockObject} onChange={tabChange}>
        {
          !_isEmpty(memTabs) && memTabs.map(item => <TabPane tab={item['title']} key={item['key']} />)
        }
      </Tabs>
      <CommonSerchPage
        rowKey='code'
        ref={pageRef}
        FormComp={a => {
          return <Searches form={a.form} {...props} onOK={(e) => {
            a.onSearch(e)
          }} />;
        }}
        isDefaultForm={true}
        {...props}
        tableColumns={getcolumn()}
        otherBtnsFunc={otherBtnsFunc}
        getSearchCallBack={getSearchCallBack}
        tableAction={getact()}
      />
      <AddEdit destroy={() => {
        pageRef.current.search({ list: searchQuery }, { pageNum: 1 })
      }} menuDataKey={['1']} />
      <AddDevlop wrappedComponentRef={addDevlopRef} onsubmit={() => {
        pageRef.current.search({ list: searchQuery }, { pageNum: 1 })
      }} canEdit={query.lockObject<4?['mems']:''} {...props} tipMsg={{}} />
      {(query.lockObject == '1' || query.lockObject == '2') && (<ExportInfo
        wrappedComponentRef={exportInfoRef}
        // wrappedComponentRef={e => this['memExportInfo'] = e}
        tableName={query.lockObject == '1' ? 'ccp_mem' : 'ccp_develop_step_log'}
        tableListQuery={{ ...searchQuery, type: query.lockObject }}
        action={'/api/search/exportSearchMem'}
      />)}
      {query.lockObject == '3' && (<ExportInfo
        wrappedComponentRef={exportInfoRef}
        // wrappedComponentRef={e => this['memExportInfo'] = e}
        tableName={'mem_flow'}
        tableListQuery={{ ...searchQuery, type: query.lockObject }}
        action={'/api/mem/flow/analysisQueryExport'}
      />)}
       {query.lockObject > '3' && (<ExportInfo
        wrappedComponentRef={exportInfoRef}
        // wrappedComponentRef={e => this['memExportInfo'] = e}
        tableName={'ccp_mem_develop'}
        tableListQuery={{ ...searchQuery, type: query.lockObject }}
        action={'/api/mem/develop/dataQuery/exportData'}
      />)}
    </div>
  )
}

export default connect(({ commonDict, memBasic, memDevelop }: any) => ({ commonDict, memBasic, memDevelop }), undefined, undefined, { forwardRef: true })(React.forwardRef(index))
