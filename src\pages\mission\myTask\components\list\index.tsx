import React,{Fragment} from 'react';
import style from './index.less';
import { DownOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Divider, Popconfirm, Menu, Button, Input, Dropdown, Switch } from 'antd';
import RuiFilter from 'src/components/RuiFilter';
import ListTable from 'src/components/ListTable';
import NowOrg from 'src/components/NowOrg';
import WhiteSpace from '@/components/WhiteSpace';
import Tip from '@/components/Tip';
import _isNumber from 'lodash/isNumber';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import {withContext} from 'src/utils/global.jsx';
import {connect} from "dva";
import {_history as router} from "@/utils/method";
import {getSession} from "@/utils/session";
import qs from 'qs';
import {setListHeight} from "@/utils/method";
const {Search} = Input;
@withContext
@connect(({myTask,commonDict,loading})=>({myTask,commonDict,loading}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state={
      filterHeight:100,
      filterChecked:{}
    }
  }
  componentDidMount(): void {
    setListHeight(this);
  }

  // 筛选
  filterChange=(val)=>{
    this.setState({filterChecked:val});
    this.props.dispatch({
      type:'myTask/updateState',
      payload:{
        filter:val
      }
    });
    this.action()
  };
  // 分页
  onPageChange=(page,pageSize)=>{
    let {query}=this.props.location;
    router.push(`?${qs.stringify({...query,pageNum:page,pageSize})}`)
  };
  action=(val?:object)=>{
    const {pagination={}}=this.props.myTask;
    const {current = 1,pageSize = 10} = pagination;
    const org=getSession('org') || {};
    this.props.dispatch({
      type:'myTask/getList',
      payload:{
        data:{
          code:org['code'],
          pageNum:current,
          pageSize,
          ...val
        }
      }
    })
  };
  search=(value)=>{
    this.props.dispatch({
      type:'myTask/updateState',
      payload:{
        memName:value
      }
    });
    this.action();
  };
  addNew=()=>{
    this['addModal'].open();
  };
  edit= async ({isSupplt,isEdit},code)=>{
    const res = await this.props.dispatch({
      type:'myTask/getDtail',
      payload:{
        type:2,
        code
      }
    });
    if(res === 0){
      this['AcModal'] && this['AcModal'].open({isSupplt,isEdit});
    }
  };
  openDtail= async (code)=>{
    const res = await this.props.dispatch({
      type:'myTask/getDtail',
      payload:{
        type:2,
        code
      }
    });
    if(res === 0){
      this['AcDetails'] && this['AcDetails'].open();
    }
  };
  showDetails=(record)=>{
    this['DetailmyTask'].open(record);
  };
  myTaskDown=(record)=>{
    this['addmyTask'].open(record);
  };
  ignore= async (val)=>{
    const {code = ''} = val || {};
    const res = await this.props.dispatch({
      type:'myTask/stopmyTask',
      payload:{
        data:{code}
      }
    });
    const {code:resCode = 500 } = res;
    if(resCode === 0){
      Tip.success('操作提示','操作成功');
      this.action();
    }
  };
  operating=(record,key)=>{
    switch (key) {
      case '1':
        this['Reply'].open(record);
        break;
      case '2':
        console.log('确认');
        break;
      case '3':
        this.myTaskDown(record);
        break;
      case '4':
        this.ignore(record);
        break;
    }
  };
  onclose=()=>{
    this.action()
  };
  render(): React.ReactNode {
    const {myTask ={},loading:{effects = {}} ={},commonDict} = this.props;
    const {list, pagination,memName,filter} = myTask;
    const {current = 1,pageSize = 10} = pagination;
    const {filterHeight}=this.state;

    const filterData = [
      // {
      //   key:'d68CodeList',name:'消息类型',value:commonDict[`dict_d67`],
      // },
    ];
    const columns=[
      {
        title:'序号',
        dataIndex:'num',
        width:60,
        render:(text,record,index)=>{
          return (current-1)*pageSize+index+1
        }
      },
      {
        title:'任务名称',
        dataIndex:'messageNme',
        width:120,
      },
      {
        title:'任务分值',
        dataIndex:'messageContext',
        render:(text)=>{
          return (
           <div className={style.messageContext}>{text}</div>
          )
        }
      },
      {
        title:'任务对象',
        dataIndex:'messageTypeNmae',
        width:90,
      },
      {
        title:'开始时间',
        dataIndex:'fromType',
        width:90,
        render:(text)=>{
          let text1 = '';
          switch (text) {
            case '1':
              text1 = '业务通知';
              break;
            case '2':
              text1 = '系统通知';
              break;
          }
          return (
            <div>{text1}</div>
          )
        }
      },
      {
        title:'结束时间',
        dataIndex:'fromTyp1e',
        width:90,
        render:(text)=>{
          let text1 = '';
          switch (text) {
            case '1':
              text1 = '业务通知';
              break;
            case '2':
              text1 = '系统通知';
              break;
          }
          return (
            <div>{text1}</div>
          )
        }
      },
      {
        title:'操作',
        width:150,
        dataIndex:'action',
        render:(text,record)=>{
          const {messageIsReply = '',replyContext = '', fromType = ''} = record;
          let final:Array<any> = [];
          switch (fromType) {
            case '1':
              final = [
                {key:'1',value:'回复'},
                // {key:'2',value:'确认'},
              ];
              if(!(!(messageIsReply === 0 || messageIsReply === 3) && _isEmpty(replyContext))){
                final = final.filter(item=>item['key']!=='1')
              }
              break;
            case '2':
              final = [
                {key:'3',value:'通知下级'},
                {key:'4',value:'不在提醒'},
              ];
              break;
          }
          return (
            <Fragment>
              <a onClick={()=>this.showDetails(record)}>详情</a>
              {
                !_isEmpty(final) ?
                  <Fragment>
                    <Divider type="vertical"/>
                    <Dropdown overlay={(
                      <Menu>
                        {
                          final.map((item,index)=>{
                            return(
                              <Menu.Item key={index}>
                                <a onClick={()=>this.operating(record,item['key'])}>{item['value']}</a>
                              </Menu.Item>
                            )
                          })
                        }
                      </Menu>
                    )}>
                      <a className="ant-dropdown-link">
                        更多 <DownOutlined />
                      </a>
                    </Dropdown>
                  </Fragment>
                  :null
              }
            </Fragment>
          );
        },
      },
    ];

    return (
      <Fragment>
        <NowOrg
          extra={
            <React.Fragment>
              <Search style={{width:200,marginLeft:16}} onSearch={this.search} placeholder={'请输入检索关键词'}/>
            </React.Fragment>
          }
        />
        <RuiFilter
          data={filterData}
          onChange={this.filterChange}
        />
        <WhiteSpace/>
        <WhiteSpace/>
        <ListTable scroll={{y:filterHeight}} columns={columns} data={list} pagination={pagination} onPageChange={this.onPageChange}/>
      </Fragment>
    )
  }
}
