import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Alert, Col, Input, InputNumber, Row, Select } from 'antd';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import _isEqual from 'lodash/isEqual';
import DictTreeSelect from '@/components/DictTreeSelect';
import { connect } from 'dva';
import DictSelect from '@/components/DictSelect';
import moment from 'moment';
import { getSession } from '@/utils/session';

const { TextArea } = Input;

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
// @ts-ignore
@connect(({ memDevelop }) => ({ memDevelop }))
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {};
  }

  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    const org = getSession('org') || {};
    const state = {};
    const { memDevelop: { basicInfo = {} } = {} } = nextProps;
    const { _basicInfo = {} } = prevState;
    if (!_isEqual(basicInfo, _basicInfo)) {
      state['_basicInfo'] = basicInfo;
      const { d08Code = '', d18Code = '', d19Code = '', isOutSystem } = basicInfo;
      state['d08Code'] = d08Code;
      if (!_isEmpty(d18Code) && d18Code !== '0') {
        state['hasLost'] = true;
      } else {
        state['hasLost'] = false;
      }
      if (isOutSystem === 1) {
        state['isOutSystem_state'] = true
      } else {
        state['isOutSystem_state'] = false
      }
      if (!_isEmpty(d19Code) && d19Code !== '0') {
        state['hasAppointment'] = true
      } else {
        state['hasAppointment'] = false
      }
    }
    return { state, org };
  };
  d19CodeOnChange = (code) => {
    if (code && code['key'] !== '0') {
      this.setState({ hasAppointment: true })
    } else {
      this.setState({ hasAppointment: false });
      this.props.form.setFieldsValue({ appointmentDate: undefined, appointmentEndDate: undefined });
    }
  };
  render() {
    const { form, memDevelop: { basicInfo = {} } = {}, handlePoliticsCodeChange } = this.props;
    const { getFieldDecorator } = form;
    return (
      <Fragment>
        <Alert message="提示：基础信息修改" type="info" showIcon />
        <div style={{ marginBottom: '10px' }} />
        <Row>
          <Col span={12}>
            <FormItem
              label="一线情况"
              {...formItemLayout}
            >
              {getFieldDecorator('d21Code', {
                rules: [{ required: true, message: '请选择一线情况' }],
                initialValue: _isEmpty(basicInfo) ? '0' : basicInfo['d21Code'],
              })(
                <DictSelect codeType={'dict_d21'} initValue={_isEmpty(basicInfo) ? '0' : basicInfo['d21Code']} backType={'object'} />
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label="申请时新社会阶层"
              {...formItemLayout}
            >
              {getFieldDecorator('d20Code', {
                rules: [{ required: true, message: '请选择申请时新社会阶层' }],
                initialValue: _isEmpty(basicInfo) ? '0' : basicInfo['d20Code'],
              })(
                <DictTreeSelect codeType={'dict_d20'} initValue={_isEmpty(basicInfo) ? '0' : basicInfo['d20Code']} backType={'object'} parentDisable={true} />
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label="专业技术职务"
              {...formItemLayout}
            >
              {getFieldDecorator('d19Code', {
                rules: [{ required: true, message: '请选择专业技术职务' }],
                initialValue: _isEmpty(basicInfo) ? '0' : basicInfo['d19Code'],
              })(
                <DictTreeSelect
                  initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d19Code']}
                  codeType={'dict_d19'}
                  placeholder={'专业技术职务'}
                  parentDisable={true}
                  backType={'object'}
                />
                // <DictSelect codeType={'dict_d19'} initValue={_isEmpty(basicInfo)?'0':basicInfo['d19Code']} backType={'object'} onChange={this.d19CodeOnChange}/>
              )}
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label="当前工作岗位"
              {...formItemLayout}
            >
              {getFieldDecorator('d09Code', {
                rules: [{ required: true, message: '请选择当前工作岗位' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d09Code'],
              })(
                <DictTreeSelect initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d09Code']}
                  codeType={'dict_d09'}
                  placeholder={'当前工作岗位'}
                  parentDisable={true}
                  // itemsDisabled={["11"]}
                  onChange={(e) => {
                    const { key = '' } = e || {};
                    if (key.startsWith('3')) {
                      form.setFieldsValue({
                        d07Code: undefined
                      });
                      this['d07Code'].clearAll();
                    }
                  }}
                  backType={'object'} />
              )}
            </FormItem>
          </Col>
          {
            (function (_this) {
              const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;

              let itemsDisabled: Array<string> = [];
              if (_key) {
                if (_key.startsWith('31')) { // 研究生
                  // itemsDisabled = ['11','12','13','14'];
                  itemsDisabled = [];
                }
                if (_key.startsWith('32')) { // 本科
                  itemsDisabled = ['11', '12', '13', '14', '21', '22', '23']
                }
                if (_key.startsWith('33')) { // 专科
                  itemsDisabled = ['11', '12', '13', '14', '21', '22', '23', '31', '32', '33', '34']
                }
                if (_key == '34') {
                  itemsDisabled = ['11', '12', '13', '14', '21', '22', '23', '31', '32', '33', '34']
                }
                if (_key == '35') {
                  itemsDisabled = ['11', '12', '13', '14', '21', '22', '23', '31', '32', '33', '34']
                }
                if (_key == '36') {
                  itemsDisabled = ['11', '12', '13', '14', '21', '22', '23', '31', '32', '33', '34']
                }
              }
              return (
                <Col span={12}>
                  <FormItem
                    label="当前学历情况"
                    {...formItemLayout}
                  >
                    {getFieldDecorator('d07Code', {
                      rules: [{ required: true, message: '请选择当前学历情况' }],
                      initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d07Code'],
                    })(
                      <DictTreeSelect initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d07Code']}
                        codeType={'dict_d07'}
                        placeholder={'党员学历'}
                        ref={e => _this['d07Code'] = e}
                        parentDisable={true}
                        itemsDisabled={itemsDisabled}
                        backType={'object'} />,
                    )}
                  </FormItem>
                </Col>
              );
            })(this)
          }
          {
            // 前后端增加毕业院校、毕业专业（高学历党员填写）
            (function (_this: any) {
              const { d07Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              const { birthday = undefined, applyDate = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let bigThan28 = true;
              if (birthday && applyDate && moment(applyDate) <= moment(birthday).add(28, 'years')) {
                bigThan28 = false;
              }
              if (birthday && !applyDate && moment() <= moment(birthday).add(28, 'years')) {
                bigThan28 = false;
              }
              let _key = typeof d07Code === 'string' ? d07Code : d07Code?.key;
              if (!['4', '5', '6', '7', '8', '9'].includes(_key)) {
                return (
                  <Fragment>
                    <Col span={12}>
                      <FormItem
                        label="毕业院校"
                        {...formItemLayout}
                      >
                        {getFieldDecorator('byyx', {
                          rules: [{ required: false, message: '毕业院校' }],
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['byyx'],
                        })(
                          <Input placeholder={'毕业院校'} />
                        )}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label="毕业专业"
                        {...formItemLayout}
                      >
                        {getFieldDecorator('d88Code', {
                          rules: [{ required: false, message: '毕业专业' }],
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['d88Code'],
                        })(
                          <DictTreeSelect initValue={_isEmpty(basicInfo) ? undefined : basicInfo['d88Code']}
                            codeType={'dict_d88'}
                            placeholder={'毕业专业'}
                            parentDisable={true}
                            backType={'object'} />
                        )}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label="政治面貌"
                        {...formItemLayout}
                      >
                        {getFieldDecorator('politicsCode', {
                          rules: [{ required: true, message: '政治面貌' }],
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['politicsCode'],
                        })(
                          <DictSelect backType={'object'}
                            ref={e => _this['politicsCode'] = e}
                            initValue={_isEmpty(basicInfo) ? undefined : basicInfo['politicsCode'] ? basicInfo['politicsCode'].split(',') : undefined}
                            codeType={'dict_d89'}
                            placeholder="请选择"
                            mode={'multiple'}
                            filter={(data) => {
                              if (bigThan28) {
                                return data.filter(it => it.key !== '12');
                              }
                              return data;
                            }}
                            onChange={(e) => {
                              handlePoliticsCodeChange && handlePoliticsCodeChange(e)
                            }}
                          />
                        )}
                      </FormItem>
                    </Col>
                  </Fragment>
                )
              }
            })(this)
          }
          {
            (function (_this) {
              const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
              if (`${_key}`.startsWith('31') || `${_key}`.startsWith('32') || `${_key}`.startsWith('33')) {
                return (
                  <Fragment>
                    <Col span={12}>
                      <FormItem
                        label="在读院校"
                        {...formItemLayout}
                      >
                        {getFieldDecorator('readingCollege', {
                          rules: [{ required: true, message: '在读院校' }],
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['readingCollege'],
                        })(
                          <Input placeholder={'在读院校'} />
                        )}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label="在读专业"
                        {...formItemLayout}
                      >
                        {getFieldDecorator('readingProfessionalCode', {
                          rules: [{ required: true, message: '在读专业' }],
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['readingProfessionalCode'],
                        })(
                          <DictTreeSelect initValue={_isEmpty(basicInfo) ? undefined : basicInfo['readingProfessionalCode']}
                            codeType={'dict_d88'}
                            placeholder={'在读专业'}
                            parentDisable={true}
                            backType={'object'} />
                        )}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label="学制"
                        {...formItemLayout}
                      >
                        {getFieldDecorator('educationalSystem', {
                          rules: [{ required: true, message: '学制' }],
                          initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['educationalSystem'],
                        })(
                          <InputNumber placeholder={'学制'} style={{ width: '100%' }} />
                        )}
                      </FormItem>
                    </Col>
                  </Fragment>
                )
              }
            })(this)
          }
          {
            (function (_this) {
              const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
              // 当工作岗位是工勤岗位（例如:岗位名称中的工勤岗位和工勤技能人员这类岗位），才弹出是否农民工和是否劳务派遣的信息选择项
              if (`${_key}`.startsWith('016') || `${_key}`.startsWith('025') || `${_key}`.startsWith('033')) {
                return (
                  <Fragment>
                    <Col span={12}>
                      <FormItem
                        label="是否劳务派遣"
                        {...formItemLayout}
                      >
                        {getFieldDecorator('isDispatch', {
                          rules: [{ required: true, message: '请选择' }],
                          initialValue: _isEmpty(basicInfo) ? undefined : _isNumber(basicInfo['isDispatch']) ? basicInfo['isDispatch'].toString() : undefined,
                        })(
                          <Select style={{ width: '100%' }}>
                            <Select.Option value="1">是</Select.Option>
                            <Select.Option value="0">否</Select.Option>
                          </Select>
                        )}
                      </FormItem>
                    </Col>
                    <Col span={12}>
                      <FormItem
                        label="申请时是否农民工"
                        {...formItemLayout}
                      >
                        {getFieldDecorator('isFarmer', {
                          rules: [{ required: true, message: '是否农民工' }],
                          initialValue: _isEmpty(basicInfo) ? undefined : _isNumber(basicInfo['isFarmer']) ? basicInfo['isFarmer'].toString() : undefined,
                        })(
                          <Select style={{ width: '100%' }}>
                            <Select.Option value="1">是</Select.Option>
                            <Select.Option value="0">否</Select.Option>
                          </Select>
                        )}
                      </FormItem>
                    </Col>
                  </Fragment>
                )
              }
            })(this)
          }
          {
            (function (_this) {
              const { d09Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d09Code === 'string' ? d09Code : d09Code?.key;
              if (`${_key}`.startsWith('016') || `${_key}`.startsWith('025')) {
                return (
                  <Col span={12}>
                    <FormItem
                      label="是否产业工人"
                      {...formItemLayout}
                    >
                      {getFieldDecorator('hasWorker', {
                        rules: [{ required: true, message: '是否产业工人' }],
                        initialValue: _isEmpty(basicInfo) ? undefined : _isNumber(basicInfo['hasWorker']) ? basicInfo['hasWorker'].toString() : undefined,
                      })(
                        <Select style={{ width: '100%' }}>
                          <Select.Option value="1">是</Select.Option>
                          <Select.Option value="0">否</Select.Option>
                        </Select>
                      )}
                    </FormItem>
                  </Col>
                )
              }
            })(this)
          }
          {/* <Col span={12}>
              <FormItem
                label="专业技术职务"
                {...formItemLayout}
              >
                {getFieldDecorator('d19Code', {
                  rules: [{ required: true, message: '请选择专业技术职务' }],
                  initialValue: _isEmpty(basicInfo)?'0':basicInfo['d19Code'],
                })(
                  <DictSelect codeType={'dict_d19'} initValue={_isEmpty(basicInfo)?'0':basicInfo['d19Code']} backType={'object'} onChange={this.d19CodeOnChange}/>
                )}
              </FormItem>
            </Col> */}
          {
            (function (_this) {
              const { d19Code = undefined } = _this?.props?.form?.getFieldsValue() || {};
              let _key = typeof d19Code === 'string' ? d19Code : d19Code?.key;
              if (`${_key}`.startsWith('1') || _key == '2') {
                return (
                  <Col span={12}>
                    <FormItem
                      label="是否高知识群体"
                      {...formItemLayout}
                    >
                      {getFieldDecorator('isHighKnowledge', {
                        rules: [{ required: true, message: '是否高知识群体' }],
                        initialValue: _isEmpty(basicInfo) ? undefined : _isNumber(basicInfo['isHighKnowledge']) ? basicInfo['isHighKnowledge'].toString() : undefined,
                      })(
                        <Select style={{ width: '100%' }}>
                          <Select.Option value="1">是</Select.Option>
                          <Select.Option value="0">否</Select.Option>
                        </Select>
                      )}
                    </FormItem>
                  </Col>
                )
              }
            })(this)
          }
          <Col span={12}>
            <FormItem
              label="先进模范人物"
              {...formItemLayout}
            >
              {getFieldDecorator('advancedModelCode', {
                rules: [{ required: false, message: '先进模范人物' }],
                initialValue: _isEmpty(basicInfo) ? undefined : basicInfo['advancedModelCode'],
              })(
                <DictSelect codeType={'dict_d104'} initValue={_isEmpty(basicInfo) ? undefined : basicInfo['advancedModelCode']} backType={'object'} />
              )}
            </FormItem>
          </Col>
        </Row>
      </Fragment>
    );
  }
}

export default index;
