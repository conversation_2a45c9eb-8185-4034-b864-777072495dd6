import React from 'react';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, DatePicker, InputNumber, Select, Button } from 'antd';
import Upload from '@/components/UploadComp';
import CheckTypes from '@/components/CheckTypes';
import MemSelect from '@/components/MemSelect';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _trim from 'lodash/trim';
import _isEqual from 'lodash/isEqual';
import _get from 'lodash/get';
import moment from 'moment';
import { unixMoment } from '@/utils/method.js';
import { connect } from 'dva';
import { getSession } from '@/utils/session';
import {frequencyData,checkTypeData,endTypeData} from '../../../config';
const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;
const formItemLayout1 = {
  labelCol: { xs: { span: 24 }, sm: { span: 4 } },
  wrapperCol: { xs: { span: 24 }, sm: { span: 18 } },
};
// const frequencyData = [{ key: 1, name: '一次' }, { key: 2, name: '每月' }, { key: 3, name: '每季度' }, { key: 4, name: '每年' }];
// const checkTypeData = [{ key: 1, name: '发布给人员' }, { key: 2, name: '发布给组织' }];
// const endTypeData = [{ key: 1, name: '本月内' }, { key: 2, name: '本季度内' }, { key: 3, name: '本年内' }, { key: 4, name: '自定义时间' }];

@connect(({ mission, loading }) => ({ mission, loading }))
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      checkType: 1,  // 选组织还是选人
      endType: '',  // 截至时间类型
      startTime: '',
    };
  }

  static getDerivedStateFromProps = (nextProps: any, prevState: any) => {
    const state = {};
    const { initData = {} } = nextProps;
    const { startDate, endDate } = initData;
    if (!_isEmpty(initData)) {
      // 没有字段，只有通过开始时间和结束时间的比较来判断
      if (moment(moment(startDate).endOf('month').valueOf() + 1).subtract(1,'day').valueOf() === endDate) {
        state['endType'] = 1;
      } else if (moment(moment(startDate).endOf('quarter').valueOf() + 1).subtract(1,'day').valueOf() === endDate) {
        state['endType'] = 2;
      } else if (moment(moment(startDate).endOf('year').valueOf() + 1).subtract(1,'day').valueOf() === endDate) {
        state['endType'] = 3;
      } else {
        state['endType'] = 4;
      }
    }
    return state;
  };
  nameValidator = (rule, value, callback) => {
    if (_isEmpty(_trim(value)) && !_isEmpty(value)) {
      callback('名称必填');
    }
    if (!_isEmpty(value) && value.length >= 50) {
      callback('名称应在50字以内');
    }
    callback();
  };
  remarkValidator = (rule, value, callback) => {
    if (!_isEmpty(value) && value.length >= 300) {
      callback('备注应在300字以内');
    }
    callback();
  };
  disabledDate = (current) => {
    return current && current < moment().endOf('day');
  };
  disabledEndDate = (endValue) => {
    const { startTime } = this.state;
    if (!endValue || !startTime) {
      return false;
    }
    return endValue.endOf('day') <= startTime.endOf('day');
  };
  startTimeChange = (val) => {
    const { form } = this.props;
    this.setState({ startTime: val });
    if (form.getFieldValue('jiezhi') === 4) {
      form.setFieldsValue({
        endDate: moment(val).add(1, 'day'),
      });
    }
  };
  // 截止类型选项s
  jiezhiChange = async (val) => {
    const { startTime } = this.state;
    await this.setState({
      endType: val,
    });
    val === 4 && this.props.form.setFieldsValue({
      endDate: moment(startTime).add(1, 'day'),
    });
  };
  submit = () => {
    const { form, onclose, initData} = this.props;
    const roles = getSession('roles') || {};
    form.validateFieldsAndScroll(async (err, values) => {
      if (!err) {
        values = unixMoment(['startDate', 'endDate'], values);
        values['objectList'] = _isEmpty(values['objectList']) ? [] : values['objectList'].map(item => item['code']);
        switch (values['jiezhi']) {
          case 1:
            values['endDate'] = moment(values['startDate']).endOf('month').valueOf();
            break;
          case 2:
            values['endDate'] = moment(values['startDate']).endOf('quarter').valueOf();
            break;
          case 3:
            values['endDate'] = moment(values['startDate']).endOf('year').valueOf();
            break;
        }
        values['taskObjectType'] = values['taskObjectType'][0];
        values['createOrgCode'] = !_isEmpty(initData) ? initData['createOrgCode'] : roles['managerOrgId'];
        values['createOrgOrgCode'] =  !_isEmpty(initData) ? initData['createOrgOrgCode'] : roles['managerOrgCode'];
        if(!_isEmpty(initData)){
          values['code'] = initData['code']
        }
        const res = await this.props.dispatch({
          type: !_isEmpty(initData) ? 'mission/edit' : 'mission/add',
          payload: {
            ...values,
          },
        });
        const { code = 500 } = res;
        if (code === 0) {
          Tip.success('操作提示', '操作成功');
          onclose && onclose();
          this.destroy();
        }
      }
    });
  };
  destroy = () => {
    this.setState({
      checkType: 1,
      endType: '',
      startTime: '',
    });
  };

  render(): React.ReactNode {
    const { form, loading: { effects = {} } = {}, initData } = this.props;
    const { getFieldDecorator } = form;
    const { checkType, endType } = this.state;

    return (
      <div>
        <Form>
          <FormItem
            label={'任务名称'}
            {...formItemLayout1}
          >
            {getFieldDecorator('taskName', {
              initialValue: _get(initData, 'taskName', ''),
              rules: [
                { required: true, message: '请输入任务名称' },
                { validator: this.nameValidator },
              ],
            })(
              <Input placeholder={'请输入任务名称'}/>,
            )}
          </FormItem>
          <FormItem
            label={'开始时间'}
            {...formItemLayout1}
          >
            {getFieldDecorator('startDate', {
              initialValue: _get(initData, 'startDate', '') ? moment(_get(initData, 'startDate', '')) : undefined,
              rules: [{ required: true, message: '开始时间' }],
            })(
              <DatePicker style={{ width: '100%' }}
                          disabledDate={this.disabledDate}
                          onChange={this.startTimeChange}
              />,
            )}
          </FormItem>
          <FormItem
            label={'截止时间'}
            {...formItemLayout1}
          >
            {getFieldDecorator('jiezhi', {
              initialValue: endType === '' ? undefined : endType,
              rules: [{ required: true, message: '截止时间' }],
            })(
              <Select onChange={this.jiezhiChange} placeholder={'请选择截止时间'}>
                {endTypeData.map((item, index) => (<Option value={item['key']} key={index}>{item['name']}</Option>))}
              </Select>,
            )}
          </FormItem>
          {
            endType === 4 &&
            <FormItem
              label={'自定义时间'}
              {...formItemLayout1}
            >
              {getFieldDecorator('endDate', {
                initialValue: _get(initData, 'endDate', '') ? moment(_get(initData, 'endDate', '')) : undefined,
                rules: [{ required: true, message: '自定义时间' }],
              })(
                <DatePicker style={{ width: '100%' }} disabledDate={this.disabledEndDate}/>,
              )}
            </FormItem>
          }
          <FormItem
            label={'任务分值'}
            {...formItemLayout1}
          >
            {getFieldDecorator('taskFraction', {
              initialValue: _get(initData, 'taskFraction', 0),
              rules: [{ required: true, message: '请输入任务分值' }],
            })(
              <InputNumber min={0}/>,
            )}
          </FormItem>
          <FormItem
            label={'任务周期'}
            {...formItemLayout1}
          >
            {getFieldDecorator('taskCycle', {
              rules: [{ required: true, message: '任务周期' }],
              initialValue: _get(initData, 'taskCycle', undefined),
            })(
              <Select placeholder={'任务周期必填'}>
                {frequencyData.map((item, index) => (<Option value={item['key']} key={index}>{item['name']}</Option>))}
              </Select>,
            )}
          </FormItem>
          <FormItem
            label={'任务对象'}
            {...formItemLayout1}
          >
            {getFieldDecorator('taskObjectType', {
              rules: [{ required: true, message: '任务对象' }],
              initialValue: [_get(initData, 'taskObjectType', 1)],
            })(
              <CheckTypes data={checkTypeData} rule={[1, 2]} init={[1]}
                          onChange={val => this.setState({ checkType: val[0] })}/>,
            )}
          </FormItem>
          {
            checkType === 1 &&
            <FormItem
              label={'发布给人员'}
              {...formItemLayout1}
            >
              {getFieldDecorator('objectList', {
                rules: [{ required: true, message: '请选择人员' }],
                initialValue: _get(initData, 'objectList', []),
              })(
                <MemSelect checkType={'checkbox'}
                           initValue={_get(initData, 'objectList', [])}
                />,
              )}
            </FormItem>
          }
          {
            checkType === 2 &&
            <FormItem
              label={'发布给组织'}
              {...formItemLayout1}
            >
              {getFieldDecorator('objectList', {
                rules: [{ required: true, message: '请选择组织' }],
                initialValue: _get(initData, 'objectList', []),
              })(
                <OrgSelect
                  initValue={_get(initData, 'objectList', [])}
                />,
              )}
            </FormItem>
          }
          <FormItem
            label={'备注'}
            {...formItemLayout1}
          >
            {getFieldDecorator('taskRemark', {
              initialValue: _get(initData, 'taskRemark', ''),
              rules: [
                { required: true, message: '请输入备注' },
                { validator: this.remarkValidator },
              ],
            })(
              <TextArea placeholder={'请输入备注'} rows={3}/>,
            )}
          </FormItem>
          {/*<FormItem*/}
          {/*label={'相关资料'}*/}
          {/*{...formItemLayout1}*/}
          {/*>*/}
          {/*{getFieldDecorator('file', {*/}
          {/*rules: [{ required: false, message: '请输入备注' }],*/}
          {/*})(*/}
          {/*<Upload/>*/}
          {/*)}*/}
          {/*</FormItem>*/}
          <div style={{ textAlign: 'center' }}>
            <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={this.submit} loading={effects[`${!_isEmpty(initData) ? 'mission/edit' : 'mission/add'}`]}>提交</Button>
          </div>
        </Form>
      </div>
    );
  }
}

export default Form.create()(index);
