/**
 * 单位管理
 */
import React from 'react';
import { connect } from 'dva';
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import { _history as router } from '@/utils/method';
import qs from 'qs';
import ExportInfo from '@/components/Export';
import AddOrEdit from './subpage/addoredit';
import WhiteSpace from '@/components/WhiteSpace';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs } from 'antd';
import NowOrg from 'src/components/NowOrg';
import { getSession } from '@/utils/session';
import { setListHeight, isFlowingParty } from '@/utils/method';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import { unitMv } from '@/pages/[unit]/services/index.js';
import _isEmpty from 'lodash/isEmpty';
import { TableActionMenu } from '@/pages/user/lock';
import _isEqual from 'lodash/isEqual';

const Search = Input.Search;
const TabPane = Tabs.TabPane;

// @ts-ignore
@connect(({ unit, commonDict, common, loading }) => ({ unit, commonDict, common, loading: loading.effects['unit/getList'] }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {}, //筛选器
      search: {}, //搜索框
      searchVal: '',
    };
  }

  addOrEdit = (record?: object) => {
    AddOrEdit['WrappedComponent'].clear();
    if (record && record['code']) {
      this.props
        .dispatch({
          type: 'unit/findOrg',
          payload: {
            code: record['code'],
          },
        })
        .then((res) => {
          AddOrEdit['WrappedComponent'].show();
        });
    } else {
      AddOrEdit['WrappedComponent'].show();
    }
  };
  componentDidMount() {
    setListHeight(this);
  }
  componentWillUnmount(): void {
    this.props.dispatch({
      type: 'unit/destroy',
    });
  }

  onPageChange = (page, pageSize) => {
    console.log(page, pageSize);
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: page, pageSize })}`);
  };
  filterChange = (val) => {
    this.setState({ filterChecked: val });
    this.props.dispatch({
      type: 'unit/updateState',
      payload: {
        filter: val,
      },
    });
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: 1 })}`);
  };
  action = (val?: any) => {
    const { pagination = {} } = this.props.unit;
    const { current = 1, pageSize = 10 } = pagination;
    const { query } = this.props.location;
    const org = getSession('org') || {};
    this.props.dispatch({
      type: 'unit/getList',
      payload: {
        data: {
          mainUnitOrgCode: org['orgCode'],
          manageUnitOrgCode: org['orgCode'],
          // isCreateOrg:'1',
          pageNum: current,
          pageSize,
          ...query,
          ...val,
        },
      },
    });
  };
  confirm = async (record) => {
    const { code = '' } = record;
    let { code: resCode = 500 } = await this.props.dispatch({
      type: 'unit/del',
      payload: { data: { code } },
    });
    if (resCode === 0) {
      Tip.success('操作提示', '删除成功');
      this.action();
    }
  };
  search = (value) => {
    this.props.dispatch({
      type: 'unit/updateState',
      payload: {
        unitName: value,
      },
    });
    const { pagination = {} } = this.props.unit;
    const { query } = this.props.location;

    router.push(`?${qs.stringify({ ...query, pageNum: 1, pageSize: pagination['pageSize'] })}`);
    // this.action();
    // this.setState({
    //   search:{unitName:value}
    // },()=>this.action());
  };
  searchClear = (e) => {
    this.setState({
      searchVal: e.target.value,
    });
    if (!e.target.value) {
      this.props.dispatch({
        type: 'unit/updateState',
        payload: { unitName: undefined },
      });
      this.action();
    }
  };
  // 导出
  exportInfo = async () => {
    this['uintExportInfo'].open();
  };
  turnChange = async (data, record) => {
    console.log('🚀 ~ index ~ turnChange= ~ data:', data, record);
    const [item = {}] = data || [];
    const { code = '' } = item || {};
    const { code: resCode = 500 } = await unitMv({
      data: {
        unitCode: record.code,
        orgCode: code,
      },
    });
    if (resCode === 0) {
      Tip.success('操作提示', '操作成功');
      this.action();
    }
  };
  lockOrUnlock = () => {
    let { query } = this.props.location;
    router.push(`?${qs.stringify({ ...query, pageNum: 1 })}`);
  };
  render() {
    const { list, pagination = {}, filter, unitName } = this.props.unit;
    const { current, pageSize } = pagination;
    const { loading, compType } = this.props;
    const { dataInfo, filterHeight, searchVal = '' } = this.state;
    const columns = [
      {
        title: '单位名称',
        dataIndex: 'name',
        width: 200,
        render: (text, record) => {
          if (compType == 'lock') {
            return text;
          }
          return <a onClick={() => this.addOrEdit(record)}>{text}</a>;
        },
      },
      {
        title: '单位类别',
        dataIndex: 'd04Name',
        width: 200,
      },
      {
        title: '隶属关系',
        dataIndex: 'd35Name',
        width: 100,
      },
      {
        title: '关联组织',
        dataIndex: 'mainOrgName',
        width: 200,
      },
      {
        title: '是否法人单位',
        dataIndex: 'isLegal',
        align: 'center',
        width: 100,
        render: (text, record) => {
          return text == 1 ? '是' : text == 0 ? '否' : '';
        },
      },
      {
        title: '单位所在党组织',
        dataIndex: 'createOrgCodeName',
        width: 200,
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        render: (text, record) => {
          if (compType == 'lock') {
            return <TableActionMenu record={record} lockOrUnlock={this.lockOrUnlock} type={'2'} />;
          }
          return (
            <span>
              <a onClick={() => this.addOrEdit(record)}>编辑</a>
              <Divider type="vertical" />
              <OrgSelect onChange={(data) => this.turnChange(data, record)}>
                <a>转移</a>
              </OrgSelect>
              <Divider type="vertical" />
              <TableActionMenu record={record} lockOrUnlock={this.lockOrUnlock} type={'2'} />
              <Divider type="vertical" />
              <Popconfirm title="确定要删除吗？" onConfirm={() => this.confirm(record)}>
                <a className={'del'}>删除</a>
              </Popconfirm>
            </span>
          );
        },
      },
    ];
    const filterData = [
      {
        key: 'd04CodeList',
        name: '单位类别',
        value: this.props.commonDict[`dict_d04_tree`],
      },
      {
        key: 'd05CodeList',
        name: '组织情况',
        value: this.props.commonDict[`dict_d05_tree`],
      },
      {
        key: 'd35CodeList',
        name: '隶属关系',
        value: this.props.commonDict[`dict_d35_tree`],
      },
      {
        key: 'isLegal',
        name: '是否法人单位',
        value: [
          { key: '1', name: '是' },
          { key: '0', name: '否' },
        ],
      },
      {
        key: 'd194CodeList',
        name: '国民经济行业',
        value: this.props.commonDict[`dict_d194_tree`],
      },
      {
        key: 'd195CodeList',
        name: '生产性服务行业',
        value: this.props.commonDict[`dict_d195_tree`],
      },
    ];
    const org = getSession('org') || {};
    return (
      <div style={{ height: '100%', overflow: 'hidden' }}>
        {!compType && (
          <Tabs defaultActiveKey="1">
            <TabPane tab="基本信息" key="1" />
          </Tabs>
        )}
        {/*<DictArea/>*/}
        <AddOrEdit dataInfo={dataInfo} />
        <NowOrg
          extra={
            <React.Fragment>
              {!compType && isFlowingParty() && (
                <React.Fragment>
                  <Button htmlType={'button'} onClick={this.exportInfo} disabled={_isEmpty(list)}>
                    导出
                  </Button>
                  <Button type={'primary'} icon={<LegacyIcon type={'plus'} />} onClick={() => this.addOrEdit()} style={{ marginLeft: 16 }}>
                    新增单位
                  </Button>
                </React.Fragment>
              )}
              <Search value={searchVal} style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'} />
            </React.Fragment>
          }
        />
        {!compType && <RuiFilter data={filterData} openCloseChange={() => setListHeight(this, 20)} onChange={this.filterChange} />}
        <WhiteSpace />
        <WhiteSpace />
        <ListTable
          scroll={{ y: filterHeight }}
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={this.onPageChange}
        />
        <ExportInfo
          wrappedComponentRef={(e) => (this['uintExportInfo'] = e)}
          tableName={'ccp_unit_all'}
          // tableListQuery={{unitName,...filter,isCreateOrg:'1',mainUnitOrgCode:org['orgCode'],manageUnitOrgCode:org['orgCode']}}
          tableListQuery={{ unitName, ...filter, mainUnitOrgCode: org['orgCode'], manageUnitOrgCode: org['orgCode'] }}
          action={'/api/data/unit/exportData'}
        />
      </div>
    );
  }
}
