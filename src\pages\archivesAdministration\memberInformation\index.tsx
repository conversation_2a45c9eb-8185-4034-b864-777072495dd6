/**
 * 届次信息列表
 */
import React, { Children, Fragment } from 'react';
import { connect } from "dva";
import ListTable from 'src/components/ListTable';
import WhiteSpace from '@/components/WhiteSpace';
import { DownOutlined, WarningTwoTone } from '@ant-design/icons';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs, Modal, Tooltip, Popover, Dropdown, Menu, } from 'antd';
import NowOrg from 'src/components/NowOrg';
import Tip from '@/components/Tip';
import { getSession } from "@/utils/session";
import moment from 'moment'
import { setListHeight, isEmpty } from '@/utils/method';
import Notice from '@/components/Notice';
import ExportInfo from '@/components/Export';
import _isEqual from 'lodash/isEqual';
import _isEmpty from 'lodash/isEmpty';
import RuiFilter from '@/components/RuiFilter';
import WorkModal from "../components/membersWorkProcedures"
import { determine, isArchived } from "../service"
import EditDevlop from '@/pages/developMem/zy/components/Edit'
import ElectronicArchives from '@/pages/developMem/zy/components/electronicArchives'
import styles from './index.less';
import Application from '../components/application'
import Supplement from '../components/supplement'


const Search = Input.Search;

@connect(({ archivesAdministration, unit, commonDict, loading, memBasic }) => ({ memBasic, archivesAdministration, unit, commonDict, loading: loading.effects['archivesAdministration/getList'] }))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {},//筛选器
      search: {},//搜索框
      visible: false,
      visible1: false,
      getList: this.getList,
      confirmLoading: false, // 认定框的loading
      isSure: undefined,
      memInfo: null,
      filterData: [
        {
          key: 'd08CodeList', name: '党员类型', value: [{ key: '1', name: '正式党员' }, { key: '2', name: '预备党员' }],
        },
        {
          key: 'isIntegrality', name: '档案完整度', value: [{ key: '1', name: '完整' }, { key: '0', name: '不完整' }],
        },
        {
          key: 'digitalType', name: '档案类别', value: [{ key: '1', name: '绿色档案' }, { key: '2', name: '蓝色档案' }, { key: '3', name: '红色档案' }, { key: '99', name: '其他' }],
        },
        {
          key: 'isVolunteer', name: '入党志愿书', value: [{ key: '1', name: '已上传' }, { key: '0', name: '未上传' }],
        },
        {
          key: 'isSure', name: '党员身份认定', value: [{ key: '1', name: '已认定' }, { key: '0', name: '未认定' }],
        },
        {
          key: 'isArchived', name: '归档情况', value: [{ key: '1', name: '已归档' }, { key: '0', name: '未归档' }],
        },
      ],
      filenameList: {},
      record: {}
    };
  }
  private modalRef = React.createRef<any>();
  componentDidMount() {
    setListHeight(this)
  }

  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const org = getSession('org') || {};
    const { _org = {}, getList, page, pageSize, name } = prevState;
    if (!_isEqual(org, _org)) {
      state['org'] = org;
      state['_org'] = org;
      getList(page, pageSize, name);
    }
    return state;
  };

  getList = (pageNum = 1, pageSize = 10, name = '') => {
    let org = getSession('org') || {};
    const { filter } = this.state;
    let val = {
      memOrgCode: org['orgCode'],
      // positionOrgName: org['name'],
      pageNum: pageNum,
      pageSize: pageSize,
      searchType: "1",
      memName: name,
      ...filter
    };
    for (let obj in val) {
      if (isEmpty(val[obj])) {
        delete val[obj]
      }
    }
    this.props.dispatch({
      type: 'archivesAdministration/getList',
      payload: {
        data: {
          ...val
        }
      }
    })
  };
  onPageChange = (page, pageSize) => {
    const { name, type } = this.state
    this.setState({
      page,
      pageSize
    }, () => {
      this.getList(page, pageSize, name)
    });

  };

  handleOk = async () => {
    const { memInfo } = this.state
    if (!memInfo) {
      return
    }
    this.setState({ confirmLoading: true })
    const { code, data } = await determine({
      memCode: memInfo?.code,
      sure: memInfo?.isSure == 1 ? 0 : 1
    })
    if (code == 0) {
      Tip.success('操作提示', '操作成功');
      this.setState({ visible: false }, () => {
        this.getList()
      })
    }
    this.setState({ confirmLoading: false })
  };
  handleCancel = () => {
    this.setState({ visible: false, confirmLoading: false })
  };
  handleOk1 = () => {
    this.setState({ visible1: false })
  }
  handleCancel1 = () => {
    this.setState({ visible1: false })
  }
  // 筛选
  filterChange = (val) => {
    console.log('val===', val);
    const { filterData } = this.state
    let data: any = []
    const data1: any = [
      {
        key: 'd08CodeList', name: '党员类型', value: [{ key: '1', name: '正式党员' }, { key: '2', name: '预备党员' }],
      },
      {
        key: 'isIntegrality', name: '档案完整度', value: [{ key: '1', name: '完整' }, { key: '0', name: '不完整' }],
      },
      {
        key: 'digitalType', name: '档案类别', value: [{ key: '1', name: '绿色档案' }, { key: '2', name: '蓝色档案' }, { key: '3', name: '红色档案' }, { key: '99', name: '其他' }],
      },
      {
        key: 'isVolunteer', name: '入党志愿书', value: [{ key: '1', name: '已上传' }, { key: '0', name: '未上传' }],
      },
      {
        key: 'isSure', name: '党员身份认定', value: [{ key: '1', name: '已认定' }, { key: '0', name: '未认定' }],
      },
      {
        key: 'isArchived', name: '归档情况', value: [{ key: '1', name: '已归档' }, { key: '0', name: '未归档' }],
      },
    ]
    const data2: any = [
      {
        key: 'd08CodeList', name: '党员类型', value: [{ key: '1', name: '正式党员' }, { key: '2', name: '预备党员' }],
      },
      {
        key: 'isIntegrality', name: '档案完整度', value: [{ key: '1', name: '完整' }, { key: '0', name: '不完整' }],
      }
    ]
    if (val.d08CodeList && val.d08CodeList.length == 1 && val.d08CodeList[0] == '2') {
      data = data2
    } else {
      data = data1
    }
    this.setState({
      filter: val,
      filterData: data
    }, () => this.getList(1, this.state['pageSize'], this.state.name));
  };
  search = (value) => {
    console.log("🚀 ~ index ~ value:", value)
    this.setState({
      name: isEmpty(value) ? undefined : value
    }, () => this.getList(1, this.state['pageSize'], value));
  };
  searchClear = (e) => {
    this.setState({
      name: e.target.value
    })
    if (!e.target.value) {
      this.getList(1, this.state['pageSize'], '');
    }
  };

  exportInfo = async () => {
    this.setState({
      jcInfoDownload: true,
    })
    await this['jcInfo'].submitNoModal();
    this.setState({
      jcInfoDownload: false,
    })
  };

  determineChange = (record) => {
    this.setState({
      visible: true,
      memInfo: record
    })
  }
  digitalArchives = async (record) => {
    // this['editDevlop'].destroy();
    // if (record && record['code']) {
    //   await this.props.dispatch({
    //     type: 'memBasic/findMem',
    //     payload: {
    //       code: record['code']
    //     }
    //   })
    // }
    // this['editDevlop'].open({ ...this.state.flowData, ...record });
    this['ElectronicArchives'].showModal(record?.code)
  }
  getFilenameList = (obj) => {
    this.setState({
      visible1: true,
      filenameList: obj
    })
  }
  daexp = async (record) => {
    if (record?.exportStatus == 0) {
      this['application'].open(record)
    }
    if (record?.exportStatus == 1) {
      Tip.info('操作提示', '导出正在申请中')
    }
    if (record?.exportStatus == 2) {
      this.setState({
        record,
        action: '/api/zunyi/digital/exportDigitalData'
      }, () => {
        this['downloadRef'] && this['downloadRef'].submitNoModal();
      })
    }


    // this.props.dispatch({
    //   type: 'archivesAdministration/exportDigitalData',
    //   payload: {
    //     data: {
    //       memOrgCode: record.memOrgCode,
    //       digitalLotNoList: [record.digitalLotNo],
    //       isExportAll: 0,
    //       type: '1'
    //     }
    //   }
    // })
  }
  logrxp = (record) => {
    this.setState({
      record,
      action: '/api/zunyi/digital/exportDigitalLogs'
    }, () => {
      this['downloadRef'] && this['downloadRef'].submitNoModal();
    })
    // this.props.dispatch({
    //   type: 'archivesAdministration/exportDigitalLogs',
    //   payload: {
    //     data: {
    //       memOrgCode: record.memOrgCode,
    //       digitalLotNoList: [record.digitalLotNo],
    //       isExportAll: 0,
    //       type: '1'
    //     }
    //   }
    // })
  }
  sq = (record) => {
    if (record?.fillStatus == 0) {
      this['supplement'].open(record)
    }
  }
  archiving = async (record) => {
    Modal.confirm({
      title: '提示',
      content: '是否确认归档',
      onOk: async () => {
        const { code = 500 } = await isArchived({ memCode: record.code, isArchived: 0 })
        if (code == 0) {
          Tip.success('操作提示', '该档案已归档')
          this.getList()
        }
      }
    })
    // const { code = 500 } = await isArchived({ memCode: record.code, isArchived: 1 })
    // if (code == 0) {
    //   Tip.success('操作提示', '该档案已归档')
    //   this.getList()
    // }
  }
  showFill = (record) => {
    let node: any = null;
    if (record?.fillStatus == 0) {
      node = <a onClick={() => this.sq(record)}>档案补充申请</a>
    }
    if (record?.fillStatus == 1) {
      node = <span style={{ color: '#CECECE' }}>档案补充申请中</span>
    }
    return node
  }
  render() {
    const { loading, archivesAdministration: { list1 = [], pagination1 = {} } } = this.props;
    const { dataInfo = {}, filterHeight, visible, confirmLoading, memInfo, filterData, visible1, filenameList } = this.state;
    const { current, pageSize } = pagination1;
    const org = getSession('org') || {};
    const content = (
      <div className={styles.content}>
        <div className={styles.rows}>
          <div style={{ backgroundColor: '#00CD29' }}></div>
          <div>已完成</div>
        </div>
        <div className={styles.rows}>
          <div style={{ backgroundColor: '#FF4343' }}></div>
          <div>未完成</div>
        </div>
        <div className={styles.rows}>
          <div style={{ backgroundColor: 'rgb(206, 206, 206)' }}></div>
          <div>未到达</div>
        </div>

      </div>
    );
    const columns = [
      {
        title: '序号',
        dataIndex: 'num',
        width: 60,
        align: 'center',
        render: (text, record, index) => {
          return (current - 1) * pageSize + index + 1
        }
      },
      {
        title: '党员姓名',
        align: 'center',
        dataIndex: 'name',
        width: 100,
      },
      {
        title: '性别',
        dataIndex: 'sexName',
        width: 80,
        align: 'center'
      },
      {
        title: '党员类型',
        dataIndex: 'd08Name',
        width: 100,
        align: 'center'
      },
      {
        title: '所在党支部',
        dataIndex: 'orgName',
        width: 300,
        // render: (text, record) => {
        //   return (
        //     <Fragment>
        //       <a onClick={()=>{}}>{text}</a>
        //     </Fragment>
        //   )
        // },
      },
      {
        title: <div>
          档案完整度
          <div>{content}</div>
          {/* <Popover content={content} title="">
            <LegacyIcon onClick={() => { }} style={{ cursor: 'pointer' }} type="question-circle" />
          </Popover> */}
        </div>,
        width: 360,
        children: [
          {
            title: '第一阶段',
            dataIndex: 'jd1',
            width: 60,
            render: (text, record) => {
              let find = record?.digitalCompletenessVOList.find(i => i.d222Code == '1')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', background: '#00CD29', cursor: 'pointer', margin: '0 auto' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', background: '#FF4343', cursor: 'pointer', margin: '0 auto' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', background: '#CECECE', cursor: 'pointer', margin: '0 auto' }}></div>
                )
              }

            }
          },
          {
            title: '第二阶段',
            dataIndex: 'jd2',
            width: 60,
            render: (text, record) => {
              let find = record?.digitalCompletenessVOList.find(i => i.d222Code == '2')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', background: '#00CD29', cursor: 'pointer', margin: '0 auto' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', background: '#FF4343', cursor: 'pointer', margin: '0 auto' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', background: '#CECECE', cursor: 'pointer', margin: '0 auto' }}></div>
                )
              }

            }
          },
          {
            title: '第三阶段',
            dataIndex: 'jd3',
            width: 60,
            render: (text, record) => {
              let find = record?.digitalCompletenessVOList.find(i => i.d222Code == '3')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', background: '#00CD29', cursor: 'pointer', margin: '0 auto' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', background: '#FF4343', cursor: 'pointer', margin: '0 auto' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', background: '#CECECE', cursor: 'pointer', margin: '0 auto' }}></div>
                )
              }

            }
          },
          {
            title: '第四阶段',
            dataIndex: 'jd4',
            width: 60,
            render: (text, record) => {
              let find = record?.digitalCompletenessVOList.find(i => i.d222Code == '4')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', background: '#00CD29', cursor: 'pointer', margin: '0 auto' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', background: '#FF4343', cursor: 'pointer', margin: '0 auto' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', background: '#CECECE', cursor: 'pointer', margin: '0 auto' }}></div>
                )
              }

            }
          },
          {
            title: '第五阶段',
            dataIndex: 'jd5',
            width: 60,
            render: (text, record) => {
              let find = record?.digitalCompletenessVOList.find(i => i.d222Code == '5')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', background: '#00CD29', cursor: 'pointer', margin: '0 auto' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', background: '#FF4343', cursor: 'pointer', margin: '0 auto' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', background: '#CECECE', cursor: 'pointer', margin: '0 auto' }}></div>
                )
              }

            }
          },
          {
            title: '其他',
            dataIndex: 'jd5',
            width: 60,
            render: (text, record) => {
              let find = record?.digitalCompletenessVOList.find(i => i.d222Code == '99')
              if (find) {
                if (find.integrality) {
                  return (
                    <div style={{ height: 6, width: '90%', background: '#00CD29', cursor: 'pointer', margin: '0 auto' }} onClick={() => this.getFilenameList(find)}></div>
                  )
                } else {
                  return (
                    <div onClick={() => this.getFilenameList(find)} style={{ height: 6, width: '90%', background: '#FF4343', cursor: 'pointer', margin: '0 auto' }}></div>
                  )
                }
              } else {
                return (
                  <div style={{ height: 6, width: '90%', background: '#CECECE', cursor: 'pointer', margin: '0 auto' }}></div>
                )
              }

            }
          },
        ]
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 120,
        render: (text, record, index) => {
          return (
            <>
              <a onClick={() => this.digitalArchives(record)}>电子档案</a>
              <Divider type="vertical" />
              <Dropdown overlay={(
                <Menu>
                  {/* <Menu.Item key="1" >
                  <a onClick={() => this.digitalArchives(record)}>电子档案</a>
                </Menu.Item> */}
                  <Menu.Item key={1} >
                    <a onClick={() => this.determineChange(record)}>{record?.isSure != 1 ? "未认定" : "取消认定"}</a>
                  </Menu.Item>
                  <Menu.Item key={2} >
                    {
                      record?.exportStatus == 0 &&
                      <a onClick={() => this.daexp(record)}>档案导出申请</a>
                    }
                    {
                      record?.exportStatus == 1 &&
                      <span style={{ color: '#CECECE' }}>导出申请中</span>
                    }
                    {
                      record?.exportStatus == 2 &&
                      <a onClick={() => this.daexp(record)}>档案导出</a>
                    }
                    {/* <a onClick={() => this.daexp(record)}>{
                    record?.exportStatus == 0 ? '档案导出申请' : record?.exportStatus == 1 ? '导出申请中' : '档案导出'
                  }</a> */}
                  </Menu.Item>
                  <Menu.Item key={3} >
                    <a onClick={() => this.logrxp(record)}>日志导出</a>
                  </Menu.Item>
                  {
                    record.isArchived == 1 &&
                    <Menu.Item key={4} >
                      {
                        this.showFill(record)
                      }
                    </Menu.Item>
                  }
                  {
                    (record?.isArchived == 0 && record?.isXJDW && record?.d08Code == 1) &&
                    <Menu.Item key={5} >
                      <a onClick={() => this.archiving(record)}>归档</a>
                    </Menu.Item>
                  }

                </Menu>)}
              >
                <a className="ant-dropdown-link">
                  业务操作 <DownOutlined />
                </a>
              </Dropdown>
            </>

          )
          return (
            <Fragment>
              <a onClick={() => this.digitalArchives(record)}>电子档案</a>
              <Divider type="vertical" />
              <a onClick={() => this.determineChange(record)}>{record?.isSure != 1 ? "未认定" : "取消认定"}</a>
              <Divider type="vertical" />
              <a onClick={() => this.daexp(record)}>{
                record?.exportStatus == 0 ? '档案导出申请' : record?.exportStatus == 1 ? '导出申请中' : '档案导出'
              }</a>
              <Divider type="vertical" />
              <a onClick={() => this.logrxp(record)}>日志导出</a>
              {
                this.showFill(record)
              }

              {/* <a onClick={() => this.sq(record)}>
                {
                  (record?.fillStatus == 0 && record?.isArchived == 1) ? '档案补充申请' : record?.fillStatus == 1 ? '档案补充申请中' : '档案补充'
                }
              </a> */}
              {
                (record?.isArchived == 0 && record?.isXJDW && record?.d08Code == 1) && <>
                  <Divider type="vertical" />
                  <Popconfirm title="是否确定归档" onConfirm={() => this.archiving(record)}>
                    <a >归档</a>
                  </Popconfirm>
                </>
              }
            </Fragment>
          )
        },
      },
    ];
    return (
      <div>
        <NowOrg extra={
          <React.Fragment>
            {/* <Button onClick={() => { this['WorkModalRef'].open("发展党员工作规程") }}>发展党员工作规程</Button> */}
            {/* <Button onClick={this.exportInfo} loading={this.state.jcInfoDownload}>导出</Button> */}
            <Search style={{ width: 200, marginLeft: 16 }} onSearch={this.search} onChange={this.searchClear} placeholder={'请输入检索关键词'} />
          </React.Fragment>
        } />
        <RuiFilter
          data={filterData}
          showLine={3}
          onChange={this.filterChange}
        />
        <WhiteSpace />
        <WhiteSpace />
        <ListTable scroll={{ y: filterHeight }} 
        
         columns={columns} data={list1} pagination={pagination1} onPageChange={this.onPageChange} />
        {/* <ExportInfo wrappedComponentRef={e => this['jcInfo'] = e}
          tableName={''}
          noModal={true}
          tableListQuery={{ memName: this.state.name, type: this.state.type, positionOrgCode: org['orgCode'], positionOrgName: org['name'], }}
          action={'/api/representative/export'}
        /> */}
        <Modal
          title={'认定党员'} visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}
          confirmLoading={confirmLoading}
        >
          <p>确定{memInfo?.isSure == 1 ? '取消' : ''}认定该党员吗？</p>
        </Modal>
        <Modal
          title={'档案材料'} visible={visible1} onOk={this.handleOk1} onCancel={this.handleCancel1}
          confirmLoading={confirmLoading}
        >
          <div>
            {
              filenameList.childs?.map((item, index) => {
                return (
                  <div style={{ display: 'flex' }}>
                    {
                      item.integrality ?
                        <div><LegacyIcon style={{ color: 'green', marginRight: 4 }} type="check" /></div> :
                        <div><LegacyIcon style={{ color: 'red', marginRight: 4 }} type="close" /></div>
                    }
                    <div style={{ paddingRight: '4px' }}>{item.d222Name}</div>
                  </div>
                )
              })
            }
          </div>
        </Modal>
        <ElectronicArchives ref={e => this['ElectronicArchives'] = e} />
        <EditDevlop wrappedComponentRef={e => this['editDevlop'] = e} onsubmit={this.getList} {...this.props} tipMsg={this.state.tipMsg} />
        <ExportInfo wrappedComponentRef={e => this['downloadRef'] = e} tableName={''} noModal={true} tableListQuery={{
          memOrgCode: this.state.record.memOrgCode,
          digitalLotNoList: [this.state.record.digitalLotNo],
          isExportAll: 0,
          type: '1'
        }} action={this.state.action || ''} />
        <Application ref={e => this['application'] = e} onsubmit={this.getList} />
        <Supplement ref={e => this['supplement'] = e} onsubmit={this.getList} />

      </div>
    );
  }
}
