// 流动党员-流入管理-已纳入支部管理
import React, { Fragment } from 'react';
import { connect } from 'dva';
import { Button, Divider, Input, Dropdown, Menu, Select } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import moment from 'moment';
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import { isEmpty, setListHeight, changeMsgTip } from '@/utils/method';
import NowOrg from 'src/components/NowOrg';
import ExportInfo from '@/components/Export';
import { getSession } from '@/utils/session';
import FlowAddOrEdit from "./flowAddOrEdit"
import FlowAddOrExamine from './flowAddORexamine';

const Search = Input.Search;
const Option = Select.Option;
//@ts-ignore
@connect(({ unit, commonDict, loading, flowMem }) => ({
  flowMem,
  unit,
  commonDict,
  loading: loading.effects['unit/getList'],
}))
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {}, //筛选器
      memName: undefined, //搜索框
      view: false,
      subordinate: getSession('subordinate'),
      exportLoading: false, //导出loading
      searchLoading: false, //查询loading
    };
  }

  exportInfo = async () => {
    this.setState({
      exportLoading: true,
    });
    await this['exportRef'].submitNoModal();
    this.setState({
      exportLoading: false,
    });
  };
  filterChange = (val) => {
    this.setState(
      {
        filter: val,
      },
      () => this.getList(val),
    );
  };
  handleSearch = (e) => {
    this.setState(
      {
        memName: e,
        searchLoading: true,
      },
      () => {
        this.getList({ memName: e });
      },
    );
  };
  searchChange(e) {
    this.setState({
      memName: e.currentTarget.value || undefined,
    });
  }

  selectChange = (e) => {
    const data = {}
    if (e) {
      data['status'] = e
    }
    this.setState({
      selectkey: e
    })
    this.getList({ ...data })
  }

  getList = async (params?: object) => {
    const { filter, memName } = this.state;
    // return
    const org = getSession('org') || {};
    await this.props.dispatch({
      type: 'flowMem/inflowOrganizationList',
      payload: {
        data: {
          pageNum: 1,
          pageSize: 10,
          name: memName || '',
          // orgCode: org['orgCode'],
          ...filter,
          sourceType: 1,
          ...params,
        },
      },
    });
    this.setState({
      searchLoading: false,
    });
  };
  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org = getSession('org') || {};
    const subordinate = getSession('subordinate') || '0';
    if (
      (!isEmpty(this.state['orgCode']) && this.state['orgCode'] !== org['orgCode']) ||
      subordinate !== this.state.subordinate
    ) {
      this.setState(
        {
          orgCode: org['orgCode'],
          subordinate,
        },
        () => {
          this.getList({ orgCode: org['orgCode'] });
        },
      );
    }
  }
  componentDidMount() {
    const org = getSession('org') || {};
    setListHeight(this);
    this.setState({ orgCode: org['orgCode'] });
    this.getList();
  }

  render() {
    const { filterHeight, filter, memName, subordinate, searchLoading, selectkey = '' } = this.state;
    const org = getSession('org') || {};
    const {
      loading,
      commonDict,
      flowMem: { list = [], pagination = { pageNum: 1, pageSize: 10 } },
    } = this.props;

    const filterData = [
      {
        key: 'statusList',
        name: '审批状态',
        value: commonDict[`dict_d202`],
      },
    ];
    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 40,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '组织名称',
        dataIndex: 'name',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          return (
            <a
              onClick={() => {
                this['flowAddOrEditRef'].open(1, false, record);
              }}
            >
              {text}
            </a>
          );
        },
      },
      {
        title: '党组织类型',
        dataIndex: 'd01Name',
        align: 'center',
        width: 100,
      },
      {
        title: '党组织成立类型',
        dataIndex: 'd200Name',
        align: 'center',
        width: 100,
      },
      {
        title: '是否有效',
        dataIndex: 'isEnableName',
        align: 'center',
        width: 100,
      },
      {
        title: '联系人',
        dataIndex: 'contacter',
        align: 'center',
        width: 100,
      },
      {
        title: '联系方式',
        dataIndex: 'contactPhone',
        align: 'center',
        width: 100,
      },
      {
        title: '审批状态',
        dataIndex: 'statusName',
        align: 'center',
        width: 100,
      },
      {
        title: '审批时间',
        dataIndex: 'auditTime',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '审批人',
        dataIndex: 'auditUserName',
        align: 'center',
        width: 100,
      },
      {
        title: '审批单位',
        dataIndex: 'auditOrgName',
        align: 'center',
        width: 100,
      },
      {
        title: '成立日期',
        dataIndex: 'createDate',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 150,
        render: (text, record) => {
          return (
            <Fragment>
              {((record.status == '0' && record.d200Code == "1") || record.status == '3') &&
                <Fragment>
                  <a
                    onClick={() => {
                      this['flowAddOrExamineRef'].open(record, '', 1);
                    }}
                  >
                    审核
                  </a>
                  <Divider type="vertical" />
                </Fragment>
              }
              <a
                onClick={() => {
                  this['flowAddOrEditRef'].open(1, false, record);
                }}
              >
                查看
              </a>
              {/* {(record.status == '1' || record.status == '2') && */}
              {((record.status == '1' && record.d200Code == "1") || record.status == '2') &&
                <Fragment>
                  <Divider type="vertical" />
                  <a
                    onClick={() => {
                      this['flowAddOrExamineRef'].open(record, "cancel", 1);
                    }}
                  >
                    撤销
                  </a>
                </Fragment>
              }
              {/* <Divider type="vertical" /> */}
              {/* <Dropdown
                overlay={
                  <Menu>
                    <Menu.Item>
                      <a
                        onClick={() => {
                          this['reviewRef'].open('inFlow', record);
                        }}
                      >
                        民主评议情况
                      </a>
                    </Menu.Item>
                    <Menu.Item>
                      <a
                        onClick={() => {
                          this['orgLifeRef'].open('inFlow', record);
                        }}
                      >
                        组织生活情况
                      </a>
                    </Menu.Item>
                    <Menu.Item>
                      <a
                        onClick={() => {
                          this['feedbackRef'].open('inFlow', record);
                        }}
                      >
                        表现反馈
                      </a>
                    </Menu.Item>
                  </Menu>
                }
              >
                <a className="ant-dropdown-link">
                  业务操作 <DownOutlined />
                </a>
              </Dropdown> */}
            </Fragment>
          );
        },
      },
    ];
    return (
      <Fragment>
        <NowOrg
          extra={
            <Fragment>
              {/* <span style={{ marginRight: 2 }}>审批状态：</span> 
              <Select style={{width: '120px'}} value={selectkey} placeholder="请选择" onSelect={(e) => this.selectChange(e)} allowClear onClear={() => this.selectChange(undefined)} maxTagTextLength={999}>
                <Option value="0">待审批</Option>
                <Option value="1">审批通过</Option>
                <Option value="2">审批未通过</Option>
                <Option value="3">撤销</Option>
              </Select> */}
              {/* <Button
                style={{ marginLeft: 16 }}
                onClick={this.exportInfo}
                loading={this.state.exportLoading}
              >
                导出
              </Button> */}
              <Search
                loading={searchLoading}
                allowClear
                placeholder="请输入组织名称"
                enterButton={'查询'}
                style={{ width: 200, marginLeft: 16 }}
                onSearch={(e) => {
                  this.handleSearch(e);
                }}
                onChange={(e) => {
                  this.searchChange(e);
                }}
              />
            </Fragment>
          }
        />
        <RuiFilter
          data={filterData}
          openCloseChange={() => setListHeight(this, 20)}
          onChange={this.filterChange}
        />
        <ListTable
          rowKey={'id'}
          scroll={{ y: filterHeight, x: 100 }}
          
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={(page, pageSize) => {
            this.getList({ pageNum: page, pageSize });
          }}
        />
        <ExportInfo
          wrappedComponentRef={(e) => (this['exportRef'] = e)}
          tableName={''}
          noModal={true}
          tableListQuery={{
            ...filter,
            pageNum: pagination.pageNumber || 1,
            pageSize: pagination.pageSize || 10,
            subordinate,
            memName,
            orgCode: org['orgCode'],
            type: '2',
          }}
          action={'/api/mem/flow/exportXsl'}
        />
        <FlowAddOrEdit
          wrappedComponentRef={(e) => (this['flowAddOrEditRef'] = e)}
          onOk={this.getList}
        />
        <FlowAddOrExamine
          wrappedComponentRef={(e) => (this['flowAddOrExamineRef'] = e)}
          onOk={() => {
            this.setState({
              selectkey: ''
            })
            this.getList()
          }}
        />
      </Fragment>
    );
  }
}
