import React, { useEffect, useRef } from 'react';

const LazyImage = ({ dataSrc, alt, className,onLoad }) => {
    const imgRef = useRef(null);

    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src; // 将 data-src 的值赋给 src
                    observer.unobserve(img); // 停止观察
                }
            },
            { root: null, rootMargin: '0px', threshold: 0.1 }
        );

        if (imgRef.current) {
            observer.observe(imgRef.current);
        }

        return () => {
            if (imgRef.current) {
                observer.unobserve(imgRef.current);
            }
        };
    }, [dataSrc]);

    return <img ref={imgRef} data-src={dataSrc} alt={alt} className={className} onLoad={onLoad}/>;
};

export default LazyImage;