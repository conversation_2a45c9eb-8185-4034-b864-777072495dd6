import React, { Fragment, useEffect, useRef, useState, useImperativeHandle } from 'react';
import ListTable from '@/components/ListTable';
import moment from 'moment';
import { But<PERSON>, Divider, Popconfirm, Modal, Input } from 'antd';
import { countrysideGetList, delUnitCountryside, getMemList } from '@/pages/[unit]/services/index';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import Add from '../subpage/addoredit/worker/components/add';
import LeaveModal from './components/LeaveModal';
import Tip from '@/components/Tip';
import NowOrg from '@/components/NowOrg';
import { getSession } from '@/utils/session';
import { setListHeight } from '@/utils/method';
import RuiFilter from '@/components/RuiFilter';
import ExportInfo from '@/components/Export/index';
import _isEmpty from 'lodash/isEmpty';

const index = (props: any) => {
  const addRef:any = useRef();
  const addRef2:any = useRef();
  const leaveRef:any = useRef();
  const downloadRef:any = useRef();

  const { orgCode = '' } = getSession('org') || {orgCode: ''};
  const subordinate = getSession('subordinate') || '0';
  const { unit: { basicInfo = {} } = {} } = props;
  const [pagination, setPagination] = useState<any>({ page: 1, pageSize: 10, total: 0});
  const [list, setList] = useState([]);
  const [search, setSearch] = useState<any>({});
  const [searchName, setSearchName] = useState<any>();
  const [loading, setLoading] = useState<any>(false);

  const action = async (p = {}) => {

    const {
      code = 500,
      data: { list = [], pageNumber: pageNum = 1, pageSize = 20, totalRow: total = 0 } = {},
    } = await getMemList({
      data:{
        pageSize: pagination.pageSize,
        pageNum: pagination.page,
        orgCode,
        unitCode: basicInfo.code,
        ...search,
        name: searchName,
        ...p,
      }
    });
    if (code === 0) {
      setList(list);
      setPagination({ page: pageNum, total, pageSize,current:pageNum });
    }
  };
  const confirm = async (record: any) => {
    const {code} = record;
    const {code:resCode = 500} = await delUnitCountryside({code});
    if(resCode === 0){
      Tip.success('操作提示', '操作成功');
      action();
    }
  };
  const filterChange = async (val) => {
    setSearch(val)
    action({...val,pageNum: 1 });
  };

  const columns = [
    {
      title: '人员名称',
      dataIndex: 'memName',
      width: 100,
    },
    {
      title: '性别',
      dataIndex: 'sexName',
      width: 50,
    },
    // {
    //   title: '是否本单位人员',
    //   dataIndex: 'memTypeName',
    //   width: 200,
    // },
    {
      title: '身份证',
      dataIndex: 'memIdcard',
      width: 180,
      render: (text,record) => {
        if(typeof text === 'string' && !_isEmpty(text)){
          let newVal=text.replace(/(\d{3})\d*([0-9a-zA-Z]{4})/, "$1***********$2");
          if(text.indexOf("*") > 0){
            return text
          }
          return (
            <span>{newVal}</span>
          );
        }else {
          return ''
        }
      }
    },
    {
      title: '学历',
      dataIndex: 'd07Name',
      width: 80,
    },
    {
      title: '人员类别',
      dataIndex: 'type',
      width: 140,
      render:(text)=>{
        return text === '1' ? '村（社区）工作者' : text === '2' ? '村（社区）后备干部' : ''
      }
    },

    {
      title: '出生日期',
      dataIndex: 'birthday',
      width: 140,
      render: (text) => {
        if (text) {
          return moment(text).format('YYYY-MM-DD');
        }
        return null;
      },
    },
    // {
    //   title: '单位名称',
    //   dataIndex: 'unitName',
    //   width: 200,
    // },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      render: (text, record) => {
        const {type = ''} = record;
        return (
          <span>
              <a onClick={() => {
                if(type === '1'){
                  addRef.current.open(record);
                }else if(type === '2'){
                  addRef2.current.open(record);
                }
              }}>编辑</a>
              <Divider type="vertical"/>
              <Popconfirm title="确定要离开？" onConfirm={() => {
                leaveRef.current.open(record);
              }}>
               <a className={'del'}>离开</a>
              </Popconfirm>
            </span>
        );
      },
    },
  ];

  const filterData = [
    {
      key: 'type', name: '人员类别', value: [{key:'1',name:'村（社区）工作者'},{key:'2',name:'村（社区）后备干部'}],
    },
  ];

  useEffect(() => {
    action({ pageNum: 1 });
  }, []);
  return (
    <div style={{margin:'0 16px'}}>
      {/* <NowOrg extra={
        <React.Fragment>
          <Button onClick={async ()=>{
            setLoading(true);
            await downloadRef.current.submitNoModal();
            setLoading(false);
          }} loading={loading}>导出</Button>
          <Button type={'primary'}
                  style={{marginLeft:10}}
                  onClick={() => {
                    if(addRef.current.open){
                      addRef.current.open();
                    }
                  }}
                  icon={<LegacyIcon type={'plus'}/>}>添加村(社区)工作者</Button>
          <Button type={'primary'}
                  style={{marginLeft:10}}
                  onClick={() => {
                    if(addRef2.current.open){
                      addRef2.current.open();
                    }
                  }}
                  icon={<LegacyIcon type={'plus'}/>}>添加村(社区)后备干部</Button>
          <Input.Search style={{width:200,marginLeft:16}} onSearch={(val)=>{
            setSearchName(val);
            action({name:val,pageNum: 1 });
          }} placeholder={'请输入检索关键词'}/>
        </React.Fragment>
      }/> */}
      <div style={{marginBottom:'10px', display:'flex', justifyContent:'end'}}>
        {/* <Button onClick={async ()=>{
          setLoading(true);
          await downloadRef.current.submitNoModal();
          setLoading(false);
        }} loading={loading}>导出</Button> */}
        <Button type={'primary'}
                style={{marginLeft:10}}
                onClick={() => {
                  if(addRef.current.open){
                    addRef.current.open();
                  }
                }}
                icon={<LegacyIcon type={'plus'}/>}>添加村(社区)工作者</Button>
        <Button type={'primary'}
                style={{marginLeft:10}}
                onClick={() => {
                  if(addRef2.current.open){
                    addRef2.current.open();
                  }
                }}
                icon={<LegacyIcon type={'plus'}/>}>添加村(社区)后备干部</Button>
        <Input.Search style={{width:200,marginLeft:16}} allowClear onSearch={(val)=>{
          setSearchName(val);
          action({name:val,pageNum: 1 });
        }} placeholder={'输入姓名查找'}/>
      </div>
      <RuiFilter data={filterData}
                 onChange={filterChange}
                 openCloseChange={() => {
                   setListHeight(this, 20);
                 }}/>
                 <div style={{marginTop:20}}/>
      <ListTable 
        scroll={{y:440}}
        columns={columns}
        data={list}
        pagination={pagination}
        onPageChange={(page: any, pageSize: any) => {
          action({ pageNum: page, pageSize });
      }}/>
      <Add ref={addRef} {...props} pageType={'1'} onOK={()=>{
        action({ pageNum: 1});
      }}/>
      <Add ref={addRef2} pageType={'2'} {...props} onOK={()=>{
        action({ pageNum: 1});
      }}/>
      <LeaveModal ref={leaveRef} {...props} onOK={()=>{
        action({ pageNum: 1});
      }}/>
      <ExportInfo wrappedComponentRef={downloadRef}
                    tableName={''}
                    noModal={true}
                    tableListQuery={{...search,orgCode,name: searchName,unitCode: basicInfo.code}}
                    action={'/api/unit/exportMem'}
        />
    </div>
  );
};
export default index;
