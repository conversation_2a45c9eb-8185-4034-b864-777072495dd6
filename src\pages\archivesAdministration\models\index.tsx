import { listPageModel, ListPageStore } from "@/utils/common-model";
import modelExtend from "dva-model-extend";
import { getList, getDevList, exportDigitalData, exportDigitalLogs } from '../service';
import Notice from '../../../components/Notice';
import { add } from '@/pages/flowMem/service';
import { getSession } from '@/utils/session';
import { fileDownloadHeader,fileDownload,fileDownloadbyUrl } from '@/utils/method';
import Tip from "@/components/Tip";
const archivesAdministration = modelExtend(listPageModel, {
  namespace: 'archivesAdministration',
  state: {
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(location => {
        const { pathname, query } = location;
        if (pathname === '/behalf') {
          const org = getSession('org') || {};
          const defaultParas = {
            pageNum: 1,
            pageSize: 10,
          };
          const dictData = ['dict_d44', 'dict_d45', 'dict_d46', 'dict_d61'];
          for (let obj of dictData) {
            dispatch({
              type: 'commonDict/getDictTree',
              payload: {
                data: {
                  dicName: obj
                }
              }
            });
          }
          dispatch({
            type: 'getList',
            payload: {
              data: {
                orgCode: org['orgCode'],
                ...defaultParas,
                ...query,
              }
            }
          })
        }
      });
    }
  },
  effects: {
    *getList({ payload }, { call, put }) {
      const info = yield call(getList, payload);
      const { data: { list = [], ...pagination } = {}, message = '操作失败', code = null } = info;
      if (code === 0) {
        yield put({
          type: 'updateState',
          payload: {
            list1: list,
            pagination1: {
              current: pagination['pageNumber'],
              pageSize: pagination['pageSize'],
              total: pagination['totalRow'],
            }
          }
        });
      } else {
        Notice("操作提示", message, "exclamation-circle-o", "orange");
      }
    },
    *getDevList({ payload }, { call, put }) {
      const info = yield call(getDevList, payload);
      const { data: { list = [], ...pagination } = {}, message = '操作失败', code = null } = info;
      if (code === 0) {
        yield put({
          type: 'updateState',
          payload: {
            list1: list,
            pagination1: {
              current: pagination['pageNumber'],
              pageSize: pagination['pageSize'],
              total: pagination['totalRow'],
            }
          }
        });
      } else {
        Notice("操作提示", message, "exclamation-circle-o", "orange");
      }
    },
    *exportDigitalData({ payload }, { call, put }) {
      const info = yield call(exportDigitalData, payload);
      const { data, message = '操作失败', code = null } = info;
      if (code === 0) {
        let dataApi = sessionStorage.getItem('dataApi') || "";
        debugger;
        console.log(`/api${data.url}`,data?.url.split('/').pop());
        
        fileDownloadbyUrl(`/api${data.url}`, data?.url.split('/').pop());
      } else {
       Tip.error('操作提示', message);
      }
    },
    *exportDigitalLogs({ payload }, { call, put }) {
      const info = yield call(exportDigitalLogs, payload);
      const { data, message = '操作失败', code = null } = info;
      if (code === 0) {
        let dataApi = sessionStorage.getItem('dataApi') || "";
        fileDownloadbyUrl(`/api${data.url}`, data?.url.split('/').pop());
      } else {
       Tip.error('操作提示', message);
      }
    },
    
  },
  reducers: {
    success(state, { payload }) {
      return { ...state, ...payload };
    },
  }
});
export default archivesAdministration
