
import React, { Fragment, useEffect, useRef, useState } from 'react';
import moment from 'moment';
import ListTable from '@/components/ListTable';
import Tip from '@/components/Tip';
import Add from './add';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Button, Divider, Input, Popconfirm, Tabs } from 'antd';
import {townshipGetList, townshipDelByCode} from '@/pages/org/services/org';

const index = (props) => {
  const newAddRef: any = useRef();
  let filterHeight = `calc(100vh - ${288}px)`;
  const [pagination, setPagination] = useState<any>({ pageSize: 20, current: 1, total: 0 });
  const [listLoading, setListLoading] = useState(false);
  const [list, setList] = useState<any>([]);

  const columns = [
    {
      title: '序号',
      dataIndex: 'num',
      width: 50,
      align: 'center',
      render: (text, record, index) => {
        return (pagination['current'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    {
      title: '年度',
      width: 100,
      align: 'center',
      dataIndex: 'year',
    },
    {
      title: '创建时间',
      width: 100,
      align: 'center',
      dataIndex: 'createTime',
      render:(text)=>{
        return moment(text).format('YYYY-MM-DD')
      }
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      align: 'center',
      render: (text, record) => {
        return (
          <div>
            <a onClick={() => {
              newAddRef.current.open(record);
            }}>编辑</a>
            <Divider type="vertical" />
            <Popconfirm title="确定要删除吗？" onConfirm={async () => {
              const {code = 500 } = await townshipDelByCode({code:record.code});
              if(code === 0){
                Tip.success('操作提示', '操作成功');
                getLists({ pageNum: 1 });
              }
            }}>
              <a href={'#'} className={'del'}>删除</a>
            </Popconfirm>
          </div>
        )
      },
    }
  ];

  const getLists = async (p = {}) => {
    const { org:{ basicInfo={} } = {} } = props;
    setListLoading(true);
    const {
      code = 500,
      data: { pageNumber: current = 1, pageSize = 20, totalRow: total = 0 ,list=[]} = {},
    } = await townshipGetList({
      data: {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
        orgCode: basicInfo?.code,
        ...p,
      },
    });
    setListLoading(false);
    if (code === 0) {
      setList(list);
      setPagination({ current, total, pageSize });
    }
  };

  useEffect(() => {
    getLists({ pageNum: 1 });
  }, []);



  return (
    <Fragment>
      <Button
        type={'primary'}
        icon={<LegacyIcon type={'plus'} />}
        onClick={() => newAddRef.current.open()}
        style={{ marginLeft: 16 }}
      >
        新增
      </Button>
      <div style={{ margin: 16 }}>
        <ListTable
          // scroll={{ y: filterHeight }}
          
          columns={columns}
          data={list}
          pagination={pagination}
          showQuickJumper={true}
          onPageChange={(page: any, pageSize: any) => {
            getLists({ pageNum: page, pageSize });
          }}
        />
        <Add
          ref={newAddRef}
          {...props}
          onOK={() => {
            getLists({ pageNum: 1 });
          }} />
      </div>
    </Fragment>
  );
};
export default index;
