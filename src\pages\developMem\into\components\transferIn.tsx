/**
 * 省外关系转入
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Col, DatePicker, Input, InputNumber, Modal, Radio, Row } from 'antd';
import {connect} from "dva";
import Tip from "@/components/Tip";
import Base from '@/pages/mem/manage/components/membasic/BasicInfo';
import { findDictCodeName, unixMoment, correctIdcard } from '@/utils/method';
import {addMem} from '@/pages/transfer/services/index.js';
import _isEmpty from 'lodash/isEmpty';
import _iisArray from 'lodash/isArray';
import _get from 'lodash/get';


// @ts-ignore
@connect(({transferIn,memBasic})=>({transferIn,memBasic}),undefined,undefined,{forwardRef:true})
class index extends React.Component<any,any>{
  static open(){}
  static close(){}
  constructor(props){
    super(props);
    this.state={
      visible:false,
      key:new Date().valueOf(),
    };
    index.open=this.open;
  }
  open=()=>{
    this.setState({
      visible:true,
    })
  };
  handleOk=()=>{
    const { memBasic: { basicInfo = {} } = {} } = this.props;
    this.props.form.validateFieldsAndScroll(async (err, val) => {
      if (!err) {
        if (val['name'] != basicInfo['name'] || val['idcard'] != basicInfo['idcard']) {
        let result =await correctIdcard(val['name'],val['idcard']);
      if(result['code']!='200'){
        this.props.form.setFields({
          idcard:{
            value:val['idcard'],
            errors:[new Error('经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')]
          }
        })
        Tip.error('操作提示','经验证，该身份证号和姓名不匹配，请认真检查姓名及身份证号，不能有空格。')
        return
      }else{
        val['idCardReason']=result['reason']
        val['idCardReasonName']=result['reasonName']
      }
      }
      let timeArr = [
        'birthday',
        'applyDate',
        'activeDate',
        'objectDate',
        'fullMemberDate',
        'lostContactDate',
        'joinOrgPartyDate',
        'joinOrgDate',
        'stopPartyDate',
        'recoverPartyDate',
        'outlandTransferInYear',
      ];
      val = unixMoment(timeArr, val);
      //bool改为int
      ['isDispatch', 'isFarmer','hasUnitStatistics','hasUnitProvince','hasExSituPovertyAlleviation'].map((item) => {
        val[`${item}`] = val[`${item}`] == 1 ? 1 :  val[`${item}`] == 0 ? 0 : undefined;
      });

      // const checkInfo = this['CheckDictIsRight'].checkDictValue({
      //   dictList:[{formKey:'readingProfessional',dictName:"dict_d88" },'d27'],
      //   val,
      // });
      // if(!checkInfo?.flag){
      //   this.props.form.setFields({
      //     [checkInfo?.fromKey]: { errors: [new Error('请重新审核此项数据是否选择正确')] }
      //   });
      //   return;
      // }


      // 增加字典表的name
      val = findDictCodeName(
        ['d49', 'd18', 'd19', 'd20', 'd21', 'd07', 'd09', 'd11', 'd27','d126',
          'd28', 'd06','d89','d04','d07','d88','readingProfessional','d135','d136','d145','d154'],
        val,
        basicInfo,
      );
      val['d08Name'] = val['d08Code'] === '1' ? '正式党员' : '预备党员';
      val['sexName'] = val['sexCode'] === '1' ? '男' : '女';

      // val['unitCode'] =typeof val['unitCode'] == 'object' ? _get(val,'unitCode[0].code') : val?.unitCode

      // 增加组织zbcode
      ['orgCode'].map((item) => {
        if (!_isEmpty(_get(val, `${item}[0]`, []))) {
          if (_isEmpty(basicInfo)) {
            val[`orgZbCode`] = val[`${item}`][0]['zbCode'];
            val['memOrgCode'] = val[`${item}`][0]['orgCode'];
            val[`${item}`] = val[`${item}`][0]['code'];
          } else {
            if (_iisArray(val[`${item}`]) && val[`${item}`][0] !== basicInfo[`${item}`]) {
              val[`orgZbCode`] = val[`${item}`][0]['zbCode'];
              val['memOrgCode'] = val[`${item}`][0]['orgCode'];
              val[`${item}`] = val[`${item}`][0]['code'];
            } else {
              val[`orgZbCode`] = basicInfo['orgZbCode'];
              val['memOrgCode'] = basicInfo['memOrgCode'];
            }
          }
        }
      });
      ['branchOrgKey'].map((item) => {
        if (!_isEmpty(_get(val, `${item}[0]`, []))) {
          if (_isEmpty(basicInfo)) {
            val['branchOrgCode'] = val[`${item}`][0]['orgCode'];
            val['branchOrgZbCode'] = val[`${item}`][0]['zbCode'];
            val['branchOrgName'] = val[`${item}`][0]['name'];
            val[`${item}`] = val[`${item}`][0]['code'];
          } else {
            if (_iisArray(val[`${item}`]) && val[`${item}`][0] !== basicInfo[`${item}`]) {
              val['branchOrgCode'] = val[`${item}`][0]['orgCode'];
              val['branchOrgZbCode'] = val[`${item}`][0]['zbCode'];
              val['branchOrgName'] = val[`${item}`][0]['name'];
              val[`${item}`] = val[`${item}`][0]['code'];
            } else {
              val['branchOrgZbCode'] = basicInfo['branchOrgZbCode'];
              val['branchOrgCode'] = basicInfo['branchOrgCode'];
              val['branchOrgName'] = basicInfo['branchOrgName'];

            }
          }
        }
      });

      // 中间交换区
      if(val['hasUnitStatistics'] == 1 && val['_d04Code']){
        val['d04Code'] = this.state.unitInformationD04Code;
      }
      if(val['hasUnitStatistics'] == 1 && val['__d04Code']){
        val['d04Code'] = val['__d04Code'];
      }
      val['middleUnitCode'] = typeof val['middleUnitCode'] == 'object' ? val['middleUnitCode']?.code : val['middleUnitCode'];

      if(!basicInfo?.code){
        val['provinceOrgName'] = typeof val['provinceOrgCode'] == 'object' ? val['provinceOrgCode'].name : basicInfo.provinceOrgName;
        val['provinceOrgCode'] = typeof val['provinceOrgCode'] == 'object' ? val['provinceOrgCode'].code : basicInfo.provinceOrgCode;
      }
      this.setState({
        loading:true,
      })
      const res = await addMem({
        data:{
          ...val
        }
      });
      const { code = 500, data = {} } = res;
      if (code === 0) {
        Tip.success('操作提示','人员转入成功，请在党员列表中直接查看');
        this.handleCancel();
        this.props.refresh();
      }
      this.setState({
        loading:false,
      })
      // await addMem({
      //   data:{
      //     ...val
      //   }
      // }).then(res=>{
      //   const { code = 500, data = {} } = res;
      //   if (code === 0) {
      //     Tip.success('操作提示','省外关系转入已提交');
      //     this.handleCancel();
      //     this.props.refresh();
      //   }
      // })
      }
    });
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
    })
  };
  render(){
    const {visible}=this.state;
    return (
      <Modal
        destroyOnClose
        title="省外（含系统外，如军队、银行等单位）转入"
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        width={1300}
        confirmLoading={this.state.loading}
        bodyStyle={{height:570,overflow:'auto'}}
      >
        <Base wrappedComponentRef={(e)=>this['base']=e} hideSave={true} isTransfer={true} {...this.props} />
      </Modal>
    );
  }
}
export default Form.create()(index)


