import React from 'react';
import { Col, Row } from 'antd';
import { formLabel } from '@/utils/method';
import style from './index.less';

export const formItemLayout4 = {
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
};
const index = (props: any) => {
  const { required, label, code, tipMsg, formItem, formItemLayout, style: propsStyle = { fontSize: 14 } } = props;
  const { labelCol, wrapperCol: { sm: { span = 12 } = {} } = {} } = formItemLayout;
  let antColXS = `ant-col-xs-${labelCol?.xs?.span}`;
  let antColSM = `ant-col-sm-${labelCol?.sm?.span}`;
  return (
    <Row className={style.items}>
      <Col
        className={`${required ? style.label : style.label2} ant-col ${antColXS} ${antColSM}`}
      >
        <label htmlFor={code} style={propsStyle}>
          {formLabel(label, tipMsg[code])}
        </label>
      </Col>
      <Col span={span} className={style.desc}>
        {formItem(formItemLayout4, code)}
      </Col>
    </Row>
  );
};
export default index;
