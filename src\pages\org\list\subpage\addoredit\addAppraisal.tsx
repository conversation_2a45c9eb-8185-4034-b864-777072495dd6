/**
 * 添加民主评议
 */
import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, Modal, Select } from 'antd';
import moment from 'moment';
import Tip from '@/components/Tip';
import { formLabel } from '@/utils/method';
import Date from '@/components/Date';

const FormItem = Form.Item;
const { Option } = Select;
const { TextArea } = Input;
const formItemLayout1 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
    };
    index.showModal = this.showModal;
  }

  static showModal() {
  };

  showModal = () => {
    this.setState({
      visible: true,
    });
  };

  handleOk = () => {
    const { basicInfo = {} } = this.props.org;
    const { children, title, dataInfo = {} } = this.props;
    setTimeout(() => {
      this.props.form.validateFieldsAndScroll(async (err, val) => {
        if (!err) {
          let obj = undefined;
          // if (val['year']) {
          //   val['year'] = moment(val['year']).valueOf();
          // }
          ['startTime', 'endTime'].forEach(key => {
            if (val[key]) {
              val[key] = moment(val[key]).valueOf();
            }
          });
          if (dataInfo['code']) {
            obj = await this.props.dispatch({
              type: 'org/appraisalUpdate',
              payload: {
                data: {
                  ...dataInfo,
                  ...val,
                },
              },
            });
          } else {
            obj = await this.props.dispatch({
              type: 'org/appraisalAdd',
              payload: {
                data: {
                  ...val,
                  orgCode: basicInfo['code'],
                },
              },
            });
          }
          if (obj && obj['code'] === 0) {
            Tip.success('操作提示', dataInfo['code'] ? '修改成功' : '新增成功');
            this.props.queryList();
            this.handleCancel();
          }
        }
      });
    }, 150);
  };
  handleCancel = () => {
    this.setState({
      visible: false,
    });
  };
  disabledDate = (current) => {
    return current < moment(moment()).subtract(5, 'years') || current > moment();
  };

  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { children, title, dataInfo = {}, tipMsg = {} } = this.props;
    return (
      <React.Fragment>
        {
          children ? React.cloneElement(children as any, {
            onClick: this.showModal,
            key: 'container',
          }) : null
        }
        {
          this.state.visible &&
          <Modal
            title={title || '请输入标题'}
            visible={this.state.visible}
            onOk={() => this.handleOk()}
            onCancel={this.handleCancel}
            width={800}
            className='add_randp_modal'
            maskClosable={false}
            destroyOnClose
          >
            <Form {...formItemLayout1}>
              {/* <FormItem
                label={formLabel('评议年度', tipMsg['year'])}
              >
                {getFieldDecorator('year', {
                  initialValue: dataInfo['year'] ? moment(dataInfo['year']).format('YYYY') : undefined,
                  rules: [{ required: true, message: '请输入评议年度' },{pattern:/[12]\d{3}/, message:'请输入正确年份'}],
                  // <DatePicker disabledDate={this.disabledDate} placeholder="请选择" style={{ width: '100%' }} picker="year" />
                })(
                  <Input maxLength={4} addonAfter="年"/>
                )}

              </FormItem> */}
              {/*<FormItem*/}
              {/*    label={formLabel('应评议人数')}*/}
              {/*>*/}
              {/*    {getFieldDecorator('peopleToBeReviewed', {*/}
              {/*        initialValue: dataInfo['peopleToBeReviewed'],*/}
              {/*        rules: [{ required: true, message: '应评议人数' }],*/}
              {/*    })(*/}
              {/*        <InputNumber placeholder="应评议人数" style={{ width: '100%' }} />*/}
              {/*    )}*/}
              {/*</FormItem>*/}
              {/*<FormItem*/}
              {/*    label={formLabel('评议情况')}*/}
              {/*>*/}
              {/*    {getFieldDecorator('situation', {*/}
              {/*        initialValue: dataInfo['situation'],*/}
              {/*        rules: [{ required: true, message: '应评议人数' }],*/}
              {/*    })(*/}
              {/*        <Select >*/}
              {/*            <Option value={1}>开始评议</Option>*/}
              {/*            <Option value={2}>结束评议</Option>*/}
              {/*        </Select>*/}
              {/*    )}*/}
              {/*</FormItem>*/}
              <FormItem
                label={formLabel('评议开始时间', tipMsg['startTime'])}
              >
                {getFieldDecorator('startTime', {
                  initialValue: dataInfo['startTime'],
                  rules: [{ required: true, message: '评议开始时间' }],
                })(
                  <Date/>,
                )}
              </FormItem>
              <FormItem
                label={formLabel('评议结束时间', tipMsg['endTime'])}
              >
                {getFieldDecorator('endTime', {
                  initialValue: dataInfo['endTime'],
                  rules: [{ required: true, message: '评议结束时间' }],
                })(
                  <Date startTime={getFieldValue('startTime')} isDefaultEnd={false}/>,
                )}
              </FormItem>
              <FormItem
                label={formLabel('地点', tipMsg['place'])}
              >
                {getFieldDecorator('place', {
                  initialValue: dataInfo['place'],
                  rules: [{ required: false, message: '地点' }],
                })(
                  <TextArea rows={2} maxLength={100}/>,
                )}
              </FormItem>
              <FormItem
                label={formLabel('主题', tipMsg['theme'])}
              >
                {getFieldDecorator('theme', {
                  initialValue: dataInfo['theme'],
                  rules: [{ required: false, message: '主题' }],
                })(
                  <TextArea rows={2} maxLength={100}/>,
                )}
              </FormItem>
              <FormItem
                label={formLabel('主要内容', tipMsg['content'])}
              >
                {getFieldDecorator('content', {
                  initialValue: dataInfo['content'],
                  rules: [{ required: false, message: '主要内容' }],
                })(
                  <TextArea rows={3} maxLength={300}/>,
                )}
              </FormItem>
            </Form>
          </Modal>
        }

      </React.Fragment>
    );
  }
}

export default Form.create<any>({})(index);
