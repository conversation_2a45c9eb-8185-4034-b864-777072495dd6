/**
 * 模块名
 */
import React from 'react';
import { TreeSelect } from 'antd';
import { connect } from 'dva';
import styles from './index.less';
import { OrderedListOutlined, DownOutlined } from '@ant-design/icons';
import { TableModal } from '../DictMoalTableTreeSelect';
import { getAllArea } from '@/services';

const TreeNode = TreeSelect.TreeNode;
interface pType {
  searchKey?: Array<string>;
  areaCode?: string;
  dispatch?: any;
  onChange?: Function;
  commonDict?: object;
  dropdownStyle?: object;
  placeholder?: string;
  initValue?: string;
  notIncludeGZ?: false;
  disabled?: boolean;
  allowClear?: boolean;
  showMoreBtn?: boolean; // 是否显示更多按钮
  // backType?:'object', // 该属性删除
}
//
//@ts-ignore
@connect(({ commonDict }) => ({ commonDict }))
export default class DictArea extends React.Component<pType, any> {
  static defaultProps = {
    searchKey: ['name'],
    areaCode: '-1',
    commonDict: { area_tree: [] },
    showMoreBtn: true, // 默认显示更多按钮
  };

  private instanceId: string;

  constructor(props) {
    super(props);
    this.state = {
      isInit: false, //是否初始化 默认值
      actionKey: [],
      hasScrollbar: false, // 是否有滚动条
      isAtBottom: false, // 是否滚动到底部
    };
    // 生成唯一实例ID
    this.instanceId = `dict-area-${Math.random().toString(36).slice(2, 10)}`;
  }
  componentDidMount(): void {
    const { areaCode } = this.props;
    this.props.dispatch({
      type: 'commonDict/getArea',
      payload: {
        parent: areaCode,
      },
    });
  }
  onChange = (value, selectedOptions) => {
    // const {backType}=this.props;
    //selectedOptions 直接下拉选返回的是字符串数组，弹窗选择返回的是对象数组
    this.setState({
      isInit: true,
      value,
    });
    let _name = '';
    if(typeof selectedOptions[0] === 'string'){
      _name = selectedOptions[0];
    } else {
      _name = selectedOptions[0].props.children
    }
    const { onChange } = this.props;
    let obj = { id: value, key: value, name: _name };
    // if(backType==='object'){
    //   onChange && onChange(obj);
    // }else{
    onChange && onChange(value, obj);
    // }
  };
  loadData = async (node) => {
    const { dataRef = {} } = node.props;
    let parent = dataRef['key'] || node['key'];
    let flag = dataRef?.children && dataRef?.children?.length > 0;
    if (flag) {
      return new Promise<void>((resolve) => {
        return resolve();
      });
    }
    return await this.props.dispatch({
      type: 'commonDict/getChildArea',
      payload: {
        parent: parent,
      },
    });
  };
  getIsLeaf = (item) => {
    return item.leaf || item.is_leaf;
  };
  loadAllData = async (node) => {
    let parent = node?.key;
    // 增加模态框加载异步树的情况
    if (node?.isGetAllChildren == 1 || this.getIsLeaf(node)) {
      return new Promise<void>((resolve) => {
        return resolve();
      });
    }
    return await this.props.dispatch({
      type: 'commonDict/getAllChildArea',
      payload: {
        parent: parent,
        keyWord: node?.keyWord,
      },
    });
  };
  ohterSearchFunc = async (p: any) => {
    const res = await getAllArea({
      parent: p?.parent,
      keyWord: p?.keyWord,
    });
    if (res.code == 0) {
      return res.data.map((it) => {
        return {
          ...it,
          topCode: it.key.substring(0, 2),
        };
      });
    }
    return [];
  };
  renderTreeNodes = (data) => {
    return data.map((item: any) => {
      //是否启用文字点击展开收起节点
      let bool = false;
      //是否禁选节点
      let disabled = !item['is_leaf'];
      //是否叶子节点
      let isLeaf = !!item['is_leaf'];
      if (disabled) {
        if (!isLeaf) {
          bool = true;
        }
      }
      if (item.children) {
        return (
          <TreeNode
            title={
              <span
                style={{ width: '100%', display: 'inline-block' }}
                onClick={() => {
                  bool && this.nodeExpand(item['key'] ?? '');
                }}
              >
                {item['name']}
              </span>
            }
            key={item['key'] ?? ''}
            value={item['key'] ?? ''}
            dataRef={item}
            isLeaf={isLeaf}
            disabled={disabled}
          >
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          title={
            <span
              style={{ width: '100%', display: 'inline-block' }}
              onClick={() => {
                bool && this.nodeExpand(item['key'] ?? '');
              }}
            >
              {item['name'] ?? ''}
            </span>
          }
          key={item['key'] ?? ''}
          value={item['key'] ?? ''}
          dataRef={item}
          isLeaf={isLeaf}
          disabled={disabled}
        />
      );
    });
  };
  filterTreeNode = (val, node) => {
    const { searchKey } = this.props;
    const { dataRef } = node.props;
    let resData = [dataRef['key'].includes(val)];
    for (let obj of searchKey as Array<string>) {
      resData.push(dataRef[obj].includes(val));
    }
    return resData.includes(true);
  };
  nodeExpand = (key) => {
    const { actionKey = [] } = this.state;
    let newKeys = [...actionKey];
    if (newKeys.includes(key)) {
      newKeys = newKeys.filter((keys) => keys != key);
    } else {
      newKeys.push(key);
    }
    this.setState({
      actionKey: newKeys,
    });
  };
  onTreeExpand = (keys) => {
    this.setState({
      actionKey: keys,
    });
  };

  // 检查滚动位置
  checkScrollPosition = (scrollElement) => {
    if (scrollElement) {
      // 如果滚动位置 + 容器高度 >= 内容总高度 - 1 (允许1px误差)，则认为已滚动到底部
      const isAtBottom = scrollElement.scrollTop + scrollElement.clientHeight >= scrollElement.scrollHeight - 1;
      if (isAtBottom !== this.state.isAtBottom) {
        this.setState({ isAtBottom });
      }
    }
  };

  // 处理点击更多选项按钮
  handleMoreClick = () => {
    const dropdown = document.querySelector(`.${this.instanceId} .ant-select-tree-list-holder`);
    if (dropdown) {
      // 获取虚拟列表的滚动容器
      const listHolder = dropdown.querySelector('.ant-select-tree-list-holder') || dropdown;

      if (listHolder) {
        // 方法1: 直接使用scrollTop - 已确认有效
        const currentScroll = listHolder.scrollTop;
        listHolder.scrollTop = currentScroll + 100;

        // 触发滚动事件确保虚拟列表更新渲染
        setTimeout(() => {
          const event = new Event('scroll', { bubbles: true });
          listHolder.dispatchEvent(event);
          
          // 检查是否滚动到底部
          this.checkScrollPosition(listHolder);
        }, 50);
      }
    }
  };

  static getDerivedStateFromProps(props, state) {
    const { initValue, onChange } = props;
    const { isInit, actionKey } = state;
    //props 暂不支持 initValue
    // if(!isInit && initValue){
    //   if( initValue ){
    //     state['value']=initValue;
    //   }
    // }
    if (props.value && props.commonDict) {
      if (props.value == state.value) {
        return null;
      }
      if (!actionKey.includes(props.value) && props.commonDict.area_map) {
        let keys: any = [];
        let find = props.commonDict.area_map.get(props.value);
        //设置展开节点
        for (let i = 0; i < 2; i++) {
          keys.push(`${props.value}`.substring(0, i * 2 + 2));
        }
        if (props.value && props.value.length > 2 && !find) {
          keys.forEach((key, index) => {
            //本系统籍贯只有三级节点 请求只需发送最多两次
            if (!actionKey.includes(key)) {
              props
                .dispatch({
                  type: 'commonDict/getChildArea',
                  payload: {
                    parent: key,
                  },
                })
                .then((res) => {
                  //最后一次请求 判断值是否存在 不存在回调修改表单值
                  if (index == 1) {
                    const { area_map } = res;
                    if (!area_map.get(props.value)) {
                      onChange && onChange(undefined, {});
                    }
                  }
                });
            }
          });
        }
        return { actionKey: keys, value: props.value };
      }
    }
    return null;
  }
  render() {
    const { dropdownStyle, commonDict, notIncludeGZ, disabled, allowClear = false, showMoreBtn } = this.props;
    const { value, actionKey, hasScrollbar } = this.state;
    //@ts-ignore
    let { area_tree = [], area_map } = commonDict;
    // console.log(area_tree.length,area_tree,'lengegegegege')
    if (area_tree.length == 0) {
      return <TreeSelect placeholder={'请选择'} style={{ width: '100%' }} />;
    }

    if (notIncludeGZ) {
      area_tree = area_tree.filter((it) => it.key != '52');
    }

    if (!area_map.get(value)) {
      return (
        <div className={styles.box}>
          <div style={{ width: `calc(100% - 30px)` }}>
            <TreeSelect
              showSearch
              treeLine
              disabled={disabled}
              treeExpandedKeys={actionKey}
              loadData={this.loadData}
              onChange={this.onChange}
              onTreeExpand={this.onTreeExpand}
              filterTreeNode={this.filterTreeNode}
              placeholder={'请选择'}
              style={{ width: '100%' }}
              dropdownClassName={`dict-area-dropdown ${this.instanceId}`}
              dropdownStyle={dropdownStyle ? dropdownStyle : { maxHeight: 400, overflow: 'auto' }}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  setTimeout(() => {
                    const dropdown = document.querySelector(`.${this.instanceId} .ant-select-tree-list-holder`);
                    if (dropdown) {
                      const hasScrollbar = dropdown.scrollHeight > dropdown.clientHeight;
                      if (this.state.hasScrollbar !== hasScrollbar) {
                        this.setState({ hasScrollbar });
                      }
                      
                      // 添加滚动事件监听器
                      dropdown.addEventListener('scroll', () => this.checkScrollPosition(dropdown));
                    }
                  }, 100);
                }
              }}
              dropdownRender={(menu) => (
                <div>
                  {menu}
                  {showMoreBtn && hasScrollbar ? (
                    <div className="more-options-button" onClick={this.handleMoreClick}>
                      {this.state.isAtBottom ? (
                        '已到底部'
                      ) : (
                        <React.Fragment>
                          更多选项
                          <DownOutlined className="arrow-icon" />
                        </React.Fragment>
                      )}
                    </div>
                  ) : (
                    <React.Fragment />
                  )}
                </div>
              )}
            >
              {area_tree.length > 0 && this.renderTreeNodes(area_tree)}
            </TreeSelect>
          </div>
          {!disabled && (
            <OrderedListOutlined
              className={styles.icon}
              onClick={() => {
                this.setState(
                  {
                    showModals: true,
                  },
                  () => {
                    setTimeout(() => {
                      this?.['AreaTableModalRef']?.open?.();
                    }, 500);
                  },
                );
              }}
            />
          )}
          {this.state.showModals && (
            <TableModal
              {...this.props}
              title={'地区选择器'}
              ref={(e) => (this['AreaTableModalRef'] = e)}
              treeDatas={area_tree}
              rowSelectionType={'radio'}
              selectedValue={value}
              loadTree={this.loadAllData}
              parentDisable={true}
              ohterSearchFunc={this.ohterSearchFunc}
              onOK={(selectedRowKeys, selectedItems) => {
                let item = selectedItems?.[0] || {};
                this.onChange(item.key, [item.name]);
              }}
              close={() => {
                this.setState({
                  showModals: false,
                });
              }}
            ></TableModal>
          )}
        </div>
      );
    }
    return (
      <div className={styles.box}>
        <div style={{ width: `calc(100% - 30px)` }}>
          <TreeSelect
            showSearch
            treeLine
            allowClear={allowClear}
            treeExpandedKeys={actionKey}
            disabled={disabled}
            loadData={this.loadData}
            onChange={this.onChange}
            onTreeExpand={this.onTreeExpand}
            filterTreeNode={this.filterTreeNode}
            placeholder={'请选择'}
            style={{ width: '100%' }}
            dropdownClassName={`dict-area-dropdown ${this.instanceId}`}
            dropdownStyle={dropdownStyle ? dropdownStyle : { maxHeight: 400, overflow: 'auto' }}
            value={value}
            onDropdownVisibleChange={(open) => {
              if (open) {
                setTimeout(() => {
                  const dropdown = document.querySelector(`.${this.instanceId} .ant-select-tree-list-holder`);
                  if (dropdown) {
                    const hasScrollbar = dropdown.scrollHeight > dropdown.clientHeight;
                    if (this.state.hasScrollbar !== hasScrollbar) {
                      this.setState({ hasScrollbar });
                    }
                    
                    // 添加滚动事件监听器
                    dropdown.addEventListener('scroll', () => this.checkScrollPosition(dropdown));
                  }
                }, 100);
              }
            }}
            dropdownRender={(menu) => (
              <div>
                {menu}
                {showMoreBtn && hasScrollbar ? (
                  <div className="more-options-button" onClick={this.handleMoreClick}>
                    {this.state.isAtBottom ? (
                      '已到底部'
                    ) : (
                      <React.Fragment>
                        更多选项
                        <DownOutlined className="arrow-icon" />
                      </React.Fragment>
                    )}
                  </div>
                ) : (
                  <React.Fragment />
                )}
              </div>
            )}
          >
            {area_tree.length > 0 && this.renderTreeNodes(area_tree)}
          </TreeSelect>
        </div>
        {!disabled && (
          <OrderedListOutlined
            className={styles.icon}
            onClick={() => {
              this.setState(
                {
                  showModals: true,
                },
                () => {
                  setTimeout(() => {
                    this?.['AreaTableModalRef']?.open?.();
                  }, 500);
                },
              );
            }}
          />
        )}
        {this.state.showModals && (
          <TableModal
            {...this.props}
            title={'地区选择器'}
            ref={(e) => (this['AreaTableModalRef'] = e)}
            treeDatas={area_tree}
            rowSelectionType={'radio'}
            selectedValue={value}
            loadTree={this.loadAllData}
            parentDisable={true}
            ohterSearchFunc={this.ohterSearchFunc}
            onOK={(selectedRowKeys, selectedItems) => {
              let item = selectedItems?.[0] || {};
              this.onChange(item.key, [item.name]);
            }}
            close={() => {
              this.setState({
                showModals: false,
              });
            }}
          ></TableModal>
        )}
      </div>
    );
  }
}
