import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import style from './index.less';
import NowOrg from '@/components/NowOrg';
import { Button, Form, Alert, Modal, Space, Upload } from 'antd';
import ListTable from '@/components/ListTable';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { getSession } from '@/utils/session';
import SpinProgress from '@/components/SpinProgress';
import Tip from '@/components/Tip';

const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 6 },
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
    },
};

const index: any = (props, ref) => {
    const {
        tableAction,
        FormComp,
        tableColumns,
        otherBtnsFunc,
        getSearchCallBack,
        progressCallback,
        otherRenderTableColumnsFunc,
        renderTableNumber,
        isDefaultForm = true,
        rowKey = 'id'
    } = props;

    useImperativeHandle(ref, () => ({
        showModal: () => {
            open();
        }
    }));
    const org: any = getSession('org') || {};
    const [form] = Form.useForm();
    const { pathname, query: urlQuery = {} } = window['g_history']?.location || {};
    const { login: { listTree = [] } = {} } = props;
    const [loading, setLoading] = useState(false);
    const [list, setList] = useState([]);
    const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 20, total: 0, current: 1 });
    const [keyword, setKeyword] = useState({});
    const [modalVisible, setModalVisible] = useState(false);
    const [fileList, setFileList] = useState([])
    const restForm = () => {
        form.resetFields();
        setList([]);
        setPagination({ pageNum: 1, pageSize: 20, total: 0, current: 1 })
    };
    const open = () => {

    }
    const cancel = () => {

    }
    const hadndleFinish = async (e) => {
        console.log('🚀 ~ e:', e);
    };
    const fileChange = ({ fileList, file, event }: any, item: any) => {

        if (file.status === 'done') {
            const { response: { code = 500, message = '' } = {} } = file || {};
            if (code !== 0) {
                Tip.error('操作提示', message);
                fileList.pop();
            } else {
                Tip.success('操作提示', '上传成功');
            }
        } else if (file.status === 'error') {
            Tip.error('操作提示', '上传失败');
        }
        setFileList(fileList)
    }
    useEffect(() => {

    }, [_get(listTree, '[0][0].code', ''), urlQuery.box]);


    return (
        <div className={style.up}>
            <div className={style.filelist}>
                <div>
                    { fileList.map((item,index)=>{
                        return (
                            <img src={item}/>
                        )
                    }) }
                </div>
                <div>
                    <Upload
                        style={{ marginBottom: 10 }}
                        {...props}
                        onChange={fileChange}
                        fileList={fileList}
                        showUploadList={false}>
                        <Button>上传</Button>
                    </Upload>
                </div>
            </div>
            <div className={style.action}>

            </div>
        </div>
    );
};
// @ts-ignore
export default React.forwardRef(index);
