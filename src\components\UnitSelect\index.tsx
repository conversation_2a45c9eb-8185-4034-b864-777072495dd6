/**
 * 单位选择器
 */
import React from 'react';
import {Input, Modal} from 'antd';
import OrgTree from '../OrgTree';
import List from './list';
import OtherList from './listVillageCommunity';
import {connect} from "dva";
import styles from './index.less';
import {root,rootParent} from 'src/common/config.js';
import {getSession} from "@/utils/session";
import { isEmpty } from '@/utils/method';
import _isEmpty from 'lodash/isEmpty';
const Search = Input.Search;
// @ts-ignore
@connect(({common})=>({common}))
export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      visible:false,
      value:undefined,
      org: props?.org || getSession('org'), // 获取当前操作党组织建立的单位，而不是建在左侧机构树上选择的党组织的单位
      data:[],
    }
  }
  show=()=>{
    const {org}=this.props;
    if(org){ // org 传递必须要orgCode 和 code, 这个两个参数少了一个都不行
      this.loadData([org['orgCode'] || org['code']])
    }
    this.setState({
      visible:true,
    })
  };
  handleOk=()=>{
    const {onChange}=this.props;
    const {data}=this.state;
    let obj=data[0] || {};
    onChange && onChange(data);
    this.setState({
      value:obj['name']
    });
    this.handleCancel();
  };
  handleCancel=()=>{
    this.setState({
      visible:false,
    })
  };
  loadData=(val)=>{
    this.props.dispatch({
      type:'common/getTree',
      payload:{
        data:{
          orgCodeList:val,
          excludeOrgCodeList:[]
        }
      }
    });
  };
  treeSearch=(val)=>{
    this.props.dispatch({
      type:'common/queryTree',
      payload:{
        name:val
      }
    });
  };
  onChange=(data=[])=>{
    this.setState({
      data,
    })
  };
  treeChange=(selectedKeys,e)=>{
    const {dataRef}=e.node;
    this.setState({
      org:dataRef
    },()=>{
      let comp = _isEmpty(this.props.unitType) ? List : OtherList;
      comp['WrappedComponent'].action({orgCode:dataRef['orgCode'],pageNum:1});
    });
  };
  static getDerivedStateFromProps(props,state){
    const {initValue,common}=props;
    const {mapTreeCode}=common;
    let {value}=state;
    if(initValue && !value){
      let obj=mapTreeCode.get(initValue);
      if(obj){
        return {value:obj['shortName']}
      }else if(root['parentCode']===initValue){
        return {value:rootParent['shortName']}
      }else{
        return {value:initValue}
      }
    }
    return null;
  };
  render(){
    const {visible,data,value,org}=this.state;
    const {common,children,placeholder,disabled=false,isCreateOrg,orgCode, unitType=[], disabledColFunc}=this.props;
    return(
      <React.Fragment>
        {
          children ? React.cloneElement(children as any,{
            onClick:this.show,
          }) :
            <Search
              value={value}
              disabled={disabled}
              onClick={this.show}
              onSearch={this.show}
              placeholder={placeholder || '请点击选择'}
              enterButton
            />
        }
        <Modal
          title="单位选择器"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1200}
          bodyStyle={{padding:0}}
        >
          <div className={styles.content}>
            <div className={styles.tree} style={{pointerEvents: this.props.org ? 'none' : 'auto' }}>
              <OrgTree
                listData={common['listTree']}
                mapData={common['mapTree']}
                filterData={common['filterData']}
                loadData={this.loadData}
                onSearch={this.treeSearch}
                onChange={this.treeChange}
                showSearch={false}
                rootCode={this.props.org ? this.props.org['code'] : undefined}
                type={'selector'}
                disabled={this.props.org ? true : false}
              />
            </div>
            <div className={styles.list}>
              {
                !_isEmpty(unitType) ?
                  <OtherList subordinate={this.props.org ? 0 : undefined} orgCode={org['orgCode'] || org['managerOrgCode']} isCreateOrg={isCreateOrg} onChange={this.onChange} {...{unitType}} disabledColFunc={disabledColFunc}/>:
                  <List orgCode={org['orgCode'] || org['managerOrgCode']} subordinate={this.props.org ? 0 : undefined} isCreateOrg={isCreateOrg} onChange={this.onChange} disabledColFunc={disabledColFunc}/>
              }
            </div>
          </div>
        </Modal>
      </React.Fragment>
    )
  }
}
