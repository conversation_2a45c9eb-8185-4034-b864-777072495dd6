/**
 * 模块名
 */

import React from 'react'
import { BookOutlined, CloseOutlined, CodeOutlined, CopyOutlined, SolutionOutlined, StarOutlined } from '@ant-design/icons';
import { Icon as LegacyIcon } from '@ant-design/compatible';
import { Avatar, Col, Menu, Modal, Row } from 'antd';
import Basic from './basic';
import WhiteSpace from '@/components/WhiteSpace';
import {connect} from "dva";
import { _history as router, changeMsgTip } from '@/utils/method';
import { tableColConfig } from '@/services';
import Income from './income';
import _isEmpty from 'lodash/isEmpty';


const menuData=[
  {
    code:'1',
    name:'基本信息',
      icon:<StarOutlined />,
  },
  {
    code:'2',
    name:'收入支出情况',
    icon:<CodeOutlined />,
  },
];
// @ts-ignore
@connect(({unitIn,commonDict})=>({unitIn,commonDict}))
export default class index extends React.Component<any,any>{
  static show(){};
  static close(){};
  static clear(){};
  constructor(props){
    super(props);
    let obj=menuData[0];
    this.state={
      visible:false,
      key:obj['code'],
      keyPath:[obj['code']],
      selected:obj,
    };
    index.show=this.open.bind(this);
    index.close=this.handleCancel.bind(this);
    index.clear=this.destroy.bind(this);
  }
  componentDidMount(): void {
    tableColConfig({id:'ccp_unit'}).then(res=>{
      if(res['code']=='0'){
        let msg = changeMsgTip(res['data']);
        this.setState({
          tipMsg:msg,
        });
      }
    });

  }
  handleOk=()=>{
    this.handleCancel();
  };
  handleCancel=(e:any = {})=>{
    const {colseType =''} = e;
    // const {location:{search = undefined} = {}} = router;
    // router.push(_isEmpty(search) || colseType === 'add' ? '?' :search)
    this.destroy();
  };
  open=()=>{
    this.setState({
      visible:true,
    })
  };
  destroy=()=>{
    const { onOk } = this.props;
    let obj=menuData[0];
    this.setState({
      visible:false,
      key:obj['code'],
      keyPath:[obj['code']],
      selected:obj,
    });
    this.props.dispatch({//重置model
      type:'unitIn/updateState',
      payload:{
        basicInfo:{},
      }
    });
    onOk && onOk();
  };
  onSelect=(item)=>{
    const {key,keyPath}=item;
    const selected=menuData.find(obj=>obj['code']===key);
    this.setState({
      key,
      keyPath,
      selected,
    });
  };
  render() {
    const {visible,selected,keyPath,key,tipMsg={}}=this.state;
    // const {basicInfo={}}=this.props.unitIn;
    const {dataInfo={}}=this.props;
    const {d04Code} = dataInfo || {};
    let bool=true;//菜单禁用
    if(dataInfo['code']){
      bool=false
    }
    return (
      <Modal
        title=""
        wrapClassName='editModal'
        destroyOnClose
        closable={false}
        visible={visible}
        onOk={this.handleOk}
        onCancel={this.handleCancel}
        width={'calc(90vw)'}
        footer={false}
      >
        <div className='container'>
          <div className='header'>
            <Row>
              <Col span={4} style={{textAlign:'center'}}>
                <Avatar style={{ backgroundColor: '#7265e6',verticalAlign: 'middle' }} size="large">
                  admin
                </Avatar>
              </Col>
              <Col span={15}><h2>{dataInfo['code'] ? '编辑集体经济情况' : '新增集体经济情况'}</h2></Col>
              <Col span={5} className={'close'}><CloseOutlined onClick={this.destroy} /></Col>
            </Row>
          </div>
          <div>
            <Row>
              <Col span={3} style={{borderRight:'1px solid rgb(233, 233, 233)'}}>
                <div className='slider'>
                  <LegacyIcon type={selected['icon'] || undefined} style={{marginRight:8}}/>{selected['name']}
                </div>
                <Menu mode="inline" selectedKeys={keyPath} onSelect={this.onSelect}>
                  {
                    menuData && menuData.map((obj,index)=>{
                      return (
                        <Menu.Item key={obj['code']} disabled={index>0 ? bool : false} icon={obj['icon']}>
                          {obj['name']}
                        </Menu.Item>
                      );
                    })
                  }
                </Menu>
              </Col>
              <Col span={21} style={{padding:16}} className='content'>
                {
                  key==='1' && <Basic tipMsg={tipMsg} {...this.props} close={this.handleCancel}/>
                }
                {
                  key==='2' && <Income tipMsg={tipMsg} {...this.props}/>
                }
                <WhiteSpace/>
                <WhiteSpace/>
              </Col>
            </Row>
          </div>
        </div>
      </Modal>
    );
  }
}
