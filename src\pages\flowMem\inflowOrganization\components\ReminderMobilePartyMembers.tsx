// 流动党员-流入管理-未纳入流入地管理
import React, { Fragment } from 'react';
import { connect } from 'dva';
import { Button, Divider, Input, Modal, Select, Radio, message, Row } from 'antd';
import { WarningTwoTone } from '@ant-design/icons';
import moment from 'moment';
import ListTable from 'src/components/ListTable';
import RuiFilter from 'src/components/RuiFilter';
import { isEmpty, setListHeight, changeMsgTip } from '@/utils/method';
import NowOrg from 'src/components/NowOrg';
import ExportInfo from '@/components/Export';
import Tip from '@/components/Tip';
import { getSession } from '@/utils/session';
import FlowAddOrExamine from './flowAddORexamine';
import ExamineFlowAddOrEdit from './ExamineFlowAddOrEdit';
import FlowAddOrEdit from '@/pages/org/list/subpage/flowaddoredit';
import { Form } from '@ant-design/compatible';
import _isEmpty from 'lodash/isEmpty';
import { flowmemapprove, findMemByIdCardAndName, approve1, outManageFind, findByCode, findOrg } from '../../service/index';
import InfoRegistration from '../../inflowManage/components/infoRegistration';
import { inflowOrganizationDInfo, auditfind, outManageCheck } from '@/pages/flowMem/service'

const Search = Input.Search;
const Option = Select.Option;
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
//@ts-ignore
@connect(({ unit, commonDict, loading, flowMem, org }) => ({
  flowMem,
  unit,
  commonDict,
  org,
  loading: loading.effects['unit/getList'],
}))
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      dataInfo: undefined,
      filter: {}, //筛选器
      memName: undefined, //搜索框
      view: false,
      subordinate: getSession('subordinate'),
      exportLoading: false, //导出loading
      searchLoading: false, //查询loading
      status: undefined, //审核状态
      refuse: undefined, //审核状态
      buttonText: '下一步',
      mstatus: undefined
    };
    this['queryDetailsRef'] = React.createRef();
  }

  exportInfo = async () => {
    this.setState({
      exportLoading: true,
    });
    await this['exportRef'].submitNoModal();
    this.setState({
      exportLoading: false,
    });
  };
  filterChange = (val) => {
    console.log('🚀 ~ index ~ val:', val);
    this.setState(
      {
        filter: val,
      },
      () => this.getList(val),
    );
  };
  handleSearch = (e) => {
    this.setState(
      {
        memName: e,
        searchLoading: true,
      },
      () => {
        this.getList({ memName: e });
      },
    );
  };
  searchChange(e) {
    this.setState({
      memName: e.currentTarget.value || undefined,
    });
  }
  selectChange = (type, e) => {
    const data = {};
    if (type == '0') {
      data['status'] = e;
    } else if (type == '1') {
      data['refuse'] = e;
    } else {
    }
    this.setState(
      {
        ...data,
      },
      () => this.getList(),
    );
  };
  getList = async (params?: object) => {
    const { filter, memName, status, refuse } = this.state;
    const resData = {};
    if (status) {
      resData['status'] = status;
    }
    if (refuse) {
      resData['refuse'] = refuse;
    }
    const org = getSession('org') || {};
    await this.props.dispatch({
      type: 'flowMem/ReminderMobilePartyMembersList',
      payload: {
        data: {
          pageNum: 1,
          pageSize: 10,
          orgCode: org['orgCode'],
          name: memName || '',
          ...resData,
          ...params,
        },
      },
    });
    this.setState({
      searchLoading: false,
    });
  };
  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    const org = getSession('org') || {};
    const subordinate = getSession('subordinate') || '0';
    if ((!isEmpty(this.state['orgCode']) && this.state['orgCode'] !== org['orgCode']) || subordinate !== this.state.subordinate) {
      this.setState(
        {
          orgCode: org['orgCode'],
          subordinate,
        },
        () => {
          this.getList({ orgCode: org['orgCode'] });
        },
      );
    }
  }
  componentDidMount() {
    const org = getSession('org') || {};
    setListHeight(this);
    this.setState({ orgCode: org['orgCode'] });
    this.getList();
  }
  flowAddOrEdit = (record?: object) => {
    FlowAddOrEdit['WrappedComponent'].clear();
    FlowAddOrEdit['WrappedComponent'].show({ ...record, isEdit: true, isapprove: true });
  };
  handleOk = async () => {
    const { buttonText, row, basicInfo = {} } = this.state
    if (buttonText == "下一步") {
      this.props.form.validateFieldsAndScroll(async (error, values) => {
        if (error) {
          return;
        }
        const { code = 500, data = undefined } = await outManageCheck({
          data: {
            codeList: [basicInfo?.code],
          },
        })
        if (!data) {
          this.setState({ visible: false });
          this['examineFlowAddOrEditRef'].open('edit-outTab3', { ...basicInfo, signAuditId: row?.signAuditId }, 'outFlow');
        } else {
          Tip.error('操作提示', data || '系统错误');
        }
      })
    } else {
      this.setState({ confirmLoading: true })
      this.props.form.validateFieldsAndScroll(async (error, values) => {
        if (error) {
          return;
        }
        const res = await approve1({ data: { memFlowSignAuditDto: { ...values, signAuditId: row?.signAuditId } } });
        if (res?.code == 0) {

          message.success('操作成功');

          this.handleCancel();
          this.getList();
        }
      });
    }
  };
  getBasicInfo = async () => {
    const { urlType, row } = this.state;
    const { getFieldDecorator, setFieldsValue, getFieldValue } = this.props.form;
    const { code: rescode = 500, data = {} } = await findMemByIdCardAndName({
      data: {
        idCard: row.idcard,
        name: row.name
      }

    });

    if (!data?.code) {
      setFieldsValue({ status: '2' })
      this.setState({
        isdisabled: true,
        mstatus: 2,
        buttonText: '保存'
      })
      return
    }
    const { code: flowcode = 500, data: flowdata = {} } = await auditfind({
      data: { id: row.id }
    })
    const { code: orgcode = 500, data: orgdata = {} } = await findOrg({ code: data?.orgCode })
    console.log(orgdata, 'orgdataorgdataorgdataorgdata')
    const { code = 500, data: memdata = {} } = await findByCode({ code: data?.code })
    this.setState({
      basicInfo: { memOrgCode: orgdata?.code, memOrgName: orgdata?.name, memOrgPhone: orgdata?.contactPhone, ...flowdata, ...memdata },
    });
  };
  upList = () => {
    this.handleCancel();
    this.getList();
  }
  handleCancel = () => {
    this.props.form.resetFields();
    this.setState({ visible: false, mstatus: undefined, isdisabled: false, confirmLoading: false, buttonText: '下一步' });
  }
  render() {
    const {
      filterHeight,
      filter,
      memName,
      subordinate,
      searchLoading,
      confirmLoading = false,
      visible = false,
      basicInfo,
      status = '',
      refuse = '',
      buttonText,
      row,
      isdisabled = false,
      mstatus,
      modalloading = false
    } = this.state;
    const org = getSession('org') || {};
    const {
      loading,
      commonDict,
      flowMem: { list = [], pagination = { pageNum: 1, pageSize: 10 } },
    } = this.props;
    console.log("🚀 ~ index ~ render ~ org1111111111111:", org)
    const { getFieldDecorator, setFieldsValue, getFieldValue } = this.props.form;
    const filterData = [
      {
        key: 'statusList',
        name: '审批状态',
        value: commonDict[`dict_d202`],
      },
      {
        key: 'refuseList',
        name: '拒绝原因',
        value: commonDict[`dict_d203`],
      },
    ];
    console.log('commonDict[`dict_d34`]', commonDict[`dict_d34`]);
    const columns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 60,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'name',
        align: 'center',
        width: 80,
        render: (text, record, index) => {
          return (
            <Fragment>
              {record['flowMessage'] ? <WarningTwoTone style={{ fontSize: '20px' }} twoToneColor={'#faad14'} title={record['flowMessage']} /> : ''}
              <a
                onClick={() => {
                  this.flowAddOrEdit({ ...record, isEdit: false, isapprove: true });
                }}
              >
                {text}
              </a>
            </Fragment>
          );
        },
      },
      {
        title: '身份证号码',
        dataIndex: 'idcard',
        align: 'center',
        width: 160,
      },
      {
        title: '党员组织关系所在党组织',
        dataIndex: 'orgFlowName',
        width: 300,
        render: (text, record, index) => {
          if(record?.dataType!='2') {
            return <span >{text}</span>
          } else {
            return <span>{record?.applyOrgFlowName}</span>
          }
        }
      },
      {
        title: '流入地党支部',
        dataIndex: 'applyOrgFlowName',
        width: 260,
        render: (text, record, index) => {
          if(record?.dataType!='2') {
            return <span >{text}</span>
          } else {
            return <span>{record?.orgFlowName}</span>
          }
        }
      },
      {
        title: '流入地联系人',
        dataIndex: 'flowConnectionName',
        align: 'center',
        width: 120,
      },
      {
        title: '流入地联系方式',
        dataIndex: 'flowConnection',
        width: 120,
      },
      {
        title: '流动原因',
        dataIndex: 'd146Name',
        align: 'center',
        width: 120,
      },
      {
        title: '外出日期',
        dataIndex: 'outDate',
        align: 'center',
        width: 100,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '审批时间',
        dataIndex: 'auditTime',
        align: 'center',
        width: 80,
        render: (text, record, index) => {
          if (text) {
            return moment(text).format('YYYY-MM-DD');
          }
        },
      },
      {
        title: '审批人',
        dataIndex: 'auditUserName',
        align: 'center',
        width: 80,
      },
      {
        title: '审批组织',
        dataIndex: 'auditOrgName',
        align: 'center',
        width: 80,
      },
      {
        title: '审批状态',
        dataIndex: 'statusName',
        align: 'center',
        width: 80,
      },
      {
        title: '拒绝原因',
        dataIndex: 'refuseName',
        align: 'center',
        width: 80,
      },
      {
        title: '操作',
        dataIndex: 'action',
        align: 'center',
        width: 150,
        fixed: 'right',
        render: (text, record) => {
          return (
            <Fragment>
              {/* dataType 1=本节点创建  2-中组部录取的数据  status  0-待审核 1-审核通过 2-审核未通过*/}
              {record?.dataType == '2' && record?.status == '0' && (
                <a
                  onClick={() => {
                    this.setState({
                      visible: true,
                      row: record,
                    }, () => {
                      this.getBasicInfo();
                    });
                  }}
                >
                  审核
                </a>
              )}
              <Divider type="vertical" />
              <a
                onClick={() => {
                  this.flowAddOrEdit({ ...record, isEdit: false, isapprove: true });
                }}
              >
                查看
              </a>
              {/* <Divider type="vertical" /> */}
              {/* <a
                onClick={() => {
                  this['flowAddOrExamineRef'].open(record, "cancel", 0);
                }}
              >
                撤销
              </a> */}
            </Fragment>
          );
        },
      },
    ];
    const noOperationColumns = [
      {
        title: '序号',
        dataIndex: 'id',
        align: 'center',
        width: 50,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: '姓名',
        dataIndex: 'name',
        align: 'center',
        width: 60,
      },
      {
        title: '性别',
        dataIndex: 'sexName',
        align: 'center',
        width: 40,
      },
      {
        title: '身份证号码',
        dataIndex: 'idcard',
        align: 'center',
        width: 100,
      },
      {
        title: '联系电话',
        dataIndex: 'phone',
        align: 'center',
        width: 100,
      },
      {
        title: '所在组织',
        dataIndex: 'orgName',
        width: 160,
      },
      {
        title: '工作岗位',
        dataIndex: 'd09Name',
        width: 80,
      },
    ];
    return (
      <Fragment>
        <NowOrg
          extra={
            <Fragment>
              {/* <span style={{ marginRight: 2 }}>审批状态：</span> 

              <Select style={{ width: '120px' }} value={status} placeholder="请选择" onSelect={(e) => this.selectChange(0, e)} allowClear onClear={() => this.selectChange(0, undefined)} maxTagTextLength={999}>
                <Option value="0">待审批</Option>
                <Option value="1">审批通过</Option>
                <Option value="2">审批未通过</Option>
              </Select>
              <span style={{ marginRight: 2, marginLeft: 8 }}>拒绝原因：</span> 

              <Select style={{ width: '200px' }} value={refuse} placeholder="请选择" onSelect={(e) => this.selectChange(1, e)} allowClear onClear={() => this.selectChange(1, undefined)} maxTagTextLength={999}>
                <Option value="1">党员不存在</Option>
                <Option value="2">党员组织关系转接中</Option>
                <Option value="3">党员信息流转中</Option>
              </Select> */}
              {/* <Button
                style={{ marginLeft: 16 }}
                onClick={this.exportInfo}
                loading={this.state.exportLoading}
              >
                导出
              </Button> */}
              {
                //流入登记只有流动党支部才有(新增 631 632 634 813 931 932)
                ['631', '632', '634', '931', '932'].includes(org['d01Code']) ? (
                  <Button
                    type="primary"
                    style={{ marginLeft: 16 }}
                    onClick={() => {
                      this['infoRegistration'].open();
                    }}
                  >
                    流入登记
                  </Button>
                ) : (
                  <React.Fragment />
                )
              }
              <Search
                loading={searchLoading}
                allowClear
                placeholder="请输入姓名"
                enterButton={'查询'}
                style={{ width: 200, marginLeft: 16 }}
                onSearch={(e) => {
                  this.handleSearch(e);
                }}
                onChange={(e) => {
                  this.searchChange(e);
                }}
              />
            </Fragment>
          }
        />
        <RuiFilter data={filterData} openCloseChange={() => setListHeight(this, 20)} onChange={this.filterChange} />
        <ListTable
          rowKey={'id'}
          scroll={{ y: filterHeight, x: 100 }}
          
          columns={columns}
          data={list}
          pagination={pagination}
          onPageChange={(page, pageSize) => {
            this.getList({ pageNum: page, pageSize });
          }}
        />
        <ExportInfo
          wrappedComponentRef={(e) => (this['exportRef'] = e)}
          tableName={''}
          noModal={true}
          tableListQuery={{
            ...filter,
            pageNum: pagination.pageNumber || 1,
            pageSize: pagination.pageSize || 10,
            subordinate,
            memName,
            orgCode: org['orgCode'],
            type: '1',
          }}
          action={'/api/mem/flow/exportXsl'}
        />
        <ExamineFlowAddOrEdit
          wrappedComponentRef={(e) => (this['examineFlowAddOrEditRef'] = e)}
          onOk={this.upList}
        />
        <FlowAddOrEdit wrappedComponentRef={(e) => (this['flowAddOrEditRef'] = e)} onOk={this.getList} />
        <FlowAddOrExamine wrappedComponentRef={(e) => (this['flowAddOrExamineRef'] = e)} onOk={this.getList} />
        {/* <FlowAddOrEdit dataInfo={{}} /> */}
        <InfoRegistration ref={(e) => (this['infoRegistration'] = e)} uplist={this.getList} org={org} />
        <Modal
          footer={
            <Fragment>
              <Button
                onClick={() => {
                  this.handleCancel();
                }}
              >
                取消
              </Button>
              <Button
                loading={confirmLoading}
                onClick={() => {
                  this.handleOk();
                }}
                type="primary"
              >
                {buttonText}
              </Button>
            </Fragment>
          }
          onCancel={() => {
            this.props.form.resetFields();
            this.setState({ visible: false, isdisabled: false, mstatus: undefined });
          }}
          bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
          destroyOnClose
          // maskClosable={false}
          width={800}
          title={'审核'}
          visible={visible}
        >
          <Form {...formItemLayout}>
            <FormItem label={'审批意见'} {...formItemLayout}>
              {getFieldDecorator('status', {
                rules: [{ required: true, message: '请输入审批意见' }],
                initialValue: mstatus,
              })(
                <RadioGroup disabled={false} onChange={(e) => {
                  if (e.target.value == 1) {
                    this.setState({
                      buttonText: e.target.value == 1 ? '下一步' : '保存',
                      mstatus: e.target.value,
                    })
                  } else {
                    this.setState({
                      buttonText: e.target.value == 1 ? '下一步' : '保存',
                      mstatus: e.target.value,
                    })
                  }

                }}>
                  {/* <Radio value={'0'}>待审批</Radio> */}
                  <Radio value={'1'} disabled={isdisabled}>审批通过</Radio>
                  <Radio value={'2'}>审批拒绝</Radio>
                </RadioGroup>,
              )}
            </FormItem>
            {getFieldValue('status') == 2 && (
              <>
                <FormItem label={'拒绝原因'} {...formItemLayout}>
                  {getFieldDecorator('refuse', {
                    rules: [{ required: true, message: '请输入审批意见' }],
                    initialValue: _isEmpty(basicInfo) ? '1' : basicInfo['refuse'],
                  })(
                    <RadioGroup disabled={false}>
                      <Radio value={'1'}>党员不存在</Radio>
                      <Radio value={'2'}>党员组织关系转接中</Radio>
                      <Radio value={'3'}>党员信息流转中</Radio>
                    </RadioGroup>,
                  )}
                </FormItem>
                <FormItem label={'拒绝说明'}>
                  {getFieldDecorator('reason', {
                    initialValue: _isEmpty(basicInfo) ? undefined : basicInfo.reason,
                    rules: [{ required: true, message: '请填写' }],
                  })(<Input.TextArea disabled={false} placeholder="请输入" showCount maxLength={100} rows={4} />)}
                </FormItem>
              </>
            )}
          </Form>
          {
            mstatus != 2 &&
            <Row>
              <h3>党员信息</h3>
              <ListTable
                scroll={{ x: '100%' }}
                rowKey={'code'}
                columns={noOperationColumns}
                data={[basicInfo] || []}
                pagination={false}
              />
            </Row>
          }
        </Modal>
      </Fragment>
    );
  }
}
export default Form.create()(index);
