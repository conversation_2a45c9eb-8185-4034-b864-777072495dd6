import request from 'src/utils/request';
import qs from 'qs';

export function getLockFiledList(params) {
  return request(`/api/lock/getLockFiledList`, {
    method: 'POST',
    body: params,
  });
}

export function saveLockFiled(params) {
  return request(`/api/lock/saveLockFiled`,{
    method:'POST',
    body:params,
  });
}

export function lock(params) {
  return request(`/api/lock`,{
    method:'POST',
    body:params,
  });
}
export function unlock(params) {
  return request(`/api/lock/unlock`,{
    method:'POST',
    body:params,
  });
}

export function msg(params) {
  return request(`/api/lock/msg`,{
    method:'POST',
    body:params,
  });
}


