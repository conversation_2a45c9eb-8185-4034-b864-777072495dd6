import React, { Fragment, useRef, useEffect, useState } from 'react';
import { Spin, Button, Select, Form } from 'antd';
import moment from 'moment';
import ReactDataSheet from 'react-datasheet';
import { getSession } from '@/utils/session';
import Tip from '@/components/Tip';
import { Colgroup, Head as SelfHead, fakeLine } from '@/components/DynamicTableHead';
import { config, getCheckTableCols } from './config';
import {
  superviseTable,
  supportExcel,
  exportWorkSupervisionExcel,
  exportMemFlowInspectionForm,
  peggingWorkSupervision,
} from '../services';
import CheckBack from './check';
import { unixMoment } from '@/utils/method.js';
import _isEmpty from 'lodash/isEmpty';
import _forEach from 'lodash/forEach';
import Date from '@/components/Date';

const index = (props: any) => {
  const org: any = getSession('org') || {};
  const checkRef: any = useRef();
  const [form] = Form.useForm();
  const { superviseTab } = sessionStorage;

  const [time, setTime] = useState<any>(moment().valueOf());
  const [tableData, setTabData] = useState<any>([]);
  const [tableLoading, setTabLoading] = useState<any>(false);
  const [search, setSearch] = useState<any>({});
  const [selectList, setSelectList] = useState<any>();

  const [table, setTable] = useState<any>();
  const tdStyle: any = {
    height: 28,
    border: '1px solid black',
    wordBreak: 'break-all',
  };

  const getSelectList = async () => {
    const { code = 500, data = [] } = await supportExcel({});
    if (code == 0) {
      let _list = data.map((it) => it.reportCode);
      let final = config.filter((it) => _list.includes(it.reportCode));
      setSelectList(config);
    }
  };

  const getTable = async (p: any = {}) => {
    let url = table.tableAction;
    if (!url) return;

    // 当第二张表，若没传2个时间，typeStatus == 1 用于后端判断
    const { endDate, startDate, reportCode } = search;
    let typeStatus: any = undefined;
    if (!endDate && !startDate && reportCode == '2') {
      typeStatus = '1';
    }

    // const { reportCode = 'excel_2' } = p;
    setTime(moment().valueOf());
    // if (reportCode) {
    setTabLoading(true);
    const { code = 500, data = [] } = await url({
      data: { orgCode: org.orgCode, ...search, ...p, typeStatus },
    });
    setTabLoading(false);
    if (code == 0) {
      setTabData(data);
    }
  };
  // 导出
  const exportFlowExcels = async () => {
    // 当第二张表，若没传2个时间，typeStatus == 1 用于后端判断
    const { endDate, startDate, reportCode } = search;
    let typeStatus: any = undefined;
    if (!endDate && !startDate && reportCode == '2') {
      typeStatus = '1';
    }
    const { code = 500 } = await exportMemFlowInspectionForm({
      data: {
        orgCode: org.orgCode,
        ...search,
        typeStatus,
      },
    });
    if (code == 0) {
      Tip.success('操作提示', '操作成功');
    }
  };
  // 流动党员表格
  const exportExcels = async () => {
    // 当第二张表，若没传2个时间，typeStatus == 1 用于后端判断
    const { endDate, startDate, reportCode } = search;
    let typeStatus: any = undefined;
    if (!endDate && !startDate && reportCode == '2') {
      typeStatus = '1';
    }
    const { code = 500 } = await exportWorkSupervisionExcel({
      data: {
        orgCode: org.orgCode,
        ...search,
        typeStatus,
      },
    });
    if (code == 0) {
      Tip.success('操作提示', '操作成功');
    }
  };
  const onFinish = async (event) => {
    let times = ['startDate', 'endDate'];
    let val = unixMoment(times, event);
    sessionStorage.setItem('superviseTab', val.reportCode);
    let table = selectList.find((it) => it.reportCode == val.reportCode) || {};
    await setTable(table);
    await setSearch(val);
    setTabData([]);
  };
  const searchInit = () => {
    form.resetFields();
    let obj = {
      reportCode: superviseTab || selectList[0].reportCode,
    };
    form.setFieldsValue(obj);
    onFinish(obj);
  };

  const selectChange = (event) => {
    // console.log("🚀 ~ selectChange ~ event:", event)
    let obj = {
      reportCode: event,
    };
    form.resetFields();
    form.setFieldsValue(obj);
    form.submit();
  }

  useEffect(() => {
    // 当存在搜索存在初始化的时候
    if (_isEmpty(search)) {
      form.submit();
    } else {
      getTable();
    }
  }, [search]);

  useEffect(() => {
    if (!_isEmpty(selectList)) {
      searchInit();
    }
  }, [selectList]);

  useEffect(() => {
    setSelectList([]);
    getSelectList();
  }, [org.orgCode, org.subordinate]);


  if (_isEmpty(table)) {
    return <div></div>;
  }
  {/*reportCode为 'N','O','A','B','C','D','E','G','F','J','Z' 的表不展示操作项 */ }
  const showType = !(['N', 'O', 'A', 'B', 'C', 'D', 'E', 'G', 'F', 'J', 'Z'].includes(table.reportCode))

  return (
    <div>
      <div style={{ margin: '10px 0' }}>
        <Form layout={'inline'} onFinish={onFinish} form={form}>
          <Form.Item label="请选择督察表" name="reportCode">
            {/* 切换的时候就需要发送请求 */}
            {/* <Select style={{ width: 250 }} onChange={async (event: any) => { }}> */}
            <Select style={{ width: 250 }} onChange={(event) => selectChange(event)}>
              {!_isEmpty(selectList) &&
                selectList.map((it) => (
                  <Select.Option value={it.reportCode} key={it.reportCode}>
                    {it.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>
          {!(['N', 'O', 'A', 'C', 'E', 'G', 'F', 'J', 'Z'].includes(table.reportCode)) && <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.reportCode !== currentValues.reportCode
            }
          >
            {({ getFieldValue }) => {
              return true ? (
                <React.Fragment>
                  <Form.Item label={["B", "D"].includes(table.reportCode) ? '请输入登记起止时间' : "请选择起止时间"}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Form.Item name="startDate">
                        <Date></Date>
                      </Form.Item>
                      至&nbsp;&nbsp;
                      <Form.Item name="endDate">
                        <Date></Date>
                      </Form.Item>
                    </div>
                  </Form.Item>
                </React.Fragment>
              ) : null;
            }}
          </Form.Item>}

          <Form.Item>
            <Button type={'primary'} onClick={form.submit}>
              查询
            </Button>
          </Form.Item>
          {/*reportCode为 'N','O','A','B','C','D','E','G','F','J','Z' 的表不展示操作项 */}
          {<Form.Item>
            <Button type={'primary'} onClick={showType ? exportExcels : exportFlowExcels}>
              导出表格
            </Button>
          </Form.Item>}
        </Form>
      </div>
      <div style={{ width: table?.width || 'auto', overflow: 'auto' }}>
        <div>
          <h2 style={{ marginTop: '20px', width: '100%', textAlign: 'center' }}>{table?.name}</h2>
          <table style={{ width: '100%' }}>
            <SelfHead tree={table.head} nodeName={'k0505'} key={time} />
            <Colgroup tree={table.head} nodeWith={table.nodeWith} />
          </table>
          <ReactDataSheet
            data={tableData}
            valueRenderer={(cell: any) => cell['v']}
            sheetRenderer={(props: any) => {
              return (
                <table style={{ width: '100%' }}>
                  <Colgroup tree={table.head} nodeWith={table.nodeWith} />
                  <tbody>{props.children}</tbody>
                </table>
              );
            }}
            cellRenderer={(props: any) => {
              // cell  c代表列 r代表行(流动党员相关有) v代表值 t代表反查后使用那个表头
              const { cell = {} } = props || {};
              return (
                <td
                  style={{
                    ...tdStyle,
                    width: props.col === 0 ? 300 : table.nodeWith,
                    textAlign: props.col === 0 ? 'left' : 'center',
                  }}
                  onClick={() => {
                    // 2025.02.26 我只加了 if(table.callBackAction) 有反查接口才打开反查弹窗的判断
                    if (table.callBackAction) {
                      // 如果是什么率 百分比列 就不让点击
                      console.log(cell, "11111111111111111");
                      if (cell.t == "disable" || cell.v == 0) {
                        return
                      }
                      if (cell.v && props.col !== 0) {
                        // 当第二张表，若没传2个时间，typeStatus == 1 用于后端判断
                        const { endDate, startDate, reportCode } = search;
                        let typeStatus: any = undefined;
                        if (!endDate && !startDate && reportCode == '2') {
                          typeStatus = '1';
                        }

                        const data = {
                          ...cell,
                          ...search,
                          reportCode: table.reportCode,
                          tableType: table.tableType,
                          typeStatus,
                        }
                        // 只有流动党员相关督查表才有cell.r  
                        if (cell.r && cell.r > 0) {
                          // 流动党员 需要拿到每一行的组织
                          data.name = tableData[cell.r - 1][0].v
                          // console.log("2222222222222222", data);
                        }
                        checkRef.current.open(data);
                      }
                    }
                  }}
                >
                  {cell['v']}
                </td>
              );
            }}
          />
        </div>
        <CheckBack
          // showItemDtetails={['mem']}
          detailListAction={table.callBackAction}
          // detailListAction={detail}
          tableColumns={(
            query,
            props,
            { renderMemDetail, renderDevelopDetail, renderOrgDetail, renderUnitDetail },
          ) => {
            return getCheckTableCols(query?.t);
          }}
          exportActionIndex={showType ? 1 : 2}
          width={1400}
          ref={checkRef}
        />
      </div>
    </div>
  );
};

export default index;
