import modelExtend from 'dva-model-extend';
import { listPageModel } from 'src/utils/common-model';
import { getList, addMem, findMem, updateMem, getListByMemCode, partyPosition, memLockedList, findHistoryByCode, getzylist,flowcountdy,zymemInfo,zyaddMem } from '../services';
import { getSession } from '@/utils/session'; //模拟数据
import { changeListPayQuery } from '@/utils/method.js';
const memBasic = modelExtend(listPageModel, {
  namespace: 'memBasic',
  state: {
    basicInfo: {}, // 人员基本信息
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen((location) => {
        const { pathname, query } = location;
        let isLockUrl = (pathname === '/unlock/locked' || pathname === '/unlock/unlock') && query.lockObject == '1';
        if (pathname === '/mem/manage' || isLockUrl || pathname === '/mem/zy/manage') {
          const org = getSession('org') || {};
          const defaultParas = {
            pageNum: 1,
            pageSize: 10,
          };
          const dictData = ['dict_d09', 'dict_d07', 'dict_d01', 'dict_d02', 'dict_d03'];
          for (let obj of dictData) {
            dispatch({
              type: 'commonDict/getDictTree',
              payload: {
                data: {
                  dicName: obj,
                },
              },
            });
          }
          if (isLockUrl) {
            dispatch({
              type: 'getUnlockList',
              payload: {
                data: {
                  memCode: org['orgCode'],
                  ...defaultParas,
                  ...query,
                },
              },
            });
          } else {

            if (pathname == '/mem/zy/manage') {
              dispatch({
                type: 'getzylist', 
                payload: {
                  data: {
                    searchType: 1,
                    memOrgCode: org['orgCode'],
                    ...defaultParas,
                    ...query,
                   
                  },
                },
              });
              dispatch({
                type: 'flowcount',
                payload: {
                  data: {
                    memOrgCode: org['orgCode'],
                    ...query,
                    searchType: 1
                  }
                }
              })
            } else {
              dispatch({
                type: 'getList',
                payload: {
                  data: {
                    searchType: 1,
                    memOrgCode: org['orgCode'],
                    ...defaultParas,
                    ...query,
                  },
                },
              });
            }
          }
        }
      });
    },
  },
  effects: {
    // 列表
    *getList({ payload }, { call, put, select }) {
      const { filter, memName } = yield select((state) => state['memBasic']);
      payload['data'] = { ...payload['data'], ...filter, memName };
      const { data = {} } = yield call(getList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
          // list:data['records'],
          // pagination:{
          //   current:data['current'],
          //   pageSize:data['size'],
          //   total:data['total'],
          // }
        },
      });
    },

    *getzylist({ payload }, { call, put, select }) {
      const { filter, memName,processNode } = yield select((state) => state['memBasic']);
      payload['data'] = { ...payload['data'], ...filter, memName ,processNode};
      const { data = {} } = yield call(getzylist, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        },
      });
    },
    *flowcount({ payload }, { call, put }) {
      const { data = {} } = payload;
      let res = yield call(flowcountdy, { data });
      yield put({
        type: 'updateState',
        payload: {
          flowCount:res.data,
        }
      })
    },
    // 获取未锁定列表
    *getUnlockList({ payload }, { call, put, select }) {
      const { filter, memName } = yield select((state) => state['memBasic']);
      payload['data'] = { ...payload['data'], ...filter, keyword: memName };
      const { data = {} } = yield call(memLockedList, payload);
      const res = changeListPayQuery(data);
      yield put({
        type: 'querySuccess',
        payload: {
          ...res,
        },
      });
    },
    // 查找人员信息
    *findMem({ payload }, { call, put }) {
      const res = yield call(findMem, payload);
      const { code = 500, data = {} } = res || {};
      yield put({
        type: 'updateState',
        payload: {
          basicInfo: code === 0 ? data : {},
        },
      });
    },
    *zymemInfo({ payload }, { call, put }) {
      const res = yield call(zymemInfo, payload);
      const { code = 500, data = {} } = res || {};
      yield put({
        type: 'updateState',
        payload: {
          basicInfo: code === 0 ? data : {}
        }
      })
    },
    // 查找人员信息
    *findHistoryMem({ payload }, { call, put }) {
      const res = yield call(findHistoryByCode, payload);
      const { code = 500, data = {} } = res || {};
      yield put({
        type: 'updateState',
        payload: {
          basicInfo: code === 0 ? { ...data, findHistory: true } : {},
        },
      });
    },
    // 新增编辑保存
    *save({ payload }, { call, put }) {
      const { type = '', data = {} } = payload;
      let res;
      switch (type) {
        case 'add':
          res = yield call(addMem, { data });
          break;
        case 'edit':
          res = yield call(updateMem, { data });
          break;
        default:
          break;
      }
      return res;
    },
       // 遵义补录新增编辑保存
       *zysave({ payload }, { call, put }) {
        const { type = '', data = {} } = payload;
        let res;
        switch (type) {
          case 'add':
            res = yield call(zyaddMem, { data });
            break;
          case 'edit':
            res = yield call(updateMem, { data });
            break;
          default:
            break;
        }
        return res;
      },
    // 党内职务列表
    *partyPosition({ payload }, { call, put }) {
      const { data = {} } = yield call(partyPosition, payload);
      yield put({
        type: 'updateState',
        payload: {
          list_partyPosition: data['list'],
          pagination_partyPosition: {
            current: data['pageNumber'],
            pageSize: data['pageSize'],
            total: data['totalRow'],
          },
        },
      });
    },
    // 行政职务列表
    *administrative({ payload }, { call, put }) {
      const { data = {} } = yield call(getListByMemCode, payload);
      yield put({
        type: 'updateState',
        payload: {
          list_administrative: data['list'],
          pagination_administrative: {
            current: data['pageNumber'],
            pageSize: data['pageSize'],
            total: data['totalRow'],
          },
        },
      });
    },
  },
});
export default memBasic;
