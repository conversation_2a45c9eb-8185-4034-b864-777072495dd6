import request from "@/utils/request";
import qs from 'qs';

// 新增组织生活
export function addOrgLife(params) {
    console.log('addOrgLife.params===', params);
    return request(`/api/org/life/addOrgLife`, {
        method: 'POST',
        body: params,
    });
}

// 组织生活列表
export function orgLifeList(params) {
    console.log('orgLifeList.params===', params);
    return request(`/api/org/life/orgLifeList`, {
        method: 'POST',
        body: params,
    });
}

// 组织生活详情
export function findOrgLifeById(params) {
    console.log('findOrgLifeById.params===', params);
    return request(`/api/org/life/findOrgLifeById?${qs.stringify(params)}`, {
        method: 'get',
    });
}

// 修改组织生活
export function updateOrgLife(params) {
    console.log('updateOrgLife.params===', params);
    return request(`/api/org/life/updateOrgLife`, {
        method: 'POST',
        body: params,
    });
}

// 删除组织生活
export function deleteOrgLife(params) {
    console.log('deleteOrgLife.params===', params);
    return request(`/api/org/life/deleteOrgLife?${qs.stringify(params)}`, {
        method: 'get',
    });
}

// 导出 组织生活
export function exportOrgLife(params) {
    console.log('updateOrgLife.params===', params);
    return request(`/api/org/life/outExportXlsx`, {
        method: 'POST',
        body: params,
    });
}
export function teamList(params) {
  return request(`/api/org/group/getList?${qs.stringify(params)}`,);
}