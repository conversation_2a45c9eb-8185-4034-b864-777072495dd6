import request from "@/utils/request";
import qs from 'qs';

// export function addOrUpdate(params) {
//   return request(`/api/unit/township/addOrUpdate`,{
//     method:'POST',
//     body:params,
//   });
// }

// export function findByUnitCode(params) {
//   return request(`/api/unit/township/findByUnitCode?${qs.stringify(params)}`,{
//     method:'GET',
//   });
// }


// 获取列表
// export function getList(params) {
//   return request(`/api/unit/expand/list`,{
//     method:'POST',
//     body:params,
//   });
// }

export function getList(params) {
  return request(`/api/unit/expand/list?${qs.stringify(params)}`,{
    method:'GET',
  });
}

// 删除
// export function del(params) {
//   return request(`/api/unit/expand/del?${qs.stringify(params)}`,{
//     method:'GET',
//   });
// }

export function del(params) {
  return request(`/api/unit/expand/del`,{
    method:'POST',
    body:params,
  });
}

// 新增或修改
export function add(params) {
  return request(`/api/unit/expand/save`,{
    method:'POST',
    body:params,
  });
}

export function addOrUpdate(params) {
  return request(`/api/unit/expand/update`,{
    method:'POST',
    body:params,
  });
}

// 根据code查找
export function findByCode(params) {
  return request(`/api/unit/township/findByCode?${qs.stringify(params)}`,{
    method:'GET',
  });
}