import request from "@/utils/request";
import qs from 'qs';

export function getList(params) {
  return request(`/api/org/getList`, {
    method: 'POST',
    body: params,
  });
}

export function findOrgResume(para) {
  return request(`/api/org/findOrgResume?${qs.stringify(para)}`, {
    method: 'get',
  });
}

export function getOrgSortList(params) {
  return request(`/api/org/getOrgSortList`, {
    method: 'POST',
    body: params,
  });
}

export function updateOrgSort(params) {
  return request(`/api/org/updateOrgSort`, {
    method: 'POST',
    body: params,
  });
}
export function tipsForChangingOrgType(params) {
  return request(`/api/org/tipsForChangingOrgType`, {
    method: 'POST',
    body: params,
  });
}

// 点击 与/不与 上级党组织所在单位建立联合党支部 时
export function superUnitOrgLinked(para) {
  return request(`/api/org/superUnitOrgLinked?${qs.stringify(para)}`, {
    method: 'get',
  });
}

// 新增党支部联系人
export function addContactMem(params) {
  console.log('memSava.params===',params);
  return request(`/api/org/contactWork/addContactMem`, {
    method: 'POST',
    body: params,
  });
}

// 修改党支部联系人
export function updateContactMem(params) {
  console.log('updateContactMem.params===',params);
  return request(`/api/org/contactWork/updateContactMem`, {
    method: 'POST',
    body: params,
  });
}

// 查询党支部联系人
export function contactMemList(para) {
  console.log('contactMemList.params===',para);
  return request(`/api/org/contactWork/contactMemList?${qs.stringify(para)}`, {
    method: 'get',
  });
}

// 删除党支部联系人
export function deleteContactMem(para) {
  console.log('deleteContactMem.params===',para);
  return request(`/api/org/contactWork/deleteContactMem?${qs.stringify(para)}`, {
    method: 'get',
  });
}

// 新增工作开展情况
export function addContactWork(params) {
  console.log('addContactWork.params===',params);
  return request(`/api/org/contactWork/addContactWork`, {
    method: 'POST',
    body: params,
  });
}

// 工作开展情况列表
export function contactWorkList(params) {
  console.log('contactWorkList.params===',params);
  return request(`/api/org/contactWork/contactWorkList`, {
    method: 'POST',
    body: params,
  });
}

// 修改工作开展情况
export function updateContactWork(params) {
  console.log('updateContactWork.params===',params);
  return request(`/api/org/contactWork/updateContactWork`, {
    method: 'POST',
    body: params,
  });
}

// 删除工作开展情况
export function deleteContactWork(para) {
  console.log('deleteContactWork.params===',para);
  return request(`/api/org/contactWork/deleteContactWork?${qs.stringify(para)}`, {
    method: 'get',
  });
}

// 工作开展情况详情
export function findContactWork(para) {
  console.log('findContactWork.params===',para);
  return request(`/api/org/contactWork/findContactWork?${qs.stringify(para)}`, {
    method: 'get',
  });
}

// 党支部达标/示范点标记 查询
export function findExampleSiteById(para) {
  console.log('findExampleSiteById.params===',para);
  return request(`/api/org/contactWork/findExampleSiteById?${qs.stringify(para)}`, {
    method: 'get',
  });
}

// 修改 党支部达标/示范点标记
export function updateExampleSite(params) {
  console.log('updateExampleSite.params===',params);
  return request(`/api/org/contactWork/updateExampleSite`, {
    method: 'POST',
    body: params,
  });
}

// 班子成员根据党员姓名查找信息
export function getOrgCommitteeSrcUnit(para) {
  console.log('getOrgCommitteeSrcUnit.params===',para);
  return request(`/api/committee/getOrgCommitteeSrcUnit?${qs.stringify(para)}`, {
    method: 'get',
  });
}

//流动党组织-查询批准成立党组织14开头
export function approveOrg(para) {
  return request(`/api/org/flow/find/approveOrg?${qs.stringify(para)}`, {
    method: 'get',
  });
}

