// 流动党员-流入管理
import React, { Fragment } from 'react';
import { Tabs } from 'antd';
import { getSession } from '@/utils/session';
import NotIncludeManage from './components/notIncludeManage';
import IncludeManage from './components/includeManage';
import InLibrary from './components/inLibrary';
import History from './components/history';
import ReminderMobilePartyMembers from '../inflowOrganization/components/ReminderMobilePartyMembers';

const TabPane = Tabs.TabPane;

export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      activeTab: '1',
    };
  }

  render() {
    const { activeTab } = this.state;

    return (
      <Fragment>
        <Tabs
          activeKey={activeTab}
          onChange={(e) => {
            this.setState({
              activeTab: e,
            });
          }}
        >
          <TabPane tab="未纳入支部管理" key="1" />
          <TabPane tab="已纳入支部管理" key="2" />
          <TabPane tab="县级流入库" key="3" />
          <TabPane tab="流入历史" key="4" />
          <TabPane tab="流动党员提醒" key="5" />
        </Tabs>
        {activeTab === '1' && <NotIncludeManage org={getSession('org')} />}
        {activeTab === '2' && <IncludeManage org={getSession('org')} />}
        {activeTab === '3' && <InLibrary org={getSession('org')} />}
        {activeTab === '4' && <History org={getSession('org')} />}
        {activeTab === '5' && <ReminderMobilePartyMembers org={getSession('org')} />}
      </Fragment>
    );
  }
}
