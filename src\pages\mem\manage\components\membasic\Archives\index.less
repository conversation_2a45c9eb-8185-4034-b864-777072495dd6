.photoBox {
  width: 100%;
  height: 500px;
  border: 1px solid #ccc;
  margin-top: 10px;
  overflow: auto;
  // display: flex;
  // justify-content: center;
  // align-items: center;
  // cursor: pointer;
}

.trans {
  width: 140px;
  text-align: center;
}
.contrast {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // > div {
  //   width: 50%;
  //   border: 1px solid red;
  // }
}
.contrast2 {
  
}
.tit {
  font-weight: bolder;
}

.step_1 {
  // height: 554px;
}
