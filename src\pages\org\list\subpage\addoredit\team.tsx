/**
 * 领导班子
 **/
import React from 'react'
import {connect} from "dva";
import { PlusOutlined } from '@ant-design/icons';
import { But<PERSON>, Collapse, Popconfirm } from 'antd';

import AddTeam from './addTeam';
import MemSelect from '@/components/MemSelect';
import head from '@/assets/head.jpg';
import styles from './leader.less';
import Tip from '@/components/Tip';

const Panel = Collapse.Panel;
// @ts-ignore
@connect(({org})=>({org}))
export default class index extends React.Component<any,any>{
  constructor(props){
    super(props);
    this.state={
      type:'add'
    }
  }
  callback=(key)=>{
    const keys=[...key].pop();
    const { teamList }=this.props.org;
    let obj=teamList[keys];
    if(obj){
      this.props.dispatch({
        type:'org/groupMemList',
        payload:{
          groupCode:obj['code'],
          keys,
          pageNum:1,
          pageSize:100,
        }
      })
    }
  };
  componentDidMount(){
    this.action();
  }
  action=()=>{
    const {basicInfo}=this.props.org;
    if(basicInfo['code']){
      this.props.dispatch({
        type:'org/findTeam',
        payload:{
          orgCode:basicInfo['orgCode'],
          pageNum:1,
          pageSize:100
        },
      });
    }
  };
  del=async (e,item)=>{
    e.stopPropagation();
    const obj=await this.props.dispatch({
      type:'org/delGroup',
      payload:{
        code:item['code']
      }
    });
    if(obj && obj['code']===0){
      this.action();
    }
  };
  header=(item)=>{
    return (
      <span className={styles.header}>
        <span>{item['name']}</span>
        <div>
          <a href={'#'} onClick={(e)=>this.editJc(e,item)}>编辑</a>
          <Popconfirm title="是否解散该小组?" onConfirm={(e)=>this.del(e,item)} okText="是" cancelText="否">
            <a href={'#'} className={'del'} onClick={e=>e.stopPropagation()}>解散</a>
          </Popconfirm>
        </div>
      </span>
    )
  };
  editJc=(e,item)=>{
    e.stopPropagation();
    this.setState({type:'edit',dataInfo:item},()=>{
      this['addTeam'].showModal();
    });
  };
  editMem=()=>{
    this.setState({type1:'editMem'});
    this['AddMember'].showModal();
  };
  add=(e)=>{
    this.setState({
      type:'add',
      dataInfo:undefined
    },()=>{
      this['addTeam'].showModal();
    });
  };
  delGroupMem=(item,key)=>{
    this.props.dispatch({
      type:'org/delGroupMem',
      payload:{
        code:item['code']
      }
    }).then(res=>{
      Tip.success('操作提示','删除成功');
      this.callback([key])
    });
  };
  addGroupMem=(val,memList,key)=>{
    if(val && val.length>0){
      const [obj,...other]=val;
      this.props.dispatch({
        type:'org/addGroupMem',
        payload:{
          data:{
            groupCode:memList['code'],
            groupName:memList['name'],
            memCode:obj['code'],
          }
        }
      }).then(res=>{
        this.callback([key])
      })
    }
  };
  render() {
    const { type } =this.state;
    const { teamList,basicInfo }=this.props.org;
    const { org }=this.props;
    return (
      <div style={{padding:'0 20px'}}>
        {/*<AddOrEdit/>*/}
        <AddTeam
          queryList={this.action}
          title={type === 'edit' ? '编辑党小组' :'新增党小组'}
          wrappedComponentRef={(e)=>this['addTeam']=e}
          {...this.props}
          {...this.state}
        />
        <Button type="primary" icon={<PlusOutlined />} style={{marginBottom:10}} onClick={this.add}>添加党小组</Button>
        <Collapse onChange={this.callback}>
        {
          teamList && teamList.map((obj,index)=>{
            let data=org[`groupMemList_${index}`] || [];
            return (
              <Panel header={this.header(obj)} key={index}>
                {
                  data.map((item,ind)=>{
                    return (
                      <div key={ind} className={styles.panel_body}>
                        <div><img src={head} style={{width:128,height:158}}/></div>
                        <div>
                          <h4>{item['memName']}</h4>
                          <div className={styles.link_edit}>
                            <Popconfirm title="是否删除?" onConfirm={()=>{this.delGroupMem(item,index)}} okText="是" cancelText="否">
                              <a>删除</a>
                            </Popconfirm>
                          </div>
                        </div>
                      </div>
                    )
                  })
                }
                <MemSelect org={basicInfo} onChange={(val)=>this.addGroupMem(val,obj,index)}>
                  <div className={styles.add}>
                    <PlusOutlined style={{fontSize: '50px', transform: 'translateY(100%)'}} />
                  </div>
                </MemSelect>
                {/*<AddTeamMem*/}
                {/*  title={type1 === 'editMem' ? '编辑' :'新增'}*/}
                {/*  wrappedComponentRef={(e)=>this['AddMember']=e}*/}
                {/*  {...this.props}*/}
                {/*>*/}
                {/*  <div className={styles.add}></div>*/}
                {/*</AddTeamMem>*/}
              </Panel>
            );
          })
        }
        </Collapse>
      </div>
    );
  }
}
