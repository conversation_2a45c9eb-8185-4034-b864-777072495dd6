import React, { Fragment, useImperativeHandle, useRef, useState } from 'react';
import st from './demo2.less';
import { Button, Form, Input, InputNumber, Modal } from 'antd';

const formItemLayout3 = {
  labelCol: {
    sm: { span: 8 },
  },
  wrapperCol: {
    sm: { span: 12 },
  },
};

const Mod = React.forwardRef((props: any, ref) => {
  const { title = '党旗设置', onOK } = props;
  const [visible, setVisible] = useState(false);
  const [query, setQurey] = useState<any>({});
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    open: (query) => {
      setVisible(true);
    },
    clear: () => {
      // clear();
    },
  }));
  const handleOk = () => {
    onOK && onOK(query);
    handleCancel();
  };
  const handleCancel = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setQurey({});
  };
  const onFinish = () => {};
  return (
    <Fragment>
      <Modal
        title={title}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
        footer={null}
        maskClosable={false}
        destroyOnClose={true}
      >
        <Form form={form} {...formItemLayout3} onFinish={onFinish}>
          <Form.Item
            name="actualWidth"
            label="实际宽度(m)"
            // initialValue={query['hasFiling'] || 0}
          >
            <InputNumber></InputNumber>
          </Form.Item>
          <Form.Item
            name="actualHeight"
            label="实际高度(m)"
            // initialValue={query['hasFiling'] || 0}
          >
            <InputNumber></InputNumber>
          </Form.Item>
          <Form.Item
            name="rightNumber"
            label="向右偏移距离(m)"
            // initialValue={query['hasFiling'] || 0}
          >
            <InputNumber></InputNumber>
          </Form.Item>
        </Form>
      </Modal>
    </Fragment>
  );
});

const demo2 = () => {
  const [flagWith, setFlagWidth] = useState<any>(2400);
  const [flagHeight, setFlagHeight] = useState<any>(1600);
  const ref: any = useRef();
  const onClick = async () => {
    ref.current.open();
  };

  const sideWidth = (8160 - flagWith) / 3 / 2;
  return (
    <React.Fragment>
      <div>
        <Button onClick={onClick}>设置</Button>
        <Mod ref={ref}></Mod>
      </div>

      <div className={st.box}>
        <div className={st.left} style={{ width: sideWidth }}></div>
        <div className={st.midBox} style={{ width: flagWith / 3, height: '100%' }}>
          <div className={st.flag} style={{ width: '100%', height: flagHeight / 3 }}></div>
          <div className={st.bot}>底部内容</div>
        </div>
        <div className={st.right} style={{ width: sideWidth }}></div>
      </div>
    </React.Fragment>
  );
};

export default demo2;
