import React, { useImperativeHandle, useRef, useEffect, useState } from 'react';
import { Modal, Button, Form, Input, Select, Checkbox } from 'antd';
import request from '@/utils/request';
import Tip from '@/components/Tip';
import styles from './index.less';
import { getTableColsCheck } from "@/pages/text/components/Pegging"
import { getReportPegging } from "@/services/tmw"

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const index = (props, ref) => {
  //isConfiguration 判断是不是报表配置  true就是报表配置
  const { canEdit = true, isConfiguration = false } = props;
  const _ref: any = useRef();
  const org = JSON.parse(sessionStorage.getItem('org') || '{}');
  useImperativeHandle(ref, () => ({
    open: (query) => {
      console.log("🚀 ~ useImperativeHandle ~ query:", query, props)
      setQuery(query);
      getInfo(query);
      setIsModalVisible(true);
    },
    clear: () => {
      // clear();
    },
  }));
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState<any>();
  const [form] = Form.useForm();
  const [selectData, setSelectData] = useState<any>([]);
  const [selectKey, setSelectKey] = useState<any>(undefined);
  const [checkData, setCheckData] = useState<any>([]);
  const [column, setColumn] = useState<any>([]);

  const getInfo = async (val) => {
    console.log("🚀 ~ getInfo ~ val:", val)
    const res = await request('/api/annual/findReportExplain', {
      method: 'POST',
      body: {
        data: {
          ...val,
        },
      },
    });
    if (res.code == 0) {
      form.setFieldsValue({
        orgCode: res.data?.explain ?? undefined,
        reportType: res.data?.reportType ?? undefined
      });
      //includeFieldList 和 peggingType 是一个参数
      let data = JSON.parse(res.data?.includeFieldList || '[]');
      console.log("🚀 ~ getInfo ~ data:", data);
      if (data.length > 0) {
        await setCheckData(data.map((item) => {
          if (item) {
            return String(item["key"])
          }
        }))
      }
      setSelectKey(res.data?.reportType)
      setColumn(getTableColsCheck(res.data?.reportType).column)
      getTableSelectData(val)
    }
  };

  const getTableSelectData = async (val) => {

    const { data, code } = await getReportPegging({
      data: {
        ...val,
        orgCode: 1,
        orgLevelCode: 500,
      }
    })
    if (code == 0) {
      form.setFieldsValue({
        reportType: data?.type ?? undefined
      });
      setSelectKey(data?.type)
      setColumn(getTableColsCheck(data?.type).column)
      console.log("🚀 ~ getTableSelectData ~ data:", data);
    }
  }

  const onFinish = async (e) => {
    const resData: any = []
    checkData.forEach((item) => {
      resData.push(column[item - 1])
    });
    console.log(checkData, resData, "***************************");
    setLoading(true);
    const res = await request('/api/annual/saveReportExplain', {
      method: 'POST',
      body: {
        data: {
          ...query,
          ...e,
          // peggingType: JSON.stringify(resData),
          fieldList: resData,
          reportType: selectKey
        },
      },
    });
    setLoading(false);
    if (res.code == 0) {
      Tip.success('操作提示', '操作成功');
      form.resetFields();
      handleCancel();
      props?.onOK();
    }
  };

  const handleCancel = async () => {
    form.resetFields();
    setCheckData([])
    setColumn([])
    setIsModalVisible(false);
    setLoading(false)
  };

  const dataReportChange = (e) => {
    setSelectKey(e)
    const data = getTableColsCheck(e)
    setCheckData([])
    setColumn(data.column)
  }

  const boxChange = (val = []) => {
    setCheckData(val);
  };

  useEffect(() => {
    const data = [  //目前前端写死  反查表头
      { value: 'ccp_mem_all', label: 'ccp_mem_all' },
      { value: 'ccp_mem_report', label: 'ccp_mem_report' },
      { value: 'ccp_org_reward', label: 'ccp_org_reward' },
      { value: "ccp_develop_excellent_mem", label: 'ccp_develop_excellent_mem' },
      { value: 'ccp_org_all', label: 'ccp_org_all' },
      { value: 'ccp_unit_all', label: 'ccp_unit_all' },
      { value: 'ccp_mem_develop_all', label: 'ccp_mem_develop_all' },
      { value: 'ccp_mem_flow_all', label: 'ccp_mem_flow_all' },
      { value: 'ccp_mem_reward_all', label: 'ccp_mem_reward_all' },
      { value: 'ccp_org_slack_all', label: 'ccp_org_slack_all' },
      { value: 'ccp_org_recognition_all', label: 'ccp_org_recognition_all' },
      { value: 'ccp_org_industry_all', label: 'ccp_org_industry_all' },
      { value: 'ccp_develop_step_log_all', label: 'ccp_develop_step_log_all' },
      { value: 'ccp_org_party_congress_committee_all', label: 'ccp_org_party_congress_committee_all' },
      { value: 'ccp_org_party', label: 'ccp_org_party' },
      { value: 'ccp_transfer_statistics', label: 'ccp_transfer_statistics' },
      { value: 'mem_flow_all', label: 'mem_flow_all' },
    ]
    setSelectData(data)
  }, [])

  return (
    <React.Fragment>
      <Modal
        title="单元格解释"
        forceRender
        visible={isModalVisible}
        onOk={() => {
          canEdit ? form.submit() : handleCancel()
        }}
        width={1400}
        onCancel={handleCancel}
        maskClosable={false}
        confirmLoading={loading}
      >
        <Form form={form} {...formItemLayout} onFinish={onFinish}>
          <Form.Item
            name="orgCode"
            label="单元格解释"
            rules={[{ required: true, message: '单元格解释' }]}
          >
            <Input.TextArea rows={4} placeholder={'单元格解释'} disabled={!canEdit} />
          </Form.Item>
          {isConfiguration && <Form.Item
            name="reportType"
            label="数据反查配置"
            rules={[{ required: true, message: '数据反查配置' }]}
          >
            <Select placeholder={'请选择'} disabled={true} onChange={(e) => { dataReportChange(e) }}>
              {selectData.map((item) => {
                return (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>}
        </Form>
        {isConfiguration && <div className={styles.clu}>
          <Checkbox.Group value={checkData} onChange={boxChange}>
            {selectKey && column.map((obj, index) => {
              return (
                <Checkbox style={{ width: "320px", marginLeft: "8px" }} key={obj['key']} value={obj['key']}>
                  {obj['label']}
                </Checkbox>
              );
            })}
          </Checkbox.Group>
        </div>}
      </Modal>
    </React.Fragment>
  );
};
export default React.forwardRef(index);
