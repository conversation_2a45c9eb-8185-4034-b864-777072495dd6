import React, {PureComponent, Fragment} from 'react';
import {Consumer} from './context';
import styles from './middleItems.less';
import _isEmpty from 'lodash/isEmpty';
import _isObject from 'lodash/isObject';
export default class index extends PureComponent<any, any>{
  constructor(props){
    super(props);
    this.state = {

    };
  }
  titleStyle = ({title = '', content = '', color = '#4F9CEE'} = {}) => {
    function createMarkup(text) {
      return {__html: text};
    }
    return (
      <div>
        <div className={styles.title} style={{borderLeft: `12px solid ${color}`}}>
          <h2 className={styles.text}>{title}</h2>
        </div>
          <div>
            {
              _isObject(content) ?
                <Fragment>{content}</Fragment> :
                <Fragment>{React.createElement('div', {dangerouslySetInnerHTML: createMarkup(content), ref:this['config'], id:'mydiv'})}</Fragment>
            }
          </div>
      </div>
    );
  };
  renderRow = (arr) => {
    return (
      <Fragment>
        {
          !_isEmpty(arr) && arr.map((item, index) => {
            return (
              <div key={index}>
                {this.titleStyle(item)}
              </div>
            );
          })
        }
      </Fragment>
    );
  };

  render() {
    return (
      <Consumer>
        { (val) => {
          //@ts-ignore
          const {middleRows = []} = val || {};
          return (
            <Fragment>
              {this.renderRow(middleRows)}
            </Fragment>
          );
        }}
      </Consumer>
    );
  }
}
