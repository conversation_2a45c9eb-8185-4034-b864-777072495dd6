.searchBg {
  background: url('../../../../assets/qzs/bg1.webp') no-repeat;
  background-size: 100% 100%;
}

.searchMem {
  .searchBg;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50px 10px 10px 10px;

  .name {
    font-size: 30px;
  }
  .muban {
    display: flex;
    flex-direction: column;
    width: 100%;
    font-size: 20px;
    flex: 1;
    .mubanTab {
      flex: 1;
      display: flex;
      flex-direction: column;
      :global {
        .ant-tabs-tab {
          background: #be0c10 !important;
          color: white !important;
          font-size: 20px;
        }
        .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
          color: #ffeeae !important;
          font-size: 20px;
        }
      }
      .mubanBody {
        font-family: Source Han Serif SC;
        font-weight: 800;
        font-size: 18px;
        color: #be0c10;
        p {
          margin: 0 !important;
        }
        :global {
          .w-e-toolbar {
            display: none !important;
          }
          .w-e-text-container {
            height: 350px !important;
            // height: 50% !important;
            border: none !important;
            z-index: 1 !important;
          }
          .w-e-text::-webkit-scrollbar {
            width: 8px !important;
            height: 8px !important;
          }
          .w-e-text::-webkit-scrollbar-thumb {
            border-radius: 5px !important;
            box-shadow: 0 0 2px #fff !important;
            background-color: #ddd !important;
          }
          .w-e-text::-webkit-scrollbar-track {
            border-radius: 0;
            box-shadow: inset 0 0 2px #fff;
          }
        }
      }
    }
  }

  .btn {
    background-color: #be0c10;
    border-radius: 78px;

    cursor: pointer;

    font-family: Microsoft YaHei;
    font-weight: bold;
    font-size: 32px;
    color: #fff9e7;
    text-align: center;
    margin-bottom: 30px;
  }
}

.select {
  :global {
    .ant-select-selector {
      // antd 版本 4.*
      font-size: 30px !important;
      height: 50px !important;
      background-color: transparent !important;
      border: 2px solid #c1a984 !important;
      border-radius: 78px !important;
    }
    .ant-select-selection-placeholder {
      line-height: 50px;
    }
    .ant-select-selection-item,
    .ant-select-selection-placeholder {
      display: flex;
      align-items: center;
    }

    // 选择器右侧图标样式
    .ant-select-arrow {
      font-size: 14px !important;
    }
  }
}
.dropdown-style {
  :global {
    .ant-select-dropdown {
      font-size: 30px !important;
      // height: 200px !important;
    }
    .ant-select-item {
      font-size: 30px !important;
      height: 50px;
      line-height: 50px;
    }
  }
}
:global {
  .ant-select-dropdown {
    font-size: 30px !important;
    // height: 200px !important;
  }
  .ant-select-item {
    font-size: 30px !important;
    height: 50px;
    line-height: 50px;
  }
}
