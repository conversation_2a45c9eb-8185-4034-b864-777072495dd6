import React, { useState, useImperativeHandle, useEffect } from 'react';
import { Modal, Form, Row, Col, Input, Button } from 'antd';
import TableSelect from '@/components/TableSelect';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import { getSession } from "@/utils/session";
import {transfer} from '@/pages/developMem/services/index';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
const index = React.forwardRef((props: any, ref) => {
  const {
    title = '人员调整',
    width = 800,
    onOK,
  } = props;
  const org = getSession('org') || {};
  const [visible, setVisible] = useState(false);
  const [query, setQurey] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const [initValueTB, setInitValueTB] = useState<any>([]);
  useImperativeHandle(ref, () => ({
    open: query => {
      setVisible(true);
      setQurey(query);
    },
    clear: () => {
      // clear();
    },
  }));
  useEffect(() => {
    if (!_isEmpty(query)) {
    }
  }, [JSON.stringify(query)])

  const handleCancel = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setQurey({});
  };
  const onFinish =async (e) => {
    if(e['memId']){
      e['memId'] = e['memId'].map(it => it.code);
    }
    e['targetOrgId'] = _get(e,'targetOrgId[0].code','');
    e['srcOrgId'] = _get(org,'code','');

    setLoading(true);
    const {code = 500 } = await transfer({data:{
      ...e
    }});
    setLoading(false);
    if(code === 0){
      Tip.success('操作提示', '操作成功');
      handleCancel();
      onOK && onOK();
    }
  }
  const col = [
    {
      title: '姓名',
      dataIndex: 'name',
    },
    {
      title: '性别',
      dataIndex: 'sexCode',
      render: (text) => {
        return (
          <span> {text === '1' ? '男' : '女'} </span>
        )
      }
    },
    {
      title: '公民身份证',
      dataIndex: 'idcard',
    },
    {
      title: '电话',
      dataIndex: 'phone',
    },
    {
      title: '党员类型',
      dataIndex: 'd08Name',
    },
  ];
  return (
    <Modal
      title={title}
      visible={visible}
      onOk={() => {
        form.submit();
      }}
      onCancel={handleCancel}
      width={width}
      confirmLoading={loading}
      destroyOnClose={true}
    >
      <Form form={form} {...formItemLayout} onFinish={onFinish}>
        <Form.Item
          name="memId"
          label="人员选择"
          rules={[{ required: true, message: '人员选择' }]}
        >
          <TableSelect
            // ref={tbRef}
            columns={col}
            title={'人员选择'}
            action={'/api/mem/develop/memList'}
            method={'POST'}
            payload={{
              memOrgCode: org['orgCode'],
            }}
            rowSelectionType={'checkbox'}
            rowKey={'code'}
            renderTableQuery={(res: any) => {
              const {
                data: { list: records = [], pageNumber: current = 1, pageSize: size = 10, totalRow: total = 0 } = {},
              } = res;
              return {
                list: records,
                pagination: { pageSize: size, total: total, current, pageNum: current },
              };
            }}
            renderSearch={({
              list,
              query,
              selectedItems,
              selectedRowKeys,
              form,
              searchCallBack,
            }: any) => {
              return <FormComp onFinish={searchCallBack}/>;
            }}
            onSearchCallBack={(val: any) => {
              return val;
            }}
            onSubmit={({ selectedRowKeys, selectedItems, query }: any) => {
              setInitValueTB(selectedItems);
              form.setFieldsValue({
                memId: selectedItems
              });
              return 0;
            }}
            inputValueRender={(val: any) => {
              return val.name;
            }}
            hasOwnShowComp={false}
          />
        </Form.Item>
        <Form.Item
          name="targetOrgId"
          label="目的组织选择"
          rules={[{ required: true, message: '目的组织选择' }]}
        >
          <OrgSelect orgTypeList={['3', '4']}
            // initValue={basicInfo['orgName']}
            onChange={(e: any) => {
              if (!_isEmpty(e)) {
                const { code, d01Code } = e[0] || {};
                // this.getUnitList(code);
              }
            }}
            // disabled={!!basicInfo['orgCode']}
            placeholder={'请选择所在党支部'} />
        </Form.Item>
      </Form>
    </Modal>
  )
});
export default index;


export const FormComp = (props: any) => {
  const { onFinish, isDetail = false } = props;
  const [form] = Form.useForm();
  return (
    <Form form={form} {...formItemLayout} onFinish={onFinish}  layout={'inline'} style={{marginBottom:10}}>
      <Form.Item name='memName'
                //  label="姓名"
                 rules={[{ required: false, message: '姓名' }]}
      >
        <Input.Search allowClear onSearch={()=>form.submit()} style={{width:260}}/>
      </Form.Item>
    </Form>
  );
};
