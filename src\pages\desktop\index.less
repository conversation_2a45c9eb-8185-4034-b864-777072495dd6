@normalMarignWidth:60px;
@hearHeight:100px;
@logoHeight:80px;
@btnsHeight:calc(@logoHeight - 20px);
@mainHeight:calc(100vh - @hearHeight);

.bg {
  background: #FFF4EB;
  height: 100vh;
  width: 100%;
  .head {
    //background: linear-gradient(#E3524C,#E76D63);
    background: url("../../assets/desktop/top.webp") no-repeat center;
    background-size: 100% 100%;
    //border: 1px solid #2e2e2e;
    height: @hearHeight;
    width: 100%;
    display: table;
    .logoTable {
      vertical-align: middle;
      display: table-cell;
      height: 100%;
      padding-left:@normalMarignWidth;
      .logo{
        height: @logoHeight;
        display: flex;
        justify-content: center;
        align-items: center;
        //width: 600px;
        //border: 1px solid #2e2e2e;
        float: left;
        >img {
          margin-right: 16px;
          position: relative;
        }
      }
      .btns {
        //background-color: rgba(255,255,255,0.2);
        height: @btnsHeight;
        margin-top: 10px;
        float: right;
        //width: 380px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        //border-radius: @btnsHeight 0 0 @btnsHeight;
        .btnsIcon {
          text-align: center;
          margin-right: 30px;
          cursor:pointer;
          > img {
            width: 26px;
            height: 32px;
          }
          // > img:hover {
          //   cursor:pointer;
          //   transition: transform 0.2s;
          //   transform: translate(3px,-3px);
          // }
        }
        .avator {
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          .avatorName {
            margin-left: 10px;
            margin-right: 20px;
            font-size: 24px;
            color: #FBF1F0;
          }
          .avatorName:hover {
            cursor:pointer;
          }
        }
      }
    }

  }
  .peopleNum{
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    .numCell{
      text-align: center;
      // width: 100px;
      // padding: 0 1vw;
      flex: auto;
      // flex-grow: 1;
    }
    .bigNum{
      white-space:nowrap;
      padding: 0 4px;
      // font-size: 32px;
      font-size: 1.8vw;
      font-weight: 400;
    }
    .text{
      font-size: 16px;
    }
  }
  .main {
    padding: 40px 60px 60px 60px;
    height: @mainHeight;
    background: url("../../assets/desktop/bg.jpg") no-repeat center;
    background-size: 100% 100%;
    &>div:first-child{
      height: 100%;
      overflow: hidden;
    }
    :global{
      .ant-card-body{
        padding: 0;
      }
      .ant-card{
        background-color: rgba(255,255,255,.1);
      }
      .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn{
        color: #CA2728;
      }
      .ant-tabs-ink-bar{
        background: #CA2728;
      }
    }
    .twoRow {
      height:100%;
      overflow: hidden;
      :global{
        .ant-tabs, .ant-tabs-content {
          height: 100%;
        }
        .ant-tabs-tabpane-active {
          height: 100%;
          overflow: auto;
        }
      }
      .rightRows {
        // height: 634px;
        overflow: auto;
      }
      // margin-top: 40px;
      .manyInfosCards {
        //min-height: 500px;
        height: 100%;
        //background: #FFFFFC;
        background-color: rgba(255,255,255,.1);
        border-radius: 4px;
        border: 1px solid #E8E8E8;
        box-shadow: 5px 5px 10px #d9d9d95e;
        padding:0 16px;

        .hasInfo{
          overflow: auto;
        }
        //padding: 24px 10px;
        .menuItem {
          display: inline-block;
          //width: 14%;
          height: auto;
          //border: 1px solid red;
          text-align: center;
          padding: 2%;
          .menuDiv {
            .img {
              width: 120px;
              height: 120px;
              display: flex;
              justify-content: center;
              align-items: center;
              img {
                width: 120px;
                height: 120px;
              }
            }
          }
          .menuDiv:hover{
            cursor:pointer;
            transition: transform 0.2s;
            transform: translate(3px,-3px);
          }
        }
        .infos {
          overflow: auto;
        }
      }
    }
    .feedback {
      //width: 100%;
      //height: 60vh;
      //overflow-y: scroll;
    }
    .footer{
      color:#626364;
      position: absolute;
      bottom: 0;
      left: calc(50% - 250px);
      text-align: center;
      width: 500px;
      height: 66px;
      line-height: 66px;
    }
  }
}
.noInfo {
  height: 100px;
  line-height: 100px;
  text-align: center;
}
//@media screen and (max-width: 1770px) {
//  .bg{
//    .main {
//      .twoRow {
//        .manyInfosCards {
//          .menuItem {
//            padding: 3%;
//            .menuDiv {
//              img {
//                width: 100px;
//                height: 100px;
//              }
//            }
//          }
//        }
//      }
//    }
//  }
//}
//@media screen and (max-width: 1620px) {
//  .bg{
//    .main {
//      .twoRow {
//        .manyInfosCards {
//          .menuItem {
//            padding: 2.4%;
//            .menuDiv {
//              img {
//                width: 90px;
//                height: 90px;
//              }
//            }
//          }
//        }
//      }
//    }
//  }
//}
// @media screen and (max-width: 1400px) {
//   .bg{
//     .head {
//       height: 90px;
//       .logoTable {
//         .logo {
//           height: 70px
//         }
//         .btns {
//           height: 50px;
//           margin-top: 10px;
//         }
//       }
//     }
//     .main {
//       padding: 40px 60px;
//       height: calc(100vh - 90px);
//       .twoRow {
//         // margin-top: 20px;
//         .manyInfosCards {
//           min-height: 390px;
//           .menuItem {
//             padding: 2%;
//             .menuDiv {
//               img {
//                 width: 80px;
//                 height: 80px;
//                 display: inline-block;
//               }
//             }
//           }
//         }
//       }
//     }
//   }
//   //.feedback{
//   //  width: 100%;
//   //  height:40vh;
//   //  overflow-y: scroll;
//   //}
//   .rightRows {
//     // height: 530px !important;
//     // overflow: auto;
//   }
// }
.nCard{
  padding-right:10px ;
  &>div{
    background: url("../../assets/desktop/1_1.png") no-repeat center;
    background-size: 100% 100%;
  }
}
.nCard2{
  padding-left: 10px;
  &>div{
    background: url("../../assets/desktop/2_1.png") no-repeat center;
    background-size: 100% 100%;
  }
}
.left{
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 消息提示
.messageContent{
  width: 100%;
  // text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  // display: -webkit-box;
  // -webkit-line-clamp: 2;
  // line-clamp: 2;
  // -webkit-box-orient: vertical;
}