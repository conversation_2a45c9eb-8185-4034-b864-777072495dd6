import React, { Fragment, useEffect, useState, useImperativeHandle, useRef } from 'react';
import ListTable from '@/components/ListTable';
import { Modal, Button, Form, Input, } from 'antd';
import { exports, detail, ignore } from '@/pages/dataCheck/services';
import { getSession } from '@/utils/session';
import { fileDownloadbyUrl } from '@/utils/method';
import { connect } from 'dva';
import Tip from '@/components/Tip';
import _isEmpty from 'lodash/isEmpty';
import _last from 'lodash/last';
import _get from 'lodash/get';

import AddEdit from '@/pages/mem/manage/components/membasic/AddEdit';
import AddDevlop from '@/pages/developMem/develop/components/Add';
import AddOrEdit from '@/pages/org/list/subpage/addoredit';
import AddOrEditt from '@/pages/[unit]/subpage/addoredit/';


const { TextArea } = Input;

interface Interface {
  title?: string,
  width?: number,
  dispatch?: any,
  // 主列表接口
  detailListAction: any,
  // 确定，回调
  onOK?: Function,
  // 主列表表头函数
  tableColumns?: Function,
  // 列表上部按钮或组件
  btns?: Function
  // 展示详情：单位unit 组织org 党员mem 发展党员devMem
  showItemDtetails?: Array<string>,
}

const ModalTL = React.forwardRef((props: Interface, ref) => {
  const {
    title = '详情',
    width = 1000,
    detailListAction,
    onOK,
    tableColumns = () => { return [] },
    btns,
    showItemDtetails = [],
  } = props;
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState([]);
  const [query, setQurey] = useState<any>({});
  const [pagination, setPagination] = useState({ pageSize: 10, total: 0, pageNum: 1, current: 1 });
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const org: any = getSession('org') || {};
  const addDevlopRef: any = useRef();


  const renderMemDetail = (text, record) => {
    return (
      <React.Fragment>
        <a onClick={async () => {
          AddEdit['WrappedComponent'].show();
          if (record && record['code']) {
            await props.dispatch({
              type: 'memBasic/findMem',
              payload: {
                code: record['code']
              }
            })
          }
        }}>{text}123</a>
      </React.Fragment>
    )
  };

  const renderDevelopDetail = (text, record) => {
    return (
      <React.Fragment>
        <a onClick={async () => {
          const { canEdit: _canEdit = [] } = props;
          addDevlopRef.current.destroy();
          if (record && record['code']) {
            await props.dispatch({
              type: 'memDevelop/findMem',
              payload: {
                code: record['code'],
                type: _get(_canEdit, '[0]', undefined) == 'mems' ? '2' : undefined,
              }
            })
          }
          addDevlopRef.current.open({ canEdit: false, editType: 'default' });
        }}>{text}</a>
      </React.Fragment>
    )
  };

  const renderOrgDetail = (text, record) => {
    return (
      <React.Fragment>
        <a onClick={async () => {
          AddOrEdit['WrappedComponent'].clear();
          if (record && record['code']) {
            props.dispatch({
              type: 'org/findOrg',
              payload: {
                code: record['code'],
              },
            }).then(res => {
              AddOrEdit['WrappedComponent'].show();
            });
          } else {
            AddOrEdit['WrappedComponent'].show();
          }
        }}>{text}</a>
      </React.Fragment>
    )
  };

  const renderUnitDetail = (text, record) => {
    return (
      <React.Fragment>
        <a onClick={async () => {
          AddOrEditt['WrappedComponent'].clear();
          if (record && record['code']) {
            props.dispatch({
              type: 'unit/findOrg',
              payload: {
                code: record['code']
              }
            }).then(res => {
              AddOrEditt['WrappedComponent'].show();
            })
          } else {
            AddOrEditt['WrappedComponent'].show();
          }
        }}>{text}</a>
      </React.Fragment>
    )
  };


  useImperativeHandle(ref, () => ({
    open: (query) => {
      setVisible(true);
      console.log("🚀 ~ useImperativeHandle ~ query:", query, props)
      if (!_isEmpty(query)) {
        setQurey(query);
        getList(query);
      }
    },
    clear: () => {
      // clear();
    },
  }));
  useEffect(() => {
  }, [JSON.stringify(query)])

  const getList = async (query, p = {}) => {
    setTableLoading(true);
    const { code = 500, data: { list = [], ...ohter } = {} } = await detailListAction({
      data: {
        pageNum: pagination?.pageNum,
        pageSize: pagination?.pageSize,
        ...query,
        ...p,
      }
    });
    setTableLoading(false);
    if (code == 0) {
      setList(list);
      setPagination({ pageSize: ohter?.pageSize, total: ohter?.totalRow, pageNum: ohter?.pageNumber, current: ohter?.pageNumber, })
    }
  };
  const handleOk = () => {
    onOK && onOK(query);
    handleCancel();
  };
  const handleCancel = () => {
    setVisible(false);
    clear();
  };
  const clear = () => {
    setQurey({});
    setList([]);
    setPagination({ pageSize: 10, total: 0, pageNum: 1, current: 1 })
  };

  const columns: any = [
    {
      title: '序号',
      dataIndex: 'num',
      align: 'center',
      width: 60,
      render: (text, record, index) => {
        return (pagination['pageNum'] - 1) * pagination['pageSize'] + index + 1;
      },
    },
    // query当前反查的信息
    ...tableColumns(query, props, { renderMemDetail, renderDevelopDetail, renderOrgDetail, renderUnitDetail }),
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   width: 80,
    //   render: (text, record) => {
    //     return (
    //       <span>
    //       </span>
    //     );
    //   },
    // },
  ];

  return (
    <Fragment>
      <Modal
        title={title}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={width}
        footer={null}
        maskClosable={false}
        destroyOnClose={true}
      >
        {btns && btns(query, props)}
        {/* <div style={{ marginBottom: 10 }}>
          <Button type={'primary'} loading={downloadLoading} onClick={async () => {
            setDownloadLoading(true);
            const { code = 500, data: { url = '' } = {} } = await exports({
              data: {
                orgCode: org?.orgCode,
                tableName: query?.tableName,
                id: query?.id,
              }
            });
            setDownloadLoading(false);
          }}>导出</Button>
        </div> */}
        <ListTable
          columns={columns}
          data={list}
          pagination={pagination}
          scroll={{
            y: 500,
          }}

          onPageChange={(pageNum, pageSize) => getList(query, { pageNum, pageSize })}
          rowKey={'id'} />
      </Modal>

      {/* 党员，发展党员，组织，单位详情弹框 */}

      {
        showItemDtetails.includes('mem') &&
        <AddEdit
          hideSave={true}
          // destroy={() => onSearch({ list: searchQuery.queryDtos }, { pageNum: 1 })}
          menuDataKey={['1']} />
      }
      {
        showItemDtetails.includes('devMem') &&
        <AddDevlop wrappedComponentRef={addDevlopRef} onsubmit={() => { }} {...props} tipMsg={{}} />
      }
      {
        showItemDtetails.includes('org') &&
        <AddOrEdit dataInfo={{}} hideSave={true} menuDataKey={['1']} />
      }
      {
        showItemDtetails.includes('unit') &&
        <AddOrEditt dataInfo={{}} hideSave={true} menuDataKey={['1']} />
      }

    </Fragment>
  )
});

export default connect(({ commonDict, memBasic, memDevelop, org, unit }: any) => ({ commonDict, memBasic, memDevelop, org, unit }), undefined, undefined, { forwardRef: true })(ModalTL)
