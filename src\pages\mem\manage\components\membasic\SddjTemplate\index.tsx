import React, { Fragment, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Col, Form, Input, Modal, Alert, Button, Popconfirm, Steps, Upload, Select } from 'antd';
import Tip from '@/components/Tip';
import UploadComp, { fitFileUrlForForm, getInitFileList } from '@/components/UploadComp';
import _isEmpty from 'lodash/isEmpty';
import st from './index.less';
import 'cropperjs/dist/cropper.css';
import _debounce from 'lodash/debounce';
import _last from 'lodash/last';

import _tr from 'lodash/trim';
import _replace from 'lodash/replace';
import { findByCode, findDomain, screensave } from '@/pages/qzs/screen/services';
import Editors from '@/components/Editor/hook';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

const index = (props: any, ref) => {
  const { title = '标题', onOK } = props;

  const editorRef: any = useRef();

  const [form] = Form.useForm();
  const [record, setRecord] = useState<any>({});
  const [info, setInfo] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [mbList, setMbList] = useState([]);

  const handleCancel = () => {
    setVisible(false);
    setRecord({});
    setInfo({});
    setMbList([]);
    setConfirmLoading(false);
    form.resetFields();
  };

  const getInfo = async (record) => {
    const res = await findByCode({ memCode: record.code });
    if (res.code == 0) {
      setInfo(res.data);
      form.setFieldsValue({
        answer: res.data.answer,
      });
      editorRef?.current?.setHtml(res.data.answer);
    }
  };

  // 模板
  const getMB = async (memCode) => {
    const res = await findDomain({ memCode });
    if (res.code == 0) {
      setMbList(res.data);
    }
  };

  const selectChange = async (v) => {
    let val = mbList?.[v - 1] || '';
    form.setFieldsValue({
      answer: val,
    });
    editorRef?.current?.setHtml(val);
  };

  const hadndleFinish = async (v) => {
    const { answer = '' } = v;
    const res = await screensave({
      data: {
        answer: answer,
        memCode: info.code,
      },
    });
    if (res.code == 0) {
      Tip.success('操作提示', '保存成功');
      handleCancel();
    }
  };

  useImperativeHandle(ref, () => ({
    open: (query) => {
      setVisible(true);
      setRecord(query);
      if (query) {
        getInfo(query);
        getMB(query.code);
      }
    },
    clear: () => {
      // clear();
    },
  }));

  return (
    <Modal
      title={'时代答卷模板录入'}
      visible={visible}
      onCancel={handleCancel}
      width={800}
      bodyStyle={{
        minHeight: 400,
      }}
      destroyOnClose={true}
      footer={[
        <Button onClick={handleCancel}>取消</Button>,
        <Button
          type="primary"
          onClick={() => {
            form.submit();
          }}
          loading={confirmLoading}
        >
          确定
        </Button>,
      ]}
    >
      {visible && (
        <div className={st.box}>
          {/* <Alert
            message="时代答卷模板录入"
            description={
              <div>
                <div>选择模板后可继续修改内容</div>
              </div>
            }
            type="info"
            showIcon
          />
          <div style={{ height: 20 }}></div> */}

          <Form form={form} onFinish={hadndleFinish} {...formItemLayout}>
            <Form.Item
              name={'mb'}
              label={'选择模板'}
              rules={[{ required: false, message: '必填' }]}
            >
              <Select onChange={selectChange}>
                {mbList.map((it, index) => (
                  <Select.Option value={(index + 1).toString()}>{`模板${index + 1}`}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name={'answer'} label={'内容'} rules={[{ required: true, message: '必填' }]}>
              <Editors id={'sddj1'} menuType={'none'} pasteFilterStyle={true} ref={editorRef} />
            </Form.Item>
          </Form>
        </div>
      )}
    </Modal>
  );
};
export default React.forwardRef(index);
