import React from 'react';
import _isEmpty from 'lodash/isEmpty';
import _isArray from 'lodash/isArray';

export const cardConfig = [
  {
    key:'4001',
    value:{
      icon:'team',
      coverImg:require('@/components/CardsGroup/assets/develop/fazhan.jpg'),
      iconColor:'#17C1C5',
      title:'发展党员总数',
      suffix:'人',
      action:'/api/chart/develop/getDevelopTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['sexName'] === '发展党员总数'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(data)=>{
        let num = 0;
        let num2 = 0;
        if(!_isEmpty(data) && _isArray(data)) {
          data.forEach(item => {
            if (item['sexName'] === '男性数量') {
              num = item['count'];
            }
            if(item['sexName'] === '女性数量'){
              num2 = item['count'];
            }
          });
        }
        return(
          <div>
            <div>男性党员:{num}人</div>
            <div>女性党员:{num2}人</div>
          </div>
        )
      },
    },
  },
  {
    key:'4005',
    value:{
      icon:'hourglass',
      coverImg:require('@/components/CardsGroup/assets/develop/shenqingren.jpg'),
      iconColor:'#6F79C1',
      title:'入党申请人',
      suffix:'人',
      action:'/api/chart/develop/getApplicantTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['type'] === 'total'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(data)=>{
        let num = 0;
        let num2 = 0;
        if(!_isEmpty(data) && _isArray(data)) {
          data.forEach(item => {
            if (item['sexCode'] === '1') {
              num = item['count'];
            }
            if(item['sexCode'] === '0'){
              num2 = item['count'];
            }
          });
        }
        return(
          <div>
            <div>男性党员:{num}人</div>
            <div>女性党员:{num2}人</div>
          </div>
      )},
    },
  },
  {
    key:'4006',
    value:{
      icon:'minus-square-o',
      coverImg:require('@/components/CardsGroup/assets/develop/jiji.jpg'),
      iconColor:'#00A0FF',
      title:'积极分子',
      suffix:'人',
      action:'/api/chart/develop/getActivistsTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['type'] === 'total'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(data)=>{
        let num = 0;
        let num2 = 0;
        if(!_isEmpty(data) && _isArray(data)) {
          data.forEach(item => {
            if (item['sexCode'] === '1') {
              num = item['count'];
            }
            if(item['sexCode'] === '0'){
              num2 = item['count'];
            }
          });
        }
        return(
          <div>
            <div>男性党员:{num}人</div>
            <div>女性党员:{num2}人</div>
          </div>
        )
      },
    },
  },
  {
    key:'4007',
    value:{
      icon:'user',
      coverImg:require('@/components/CardsGroup/assets/develop/duixiang.jpg'),
      iconColor:'#f3715c',
      title:'发展对象',
      suffix:'人',
      action:'/api/chart/develop/getObjectTotal',
      end:(val)=>{
        let num = 0;
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            if(item['type'] === 'total'){
              num = item['count'] ;
            }
          });
        }
        return num;
      },
      content:(data)=>{
        let num = 0;
        let num2 = 0;
        if(!_isEmpty(data) && _isArray(data)) {
          data.forEach(item => {
            if (item['sexCode'] === '1') {
              num = item['count'];
            }
            if(item['sexCode'] === '0'){
              num2 = item['count'];
            }
          });
        }
        return(
          <div>
            <div>男性党员:{num}人</div>
            <div>女性党员:{num2}人</div>
          </div>
        )
      },
    },
  },
];
export const chartConfig = [
  {
    key:'4002', // 发展党员类别
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_fazhan.jpg'),
      action:'/api/chart/develop/getDevelopTypeTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d08Name']);
            arr.push({
              name:item['d08Name'],
              value:item['count']
            })
          });
          arr = arr.filter(item=>item['name'] !== '党员总数');

        }
        return {
          title : {
            text: '发展党员类别',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '人员信息',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
    }
  },
  {
    key:'4003', // 男女比例
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_nannvbili.jpg'),
      action:'/api/chart/develop/getSexRatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['sexName']);
            arr.push({
              name:item['sexName'],
              value:item['count']
            })
          });
        }
        return {
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01]
          },
          yAxis: {
            type: 'category',
            data:arrName
          },
          title : {
            text: '男女比例',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c}"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series: [
            {
              name: '男女比例',
              type: 'bar',
              data: arr,
              barWidth: '50%',
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ],
        }
      },
    }
  },
  {
    key:'4014', // 学历情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_xueli.jpg'),
      action:'/api/chart/develop/getEducationRatioTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d07Name']);
            arr.push({
              name:item['d07Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '学历情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series: [
            {
              name:'学历情况',
              type:'pie',
              center: ['50%', '60%'],
              radius: ['40%', '60%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '24',
                    fontWeight: 'bold'
                  }
                }
              },
              labelLine: {
                normal: {
                  show: true
                }
              },
              data:arr
            }
          ]
        }
      },
    }
  },
  {
    key:'4015', // 专科学历以上情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_zhuanke.jpg'),
      action:'/api/chart/develop/getDzTotal',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d07Name']);
            arr.push({
              name:item['d07Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '专科学历以上情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series: [
            {
              name:'专科学历以上情况',
              type:'pie',
              center: ['50%', '60%'],
              radius: ['40%', '60%'],
              avoidLabelOverlap: false,
              label: {
                normal: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '24',
                    fontWeight: 'bold'
                  }
                }
              },
              labelLine: {
                normal: {
                  show: true
                }
              },
              data:arr
            }
          ]
        }
      },
    }
  },

  {
    key:'4008', // 发展党员一线情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_yixian.jpg'),
      action:'/api/chart/develop/getLineSituation',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['type']);
            arr.push({
              name:item['type'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '发展党员一线情况',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arr,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'4009', // 工作岗位分布情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_gongzuogangwei.jpg'),
      action:'/api/chart/develop/getJobType',
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['d09Name']);
            arr.push({
              name:item['d09Name'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '工作岗位分布情况',
            x:'left'
          },
          tooltip : {
            trigger: 'axis',
            axisPointer : {
              type : 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis : [
            {
              type : 'category',
              data : arrName,
              axisLabel : {//坐标轴刻度标签的相关设置。
                interval:0,
                rotate:"15"
              },
              axisTick: {
                alignWithLabel: true
              }
            }
          ],
          yAxis : [
            {
              type : 'value'
            }
          ],
          series : [
            {
              name:'数量',
              type:'bar',
              barWidth: '60%',
              data:arr,
              itemStyle: {
                color: function(params) {
                  let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
                  return colorList[params.dataIndex];
                }
              },
            }
          ]
        }
      },
    }
  },
  {
    key:'4010', // 公有制单位所有分类情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_gongyouzhi.jpg'),
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['type']);
            arr.push({
              name:item['type'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '公有制单位所有分类情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          // legend: {
          //   // orient: 'horizontal',
          //   // left: 'middle',
          //   top:'bottom',
          //   data: arrName
          // },
          series : [
            {
              name: '公有制单位所有分类情况',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr.sort(function(a, b) {
                return a['value'] - b['value']
              }),
              roseType : 'area',
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ],
        }
      },
      action:'/api/chart/develop/getPublicUnit',
    }
  },
  {
    key:'4011', // 非公有制单位所有分类情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_feigong.jpg'),
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['type']);
            arr.push({
              name:item['type'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '非公有制单位所有分类情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },

          // legend: {
          //   orient: 'vertical',
          //   left: 'right',
          //   top:'middle',
          //   data: arrName
          // },
          series : [
            {
              name: '非公有制单位所有分类情况',
              type: 'pie',
              radius : '55%',
              center: ['50%', '60%'],
              data:arr.sort(function(a, b) {
                return a['value'] - b['value']
              }),
              roseType : 'area',
              // itemStyle: {
              //   emphasis: {
              //     shadowBlur: 10,
              //     shadowOffsetX: 0,
              //     shadowColor: 'rgba(0, 0, 0, 0.5)'
              //   }
              // },
            }
          ],
        }
      },
      action:'/api/chart/develop/getNoPublicUnit',
    }
  },
  {
    key:'4012', // 社会组织所有分类情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_shehui.jpg'),
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['type']);
            arr.push({
              name:item['type'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '社会组织所有分类情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          // legend: {
          //   orient: 'vertical',
          //   left: 'right',
          //   top:'middle',
          //   data: arrName
          // },
          series : [
            {
              name: '人员信息',
              type: 'pie',
              radius: ['40%', '60%'],
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
      action:'/api/chart/develop/getSocietyUnit',
    }
  },
  {
    key:'4013', // 农牧渔民所有分类情况
    value:{
      coverImg:require('@/components/CardsGroup/assets/develop/chart_nongmu.jpg'),
      option:(val)=>{
        let arr:Array<object> = [];
        let arrName:Array<string> = [];
        if(!_isEmpty(val) && _isArray(val)){
          val.forEach(item=>{
            arrName.push(item['type']);
            arr.push({
              name:item['type'],
              value:item['count']
            })
          });
        }
        return {
          title : {
            text: '农牧渔民所有分类情况',
            x:'left'
          },
          tooltip : {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          legend: {
            orient: 'vertical',
            left: 'right',
            top:'middle',
            data: arrName
          },
          series : [
            {
              name: '农牧渔民所有分类情况',
              type: 'pie',
              radius: ['40%', '60%'],
              center: ['50%', '60%'],
              data:arr,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      },
      action:'/api/chart/develop/getFarmingUnit',
    }
  },
  // {
  //   key:'1017', // 党员年龄分布
  //   value:{
  //     // coverImg:require('@/components/CardsGroup/assets/develop/chart_edu.jpg'),
  //     action:'/api/chart/mem/getFgyJobRatioTotal',
  //     option:(val)=>{
  //       let arrName:Array<string> = [];
  //       let arrValue:Array<string> = [];
  //       if(!_isEmpty(val) && _isArray(val)){
  //         val.forEach(item=>{
  //           arrName.push(item['d09Name']);
  //           arrValue.push(item['count']);
  //         });
  //       }
  //       return {
  //         title : {
  //           text: '党员年龄分布',
  //           x:'left'
  //         },
  //         tooltip : {
  //           trigger: 'axis',
  //           axisPointer : {
  //             type : 'shadow'
  //           }
  //         },
  //         grid: {
  //           left: '3%',
  //           right: '4%',
  //           bottom: '3%',
  //           containLabel: true
  //         },
  //         xAxis : [
  //           {
  //             type : 'category',
  //             data : arrName,
  //             axisTick: {
  //               alignWithLabel: true
  //             }
  //           }
  //         ],
  //         yAxis : [
  //           {
  //             type : 'value'
  //           }
  //         ],
  //         series : [
  //           {
  //             name:'数量',
  //             type:'bar',
  //             barWidth: '60%',
  //             data:arrValue,
  //             itemStyle: {
  //               color: function(params) {
  //                 let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
  //                 return colorList[params.dataIndex];
  //               }
  //             },
  //           }
  //         ]
  //       }
  //     },
  //   }
  // },
  // {
  //   key:'1018', // 党员党龄分布
  //   value:{
  //     // coverImg:require('@/components/CardsGroup/assets/develop/chart_edu.jpg'),
  //     action:'/api/chart/mem/getEducationRatioTotal',
  //     option:(val)=>{
  //       let arrName:Array<string> = [];
  //       let arrValue:Array<string> = [];
  //       if(!_isEmpty(val) && _isArray(val)){
  //         val.forEach(item=>{
  //           arrName.push(item['d09Name']);
  //           arrValue.push(item['count']);
  //         });
  //       }
  //       return {
  //         title : {
  //           text: '党员党龄分布',
  //           x:'left'
  //         },
  //         tooltip : {
  //           trigger: 'axis',
  //           axisPointer : {
  //             type : 'shadow'
  //           }
  //         },
  //         grid: {
  //           left: '3%',
  //           right: '4%',
  //           bottom: '3%',
  //           containLabel: true
  //         },
  //         xAxis : [
  //           {
  //             type : 'category',
  //             data : arrName,
  //             axisTick: {
  //               alignWithLabel: true
  //             }
  //           }
  //         ],
  //         yAxis : [
  //           {
  //             type : 'value'
  //           }
  //         ],
  //         series : [
  //           {
  //             name:'数量',
  //             type:'bar',
  //             barWidth: '60%',
  //             data:arrValue,
  //             itemStyle: {
  //               color: function(params) {
  //                 let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
  //                 return colorList[params.dataIndex];
  //               }
  //             },
  //           }
  //         ]
  //       }
  //     },
  //   }
  // },
  // {
  //   key:'1019',
  //   value:{
  //     // coverImg:require('@/components/CardsGroup/assets/develop/chart_edu.jpg'),
  //     action:'/api/chart/mem/getEducationRatioTotal',
  //     option:(val)=>{
  //       let arrName:Array<string> = [];
  //       let arrValue:Array<string> = [];
  //       if(!_isEmpty(val) && _isArray(val)){
  //         val.forEach(item=>{
  //           arrName.push(item['d09Name']);
  //           arrValue.push(item['count']);
  //         });
  //       }
  //       return {
  //         title : {
  //           text: '党员入党时间分布',
  //           x:'left'
  //         },
  //         tooltip : {
  //           trigger: 'axis',
  //           axisPointer : {
  //             type : 'shadow'
  //           }
  //         },
  //         grid: {
  //           left: '3%',
  //           right: '4%',
  //           bottom: '3%',
  //           containLabel: true
  //         },
  //         xAxis : [
  //           {
  //             type : 'category',
  //             data : arrName,
  //             axisTick: {
  //               alignWithLabel: true
  //             }
  //           }
  //         ],
  //         yAxis : [
  //           {
  //             type : 'value'
  //           }
  //         ],
  //         series : [
  //           {
  //             name:'数量',
  //             type:'bar',
  //             barWidth: '60%',
  //             data:arrValue,
  //             itemStyle: {
  //               color: function(params) {
  //                 let colorList = ['#5cc6ca', '#d87a7f', '#f5b97f', '#5ab1ef', '#b6a2de', '#8d98b3', '#e5d02d', '#97b552', '#956f6d', '#d0579c'];
  //                 return colorList[params.dataIndex];
  //               }
  //             },
  //           }
  //         ]
  //       }
  //     },
  //   }
  // },
];
