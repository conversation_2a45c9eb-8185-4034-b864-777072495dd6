import NumberCard from '@/components/NumberCard';
import React from 'react';
import _isEqual from 'lodash/isEqual';
import { withContext } from '@/utils/global';
import { isEmpty } from '@/utils/method';
import request from "@/utils/request";
import { getSession } from '@/utils/session';

@withContext
export default class MemTotal extends React.Component<any, any> {
  state = {
    total:0,
    dataSource:{},
    getList:async (orgCode, startDate, endDate, action)=> {
      if(!isEmpty(orgCode)){
        request(action,{
          method:'POST',
          body:{
            data:{
              orgCode,
              startDate,
              endDate
            }
          },
        }).then(res=>{
          if(res['code']===0){
            this.setState({
              dataSource: res['data']
            });
          }
        });
      }
    }
  };
  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const {context,timeRange:{startDate = undefined,endDate = undefined}={},cardData:{action = ''}={}} = nextProps;
    const {_context, getList, _startDate, _endDate } = prevState;
    if(!_isEqual(context, _context) || !_isEqual(startDate,_startDate) || !_isEqual(endDate,_endDate) ){
      state['_context'] = context;
      state['_startDate'] = startDate;
      state['_endDate'] = endDate;
      const { orgCode = ''} = getSession('org') || {orgCode:''}
      action && orgCode && getList(orgCode,startDate,endDate,action);
    }
    return state;
  };
  render() {
    const {total = 0,dataSource} =this.state;
    const {content,icon,iconColor,title,suffix,end,prefix,decimals}=this.props.cardData;

    return (
      <NumberCard icon={icon}
                  iconColor={iconColor}
                  end={
                    function(end,dataSource) {
                      if(end && typeof end === 'function'){
                        return end(dataSource)
                      }
                    }(end,dataSource)
                  }
                  decimals={decimals}
                  title={title}
                  prefix={prefix}
                  suffix={suffix}>
        {
          (
            function (content,dataSource) {
              if(content && typeof content==='function'){
                return content(dataSource)
              }
            }
          )(content,dataSource)
        }
      </NumberCard>
    )
  }
}
