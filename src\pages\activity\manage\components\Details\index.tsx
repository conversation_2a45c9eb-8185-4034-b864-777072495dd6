import React from 'react';
import { DeleteOutlined, EditOutlined, QuestionOutlined } from '@ant-design/icons';
import { Modal } from 'antd';
import Detail from '@/components/Detail';
import moment from 'moment';
import _isNumber from 'lodash/isNumber';
import _isEmpty from 'lodash/isEmpty';
export default class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state={
      visible:false
    }
  }
  renderMemNames=(arr)=>{
    let names:Array<string> = [];
    if(!_isEmpty(arr)){
      arr.map(item=>{
        names.push(item['name'])
      })
    }
    return names.toString();
  };
  renderTime=(number)=>{
    return _isNumber(number) ? moment(number).format('YYYY-MM-DD') : '';
  };
  renderWay=(code)=>{
    let arr = [{name:'扫码签到',key:'1',},{name:'打开签到',key:'2',},{name:'定位签到',key:'3',}];
    let str = arr.find(item=>item['key']===code);
    const {name = ''} = str || {};
    return name;
  };
  renderNoticeWay=(code)=>{
    let arr = [{name:'微信通知',key:'1',},{name:'短信通知',key:'2',},{name:'平台通知',key:'3',}];
    let str = arr.find(item=>item['key']===code);
    const {name = ''} = str || {};
    return name;
  };
  open=()=>{
    this.setState({
      visible:true
    })
  };
  close=()=>{
    this.setState({
      visible:false
    });
    this.props.dispatch({
      type:'activityManage/updateState',
      payload:{
        details:{}
      }
    })
  };
  render(): React.ReactNode {
    const {activityManage} = this.props;
    const {details} = activityManage;
    const {
      name = '',
      typeNames = [],
      hostorList = [],
      holdTime = '',
      endTime = '',
      canReview = '',
      noticePlan = '',
      attendList = [],
      lecturerList = [],
      memList = [],
      absenceList = [],
      acPlan= '',
      content = '',
    } = details || {};
    const {visible} = this.state;
    const subTitle = (
      <div>
        <a><EditOutlined />编辑</a>&nbsp;&nbsp;
        <a><QuestionOutlined />问题</a>&nbsp;&nbsp;
        <a><DeleteOutlined />删除</a>
      </div>
    );
    const middleRows = [
      {title:'主要议程', content:acPlan},
      {title:'活动内容', content:content},
      // {title:'这是标题3', content:'可以改变颜色,默认是蓝色', color:'red'}
    ];
    const itemsArr = [
      {leftTitle:'活动类型', leftInfo:typeNames.toString() || '无', rightTitle:'主持人', rightInfo:this.renderMemNames(hostorList) || '无'},
      {leftTitle:'开始时间', leftInfo:this.renderTime(holdTime), rightTitle:'结束时间', rightInfo:this.renderTime(endTime)},
      {leftTitle:'签到方式', leftInfo:this.renderWay(canReview), rightTitle:'通知方式', rightInfo:this.renderNoticeWay(noticePlan)},
      {leftTitle:'列席人员', leftInfo:this.renderMemNames(attendList) || '无', rightTitle:'讲课人', rightInfo:this.renderMemNames(lecturerList) || '无'},
      {leftTitle:'应到人员', leftInfo:this.renderMemNames(memList) || '无', rightTitle:'缺席人员', rightInfo:this.renderMemNames(absenceList) || '无'},
    ];
    return (
      <div>
        <Modal
          destroyOnClose
          title={name}
          visible={visible}
          // onOk={this.handleOk}
          onCancel={this.close}
          width={'1200px'}
          footer={false}
          bodyStyle={{height:570,overflow:'auto',padding:'0.2rem'}}
        >
          <Detail
                  // title='干掉黄球安，我就是老大'
                  // goBack={this.goBack}
                  // subTitle={subTitle}
                  itemsArr={itemsArr}
                  middleRows={middleRows}
          />
        </Modal>

      </div>
    )
  }
}

