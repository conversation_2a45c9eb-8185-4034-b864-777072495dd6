/**
 * 基本信息
 */
import React, { Fragment } from 'react';
import { Form, Icon as LegacyIcon } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Button, Col, DatePicker, Input, Radio, Row, Switch, Tooltip, Select, InputNumber, Modal } from 'antd';
import DictTreeSelect from '@/components/DictTreeSelect';
import OrgSelect from '@/components/OrgSelect';
import Tip from '@/components/Tip';
import moment from 'moment';
import { NumberReg } from "@/utils/validator";
import { connect } from "dva";
import { QuestionCircleOutlined } from '@ant-design/icons';
import { formLabel, formTip, isEmpty, jsonToTree, treeToList, findDictCodeName } from '@/utils/method';
import Date from '@/components/Date';
import YN from '@/components/YesOrNoSelect';
import _isEmpty from 'lodash/isEmpty';
import _isNumber from 'lodash/isNumber';
import _isEqual from 'lodash/isEqual';
import _isArray from 'lodash/isArray';
import _get from 'lodash/get';
import _cloneDeep from 'lodash/cloneDeep';
import _trim from 'lodash/trim'
import _uniqBy from 'lodash/uniqBy'
import _differenceBy from 'lodash/differenceBy'
import DictSelect from '@/components/DictSelect';
import LongLabelFormItem from '@/components/LongLabelFormItem';
import LinkedSpecialOrg from '@/pages/org/special/components/linkedOrg';
import { getSession, getLocalSession } from '@/utils/session';
import { tipsForChangingOrgType, superUnitOrgLinked, approveOrg } from '@/pages/org/services';
import { getMainUnitByOrg } from '@/pages/org/services/org';
import { LockMsg } from '@/pages/user/lock';
import { normalList } from '@/services';
import { validateLength, validateMobilePhoneNumber } from '@/utils/formValidator';
import ListTable from 'src/components/ListTable';
import { inflowOrganizationDInfo, auditfind } from '@/pages/flowMem/service'

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const formItemLayout2 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
};
const formItemLayout3 = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
};
// @ts-ignore
@connect(({ loading, commonDict }) => ({ orgAdd: loading.effects['org/add'], orgUpdate: loading.effects['org/update'], commonDict }))
class index extends React.Component<any, any> {
  constructor(props) {
    super(props);
    this.state = {
      timeKey: moment().valueOf(),
      basicInfoCollectiveEconomy: [],
      linkedDTOListLh: [],
      timeKey2: moment().valueOf(),
    };
  }
  componentDidMount() {
    this.setState({ timeKey: moment().valueOf() });

    if (this.props?.record?.id) {
      this.findBaseInfo()
    }

  }



  static getDerivedStateFromProps = (nextProps, prevState) => {
    const state = {};
    const { org: { basicInfo = {} } = {} } = nextProps;
    const { _basicInfo } = prevState;
    if (!_isEqual(_basicInfo, basicInfo)) {
      state['linkedDTOListLh'] = basicInfo['linkedDTOList']
      state['linkedDTOList_old'] = _cloneDeep(basicInfo['linkedDTOList'])
      state['_basicInfo'] = basicInfo;
      // 这点需要对form表单重新赋值，因为194和195传入的值和返回的值会不一样
      if (basicInfo?.d01Code && basicInfo.d01Code != '25') {  //梁才--组织类别为2开头时国民经济赋默认值S
        if (basicInfo.d01Code.startsWith('1') || basicInfo.d01Code.startsWith('2')) {
          state['d194CodeSatate'] = 'S'
          state['d195CodeSatate'] = 'V0000';
        } else {
          state['d194CodeSatate'] = undefined
          state['d195CodeSatate'] = undefined;
        }
      } else {
        state['d194CodeSatate'] = undefined
        state['d195CodeSatate'] = undefined;
      }



      state['d194CodeKey'] = moment().valueOf();
      state['d195CodeKey'] = moment().valueOf();
      nextProps.form.setFieldsValue({
        ...basicInfo
      })
      state['basicInfoCollectiveEconomy'] = (basicInfo?.collectiveEconomy || []).map((it, index) => {
        return { ...it, id: moment().valueOf() + index }
      })
    }
    return state;
  };

  findBaseInfo = async () => {
    const { code = 500, data = {} } = await auditfind({
      data: { id: this.props.record.id }
    })
    // console.log(res,'rrrrrrrrrrrrrrrrr')
    // this.props.form.setFieldsValue({
    //   ...data
    // })
    this.setState({
      memInfo: data
    })
  }


  showModal = () => {
    this.setState({
      visible: true,
      loading: true
    })
  }
  render() {
    const { orgAdd, orgUpdate, tipMsg = {}, commonDict, record = {} } = this.props;
    console.log("🚀 ~ index ~ render ~ tipMsg:", tipMsg)
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { basicInfo = {} } = this.props.org;
    const { lockFields = [] } = basicInfo || {};
    const { selectedRowKeys, loading, visible, list, pagination, selectedItems, memInfo = {} } = this.state;
    return (
      <>
        <Form {...formItemLayout} key={this.state.timeKey} style={{ pointerEvents: record.isEdit ? 'auto' : 'none' }}>
          <FormItem
            label={formLabel('姓名', tipMsg['name1'])}
          >
            {getFieldDecorator('name', {
              initialValue: memInfo['name'],
              rules: [{ required: true, message: '姓名' }],
            })(
              <Input placeholder={'姓名'} disabled />
            )}
          </FormItem>

          <Row>
            <Col span={12}>
              <FormItem
                label={formLabel('性别', tipMsg['sexName'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('sexName', {
                  initialValue: memInfo['sexName'] || undefined,
                  rules: [{ required: true, message: '成立日期' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('身份证', tipMsg['idcard'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('idcard', {
                  initialValue: memInfo['idcard'],
                  rules: [{ required: true, message: '党组织联系人' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('手机号码', tipMsg['phone'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('phone', {
                  //   getValueFromEvent: e => _trim(e.target.value),
                  initialValue: memInfo['phone'],
                  // rules: [{ required: true, message: '联系电话' }, { pattern: new RegExp('((\\d{11})|^((\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})|(\\d{4}|\\d{3})-(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1})|(\\d{7,8})-(\\d{4}|\\d{3}|\\d{2}|\\d{1}))$)'), message: '请输入正确的联系电话' }],
                  rules: [{ required: true, message: '联系电话' }],
                })(
                  <Input placeholder={'联系电话'} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('出生日期', tipMsg['birthday'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('birthday', {
                  initialValue: _isEmpty(memInfo) ? undefined : _isNumber(memInfo['birthday']) ? moment(memInfo['birthday']).format('YYYY.MM.DD') : '',
                  rules: [{ required: true, message: '出生日期' }],
                })(
                  // <Date disabled />
                  <Input disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>

              <FormItem
                label={formLabel('民族', tipMsg['d06Name'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('d06Name', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['d06Name'],
                  rules: [{ required: true, message: '批准成立的党组织' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('学历', tipMsg['d07Name'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('d07Name', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['d07Name'],
                  rules: [{ required: true, message: '批准成立的党组织' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('学位', tipMsg['d145Name'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('d145Name', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['d145Name'],
                  rules: [{ required: true, message: '批准成立的党组织' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('入党日期', tipMsg['joinDate'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('joinDate', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['joinDate'],
                  rules: [{ required: true, message: '' }],
                })(
                  <Date disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('转正日期', tipMsg['formalDate'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('formalDate', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['formalDate'],
                  rules: [{ required: true, message: '' }],
                })(
                  <Date disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('工作岗位', tipMsg['d09Name'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('d09Name', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['d09Name'],
                  rules: [{ required: true, message: '' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('新社会阶层', tipMsg['d20Name'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('d20Name', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['d20Name'],
                  rules: [{ required: true, message: '' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('从事专业技术职务', tipMsg['workPostName'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('workPostName', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['workPostName'],
                  rules: [{ required: true, message: '' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('人员类别', tipMsg['d08Name'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('d08Name', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['d08Name'],
                  rules: [{ required: true, message: '' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('是否农民工', tipMsg['isFarmerName'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('isFarmerName', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['isFarmerName'],
                  rules: [{ required: true, message: '' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('户籍所在地', tipMsg['houseHoldRegister'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('houseHoldRegister', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['houseHoldRegister'],
                  rules: [{ required: true, message: '' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('家庭住址', tipMsg['homeAddress'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('homeAddress', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['homeAddress'],
                  rules: [{ required: true, message: '' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem
                label={formLabel('党龄校正值', tipMsg['ageCorrection'])}
                {...formItemLayout2}
              >
                {getFieldDecorator('ageCorrection', {
                  initialValue: _isEmpty(memInfo) ? undefined : memInfo['ageCorrection'],
                  rules: [{ required: true, message: '' }],
                })(
                  <Input placeholder={''} disabled />
                )}
              </FormItem>
            </Col>
          </Row>
        </Form>

      </>

    );
  }
}
export default Form.create<any>()(index);
